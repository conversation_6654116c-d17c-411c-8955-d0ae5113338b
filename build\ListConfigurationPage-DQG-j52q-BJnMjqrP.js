import{cG as I,ba as G,a as N,c as Y,cN as k,cP as J,f2 as Q,r as f,eI as X,m as e,aV as q,n as L,L as A,aX as K,O as z,bF as x,eK as u,f3 as H,A as P,bh as Z,w as S,d as ee,s as O,G as D,Q as _,aS as te,cJ as se,eU as ae,J as V,a4 as ne,S as E,f4 as oe,M as C,F as ie,aC as re,i as le,j as de,l as ce,bE as ue,e_ as w,V as pe,b0 as me}from"./strapi-YzJfjJ2z.js";import{u as ge}from"./hooks-E5u1mcgM-Ce79Lxm5.js";import{s as fe}from"./objects-D6yBsdmx-8Q5QgcI9.js";import{C as B}from"./CardDragPreview-DOxamsuj-B5-jyj7W.js";import{u as he,I as be}from"./useDragAndDrop-DdHgKsqq-DDLMDz6i.js";import{F as ye}from"./FieldTypeIcon-CMlNO8PE-C0u_4SP8.js";import{g as je}from"./getEmptyImage-CjqolaH3.js";const xe=({name:t})=>{const{formatMessage:c}=I(),l=x("Header",m=>m.modified),n=x("Header",m=>m.isSubmitting);return e.jsx(A.Header,{navigationAction:e.jsx(Z,{}),primaryAction:e.jsx(P,{size:"S",disabled:!l,type:"submit",loading:n,children:c({id:"global.save",defaultMessage:"Save"})}),subtitle:c({id:u("components.SettingsViewWrapper.pluginHeader.description.list-settings"),defaultMessage:"Define the settings of the list view."}),title:c({id:u("components.SettingsViewWrapper.pluginHeader.title"),defaultMessage:"Configure the view - {name}"},{name:H(t)})})},Se=["media","richtext","dynamiczone","relation","component","json","blocks"],Me=()=>{const{formatMessage:t,locale:c}=I(),l=ee(c,{sensitivity:"base"}),{schema:n}=k(),m=x("Settings",o=>o.values.layout??[]),g=x("Settings",o=>o.values.settings.defaultSortBy),b=x("Settings",o=>o.onChange),i=f.useMemo(()=>Object.values(m).reduce((o,a)=>(n&&!Se.includes(n.attributes[a.name].type)&&o.push({value:a.name,label:typeof a.label!="string"?t(a.label):a.label}),o),[]),[t,m,n]).sort((o,a)=>l.compare(o.label,a.label));f.useEffect(()=>{i.findIndex(o=>o.value===g)===-1&&b("settings.defaultSortBy",i[0]?.value)},[g,b,i]);const h=f.useMemo(()=>ve.map(o=>o.map(a=>a.type==="enumeration"?{...a,hint:a.hint?t(a.hint):void 0,label:t(a.label),options:a.name==="settings.defaultSortBy"?i:a.options}:{...a,hint:a.hint?t(a.hint):void 0,label:t(a.label)})),[t,i]);return e.jsxs(S,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(O,{variant:"delta",tag:"h2",children:t({id:u("containers.SettingPage.settings"),defaultMessage:"Settings"})}),e.jsx(D.Root,{gap:4,children:h.map(o=>o.map(({size:a,...p})=>e.jsx(D.Item,{s:12,col:a,direction:"column",alignItems:"stretch",children:e.jsx(_,{...p})},p.name)))},"bottom")]})},ve=[[{label:{id:u("form.Input.search"),defaultMessage:"Enable search"},name:"settings.searchable",size:4,type:"boolean"},{label:{id:u("form.Input.filters"),defaultMessage:"Enable filters"},name:"settings.filterable",size:4,type:"boolean"},{label:{id:u("form.Input.bulkActions"),defaultMessage:"Enable bulk actions"},name:"settings.bulkable",size:4,type:"boolean"}],[{hint:{id:u("form.Input.pageEntries.inputDescription"),defaultMessage:"Note: You can override this value in the Collection Type settings page."},label:{id:u("form.Input.pageEntries"),defaultMessage:"Entries per page"},name:"settings.pageSize",options:["10","20","50","100"].map(t=>({value:t,label:t})),size:6,type:"enumeration"},{label:{id:u("form.Input.defaultSort"),defaultMessage:"Default sort attribute"},name:"settings.defaultSortBy",options:[],size:3,type:"enumeration"},{label:{id:u("form.Input.sort.order"),defaultMessage:"Default sort order"},name:"settings.defaultSortOrder",options:["ASC","DESC"].map(t=>({value:t,label:t})),size:3,type:"enumeration"}]],Ce=le().shape({label:ce().required(),sortable:de()}),Fe=({attribute:t,name:c,onClose:l})=>{const{formatMessage:n}=I(),{toggleNotification:m}=N(),{value:g,onChange:b}=re(c);if(!g)return console.error("You've opened a field to edit without it being part of the form, this is likely a bug with Strapi. Please open an issue."),m({message:n({id:"content-manager.containers.list-settings.modal-form.error",defaultMessage:"An error occurred while trying to open the form."}),type:"danger"}),null;let d=!["media","relation"].includes(t.type);return"relation"in t&&["oneWay","oneToOne","manyToOne"].includes(t.relation)&&(d=!0),e.jsx(C.Content,{children:e.jsxs(z,{method:"PUT",initialValues:g,validationSchema:Ce,onSubmit:i=>{b(c,i),l()},children:[e.jsx(C.Header,{children:e.jsxs(Ie,{children:[e.jsx(ye,{type:t.type}),e.jsx(C.Title,{children:n({id:u("containers.list-settings.modal-form.label"),defaultMessage:"Edit {fieldName}"},{fieldName:H(g.label)})})]})}),e.jsx(C.Body,{children:e.jsx(D.Root,{gap:4,children:[{name:"label",label:n({id:u("form.Input.label"),defaultMessage:"Label"}),hint:n({id:u("form.Input.label.inputDescription"),defaultMessage:"This value overrides the label displayed in the table's head"}),size:6,type:"string"},{label:n({id:u("form.Input.sort.field"),defaultMessage:"Enable sort on this field"}),name:"sortable",size:6,type:"boolean"}].filter(i=>i.name!=="sortable"||i.name==="sortable"&&d).map(({size:i,...h})=>e.jsx(D.Item,{s:12,col:i,direction:"column",alignItems:"stretch",children:e.jsx(_,{...h})},h.name))})}),e.jsxs(C.Footer,{children:[e.jsx(P,{onClick:l,variant:"tertiary",children:n({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),e.jsx(P,{type:"submit",children:n({id:"global.finish",defaultMessage:"Finish"})})]})]})})},Ie=E(S)`
  svg {
    width: 3.2rem;
    margin-right: ${({theme:t})=>t.spaces[3]};
  }
`,Re=({attribute:t,index:c,isDraggingSibling:l,label:n,name:m,onMoveField:g,onRemoveField:b,setIsDraggingSibling:d})=>{const[i,h]=f.useState(!1),{formatMessage:o}=I(),[,a]=f.useState(!1),[{isDragging:p},y,M,F,j]=he(!0,{type:be.FIELD,item:{index:c,label:n,name:m},index:c,onMoveItem:g,onEnd:()=>d(!1)});f.useEffect(()=>{j(je(),{captureDraggingState:!1})},[j]),f.useEffect(()=>{p&&d(!0)},[p,d]),f.useEffect(()=>{l||a(r=>!r)},[l]);const s=ne(M,y);return e.jsxs(we,{ref:s,children:[p&&e.jsx(B,{label:n}),!p&&l&&e.jsx(B,{isSibling:!0,label:n}),!p&&!l&&e.jsxs(Te,{borderColor:"neutral150",background:"neutral100",hasRadius:!0,justifyContent:"space-between",onClick:()=>h(!0),children:[e.jsxs(S,{gap:3,children:[e.jsx(Ee,{ref:F,"aria-label":o({id:u("components.DraggableCard.move.field"),defaultMessage:"Move {item}"},{item:n}),onClick:r=>r.stopPropagation(),children:e.jsx(oe,{})}),e.jsx(O,{fontWeight:"bold",children:n})]}),e.jsxs(S,{paddingLeft:3,onClick:r=>r.stopPropagation(),children:[e.jsxs(C.Root,{open:i,onOpenChange:h,children:[e.jsx(C.Trigger,{children:e.jsx($,{onClick:r=>{r.stopPropagation()},"aria-label":o({id:u("components.DraggableCard.edit.field"),defaultMessage:"Edit {item}"},{item:n}),type:"button",children:e.jsx(ie,{width:"1.2rem",height:"1.2rem"})})}),e.jsx(Fe,{attribute:t,name:`layout.${c}`,onClose:()=>{h(!1)}})]}),e.jsx($,{onClick:b,"data-testid":`delete-${m}`,"aria-label":o({id:u("components.DraggableCard.delete.field"),defaultMessage:"Delete {item}"},{item:n}),type:"button",children:e.jsx(ue,{width:"1.2rem",height:"1.2rem"})})]})]})]})},$=E.button`
  display: flex;
  align-items: center;
  height: ${({theme:t})=>t.spaces[7]};
  color: ${({theme:t})=>t.colors.neutral600};

  &:hover {
    color: ${({theme:t})=>t.colors.neutral700};
  }

  &:last-child {
    padding: 0 ${({theme:t})=>t.spaces[3]};
  }
`,Ee=E($)`
  padding: 0 ${({theme:t})=>t.spaces[3]};
  border-right: 1px solid ${({theme:t})=>t.colors.neutral150};
  cursor: all-scroll;
`,Te=E(S)`
  max-height: 3.2rem;
  cursor: pointer;
`,we=E(V)`
  &:last-child {
    padding-right: ${({theme:t})=>t.spaces[3]};
  }
`,De=()=>{const{formatMessage:t}=I(),{model:c,schema:l}=k(),[n,m]=f.useState(!1),[g,b]=f.useState(null),d=f.useRef(null),i=x("SortDisplayedFields",s=>s.values.layout??[]),h=x("SortDisplayedFields",s=>s.addFieldRow),o=x("SortDisplayedFields",s=>s.removeFieldRow),a=x("SortDisplayedFields",s=>s.moveFieldRow),{metadata:p}=se(c,{selectFromResult:({data:s})=>({metadata:s?.contentType.metadatas??{}})}),y=f.useMemo(()=>{if(!l)return[];const s=i.map(r=>r.name);return Object.entries(l.attributes).reduce((r,[v,R])=>{if(!s.includes(v)&&ae(R)){const{list:T}=p[v];r.push({name:v,label:T.label||v,sortable:T.sortable})}return r},[])},[p,i,l]),M=s=>{b("add"),h("layout",s)},F=s=>{b("remove"),o("layout",s)},j=(s,r)=>{a("layout",s,r)};return f.useEffect(()=>{g==="add"&&d?.current&&(d.current.scrollLeft=d.current.scrollWidth)},[g]),e.jsxs(S,{alignItems:"stretch",direction:"column",gap:4,children:[e.jsx(O,{variant:"delta",tag:"h2",children:t({id:u("containers.SettingPage.view"),defaultMessage:"View"})}),e.jsxs(S,{padding:4,borderColor:"neutral300",borderStyle:"dashed",borderWidth:"1px",hasRadius:!0,children:[e.jsx(V,{flex:"1",overflow:"auto hidden",ref:d,children:e.jsx(S,{gap:3,children:i.map((s,r)=>e.jsx(Re,{index:r,isDraggingSibling:n,onMoveField:j,onRemoveField:()=>F(r),setIsDraggingSibling:m,...s,attribute:l.attributes[s.name],label:typeof s.label=="object"?t(s.label):s.label},s.name))})}),e.jsxs(w.Root,{children:[e.jsxs(w.Trigger,{paddingLeft:2,paddingRight:2,justifyContent:"center",endIcon:null,disabled:y.length===0,variant:"tertiary",children:[e.jsx(pe,{tag:"span",children:t({id:u("components.FieldSelect.label"),defaultMessage:"Add a field"})}),e.jsx(me,{"aria-hidden":!0,focusable:!1,style:{position:"relative",top:2}})]}),e.jsx(w.Content,{children:y.map(s=>e.jsx(w.Item,{onSelect:()=>M(s),children:typeof s.label=="object"?t(s.label):s.label},s.name))})]})]})]})},Le=()=>{const{formatMessage:t}=I(),{trackUsage:c}=G(),{toggleNotification:l}=N(),{_unstableFormatAPIError:n}=Y(),{model:m,collectionType:g}=k(),{isLoading:b,list:d,edit:i}=J(),[h]=Q(),o=async p=>{try{c("willSaveContentTypeLayout");const y=p.layout??[],M=Object.entries(i.metadatas).reduce((j,[s,r])=>{const{mainField:v,...R}=d.metadatas[s],{label:T,sortable:U}=y.find(W=>W.name===s)??{};return j[s]={edit:r,list:{...R,label:T||R.label,sortable:U||R.sortable}},j},{}),F=await h({layouts:{edit:i.layout.flatMap(j=>j.map(s=>s.map(({name:r,size:v})=>({name:r,size:v})))),list:y.map(j=>j.name)},settings:fe(p.settings,"displayName",void 0),metadatas:M,uid:m});"data"in F?(c("didEditListSettings"),l({type:"success",message:t({id:"notification.success.saved",defaultMessage:"Saved"})})):l({type:"danger",message:n(F.error)})}catch(y){console.error(y),l({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred"})})}},a=f.useMemo(()=>({layout:d.layout.map(({label:p,sortable:y,name:M})=>({label:typeof p=="string"?p:t(p),sortable:y,name:M})),settings:d.settings}),[t,d.layout,d.settings]);return g===X?e.jsx(q,{to:`/single-types/${m}`}):b?e.jsx(L.Loading,{}):e.jsxs(A.Root,{children:[e.jsx(L.Title,{children:`Configure ${d.settings.displayName} List View`}),e.jsx(K,{children:e.jsxs(z,{initialValues:a,onSubmit:o,method:"PUT",children:[e.jsx(xe,{collectionType:g,model:m,name:d.settings.displayName??""}),e.jsx(A.Content,{children:e.jsxs(S,{alignItems:"stretch",background:"neutral0",direction:"column",gap:6,hasRadius:!0,shadow:"tableShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:[e.jsx(Me,{}),e.jsx(te,{}),e.jsx(De,{})]})})]})})]})},ze=()=>{const t=ge(c=>c.admin_app.permissions.contentManager?.collectionTypesConfigurations);return e.jsx(L.Protect,{permissions:t,children:e.jsx(Le,{})})};export{Le as ListConfiguration,ze as ProtectedListConfiguration};
