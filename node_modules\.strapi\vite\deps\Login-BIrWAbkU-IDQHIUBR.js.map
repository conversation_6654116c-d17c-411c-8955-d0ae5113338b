{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/AuthPage/components/Login.tsx"], "sourcesContent": ["import { Box, Divider, Flex, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { Login, LoginProps } from '../../../../../../admin/src/pages/Auth/components/Login';\nimport { useGetProvidersQuery } from '../../../../../../admin/src/services/auth';\n\nimport { SSOProviders } from './SSOProviders';\n\nconst DividerFull = styled(Divider)`\n  flex: 1;\n`;\n\nconst LoginEE = (loginProps: LoginProps) => {\n  const { formatMessage } = useIntl();\n  const { isLoading, data: providers = [] } = useGetProvidersQuery(undefined, {\n    skip: !window.strapi.features.isEnabled(window.strapi.features.SSO),\n  });\n\n  if (\n    !window.strapi.features.isEnabled(window.strapi.features.SSO) ||\n    (!isLoading && providers.length === 0)\n  ) {\n    return <Login {...loginProps} />;\n  }\n\n  return (\n    <Login {...loginProps}>\n      <Box paddingTop={7}>\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={7}>\n          <Flex>\n            <DividerFull />\n            <Box paddingLeft={3} paddingRight={3}>\n              <Typography variant=\"sigma\" textColor=\"neutral600\">\n                {formatMessage({ id: 'Auth.login.sso.divider' })}\n              </Typography>\n            </Box>\n            <DividerFull />\n          </Flex>\n          <SSOProviders providers={providers} displayAllProviders={false} />\n        </Flex>\n      </Box>\n    </Login>\n  );\n};\n\nexport { LoginEE };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,cAAc,GAAO,OAAO;;;AAI5B,IAAA,UAAU,CAAC,eAA2B;AACpC,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,WAAW,MAAM,YAAY,CAAA,EAAG,IAAI,qBAAqB,QAAW;IAC1E,MAAM,CAAC,OAAO,OAAO,SAAS,UAAU,OAAO,OAAO,SAAS,GAAG;EAAA,CACnE;AAED,MACE,CAAC,OAAO,OAAO,SAAS,UAAU,OAAO,OAAO,SAAS,GAAG,KAC3D,CAAC,aAAa,UAAU,WAAW,GACpC;AACO,eAAA,wBAAC,OAAO,EAAA,GAAG,WAAY,CAAA;EAAA;AAGhC,aACG,wBAAA,OAAA,EAAO,GAAG,YACT,cAAA,wBAAC,KAAI,EAAA,YAAY,GACf,cAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;QAAA,yBAAC,MACC,EAAA,UAAA;UAAA,wBAAC,aAAY,CAAA,CAAA;UAAA,wBACZ,KAAI,EAAA,aAAa,GAAG,cAAc,GACjC,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA,EAAE,IAAI,yBAAyB,CAAC,EAAA,CACjD,EACF,CAAA;UAAA,wBACC,aAAY,CAAA,CAAA;IAAA,EACf,CAAA;QACC,wBAAA,cAAA,EAAa,WAAsB,qBAAqB,MAAO,CAAA;EAAA,EAAA,CAClE,EAAA,CACF,EACF,CAAA;AAEJ;", "names": []}