{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/_chunks/zh-5hKkVPA4.mjs"], "sourcesContent": ["const zh = {\n  \"BoundRoute.title\": \"綁定路徑到\",\n  \"EditForm.inputSelect.description.role\": \"將新的驗證使用者加入此身份。\",\n  \"EditForm.inputSelect.label.role\": \"驗證使用者預設身份\",\n  \"EditForm.inputToggle.description.email\": \"禁止使用者使用同一個電子郵件地址 + 不同的驗證方式註冊多個帳號\",\n  \"EditForm.inputToggle.description.email-confirmation\": \"當啟用後，新註冊的使用者將會收到一封認證郵件。\",\n  \"EditForm.inputToggle.description.email-confirmation-redirection\": \"認證完後後，使用者被重新導向的網址。\",\n  \"EditForm.inputToggle.description.email-reset-password\": \"您的應用程式的重設密碼頁面的網址\",\n  \"EditForm.inputToggle.description.sign-up\": \"當停用後，不論使用任何驗證方式使用者將無法註冊。\",\n  \"EditForm.inputToggle.label.email\": \"電子郵件地址單一帳號限制\",\n  \"EditForm.inputToggle.label.email-confirmation\": \"啟用電子郵件地址驗證\",\n  \"EditForm.inputToggle.label.email-confirmation-redirection\": \"重新導向網址\",\n  \"EditForm.inputToggle.label.email-reset-password\": \"密碼重設頁面\",\n  \"EditForm.inputToggle.label.sign-up\": \"啟用註冊\",\n  \"EditForm.inputToggle.placeholder.email-confirmation-redirection\": \"例如：https://yourfrontend.com/email-confirmation-redirection\",\n  \"EditForm.inputToggle.placeholder.email-reset-password\": \"例如：https://yourfrontend.com/reset-password\",\n  \"EditPage.form.roles\": \"角色詳細資訊\",\n  \"Email.template.data.loaded\": \"已載入電子郵件範本\",\n  \"Email.template.email_confirmation\": \"電子郵件地址確認\",\n  \"Email.template.form.edit.label\": \"編輯範本\",\n  \"Email.template.table.action.label\": \"操作\",\n  \"Email.template.table.icon.label\": \"圖示\",\n  \"Email.template.table.name.label\": \"名稱\",\n  \"Form.advancedSettings.data.loaded\": \"已載入進階設定資料\",\n  \"HeaderNav.link.advancedSettings\": \"進階設定\",\n  \"HeaderNav.link.emailTemplates\": \"郵件範本\",\n  \"HeaderNav.link.providers\": \"驗證方式\",\n  \"Plugin.permissions.plugins.description\": \"為 {name} 擴充功能定義所有可用的操作\",\n  \"Plugins.header.description\": \"只有綁定路徑的操作會顯示在下方\",\n  \"Plugins.header.title\": \"權限\",\n  \"Policies.header.hint\": \"選取應用程式或擴充功能的操作然後點擊齒輪圖示以顯示綁定路徑\",\n  \"Policies.header.title\": \"進階設定\",\n  \"PopUpForm.Email.email_templates.inputDescription\": \"如果您不確定要怎麼使用變數，請 {link}\",\n  \"PopUpForm.Email.link.documentation\": \"查閱我們的說明文件。\",\n  \"PopUpForm.Email.options.from.email.label\": \"寄件人地址\",\n  \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Email.options.from.name.label\": \"寄件人名稱\",\n  \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n  \"PopUpForm.Email.options.message.label\": \"訊息\",\n  \"PopUpForm.Email.options.object.label\": \"主旨\",\n  \"PopUpForm.Email.options.object.placeholder\": \"請驗證 %APP_NAME% 的電子郵件地址\",\n  \"PopUpForm.Email.options.response_email.label\": \"回覆地址\",\n  \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Providers.enabled.description\": \"如果停用，使用者將無法使用這個驗證方式\",\n  \"PopUpForm.Providers.enabled.label\": \"啟用\",\n  \"PopUpForm.Providers.key.label\": \"客戶端 ID\",\n  \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.redirectURL.front-end.label\": \"您應用程式的前端頁面網址\",\n  \"PopUpForm.Providers.redirectURL.label\": \"The redirect URL to add in your {provider} application configurations\",\n  \"PopUpForm.Providers.secret.label\": \"客戶端密鑰\",\n  \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.subdomain.label\": \"Host URI (子網域)\",\n  \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n  \"PopUpForm.header.edit.email-templates\": \"編輯郵件範本\",\n  \"PopUpForm.header.edit.providers\": \"編輯供應者\",\n  \"Providers.data.loaded\": \"已載入供應者\",\n  \"Providers.status\": \"狀態\",\n  \"Roles.empty\": \"您目前沒有任何角色。\",\n  \"Roles.empty.search\": \"沒有符合搜尋的角色。\",\n  \"Settings.roles.deleted\": \"已刪除角色\",\n  \"Settings.roles.edited\": \"已編輯角色\",\n  \"Settings.section-label\": \"使用者與權限外掛程式\",\n  \"components.Input.error.validation.email\": \"此電子郵件地址無效\",\n  \"components.Input.error.validation.json\": \"不符合 JSON 格式\",\n  \"components.Input.error.validation.max\": \"數值過高。\",\n  \"components.Input.error.validation.maxLength\": \"數值過長。\",\n  \"components.Input.error.validation.min\": \"數值過低。\",\n  \"components.Input.error.validation.minLength\": \"數值過短。\",\n  \"components.Input.error.validation.minSupMax\": \"Can't be superior\",\n  \"components.Input.error.validation.regex\": \"數值與 regex 不符。\",\n  \"components.Input.error.validation.required\": \"此數值為必填。\",\n  \"components.Input.error.validation.unique\": \"此值已被使用。\",\n  \"notification.success.submit\": \"設定已更新\",\n  \"page.title\": \"設定 - 角色\",\n  \"plugin.description.long\": \"使用 JWT 認證保護您的 API。這個擴充功能也使用 ACL 來讓你管理不同群組使用者的權限。\",\n  \"plugin.description.short\": \"使用 JWT 認證保護您的 API\",\n  \"plugin.name\": \"身份與權限\",\n  \"popUpWarning.button.cancel\": \"取消\",\n  \"popUpWarning.button.confirm\": \"確認\",\n  \"popUpWarning.title\": \"請確認\",\n  \"popUpWarning.warning.cancel\": \"您確定要取消變更嗎？\"\n};\nexport {\n  zh as default\n};\n//# sourceMappingURL=zh-5hKkVPA4.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACjC;", "names": []}