import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/upload/dist/_chunks/pl-Cj8jChOO.mjs
var pl = {
  "bulk.select.label": "Wybierz wszystkie",
  "button.next": "<PERSON><PERSON>",
  "checkControl.crop-duplicate": "Duplikuj i przytnij",
  "checkControl.crop-original": "Przytnij",
  "content.isLoading": "Ładowanie listy.",
  "control-card.add": "Dodaj",
  "control-card.cancel": "Anuluj",
  "control-card.copy-link": "Kopiuj link",
  "control-card.crop": "Przytnij",
  "control-card.download": "Pobierz",
  "control-card.edit": "Edytuj",
  "control-card.replace-media": "Zamień",
  "control-card.save": "Zapisz",
  "control-card.stop-crop": "Zatrzymaj przycinanie",
  "filter.add": "Dodaj filtr",
  "form.button.replace-media": "Zamień",
  "form.input.decription.file-alt": "Ten tekst zostanie wyświetlony, je<PERSON><PERSON> medium nie będzie mogło zostać pokazane.",
  "form.input.label.file-alt": "Tekst alternatywny",
  "form.input.label.file-caption": "Podpis",
  "form.input.label.file-name": "Nazwa pliku",
  "form.upload-url.error.url.invalid": "Link URL jest niepoprawny",
  "form.upload-url.error.url.invalids": "{number} linków URL jest niepoprawnych",
  "header.actions.add-assets": "Dodaj",
  "header.actions.upload-assets": "Prześlij zasób",
  "header.content.assets": "{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 zasób} other {# zasobów}}",
  "input.button.label": "Przeglądaj pliki",
  "input.label": "Przeciągnij i upuść tutaj lub",
  "input.label-bold": "Przeciągnij & upuść",
  "input.label-normal": "do przesłania lub",
  "input.placeholder": "Kliknij aby wybrać lub przeciągnij i upuść plik tutaj",
  "input.placeholder.icon": "Upuść tutaj",
  "input.url.description": "Oddziel swoje linki URL umieszczając je w nowych liniach.",
  "input.url.label": "URL",
  "list.asset.at.finished": "Załadowano.",
  "list.assets-empty.search": "Brak wyników wyszukiwania",
  "list.assets-empty.subtitle": "Dodaj jeden do listy.",
  "list.assets-empty.title": "Nie ma jeszcze żadnych zasobów",
  "list.assets-empty.title-withSearch": "Brak zasobów pasujących do wybranych filtrów",
  "list.assets.empty": "Prześlij pierwszy plik...",
  "list.assets.empty.no-permissions": "Lista jest pusta.",
  "list.assets.loading-asset": "Ładowanie podglądu dla: {path}",
  "list.assets.not-supported-content": "Podgląd nie jest dostępny",
  "list.assets.preview-asset": "Podgląd dla video: {path}",
  "list.assets.selected": "{number, plural, =0 {No asset} other {# zaznaczono}}",
  "list.assets.type-not-allowed": "Ten typ pliku jest niedozwolony.",
  "mediaLibraryInput.actions.nextSlide": "Następny",
  "mediaLibraryInput.actions.previousSlide": "Poprzedni",
  "mediaLibraryInput.placeholder": "Kliknij by dodać plik lub przeciągnij i upuść plik w to miejsce",
  "mediaLibraryInput.slideCount": "{n} z {m}",
  "modal.file-details.date": "Data",
  "modal.file-details.dimensions": "Wymiary",
  "modal.file-details.extension": "Rozszerzenia",
  "modal.file-details.size": "Rozmiar",
  "modal.header.browse": "Prześlij media",
  "modal.header.file-detail": "Szczegóły",
  "modal.header.pending-assets": "Oczekujące",
  "modal.header.select-files": "Wybrane pliki",
  "modal.nav.browse": "przeglądaj",
  "modal.nav.computer": "z komputera",
  "modal.nav.selected": "wybrane",
  "modal.nav.url": "z linku URL",
  "modal.remove.success-label": "Usunięto",
  "modal.selected-list.sub-header-subtitle": "Przeciągnij i upuść, aby zmienić kolejność zasobów w polu",
  "modal.upload-list.footer.button": "Prześlij",
  "modal.upload-list.sub-header-subtitle": "Zarządzaj zasobami przed dodaniem ich do Biblioteki Multimediów",
  "modal.upload-list.sub-header.button": "Dodaj więcej",
  "modal.upload.cancelled": "Przesyłanie przerwane.",
  "page.title": "Ustawienia - Biblioteka Multimediów",
  "permissions.not-allowed.update": "Brak dostępu do edycji pliku.",
  "plugin.description.long": "Zarządzanie plikami multimedialnymi.",
  "plugin.description.short": "Zarządzanie plikami multimedialnymi.",
  "plugin.name": "Bilbioteka Multimediów",
  "search.clear.label": "Wyczyść",
  "search.label": "Szukaj",
  "search.placeholder": "Szukaj...",
  "settings.blockTitle": "Zarządzanie plikami",
  "settings.form.autoOrientation.description": "Automatycznie obróć obraz zgodnie ze znacznikiem orientacji EXIF",
  "settings.form.autoOrientation.label": "Włącz automatyczną orientację",
  "settings.form.responsiveDimensions.description": "Automatycznie generuje wiele formatów (duży, średni, mały) przesłanego zasobu",
  "settings.form.responsiveDimensions.label": "Włącz przesyłanie przyjazne responsywności",
  "settings.form.sizeOptimization.description": "Włączenie tej opcji zmniejszy rozmiar obrazka i delikatnie zmniejszy jego jakość.",
  "settings.form.sizeOptimization.label": "Włącz optymalizację rozmiaru (bez utraty jakości)",
  "settings.form.videoPreview.description": "Wygeneruje sześciosekundowy podgląd wideo (GIF)",
  "settings.form.videoPreview.label": "Podgląd",
  "settings.header.label": "Biblioteka Multimediów",
  "settings.section.doc.label": "Dokument",
  "settings.section.image.label": "Obraz",
  "settings.section.video.label": "Video",
  "settings.sub-header.label": "Skonfiguruj ustawienia biblioteki multimediów",
  "sort.created_at_asc": "Najstarsze przesłane",
  "sort.created_at_desc": "Ostatnio przesłane",
  "sort.label": "Sortuj po",
  "sort.name_asc": "Kolejność alfabetyczna (od A do Z)",
  "sort.name_desc": "Odwróć kolejność alfabetyczną (od Z do A)",
  "sort.updated_at_asc": "Najstarsze aktualizacje",
  "sort.updated_at_desc": "Ostatnie aktualizacje",
  "tabs.title": "W jaki sposób chcesz przesłać pliki?",
  "window.confirm.close-modal.file": "Czy napewno? Twoje zmiany zostaną utracone.",
  "window.confirm.close-modal.files": "Czy napewno? Masz pliki, które nie zostały jeszcze przesłane."
};
export {
  pl as default
};
//# sourceMappingURL=pl-Cj8jChOO-EZYWG5F6.js.map
