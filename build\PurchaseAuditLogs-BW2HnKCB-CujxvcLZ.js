import{au as s,m as a,L as e,aX as i,J as o,bM as n,bK as r,bT as d,bU as l}from"./strapi-YzJfjJ2z.js";const g=()=>{const{formatMessage:t}=s();return a.jsx(e.Root,{children:a.jsxs(i,{children:[a.jsx(e.<PERSON>,{title:t({id:"global.auditLogs",defaultMessage:"Audit Logs"}),subtitle:t({id:"Settings.permissions.auditLogs.listview.header.subtitle",defaultMessage:"Logs of all the activities that happened in your environment"})}),a.jsx(o,{paddingLeft:10,paddingRight:10,children:a.jsx(n,{icon:a.jsx(l,{width:"16rem"}),content:t({id:"Settings.permissions.auditLogs.not-available",defaultMessage:"Audit Logs is only available as part of a paid plan. Upgrade to get a searchable and filterable display of all activities."}),action:a.jsx(r,{variant:"default",endIcon:a.jsx(d,{}),href:"https://strp.cc/45mbAdF",isExternal:!0,target:"_blank",children:t({id:"global.learn-more",defaultMessage:"Learn more"})})})})]})})};export{g as PurchaseAuditLogs};
