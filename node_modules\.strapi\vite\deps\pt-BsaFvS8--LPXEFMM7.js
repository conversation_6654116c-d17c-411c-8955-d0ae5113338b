import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/pt-BsaFvS8-.mjs
var pageNotFound = "Página não encontrada";
var pt = {
  "EditRelations.title": "Dados relacionais",
  "components.AddFilterCTA.add": "Filtros",
  "components.AddFilterCTA.hide": "Filtros",
  "components.DraggableAttr.edit": "Clique para editar",
  "components.EmptyAttributesBlock.button": "Ir para a página de configurações",
  "components.EmptyAttributesBlock.description": "Pode alterar as configurações",
  "components.FilterOptions.button.apply": "Aplicar",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Aplicar",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Limpar tudo",
  "components.FiltersPickWrapper.PluginHeader.description": "Definir as condições a serem aplicadas para filtrar as entradas",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filtros",
  "components.FiltersPickWrapper.hide": "Esconder",
  "components.LimitSelect.itemsPerPage": "Itens por página",
  "components.Search.placeholder": "Procurar por uma entrada...",
  "components.TableDelete.delete": "Apagar tudo",
  "components.TableDelete.deleteSelected": "Excluir selecionado",
  "components.TableEmpty.withFilters": "Não há {contentType} com os filtros aplicados...",
  "components.TableEmpty.withSearch": "Não há {contentType} correspondente à pesquisa ({search})...",
  "components.TableEmpty.withoutFilter": "Não há {contentType}...",
  "containers.Edit.addAnItem": "Adicionar uma entrada...",
  "containers.Edit.clickToJump": "Clique para saltar para a entrada",
  "containers.Edit.delete": "Apagar",
  "containers.Edit.editing": "Editando...",
  "containers.Edit.reset": "Restabelecer",
  "containers.Edit.returnList": "Retornar à lista",
  "containers.Edit.seeDetails": "Detalhes",
  "containers.Edit.submit": "Guardar",
  "containers.Home.introduction": "Para editar suas entradas, acesse o link específico no menu à esquerda. Esta extensão não tem uma maneira correcta de editar configurações e ainda está em desenvolvimento activo.",
  "containers.Home.pluginHeaderDescription": "Administre as suas entradas através de uma interface poderosa e bonita.",
  "containers.Home.pluginHeaderTitle": "Gestor de conteúdos",
  "containers.List.errorFetchRecords": "Erro",
  "containers.list.displayedFields": "Campos visíveis",
  "containers.SettingPage.attributes": "Campos de atributos",
  "containers.SettingPage.attributes.description": "Definir a ordem dos atributos",
  "containers.SettingPage.editSettings.description": "Agarre & solte os campos para criar o layout",
  "containers.SettingPage.editSettings.title": "Edite (configurações)",
  "containers.SettingPage.listSettings.title": "Lista (configurações)",
  "containers.SettingPage.relations": "Campos Relacionais",
  "containers.SettingsPage.Block.contentType.description": "Configurar configurações específicas",
  "containers.SettingsPage.Block.generalSettings.title": "Geral",
  "emptyAttributes.title": "Ainda não há campos",
  "error.attribute.key.taken": "Este valor já existe",
  "error.attribute.sameKeyAndName": "Não pode ser igual",
  "error.attribute.taken": "O nome deste campo já existe",
  "error.contentTypeName.taken": "Este nome já existe",
  "error.model.fetch": "Ocorreu um erro durante a configuração dos modelos de pesquisa.",
  "error.record.create": "Ocorreu um erro durante a criação de registo.",
  "error.record.delete": "Ocorreu um erro durante a exclusão do registo.",
  "error.record.fetch": "Ocorreu um erro durante o registo de pesquisa.",
  "error.record.update": "Ocorreu um erro durante a actualização do registo.",
  "error.records.count": "Ocorreu um erro durante a contagem de registos a pesquisar.",
  "error.records.fetch": "Ocorreu um erro durante os registos de busca.",
  "error.schema.generation": "Ocorreu um erro durante a geração de esquemas.",
  "error.validation.json": "Isto não corresponde com o formato JSON",
  "error.validation.max": "O valor é muito alto.",
  "error.validation.maxLength": "O valor é muito longo.",
  "error.validation.min": "O valor é muito baixo.",
  "error.validation.minLength": "O valor é muito curto.",
  "error.validation.minSupMax": "Não pode ser superior",
  "error.validation.regex": "Este valor não corresponde ao regex.",
  "error.validation.required": "O valor desta entrada é obrigatória.",
  "form.Input.bulkActions": "Activar acções em massa",
  "form.Input.defaultSort": "Ordenação por defeito",
  "form.Input.description": "Descrição",
  "form.Input.description.placeholder": "Nome a exibir no perfil",
  "form.Input.editable": "Campo editável",
  "form.Input.filters": "Activar filtros",
  "form.Input.label": "Legenda",
  "form.Input.label.inputDescription": "Este valor sobrepõe a legenda visível no cabeçalho da tabela",
  "form.Input.pageEntries": "Entradas por página",
  "form.Input.placeholder": "Preenchimento",
  "form.Input.placeholder.placeholder": "O meu valor espetacular",
  "form.Input.search": "Activar pesquisa",
  "form.Input.search.field": "Active a pesquisa neste campo",
  "form.Input.sort.field": "Active a ordenação neste campo",
  "notification.error.displayedFields": "Precisa de ter pelo menos um campo visível",
  "notification.error.relationship.fetch": "Ocorreu um erro durante a pesquisa da relação.",
  "notification.info.SettingPage.disableSort": "Precisa de ter pelo menos um atributo com ordenação activa",
  pageNotFound,
  "plugin.description.long": "Maneira rápida de ver, editar e excluir os dados da sua base de dados.",
  "plugin.description.short": "Maneira rápida de ver, editar e excluir os dados da sua base de dados.",
  "popUpWarning.bodyMessage.contentType.delete": "Tem a certeza de que pretende apagar esta entrada?",
  "popUpWarning.bodyMessage.contentType.delete.all": "Tem a certeza de que pretende apagar estas entradas?",
  "popUpWarning.warning.cancelAllSettings": "Tem a certeza de que quer cancelar as alterações?",
  "popUpWarning.warning.updateAllSettings": "Isto vai alterar todas as suas configurações",
  "success.record.delete": "Apagado",
  "success.record.save": "Guardado"
};
export {
  pt as default,
  pageNotFound
};
//# sourceMappingURL=pt-BsaFvS8--LPXEFMM7.js.map
