import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/pt-BR-C71iDxnh.mjs
var groups = "Grupos";
var models = "Tipos de Coleção";
var pageNotFound = "Página não encontrada";
var ptBR = {
  "App.schemas.data-loaded": "Os esquemas foram carregados com sucesso",
  "ListViewTable.relation-loaded": "Os relacionamentos foram carregados",
  "ListViewTable.relation-loading": "As relações estão carregando",
  "ListViewTable.relation-more": "Esta relação contém mais entidades do que as exibidas",
  "EditRelations.title": "Dados relacionais",
  "HeaderLayout.button.label-add-entry": "Criar novo registro",
  "api.id": "API ID",
  "components.AddFilterCTA.add": "Filtros",
  "components.AddFilterCTA.hide": "Filtros",
  "components.DragHandle-label": "Arrastar",
  "components.DraggableAttr.edit": "Clique para editar",
  "components.DraggableCard.delete.field": "Remover {item}",
  "components.DraggableCard.edit.field": "Editar {item}",
  "components.DraggableCard.move.field": "Mover {item}",
  "components.ListViewTable.row-line": "item {number}",
  "components.DynamicZone.ComponentPicker-label": "Selecione um componente",
  "components.DynamicZone.add-component": "Adicionar componente a {componentName}",
  "components.DynamicZone.delete-label": "Remover {name}",
  "components.DynamicZone.error-message": "O componente contém erro(s)",
  "components.DynamicZone.missing-components": "Há {number, plural, =0 {# componentes faltando} one {# componente faltando} other {# componentes faltando}}",
  "components.DynamicZone.move-down-label": "Mover pra cima",
  "components.DynamicZone.move-up-label": "Mover pra baixo",
  "components.DynamicZone.pick-compo": "Selecione um componente",
  "components.DynamicZone.required": "Um componente é necessário",
  "components.EmptyAttributesBlock.button": "Ir para página de configurações",
  "components.EmptyAttributesBlock.description": "Você pode alterar suas configurações",
  "components.FieldItem.linkToComponentLayout": "Definir layout do componente",
  "components.FieldSelect.label": "Adicionar um campo",
  "components.FilterOptions.button.apply": "Aplicar",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Aplicar",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Limpar tudo",
  "components.FiltersPickWrapper.PluginHeader.description": "Definir as condições a serem aplicadas para filtrar os registros",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filtros",
  "components.FiltersPickWrapper.hide": "Esconder",
  "components.LeftMenu.Search.label": "Procurar um tipo de conteúdo",
  "components.LeftMenu.collection-types": "Tipos de Coleção",
  "components.LeftMenu.single-types": "Tipos Singulares",
  "components.LimitSelect.itemsPerPage": "Registros por página",
  "components.NotAllowedInput.text": "Sem permissão para ver esse campo",
  "components.RepeatableComponent.error-message": "Um ou mais componentes contêm erros",
  "components.Search.placeholder": "Buscar registro...",
  "components.Select.draft-info-title": "Estado: Rascunho",
  "components.Select.publish-info-title": "Estado: Publicado",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Customizar visualização de edição.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Customizar visualização de lista.",
  "components.SettingsViewWrapper.pluginHeader.title": "Customizar visualização - {name}",
  "components.TableDelete.delete": "Remover tudo",
  "components.TableDelete.deleteSelected": "Remover selecionado",
  "components.TableDelete.label": "{number, plural, one {# registro} other {# registros}} selecionados",
  "components.TableEmpty.withFilters": "Nenhum {contentType} com os filtros aplicados...",
  "components.TableEmpty.withSearch": "Nenhum {contentType} encontrado na pesquisa ({search})...",
  "components.TableEmpty.withoutFilter": "Nenhum {contentType}...",
  "components.empty-repeatable": "Nenhum registro ainda. Clique no botão abaixo para adicionar um.",
  "components.notification.info.maximum-requirement": "Você atingiu o número máximo de campos",
  "components.notification.info.minimum-requirement": "Um campo foi criado para atender aos requisitos mínimos",
  "components.repeatable.reorder.error": "Um erro ocorreu ao reordenar o campo do seu componente. Por favor, tente novamente.",
  "components.reset-entry": "Reiniciar",
  "components.uid.apply": "aplicar",
  "components.uid.available": "Disponível",
  "components.uid.regenerate": "Regerar",
  "components.uid.suggested": "sugerido",
  "components.uid.unavailable": "Indisponível",
  "containers.Edit.Link.Layout": "Configurar o layout",
  "containers.Edit.Link.Model": "Editar o tipo de coleção",
  "containers.Edit.addAnItem": "Adicione um item...",
  "containers.Edit.clickToJump": "Clique para pular para o registro",
  "containers.Edit.delete": "Remover",
  "containers.Edit.delete-entry": "Remover este registro",
  "containers.Edit.editing": "Editando...",
  "containers.Edit.information": "Informação",
  "containers.Edit.information.by": "Por",
  "containers.Edit.information.created": "Criado",
  "containers.Edit.information.draftVersion": "versão rascunho",
  "containers.Edit.information.editing": "Editando",
  "containers.Edit.information.lastUpdate": "Última atualização",
  "containers.Edit.information.publishedVersion": "versão publicada",
  "containers.Edit.pluginHeader.title.new": "Criar um registro",
  "containers.Edit.reset": "Reiniciar",
  "containers.Edit.returnList": "Retornar à lista",
  "containers.Edit.seeDetails": "Detalhes",
  "containers.Edit.submit": "Salvar",
  "containers.EditSettingsView.modal-form.edit-field": "Editar o campo",
  "containers.EditView.add.new-entry": "Adicionar um registro",
  "containers.EditView.notification.errors": "O formulário contém erros",
  "containers.Home.introduction": "Para editar seus registros, acesse o link específico no menu à esquerda. Esta extensão não permite editar configurações, ainda está em desenvolvimento.",
  "containers.Home.pluginHeaderDescription": "Gerencie seus registros através de uma interface poderosa e elegante.",
  "containers.Home.pluginHeaderTitle": "Gestão de conteúdos",
  "containers.List.draft": "Rascunho",
  "containers.List.errorFetchRecords": "Erro",
  "containers.List.published": "Publicado",
  "containers.list.displayedFields": "Campos exibidos",
  "containers.list.items": "{number, plural, =0 {itens} one {item} other {itens}}",
  "containers.list.table-headers.publishedAt": "Estado",
  "containers.ListSettingsView.modal-form.edit-label": "Editar {fieldName}",
  "containers.SettingPage.add.field": "Criar outro campo",
  "containers.SettingPage.attributes": "Atributos",
  "containers.SettingPage.attributes.description": "Define a ordem dos atributos",
  "containers.SettingPage.editSettings.description": "Arraste e solte os campos para construir o layout",
  "containers.SettingPage.editSettings.entry.title": "Título do registro",
  "containers.SettingPage.editSettings.entry.title.description": "Defina o campo exibido do registro",
  "containers.SettingPage.editSettings.relation-field.description": "Defina o campo exibido nas visualizações de edição e lista",
  "containers.SettingPage.editSettings.title": "Editar (configurações)",
  "containers.SettingPage.layout": "Layout",
  "containers.SettingPage.listSettings.description": "Configurar as opções desse tipo de coleção",
  "containers.SettingPage.listSettings.title": "Lista (configurações)",
  "containers.SettingPage.pluginHeaderDescription": "Configurar as opções específicas desse tipo de coleção",
  "containers.SettingPage.settings": "Configurações",
  "containers.SettingPage.view": "Visualização",
  "containers.SettingViewModel.pluginHeader.title": "Gestão de Conteúdo - {name}",
  "containers.SettingsPage.Block.contentType.description": "Defina as configurações específicas",
  "containers.SettingsPage.Block.contentType.title": "Tipos de Coleção",
  "containers.SettingsPage.Block.generalSettings.description": "Configurar opções padrão para seus Tipos de Coleção",
  "containers.SettingsPage.Block.generalSettings.title": "Geral",
  "containers.SettingsPage.pluginHeaderDescription": "Configurar opções para todos os seus Tipos de Coleção e Grupos",
  "containers.SettingsView.list.subtitle": "Configurar layout e exibição dos seus Tipos de Coleção e Grupos",
  "containers.SettingsView.list.title": "Exibir configurações",
  "edit-settings-view.link-to-ctb.components": "Editar o componente",
  "edit-settings-view.link-to-ctb.content-types": "Editar o tipo de conteúdo",
  "emptyAttributes.button": "Ir para o criador de Tipo de Coleção",
  "emptyAttributes.description": "Adicione o primeiro campo ao seu Tipo de Coleção",
  "emptyAttributes.title": "Nenhum campo",
  "error.attribute.key.taken": "Este valor já existe",
  "error.attribute.sameKeyAndName": "Não pode ser igual",
  "error.attribute.taken": "O nome deste campo já existe",
  "error.contentTypeName.taken": "Este nome já existe",
  "error.model.fetch": "Ocorreu um erro durante a configuração dos modelos de busca.",
  "error.record.create": "Ocorreu um erro durante a criação de registro.",
  "error.record.delete": "Ocorreu um erro durante a remoção do registro.",
  "error.record.fetch": "Ocorreu um erro durante o registro de busca.",
  "error.record.update": "Ocorreu um erro durante a atualização do registro.",
  "error.records.count": "Ocorreu um erro durante a contagem de registros da buscar.",
  "error.records.fetch": "Ocorreu um erro durante os registros de busca.",
  "error.schema.generation": "Ocorreu um erro durante a geração dos esquemas.",
  "error.validation.json": "Isto não corresponde com o formato JSON",
  "error.validation.max": "O valor é muito alto.",
  "error.validation.maxLength": "O valor é muito logo.",
  "error.validation.min": "O valor é muito baixo.",
  "error.validation.minLength": "O valor é muito curto.",
  "error.validation.minSupMax": "Não pode ser superior",
  "error.validation.regex": "Este valor não corresponde ao regex.",
  "error.validation.required": "O valor deste registro é obrigatório.",
  "form.Input.bulkActions": "Habilitar ações em lote",
  "form.Input.defaultSort": "Atributo de ordenação padrão",
  "form.Input.description": "Descrição",
  "form.Input.description.placeholder": "Nome exibido no perfil",
  "form.Input.editable": "Campo editável",
  "form.Input.filters": "Habilitar filtros",
  "form.Input.label": "Rótulo",
  "form.Input.label.inputDescription": "Este valor substitui o rótulo apresentado no cabeçalho da tabela",
  "form.Input.pageEntries": "Entradas por página",
  "form.Input.pageEntries.inputDescription": "Nota: Voce pode redefinir esse valor na página de configuração dos Tipos de Coleção.",
  "form.Input.placeholder": "Placeholder",
  "form.Input.placeholder.placeholder": "Meu valor incrível",
  "form.Input.search": "Habilitar busca",
  "form.Input.search.field": "Habilitar busca neste campo",
  "form.Input.sort.field": "Habilitar ordenação neste campo",
  "form.Input.sort.order": "Ordenação padrão",
  "form.Input.wysiwyg": "Mostrar como WYSIWYG",
  "global.displayedFields": "Campos exibidos",
  groups,
  "groups.numbered": "Grupos ({number})",
  "header.name": "Conteúdo",
  "link-to-ctb": "Editar o modelo",
  models,
  "models.numbered": "Tipos de Coleção ({number})",
  "notification.error.displayedFields": "Você precisa ao menos um campo exibido",
  "notification.error.relationship.fetch": "Ocorreu um erro durante a busca do relacionamento.",
  "notification.info.SettingPage.disableSort": "Você precisa de um atributo com permissão de ordenação",
  "notification.info.minimumFields": "Você precisa ter pelo menos um campo exibido",
  "notification.upload.error": "Ocorreu um erro ao fazer upload dos seus arquivos",
  pageNotFound,
  "pages.ListView.header-subtitle": "{number, plural, =0 {# registros encontrados} one {# registro encontrado} other {# registros encontrados}}",
  "pages.NoContentType.button": "Criar seu primeiro Tipo de Conteúdo",
  "pages.NoContentType.text": "Você ainda não tem nenhum conteúdo. Recomendamos que você crie seu primeiro Tipo de Conteúdo.",
  "permissions.not-allowed.create": "Você não tem permissão para criar documentos",
  "permissions.not-allowed.update": "Você não tem permissão para ver esse documento",
  "plugin.description.long": "Maneira rápida de ver, editar e excluir os dados em seu banco de dados.",
  "plugin.description.short": "Maneira rápida de ver, editar e excluir os dados em seu banco de dados.",
  "popover.display-relations.label": "Exibir relacionamentos",
  "select.currently.selected": "{count} selecionado",
  "success.record.delete": "Removido",
  "success.record.publish": "Publicado",
  "success.record.save": "Salvo",
  "success.record.unpublish": "Despublicado",
  "utils.data-loaded": "{number, plural, =1 {O registro foi carregado} other {Os registros foram carregados}} com sucesso",
  "apiError.This attribute must be unique": "{field} deve ser único",
  "popUpWarning.warning.publish-question": "Você ainda quer publicar esse conteúdo?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Sim, publicar",
  "popUpwarning.warning.has-draft-relations.message": "<b>{count, plural, =0 { conteúdos relacionados não estão publicados} one { conteúdo relacionado não está publicado} other { conteúdos relacionados não estão publicados}}</b>.<br></br>Isso pode acarretar em links quebrados e erros em seu projeto."
};
export {
  ptBR as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=pt-BR-C71iDxnh-SBOAPGAI.js.map
