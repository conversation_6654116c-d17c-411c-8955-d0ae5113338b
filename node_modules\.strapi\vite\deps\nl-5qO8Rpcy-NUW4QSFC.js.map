{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/_chunks/nl-5qO8Rpcy.mjs"], "sourcesContent": ["const nl = {\n  \"BoundRoute.title\": \"Gebonden route naar\",\n  \"EditForm.inputSelect.description.role\": \"Het zal de nieuwe geautoriseerde gebruiker aan de geselecteerde rol verbinden.\",\n  \"EditForm.inputSelect.label.role\": \"Standaard rol voor geautoriseerde gebruikers\",\n  \"EditForm.inputToggle.description.email\": \"Zorg ervoor dat de gebruiker niet meerdere accounts kan maken met hetzelfde e-mailadres maar met verschillende leveranciers.\",\n  \"EditForm.inputToggle.description.email-confirmation\": \"<PERSON><PERSON> ingeschakeld (ON), ontvangen nieuw geregistreerde gebruikers een bevestigingsmail.\",\n  \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Na het bevestigen van je e-mail, kies naar waar je doorgestuurd zal worden.\",\n  \"EditForm.inputToggle.description.sign-up\": \"<PERSON><PERSON> uitgeschakeld (OFF), is registratie verboden. Niemand kan abonneren ongeacht de leverancier\",\n  \"EditForm.inputToggle.label.email\": \"Één account per e-mailadres.\",\n  \"EditForm.inputToggle.label.email-confirmation\": \"Schakel emailbevestiging in\",\n  \"EditForm.inputToggle.label.email-confirmation-redirection\": \"Doorstuur URL\",\n  \"EditForm.inputToggle.label.sign-up\": \"Registratie inschakelen\",\n  \"HeaderNav.link.advancedSettings\": \"Geavanceerde instellingen\",\n  \"HeaderNav.link.emailTemplates\": \"E-mail sjabloon\",\n  \"HeaderNav.link.providers\": \"Leveranciers\",\n  \"Plugin.permissions.plugins.description\": \"Voer alle toegestane acties in voor extensie {name}.\",\n  \"Plugins.header.description\": \"Alleen acties gekoppeld aan een route worden hieronder weergegeven.\",\n  \"Plugins.header.title\": \"Permissies\",\n  \"Policies.header.hint\": \"Selecteer de actie van de applicatie of de acties van de extensie en klik op het tandwiel icoontje om de gekoppelde route weer te geven\",\n  \"Policies.header.title\": \"Geavanceerde instellingen\",\n  \"PopUpForm.Email.email_templates.inputDescription\": \"Als je niet zeker weet hoe je variabelen moet gebruiken, {link}\",\n  \"PopUpForm.Email.options.from.email.label\": \"Afzender e-mail\",\n  \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Email.options.from.name.label\": \"Afzender naam\",\n  \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n  \"PopUpForm.Email.options.message.label\": \"Bericht\",\n  \"PopUpForm.Email.options.object.label\": \"Onderwerp\",\n  \"PopUpForm.Email.options.response_email.label\": \"Antwoord e-mail\",\n  \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Providers.enabled.description\": \"Als deze uitgeschakeld is kunnen gebruikers geen gebruik maken van deze leverancier.\",\n  \"PopUpForm.Providers.enabled.label\": \"Inschakelen\",\n  \"PopUpForm.Providers.key.label\": \"Client ID\",\n  \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.redirectURL.front-end.label\": \"De doorstuur URL voor jouw front-end app\",\n  \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n  \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n  \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n  \"PopUpForm.header.edit.email-templates\": \"E-mail sjablonen aanpassen\",\n  \"notification.success.submit\": \"Instellingen zijn geüpdatet\",\n  \"plugin.description.long\": \"Beveilig je API met een volledig authenticatie proces op JWT. Deze extensie komt ook met een ACL strategie welke ervoor zorgt dat je de permissies tussen groepen van gebruikers kan beheren.\",\n  \"plugin.description.short\": \"Beveilig je API met een volledig authenticatie proces op JWT\",\n  \"plugin.name\": \"Rollen & Permissies\"\n};\nexport {\n  nl as default\n};\n//# sourceMappingURL=nl-5qO8Rpcy.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACjB;", "names": []}