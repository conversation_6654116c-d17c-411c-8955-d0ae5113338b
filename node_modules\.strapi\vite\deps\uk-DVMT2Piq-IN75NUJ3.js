import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/upload/dist/_chunks/uk-DVMT2Piq.mjs
var uk = {
  "button.next": "Далі",
  "checkControl.crop-duplicate": "Зробити копію та обрізати",
  "checkControl.crop-original": "Обрізати оригінал",
  "control-card.add": "Додати",
  "control-card.cancel": "Скасувати",
  "control-card.copy-link": "Скопіювати посилання",
  "control-card.crop": "Обрізати",
  "control-card.download": "Завантажити",
  "control-card.edit": "Редагувати",
  "control-card.save": "Зберегти",
  "filter.add": "Додати фільтр",
  "form.button.replace-media": "Замінити медіа",
  "form.input.decription.file-alt": "Цей текст буде показаний якщо файл не може бути показаний",
  "form.input.label.file-alt": "Альтернативний текст (alt)",
  "form.input.label.file-caption": "Підпис",
  "form.input.label.file-name": "Назва файлу",
  "form.upload-url.error.url.invalid": "Одина URL-адреса неправильна",
  "form.upload-url.error.url.invalids": "{number} URL-адреси неправильні",
  "header.actions.upload-assets": "Завантажити файл",
  "header.content.assets": "{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 файл} other {# файлів}}",
  "input.button.label": "Перегляд файлів",
  "input.label-bold": "Перетягніть сюди",
  "input.label-normal": "щоб завантажити або",
  "input.placeholder": "Натисніть щоб вибрати файл або претягніть файл сюди",
  "input.url.description": "Кожен URL має бути з нового рядка.",
  "input.url.label": "URL-адреса",
  "list.assets-empty.subtitle": "Додайте свій перший файл.",
  "list.assets-empty.title": "Ще немає файлів",
  "list.assets-empty.title-withSearch": "Немає файлів, що відповідають застосованим фільтрам",
  "list.assets.type-not-allowed": "Цей тип файлу не дозволений.",
  "modal.file-details.date": "Дата",
  "modal.file-details.dimensions": "Розміри",
  "modal.file-details.extension": "Тип файлу",
  "modal.file-details.size": "Розмір",
  "modal.header.browse": "Завантаження файлу",
  "modal.header.file-detail": "Докладніше",
  "modal.header.pending-assets": "Очикувані файли",
  "modal.header.select-files": "Вибрані файли",
  "modal.nav.browse": "ереглянути",
  "modal.nav.computer": "З комп'ютера",
  "modal.nav.selected": "вібраний",
  "modal.nav.url": "Через URL",
  "modal.selected-list.sub-header-subtitle": "Перетягніть, щоб змінити порядок файлів в полі",
  "modal.upload-list.sub-header-subtitle": "Управляйте файлами, перш ніж додати їх до медіатеки",
  "modal.upload-list.sub-header.button": "Додати ще файлів",
  "plugin.description.long": "Управляйте медіафайлами.",
  "plugin.description.short": "Управляйте медіафайлами.",
  "plugin.name": "Медіатека",
  "search.placeholder": "Пошук файлів...",
  "settings.form.responsiveDimensions.description": "Автоматично буде створено кілька розмірів (великий, середній, малий) завантаженого файлу",
  "settings.form.responsiveDimensions.label": "Увімкнути responsive-friendly завантаження",
  "settings.form.sizeOptimization.label": "Увімкнути оптимізацію розміру (без втрати якості)",
  "settings.form.videoPreview.description": "Згенерується шостисекундне відео для попереднього перегляду (GIF)",
  "settings.form.videoPreview.label": "Попередній перегляд",
  "settings.header.label": "Медіатека - Налаштування",
  "settings.section.image.label": "Картинка",
  "settings.section.video.label": "Відео",
  "settings.sub-header.label": "Налаштуйте параметри медіатеки",
  "sort.created_at_asc": "Найдавніші завантаження",
  "sort.created_at_desc": "Останні завантаження",
  "sort.label": "Сортувати",
  "sort.name_asc": "За алфавітом (А до Я)",
  "sort.name_desc": "За зворотнім алфавітом (Z до А)",
  "sort.updated_at_asc": "Найдавніші оновлення",
  "sort.updated_at_desc": "Останні оновлення",
  "window.confirm.close-modal.file": "Ви впевнені? Ваші зміни будуть втрачені.",
  "window.confirm.close-modal.files": "Ви впевнені? Деякі файли все ще не завантажені."
};
export {
  uk as default
};
//# sourceMappingURL=uk-DVMT2Piq-IN75NUJ3.js.map
