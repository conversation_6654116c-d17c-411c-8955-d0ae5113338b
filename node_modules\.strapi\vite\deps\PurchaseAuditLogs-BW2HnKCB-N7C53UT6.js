import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$H
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  EmptyStateLayout,
  LinkButton,
  Main,
  useIntl
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$3t
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/PurchaseAuditLogs-BW2HnKCB.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PurchaseAuditLogs = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Layouts.Root, { children: (0, import_jsx_runtime.jsxs)(Main, { children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({ id: "global.auditLogs", defaultMessage: "Audit Logs" }),
        subtitle: formatMessage({
          id: "Settings.permissions.auditLogs.listview.header.subtitle",
          defaultMessage: "Logs of all the activities that happened in your environment"
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 10, paddingRight: 10, children: (0, import_jsx_runtime.jsx)(
      EmptyStateLayout,
      {
        icon: (0, import_jsx_runtime.jsx)(ForwardRef$H, { width: "16rem" }),
        content: formatMessage({
          id: "Settings.permissions.auditLogs.not-available",
          defaultMessage: "Audit Logs is only available as part of a paid plan. Upgrade to get a searchable and filterable display of all activities."
        }),
        action: (0, import_jsx_runtime.jsx)(
          LinkButton,
          {
            variant: "default",
            endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3t, {}),
            href: "https://strp.cc/45mbAdF",
            isExternal: true,
            target: "_blank",
            children: formatMessage({
              id: "global.learn-more",
              defaultMessage: "Learn more"
            })
          }
        )
      }
    ) })
  ] }) });
};
export {
  PurchaseAuditLogs
};
//# sourceMappingURL=PurchaseAuditLogs-BW2HnKCB-N7C53UT6.js.map
