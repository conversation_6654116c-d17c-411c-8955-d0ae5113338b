{"version": 3, "sources": ["../../../semver/functions/clean.js", "../../../semver/functions/inc.js", "../../../semver/functions/diff.js", "../../../semver/functions/major.js", "../../../semver/functions/minor.js", "../../../semver/functions/patch.js", "../../../semver/functions/prerelease.js", "../../../semver/functions/rcompare.js", "../../../semver/functions/compare-loose.js", "../../../semver/functions/compare-build.js", "../../../semver/functions/sort.js", "../../../semver/functions/rsort.js", "../../../semver/functions/gt.js", "../../../semver/functions/eq.js", "../../../semver/functions/neq.js", "../../../semver/functions/gte.js", "../../../semver/functions/lte.js", "../../../semver/functions/cmp.js", "../../../semver/functions/coerce.js", "../../../yallist/iterator.js", "../../../yallist/yallist.js", "../../../lru-cache/index.js", "../../../semver/classes/range.js", "../../../semver/classes/comparator.js", "../../../semver/functions/satisfies.js", "../../../semver/ranges/to-comparators.js", "../../../semver/ranges/max-satisfying.js", "../../../semver/ranges/min-satisfying.js", "../../../semver/ranges/min-version.js", "../../../semver/ranges/valid.js", "../../../semver/ranges/outside.js", "../../../semver/ranges/gtr.js", "../../../semver/ranges/ltr.js", "../../../semver/ranges/intersects.js", "../../../semver/ranges/simplify.js", "../../../semver/ranges/subset.js", "../../../semver/index.js", "../../../@strapi/admin/admin/src/hooks/useDebounce.ts", "../../../@strapi/admin/admin/src/pages/Marketplace/components/NpmPackagesFilters.tsx", "../../../@strapi/admin/admin/src/pages/Marketplace/components/NpmPackageCard.tsx", "../../../@strapi/admin/admin/src/pages/Marketplace/components/NpmPackagesGrid.tsx", "../../../@strapi/admin/admin/src/pages/Marketplace/components/PageHeader.tsx", "../../../@strapi/admin/admin/src/pages/Marketplace/components/OfflineLayout.tsx", "../../../@strapi/admin/admin/src/pages/Marketplace/components/SortSelect.tsx", "../../../@strapi/admin/admin/src/pages/Marketplace/hooks/useMarketplaceData.ts", "../../../@strapi/admin/admin/src/pages/Marketplace/hooks/useNavigatorOnline.ts", "../../../@strapi/admin/admin/src/pages/Marketplace/MarketplacePage.tsx"], "sourcesContent": ["const parse = require('./parse')\nconst clean = (version, options) => {\n  const s = parse(version.trim().replace(/^[=v]+/, ''), options)\n  return s ? s.version : null\n}\nmodule.exports = clean\n", "const SemVer = require('../classes/semver')\n\nconst inc = (version, release, options, identifier, identifierBase) => {\n  if (typeof (options) === 'string') {\n    identifierBase = identifier\n    identifier = options\n    options = undefined\n  }\n\n  try {\n    return new SemVer(\n      version instanceof SemVer ? version.version : version,\n      options\n    ).inc(release, identifier, identifierBase).version\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = inc\n", "const parse = require('./parse.js')\n\nconst diff = (version1, version2) => {\n  const v1 = parse(version1, null, true)\n  const v2 = parse(version2, null, true)\n  const comparison = v1.compare(v2)\n\n  if (comparison === 0) {\n    return null\n  }\n\n  const v1Higher = comparison > 0\n  const highVersion = v1Higher ? v1 : v2\n  const lowVersion = v1Higher ? v2 : v1\n  const highHasPre = !!highVersion.prerelease.length\n  const lowHasPre = !!lowVersion.prerelease.length\n\n  if (lowHasPre && !highHasPre) {\n    // Going from prerelease -> no prerelease requires some special casing\n\n    // If the low version has only a major, then it will always be a major\n    // Some examples:\n    // 1.0.0-1 -> 1.0.0\n    // 1.0.0-1 -> 1.1.1\n    // 1.0.0-1 -> 2.0.0\n    if (!lowVersion.patch && !lowVersion.minor) {\n      return 'major'\n    }\n\n    // Otherwise it can be determined by checking the high version\n\n    if (highVersion.patch) {\n      // anything higher than a patch bump would result in the wrong version\n      return 'patch'\n    }\n\n    if (highVersion.minor) {\n      // anything higher than a minor bump would result in the wrong version\n      return 'minor'\n    }\n\n    // bumping major/minor/patch all have same result\n    return 'major'\n  }\n\n  // add the `pre` prefix if we are going to a prerelease version\n  const prefix = highHasPre ? 'pre' : ''\n\n  if (v1.major !== v2.major) {\n    return prefix + 'major'\n  }\n\n  if (v1.minor !== v2.minor) {\n    return prefix + 'minor'\n  }\n\n  if (v1.patch !== v2.patch) {\n    return prefix + 'patch'\n  }\n\n  // high and low are preleases\n  return 'prerelease'\n}\n\nmodule.exports = diff\n", "const SemVer = require('../classes/semver')\nconst major = (a, loose) => new SemVer(a, loose).major\nmodule.exports = major\n", "const SemVer = require('../classes/semver')\nconst minor = (a, loose) => new SemVer(a, loose).minor\nmodule.exports = minor\n", "const SemVer = require('../classes/semver')\nconst patch = (a, loose) => new SemVer(a, loose).patch\nmodule.exports = patch\n", "const parse = require('./parse')\nconst prerelease = (version, options) => {\n  const parsed = parse(version, options)\n  return (parsed && parsed.prerelease.length) ? parsed.prerelease : null\n}\nmodule.exports = prerelease\n", "const compare = require('./compare')\nconst rcompare = (a, b, loose) => compare(b, a, loose)\nmodule.exports = rcompare\n", "const compare = require('./compare')\nconst compareLoose = (a, b) => compare(a, b, true)\nmodule.exports = compareLoose\n", "const SemVer = require('../classes/semver')\nconst compareBuild = (a, b, loose) => {\n  const versionA = new SemVer(a, loose)\n  const versionB = new SemVer(b, loose)\n  return versionA.compare(versionB) || versionA.compareBuild(versionB)\n}\nmodule.exports = compareBuild\n", "const compareBuild = require('./compare-build')\nconst sort = (list, loose) => list.sort((a, b) => compareBuild(a, b, loose))\nmodule.exports = sort\n", "const compareBuild = require('./compare-build')\nconst rsort = (list, loose) => list.sort((a, b) => compareBuild(b, a, loose))\nmodule.exports = rsort\n", "const compare = require('./compare')\nconst gt = (a, b, loose) => compare(a, b, loose) > 0\nmodule.exports = gt\n", "const compare = require('./compare')\nconst eq = (a, b, loose) => compare(a, b, loose) === 0\nmodule.exports = eq\n", "const compare = require('./compare')\nconst neq = (a, b, loose) => compare(a, b, loose) !== 0\nmodule.exports = neq\n", "const compare = require('./compare')\nconst gte = (a, b, loose) => compare(a, b, loose) >= 0\nmodule.exports = gte\n", "const compare = require('./compare')\nconst lte = (a, b, loose) => compare(a, b, loose) <= 0\nmodule.exports = lte\n", "const eq = require('./eq')\nconst neq = require('./neq')\nconst gt = require('./gt')\nconst gte = require('./gte')\nconst lt = require('./lt')\nconst lte = require('./lte')\n\nconst cmp = (a, op, b, loose) => {\n  switch (op) {\n    case '===':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a === b\n\n    case '!==':\n      if (typeof a === 'object') {\n        a = a.version\n      }\n      if (typeof b === 'object') {\n        b = b.version\n      }\n      return a !== b\n\n    case '':\n    case '=':\n    case '==':\n      return eq(a, b, loose)\n\n    case '!=':\n      return neq(a, b, loose)\n\n    case '>':\n      return gt(a, b, loose)\n\n    case '>=':\n      return gte(a, b, loose)\n\n    case '<':\n      return lt(a, b, loose)\n\n    case '<=':\n      return lte(a, b, loose)\n\n    default:\n      throw new TypeError(`Invalid operator: ${op}`)\n  }\n}\nmodule.exports = cmp\n", "const SemVer = require('../classes/semver')\nconst parse = require('./parse')\nconst { safeRe: re, t } = require('../internal/re')\n\nconst coerce = (version, options) => {\n  if (version instanceof SemVer) {\n    return version\n  }\n\n  if (typeof version === 'number') {\n    version = String(version)\n  }\n\n  if (typeof version !== 'string') {\n    return null\n  }\n\n  options = options || {}\n\n  let match = null\n  if (!options.rtl) {\n    match = version.match(re[t.COERCE])\n  } else {\n    // Find the right-most coercible string that does not share\n    // a terminus with a more left-ward coercible string.\n    // Eg, '1.2.3.4' wants to coerce '2.3.4', not '3.4' or '4'\n    //\n    // Walk through the string checking with a /g regexp\n    // Manually set the index so as to pick up overlapping matches.\n    // Stop when we get a match that ends at the string end, since no\n    // coercible string can be more right-ward without the same terminus.\n    let next\n    while ((next = re[t.COERCERTL].exec(version)) &&\n        (!match || match.index + match[0].length !== version.length)\n    ) {\n      if (!match ||\n            next.index + next[0].length !== match.index + match[0].length) {\n        match = next\n      }\n      re[t.COERCERTL].lastIndex = next.index + next[1].length + next[2].length\n    }\n    // leave it in a clean state\n    re[t.COERCERTL].lastIndex = -1\n  }\n\n  if (match === null) {\n    return null\n  }\n\n  return parse(`${match[2]}.${match[3] || '0'}.${match[4] || '0'}`, options)\n}\nmodule.exports = coerce\n", "'use strict'\nmodule.exports = function (<PERSON>llist) {\n  Yallist.prototype[Symbol.iterator] = function* () {\n    for (let walker = this.head; walker; walker = walker.next) {\n      yield walker.value\n    }\n  }\n}\n", "'use strict'\nmodule.exports = Yallist\n\nYallist.Node = Node\nYallist.create = Yallist\n\nfunction Yallist (list) {\n  var self = this\n  if (!(self instanceof Yallist)) {\n    self = new Yallist()\n  }\n\n  self.tail = null\n  self.head = null\n  self.length = 0\n\n  if (list && typeof list.forEach === 'function') {\n    list.forEach(function (item) {\n      self.push(item)\n    })\n  } else if (arguments.length > 0) {\n    for (var i = 0, l = arguments.length; i < l; i++) {\n      self.push(arguments[i])\n    }\n  }\n\n  return self\n}\n\nYallist.prototype.removeNode = function (node) {\n  if (node.list !== this) {\n    throw new Error('removing node which does not belong to this list')\n  }\n\n  var next = node.next\n  var prev = node.prev\n\n  if (next) {\n    next.prev = prev\n  }\n\n  if (prev) {\n    prev.next = next\n  }\n\n  if (node === this.head) {\n    this.head = next\n  }\n  if (node === this.tail) {\n    this.tail = prev\n  }\n\n  node.list.length--\n  node.next = null\n  node.prev = null\n  node.list = null\n\n  return next\n}\n\nYallist.prototype.unshiftNode = function (node) {\n  if (node === this.head) {\n    return\n  }\n\n  if (node.list) {\n    node.list.removeNode(node)\n  }\n\n  var head = this.head\n  node.list = this\n  node.next = head\n  if (head) {\n    head.prev = node\n  }\n\n  this.head = node\n  if (!this.tail) {\n    this.tail = node\n  }\n  this.length++\n}\n\nYallist.prototype.pushNode = function (node) {\n  if (node === this.tail) {\n    return\n  }\n\n  if (node.list) {\n    node.list.removeNode(node)\n  }\n\n  var tail = this.tail\n  node.list = this\n  node.prev = tail\n  if (tail) {\n    tail.next = node\n  }\n\n  this.tail = node\n  if (!this.head) {\n    this.head = node\n  }\n  this.length++\n}\n\nYallist.prototype.push = function () {\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    push(this, arguments[i])\n  }\n  return this.length\n}\n\nYallist.prototype.unshift = function () {\n  for (var i = 0, l = arguments.length; i < l; i++) {\n    unshift(this, arguments[i])\n  }\n  return this.length\n}\n\nYallist.prototype.pop = function () {\n  if (!this.tail) {\n    return undefined\n  }\n\n  var res = this.tail.value\n  this.tail = this.tail.prev\n  if (this.tail) {\n    this.tail.next = null\n  } else {\n    this.head = null\n  }\n  this.length--\n  return res\n}\n\nYallist.prototype.shift = function () {\n  if (!this.head) {\n    return undefined\n  }\n\n  var res = this.head.value\n  this.head = this.head.next\n  if (this.head) {\n    this.head.prev = null\n  } else {\n    this.tail = null\n  }\n  this.length--\n  return res\n}\n\nYallist.prototype.forEach = function (fn, thisp) {\n  thisp = thisp || this\n  for (var walker = this.head, i = 0; walker !== null; i++) {\n    fn.call(thisp, walker.value, i, this)\n    walker = walker.next\n  }\n}\n\nYallist.prototype.forEachReverse = function (fn, thisp) {\n  thisp = thisp || this\n  for (var walker = this.tail, i = this.length - 1; walker !== null; i--) {\n    fn.call(thisp, walker.value, i, this)\n    walker = walker.prev\n  }\n}\n\nYallist.prototype.get = function (n) {\n  for (var i = 0, walker = this.head; walker !== null && i < n; i++) {\n    // abort out of the list early if we hit a cycle\n    walker = walker.next\n  }\n  if (i === n && walker !== null) {\n    return walker.value\n  }\n}\n\nYallist.prototype.getReverse = function (n) {\n  for (var i = 0, walker = this.tail; walker !== null && i < n; i++) {\n    // abort out of the list early if we hit a cycle\n    walker = walker.prev\n  }\n  if (i === n && walker !== null) {\n    return walker.value\n  }\n}\n\nYallist.prototype.map = function (fn, thisp) {\n  thisp = thisp || this\n  var res = new Yallist()\n  for (var walker = this.head; walker !== null;) {\n    res.push(fn.call(thisp, walker.value, this))\n    walker = walker.next\n  }\n  return res\n}\n\nYallist.prototype.mapReverse = function (fn, thisp) {\n  thisp = thisp || this\n  var res = new Yallist()\n  for (var walker = this.tail; walker !== null;) {\n    res.push(fn.call(thisp, walker.value, this))\n    walker = walker.prev\n  }\n  return res\n}\n\nYallist.prototype.reduce = function (fn, initial) {\n  var acc\n  var walker = this.head\n  if (arguments.length > 1) {\n    acc = initial\n  } else if (this.head) {\n    walker = this.head.next\n    acc = this.head.value\n  } else {\n    throw new TypeError('Reduce of empty list with no initial value')\n  }\n\n  for (var i = 0; walker !== null; i++) {\n    acc = fn(acc, walker.value, i)\n    walker = walker.next\n  }\n\n  return acc\n}\n\nYallist.prototype.reduceReverse = function (fn, initial) {\n  var acc\n  var walker = this.tail\n  if (arguments.length > 1) {\n    acc = initial\n  } else if (this.tail) {\n    walker = this.tail.prev\n    acc = this.tail.value\n  } else {\n    throw new TypeError('Reduce of empty list with no initial value')\n  }\n\n  for (var i = this.length - 1; walker !== null; i--) {\n    acc = fn(acc, walker.value, i)\n    walker = walker.prev\n  }\n\n  return acc\n}\n\nYallist.prototype.toArray = function () {\n  var arr = new Array(this.length)\n  for (var i = 0, walker = this.head; walker !== null; i++) {\n    arr[i] = walker.value\n    walker = walker.next\n  }\n  return arr\n}\n\nYallist.prototype.toArrayReverse = function () {\n  var arr = new Array(this.length)\n  for (var i = 0, walker = this.tail; walker !== null; i++) {\n    arr[i] = walker.value\n    walker = walker.prev\n  }\n  return arr\n}\n\nYallist.prototype.slice = function (from, to) {\n  to = to || this.length\n  if (to < 0) {\n    to += this.length\n  }\n  from = from || 0\n  if (from < 0) {\n    from += this.length\n  }\n  var ret = new Yallist()\n  if (to < from || to < 0) {\n    return ret\n  }\n  if (from < 0) {\n    from = 0\n  }\n  if (to > this.length) {\n    to = this.length\n  }\n  for (var i = 0, walker = this.head; walker !== null && i < from; i++) {\n    walker = walker.next\n  }\n  for (; walker !== null && i < to; i++, walker = walker.next) {\n    ret.push(walker.value)\n  }\n  return ret\n}\n\nYallist.prototype.sliceReverse = function (from, to) {\n  to = to || this.length\n  if (to < 0) {\n    to += this.length\n  }\n  from = from || 0\n  if (from < 0) {\n    from += this.length\n  }\n  var ret = new Yallist()\n  if (to < from || to < 0) {\n    return ret\n  }\n  if (from < 0) {\n    from = 0\n  }\n  if (to > this.length) {\n    to = this.length\n  }\n  for (var i = this.length, walker = this.tail; walker !== null && i > to; i--) {\n    walker = walker.prev\n  }\n  for (; walker !== null && i > from; i--, walker = walker.prev) {\n    ret.push(walker.value)\n  }\n  return ret\n}\n\nYallist.prototype.splice = function (start, deleteCount, ...nodes) {\n  if (start > this.length) {\n    start = this.length - 1\n  }\n  if (start < 0) {\n    start = this.length + start;\n  }\n\n  for (var i = 0, walker = this.head; walker !== null && i < start; i++) {\n    walker = walker.next\n  }\n\n  var ret = []\n  for (var i = 0; walker && i < deleteCount; i++) {\n    ret.push(walker.value)\n    walker = this.removeNode(walker)\n  }\n  if (walker === null) {\n    walker = this.tail\n  }\n\n  if (walker !== this.head && walker !== this.tail) {\n    walker = walker.prev\n  }\n\n  for (var i = 0; i < nodes.length; i++) {\n    walker = insert(this, walker, nodes[i])\n  }\n  return ret;\n}\n\nYallist.prototype.reverse = function () {\n  var head = this.head\n  var tail = this.tail\n  for (var walker = head; walker !== null; walker = walker.prev) {\n    var p = walker.prev\n    walker.prev = walker.next\n    walker.next = p\n  }\n  this.head = tail\n  this.tail = head\n  return this\n}\n\nfunction insert (self, node, value) {\n  var inserted = node === self.head ?\n    new Node(value, null, node, self) :\n    new Node(value, node, node.next, self)\n\n  if (inserted.next === null) {\n    self.tail = inserted\n  }\n  if (inserted.prev === null) {\n    self.head = inserted\n  }\n\n  self.length++\n\n  return inserted\n}\n\nfunction push (self, item) {\n  self.tail = new Node(item, self.tail, null, self)\n  if (!self.head) {\n    self.head = self.tail\n  }\n  self.length++\n}\n\nfunction unshift (self, item) {\n  self.head = new Node(item, null, self.head, self)\n  if (!self.tail) {\n    self.tail = self.head\n  }\n  self.length++\n}\n\nfunction Node (value, prev, next, list) {\n  if (!(this instanceof Node)) {\n    return new Node(value, prev, next, list)\n  }\n\n  this.list = list\n  this.value = value\n\n  if (prev) {\n    prev.next = this\n    this.prev = prev\n  } else {\n    this.prev = null\n  }\n\n  if (next) {\n    next.prev = this\n    this.next = next\n  } else {\n    this.next = null\n  }\n}\n\ntry {\n  // add if support for Symbol.iterator is present\n  require('./iterator.js')(Yallist)\n} catch (er) {}\n", "'use strict'\n\n// A linked list to keep track of recently-used-ness\nconst Yallist = require('yallist')\n\nconst MAX = Symbol('max')\nconst LENGTH = Symbol('length')\nconst LENGTH_CALCULATOR = Symbol('lengthCalculator')\nconst ALLOW_STALE = Symbol('allowStale')\nconst MAX_AGE = Symbol('maxAge')\nconst DISPOSE = Symbol('dispose')\nconst NO_DISPOSE_ON_SET = Symbol('noDisposeOnSet')\nconst LRU_LIST = Symbol('lruList')\nconst CACHE = Symbol('cache')\nconst UPDATE_AGE_ON_GET = Symbol('updateAgeOnGet')\n\nconst naiveLength = () => 1\n\n// lruList is a yallist where the head is the youngest\n// item, and the tail is the oldest.  the list contains the Hit\n// objects as the entries.\n// Each Hit object has a reference to its Yallist.Node.  This\n// never changes.\n//\n// cache is a Map (or PseudoMap) that matches the keys to\n// the Yallist.Node object.\nclass LRUCache {\n  constructor (options) {\n    if (typeof options === 'number')\n      options = { max: options }\n\n    if (!options)\n      options = {}\n\n    if (options.max && (typeof options.max !== 'number' || options.max < 0))\n      throw new TypeError('max must be a non-negative number')\n    // Kind of weird to have a default max of Infinity, but oh well.\n    const max = this[MAX] = options.max || Infinity\n\n    const lc = options.length || naiveLength\n    this[LENGTH_CALCULATOR] = (typeof lc !== 'function') ? naiveLength : lc\n    this[ALLOW_STALE] = options.stale || false\n    if (options.maxAge && typeof options.maxAge !== 'number')\n      throw new TypeError('maxAge must be a number')\n    this[MAX_AGE] = options.maxAge || 0\n    this[DISPOSE] = options.dispose\n    this[NO_DISPOSE_ON_SET] = options.noDisposeOnSet || false\n    this[UPDATE_AGE_ON_GET] = options.updateAgeOnGet || false\n    this.reset()\n  }\n\n  // resize the cache when the max changes.\n  set max (mL) {\n    if (typeof mL !== 'number' || mL < 0)\n      throw new TypeError('max must be a non-negative number')\n\n    this[MAX] = mL || Infinity\n    trim(this)\n  }\n  get max () {\n    return this[MAX]\n  }\n\n  set allowStale (allowStale) {\n    this[ALLOW_STALE] = !!allowStale\n  }\n  get allowStale () {\n    return this[ALLOW_STALE]\n  }\n\n  set maxAge (mA) {\n    if (typeof mA !== 'number')\n      throw new TypeError('maxAge must be a non-negative number')\n\n    this[MAX_AGE] = mA\n    trim(this)\n  }\n  get maxAge () {\n    return this[MAX_AGE]\n  }\n\n  // resize the cache when the lengthCalculator changes.\n  set lengthCalculator (lC) {\n    if (typeof lC !== 'function')\n      lC = naiveLength\n\n    if (lC !== this[LENGTH_CALCULATOR]) {\n      this[LENGTH_CALCULATOR] = lC\n      this[LENGTH] = 0\n      this[LRU_LIST].forEach(hit => {\n        hit.length = this[LENGTH_CALCULATOR](hit.value, hit.key)\n        this[LENGTH] += hit.length\n      })\n    }\n    trim(this)\n  }\n  get lengthCalculator () { return this[LENGTH_CALCULATOR] }\n\n  get length () { return this[LENGTH] }\n  get itemCount () { return this[LRU_LIST].length }\n\n  rforEach (fn, thisp) {\n    thisp = thisp || this\n    for (let walker = this[LRU_LIST].tail; walker !== null;) {\n      const prev = walker.prev\n      forEachStep(this, fn, walker, thisp)\n      walker = prev\n    }\n  }\n\n  forEach (fn, thisp) {\n    thisp = thisp || this\n    for (let walker = this[LRU_LIST].head; walker !== null;) {\n      const next = walker.next\n      forEachStep(this, fn, walker, thisp)\n      walker = next\n    }\n  }\n\n  keys () {\n    return this[LRU_LIST].toArray().map(k => k.key)\n  }\n\n  values () {\n    return this[LRU_LIST].toArray().map(k => k.value)\n  }\n\n  reset () {\n    if (this[DISPOSE] &&\n        this[LRU_LIST] &&\n        this[LRU_LIST].length) {\n      this[LRU_LIST].forEach(hit => this[DISPOSE](hit.key, hit.value))\n    }\n\n    this[CACHE] = new Map() // hash of items by key\n    this[LRU_LIST] = new Yallist() // list of items in order of use recency\n    this[LENGTH] = 0 // length of items in the list\n  }\n\n  dump () {\n    return this[LRU_LIST].map(hit =>\n      isStale(this, hit) ? false : {\n        k: hit.key,\n        v: hit.value,\n        e: hit.now + (hit.maxAge || 0)\n      }).toArray().filter(h => h)\n  }\n\n  dumpLru () {\n    return this[LRU_LIST]\n  }\n\n  set (key, value, maxAge) {\n    maxAge = maxAge || this[MAX_AGE]\n\n    if (maxAge && typeof maxAge !== 'number')\n      throw new TypeError('maxAge must be a number')\n\n    const now = maxAge ? Date.now() : 0\n    const len = this[LENGTH_CALCULATOR](value, key)\n\n    if (this[CACHE].has(key)) {\n      if (len > this[MAX]) {\n        del(this, this[CACHE].get(key))\n        return false\n      }\n\n      const node = this[CACHE].get(key)\n      const item = node.value\n\n      // dispose of the old one before overwriting\n      // split out into 2 ifs for better coverage tracking\n      if (this[DISPOSE]) {\n        if (!this[NO_DISPOSE_ON_SET])\n          this[DISPOSE](key, item.value)\n      }\n\n      item.now = now\n      item.maxAge = maxAge\n      item.value = value\n      this[LENGTH] += len - item.length\n      item.length = len\n      this.get(key)\n      trim(this)\n      return true\n    }\n\n    const hit = new Entry(key, value, len, now, maxAge)\n\n    // oversized objects fall out of cache automatically.\n    if (hit.length > this[MAX]) {\n      if (this[DISPOSE])\n        this[DISPOSE](key, value)\n\n      return false\n    }\n\n    this[LENGTH] += hit.length\n    this[LRU_LIST].unshift(hit)\n    this[CACHE].set(key, this[LRU_LIST].head)\n    trim(this)\n    return true\n  }\n\n  has (key) {\n    if (!this[CACHE].has(key)) return false\n    const hit = this[CACHE].get(key).value\n    return !isStale(this, hit)\n  }\n\n  get (key) {\n    return get(this, key, true)\n  }\n\n  peek (key) {\n    return get(this, key, false)\n  }\n\n  pop () {\n    const node = this[LRU_LIST].tail\n    if (!node)\n      return null\n\n    del(this, node)\n    return node.value\n  }\n\n  del (key) {\n    del(this, this[CACHE].get(key))\n  }\n\n  load (arr) {\n    // reset the cache\n    this.reset()\n\n    const now = Date.now()\n    // A previous serialized cache has the most recent items first\n    for (let l = arr.length - 1; l >= 0; l--) {\n      const hit = arr[l]\n      const expiresAt = hit.e || 0\n      if (expiresAt === 0)\n        // the item was created without expiration in a non aged cache\n        this.set(hit.k, hit.v)\n      else {\n        const maxAge = expiresAt - now\n        // dont add already expired items\n        if (maxAge > 0) {\n          this.set(hit.k, hit.v, maxAge)\n        }\n      }\n    }\n  }\n\n  prune () {\n    this[CACHE].forEach((value, key) => get(this, key, false))\n  }\n}\n\nconst get = (self, key, doUse) => {\n  const node = self[CACHE].get(key)\n  if (node) {\n    const hit = node.value\n    if (isStale(self, hit)) {\n      del(self, node)\n      if (!self[ALLOW_STALE])\n        return undefined\n    } else {\n      if (doUse) {\n        if (self[UPDATE_AGE_ON_GET])\n          node.value.now = Date.now()\n        self[LRU_LIST].unshiftNode(node)\n      }\n    }\n    return hit.value\n  }\n}\n\nconst isStale = (self, hit) => {\n  if (!hit || (!hit.maxAge && !self[MAX_AGE]))\n    return false\n\n  const diff = Date.now() - hit.now\n  return hit.maxAge ? diff > hit.maxAge\n    : self[MAX_AGE] && (diff > self[MAX_AGE])\n}\n\nconst trim = self => {\n  if (self[LENGTH] > self[MAX]) {\n    for (let walker = self[LRU_LIST].tail;\n      self[LENGTH] > self[MAX] && walker !== null;) {\n      // We know that we're about to delete this one, and also\n      // what the next least recently used key will be, so just\n      // go ahead and set it now.\n      const prev = walker.prev\n      del(self, walker)\n      walker = prev\n    }\n  }\n}\n\nconst del = (self, node) => {\n  if (node) {\n    const hit = node.value\n    if (self[DISPOSE])\n      self[DISPOSE](hit.key, hit.value)\n\n    self[LENGTH] -= hit.length\n    self[CACHE].delete(hit.key)\n    self[LRU_LIST].removeNode(node)\n  }\n}\n\nclass Entry {\n  constructor (key, value, length, now, maxAge) {\n    this.key = key\n    this.value = value\n    this.length = length\n    this.now = now\n    this.maxAge = maxAge || 0\n  }\n}\n\nconst forEachStep = (self, fn, node, thisp) => {\n  let hit = node.value\n  if (isStale(self, hit)) {\n    del(self, node)\n    if (!self[ALLOW_STALE])\n      hit = undefined\n  }\n  if (hit)\n    fn.call(thisp, hit.value, hit.key, self)\n}\n\nmodule.exports = LRUCache\n", "// hoisted class for cyclic dependency\nclass Range {\n  constructor (range, options) {\n    options = parseOptions(options)\n\n    if (range instanceof Range) {\n      if (\n        range.loose === !!options.loose &&\n        range.includePrerelease === !!options.includePrerelease\n      ) {\n        return range\n      } else {\n        return new Range(range.raw, options)\n      }\n    }\n\n    if (range instanceof Comparator) {\n      // just put it in the set and return\n      this.raw = range.value\n      this.set = [[range]]\n      this.format()\n      return this\n    }\n\n    this.options = options\n    this.loose = !!options.loose\n    this.includePrerelease = !!options.includePrerelease\n\n    // First reduce all whitespace as much as possible so we do not have to rely\n    // on potentially slow regexes like \\s*. This is then stored and used for\n    // future error messages as well.\n    this.raw = range\n      .trim()\n      .split(/\\s+/)\n      .join(' ')\n\n    // First, split on ||\n    this.set = this.raw\n      .split('||')\n      // map the range to a 2d array of comparators\n      .map(r => this.parseRange(r.trim()))\n      // throw out any comparator lists that are empty\n      // this generally means that it was not a valid range, which is allowed\n      // in loose mode, but will still throw if the WHOLE range is invalid.\n      .filter(c => c.length)\n\n    if (!this.set.length) {\n      throw new TypeError(`Invalid SemVer Range: ${this.raw}`)\n    }\n\n    // if we have any that are not the null set, throw out null sets.\n    if (this.set.length > 1) {\n      // keep the first one, in case they're all null sets\n      const first = this.set[0]\n      this.set = this.set.filter(c => !isNullSet(c[0]))\n      if (this.set.length === 0) {\n        this.set = [first]\n      } else if (this.set.length > 1) {\n        // if we have any that are *, then the range is just *\n        for (const c of this.set) {\n          if (c.length === 1 && isAny(c[0])) {\n            this.set = [c]\n            break\n          }\n        }\n      }\n    }\n\n    this.format()\n  }\n\n  format () {\n    this.range = this.set\n      .map((comps) => comps.join(' ').trim())\n      .join('||')\n      .trim()\n    return this.range\n  }\n\n  toString () {\n    return this.range\n  }\n\n  parseRange (range) {\n    // memoize range parsing for performance.\n    // this is a very hot path, and fully deterministic.\n    const memoOpts =\n      (this.options.includePrerelease && FLAG_INCLUDE_PRERELEASE) |\n      (this.options.loose && FLAG_LOOSE)\n    const memoKey = memoOpts + ':' + range\n    const cached = cache.get(memoKey)\n    if (cached) {\n      return cached\n    }\n\n    const loose = this.options.loose\n    // `1.2.3 - 1.2.4` => `>=1.2.3 <=1.2.4`\n    const hr = loose ? re[t.HYPHENRANGELOOSE] : re[t.HYPHENRANGE]\n    range = range.replace(hr, hyphenReplace(this.options.includePrerelease))\n    debug('hyphen replace', range)\n\n    // `> 1.2.3 < 1.2.5` => `>1.2.3 <1.2.5`\n    range = range.replace(re[t.COMPARATORTRIM], comparatorTrimReplace)\n    debug('comparator trim', range)\n\n    // `~ 1.2.3` => `~1.2.3`\n    range = range.replace(re[t.TILDETRIM], tildeTrimReplace)\n    debug('tilde trim', range)\n\n    // `^ 1.2.3` => `^1.2.3`\n    range = range.replace(re[t.CARETTRIM], caretTrimReplace)\n    debug('caret trim', range)\n\n    // At this point, the range is completely trimmed and\n    // ready to be split into comparators.\n\n    let rangeList = range\n      .split(' ')\n      .map(comp => parseComparator(comp, this.options))\n      .join(' ')\n      .split(/\\s+/)\n      // >=0.0.0 is equivalent to *\n      .map(comp => replaceGTE0(comp, this.options))\n\n    if (loose) {\n      // in loose mode, throw out any that are not valid comparators\n      rangeList = rangeList.filter(comp => {\n        debug('loose invalid filter', comp, this.options)\n        return !!comp.match(re[t.COMPARATORLOOSE])\n      })\n    }\n    debug('range list', rangeList)\n\n    // if any comparators are the null set, then replace with JUST null set\n    // if more than one comparator, remove any * comparators\n    // also, don't include the same comparator more than once\n    const rangeMap = new Map()\n    const comparators = rangeList.map(comp => new Comparator(comp, this.options))\n    for (const comp of comparators) {\n      if (isNullSet(comp)) {\n        return [comp]\n      }\n      rangeMap.set(comp.value, comp)\n    }\n    if (rangeMap.size > 1 && rangeMap.has('')) {\n      rangeMap.delete('')\n    }\n\n    const result = [...rangeMap.values()]\n    cache.set(memoKey, result)\n    return result\n  }\n\n  intersects (range, options) {\n    if (!(range instanceof Range)) {\n      throw new TypeError('a Range is required')\n    }\n\n    return this.set.some((thisComparators) => {\n      return (\n        isSatisfiable(thisComparators, options) &&\n        range.set.some((rangeComparators) => {\n          return (\n            isSatisfiable(rangeComparators, options) &&\n            thisComparators.every((thisComparator) => {\n              return rangeComparators.every((rangeComparator) => {\n                return thisComparator.intersects(rangeComparator, options)\n              })\n            })\n          )\n        })\n      )\n    })\n  }\n\n  // if ANY of the sets match ALL of its comparators, then pass\n  test (version) {\n    if (!version) {\n      return false\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    for (let i = 0; i < this.set.length; i++) {\n      if (testSet(this.set[i], version, this.options)) {\n        return true\n      }\n    }\n    return false\n  }\n}\n\nmodule.exports = Range\n\nconst LRU = require('lru-cache')\nconst cache = new LRU({ max: 1000 })\n\nconst parseOptions = require('../internal/parse-options')\nconst Comparator = require('./comparator')\nconst debug = require('../internal/debug')\nconst SemVer = require('./semver')\nconst {\n  safeRe: re,\n  t,\n  comparatorTrimReplace,\n  tildeTrimReplace,\n  caretTrimReplace,\n} = require('../internal/re')\nconst { FLAG_INCLUDE_PRERELEASE, FLAG_LOOSE } = require('../internal/constants')\n\nconst isNullSet = c => c.value === '<0.0.0-0'\nconst isAny = c => c.value === ''\n\n// take a set of comparators and determine whether there\n// exists a version which can satisfy it\nconst isSatisfiable = (comparators, options) => {\n  let result = true\n  const remainingComparators = comparators.slice()\n  let testComparator = remainingComparators.pop()\n\n  while (result && remainingComparators.length) {\n    result = remainingComparators.every((otherComparator) => {\n      return testComparator.intersects(otherComparator, options)\n    })\n\n    testComparator = remainingComparators.pop()\n  }\n\n  return result\n}\n\n// comprised of xranges, tildes, stars, and gtlt's at this point.\n// already replaced the hyphen ranges\n// turn into a set of JUST comparators.\nconst parseComparator = (comp, options) => {\n  debug('comp', comp, options)\n  comp = replaceCarets(comp, options)\n  debug('caret', comp)\n  comp = replaceTildes(comp, options)\n  debug('tildes', comp)\n  comp = replaceXRanges(comp, options)\n  debug('xrange', comp)\n  comp = replaceStars(comp, options)\n  debug('stars', comp)\n  return comp\n}\n\nconst isX = id => !id || id.toLowerCase() === 'x' || id === '*'\n\n// ~, ~> --> * (any, kinda silly)\n// ~2, ~2.x, ~2.x.x, ~>2, ~>2.x ~>2.x.x --> >=2.0.0 <3.0.0-0\n// ~2.0, ~2.0.x, ~>2.0, ~>2.0.x --> >=2.0.0 <2.1.0-0\n// ~1.2, ~1.2.x, ~>1.2, ~>1.2.x --> >=1.2.0 <1.3.0-0\n// ~1.2.3, ~>1.2.3 --> >=1.2.3 <1.3.0-0\n// ~1.2.0, ~>1.2.0 --> >=1.2.0 <1.3.0-0\n// ~0.0.1 --> >=0.0.1 <0.1.0-0\nconst replaceTildes = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceTilde(c, options))\n    .join(' ')\n}\n\nconst replaceTilde = (comp, options) => {\n  const r = options.loose ? re[t.TILDELOOSE] : re[t.TILDE]\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('tilde', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0 <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      // ~1.2 == >=1.2.0 <1.3.0-0\n      ret = `>=${M}.${m}.0 <${M}.${+m + 1}.0-0`\n    } else if (pr) {\n      debug('replaceTilde pr', pr)\n      ret = `>=${M}.${m}.${p}-${pr\n      } <${M}.${+m + 1}.0-0`\n    } else {\n      // ~1.2.3 == >=1.2.3 <1.3.0-0\n      ret = `>=${M}.${m}.${p\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('tilde return', ret)\n    return ret\n  })\n}\n\n// ^ --> * (any, kinda silly)\n// ^2, ^2.x, ^2.x.x --> >=2.0.0 <3.0.0-0\n// ^2.0, ^2.0.x --> >=2.0.0 <3.0.0-0\n// ^1.2, ^1.2.x --> >=1.2.0 <2.0.0-0\n// ^1.2.3 --> >=1.2.3 <2.0.0-0\n// ^1.2.0 --> >=1.2.0 <2.0.0-0\n// ^0.0.1 --> >=0.0.1 <0.0.2-0\n// ^0.1.0 --> >=0.1.0 <0.2.0-0\nconst replaceCarets = (comp, options) => {\n  return comp\n    .trim()\n    .split(/\\s+/)\n    .map((c) => replaceCaret(c, options))\n    .join(' ')\n}\n\nconst replaceCaret = (comp, options) => {\n  debug('caret', comp, options)\n  const r = options.loose ? re[t.CARETLOOSE] : re[t.CARET]\n  const z = options.includePrerelease ? '-0' : ''\n  return comp.replace(r, (_, M, m, p, pr) => {\n    debug('caret', comp, _, M, m, p, pr)\n    let ret\n\n    if (isX(M)) {\n      ret = ''\n    } else if (isX(m)) {\n      ret = `>=${M}.0.0${z} <${+M + 1}.0.0-0`\n    } else if (isX(p)) {\n      if (M === '0') {\n        ret = `>=${M}.${m}.0${z} <${M}.${+m + 1}.0-0`\n      } else {\n        ret = `>=${M}.${m}.0${z} <${+M + 1}.0.0-0`\n      }\n    } else if (pr) {\n      debug('replaceCaret pr', pr)\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p}-${pr\n          } <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p}-${pr\n        } <${+M + 1}.0.0-0`\n      }\n    } else {\n      debug('no pr')\n      if (M === '0') {\n        if (m === '0') {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${m}.${+p + 1}-0`\n        } else {\n          ret = `>=${M}.${m}.${p\n          }${z} <${M}.${+m + 1}.0-0`\n        }\n      } else {\n        ret = `>=${M}.${m}.${p\n        } <${+M + 1}.0.0-0`\n      }\n    }\n\n    debug('caret return', ret)\n    return ret\n  })\n}\n\nconst replaceXRanges = (comp, options) => {\n  debug('replaceXRanges', comp, options)\n  return comp\n    .split(/\\s+/)\n    .map((c) => replaceXRange(c, options))\n    .join(' ')\n}\n\nconst replaceXRange = (comp, options) => {\n  comp = comp.trim()\n  const r = options.loose ? re[t.XRANGELOOSE] : re[t.XRANGE]\n  return comp.replace(r, (ret, gtlt, M, m, p, pr) => {\n    debug('xRange', comp, ret, gtlt, M, m, p, pr)\n    const xM = isX(M)\n    const xm = xM || isX(m)\n    const xp = xm || isX(p)\n    const anyX = xp\n\n    if (gtlt === '=' && anyX) {\n      gtlt = ''\n    }\n\n    // if we're including prereleases in the match, then we need\n    // to fix this to -0, the lowest possible prerelease value\n    pr = options.includePrerelease ? '-0' : ''\n\n    if (xM) {\n      if (gtlt === '>' || gtlt === '<') {\n        // nothing is allowed\n        ret = '<0.0.0-0'\n      } else {\n        // nothing is forbidden\n        ret = '*'\n      }\n    } else if (gtlt && anyX) {\n      // we know patch is an x, because we have any x at all.\n      // replace X with 0\n      if (xm) {\n        m = 0\n      }\n      p = 0\n\n      if (gtlt === '>') {\n        // >1 => >=2.0.0\n        // >1.2 => >=1.3.0\n        gtlt = '>='\n        if (xm) {\n          M = +M + 1\n          m = 0\n          p = 0\n        } else {\n          m = +m + 1\n          p = 0\n        }\n      } else if (gtlt === '<=') {\n        // <=0.7.x is actually <0.8.0, since any 0.7.x should\n        // pass.  Similarly, <=7.x is actually <8.0.0, etc.\n        gtlt = '<'\n        if (xm) {\n          M = +M + 1\n        } else {\n          m = +m + 1\n        }\n      }\n\n      if (gtlt === '<') {\n        pr = '-0'\n      }\n\n      ret = `${gtlt + M}.${m}.${p}${pr}`\n    } else if (xm) {\n      ret = `>=${M}.0.0${pr} <${+M + 1}.0.0-0`\n    } else if (xp) {\n      ret = `>=${M}.${m}.0${pr\n      } <${M}.${+m + 1}.0-0`\n    }\n\n    debug('xRange return', ret)\n\n    return ret\n  })\n}\n\n// Because * is AND-ed with everything else in the comparator,\n// and '' means \"any version\", just remove the *s entirely.\nconst replaceStars = (comp, options) => {\n  debug('replaceStars', comp, options)\n  // Looseness is ignored here.  star is always as loose as it gets!\n  return comp\n    .trim()\n    .replace(re[t.STAR], '')\n}\n\nconst replaceGTE0 = (comp, options) => {\n  debug('replaceGTE0', comp, options)\n  return comp\n    .trim()\n    .replace(re[options.includePrerelease ? t.GTE0PRE : t.GTE0], '')\n}\n\n// This function is passed to string.replace(re[t.HYPHENRANGE])\n// M, m, patch, prerelease, build\n// 1.2 - 3.4.5 => >=1.2.0 <=3.4.5\n// 1.2.3 - 3.4 => >=1.2.0 <3.5.0-0 Any 3.4.x will do\n// 1.2 - 3.4 => >=1.2.0 <3.5.0-0\nconst hyphenReplace = incPr => ($0,\n  from, fM, fm, fp, fpr, fb,\n  to, tM, tm, tp, tpr, tb) => {\n  if (isX(fM)) {\n    from = ''\n  } else if (isX(fm)) {\n    from = `>=${fM}.0.0${incPr ? '-0' : ''}`\n  } else if (isX(fp)) {\n    from = `>=${fM}.${fm}.0${incPr ? '-0' : ''}`\n  } else if (fpr) {\n    from = `>=${from}`\n  } else {\n    from = `>=${from}${incPr ? '-0' : ''}`\n  }\n\n  if (isX(tM)) {\n    to = ''\n  } else if (isX(tm)) {\n    to = `<${+tM + 1}.0.0-0`\n  } else if (isX(tp)) {\n    to = `<${tM}.${+tm + 1}.0-0`\n  } else if (tpr) {\n    to = `<=${tM}.${tm}.${tp}-${tpr}`\n  } else if (incPr) {\n    to = `<${tM}.${tm}.${+tp + 1}-0`\n  } else {\n    to = `<=${to}`\n  }\n\n  return `${from} ${to}`.trim()\n}\n\nconst testSet = (set, version, options) => {\n  for (let i = 0; i < set.length; i++) {\n    if (!set[i].test(version)) {\n      return false\n    }\n  }\n\n  if (version.prerelease.length && !options.includePrerelease) {\n    // Find the set of versions that are allowed to have prereleases\n    // For example, ^1.2.3-pr.1 desugars to >=1.2.3-pr.1 <2.0.0\n    // That should allow `1.2.3-pr.2` to pass.\n    // However, `1.2.4-alpha.notready` should NOT be allowed,\n    // even though it's within the range set by the comparators.\n    for (let i = 0; i < set.length; i++) {\n      debug(set[i].semver)\n      if (set[i].semver === Comparator.ANY) {\n        continue\n      }\n\n      if (set[i].semver.prerelease.length > 0) {\n        const allowed = set[i].semver\n        if (allowed.major === version.major &&\n            allowed.minor === version.minor &&\n            allowed.patch === version.patch) {\n          return true\n        }\n      }\n    }\n\n    // Version has a -pre, but it's not one of the ones we like.\n    return false\n  }\n\n  return true\n}\n", "const ANY = Symbol('SemVer ANY')\n// hoisted class for cyclic dependency\nclass Comparator {\n  static get ANY () {\n    return ANY\n  }\n\n  constructor (comp, options) {\n    options = parseOptions(options)\n\n    if (comp instanceof Comparator) {\n      if (comp.loose === !!options.loose) {\n        return comp\n      } else {\n        comp = comp.value\n      }\n    }\n\n    comp = comp.trim().split(/\\s+/).join(' ')\n    debug('comparator', comp, options)\n    this.options = options\n    this.loose = !!options.loose\n    this.parse(comp)\n\n    if (this.semver === ANY) {\n      this.value = ''\n    } else {\n      this.value = this.operator + this.semver.version\n    }\n\n    debug('comp', this)\n  }\n\n  parse (comp) {\n    const r = this.options.loose ? re[t.COMPARATORLOOSE] : re[t.COMPARATOR]\n    const m = comp.match(r)\n\n    if (!m) {\n      throw new TypeError(`Invalid comparator: ${comp}`)\n    }\n\n    this.operator = m[1] !== undefined ? m[1] : ''\n    if (this.operator === '=') {\n      this.operator = ''\n    }\n\n    // if it literally is just '>' or '' then allow anything.\n    if (!m[2]) {\n      this.semver = ANY\n    } else {\n      this.semver = new SemVer(m[2], this.options.loose)\n    }\n  }\n\n  toString () {\n    return this.value\n  }\n\n  test (version) {\n    debug('Comparator.test', version, this.options.loose)\n\n    if (this.semver === ANY || version === ANY) {\n      return true\n    }\n\n    if (typeof version === 'string') {\n      try {\n        version = new SemVer(version, this.options)\n      } catch (er) {\n        return false\n      }\n    }\n\n    return cmp(version, this.operator, this.semver, this.options)\n  }\n\n  intersects (comp, options) {\n    if (!(comp instanceof Comparator)) {\n      throw new TypeError('a Comparator is required')\n    }\n\n    if (this.operator === '') {\n      if (this.value === '') {\n        return true\n      }\n      return new Range(comp.value, options).test(this.value)\n    } else if (comp.operator === '') {\n      if (comp.value === '') {\n        return true\n      }\n      return new Range(this.value, options).test(comp.semver)\n    }\n\n    options = parseOptions(options)\n\n    // Special cases where nothing can possibly be lower\n    if (options.includePrerelease &&\n      (this.value === '<0.0.0-0' || comp.value === '<0.0.0-0')) {\n      return false\n    }\n    if (!options.includePrerelease &&\n      (this.value.startsWith('<0.0.0') || comp.value.startsWith('<0.0.0'))) {\n      return false\n    }\n\n    // Same direction increasing (> or >=)\n    if (this.operator.startsWith('>') && comp.operator.startsWith('>')) {\n      return true\n    }\n    // Same direction decreasing (< or <=)\n    if (this.operator.startsWith('<') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // same SemVer and both sides are inclusive (<= or >=)\n    if (\n      (this.semver.version === comp.semver.version) &&\n      this.operator.includes('=') && comp.operator.includes('=')) {\n      return true\n    }\n    // opposite directions less than\n    if (cmp(this.semver, '<', comp.semver, options) &&\n      this.operator.startsWith('>') && comp.operator.startsWith('<')) {\n      return true\n    }\n    // opposite directions greater than\n    if (cmp(this.semver, '>', comp.semver, options) &&\n      this.operator.startsWith('<') && comp.operator.startsWith('>')) {\n      return true\n    }\n    return false\n  }\n}\n\nmodule.exports = Comparator\n\nconst parseOptions = require('../internal/parse-options')\nconst { safeRe: re, t } = require('../internal/re')\nconst cmp = require('../functions/cmp')\nconst debug = require('../internal/debug')\nconst SemVer = require('./semver')\nconst Range = require('./range')\n", "const Range = require('../classes/range')\nconst satisfies = (version, range, options) => {\n  try {\n    range = new Range(range, options)\n  } catch (er) {\n    return false\n  }\n  return range.test(version)\n}\nmodule.exports = satisfies\n", "const Range = require('../classes/range')\n\n// Mostly just for testing and legacy API reasons\nconst toComparators = (range, options) =>\n  new Range(range, options).set\n    .map(comp => comp.map(c => c.value).join(' ').trim().split(' '))\n\nmodule.exports = toComparators\n", "const SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\n\nconst maxSatisfying = (versions, range, options) => {\n  let max = null\n  let maxSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!max || maxSV.compare(v) === -1) {\n        // compare(max, v, true)\n        max = v\n        maxSV = new SemVer(max, options)\n      }\n    }\n  })\n  return max\n}\nmodule.exports = maxSatisfying\n", "const SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\nconst minSatisfying = (versions, range, options) => {\n  let min = null\n  let minSV = null\n  let rangeObj = null\n  try {\n    rangeObj = new Range(range, options)\n  } catch (er) {\n    return null\n  }\n  versions.forEach((v) => {\n    if (rangeObj.test(v)) {\n      // satisfies(v, range, options)\n      if (!min || minSV.compare(v) === 1) {\n        // compare(min, v, true)\n        min = v\n        minSV = new SemVer(min, options)\n      }\n    }\n  })\n  return min\n}\nmodule.exports = minSatisfying\n", "const SemVer = require('../classes/semver')\nconst Range = require('../classes/range')\nconst gt = require('../functions/gt')\n\nconst minVersion = (range, loose) => {\n  range = new Range(range, loose)\n\n  let minver = new SemVer('0.0.0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = new SemVer('0.0.0-0')\n  if (range.test(minver)) {\n    return minver\n  }\n\n  minver = null\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let setMin = null\n    comparators.forEach((comparator) => {\n      // Clone to avoid manipulating the comparator's semver object.\n      const compver = new SemVer(comparator.semver.version)\n      switch (comparator.operator) {\n        case '>':\n          if (compver.prerelease.length === 0) {\n            compver.patch++\n          } else {\n            compver.prerelease.push(0)\n          }\n          compver.raw = compver.format()\n          /* fallthrough */\n        case '':\n        case '>=':\n          if (!setMin || gt(compver, setMin)) {\n            setMin = compver\n          }\n          break\n        case '<':\n        case '<=':\n          /* Ignore maximum versions */\n          break\n        /* istanbul ignore next */\n        default:\n          throw new Error(`Unexpected operation: ${comparator.operator}`)\n      }\n    })\n    if (setMin && (!minver || gt(minver, setMin))) {\n      minver = setMin\n    }\n  }\n\n  if (minver && range.test(minver)) {\n    return minver\n  }\n\n  return null\n}\nmodule.exports = minVersion\n", "const Range = require('../classes/range')\nconst validRange = (range, options) => {\n  try {\n    // Return '*' instead of '' so that truthiness works.\n    // This will throw if it's invalid anyway\n    return new Range(range, options).range || '*'\n  } catch (er) {\n    return null\n  }\n}\nmodule.exports = validRange\n", "const SemVer = require('../classes/semver')\nconst Comparator = require('../classes/comparator')\nconst { ANY } = Comparator\nconst Range = require('../classes/range')\nconst satisfies = require('../functions/satisfies')\nconst gt = require('../functions/gt')\nconst lt = require('../functions/lt')\nconst lte = require('../functions/lte')\nconst gte = require('../functions/gte')\n\nconst outside = (version, range, hilo, options) => {\n  version = new SemVer(version, options)\n  range = new Range(range, options)\n\n  let gtfn, ltefn, ltfn, comp, ecomp\n  switch (hilo) {\n    case '>':\n      gtfn = gt\n      ltefn = lte\n      ltfn = lt\n      comp = '>'\n      ecomp = '>='\n      break\n    case '<':\n      gtfn = lt\n      ltefn = gte\n      ltfn = gt\n      comp = '<'\n      ecomp = '<='\n      break\n    default:\n      throw new TypeError('Must provide a hilo val of \"<\" or \">\"')\n  }\n\n  // If it satisfies the range it is not outside\n  if (satisfies(version, range, options)) {\n    return false\n  }\n\n  // From now on, variable terms are as if we're in \"gtr\" mode.\n  // but note that everything is flipped for the \"ltr\" function.\n\n  for (let i = 0; i < range.set.length; ++i) {\n    const comparators = range.set[i]\n\n    let high = null\n    let low = null\n\n    comparators.forEach((comparator) => {\n      if (comparator.semver === ANY) {\n        comparator = new Comparator('>=0.0.0')\n      }\n      high = high || comparator\n      low = low || comparator\n      if (gtfn(comparator.semver, high.semver, options)) {\n        high = comparator\n      } else if (ltfn(comparator.semver, low.semver, options)) {\n        low = comparator\n      }\n    })\n\n    // If the edge version comparator has a operator then our version\n    // isn't outside it\n    if (high.operator === comp || high.operator === ecomp) {\n      return false\n    }\n\n    // If the lowest version comparator has an operator and our version\n    // is less than it then it isn't higher than the range\n    if ((!low.operator || low.operator === comp) &&\n        ltefn(version, low.semver)) {\n      return false\n    } else if (low.operator === ecomp && ltfn(version, low.semver)) {\n      return false\n    }\n  }\n  return true\n}\n\nmodule.exports = outside\n", "// Determine if version is greater than all the versions possible in the range.\nconst outside = require('./outside')\nconst gtr = (version, range, options) => outside(version, range, '>', options)\nmodule.exports = gtr\n", "const outside = require('./outside')\n// Determine if version is less than all the versions possible in the range\nconst ltr = (version, range, options) => outside(version, range, '<', options)\nmodule.exports = ltr\n", "const Range = require('../classes/range')\nconst intersects = (r1, r2, options) => {\n  r1 = new Range(r1, options)\n  r2 = new Range(r2, options)\n  return r1.intersects(r2, options)\n}\nmodule.exports = intersects\n", "// given a set of versions and a range, create a \"simplified\" range\n// that includes the same versions that the original range does\n// If the original range is shorter than the simplified one, return that.\nconst satisfies = require('../functions/satisfies.js')\nconst compare = require('../functions/compare.js')\nmodule.exports = (versions, range, options) => {\n  const set = []\n  let first = null\n  let prev = null\n  const v = versions.sort((a, b) => compare(a, b, options))\n  for (const version of v) {\n    const included = satisfies(version, range, options)\n    if (included) {\n      prev = version\n      if (!first) {\n        first = version\n      }\n    } else {\n      if (prev) {\n        set.push([first, prev])\n      }\n      prev = null\n      first = null\n    }\n  }\n  if (first) {\n    set.push([first, null])\n  }\n\n  const ranges = []\n  for (const [min, max] of set) {\n    if (min === max) {\n      ranges.push(min)\n    } else if (!max && min === v[0]) {\n      ranges.push('*')\n    } else if (!max) {\n      ranges.push(`>=${min}`)\n    } else if (min === v[0]) {\n      ranges.push(`<=${max}`)\n    } else {\n      ranges.push(`${min} - ${max}`)\n    }\n  }\n  const simplified = ranges.join(' || ')\n  const original = typeof range.raw === 'string' ? range.raw : String(range)\n  return simplified.length < original.length ? simplified : range\n}\n", "const Range = require('../classes/range.js')\nconst Comparator = require('../classes/comparator.js')\nconst { ANY } = Comparator\nconst satisfies = require('../functions/satisfies.js')\nconst compare = require('../functions/compare.js')\n\n// Complex range `r1 || r2 || ...` is a subset of `R1 || R2 || ...` iff:\n// - Every simple range `r1, r2, ...` is a null set, OR\n// - Every simple range `r1, r2, ...` which is not a null set is a subset of\n//   some `R1, R2, ...`\n//\n// Simple range `c1 c2 ...` is a subset of simple range `C1 C2 ...` iff:\n// - If c is only the ANY comparator\n//   - If C is only the ANY comparator, return true\n//   - Else if in prerelease mode, return false\n//   - else replace c with `[>=0.0.0]`\n// - If C is only the ANY comparator\n//   - if in prerelease mode, return true\n//   - else replace C with `[>=0.0.0]`\n// - Let EQ be the set of = comparators in c\n// - If EQ is more than one, return true (null set)\n// - Let GT be the highest > or >= comparator in c\n// - Let LT be the lowest < or <= comparator in c\n// - If GT and LT, and GT.semver > LT.semver, return true (null set)\n// - If any C is a = range, and GT or LT are set, return false\n// - If EQ\n//   - If GT, and EQ does not satisfy GT, return true (null set)\n//   - If LT, and EQ does not satisfy LT, return true (null set)\n//   - If EQ satisfies every C, return true\n//   - Else return false\n// - If GT\n//   - If GT.semver is lower than any > or >= comp in C, return false\n//   - If GT is >=, and GT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the GT.semver tuple, return false\n// - If LT\n//   - If LT.semver is greater than any < or <= comp in C, return false\n//   - If LT is <=, and LT.semver does not satisfy every C, return false\n//   - If GT.semver has a prerelease, and not in prerelease mode\n//     - If no C has a prerelease and the LT.semver tuple, return false\n// - Else return true\n\nconst subset = (sub, dom, options = {}) => {\n  if (sub === dom) {\n    return true\n  }\n\n  sub = new Range(sub, options)\n  dom = new Range(dom, options)\n  let sawNonNull = false\n\n  OUTER: for (const simpleSub of sub.set) {\n    for (const simpleDom of dom.set) {\n      const isSub = simpleSubset(simpleSub, simpleDom, options)\n      sawNonNull = sawNonNull || isSub !== null\n      if (isSub) {\n        continue OUTER\n      }\n    }\n    // the null set is a subset of everything, but null simple ranges in\n    // a complex range should be ignored.  so if we saw a non-null range,\n    // then we know this isn't a subset, but if EVERY simple range was null,\n    // then it is a subset.\n    if (sawNonNull) {\n      return false\n    }\n  }\n  return true\n}\n\nconst minimumVersionWithPreRelease = [new Comparator('>=0.0.0-0')]\nconst minimumVersion = [new Comparator('>=0.0.0')]\n\nconst simpleSubset = (sub, dom, options) => {\n  if (sub === dom) {\n    return true\n  }\n\n  if (sub.length === 1 && sub[0].semver === ANY) {\n    if (dom.length === 1 && dom[0].semver === ANY) {\n      return true\n    } else if (options.includePrerelease) {\n      sub = minimumVersionWithPreRelease\n    } else {\n      sub = minimumVersion\n    }\n  }\n\n  if (dom.length === 1 && dom[0].semver === ANY) {\n    if (options.includePrerelease) {\n      return true\n    } else {\n      dom = minimumVersion\n    }\n  }\n\n  const eqSet = new Set()\n  let gt, lt\n  for (const c of sub) {\n    if (c.operator === '>' || c.operator === '>=') {\n      gt = higherGT(gt, c, options)\n    } else if (c.operator === '<' || c.operator === '<=') {\n      lt = lowerLT(lt, c, options)\n    } else {\n      eqSet.add(c.semver)\n    }\n  }\n\n  if (eqSet.size > 1) {\n    return null\n  }\n\n  let gtltComp\n  if (gt && lt) {\n    gtltComp = compare(gt.semver, lt.semver, options)\n    if (gtltComp > 0) {\n      return null\n    } else if (gtltComp === 0 && (gt.operator !== '>=' || lt.operator !== '<=')) {\n      return null\n    }\n  }\n\n  // will iterate one or zero times\n  for (const eq of eqSet) {\n    if (gt && !satisfies(eq, String(gt), options)) {\n      return null\n    }\n\n    if (lt && !satisfies(eq, String(lt), options)) {\n      return null\n    }\n\n    for (const c of dom) {\n      if (!satisfies(eq, String(c), options)) {\n        return false\n      }\n    }\n\n    return true\n  }\n\n  let higher, lower\n  let hasDomLT, hasDomGT\n  // if the subset has a prerelease, we need a comparator in the superset\n  // with the same tuple and a prerelease, or it's not a subset\n  let needDomLTPre = lt &&\n    !options.includePrerelease &&\n    lt.semver.prerelease.length ? lt.semver : false\n  let needDomGTPre = gt &&\n    !options.includePrerelease &&\n    gt.semver.prerelease.length ? gt.semver : false\n  // exception: <1.2.3-0 is the same as <1.2.3\n  if (needDomLTPre && needDomLTPre.prerelease.length === 1 &&\n      lt.operator === '<' && needDomLTPre.prerelease[0] === 0) {\n    needDomLTPre = false\n  }\n\n  for (const c of dom) {\n    hasDomGT = hasDomGT || c.operator === '>' || c.operator === '>='\n    hasDomLT = hasDomLT || c.operator === '<' || c.operator === '<='\n    if (gt) {\n      if (needDomGTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomGTPre.major &&\n            c.semver.minor === needDomGTPre.minor &&\n            c.semver.patch === needDomGTPre.patch) {\n          needDomGTPre = false\n        }\n      }\n      if (c.operator === '>' || c.operator === '>=') {\n        higher = higherGT(gt, c, options)\n        if (higher === c && higher !== gt) {\n          return false\n        }\n      } else if (gt.operator === '>=' && !satisfies(gt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (lt) {\n      if (needDomLTPre) {\n        if (c.semver.prerelease && c.semver.prerelease.length &&\n            c.semver.major === needDomLTPre.major &&\n            c.semver.minor === needDomLTPre.minor &&\n            c.semver.patch === needDomLTPre.patch) {\n          needDomLTPre = false\n        }\n      }\n      if (c.operator === '<' || c.operator === '<=') {\n        lower = lowerLT(lt, c, options)\n        if (lower === c && lower !== lt) {\n          return false\n        }\n      } else if (lt.operator === '<=' && !satisfies(lt.semver, String(c), options)) {\n        return false\n      }\n    }\n    if (!c.operator && (lt || gt) && gtltComp !== 0) {\n      return false\n    }\n  }\n\n  // if there was a < or >, and nothing in the dom, then must be false\n  // UNLESS it was limited by another range in the other direction.\n  // Eg, >1.0.0 <1.0.1 is still a subset of <2.0.0\n  if (gt && hasDomLT && !lt && gtltComp !== 0) {\n    return false\n  }\n\n  if (lt && hasDomGT && !gt && gtltComp !== 0) {\n    return false\n  }\n\n  // we needed a prerelease range in a specific tuple, but didn't get one\n  // then this isn't a subset.  eg >=1.2.3-pre is not a subset of >=1.0.0,\n  // because it includes prereleases in the 1.2.3 tuple\n  if (needDomGTPre || needDomLTPre) {\n    return false\n  }\n\n  return true\n}\n\n// >=1.2.3 is lower than >1.2.3\nconst higherGT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp > 0 ? a\n    : comp < 0 ? b\n    : b.operator === '>' && a.operator === '>=' ? b\n    : a\n}\n\n// <=1.2.3 is higher than <1.2.3\nconst lowerLT = (a, b, options) => {\n  if (!a) {\n    return b\n  }\n  const comp = compare(a.semver, b.semver, options)\n  return comp < 0 ? a\n    : comp > 0 ? b\n    : b.operator === '<' && a.operator === '<=' ? b\n    : a\n}\n\nmodule.exports = subset\n", "// just pre-load all the stuff that index.js lazily exports\nconst internalRe = require('./internal/re')\nconst constants = require('./internal/constants')\nconst SemVer = require('./classes/semver')\nconst identifiers = require('./internal/identifiers')\nconst parse = require('./functions/parse')\nconst valid = require('./functions/valid')\nconst clean = require('./functions/clean')\nconst inc = require('./functions/inc')\nconst diff = require('./functions/diff')\nconst major = require('./functions/major')\nconst minor = require('./functions/minor')\nconst patch = require('./functions/patch')\nconst prerelease = require('./functions/prerelease')\nconst compare = require('./functions/compare')\nconst rcompare = require('./functions/rcompare')\nconst compareLoose = require('./functions/compare-loose')\nconst compareBuild = require('./functions/compare-build')\nconst sort = require('./functions/sort')\nconst rsort = require('./functions/rsort')\nconst gt = require('./functions/gt')\nconst lt = require('./functions/lt')\nconst eq = require('./functions/eq')\nconst neq = require('./functions/neq')\nconst gte = require('./functions/gte')\nconst lte = require('./functions/lte')\nconst cmp = require('./functions/cmp')\nconst coerce = require('./functions/coerce')\nconst Comparator = require('./classes/comparator')\nconst Range = require('./classes/range')\nconst satisfies = require('./functions/satisfies')\nconst toComparators = require('./ranges/to-comparators')\nconst maxSatisfying = require('./ranges/max-satisfying')\nconst minSatisfying = require('./ranges/min-satisfying')\nconst minVersion = require('./ranges/min-version')\nconst validRange = require('./ranges/valid')\nconst outside = require('./ranges/outside')\nconst gtr = require('./ranges/gtr')\nconst ltr = require('./ranges/ltr')\nconst intersects = require('./ranges/intersects')\nconst simplifyRange = require('./ranges/simplify')\nconst subset = require('./ranges/subset')\nmodule.exports = {\n  parse,\n  valid,\n  clean,\n  inc,\n  diff,\n  major,\n  minor,\n  patch,\n  prerelease,\n  compare,\n  rcompare,\n  compareLoose,\n  compareBuild,\n  sort,\n  rsort,\n  gt,\n  lt,\n  eq,\n  neq,\n  gte,\n  lte,\n  cmp,\n  coerce,\n  Comparator,\n  Range,\n  satisfies,\n  toComparators,\n  maxSatisfying,\n  minSatisfying,\n  minVersion,\n  validRange,\n  outside,\n  gtr,\n  ltr,\n  intersects,\n  simplifyRange,\n  subset,\n  SemVer,\n  re: internalRe.re,\n  src: internalRe.src,\n  tokens: internalRe.t,\n  SEMVER_SPEC_VERSION: constants.SEMVER_SPEC_VERSION,\n  RELEASE_TYPES: constants.RELEASE_TYPES,\n  compareIdentifiers: identifiers.compareIdentifiers,\n  rcompareIdentifiers: identifiers.rcompareIdentifiers,\n}\n", "import * as React from 'react';\n\nexport function useDebounce<TValue>(value: TValue, delay: number): TValue {\n  const [debouncedValue, setDebouncedValue] = React.useState(value);\n\n  React.useEffect(() => {\n    const handler = setTimeout(() => {\n      setDebouncedValue(value);\n    }, delay);\n\n    return () => {\n      clearTimeout(handler);\n    };\n  }, [value, delay]);\n\n  return debouncedValue;\n}\n", "import * as React from 'react';\n\nimport {\n  Box,\n  Button,\n  Tag,\n  MultiSelect,\n  MultiSelectOption,\n  MultiSelectProps,\n  Popover,\n  Flex,\n} from '@strapi/design-system';\nimport { Cross, Filter } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport type { Categories, Collections, FilterTypes } from '../hooks/useMarketplaceData';\nimport type { NpmPackageType, MarketplacePageQuery } from '../MarketplacePage';\n\ninterface NpmPackagesFiltersProps {\n  handleSelectClear: (type: FilterTypes) => void;\n  handleSelectChange: (update: Partial<MarketplacePageQuery>) => void;\n  npmPackageType: NpmPackageType;\n  possibleCategories: Partial<Record<Categories, number>>;\n  possibleCollections: Partial<Record<Collections, number>>;\n  query: MarketplacePageQuery;\n}\n\nconst NpmPackagesFilters = ({\n  handleSelectClear,\n  handleSelectChange,\n  npmPackageType,\n  possibleCategories,\n  possibleCollections,\n  query,\n}: NpmPackagesFiltersProps) => {\n  const { formatMessage } = useIntl();\n\n  const handleTagRemove = (tagToRemove: string, filterType: FilterTypes) => {\n    const update = {\n      [filterType]: (query[filterType] ?? []).filter((previousTag) => previousTag !== tagToRemove),\n    };\n\n    handleSelectChange(update);\n  };\n\n  return (\n    <Popover.Root>\n      <Popover.Trigger>\n        <Button variant=\"tertiary\" startIcon={<Filter />}>\n          {formatMessage({ id: 'app.utils.filters', defaultMessage: 'Filters' })}\n        </Button>\n      </Popover.Trigger>\n      <Popover.Content sideOffset={4}>\n        <Flex padding={3} direction=\"column\" alignItems=\"stretch\" gap={1}>\n          <FilterSelect\n            message={formatMessage({\n              id: 'admin.pages.MarketPlacePage.filters.collections',\n              defaultMessage: 'Collections',\n            })}\n            value={query?.collections || []}\n            onChange={(newCollections) => {\n              const update = { collections: newCollections };\n              handleSelectChange(update);\n            }}\n            onClear={() => handleSelectClear('collections')}\n            possibleFilters={possibleCollections}\n            customizeContent={(values) =>\n              formatMessage(\n                {\n                  id: 'admin.pages.MarketPlacePage.filters.collectionsSelected',\n                  defaultMessage:\n                    '{count, plural, =0 {No collections} one {# collection} other {# collections}} selected',\n                },\n                { count: values?.length ?? 0 }\n              )\n            }\n          />\n          {npmPackageType === 'plugin' && (\n            <FilterSelect\n              message={formatMessage({\n                id: 'admin.pages.MarketPlacePage.filters.categories',\n                defaultMessage: 'Categories',\n              })}\n              value={query?.categories || []}\n              onChange={(newCategories) => {\n                const update = { categories: newCategories };\n                handleSelectChange(update);\n              }}\n              onClear={() => handleSelectClear('categories')}\n              possibleFilters={possibleCategories}\n              customizeContent={(values) =>\n                formatMessage(\n                  {\n                    id: 'admin.pages.MarketPlacePage.filters.categoriesSelected',\n                    defaultMessage:\n                      '{count, plural, =0 {No categories} one {# category} other {# categories}} selected',\n                  },\n                  { count: values?.length ?? 0 }\n                )\n              }\n            />\n          )}\n        </Flex>\n      </Popover.Content>\n\n      {query.collections?.map((collection) => (\n        <Box key={collection} padding={1}>\n          <Tag icon={<Cross />} onClick={() => handleTagRemove(collection, 'collections')}>\n            {collection}\n          </Tag>\n        </Box>\n      ))}\n      {npmPackageType === 'plugin' &&\n        query.categories?.map((category) => (\n          <Box key={category} padding={1}>\n            <Tag icon={<Cross />} onClick={() => handleTagRemove(category, 'categories')}>\n              {category}\n            </Tag>\n          </Box>\n        ))}\n    </Popover.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * FilterSelect\n * -----------------------------------------------------------------------------------------------*/\n\ninterface FilterSelectProps\n  extends Pick<MultiSelectProps, 'customizeContent' | 'onClear' | 'onChange' | 'value'> {\n  message: string;\n  possibleFilters:\n    | NpmPackagesFiltersProps['possibleCategories']\n    | NpmPackagesFiltersProps['possibleCollections'];\n}\n\nconst FilterSelect = ({\n  message,\n  value,\n  onChange,\n  possibleFilters,\n  onClear,\n  customizeContent,\n}: FilterSelectProps) => {\n  return (\n    <MultiSelect\n      data-testid={`${message}-button`}\n      aria-label={message}\n      placeholder={message}\n      onChange={onChange}\n      onClear={onClear}\n      value={value}\n      customizeContent={customizeContent}\n    >\n      {Object.entries(possibleFilters).map(([filterName, count]) => {\n        return (\n          <MultiSelectOption\n            data-testid={`${filterName}-${count}`}\n            key={filterName}\n            value={filterName}\n          >\n            {`${filterName} (${count})`}\n          </MultiSelectOption>\n        );\n      })}\n    </MultiSelect>\n  );\n};\n\nexport { NpmPackagesFilters };\nexport type { NpmPackagesFiltersProps };\n", "import {\n  <PERSON>,\n  <PERSON>lex,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>r,\n  Button,\n  LinkButton,\n  TypographyComponent,\n} from '@strapi/design-system';\nimport { CheckCircle, ExternalLink, Download, Star, Check, Duplicate } from '@strapi/icons';\nimport { GitHub } from '@strapi/icons/symbols';\nimport pluralize from 'pluralize';\nimport { useIntl } from 'react-intl';\nimport * as semver from 'semver';\nimport { styled } from 'styled-components';\n\nimport StrapiLogo from '../../../assets/images/logo-strapi-2022.svg';\nimport { AppInfoContextValue } from '../../../features/AppInfo';\nimport { useNotification } from '../../../features/Notifications';\nimport { useTracking } from '../../../features/Tracking';\nimport { useClipboard } from '../../../hooks/useClipboard';\n\nimport type { Plugin, Provider } from '../hooks/useMarketplaceData';\nimport type { NpmPackageType } from '../MarketplacePage';\n\n// Custom component to have an ellipsis after the 2nd line\nconst EllipsisText = styled<TypographyComponent<'p'>>(Typography)`\n  display: -webkit-box;\n  -webkit-box-orient: vertical;\n  -webkit-line-clamp: 2;\n  overflow: hidden;\n`;\n\ninterface NpmPackageCardProps extends Pick<AppInfoContextValue, 'useYarn'> {\n  npmPackage: Plugin | Provider;\n  isInstalled: boolean;\n  isInDevelopmentMode: AppInfoContextValue['autoReload'];\n  npmPackageType: NpmPackageType;\n  strapiAppVersion: AppInfoContextValue['strapiVersion'];\n}\n\nconst NpmPackageCard = ({\n  npmPackage,\n  isInstalled,\n  useYarn,\n  isInDevelopmentMode,\n  npmPackageType,\n  strapiAppVersion,\n}: NpmPackageCardProps) => {\n  const { attributes } = npmPackage;\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n\n  const commandToCopy = useYarn\n    ? `yarn add ${attributes.npmPackageName}`\n    : `npm install ${attributes.npmPackageName}`;\n\n  const madeByStrapiMessage = formatMessage({\n    id: 'admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi',\n    defaultMessage: 'Made by Strapi',\n  });\n\n  const npmPackageHref = `https://market.strapi.io/${pluralize.plural(npmPackageType)}/${\n    attributes.slug\n  }`;\n\n  const versionRange = semver.validRange(attributes.strapiVersion);\n\n  const isCompatible = versionRange\n    ? semver.satisfies(strapiAppVersion ?? '', versionRange)\n    : false;\n\n  return (\n    <Flex\n      direction=\"column\"\n      justifyContent=\"space-between\"\n      paddingTop={4}\n      paddingRight={4}\n      paddingBottom={4}\n      paddingLeft={4}\n      hasRadius\n      background=\"neutral0\"\n      shadow=\"tableShadow\"\n      height=\"100%\"\n      alignItems=\"normal\"\n      data-testid=\"npm-package-card\"\n    >\n      <Box>\n        <Flex direction=\"row\" justifyContent=\"space-between\" alignItems=\"flex-start\">\n          <Box\n            tag=\"img\"\n            src={attributes.logo.url}\n            alt={`${attributes.name} logo`}\n            hasRadius\n            width={11}\n            height={11}\n          />\n          <PackageStats\n            githubStars={attributes.githubStars}\n            npmDownloads={attributes.npmDownloads}\n            npmPackageType={npmPackageType}\n          />\n        </Flex>\n        <Box paddingTop={4}>\n          <Typography tag=\"h3\" variant=\"delta\">\n            <Flex\n              alignItems=\"center\"\n              gap={attributes.validated && !attributes.madeByStrapi ? 2 : 1}\n            >\n              {attributes.name}\n              {attributes.validated && !attributes.madeByStrapi && (\n                <Tooltip\n                  description={formatMessage({\n                    id: 'admin.pages.MarketPlacePage.plugin.tooltip.verified',\n                    defaultMessage: 'Plugin verified by Strapi',\n                  })}\n                >\n                  <CheckCircle fill=\"success600\" />\n                </Tooltip>\n              )}\n              {attributes.madeByStrapi && (\n                <Tooltip description={madeByStrapiMessage}>\n                  <Box\n                    tag=\"img\"\n                    src={StrapiLogo}\n                    alt={madeByStrapiMessage}\n                    width={6}\n                    height=\"auto\"\n                  />\n                </Tooltip>\n              )}\n            </Flex>\n          </Typography>\n        </Box>\n        <Box paddingTop={2}>\n          <EllipsisText tag=\"p\" variant=\"omega\" textColor=\"neutral600\">\n            {attributes.description}\n          </EllipsisText>\n        </Box>\n      </Box>\n\n      <Flex gap={2} style={{ alignSelf: 'flex-end' }} paddingTop={6}>\n        <LinkButton\n          size=\"S\"\n          href={npmPackageHref}\n          isExternal\n          endIcon={<ExternalLink />}\n          aria-label={formatMessage(\n            {\n              id: 'admin.pages.MarketPlacePage.plugin.info.label',\n              defaultMessage: 'Learn more about {pluginName}',\n            },\n            { pluginName: attributes.name }\n          )}\n          variant=\"tertiary\"\n          onClick={() => trackUsage('didPluginLearnMore')}\n        >\n          {formatMessage({\n            id: 'admin.pages.MarketPlacePage.plugin.info.text',\n            defaultMessage: 'More',\n          })}\n        </LinkButton>\n        <InstallPluginButton\n          isInstalled={isInstalled}\n          isInDevelopmentMode={isInDevelopmentMode}\n          isCompatible={isCompatible}\n          commandToCopy={commandToCopy}\n          strapiAppVersion={strapiAppVersion}\n          strapiPeerDepVersion={attributes.strapiVersion}\n          pluginName={attributes.name}\n        />\n      </Flex>\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * InstallPluginButton\n * -----------------------------------------------------------------------------------------------*/\n\ninterface InstallPluginButtonProps\n  extends Pick<NpmPackageCardProps, 'isInstalled' | 'isInDevelopmentMode' | 'strapiAppVersion'> {\n  commandToCopy: string;\n  pluginName: string;\n  strapiPeerDepVersion?: string;\n  isCompatible?: boolean;\n}\n\nconst InstallPluginButton = ({\n  isInstalled,\n  isInDevelopmentMode,\n  isCompatible,\n  commandToCopy,\n  strapiAppVersion,\n  strapiPeerDepVersion,\n  pluginName,\n}: InstallPluginButtonProps) => {\n  const { toggleNotification } = useNotification();\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { copy } = useClipboard();\n\n  const handleCopy = async () => {\n    const didCopy = await copy(commandToCopy);\n\n    if (didCopy) {\n      trackUsage('willInstallPlugin');\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: 'admin.pages.MarketPlacePage.plugin.copy.success' }),\n      });\n    }\n  };\n\n  // Already installed\n  if (isInstalled) {\n    return (\n      <Flex gap={2} paddingLeft={4}>\n        <Check width=\"1.2rem\" height=\"1.2rem\" color=\"success600\" />\n        <Typography variant=\"omega\" textColor=\"success600\" fontWeight=\"bold\">\n          {formatMessage({\n            id: 'admin.pages.MarketPlacePage.plugin.installed',\n            defaultMessage: 'Installed',\n          })}\n        </Typography>\n      </Flex>\n    );\n  }\n\n  // In development, show install button\n  if (isInDevelopmentMode && isCompatible !== false) {\n    return (\n      <CardButton\n        strapiAppVersion={strapiAppVersion}\n        strapiPeerDepVersion={strapiPeerDepVersion}\n        handleCopy={handleCopy}\n        pluginName={pluginName}\n      />\n    );\n  }\n\n  // Not in development and plugin not installed already. Show nothing\n  return null;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * CardButton\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CardButtonProps\n  extends Pick<NpmPackageCardProps, 'strapiAppVersion'>,\n    Pick<InstallPluginButtonProps, 'strapiPeerDepVersion' | 'pluginName'> {\n  handleCopy: () => void;\n}\n\nconst CardButton = ({\n  strapiPeerDepVersion,\n  strapiAppVersion,\n  handleCopy,\n  pluginName,\n}: CardButtonProps) => {\n  const { formatMessage } = useIntl();\n  const versionRange = semver.validRange(strapiPeerDepVersion);\n  const isCompatible = semver.satisfies(strapiAppVersion ?? '', versionRange ?? '');\n\n  const installMessage = formatMessage({\n    id: 'admin.pages.MarketPlacePage.plugin.copy',\n    defaultMessage: 'Copy install command',\n  });\n\n  // Only plugins receive a strapiAppVersion\n  if (strapiAppVersion) {\n    if (!versionRange || !isCompatible) {\n      return (\n        <Tooltip\n          data-testid={`tooltip-${pluginName}`}\n          label={formatMessage(\n            {\n              id: 'admin.pages.MarketPlacePage.plugin.version',\n              defaultMessage:\n                'Update your Strapi version: \"{strapiAppVersion}\" to: \"{versionRange}\"',\n            },\n            {\n              strapiAppVersion,\n              versionRange,\n            }\n          )}\n        >\n          <span>\n            <Button\n              size=\"S\"\n              startIcon={<Duplicate />}\n              variant=\"secondary\"\n              onClick={handleCopy}\n              disabled={!isCompatible}\n            >\n              {installMessage}\n            </Button>\n          </span>\n        </Tooltip>\n      );\n    }\n  }\n\n  return (\n    <Button size=\"S\" startIcon={<Duplicate />} variant=\"secondary\" onClick={handleCopy}>\n      {installMessage}\n    </Button>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PackageStats\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PackageStatsProps {\n  githubStars?: number;\n  npmDownloads?: number;\n  npmPackageType: NpmPackageType;\n}\n\nconst PackageStats = ({ githubStars = 0, npmDownloads = 0, npmPackageType }: PackageStatsProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex gap={1}>\n      {!!githubStars && (\n        <>\n          <GitHub height=\"1.2rem\" width=\"1.2rem\" aria-hidden />\n          <Star height=\"1.2rem\" width=\"1.2rem\" fill=\"warning500\" aria-hidden />\n          <p\n            aria-label={formatMessage(\n              {\n                id: `admin.pages.MarketPlacePage.${npmPackageType}.githubStars`,\n                defaultMessage: `This {package} was starred {starsCount} on GitHub`,\n              },\n              {\n                starsCount: githubStars,\n                package: npmPackageType,\n              }\n            )}\n          >\n            <Typography variant=\"pi\" textColor=\"neutral800\">\n              {githubStars}\n            </Typography>\n          </p>\n          <VerticalDivider />\n        </>\n      )}\n      <Download height=\"1.2rem\" width=\"1.2rem\" aria-hidden />\n      <p\n        aria-label={formatMessage(\n          {\n            id: `admin.pages.MarketPlacePage.${npmPackageType}.downloads`,\n            defaultMessage: `This {package} has {downloadsCount} weekly downloads`,\n          },\n          {\n            downloadsCount: npmDownloads,\n            package: npmPackageType,\n          }\n        )}\n      >\n        <Typography variant=\"pi\" textColor=\"neutral800\">\n          {npmDownloads}\n        </Typography>\n      </p>\n    </Flex>\n  );\n};\n\nconst VerticalDivider = styled(Divider)`\n  width: 1.2rem;\n  transform: rotate(90deg);\n`;\n\nexport { NpmPackageCard };\nexport type { NpmPackageCardProps };\n", "import { Box, BoxComponent, Flex, Grid, Typography } from '@strapi/design-system';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { Layouts } from '../../../components/Layouts/Layout';\nimport { Page } from '../../../components/PageHelpers';\nimport { AppInfoContextValue } from '../../../features/AppInfo';\n\nimport { NpmPackageCard, NpmPackageCardProps } from './NpmPackageCard';\n\nimport type { Plugin, Provider } from '../hooks/useMarketplaceData';\n\ninterface NpmPackagesGridProps extends Pick<NpmPackageCardProps, 'npmPackageType' | 'useYarn'> {\n  debouncedSearch: string;\n  installedPackageNames: string[];\n  isInDevelopmentMode: AppInfoContextValue['autoReload'];\n  npmPackages?: Array<Plugin | Provider>;\n  status: 'idle' | 'loading' | 'error' | 'success';\n  strapiAppVersion?: NpmPackageCardProps['strapiAppVersion'];\n}\n\nconst NpmPackagesGrid = ({\n  status,\n  npmPackages = [],\n  installedPackageNames = [],\n  useYarn,\n  isInDevelopmentMode,\n  npmPackageType,\n  strapiAppVersion,\n  debouncedSearch,\n}: NpmPackagesGridProps) => {\n  const { formatMessage } = useIntl();\n\n  if (status === 'error') {\n    return <Page.Error />;\n  }\n\n  if (status === 'loading') {\n    return <Page.Loading />;\n  }\n\n  const emptySearchMessage = formatMessage(\n    {\n      id: 'admin.pages.MarketPlacePage.search.empty',\n      defaultMessage: 'No result for \"{target}\"',\n    },\n    { target: debouncedSearch }\n  );\n\n  if (npmPackages.length === 0) {\n    return (\n      <Box position=\"relative\">\n        <Layouts.Grid size=\"M\">\n          {Array(12)\n            .fill(null)\n            .map((_, idx) => (\n              <EmptyPluginCard key={idx} height=\"234px\" hasRadius />\n            ))}\n        </Layouts.Grid>\n        <Box position=\"absolute\" top={11} width=\"100%\">\n          <Flex alignItems=\"center\" justifyContent=\"center\" direction=\"column\">\n            <EmptyDocuments width=\"160px\" height=\"88px\" />\n            <Box paddingTop={6}>\n              <Typography variant=\"delta\" tag=\"p\" textColor=\"neutral600\">\n                {emptySearchMessage}\n              </Typography>\n            </Box>\n          </Flex>\n        </Box>\n      </Box>\n    );\n  }\n\n  return (\n    <Grid.Root gap={4}>\n      {npmPackages.map((npmPackage) => (\n        <Grid.Item\n          col={4}\n          s={6}\n          xs={12}\n          style={{ height: '100%' }}\n          key={npmPackage.id}\n          direction=\"column\"\n          alignItems=\"stretch\"\n        >\n          <NpmPackageCard\n            npmPackage={npmPackage}\n            isInstalled={installedPackageNames.includes(npmPackage.attributes.npmPackageName)}\n            useYarn={useYarn}\n            isInDevelopmentMode={isInDevelopmentMode}\n            npmPackageType={npmPackageType}\n            strapiAppVersion={strapiAppVersion}\n          />\n        </Grid.Item>\n      ))}\n    </Grid.Root>\n  );\n};\n\nconst EmptyPluginCard = styled<BoxComponent>(Box)`\n  background: ${({ theme }) =>\n    `linear-gradient(180deg, rgba(234, 234, 239, 0) 0%, ${theme.colors.neutral150} 100%)`};\n  opacity: 0.33;\n`;\n\nexport { NpmPackagesGrid };\n", "import { LinkButton } from '@strapi/design-system';\nimport { Upload } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { Layouts } from '../../../components/Layouts/Layout';\nimport { useTracking } from '../../../features/Tracking';\n\nimport type { NpmPackageType } from '../MarketplacePage';\n\ninterface PageHeaderProps {\n  isOnline?: boolean;\n  npmPackageType?: NpmPackageType;\n}\n\nconst PageHeader = ({ isOnline, npmPackageType = 'plugin' }: PageHeaderProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n\n  const tracking = npmPackageType === 'provider' ? 'didSubmitProvider' : 'didSubmitPlugin';\n\n  return (\n    <Layouts.Header\n      title={formatMessage({\n        id: 'global.marketplace',\n        defaultMessage: 'Marketplace',\n      })}\n      subtitle={formatMessage({\n        id: 'admin.pages.MarketPlacePage.subtitle',\n        defaultMessage: 'Get more out of Strapi',\n      })}\n      primaryAction={\n        isOnline && (\n          <LinkButton\n            startIcon={<Upload />}\n            variant=\"tertiary\"\n            href={`https://market.strapi.io/submit-${npmPackageType}`}\n            onClick={() => trackUsage(tracking)}\n            isExternal\n          >\n            {formatMessage({\n              id: `admin.pages.MarketPlacePage.submit.${npmPackageType}.link`,\n              defaultMessage: `Submit ${npmPackageType}`,\n            })}\n          </LinkButton>\n        )\n      }\n    />\n  );\n};\n\nexport { PageHeader };\nexport type { PageHeaderProps };\n", "import { Box, Flex, Main, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { Layouts } from '../../../components/Layouts/Layout';\n\nimport { PageHeader } from './PageHeader';\n\nconst OfflineLayout = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Layouts.Root>\n      <Main>\n        <PageHeader />\n        <Flex\n          width=\"100%\"\n          direction=\"column\"\n          alignItems=\"center\"\n          justifyContent=\"center\"\n          paddingTop={`12rem`}\n        >\n          <Box paddingBottom={2}>\n            <Typography textColor=\"neutral700\" variant=\"alpha\">\n              {formatMessage({\n                id: 'admin.pages.MarketPlacePage.offline.title',\n                defaultMessage: 'You are offline',\n              })}\n            </Typography>\n          </Box>\n          <Box paddingBottom={6}>\n            <Typography textColor=\"neutral700\" variant=\"epsilon\">\n              {formatMessage({\n                id: 'admin.pages.MarketPlacePage.offline.subtitle',\n                defaultMessage: 'You need to be connected to the Internet to access Strapi Market.',\n              })}\n            </Typography>\n          </Box>\n          <svg\n            width=\"88\"\n            height=\"88\"\n            viewBox=\"0 0 88 88\"\n            fill=\"none\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n          >\n            <rect x=\".5\" y=\".5\" width=\"87\" height=\"87\" rx=\"43.5\" fill=\"#F0F0FF\" />\n            <path\n              d=\"M34 39.3h-4c-2.6 0-4.7 1-6.6 2.8a9 9 0 0 0-2.7 6.6 9 9 0 0 0 2.7 6.6A9 9 0 0 0 30 58h22.8L34 39.3Zm-11-11 3-3 39 39-3 3-4.7-4.6H30a13.8 13.8 0 0 1-14-14c0-3.8 1.3-7 4-9.7 2.6-2.7 5.7-4.2 9.5-4.3L23 28.2Zm38.2 11.1c3 .2 5.5 1.5 7.6 3.7A11 11 0 0 1 72 51c0 4-1.6 7.2-5 9.5l-3.3-3.4a6.5 6.5 0 0 0 3.6-6.1c0-1.9-.7-3.5-2-5-1.5-1.3-3.1-2-5-2h-3.5v-1.2c0-3.6-1.2-6.6-3.7-9a13 13 0 0 0-15-2.3L34.6 28a17 17 0 0 1 20.3 1.5c3.5 2.7 5.5 6 6.3 10Z\"\n              fill=\"#4945FF\"\n            />\n            <rect x=\".5\" y=\".5\" width=\"87\" height=\"87\" rx=\"43.5\" stroke=\"#D9D8FF\" />\n          </svg>\n        </Flex>\n      </Main>\n    </Layouts.Root>\n  );\n};\n\nexport { OfflineLayout };\n", "import { Box, SingleSelectOption, SingleSelect, BoxComponent } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nconst SORT_TYPES = {\n  'name:asc': {\n    selected: {\n      id: 'admin.pages.MarketPlacePage.sort.alphabetical.selected',\n      defaultMessage: 'Sort by alphabetical order',\n    },\n    option: {\n      id: 'admin.pages.MarketPlacePage.sort.alphabetical',\n      defaultMessage: 'Alphabetical order',\n    },\n  },\n  'submissionDate:desc': {\n    selected: {\n      id: 'admin.pages.MarketPlacePage.sort.newest.selected',\n      defaultMessage: 'Sort by newest',\n    },\n    option: {\n      id: 'admin.pages.MarketPlacePage.sort.newest',\n      defaultMessage: 'Newest',\n    },\n  },\n  'githubStars:desc': {\n    selected: {\n      id: 'admin.pages.MarketPlacePage.sort.githubStars.selected',\n      defaultMessage: 'Sort by GitHub stars',\n    },\n    option: {\n      id: 'admin.pages.MarketPlacePage.sort.githubStars',\n      defaultMessage: 'Number of GitHub stars',\n    },\n  },\n  'npmDownloads:desc': {\n    selected: {\n      id: 'admin.pages.MarketPlacePage.sort.npmDownloads.selected',\n      defaultMessage: 'Sort by npm downloads',\n    },\n    option: {\n      id: 'admin.pages.MarketPlacePage.sort.npmDownloads',\n      defaultMessage: 'Number of downloads',\n    },\n  },\n} as const;\n\ninterface SortSelectProps {\n  sortQuery: keyof typeof SORT_TYPES;\n  handleSelectChange: (payload: { sort: string }) => void;\n}\n\nconst SortSelect = ({ sortQuery, handleSelectChange }: SortSelectProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <SelectWrapper>\n      <SingleSelect\n        value={sortQuery}\n        customizeContent={() => formatMessage(SORT_TYPES[sortQuery].selected)}\n        onChange={(sortName) => {\n          // @ts-expect-error – in V2 design-system we'll only ever return strings.\n          handleSelectChange({ sort: sortName });\n        }}\n        aria-label={formatMessage({\n          id: 'admin.pages.MarketPlacePage.sort.label',\n          defaultMessage: 'Sort by',\n        })}\n        size=\"S\"\n      >\n        {Object.entries(SORT_TYPES).map(([sortName, messages]) => {\n          return (\n            <SingleSelectOption key={sortName} value={sortName}>\n              {formatMessage(messages.option)}\n            </SingleSelectOption>\n          );\n        })}\n      </SingleSelect>\n    </SelectWrapper>\n  );\n};\n\nconst SelectWrapper = styled<BoxComponent>(Box)`\n  font-weight: ${({ theme }) => theme.fontWeights.semiBold};\n\n  span {\n    font-size: ${({ theme }) => theme.fontSizes[1]};\n  }\n`;\n\nexport { SortSelect };\nexport type { SortSelectProps };\n", "import { useNotifyAT } from '@strapi/design-system';\nimport * as qs from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useQuery } from 'react-query';\n\nimport { useNotification } from '../../../features/Notifications';\n\nimport type { MarketplacePageQuery, NpmPackageType, TabQuery } from '../MarketplacePage';\n\nconst MARKETPLACE_API_URL = 'https://market-api.strapi.io';\n\ninterface UseMarketplaceDataParams {\n  npmPackageType: NpmPackageType;\n  debouncedSearch: string;\n  query?: MarketplacePageQuery;\n  tabQuery: TabQuery;\n  strapiVersion?: string | null;\n}\n\ntype Collections =\n  | 'Verified'\n  | 'Made by the community'\n  | 'Made by Strapi'\n  | 'Made by official partners';\n\ntype Categories = 'Custom fields' | 'Deployment' | 'Monitoring';\n\ntype FilterTypes = 'categories' | 'collections';\n\ninterface Plugin {\n  id: string;\n  attributes: {\n    name: string;\n    description: string;\n    slug: string;\n    npmPackageName: string;\n    npmPackageUrl: string;\n    npmDownloads: number;\n    repositoryUrl: string;\n    githubStars: number;\n    logo: {\n      url: string;\n    };\n    developerName: string;\n    validated: boolean;\n    madeByStrapi: boolean;\n    strapiCompatibility: string;\n    submissionDate: string;\n    collections: Collections[];\n    categories: Categories[];\n    strapiVersion: string;\n    screenshots: Array<{\n      url: string;\n    }>;\n  };\n}\n\ninterface Provider {\n  id: string;\n  attributes: {\n    name: string;\n    description: string;\n    slug: string;\n    npmPackageName: string;\n    npmPackageUrl: string;\n    npmDownloads: number;\n    repositoryUrl: string;\n    githubStars: number;\n    pluginName: string;\n    logo: {\n      url: string;\n    };\n    developerName: string;\n    validated: boolean;\n    madeByStrapi: boolean;\n    strapiCompatibility: string;\n    strapiVersion?: never;\n    submissionDate: string;\n    collections: Collections[];\n  };\n}\n\ninterface MarketplaceMeta {\n  collections: Record<Collections, number>;\n  pagination: {\n    page: number;\n    pageSize: number;\n    pageCount: number;\n    total: number;\n  };\n}\n\ninterface MarketplaceResponse<TData extends Plugin | Provider> {\n  data: TData[];\n  meta: TData extends Provider\n    ? MarketplaceMeta\n    : MarketplaceMeta & { categories: Record<Categories, number> };\n}\n\nfunction useMarketplaceData({\n  npmPackageType,\n  debouncedSearch,\n  query,\n  tabQuery,\n  strapiVersion,\n}: UseMarketplaceDataParams) {\n  const { notifyStatus } = useNotifyAT();\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const marketplaceTitle = formatMessage({\n    id: 'global.marketplace',\n    defaultMessage: 'Marketplace',\n  });\n\n  const notifyMarketplaceLoad = () => {\n    notifyStatus(\n      formatMessage(\n        {\n          id: 'app.utils.notify.data-loaded',\n          defaultMessage: 'The {target} has loaded',\n        },\n        { target: marketplaceTitle }\n      )\n    );\n  };\n\n  const paginationParams = {\n    page: query?.page || 1,\n    pageSize: query?.pageSize || 24,\n  };\n\n  const pluginParams = {\n    ...tabQuery.plugin,\n    pagination: paginationParams,\n    search: debouncedSearch,\n    version: strapiVersion,\n  };\n\n  const { data: pluginsResponse, status: pluginsStatus } = useQuery(\n    ['marketplace', 'plugins', pluginParams],\n    async () => {\n      try {\n        const queryString = qs.stringify(pluginParams);\n        const res = await fetch(`${MARKETPLACE_API_URL}/plugins?${queryString}`);\n\n        if (!res.ok) {\n          throw new Error('Failed to fetch marketplace plugins.');\n        }\n\n        const data = (await res.json()) as MarketplaceResponse<Plugin>;\n        return data;\n      } catch (error) {\n        // silence\n      }\n\n      return null;\n    },\n    {\n      onSuccess() {\n        notifyMarketplaceLoad();\n      },\n      onError() {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n        });\n      },\n    }\n  );\n\n  const providerParams = {\n    ...tabQuery.provider,\n    pagination: paginationParams,\n    search: debouncedSearch,\n    version: strapiVersion,\n  };\n\n  const { data: providersResponse, status: providersStatus } = useQuery(\n    ['marketplace', 'providers', providerParams],\n    async () => {\n      const queryString = qs.stringify(providerParams);\n      const res = await fetch(`${MARKETPLACE_API_URL}/providers?${queryString}`);\n\n      if (!res.ok) {\n        throw new Error('Failed to fetch marketplace providers.');\n      }\n\n      const data = (await res.json()) as MarketplaceResponse<Provider>;\n\n      return data;\n    },\n    {\n      onSuccess() {\n        notifyMarketplaceLoad();\n      },\n      onError() {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n        });\n      },\n    }\n  );\n\n  const npmPackageTypeResponse = npmPackageType === 'plugin' ? pluginsResponse : providersResponse;\n\n  const possibleCollections = npmPackageTypeResponse?.meta.collections ?? {};\n  const possibleCategories = pluginsResponse?.meta.categories ?? {};\n\n  const { pagination } = npmPackageTypeResponse?.meta ?? {};\n\n  return {\n    pluginsResponse,\n    providersResponse,\n    pluginsStatus,\n    providersStatus,\n    possibleCollections,\n    possibleCategories,\n    pagination,\n  };\n}\n\nexport { useMarketplaceData };\nexport type {\n  MarketplaceResponse,\n  Plugin,\n  Provider,\n  MarketplaceMeta,\n  Collections,\n  Categories,\n  FilterTypes,\n  UseMarketplaceDataParams,\n};\n", "import * as React from 'react';\n\n/**\n * For more details about this hook see:\n * https://www.30secondsofcode.org/react/s/use-navigator-on-line\n */\nexport const useNavigatorOnline = (): boolean => {\n  const onlineStatus =\n    typeof navigator !== 'undefined' && typeof navigator.onLine === 'boolean'\n      ? navigator.onLine\n      : true;\n\n  const [isOnline, setIsOnline] = React.useState(onlineStatus);\n\n  const setOnline = () => setIsOnline(true);\n  const setOffline = () => setIsOnline(false);\n\n  React.useEffect(() => {\n    window.addEventListener('online', setOnline);\n    window.addEventListener('offline', setOffline);\n\n    return () => {\n      window.removeEventListener('online', setOnline);\n      window.removeEventListener('offline', setOffline);\n    };\n  }, []);\n\n  return isOnline;\n};\n", "import * as React from 'react';\n\nimport { Box, Flex, Searchbar, Tabs } from '@strapi/design-system';\nimport { ExternalLink } from '@strapi/icons';\nimport { GlassesSquare } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nimport { ContentBox } from '../../components/ContentBox';\nimport { Layouts } from '../../components/Layouts/Layout';\nimport { Page } from '../../components/PageHelpers';\nimport { Pagination } from '../../components/Pagination';\nimport { useTypedSelector } from '../../core/store/hooks';\nimport { useAppInfo } from '../../features/AppInfo';\nimport { useNotification } from '../../features/Notifications';\nimport { useTracking } from '../../features/Tracking';\nimport { useDebounce } from '../../hooks/useDebounce';\nimport { useQueryParams } from '../../hooks/useQueryParams';\n\nimport { NpmPackagesFilters } from './components/NpmPackagesFilters';\nimport { NpmPackagesGrid } from './components/NpmPackagesGrid';\nimport { OfflineLayout } from './components/OfflineLayout';\nimport { PageHeader } from './components/PageHeader';\nimport { SortSelect, SortSelectProps } from './components/SortSelect';\nimport { FilterTypes, useMarketplaceData } from './hooks/useMarketplaceData';\nimport { useNavigatorOnline } from './hooks/useNavigatorOnline';\n\ntype NpmPackageType = 'plugin' | 'provider';\n\ninterface MarketplacePageQuery {\n  collections?: string[];\n  categories?: string[];\n  npmPackageType?: NpmPackageType;\n  page?: number;\n  pageSize?: number;\n  search?: string;\n  sort?: SortSelectProps['sortQuery'];\n}\n\ninterface TabQuery {\n  plugin: MarketplacePageQuery;\n  provider: MarketplacePageQuery;\n}\n\nconst PLUGIN = 'plugin';\nconst PROVIDER = 'provider';\n\nconst MarketplacePage = () => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { toggleNotification } = useNotification();\n  const [{ query }, setQuery] = useQueryParams<MarketplacePageQuery>();\n  const debouncedSearch = useDebounce(query?.search, 500) || '';\n\n  const {\n    autoReload: isInDevelopmentMode,\n    dependencies,\n    useYarn,\n    strapiVersion,\n  } = useAppInfo('MarketplacePage', (state) => state);\n  const isOnline = useNavigatorOnline();\n\n  const npmPackageType = query?.npmPackageType || PLUGIN;\n\n  const [tabQuery, setTabQuery] = React.useState<TabQuery>({\n    plugin: npmPackageType === PLUGIN ? { ...query } : {},\n    provider: npmPackageType === PROVIDER ? { ...query } : {},\n  });\n\n  React.useEffect(() => {\n    trackUsage('didGoToMarketplace');\n  }, [trackUsage]);\n\n  React.useEffect(() => {\n    if (!isInDevelopmentMode) {\n      toggleNotification({\n        type: 'info',\n        message: formatMessage({\n          id: 'admin.pages.MarketPlacePage.production',\n          defaultMessage: 'Manage plugins from the development environment',\n        }),\n      });\n    }\n  }, [toggleNotification, isInDevelopmentMode, formatMessage]);\n\n  const {\n    pluginsResponse,\n    providersResponse,\n    pluginsStatus,\n    providersStatus,\n    possibleCollections,\n    possibleCategories,\n    pagination,\n  } = useMarketplaceData({ npmPackageType, debouncedSearch, query, tabQuery, strapiVersion });\n\n  if (!isOnline) {\n    return <OfflineLayout />;\n  }\n\n  const handleTabChange = (tab: string) => {\n    const selectedTab = tab === PLUGIN || tab === PROVIDER ? tab : PLUGIN;\n\n    const hasTabQuery = tabQuery[selectedTab] && Object.keys(tabQuery[selectedTab]).length;\n\n    if (hasTabQuery) {\n      setQuery({\n        // Keep filters and search\n        ...tabQuery[selectedTab],\n        search: query?.search || '',\n        // Set tab and reset page\n        npmPackageType: selectedTab,\n        page: 1,\n      });\n    } else {\n      setQuery({\n        // Set tab\n        npmPackageType: selectedTab,\n        // Clear filters\n        collections: [],\n        categories: [],\n        sort: 'name:asc',\n        page: 1,\n        // Keep search\n        search: query?.search || '',\n      });\n    }\n  };\n\n  const handleSelectChange = (update: Partial<MarketplacePageQuery>) => {\n    setQuery({ ...update, page: 1 });\n    setTabQuery((prev) => ({\n      ...prev,\n      [npmPackageType]: { ...prev[npmPackageType], ...update },\n    }));\n  };\n\n  const handleSelectClear = (filterType: FilterTypes) => {\n    setQuery({ [filterType]: [], page: undefined }, 'remove');\n    setTabQuery((prev) => ({ ...prev, [npmPackageType]: {} }));\n  };\n\n  const handleSortSelectChange: SortSelectProps['handleSelectChange'] = ({ sort }) =>\n    // @ts-expect-error - this is a narrowing issue.\n    handleSelectChange({ sort });\n\n  // Check if plugins and providers are installed already\n  const installedPackageNames = Object.keys(dependencies ?? {});\n\n  return (\n    <Layouts.Root>\n      <Page.Main>\n        <Page.Title>\n          {formatMessage({\n            id: 'admin.pages.MarketPlacePage.head',\n            defaultMessage: 'Marketplace - Plugins',\n          })}\n        </Page.Title>\n        <PageHeader isOnline={isOnline} npmPackageType={npmPackageType} />\n        <Layouts.Content>\n          <Tabs.Root variant=\"simple\" onValueChange={handleTabChange} value={npmPackageType}>\n            <Flex justifyContent=\"space-between\" paddingBottom={4}>\n              <Tabs.List\n                aria-label={formatMessage({\n                  id: 'admin.pages.MarketPlacePage.tab-group.label',\n                  defaultMessage: 'Plugins and Providers for Strapi',\n                })}\n              >\n                <Tabs.Trigger value={PLUGIN}>\n                  {formatMessage({\n                    id: 'admin.pages.MarketPlacePage.plugins',\n                    defaultMessage: 'Plugins',\n                  })}{' '}\n                  {pluginsResponse ? `(${pluginsResponse.meta.pagination.total})` : '...'}\n                </Tabs.Trigger>\n                <Tabs.Trigger value={PROVIDER}>\n                  {formatMessage({\n                    id: 'admin.pages.MarketPlacePage.providers',\n                    defaultMessage: 'Providers',\n                  })}{' '}\n                  {providersResponse ? `(${providersResponse.meta.pagination.total})` : '...'}\n                </Tabs.Trigger>\n              </Tabs.List>\n\n              <Box width=\"25%\">\n                <Searchbar\n                  name=\"searchbar\"\n                  onClear={() => setQuery({ search: '', page: 1 })}\n                  value={query?.search}\n                  onChange={(e) => setQuery({ search: e.target.value, page: 1 })}\n                  clearLabel={formatMessage({\n                    id: 'admin.pages.MarketPlacePage.search.clear',\n                    defaultMessage: 'Clear the search',\n                  })}\n                  placeholder={formatMessage({\n                    id: 'admin.pages.MarketPlacePage.search.placeholder',\n                    defaultMessage: 'Search',\n                  })}\n                >\n                  {formatMessage({\n                    id: 'admin.pages.MarketPlacePage.search.placeholder',\n                    defaultMessage: 'Search',\n                  })}\n                </Searchbar>\n              </Box>\n            </Flex>\n            <Flex paddingBottom={4} gap={2}>\n              <SortSelect\n                sortQuery={query?.sort || 'name:asc'}\n                handleSelectChange={handleSortSelectChange}\n              />\n              <NpmPackagesFilters\n                npmPackageType={npmPackageType}\n                possibleCollections={possibleCollections}\n                possibleCategories={possibleCategories}\n                query={query || {}}\n                handleSelectChange={handleSelectChange}\n                handleSelectClear={handleSelectClear}\n              />\n            </Flex>\n            <Tabs.Content value={PLUGIN}>\n              <NpmPackagesGrid\n                npmPackages={pluginsResponse?.data}\n                status={pluginsStatus}\n                installedPackageNames={installedPackageNames}\n                useYarn={useYarn}\n                isInDevelopmentMode={isInDevelopmentMode}\n                npmPackageType=\"plugin\"\n                strapiAppVersion={strapiVersion}\n                debouncedSearch={debouncedSearch}\n              />\n            </Tabs.Content>\n            <Tabs.Content value={PROVIDER}>\n              <NpmPackagesGrid\n                npmPackages={providersResponse?.data}\n                status={providersStatus}\n                installedPackageNames={installedPackageNames}\n                useYarn={useYarn}\n                isInDevelopmentMode={isInDevelopmentMode}\n                npmPackageType=\"provider\"\n                debouncedSearch={debouncedSearch}\n              />\n            </Tabs.Content>\n            <Pagination.Root {...pagination} defaultPageSize={24}>\n              <Pagination.PageSize options={['12', '24', '50', '100']} />\n              <Pagination.Links />\n            </Pagination.Root>\n            <Box paddingTop={8}>\n              <a\n                href=\"https://strapi.canny.io/plugin-requests\"\n                target=\"_blank\"\n                rel=\"noopener noreferrer nofollow\"\n                style={{ textDecoration: 'none' }}\n                onClick={() => trackUsage('didMissMarketplacePlugin')}\n              >\n                <ContentBox\n                  title={formatMessage({\n                    id: 'admin.pages.MarketPlacePage.missingPlugin.title',\n                    defaultMessage: 'Documentation',\n                  })}\n                  subtitle={formatMessage({\n                    id: 'admin.pages.MarketPlacePage.missingPlugin.description',\n                    defaultMessage:\n                      \"Tell us what plugin you are looking for and we'll let our community plugin developers know in case they are in search for inspiration!\",\n                  })}\n                  icon={<GlassesSquare />}\n                  iconBackground=\"alternative100\"\n                  endAction={\n                    <ExternalLink\n                      fill=\"neutral600\"\n                      width=\"1.2rem\"\n                      height=\"1.2rem\"\n                      style={{ marginLeft: '0.8rem' }}\n                    />\n                  }\n                />\n              </a>\n            </Box>\n          </Tabs.Root>\n        </Layouts.Content>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\nconst ProtectedMarketplacePage = () => {\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.marketplace?.main);\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <MarketplacePage />\n    </Page.Protect>\n  );\n};\n\nexport { MarketplacePage, ProtectedMarketplacePage };\nexport type { NpmPackageType, MarketplacePageQuery, TabQuery };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,QAAQ,CAAC,SAAS,YAAY;AAClC,YAAM,IAAI,MAAM,QAAQ,KAAK,EAAE,QAAQ,UAAU,EAAE,GAAG,OAAO;AAC7D,aAAO,IAAI,EAAE,UAAU;AAAA,IACzB;AACA,WAAO,UAAU;AAAA;AAAA;;;ACLjB;AAAA;AAAA,QAAM,SAAS;AAEf,QAAM,MAAM,CAAC,SAAS,SAAS,SAAS,YAAY,mBAAmB;AACrE,UAAI,OAAQ,YAAa,UAAU;AACjC,yBAAiB;AACjB,qBAAa;AACb,kBAAU;AAAA,MACZ;AAEA,UAAI;AACF,eAAO,IAAI;AAAA,UACT,mBAAmB,SAAS,QAAQ,UAAU;AAAA,UAC9C;AAAA,QACF,EAAE,IAAI,SAAS,YAAY,cAAc,EAAE;AAAA,MAC7C,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;AClBjB;AAAA;AAAA,QAAM,QAAQ;AAEd,QAAM,OAAO,CAAC,UAAU,aAAa;AACnC,YAAM,KAAK,MAAM,UAAU,MAAM,IAAI;AACrC,YAAM,KAAK,MAAM,UAAU,MAAM,IAAI;AACrC,YAAM,aAAa,GAAG,QAAQ,EAAE;AAEhC,UAAI,eAAe,GAAG;AACpB,eAAO;AAAA,MACT;AAEA,YAAM,WAAW,aAAa;AAC9B,YAAM,cAAc,WAAW,KAAK;AACpC,YAAM,aAAa,WAAW,KAAK;AACnC,YAAM,aAAa,CAAC,CAAC,YAAY,WAAW;AAC5C,YAAM,YAAY,CAAC,CAAC,WAAW,WAAW;AAE1C,UAAI,aAAa,CAAC,YAAY;AAQ5B,YAAI,CAAC,WAAW,SAAS,CAAC,WAAW,OAAO;AAC1C,iBAAO;AAAA,QACT;AAIA,YAAI,YAAY,OAAO;AAErB,iBAAO;AAAA,QACT;AAEA,YAAI,YAAY,OAAO;AAErB,iBAAO;AAAA,QACT;AAGA,eAAO;AAAA,MACT;AAGA,YAAM,SAAS,aAAa,QAAQ;AAEpC,UAAI,GAAG,UAAU,GAAG,OAAO;AACzB,eAAO,SAAS;AAAA,MAClB;AAEA,UAAI,GAAG,UAAU,GAAG,OAAO;AACzB,eAAO,SAAS;AAAA,MAClB;AAEA,UAAI,GAAG,UAAU,GAAG,OAAO;AACzB,eAAO,SAAS;AAAA,MAClB;AAGA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AChEjB;AAAA;AAAA,QAAM,SAAS;AACf,QAAM,QAAQ,CAAC,GAAG,UAAU,IAAI,OAAO,GAAG,KAAK,EAAE;AACjD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,SAAS;AACf,QAAM,QAAQ,CAAC,GAAG,UAAU,IAAI,OAAO,GAAG,KAAK,EAAE;AACjD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,SAAS;AACf,QAAM,QAAQ,CAAC,GAAG,UAAU,IAAI,OAAO,GAAG,KAAK,EAAE;AACjD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,aAAa,CAAC,SAAS,YAAY;AACvC,YAAM,SAAS,MAAM,SAAS,OAAO;AACrC,aAAQ,UAAU,OAAO,WAAW,SAAU,OAAO,aAAa;AAAA,IACpE;AACA,WAAO,UAAU;AAAA;AAAA;;;ACLjB;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,WAAW,CAAC,GAAG,GAAG,UAAU,QAAQ,GAAG,GAAG,KAAK;AACrD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,eAAe,CAAC,GAAG,MAAM,QAAQ,GAAG,GAAG,IAAI;AACjD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,SAAS;AACf,QAAM,eAAe,CAAC,GAAG,GAAG,UAAU;AACpC,YAAM,WAAW,IAAI,OAAO,GAAG,KAAK;AACpC,YAAM,WAAW,IAAI,OAAO,GAAG,KAAK;AACpC,aAAO,SAAS,QAAQ,QAAQ,KAAK,SAAS,aAAa,QAAQ;AAAA,IACrE;AACA,WAAO,UAAU;AAAA;AAAA;;;ACNjB;AAAA;AAAA,QAAM,eAAe;AACrB,QAAM,OAAO,CAAC,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,MAAM,aAAa,GAAG,GAAG,KAAK,CAAC;AAC3E,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,eAAe;AACrB,QAAM,QAAQ,CAAC,MAAM,UAAU,KAAK,KAAK,CAAC,GAAG,MAAM,aAAa,GAAG,GAAG,KAAK,CAAC;AAC5E,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,KAAK,CAAC,GAAG,GAAG,UAAU,QAAQ,GAAG,GAAG,KAAK,IAAI;AACnD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,KAAK,CAAC,GAAG,GAAG,UAAU,QAAQ,GAAG,GAAG,KAAK,MAAM;AACrD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,MAAM,CAAC,GAAG,GAAG,UAAU,QAAQ,GAAG,GAAG,KAAK,MAAM;AACtD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,MAAM,CAAC,GAAG,GAAG,UAAU,QAAQ,GAAG,GAAG,KAAK,KAAK;AACrD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,UAAU;AAChB,QAAM,MAAM,CAAC,GAAG,GAAG,UAAU,QAAQ,GAAG,GAAG,KAAK,KAAK;AACrD,WAAO,UAAU;AAAA;AAAA;;;ACFjB;AAAA;AAAA,QAAM,KAAK;AACX,QAAM,MAAM;AACZ,QAAM,KAAK;AACX,QAAM,MAAM;AACZ,QAAM,KAAK;AACX,QAAM,MAAM;AAEZ,QAAM,MAAM,CAAC,GAAG,IAAI,GAAG,UAAU;AAC/B,cAAQ,IAAI;AAAA,QACV,KAAK;AACH,cAAI,OAAO,MAAM,UAAU;AACzB,gBAAI,EAAE;AAAA,UACR;AACA,cAAI,OAAO,MAAM,UAAU;AACzB,gBAAI,EAAE;AAAA,UACR;AACA,iBAAO,MAAM;AAAA,QAEf,KAAK;AACH,cAAI,OAAO,MAAM,UAAU;AACzB,gBAAI,EAAE;AAAA,UACR;AACA,cAAI,OAAO,MAAM,UAAU;AACzB,gBAAI,EAAE;AAAA,UACR;AACA,iBAAO,MAAM;AAAA,QAEf,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,iBAAO,GAAG,GAAG,GAAG,KAAK;AAAA,QAEvB,KAAK;AACH,iBAAO,IAAI,GAAG,GAAG,KAAK;AAAA,QAExB,KAAK;AACH,iBAAO,GAAG,GAAG,GAAG,KAAK;AAAA,QAEvB,KAAK;AACH,iBAAO,IAAI,GAAG,GAAG,KAAK;AAAA,QAExB,KAAK;AACH,iBAAO,GAAG,GAAG,GAAG,KAAK;AAAA,QAEvB,KAAK;AACH,iBAAO,IAAI,GAAG,GAAG,KAAK;AAAA,QAExB;AACE,gBAAM,IAAI,UAAU,qBAAqB,EAAE,EAAE;AAAA,MACjD;AAAA,IACF;AACA,WAAO,UAAU;AAAA;AAAA;;;ACnDjB;AAAA;AAAA,QAAM,SAAS;AACf,QAAM,QAAQ;AACd,QAAM,EAAE,QAAQ,IAAI,EAAE,IAAI;AAE1B,QAAM,SAAS,CAAC,SAAS,YAAY;AACnC,UAAI,mBAAmB,QAAQ;AAC7B,eAAO;AAAA,MACT;AAEA,UAAI,OAAO,YAAY,UAAU;AAC/B,kBAAU,OAAO,OAAO;AAAA,MAC1B;AAEA,UAAI,OAAO,YAAY,UAAU;AAC/B,eAAO;AAAA,MACT;AAEA,gBAAU,WAAW,CAAC;AAEtB,UAAI,QAAQ;AACZ,UAAI,CAAC,QAAQ,KAAK;AAChB,gBAAQ,QAAQ,MAAM,GAAG,EAAE,MAAM,CAAC;AAAA,MACpC,OAAO;AASL,YAAI;AACJ,gBAAQ,OAAO,GAAG,EAAE,SAAS,EAAE,KAAK,OAAO,OACtC,CAAC,SAAS,MAAM,QAAQ,MAAM,CAAC,EAAE,WAAW,QAAQ,SACvD;AACA,cAAI,CAAC,SACC,KAAK,QAAQ,KAAK,CAAC,EAAE,WAAW,MAAM,QAAQ,MAAM,CAAC,EAAE,QAAQ;AACnE,oBAAQ;AAAA,UACV;AACA,aAAG,EAAE,SAAS,EAAE,YAAY,KAAK,QAAQ,KAAK,CAAC,EAAE,SAAS,KAAK,CAAC,EAAE;AAAA,QACpE;AAEA,WAAG,EAAE,SAAS,EAAE,YAAY;AAAA,MAC9B;AAEA,UAAI,UAAU,MAAM;AAClB,eAAO;AAAA,MACT;AAEA,aAAO,MAAM,GAAG,MAAM,CAAC,CAAC,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,MAAM,CAAC,KAAK,GAAG,IAAI,OAAO;AAAA,IAC3E;AACA,WAAO,UAAU;AAAA;AAAA;;;ACnDjB;AAAA;AAAA;AACA,WAAO,UAAU,SAAU,SAAS;AAClC,cAAQ,UAAU,OAAO,QAAQ,IAAI,aAAa;AAChD,iBAAS,SAAS,KAAK,MAAM,QAAQ,SAAS,OAAO,MAAM;AACzD,gBAAM,OAAO;AAAA,QACf;AAAA,MACF;AAAA,IACF;AAAA;AAAA;;;ACPA;AAAA;AAAA;AACA,WAAO,UAAU;AAEjB,YAAQ,OAAO;AACf,YAAQ,SAAS;AAEjB,aAAS,QAAS,MAAM;AACtB,UAAI,OAAO;AACX,UAAI,EAAE,gBAAgB,UAAU;AAC9B,eAAO,IAAI,QAAQ;AAAA,MACrB;AAEA,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,SAAS;AAEd,UAAI,QAAQ,OAAO,KAAK,YAAY,YAAY;AAC9C,aAAK,QAAQ,SAAU,MAAM;AAC3B,eAAK,KAAK,IAAI;AAAA,QAChB,CAAC;AAAA,MACH,WAAW,UAAU,SAAS,GAAG;AAC/B,iBAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,eAAK,KAAK,UAAU,CAAC,CAAC;AAAA,QACxB;AAAA,MACF;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,aAAa,SAAU,MAAM;AAC7C,UAAI,KAAK,SAAS,MAAM;AACtB,cAAM,IAAI,MAAM,kDAAkD;AAAA,MACpE;AAEA,UAAI,OAAO,KAAK;AAChB,UAAI,OAAO,KAAK;AAEhB,UAAI,MAAM;AACR,aAAK,OAAO;AAAA,MACd;AAEA,UAAI,MAAM;AACR,aAAK,OAAO;AAAA,MACd;AAEA,UAAI,SAAS,KAAK,MAAM;AACtB,aAAK,OAAO;AAAA,MACd;AACA,UAAI,SAAS,KAAK,MAAM;AACtB,aAAK,OAAO;AAAA,MACd;AAEA,WAAK,KAAK;AACV,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,WAAK,OAAO;AAEZ,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,cAAc,SAAU,MAAM;AAC9C,UAAI,SAAS,KAAK,MAAM;AACtB;AAAA,MACF;AAEA,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,WAAW,IAAI;AAAA,MAC3B;AAEA,UAAI,OAAO,KAAK;AAChB,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,UAAI,MAAM;AACR,aAAK,OAAO;AAAA,MACd;AAEA,WAAK,OAAO;AACZ,UAAI,CAAC,KAAK,MAAM;AACd,aAAK,OAAO;AAAA,MACd;AACA,WAAK;AAAA,IACP;AAEA,YAAQ,UAAU,WAAW,SAAU,MAAM;AAC3C,UAAI,SAAS,KAAK,MAAM;AACtB;AAAA,MACF;AAEA,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,WAAW,IAAI;AAAA,MAC3B;AAEA,UAAI,OAAO,KAAK;AAChB,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,UAAI,MAAM;AACR,aAAK,OAAO;AAAA,MACd;AAEA,WAAK,OAAO;AACZ,UAAI,CAAC,KAAK,MAAM;AACd,aAAK,OAAO;AAAA,MACd;AACA,WAAK;AAAA,IACP;AAEA,YAAQ,UAAU,OAAO,WAAY;AACnC,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,aAAK,MAAM,UAAU,CAAC,CAAC;AAAA,MACzB;AACA,aAAO,KAAK;AAAA,IACd;AAEA,YAAQ,UAAU,UAAU,WAAY;AACtC,eAAS,IAAI,GAAG,IAAI,UAAU,QAAQ,IAAI,GAAG,KAAK;AAChD,gBAAQ,MAAM,UAAU,CAAC,CAAC;AAAA,MAC5B;AACA,aAAO,KAAK;AAAA,IACd;AAEA,YAAQ,UAAU,MAAM,WAAY;AAClC,UAAI,CAAC,KAAK,MAAM;AACd,eAAO;AAAA,MACT;AAEA,UAAI,MAAM,KAAK,KAAK;AACpB,WAAK,OAAO,KAAK,KAAK;AACtB,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,OAAO;AAAA,MACnB,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AACA,WAAK;AACL,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,QAAQ,WAAY;AACpC,UAAI,CAAC,KAAK,MAAM;AACd,eAAO;AAAA,MACT;AAEA,UAAI,MAAM,KAAK,KAAK;AACpB,WAAK,OAAO,KAAK,KAAK;AACtB,UAAI,KAAK,MAAM;AACb,aAAK,KAAK,OAAO;AAAA,MACnB,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AACA,WAAK;AACL,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,UAAU,SAAU,IAAI,OAAO;AAC/C,cAAQ,SAAS;AACjB,eAAS,SAAS,KAAK,MAAM,IAAI,GAAG,WAAW,MAAM,KAAK;AACxD,WAAG,KAAK,OAAO,OAAO,OAAO,GAAG,IAAI;AACpC,iBAAS,OAAO;AAAA,MAClB;AAAA,IACF;AAEA,YAAQ,UAAU,iBAAiB,SAAU,IAAI,OAAO;AACtD,cAAQ,SAAS;AACjB,eAAS,SAAS,KAAK,MAAM,IAAI,KAAK,SAAS,GAAG,WAAW,MAAM,KAAK;AACtE,WAAG,KAAK,OAAO,OAAO,OAAO,GAAG,IAAI;AACpC,iBAAS,OAAO;AAAA,MAClB;AAAA,IACF;AAEA,YAAQ,UAAU,MAAM,SAAU,GAAG;AACnC,eAAS,IAAI,GAAG,SAAS,KAAK,MAAM,WAAW,QAAQ,IAAI,GAAG,KAAK;AAEjE,iBAAS,OAAO;AAAA,MAClB;AACA,UAAI,MAAM,KAAK,WAAW,MAAM;AAC9B,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAEA,YAAQ,UAAU,aAAa,SAAU,GAAG;AAC1C,eAAS,IAAI,GAAG,SAAS,KAAK,MAAM,WAAW,QAAQ,IAAI,GAAG,KAAK;AAEjE,iBAAS,OAAO;AAAA,MAClB;AACA,UAAI,MAAM,KAAK,WAAW,MAAM;AAC9B,eAAO,OAAO;AAAA,MAChB;AAAA,IACF;AAEA,YAAQ,UAAU,MAAM,SAAU,IAAI,OAAO;AAC3C,cAAQ,SAAS;AACjB,UAAI,MAAM,IAAI,QAAQ;AACtB,eAAS,SAAS,KAAK,MAAM,WAAW,QAAO;AAC7C,YAAI,KAAK,GAAG,KAAK,OAAO,OAAO,OAAO,IAAI,CAAC;AAC3C,iBAAS,OAAO;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,aAAa,SAAU,IAAI,OAAO;AAClD,cAAQ,SAAS;AACjB,UAAI,MAAM,IAAI,QAAQ;AACtB,eAAS,SAAS,KAAK,MAAM,WAAW,QAAO;AAC7C,YAAI,KAAK,GAAG,KAAK,OAAO,OAAO,OAAO,IAAI,CAAC;AAC3C,iBAAS,OAAO;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,SAAS,SAAU,IAAI,SAAS;AAChD,UAAI;AACJ,UAAI,SAAS,KAAK;AAClB,UAAI,UAAU,SAAS,GAAG;AACxB,cAAM;AAAA,MACR,WAAW,KAAK,MAAM;AACpB,iBAAS,KAAK,KAAK;AACnB,cAAM,KAAK,KAAK;AAAA,MAClB,OAAO;AACL,cAAM,IAAI,UAAU,4CAA4C;AAAA,MAClE;AAEA,eAAS,IAAI,GAAG,WAAW,MAAM,KAAK;AACpC,cAAM,GAAG,KAAK,OAAO,OAAO,CAAC;AAC7B,iBAAS,OAAO;AAAA,MAClB;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,gBAAgB,SAAU,IAAI,SAAS;AACvD,UAAI;AACJ,UAAI,SAAS,KAAK;AAClB,UAAI,UAAU,SAAS,GAAG;AACxB,cAAM;AAAA,MACR,WAAW,KAAK,MAAM;AACpB,iBAAS,KAAK,KAAK;AACnB,cAAM,KAAK,KAAK;AAAA,MAClB,OAAO;AACL,cAAM,IAAI,UAAU,4CAA4C;AAAA,MAClE;AAEA,eAAS,IAAI,KAAK,SAAS,GAAG,WAAW,MAAM,KAAK;AAClD,cAAM,GAAG,KAAK,OAAO,OAAO,CAAC;AAC7B,iBAAS,OAAO;AAAA,MAClB;AAEA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,UAAU,WAAY;AACtC,UAAI,MAAM,IAAI,MAAM,KAAK,MAAM;AAC/B,eAAS,IAAI,GAAG,SAAS,KAAK,MAAM,WAAW,MAAM,KAAK;AACxD,YAAI,CAAC,IAAI,OAAO;AAChB,iBAAS,OAAO;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,iBAAiB,WAAY;AAC7C,UAAI,MAAM,IAAI,MAAM,KAAK,MAAM;AAC/B,eAAS,IAAI,GAAG,SAAS,KAAK,MAAM,WAAW,MAAM,KAAK;AACxD,YAAI,CAAC,IAAI,OAAO;AAChB,iBAAS,OAAO;AAAA,MAClB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,QAAQ,SAAU,MAAM,IAAI;AAC5C,WAAK,MAAM,KAAK;AAChB,UAAI,KAAK,GAAG;AACV,cAAM,KAAK;AAAA,MACb;AACA,aAAO,QAAQ;AACf,UAAI,OAAO,GAAG;AACZ,gBAAQ,KAAK;AAAA,MACf;AACA,UAAI,MAAM,IAAI,QAAQ;AACtB,UAAI,KAAK,QAAQ,KAAK,GAAG;AACvB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,GAAG;AACZ,eAAO;AAAA,MACT;AACA,UAAI,KAAK,KAAK,QAAQ;AACpB,aAAK,KAAK;AAAA,MACZ;AACA,eAAS,IAAI,GAAG,SAAS,KAAK,MAAM,WAAW,QAAQ,IAAI,MAAM,KAAK;AACpE,iBAAS,OAAO;AAAA,MAClB;AACA,aAAO,WAAW,QAAQ,IAAI,IAAI,KAAK,SAAS,OAAO,MAAM;AAC3D,YAAI,KAAK,OAAO,KAAK;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,eAAe,SAAU,MAAM,IAAI;AACnD,WAAK,MAAM,KAAK;AAChB,UAAI,KAAK,GAAG;AACV,cAAM,KAAK;AAAA,MACb;AACA,aAAO,QAAQ;AACf,UAAI,OAAO,GAAG;AACZ,gBAAQ,KAAK;AAAA,MACf;AACA,UAAI,MAAM,IAAI,QAAQ;AACtB,UAAI,KAAK,QAAQ,KAAK,GAAG;AACvB,eAAO;AAAA,MACT;AACA,UAAI,OAAO,GAAG;AACZ,eAAO;AAAA,MACT;AACA,UAAI,KAAK,KAAK,QAAQ;AACpB,aAAK,KAAK;AAAA,MACZ;AACA,eAAS,IAAI,KAAK,QAAQ,SAAS,KAAK,MAAM,WAAW,QAAQ,IAAI,IAAI,KAAK;AAC5E,iBAAS,OAAO;AAAA,MAClB;AACA,aAAO,WAAW,QAAQ,IAAI,MAAM,KAAK,SAAS,OAAO,MAAM;AAC7D,YAAI,KAAK,OAAO,KAAK;AAAA,MACvB;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,SAAS,SAAU,OAAO,gBAAgB,OAAO;AACjE,UAAI,QAAQ,KAAK,QAAQ;AACvB,gBAAQ,KAAK,SAAS;AAAA,MACxB;AACA,UAAI,QAAQ,GAAG;AACb,gBAAQ,KAAK,SAAS;AAAA,MACxB;AAEA,eAAS,IAAI,GAAG,SAAS,KAAK,MAAM,WAAW,QAAQ,IAAI,OAAO,KAAK;AACrE,iBAAS,OAAO;AAAA,MAClB;AAEA,UAAI,MAAM,CAAC;AACX,eAAS,IAAI,GAAG,UAAU,IAAI,aAAa,KAAK;AAC9C,YAAI,KAAK,OAAO,KAAK;AACrB,iBAAS,KAAK,WAAW,MAAM;AAAA,MACjC;AACA,UAAI,WAAW,MAAM;AACnB,iBAAS,KAAK;AAAA,MAChB;AAEA,UAAI,WAAW,KAAK,QAAQ,WAAW,KAAK,MAAM;AAChD,iBAAS,OAAO;AAAA,MAClB;AAEA,eAAS,IAAI,GAAG,IAAI,MAAM,QAAQ,KAAK;AACrC,iBAAS,OAAO,MAAM,QAAQ,MAAM,CAAC,CAAC;AAAA,MACxC;AACA,aAAO;AAAA,IACT;AAEA,YAAQ,UAAU,UAAU,WAAY;AACtC,UAAI,OAAO,KAAK;AAChB,UAAI,OAAO,KAAK;AAChB,eAAS,SAAS,MAAM,WAAW,MAAM,SAAS,OAAO,MAAM;AAC7D,YAAI,IAAI,OAAO;AACf,eAAO,OAAO,OAAO;AACrB,eAAO,OAAO;AAAA,MAChB;AACA,WAAK,OAAO;AACZ,WAAK,OAAO;AACZ,aAAO;AAAA,IACT;AAEA,aAAS,OAAQ,MAAM,MAAM,OAAO;AAClC,UAAI,WAAW,SAAS,KAAK,OAC3B,IAAI,KAAK,OAAO,MAAM,MAAM,IAAI,IAChC,IAAI,KAAK,OAAO,MAAM,KAAK,MAAM,IAAI;AAEvC,UAAI,SAAS,SAAS,MAAM;AAC1B,aAAK,OAAO;AAAA,MACd;AACA,UAAI,SAAS,SAAS,MAAM;AAC1B,aAAK,OAAO;AAAA,MACd;AAEA,WAAK;AAEL,aAAO;AAAA,IACT;AAEA,aAAS,KAAM,MAAM,MAAM;AACzB,WAAK,OAAO,IAAI,KAAK,MAAM,KAAK,MAAM,MAAM,IAAI;AAChD,UAAI,CAAC,KAAK,MAAM;AACd,aAAK,OAAO,KAAK;AAAA,MACnB;AACA,WAAK;AAAA,IACP;AAEA,aAAS,QAAS,MAAM,MAAM;AAC5B,WAAK,OAAO,IAAI,KAAK,MAAM,MAAM,KAAK,MAAM,IAAI;AAChD,UAAI,CAAC,KAAK,MAAM;AACd,aAAK,OAAO,KAAK;AAAA,MACnB;AACA,WAAK;AAAA,IACP;AAEA,aAAS,KAAM,OAAO,MAAM,MAAM,MAAM;AACtC,UAAI,EAAE,gBAAgB,OAAO;AAC3B,eAAO,IAAI,KAAK,OAAO,MAAM,MAAM,IAAI;AAAA,MACzC;AAEA,WAAK,OAAO;AACZ,WAAK,QAAQ;AAEb,UAAI,MAAM;AACR,aAAK,OAAO;AACZ,aAAK,OAAO;AAAA,MACd,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AAEA,UAAI,MAAM;AACR,aAAK,OAAO;AACZ,aAAK,OAAO;AAAA,MACd,OAAO;AACL,aAAK,OAAO;AAAA,MACd;AAAA,IACF;AAEA,QAAI;AAEF,yBAAyB,OAAO;AAAA,IAClC,SAAS,IAAI;AAAA,IAAC;AAAA;AAAA;;;ACzad;AAAA;AAAA;AAGA,QAAM,UAAU;AAEhB,QAAM,MAAM,OAAO,KAAK;AACxB,QAAM,SAAS,OAAO,QAAQ;AAC9B,QAAM,oBAAoB,OAAO,kBAAkB;AACnD,QAAM,cAAc,OAAO,YAAY;AACvC,QAAM,UAAU,OAAO,QAAQ;AAC/B,QAAM,UAAU,OAAO,SAAS;AAChC,QAAM,oBAAoB,OAAO,gBAAgB;AACjD,QAAM,WAAW,OAAO,SAAS;AACjC,QAAM,QAAQ,OAAO,OAAO;AAC5B,QAAM,oBAAoB,OAAO,gBAAgB;AAEjD,QAAM,cAAc,MAAM;AAU1B,QAAM,WAAN,MAAe;AAAA,MACb,YAAa,SAAS;AACpB,YAAI,OAAO,YAAY;AACrB,oBAAU,EAAE,KAAK,QAAQ;AAE3B,YAAI,CAAC;AACH,oBAAU,CAAC;AAEb,YAAI,QAAQ,QAAQ,OAAO,QAAQ,QAAQ,YAAY,QAAQ,MAAM;AACnE,gBAAM,IAAI,UAAU,mCAAmC;AAEzD,cAAM,MAAM,KAAK,GAAG,IAAI,QAAQ,OAAO;AAEvC,cAAM,KAAK,QAAQ,UAAU;AAC7B,aAAK,iBAAiB,IAAK,OAAO,OAAO,aAAc,cAAc;AACrE,aAAK,WAAW,IAAI,QAAQ,SAAS;AACrC,YAAI,QAAQ,UAAU,OAAO,QAAQ,WAAW;AAC9C,gBAAM,IAAI,UAAU,yBAAyB;AAC/C,aAAK,OAAO,IAAI,QAAQ,UAAU;AAClC,aAAK,OAAO,IAAI,QAAQ;AACxB,aAAK,iBAAiB,IAAI,QAAQ,kBAAkB;AACpD,aAAK,iBAAiB,IAAI,QAAQ,kBAAkB;AACpD,aAAK,MAAM;AAAA,MACb;AAAA;AAAA,MAGA,IAAI,IAAK,IAAI;AACX,YAAI,OAAO,OAAO,YAAY,KAAK;AACjC,gBAAM,IAAI,UAAU,mCAAmC;AAEzD,aAAK,GAAG,IAAI,MAAM;AAClB,aAAK,IAAI;AAAA,MACX;AAAA,MACA,IAAI,MAAO;AACT,eAAO,KAAK,GAAG;AAAA,MACjB;AAAA,MAEA,IAAI,WAAY,YAAY;AAC1B,aAAK,WAAW,IAAI,CAAC,CAAC;AAAA,MACxB;AAAA,MACA,IAAI,aAAc;AAChB,eAAO,KAAK,WAAW;AAAA,MACzB;AAAA,MAEA,IAAI,OAAQ,IAAI;AACd,YAAI,OAAO,OAAO;AAChB,gBAAM,IAAI,UAAU,sCAAsC;AAE5D,aAAK,OAAO,IAAI;AAChB,aAAK,IAAI;AAAA,MACX;AAAA,MACA,IAAI,SAAU;AACZ,eAAO,KAAK,OAAO;AAAA,MACrB;AAAA;AAAA,MAGA,IAAI,iBAAkB,IAAI;AACxB,YAAI,OAAO,OAAO;AAChB,eAAK;AAEP,YAAI,OAAO,KAAK,iBAAiB,GAAG;AAClC,eAAK,iBAAiB,IAAI;AAC1B,eAAK,MAAM,IAAI;AACf,eAAK,QAAQ,EAAE,QAAQ,SAAO;AAC5B,gBAAI,SAAS,KAAK,iBAAiB,EAAE,IAAI,OAAO,IAAI,GAAG;AACvD,iBAAK,MAAM,KAAK,IAAI;AAAA,UACtB,CAAC;AAAA,QACH;AACA,aAAK,IAAI;AAAA,MACX;AAAA,MACA,IAAI,mBAAoB;AAAE,eAAO,KAAK,iBAAiB;AAAA,MAAE;AAAA,MAEzD,IAAI,SAAU;AAAE,eAAO,KAAK,MAAM;AAAA,MAAE;AAAA,MACpC,IAAI,YAAa;AAAE,eAAO,KAAK,QAAQ,EAAE;AAAA,MAAO;AAAA,MAEhD,SAAU,IAAI,OAAO;AACnB,gBAAQ,SAAS;AACjB,iBAAS,SAAS,KAAK,QAAQ,EAAE,MAAM,WAAW,QAAO;AACvD,gBAAM,OAAO,OAAO;AACpB,sBAAY,MAAM,IAAI,QAAQ,KAAK;AACnC,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,MAEA,QAAS,IAAI,OAAO;AAClB,gBAAQ,SAAS;AACjB,iBAAS,SAAS,KAAK,QAAQ,EAAE,MAAM,WAAW,QAAO;AACvD,gBAAM,OAAO,OAAO;AACpB,sBAAY,MAAM,IAAI,QAAQ,KAAK;AACnC,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,MAEA,OAAQ;AACN,eAAO,KAAK,QAAQ,EAAE,QAAQ,EAAE,IAAI,OAAK,EAAE,GAAG;AAAA,MAChD;AAAA,MAEA,SAAU;AACR,eAAO,KAAK,QAAQ,EAAE,QAAQ,EAAE,IAAI,OAAK,EAAE,KAAK;AAAA,MAClD;AAAA,MAEA,QAAS;AACP,YAAI,KAAK,OAAO,KACZ,KAAK,QAAQ,KACb,KAAK,QAAQ,EAAE,QAAQ;AACzB,eAAK,QAAQ,EAAE,QAAQ,SAAO,KAAK,OAAO,EAAE,IAAI,KAAK,IAAI,KAAK,CAAC;AAAA,QACjE;AAEA,aAAK,KAAK,IAAI,oBAAI,IAAI;AACtB,aAAK,QAAQ,IAAI,IAAI,QAAQ;AAC7B,aAAK,MAAM,IAAI;AAAA,MACjB;AAAA,MAEA,OAAQ;AACN,eAAO,KAAK,QAAQ,EAAE,IAAI,SACxB,QAAQ,MAAM,GAAG,IAAI,QAAQ;AAAA,UAC3B,GAAG,IAAI;AAAA,UACP,GAAG,IAAI;AAAA,UACP,GAAG,IAAI,OAAO,IAAI,UAAU;AAAA,QAC9B,CAAC,EAAE,QAAQ,EAAE,OAAO,OAAK,CAAC;AAAA,MAC9B;AAAA,MAEA,UAAW;AACT,eAAO,KAAK,QAAQ;AAAA,MACtB;AAAA,MAEA,IAAK,KAAK,OAAO,QAAQ;AACvB,iBAAS,UAAU,KAAK,OAAO;AAE/B,YAAI,UAAU,OAAO,WAAW;AAC9B,gBAAM,IAAI,UAAU,yBAAyB;AAE/C,cAAM,MAAM,SAAS,KAAK,IAAI,IAAI;AAClC,cAAM,MAAM,KAAK,iBAAiB,EAAE,OAAO,GAAG;AAE9C,YAAI,KAAK,KAAK,EAAE,IAAI,GAAG,GAAG;AACxB,cAAI,MAAM,KAAK,GAAG,GAAG;AACnB,gBAAI,MAAM,KAAK,KAAK,EAAE,IAAI,GAAG,CAAC;AAC9B,mBAAO;AAAA,UACT;AAEA,gBAAM,OAAO,KAAK,KAAK,EAAE,IAAI,GAAG;AAChC,gBAAM,OAAO,KAAK;AAIlB,cAAI,KAAK,OAAO,GAAG;AACjB,gBAAI,CAAC,KAAK,iBAAiB;AACzB,mBAAK,OAAO,EAAE,KAAK,KAAK,KAAK;AAAA,UACjC;AAEA,eAAK,MAAM;AACX,eAAK,SAAS;AACd,eAAK,QAAQ;AACb,eAAK,MAAM,KAAK,MAAM,KAAK;AAC3B,eAAK,SAAS;AACd,eAAK,IAAI,GAAG;AACZ,eAAK,IAAI;AACT,iBAAO;AAAA,QACT;AAEA,cAAM,MAAM,IAAI,MAAM,KAAK,OAAO,KAAK,KAAK,MAAM;AAGlD,YAAI,IAAI,SAAS,KAAK,GAAG,GAAG;AAC1B,cAAI,KAAK,OAAO;AACd,iBAAK,OAAO,EAAE,KAAK,KAAK;AAE1B,iBAAO;AAAA,QACT;AAEA,aAAK,MAAM,KAAK,IAAI;AACpB,aAAK,QAAQ,EAAE,QAAQ,GAAG;AAC1B,aAAK,KAAK,EAAE,IAAI,KAAK,KAAK,QAAQ,EAAE,IAAI;AACxC,aAAK,IAAI;AACT,eAAO;AAAA,MACT;AAAA,MAEA,IAAK,KAAK;AACR,YAAI,CAAC,KAAK,KAAK,EAAE,IAAI,GAAG;AAAG,iBAAO;AAClC,cAAM,MAAM,KAAK,KAAK,EAAE,IAAI,GAAG,EAAE;AACjC,eAAO,CAAC,QAAQ,MAAM,GAAG;AAAA,MAC3B;AAAA,MAEA,IAAK,KAAK;AACR,eAAO,IAAI,MAAM,KAAK,IAAI;AAAA,MAC5B;AAAA,MAEA,KAAM,KAAK;AACT,eAAO,IAAI,MAAM,KAAK,KAAK;AAAA,MAC7B;AAAA,MAEA,MAAO;AACL,cAAM,OAAO,KAAK,QAAQ,EAAE;AAC5B,YAAI,CAAC;AACH,iBAAO;AAET,YAAI,MAAM,IAAI;AACd,eAAO,KAAK;AAAA,MACd;AAAA,MAEA,IAAK,KAAK;AACR,YAAI,MAAM,KAAK,KAAK,EAAE,IAAI,GAAG,CAAC;AAAA,MAChC;AAAA,MAEA,KAAM,KAAK;AAET,aAAK,MAAM;AAEX,cAAM,MAAM,KAAK,IAAI;AAErB,iBAAS,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,KAAK;AACxC,gBAAM,MAAM,IAAI,CAAC;AACjB,gBAAM,YAAY,IAAI,KAAK;AAC3B,cAAI,cAAc;AAEhB,iBAAK,IAAI,IAAI,GAAG,IAAI,CAAC;AAAA,eAClB;AACH,kBAAM,SAAS,YAAY;AAE3B,gBAAI,SAAS,GAAG;AACd,mBAAK,IAAI,IAAI,GAAG,IAAI,GAAG,MAAM;AAAA,YAC/B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,MAEA,QAAS;AACP,aAAK,KAAK,EAAE,QAAQ,CAAC,OAAO,QAAQ,IAAI,MAAM,KAAK,KAAK,CAAC;AAAA,MAC3D;AAAA,IACF;AAEA,QAAM,MAAM,CAAC,MAAM,KAAK,UAAU;AAChC,YAAM,OAAO,KAAK,KAAK,EAAE,IAAI,GAAG;AAChC,UAAI,MAAM;AACR,cAAM,MAAM,KAAK;AACjB,YAAI,QAAQ,MAAM,GAAG,GAAG;AACtB,cAAI,MAAM,IAAI;AACd,cAAI,CAAC,KAAK,WAAW;AACnB,mBAAO;AAAA,QACX,OAAO;AACL,cAAI,OAAO;AACT,gBAAI,KAAK,iBAAiB;AACxB,mBAAK,MAAM,MAAM,KAAK,IAAI;AAC5B,iBAAK,QAAQ,EAAE,YAAY,IAAI;AAAA,UACjC;AAAA,QACF;AACA,eAAO,IAAI;AAAA,MACb;AAAA,IACF;AAEA,QAAM,UAAU,CAAC,MAAM,QAAQ;AAC7B,UAAI,CAAC,OAAQ,CAAC,IAAI,UAAU,CAAC,KAAK,OAAO;AACvC,eAAO;AAET,YAAM,OAAO,KAAK,IAAI,IAAI,IAAI;AAC9B,aAAO,IAAI,SAAS,OAAO,IAAI,SAC3B,KAAK,OAAO,KAAM,OAAO,KAAK,OAAO;AAAA,IAC3C;AAEA,QAAM,OAAO,UAAQ;AACnB,UAAI,KAAK,MAAM,IAAI,KAAK,GAAG,GAAG;AAC5B,iBAAS,SAAS,KAAK,QAAQ,EAAE,MAC/B,KAAK,MAAM,IAAI,KAAK,GAAG,KAAK,WAAW,QAAO;AAI9C,gBAAM,OAAO,OAAO;AACpB,cAAI,MAAM,MAAM;AAChB,mBAAS;AAAA,QACX;AAAA,MACF;AAAA,IACF;AAEA,QAAM,MAAM,CAAC,MAAM,SAAS;AAC1B,UAAI,MAAM;AACR,cAAM,MAAM,KAAK;AACjB,YAAI,KAAK,OAAO;AACd,eAAK,OAAO,EAAE,IAAI,KAAK,IAAI,KAAK;AAElC,aAAK,MAAM,KAAK,IAAI;AACpB,aAAK,KAAK,EAAE,OAAO,IAAI,GAAG;AAC1B,aAAK,QAAQ,EAAE,WAAW,IAAI;AAAA,MAChC;AAAA,IACF;AAEA,QAAM,QAAN,MAAY;AAAA,MACV,YAAa,KAAK,OAAO,QAAQ,KAAK,QAAQ;AAC5C,aAAK,MAAM;AACX,aAAK,QAAQ;AACb,aAAK,SAAS;AACd,aAAK,MAAM;AACX,aAAK,SAAS,UAAU;AAAA,MAC1B;AAAA,IACF;AAEA,QAAM,cAAc,CAAC,MAAM,IAAI,MAAM,UAAU;AAC7C,UAAI,MAAM,KAAK;AACf,UAAI,QAAQ,MAAM,GAAG,GAAG;AACtB,YAAI,MAAM,IAAI;AACd,YAAI,CAAC,KAAK,WAAW;AACnB,gBAAM;AAAA,MACV;AACA,UAAI;AACF,WAAG,KAAK,OAAO,IAAI,OAAO,IAAI,KAAK,IAAI;AAAA,IAC3C;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC7UjB;AAAA;AACA,QAAM,QAAN,MAAM,OAAM;AAAA,MACV,YAAa,OAAO,SAAS;AAC3B,kBAAU,aAAa,OAAO;AAE9B,YAAI,iBAAiB,QAAO;AAC1B,cACE,MAAM,UAAU,CAAC,CAAC,QAAQ,SAC1B,MAAM,sBAAsB,CAAC,CAAC,QAAQ,mBACtC;AACA,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,IAAI,OAAM,MAAM,KAAK,OAAO;AAAA,UACrC;AAAA,QACF;AAEA,YAAI,iBAAiB,YAAY;AAE/B,eAAK,MAAM,MAAM;AACjB,eAAK,MAAM,CAAC,CAAC,KAAK,CAAC;AACnB,eAAK,OAAO;AACZ,iBAAO;AAAA,QACT;AAEA,aAAK,UAAU;AACf,aAAK,QAAQ,CAAC,CAAC,QAAQ;AACvB,aAAK,oBAAoB,CAAC,CAAC,QAAQ;AAKnC,aAAK,MAAM,MACR,KAAK,EACL,MAAM,KAAK,EACX,KAAK,GAAG;AAGX,aAAK,MAAM,KAAK,IACb,MAAM,IAAI,EAEV,IAAI,OAAK,KAAK,WAAW,EAAE,KAAK,CAAC,CAAC,EAIlC,OAAO,OAAK,EAAE,MAAM;AAEvB,YAAI,CAAC,KAAK,IAAI,QAAQ;AACpB,gBAAM,IAAI,UAAU,yBAAyB,KAAK,GAAG,EAAE;AAAA,QACzD;AAGA,YAAI,KAAK,IAAI,SAAS,GAAG;AAEvB,gBAAM,QAAQ,KAAK,IAAI,CAAC;AACxB,eAAK,MAAM,KAAK,IAAI,OAAO,OAAK,CAAC,UAAU,EAAE,CAAC,CAAC,CAAC;AAChD,cAAI,KAAK,IAAI,WAAW,GAAG;AACzB,iBAAK,MAAM,CAAC,KAAK;AAAA,UACnB,WAAW,KAAK,IAAI,SAAS,GAAG;AAE9B,uBAAW,KAAK,KAAK,KAAK;AACxB,kBAAI,EAAE,WAAW,KAAK,MAAM,EAAE,CAAC,CAAC,GAAG;AACjC,qBAAK,MAAM,CAAC,CAAC;AACb;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAEA,aAAK,OAAO;AAAA,MACd;AAAA,MAEA,SAAU;AACR,aAAK,QAAQ,KAAK,IACf,IAAI,CAAC,UAAU,MAAM,KAAK,GAAG,EAAE,KAAK,CAAC,EACrC,KAAK,IAAI,EACT,KAAK;AACR,eAAO,KAAK;AAAA,MACd;AAAA,MAEA,WAAY;AACV,eAAO,KAAK;AAAA,MACd;AAAA,MAEA,WAAY,OAAO;AAGjB,cAAM,YACH,KAAK,QAAQ,qBAAqB,4BAClC,KAAK,QAAQ,SAAS;AACzB,cAAM,UAAU,WAAW,MAAM;AACjC,cAAM,SAAS,MAAM,IAAI,OAAO;AAChC,YAAI,QAAQ;AACV,iBAAO;AAAA,QACT;AAEA,cAAM,QAAQ,KAAK,QAAQ;AAE3B,cAAM,KAAK,QAAQ,GAAG,EAAE,gBAAgB,IAAI,GAAG,EAAE,WAAW;AAC5D,gBAAQ,MAAM,QAAQ,IAAI,cAAc,KAAK,QAAQ,iBAAiB,CAAC;AACvE,cAAM,kBAAkB,KAAK;AAG7B,gBAAQ,MAAM,QAAQ,GAAG,EAAE,cAAc,GAAG,qBAAqB;AACjE,cAAM,mBAAmB,KAAK;AAG9B,gBAAQ,MAAM,QAAQ,GAAG,EAAE,SAAS,GAAG,gBAAgB;AACvD,cAAM,cAAc,KAAK;AAGzB,gBAAQ,MAAM,QAAQ,GAAG,EAAE,SAAS,GAAG,gBAAgB;AACvD,cAAM,cAAc,KAAK;AAKzB,YAAI,YAAY,MACb,MAAM,GAAG,EACT,IAAI,UAAQ,gBAAgB,MAAM,KAAK,OAAO,CAAC,EAC/C,KAAK,GAAG,EACR,MAAM,KAAK,EAEX,IAAI,UAAQ,YAAY,MAAM,KAAK,OAAO,CAAC;AAE9C,YAAI,OAAO;AAET,sBAAY,UAAU,OAAO,UAAQ;AACnC,kBAAM,wBAAwB,MAAM,KAAK,OAAO;AAChD,mBAAO,CAAC,CAAC,KAAK,MAAM,GAAG,EAAE,eAAe,CAAC;AAAA,UAC3C,CAAC;AAAA,QACH;AACA,cAAM,cAAc,SAAS;AAK7B,cAAM,WAAW,oBAAI,IAAI;AACzB,cAAM,cAAc,UAAU,IAAI,UAAQ,IAAI,WAAW,MAAM,KAAK,OAAO,CAAC;AAC5E,mBAAW,QAAQ,aAAa;AAC9B,cAAI,UAAU,IAAI,GAAG;AACnB,mBAAO,CAAC,IAAI;AAAA,UACd;AACA,mBAAS,IAAI,KAAK,OAAO,IAAI;AAAA,QAC/B;AACA,YAAI,SAAS,OAAO,KAAK,SAAS,IAAI,EAAE,GAAG;AACzC,mBAAS,OAAO,EAAE;AAAA,QACpB;AAEA,cAAM,SAAS,CAAC,GAAG,SAAS,OAAO,CAAC;AACpC,cAAM,IAAI,SAAS,MAAM;AACzB,eAAO;AAAA,MACT;AAAA,MAEA,WAAY,OAAO,SAAS;AAC1B,YAAI,EAAE,iBAAiB,SAAQ;AAC7B,gBAAM,IAAI,UAAU,qBAAqB;AAAA,QAC3C;AAEA,eAAO,KAAK,IAAI,KAAK,CAAC,oBAAoB;AACxC,iBACE,cAAc,iBAAiB,OAAO,KACtC,MAAM,IAAI,KAAK,CAAC,qBAAqB;AACnC,mBACE,cAAc,kBAAkB,OAAO,KACvC,gBAAgB,MAAM,CAAC,mBAAmB;AACxC,qBAAO,iBAAiB,MAAM,CAAC,oBAAoB;AACjD,uBAAO,eAAe,WAAW,iBAAiB,OAAO;AAAA,cAC3D,CAAC;AAAA,YACH,CAAC;AAAA,UAEL,CAAC;AAAA,QAEL,CAAC;AAAA,MACH;AAAA;AAAA,MAGA,KAAM,SAAS;AACb,YAAI,CAAC,SAAS;AACZ,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,YAAY,UAAU;AAC/B,cAAI;AACF,sBAAU,IAAI,OAAO,SAAS,KAAK,OAAO;AAAA,UAC5C,SAAS,IAAI;AACX,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,iBAAS,IAAI,GAAG,IAAI,KAAK,IAAI,QAAQ,KAAK;AACxC,cAAI,QAAQ,KAAK,IAAI,CAAC,GAAG,SAAS,KAAK,OAAO,GAAG;AAC/C,mBAAO;AAAA,UACT;AAAA,QACF;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AAEjB,QAAM,MAAM;AACZ,QAAM,QAAQ,IAAI,IAAI,EAAE,KAAK,IAAK,CAAC;AAEnC,QAAM,eAAe;AACrB,QAAM,aAAa;AACnB,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,QAAM;AAAA,MACJ,QAAQ;AAAA,MACR;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF,IAAI;AACJ,QAAM,EAAE,yBAAyB,WAAW,IAAI;AAEhD,QAAM,YAAY,OAAK,EAAE,UAAU;AACnC,QAAM,QAAQ,OAAK,EAAE,UAAU;AAI/B,QAAM,gBAAgB,CAAC,aAAa,YAAY;AAC9C,UAAI,SAAS;AACb,YAAM,uBAAuB,YAAY,MAAM;AAC/C,UAAI,iBAAiB,qBAAqB,IAAI;AAE9C,aAAO,UAAU,qBAAqB,QAAQ;AAC5C,iBAAS,qBAAqB,MAAM,CAAC,oBAAoB;AACvD,iBAAO,eAAe,WAAW,iBAAiB,OAAO;AAAA,QAC3D,CAAC;AAED,yBAAiB,qBAAqB,IAAI;AAAA,MAC5C;AAEA,aAAO;AAAA,IACT;AAKA,QAAM,kBAAkB,CAAC,MAAM,YAAY;AACzC,YAAM,QAAQ,MAAM,OAAO;AAC3B,aAAO,cAAc,MAAM,OAAO;AAClC,YAAM,SAAS,IAAI;AACnB,aAAO,cAAc,MAAM,OAAO;AAClC,YAAM,UAAU,IAAI;AACpB,aAAO,eAAe,MAAM,OAAO;AACnC,YAAM,UAAU,IAAI;AACpB,aAAO,aAAa,MAAM,OAAO;AACjC,YAAM,SAAS,IAAI;AACnB,aAAO;AAAA,IACT;AAEA,QAAM,MAAM,QAAM,CAAC,MAAM,GAAG,YAAY,MAAM,OAAO,OAAO;AAS5D,QAAM,gBAAgB,CAAC,MAAM,YAAY;AACvC,aAAO,KACJ,KAAK,EACL,MAAM,KAAK,EACX,IAAI,CAAC,MAAM,aAAa,GAAG,OAAO,CAAC,EACnC,KAAK,GAAG;AAAA,IACb;AAEA,QAAM,eAAe,CAAC,MAAM,YAAY;AACtC,YAAM,IAAI,QAAQ,QAAQ,GAAG,EAAE,UAAU,IAAI,GAAG,EAAE,KAAK;AACvD,aAAO,KAAK,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,OAAO;AACzC,cAAM,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE;AACnC,YAAI;AAEJ,YAAI,IAAI,CAAC,GAAG;AACV,gBAAM;AAAA,QACR,WAAW,IAAI,CAAC,GAAG;AACjB,gBAAM,KAAK,CAAC,SAAS,CAAC,IAAI,CAAC;AAAA,QAC7B,WAAW,IAAI,CAAC,GAAG;AAEjB,gBAAM,KAAK,CAAC,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,QACrC,WAAW,IAAI;AACb,gBAAM,mBAAmB,EAAE;AAC3B,gBAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAC1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,QAClB,OAAO;AAEL,gBAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CACrB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,QAClB;AAEA,cAAM,gBAAgB,GAAG;AACzB,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAUA,QAAM,gBAAgB,CAAC,MAAM,YAAY;AACvC,aAAO,KACJ,KAAK,EACL,MAAM,KAAK,EACX,IAAI,CAAC,MAAM,aAAa,GAAG,OAAO,CAAC,EACnC,KAAK,GAAG;AAAA,IACb;AAEA,QAAM,eAAe,CAAC,MAAM,YAAY;AACtC,YAAM,SAAS,MAAM,OAAO;AAC5B,YAAM,IAAI,QAAQ,QAAQ,GAAG,EAAE,UAAU,IAAI,GAAG,EAAE,KAAK;AACvD,YAAM,IAAI,QAAQ,oBAAoB,OAAO;AAC7C,aAAO,KAAK,QAAQ,GAAG,CAAC,GAAG,GAAG,GAAG,GAAG,OAAO;AACzC,cAAM,SAAS,MAAM,GAAG,GAAG,GAAG,GAAG,EAAE;AACnC,YAAI;AAEJ,YAAI,IAAI,CAAC,GAAG;AACV,gBAAM;AAAA,QACR,WAAW,IAAI,CAAC,GAAG;AACjB,gBAAM,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,IAAI,CAAC;AAAA,QACjC,WAAW,IAAI,CAAC,GAAG;AACjB,cAAI,MAAM,KAAK;AACb,kBAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,UACzC,OAAO;AACL,kBAAM,KAAK,CAAC,IAAI,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,CAAC;AAAA,UACpC;AAAA,QACF,WAAW,IAAI;AACb,gBAAM,mBAAmB,EAAE;AAC3B,cAAI,MAAM,KAAK;AACb,gBAAI,MAAM,KAAK;AACb,oBAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAC1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,YACvB,OAAO;AACL,oBAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAC1B,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,YAClB;AAAA,UACF,OAAO;AACL,kBAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,EAC1B,KAAK,CAAC,IAAI,CAAC;AAAA,UACb;AAAA,QACF,OAAO;AACL,gBAAM,OAAO;AACb,cAAI,MAAM,KAAK;AACb,gBAAI,MAAM,KAAK;AACb,oBAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CACrB,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,YAC3B,OAAO;AACL,oBAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CACrB,GAAG,CAAC,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,YACtB;AAAA,UACF,OAAO;AACL,kBAAM,KAAK,CAAC,IAAI,CAAC,IAAI,CACrB,KAAK,CAAC,IAAI,CAAC;AAAA,UACb;AAAA,QACF;AAEA,cAAM,gBAAgB,GAAG;AACzB,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAEA,QAAM,iBAAiB,CAAC,MAAM,YAAY;AACxC,YAAM,kBAAkB,MAAM,OAAO;AACrC,aAAO,KACJ,MAAM,KAAK,EACX,IAAI,CAAC,MAAM,cAAc,GAAG,OAAO,CAAC,EACpC,KAAK,GAAG;AAAA,IACb;AAEA,QAAM,gBAAgB,CAAC,MAAM,YAAY;AACvC,aAAO,KAAK,KAAK;AACjB,YAAM,IAAI,QAAQ,QAAQ,GAAG,EAAE,WAAW,IAAI,GAAG,EAAE,MAAM;AACzD,aAAO,KAAK,QAAQ,GAAG,CAAC,KAAK,MAAM,GAAG,GAAG,GAAG,OAAO;AACjD,cAAM,UAAU,MAAM,KAAK,MAAM,GAAG,GAAG,GAAG,EAAE;AAC5C,cAAM,KAAK,IAAI,CAAC;AAChB,cAAM,KAAK,MAAM,IAAI,CAAC;AACtB,cAAM,KAAK,MAAM,IAAI,CAAC;AACtB,cAAM,OAAO;AAEb,YAAI,SAAS,OAAO,MAAM;AACxB,iBAAO;AAAA,QACT;AAIA,aAAK,QAAQ,oBAAoB,OAAO;AAExC,YAAI,IAAI;AACN,cAAI,SAAS,OAAO,SAAS,KAAK;AAEhC,kBAAM;AAAA,UACR,OAAO;AAEL,kBAAM;AAAA,UACR;AAAA,QACF,WAAW,QAAQ,MAAM;AAGvB,cAAI,IAAI;AACN,gBAAI;AAAA,UACN;AACA,cAAI;AAEJ,cAAI,SAAS,KAAK;AAGhB,mBAAO;AACP,gBAAI,IAAI;AACN,kBAAI,CAAC,IAAI;AACT,kBAAI;AACJ,kBAAI;AAAA,YACN,OAAO;AACL,kBAAI,CAAC,IAAI;AACT,kBAAI;AAAA,YACN;AAAA,UACF,WAAW,SAAS,MAAM;AAGxB,mBAAO;AACP,gBAAI,IAAI;AACN,kBAAI,CAAC,IAAI;AAAA,YACX,OAAO;AACL,kBAAI,CAAC,IAAI;AAAA,YACX;AAAA,UACF;AAEA,cAAI,SAAS,KAAK;AAChB,iBAAK;AAAA,UACP;AAEA,gBAAM,GAAG,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,GAAG,EAAE;AAAA,QAClC,WAAW,IAAI;AACb,gBAAM,KAAK,CAAC,OAAO,EAAE,KAAK,CAAC,IAAI,CAAC;AAAA,QAClC,WAAW,IAAI;AACb,gBAAM,KAAK,CAAC,IAAI,CAAC,KAAK,EACtB,KAAK,CAAC,IAAI,CAAC,IAAI,CAAC;AAAA,QAClB;AAEA,cAAM,iBAAiB,GAAG;AAE1B,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AAIA,QAAM,eAAe,CAAC,MAAM,YAAY;AACtC,YAAM,gBAAgB,MAAM,OAAO;AAEnC,aAAO,KACJ,KAAK,EACL,QAAQ,GAAG,EAAE,IAAI,GAAG,EAAE;AAAA,IAC3B;AAEA,QAAM,cAAc,CAAC,MAAM,YAAY;AACrC,YAAM,eAAe,MAAM,OAAO;AAClC,aAAO,KACJ,KAAK,EACL,QAAQ,GAAG,QAAQ,oBAAoB,EAAE,UAAU,EAAE,IAAI,GAAG,EAAE;AAAA,IACnE;AAOA,QAAM,gBAAgB,WAAS,CAAC,IAC9B,MAAM,IAAI,IAAI,IAAI,KAAK,IACvB,IAAI,IAAI,IAAI,IAAI,KAAK,OAAO;AAC5B,UAAI,IAAI,EAAE,GAAG;AACX,eAAO;AAAA,MACT,WAAW,IAAI,EAAE,GAAG;AAClB,eAAO,KAAK,EAAE,OAAO,QAAQ,OAAO,EAAE;AAAA,MACxC,WAAW,IAAI,EAAE,GAAG;AAClB,eAAO,KAAK,EAAE,IAAI,EAAE,KAAK,QAAQ,OAAO,EAAE;AAAA,MAC5C,WAAW,KAAK;AACd,eAAO,KAAK,IAAI;AAAA,MAClB,OAAO;AACL,eAAO,KAAK,IAAI,GAAG,QAAQ,OAAO,EAAE;AAAA,MACtC;AAEA,UAAI,IAAI,EAAE,GAAG;AACX,aAAK;AAAA,MACP,WAAW,IAAI,EAAE,GAAG;AAClB,aAAK,IAAI,CAAC,KAAK,CAAC;AAAA,MAClB,WAAW,IAAI,EAAE,GAAG;AAClB,aAAK,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;AAAA,MACxB,WAAW,KAAK;AACd,aAAK,KAAK,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,GAAG;AAAA,MACjC,WAAW,OAAO;AAChB,aAAK,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC,KAAK,CAAC;AAAA,MAC9B,OAAO;AACL,aAAK,KAAK,EAAE;AAAA,MACd;AAEA,aAAO,GAAG,IAAI,IAAI,EAAE,GAAG,KAAK;AAAA,IAC9B;AAEA,QAAM,UAAU,CAAC,KAAK,SAAS,YAAY;AACzC,eAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,YAAI,CAAC,IAAI,CAAC,EAAE,KAAK,OAAO,GAAG;AACzB,iBAAO;AAAA,QACT;AAAA,MACF;AAEA,UAAI,QAAQ,WAAW,UAAU,CAAC,QAAQ,mBAAmB;AAM3D,iBAAS,IAAI,GAAG,IAAI,IAAI,QAAQ,KAAK;AACnC,gBAAM,IAAI,CAAC,EAAE,MAAM;AACnB,cAAI,IAAI,CAAC,EAAE,WAAW,WAAW,KAAK;AACpC;AAAA,UACF;AAEA,cAAI,IAAI,CAAC,EAAE,OAAO,WAAW,SAAS,GAAG;AACvC,kBAAM,UAAU,IAAI,CAAC,EAAE;AACvB,gBAAI,QAAQ,UAAU,QAAQ,SAC1B,QAAQ,UAAU,QAAQ,SAC1B,QAAQ,UAAU,QAAQ,OAAO;AACnC,qBAAO;AAAA,YACT;AAAA,UACF;AAAA,QACF;AAGA,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAAA;AAAA;;;AC1hBA;AAAA;AAAA,QAAM,MAAM,OAAO,YAAY;AAE/B,QAAM,aAAN,MAAM,YAAW;AAAA,MACf,WAAW,MAAO;AAChB,eAAO;AAAA,MACT;AAAA,MAEA,YAAa,MAAM,SAAS;AAC1B,kBAAU,aAAa,OAAO;AAE9B,YAAI,gBAAgB,aAAY;AAC9B,cAAI,KAAK,UAAU,CAAC,CAAC,QAAQ,OAAO;AAClC,mBAAO;AAAA,UACT,OAAO;AACL,mBAAO,KAAK;AAAA,UACd;AAAA,QACF;AAEA,eAAO,KAAK,KAAK,EAAE,MAAM,KAAK,EAAE,KAAK,GAAG;AACxC,cAAM,cAAc,MAAM,OAAO;AACjC,aAAK,UAAU;AACf,aAAK,QAAQ,CAAC,CAAC,QAAQ;AACvB,aAAK,MAAM,IAAI;AAEf,YAAI,KAAK,WAAW,KAAK;AACvB,eAAK,QAAQ;AAAA,QACf,OAAO;AACL,eAAK,QAAQ,KAAK,WAAW,KAAK,OAAO;AAAA,QAC3C;AAEA,cAAM,QAAQ,IAAI;AAAA,MACpB;AAAA,MAEA,MAAO,MAAM;AACX,cAAM,IAAI,KAAK,QAAQ,QAAQ,GAAG,EAAE,eAAe,IAAI,GAAG,EAAE,UAAU;AACtE,cAAM,IAAI,KAAK,MAAM,CAAC;AAEtB,YAAI,CAAC,GAAG;AACN,gBAAM,IAAI,UAAU,uBAAuB,IAAI,EAAE;AAAA,QACnD;AAEA,aAAK,WAAW,EAAE,CAAC,MAAM,SAAY,EAAE,CAAC,IAAI;AAC5C,YAAI,KAAK,aAAa,KAAK;AACzB,eAAK,WAAW;AAAA,QAClB;AAGA,YAAI,CAAC,EAAE,CAAC,GAAG;AACT,eAAK,SAAS;AAAA,QAChB,OAAO;AACL,eAAK,SAAS,IAAI,OAAO,EAAE,CAAC,GAAG,KAAK,QAAQ,KAAK;AAAA,QACnD;AAAA,MACF;AAAA,MAEA,WAAY;AACV,eAAO,KAAK;AAAA,MACd;AAAA,MAEA,KAAM,SAAS;AACb,cAAM,mBAAmB,SAAS,KAAK,QAAQ,KAAK;AAEpD,YAAI,KAAK,WAAW,OAAO,YAAY,KAAK;AAC1C,iBAAO;AAAA,QACT;AAEA,YAAI,OAAO,YAAY,UAAU;AAC/B,cAAI;AACF,sBAAU,IAAI,OAAO,SAAS,KAAK,OAAO;AAAA,UAC5C,SAAS,IAAI;AACX,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,eAAO,IAAI,SAAS,KAAK,UAAU,KAAK,QAAQ,KAAK,OAAO;AAAA,MAC9D;AAAA,MAEA,WAAY,MAAM,SAAS;AACzB,YAAI,EAAE,gBAAgB,cAAa;AACjC,gBAAM,IAAI,UAAU,0BAA0B;AAAA,QAChD;AAEA,YAAI,KAAK,aAAa,IAAI;AACxB,cAAI,KAAK,UAAU,IAAI;AACrB,mBAAO;AAAA,UACT;AACA,iBAAO,IAAI,MAAM,KAAK,OAAO,OAAO,EAAE,KAAK,KAAK,KAAK;AAAA,QACvD,WAAW,KAAK,aAAa,IAAI;AAC/B,cAAI,KAAK,UAAU,IAAI;AACrB,mBAAO;AAAA,UACT;AACA,iBAAO,IAAI,MAAM,KAAK,OAAO,OAAO,EAAE,KAAK,KAAK,MAAM;AAAA,QACxD;AAEA,kBAAU,aAAa,OAAO;AAG9B,YAAI,QAAQ,sBACT,KAAK,UAAU,cAAc,KAAK,UAAU,aAAa;AAC1D,iBAAO;AAAA,QACT;AACA,YAAI,CAAC,QAAQ,sBACV,KAAK,MAAM,WAAW,QAAQ,KAAK,KAAK,MAAM,WAAW,QAAQ,IAAI;AACtE,iBAAO;AAAA,QACT;AAGA,YAAI,KAAK,SAAS,WAAW,GAAG,KAAK,KAAK,SAAS,WAAW,GAAG,GAAG;AAClE,iBAAO;AAAA,QACT;AAEA,YAAI,KAAK,SAAS,WAAW,GAAG,KAAK,KAAK,SAAS,WAAW,GAAG,GAAG;AAClE,iBAAO;AAAA,QACT;AAEA,YACG,KAAK,OAAO,YAAY,KAAK,OAAO,WACrC,KAAK,SAAS,SAAS,GAAG,KAAK,KAAK,SAAS,SAAS,GAAG,GAAG;AAC5D,iBAAO;AAAA,QACT;AAEA,YAAI,IAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,KAC5C,KAAK,SAAS,WAAW,GAAG,KAAK,KAAK,SAAS,WAAW,GAAG,GAAG;AAChE,iBAAO;AAAA,QACT;AAEA,YAAI,IAAI,KAAK,QAAQ,KAAK,KAAK,QAAQ,OAAO,KAC5C,KAAK,SAAS,WAAW,GAAG,KAAK,KAAK,SAAS,WAAW,GAAG,GAAG;AAChE,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAEA,WAAO,UAAU;AAEjB,QAAM,eAAe;AACrB,QAAM,EAAE,QAAQ,IAAI,EAAE,IAAI;AAC1B,QAAM,MAAM;AACZ,QAAM,QAAQ;AACd,QAAM,SAAS;AACf,QAAM,QAAQ;AAAA;AAAA;;;AC5Id;AAAA;AAAA,QAAM,QAAQ;AACd,QAAMA,aAAY,CAAC,SAAS,OAAO,YAAY;AAC7C,UAAI;AACF,gBAAQ,IAAI,MAAM,OAAO,OAAO;AAAA,MAClC,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AACA,aAAO,MAAM,KAAK,OAAO;AAAA,IAC3B;AACA,WAAO,UAAUA;AAAA;AAAA;;;ACTjB;AAAA;AAAA,QAAM,QAAQ;AAGd,QAAM,gBAAgB,CAAC,OAAO,YAC5B,IAAI,MAAM,OAAO,OAAO,EAAE,IACvB,IAAI,UAAQ,KAAK,IAAI,OAAK,EAAE,KAAK,EAAE,KAAK,GAAG,EAAE,KAAK,EAAE,MAAM,GAAG,CAAC;AAEnE,WAAO,UAAU;AAAA;AAAA;;;ACPjB;AAAA;AAAA,QAAM,SAAS;AACf,QAAM,QAAQ;AAEd,QAAM,gBAAgB,CAAC,UAAU,OAAO,YAAY;AAClD,UAAI,MAAM;AACV,UAAI,QAAQ;AACZ,UAAI,WAAW;AACf,UAAI;AACF,mBAAW,IAAI,MAAM,OAAO,OAAO;AAAA,MACrC,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AACA,eAAS,QAAQ,CAAC,MAAM;AACtB,YAAI,SAAS,KAAK,CAAC,GAAG;AAEpB,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,MAAM,IAAI;AAEnC,kBAAM;AACN,oBAAQ,IAAI,OAAO,KAAK,OAAO;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AACA,WAAO,UAAU;AAAA;AAAA;;;ACxBjB;AAAA;AAAA,QAAM,SAAS;AACf,QAAM,QAAQ;AACd,QAAM,gBAAgB,CAAC,UAAU,OAAO,YAAY;AAClD,UAAI,MAAM;AACV,UAAI,QAAQ;AACZ,UAAI,WAAW;AACf,UAAI;AACF,mBAAW,IAAI,MAAM,OAAO,OAAO;AAAA,MACrC,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AACA,eAAS,QAAQ,CAAC,MAAM;AACtB,YAAI,SAAS,KAAK,CAAC,GAAG;AAEpB,cAAI,CAAC,OAAO,MAAM,QAAQ,CAAC,MAAM,GAAG;AAElC,kBAAM;AACN,oBAAQ,IAAI,OAAO,KAAK,OAAO;AAAA,UACjC;AAAA,QACF;AAAA,MACF,CAAC;AACD,aAAO;AAAA,IACT;AACA,WAAO,UAAU;AAAA;AAAA;;;ACvBjB;AAAA;AAAA,QAAM,SAAS;AACf,QAAM,QAAQ;AACd,QAAM,KAAK;AAEX,QAAM,aAAa,CAAC,OAAO,UAAU;AACnC,cAAQ,IAAI,MAAM,OAAO,KAAK;AAE9B,UAAI,SAAS,IAAI,OAAO,OAAO;AAC/B,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,eAAO;AAAA,MACT;AAEA,eAAS,IAAI,OAAO,SAAS;AAC7B,UAAI,MAAM,KAAK,MAAM,GAAG;AACtB,eAAO;AAAA,MACT;AAEA,eAAS;AACT,eAAS,IAAI,GAAG,IAAI,MAAM,IAAI,QAAQ,EAAE,GAAG;AACzC,cAAM,cAAc,MAAM,IAAI,CAAC;AAE/B,YAAI,SAAS;AACb,oBAAY,QAAQ,CAAC,eAAe;AAElC,gBAAM,UAAU,IAAI,OAAO,WAAW,OAAO,OAAO;AACpD,kBAAQ,WAAW,UAAU;AAAA,YAC3B,KAAK;AACH,kBAAI,QAAQ,WAAW,WAAW,GAAG;AACnC,wBAAQ;AAAA,cACV,OAAO;AACL,wBAAQ,WAAW,KAAK,CAAC;AAAA,cAC3B;AACA,sBAAQ,MAAM,QAAQ,OAAO;AAAA,YAE/B,KAAK;AAAA,YACL,KAAK;AACH,kBAAI,CAAC,UAAU,GAAG,SAAS,MAAM,GAAG;AAClC,yBAAS;AAAA,cACX;AACA;AAAA,YACF,KAAK;AAAA,YACL,KAAK;AAEH;AAAA,YAEF;AACE,oBAAM,IAAI,MAAM,yBAAyB,WAAW,QAAQ,EAAE;AAAA,UAClE;AAAA,QACF,CAAC;AACD,YAAI,WAAW,CAAC,UAAU,GAAG,QAAQ,MAAM,IAAI;AAC7C,mBAAS;AAAA,QACX;AAAA,MACF;AAEA,UAAI,UAAU,MAAM,KAAK,MAAM,GAAG;AAChC,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AACA,WAAO,UAAU;AAAA;AAAA;;;AC5DjB,IAAAC,iBAAA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAMC,cAAa,CAAC,OAAO,YAAY;AACrC,UAAI;AAGF,eAAO,IAAI,MAAM,OAAO,OAAO,EAAE,SAAS;AAAA,MAC5C,SAAS,IAAI;AACX,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO,UAAUA;AAAA;AAAA;;;ACVjB;AAAA;AAAA,QAAM,SAAS;AACf,QAAM,aAAa;AACnB,QAAM,EAAE,IAAI,IAAI;AAChB,QAAM,QAAQ;AACd,QAAMC,aAAY;AAClB,QAAM,KAAK;AACX,QAAM,KAAK;AACX,QAAM,MAAM;AACZ,QAAM,MAAM;AAEZ,QAAM,UAAU,CAAC,SAAS,OAAO,MAAM,YAAY;AACjD,gBAAU,IAAI,OAAO,SAAS,OAAO;AACrC,cAAQ,IAAI,MAAM,OAAO,OAAO;AAEhC,UAAI,MAAM,OAAO,MAAM,MAAM;AAC7B,cAAQ,MAAM;AAAA,QACZ,KAAK;AACH,iBAAO;AACP,kBAAQ;AACR,iBAAO;AACP,iBAAO;AACP,kBAAQ;AACR;AAAA,QACF,KAAK;AACH,iBAAO;AACP,kBAAQ;AACR,iBAAO;AACP,iBAAO;AACP,kBAAQ;AACR;AAAA,QACF;AACE,gBAAM,IAAI,UAAU,uCAAuC;AAAA,MAC/D;AAGA,UAAIA,WAAU,SAAS,OAAO,OAAO,GAAG;AACtC,eAAO;AAAA,MACT;AAKA,eAAS,IAAI,GAAG,IAAI,MAAM,IAAI,QAAQ,EAAE,GAAG;AACzC,cAAM,cAAc,MAAM,IAAI,CAAC;AAE/B,YAAI,OAAO;AACX,YAAI,MAAM;AAEV,oBAAY,QAAQ,CAAC,eAAe;AAClC,cAAI,WAAW,WAAW,KAAK;AAC7B,yBAAa,IAAI,WAAW,SAAS;AAAA,UACvC;AACA,iBAAO,QAAQ;AACf,gBAAM,OAAO;AACb,cAAI,KAAK,WAAW,QAAQ,KAAK,QAAQ,OAAO,GAAG;AACjD,mBAAO;AAAA,UACT,WAAW,KAAK,WAAW,QAAQ,IAAI,QAAQ,OAAO,GAAG;AACvD,kBAAM;AAAA,UACR;AAAA,QACF,CAAC;AAID,YAAI,KAAK,aAAa,QAAQ,KAAK,aAAa,OAAO;AACrD,iBAAO;AAAA,QACT;AAIA,aAAK,CAAC,IAAI,YAAY,IAAI,aAAa,SACnC,MAAM,SAAS,IAAI,MAAM,GAAG;AAC9B,iBAAO;AAAA,QACT,WAAW,IAAI,aAAa,SAAS,KAAK,SAAS,IAAI,MAAM,GAAG;AAC9D,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC/EjB;AAAA;AACA,QAAM,UAAU;AAChB,QAAM,MAAM,CAAC,SAAS,OAAO,YAAY,QAAQ,SAAS,OAAO,KAAK,OAAO;AAC7E,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA,QAAM,UAAU;AAEhB,QAAM,MAAM,CAAC,SAAS,OAAO,YAAY,QAAQ,SAAS,OAAO,KAAK,OAAO;AAC7E,WAAO,UAAU;AAAA;AAAA;;;ACHjB;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,aAAa,CAAC,IAAI,IAAI,YAAY;AACtC,WAAK,IAAI,MAAM,IAAI,OAAO;AAC1B,WAAK,IAAI,MAAM,IAAI,OAAO;AAC1B,aAAO,GAAG,WAAW,IAAI,OAAO;AAAA,IAClC;AACA,WAAO,UAAU;AAAA;AAAA;;;ACNjB;AAAA;AAGA,QAAMC,aAAY;AAClB,QAAM,UAAU;AAChB,WAAO,UAAU,CAAC,UAAU,OAAO,YAAY;AAC7C,YAAM,MAAM,CAAC;AACb,UAAI,QAAQ;AACZ,UAAI,OAAO;AACX,YAAM,IAAI,SAAS,KAAK,CAAC,GAAG,MAAM,QAAQ,GAAG,GAAG,OAAO,CAAC;AACxD,iBAAW,WAAW,GAAG;AACvB,cAAM,WAAWA,WAAU,SAAS,OAAO,OAAO;AAClD,YAAI,UAAU;AACZ,iBAAO;AACP,cAAI,CAAC,OAAO;AACV,oBAAQ;AAAA,UACV;AAAA,QACF,OAAO;AACL,cAAI,MAAM;AACR,gBAAI,KAAK,CAAC,OAAO,IAAI,CAAC;AAAA,UACxB;AACA,iBAAO;AACP,kBAAQ;AAAA,QACV;AAAA,MACF;AACA,UAAI,OAAO;AACT,YAAI,KAAK,CAAC,OAAO,IAAI,CAAC;AAAA,MACxB;AAEA,YAAM,SAAS,CAAC;AAChB,iBAAW,CAAC,KAAK,GAAG,KAAK,KAAK;AAC5B,YAAI,QAAQ,KAAK;AACf,iBAAO,KAAK,GAAG;AAAA,QACjB,WAAW,CAAC,OAAO,QAAQ,EAAE,CAAC,GAAG;AAC/B,iBAAO,KAAK,GAAG;AAAA,QACjB,WAAW,CAAC,KAAK;AACf,iBAAO,KAAK,KAAK,GAAG,EAAE;AAAA,QACxB,WAAW,QAAQ,EAAE,CAAC,GAAG;AACvB,iBAAO,KAAK,KAAK,GAAG,EAAE;AAAA,QACxB,OAAO;AACL,iBAAO,KAAK,GAAG,GAAG,MAAM,GAAG,EAAE;AAAA,QAC/B;AAAA,MACF;AACA,YAAM,aAAa,OAAO,KAAK,MAAM;AACrC,YAAM,WAAW,OAAO,MAAM,QAAQ,WAAW,MAAM,MAAM,OAAO,KAAK;AACzE,aAAO,WAAW,SAAS,SAAS,SAAS,aAAa;AAAA,IAC5D;AAAA;AAAA;;;AC9CA;AAAA;AAAA,QAAM,QAAQ;AACd,QAAM,aAAa;AACnB,QAAM,EAAE,IAAI,IAAI;AAChB,QAAMC,aAAY;AAClB,QAAM,UAAU;AAsChB,QAAM,SAAS,CAAC,KAAK,KAAK,UAAU,CAAC,MAAM;AACzC,UAAI,QAAQ,KAAK;AACf,eAAO;AAAA,MACT;AAEA,YAAM,IAAI,MAAM,KAAK,OAAO;AAC5B,YAAM,IAAI,MAAM,KAAK,OAAO;AAC5B,UAAI,aAAa;AAEjB;AAAO,mBAAW,aAAa,IAAI,KAAK;AACtC,qBAAW,aAAa,IAAI,KAAK;AAC/B,kBAAM,QAAQ,aAAa,WAAW,WAAW,OAAO;AACxD,yBAAa,cAAc,UAAU;AACrC,gBAAI,OAAO;AACT,uBAAS;AAAA,YACX;AAAA,UACF;AAKA,cAAI,YAAY;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AACA,aAAO;AAAA,IACT;AAEA,QAAM,+BAA+B,CAAC,IAAI,WAAW,WAAW,CAAC;AACjE,QAAM,iBAAiB,CAAC,IAAI,WAAW,SAAS,CAAC;AAEjD,QAAM,eAAe,CAAC,KAAK,KAAK,YAAY;AAC1C,UAAI,QAAQ,KAAK;AACf,eAAO;AAAA,MACT;AAEA,UAAI,IAAI,WAAW,KAAK,IAAI,CAAC,EAAE,WAAW,KAAK;AAC7C,YAAI,IAAI,WAAW,KAAK,IAAI,CAAC,EAAE,WAAW,KAAK;AAC7C,iBAAO;AAAA,QACT,WAAW,QAAQ,mBAAmB;AACpC,gBAAM;AAAA,QACR,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAEA,UAAI,IAAI,WAAW,KAAK,IAAI,CAAC,EAAE,WAAW,KAAK;AAC7C,YAAI,QAAQ,mBAAmB;AAC7B,iBAAO;AAAA,QACT,OAAO;AACL,gBAAM;AAAA,QACR;AAAA,MACF;AAEA,YAAM,QAAQ,oBAAI,IAAI;AACtB,UAAI,IAAI;AACR,iBAAW,KAAK,KAAK;AACnB,YAAI,EAAE,aAAa,OAAO,EAAE,aAAa,MAAM;AAC7C,eAAK,SAAS,IAAI,GAAG,OAAO;AAAA,QAC9B,WAAW,EAAE,aAAa,OAAO,EAAE,aAAa,MAAM;AACpD,eAAK,QAAQ,IAAI,GAAG,OAAO;AAAA,QAC7B,OAAO;AACL,gBAAM,IAAI,EAAE,MAAM;AAAA,QACpB;AAAA,MACF;AAEA,UAAI,MAAM,OAAO,GAAG;AAClB,eAAO;AAAA,MACT;AAEA,UAAI;AACJ,UAAI,MAAM,IAAI;AACZ,mBAAW,QAAQ,GAAG,QAAQ,GAAG,QAAQ,OAAO;AAChD,YAAI,WAAW,GAAG;AAChB,iBAAO;AAAA,QACT,WAAW,aAAa,MAAM,GAAG,aAAa,QAAQ,GAAG,aAAa,OAAO;AAC3E,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,iBAAW,MAAM,OAAO;AACtB,YAAI,MAAM,CAACA,WAAU,IAAI,OAAO,EAAE,GAAG,OAAO,GAAG;AAC7C,iBAAO;AAAA,QACT;AAEA,YAAI,MAAM,CAACA,WAAU,IAAI,OAAO,EAAE,GAAG,OAAO,GAAG;AAC7C,iBAAO;AAAA,QACT;AAEA,mBAAW,KAAK,KAAK;AACnB,cAAI,CAACA,WAAU,IAAI,OAAO,CAAC,GAAG,OAAO,GAAG;AACtC,mBAAO;AAAA,UACT;AAAA,QACF;AAEA,eAAO;AAAA,MACT;AAEA,UAAI,QAAQ;AACZ,UAAI,UAAU;AAGd,UAAI,eAAe,MACjB,CAAC,QAAQ,qBACT,GAAG,OAAO,WAAW,SAAS,GAAG,SAAS;AAC5C,UAAI,eAAe,MACjB,CAAC,QAAQ,qBACT,GAAG,OAAO,WAAW,SAAS,GAAG,SAAS;AAE5C,UAAI,gBAAgB,aAAa,WAAW,WAAW,KACnD,GAAG,aAAa,OAAO,aAAa,WAAW,CAAC,MAAM,GAAG;AAC3D,uBAAe;AAAA,MACjB;AAEA,iBAAW,KAAK,KAAK;AACnB,mBAAW,YAAY,EAAE,aAAa,OAAO,EAAE,aAAa;AAC5D,mBAAW,YAAY,EAAE,aAAa,OAAO,EAAE,aAAa;AAC5D,YAAI,IAAI;AACN,cAAI,cAAc;AAChB,gBAAI,EAAE,OAAO,cAAc,EAAE,OAAO,WAAW,UAC3C,EAAE,OAAO,UAAU,aAAa,SAChC,EAAE,OAAO,UAAU,aAAa,SAChC,EAAE,OAAO,UAAU,aAAa,OAAO;AACzC,6BAAe;AAAA,YACjB;AAAA,UACF;AACA,cAAI,EAAE,aAAa,OAAO,EAAE,aAAa,MAAM;AAC7C,qBAAS,SAAS,IAAI,GAAG,OAAO;AAChC,gBAAI,WAAW,KAAK,WAAW,IAAI;AACjC,qBAAO;AAAA,YACT;AAAA,UACF,WAAW,GAAG,aAAa,QAAQ,CAACA,WAAU,GAAG,QAAQ,OAAO,CAAC,GAAG,OAAO,GAAG;AAC5E,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,IAAI;AACN,cAAI,cAAc;AAChB,gBAAI,EAAE,OAAO,cAAc,EAAE,OAAO,WAAW,UAC3C,EAAE,OAAO,UAAU,aAAa,SAChC,EAAE,OAAO,UAAU,aAAa,SAChC,EAAE,OAAO,UAAU,aAAa,OAAO;AACzC,6BAAe;AAAA,YACjB;AAAA,UACF;AACA,cAAI,EAAE,aAAa,OAAO,EAAE,aAAa,MAAM;AAC7C,oBAAQ,QAAQ,IAAI,GAAG,OAAO;AAC9B,gBAAI,UAAU,KAAK,UAAU,IAAI;AAC/B,qBAAO;AAAA,YACT;AAAA,UACF,WAAW,GAAG,aAAa,QAAQ,CAACA,WAAU,GAAG,QAAQ,OAAO,CAAC,GAAG,OAAO,GAAG;AAC5E,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,CAAC,EAAE,aAAa,MAAM,OAAO,aAAa,GAAG;AAC/C,iBAAO;AAAA,QACT;AAAA,MACF;AAKA,UAAI,MAAM,YAAY,CAAC,MAAM,aAAa,GAAG;AAC3C,eAAO;AAAA,MACT;AAEA,UAAI,MAAM,YAAY,CAAC,MAAM,aAAa,GAAG;AAC3C,eAAO;AAAA,MACT;AAKA,UAAI,gBAAgB,cAAc;AAChC,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAGA,QAAM,WAAW,CAAC,GAAG,GAAG,YAAY;AAClC,UAAI,CAAC,GAAG;AACN,eAAO;AAAA,MACT;AACA,YAAM,OAAO,QAAQ,EAAE,QAAQ,EAAE,QAAQ,OAAO;AAChD,aAAO,OAAO,IAAI,IACd,OAAO,IAAI,IACX,EAAE,aAAa,OAAO,EAAE,aAAa,OAAO,IAC5C;AAAA,IACN;AAGA,QAAM,UAAU,CAAC,GAAG,GAAG,YAAY;AACjC,UAAI,CAAC,GAAG;AACN,eAAO;AAAA,MACT;AACA,YAAM,OAAO,QAAQ,EAAE,QAAQ,EAAE,QAAQ,OAAO;AAChD,aAAO,OAAO,IAAI,IACd,OAAO,IAAI,IACX,EAAE,aAAa,OAAO,EAAE,aAAa,OAAO,IAC5C;AAAA,IACN;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtPjB,IAAAC,kBAAA;AAAA;AACA,QAAM,aAAa;AACnB,QAAM,YAAY;AAClB,QAAM,SAAS;AACf,QAAM,cAAc;AACpB,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,QAAM,MAAM;AACZ,QAAM,OAAO;AACb,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,QAAM,QAAQ;AACd,QAAM,aAAa;AACnB,QAAM,UAAU;AAChB,QAAM,WAAW;AACjB,QAAM,eAAe;AACrB,QAAM,eAAe;AACrB,QAAM,OAAO;AACb,QAAM,QAAQ;AACd,QAAM,KAAK;AACX,QAAM,KAAK;AACX,QAAM,KAAK;AACX,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,SAAS;AACf,QAAM,aAAa;AACnB,QAAM,QAAQ;AACd,QAAMC,aAAY;AAClB,QAAM,gBAAgB;AACtB,QAAM,gBAAgB;AACtB,QAAM,gBAAgB;AACtB,QAAM,aAAa;AACnB,QAAMC,cAAa;AACnB,QAAM,UAAU;AAChB,QAAM,MAAM;AACZ,QAAM,MAAM;AACZ,QAAM,aAAa;AACnB,QAAM,gBAAgB;AACtB,QAAM,SAAS;AACf,WAAO,UAAU;AAAA,MACf;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,WAAAD;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,YAAAC;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,MACA,IAAI,WAAW;AAAA,MACf,KAAK,WAAW;AAAA,MAChB,QAAQ,WAAW;AAAA,MACnB,qBAAqB,UAAU;AAAA,MAC/B,eAAe,UAAU;AAAA,MACzB,oBAAoB,YAAY;AAAA,MAChC,qBAAqB,YAAY;AAAA,IACnC;AAAA;AAAA;;;;;;;;ACtFgB,SAAA,YAAoB,OAAe,OAAuB;AACxE,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,eAAS,KAAK;AAEhE,EAAM,gBAAU,MAAM;AACd,UAAA,UAAU,WAAW,MAAM;AAC/B,wBAAkB,KAAK;IAAA,GACtB,KAAK;AAER,WAAO,MAAM;AACX,mBAAa,OAAO;IAAA;EACtB,GACC,CAAC,OAAO,KAAK,CAAC;AAEV,SAAA;AACT;ACWA,IAAM,qBAAqB,CAAC;EAC1B;EACA;EACA;EACA;EACA;EACA;AACF,MAA+B;;AACvB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAE5B,QAAA,kBAAkB,CAAC,aAAqB,eAA4B;AACxE,UAAM,SAAS;MACb,CAAC,UAAU,IAAI,MAAM,UAAU,KAAK,CAAA,GAAI,OAAO,CAAC,gBAAgB,gBAAgB,WAAW;IAAA;AAG7F,uBAAmB,MAAM;EAAA;AAIzB,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAA,wBAAC,QAAQ,SAAR,EACC,cAAA,wBAAC,QAAO,EAAA,SAAQ,YAAW,eAAW,wBAAC,eAAA,CAAA,CAAO,GAC3C,UAAA,cAAc,EAAE,IAAI,qBAAqB,gBAAgB,UAAU,CAAC,EAAA,CACvE,EACF,CAAA;QACC,wBAAA,QAAQ,SAAR,EAAgB,YAAY,GAC3B,cAAA,yBAAC,MAAK,EAAA,SAAS,GAAG,WAAU,UAAS,YAAW,WAAU,KAAK,GAC7D,UAAA;UAAA;QAAC;QAAA;UACC,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,QAAO,+BAAO,gBAAe,CAAA;UAC7B,UAAU,CAAC,mBAAmB;AACtB,kBAAA,SAAS,EAAE,aAAa,eAAe;AAC7C,+BAAmB,MAAM;UAAA;UAE3B,SAAS,MAAM,kBAAkB,aAAa;UAC9C,iBAAiB;UACjB,kBAAkB,CAAC,WACjB;YACE;cACE,IAAI;cACJ,gBACE;YAAA;YAEJ,EAAE,QAAO,iCAAQ,WAAU,EAAE;UAAA;QAC/B;MAAA;MAGH,mBAAmB,gBAClB;QAAC;QAAA;UACC,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,QAAO,+BAAO,eAAc,CAAA;UAC5B,UAAU,CAAC,kBAAkB;AACrB,kBAAA,SAAS,EAAE,YAAY,cAAc;AAC3C,+BAAmB,MAAM;UAAA;UAE3B,SAAS,MAAM,kBAAkB,YAAY;UAC7C,iBAAiB;UACjB,kBAAkB,CAAC,WACjB;YACE;cACE,IAAI;cACJ,gBACE;YAAA;YAEJ,EAAE,QAAO,iCAAQ,WAAU,EAAE;UAAA;QAC/B;MAAA;IAEJ,EAAA,CAEJ,EACF,CAAA;KAEC,WAAM,gBAAN,mBAAmB,IAAI,CAAC,mBAAA,wBACtB,KAAqB,EAAA,SAAS,GAC7B,cAAA,wBAAC,KAAI,EAAA,UAAA,wBAAO,eAAM,CAAA,CAAA,GAAI,SAAS,MAAM,gBAAgB,YAAY,aAAa,GAC3E,UAAA,WAAA,CACH,EAHQ,GAAA,UAIV;IAED,mBAAmB,cAClB,WAAM,eAAN,mBAAkB,IAAI,CAAC,iBACpB,wBAAA,KAAA,EAAmB,SAAS,GAC3B,cAAC,wBAAA,KAAA,EAAI,UAAO,wBAAA,eAAA,CAAA,CAAM,GAAI,SAAS,MAAM,gBAAgB,UAAU,YAAY,GACxE,UAAA,SAAA,CACH,EAHQ,GAAA,QAIV;EACD,EACL,CAAA;AAEJ;AAcA,IAAM,eAAe,CAAC;EACpB;EACA;EACA;EACA;EACA;EACA;AACF,MAAyB;AAErB,aAAA;IAAC;IAAA;MACC,eAAa,GAAG,OAAO;MACvB,cAAY;MACZ,aAAa;MACb;MACA;MACA;MACA;MAEC,UAAA,OAAO,QAAQ,eAAe,EAAE,IAAI,CAAC,CAAC,YAAY,KAAK,MAAM;AAE1D,mBAAA;UAAC;UAAA;YACC,eAAa,GAAG,UAAU,IAAI,KAAK;YAEnC,OAAO;YAEN,UAAA,GAAG,UAAU,KAAK,KAAK;UAAA;UAHnB;QAAA;MAIP,CAEH;IAAA;EAAA;AAGP;AC5IA,IAAM,eAAe,GAAiC,UAAU;;;;;;AAehE,IAAM,iBAAiB,CAAC;EACtB;EACA;EACA;EACA;EACA;EACA;AACF,MAA2B;AACnB,QAAA,EAAE,WAAA,IAAe;AACjB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,WAAW,IAAI,YAAY;AAE7B,QAAA,gBAAgB,UAClB,YAAY,WAAW,cAAc,KACrC,eAAe,WAAW,cAAc;AAE5C,QAAM,sBAAsB,cAAc;IACxC,IAAI;IACJ,gBAAgB;EAAA,CACjB;AAEK,QAAA,iBAAiB,4BAA4B,iBAAAC,QAAU,OAAO,cAAc,CAAC,IACjF,WAAW,IACb;AAEA,QAAM,eAAsB,kBAAW,WAAW,aAAa;AAE/D,QAAM,eAAe,eACV,iBAAU,oBAAoB,IAAI,YAAY,IACrD;AAGF,aAAA;IAAC;IAAA;MACC,WAAU;MACV,gBAAe;MACf,YAAY;MACZ,cAAc;MACd,eAAe;MACf,aAAa;MACb,WAAS;MACT,YAAW;MACX,QAAO;MACP,QAAO;MACP,YAAW;MACX,eAAY;MAEZ,UAAA;YAAA,yBAAC,KACC,EAAA,UAAA;cAAA,yBAAC,MAAA,EAAK,WAAU,OAAM,gBAAe,iBAAgB,YAAW,cAC9D,UAAA;gBAAA;cAAC;cAAA;gBACC,KAAI;gBACJ,KAAK,WAAW,KAAK;gBACrB,KAAK,GAAG,WAAW,IAAI;gBACvB,WAAS;gBACT,OAAO;gBACP,QAAQ;cAAA;YAAA;gBAEV;cAAC;cAAA;gBACC,aAAa,WAAW;gBACxB,cAAc,WAAW;gBACzB;cAAA;YAAA;UACF,EACF,CAAA;cACA,wBAAC,KAAA,EAAI,YAAY,GACf,cAAA,wBAAC,YAAW,EAAA,KAAI,MAAK,SAAQ,SAC3B,cAAA;YAAC;YAAA;cACC,YAAW;cACX,KAAK,WAAW,aAAa,CAAC,WAAW,eAAe,IAAI;cAE3D,UAAA;gBAAW,WAAA;gBACX,WAAW,aAAa,CAAC,WAAW,oBACnC;kBAAC;kBAAA;oBACC,aAAa,cAAc;sBACzB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBAED,cAAA,wBAAC,eAAY,EAAA,MAAK,aAAa,CAAA;kBAAA;gBAAA;gBAGlC,WAAW,oBACT,wBAAA,aAAA,EAAQ,aAAa,qBACpB,cAAA;kBAAC;kBAAA;oBACC,KAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,OAAO;oBACP,QAAO;kBAAA;gBAAA,EAEX,CAAA;cAAA;YAAA;UAAA,EAAA,CAGN,EACF,CAAA;cACC,wBAAA,KAAA,EAAI,YAAY,GACf,cAAA,wBAAC,cAAa,EAAA,KAAI,KAAI,SAAQ,SAAQ,WAAU,cAC7C,UAAA,WAAW,YAAA,CACd,EACF,CAAA;QAAA,EACF,CAAA;YAEA,yBAAC,MAAK,EAAA,KAAK,GAAG,OAAO,EAAE,WAAW,WAAA,GAAc,YAAY,GAC1D,UAAA;cAAA;YAAC;YAAA;cACC,MAAK;cACL,MAAM;cACN,YAAU;cACV,aAAA,wBAAU,eAAa,CAAA,CAAA;cACvB,cAAY;gBACV;kBACE,IAAI;kBACJ,gBAAgB;gBAAA;gBAElB,EAAE,YAAY,WAAW,KAAK;cAAA;cAEhC,SAAQ;cACR,SAAS,MAAM,WAAW,oBAAoB;cAE7C,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA;UAAA;cAEH;YAAC;YAAA;cACC;cACA;cACA;cACA;cACA;cACA,sBAAsB,WAAW;cACjC,YAAY,WAAW;YAAA;UAAA;QACzB,EACF,CAAA;MAAA;IAAA;EAAA;AAGN;AAcA,IAAM,sBAAsB,CAAC;EAC3B;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MAAgC;AACxB,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AACzC,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,WAAW,IAAI,YAAY;AAC7B,QAAA,EAAE,KAAK,IAAI,aAAa;AAE9B,QAAM,aAAa,YAAY;AACvB,UAAA,UAAU,MAAM,KAAK,aAAa;AAExC,QAAI,SAAS;AACX,iBAAW,mBAAmB;AACX,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,kDAAA,CAAmD;MAAA,CACjF;IAAA;EACH;AAIF,MAAI,aAAa;AACf,eACG,yBAAA,MAAA,EAAK,KAAK,GAAG,aAAa,GACzB,UAAA;UAAA,wBAAC,eAAA,EAAM,OAAM,UAAS,QAAO,UAAS,OAAM,aAAa,CAAA;UACzD,wBAAC,YAAA,EAAW,SAAQ,SAAQ,WAAU,cAAa,YAAW,QAC3D,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;IAAA,EACF,CAAA;EAAA;AAKA,MAAA,uBAAuB,iBAAiB,OAAO;AAE/C,eAAA;MAAC;MAAA;QACC;QACA;QACA;QACA;MAAA;IAAA;EACF;AAKG,SAAA;AACT;AAYA,IAAM,aAAa,CAAC;EAClB;EACA;EACA;EACA;AACF,MAAuB;AACf,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,eAAsB,kBAAW,oBAAoB;AAC3D,QAAM,eAAsB,iBAAU,oBAAoB,IAAI,gBAAgB,EAAE;AAEhF,QAAM,iBAAiB,cAAc;IACnC,IAAI;IACJ,gBAAgB;EAAA,CACjB;AAGD,MAAI,kBAAkB;AAChB,QAAA,CAAC,gBAAgB,CAAC,cAAc;AAEhC,iBAAA;QAAC;QAAA;UACC,eAAa,WAAW,UAAU;UAClC,OAAO;YACL;cACE,IAAI;cACJ,gBACE;YAAA;YAEJ;cACE;cACA;YAAA;UACF;UAGF,cAAA,wBAAC,QACC,EAAA,cAAA;YAAC;YAAA;cACC,MAAK;cACL,eAAA,wBAAY,eAAU,CAAA,CAAA;cACtB,SAAQ;cACR,SAAS;cACT,UAAU,CAAC;cAEV,UAAA;YAAA;UAAA,EAEL,CAAA;QAAA;MAAA;IACF;EAEJ;AAGF,aACG,wBAAA,QAAA,EAAO,MAAK,KAAI,eAAW,wBAAC,eAAU,CAAA,CAAA,GAAI,SAAQ,aAAY,SAAS,YACrE,UACH,eAAA,CAAA;AAEJ;AAYA,IAAM,eAAe,CAAC,EAAE,cAAc,GAAG,eAAe,GAAG,eAAA,MAAwC;AAC3F,QAAA,EAAE,cAAc,IAAI,QAAQ;AAGhC,aAAA,yBAAC,MAAK,EAAA,KAAK,GACR,UAAA;IAAC,CAAA,CAAC,mBAEC,yBAAA,6BAAA,EAAA,UAAA;UAAA,wBAAC,cAAA,EAAO,QAAO,UAAS,OAAM,UAAS,eAAW,KAAC,CAAA;UACnD,wBAAC,cAAA,EAAK,QAAO,UAAS,OAAM,UAAS,MAAK,cAAa,eAAW,KAAC,CAAA;UACnE;QAAC;QAAA;UACC,cAAY;YACV;cACE,IAAI,+BAA+B,cAAc;cACjD,gBAAgB;YAAA;YAElB;cACE,YAAY;cACZ,SAAS;YAAA;UACX;UAGF,cAAA,wBAAC,YAAW,EAAA,SAAQ,MAAK,WAAU,cAChC,UACH,YAAA,CAAA;QAAA;MAAA;UACF,wBACC,iBAAgB,CAAA,CAAA;IAAA,EACnB,CAAA;QAAA,wBAED,eAAS,EAAA,QAAO,UAAS,OAAM,UAAS,eAAW,KAAC,CAAA;QACrD;MAAC;MAAA;QACC,cAAY;UACV;YACE,IAAI,+BAA+B,cAAc;YACjD,gBAAgB;UAAA;UAElB;YACE,gBAAgB;YAChB,SAAS;UAAA;QACX;QAGF,cAAA,wBAAC,YAAW,EAAA,SAAQ,MAAK,WAAU,cAChC,UACH,aAAA,CAAA;MAAA;IAAA;EACF,EACF,CAAA;AAEJ;AAEA,IAAM,kBAAkB,GAAO,OAAO;;;;AC7VtC,IAAM,kBAAkB,CAAC;EACvB;EACA,cAAc,CAAA;EACd,wBAAwB,CAAA;EACxB;EACA;EACA;EACA;EACA;AACF,MAA4B;AACpB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,MAAI,WAAW,SAAS;AACf,eAAA,wBAAC,KAAK,OAAL,CAAA,CAAW;EAAA;AAGrB,MAAI,WAAW,WAAW;AACjB,eAAA,wBAAC,KAAK,SAAL,CAAA,CAAa;EAAA;AAGvB,QAAM,qBAAqB;IACzB;MACE,IAAI;MACJ,gBAAgB;IAAA;IAElB,EAAE,QAAQ,gBAAgB;EAAA;AAGxB,MAAA,YAAY,WAAW,GAAG;AAE1B,eAAA,yBAAC,KAAI,EAAA,UAAS,YACZ,UAAA;UAAC,wBAAA,QAAQ,MAAR,EAAa,MAAK,KAChB,UAAM,MAAA,EAAE,EACN,KAAK,IAAI,EACT,IAAI,CAAC,GAAG,YACP,wBAAC,iBAA0B,EAAA,QAAO,SAAQ,WAAS,KAAA,GAA7B,GAA8B,CACrD,EACL,CAAA;UACC,wBAAA,KAAA,EAAI,UAAS,YAAW,KAAK,IAAI,OAAM,QACtC,cAAA,yBAAC,MAAA,EAAK,YAAW,UAAS,gBAAe,UAAS,WAAU,UAC1D,UAAA;YAAA,wBAAC,cAAe,EAAA,OAAM,SAAQ,QAAO,OAAO,CAAA;YAC3C,wBAAA,KAAA,EAAI,YAAY,GACf,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,KAAI,KAAI,WAAU,cAC3C,UAAA,mBAAA,CACH,EACF,CAAA;MAAA,EAAA,CACF,EACF,CAAA;IAAA,EACF,CAAA;EAAA;AAKF,aAAA,wBAAC,KAAK,MAAL,EAAU,KAAK,GACb,UAAA,YAAY,IAAI,CAAC,mBAChB;IAAC,KAAK;IAAL;MACC,KAAK;MACL,GAAG;MACH,IAAI;MACJ,OAAO,EAAE,QAAQ,OAAO;MAExB,WAAU;MACV,YAAW;MAEX,cAAA;QAAC;QAAA;UACC;UACA,aAAa,sBAAsB,SAAS,WAAW,WAAW,cAAc;UAChF;UACA;UACA;UACA;QAAA;MAAA;IACF;IAXK,WAAW;EAAA,CAanB,EACH,CAAA;AAEJ;AAEA,IAAM,kBAAkB,GAAqB,GAAG;gBAChC,CAAC,EAAE,MAAM,MACrB,sDAAsD,MAAM,OAAO,UAAU,QAAQ;;;ACxFzF,IAAM,aAAa,CAAC,EAAE,UAAU,iBAAiB,SAAA,MAAgC;AACzE,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,WAAW,IAAI,YAAY;AAE7B,QAAA,WAAW,mBAAmB,aAAa,sBAAsB;AAGrE,aAAA;IAAC,QAAQ;IAAR;MACC,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,UAAU,cAAc;QACtB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,eACE,gBACE;QAAC;QAAA;UACC,eAAA,wBAAY,cAAO,CAAA,CAAA;UACnB,SAAQ;UACR,MAAM,mCAAmC,cAAc;UACvD,SAAS,MAAM,WAAW,QAAQ;UAClC,YAAU;UAET,UAAc,cAAA;YACb,IAAI,sCAAsC,cAAc;YACxD,gBAAgB,UAAU,cAAc;UAAA,CACzC;QAAA;MAAA;IACH;EAAA;AAKV;ACzCA,IAAM,gBAAgB,MAAM;AACpB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,aACG,wBAAA,QAAQ,MAAR,EACC,cAAA,yBAAC,MACC,EAAA,UAAA;QAAA,wBAAC,YAAW,CAAA,CAAA;QACZ;MAAC;MAAA;QACC,OAAM;QACN,WAAU;QACV,YAAW;QACX,gBAAe;QACf,YAAY;QAEZ,UAAA;cAAC,wBAAA,KAAA,EAAI,eAAe,GAClB,cAAA,wBAAC,YAAA,EAAW,WAAU,cAAa,SAAQ,SACxC,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EAAA,CACH,EACF,CAAA;cACA,wBAAC,KAAI,EAAA,eAAe,GAClB,cAAA,wBAAC,YAAA,EAAW,WAAU,cAAa,SAAQ,WACxC,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EAAA,CACH,EACF,CAAA;cACA;YAAC;YAAA;cACC,OAAM;cACN,QAAO;cACP,SAAQ;cACR,MAAK;cACL,OAAM;cAEN,UAAA;oBAAA,wBAAC,QAAK,EAAA,GAAE,MAAK,GAAE,MAAK,OAAM,MAAK,QAAO,MAAK,IAAG,QAAO,MAAK,UAAU,CAAA;oBACpE;kBAAC;kBAAA;oBACC,GAAE;oBACF,MAAK;kBAAA;gBAAA;oBAEN,wBAAA,QAAA,EAAK,GAAE,MAAK,GAAE,MAAK,OAAM,MAAK,QAAO,MAAK,IAAG,QAAO,QAAO,UAAU,CAAA;cAAA;YAAA;UAAA;QACxE;MAAA;IAAA;EACF,EAAA,CACF,EACF,CAAA;AAEJ;ACnDA,IAAM,aAAa;EACjB,YAAY;IACV,UAAU;MACR,IAAI;MACJ,gBAAgB;IAAA;IAElB,QAAQ;MACN,IAAI;MACJ,gBAAgB;IAAA;EAClB;EAEF,uBAAuB;IACrB,UAAU;MACR,IAAI;MACJ,gBAAgB;IAAA;IAElB,QAAQ;MACN,IAAI;MACJ,gBAAgB;IAAA;EAClB;EAEF,oBAAoB;IAClB,UAAU;MACR,IAAI;MACJ,gBAAgB;IAAA;IAElB,QAAQ;MACN,IAAI;MACJ,gBAAgB;IAAA;EAClB;EAEF,qBAAqB;IACnB,UAAU;MACR,IAAI;MACJ,gBAAgB;IAAA;IAElB,QAAQ;MACN,IAAI;MACJ,gBAAgB;IAAA;EAClB;AAEJ;AAOA,IAAM,aAAa,CAAC,EAAE,WAAW,mBAAA,MAA0C;AACnE,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,aAAA,wBACG,eACC,EAAA,cAAA;IAAC;IAAA;MACC,OAAO;MACP,kBAAkB,MAAM,cAAc,WAAW,SAAS,EAAE,QAAQ;MACpE,UAAU,CAAC,aAAa;AAEH,2BAAA,EAAE,MAAM,SAAA,CAAU;MAAA;MAEvC,cAAY,cAAc;QACxB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,MAAK;MAEJ,UAAA,OAAO,QAAQ,UAAU,EAAE,IAAI,CAAC,CAAC,UAAU,QAAQ,MAAM;AAEtD,mBAAA,wBAAC,oBAAA,EAAkC,OAAO,UACvC,UAAA,cAAc,SAAS,MAAM,EAAA,GADP,QAEzB;MAAA,CAEH;IAAA;EAAA,EAEL,CAAA;AAEJ;AAEA,IAAM,gBAAgB,GAAqB,GAAG;iBAC7B,CAAC,EAAE,MAAA,MAAY,MAAM,YAAY,QAAQ;;;iBAGzC,CAAC,EAAE,MAAA,MAAY,MAAM,UAAU,CAAC,CAAC;;;AC7ElD,IAAM,sBAAsB;AA0F5B,SAAS,mBAAmB;EAC1B;EACA;EACA;EACA;EACA;AACF,GAA6B;AACrB,QAAA,EAAE,aAAa,IAAI,YAAY;AAC/B,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,mBAAmB,cAAc;IACrC,IAAI;IACJ,gBAAgB;EAAA,CACjB;AAED,QAAM,wBAAwB,MAAM;AAClC;MACE;QACE;UACE,IAAI;UACJ,gBAAgB;QAAA;QAElB,EAAE,QAAQ,iBAAiB;MAAA;IAC7B;EACF;AAGF,QAAM,mBAAmB;IACvB,OAAM,+BAAO,SAAQ;IACrB,WAAU,+BAAO,aAAY;EAAA;AAG/B,QAAM,eAAe;IACnB,GAAG,SAAS;IACZ,YAAY;IACZ,QAAQ;IACR,SAAS;EAAA;AAGX,QAAM,EAAE,MAAM,iBAAiB,QAAQ,cAAA,IAAkB;IACvD,CAAC,eAAe,WAAW,YAAY;IACvC,YAAY;AACN,UAAA;AACI,cAAA,cAAiB,aAAU,YAAY;AAC7C,cAAM,MAAM,MAAM,MAAM,GAAG,mBAAmB,YAAY,WAAW,EAAE;AAEnE,YAAA,CAAC,IAAI,IAAI;AACL,gBAAA,IAAI,MAAM,sCAAsC;QAAA;AAGlD,cAAA,OAAQ,MAAM,IAAI,KAAK;AACtB,eAAA;MAAA,SACA,OAAO;MAAA;AAIT,aAAA;IAAA;IAET;MACE,YAAY;AACY,8BAAA;MAAA;MAExB,UAAU;AACW,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc,EAAE,IAAI,sBAAsB,gBAAgB,mBAAA,CAAoB;QAAA,CACxF;MAAA;IACH;EACF;AAGF,QAAM,iBAAiB;IACrB,GAAG,SAAS;IACZ,YAAY;IACZ,QAAQ;IACR,SAAS;EAAA;AAGX,QAAM,EAAE,MAAM,mBAAmB,QAAQ,gBAAA,IAAoB;IAC3D,CAAC,eAAe,aAAa,cAAc;IAC3C,YAAY;AACJ,YAAA,cAAiB,aAAU,cAAc;AAC/C,YAAM,MAAM,MAAM,MAAM,GAAG,mBAAmB,cAAc,WAAW,EAAE;AAErE,UAAA,CAAC,IAAI,IAAI;AACL,cAAA,IAAI,MAAM,wCAAwC;MAAA;AAGpD,YAAA,OAAQ,MAAM,IAAI,KAAK;AAEtB,aAAA;IAAA;IAET;MACE,YAAY;AACY,8BAAA;MAAA;MAExB,UAAU;AACW,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc,EAAE,IAAI,sBAAsB,gBAAgB,mBAAA,CAAoB;QAAA,CACxF;MAAA;IACH;EACF;AAGI,QAAA,yBAAyB,mBAAmB,WAAW,kBAAkB;AAE/E,QAAM,uBAAsB,iEAAwB,KAAK,gBAAe,CAAA;AACxE,QAAM,sBAAqB,mDAAiB,KAAK,eAAc,CAAA;AAE/D,QAAM,EAAE,WAAA,KAAe,iEAAwB,SAAQ,CAAA;AAEhD,SAAA;IACL;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;AAEJ;ACtNO,IAAM,qBAAqB,MAAe;AACzC,QAAA,eACJ,OAAO,cAAc,eAAe,OAAO,UAAU,WAAW,YAC5D,UAAU,SACV;AAEN,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS,YAAY;AAErD,QAAA,YAAY,MAAM,YAAY,IAAI;AAClC,QAAA,aAAa,MAAM,YAAY,KAAK;AAE1C,EAAM,gBAAU,MAAM;AACb,WAAA,iBAAiB,UAAU,SAAS;AACpC,WAAA,iBAAiB,WAAW,UAAU;AAE7C,WAAO,MAAM;AACJ,aAAA,oBAAoB,UAAU,SAAS;AACvC,aAAA,oBAAoB,WAAW,UAAU;IAAA;EAClD,GACC,CAAA,CAAE;AAEE,SAAA;AACT;ACeA,IAAM,SAAS;AACf,IAAM,WAAW;AAEjB,IAAM,kBAAkB,MAAM;AACtB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,WAAW,IAAI,YAAY;AAC7B,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,CAAC,EAAE,MAAA,GAAS,QAAQ,IAAI,eAAqC;AACnE,QAAM,kBAAkB,YAAY,+BAAO,QAAQ,GAAG,KAAK;AAErD,QAAA;IACJ,YAAY;IACZ;IACA;IACA;EAAA,IACE,WAAW,mBAAmB,CAAC,UAAU,KAAK;AAClD,QAAM,WAAW,mBAAmB;AAE9B,QAAA,kBAAiB,+BAAO,mBAAkB;AAEhD,QAAM,CAAC,UAAU,WAAW,IAAU,eAAmB;IACvD,QAAQ,mBAAmB,SAAS,EAAE,GAAG,MAAA,IAAU,CAAA;IACnD,UAAU,mBAAmB,WAAW,EAAE,GAAG,MAAA,IAAU,CAAA;EAAC,CACzD;AAED,EAAM,gBAAU,MAAM;AACpB,eAAW,oBAAoB;EAAA,GAC9B,CAAC,UAAU,CAAC;AAEf,EAAM,gBAAU,MAAM;AACpB,QAAI,CAAC,qBAAqB;AACL,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IAAA;EACH,GACC,CAAC,oBAAoB,qBAAqB,aAAa,CAAC;AAErD,QAAA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,IACE,mBAAmB,EAAE,gBAAgB,iBAAiB,OAAO,UAAU,cAAA,CAAe;AAE1F,MAAI,CAAC,UAAU;AACb,eAAA,wBAAQ,eAAc,CAAA,CAAA;EAAA;AAGlB,QAAA,kBAAkB,CAAC,QAAgB;AACvC,UAAM,cAAc,QAAQ,UAAU,QAAQ,WAAW,MAAM;AAEzD,UAAA,cAAc,SAAS,WAAW,KAAK,OAAO,KAAK,SAAS,WAAW,CAAC,EAAE;AAEhF,QAAI,aAAa;AACN,eAAA;;QAEP,GAAG,SAAS,WAAW;QACvB,SAAQ,+BAAO,WAAU;;QAEzB,gBAAgB;QAChB,MAAM;MAAA,CACP;IAAA,OACI;AACI,eAAA;;QAEP,gBAAgB;;QAEhB,aAAa,CAAA;QACb,YAAY,CAAA;QACZ,MAAM;QACN,MAAM;;QAEN,SAAQ,+BAAO,WAAU;MAAA,CAC1B;IAAA;EACH;AAGI,QAAA,qBAAqB,CAAC,WAA0C;AACpE,aAAS,EAAE,GAAG,QAAQ,MAAM,EAAA,CAAG;AAC/B,gBAAY,CAAC,UAAU;MACrB,GAAG;MACH,CAAC,cAAc,GAAG,EAAE,GAAG,KAAK,cAAc,GAAG,GAAG,OAAO;IAAA,EACvD;EAAA;AAGE,QAAA,oBAAoB,CAAC,eAA4B;AAC5C,aAAA,EAAE,CAAC,UAAU,GAAG,CAAA,GAAI,MAAM,OAAU,GAAG,QAAQ;AAC5C,gBAAA,CAAC,UAAU,EAAE,GAAG,MAAM,CAAC,cAAc,GAAG,CAAA,EAAC,EAAI;EAAA;AAGrD,QAAA,yBAAgE,CAAC,EAAE,KAAK;;IAE5E,mBAAmB,EAAE,KAAA,CAAM;;AAG7B,QAAM,wBAAwB,OAAO,KAAK,gBAAgB,CAAA,CAAE;AAE5D,aAAA,wBACG,QAAQ,MAAR,EACC,cAAC,yBAAA,KAAK,MAAL,EACC,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAc,cAAA;MACb,IAAI;MACJ,gBAAgB;IAAA,CACjB,EACH,CAAA;QACA,wBAAC,YAAW,EAAA,UAAoB,eAAgC,CAAA;QAC/D,wBAAA,QAAQ,SAAR,EACC,cAAC,yBAAA,KAAK,MAAL,EAAU,SAAQ,UAAS,eAAe,iBAAiB,OAAO,gBACjE,UAAA;UAAA,yBAAC,MAAK,EAAA,gBAAe,iBAAgB,eAAe,GAClD,UAAA;YAAA;UAAC,KAAK;UAAL;YACC,cAAY,cAAc;cACxB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YAED,UAAA;kBAAA,yBAAC,KAAK,SAAL,EAAa,OAAO,QAClB,UAAA;gBAAc,cAAA;kBACb,IAAI;kBACJ,gBAAgB;gBAAA,CACjB;gBAAG;gBACH,kBAAkB,IAAI,gBAAgB,KAAK,WAAW,KAAK,MAAM;cAAA,EACpE,CAAA;kBACC,yBAAA,KAAK,SAAL,EAAa,OAAO,UAClB,UAAA;gBAAc,cAAA;kBACb,IAAI;kBACJ,gBAAgB;gBAAA,CACjB;gBAAG;gBACH,oBAAoB,IAAI,kBAAkB,KAAK,WAAW,KAAK,MAAM;cAAA,EACxE,CAAA;YAAA;UAAA;QAAA;YAGF,wBAAC,KAAI,EAAA,OAAM,OACT,cAAA;UAAC;UAAA;YACC,MAAK;YACL,SAAS,MAAM,SAAS,EAAE,QAAQ,IAAI,MAAM,EAAA,CAAG;YAC/C,OAAO,+BAAO;YACd,UAAU,CAAC,MAAM,SAAS,EAAE,QAAQ,EAAE,OAAO,OAAO,MAAM,EAAA,CAAG;YAC7D,YAAY,cAAc;cACxB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,aAAa,cAAc;cACzB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YAEA,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QAAA,EAEL,CAAA;MAAA,EACF,CAAA;UACC,yBAAA,MAAA,EAAK,eAAe,GAAG,KAAK,GAC3B,UAAA;YAAA;UAAC;UAAA;YACC,YAAW,+BAAO,SAAQ;YAC1B,oBAAoB;UAAA;QAAA;YAEtB;UAAC;UAAA;YACC;YACA;YACA;YACA,OAAO,SAAS,CAAA;YAChB;YACA;UAAA;QAAA;MACF,EACF,CAAA;UACC,wBAAA,KAAK,SAAL,EAAa,OAAO,QACnB,cAAA;QAAC;QAAA;UACC,aAAa,mDAAiB;UAC9B,QAAQ;UACR;UACA;UACA;UACA,gBAAe;UACf,kBAAkB;UAClB;QAAA;MAAA,EAEJ,CAAA;UACC,wBAAA,KAAK,SAAL,EAAa,OAAO,UACnB,cAAA;QAAC;QAAA;UACC,aAAa,uDAAmB;UAChC,QAAQ;UACR;UACA;UACA;UACA,gBAAe;UACf;QAAA;MAAA,EAEJ,CAAA;UAAA,yBACC,WAAW,MAAX,EAAiB,GAAG,YAAY,iBAAiB,IAChD,UAAA;YAAC,wBAAA,WAAW,UAAX,EAAoB,SAAS,CAAC,MAAM,MAAM,MAAM,KAAK,EAAG,CAAA;YACzD,wBAAC,WAAW,OAAX,CAAA,CAAiB;MAAA,EACpB,CAAA;UACA,wBAAC,KAAI,EAAA,YAAY,GACf,cAAA;QAAC;QAAA;UACC,MAAK;UACL,QAAO;UACP,KAAI;UACJ,OAAO,EAAE,gBAAgB,OAAO;UAChC,SAAS,MAAM,WAAW,0BAA0B;UAEpD,cAAA;YAAC;YAAA;cACC,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,UAAU,cAAc;gBACtB,IAAI;gBACJ,gBACE;cAAA,CACH;cACD,UAAA,wBAAO,cAAc,CAAA,CAAA;cACrB,gBAAe;cACf,eACE;gBAAC;gBAAA;kBACC,MAAK;kBACL,OAAM;kBACN,QAAO;kBACP,OAAO,EAAE,YAAY,SAAS;gBAAA;cAAA;YAChC;UAAA;QAEJ;MAAA,EAEJ,CAAA;IAAA,EAAA,CACF,EACF,CAAA;EAAA,EAAA,CACF,EACF,CAAA;AAEJ;AAEA,IAAM,2BAA2B,MAAM;AAC/B,QAAA,cAAc,iBAAiB,CAAC,UAAA;;AAAU,uBAAM,UAAU,YAAY,gBAA5B,mBAAyC;GAAI;AAE7F,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,iBAAA,CAAA,CAAgB,EACnB,CAAA;AAEJ;", "names": ["satisfies", "require_valid", "validRange", "satisfies", "satisfies", "satisfies", "require_semver", "satisfies", "validRange", "pluralize"]}