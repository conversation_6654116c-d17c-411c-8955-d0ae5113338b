import {
  getTranslation,
  useIntl
} from "./chunk-D63J2BWQ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$J,
  Page
} from "./chunk-4C2ZQ5OG.js";
import {
  EmptyStateLayout,
  LinkButton
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  NavLink
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1d
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/NoContentTypePage-5kwUPeFg.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var NoContentType = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Page.Main, { children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: getTranslation("header.name"),
          defaultMessage: "Content"
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsx)(
      EmptyStateLayout,
      {
        action: (0, import_jsx_runtime.jsx)(
          LinkButton,
          {
            tag: NavLink,
            variant: "secondary",
            startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
            to: "/plugins/content-type-builder/content-types/create-content-type",
            children: formatMessage({
              id: "app.components.HomePage.create",
              defaultMessage: "Create your first Content-type"
            })
          }
        ),
        content: formatMessage({
          id: "content-manager.pages.NoContentType.text",
          defaultMessage: "You don't have any content yet, we recommend you to create your first Content-Type."
        }),
        hasRadius: true,
        icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: "16rem" }),
        shadow: "tableShadow"
      }
    ) })
  ] });
};
export {
  NoContentType
};
//# sourceMappingURL=NoContentTypePage-5kwUPeFg-IORBW6CB.js.map
