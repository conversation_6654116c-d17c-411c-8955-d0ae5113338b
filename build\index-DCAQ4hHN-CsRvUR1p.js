const __vite__fileDeps=["index-C_cWV2yS-DghWe-Ea.js","strapi-YzJfjJ2z.js","strapi-COJtagOC.css","immer.esm-DNdbQyeB.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{ft as ze,ay as q,fu as C,fv as c,r as v,m as e,n as R,fw as Ge,fx as xe,al as _e,fy as He,a5 as Me,ba as Se,fz as Ne,fA as $,fB as We,fC as Ye,fD as Ze,fE as Je,fF as Ke,fG as E,fH as Xe,L as P,w as U,A as k,b0 as Z,Y as es,bl as ke,Z as ss,_ as ts,fI as as,S as K,J as O,I as N,ej as ns,aU as os,fJ as is,dX as rs,b1 as ls,a6 as Le,fK as ds,aj as je,ak as cs,fL as us,fM as be,fN as gs,s as X,fO as fs,b5 as ye,b3 as ps,b6 as hs,M as y,d$ as ms,fP as xs,a as js,u as bs,b as ys,g as vs,fQ as W,aI as Cs,fR as Fs,fS as Ms,G as J,x as ve,fT as Ss,fU as ks,eY as Ls,fV as Rs,bU as As,fW as ws,fX as Es,fY as Ts,fZ as Ds,f_ as Is,V as Bs,F as $s,f$ as Ps,aS as qs,g0 as Us,aH as Y,g1 as Os,g2 as Qs,g3 as Vs,bk as zs,E as p,g4 as ee,g5 as w,g6 as Gs}from"./strapi-YzJfjJ2z.js";const _s=(s,{pathname:a,query:n})=>{let o=[{id:null,label:{id:c("plugin.name"),defaultMessage:"Media Library"},href:s?E(a,n||{}):void 0}];return s?.parent&&typeof s?.parent!="number"&&s?.parent?.parent&&o.push([]),s?.parent&&typeof s.parent!="number"&&o.push({id:s.parent.id,label:s.parent.name,href:E(a,n||{},{folder:s.parent.id?.toString(),folderPath:s.parent.path})}),s&&o.push({id:s.id,label:s.name}),o},Hs=s=>{const{id:a}=Ps();return e.jsx(O,{position:"relative",zIndex:2,children:e.jsx(Le,{"aria-labelledby":`${a}-title`,...s})})},Re=({selected:s,onSuccess:a})=>{const{formatMessage:n}=C(),{remove:o}=fs(),d=async()=>{await o(s),a()};return e.jsxs(ye.Root,{children:[e.jsx(ye.Trigger,{children:e.jsx(k,{variant:"danger-light",size:"S",startIcon:e.jsx(ps,{}),children:n({id:"global.delete",defaultMessage:"Delete"})})}),e.jsx(hs,{onConfirm:d})]})};Re.propTypes={selected:p.arrayOf(ee,w).isRequired,onSuccess:p.func.isRequired};const Ns=()=>{const{formatMessage:s}=C(),{toggleNotification:a}=js(),n=bs(),{post:o}=ys(),i=vs(({destinationFolderId:h,filesAndFolders:f})=>{const l=f.reduce((u,b)=>{const{id:r,type:S}=b,m=S==="asset"?"fileIds":"folderIds";return u[m]||(u[m]=[]),u[m].push(r),u},{});return o("/upload/actions/bulk-move",{...l,destinationFolderId:h})},{onSuccess(h){const{data:{data:f}}=h;f?.files?.length>0&&(n.refetchQueries([W,"assets"],{active:!0}),n.refetchQueries([W,"asset-count"],{active:!0})),n.refetchQueries([W,"folders"],{active:!0}),a({type:"success",message:s({id:c("modal.move.success-label"),defaultMessage:"Elements have been moved successfully."})})}});return{...i,move:(h,f)=>i.mutateAsync({destinationFolderId:h,filesAndFolders:f})}},Ws=({onClose:s,selected:a=[],currentFolder:n})=>{const{formatMessage:o}=C(),{data:d,isLoading:i}=xs(),{move:g}=Ns();if(!d)return null;const h=async(l,{setErrors:u})=>{try{if(typeof l.destination!="string"){const b=l.destination.value;await g(b,a),s()}}catch(b){const r=ks(b);if(r&&"errors"in r){const S=r.errors?.reduce((m,T)=>(m[T.values?.path?.length||"destination"]=T.defaultMessage,m),{});Ls(S)||u(S)}}};if(i)return e.jsx(y.Content,{children:e.jsx(y.Body,{children:e.jsx(U,{justifyContent:"center",paddingTop:4,paddingBottom:4,children:e.jsx(Cs,{children:o({id:c("content.isLoading"),defaultMessage:"Content is loading."})})})})});const f={destination:{value:n?.id||"",label:n?.name||d[0].label}};return e.jsx(y.Content,{children:e.jsx(Fs,{validateOnChange:!1,onSubmit:h,initialValues:f,children:({values:l,errors:u,setFieldValue:b})=>e.jsxs(Ms,{noValidate:!0,children:[e.jsx(y.Header,{children:e.jsx(y.Title,{children:o({id:c("modal.folder.move.title"),defaultMessage:"Move elements to"})})}),e.jsx(y.Body,{children:e.jsx(J.Root,{gap:4,children:e.jsx(J.Item,{xs:12,col:12,direction:"column",alignItems:"stretch",children:e.jsxs(ve.Root,{id:"folder-destination",children:[e.jsx(ve.Label,{children:o({id:c("form.input.label.folder-location"),defaultMessage:"Location"})}),e.jsx(Ss,{options:d,onChange:r=>{b("destination",r)},defaultValue:typeof l.destination!="string"?l.destination:void 0,name:"destination",menuPortalTarget:document.querySelector("body"),inputId:"folder-destination",error:u?.destination,ariaErrorMessage:"destination-error"}),u.destination&&e.jsx(X,{variant:"pi",tag:"p",textColor:"danger600",children:u.destination})]})})})}),e.jsxs(y.Footer,{children:[e.jsx(y.Close,{children:e.jsx(k,{variant:"tertiary",name:"cancel",children:o({id:"cancel",defaultMessage:"Cancel"})})}),e.jsx(k,{type:"submit",loading:i,children:o({id:"modal.folder.move.submit",defaultMessage:"Move"})})]})]})})})},se=({selected:s,onSuccess:a,currentFolder:n})=>{const{formatMessage:o}=C(),[d,i]=v.useState(!1),g=()=>{i(!1),a()};return e.jsxs(y.Root,{open:d,onOpenChange:i,children:[e.jsx(y.Trigger,{children:e.jsx(k,{variant:"secondary",size:"S",startIcon:e.jsx(ms,{}),children:o({id:"global.move",defaultMessage:"Move"})})}),e.jsx(Ws,{currentFolder:n,onClose:g,selected:s})]})};se.defaultProps={currentFolder:void 0,selected:[]};se.propTypes={onSuccess:p.func.isRequired,currentFolder:w,selected:p.arrayOf(ee,w)};const te=({selected:s,onSuccess:a,currentFolder:n})=>{const{formatMessage:o}=C(),d=s.reduce(function(i,g){return g?.type==="folder"?i+g.files.count:i+1},0);return e.jsxs(U,{gap:2,paddingBottom:5,children:[e.jsx(X,{variant:"epsilon",textColor:"neutral600",children:o({id:c("list.assets.selected"),defaultMessage:"{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 asset} other {# assets}} selected"},{numberFolders:s.filter(({type:i})=>i==="folder").length,numberAssets:d})}),e.jsx(Re,{selected:s,onSuccess:a}),e.jsx(se,{currentFolder:n,selected:s,onSuccess:a})]})};te.defaultProps={currentFolder:void 0,selected:[]};te.propTypes={onSuccess:p.func.isRequired,currentFolder:w,selected:p.arrayOf(ee,w)};const Ys=({isFiltering:s,canCreate:a,canRead:n})=>s?{id:"list.assets-empty.title-withSearch",defaultMessage:"There are no elements with the applied filters"}:n?a?{id:"list.assets.empty-upload",defaultMessage:"Upload your first assets..."}:{id:"list.assets.empty",defaultMessage:"Media Library is empty"}:{id:"header.actions.no-permissions",defaultMessage:"No permissions to view"},Ae=({canCreate:s,isFiltering:a,canRead:n,onActionClick:o})=>{const{formatMessage:d}=C(),i=Ys({isFiltering:a,canCreate:s,canRead:n});return e.jsx(Rs,{icon:n?void 0:As,action:s&&!a&&e.jsx(k,{variant:"secondary",startIcon:e.jsx(Z,{}),onClick:o,children:d({id:c("header.actions.add-assets"),defaultMessage:"Add new assets"})}),content:d({...i,id:c(i.id)})})};Ae.propTypes={canCreate:p.bool.isRequired,canRead:p.bool.isRequired,isFiltering:p.bool.isRequired,onActionClick:p.func.isRequired};const Zs=()=>{const[s,a]=v.useState(!1),{formatMessage:n}=C(),{trackUsage:o}=Se(),[{query:d},i]=q(),g=d?.filters?.$and||[],h=l=>{i({filters:{$and:l},page:1})},f=l=>{o("didFilterMediaLibraryElements",{location:"content-manager",filter:Object.keys(l[l.length-1])[0]}),i({filters:{$and:l},page:1})};return e.jsxs(je.Root,{open:s,onOpenChange:a,children:[e.jsx(je.Trigger,{children:e.jsx(k,{variant:"tertiary",startIcon:e.jsx(cs,{}),size:"S",children:n({id:"app.utils.filters",defaultMessage:"Filters"})})}),e.jsx(us,{displayedFilters:be,filters:g,onSubmit:f,onToggle:a}),e.jsx(gs,{appliedFilters:g,filtersSchema:be,onRemoveFilter:h})]})},ae=({breadcrumbs:s,canCreate:a,folder:n,onToggleEditFolderDialog:o,onToggleUploadAssetDialog:d})=>{const{formatMessage:i}=C(),{pathname:g}=Me(),[{query:h}]=q(),f={...h,folder:n?.parent?.id??void 0,folderPath:n?.parent?.path??void 0};return e.jsx(P.Header,{title:i({id:c("plugin.name"),defaultMessage:"Media Library"}),subtitle:s&&n&&e.jsx(as,{tag:"nav",label:i({id:c("header.breadcrumbs.nav.label"),defaultMessage:"Folders navigation"}),breadcrumbs:s,currentFolderId:n?.id}),navigationAction:n&&e.jsx(es,{tag:ts,startIcon:e.jsx(ss,{}),to:`${g}?${ke.stringify(f,{encode:!1})}`,children:i({id:c("header.actions.folder-level-up"),defaultMessage:"Back"})}),primaryAction:a&&e.jsxs(U,{gap:2,children:[e.jsx(k,{startIcon:e.jsx(Z,{}),variant:"secondary",onClick:o,children:i({id:c("header.actions.add-folder"),defaultMessage:"Add new folder"})}),e.jsx(k,{startIcon:e.jsx(Z,{}),onClick:d,children:i({id:c("header.actions.add-assets"),defaultMessage:"Add new assets"})})]})})};ae.defaultProps={breadcrumbs:!1,folder:null};ae.propTypes={breadcrumbs:p.oneOfType([Gs,p.bool]),canCreate:p.bool.isRequired,folder:w,onToggleEditFolderDialog:p.func.isRequired,onToggleUploadAssetDialog:p.func.isRequired};const Js=K(O)`
  height: 3.2rem;
  display: flex;
  align-items: center;
`,Ce=K(X)`
  max-width: 100%;
`,Fe=K(O)`
  svg {
    path {
      fill: ${({theme:s})=>s.colors.neutral500};
    }
  }
`,Ks=()=>{const s=_e(),{canRead:a,canCreate:n,canUpdate:o,canCopyLink:d,canDownload:i,canConfigureView:g,isLoading:h}=He(),f=v.useRef(),{formatMessage:l}=C(),{pathname:u}=Me(),{trackUsage:b}=Se(),[{query:r},S]=q(),m=!!(r._q||r.filters),[T,we]=Ne(We.view,$.GRID),L=T===$.GRID,{data:F,isLoading:Ee,errors:Te}=Ye({skipWhen:!a,query:r}),{data:De,isLoading:Ie,errors:Be}=Ze({enabled:a&&F?.pagination?.page===1&&!Je(r),query:r}),{data:Q,isLoading:ne,error:$e}=Ke(r?.folder,{enabled:a&&!!r?.folder});$e?.response?.status===404&&s(u);const D=De?.map(t=>({...t,type:"folder",folderURL:E(u,r,t.id),isSelectable:o}))??[],x=D?.length||0,I=F?.results?.map(t=>({...t,type:"asset",isSelectable:o}))||[],j=I?.length??0,Pe=F?.pagination?.total,qe=ne||Ie||h||Ee,[oe,Ue]=v.useState(!1),[ie,re]=v.useState(!1),[V,z]=v.useState(void 0),[G,le]=v.useState(void 0),[M,{selectOne:B,selectAll:de}]=Xe(["type","id"],[]),ce=M?.length>0&&M?.length!==j+x,_=()=>Ue(t=>!t),ue=({created:t=!1}={})=>{t&&r?.page!=="1"&&S({...r,page:1}),re(A=>!A)},ge=(t,A)=>{t&&b("didSelectAllMediaLibraryElements"),de(A)},fe=t=>{b("didSortMediaLibraryElements",{location:"upload",sort:t}),S({sort:t})},pe=t=>{le(t),re(!0)},Oe=t=>{le(null),ue(t),f.current&&f.current.focus()},he=t=>{t===j&&F.pagination.page===F.pagination.pageCount&&F.pagination.page>1&&S({...r,page:F.pagination.page-1})},Qe=()=>{de(),he(M.length)};return qe?e.jsx(R.Loading,{}):Te||Be?e.jsx(R.Error,{}):e.jsxs(P.Root,{children:[e.jsxs(R.Main,{children:[e.jsx(ae,{breadcrumbs:!ne&&_s(Q,{pathname:u,query:r}),canCreate:n,onToggleEditFolderDialog:ue,onToggleUploadAssetDialog:_,folder:Q}),e.jsx(P.Action,{startActions:e.jsxs(e.Fragment,{children:[o&&L&&(j>0||x>0)&&e.jsx(Js,{paddingLeft:2,paddingRight:2,background:"neutral0",hasRadius:!0,borderColor:"neutral200",children:e.jsx(Le,{"aria-label":l({id:c("bulk.select.label"),defaultMessage:"Select all folders & assets"}),checked:ce?"indeterminate":(j>0||x>0)&&M.length===j+x,onCheckedChange:t=>ge(t,[...I,...D])})}),a&&L&&e.jsx(ds,{value:r?.sort,onChangeSort:fe}),a&&e.jsx(Zs,{})]}),endActions:e.jsxs(e.Fragment,{children:[g?e.jsx(Fe,{paddingTop:1,paddingBottom:1,children:e.jsx(N,{tag:os,to:{pathname:`${u}/configuration`,search:ke.stringify(r,{encode:!1})},label:l({id:"app.links.configure-view",defaultMessage:"Configure the view"}),children:e.jsx(ns,{})})}):null,e.jsx(Fe,{paddingTop:1,paddingBottom:1,children:e.jsx(N,{label:l(L?{id:c("view-switch.list"),defaultMessage:"List View"}:{id:c("view-switch.grid"),defaultMessage:"Grid View"}),onClick:()=>we(L?$.LIST:$.GRID),children:L?e.jsx(is,{}):e.jsx(rs,{})})}),e.jsx(ls,{label:l({id:c("search.label"),defaultMessage:"Search for an asset"}),trackedEvent:"didSearchMediaLibraryElements",trackedEventDetails:{location:"upload"}})]})}),e.jsxs(P.Content,{children:[M.length>0&&e.jsx(te,{currentFolder:Q,selected:M,onSuccess:Qe}),x===0&&j===0&&e.jsx(Ae,{canCreate:n,canRead:a,isFiltering:m,onActionClick:_}),a&&!L&&(j>0||x>0)&&e.jsx(ws,{assetCount:j,folderCount:x,indeterminate:ce,onChangeSort:fe,onChangeFolder:(t,A)=>s(E(u,r,{folder:t,folderPath:A})),onEditAsset:z,onEditFolder:pe,onSelectOne:B,onSelectAll:ge,rows:[...D,...I],selected:M,shouldDisableBulkSelect:!o,sortQuery:r?.sort??""}),a&&L&&e.jsxs(e.Fragment,{children:[x>0&&e.jsx(Es,{title:(m&&j>0||!m)&&l({id:c("list.folders.title"),defaultMessage:"Folders ({count})"},{count:x})||"",children:D.map(t=>{const Ve=!!M.filter(({type:H})=>H==="folder").find(H=>H.id===t.id),me=E(u,r,{folder:t?.id,folderPath:t?.path});return e.jsx(J.Item,{col:3,direction:"column",alignItems:"stretch",children:e.jsx(Ts,{ref:G&&t.id===G.id?f:void 0,ariaLabel:t.name,id:`folder-${t.id}`,to:me,startAction:B&&t.isSelectable?e.jsx(Hs,{"data-testid":`folder-checkbox-${t.id}`,checked:Ve,onCheckedChange:()=>B(t)}):null,cardActions:e.jsx(N,{"aria-label":l({id:c("list.folder.edit"),defaultMessage:"Edit folder"}),onClick:()=>pe(t),children:e.jsx($s,{})}),children:e.jsx(Ds,{children:e.jsx(Is,{to:me,children:e.jsxs(U,{tag:"h2",direction:"column",alignItems:"start",maxWidth:"100%",children:[e.jsxs(Ce,{fontWeight:"semiBold",textColor:"neutral800",ellipsis:!0,children:[t.name,e.jsx(Bs,{children:":"})]}),e.jsx(Ce,{tag:"span",textColor:"neutral600",variant:"pi",ellipsis:!0,children:l({id:c("list.folder.subtitle"),defaultMessage:"{folderCount, plural, =0 {# folder} one {# folder} other {# folders}}, {filesCount, plural, =0 {# asset} one {# asset} other {# assets}}"},{folderCount:t.children.count,filesCount:t.files.count})})]})})})})},`folder-${t.id}`)})}),j>0&&x>0&&e.jsx(O,{paddingTop:6,paddingBottom:4,children:e.jsx(qs,{})}),j>0&&e.jsx(Us,{assets:I,onEditAsset:z,onSelectAsset:B,selectedAssets:M.filter(({type:t})=>t==="asset"),title:(!m||m&&x>0)&&F?.pagination?.page===1&&l({id:c("list.assets.title"),defaultMessage:"Assets ({count})"},{count:Pe})||""})]}),e.jsxs(Y.Root,{...F.pagination,children:[e.jsx(Y.PageSize,{}),e.jsx(Y.Links,{})]})]})]}),oe&&e.jsx(Os,{open:oe,onClose:_,trackedLocation:"upload",folderId:r?.folder}),ie&&e.jsx(Qs,{open:ie,onClose:Oe,folder:G,parentFolderId:r?.folder,location:"upload"}),V&&e.jsx(Vs,{onClose:t=>{t===null&&he(1),z(void 0)},open:!!V,asset:V,canUpdate:o,canCopyLink:d,canDownload:i,trackedLocation:"upload"})]})},Xs=v.lazy(()=>zs(()=>import("./index-C_cWV2yS-DghWe-Ea.js"),__vite__mapDeps([0,1,2,3]))),st=()=>{const{config:{isLoading:s,isError:a,data:n}}=ze(),[{rawQuery:o},d]=q(),{formatMessage:i}=C(),g=i({id:c("plugin.name"),defaultMessage:"Media Library"});return v.useEffect(()=>{s||a||o||d({sort:n.sort,page:1,pageSize:n.pageSize})},[s,a,n,o,d]),s?e.jsxs(e.Fragment,{children:[e.jsx(R.Title,{children:g}),e.jsx(R.Loading,{})]}):e.jsx(R.Main,{children:o?e.jsx(v.Suspense,{fallback:e.jsx(R.Loading,{}),children:e.jsxs(Ge,{children:[e.jsx(xe,{index:!0,element:e.jsx(Ks,{})}),e.jsx(xe,{path:"configuration",element:e.jsx(Xs,{config:n})})]})}):null})};export{st as default};
