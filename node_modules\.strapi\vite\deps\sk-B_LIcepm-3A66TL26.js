import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/sk-B_LIcepm.mjs
var sk = {
  "BoundRoute.title": "URL endpoint naviazaný k",
  "EditForm.inputSelect.description.role": "Pridá rolu k používateľovi.",
  "EditForm.inputSelect.label.role": "Predvolená rola pre autorizovaných používateľov",
  "EditForm.inputToggle.description.email": "Zakázať používateľovi vytvoriť viac účtov s rovnakou e-mailovou adresou pre rôznych poskytovateľov.",
  "EditForm.inputToggle.description.email-confirmation": "Ak je povolen<PERSON> (ON), registrovaní používatelia dostanú potvrdzovací e-mail.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "URL na ktorú bude používateľ presmerovaný po potvrdení e-mailu.",
  "EditForm.inputToggle.description.email-reset-password": "URL pre nastavenie nového hesla",
  "EditForm.inputToggle.description.sign-up": "Ak je zakázané (OFF), registrácie nebudú povolené. Nikto sa nebude môcť registrovať bez ohľadu na zvoleného poskytovateľa.",
  "EditForm.inputToggle.label.email": "Iba jedno konto pre jednu e-mailovú adresu",
  "EditForm.inputToggle.label.email-confirmation": "Povoliť potvrdzovanie e-mailových adries",
  "EditForm.inputToggle.label.email-confirmation-redirection": "URL pre potvrdenie e-mailovej adresy",
  "EditForm.inputToggle.label.email-reset-password": "URL pre obnovu hesla",
  "EditForm.inputToggle.label.sign-up": "Povoliť registrácie",
  "HeaderNav.link.advancedSettings": "Pokročilé nastavenia",
  "HeaderNav.link.emailTemplates": "Šablóny emailov",
  "HeaderNav.link.providers": "Poskytovatelia",
  "Plugin.permissions.plugins.description": "Zvoľte akcie, ktoré majú byť povolené pre plugin {name}.",
  "Plugins.header.description": "Zobrazujú sa iba akcie naviazané na URL endpoint.",
  "Plugins.header.title": "Oprávnenia",
  "Policies.header.hint": "Vyberte akciu a kliknite na ikonku nastavení pre zobrazenie naviazanej URL",
  "Policies.header.title": "Pokročilé nastavenia",
  "PopUpForm.Email.email_templates.inputDescription": "Ak si nie ste istý ako používať premenné, {link}",
  "PopUpForm.Email.options.from.email.label": "E-mail odosielateľa",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Meno odosielateľa",
  "PopUpForm.Email.options.from.name.placeholder": "Janko Hraško",
  "PopUpForm.Email.options.message.label": "Obsah e-mailu",
  "PopUpForm.Email.options.object.label": "Predmet",
  "PopUpForm.Email.options.response_email.label": "Odpovedať na e-mail",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Ak je zakázané, používatelia nebudú môcť použiť tohto poskytovateľa.",
  "PopUpForm.Providers.enabled.label": "Povoliť",
  "PopUpForm.Providers.key.label": "Client ID",
  "PopUpForm.Providers.key.placeholder": "TEXT",
  "PopUpForm.Providers.redirectURL.front-end.label": "URL presmerovania do vašej aplikácie",
  "PopUpForm.Providers.secret.label": "Client Secret",
  "PopUpForm.Providers.secret.placeholder": "TEXT",
  "PopUpForm.Providers.subdomain.label": "Host URI (Subdomain)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "Upraviť šablóny e-mailov",
  "notification.success.submit": "Nastavenia boli uložené",
  "plugin.description.long": "Zabezpečte vaše API pomocou JWT tokenov. Tento plugin taktiež podporuje ACL záznamy, ktoré umožňujú spravovať oprávnenia v rámci skupín používateľov.",
  "plugin.description.short": "Zabezpečte vaše API pomocou JWT tokenov",
  "plugin.name": "Roly a oprávnenia"
};
export {
  sk as default
};
//# sourceMappingURL=sk-B_LIcepm-3A66TL26.js.map
