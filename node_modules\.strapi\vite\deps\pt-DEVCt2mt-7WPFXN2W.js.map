{"version": 3, "sources": ["../../../@strapi/email/dist/_chunks/pt-DEVCt2mt.mjs"], "sourcesContent": ["const link = \"Link\";\nconst pt = {\n  link,\n  \"Settings.email.plugin.button.test-email\": \"Enviar e-mail de teste\",\n  \"Settings.email.plugin.label.defaultFrom\": \"E-mail do remetente padrão\",\n  \"Settings.email.plugin.label.defaultReplyTo\": \"E-mail de resposta padrão\",\n  \"Settings.email.plugin.label.provider\": \"Provedor de E-mail\",\n  \"Settings.email.plugin.label.testAddress\": \"E-mail do destinatário\",\n  \"Settings.email.plugin.notification.config.error\": \"Falha ao recuperar a configuração de e-mail\",\n  \"Settings.email.plugin.notification.data.loaded\": \"Os dados de configurações de e-mail foram carregados\",\n  \"Settings.email.plugin.notification.test.error\": \"Falha ao enviar um e-mail de teste para {to}\",\n  \"Settings.email.plugin.notification.test.success\": \"Teste de e-mail bem-sucedido, verifique a caixa de correio {to}\",\n  \"Settings.email.plugin.placeholder.defaultFrom\": \"ex: Strapi No-Reply <<EMAIL>>\",\n  \"Settings.email.plugin.placeholder.defaultReplyTo\": \"ex: Strapi <<EMAIL>>\",\n  \"Settings.email.plugin.placeholder.testAddress\": \"ex: <EMAIL>\",\n  \"Settings.email.plugin.subTitle\": \"Teste as configurações do plug-in de e-mail\",\n  \"Settings.email.plugin.text.configuration\": \"O plugin é configurado através do ficheiro {file}, acede a este {link} para a documentação.\",\n  \"Settings.email.plugin.title\": \"Configuração\",\n  \"Settings.email.plugin.title.config\": \"Configuração\",\n  \"Settings.email.plugin.title.test\": \"Testar entrega de e-mail\",\n  \"SettingsNav.link.settings\": \"Definições\",\n  \"SettingsNav.section-label\": \"Plug-in de e-mail\",\n  \"components.Input.error.validation.email\": \"Este é um e-mail inválido\"\n};\nexport {\n  pt as default,\n  link\n};\n//# sourceMappingURL=pt-DEVCt2mt.mjs.map\n"], "mappings": ";;;AAAA,IAAM,OAAO;AACb,IAAM,KAAK;AAAA,EACT;AAAA,EACA,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,iDAAiD;AAAA,EACjD,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,2CAA2C;AAC7C;", "names": []}