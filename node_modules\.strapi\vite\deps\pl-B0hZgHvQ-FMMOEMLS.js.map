{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/pl-B0hZgHvQ.mjs"], "sourcesContent": ["const Analytics = \"Analityka\";\nconst Documentation = \"Do<PERSON>mentacja\";\nconst Email = \"E-mail\";\nconst Password = \"Hasło\";\nconst Provider = \"Dostaw<PERSON>\";\nconst ResetPasswordToken = \"Token odzyskiwania hasła\";\nconst Role = \"Rola\";\nconst Username = \"Nazwa użytkownika\";\nconst Users = \"Użytkownicy\";\nconst anErrorOccurred = \"Ups! Coś poszło nie tak. Spróbuj ponownie.\";\nconst clearLabel = \"Wyczyść\";\nconst or = \"LUB\";\nconst skipToContent = \"Przeskocz do zawartości\";\nconst submit = \"Wyślij\";\nconst pl = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"Twoje konto zostało zablokowane\",\n\t\"Auth.components.Oops.text.admin\": \"Jeśli to błąd, skontaktuj się z administratorem.\",\n\t\"Auth.components.Oops.title\": \"Ups...\",\n\t\"Auth.form.button.forgot-password\": \"Wyślij e-mail\",\n\t\"Auth.form.button.go-home\": \"PRZEJDŹ DO STRONY GŁÓWNEJ\",\n\t\"Auth.form.button.login\": \"Zaloguj się\",\n\t\"Auth.form.button.login.providers.error\": \"Nie możemy połączyć Cię za pośrednictwem wybranego dostawcy.\",\n\t\"Auth.form.button.login.strapi\": \"Zaloguj się przez Strapi\",\n\t\"Auth.form.button.password-recovery\": \"Odzyskiwanie hasła\",\n\t\"Auth.form.button.register\": \"Zaczynajmy\",\n\t\"Auth.form.confirmPassword.label\": \"Potwierdź hasło\",\n\t\"Auth.form.currentPassword.label\": \"Obecne hasło\",\n\t\"Auth.form.email.label\": \"E-mail\",\n\t\"Auth.form.email.placeholder\": \"np. <EMAIL>\",\n\t\"Auth.form.error.blocked\": \"Twoje konto zostało zablokowane przez administratora.\",\n\t\"Auth.form.error.code.provide\": \"Podano nieprawidłowy kod.\",\n\t\"Auth.form.error.confirmed\": \"Adres e-mail Twojego konta nie został potwierdzony.\",\n\t\"Auth.form.error.email.invalid\": \"Ten e-mail jest nieprawidłowy.\",\n\t\"Auth.form.error.email.provide\": \"Podaj swoją nazwę użytkownika lub adres e-mail.\",\n\t\"Auth.form.error.email.taken\": \"Adres e-mail jest już zajęty.\",\n\t\"Auth.form.error.invalid\": \"Identyfikator lub hasło jest nieprawidłowe.\",\n\t\"Auth.form.error.params.provide\": \"Podano nieprawidłowe parametry.\",\n\t\"Auth.form.error.password.format\": \"Twoje hasło nie może zawierać symbolu `$` więcej niż trzy razy.\",\n\t\"Auth.form.error.password.local\": \"Ten użytkownik nigdy nie ustawił hasła lokalnego, zaloguj się za pośrednictwem dostawcy użytego podczas tworzenia konta.\",\n\t\"Auth.form.error.password.matching\": \"Hasła różnią się od siebie.\",\n\t\"Auth.form.error.password.provide\": \"Proszę podać swoje hasło.\",\n\t\"Auth.form.error.ratelimit\": \"Zbyt wiele prób, spróbuj ponownie za minutę.\",\n\t\"Auth.form.error.user.not-exist\": \"Ten adres e-mail nie istnieje.\",\n\t\"Auth.form.error.username.taken\": \"Nazwa użytkownika jest już zajęta.\",\n\t\"Auth.form.firstname.label\": \"Imię\",\n\t\"Auth.form.firstname.placeholder\": \"Jan\",\n\t\"Auth.form.forgot-password.email.label\": \"Wpisz swój e-mail\",\n\t\"Auth.form.forgot-password.email.label.success\": \"E-mail pomyślnie wysłany do\",\n\t\"Auth.form.lastname.label\": \"Nazwisko\",\n\t\"Auth.form.lastname.placeholder\": \"Kowalski\",\n\t\"Auth.form.password.hide-password\": \"Ukryj hasło\",\n\t\"Auth.form.password.hint\": \"Minimum 8 znaków, 1 z dużej litery, 1 z małej litery i 1 cyfra\",\n\t\"Auth.form.password.show-password\": \"Pokaż hasło\",\n\t\"Auth.form.register.news.label\": \"Informuj mnie na bieżąco o nowych funkcjach i nadchodzących ulepszeniach (robiąc to, akceptujesz postanowienia zawarte w niniejszych dokumentach - {terms} i {policy}).\",\n\t\"Auth.form.register.subtitle\": \"Dane logowania uzywane są tylko do uwierzytelniania w Strapi. Wszystkie zapisane dane będą przechowywane w twojej bazie danych.\",\n\t\"Auth.form.rememberMe.label\": \"Zapamiętaj mnie\",\n\t\"Auth.form.username.label\": \"Nazwa użytkownika\",\n\t\"Auth.form.username.placeholder\": \"Jan Kowalski\",\n\t\"Auth.form.welcome.subtitle\": \"Zaloguj się do swojego konta\",\n\t\"Auth.form.welcome.title\": \"Witaj w Strapi!\",\n\t\"Auth.link.forgot-password\": \"Zapomniałeś hasła?\",\n\t\"Auth.link.ready\": \"Chcesz się zalogować?\",\n\t\"Auth.link.signin\": \"Zaloguj\",\n\t\"Auth.link.signin.account\": \"Posiadasz już konto?\",\n\t\"Auth.login.sso.divider\": \"Lub zaloguj się za pomocą\",\n\t\"Auth.login.sso.loading\": \"Ładowanie dostawców...\",\n\t\"Auth.login.sso.subtitle\": \"Zaloguj się do twojego konta za pomocą SSO\",\n\t\"Auth.privacy-policy-agreement.policy\": \"polityka prywatności\",\n\t\"Auth.privacy-policy-agreement.terms\": \"warunki\",\n\t\"Auth.reset-password.title\": \"Zresetuj hasło\",\n\t\"Content Manager\": \"Menedżer treści\",\n\t\"Content Type Builder\": \"Kreator typów treści\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Przesyłanie plików\",\n\t\"HomePage.head.title\": \"Strona główna\",\n\t\"HomePage.roadmap\": \"Zobacz naszą roadmapę\",\n\t\"HomePage.welcome.congrats\": \"Gratulacje!\",\n\t\"HomePage.welcome.congrats.content\": \"Jesteś zalogowany jako pierwszy administrator. Aby odkryć potężne funkcje oferowane przez Strapi,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"zachęcamy do utworzenia pierwszego typu kolekcji.\",\n\t\"Media Library\": \"Biblioteka mediów\",\n\t\"New entry\": \"Nowy wpis\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"Role i Uprawnienia\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"Niektórych ról nie można usunąć, ponieważ są powiązane z użytkownikami\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"Nie można usunąć roli, jeśli jest powiązana z użytkownikami\",\n\t\"Roles.RoleRow.select-all\": \"Wybierz {name} do grupowych czynności\",\n\t\"Roles.RoleRow.user-count\": \"{number, plural, =0 {#  } one {# } other {# }}\",\n\t\"Roles.components.List.empty.withSearch\": \"Brak roli odpowiadającej wyszukiwaniu ({search}) ...\",\n\t\"Settings.PageTitle\": \"Ustawienia - {name}\",\n\t\"Settings.apiTokens.addFirstToken\": \"Dodaj pierwszy API Token\",\n\t\"Settings.apiTokens.addNewToken\": \"Dodaj nowy API Token\",\n\t\"Settings.tokens.copy.editMessage\": \"Dla bezpieczeństwa, możesz tylko raz zobaczyć swój token.\",\n\t\"Settings.tokens.copy.editTitle\": \"Ten token nie jest już dostępny.\",\n\t\"Settings.tokens.copy.lastWarning\": \"Pamiętaj żeby skopiować token, nie będziesz w stanie kolejny raz go zobaczyć!\",\n\t\"Settings.apiTokens.create\": \"Stwórz nowy API Token\",\n\t\"Settings.apiTokens.description\": \"Lista wygenerowanych tokenów pozwalających korzystać z API\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"Brak zawartości...\",\n\t\"Settings.tokens.notification.copied\": \"Token skopiowany do schowka\",\n\t\"Settings.apiTokens.title\": \"API Tokeny\",\n\t\"Settings.tokens.types.full-access\": \"Full access\",\n\t\"Settings.tokens.types.read-only\": \"Read-only\",\n\t\"Settings.application.description\": \"Globalne informacje dotyczące panelu administratora\",\n\t\"Settings.application.edition-title\": \"obecny plan\",\n\t\"Settings.application.get-help\": \"Uzyskaj pomoc\",\n\t\"Settings.application.link-pricing\": \"Zobacz cennik\",\n\t\"Settings.application.link-upgrade\": \"Aktualizuj panel admina\",\n\t\"Settings.application.node-version\": \"wersja node\",\n\t\"Settings.application.strapi-version\": \"wersja strapi\",\n\t\"Settings.application.strapiVersion\": \"wersja strapi\",\n\t\"Settings.application.title\": \"Ogólne\",\n\t\"Settings.error\": \"Błąd\",\n\t\"Settings.global\": \"Ustawienia Globalne\",\n\t\"Settings.permissions\": \"Panel administracyjny\",\n\t\"Settings.permissions.category\": \"Ustawienia uprawnień dla {category}\",\n\t\"Settings.permissions.category.plugins\": \"Ustawienia uprawnień dla pluginu {category} \",\n\t\"Settings.permissions.conditions.anytime\": \"W dowolnym momencie\",\n\t\"Settings.permissions.conditions.apply\": \"Zastosuj\",\n\t\"Settings.permissions.conditions.can\": \"Może\",\n\t\"Settings.permissions.conditions.conditions\": \"Definiować warunki\",\n\t\"Settings.permissions.conditions.links\": \"Linki\",\n\t\"Settings.permissions.conditions.no-actions\": \"Najpierw musisz wybrać akcje (tworzenie, odczytywanie, aktualizowanie, ...) przed zdefiniowaniem dla nich warunków.\",\n\t\"Settings.permissions.conditions.none-selected\": \"W dowolnym momencie\",\n\t\"Settings.permissions.conditions.or\": \"LUB\",\n\t\"Settings.permissions.conditions.when\": \"Kiedy\",\n\t\"Settings.permissions.select-all-by-permission\": \"Wybierz wszystkie {label} uprawnienia\",\n\t\"Settings.permissions.select-by-permission\": \"Wybierz {label} uprawnienie\",\n\t\"Settings.permissions.users.create\": \"Utwórz nowego użytkownika\",\n\t\"Settings.permissions.users.email\": \"E-mail\",\n\t\"Settings.permissions.users.firstname\": \"Imię\",\n\t\"Settings.permissions.users.lastname\": \"Nazwisko\",\n\t\"Settings.permissions.users.form.sso\": \"Połącz z logowaniem jednokrotnym (SSO)\",\n\t\"Settings.permissions.users.form.sso.description\": \"Po włączeniu (ON) użytkownicy mogą logować się za pomocą logowania jednokrotnego (SSO)\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"Wszyscy użytkownicy posiadający dostęp do panelu admina\",\n\t\"Settings.permissions.users.tabs.label\": \"Uprawnienia\",\n\t\"Settings.profile.form.notify.data.loaded\": \"Dane twojego profilu zostały załadowane\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"Usuń wybór języka interfejsu\",\n\t\"Settings.profile.form.section.experience.here\": \"tutaj\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"Język aplikacji\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Wyświetla aplikację w wybranym języku.\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Zmiany preferencji będą miały zastosowanie tylko do tego profilu. Więcej informacji {tutaj}.\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"Motyw aplikacji\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"Wyświetla aplikację w wybranym motywie.\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{name} mode\",\n\t\"Settings.profile.form.section.experience.title\": \"Korzystanie\",\n\t\"Settings.profile.form.section.head.title\": \"Profil użytkownika\",\n\t\"Settings.profile.form.section.profile.page.title\": \"Strona profilu\",\n\t\"Settings.roles.create.description\": \"Zdefiniuj uprawnienia nadane roli\",\n\t\"Settings.roles.create.title\": \"Utwórz rolę\",\n\t\"Settings.roles.created\": \"Utworzono rolę\",\n\t\"Settings.roles.edit.title\": \"Edytuj rolę\",\n\t\"Settings.roles.form.button.users-with-role\": \"Użytkownicy z tą rolą\",\n\t\"Settings.roles.form.created\": \"Utworzono\",\n\t\"Settings.roles.form.description\": \"Nazwa i opis roli\",\n\t\"Settings.roles.form.permission.property-label\": \"Uprawnienia dla {label}\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"Uprawnienia do pól\",\n\t\"Settings.roles.form.permissions.create\": \"Tworzenie\",\n\t\"Settings.roles.form.permissions.delete\": \"Usuwanie\",\n\t\"Settings.roles.form.permissions.publish\": \"Publikowanie\",\n\t\"Settings.roles.form.permissions.read\": \"Odczyt\",\n\t\"Settings.roles.form.permissions.update\": \"Aktualizowanie\",\n\t\"Settings.roles.list.button.add\": \"Dodaj nową rolę\",\n\t\"Settings.roles.list.description\": \"Lista ról\",\n\t\"Settings.roles.title.singular\": \"rola\",\n\t\"Settings.sso.description\": \"Skonfiguruj ustawienia funkcji logowania jednokrotnego (SSO).\",\n\t\"Settings.sso.form.defaultRole.description\": \"Połączy to nowego uwierzytelnionego użytkownika do wybranej roli\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"Musisz mieć uprawnienia do odczytu ról administratora\",\n\t\"Settings.sso.form.defaultRole.label\": \"Domyślna rola\",\n\t\"Settings.sso.form.registration.description\": \"Utwórz nowego użytkownika przy logowaniu SSO, jeśli konto nie istnieje\",\n\t\"Settings.sso.form.registration.label\": \"Automatyczna rejestracja\",\n\t\"Settings.sso.title\": \"Jednokrotne logowanie (SSO)\",\n\t\"Settings.webhooks.create\": \"Utwórz webhook\",\n\t\"Settings.webhooks.create.header\": \"Utwórz nowy nagłówek\",\n\t\"Settings.webhooks.created\": \"Utworzono webhook\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"To zdarzenie istnieje tylko dla treści z włączonym systemem wersji roboczej/publikacji\",\n\t\"Settings.webhooks.events.create\": \"Utwórz\",\n\t\"Settings.webhooks.events.update\": \"Edytuj\",\n\t\"Settings.webhooks.form.events\": \"Zdarzenia\",\n\t\"Settings.webhooks.form.headers\": \"Nagłówki\",\n\t\"Settings.webhooks.form.url\": \"URL\",\n\t\"Settings.webhooks.headers.remove\": \"Usuń rząd {number}\",\n\t\"Settings.webhooks.key\": \"Klucz\",\n\t\"Settings.webhooks.list.button.add\": \"Dodaj nowy webhook\",\n\t\"Settings.webhooks.list.description\": \"Otrzymaj powiadomienia o zmianach POST.\",\n\t\"Settings.webhooks.list.empty.description\": \"Nie ma jeszcze żadnych webhooków\",\n\t\"Settings.webhooks.list.empty.link\": \"Zobacz naszą dokumentację\",\n\t\"Settings.webhooks.list.empty.title\": \"Nie ma jeszcze żadnych webhooków\",\n\t\"Settings.webhooks.list.th.actions\": \"akcje\",\n\t\"Settings.webhooks.list.th.status\": \"stan\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhooki\",\n\t\"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# } other {# }} wybrano\",\n\t\"Settings.webhooks.trigger\": \"Uruchom\",\n\t\"Settings.webhooks.trigger.cancel\": \"Anuluj wyzwalacz\",\n\t\"Settings.webhooks.trigger.pending\": \"W oczekiwaniu...\",\n\t\"Settings.webhooks.trigger.save\": \"Zapisz, aby uruchomić\",\n\t\"Settings.webhooks.trigger.success\": \"Powodzenie!\",\n\t\"Settings.webhooks.trigger.success.label\": \"Uruchomiono poprawnie\",\n\t\"Settings.webhooks.trigger.test\": \"Testowy-wyzwalacz\",\n\t\"Settings.webhooks.trigger.title\": \"Zapisz przed wyzwoleniem\",\n\t\"Settings.webhooks.value\": \"Wartość\",\n\t\"Usecase.back-end\": \"Back-end developer\",\n\t\"Usecase.button.skip\": \"Pomiń to pytanie\",\n\t\"Usecase.content-creator\": \"Content Creator\",\n\t\"Usecase.front-end\": \"Front-end developer\",\n\t\"Usecase.full-stack\": \"Full-stack developer\",\n\t\"Usecase.input.work-type\": \"Jaki rodzaj pracy wykonujesz?\",\n\t\"Usecase.notification.success.project-created\": \"Projekt został utworzony\",\n\t\"Usecase.other\": \"Inny\",\n\t\"Usecase.title\": \"Opowiedz nam trochę o sobie\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"Użytkownicy i Uprawnienia\",\n\t\"Users.components.List.empty\": \"Brak użytkowników...\",\n\t\"Users.components.List.empty.withFilters\": \"Brak użytkowników z zastosowanymi filtrami...\",\n\t\"Users.components.List.empty.withSearch\": \"Brak użytkowników odpowiadających wyszukiwaniu ({search})...\",\n\t\"admin.pages.MarketPlacePage.head\": \"Sklep - Pluginy\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"Jesteś offline\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"Musisz być połączony z internetem żeby skorzystać ze sklepu Strapi.\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"Skopiuj instalację\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"Polecenie instalacji gotowe do użycia w twoim terminalu\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"Dowiedz się więcej\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"Dowiedz się więcej o {pluginName}\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"Informacje\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"Zainstalowano\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Stworzony przez Strapi\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Zweryfikowany przez Strapi\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"Wyczyść wyszukiwarkę\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \"Brak wyników dla \\\"{target}\\\"\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"Szukaj pluginu\",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"Wyślij swój plugin\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \"Wykorzystaj Strapi lepiej\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"Brakuje pluginu?\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"Powiedz nam jakiego pluginu szukasz, a my damy znać o tym naszym developerom w razie gdyby szukali inspiracji!\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"Skopiuj do schowka\",\n\t\"app.component.search.label\": \"Szukaj {target}\",\n\t\"app.component.table.duplicate\": \"Duplikuj {target}\",\n\t\"app.component.table.edit\": \"Edytuj {target}\",\n\t\"app.component.table.select.one-entry\": \"Wybierz {target}\",\n\t\"app.components.BlockLink.blog\": \"Blog\",\n\t\"app.components.BlockLink.blog.content\": \"Czytaj najnowsze wiadomości na temat Strapi.\",\n\t\"app.components.BlockLink.code\": \"Przykłady\",\n\t\"app.components.BlockLink.code.content\": \"Ucz się poprzez testowanie prawdziwych projektów tworzonych przez naszą społeczność.\",\n\t\"app.components.BlockLink.documentation.content\": \"Odkryj kluczowe pojęcia, wskazówki i instrukcje.\",\n\t\"app.components.BlockLink.tutorial\": \"Tutoriale\",\n\t\"app.components.BlockLink.tutorial.content\": \"Podążaj za instrukcjami krok po kroku żeby użyć i dostosować Strapi.\",\n\t\"app.components.Button.cancel\": \"Anuluj\",\n\t\"app.components.Button.confirm\": \"Potwierdź\",\n\t\"app.components.Button.reset\": \"Resetuj\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"Wkrótce\",\n\t\"app.components.ConfirmDialog.title\": \"Potwierdzenie\",\n\t\"app.components.DownloadInfo.download\": \"Pobieranie w toku...\",\n\t\"app.components.DownloadInfo.text\": \"Może to chwilę potrwać. Dziękujemy za cierpliwość.\",\n\t\"app.components.EmptyAttributes.title\": \"Nie ma jeszcze żadnych pól\",\n\t\"app.components.EmptyStateLayout.content-document\": \"Brak zawartości\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"Nie masz dostępu do tej zawartości\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>Twórz i zarządzaj zawartością tutaj w Menedżerze Treści.</p><p>Przykład: Weźmy pod uwagę przykład z blogiem, możesz napisać aktykuł, zapisać i opublikować go kiedy tylko chcesz.</p><p>💡 Szybka wskazówka - Nie zapomnij kliknąć opublikuj, w treści którą tworzysz.</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"⚡️ Stwórz treść\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>Super, został ostatni krok!</p><b>🚀 Zobacz materiały w praktyce</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"Przetestuj API\",\n\t\"app.components.GuidedTour.CM.success.title\": \"Krok 2: Ukończony ✅\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>Kolekcje pomagają zarządzać wieloma pozycjami, a pojedyncze typy są odpowiednie do zarządzania tylko jednym wpisem.</p> <p>Przykład: Wyobraź sobie stronę z blogiem. Tam artykuły byłyby kolekcjami, a strona główna byłaby pojedynczym typem.</p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"Stwórz kolekcję\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 Stwórz swoją pierwszą kolekcję\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>Niezła robota!</p><b>⚡️ Czym chciałbyś się podzielić ze światem?</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"Krok 1: Ukończony ✅\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>Wygeneruj token aby otrzymać dostęp do treści, którą stworzyłeś.</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"Wygeneruj API Token\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Zobacz materiały w praktyce\",\n\t\"app.components.GuidedTour.apiTokens.success.content\": \"<p>Przetestuj treść wykonując żądanie HTTP:</p><ul><li><p>Pod URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Przy użyciu nagłówka: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Zerknij na <documentationLink>dokumentację</documentationLink> by poznać więcej sposobów na interakcję z treścią.</p>\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"Powrót na stronę główną\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"Krok 3: Ukończony ✅\",\n\t\"app.components.GuidedTour.create-content\": \"Stwórz zawartość\",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️ Czym chciałbyś się podzielić ze światem?\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"Przejdź do kreatora typów treści\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 Stwórz strukturę treści\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"Przetestuj API\",\n\t\"app.components.GuidedTour.skip\": \"Pomiń\",\n\t\"app.components.GuidedTour.title\": \"3 kroki żeby zacząć\",\n\t\"app.components.HomePage.button.blog\": \"Zobacz więcej na blogu\",\n\t\"app.components.HomePage.community\": \"Dołącz do społeczności\",\n\t\"app.components.HomePage.community.content\": \"Porozmawiaj z członkami zespołu, współtwórcami i programistami na różnych kanałach.\",\n\t\"app.components.HomePage.create\": \"Utwórz swój pierwszy typ zawartości\",\n\t\"app.components.HomePage.roadmap\": \"Zobacz naszą roadmapę\",\n\t\"app.components.HomePage.welcome\": \"Witaj na pokładzie 👋!\",\n\t\"app.components.HomePage.welcome.again\": \"Witaj 👋\",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Cieszymy się, że jesteś częścią społeczności. Nieustannie poszukujemy opinii, więc zachęcamy do wysyłania nam wiadomości\",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"Mamy nadzieję, że robisz postępy w swoim projekcie! Zachęcamy do zapoznania się z najnowszymi wiadomościami o Strapi. Dokładamy wszelkich starań, aby ulepszyć produkt w oparciu o Wasze opinie.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"problemy.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" lub wskazać \",\n\t\"app.components.ImgPreview.hint\": \"Przeciągnij i upuść plik w tym obszarze lub {browse}, aby przesłać plik\",\n\t\"app.components.ImgPreview.hint.browse\": \"przeglądaj\",\n\t\"app.components.InputFile.newFile\": \"Dodaj nowy plik\",\n\t\"app.components.InputFileDetails.open\": \"Otwórz w nowej karcie\",\n\t\"app.components.InputFileDetails.originalName\": \"Oryginalna nazwa:\",\n\t\"app.components.InputFileDetails.remove\": \"Usuń ten plik\",\n\t\"app.components.InputFileDetails.size\": \"Rozmiar:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"Pobranie i zainstalowanie pluginu może zająć kilka sekund.\",\n\t\"app.components.InstallPluginPage.Download.title\": \"Pobieranie...\",\n\t\"app.components.InstallPluginPage.description\": \"Rozszerz swoją aplikację bez wysiłku.\",\n\t\"app.components.LeftMenu.collapse\": \"Zwiń nawigację\",\n\t\"app.components.LeftMenu.expand\": \"Rozszerz nawigację\",\n\t\"app.components.LeftMenu.logout\": \"Wyloguj\",\n\t\"app.components.LeftMenu.navbrand.title\": \"Strapi Dashboard\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"Workplace\",\n\t\"app.components.LeftMenuFooter.help\": \"Wsparcie\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Obsługiwane przez \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Typy kolekcji\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Ustawienia\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Ogólne\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Nie zainstalowano jeszcze żadnych pluginów\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Pluginy\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Pojedyncze typy\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"Odinstalowanie pluginu może zająć kilka sekund.\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"Odinstalowywanie\",\n\t\"app.components.ListPluginsPage.description\": \"Lista zainstalowanych pluginów.\",\n\t\"app.components.ListPluginsPage.head.title\": \"Lista pluginów\",\n\t\"app.components.Logout.logout\": \"Wyloguj\",\n\t\"app.components.Logout.profile\": \"Profil\",\n\t\"app.components.MarketplaceBanner\": \"Odkryj pluginy tworzone przez społeczność oraz wiele więcej rzeczy żeby odpalić projekt, używając Strapi.\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"strapi logo\",\n\t\"app.components.MarketplaceBanner.link\": \"Sprawdź\",\n\t\"app.components.NotFoundPage.back\": \"Powrót na stronę główną\",\n\t\"app.components.NotFoundPage.description\": \"Nie znaleziono\",\n\t\"app.components.Official\": \"Oficjalne\",\n\t\"app.components.Onboarding.help.button\": \"Pomoc\",\n\t\"app.components.Onboarding.label.completed\": \"% ukończono\",\n\t\"app.components.Onboarding.title\": \"Odpal filmy szkoleniowe\",\n\t\"app.components.PluginCard.Button.label.download\": \"Pobierz\",\n\t\"app.components.PluginCard.Button.label.install\": \"Już zainstalowane\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Funkcja autoReload musi być włączona. Uruchom aplikację za pomocą polecenia `yarn develop`.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Rozumiem!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Ze względów bezpieczeństwa plugin można pobrać tylko w środowisku programistycznym.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Pobieranie jest niemożliwe\",\n\t\"app.components.PluginCard.compatible\": \"Zgodny z Twoją aplikacją\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Zgodny ze społecznością\",\n\t\"app.components.PluginCard.more-details\": \"Więcej szczegółów\",\n\t\"app.components.ToggleCheckbox.off-label\": \"Nie\",\n\t\"app.components.ToggleCheckbox.on-label\": \"Tak\",\n\t\"app.components.Users.MagicLink.connect\": \"Wyślij ten link do użytkownika, aby mógł się połączyć.\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"Wyślij ten link do użytkownika, pierwsze logowanie można wykonać za pośrednictwem dostawcy SSO\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"Szczegóły\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"Role użytkownika\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Twój użytkownik może mieć jedną lub kilka ról\",\n\t\"app.components.Users.SortPicker.button-label\": \"Sortuj według\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"E-mail (od A do Z)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"E-mail (od Z do A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"Imię (od A do Z)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"Imię (od Z do A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"Nazwisko (od A do Z)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"Nazwisko (od Z do A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"Nazwa użytkownika (od A do Z)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"Nazwa użytkownika (od Z do A)\",\n\t\"app.components.listPlugins.button\": \"Dodaj nowy plugin\",\n\t\"app.components.listPlugins.title.none\": \"Brak zainstalowanych pluginów\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Wystąpił błąd podczas odinstalowywania pluginu\",\n\t\"app.containers.App.notification.error.init\": \"Wystąpił błąd podczas żądania interfejsu API\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Jeśli nie otrzymasz tego łącza, skontaktuj się z administratorem.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Do otrzymania linku do odzyskiwania hasła może minąć kilka minut.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"E-mail wysłany\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"Aktywne\",\n\t\"app.containers.Users.EditPage.header.label\": \"Edytuj {name}\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"Edytuj użytkownika\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"Role przypisane\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"Stwórz użytkownika\",\n\t\"app.links.configure-view\": \"Skonfiguruj widok\",\n\t\"app.page.not.found\": \"Ups! Nie możemy znaleźć strony, której szukasz...\",\n\t\"app.static.links.cheatsheet\": \"Ściąga\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"Dodaj filtr\",\n\t\"app.utils.close-label\": \"Zamknij\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.duplicate\": \"Duplikuj\",\n\t\"app.utils.edit\": \"Edytuj\",\n\t\"app.utils.errors.file-too-big.message\": \"Plik jest za duży\",\n\t\"app.utils.filter-value\": \"Filtr\",\n\t\"app.utils.filters\": \"Filtry\",\n\t\"app.utils.notify.data-loaded\": \"{target} został załadowany\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"Opublikuj\",\n\t\"app.utils.select-all\": \"Zaznacz wszystko\",\n\t\"app.utils.select-field\": \"Zaznacz pole\",\n\t\"app.utils.select-filter\": \"Zaznacz filtr\",\n\t\"app.utils.unpublish\": \"Cofnij publikację\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"Ta zawartość jest aktualnie w trakcie budowy i wróci za jakiś czas!\",\n\t\"component.Input.error.validation.integer\": \"Wartość ta musi być liczbą całkowitą\",\n\t\"components.AutoReloadBlocker.description\": \"Uruchom Strapi za pomocą jednego z następujących poleceń:\",\n\t\"components.AutoReloadBlocker.header\": \"Ten plugin wymaga funkcji przeładowania.\",\n\t\"components.ErrorBoundary.title\": \"Coś poszło nie tak...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"zawiera\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"zawiera (wielkość liter nie ma znaczenia)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"kończy się\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"kończy się (wielkość liter nie ma znaczenia)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"kończy się na\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"kończy się na (wielkość liter nie ma znaczenia)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"jest większa niż\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"jest większe lub równe\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"jest mniejsze niż\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"jest mniejsze lub równe\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"nie jest\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"nie jest (wielkość liter nie ma znaczenia)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"nie zawiera\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"nie zawiera (wielkość liter nie ma znaczenia)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"nie jest null\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"jest null\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"zaczyna się na\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"zaczyna się na (wielkość liter nie ma znaczenia)\",\n\t\"components.Input.error.attribute.key.taken\": \"Ta wartość już istnieje\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Nie mogą być równe\",\n\t\"components.Input.error.attribute.taken\": \"Ta nazwa pola już istnieje\",\n\t\"components.Input.error.contain.lowercase\": \"Hasło musi zawierać co najmniej jedną małą literę\",\n\t\"components.Input.error.contain.number\": \"Hasło musi zawierać co najmniej jedną cyfrę\",\n\t\"components.Input.error.contain.uppercase\": \"Hasło musi zawierać co najmniej jedną wielką literę\",\n\t\"components.Input.error.contentTypeName.taken\": \"Ta nazwa już istnieje\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"Hasła nie pasują do siebie\",\n\t\"components.Input.error.validation.email\": \"To nie jest e-mail\",\n\t\"components.Input.error.validation.json\": \"To nie pasuje do formatu JSON\",\n\t\"components.Input.error.validation.lowercase\": \"Wartość musi być zapisana małymi literami\",\n\t\"components.Input.error.validation.max\": \"Wartość jest za wysoka {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"Wartość jest za długa {max}.\",\n\t\"components.Input.error.validation.min\": \"Wartość jest za mała {min}.\",\n\t\"components.Input.error.validation.minLength\": \"Wartość jest za krótka {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"Wartość nie może być większa\",\n\t\"components.Input.error.validation.regex\": \"Wartość nie jest zgodna z wyrażeniem regularnym.\",\n\t\"components.Input.error.validation.required\": \"To pole jest wymagane.\",\n\t\"components.Input.error.validation.unique\": \"Ta wartość jest już używana.\",\n\t\"components.InputSelect.option.placeholder\": \"Wybierz tutaj\",\n\t\"components.ListRow.empty\": \"Brak danych do wyświetlenia.\",\n\t\"components.NotAllowedInput.text\": \"Brak uprawnień do wyświetlania tego pola\",\n\t\"components.OverlayBlocker.description\": \"Używasz funkcji, która wymaga ponownego uruchomienia serwera. Poczekaj, aż serwer się uruchomi.\",\n\t\"components.OverlayBlocker.description.serverError\": \"Serwer powinien się już zrestartować, sprawdź swoje logi w terminalu.\",\n\t\"components.OverlayBlocker.title\": \"Czekam na ponowne uruchomienie...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Ponowne uruchomienie trwa dłużej niż oczekiwano\",\n\t\"components.PageFooter.select\": \"wpisów na stronie\",\n\t\"components.ProductionBlocker.description\": \"Ze względów bezpieczeństwa musimy wyłączyć ten plugin w innych środowiskach.\",\n\t\"components.ProductionBlocker.header\": \"Ten plugin jest dostępna tylko w trybie deweloperskim.\",\n\t\"components.Search.placeholder\": \"Szukaj...\",\n\t\"components.TableHeader.sort\": \"Sortuj {label}\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"Tryb Markdown\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"Tryb podglądu\",\n\t\"components.Wysiwyg.collapse\": \"Zwiń\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"Tytuł H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"Tytuł H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"Tytuł H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"Tytuł H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"Tytuł H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"Tytuł H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Dodaj tytuł\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"znaków\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Rozszerz\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Przeciągnij i upuść pliki, wklej ze schowka lub {browse}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"wybierz je\",\n\t\"components.pagination.go-to\": \"Idź do {page}\",\n\t\"components.pagination.go-to-next\": \"Idź do następnej strony\",\n\t\"components.pagination.go-to-previous\": \"Idź do poprzedniej strony\",\n\t\"components.pagination.remaining-links\": \"Dodaj {number} inne linki\",\n\t\"components.popUpWarning.button.cancel\": \"Nie, anuluj\",\n\t\"components.popUpWarning.button.confirm\": \"Tak, potwierdź\",\n\t\"components.popUpWarning.message\": \"Czy na pewno chcesz to usunąć?\",\n\t\"components.popUpWarning.title\": \"Proszę potwierdzić\",\n\t\"form.button.continue\": \"Dalej\",\n\t\"form.button.done\": \"Gotowe\",\n\t\"global.actions\": \"Akcje\",\n\t\"global.back\": \"Powrót\",\n\t\"global.change-password\": \"Zmień hasło\",\n\t\"global.content-manager\": \"Menedżer treści\",\n\t\"global.continue\": \"Dalej\",\n\t\"global.delete\": \"Usuń\",\n\t\"global.delete-target\": \"Usuń {target}\",\n\t\"global.description\": \"Opis\",\n\t\"global.details\": \"Szczegóły\",\n\t\"global.disabled\": \"Wyłączony\",\n\t\"global.documentation\": \"Dokumentacja\",\n\t\"global.enabled\": \"Włączony\",\n\t\"global.finish\": \"Zapisz\",\n\t\"global.marketplace\": \"Sklep\",\n\t\"global.name\": \"Nazwa\",\n\t\"global.none\": \"None\",\n\t\"global.password\": \"Hasło\",\n\t\"global.plugins\": \"Pluginy\",\n\t\"global.profile\": \"Profil\",\n\t\"global.prompt.unsaved\": \"Czy na pewno chcesz opuścić tę stronę? Wszystkie twoje modyfikacje zostaną utracone\",\n\t\"global.reset-password\": \"Zresetuj hasło\",\n\t\"global.roles\": \"Role\",\n\t\"global.save\": \"Zapisz\",\n\t\"global.see-more\": \"Zobacz więcej\",\n\t\"global.select\": \"Wybierz\",\n\t\"global.select-all-entries\": \"Wybierz wszystkie wpisy\",\n\t\"global.settings\": \"Ustawienia\",\n\t\"global.type\": \"Typ\",\n\t\"global.users\": \"Użytkownicy\",\n\t\"notification.contentType.relations.conflict\": \"Typ treści ma sprzeczne relacje\",\n\t\"notification.default.title\": \"Informacja:\",\n\t\"notification.error\": \"Wystąpił bład\",\n\t\"notification.error.layout\": \"Nie udało się pobrać układu\",\n\t\"notification.form.error.fields\": \"Ten formularz zawiera błędy\",\n\t\"notification.form.success.fields\": \"Zapisano zmiany\",\n\t\"notification.link-copied\": \"Link został skopiowany do schowka\",\n\t\"notification.permission.not-allowed-read\": \"Nie masz uprawnień, by zobaczyć ten dokument\",\n\t\"notification.success.delete\": \"Pozycja została usunięta\",\n\t\"notification.success.saved\": \"Zapisano\",\n\t\"notification.success.title\": \"Udało się:\",\n\t\"notification.version.update.message\": \"Dostępna jest nowa wersja Strapi!\",\n\t\"notification.warning.title\": \"Ostrzeżenie:\",\n\tor: or,\n\t\"request.error.model.unknown\": \"Ten model nie istnieje\",\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, pl as default, or, skipToContent, submit };\n//# sourceMappingURL=pl-B0hZgHvQ.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACD;", "names": []}