{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/ApplicationInfoPage/components/AdminSeatInfo.tsx"], "sourcesContent": ["import { Flex, <PERSON><PERSON><PERSON>, Typo<PERSON>, <PERSON>, Grid } from '@strapi/design-system';\nimport { ExternalLink, WarningCircle } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useSelector } from 'react-redux';\n\nimport { useRBAC } from '../../../../../../../../admin/src/hooks/useRBAC';\nimport { selectAdminPermissions } from '../../../../../../../../admin/src/selectors';\nimport { useLicenseLimits } from '../../../../../hooks/useLicenseLimits';\n\nconst BILLING_STRAPI_CLOUD_URL = 'https://cloud.strapi.io/profile/billing';\nconst BILLING_SELF_HOSTED_URL = 'https://strapi.io/billing/request-seats';\n\nexport const AdminSeatInfoEE = () => {\n  const { formatMessage } = useIntl();\n  const { settings } = useSelector(selectAdminPermissions);\n  const {\n    isLoading: isRBACLoading,\n    allowedActions: { canRead, canCreate, canUpdate, canDelete },\n  } = useRBAC(settings?.users ?? {});\n  const {\n    license,\n    isError,\n    isLoading: isLicenseLoading,\n  } = useLicenseLimits({\n    // TODO: this creates a waterfall which we should avoid to render earlier, but for that\n    // we will have to move away from data-fetching hooks to query functions.\n    // Short-term we could at least implement a loader, for the user to have visual feedback\n    // in case the requests take a while\n    enabled: !isRBACLoading && canRead && canCreate && canUpdate && canDelete,\n  });\n\n  const isLoading = isRBACLoading || isLicenseLoading;\n\n  if (isError || isLoading || !license) {\n    return null;\n  }\n\n  const { licenseLimitStatus, enforcementUserCount, permittedSeats, isHostedOnStrapiCloud } =\n    license;\n\n  if (!permittedSeats) {\n    return null;\n  }\n\n  return (\n    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n      <Typography variant=\"sigma\" textColor=\"neutral600\">\n        {formatMessage({\n          id: 'Settings.application.admin-seats',\n          defaultMessage: 'Admin seats',\n        })}\n      </Typography>\n      <Flex gap={2}>\n        <Flex>\n          <Typography tag=\"p\">\n            {formatMessage(\n              {\n                id: 'Settings.application.ee.admin-seats.count',\n                defaultMessage: '<text>{enforcementUserCount}</text>/{permittedSeats}',\n              },\n              {\n                permittedSeats,\n                enforcementUserCount,\n                text: (chunks) =>\n                  (\n                    <Typography\n                      fontWeight=\"semiBold\"\n                      textColor={enforcementUserCount > permittedSeats ? 'danger500' : undefined}\n                    >\n                      {chunks}\n                    </Typography>\n                  ) as any,\n              }\n            )}\n          </Typography>\n        </Flex>\n        {licenseLimitStatus === 'OVER_LIMIT' && (\n          <Tooltip\n            label={formatMessage({\n              id: 'Settings.application.ee.admin-seats.at-limit-tooltip',\n              defaultMessage: 'At limit: add seats to invite more users',\n            })}\n          >\n            <WarningCircle width=\"1.4rem\" height=\"1.4rem\" fill=\"danger500\" />\n          </Tooltip>\n        )}\n      </Flex>\n      <Link\n        href={isHostedOnStrapiCloud ? BILLING_STRAPI_CLOUD_URL : BILLING_SELF_HOSTED_URL}\n        isExternal\n        endIcon={<ExternalLink />}\n      >\n        {formatMessage(\n          {\n            id: 'Settings.application.ee.admin-seats.add-seats',\n            defaultMessage:\n              '{isHostedOnStrapiCloud, select, true {Add seats} other {Contact sales}}',\n          },\n          { isHostedOnStrapiCloud }\n        )}\n      </Link>\n    </Grid.Item>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,2BAA2B;AACjC,IAAM,0BAA0B;AAEzB,IAAM,kBAAkB,MAAM;AAC7B,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,EAAE,SAAA,IAAa,YAAY,sBAAsB;AACjD,QAAA;IACJ,WAAW;IACX,gBAAgB,EAAE,SAAS,WAAW,WAAW,UAAU;EAAA,IACzD,SAAQ,qCAAU,UAAS,CAAA,CAAE;AAC3B,QAAA;IACJ;IACA;IACA,WAAW;EAAA,IACT,iBAAiB;;;;;IAKnB,SAAS,CAAC,iBAAiB,WAAW,aAAa,aAAa;EAAA,CACjE;AAED,QAAM,YAAY,iBAAiB;AAE/B,MAAA,WAAW,aAAa,CAAC,SAAS;AAC7B,WAAA;EAAA;AAGT,QAAM,EAAE,oBAAoB,sBAAsB,gBAAgB,sBAAA,IAChE;AAEF,MAAI,CAAC,gBAAgB;AACZ,WAAA;EAAA;AAIP,aAAA,yBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,UAAA;QAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;MACb,IAAI;MACJ,gBAAgB;IAAA,CACjB,EACH,CAAA;QACA,yBAAC,MAAK,EAAA,KAAK,GACT,UAAA;UAAA,wBAAC,MACC,EAAA,cAAA,wBAAC,YAAW,EAAA,KAAI,KACb,UAAA;QACC;UACE,IAAI;UACJ,gBAAgB;QAAA;QAElB;UACE;UACA;UACA,MAAM,CAAC,eAEH;YAAC;YAAA;cACC,YAAW;cACX,WAAW,uBAAuB,iBAAiB,cAAc;cAEhE,UAAA;YAAA;UAAA;QACH;MAEN,EAAA,CAEJ,EACF,CAAA;MACC,uBAAuB,oBACtB;QAAC;QAAA;UACC,OAAO,cAAc;YACnB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UAED,cAAA,wBAAC,cAAc,EAAA,OAAM,UAAS,QAAO,UAAS,MAAK,YAAY,CAAA;QAAA;MAAA;IACjE,EAEJ,CAAA;QACA;MAAC;MAAA;QACC,MAAM,wBAAwB,2BAA2B;QACzD,YAAU;QACV,aAAA,wBAAU,eAAa,CAAA,CAAA;QAEtB,UAAA;UACC;YACE,IAAI;YACJ,gBACE;UAAA;UAEJ,EAAE,sBAAsB;QAAA;MAC1B;IAAA;EACF,EACF,CAAA;AAEJ;", "names": []}