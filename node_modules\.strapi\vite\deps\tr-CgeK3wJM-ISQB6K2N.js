import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/tr-CgeK3wJM.mjs
var groups = "Gruplar";
var models = "Koleksiyon Tipleri";
var pageNotFound = "Sayfa bulunamadı";
var tr = {
  "App.schemas.data-loaded": "Şemalar başarıyla yüklendi",
  "ListViewTable.relation-loaded": "İlişkiler yüklendi",
  "ListViewTable.relation-loading": "İlişkiler yükleniyor",
  "ListViewTable.relation-more": "Bu ilişki gösterilenden daha çok kayıt içeriyor",
  "EditRelations.title": "İlişkili Data",
  "HeaderLayout.button.label-add-entry": "Yeni bir girdi oluştur",
  "api.id": "API KİMLİK NO",
  "apiError.This attribute must be unique": "{field} benzersiz olmalı",
  "components.AddFilterCTA.add": "Filtreler",
  "components.AddFilterCTA.hide": "Filtreler",
  "components.DragHandle-label": "Sürükle",
  "components.DraggableAttr.edit": "Düzenlemek için tıklayın",
  "components.DraggableCard.delete.field": "Sil: {item}",
  "components.DraggableCard.edit.field": "Düzenle: {item}",
  "components.DraggableCard.move.field": "Taşı: {item}",
  "components.ListViewTable.row-line": "öğe satır {number}",
  "components.DynamicZone.ComponentPicker-label": "Bir bileşen seç",
  "components.DynamicZone.add-component": "{componentName}'e bir bileşen ekle",
  "components.DynamicZone.delete-label": "Sil: {name}",
  "components.DynamicZone.error-message": "Bileşen bir ya da daha fazla hata içeriyor",
  "components.DynamicZone.missing-components": "{number} eksik bileşen var",
  "components.DynamicZone.move-down-label": "Bileşeni aşağı taşı",
  "components.DynamicZone.move-up-label": "Bileşeni yukarı taşı",
  "components.DynamicZone.pick-compo": "Bir bileşen seç",
  "components.DynamicZone.required": "Bileşen zorunlu",
  "components.EmptyAttributesBlock.button": "Ayarlar sayfasına git",
  "components.EmptyAttributesBlock.description": "Ayarlarınızı değiştirebilirsiniz",
  "components.FieldItem.linkToComponentLayout": "Bileşenin düzenini belirle",
  "components.FieldSelect.label": "Bir alan ekle",
  "components.FilterOptions.button.apply": "Uygula",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Uygula",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Hepsini temizle",
  "components.FiltersPickWrapper.PluginHeader.description": "Filtrelemek için uygulanacak şartları ayarlayın",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filtreler",
  "components.FiltersPickWrapper.hide": "Gizle",
  "components.LeftMenu.Search.label": "Bir içerik tipi ara",
  "components.LeftMenu.collection-types": "Koleksiyon Tipleri",
  "components.LeftMenu.single-types": "Tekil Tipler",
  "components.LimitSelect.itemsPerPage": "Sayfa başı",
  "components.NotAllowedInput.text": "Bu alanı görmek için iznin yok",
  "components.RepeatableComponent.error-message": "Bileşen ya da bileşenler bir ya da daha çok hata içeriyor",
  "components.Search.placeholder": "Kayıt aramak için...",
  "components.Select.draft-info-title": "Durum: Taslak",
  "components.Select.publish-info-title": "Durum: Yayında",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Düzenleme görünümünü özelleştir",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Liste görünümünün ayarlarını belirle.",
  "components.SettingsViewWrapper.pluginHeader.title": "Görünümü ayarla - {name}",
  "components.TableDelete.delete": "Hepsini sil",
  "components.TableDelete.deleteSelected": "Silme seçildi",
  "components.TableDelete.label": "{number} girdi seçildi",
  "components.TableEmpty.withFilters": "Uygulanan filtrelerle {contentType} yoktur...",
  "components.TableEmpty.withSearch": "Aramaya karşılık gelen {contentType} yoktur ({search})...",
  "components.TableEmpty.withoutFilter": "{contentType} yoktur...",
  "components.empty-repeatable": "Henüz bir girdi yok. Aşağıdaki butona bas ve ekle.",
  "components.notification.info.maximum-requirement": "Alan sayı sınırına ulaştınız.",
  "components.notification.info.minimum-requirement": "Minimum gerekleri sağlayacak bir alan eklendi",
  "components.repeatable.reorder.error": "Bileşenin alanlarının sırasını değiştirirken bir hata oluştu. Lütfen tekrar deneyin.",
  "components.reset-entry": "Girdiyi sıfırla",
  "components.uid.apply": "uygula",
  "components.uid.available": "Müsait",
  "components.uid.regenerate": "Yeniden Üret",
  "components.uid.suggested": "önerilen",
  "components.uid.unavailable": "Müsait Değil",
  "containers.Edit.Link.Layout": "Düzeni ayarla",
  "containers.Edit.Link.Model": "Koleksiyon-tipini düzenle",
  "containers.Edit.addAnItem": "Bir öğe ekle...",
  "containers.Edit.clickToJump": "Kayıta atlamak için tıklayın",
  "containers.Edit.delete": "Sil",
  "containers.Edit.editing": "Düzenleniyor...",
  "containers.Edit.information": "Bilgi",
  "containers.Edit.information.by": "Tarafından",
  "containers.Edit.information.created": "Oluşturuldu",
  "containers.Edit.information.draftVersion": "taslak versiyonu",
  "containers.Edit.information.editing": "Düzenleniyor",
  "containers.Edit.information.lastUpdate": "Son güncelleme",
  "containers.Edit.information.publishedVersion": "yayınlanan versiyonu",
  "containers.Edit.pluginHeader.title.new": "Bir girdi oluştur",
  "containers.Edit.reset": "Reset",
  "containers.Edit.returnList": "Listeye dön",
  "containers.Edit.seeDetails": "Detaylar",
  "containers.Edit.submit": "Kaydet",
  "containers.EditSettingsView.modal-form.edit-field": "Alanı düzenle",
  "containers.EditView.add.new-entry": "Bir girdi ekle",
  "containers.Home.introduction": "Girişlerinizi düzenlemek için soldaki menüdeki ilgili bağlantıya gidin. Bu eklentinin ayarları düzenlemek için uygun bir yol bulunmamaktadır ve halen aktif geliştirme aşamasındadır.",
  "containers.Home.pluginHeaderDescription": "Güçlü ve güzel bir arayüz aracılığıyla girişlerinizi yönetin.",
  "containers.Home.pluginHeaderTitle": "İçerik Yönetimi",
  "containers.List.draft": "Taslak",
  "containers.List.errorFetchRecords": "Hata",
  "containers.List.published": "Yayınlandı",
  "containers.list.displayedFields": "Görüntülenen Alanlar",
  "containers.list.items": "{number} öğe",
  "containers.list.table-headers.publishedAt": "Durum",
  "containers.ListSettingsView.modal-form.edit-label": "Düzenle: {fieldName}",
  "containers.SettingPage.add.field": "Bir başka Alan ekle",
  "containers.SettingPage.attributes": "Nitelik alanları",
  "containers.SettingPage.attributes.description": "Niteliklerin sırasını tanımlayın",
  "containers.SettingPage.editSettings.description": "Yerleşimi oluşturmak için alanları sürükleyip bırakın",
  "containers.SettingPage.editSettings.entry.title": "Girdi başlığı",
  "containers.SettingPage.editSettings.entry.title.description": "Girdinin görüntülenen alanını belirle",
  "containers.SettingPage.editSettings.relation-field.description": "Düzenleme ve listeleme görünümünde görüntülenecek alanı belirle",
  "containers.SettingPage.editSettings.title": "Düzenle (ayarlar)",
  "containers.SettingPage.layout": "Düzen",
  "containers.SettingPage.listSettings.description": "Bu koleksiyon tipi için seçenekleri düzenle",
  "containers.SettingPage.listSettings.title": "Liste (ayarlar)",
  "containers.SettingPage.pluginHeaderDescription": "Bu koleksiyon tipi için özel ayarlı düzenle",
  "containers.SettingPage.settings": "Ayarlar",
  "containers.SettingPage.view": "Görüntüle",
  "containers.SettingViewModel.pluginHeader.title": "İçerik Yöneticisi - {name}",
  "containers.SettingsPage.Block.contentType.description": "Belirli ayarları yapılandırın",
  "containers.SettingsPage.Block.contentType.title": "Koleksiyon Tipleri",
  "containers.SettingsPage.Block.generalSettings.description": "Koleksiyon tiplerin için varsayılan seçenekleri düzenle",
  "containers.SettingsPage.Block.generalSettings.title": "Genel",
  "containers.SettingsPage.pluginHeaderDescription": "Tüm Koleksiyon Tiplerin ve Gruplarının ayarlarını düzenle",
  "containers.SettingsView.list.subtitle": "Koleksiyon ve Gruplarının düzen ve görünümlerini düzenle",
  "containers.SettingsView.list.title": "Düzenlemelerini görüntüle",
  "edit-settings-view.link-to-ctb.components": "Bileşeni düzenle",
  "edit-settings-view.link-to-ctb.content-types": "İçerik tipini düzenle",
  "emptyAttributes.button": "Koleksiyon Tipi kurucusuna git",
  "emptyAttributes.description": "Koleksiyon Tipine ilk alanı ekle",
  "emptyAttributes.title": "Henüz bir alan yok",
  "error.attribute.key.taken": "Bu değer zaten var.",
  "error.attribute.sameKeyAndName": "Eşit olamaz",
  "error.attribute.taken": "Bu alan ismi zaten var.",
  "error.contentTypeName.taken": "Bu alan ismi zaten var.",
  "error.model.fetch": "Modellerin yapılandırması getirilirken bir hata oluştu.",
  "error.record.create": "Kayıt oluşturulurken bir hata oluştu.",
  "error.record.delete": "Kayıt silinirken bir hata oluştu.",
  "error.record.fetch": "Kayıt getirilirken bir hata oluştu.",
  "error.record.update": "Kayıt güncelleme sırasında bir hata oluştu.",
  "error.records.count": "Sayım kayıtları getirilinceye kadar",
  "error.records.fetch": "Kayıtlar getirilirken bir hata oluştu.",
  "error.schema.generation": "Şema oluşturma sırasında bir hata oluştu.",
  "error.validation.json": "Bu JSON biçimi ile eşleşmiyor",
  "error.validation.max": "Değer çok yüksek.",
  "error.validation.maxLength": "Değer çok uzun.",
  "error.validation.min": "Değer çok az.",
  "error.validation.minLength": "Değer çok kısa.",
  "error.validation.minSupMax": "Üstü olamaz",
  "error.validation.regex": "Regex ile eşleşmiyor.",
  "error.validation.required": "Zorunlu alandır.",
  "form.Input.bulkActions": "Toplu işlemleri etkinleştir",
  "form.Input.defaultSort": "Varsayılan sıralama özelliği",
  "form.Input.description": "Açıklama",
  "form.Input.description.placeholder": "Profildeki görünen ad",
  "form.Input.editable": "Düzenlenebilir alan",
  "form.Input.filters": "Filtreleri etkinleştir",
  "form.Input.label": "Etiket",
  "form.Input.label.inputDescription": "Bu değer, tablonun başında görüntülenen etiketi geçersiz kılar",
  "form.Input.pageEntries": "Sayfa başına kayıtlar",
  "form.Input.placeholder": "Placeholder",
  "form.Input.placeholder.placeholder": "Müthiş değerim",
  "form.Input.search": "Aramayı etkinleştir",
  "form.Input.search.field": "Bu alanda aramayı etkinleştir",
  "form.Input.sort.field": "Bu alana göre sıralamayı etkinleştir",
  "form.Input.sort.order": "Varsayılan sıralama",
  "form.Input.wysiwyg": "WYSIWYG olarak görüntüle",
  "global.displayedFields": "Görüntülenen Alanlar",
  groups,
  "groups.numbered": "Gruplar ({number})",
  "header.name": "İçerik",
  "link-to-ctb": "Modeli düzenle",
  models,
  "models.numbered": "Koleksiyon Tipleri ({number})",
  "notification.error.displayedFields": "En az bir görüntülenen alana ihtiyacınız var",
  "notification.error.relationship.fetch": "İlişki getirme sırasında bir hata oluştu.",
  "notification.info.SettingPage.disableSort": "Sıralamaya izin verilen tek bir özelliğe sahip olmanız gerekir",
  "notification.info.minimumFields": "En az bir alan görüntülenebilir olmalı",
  "notification.upload.error": "Dosyalarını yüklerken bir hata oluştu",
  pageNotFound,
  "pages.ListView.header-subtitle": "{number} girdi bulundu",
  "pages.NoContentType.button": "İlk İçerik Tipini oluştur",
  "pages.NoContentType.text": "Henüz hiç içeriğin yok. Bir İçerik Tipi oluşturarak işe başlamanı öneririz.",
  "permissions.not-allowed.create": "Belge oluşturma iznin yok",
  "permissions.not-allowed.update": "Bu belgeyi görme iznin yok",
  "plugin.description.long": "Veritabanındaki verileri görmek, düzenlemek ve silmek için hızlı bir yol.",
  "plugin.description.short": "Veritabanındaki verileri görmek, düzenlemek ve silmek için hızlı bir yol.",
  "popUpWarning.warning.has-draft-relations.title": "Onay",
  "popUpWarning.warning.publish-question": "Hala yayınlamak istiyor musun?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Evet, yayınla",
  "popUpwarning.warning.has-draft-relations.message": "<b>{count} ilişki henüz yayınlanmadı ve bu beklenmedik bir davranışa yol açabilir.",
  "popover.display-relations.label": "İlişkileri göster",
  "select.currently.selected": "{count} tane seçili",
  "success.record.delete": "Silindi",
  "success.record.publish": "Yayınlandı",
  "success.record.save": "Kaydedildi",
  "success.record.unpublish": "Yayından Kaldırıldı",
  "utils.data-loaded": "{number} girdi başarıyla yüklendi"
};
export {
  tr as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=tr-CgeK3wJM-ISQB6K2N.js.map
