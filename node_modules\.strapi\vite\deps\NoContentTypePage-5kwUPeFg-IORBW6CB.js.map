{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/NoContentTypePage.tsx"], "sourcesContent": ["import { Page, Layouts } from '@strapi/admin/strapi-admin';\nimport { EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\n\nimport { getTranslation } from '../utils/translations';\n\nconst NoContentType = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Page.Main>\n      <Layouts.Header\n        title={formatMessage({\n          id: getTranslation('header.name'),\n          defaultMessage: 'Content',\n        })}\n      />\n      <Layouts.Content>\n        <EmptyStateLayout\n          action={\n            <LinkButton\n              tag={NavLink}\n              variant=\"secondary\"\n              startIcon={<Plus />}\n              to=\"/plugins/content-type-builder/content-types/create-content-type\"\n            >\n              {formatMessage({\n                id: 'app.components.HomePage.create',\n                defaultMessage: 'Create your first Content-type',\n              })}\n            </LinkButton>\n          }\n          content={formatMessage({\n            id: 'content-manager.pages.NoContentType.text',\n            defaultMessage:\n              \"You don't have any content yet, we recommend you to create your first Content-Type.\",\n          })}\n          hasRadius\n          icon={<EmptyDocuments width=\"16rem\" />}\n          shadow=\"tableShadow\"\n        />\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n\nexport { NoContentType };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,gBAAgB,MAAM;AACpB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAGxB,aAAA,yBAAC,KAAK,MAAL,EACC,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc;UACnB,IAAI,eAAe,aAAa;UAChC,gBAAgB;QAAA,CACjB;MAAA;IACH;QACA,wBAAC,QAAQ,SAAR,EACC,cAAA;MAAC;MAAA;QACC,YACE;UAAC;UAAA;YACC,KAAK;YACL,SAAQ;YACR,eAAA,wBAAY,eAAK,CAAA,CAAA;YACjB,IAAG;YAEF,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QACH;QAEF,SAAS,cAAc;UACrB,IAAI;UACJ,gBACE;QAAA,CACH;QACD,WAAS;QACT,UAAM,wBAAC,cAAe,EAAA,OAAM,QAAQ,CAAA;QACpC,QAAO;MAAA;IAAA,EAAA,CAEX;EACF,EAAA,CAAA;AAEJ;", "names": []}