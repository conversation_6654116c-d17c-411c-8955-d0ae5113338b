import { RuleIndex, RuleIndexOptions, RuleIndexOptionsOf, Public, RawRuleOf } from './RuleIndex';
import { Abilities, AbilityTuple, CanParameters } from './types';
import { Rule } from './Rule';
export interface AbilityOptions<A extends Abilities, Conditions> extends RuleIndexOptions<A, Conditions> {
}
export interface AnyAbility extends Public<PureAbility<any, any>> {
}
export interface AbilityOptionsOf<T extends AnyAbility> extends RuleIndexOptionsOf<T> {
}
export declare type AbilityClass<T extends AnyAbility> = new (rules?: RawRuleOf<T>[], options?: AbilityOptionsOf<T>) => T;
export declare type CreateAbility<T extends AnyAbility> = (rules?: RawRuleOf<T>[], options?: AbilityOptionsOf<T>) => T;
export declare class PureAbility<A extends Abilities = AbilityTuple, Conditions = unknown> extends RuleIndex<A, Conditions> {
    can(...args: CanParameters<A>): boolean;
    relevantRuleFor(...args: CanParameters<A>): Rule<A, Conditions> | null;
    cannot(...args: CanParameters<A>): boolean;
}
