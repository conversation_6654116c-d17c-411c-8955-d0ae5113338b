import{au as l,aQ as p,m as s,aR as r,J as a,w as t,S as x,aS as u,s as c}from"./strapi-YzJfjJ2z.js";import{S as g}from"./SSOProviders-BD7LHrkI-G0ZYqapf.js";const d=x(u)`
  flex: 1;
`,j=i=>{const{formatMessage:n}=l(),{isLoading:o,data:e=[]}=p(void 0,{skip:!window.strapi.features.isEnabled(window.strapi.features.SSO)});return!window.strapi.features.isEnabled(window.strapi.features.SSO)||!o&&e.length===0?s.jsx(r,{...i}):s.jsx(r,{...i,children:s.jsx(a,{paddingTop:7,children:s.jsxs(t,{direction:"column",alignItems:"stretch",gap:7,children:[s.jsxs(t,{children:[s.jsx(d,{}),s.jsx(a,{paddingLeft:3,paddingRight:3,children:s.jsx(c,{variant:"sigma",textColor:"neutral600",children:n({id:"Auth.login.sso.divider"})})}),s.jsx(d,{})]}),s.jsx(g,{providers:e,displayAllProviders:!1})]})})})};export{j as LoginEE};
