{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/_chunks/ms-ByvsQjRt.mjs"], "sourcesContent": ["const ms = {\n  \"BoundRoute.title\": \"<PERSON><PERSON> laluan ke\",\n  \"EditForm.inputSelect.description.role\": \"Ini akan meletakkan peranan yang dipilih pada pengguna baru.\",\n  \"EditForm.inputSelect.label.role\": \"<PERSON><PERSON>n asal untuk pengguna yang disahkan\",\n  \"EditForm.inputToggle.description.email\": \"Tidak membenarkan pengguna membuat beberapa akaun menggunakan alamat e-mel yang sama dengan pembekal pengesahan yang berbeza.\",\n  \"EditForm.inputToggle.description.email-confirmation\": \"<PERSON><PERSON><PERSON><PERSON> <PERSON> (ON), pengguna berdaftar baru akan menerima e-mel pengesahan.\",\n  \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Setelah mengesahkan e-mel anda, pilih di mana anda akan di redirect kan.\",\n  \"EditForm.inputToggle.description.email-reset-password\": \"URL halaman kata laluan tetapan semula untuk aplikasi anda\",\n  \"EditForm.inputToggle.description.sign-up\": \"<PERSON><PERSON><PERSON><PERSON> (MATI), proses pendaftaran tidak dibenarkan. Tidak ada yang boleh melanggan lagi tidak kira pembekal telah dipakai.\",\n  \"EditForm.inputToggle.label.email\": \"Satu akaun setiap alamat e-mel\",\n  \"EditForm.inputToggle.label.email-confirmation\": \"Aktifkan pengesahan e-mel\",\n  \"EditForm.inputToggle.label.email-confirmation-redirection\": \"Redirection url\",\n  \"EditForm.inputToggle.label.email-reset-password\": \"Halaman tetapan semula kata laluan\",\n  \"EditForm.inputToggle.label.sign-up\": \"Aktifkan pendaftaran\",\n  \"HeaderNav.link.advancedSettings\": \"Tetapan lanjut\",\n  \"HeaderNav.link.emailTemplates\": \"Templat e-mel\",\n  \"HeaderNav.link.providers\": \"Pembekal\",\n  \"Plugin.permissions.plugins.description\": \"Pilih arahan yang dibenarkan untuk plugin {name}.\",\n  \"Plugins.header.description\": \"Hanya arahan yang terpasang dengan laluan sahaja yang tersenarai di bawah.\",\n  \"Plugins.header.title\": \"Keizinan\",\n  \"Policies.header.hint\": \"Pilih tindakan aplikasi atau plugin dan klik pada ikon gear untuk melihat laluan yang terpasang\",\n  \"Policies.header.title\": \"Tetapan lanjut\",\n  \"PopUpForm.Email.email_templates.inputDescription\": \"Sekiranya anda tidak pasti cara menggunakan pemboleh ubah, {link} \",\n  \"PopUpForm.Email.options.from.email.label\": \"E-mel penghantar\",\n  \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Email.options.from.name.label\": \"Nama pengirim\",\n  \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n  \"PopUpForm.Email.options.message.label\": \"Mesej\",\n  \"PopUpForm.Email.options.object.label\": \"Subjek\",\n  \"PopUpForm.Email.options.response_email.label\": \"E-mel jawapan\",\n  \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Providers.enabled.description\": \"Sekiranya dinyahaktifkan, pengguna tidak akan dapat menggunakan pembekal ini.\",\n  \"PopUpForm.Providers.enabled.label\": \"Aktifkan\",\n  \"PopUpForm.Providers.key.label\": \"ID Pelanggan\",\n  \"PopUpForm.Providers.key.placeholder\": \"TEKS\",\n  \"PopUpForm.Providers.redirectURL.front-end.label\": \"URL pengubah hala ke aplikasi 'front-end' anda\",\n  \"PopUpForm.Providers.secret.label\": \"Client Secret\",\n  \"PopUpForm.Providers.secret.placeholder\": \"TEKS\",\n  \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n  \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n  \"PopUpForm.header.edit.email-templates\": \"Edit Templat E-mel\",\n  \"notification.success.submit\": \"Tetapan telah dikemas kini\",\n  \"plugin.description.long\": \"Lindungi API anda dengan proses pengesahan penuh berdasarkan JWT. Plugin ini juga dilengkapi dengan strategi ACL yang membolehkan anda mengurus pengizinan antara kumpulan pengguna.\",\n  \"plugin.description.short\": \"Lindungi API anda dengan proses pengesahan penuh berdasarkan JWT\"\n};\nexport {\n  ms as default\n};\n//# sourceMappingURL=ms-ByvsQjRt.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAC9B;", "names": []}