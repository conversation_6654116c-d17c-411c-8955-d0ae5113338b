{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/HomePage.tsx"], "sourcesContent": ["import { HomePageCE } from '../../../../admin/src/pages/HomePage';\nimport { useLicenseLimitNotification } from '../hooks/useLicenseLimitNotification';\n\nexport const HomePageEE = () => {\n  useLicenseLimitNotification();\n\n  return <HomePageCE />;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,IAAM,aAAa,MAAM;AACF,8BAAA;AAE5B,aAAA,wBAAQ,YAAW,CAAA,CAAA;AACrB;", "names": []}