{"version": 3, "sources": ["../../../@strapi/email/dist/_chunks/pl-DXcJCevg.mjs"], "sourcesContent": ["const pl = {\n  \"Settings.email.plugin.button.test-email\": \"<PERSON><PERSON><PERSON><PERSON><PERSON> testowy email\",\n  \"Settings.email.plugin.label.defaultFrom\": \"Domyślny nadawca\",\n  \"Settings.email.plugin.label.defaultReplyTo\": \"Domyślny obiorca\",\n  \"Settings.email.plugin.label.provider\": \"Dostawca\",\n  \"Settings.email.plugin.label.testAddress\": \"Odbiorca\",\n  \"Settings.email.plugin.notification.config.error\": \"Nie udało się uzyskać konfiguracji email\",\n  \"Settings.email.plugin.notification.data.loaded\": \"<PERSON> dotyczące ustawień email zostały załadowane\",\n  \"Settings.email.plugin.notification.test.error\": \"Nie udało się wysłać testowego maila do {to}\",\n  \"Settings.email.plugin.notification.test.success\": \"Testowy email wysłany, sprawdź skrzynkę: {to}\",\n  \"Settings.email.plugin.placeholder.defaultFrom\": \"np: Strapi No-Reply <<EMAIL>>\",\n  \"Settings.email.plugin.placeholder.defaultReplyTo\": \"np: Strapi <<EMAIL>>\",\n  \"Settings.email.plugin.placeholder.testAddress\": \"np: <EMAIL>\",\n  \"Settings.email.plugin.subTitle\": \"Sprawdź ustawienia dla testowego maila\",\n  \"Settings.email.plugin.text.configuration\": \"Ten plugin jest skonfigurowany w pliku {file}, sprawdź {link} do dokumentacji.\",\n  \"Settings.email.plugin.title\": \"Ustawienia\",\n  \"Settings.email.plugin.title.config\": \"Ustawienia\",\n  \"Settings.email.plugin.title.test\": \"Przetestuj dostarczanie maila\",\n  \"SettingsNav.link.settings\": \"Ustawienia\",\n  \"SettingsNav.section-label\": \"Plugin e-mail\",\n  \"components.Input.error.validation.email\": \"Ten email jest niepoprawny.\"\n};\nexport {\n  pl as default\n};\n//# sourceMappingURL=pl-DXcJCevg.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,iDAAiD;AAAA,EACjD,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,2CAA2C;AAC7C;", "names": []}