import {
  contentManagerApi
} from "./chunk-D63J2BWQ.js";
import {
  generateNKeysBetween
} from "./chunk-ELTZWS66.js";

// node_modules/@strapi/content-manager/dist/_chunks/relations-sRERvWmr.mjs
var relationsApi = contentManagerApi.injectEndpoints({
  endpoints: (build) => ({
    getRelations: build.query({
      query: ({ model, id, targetField, params }) => {
        return {
          url: `/content-manager/relations/${model}/${id}/${targetField}`,
          method: "GET",
          config: {
            params
          }
        };
      },
      serializeQueryArgs: (args) => {
        var _a, _b;
        const { endpointName, queryArgs } = args;
        return {
          endpointName,
          model: queryArgs.model,
          id: queryArgs.id,
          targetField: queryArgs.targetField,
          locale: (_a = queryArgs.params) == null ? void 0 : _a.locale,
          status: (_b = queryArgs.params) == null ? void 0 : _b.status
        };
      },
      merge: (currentCache, newItems) => {
        if (currentCache.pagination && newItems.pagination) {
          if (currentCache.pagination.page < newItems.pagination.page) {
            currentCache.results = [
              ...prepareTempKeys(newItems.results, currentCache.results),
              ...currentCache.results
            ];
            currentCache.pagination = newItems.pagination;
          } else if (newItems.pagination.page === 1) {
            currentCache.results = prepareTempKeys(newItems.results);
            currentCache.pagination = newItems.pagination;
          }
        }
      },
      forceRefetch({ currentArg, previousArg }) {
        var _a, _b, _c, _d;
        if (!(currentArg == null ? void 0 : currentArg.params) && !(previousArg == null ? void 0 : previousArg.params)) {
          return false;
        }
        return ((_a = currentArg == null ? void 0 : currentArg.params) == null ? void 0 : _a.page) !== ((_b = previousArg == null ? void 0 : previousArg.params) == null ? void 0 : _b.page) || ((_c = currentArg == null ? void 0 : currentArg.params) == null ? void 0 : _c.pageSize) !== ((_d = previousArg == null ? void 0 : previousArg.params) == null ? void 0 : _d.pageSize);
      },
      transformResponse: (response) => {
        if ("results" in response && response.results) {
          return {
            ...response,
            results: prepareTempKeys(response.results.toReversed())
          };
        } else {
          return response;
        }
      },
      providesTags: ["Relations"]
    }),
    searchRelations: build.query({
      query: ({ model, targetField, params }) => {
        return {
          url: `/content-manager/relations/${model}/${targetField}`,
          method: "GET",
          config: {
            params
          }
        };
      },
      serializeQueryArgs: (args) => {
        var _a, _b, _c;
        const { endpointName, queryArgs } = args;
        return {
          endpointName,
          model: queryArgs.model,
          targetField: queryArgs.targetField,
          _q: (_a = queryArgs.params) == null ? void 0 : _a._q,
          idsToOmit: (_b = queryArgs.params) == null ? void 0 : _b.idsToOmit,
          idsToInclude: (_c = queryArgs.params) == null ? void 0 : _c.idsToInclude
        };
      },
      merge: (currentCache, newItems) => {
        if (currentCache.pagination && newItems.pagination) {
          if (currentCache.pagination.page < newItems.pagination.page) {
            const existingIds = currentCache.results.map((item) => item.documentId);
            const uniqueNewItems = newItems.results.filter(
              (item) => !existingIds.includes(item.documentId)
            );
            currentCache.results.push(...uniqueNewItems);
            currentCache.pagination = newItems.pagination;
          } else if (newItems.pagination.page === 1) {
            currentCache.results = newItems.results;
            currentCache.pagination = newItems.pagination;
          }
        }
      },
      forceRefetch({ currentArg, previousArg }) {
        var _a, _b, _c, _d;
        if (!(currentArg == null ? void 0 : currentArg.params) && !(previousArg == null ? void 0 : previousArg.params)) {
          return false;
        }
        return ((_a = currentArg == null ? void 0 : currentArg.params) == null ? void 0 : _a.page) !== ((_b = previousArg == null ? void 0 : previousArg.params) == null ? void 0 : _b.page) || ((_c = currentArg == null ? void 0 : currentArg.params) == null ? void 0 : _c.pageSize) !== ((_d = previousArg == null ? void 0 : previousArg.params) == null ? void 0 : _d.pageSize);
      },
      transformResponse: (response) => {
        if (response.results) {
          return {
            ...response,
            results: response.results
          };
        } else {
          return response;
        }
      }
    })
  })
});
var prepareTempKeys = (relations, existingRelations = []) => {
  const [firstItem] = existingRelations.slice(0);
  const keys = generateNKeysBetween(null, (firstItem == null ? void 0 : firstItem.__temp_key__) ?? null, relations.length);
  return relations.map((datum, index) => ({
    ...datum,
    __temp_key__: keys[index]
  }));
};
var { useGetRelationsQuery, useLazySearchRelationsQuery } = relationsApi;
var getRelationLabel = (relation, mainField) => {
  const label = mainField && relation[mainField.name] ? relation[mainField.name] : null;
  if (typeof label === "string") {
    return label;
  }
  return relation.documentId;
};

export {
  useGetRelationsQuery,
  useLazySearchRelationsQuery,
  getRelationLabel
};
//# sourceMappingURL=chunk-3O7FGDSC.js.map
