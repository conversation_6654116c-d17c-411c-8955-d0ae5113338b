const i={"Settings.email.plugin.button.test-email":"<PERSON><PERSON><PERSON><PERSON><PERSON> testowy email","Settings.email.plugin.label.defaultFrom":"Domyślny nadawca","Settings.email.plugin.label.defaultReplyTo":"Domyślny obiorca","Settings.email.plugin.label.provider":"Dostawca","Settings.email.plugin.label.testAddress":"Odbiorca","Settings.email.plugin.notification.config.error":"Nie udało się uzyskać konfiguracji email","Settings.email.plugin.notification.data.loaded":"Dane dotyczące ustawień email zostały załadowane","Settings.email.plugin.notification.test.error":"Nie udało się wysłać testowego maila do {to}","Settings.email.plugin.notification.test.success":"Testowy email wysłany, sprawdź skrzynkę: {to}","Settings.email.plugin.placeholder.defaultFrom":"np: Strapi No-Reply <<EMAIL>>","Settings.email.plugin.placeholder.defaultReplyTo":"np: Strapi <<EMAIL>>","Settings.email.plugin.placeholder.testAddress":"np: <EMAIL>","Settings.email.plugin.subTitle":"Sprawdź ustawienia dla testowego maila","Settings.email.plugin.text.configuration":"Ten plugin jest skonfigurowany w pliku {file}, sprawdź {link} do dokumentacji.","Settings.email.plugin.title":"Ustawienia","Settings.email.plugin.title.config":"Ustawienia","Settings.email.plugin.title.test":"Przetestuj dostarczanie maila","SettingsNav.link.settings":"Ustawienia","SettingsNav.section-label":"Plugin e-mail","components.Input.error.validation.email":"Ten email jest niepoprawny."};export{i as default};
