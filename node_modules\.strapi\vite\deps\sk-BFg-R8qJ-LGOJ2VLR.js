import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/sk-BFg-R8qJ.mjs
var groups = "Skupiny";
var models = "Kolekcie";
var pageNotFound = "Stránka nebola nájdená";
var sk = {
  "App.schemas.data-loaded": "Schéma bola úspešne načítaná",
  "ListViewTable.relation-loaded": "Prepojenia boli úspešne načítané",
  "ListViewTable.relation-loading": "Prepojenia sa načítavajú",
  "ListViewTable.relation-more": "Toto prepojenie obsahuje viac záznamov, ako je zobrazených",
  "EditRelations.title": "Relačné dáta",
  "HeaderLayout.button.label-add-entry": "Nový záznam",
  "api.id": "API ID",
  "components.AddFilterCTA.add": "Filtre",
  "components.AddFilterCTA.hide": "Filtre",
  "components.DragHandle-label": "Pretiahnuť",
  "components.DraggableAttr.edit": "Kliknutím upravte",
  "components.DraggableCard.delete.field": "Odstrániť {item}",
  "components.DraggableCard.edit.field": "Upraviť {item}",
  "components.DraggableCard.move.field": "Presunúť {item}",
  "components.ListViewTable.row-line": "riadok {number}",
  "components.DynamicZone.ComponentPicker-label": "Vyberte komponent",
  "components.DynamicZone.add-component": "Pridať komponent do {componentName}",
  "components.DynamicZone.delete-label": "Odstrániť {name}",
  "components.DynamicZone.error-message": "Komponent obsahuje chybu(y)",
  "components.DynamicZone.missing-components": "{number, plural, one {Chýba # komponent} few {Chýbajú # komponenty} other {Chýba # komponentov}}",
  "components.DynamicZone.move-down-label": "Posunúť komponent nižšie",
  "components.DynamicZone.move-up-label": "Posunúť komponent vyššie",
  "components.DynamicZone.pick-compo": "Vyberte jeden komponent",
  "components.DynamicZone.required": "Komponent je povinný",
  "components.EmptyAttributesBlock.button": "Prejsť do nastavení",
  "components.EmptyAttributesBlock.description": "Môžte upravovať nastavenia",
  "components.FieldItem.linkToComponentLayout": "Nastaviť rozloženie komponenty",
  "components.FieldSelect.label": "Pridať políčko",
  "components.FilterOptions.button.apply": "Použiť",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Použiť",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Zmazať všetko",
  "components.FiltersPickWrapper.PluginHeader.description": "Nastaviť podmienky pre filtrovanie záznamov",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filtre",
  "components.FiltersPickWrapper.hide": "Skryť",
  "components.LeftMenu.Search.label": "Vyhľadávať obsahový typ",
  "components.LeftMenu.collection-types": "Collection Types",
  "components.LeftMenu.single-types": "Single Types",
  "components.LimitSelect.itemsPerPage": "Položky na stránku",
  "components.NotAllowedInput.text": "Nemáte oprávnenia na zobrazenie tohto políčka",
  "components.RepeatableComponent.error-message": "Jeden alebo viac komponentov obsahuje chybu(y)",
  "components.Search.placeholder": "Hľadať záznam...",
  "components.Select.draft-info-title": "Stav: Návrh",
  "components.Select.publish-info-title": "Stav: Publikované",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Upravte vzhľad zobrazenia úprav.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Zvoľte nastavenia zobrazenia zoznamu.",
  "components.SettingsViewWrapper.pluginHeader.title": "Nastavenia zobrazenia - {name}",
  "components.TableDelete.delete": "Zmazať všetko",
  "components.TableDelete.deleteSelected": "Odstrániť vyznačené",
  "components.TableDelete.label": "{number, plural, one {# vybraný záznam} few {# vybrané záznamy} other {# vybraných záznamov}}",
  "components.TableEmpty.withFilters": "Nenašiel sa žiaden {contentType} pre dané filtre...",
  "components.TableEmpty.withSearch": "Nenašiel sa žiaden {contentType} spĺňujúci výraz ({search})...",
  "components.TableEmpty.withoutFilter": "Žiadne záznamy",
  "components.empty-repeatable": "Zatiaľ žiadne záznamy. Pridajte nový kliknutím na tlačidlo nižšie.",
  "components.notification.info.maximum-requirement": "Dosiahli ste maximálny počet políčok",
  "components.notification.info.minimum-requirement": "Políčko bolo pridané aby spĺňalo minimálne požiadavky",
  "components.repeatable.reorder.error": "Počas preusporiadavania došlo k chybe, prosím skúste znovu",
  "components.reset-entry": "Zrušiť záznam",
  "components.uid.apply": "použiť",
  "components.uid.available": "dostupné",
  "components.uid.regenerate": "pregenerovať",
  "components.uid.suggested": "odporúčané",
  "components.uid.unavailable": "nedostupné",
  "containers.Edit.Link.Layout": "Upraviť rozloženie",
  "containers.Edit.Link.Model": "Upraviť obrahový typ",
  "containers.Edit.addAnItem": "Pridať položku...",
  "containers.Edit.clickToJump": "Kliknutím zobrazte položku",
  "containers.Edit.delete": "Zmazať",
  "containers.Edit.delete-entry": "Zmazať túto položku",
  "containers.Edit.editing": "Úprava...",
  "containers.Edit.information": "Informácie",
  "containers.Edit.information.by": "Autor",
  "containers.Edit.information.created": "Vytvorené",
  "containers.Edit.information.draftVersion": "verzia návrhu",
  "containers.Edit.information.editing": "Upravuje sa",
  "containers.Edit.information.lastUpdate": "Naposledy upravené",
  "containers.Edit.information.publishedVersion": "publikovaná verzia",
  "containers.Edit.pluginHeader.title.new": "Vytvoriť záznam",
  "containers.Edit.reset": "Zrušiť",
  "containers.Edit.returnList": "Návrat do zoznamu",
  "containers.Edit.seeDetails": "Detaily",
  "containers.Edit.submit": "Uložiť",
  "containers.EditSettingsView.modal-form.edit-field": "Upraviť políčko",
  "containers.EditView.add.new-entry": "Pridať záznam",
  "containers.EditView.notification.errors": "Formulár obsahuje chyby",
  "containers.Home.introduction": "Pre úpravu záznamov kliknite na konkrétny link v ľavom menu.",
  "containers.Home.pluginHeaderDescription": "Spravujte obsah cez robustné a intuitívne rozhranie.",
  "containers.Home.pluginHeaderTitle": "Správca obsahu",
  "containers.List.draft": "Návrh",
  "containers.List.errorFetchRecords": "Chyba",
  "containers.List.published": "Publikované",
  "containers.list.displayedFields": "Zobrazené políčka",
  "containers.list.items": "{number, plural, =0 {položiek} one {položka} few {položky} other {položiek}}",
  "containers.list.table-headers.publishedAt": "Stav",
  "containers.ListSettingsView.modal-form.edit-label": "Upraviť popis",
  "containers.SettingPage.add.field": "Vložiť ďalšie políčko",
  "containers.SettingPage.attributes": "Atribúty políčok",
  "containers.SettingPage.attributes.description": "Nastavte poradie atribútov",
  "containers.SettingPage.editSettings.description": "Potiahnutím nastavte rozloženie políčok",
  "containers.SettingPage.editSettings.entry.title": "Názov záznamu",
  "containers.SettingPage.editSettings.entry.title.description": "Nastavte zobrazené políčko záznamu",
  "containers.SettingPage.editSettings.relation-field.description": "Nastaviť pole na zobrazenie v oboch zobrazeniach (zoznam, upravenie)",
  "containers.SettingPage.editSettings.title": "Upraviť zobrazenie (nastavenia)",
  "containers.SettingPage.layout": "Rozloženie",
  "containers.SettingPage.listSettings.description": "Konfigurovať možnosti pre tento typ kolekcie",
  "containers.SettingPage.listSettings.title": "Zobrazenie zoznamu (nastavenia)",
  "containers.SettingPage.pluginHeaderDescription": "Konfigurovať špecifické nastavenia pre tento typ kolekcie",
  "containers.SettingPage.settings": "Nastavenia",
  "containers.SettingPage.view": "Vzhľad",
  "containers.SettingViewModel.pluginHeader.title": "Správca obsahu - {name}",
  "containers.SettingsPage.Block.contentType.description": "Nastavte špecifické vlastnosti",
  "containers.SettingsPage.Block.contentType.title": "Kolekcie",
  "containers.SettingsPage.Block.generalSettings.description": "Konfigurovať predvolené možnosti pre Vaše kolekcie",
  "containers.SettingsPage.Block.generalSettings.title": "Všeobecné",
  "containers.SettingsPage.pluginHeaderDescription": "Konfigurovať nastavenia pre všetky Vaše kolekcie a skupiny",
  "containers.SettingsView.list.subtitle": "Konfigurovať rozloženie a zobrazenie stránky Vaších kolekcií a skupín",
  "containers.SettingsView.list.title": "Zobraziť nastavenia",
  "edit-settings-view.link-to-ctb.components": "Upraviť komponent",
  "edit-settings-view.link-to-ctb.content-types": "Upraviť obsahový typ",
  "emptyAttributes.button": "Ísť do tvorcu kolekcií",
  "emptyAttributes.description": "Pridať prvé políčko do Vašej kolekcie",
  "emptyAttributes.title": "Zatiaľ tu nie sú žiadne políčka",
  "error.attribute.key.taken": "Táto hodnota už existuje",
  "error.attribute.sameKeyAndName": "Hodnoty nesmú byť rovnaké",
  "error.attribute.taken": "Toto políčko už existuje",
  "error.contentTypeName.taken": "Toto meno už existuje",
  "error.model.fetch": "Nastala chyba pri načitávaní nastavení modelov.",
  "error.record.create": "Nastala chyba pri vytváraní záznamu.",
  "error.record.delete": "Nastala chyba pri vymazávaní záznamu.",
  "error.record.fetch": "Nastala chyba pri načitávaní záznamu.",
  "error.record.update": "Nastala chyba pri upravovaní záznamu.",
  "error.records.count": "Nastala chyba pri načitávaní počtu záznamov.",
  "error.records.fetch": "Nastala chyba pri načitávaní záznamov.",
  "error.schema.generation": "Nastala chyba pri vytváraní návrhu.",
  "error.validation.json": "Toto nie je JSON formát",
  "error.validation.max": "Táto hodnota je príliš vysoká.",
  "error.validation.maxLength": "Táto hodnota je príliš dlhá.",
  "error.validation.min": "Táto hodnota je príliš nízka.",
  "error.validation.minLength": "Táto hodnota je príliš krátka.",
  "error.validation.minSupMax": "Nemôže byť nadriadený",
  "error.validation.regex": "Táto hodnota nespĺňa požadovaný vzor",
  "error.validation.required": "Táto hodnota je povinná.",
  "form.Input.bulkActions": "Povoliť hromadné akcie",
  "form.Input.defaultSort": "Predvolený atribút pre zoradenie",
  "form.Input.description": "Popis políčka",
  "form.Input.description.placeholder": "Zobraziť meno v profile",
  "form.Input.editable": "Editovateľné políčko",
  "form.Input.filters": "Povoliť filtre",
  "form.Input.label": "Názov",
  "form.Input.label.inputDescription": "Táto hodnota prepisuje popis zobrazený v hlavičke tabuľky",
  "form.Input.pageEntries": "Záznamy na stránku",
  "form.Input.pageEntries.inputDescription": "Poznámka: Túto hodnotu môžete prepísať na stránke nastavení typu kolekcie.",
  "form.Input.placeholder": "Placeholder",
  "form.Input.placeholder.placeholder": "Hodnota",
  "form.Input.search": "Povoliť vyhľadávanie",
  "form.Input.search.field": "Povoliť vyhľadávanie na tomto políčku",
  "form.Input.sort.field": "Povoliť zoradenie na tomto políčku",
  "form.Input.sort.order": "Predvolené zoradenie",
  "form.Input.wysiwyg": "Zobraziť ako WYSIWYG",
  "global.displayedFields": "Zobrazené políčka",
  groups,
  "groups.numbered": "Skupiny ({number})",
  "header.name": "Obsah",
  "link-to-ctb": "Upraviť model",
  models,
  "models.numbered": "Kolekcie ({number})",
  "notification.error.displayedFields": "Aspoň jedno políčko musí byť zobrazené",
  "notification.error.relationship.fetch": "Nastala chyba pri načitávaní vzťahu.",
  "notification.info.SettingPage.disableSort": "Aspoň jedno políčko musí mať nastavené zoradenie",
  "notification.info.minimumFields": "Aspoň jedno políčko musí byť zobrazené",
  "notification.upload.error": "Nastala chyba pri nahrávaní súborov",
  pageNotFound,
  "pages.ListView.header-subtitle": "{number, plural, =0 {# nájdených záznamov} one {# nájdený záznam} few {# nájdené záznamy} other {# nájdených záznamov}}",
  "pages.NoContentType.button": "Vytvore svoj prvý obsahový typ",
  "pages.NoContentType.text": "Nemáte vytvorené žiadne obsahové type. Vytvorte svoj prvý obsahový typ.",
  "permissions.not-allowed.create": "Nemáte oprávnenia na vytvorenie dokumentu",
  "permissions.not-allowed.update": "Nemáte oprávnenia na čítanie dokumentu",
  "plugin.description.long": "Jednoduchý spôsob zobrazenia a úpravy dát v databáze.",
  "plugin.description.short": "Jednoduchý spôsob zobrazenia a úpravy dát v databáze.",
  "popover.display-relations.label": "Zobraziť prepojenia",
  "select.currently.selected": "{count} aktuálne vybrané",
  "success.record.delete": "Zmazané",
  "success.record.publish": "Publikované",
  "success.record.save": "Uložené",
  "success.record.unpublish": "Nepublikované",
  "utils.data-loaded": "{number, plural, =1 {Záznam bol úspešne načítaný} other {Záznamy boli úspešne načítané}}",
  "apiError.This attribute must be unique": "Pole {field} musí byť jedinečné",
  "popUpWarning.warning.has-draft-relations.title": "Potvrdenie",
  "popUpWarning.warning.publish-question": "Stále si prajete publikovať?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Áno, publikovať",
  "popUpwarning.warning.has-draft-relations.message": "<b>{count, plural, one { prepojenie nie je publikované } few { prepojenia nie sú publikované } other { prepojení nie je publikovaných } }</b>, čo môže viesť k neočakávanému správaniu."
};
export {
  sk as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=sk-BFg-R8qJ-LGOJ2VLR.js.map
