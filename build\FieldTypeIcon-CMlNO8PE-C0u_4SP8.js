import{c9 as j,m as s,f5 as F,f6 as c,f7 as m,f8 as l,f9 as w,fa as e,fb as o,fc as R,fd as $,fe as u,ff as r,fg as p,fh as g,fi as h,J as b}from"./strapi-YzJfjJ2z.js";const x={biginteger:s.jsx(r,{}),boolean:s.jsx(h,{}),date:s.jsx(e,{}),datetime:s.jsx(e,{}),decimal:s.jsx(r,{}),email:s.jsx(g,{}),enumeration:s.jsx(p,{}),float:s.jsx(r,{}),integer:s.jsx(r,{}),media:s.jsx(u,{}),password:s.jsx($,{}),relation:s.jsx(R,{}),string:s.jsx(o,{}),text:s.jsx(o,{}),richtext:s.jsx(o,{}),time:s.jsx(e,{}),timestamp:s.jsx(e,{}),json:s.jsx(w,{}),uid:s.jsx(l,{}),component:s.jsx(m,{}),dynamiczone:s.jsx(c,{}),blocks:s.jsx(F,{})},C=({type:a,customFieldUid:t})=>{const d=j("FieldTypeIcon",i=>i.customFields.get);if(!a)return null;let f=x[a];if(t){const n=d(t)?.icon;n&&(f=s.jsx(b,{marginRight:3,width:7,height:6,children:s.jsx(n,{})}))}return x[a]?f:null};export{C as F};
