import {
  useIntl
} from "./chunk-CH6LMMF3.js";
import "./chunk-RPX6VIML.js";
import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$H
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  EmptyStateLayout,
  LinkButton,
  Main
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$3t
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/review-workflows/dist/_chunks/purchase-review-workflows-BN-5Ube7.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PurchaseReviewWorkflows = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Layouts.Root, { children: (0, import_jsx_runtime.jsxs)(Main, { children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: "Settings.review-workflows.list.page.title",
          defaultMessage: "Review Workflows"
        }),
        subtitle: formatMessage({
          id: "Settings.review-workflows.list.page.subtitle",
          defaultMessage: "Manage your content review process"
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 10, paddingRight: 10, children: (0, import_jsx_runtime.jsx)(
      EmptyStateLayout,
      {
        icon: (0, import_jsx_runtime.jsx)(ForwardRef$H, { width: "16rem" }),
        content: formatMessage({
          id: "Settings.review-workflows.not-available",
          defaultMessage: "Review Workflows is only available as part of a paid plan. Upgrade to create and manage workflows."
        }),
        action: (0, import_jsx_runtime.jsx)(
          LinkButton,
          {
            variant: "default",
            endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3t, {}),
            href: "https://strp.cc/3tdNfJq",
            isExternal: true,
            target: "_blank",
            children: formatMessage({
              id: "global.learn-more",
              defaultMessage: "Learn more"
            })
          }
        )
      }
    ) })
  ] }) });
};
export {
  PurchaseReviewWorkflows
};
//# sourceMappingURL=purchase-review-workflows-BN-5Ube7-UPPEFLHM.js.map
