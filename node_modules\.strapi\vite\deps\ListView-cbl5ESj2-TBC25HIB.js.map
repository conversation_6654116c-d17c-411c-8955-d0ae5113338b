{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/TransferTokens/ListView.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { Data } from '@strapi/types';\nimport * as qs from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Link, useNavigate } from 'react-router-dom';\n\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useTracking } from '../../../../features/Tracking';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useOnce } from '../../../../hooks/useOnce';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport {\n  useDeleteTransferTokenMutation,\n  useGetTransferTokensQuery,\n} from '../../../../services/transferTokens';\nimport { TRANSFER_TOKEN_TYPE } from '../../components/Tokens/constants';\nimport { Table } from '../../components/Tokens/Table';\n\nconst tableHeaders = [\n  {\n    name: 'name',\n    label: {\n      id: 'Settings.tokens.ListView.headers.name',\n      defaultMessage: 'Name',\n    },\n    sortable: true,\n  },\n  {\n    name: 'description',\n    label: {\n      id: 'Settings.tokens.ListView.headers.description',\n      defaultMessage: 'Description',\n    },\n    sortable: false,\n  },\n  {\n    name: 'createdAt',\n    label: {\n      id: 'Settings.tokens.ListView.headers.createdAt',\n      defaultMessage: 'Created at',\n    },\n    sortable: false,\n  },\n  {\n    name: 'lastUsedAt',\n    label: {\n      id: 'Settings.tokens.ListView.headers.lastUsedAt',\n      defaultMessage: 'Last used',\n    },\n    sortable: false,\n  },\n] as const;\n\n/* -------------------------------------------------------------------------------------------------\n * ListView\n * -----------------------------------------------------------------------------------------------*/\n\nconst ListView = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['transfer-tokens']\n  );\n  const {\n    isLoading: isLoadingRBAC,\n    allowedActions: { canCreate, canDelete, canUpdate, canRead },\n  } = useRBAC(permissions);\n  const navigate = useNavigate();\n  const { trackUsage } = useTracking();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  React.useEffect(() => {\n    navigate({ search: qs.stringify({ sort: 'name:ASC' }, { encode: false }) });\n  }, [navigate]);\n\n  useOnce(() => {\n    trackUsage('willAccessTokenList', {\n      tokenType: TRANSFER_TOKEN_TYPE,\n    });\n  });\n\n  const headers = tableHeaders.map((header) => ({\n    ...header,\n    label: formatMessage(header.label),\n  }));\n\n  const {\n    data: transferTokens = [],\n    isLoading: isLoadingTokens,\n    error,\n  } = useGetTransferTokensQuery(undefined, {\n    skip: !canRead,\n  });\n\n  React.useEffect(() => {\n    if (transferTokens) {\n      trackUsage('didAccessTokenList', {\n        number: transferTokens.length,\n        tokenType: TRANSFER_TOKEN_TYPE,\n      });\n    }\n  }, [trackUsage, transferTokens]);\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  const [deleteToken] = useDeleteTransferTokenMutation();\n\n  const handleDelete = async (id: Data.ID) => {\n    try {\n      const res = await deleteToken(id);\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n      });\n    }\n  };\n\n  const isLoading = isLoadingTokens || isLoadingRBAC;\n\n  return (\n    <>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Transfer Tokens',\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        title={formatMessage({\n          id: 'Settings.transferTokens.title',\n          defaultMessage: 'Transfer Tokens',\n        })}\n        subtitle={formatMessage({\n          id: 'Settings.transferTokens.description',\n          defaultMessage: '\"List of generated transfer tokens\"', // TODO change this message\n        })}\n        primaryAction={\n          canCreate ? (\n            <LinkButton\n              role=\"button\"\n              tag={Link}\n              data-testid=\"create-transfer-token-button\"\n              startIcon={<Plus />}\n              size=\"S\"\n              onClick={() =>\n                trackUsage('willAddTokenFromList', {\n                  tokenType: TRANSFER_TOKEN_TYPE,\n                })\n              }\n              to=\"/settings/transfer-tokens/create\"\n            >\n              {formatMessage({\n                id: 'Settings.transferTokens.create',\n                defaultMessage: 'Create new Transfer Token',\n              })}\n            </LinkButton>\n          ) : undefined\n        }\n      />\n      {!canRead ? (\n        <Page.NoPermissions />\n      ) : (\n        <Page.Main aria-busy={isLoading}>\n          <Layouts.Content>\n            {transferTokens.length > 0 && (\n              <Table\n                permissions={{ canRead, canDelete, canUpdate }}\n                headers={headers}\n                isLoading={isLoading}\n                onConfirmDelete={handleDelete}\n                tokens={transferTokens}\n                tokenType={TRANSFER_TOKEN_TYPE}\n              />\n            )}\n            {canCreate && transferTokens.length === 0 ? (\n              <EmptyStateLayout\n                action={\n                  <LinkButton\n                    tag={Link}\n                    variant=\"secondary\"\n                    startIcon={<Plus />}\n                    to=\"/settings/transfer-tokens/create\"\n                  >\n                    {formatMessage({\n                      id: 'Settings.transferTokens.addNewToken',\n                      defaultMessage: 'Add new Transfer Token',\n                    })}\n                  </LinkButton>\n                }\n                icon={<EmptyDocuments width=\"16rem\" />}\n                content={formatMessage({\n                  id: 'Settings.transferTokens.addFirstToken',\n                  defaultMessage: 'Add your first Transfer Token',\n                })}\n              />\n            ) : null}\n            {!canCreate && transferTokens.length === 0 ? (\n              <EmptyStateLayout\n                icon={<EmptyDocuments width=\"16rem\" />}\n                content={formatMessage({\n                  id: 'Settings.transferTokens.emptyStateLayout',\n                  defaultMessage: 'You don’t have any content yet...',\n                })}\n              />\n            ) : null}\n          </Layouts.Content>\n        </Page.Main>\n      )}\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedListView\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedListView = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['transfer-tokens'].main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListView />\n    </Page.Protect>\n  );\n};\n\nexport { ListView, ProtectedListView };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAyBA,IAAM,eAAe;EACnB;IACE,MAAM;IACN,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,UAAU;EAAA;EAEZ;IACE,MAAM;IACN,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,UAAU;EAAA;EAEZ;IACE,MAAM;IACN,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,UAAU;EAAA;EAEZ;IACE,MAAM;IACN,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,UAAU;EAAA;AAEd;AAMA,IAAM,WAAW,MAAM;AACf,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,cAAc;IAClB,CAAC,UAAU;;AAAA,yBAAM,UAAU,YAAY,aAA5B,mBAAuC;;EAAiB;AAE/D,QAAA;IACJ,WAAW;IACX,gBAAgB,EAAE,WAAW,WAAW,WAAW,QAAQ;EAAA,IACzD,QAAQ,WAAW;AACvB,QAAM,WAAW,YAAY;AACvB,QAAA,EAAE,WAAW,IAAI,YAAY;AACnC,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AAEvE,EAAM,gBAAU,MAAM;AACpB,aAAS,EAAE,QAAW,aAAU,EAAE,MAAM,WAAW,GAAG,EAAE,QAAQ,MAAM,CAAC,EAAA,CAAG;EAAA,GACzE,CAAC,QAAQ,CAAC;AAEb,UAAQ,MAAM;AACZ,eAAW,uBAAuB;MAChC,WAAW;IAAA,CACZ;EAAA,CACF;AAED,QAAM,UAAU,aAAa,IAAI,CAAC,YAAY;IAC5C,GAAG;IACH,OAAO,cAAc,OAAO,KAAK;EAAA,EACjC;AAEI,QAAA;IACJ,MAAM,iBAAiB,CAAA;IACvB,WAAW;IACX;EAAA,IACE,0BAA0B,QAAW;IACvC,MAAM,CAAC;EAAA,CACR;AAED,EAAM,gBAAU,MAAM;AACpB,QAAI,gBAAgB;AAClB,iBAAW,sBAAsB;QAC/B,QAAQ,eAAe;QACvB,WAAW;MAAA,CACZ;IAAA;EACH,GACC,CAAC,YAAY,cAAc,CAAC;AAE/B,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACU,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IAAA;EACH,GACC,CAAC,OAAO,gBAAgB,kBAAkB,CAAC;AAExC,QAAA,CAAC,WAAW,IAAI,+BAA+B;AAE/C,QAAA,eAAe,OAAO,OAAgB;AACtC,QAAA;AACI,YAAA,MAAM,MAAM,YAAY,EAAE;AAEhC,UAAI,WAAW,KAAK;AACC,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,IAAI,KAAK;QAAA,CAClC;MAAA;IACH,QACM;AACa,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,sBAAsB,gBAAgB,mBAAA,CAAoB;MAAA,CACxF;IAAA;EACH;AAGF,QAAM,YAAY,mBAAmB;AAErC,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM;MAAA;IACR,EAEJ,CAAA;QACA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;;QAAA,CACjB;QACD,eACE,gBACE;UAAC;UAAA;YACC,MAAK;YACL,KAAK;YACL,eAAY;YACZ,eAAA,wBAAY,eAAK,CAAA,CAAA;YACjB,MAAK;YACL,SAAS,MACP,WAAW,wBAAwB;cACjC,WAAW;YAAA,CACZ;YAEH,IAAG;YAEF,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QAAA,IAED;MAAA;IAAA;IAGP,CAAC,cACA,wBAAC,KAAK,eAAL,CAAA,CAAmB,QAEpB,wBAAC,KAAK,MAAL,EAAU,aAAW,WACpB,cAAC,yBAAA,QAAQ,SAAR,EACE,UAAA;MAAA,eAAe,SAAS,SACvB;QAAC;QAAA;UACC,aAAa,EAAE,SAAS,WAAW,UAAU;UAC7C;UACA;UACA,iBAAiB;UACjB,QAAQ;UACR,WAAW;QAAA;MAAA;MAGd,aAAa,eAAe,WAAW,QACtC;QAAC;QAAA;UACC,YACE;YAAC;YAAA;cACC,KAAK;cACL,SAAQ;cACR,eAAA,wBAAY,eAAK,CAAA,CAAA;cACjB,IAAG;cAEF,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA;UAAA;UAGL,UAAM,wBAAC,cAAe,EAAA,OAAM,QAAQ,CAAA;UACpC,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA;MAAA,IAED;MACH,CAAC,aAAa,eAAe,WAAW,QACvC;QAAC;QAAA;UACC,UAAM,wBAAC,cAAe,EAAA,OAAM,QAAQ,CAAA;UACpC,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA;MAAA,IAED;IAAA,EAAA,CACN,EACF,CAAA;EAAA,EAEJ,CAAA;AAEJ;AAMA,IAAM,oBAAoB,MAAM;AAC9B,QAAM,cAAc;IAClB,CAAC,UAAU;;AAAA,yBAAM,UAAU,YAAY,aAA5B,mBAAuC,mBAAmB;;EAAA;AAGvE,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,UAAA,CAAA,CAAS,EACZ,CAAA;AAEJ;", "names": []}