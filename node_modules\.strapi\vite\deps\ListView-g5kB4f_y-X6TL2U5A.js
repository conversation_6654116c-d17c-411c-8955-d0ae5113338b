import {
  Attribute<PERSON>con,
  COMPONENT_ICONS,
  getTrad,
  use<PERSON><PERSON><PERSON><PERSON><PERSON>,
  useFormModalNavigation
} from "./chunk-WWQEULCJ.js";
import "./chunk-S5DFQGWM.js";
import "./chunk-RBFGK6LJ.js";
import "./chunk-U7XWYUVC.js";
import {
  useIntl
} from "./chunk-IZBVLW72.js";
import "./chunk-M5EKOYPZ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-O6QFUROF.js";
import "./chunk-5KZDF3JA.js";
import "./chunk-HYNTGZ4P.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-VUQGR7S3.js";
import "./chunk-MMOBCIZG.js";
import {
  BackButton,
  Layouts,
  require_has,
  require_upperFirst
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$J,
  require_isEqual,
  useRBAC,
  useTracking
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Button,
  EmptyStateLayout,
  Flex,
  IconButton,
  TFooter,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Typography,
  require_get
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  useMatch,
  useNavigate,
  usePrompt
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1d,
  ForwardRef$1r,
  ForwardRef$2d,
  ForwardRef$2h,
  ForwardRef$3V,
  ForwardRef$4p,
  ForwardRef$j
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-type-builder/dist/_chunks/ListView-g5kB4f_y.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_get = __toESM(require_get(), 1);
var import_has = __toESM(require_has(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);
var import_upperFirst = __toESM(require_upperFirst(), 1);
var import_react = __toESM(require_react(), 1);
var BoxWrapper$1 = dt(Box)`
  table {
    width: 100%;
    white-space: nowrap;
  }

  thead {
    border-bottom: 1px solid ${({ theme }) => theme.colors.neutral150};

    tr {
      border-top: 0;
    }
  }

  tr {
    border-top: 1px solid ${({ theme }) => theme.colors.neutral150};

    & td,
    & th {
      padding: ${({ theme }) => theme.spaces[4]};
    }

    & td:first-of-type,
    & th:first-of-type {
      padding: 0 ${({ theme }) => theme.spaces[1]};
    }
  }

  th,
  td {
    vertical-align: middle;
    text-align: left;
    color: ${({ theme }) => theme.colors.neutral600};
    outline-offset: -4px;
  }
`;
var Tr2 = dt.tr`
  &.component-row,
  &.dynamiczone-row {
    position: relative;
    border-top: none !important;

    table tr:first-child {
      border-top: none;
    }

    > td:first-of-type {
      padding: 0 0 0 2rem;
      position: relative;

      &::before {
        content: '';
        width: 0.4rem;
        height: calc(100% - 40px);
        position: absolute;
        top: -7px;
        left: 2.6rem;
        border-radius: 4px;

        ${({ $isFromDynamicZone, $isChildOfDynamicZone, theme }) => {
  if ($isChildOfDynamicZone) {
    return `background-color: ${theme.colors.primary200};`;
  }
  if ($isFromDynamicZone) {
    return `background-color: ${theme.colors.primary200};`;
  }
  return `background: ${theme.colors.neutral150};`;
}}
      }
    }
  }

  &.dynamiczone-row > td:first-of-type {
    padding: 0;
  }
`;
var ComponentList = ({
  customRowComponent,
  component,
  isFromDynamicZone = false,
  isNestedInDZComponent = false,
  firstLoopComponentUid
}) => {
  const { modifiedData } = useDataManager();
  const {
    schema: { attributes }
  } = (0, import_get.default)(modifiedData, ["components", component], {
    schema: { attributes: [] }
  });
  return (0, import_jsx_runtime.jsx)(Tr2, { $isChildOfDynamicZone: isFromDynamicZone, className: "component-row", children: (0, import_jsx_runtime.jsx)("td", { colSpan: 12, children: (0, import_jsx_runtime.jsx)(
    List,
    {
      customRowComponent,
      items: attributes,
      targetUid: component,
      firstLoopComponentUid: firstLoopComponentUid || component,
      editTarget: "components",
      isFromDynamicZone,
      isNestedInDZComponent,
      isSub: true,
      secondLoopComponentUid: firstLoopComponentUid ? component : null
    }
  ) }) });
};
var ComponentIcon = ({ isActive = false, icon = "dashboard" }) => {
  const Icon = COMPONENT_ICONS[icon] || COMPONENT_ICONS.dashboard;
  return (0, import_jsx_runtime.jsx)(
    Flex,
    {
      alignItems: "center",
      background: isActive ? "primary200" : "neutral200",
      justifyContent: "center",
      height: 8,
      width: 8,
      borderRadius: "50%",
      children: (0, import_jsx_runtime.jsx)(Icon, { height: "2rem", width: "2rem" })
    }
  );
};
var CloseButton = dt(Box)`
  position: absolute;
  display: none;
  top: 5px;
  right: 0.8rem;

  svg {
    width: 1rem;
    height: 1rem;

    path {
      fill: ${({ theme }) => theme.colors.primary600};
    }
  }
`;
var ComponentBox = dt(Flex)`
  width: 14rem;
  height: 8rem;
  position: relative;
  border: 1px solid ${({ theme }) => theme.colors.neutral200};
  background: ${({ theme }) => theme.colors.neutral100};
  border-radius: ${({ theme }) => theme.borderRadius};
  max-width: 100%;

  &.active,
  &:focus,
  &:hover {
    border: 1px solid ${({ theme }) => theme.colors.primary200};
    background: ${({ theme }) => theme.colors.primary100};
    color: ${({ theme }) => theme.colors.primary600};

    ${CloseButton} {
      display: block;
    }

    /* > ComponentIcon */
    > div:first-child {
      background: ${({ theme }) => theme.colors.primary200};
      color: ${({ theme }) => theme.colors.primary600};

      svg {
        path {
          fill: ${({ theme }) => theme.colors.primary600};
        }
      }
    }
  }
`;
var ComponentCard = ({
  component,
  dzName,
  index,
  isActive = false,
  isInDevelopmentMode = false,
  onClick
}) => {
  const { modifiedData, removeComponentFromDynamicZone } = useDataManager();
  const {
    schema: { icon, displayName }
  } = (0, import_get.default)(modifiedData, ["components", component], { schema: {} });
  const onClose = (e) => {
    e.stopPropagation();
    removeComponentFromDynamicZone(dzName, index);
  };
  return (0, import_jsx_runtime.jsxs)(
    ComponentBox,
    {
      alignItems: "center",
      direction: "column",
      className: isActive ? "active" : "",
      borderRadius: "borderRadius",
      justifyContent: "center",
      paddingLeft: 4,
      paddingRight: 4,
      shrink: 0,
      onClick,
      role: "tab",
      tabIndex: isActive ? 0 : -1,
      cursor: "pointer",
      "aria-selected": isActive,
      "aria-controls": `dz-${dzName}-panel-${index}`,
      id: `dz-${dzName}-tab-${index}`,
      children: [
        (0, import_jsx_runtime.jsx)(ComponentIcon, { icon, isActive }),
        (0, import_jsx_runtime.jsx)(Box, { marginTop: 1, maxWidth: "100%", children: (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", fontWeight: "bold", ellipsis: true, children: displayName }) }),
        isInDevelopmentMode && (0, import_jsx_runtime.jsx)(CloseButton, { tag: "button", onClick: onClose, children: (0, import_jsx_runtime.jsx)(ForwardRef$3V, {}) })
      ]
    }
  );
};
var StyledAddIcon = dt(ForwardRef$1d)`
  width: 3.2rem;
  height: 3.2rem;
  padding: 0.9rem;
  border-radius: 6.4rem;
  background: ${({ theme }) => theme.colors.primary100};
  path {
    fill: ${({ theme }) => theme.colors.primary600};
  }
`;
var FixedBox = dt(Box)`
  height: 9rem;
  position: absolute;
  width: 100%;
  top: 0;
  left: 0;
`;
var ScrollableStack = dt(Flex)`
  width: 100%;
  overflow-x: auto;
`;
var ComponentContentBox = dt(Box)`
  padding-top: 9rem;
`;
var ComponentStack = dt(Flex)`
  flex-shrink: 0;
  width: 14rem;
  height: 8rem;
  justify-content: center;
  align-items: center;
`;
var DynamicZoneList = ({
  customRowComponent,
  components = [],
  addComponent,
  name,
  targetUid
}) => {
  const { isInDevelopmentMode } = useDataManager();
  const [activeTab, setActiveTab] = (0, import_react.useState)(0);
  const { formatMessage } = useIntl();
  const toggle = (tab) => {
    if (activeTab !== tab) {
      setActiveTab(tab);
    }
  };
  const handleClickAdd = () => {
    addComponent(name);
  };
  return (0, import_jsx_runtime.jsx)(Tr2, { className: "dynamiczone-row", $isFromDynamicZone: true, children: (0, import_jsx_runtime.jsxs)("td", { colSpan: 12, children: [
    (0, import_jsx_runtime.jsx)(FixedBox, { paddingLeft: 8, children: (0, import_jsx_runtime.jsxs)(ScrollableStack, { gap: 2, children: [
      isInDevelopmentMode && (0, import_jsx_runtime.jsx)("button", { type: "button", onClick: handleClickAdd, children: (0, import_jsx_runtime.jsxs)(ComponentStack, { direction: "column", alignItems: "stretch", gap: 1, children: [
        (0, import_jsx_runtime.jsx)(StyledAddIcon, {}),
        (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", fontWeight: "bold", textColor: "primary600", children: formatMessage({
          id: getTrad("button.component.add"),
          defaultMessage: "Add a component"
        }) })
      ] }) }),
      (0, import_jsx_runtime.jsx)(Flex, { role: "tablist", gap: 2, children: components.map((component, index) => {
        return (0, import_jsx_runtime.jsx)(
          ComponentCard,
          {
            dzName: name || "",
            index,
            component,
            isActive: activeTab === index,
            isInDevelopmentMode,
            onClick: () => toggle(index)
          },
          component
        );
      }) })
    ] }) }),
    (0, import_jsx_runtime.jsx)(ComponentContentBox, { children: components.map((component, index) => {
      const props = {
        customRowComponent,
        component
      };
      return (0, import_jsx_runtime.jsx)(
        Box,
        {
          id: `dz-${name}-panel-${index}`,
          role: "tabpanel",
          "aria-labelledby": `dz-${name}-tab-${index}`,
          style: { display: activeTab === index ? "block" : "none" },
          children: (0, import_jsx_runtime.jsx)("table", { children: (0, import_jsx_runtime.jsx)("tbody", { children: (0, import_react.createElement)(
            ComponentList,
            {
              ...props,
              isFromDynamicZone: true,
              component: targetUid,
              key: component
            }
          ) }) })
        },
        component
      );
    }) })
  ] }) });
};
var IconBox = dt(Box)`
  height: 2.4rem;
  width: 2.4rem;
  border-radius: 50%;
  display: flex;
  justify-content: center;
  align-items: center;

  svg {
    height: 1rem;
    width: 1rem;
  }

  svg path {
    fill: ${({ theme, color }) => theme.colors[`${color}600`]};
  }
`;
var ButtonBox = dt(Box)`
  border-radius: 0 0 ${({ theme }) => theme.borderRadius} ${({ theme }) => theme.borderRadius};
  display: block;
  width: 100%;
  border: none;
  position: relative;
  left: -0.4rem;
`;
var NestedTFooter = ({ children, icon, color, ...props }) => {
  return (0, import_jsx_runtime.jsx)(ButtonBox, { paddingBottom: 4, paddingTop: 4, tag: "button", type: "button", ...props, children: (0, import_jsx_runtime.jsxs)(Flex, { children: [
    (0, import_jsx_runtime.jsx)(IconBox, { color, "aria-hidden": true, background: `${color}200`, children: icon }),
    (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 3, children: (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", fontWeight: "bold", textColor: `${color}600`, children }) })
  ] }) });
};
var List = ({
  addComponentToDZ,
  customRowComponent,
  editTarget,
  firstLoopComponentUid,
  isFromDynamicZone = false,
  isMain = false,
  isNestedInDZComponent = false,
  isSub = false,
  items = [],
  secondLoopComponentUid,
  targetUid
}) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { isInDevelopmentMode, modifiedData, isInContentTypeView } = useDataManager();
  const { onOpenModalAddField } = useFormModalNavigation();
  const onClickAddField = () => {
    trackUsage("hasClickedCTBAddFieldBanner");
    onOpenModalAddField({ forTarget: editTarget, targetUid });
  };
  if (!targetUid) {
    return (0, import_jsx_runtime.jsxs)(Table, { colCount: 2, rowCount: 2, children: [
      (0, import_jsx_runtime.jsx)(Thead, { children: (0, import_jsx_runtime.jsxs)(Tr, { children: [
        (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({ id: "global.name", defaultMessage: "Name" }) }) }),
        (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({ id: "global.type", defaultMessage: "Type" }) }) })
      ] }) }),
      (0, import_jsx_runtime.jsx)(Tbody, { children: (0, import_jsx_runtime.jsx)(Tr, { children: (0, import_jsx_runtime.jsx)(Td, { colSpan: 2, children: (0, import_jsx_runtime.jsx)(
        EmptyStateLayout,
        {
          content: formatMessage({
            id: getTrad("table.content.create-first-content-type"),
            defaultMessage: "Create your first Collection-Type"
          }),
          hasRadius: true,
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: "16rem" })
        }
      ) }) }) })
    ] });
  }
  if (items.length === 0 && isMain) {
    return (0, import_jsx_runtime.jsxs)(Table, { colCount: 2, rowCount: 2, children: [
      (0, import_jsx_runtime.jsx)(Thead, { children: (0, import_jsx_runtime.jsxs)(Tr, { children: [
        (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({ id: "global.name", defaultMessage: "Name" }) }) }),
        (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({ id: "global.type", defaultMessage: "Type" }) }) })
      ] }) }),
      (0, import_jsx_runtime.jsx)(Tbody, { children: (0, import_jsx_runtime.jsx)(Tr, { children: (0, import_jsx_runtime.jsx)(Td, { colSpan: 2, children: (0, import_jsx_runtime.jsx)(
        EmptyStateLayout,
        {
          action: (0, import_jsx_runtime.jsx)(
            Button,
            {
              onClick: onClickAddField,
              size: "L",
              startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
              variant: "secondary",
              children: formatMessage({
                id: getTrad("table.button.no-fields"),
                defaultMessage: "Add new field"
              })
            }
          ),
          content: formatMessage(
            isInContentTypeView ? {
              id: getTrad("table.content.no-fields.collection-type"),
              defaultMessage: "Add your first field to this Collection-Type"
            } : {
              id: getTrad("table.content.no-fields.component"),
              defaultMessage: "Add your first field to this component"
            }
          ),
          hasRadius: true,
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: "16rem" })
        }
      ) }) }) })
    ] });
  }
  return (0, import_jsx_runtime.jsxs)(BoxWrapper$1, { children: [
    (0, import_jsx_runtime.jsx)(
      Box,
      {
        paddingLeft: 6,
        paddingRight: isMain ? 6 : 0,
        ...isMain && { style: { overflowX: "auto" } },
        children: (0, import_jsx_runtime.jsxs)("table", { children: [
          isMain && (0, import_jsx_runtime.jsx)("thead", { children: (0, import_jsx_runtime.jsxs)("tr", { children: [
            (0, import_jsx_runtime.jsx)("th", { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral800", children: formatMessage({ id: "global.name", defaultMessage: "Name" }) }) }),
            (0, import_jsx_runtime.jsx)("th", { colSpan: 2, children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral800", children: formatMessage({ id: "global.type", defaultMessage: "Type" }) }) })
          ] }) }),
          (0, import_jsx_runtime.jsx)("tbody", { children: items.map((item) => {
            const { type } = item;
            const CustomRow = customRowComponent;
            return (0, import_jsx_runtime.jsxs)(import_react.Fragment, { children: [
              (0, import_jsx_runtime.jsx)(
                CustomRow,
                {
                  ...item,
                  isNestedInDZComponent,
                  targetUid,
                  editTarget,
                  firstLoopComponentUid,
                  isFromDynamicZone,
                  secondLoopComponentUid
                }
              ),
              type === "component" && (0, import_jsx_runtime.jsx)(
                ComponentList,
                {
                  ...item,
                  customRowComponent,
                  targetUid,
                  isNestedInDZComponent: isFromDynamicZone,
                  editTarget,
                  firstLoopComponentUid
                }
              ),
              type === "dynamiczone" && (0, import_jsx_runtime.jsx)(
                DynamicZoneList,
                {
                  ...item,
                  customRowComponent,
                  addComponent: addComponentToDZ,
                  targetUid
                }
              )
            ] }, item.name);
          }) })
        ] })
      }
    ),
    isMain && isInDevelopmentMode && (0, import_jsx_runtime.jsx)(TFooter, { icon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}), onClick: onClickAddField, children: formatMessage({
      id: getTrad(
        `form.button.add.field.to.${modifiedData.contentType ? modifiedData.contentType.schema.kind : editTarget || "collectionType"}`
      ),
      defaultMessage: "Add another field"
    }) }),
    isSub && isInDevelopmentMode && !isFromDynamicZone && (0, import_jsx_runtime.jsx)(
      NestedTFooter,
      {
        icon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
        onClick: onClickAddField,
        color: isFromDynamicZone ? "primary" : "neutral",
        children: formatMessage({
          id: getTrad(`form.button.add.field.to.component`),
          defaultMessage: "Add another field"
        })
      }
    )
  ] });
};
var StyledBox = dt(Box)`
  position: absolute;
  left: -1.8rem;
  top: 0px;

  &:before {
    content: '';
    width: 0.4rem;
    height: 1.2rem;
    background: ${({ theme, color }) => theme.colors[color]};
    display: block;
  }
`;
var Svg = dt.svg`
  position: relative;
  flex-shrink: 0;
  transform: translate(-0.5px, -1px);

  * {
    fill: ${({ theme, color }) => theme.colors[color]};
  }
`;
var Curve = (props) => (0, import_jsx_runtime.jsx)(StyledBox, { children: (0, import_jsx_runtime.jsx)(
  Svg,
  {
    width: "20",
    height: "23",
    viewBox: "0 0 20 23",
    fill: "none",
    xmlns: "http://www.w3.org/2000/svg",
    ...props,
    children: (0, import_jsx_runtime.jsx)(
      "path",
      {
        fillRule: "evenodd",
        clipRule: "evenodd",
        d: "M7.02477 14.7513C8.65865 17.0594 11.6046 18.6059 17.5596 18.8856C18.6836 18.9384 19.5976 19.8435 19.5976 20.9688V20.9688C19.5976 22.0941 18.6841 23.0125 17.5599 22.9643C10.9409 22.6805 6.454 20.9387 3.75496 17.1258C0.937988 13.1464 0.486328 7.39309 0.486328 0.593262H4.50974C4.50974 7.54693 5.06394 11.9813 7.02477 14.7513Z"
      }
    )
  }
) });
var DisplayedType = ({
  type,
  customField = null,
  repeatable = false
}) => {
  const { formatMessage } = useIntl();
  let readableType = type;
  if (["integer", "biginteger", "float", "decimal"].includes(type)) {
    readableType = "number";
  } else if (["string"].includes(type)) {
    readableType = "text";
  }
  if (customField) {
    return (0, import_jsx_runtime.jsx)(Typography, { children: formatMessage({
      id: getTrad("attribute.customField"),
      defaultMessage: "Custom field"
    }) });
  }
  return (0, import_jsx_runtime.jsxs)(Typography, { textColor: "neutral800", children: [
    formatMessage({
      id: getTrad(`attribute.${readableType}`),
      defaultMessage: type
    }),
    " ",
    repeatable && formatMessage({
      id: getTrad("component.repeatable"),
      defaultMessage: "(repeatable)"
    })
  ] });
};
var UpperFirst = ({ content }) => (0, import_jsx_runtime.jsx)(import_jsx_runtime.Fragment, { children: (0, import_upperFirst.default)(content) });
var BoxWrapper = dt(Box)`
  position: relative;
`;
var ListRow = (0, import_react.memo)(
  ({
    configurable = true,
    customField = null,
    editTarget,
    firstLoopComponentUid = null,
    isFromDynamicZone = false,
    name,
    onClick,
    relation = "",
    repeatable = false,
    secondLoopComponentUid = null,
    target = null,
    targetUid = null,
    type
  }) => {
    const { contentTypes, isInDevelopmentMode, removeAttribute } = useDataManager();
    const { formatMessage } = useIntl();
    const isMorph = type === "relation" && relation.includes("morph");
    const ico = ["integer", "biginteger", "float", "decimal"].includes(type) ? "number" : type;
    const contentType = (0, import_get.default)(contentTypes, [target], {});
    const contentTypeFriendlyName = (0, import_get.default)(contentType, ["schema", "displayName"], "");
    const isPluginContentType = (0, import_get.default)(contentType, "plugin");
    const src = target ? "relation" : ico;
    const handleClick = () => {
      if (isMorph) {
        return;
      }
      if (configurable !== false) {
        const attrType = type;
        onClick(
          // Tells where the attribute is located in the main modifiedData object : contentType, component or components
          editTarget,
          // main data type uid
          secondLoopComponentUid || firstLoopComponentUid || targetUid,
          // Name of the attribute
          name,
          // Type of the attribute
          attrType,
          customField
        );
      }
    };
    let loopNumber;
    if (secondLoopComponentUid && firstLoopComponentUid) {
      loopNumber = 2;
    } else if (firstLoopComponentUid) {
      loopNumber = 1;
    } else {
      loopNumber = 0;
    }
    return (0, import_jsx_runtime.jsxs)(
      BoxWrapper,
      {
        tag: "tr",
        onClick: isInDevelopmentMode && configurable && !isMorph ? handleClick : void 0,
        children: [
          (0, import_jsx_runtime.jsxs)("td", { style: { position: "relative" }, children: [
            loopNumber !== 0 && (0, import_jsx_runtime.jsx)(Curve, { color: isFromDynamicZone ? "primary200" : "neutral150" }),
            (0, import_jsx_runtime.jsxs)(Flex, { paddingLeft: 2, gap: 4, children: [
              (0, import_jsx_runtime.jsx)(AttributeIcon, { type: src, customField }),
              (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", fontWeight: "bold", children: name })
            ] })
          ] }),
          (0, import_jsx_runtime.jsx)("td", { children: target ? (0, import_jsx_runtime.jsxs)(Typography, { textColor: "neutral800", children: [
            formatMessage({
              id: getTrad(
                `modelPage.attribute.${isMorph ? "relation-polymorphic" : "relationWith"}`
              ),
              defaultMessage: "Relation with"
            }),
            " ",
            (0, import_jsx_runtime.jsxs)("span", { style: { fontStyle: "italic" }, children: [
              (0, import_jsx_runtime.jsx)(UpperFirst, { content: contentTypeFriendlyName }),
              " ",
              isPluginContentType && `(${formatMessage({
                id: getTrad(`from`),
                defaultMessage: "from"
              })}: ${isPluginContentType})`
            ] })
          ] }) : (0, import_jsx_runtime.jsx)(DisplayedType, { type, customField, repeatable }) }),
          (0, import_jsx_runtime.jsx)("td", { children: isInDevelopmentMode ? (0, import_jsx_runtime.jsx)(Flex, { justifyContent: "flex-end", onClick: (e) => e.stopPropagation(), children: configurable ? (0, import_jsx_runtime.jsxs)(Flex, { gap: 1, children: [
            !isMorph && (0, import_jsx_runtime.jsx)(
              IconButton,
              {
                onClick: handleClick,
                label: `${formatMessage({
                  id: "app.utils.edit",
                  defaultMessage: "Edit"
                })} ${name}`,
                variant: "ghost",
                children: (0, import_jsx_runtime.jsx)(ForwardRef$1r, {})
              }
            ),
            (0, import_jsx_runtime.jsx)(
              IconButton,
              {
                onClick: (e) => {
                  e.stopPropagation();
                  removeAttribute(
                    editTarget,
                    name,
                    secondLoopComponentUid || firstLoopComponentUid || ""
                  );
                },
                label: `${formatMessage({
                  id: "global.delete",
                  defaultMessage: "Delete"
                })} ${name}`,
                variant: "ghost",
                children: (0, import_jsx_runtime.jsx)(ForwardRef$j, {})
              }
            )
          ] }) : (0, import_jsx_runtime.jsx)(ForwardRef$2d, {}) }) : (
            /*
              In production mode the edit icons aren't visible, therefore
              we need to reserve the same space, otherwise the height of the
              row might collapse, leading to bad positioned curve icons
            */
            (0, import_jsx_runtime.jsx)(Box, { height: "3.2rem" })
          ) })
        ]
      }
    );
  }
);
var getAttributeDisplayedType = (type) => {
  let displayedType;
  switch (type) {
    case "date":
    case "datetime":
    case "time":
    case "timestamp":
      displayedType = "date";
      break;
    case "integer":
    case "biginteger":
    case "decimal":
    case "float":
      displayedType = "number";
      break;
    case "string":
    case "text":
      displayedType = "text";
      break;
    case "":
      displayedType = "relation";
      break;
    default:
      displayedType = type;
  }
  return displayedType;
};
var cmPermissions = {
  collectionTypesConfigurations: [
    {
      action: "plugin::content-manager.collection-types.configure-view",
      subject: null
    }
  ],
  componentsConfigurations: [
    {
      action: "plugin::content-manager.components.configure-layout",
      subject: null
    }
  ],
  singleTypesConfigurations: [
    {
      action: "plugin::content-manager.single-types.configure-view",
      subject: null
    }
  ]
};
var LinkToCMSettingsView = (0, import_react.memo)(
  ({
    disabled,
    isTemporary = false,
    isInContentTypeView = true,
    contentTypeKind = "collectionType",
    targetUid = ""
  }) => {
    const { formatMessage } = useIntl();
    const navigate = useNavigate();
    const { collectionTypesConfigurations, componentsConfigurations, singleTypesConfigurations } = cmPermissions;
    const label = formatMessage({
      id: "content-type-builder.form.button.configure-view",
      defaultMessage: "Configure the view"
    });
    let permissionsToApply = collectionTypesConfigurations;
    const handleClick = () => {
      if (isTemporary) {
        return false;
      }
      if (isInContentTypeView) {
        navigate(`/content-manager/collection-types/${targetUid}/configurations/edit`);
      } else {
        navigate(`/content-manager/components/${targetUid}/configurations/edit`);
      }
      return false;
    };
    if (isInContentTypeView && contentTypeKind === "singleType") {
      permissionsToApply = singleTypesConfigurations;
    }
    if (!isInContentTypeView) {
      permissionsToApply = componentsConfigurations;
    }
    const { isLoading, allowedActions } = useRBAC({
      viewConfig: permissionsToApply
    });
    if (isLoading) {
      return null;
    }
    if (!allowedActions.canConfigureView) {
      return null;
    }
    return (0, import_jsx_runtime.jsx)(
      Button,
      {
        startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$2h, {}),
        variant: "tertiary",
        onClick: handleClick,
        disabled: isTemporary || disabled,
        children: label
      }
    );
  }
);
var LayoutsHeaderCustom = dt(Layouts.Header)`
  overflow-wrap: anywhere;
`;
var ListView = () => {
  const { initialData, modifiedData, isInDevelopmentMode, isInContentTypeView, submitData } = useDataManager();
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const match = useMatch("/plugins/content-type-builder/:kind/:currentUID");
  const {
    onOpenModalAddComponentsToDZ,
    onOpenModalAddField,
    onOpenModalEditField,
    onOpenModalEditSchema,
    onOpenModalEditCustomField
  } = useFormModalNavigation();
  const firstMainDataPath = isInContentTypeView ? "contentType" : "component";
  const mainDataTypeAttributesPath = [firstMainDataPath, "schema", "attributes"];
  const targetUid = (0, import_get.default)(modifiedData, [firstMainDataPath, "uid"]);
  const isTemporary = (0, import_get.default)(modifiedData, [firstMainDataPath, "isTemporary"], false);
  const contentTypeKind = (0, import_get.default)(modifiedData, [firstMainDataPath, "schema", "kind"], null);
  const attributes = (0, import_get.default)(modifiedData, mainDataTypeAttributesPath, []);
  const isFromPlugin = (0, import_has.default)(initialData, [firstMainDataPath, "plugin"]);
  const hasModelBeenModified = !(0, import_isEqual.default)(modifiedData, initialData);
  const forTarget = isInContentTypeView ? "contentType" : "component";
  const handleClickAddComponentToDZ = (dynamicZoneTarget) => {
    onOpenModalAddComponentsToDZ({ dynamicZoneTarget, targetUid });
  };
  const handleClickEditField = async (forTarget2, targetUid2, attributeName, type, customField) => {
    const attributeType = getAttributeDisplayedType(type);
    const step = type === "component" ? "2" : null;
    if (customField) {
      onOpenModalEditCustomField({
        forTarget: forTarget2,
        targetUid: targetUid2,
        attributeName,
        attributeType,
        customFieldUid: customField
      });
    } else {
      onOpenModalEditField({
        forTarget: forTarget2,
        targetUid: targetUid2,
        attributeName,
        attributeType,
        step
      });
    }
  };
  let label = (0, import_get.default)(modifiedData, [firstMainDataPath, "schema", "displayName"], "");
  const kind = (0, import_get.default)(modifiedData, [firstMainDataPath, "schema", "kind"], "");
  const isCreatingFirstContentType = (match == null ? void 0 : match.params.currentUID) === "create-content-type";
  if (!label && isCreatingFirstContentType) {
    label = formatMessage({
      id: getTrad("button.model.create"),
      defaultMessage: "Create new collection type"
    });
  }
  const onEdit = () => {
    const contentType = kind || firstMainDataPath;
    if (contentType === "collectionType") {
      trackUsage("willEditNameOfContentType");
    }
    if (contentType === "singleType") {
      trackUsage("willEditNameOfSingleType");
    }
    onOpenModalEditSchema({
      modalType: firstMainDataPath,
      forTarget: firstMainDataPath,
      targetUid,
      kind: contentType
    });
  };
  usePrompt({
    when: hasModelBeenModified,
    message: formatMessage({ id: getTrad("prompt.unsaved"), defaultMessage: "Are you sure?" })
  });
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(
      LayoutsHeaderCustom,
      {
        id: "title",
        primaryAction: isInDevelopmentMode && (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, marginLeft: 2, children: [
          !isCreatingFirstContentType && (0, import_jsx_runtime.jsx)(
            Button,
            {
              startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
              variant: "secondary",
              minWidth: "max-content",
              onClick: () => {
                onOpenModalAddField({ forTarget, targetUid });
              },
              children: formatMessage({
                id: getTrad("button.attributes.add.another"),
                defaultMessage: "Add another field"
              })
            }
          ),
          (0, import_jsx_runtime.jsx)(
            Button,
            {
              startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
              onClick: async () => await submitData(),
              type: "submit",
              disabled: (0, import_isEqual.default)(modifiedData, initialData),
              children: formatMessage({
                id: "global.save",
                defaultMessage: "Save"
              })
            }
          )
        ] }),
        secondaryAction: isInDevelopmentMode && !isFromPlugin && !isCreatingFirstContentType && (0, import_jsx_runtime.jsx)(Button, { startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1r, {}), variant: "tertiary", onClick: onEdit, children: formatMessage({
          id: "app.utils.edit",
          defaultMessage: "Edit"
        }) }),
        title: (0, import_upperFirst.default)(label),
        subtitle: formatMessage({
          id: getTrad("listView.headerLayout.description"),
          defaultMessage: "Build the data architecture of your content"
        }),
        navigationAction: (0, import_jsx_runtime.jsx)(BackButton, {})
      }
    ),
    (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
      (0, import_jsx_runtime.jsx)(Flex, { justifyContent: "flex-end", children: (0, import_jsx_runtime.jsx)(Flex, { gap: 2, children: (0, import_jsx_runtime.jsx)(
        LinkToCMSettingsView,
        {
          targetUid,
          isTemporary,
          isInContentTypeView,
          contentTypeKind,
          disabled: isCreatingFirstContentType
        },
        "link-to-cm-settings-view"
      ) }) }),
      (0, import_jsx_runtime.jsx)(Box, { background: "neutral0", shadow: "filterShadow", hasRadius: true, children: (0, import_jsx_runtime.jsx)(
        List,
        {
          items: attributes,
          customRowComponent: (props) => (0, import_jsx_runtime.jsx)(ListRow, { ...props, onClick: handleClickEditField }),
          addComponentToDZ: handleClickAddComponentToDZ,
          targetUid,
          editTarget: forTarget,
          isMain: true
        }
      ) })
    ] }) })
  ] });
};
export {
  ListView as default
};
//# sourceMappingURL=ListView-g5kB4f_y-X6TL2U5A.js.map
