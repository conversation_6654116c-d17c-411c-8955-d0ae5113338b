import * as React from 'react'
import isBrowser from '#is-browser'

const syncFallback = create => create()

const useInsertionEffect = React['useInsertion' + 'Effect']
  ? React['useInsertion' + 'Effect']
  : false

export const useInsertionEffectAlwaysWithSyncFallback = !isBrowser
  ? syncFallback
  : useInsertionEffect || syncFallback

export const useInsertionEffectWithLayoutFallback =
  useInsertionEffect || React.useLayoutEffect
