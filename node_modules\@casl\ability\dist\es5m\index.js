import{createFactory as t,$eq as r,$ne as i,$lt as n,$lte as e,$gt as u,$gte as o,$in as s,$nin as a,$all as f,$size as c,$regex as h,$options as v,$elemMatch as l,$exists as d,eq as b,ne as y,lt as p,lte as w,gt as g,gte as A,within as j,nin as E,all as m,size as $,regex as M,elemMatch as O,exists as x,and as F}from"@ucast/mongo2js";function _(t,r){for(var i=0;i<r.length;i++){var n=r[i];n.enumerable=n.enumerable||false;n.configurable=true;if("value"in n)n.writable=true;Object.defineProperty(t,B(n.key),n)}}function S(t,r,i){if(r)_(t.prototype,r);if(i)_(t,i);Object.defineProperty(t,"prototype",{writable:false});return t}function C(){C=Object.assign?Object.assign.bind():function(t){for(var r=1;r<arguments.length;r++){var i=arguments[r];for(var n in i)if(Object.prototype.hasOwnProperty.call(i,n))t[n]=i[n]}return t};return C.apply(this,arguments)}function P(t,r){t.prototype=Object.create(r.prototype);t.prototype.constructor=t;R(t,r)}function R(t,r){R=Object.setPrototypeOf?Object.setPrototypeOf.bind():function t(r,i){r.__proto__=i;return r};return R(t,r)}function T(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function k(t,r){if("object"!==typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var n=i.call(t,r||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===r?String:Number)(t)}function B(t){var r=k(t,"string");return"symbol"===typeof r?r:String(r)}function q(t){return Array.isArray(t)?t:[t]}var z=Object.hasOwn||Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);var Y="__caslSubjectType__";function D(t,r){if(r)if(!z(r,Y))Object.defineProperty(r,Y,{value:t});else if(t!==r[Y])throw new Error("Trying to cast object to subject type "+t+" but previously it was casted to "+r[Y]);return r}var L=function t(r){var i=typeof r;return"string"===i||"function"===i};var N=function t(r){return r.modelName||r.name};var G=function t(r){return"string"===typeof r?r:N(r)};function H(t){if(z(t,Y))return t[Y];return N(t.constructor)}function I(t,r,i){var n=q(r);var e=0;while(e<n.length){var u=n[e++];if(z(t,u))n=i(n,t[u])}return n}function J(t,r){if("string"===typeof r&&-1!==t.indexOf(r))return r;for(var i=0;i<r.length;i++)if(-1!==t.indexOf(r[i]))return r[i];return null}var K=function t(r,i){return r.concat(i)};function Q(t,r){if(r in t)throw new Error('Cannot use "'+r+"\" as an alias because it's reserved action.");var i=Object.keys(t);var n=function t(i,n){var e=J(i,n);if(e)throw new Error("Detected cycle "+e+" -> "+i.join(", "));var u="string"===typeof n&&n===r||-1!==i.indexOf(r)||Array.isArray(n)&&-1!==n.indexOf(r);if(u)throw new Error('Cannot make an alias to "'+r+'" because this is reserved action');return i.concat(n)};for(var e=0;e<i.length;e++)I(t,i[e],n)}function U(t,r){if(!r||false!==r.skipValidate)Q(t,r&&r.anyAction||"manage");return function(r){return I(t,r,K)}}function V(t,r,i){for(var n=i;n<r.length;n++)t.push(r[n])}function W(t,r){if(!t||!t.length)return r||[];if(!r||!r.length)return t||[];var i=0;var n=0;var e=[];while(i<t.length&&n<r.length)if(t[i].priority<r[n].priority){e.push(t[i]);i++}else{e.push(r[n]);n++}V(e,t,i);V(e,r,n);return e}function X(t,r,i){var n=t.get(r);if(!n){n=i();t.set(r,n)}return n}var Z=function t(r){return r};function tt(t,r){if(Array.isArray(t.fields)&&!t.fields.length)throw new Error("`rawRule.fields` cannot be an empty array. https://bit.ly/390miLa");if(t.fields&&!r.fieldMatcher)throw new Error('You need to pass "fieldMatcher" option in order to restrict access by fields');if(t.conditions&&!r.conditionsMatcher)throw new Error('You need to pass "conditionsMatcher" option in order to restrict access by conditions')}var rt=function(){function t(t,r,i){if(void 0===i)i=0;tt(t,r);this.action=r.resolveAction(t.action);this.subject=t.subject;this.inverted=!!t.inverted;this.conditions=t.conditions;this.reason=t.reason;this.origin=t;this.fields=t.fields?q(t.fields):void 0;this.priority=i;this.t=r}var r=t.prototype;r.i=function t(){if(this.conditions&&!this.u)this.u=this.t.conditionsMatcher(this.conditions);return this.u};r.matchesConditions=function t(r){if(!this.conditions)return true;if(!r||L(r))return!this.inverted;var i=this.i();return i(r)};r.matchesField=function t(r){if(!this.fields)return true;if(!r)return!this.inverted;if(this.fields&&!this.o)this.o=this.t.fieldMatcher(this.fields);return this.o(r)};S(t,[{key:"ast",get:function t(){var r=this.i();return r?r.ast:void 0}}]);return t}();function it(t,r){var i={value:t,prev:r,next:null};if(r)r.next=i;return i}function nt(t){if(t.next)t.next.prev=t.prev;if(t.prev)t.prev.next=t.next;t.next=t.prev=null}var et=function t(r){return{value:r.value,prev:r.prev,next:r.next}};var ut=function t(){return{rules:[],merged:false}};var ot=function t(){return new Map};var st=function(){function t(t,r){if(void 0===t)t=[];if(void 0===r)r={};this.h=false;this.v={conditionsMatcher:r.conditionsMatcher,fieldMatcher:r.fieldMatcher,resolveAction:r.resolveAction||Z};this.l=r.anyAction||"manage";this.p=r.anySubjectType||"all";this.g=r.detectSubjectType||H;this.A=t;this.j=this.m(t)}var r=t.prototype;r.detectSubjectType=function t(r){if(L(r))return r;if(!r)return this.p;return this.g(r)};r.update=function t(r){var i={rules:r,ability:this,target:this};this.$("update",i);this.A=r;this.j=this.m(r);this.$("updated",i);return this};r.m=function t(r){var i=new Map;for(var n=r.length-1;n>=0;n--){var e=r.length-n-1;var u=new rt(r[n],this.v,e);var o=q(u.action);var s=q(u.subject||this.p);if(!this.h&&u.fields)this.h=true;for(var a=0;a<s.length;a++){var f=X(i,s[a],ot);for(var c=0;c<o.length;c++)X(f,o[c],ut).rules.push(u)}}return i};r.possibleRulesFor=function t(r,i){if(void 0===i)i=this.p;if(!L(i))throw new Error('"possibleRulesFor" accepts only subject types (i.e., string or class) as the 2nd parameter');var n=X(this.j,i,ot);var e=X(n,r,ut);if(e.merged)return e.rules;var u=r!==this.l&&n.has(this.l)?n.get(this.l).rules:void 0;var o=W(e.rules,u);if(i!==this.p)o=W(o,this.possibleRulesFor(r,this.p));e.rules=o;e.merged=true;return o};r.rulesFor=function t(r,i,n){var e=this.possibleRulesFor(r,i);if(n&&"string"!==typeof n)throw new Error("The 3rd, `field` parameter is expected to be a string. See https://stalniy.github.io/casl/en/api/casl-ability#can-of-pure-ability for details");if(!this.h)return e;return e.filter((function(t){return t.matchesField(n)}))};r.actionsFor=function t(r){if(!L(r))throw new Error('"actionsFor" accepts only subject types (i.e., string or class) as a parameter');var i=new Set;var n=this.j.get(r);if(n)Array.from(n.keys()).forEach((function(t){return i.add(t)}));var e=r!==this.p?this.j.get(this.p):void 0;if(e)Array.from(e.keys()).forEach((function(t){return i.add(t)}));return Array.from(i)};r.on=function t(r,i){this.M=this.M||new Map;var n=this.M;var e=n.get(r)||null;var u=it(i,e);n.set(r,u);return function(){var t=n.get(r);if(!u.next&&!u.prev&&t===u)n.delete(r);else if(u===t)n.set(r,u.prev);nt(u)}};r.$=function t(r,i){if(!this.M)return;var n=this.M.get(r)||null;while(null!==n){var e=n.prev?et(n.prev):null;n.value(i);n=e}};S(t,[{key:"rules",get:function t(){return this.A}}]);return t}();var at=function(t){P(PureAbility,t);function PureAbility(){return t.apply(this,arguments)||this}var r=PureAbility.prototype;r.can=function t(r,i,n){var e=this.relevantRuleFor(r,i,n);return!!e&&!e.inverted};r.relevantRuleFor=function t(r,i,n){var e=this.detectSubjectType(i);var u=this.rulesFor(r,e,n);for(var o=0,s=u.length;o<s;o++)if(u[o].matchesConditions(i))return u[o];return null};r.cannot=function t(r,i,n){return!this.can(r,i,n)};return PureAbility}(st);var ft={$eq:r,$ne:i,$lt:n,$lte:e,$gt:u,$gte:o,$in:s,$nin:a,$all:f,$size:c,$regex:h,$options:v,$elemMatch:l,$exists:d};var ct={eq:b,ne:y,lt:p,lte:w,gt:g,gte:A,in:j,nin:E,all:m,size:$,regex:M,elemMatch:O,exists:x,and:F};var ht=function r(i,n,e){return t(C({},ft,i),C({},ct,n),e)};var vt=t(ft,ct);var lt=/[-/\\^$+?.()|[\]{}]/g;var dt=/\.?\*+\.?/g;var bt=/\*+/;var yt=/\./g;function pt(t,r,i){var n="*"===i[0]||"."===t[0]&&"."===t[t.length-1]?"+":"*";var e=-1===t.indexOf("**")?"[^.]":".";var u=t.replace(yt,"\\$&").replace(bt,e+n);return r+t.length===i.length?"(?:"+u+")?":u}function wt(t,r,i){if("."===t&&("*"===i[r-1]||"*"===i[r+1]))return t;return"\\"+t}function gt(t){var r=t.map((function(t){return t.replace(lt,wt).replace(dt,pt)}));var i=r.length>1?"(?:"+r.join("|")+")":r[0];return new RegExp("^"+i+"$")}var At=function t(r){var i;return function(t){if("undefined"===typeof i)i=r.every((function(t){return-1===t.indexOf("*")}))?null:gt(r);return null===i?-1!==r.indexOf(t):i.test(t)}};var jt=function(t){P(Ability,t);function Ability(r,i){if(void 0===r)r=[];if(void 0===i)i={};return t.call(this,r,C({conditionsMatcher:vt,fieldMatcher:At},i))||this}return Ability}(at);function createMongoAbility(t,r){if(void 0===t)t=[];if(void 0===r)r={};return new at(t,C({conditionsMatcher:vt,fieldMatcher:At},r))}function isAbilityClass(t){return"function"===typeof t.prototype.possibleRulesFor}var Et=function(){function t(t){this.O=t}var r=t.prototype;r.because=function t(r){this.O.reason=r;return this};return t}();var mt=function(){function AbilityBuilder(t){var r=this;this.rules=[];this.F=t;this.can=function(t,i,n,e){return r._(t,i,n,e,false)};this.cannot=function(t,i,n,e){return r._(t,i,n,e,true)};this.build=function(t){return isAbilityClass(r.F)?new r.F(r.rules,t):r.F(r.rules,t)}}var t=AbilityBuilder.prototype;t._=function t(r,i,n,e,u){var o={action:r};if(u)o.inverted=u;if(i){o.subject=i;if(Array.isArray(n)||"string"===typeof n)o.fields=n;else if("undefined"!==typeof n)o.conditions=n;if("undefined"!==typeof e)o.conditions=e}this.rules.push(o);return new Et(o)};return AbilityBuilder}();function defineAbility(t,r){var i=new mt(createMongoAbility);var n=t(i.can,i.cannot);if(n&&"function"===typeof n.then)return n.then((function(){return i.build(r)}));return i.build(r)}var $t=function t(r){return'Cannot execute "'+r.action+'" on "'+r.subjectType+'"'};var Mt=function t(r){this.message=r};Mt.prototype=Object.create(Error.prototype);var Ot=function(t){P(ForbiddenError,t);ForbiddenError.setDefaultMessage=function t(r){this.S="string"===typeof r?function(){return r}:r};ForbiddenError.from=function t(r){return new this(r)};function ForbiddenError(r){var i;i=t.call(this,"")||this;i.ability=r;if("function"===typeof Error.captureStackTrace){i.name="ForbiddenError";Error.captureStackTrace(T(i),i.constructor)}return i}var r=ForbiddenError.prototype;r.setMessage=function t(r){this.message=r;return this};r.throwUnlessCan=function t(r,i,n){var e=this.unlessCan(r,i,n);if(e)throw e};r.unlessCan=function t(r,i,n){var e=this.ability.relevantRuleFor(r,i,n);if(e&&!e.inverted)return;this.action=r;this.subject=i;this.subjectType=G(this.ability.detectSubjectType(i));this.field=n;var u=e?e.reason:"";this.message=this.message||u||this.constructor.S(this);return this};return ForbiddenError}(Mt);Ot.S=$t;var xt=Object.freeze({__proto__:null});export{jt as Ability,mt as AbilityBuilder,Ot as ForbiddenError,at as PureAbility,ht as buildMongoQueryMatcher,U as createAliasResolver,createMongoAbility,defineAbility,H as detectSubjectType,At as fieldPatternMatcher,$t as getDefaultErrorMessage,xt as hkt,vt as mongoQueryMatcher,D as subject,q as wrapArray};
//# sourceMappingURL=index.js.map
