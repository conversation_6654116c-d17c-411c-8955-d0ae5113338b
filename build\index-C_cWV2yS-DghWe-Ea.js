import{ba as I,fu as v,a as O,ft as $,r as x,fQ as f,gr as z,g7 as q,m as e,L as c,n as H,fv as o,A as _,a1 as B,N as F,Y as G,Z as W,_ as P,J as U,G as u,x as r,a9 as j,gu as Y,aa as y,gv as Z,b5 as J,b6 as Q,E as d}from"./strapi-YzJfjJ2z.js";import{f as V}from"./immer.esm-DNdbQyeB.js";const S=({sort:t="",pageSize:n=10,onChange:s})=>{const{formatMessage:i}=v();return e.jsx(U,{background:"neutral0",hasRadius:!0,shadow:"tableShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(u.Root,{gap:4,children:[e.jsx(u.Item,{s:12,col:6,direction:"column",alignItems:"stretch",children:e.jsxs(r.Root,{hint:i({id:o("config.entries.note"),defaultMessage:"Number of assets displayed by default in the Media Library"}),name:"pageSize",children:[e.jsx(r.Label,{children:i({id:o("config.entries.title"),defaultMessage:"Entries per page"})}),e.jsx(j,{onChange:a=>s({target:{name:"pageSize",value:a}}),value:n,children:Y.map(a=>e.jsx(y,{value:a,children:a},a))}),e.jsx(r.Hint,{})]})}),e.jsx(u.Item,{s:12,col:6,direction:"column",alignItems:"stretch",children:e.jsxs(r.Root,{hint:i({id:o("config.note"),defaultMessage:"Note: You can override this value in the media library."}),name:"sort",children:[e.jsx(r.Label,{children:i({id:o("config.sort.title"),defaultMessage:"Default sort order"})}),e.jsx(j,{onChange:a=>s({target:{name:"sort",value:a}}),value:t,"test-sort":t,"data-testid":"sort-select",children:Z.map(a=>e.jsx(y,{"data-testid":`sort-option-${a.value}`,value:a.value,children:i({id:o(a.key),defaultMessage:`${a.value}`})},a.key))}),e.jsx(r.Hint,{})]})})]})})};S.propTypes={sort:d.string.isRequired,pageSize:d.number.isRequired,onChange:d.func.isRequired};const D=`${f}/ON_CHANGE`,C=`${f}/SET_LOADED`,K=({name:t,value:n})=>({type:D,keys:t,value:n}),X=()=>({type:C}),p={initialData:{},modifiedData:{}},M=t=>({...p,initialData:t,modifiedData:t}),ee=(t=p,n={type:""})=>V(t,s=>{switch(n.type){case D:{q(s,["modifiedData",...n.keys.split(".")],n.value);break}case C:{const i=M(z(s,["modifiedData"],{}));s.initialData=i.initialData,s.modifiedData=i.modifiedData;break}default:return s}}),ae=({config:t})=>{const{trackUsage:n}=I(),{formatMessage:s}=v(),{toggleNotification:i}=O(),{mutateConfig:a}=$(),{isLoading:R}=a,[k,h]=x.useState(!1),m=()=>h(l=>!l),[L,b]=x.useReducer(ee,p,()=>M(t)),{initialData:w,modifiedData:g}=L,E=l=>{l.preventDefault(),m()},N=async()=>{n("willEditMediaLibraryConfig"),await a.mutateAsync(g),h(!1),b(X()),i({type:"success",message:s({id:"notification.form.success.fields",defaultMessage:"Changes saved"})})},T=({target:{name:l,value:A}})=>{b(K({name:l,value:A}))};return e.jsx(c.Root,{children:e.jsx(H.Main,{"aria-busy":R,children:e.jsxs("form",{onSubmit:E,children:[e.jsx(c.Header,{navigationAction:e.jsx(G,{tag:P,startIcon:e.jsx(W,{}),to:`/plugins/${f}`,id:"go-back",children:s({id:o("config.back"),defaultMessage:"Back"})}),primaryAction:e.jsx(_,{size:"S",startIcon:e.jsx(F,{}),disabled:B(g,w),type:"submit",children:s({id:"global.save",defaultMessage:"Save"})}),subtitle:s({id:o("config.subtitle"),defaultMessage:"Define the view settings of the media library."}),title:s({id:o("config.title"),defaultMessage:"Configure the view - Media Library"})}),e.jsx(c.Content,{children:e.jsx(S,{"data-testid":"settings",pageSize:g.pageSize||"",sort:g.sort||"",onChange:T})}),e.jsx(J.Root,{open:k,onOpenChange:m,children:e.jsx(Q,{onConfirm:N,variant:"default",children:s({id:o("config.popUpWarning.warning.updateAllSettings"),defaultMessage:"This will modify all your settings"})})})]})})})};ae.propTypes={config:d.shape({pageSize:d.number,sort:d.string}).isRequired};export{ae as default};
