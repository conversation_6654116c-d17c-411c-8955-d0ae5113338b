import{a as S,gn as x,c as T,gA as P,r as g,gB as A,gC as O,gD as W,m as a,n as k,L as D,J as w,w as h,am as I,s as L}from"./strapi-YzJfjJ2z.js";import{u as _}from"./useDragLayer-Bta-luUM.js";const f={id:"notification.error",defaultMessage:"An error occurred, please try again"},B=(n={})=>{const{toggleNotification:e}=S(),{formatMessage:t}=x(),{_unstableFormatAPIError:r}=T(),{skip:i=!1,...u}=n,{data:o,isLoading:E,error:l}=P({populate:["stages","stageRequiredToPublish"],...u},{skip:i});g.useEffect(()=>{l&&e({type:"danger",message:r(l)})},[l,r,e]);const[p]=A(),j=g.useCallback(async c=>{try{const s=await p({data:c});return"error"in s?(e({type:"danger",message:r(s.error)}),s):(e({type:"success",message:t({id:"actions.created",defaultMessage:"Created workflow"})}),s)}catch(s){throw e({type:"danger",message:t(f)}),s}},[p,r,t,e]),[m]=O(),R=g.useCallback(async(c,s)=>{try{const d=await m({id:c,data:s});return"error"in d?(e({type:"danger",message:r(d.error)}),d):(e({type:"success",message:t({id:"actions.updated",defaultMessage:"Updated workflow"})}),d)}catch(d){throw e({type:"danger",message:t(f)}),d}},[r,t,e,m]),[y]=W(),b=g.useCallback(async c=>{try{const s=await y({id:c});if("error"in s){e({type:"danger",message:r(s.error)});return}return e({type:"success",message:t({id:"actions.deleted",defaultMessage:"Deleted workflow"})}),s.data}catch(s){throw e({type:"danger",message:t(f)}),s}},[y,r,t,e]),{workflows:C=[],meta:M}=o??{};return{meta:M,workflows:C,isLoading:E,error:l,create:j,delete:b,update:R}},F={STAGE:"stage"},G=({name:n})=>a.jsxs(h,{background:"primary100",borderStyle:"dashed",borderColor:"primary600",borderWidth:"1px",gap:3,hasRadius:!0,padding:3,shadow:"tableShadow",width:"30rem",children:[a.jsx(h,{alignItems:"center",background:"neutral200",borderRadius:"50%",height:6,justifyContent:"center",width:6,children:a.jsx(I,{width:"0.8rem",fill:"neutral600"})}),a.jsx(L,{fontWeight:"bold",children:n})]});function H(n,e,t){if(!n||!e||!t)return{display:"none"};const{x:r,y:i}=t;return{transform:`translate(${r}px, ${i}px)`}}const N=()=>{const{itemType:n,isDragging:e,item:t,initialOffset:r,currentOffset:i,mouseOffset:u}=_(o=>({item:o.getItem(),itemType:o.getItemType(),initialOffset:o.getInitialSourceClientOffset(),currentOffset:o.getSourceClientOffset(),isDragging:o.isDragging(),mouseOffset:o.getClientOffset()}));return!e||n!==F.STAGE?null:a.jsx(w,{height:"100%",left:0,position:"fixed",pointerEvents:"none",top:0,zIndex:100,width:"100%",children:a.jsxs(w,{style:H(r,i,u),children:[a.jsx(G,{name:typeof t.item=="string"?t.item:null}),";"]})})},$=({children:n})=>a.jsx(k.Main,{children:a.jsx(D.Content,{children:n})}),q=({title:n,subtitle:e,navigationAction:t,primaryAction:r})=>{const{formatMessage:i}=x();return a.jsxs(a.Fragment,{children:[a.jsx(k.Title,{children:i({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:n})}),a.jsx(D.BaseHeader,{navigationAction:t,primaryAction:r,title:n,subtitle:e})]})};export{N as D,q as H,$ as R,F as a,B as u};
