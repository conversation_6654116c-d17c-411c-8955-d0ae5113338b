import{r as c,gN as G,gO as be,gP as ge,gQ as V,gR as Se}from"./strapi-YzJfjJ2z.js";var We=function(t){return Ge(t)&&!$e(t)};function Ge(e){return!!e&&typeof e=="object"}function $e(e){var t=Object.prototype.toString.call(e);return t==="[object RegExp]"||t==="[object Date]"||ze(e)}var Ke=typeof Symbol=="function"&&Symbol.for,Ye=Ke?Symbol.for("react.element"):60103;function ze(e){return e.$$typeof===Ye}function qe(e){return Array.isArray(e)?[]:{}}function $(e,t){return t.clone!==!1&&t.isMergeableObject(e)?j(qe(e),e,t):e}function xe(e,t,n){return e.concat(t).map(function(i){return $(i,n)})}function Qe(e,t,n){var i={};return n.isMergeableObject(e)&&Object.keys(e).forEach(function(a){i[a]=$(e[a],n)}),Object.keys(t).forEach(function(a){!n.isMergeableObject(t[a])||!e[a]?i[a]=$(t[a],n):i[a]=j(e[a],t[a],n)}),i}function j(e,t,n){n=n||{},n.arrayMerge=n.arrayMerge||xe,n.isMergeableObject=n.isMergeableObject||We;var i=Array.isArray(t),a=Array.isArray(e),s=i===a;return s?i?n.arrayMerge(e,t,n):Qe(e,t,n):$(t,n)}j.all=function(t,n){if(!Array.isArray(t))throw new Error("first argument should be an array");return t.reduce(function(i,a){return j(i,a,n)},{})};var te=j;function b(){return b=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var n=arguments[t];for(var i in n)Object.prototype.hasOwnProperty.call(n,i)&&(e[i]=n[i])}return e},b.apply(this,arguments)}function Ae(e,t){if(e==null)return{};var n={},i=Object.keys(e),a,s;for(s=0;s<i.length;s++)a=i[s],!(t.indexOf(a)>=0)&&(n[a]=e[a]);return n}var K=c.createContext(void 0);K.displayName="FormikContext";var Je=K.Provider;K.Consumer;function Xe(){var e=c.useContext(K);return e||G(!1,"Formik context is undefined, please verify you are calling useFormikContext() as child of a <Formik> component."),e}var g=function(t){return typeof t=="function"},Y=function(t){return t!==null&&typeof t=="object"},Ze=function(t){return String(Math.floor(Number(t)))===t},ee=function(t){return Object.prototype.toString.call(t)==="[object String]"},er=function(t){return c.Children.count(t)===0},re=function(t){return Y(t)&&g(t.then)};function rr(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}function S(e,t,n,i){i===void 0&&(i=0);for(var a=ge(t);e&&i<a.length;)e=e[a[i++]];return i!==a.length&&!e||e===void 0?n:e}function w(e,t,n){for(var i=be(e),a=i,s=0,v=ge(t);s<v.length-1;s++){var m=v[s],_=S(e,v.slice(0,s+1));if(_&&(Y(_)||Array.isArray(_)))a=a[m]=be(_);else{var A=v[s+1];a=a[m]=Ze(A)&&Number(A)>=0?[]:{}}}return(s===0?e:a)[v[s]]===n?e:(n===void 0?delete a[v[s]]:a[v[s]]=n,s===0&&n===void 0&&delete i[v[s]],i)}function Ie(e,t,n,i){n===void 0&&(n=new WeakMap),i===void 0&&(i={});for(var a=0,s=Object.keys(e);a<s.length;a++){var v=s[a],m=e[v];Y(m)?n.get(m)||(n.set(m,!0),i[v]=Array.isArray(m)?[]:{},Ie(m,t,n,i[v])):i[v]=t}return i}function tr(e,t){switch(t.type){case"SET_VALUES":return b({},e,{values:t.payload});case"SET_TOUCHED":return b({},e,{touched:t.payload});case"SET_ERRORS":return V(e.errors,t.payload)?e:b({},e,{errors:t.payload});case"SET_STATUS":return b({},e,{status:t.payload});case"SET_ISSUBMITTING":return b({},e,{isSubmitting:t.payload});case"SET_ISVALIDATING":return b({},e,{isValidating:t.payload});case"SET_FIELD_VALUE":return b({},e,{values:w(e.values,t.payload.field,t.payload.value)});case"SET_FIELD_TOUCHED":return b({},e,{touched:w(e.touched,t.payload.field,t.payload.value)});case"SET_FIELD_ERROR":return b({},e,{errors:w(e.errors,t.payload.field,t.payload.value)});case"RESET_FORM":return b({},e,t.payload);case"SET_FORMIK_STATE":return t.payload(e);case"SUBMIT_ATTEMPT":return b({},e,{touched:Ie(e.values,!0),isSubmitting:!0,submitCount:e.submitCount+1});case"SUBMIT_FAILURE":return b({},e,{isSubmitting:!1});case"SUBMIT_SUCCESS":return b({},e,{isSubmitting:!1});default:return e}}var L={},W={};function nr(e){var t=e.validateOnChange,n=t===void 0?!0:t,i=e.validateOnBlur,a=i===void 0?!0:i,s=e.validateOnMount,v=s===void 0?!1:s,m=e.isInitialValid,_=e.enableReinitialize,A=_===void 0?!1:_,ie=e.onSubmit,Fe=Ae(e,["validateOnChange","validateOnBlur","validateOnMount","isInitialValid","enableReinitialize","onSubmit"]),f=b({validateOnChange:n,validateOnBlur:a,validateOnMount:v,onSubmit:ie},Fe),I=c.useRef(f.initialValues),O=c.useRef(f.initialErrors||L),k=c.useRef(f.initialTouched||W),D=c.useRef(f.initialStatus),F=c.useRef(!1),M=c.useRef({});c.useEffect(function(){typeof m>"u"||G(!1,"isInitialValid has been deprecated and will be removed in future versions of Formik. Please use initialErrors or validateOnMount instead.")},[]),c.useEffect(function(){return F.current=!0,function(){F.current=!1}},[]);var Re=c.useState(0),Ce=Re[1],P=c.useRef({values:f.initialValues,errors:f.initialErrors||L,touched:f.initialTouched||W,status:f.initialStatus,isSubmitting:!1,isValidating:!1,submitCount:0}),E=P.current,y=c.useCallback(function(r){var u=P.current;P.current=tr(u,r),u!==P.current&&Ce(function(o){return o+1})},[]),ae=c.useCallback(function(r,u){return new Promise(function(o,l){var d=f.validate(r,u);d==null?o(L):re(d)?d.then(function(h){o(h||L)},function(h){console.warn("Warning: An unhandled error was caught during validation in <Formik validate />",h),l(h)}):o(d)})},[f.validate]),z=c.useCallback(function(r,u){var o=f.validationSchema,l=g(o)?o(u):o,d=u&&l.validateAt?l.validateAt(u,r):ar(r,l);return new Promise(function(h,p){d.then(function(){h(L)},function(C){C.name==="ValidationError"?h(ir(C)):(console.warn("Warning: An unhandled error was caught during validation in <Formik validationSchema />",C),p(C))})})},[f.validationSchema]),ue=c.useCallback(function(r,u){return new Promise(function(o){return o(M.current[r].validate(u))})},[]),oe=c.useCallback(function(r){var u=Object.keys(M.current).filter(function(l){return g(M.current[l].validate)}),o=u.length>0?u.map(function(l){return ue(l,S(r,l))}):[Promise.resolve("DO_NOT_DELETE_YOU_WILL_BE_FIRED")];return Promise.all(o).then(function(l){return l.reduce(function(d,h,p){return h==="DO_NOT_DELETE_YOU_WILL_BE_FIRED"||h&&(d=w(d,u[p],h)),d},{})})},[ue]),_e=c.useCallback(function(r){return Promise.all([oe(r),f.validationSchema?z(r):{},f.validate?ae(r):{}]).then(function(u){var o=u[0],l=u[1],d=u[2],h=te.all([o,l,d],{arrayMerge:ur});return h})},[f.validate,f.validationSchema,oe,ae,z]),R=T(function(r){return r===void 0&&(r=E.values),y({type:"SET_ISVALIDATING",payload:!0}),_e(r).then(function(u){return F.current&&(y({type:"SET_ISVALIDATING",payload:!1}),y({type:"SET_ERRORS",payload:u})),u})});c.useEffect(function(){v&&F.current===!0&&V(I.current,f.initialValues)&&R(I.current)},[v,R]);var B=c.useCallback(function(r){var u=r&&r.values?r.values:I.current,o=r&&r.errors?r.errors:O.current?O.current:f.initialErrors||{},l=r&&r.touched?r.touched:k.current?k.current:f.initialTouched||{},d=r&&r.status?r.status:D.current?D.current:f.initialStatus;I.current=u,O.current=o,k.current=l,D.current=d;var h=function(){y({type:"RESET_FORM",payload:{isSubmitting:!!r&&!!r.isSubmitting,errors:o,touched:l,status:d,values:u,isValidating:!!r&&!!r.isValidating,submitCount:r&&r.submitCount&&typeof r.submitCount=="number"?r.submitCount:0}})};if(f.onReset){var p=f.onReset(E.values,me);re(p)?p.then(h):h()}else h()},[f.initialErrors,f.initialStatus,f.initialTouched,f.onReset]);c.useEffect(function(){F.current===!0&&!V(I.current,f.initialValues)&&A&&(I.current=f.initialValues,B(),v&&R(I.current))},[A,f.initialValues,B,v,R]),c.useEffect(function(){A&&F.current===!0&&!V(O.current,f.initialErrors)&&(O.current=f.initialErrors||L,y({type:"SET_ERRORS",payload:f.initialErrors||L}))},[A,f.initialErrors]),c.useEffect(function(){A&&F.current===!0&&!V(k.current,f.initialTouched)&&(k.current=f.initialTouched||W,y({type:"SET_TOUCHED",payload:f.initialTouched||W}))},[A,f.initialTouched]),c.useEffect(function(){A&&F.current===!0&&!V(D.current,f.initialStatus)&&(D.current=f.initialStatus,y({type:"SET_STATUS",payload:f.initialStatus}))},[A,f.initialStatus,f.initialTouched]);var le=T(function(r){if(M.current[r]&&g(M.current[r].validate)){var u=S(E.values,r),o=M.current[r].validate(u);return re(o)?(y({type:"SET_ISVALIDATING",payload:!0}),o.then(function(l){return l}).then(function(l){y({type:"SET_FIELD_ERROR",payload:{field:r,value:l}}),y({type:"SET_ISVALIDATING",payload:!1})})):(y({type:"SET_FIELD_ERROR",payload:{field:r,value:o}}),Promise.resolve(o))}else if(f.validationSchema)return y({type:"SET_ISVALIDATING",payload:!0}),z(E.values,r).then(function(l){return l}).then(function(l){y({type:"SET_FIELD_ERROR",payload:{field:r,value:S(l,r)}}),y({type:"SET_ISVALIDATING",payload:!1})});return Promise.resolve()}),Me=c.useCallback(function(r,u){var o=u.validate;M.current[r]={validate:o}},[]),Oe=c.useCallback(function(r){delete M.current[r]},[]),ce=T(function(r,u){y({type:"SET_TOUCHED",payload:r});var o=u===void 0?a:u;return o?R(E.values):Promise.resolve()}),se=c.useCallback(function(r){y({type:"SET_ERRORS",payload:r})},[]),fe=T(function(r,u){var o=g(r)?r(E.values):r;y({type:"SET_VALUES",payload:o});var l=u===void 0?n:u;return l?R(o):Promise.resolve()}),H=c.useCallback(function(r,u){y({type:"SET_FIELD_ERROR",payload:{field:r,value:u}})},[]),U=T(function(r,u,o){y({type:"SET_FIELD_VALUE",payload:{field:r,value:u}});var l=o===void 0?n:o;return l?R(w(E.values,r,u)):Promise.resolve()}),de=c.useCallback(function(r,u){var o=u,l=r,d;if(!ee(r)){r.persist&&r.persist();var h=r.target?r.target:r.currentTarget,p=h.type,C=h.name,X=h.id,Z=h.value,je=h.checked,Pe=h.outerHTML,pe=h.options,He=h.multiple;o=u||C||X,o||Te({htmlContent:Pe,documentationAnchorLink:"handlechange-e-reactchangeeventany--void",handlerName:"handleChange"}),l=/number|range/.test(p)?(d=parseFloat(Z),isNaN(d)?"":d):/checkbox/.test(p)?lr(S(E.values,o),je,Z):pe&&He?or(pe):Z}o&&U(o,l)},[U,E.values]),q=T(function(r){if(ee(r))return function(u){return de(u,r)};de(r)}),N=T(function(r,u,o){u===void 0&&(u=!0),y({type:"SET_FIELD_TOUCHED",payload:{field:r,value:u}});var l=o===void 0?a:o;return l?R(E.values):Promise.resolve()}),ve=c.useCallback(function(r,u){r.persist&&r.persist();var o=r.target,l=o.name,d=o.id,h=o.outerHTML,p=u||l||d;p||Te({htmlContent:h,documentationAnchorLink:"handleblur-e-any--void",handlerName:"handleBlur"}),N(p,!0)},[N]),x=T(function(r){if(ee(r))return function(u){return ve(u,r)};ve(r)}),he=c.useCallback(function(r){g(r)?y({type:"SET_FORMIK_STATE",payload:r}):y({type:"SET_FORMIK_STATE",payload:function(){return r}})},[]),ye=c.useCallback(function(r){y({type:"SET_STATUS",payload:r})},[]),Ee=c.useCallback(function(r){y({type:"SET_ISSUBMITTING",payload:r})},[]),Q=T(function(){return y({type:"SUBMIT_ATTEMPT"}),R().then(function(r){var u=r instanceof Error,o=!u&&Object.keys(r).length===0;if(o){var l;try{if(l=Le(),l===void 0)return}catch(d){throw d}return Promise.resolve(l).then(function(d){return F.current&&y({type:"SUBMIT_SUCCESS"}),d}).catch(function(d){if(F.current)throw y({type:"SUBMIT_FAILURE"}),d})}else if(F.current&&(y({type:"SUBMIT_FAILURE"}),u))throw r})}),ke=T(function(r){if(r&&r.preventDefault&&g(r.preventDefault)&&r.preventDefault(),r&&r.stopPropagation&&g(r.stopPropagation)&&r.stopPropagation(),typeof document<"u"){var u=rr();u!==null&&u instanceof HTMLButtonElement&&(u.attributes&&u.attributes.getNamedItem("type")||G(!1,'You submitted a Formik form using a button with an unspecified `type` attribute.  Most browsers default button elements to `type="submit"`. If this is not a submit button, please add `type="button"`.'))}Q().catch(function(o){console.warn("Warning: An unhandled error was caught from submitForm()",o)})}),me={resetForm:B,validateForm:R,validateField:le,setErrors:se,setFieldError:H,setFieldTouched:N,setFieldValue:U,setStatus:ye,setSubmitting:Ee,setTouched:ce,setValues:fe,setFormikState:he,submitForm:Q},Le=T(function(){return ie(E.values,me)}),Ve=T(function(r){r&&r.preventDefault&&g(r.preventDefault)&&r.preventDefault(),r&&r.stopPropagation&&g(r.stopPropagation)&&r.stopPropagation(),B()}),we=c.useCallback(function(r){return{value:S(E.values,r),error:S(E.errors,r),touched:!!S(E.touched,r),initialValue:S(I.current,r),initialTouched:!!S(k.current,r),initialError:S(O.current,r)}},[E.errors,E.touched,E.values]),De=c.useCallback(function(r){return{setValue:function(o,l){return U(r,o,l)},setTouched:function(o,l){return N(r,o,l)},setError:function(o){return H(r,o)}}},[U,N,H]),Ue=c.useCallback(function(r){var u=Y(r),o=u?r.name:r,l=S(E.values,o),d={name:o,value:l,onChange:q,onBlur:x};if(u){var h=r.type,p=r.value,C=r.as,X=r.multiple;h==="checkbox"?p===void 0?d.checked=!!l:(d.checked=!!(Array.isArray(l)&&~l.indexOf(p)),d.value=p):h==="radio"?(d.checked=l===p,d.value=p):C==="select"&&X&&(d.value=d.value||[],d.multiple=!0)}return d},[x,q,E.values]),J=c.useMemo(function(){return!V(I.current,E.values)},[I.current,E.values]),Ne=c.useMemo(function(){return typeof m<"u"?J?E.errors&&Object.keys(E.errors).length===0:m!==!1&&g(m)?m(f):m:E.errors&&Object.keys(E.errors).length===0},[m,J,E.errors,f]),Be=b({},E,{initialValues:I.current,initialErrors:O.current,initialTouched:k.current,initialStatus:D.current,handleBlur:x,handleChange:q,handleReset:Ve,handleSubmit:ke,resetForm:B,setErrors:se,setFormikState:he,setFieldTouched:N,setFieldValue:U,setFieldError:H,setStatus:ye,setSubmitting:Ee,setTouched:ce,setValues:fe,submitForm:Q,validateForm:R,validateField:le,isValid:Ne,dirty:J,unregisterField:Oe,registerField:Me,getFieldProps:Ue,getFieldMeta:we,getFieldHelpers:De,validateOnBlur:a,validateOnChange:n,validateOnMount:v});return Be}function dr(e){var t=nr(e),n=e.component,i=e.children,a=e.render,s=e.innerRef;return c.useImperativeHandle(s,function(){return t}),c.useEffect(function(){e.render&&G(!1,"<Formik render> has been deprecated and will be removed in future versions of Formik. Please use a child callback function instead. To get rid of this warning, replace <Formik render={(props) => ...} /> with <Formik>{(props) => ...}</Formik>")},[]),c.createElement(Je,{value:t},n?c.createElement(n,t):a?a(t):i?g(i)?i(t):er(i)?null:c.Children.only(i):null)}function Te(e){var t=e.htmlContent,n=e.documentationAnchorLink,i=e.handlerName;console.warn("Warning: Formik called `"+i+"`, but you forgot to pass an `id` or `name` attribute to your input:\n    "+t+`
    Formik cannot determine which value to update. For more info see https://formik.org/docs/api/formik#`+n+`
  `)}function ir(e){var t={};if(e.inner){if(e.inner.length===0)return w(t,e.path,e.message);for(var a=e.inner,n=Array.isArray(a),i=0,a=n?a:a[Symbol.iterator]();;){var s;if(n){if(i>=a.length)break;s=a[i++]}else{if(i=a.next(),i.done)break;s=i.value}var v=s;S(t,v.path)||(t=w(t,v.path,v.message))}}return t}function ar(e,t,n,i){n===void 0&&(n=!1);var a=ne(e);return t[n?"validateSync":"validate"](a,{abortEarly:!1,context:a})}function ne(e){var t=Array.isArray(e)?[]:{};for(var n in e)if(Object.prototype.hasOwnProperty.call(e,n)){var i=String(n);Array.isArray(e[i])===!0?t[i]=e[i].map(function(a){return Array.isArray(a)===!0||Se(a)?ne(a):a!==""?a:void 0}):Se(e[i])?t[i]=ne(e[i]):t[i]=e[i]!==""?e[i]:void 0}return t}function ur(e,t,n){var i=e.slice();return t.forEach(function(s,v){if(typeof i[v]>"u"){var m=n.clone!==!1,_=m&&n.isMergeableObject(s);i[v]=_?te(Array.isArray(s)?[]:{},s,n):s}else n.isMergeableObject(s)?i[v]=te(e[v],s,n):e.indexOf(s)===-1&&i.push(s)}),i}function or(e){return Array.from(e).filter(function(t){return t.selected}).map(function(t){return t.value})}function lr(e,t,n){if(typeof e=="boolean")return!!t;var i=[],a=!1,s=-1;if(Array.isArray(e))i=e,s=e.indexOf(n),a=s>=0;else if(!n||n=="true"||n=="false")return!!t;return t&&n&&!a?i.concat(n):a?i.slice(0,s).concat(i.slice(s+1)):i}var cr=typeof window<"u"&&typeof window.document<"u"&&typeof window.document.createElement<"u"?c.useLayoutEffect:c.useEffect;function T(e){var t=c.useRef(e);return cr(function(){t.current=e}),c.useCallback(function(){for(var n=arguments.length,i=new Array(n),a=0;a<n;a++)i[a]=arguments[a];return t.current.apply(void 0,i)},[])}var sr=c.forwardRef(function(e,t){var n=e.action,i=Ae(e,["action"]),a=n??"#",s=Xe(),v=s.handleReset,m=s.handleSubmit;return c.createElement("form",b({onSubmit:m,ref:t,onReset:v,action:a},i))});sr.displayName="Form";export{dr as F,sr as a};
