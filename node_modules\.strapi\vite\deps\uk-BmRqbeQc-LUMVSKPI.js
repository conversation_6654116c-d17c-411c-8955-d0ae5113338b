import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/uk-BmRqbeQc.mjs
var uk = {
  "BoundRoute.title": "Пов'язано з",
  "EditForm.inputSelect.description.role": "Підключає нового автентифікованого користувача до вибраної ролі.",
  "EditForm.inputSelect.label.role": "Роль за замовчуванням для автентифікованих користувачів",
  "EditForm.inputToggle.description.email": "Не дозволяти користувачам створювати кілька аккаунтів з тим самим email, але різним провайдером автентифікації.",
  "EditForm.inputToggle.description.email-confirmation": "Якщо увімкнено (ON), щойно зареєстровані користувачі отримують листа для підтверждення.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "Куди ви будете перенаправлені після підтвердження свого email.",
  "EditForm.inputToggle.description.email-reset-password": "URL-адреса сторінки скидання пароля вашого додатку",
  "EditForm.inputToggle.description.sign-up": "Якщо вимкнено (OFF), реєстрація заборонена. Незалежно від використовуваного провайдера більше ніхто не зможе приєднатись.",
  "EditForm.inputToggle.label.email": "Один аккаунт на email",
  "EditForm.inputToggle.label.email-confirmation": "Увімкнути підтверження email",
  "EditForm.inputToggle.label.email-confirmation-redirection": "URL для перенаправлення",
  "EditForm.inputToggle.label.email-reset-password": "Сторінка скидання пароля",
  "EditForm.inputToggle.label.sign-up": "Увімкнути реєстрацію",
  "HeaderNav.link.advancedSettings": "Розширені налаштування",
  "HeaderNav.link.emailTemplates": "Шаблони листів",
  "HeaderNav.link.providers": "Провайдери",
  "Plugin.permissions.plugins.description": "Встановіть всі дозволені дії у плаґіні {name}.",
  "Plugins.header.description": "Нижче перераховано лише дії, пов'язані з маршрутом.",
  "Plugins.header.title": "Дозволи",
  "Policies.header.hint": "Виберіть дії вашого додатку або дії плаґіну та натисніть на значок шестірні, щоб відобразити пов'язаний маршрут",
  "Policies.header.title": "Розширені налашрування",
  "PopUpForm.Email.email_templates.inputDescription": "Якщо ви не впевнені, як використовувати змінні, {link}",
  "PopUpForm.Email.options.from.email.label": "Email відправника",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Ім'я відправника",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "Повідомлення",
  "PopUpForm.Email.options.object.label": "Тема",
  "PopUpForm.Email.options.response_email.label": "Email для відповіді",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Якщо вимкнуто, користувачі не зможуть вікористати цей провайдер.",
  "PopUpForm.Providers.enabled.label": "Увімкнути",
  "PopUpForm.Providers.key.label": "Client ID",
  "PopUpForm.Providers.key.placeholder": "TEXT",
  "PopUpForm.Providers.redirectURL.front-end.label": "URL переадресації для вашего front-end додатку",
  "PopUpForm.Providers.secret.label": "Client Secret",
  "PopUpForm.Providers.secret.placeholder": "TEXT",
  "PopUpForm.Providers.subdomain.label": "Host URI (Subdomain)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "Редагування шаблони листів",
  "notification.success.submit": "Налаштування оновлено",
  "plugin.description.long": "Захистіть API за допомогою процесу аутентифікації на основі JWT. Цей плагін також включає можливості ACL, які дозволяють керувати дозволами між групами користувачів.",
  "plugin.description.short": "Захистіть API за допомогою процесу аутентифікації на основі JWT"
};
export {
  uk as default
};
//# sourceMappingURL=uk-BmRqbeQc-LUMVSKPI.js.map
