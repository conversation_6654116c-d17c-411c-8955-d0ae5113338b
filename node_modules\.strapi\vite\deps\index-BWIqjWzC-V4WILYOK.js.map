{"version": 3, "sources": ["../../../@strapi/upload/admin/src/pages/SettingsPage/init.js", "../../../@strapi/upload/admin/src/pages/SettingsPage/reducer.js", "../../../@strapi/upload/admin/src/pages/SettingsPage/index.jsx"], "sourcesContent": ["const init = (initialState) => {\n  return initialState;\n};\n\nexport default init;\n", "import { produce } from 'immer';\nimport set from 'lodash/set';\n\nconst initialState = {\n  initialData: {\n    responsiveDimensions: true,\n    sizeOptimization: true,\n    autoOrientation: false,\n    videoPreview: false,\n  },\n  modifiedData: {\n    responsiveDimensions: true,\n    sizeOptimization: true,\n    autoOrientation: false,\n    videoPreview: false,\n  },\n};\n\nconst reducer = (state, action) =>\n  // eslint-disable-next-line consistent-return\n  produce(state, (drafState) => {\n    switch (action.type) {\n      case 'GET_DATA_SUCCEEDED': {\n        drafState.initialData = action.data;\n        drafState.modifiedData = action.data;\n        break;\n      }\n      case 'ON_CHANGE': {\n        set(drafState, ['modifiedData', ...action.keys.split('.')], action.value);\n        break;\n      }\n      default:\n        return state;\n    }\n  });\n\nexport default reducer;\nexport { initialState };\n", "import React, { useReducer } from 'react';\n\nimport { Page, useNotification, useFetchClient, Layouts } from '@strapi/admin/strapi-admin';\nimport { Box, Button, Flex, Grid, Toggle, Typography, Field } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport isEqual from 'lodash/isEqual';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQuery } from 'react-query';\n\nimport { PERMISSIONS } from '../../constants';\nimport { getTrad } from '../../utils';\n\nimport init from './init';\nimport reducer, { initialState } from './reducer';\n\nexport const SettingsPage = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { get, put } = useFetchClient();\n\n  const [{ initialData, modifiedData }, dispatch] = useReducer(reducer, initialState, init);\n\n  const { data, isLoading, refetch } = useQuery({\n    queryKey: ['upload', 'settings'],\n    async queryFn() {\n      const {\n        data: { data },\n      } = await get('/upload/settings');\n\n      return data;\n    },\n  });\n\n  React.useEffect(() => {\n    if (data) {\n      dispatch({\n        type: 'GET_DATA_SUCCEEDED',\n        data,\n      });\n    }\n  }, [data]);\n\n  const isSaveButtonDisabled = isEqual(initialData, modifiedData);\n\n  const { mutateAsync, isLoading: isSubmiting } = useMutation({\n    async mutationFn(body) {\n      return put('/upload/settings', body);\n    },\n    onSuccess() {\n      refetch();\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: 'notification.form.success.fields' }),\n      });\n    },\n    onError(err) {\n      console.error(err);\n    },\n  });\n\n  const handleSubmit = async (e) => {\n    e.preventDefault();\n\n    if (isSaveButtonDisabled) {\n      return;\n    }\n\n    await mutateAsync(modifiedData);\n  };\n\n  const handleChange = ({ target: { name, value } }) => {\n    dispatch({\n      type: 'ON_CHANGE',\n      keys: name,\n      value,\n    });\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Page.Main tabIndex={-1}>\n      <Page.Title>\n        {formatMessage({\n          id: getTrad('page.title'),\n          defaultMessage: 'Settings - Media Libray',\n        })}\n      </Page.Title>\n      <form onSubmit={handleSubmit}>\n        <Layouts.Header\n          title={formatMessage({\n            id: getTrad('settings.header.label'),\n            defaultMessage: 'Media Library',\n          })}\n          primaryAction={\n            <Button\n              disabled={isSaveButtonDisabled}\n              loading={isSubmiting}\n              type=\"submit\"\n              startIcon={<Check />}\n              size=\"S\"\n            >\n              {formatMessage({\n                id: 'global.save',\n                defaultMessage: 'Save',\n              })}\n            </Button>\n          }\n          subtitle={formatMessage({\n            id: getTrad('settings.sub-header.label'),\n            defaultMessage: 'Configure the settings for the Media Library',\n          })}\n        />\n        <Layouts.Content>\n          <Layouts.Root>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={12}>\n              <Box background=\"neutral0\" padding={6} shadow=\"filterShadow\" hasRadius>\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                  <Flex>\n                    <Typography variant=\"delta\" tag=\"h2\">\n                      {formatMessage({\n                        id: getTrad('settings.blockTitle'),\n                        defaultMessage: 'Asset management',\n                      })}\n                    </Typography>\n                  </Flex>\n                  <Grid.Root gap={6}>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        hint={formatMessage({\n                          id: getTrad('settings.form.responsiveDimensions.description'),\n                          defaultMessage:\n                            'Enabling this option will generate multiple formats (small, medium and large) of the uploaded asset.',\n                        })}\n                        name=\"responsiveDimensions\"\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: getTrad('settings.form.responsiveDimensions.label'),\n                            defaultMessage: 'Responsive friendly upload',\n                          })}\n                        </Field.Label>\n                        <Toggle\n                          checked={modifiedData.responsiveDimensions}\n                          offLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.off-label',\n                            defaultMessage: 'Off',\n                          })}\n                          onLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.on-label',\n                            defaultMessage: 'On',\n                          })}\n                          onChange={(e) => {\n                            handleChange({\n                              target: { name: 'responsiveDimensions', value: e.target.checked },\n                            });\n                          }}\n                        />\n                        <Field.Hint />\n                      </Field.Root>\n                    </Grid.Item>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        hint={formatMessage({\n                          id: getTrad('settings.form.sizeOptimization.description'),\n                          defaultMessage:\n                            'Enabling this option will reduce the image size and slightly reduce its quality.',\n                        })}\n                        name=\"sizeOptimization\"\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: getTrad('settings.form.sizeOptimization.label'),\n                            defaultMessage: 'Size optimization',\n                          })}\n                        </Field.Label>\n                        <Toggle\n                          checked={modifiedData.sizeOptimization}\n                          offLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.off-label',\n                            defaultMessage: 'Off',\n                          })}\n                          onLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.on-label',\n                            defaultMessage: 'On',\n                          })}\n                          onChange={(e) => {\n                            handleChange({\n                              target: { name: 'sizeOptimization', value: e.target.checked },\n                            });\n                          }}\n                        />\n                        <Field.Hint />\n                      </Field.Root>\n                    </Grid.Item>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        hint={formatMessage({\n                          id: getTrad('settings.form.autoOrientation.description'),\n                          defaultMessage:\n                            'Enabling this option will automatically rotate the image according to EXIF orientation tag.',\n                        })}\n                        name=\"autoOrientation\"\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: getTrad('settings.form.autoOrientation.label'),\n                            defaultMessage: 'Auto orientation',\n                          })}\n                        </Field.Label>\n                        <Toggle\n                          checked={modifiedData.autoOrientation}\n                          offLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.off-label',\n                            defaultMessage: 'Off',\n                          })}\n                          onLabel={formatMessage({\n                            id: 'app.components.ToggleCheckbox.on-label',\n                            defaultMessage: 'On',\n                          })}\n                          onChange={(e) => {\n                            handleChange({\n                              target: { name: 'autoOrientation', value: e.target.checked },\n                            });\n                          }}\n                        />\n                        <Field.Hint />\n                      </Field.Root>\n                    </Grid.Item>\n                  </Grid.Root>\n                </Flex>\n              </Box>\n            </Flex>\n          </Layouts.Root>\n        </Layouts.Content>\n      </form>\n    </Page.Main>\n  );\n};\n\nconst ProtectedSettingsPage = () => (\n  <Page.Protect permissions={PERMISSIONS.settings}>\n    <SettingsPage />\n  </Page.Protect>\n);\n\nexport default ProtectedSettingsPage;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA,IAAM,OAAO,CAACA,kBAAiB;AAC7B,SAAOA;AACT;ACCA,IAAM,eAAe;EACnB,aAAa;IACX,sBAAsB;IACtB,kBAAkB;IAClB,iBAAiB;IACjB,cAAc;EACf;EACD,cAAc;IACZ,sBAAsB;IACtB,kBAAkB;IAClB,iBAAiB;IACjB,cAAc;EACf;AACH;AAEA,IAAM,UAAU,CAAC,OAAO;;EAEtB,GAAQ,OAAO,CAAC,cAAc;AAC5B,YAAQ,OAAO,MAAI;MACjB,KAAK,sBAAsB;AACzB,kBAAU,cAAc,OAAO;AAC/B,kBAAU,eAAe,OAAO;AAChC;MACD;MACD,KAAK,aAAa;AAChB,uBAAAC,SAAI,WAAW,CAAC,gBAAgB,GAAG,OAAO,KAAK,MAAM,GAAG,CAAC,GAAG,OAAO,KAAK;AACxE;MACD;MACD;AACE,eAAO;IACV;EACL,CAAG;;ACnBI,IAAM,eAAe,MAAM;AAC1B,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,EAAE,KAAK,IAAI,IAAI,eAAe;AAE9B,QAAA,CAAC,EAAE,aAAa,aAAA,GAAgB,QAAQ,QAAI,yBAAW,SAAS,cAAc,IAAI;AAExF,QAAM,EAAE,MAAM,WAAW,QAAA,IAAY,SAAS;IAC5C,UAAU,CAAC,UAAU,UAAU;IAC/B,MAAM,UAAU;AACR,YAAA;QACJ,MAAM,EAAE,MAAAC,MAAK;MAAA,IACX,MAAM,IAAI,kBAAkB;AAEzBA,aAAAA;IACT;EAAA,CACD;AAEDC,eAAAA,QAAM,UAAU,MAAM;AACpB,QAAI,MAAM;AACC,eAAA;QACP,MAAM;QACN;MAAA,CACD;IACH;EAAA,GACC,CAAC,IAAI,CAAC;AAEH,QAAA,2BAAuB,eAAAC,SAAQ,aAAa,YAAY;AAE9D,QAAM,EAAE,aAAa,WAAW,YAAA,IAAgB,YAAY;IAC1D,MAAM,WAAW,MAAM;AACd,aAAA,IAAI,oBAAoB,IAAI;IACrC;IACA,YAAY;AACF,cAAA;AAEW,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,mCAAA,CAAoC;MAAA,CAClE;IACH;IACA,QAAQ,KAAK;AACX,cAAQ,MAAM,GAAG;IACnB;EAAA,CACD;AAEK,QAAA,eAAe,OAAO,MAAM;AAChC,MAAE,eAAe;AAEjB,QAAI,sBAAsB;AACxB;IACF;AAEA,UAAM,YAAY,YAAY;EAAA;AAG1B,QAAA,eAAe,CAAC,EAAE,QAAQ,EAAE,MAAM,MAAA,EAAA,MAAc;AAC3C,aAAA;MACP,MAAM;MACN,MAAM;MACN;IAAA,CACD;EAAA;AAGH,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEA,aACG,yBAAA,KAAK,MAAL,EAAU,UAAU,IACnB,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAc,cAAA;MACb,IAAI,QAAQ,YAAY;MACxB,gBAAgB;IACjB,CAAA,EAAA,CACH;QACA,yBAAC,QAAK,EAAA,UAAU,cACd,UAAA;UAAA;QAAC,QAAQ;QAAR;UACC,OAAO,cAAc;YACnB,IAAI,QAAQ,uBAAuB;YACnC,gBAAgB;UAAA,CACjB;UACD,mBACE;YAAC;YAAA;cACC,UAAU;cACV,SAAS;cACT,MAAK;cACL,eAAA,wBAAY,eAAM,CAAA,CAAA;cAClB,MAAK;cAEJ,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA;UACH;UAEF,UAAU,cAAc;YACtB,IAAI,QAAQ,2BAA2B;YACvC,gBAAgB;UAAA,CACjB;QAAA;MACH;UACC,wBAAA,QAAQ,SAAR,EACC,cAAA,wBAAC,QAAQ,MAAR,EACC,cAAA,wBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,IACjD,cAAC,wBAAA,KAAA,EAAI,YAAW,YAAW,SAAS,GAAG,QAAO,gBAAe,WAAS,MACpE,cAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;YAAA,wBAAC,MAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;UACb,IAAI,QAAQ,qBAAqB;UACjC,gBAAgB;QAAA,CACjB,EAAA,CACH,EACF,CAAA;YACC,yBAAA,KAAK,MAAL,EAAU,KAAK,GACd,UAAA;cAAC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA;YAAC,MAAM;YAAN;cACC,MAAM,cAAc;gBAClB,IAAI,QAAQ,gDAAgD;gBAC5D,gBACE;cAAA,CACH;cACD,MAAK;cAEL,UAAA;oBAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;kBACb,IAAI,QAAQ,0CAA0C;kBACtD,gBAAgB;gBACjB,CAAA,EAAA,CACH;oBACA;kBAAC;kBAAA;oBACC,SAAS,aAAa;oBACtB,UAAU,cAAc;sBACtB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,SAAS,cAAc;sBACrB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,UAAU,CAAC,MAAM;AACF,mCAAA;wBACX,QAAQ,EAAE,MAAM,wBAAwB,OAAO,EAAE,OAAO,QAAQ;sBAAA,CACjE;oBACH;kBAAA;gBACF;oBACA,wBAAC,MAAM,MAAN,CAAA,CAAW;cAAA;YAAA;UAAA,EAAA,CAEhB;cACA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA;YAAC,MAAM;YAAN;cACC,MAAM,cAAc;gBAClB,IAAI,QAAQ,4CAA4C;gBACxD,gBACE;cAAA,CACH;cACD,MAAK;cAEL,UAAA;oBAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;kBACb,IAAI,QAAQ,sCAAsC;kBAClD,gBAAgB;gBACjB,CAAA,EAAA,CACH;oBACA;kBAAC;kBAAA;oBACC,SAAS,aAAa;oBACtB,UAAU,cAAc;sBACtB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,SAAS,cAAc;sBACrB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,UAAU,CAAC,MAAM;AACF,mCAAA;wBACX,QAAQ,EAAE,MAAM,oBAAoB,OAAO,EAAE,OAAO,QAAQ;sBAAA,CAC7D;oBACH;kBAAA;gBACF;oBACA,wBAAC,MAAM,MAAN,CAAA,CAAW;cAAA;YAAA;UAAA,EAAA,CAEhB;cACA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA;YAAC,MAAM;YAAN;cACC,MAAM,cAAc;gBAClB,IAAI,QAAQ,2CAA2C;gBACvD,gBACE;cAAA,CACH;cACD,MAAK;cAEL,UAAA;oBAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;kBACb,IAAI,QAAQ,qCAAqC;kBACjD,gBAAgB;gBACjB,CAAA,EAAA,CACH;oBACA;kBAAC;kBAAA;oBACC,SAAS,aAAa;oBACtB,UAAU,cAAc;sBACtB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,SAAS,cAAc;sBACrB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,UAAU,CAAC,MAAM;AACF,mCAAA;wBACX,QAAQ,EAAE,MAAM,mBAAmB,OAAO,EAAE,OAAO,QAAQ;sBAAA,CAC5D;oBACH;kBAAA;gBACF;oBACA,wBAAC,MAAM,MAAN,CAAA,CAAW;cAAA;YAAA;UAAA,EAAA,CAEhB;QAAA,EAAA,CACF;MACF,EAAA,CAAA,EACF,CAAA,EAAA,CACF,EAAA,CACF,EACF,CAAA;IAAA,EAAA,CACF;EACF,EAAA,CAAA;AAEJ;AAEM,IAAA,wBAAwB,UAC5B,wBAAC,KAAK,SAAL,EAAa,aAAa,YAAY,UACrC,cAAC,wBAAA,cAAA,CAAa,CAAA,EAChB,CAAA;", "names": ["initialState", "set", "data", "React", "isEqual"]}