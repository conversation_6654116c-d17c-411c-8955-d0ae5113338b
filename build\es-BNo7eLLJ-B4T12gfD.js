const e={"Settings.email.plugin.button.test-email":"Enviar email de prueba","Settings.email.plugin.label.defaultFrom":"Email del remitente predeterminado","Settings.email.plugin.label.defaultReplyTo":"Email de respuesta predeterminado","Settings.email.plugin.label.provider":"Proveedor de email","Settings.email.plugin.label.testAddress":"Receptor de email","Settings.email.plugin.notification.config.error":"No se pudo recuperar la configuración del email","Settings.email.plugin.notification.data.loaded":"Se han cargado los datos de configuración de email","Settings.email.plugin.notification.test.error":"No se pudo enviar un correo de prueba a {to}","Settings.email.plugin.notification.test.success":"La prueba de correo electrónico se realizó correctamente, verifique el buzón de {to}","Settings.email.plugin.placeholder.defaultFrom":"ej: Strapi No-Reply <<EMAIL>>","Settings.email.plugin.placeholder.defaultReplyTo":"ej: Strapi <<EMAIL>>","Settings.email.plugin.placeholder.testAddress":"ej: <EMAIL>","Settings.email.plugin.subTitle":"Pruebe la configuración del complemento de email","Settings.email.plugin.text.configuration":"El complemento se configura a través del archivo {file}, consulte este {link} para ver la documentación.","Settings.email.plugin.title":"Configuración","Settings.email.plugin.title.config":"Configuración","Settings.email.plugin.title.test":"Prueba el envío de email","SettingsNav.link.settings":"Ajustes","SettingsNav.section-label":"Plugin de email","components.Input.error.validation.email":"Este es un correo electrónico inválido"};export{e as default};
