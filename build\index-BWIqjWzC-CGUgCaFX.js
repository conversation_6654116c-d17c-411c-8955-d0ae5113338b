import{fu as E,a as C,b as k,r as z,g7 as L,f as S,g8 as T,a1 as I,g as w,m as e,n as d,fv as i,L as u,A as R,N as A,w as f,J as N,s as P,G as r,x as a,y as m,g9 as _}from"./strapi-YzJfjJ2z.js";import{f as F}from"./immer.esm-DNdbQyeB.js";const G=t=>t,H={initialData:{responsiveDimensions:!0,sizeOptimization:!0,autoOrientation:!1,videoPreview:!1},modifiedData:{responsiveDimensions:!0,sizeOptimization:!0,autoOrientation:!1,videoPreview:!1}},q=(t,n)=>F(t,o=>{switch(n.type){case"GET_DATA_SUCCEEDED":{o.initialData=n.data,o.modifiedData=n.data;break}case"ON_CHANGE":{L(o,["modifiedData",...n.keys.split(".")],n.value);break}default:return t}}),B=()=>{const{formatMessage:t}=E(),{toggleNotification:n}=C(),{get:o,put:b}=k(),[{initialData:x,modifiedData:l},p]=z.useReducer(q,H,G),{data:c,isLoading:j,refetch:y}=S({queryKey:["upload","settings"],async queryFn(){const{data:{data:s}}=await o("/upload/settings");return s}});T.useEffect(()=>{c&&p({type:"GET_DATA_SUCCEEDED",data:c})},[c]);const h=I(x,l),{mutateAsync:M,isLoading:O}=w({async mutationFn(s){return b("/upload/settings",s)},onSuccess(){y(),n({type:"success",message:t({id:"notification.form.success.fields"})})},onError(s){console.error(s)}}),v=async s=>{s.preventDefault(),!h&&await M(l)},g=({target:{name:s,value:D}})=>{p({type:"ON_CHANGE",keys:s,value:D})};return j?e.jsx(d.Loading,{}):e.jsxs(d.Main,{tabIndex:-1,children:[e.jsx(d.Title,{children:t({id:i("page.title"),defaultMessage:"Settings - Media Libray"})}),e.jsxs("form",{onSubmit:v,children:[e.jsx(u.Header,{title:t({id:i("settings.header.label"),defaultMessage:"Media Library"}),primaryAction:e.jsx(R,{disabled:h,loading:O,type:"submit",startIcon:e.jsx(A,{}),size:"S",children:t({id:"global.save",defaultMessage:"Save"})}),subtitle:t({id:i("settings.sub-header.label"),defaultMessage:"Configure the settings for the Media Library"})}),e.jsx(u.Content,{children:e.jsx(u.Root,{children:e.jsx(f,{direction:"column",alignItems:"stretch",gap:12,children:e.jsx(N,{background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:e.jsxs(f,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(f,{children:e.jsx(P,{variant:"delta",tag:"h2",children:t({id:i("settings.blockTitle"),defaultMessage:"Asset management"})})}),e.jsxs(r.Root,{gap:6,children:[e.jsx(r.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(a.Root,{hint:t({id:i("settings.form.responsiveDimensions.description"),defaultMessage:"Enabling this option will generate multiple formats (small, medium and large) of the uploaded asset."}),name:"responsiveDimensions",children:[e.jsx(a.Label,{children:t({id:i("settings.form.responsiveDimensions.label"),defaultMessage:"Responsive friendly upload"})}),e.jsx(m,{checked:l.responsiveDimensions,offLabel:t({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"Off"}),onLabel:t({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"On"}),onChange:s=>{g({target:{name:"responsiveDimensions",value:s.target.checked}})}}),e.jsx(a.Hint,{})]})}),e.jsx(r.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(a.Root,{hint:t({id:i("settings.form.sizeOptimization.description"),defaultMessage:"Enabling this option will reduce the image size and slightly reduce its quality."}),name:"sizeOptimization",children:[e.jsx(a.Label,{children:t({id:i("settings.form.sizeOptimization.label"),defaultMessage:"Size optimization"})}),e.jsx(m,{checked:l.sizeOptimization,offLabel:t({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"Off"}),onLabel:t({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"On"}),onChange:s=>{g({target:{name:"sizeOptimization",value:s.target.checked}})}}),e.jsx(a.Hint,{})]})}),e.jsx(r.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(a.Root,{hint:t({id:i("settings.form.autoOrientation.description"),defaultMessage:"Enabling this option will automatically rotate the image according to EXIF orientation tag."}),name:"autoOrientation",children:[e.jsx(a.Label,{children:t({id:i("settings.form.autoOrientation.label"),defaultMessage:"Auto orientation"})}),e.jsx(m,{checked:l.autoOrientation,offLabel:t({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"Off"}),onLabel:t({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"On"}),onChange:s=>{g({target:{name:"autoOrientation",value:s.target.checked}})}}),e.jsx(a.Hint,{})]})})]})]})})})})})]})]})},K=()=>e.jsx(d.Protect,{permissions:_.settings,children:e.jsx(B,{})});export{B as SettingsPage,K as default};
