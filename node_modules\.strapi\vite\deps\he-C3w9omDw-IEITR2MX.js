import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/he-C3w9omDw.mjs
var Analytics = "אנליטיקס";
var Documentation = "תיאור";
var Email = 'דוא"ל';
var Password = "סיסמה";
var Provider = "ספק";
var ResetPasswordToken = "אפס אסימון סיסמה";
var Role = "תפקיד";
var Username = "שם משתמש";
var Users = "משתמשים";
var or = "או";
var he = {
  Analytics,
  "Auth.components.Oops.text": "החשבון הושעה",
  "Auth.form.button.forgot-password": 'שלח דוא"ל',
  "Auth.form.button.go-home": "חזרה ",
  "Auth.form.button.login": "התחבר",
  "Auth.form.button.login.providers.error": "אנחנו לא יכולים לחבר אותך באמצעות הספק שנבחר.",
  "Auth.form.button.login.strapi": "התחבר באמצעות סטראפי",
  "Auth.form.button.register": "בוא נתחיל",
  "Auth.form.confirmPassword.label": "אישור סיסמה",
  "Auth.form.email.label": 'דוא"ל',
  "Auth.form.email.placeholder": "<EMAIL>",
  "Auth.form.error.blocked": "החשבון נחסם על ידי מנהל המערכת.",
  "Auth.form.error.code.provide": "סופק קוד שגוי.",
  "Auth.form.error.confirmed": 'הדוא"ל של החשבון לא אושר.',
  "Auth.form.error.email.invalid": 'הדוא"ל שגוי.',
  "Auth.form.error.email.provide": 'יש להזין שם משתמש או דוא"ל.',
  "Auth.form.error.email.taken": 'הדוא"ל כבר בשימוש.',
  "Auth.form.error.invalid": "המזהה או הסיסמה שגויים.",
  "Auth.form.error.params.provide": "הוזנו פרמטרים שגויים.",
  "Auth.form.error.password.format": "הסיסמה לא יכולה להכיל את הסמל `$` יותר משלוש פעמים.",
  "Auth.form.error.password.local": "משתמש זה מעולם לא הגדיר סיסמה מקומית, יש להתחבר באמצעות הספק ששימש במהלך יצירת החשבון.",
  "Auth.form.error.password.matching": "הסיסמאות אינן תואמות.",
  "Auth.form.error.password.provide": "יש להזין סיסמה.",
  "Auth.form.error.ratelimit": "יותר מדי ניסיונות, יש לנסות שוב בעוד דקה.",
  "Auth.form.error.user.not-exist": 'הדוא"ל אינו קיים.',
  "Auth.form.error.username.taken": "שם המשתמש כבר בשימוש.",
  "Auth.form.firstname.label": "שם פרטי",
  "Auth.form.firstname.placeholder": "ישראל",
  "Auth.form.forgot-password.email.label": 'הכנס את הדוא"ל',
  "Auth.form.forgot-password.email.label.success": 'הדוא"ל נשלח בהצלחה אל ',
  "Auth.form.lastname.label": "שם משפחה",
  "Auth.form.lastname.placeholder": "ישראלי",
  "Auth.form.register.news.label": "עדכנו אותי לגבי תכונות חדשות ושיפורים עתידיים (על ידי כך אתה מאשר את {terms} ואת {policy}).",
  "Auth.form.rememberMe.label": "זכור אותי",
  "Auth.form.username.label": "שם משתמש",
  "Auth.form.username.placeholder": "ישראל ישראלי",
  "Auth.link.forgot-password": "שיחזור סיסמה",
  "Auth.link.ready": "מוכן להירשם?",
  "Auth.link.signin": "הרשמה",
  "Auth.link.signin.account": "כבר יש לך חשבון?",
  "Auth.privacy-policy-agreement.policy": "מדיניות פרטיות",
  "Auth.privacy-policy-agreement.terms": "תנאים",
  "Content Manager": "מנהל תוכן",
  "Content Type Builder": "בונה סוגי תוכן",
  Documentation,
  Email,
  "Files Upload": "העלאת קבצים",
  "HomePage.head.title": "עמוד ראשי",
  "HomePage.roadmap": "ראה את מפת הדרכים שלנו",
  "HomePage.welcome.congrats": "כל הכבוד!",
  "HomePage.welcome.congrats.content": "אתה מחובר כמנהל הראשון. כדי לגלות את התכונות שסטראפי מספק,",
  "HomePage.welcome.congrats.content.bold": "מומלץ ליצור את סוג האוסף הראשון.",
  "Media Library": "ספריית המדיה",
  "New entry": "רשומה חדשה",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "תפקידים והרשאות",
  "Roles.ListPage.notification.delete-all-not-allowed": "לא ניתן היה למחוק תפקידים מסוימים מכיוון שהם משויכים למשתמשים",
  "Roles.ListPage.notification.delete-not-allowed": "לא ניתן למחוק תפקיד אם הוא משויך למשתמשים",
  "Roles.components.List.empty.withSearch": "אין תפקיד שמתאים לחיפוש ({search}) ...",
  "Settings.PageTitle": "הגדרות - {name}",
  "Settings.application.description": "צפה בפרטי הפרויקטים שלך",
  "Settings.application.edition-title": "חבילה נוכחית",
  "Settings.application.link-pricing": "צפה בכל המחירים",
  "Settings.application.link-upgrade": "שדרג את הפרויקט שלך",
  "Settings.application.node-version": "גרסת NODE",
  "Settings.application.strapi-version": "גרסת סטראפי",
  "Settings.application.title": "אפליקציה",
  "Settings.error": "שגיאה",
  "Settings.global": "הגדרות גלובליות",
  "Settings.permissions": "לוח בקרה",
  "Settings.permissions.category": "הגדרת הרשאות עבור {category}",
  "Settings.permissions.category.plugins": "הגדרת הרשאות עבור התוסף {category}",
  "Settings.permissions.conditions.anytime": "בכל עת",
  "Settings.permissions.conditions.apply": "החל",
  "Settings.permissions.conditions.can": "יכול",
  "Settings.permissions.conditions.conditions": "הגדר תנאים",
  "Settings.permissions.conditions.links": "קישורים",
  "Settings.permissions.conditions.no-actions": "אין שום פעולה",
  "Settings.permissions.conditions.or": "או",
  "Settings.permissions.conditions.when": "כאשר",
  "Settings.permissions.users.create": "יצירת משתמש חדש",
  "Settings.permissions.users.email": 'דוא"ל',
  "Settings.permissions.users.firstname": "שם פרטי",
  "Settings.permissions.users.lastname": "שם משפחה",
  "Settings.permissions.users.form.sso": "התחבר באמצעות SSO",
  "Settings.permissions.users.form.sso.description": "כשמאופשר (ON), משתמשים יוכלו להתחבר באמצעות SSO",
  "Settings.roles.create.description": "הגדירו את הזכויות הניתנות לתפקיד",
  "Settings.roles.create.title": "יצירת תפקיד",
  "Settings.roles.created": "התפקיד נוצר",
  "Settings.roles.edit.title": "עריכת תפקיד",
  "Settings.roles.form.button.users-with-role": "משתמשים עם תפקיד זה",
  "Settings.roles.form.created": "נוצר",
  "Settings.roles.form.description": "שם ותיאור התפקיד",
  "Settings.roles.form.permissions.attributesPermissions": "הרשאות שדות",
  "Settings.roles.form.permissions.create": "יצירה",
  "Settings.roles.form.permissions.delete": "מחיקה",
  "Settings.roles.form.permissions.publish": "פרסום",
  "Settings.roles.form.permissions.read": "קריאה",
  "Settings.roles.form.permissions.update": "עדכון",
  "Settings.roles.list.button.add": "הוספת תפקיד חדש",
  "Settings.roles.list.description": "רשימת תפקידים",
  "Settings.roles.title.singular": "תפקיד",
  "Settings.sso.description": "הגדרת הרשאות עבור הפיצ'ר Single Sign-On.",
  "Settings.sso.form.defaultRole.description": "אפשרות זו תעניק הרשאה דיפולטית למשתמש שהתחבר בפעם הראשונה",
  "Settings.sso.form.defaultRole.description-not-allowed": "נדרשות הרשאות כדי לצפות בהרשאות מנהל",
  "Settings.sso.form.defaultRole.label": "הרשאה דיפולטית",
  "Settings.sso.form.registration.description": "צור חשבון משתמש בעת התחברות עם SSO במידה ולא קיים חשבון זהה",
  "Settings.sso.form.registration.label": "הרשמה אוטומטית",
  "Settings.sso.title": "Single Sign-On",
  "Settings.webhooks.create": "צור webhook",
  "Settings.webhooks.create.header": "יצירת כותרת חדשה",
  "Settings.webhooks.created": "נוצר Webhook",
  "Settings.webhooks.event.publish-tooltip": "מופע זה קיים רק לתוכן לו מופעל מנגנון הטיוטה/פורסם",
  "Settings.webhooks.events.create": "יצירה",
  "Settings.webhooks.events.update": "עדכון",
  "Settings.webhooks.form.events": "ארועים",
  "Settings.webhooks.form.headers": "כותרות",
  "Settings.webhooks.form.url": "קישור",
  "Settings.webhooks.key": "מפתח",
  "Settings.webhooks.list.button.add": "הוספת webhook חדש",
  "Settings.webhooks.list.description": "קבל הודעות על שינויים ב-POST.",
  "Settings.webhooks.list.empty.description": "הוסף את הראשון לרשימה זו.",
  "Settings.webhooks.list.empty.link": "ראה את התיעוד שלנו",
  "Settings.webhooks.list.empty.title": "עדיין אין webhooks",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "Webhooks",
  "Settings.webhooks.trigger": "טריגרים",
  "Settings.webhooks.trigger.cancel": "ביטול טריגר",
  "Settings.webhooks.trigger.pending": "בהמתנה…",
  "Settings.webhooks.trigger.save": "נא לשמור כדי להפעיל",
  "Settings.webhooks.trigger.success": "הצלחה!",
  "Settings.webhooks.trigger.success.label": "טריגר הצליח",
  "Settings.webhooks.trigger.test": "ניסוי טריגר",
  "Settings.webhooks.trigger.title": "שמור לפני הטריגר",
  "Settings.webhooks.value": "ערך",
  Username,
  Users,
  "Users & Permissions": "משתמשים והרשאות",
  "Users.components.List.empty": "אין משתמשים ...",
  "Users.components.List.empty.withFilters": "אין משתמשים עם הפילטרים שנבחרו ...",
  "Users.components.List.empty.withSearch": "אין משתמשים המתאימים לחיפוש ({search})...",
  "app.components.BlockLink.code": "דוגמאות קוד",
  "app.components.Button.cancel": "ביטול",
  "app.components.Button.reset": "איפוס",
  "app.components.ComingSoonPage.comingSoon": "בקרוב",
  "app.components.DownloadInfo.download": "ההורדה בעיצומה...",
  "app.components.DownloadInfo.text": "זה יכול לקחת דקה. תודה על סבלנותך.",
  "app.components.EmptyAttributes.title": "עדיין אין שדות",
  "app.components.HomePage.button.blog": "ראה עוד בבלוג",
  "app.components.HomePage.community": "מצא את הקהילה ברשת",
  "app.components.HomePage.community.content": "שוחח בערוצים שונים עם חברי הצוות, התורמים והמפתחים.",
  "app.components.HomePage.create": "צור את סוג התוכן הראשון שלך",
  "app.components.HomePage.welcome": "ברוך הבא!",
  "app.components.HomePage.welcome.again": "ברוכים הבאים ",
  "app.components.HomePage.welcomeBlock.content": "אנו שמחים לקבל אתכם כחלק מהקהילה. אנחנו כל הזמן מחפשים משוב אז אל תהסס לשלוח לנו DM",
  "app.components.HomePage.welcomeBlock.content.again": "אנו מקווים שאתה מתקדם בפרויקט שלך... אל תהסס לקרוא את החדשות האחרונות על סטראפי. אנו עושים כמיטב יכולתנו לשפר את המוצר על סמך המשוב שלך.",
  "app.components.HomePage.welcomeBlock.content.issues": "בעיות.",
  "app.components.HomePage.welcomeBlock.content.raise": " או אעלה ",
  "app.components.ImgPreview.hint": "גרור ושחרר את הקובץ לאזור זה או {browse} קובץ להעלאה",
  "app.components.ImgPreview.hint.browse": "בחירת קובץ",
  "app.components.InputFile.newFile": "הוסף קובץ חדש",
  "app.components.InputFileDetails.open": "פתח בכרטיסייה חדשה",
  "app.components.InputFileDetails.originalName": "שם מקורי:",
  "app.components.InputFileDetails.remove": "הסר קובץ זה",
  "app.components.InputFileDetails.size": "גודל:",
  "app.components.InstallPluginPage.Download.description": "ייתכן שיידרשו כמה שניות להוריד ולהתקין את התוסף.",
  "app.components.InstallPluginPage.Download.title": "מוריד...",
  "app.components.InstallPluginPage.description": "הרחב את האפליקציה ללא מאמץ.",
  "app.components.LeftMenuFooter.help": "עזרה",
  "app.components.LeftMenuFooter.poweredBy": "מופעל באמצעות ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "סוגי אוספים",
  "app.components.LeftMenuLinkContainer.configuration": "תצורות",
  "app.components.LeftMenuLinkContainer.general": "כללי",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "עדיין לא הותקנו תוספים",
  "app.components.LeftMenuLinkContainer.plugins": "תוספים",
  "app.components.LeftMenuLinkContainer.singleTypes": "סוגים בודדים",
  "app.components.ListPluginsPage.deletePlugin.description": "ייתכן שיחלפו מספר שניות להסרת התוסף.",
  "app.components.ListPluginsPage.deletePlugin.title": "מסיר התקנה",
  "app.components.ListPluginsPage.description": "רשימת התוספים המותקנים בפרויקט.",
  "app.components.ListPluginsPage.head.title": "רשימת תוספים",
  "app.components.Logout.logout": "התנתק",
  "app.components.Logout.profile": "פרופיל",
  "app.components.NotFoundPage.back": "בחזרה לעמוד הבית",
  "app.components.NotFoundPage.description": "לא נמצא",
  "app.components.Official": "רשמי",
  "app.components.Onboarding.label.completed": "% הושלם",
  "app.components.Onboarding.title": "סרטוני הדרכה",
  "app.components.PluginCard.Button.label.download": "הורד",
  "app.components.PluginCard.Button.label.install": "כבר מותקן",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "יש להפעיל את תכונת הטעינה האוטומטית. נא להתחיל את האפליקציה באמצעות `yarn develop`.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "אני מבין!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "מטעמי אבטחה, ניתן להוריד תוספים רק בסביבת פיתוח.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "ההורדה בלתי אפשרית",
  "app.components.PluginCard.compatible": "תואם לאפליקציה שלך",
  "app.components.PluginCard.compatibleCommunity": "תואם לקהילה",
  "app.components.PluginCard.more-details": "פרטים נוספים",
  "app.components.Users.MagicLink.connect": "שלח קישור זה למשתמש כדי שיוכל להתחבר.",
  "app.components.Users.MagicLink.connect.sso": "שלח את הקישור הזה למשתמש, ההתחברות הראשונה יכולה להתבצע באמצעות ספק SSO",
  "app.components.Users.ModalCreateBody.block-title.details": "פרטים",
  "app.components.Users.ModalCreateBody.block-title.roles": "תפקידי משתמש",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "לחשבון שלך יכולה להיות הרשאה אחת או יותר",
  "app.components.Users.SortPicker.button-label": "מיין לפי",
  "app.components.Users.SortPicker.sortby.email_asc": 'דוא"ל (א עד ת)',
  "app.components.Users.SortPicker.sortby.email_desc": 'דוא"ל (ת עד א)',
  "app.components.Users.SortPicker.sortby.firstname_asc": "שם פרטי (א עד ת)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "שם פרטי (ת עד א)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "שם משפחה (א עד ת)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "שם משפחה (ת עד א)",
  "app.components.Users.SortPicker.sortby.username_asc": "שם משתמש (א עד ת)",
  "app.components.Users.SortPicker.sortby.username_desc": "שם משתמש (ת עד א)",
  "app.components.listPlugins.button": "הוספת תוסף חדש",
  "app.components.listPlugins.title.none": "לא הותקנו תוספים",
  "app.components.listPluginsPage.deletePlugin.error": "אירעה שגיאה במהלך הסרת התוסף",
  "app.containers.App.notification.error.init": "אירעה שגיאה בעת בקשת ה- API",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "אם אינך מקבל קישור זה, יש לפנות אל מנהל המערכת.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "זה יכול לקחת כמה דקות עד לקבלת הקישור לשחזור הסיסמה.",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": 'דוא"ל נשלח',
  "app.containers.Users.EditPage.form.active.label": "פעיל",
  "app.containers.Users.EditPage.header.label": "עריכת {name}",
  "app.containers.Users.EditPage.header.label-loading": "עריכת משתמש",
  "app.containers.Users.EditPage.roles-bloc-title": "הרשאות ייחוס",
  "app.containers.Users.ModalForm.footer.button-success": "צור משתמש",
  "app.links.configure-view": "הגדר תצוגה",
  "app.static.links.cheatsheet": "שליף",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "הוסף פילטר",
  "app.utils.defaultMessage": " ",
  "app.utils.errors.file-too-big.message": "הקובץ גדול מידי",
  "app.utils.filters": "פילטר",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "פרסום",
  "app.utils.select-all": "בחר הכל",
  "app.utils.unpublish": "ביטול פרסום",
  "component.Input.error.validation.integer": "הערך חייב להיות מספר שלם",
  "components.AutoReloadBlocker.description": "הפעל את סטראפי באמצעות אחת מהפקודות הבאות:",
  "components.AutoReloadBlocker.header": "יש צורך בתכונת טעינה מחדש עבור תוסף זה.",
  "components.ErrorBoundary.title": "משהו השתבש...",
  "components.Input.error.attribute.key.taken": "ערך זה כבר קיים",
  "components.Input.error.attribute.sameKeyAndName": "לא יכול להיות שווה",
  "components.Input.error.attribute.taken": "שם שדה זה כבר קיים",
  "components.Input.error.contain.lowercase": "הסיסמה חייבת להכיל לפחות תו אחד עם אות קטנה",
  "components.Input.error.contain.number": "הסיסמא חייבת להכיל לפחות ספרה אחת",
  "components.Input.error.contain.uppercase": "על הסיסמה להכיל לפחות תו אחד עם אות גדולה",
  "components.Input.error.contentTypeName.taken": "השם הזה כבר קיים",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "הסיסמאות לא תואמות",
  "components.Input.error.validation.email": 'זה לא דוא"ל',
  "components.Input.error.validation.json": "זה לא תואם לפורמט JSON",
  "components.Input.error.validation.max": "הערך גבוה מדי {max}.",
  "components.Input.error.validation.maxLength": "הערך ארוך מדי {max}.",
  "components.Input.error.validation.min": "הערך נמוך מדי {min}.",
  "components.Input.error.validation.minLength": "הערך קצר מדי {min}.",
  "components.Input.error.validation.minSupMax": "לא יכול להיות מעולה",
  "components.Input.error.validation.regex": "הערך אינו תואם את ה- regex.",
  "components.Input.error.validation.required": "ערך זה נדרש.",
  "components.Input.error.validation.unique": "כבר נעשה שימוש בערך זה.",
  "components.InputSelect.option.placeholder": "בחר כאן",
  "components.ListRow.empty": "אין נתונים להצגה.",
  "components.NotAllowedInput.text": "אין לך הרשאות לצפייה בשדה זה",
  "components.OverlayBlocker.description": "אתה משתמש בתכונה שדורשת להפעיל מחדש את השרת. אנא המתן עד שהשרת יהיה למעלה.",
  "components.OverlayBlocker.description.serverError": "השרת צריך היה להתחיל מחדש, אנא בדוק את יומני המסוף.",
  "components.OverlayBlocker.title": "ממתין להפעלה מחדש ...",
  "components.OverlayBlocker.title.serverError": "ההפעלה מחדש אורכת זמן רב מהצפוי",
  "components.PageFooter.select": "מספר רשומות בעמוד",
  "components.ProductionBlocker.description": "מטעמי אבטחה עלינו להשבית את התוסף בסביבות אחרות.",
  "components.ProductionBlocker.header": "התוסף זה זמין רק בסביבת פיתוח.",
  "components.Search.placeholder": "חיפוש...",
  "components.Wysiwyg.collapse": "הסתר",
  "components.Wysiwyg.selectOptions.H1": "כותרת H1",
  "components.Wysiwyg.selectOptions.H2": "כותרת H2",
  "components.Wysiwyg.selectOptions.H3": "כותרת H3",
  "components.Wysiwyg.selectOptions.H4": "כותרת H4",
  "components.Wysiwyg.selectOptions.H5": "כותרת H5",
  "components.Wysiwyg.selectOptions.H6": "כותרת H6",
  "components.Wysiwyg.selectOptions.title": "הוסף כותרת",
  "components.WysiwygBottomControls.charactersIndicators": "תווים",
  "components.WysiwygBottomControls.fullscreen": "הצג",
  "components.WysiwygBottomControls.uploadFiles": "גרור ושחרר קבצים, הדבק מהלוח או {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "בחר אותם",
  "components.popUpWarning.button.cancel": "בטל",
  "components.popUpWarning.button.confirm": "אשר",
  "components.popUpWarning.message": "האם אתה בטוח שברצונך למחוק זאת?",
  "components.popUpWarning.title": "נא לאשר",
  "form.button.done": "הושלם",
  "global.prompt.unsaved": "האם אתה בטוח שברצונך לעזוב את עמוד? כל השינויים ילכו לאיבוד",
  "notification.contentType.relations.conflict": "לסוג התוכן יש יחסים סותרים",
  "notification.error": "התרחשה שגיאה",
  "notification.error.layout": "לא ניתן היה לאחזר את הפריסה",
  "notification.form.error.fields": "הטופס מכיל מספר שגיאות",
  "notification.form.success.fields": "שינויים נשמרו",
  "notification.link-copied": "הקישור הועתק ללוח",
  "notification.permission.not-allowed-read": "You are not allowed to see this document",
  "notification.success.delete": "הפריט נמחק",
  "notification.success.saved": "נשמר",
  "notification.version.update.message": "גרסה חדשה של סטראפי זמינה!",
  or,
  "request.error.model.unknown": "המודל הזה לא קיים"
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  he as default,
  or
};
//# sourceMappingURL=he-C3w9omDw-IEITR2MX.js.map
