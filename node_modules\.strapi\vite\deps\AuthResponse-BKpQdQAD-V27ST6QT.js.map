{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/utils/cookies.ts", "../../../@strapi/admin/ee/admin/src/pages/AuthResponse.tsx"], "sourcesContent": ["export const getCookieValue = (name: string) => {\n  let result = null;\n  const cookieArray = document.cookie.split(';');\n  cookieArray.forEach((cookie) => {\n    const [key, value] = cookie.split('=').map((item) => item.trim());\n    if (key.trim() === name) {\n      result = decodeURIComponent(value);\n    }\n  });\n  return result;\n};\n\nexport const deleteCookie = (name: string) => {\n  // Set the cookie to expire in the past\n  document.cookie = name + '=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;';\n};\n", "import * as React from 'react';\n\nimport { useIntl } from 'react-intl';\nimport { useNavigate, useMatch } from 'react-router-dom';\n\nimport { Page } from '../../../../admin/src/components/PageHelpers';\nimport { useTypedDispatch } from '../../../../admin/src/core/store/hooks';\nimport { login } from '../../../../admin/src/reducer';\nimport { getCookieValue, deleteCookie } from '../utils/cookies';\n\nconst AuthResponse = () => {\n  const match = useMatch('/auth/login/:authResponse');\n  const { formatMessage } = useIntl();\n  const navigate = useNavigate();\n  const dispatch = useTypedDispatch();\n\n  const redirectToOops = React.useCallback(() => {\n    navigate({\n      pathname: '/auth/oops',\n      search: `?info=${encodeURIComponent(\n        formatMessage({\n          id: 'Auth.form.button.login.providers.error',\n          defaultMessage: 'We cannot connect you through the selected provider.',\n        })\n      )}`,\n    });\n  }, [navigate, formatMessage]);\n\n  React.useEffect(() => {\n    if (match?.params.authResponse === 'error') {\n      redirectToOops();\n    }\n\n    if (match?.params.authResponse === 'success') {\n      const jwtToken = getCookieValue('jwtToken');\n\n      if (jwtToken) {\n        dispatch(\n          login({\n            token: jwtToken,\n          })\n        );\n\n        deleteCookie('jwtToken');\n\n        navigate('/auth/login');\n      } else {\n        redirectToOops();\n      }\n    }\n  }, [dispatch, match, redirectToOops, navigate]);\n\n  return <Page.Loading />;\n};\n\nexport { AuthResponse };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAa,IAAA,iBAAiB,CAAC,SAAiB;AAC9C,MAAI,SAAS;AACb,QAAM,cAAc,SAAS,OAAO,MAAM,GAAG;AACjC,cAAA,QAAQ,CAAC,WAAW;AAC9B,UAAM,CAAC,KAAK,KAAK,IAAI,OAAO,MAAM,GAAG,EAAE,IAAI,CAAC,SAAS,KAAK,KAAA,CAAM;AAC5D,QAAA,IAAI,KAAK,MAAM,MAAM;AACvB,eAAS,mBAAmB,KAAK;IAAA;EACnC,CACD;AACM,SAAA;AACT;AAEa,IAAA,eAAe,CAAC,SAAiB;AAE5C,WAAS,SAAS,OAAO;AAC3B;ACLA,IAAM,eAAe,MAAM;AACnB,QAAA,QAAQ,SAAS,2BAA2B;AAC5C,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,WAAW,YAAY;AAC7B,QAAM,WAAW,iBAAiB;AAE5B,QAAA,iBAAuB,kBAAY,MAAM;AACpC,aAAA;MACP,UAAU;MACV,QAAQ,SAAS;QACf,cAAc;UACZ,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IAAA,CACF;EAAA,GACA,CAAC,UAAU,aAAa,CAAC;AAE5B,EAAM,gBAAU,MAAM;AAChB,SAAA,+BAAO,OAAO,kBAAiB,SAAS;AAC3B,qBAAA;IAAA;AAGb,SAAA,+BAAO,OAAO,kBAAiB,WAAW;AACtC,YAAA,WAAW,eAAe,UAAU;AAE1C,UAAI,UAAU;AACZ;UACE,MAAM;YACJ,OAAO;UAAA,CACR;QAAA;AAGH,qBAAa,UAAU;AAEvB,iBAAS,aAAa;MAAA,OACjB;AACU,uBAAA;MAAA;IACjB;EACF,GACC,CAAC,UAAU,OAAO,gBAAgB,QAAQ,CAAC;AAEvC,aAAA,wBAAC,KAAK,SAAL,CAAA,CAAa;AACvB;", "names": []}