import{au as F,a as N,al as _,a5 as q,r as k,ba as O,bO as G,ax as L,e as K,at as U,c as $,m as e,n as h,bf as D,bg as H,L as Q,w as C,J as z,s as J,G as m,aO as w,i as Y,l as S,k as M,bR as W}from"./strapi-YzJfjJ2z.js";import{u as X,a as Z,b as ee}from"./transferTokens-CXTFej3W-kOI9Cp4M.js";import{T as f}from"./constants-CRj0ViV1-Q2dfXdfa.js";import{F as se,T as te,a as ne,b as re,L as ae,c as ie}from"./TokenTypeSelect-Cr8pc-yV-C65ZkGgP.js";import"./index-CJ4ZBILS.js";import"./index-BRVyLNfZ.js";const oe=Y().shape({name:S().max(100).required(M.required.id),description:S().nullable(),lifespan:W().integer().min(0).nullable().defined(M.required.id),permissions:S().required(M.required.id)}),le=()=>{const{formatMessage:a}=F(),{toggleNotification:r}=N(),d=_(),{state:u}=q(),[t,l]=k.useState(u&&"accessKey"in u.transferToken?{...u.transferToken}:null),{trackUsage:o}=O();G("EditView",n=>n.setCurrentStep);const x=L(n=>n.admin_app.permissions.settings?.["transfer-tokens"]),{allowedActions:{canCreate:y,canUpdate:R,canRegenerate:v}}=K(x),g=U("/settings/transfer-tokens/:id")?.params?.id,i=g==="create",{_unstableFormatAPIError:T,_unstableFormatValidationErrors:I}=$();k.useEffect(()=>{o(i?"didAddTokenFromList":"didEditTokenFromList",{tokenType:f})},[i,o]);const{data:j,error:E}=X(g,{skip:i||t!==null||!g});k.useEffect(()=>{E&&r({type:"danger",message:T(E)})},[E,T,r]),k.useEffect(()=>{j&&l(j)},[j]);const[A]=Z(),[V]=ee(),B=async(n,c)=>{o(i?"willCreateToken":"willEditToken",{tokenType:f});const p=n.permissions.split("-");if((s=>s.length===1?s[0]==="push"||s[0]==="pull":s[0]==="push"&&s[1]==="pull")(p))try{if(i){const s=await A({...n,lifespan:n?.lifespan&&n.lifespan!=="0"?parseInt(n.lifespan.toString(),10):null,permissions:p});if("error"in s){w(s.error)&&s.error.name==="ValidationError"?c.setErrors(I(s.error)):r({type:"danger",message:T(s.error)});return}l(s.data),r({type:"success",message:a({id:"notification.success.transfertokencreated",defaultMessage:"Transfer Token successfully created"})}),o("didCreateToken",{type:t?.permissions,tokenType:f}),d(`../transfer-tokens/${s.data.id.toString()}`,{replace:!0,state:{transferToken:s.data}})}else{const s=await V({id:g,name:n.name,description:n.description,permissions:p});if("error"in s){w(s.error)&&s.error.name==="ValidationError"?c.setErrors(I(s.error)):r({type:"danger",message:T(s.error)});return}l(s.data),r({type:"success",message:a({id:"notification.success.transfertokenedited",defaultMessage:"Transfer Token successfully edited"})}),o("didEditToken",{type:t?.permissions,tokenType:f})}}catch{r({type:"danger",message:a({id:"notification.error",defaultMessage:"Something went wrong"})})}},P=R&&!i||y&&i;return!i&&!t?e.jsx(h.Loading,{}):e.jsxs(h.Main,{children:[e.jsx(h.Title,{children:a({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Transfer Tokens"})}),e.jsx(D,{validationSchema:oe,validateOnChange:!1,initialValues:{name:t?.name||"",description:t?.description||"",lifespan:t?.lifespan||null,permissions:t?.permissions.join("-")??""},enableReinitialize:!0,onSubmit:(n,c)=>B(n,c),children:({errors:n,handleChange:c,isSubmitting:p,values:b})=>e.jsxs(H,{children:[e.jsx(se,{title:{id:"Settings.transferTokens.createPage.title",defaultMessage:"TokenCreate Transfer Token"},token:t,setToken:l,canEditInputs:P,canRegenerate:v,isSubmitting:p,regenerateUrl:"/admin/transfer/tokens/"}),e.jsx(Q.Content,{children:e.jsxs(C,{direction:"column",alignItems:"stretch",gap:6,children:[t&&!!t?.name&&"accessKey"in t&&e.jsx(te,{token:t.accessKey,tokenType:f}),e.jsx(ce,{errors:n,onChange:c,canEditInputs:P,isCreating:i,values:b,transferToken:t})]})})]})})]})},he=()=>{const a=L(r=>r.admin_app.permissions.settings?.["transfer-tokens"].read);return e.jsx(h.Protect,{permissions:a,children:e.jsx(le,{})})},ce=({errors:a={},onChange:r,canEditInputs:d,isCreating:u,values:t,transferToken:l={}})=>{const{formatMessage:o}=F(),x=[{value:"push",label:{id:"Settings.transferTokens.types.push",defaultMessage:"Push"}},{value:"pull",label:{id:"Settings.transferTokens.types.pull",defaultMessage:"Pull"}},{value:"push-pull",label:{id:"Settings.transferTokens.types.push-pull",defaultMessage:"Full Access"}}];return e.jsx(z,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(C,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(J,{variant:"delta",tag:"h2",children:o({id:"global.details",defaultMessage:"Details"})}),e.jsxs(m.Root,{gap:5,children:[e.jsx(m.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(ne,{error:a.name,value:t.name,canEditInputs:d,onChange:r})},"name"),e.jsx(m.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(re,{error:a.description,value:t.description,canEditInputs:d,onChange:r})},"description"),e.jsx(m.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(ae,{isCreating:u,error:a.lifespan,value:t.lifespan,onChange:r,token:l})},"lifespan"),e.jsx(m.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(ie,{name:"permissions",value:t.permissions,error:a.permissions,label:{id:"Settings.tokens.form.type",defaultMessage:"Token type"},onChange:y=>{r({target:{name:"permissions",value:y}})},options:x,canEditInputs:d})},"permissions")]})]})})};export{le as EditView,he as ProtectedEditView};
