const a={"BoundRoute.title":"<PERSON><PERSON> laluan ke","EditForm.inputSelect.description.role":"Ini akan meletakkan peranan yang dipilih pada pengguna baru.","EditForm.inputSelect.label.role":"<PERSON><PERSON>n asal untuk pengguna yang disahkan","EditForm.inputToggle.description.email":"Tidak membenarkan pengguna membuat beberapa akaun menggunakan alamat e-mel yang sama dengan pembekal pengesahan yang berbeza.","EditForm.inputToggle.description.email-confirmation":"<PERSON><PERSON><PERSON><PERSON> <PERSON> (ON), pengguna berdaftar baru akan menerima e-mel pengesahan.","EditForm.inputToggle.description.email-confirmation-redirection":"Setelah mengesahkan e-mel anda, pilih di mana anda akan di redirect kan.","EditForm.inputToggle.description.email-reset-password":"URL halaman kata laluan tetapan semula untuk aplikasi anda","EditForm.inputToggle.description.sign-up":"<PERSON><PERSON><PERSON><PERSON> (MATI), proses pendaftaran tidak dibenarkan. Tidak ada yang boleh melanggan lagi tidak kira pembekal telah dipakai.","EditForm.inputToggle.label.email":"Satu akaun setiap alamat e-mel","EditForm.inputToggle.label.email-confirmation":"Aktifkan pengesahan e-mel","EditForm.inputToggle.label.email-confirmation-redirection":"Redirection url","EditForm.inputToggle.label.email-reset-password":"Halaman tetapan semula kata laluan","EditForm.inputToggle.label.sign-up":"Aktifkan pendaftaran","HeaderNav.link.advancedSettings":"Tetapan lanjut","HeaderNav.link.emailTemplates":"Templat e-mel","HeaderNav.link.providers":"Pembekal","Plugin.permissions.plugins.description":"Pilih arahan yang dibenarkan untuk plugin {name}.","Plugins.header.description":"Hanya arahan yang terpasang dengan laluan sahaja yang tersenarai di bawah.","Plugins.header.title":"Keizinan","Policies.header.hint":"Pilih tindakan aplikasi atau plugin dan klik pada ikon gear untuk melihat laluan yang terpasang","Policies.header.title":"Tetapan lanjut","PopUpForm.Email.email_templates.inputDescription":"Sekiranya anda tidak pasti cara menggunakan pemboleh ubah, {link} ","PopUpForm.Email.options.from.email.label":"E-mel penghantar","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Nama pengirim","PopUpForm.Email.options.from.name.placeholder":"Kai Doe","PopUpForm.Email.options.message.label":"Mesej","PopUpForm.Email.options.object.label":"Subjek","PopUpForm.Email.options.response_email.label":"E-mel jawapan","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"Sekiranya dinyahaktifkan, pengguna tidak akan dapat menggunakan pembekal ini.","PopUpForm.Providers.enabled.label":"Aktifkan","PopUpForm.Providers.key.label":"ID Pelanggan","PopUpForm.Providers.key.placeholder":"TEKS","PopUpForm.Providers.redirectURL.front-end.label":"URL pengubah hala ke aplikasi 'front-end' anda","PopUpForm.Providers.secret.label":"Client Secret","PopUpForm.Providers.secret.placeholder":"TEKS","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Edit Templat E-mel","notification.success.submit":"Tetapan telah dikemas kini","plugin.description.long":"Lindungi API anda dengan proses pengesahan penuh berdasarkan JWT. Plugin ini juga dilengkapi dengan strategi ACL yang membolehkan anda mengurus pengizinan antara kumpulan pengguna.","plugin.description.short":"Lindungi API anda dengan proses pengesahan penuh berdasarkan JWT"};export{a as default};
