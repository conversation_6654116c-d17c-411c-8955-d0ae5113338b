{"version": 3, "sources": ["../../../@strapi/content-manager/dist/_chunks/pt-BsaFvS8-.mjs"], "sourcesContent": ["const pageNotFound = \"Página não encontrada\";\nconst pt = {\n  \"EditRelations.title\": \"Dados relacionais\",\n  \"components.AddFilterCTA.add\": \"Filtros\",\n  \"components.AddFilterCTA.hide\": \"Filtros\",\n  \"components.DraggableAttr.edit\": \"Clique para editar\",\n  \"components.EmptyAttributesBlock.button\": \"Ir para a página de configurações\",\n  \"components.EmptyAttributesBlock.description\": \"Pode alterar as configurações\",\n  \"components.FilterOptions.button.apply\": \"Aplicar\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Aplicar\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Limpar tudo\",\n  \"components.FiltersPickWrapper.PluginHeader.description\": \"Definir as condições a serem aplicadas para filtrar as entradas\",\n  \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filtros\",\n  \"components.FiltersPickWrapper.hide\": \"Esconder\",\n  \"components.LimitSelect.itemsPerPage\": \"Itens por página\",\n  \"components.Search.placeholder\": \"Procurar por uma entrada...\",\n  \"components.TableDelete.delete\": \"Apagar tudo\",\n  \"components.TableDelete.deleteSelected\": \"Excluir selecionado\",\n  \"components.TableEmpty.withFilters\": \"Não há {contentType} com os filtros aplicados...\",\n  \"components.TableEmpty.withSearch\": \"Não há {contentType} correspondente à pesquisa ({search})...\",\n  \"components.TableEmpty.withoutFilter\": \"Não há {contentType}...\",\n  \"containers.Edit.addAnItem\": \"Adicionar uma entrada...\",\n  \"containers.Edit.clickToJump\": \"Clique para saltar para a entrada\",\n  \"containers.Edit.delete\": \"Apagar\",\n  \"containers.Edit.editing\": \"Editando...\",\n  \"containers.Edit.reset\": \"Restabelecer\",\n  \"containers.Edit.returnList\": \"Retornar à lista\",\n  \"containers.Edit.seeDetails\": \"Detalhes\",\n  \"containers.Edit.submit\": \"Guardar\",\n  \"containers.Home.introduction\": \"Para editar suas entradas, acesse o link específico no menu à esquerda. Esta extensão não tem uma maneira correcta de editar configurações e ainda está em desenvolvimento activo.\",\n  \"containers.Home.pluginHeaderDescription\": \"Administre as suas entradas através de uma interface poderosa e bonita.\",\n  \"containers.Home.pluginHeaderTitle\": \"Gestor de conteúdos\",\n  \"containers.List.errorFetchRecords\": \"Erro\",\n  \"containers.list.displayedFields\": \"Campos visíveis\",\n  \"containers.SettingPage.attributes\": \"Campos de atributos\",\n  \"containers.SettingPage.attributes.description\": \"Definir a ordem dos atributos\",\n  \"containers.SettingPage.editSettings.description\": \"Agarre & solte os campos para criar o layout\",\n  \"containers.SettingPage.editSettings.title\": \"Edite (configurações)\",\n  \"containers.SettingPage.listSettings.title\": \"Lista (configurações)\",\n  \"containers.SettingPage.relations\": \"Campos Relacionais\",\n  \"containers.SettingsPage.Block.contentType.description\": \"Configurar configurações específicas\",\n  \"containers.SettingsPage.Block.generalSettings.title\": \"Geral\",\n  \"emptyAttributes.title\": \"Ainda não há campos\",\n  \"error.attribute.key.taken\": \"Este valor já existe\",\n  \"error.attribute.sameKeyAndName\": \"Não pode ser igual\",\n  \"error.attribute.taken\": \"O nome deste campo já existe\",\n  \"error.contentTypeName.taken\": \"Este nome já existe\",\n  \"error.model.fetch\": \"Ocorreu um erro durante a configuração dos modelos de pesquisa.\",\n  \"error.record.create\": \"Ocorreu um erro durante a criação de registo.\",\n  \"error.record.delete\": \"Ocorreu um erro durante a exclusão do registo.\",\n  \"error.record.fetch\": \"Ocorreu um erro durante o registo de pesquisa.\",\n  \"error.record.update\": \"Ocorreu um erro durante a actualização do registo.\",\n  \"error.records.count\": \"Ocorreu um erro durante a contagem de registos a pesquisar.\",\n  \"error.records.fetch\": \"Ocorreu um erro durante os registos de busca.\",\n  \"error.schema.generation\": \"Ocorreu um erro durante a geração de esquemas.\",\n  \"error.validation.json\": \"Isto não corresponde com o formato JSON\",\n  \"error.validation.max\": \"O valor é muito alto.\",\n  \"error.validation.maxLength\": \"O valor é muito longo.\",\n  \"error.validation.min\": \"O valor é muito baixo.\",\n  \"error.validation.minLength\": \"O valor é muito curto.\",\n  \"error.validation.minSupMax\": \"Não pode ser superior\",\n  \"error.validation.regex\": \"Este valor não corresponde ao regex.\",\n  \"error.validation.required\": \"O valor desta entrada é obrigatória.\",\n  \"form.Input.bulkActions\": \"Activar acções em massa\",\n  \"form.Input.defaultSort\": \"Ordenação por defeito\",\n  \"form.Input.description\": \"Descrição\",\n  \"form.Input.description.placeholder\": \"Nome a exibir no perfil\",\n  \"form.Input.editable\": \"Campo editável\",\n  \"form.Input.filters\": \"Activar filtros\",\n  \"form.Input.label\": \"Legenda\",\n  \"form.Input.label.inputDescription\": \"Este valor sobrepõe a legenda visível no cabeçalho da tabela\",\n  \"form.Input.pageEntries\": \"Entradas por página\",\n  \"form.Input.placeholder\": \"Preenchimento\",\n  \"form.Input.placeholder.placeholder\": \"O meu valor espetacular\",\n  \"form.Input.search\": \"Activar pesquisa\",\n  \"form.Input.search.field\": \"Active a pesquisa neste campo\",\n  \"form.Input.sort.field\": \"Active a ordenação neste campo\",\n  \"notification.error.displayedFields\": \"Precisa de ter pelo menos um campo visível\",\n  \"notification.error.relationship.fetch\": \"Ocorreu um erro durante a pesquisa da relação.\",\n  \"notification.info.SettingPage.disableSort\": \"Precisa de ter pelo menos um atributo com ordenação activa\",\n  pageNotFound,\n  \"plugin.description.long\": \"Maneira rápida de ver, editar e excluir os dados da sua base de dados.\",\n  \"plugin.description.short\": \"Maneira rápida de ver, editar e excluir os dados da sua base de dados.\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"Tem a certeza de que pretende apagar esta entrada?\",\n  \"popUpWarning.bodyMessage.contentType.delete.all\": \"Tem a certeza de que pretende apagar estas entradas?\",\n  \"popUpWarning.warning.cancelAllSettings\": \"Tem a certeza de que quer cancelar as alterações?\",\n  \"popUpWarning.warning.updateAllSettings\": \"Isto vai alterar todas as suas configurações\",\n  \"success.record.delete\": \"Apagado\",\n  \"success.record.save\": \"Guardado\"\n};\nexport {\n  pt as default,\n  pageNotFound\n};\n//# sourceMappingURL=pt-BsaFvS8-.mjs.map\n"], "mappings": ";;;AAAA,IAAM,eAAe;AACrB,IAAM,KAAK;AAAA,EACT,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,6CAA6C;AAAA,EAC7C,oCAAoC;AAAA,EACpC,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C;AAAA,EACA,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,uBAAuB;AACzB;", "names": []}