import {
  SETTINGS_SCHEMA
} from "./chunk-FHDIK6Y5.js";
import {
  getTimezones,
  useGetReleaseSettingsQuery,
  useUpdateReleaseSettingsMutation
} from "./chunk-6NLSGXWT.js";
import {
  useIntl
} from "./chunk-DC3UNANX.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-D63J2BWQ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Form2 as Form,
  Layouts,
  useField
} from "./chunk-ELTZWS66.js";
import {
  Page,
  useAPIErrorHandler,
  useNotification,
  useRBAC
} from "./chunk-4C2ZQ5OG.js";
import {
  Button,
  Combobox,
  Field,
  Flex,
  Grid,
  Option,
  Typography,
  isFetchError,
  useSelector
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$4p
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-releases/dist/_chunks/ReleasesSettingsPage-BMgLwqci.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var useTypedSelector = useSelector;
var ReleasesSettingsPage = () => {
  const { formatMessage } = useIntl();
  const { formatAPIError } = useAPIErrorHandler();
  const { toggleNotification } = useNotification();
  const { data, isLoading: isLoadingSettings } = useGetReleaseSettingsQuery();
  const [updateReleaseSettings, { isLoading: isSubmittingForm }] = useUpdateReleaseSettingsMutation();
  const permissions = useTypedSelector(
    (state) => {
      var _a;
      return (_a = state.admin_app.permissions["settings"]) == null ? void 0 : _a["releases"];
    }
  );
  const {
    allowedActions: { canUpdate }
  } = useRBAC(permissions);
  const { timezoneList } = getTimezones(/* @__PURE__ */ new Date());
  const handleSubmit = async (body) => {
    const { defaultTimezone } = body;
    const isBodyTimezoneValid = timezoneList.some((timezone) => timezone.value === defaultTimezone);
    const newBody = !defaultTimezone || !isBodyTimezoneValid ? { defaultTimezone: null } : { ...body };
    try {
      const response = await updateReleaseSettings(newBody);
      if ("data" in response) {
        toggleNotification({
          type: "success",
          message: formatMessage({
            id: "content-releases.pages.Settings.releases.setting.default-timezone-notification-success",
            defaultMessage: "Default timezone updated."
          })
        });
      } else if (isFetchError(response.error)) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(response.error)
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatMessage({ id: "notification.error", defaultMessage: "An error occurred" })
        });
      }
    } catch (error) {
      toggleNotification({
        type: "danger",
        message: formatMessage({ id: "notification.error", defaultMessage: "An error occurred" })
      });
    }
  };
  if (isLoadingSettings) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Layouts.Root, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      {
        name: "Releases"
      }
    ) }),
    (0, import_jsx_runtime.jsx)(Page.Main, { "aria-busy": isLoadingSettings, tabIndex: -1, children: (0, import_jsx_runtime.jsx)(
      Form,
      {
        method: "PUT",
        initialValues: {
          defaultTimezone: data == null ? void 0 : data.data.defaultTimezone
        },
        onSubmit: handleSubmit,
        validationSchema: SETTINGS_SCHEMA,
        children: ({ modified, isSubmitting }) => {
          return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
            (0, import_jsx_runtime.jsx)(
              Layouts.Header,
              {
                primaryAction: canUpdate ? (0, import_jsx_runtime.jsx)(
                  Button,
                  {
                    disabled: !modified || isSubmittingForm,
                    loading: isSubmitting,
                    startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
                    type: "submit",
                    children: formatMessage({
                      id: "global.save",
                      defaultMessage: "Save"
                    })
                  }
                ) : null,
                title: formatMessage({
                  id: "content-releases.pages.Settings.releases.title",
                  defaultMessage: "Releases"
                }),
                subtitle: formatMessage({
                  id: "content-releases.pages.Settings.releases.description",
                  defaultMessage: "Create and manage content updates"
                })
              }
            ),
            (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(
              Flex,
              {
                direction: "column",
                background: "neutral0",
                alignItems: "stretch",
                padding: 6,
                gap: 6,
                shadow: "filterShadow",
                hasRadius: true,
                children: [
                  (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
                    id: "content-releases.pages.Settings.releases.preferences.title",
                    defaultMessage: "Preferences"
                  }) }),
                  (0, import_jsx_runtime.jsx)(Grid.Root, { children: (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(TimezoneDropdown, {}) }) })
                ]
              }
            ) })
          ] });
        }
      }
    ) })
  ] });
};
var TimezoneDropdown = () => {
  const permissions = useTypedSelector(
    (state) => {
      var _a;
      return (_a = state.admin_app.permissions["settings"]) == null ? void 0 : _a["releases"];
    }
  );
  const {
    allowedActions: { canUpdate }
  } = useRBAC(permissions);
  const { formatMessage } = useIntl();
  const { timezoneList } = getTimezones(/* @__PURE__ */ new Date());
  const field = useField("defaultTimezone");
  return (0, import_jsx_runtime.jsxs)(
    Field.Root,
    {
      name: "defaultTimezone",
      hint: formatMessage({
        id: "content-releases.pages.Settings.releases.timezone.hint",
        defaultMessage: "The timezone of every release can still be changed individually. "
      }),
      error: field.error,
      children: [
        (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
          id: "content-releases.pages.Settings.releases.timezone.label",
          defaultMessage: "Default timezone"
        }) }),
        (0, import_jsx_runtime.jsx)(
          Combobox,
          {
            autocomplete: { type: "list", filter: "contains" },
            onChange: (value) => field.onChange("defaultTimezone", value),
            onTextValueChange: (value) => field.onChange("defaultTimezone", value),
            onClear: () => field.onChange("defaultTimezone", ""),
            value: field.value,
            disabled: !canUpdate,
            children: timezoneList.map((timezone) => (0, import_jsx_runtime.jsx)(Option, { value: timezone.value, children: timezone.value.replace(/&/, " ") }, timezone.value))
          }
        ),
        (0, import_jsx_runtime.jsx)(Field.Hint, {}),
        (0, import_jsx_runtime.jsx)(Field.Error, {})
      ]
    }
  );
};
var ProtectedReleasesSettingsPage = () => {
  const permissions = useTypedSelector(
    (state) => {
      var _a, _b;
      return (_b = (_a = state.admin_app.permissions["settings"]) == null ? void 0 : _a["releases"]) == null ? void 0 : _b.read;
    }
  );
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions, children: (0, import_jsx_runtime.jsx)(ReleasesSettingsPage, {}) });
};
export {
  ProtectedReleasesSettingsPage
};
//# sourceMappingURL=ReleasesSettingsPage-BMgLwqci-AHLMEXJI.js.map
