import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/id-cH3Ovozx.mjs
var Analytics = "Analisis";
var Documentation = "Dokumentasi";
var Email = "Email";
var Password = "Kada sandi";
var Provider = "Penyedia";
var ResetPasswordToken = "Setel Ulang Token Sandi";
var Role = "Peran";
var Username = "Nama pengguna";
var Users = "Pengguna";
var id = {
  Analytics,
  "Auth.components.Oops.text": "Akun anda telah disuspen",
  "Auth.form.button.forgot-password": "<PERSON><PERSON> Email",
  "Auth.form.button.go-home": "KE BERANDA",
  "Auth.form.button.login": "Masuk",
  "Auth.form.button.register": "AYO MULAI",
  "Auth.form.confirmPassword.label": "Konfirmasi Kata sandi",
  "Auth.form.email.label": "Email",
  "Auth.form.email.placeholder": "<EMAIL>",
  "Auth.form.error.blocked": "<PERSON>kun anda diblokir administator.",
  "Auth.form.error.code.provide": "Kode yang anda masukkan salah.",
  "Auth.form.error.confirmed": "Email anda belum dikonfirmasi.",
  "Auth.form.error.email.invalid": "Email tidak valid.",
  "Auth.form.error.email.provide": "Harap berikan nama pengguna atau email.",
  "Auth.form.error.email.taken": "Email sudah digunakan.",
  "Auth.form.error.invalid": "Pengenal atau kata sandi tidak valid.",
  "Auth.form.error.params.provide": "Parameter yang salah.",
  "Auth.form.error.password.format": "Kata sandi Anda tidak boleh mengandung simbol `$` lebih dari tiga kali.",
  "Auth.form.error.password.local": "Pengguna ini tidak pernah menyetel kata sandi lokal, harap masuk melalui penyedia yang digunakan selama pembuatan akun.",
  "Auth.form.error.password.matching": "Sandi tidak cocok.",
  "Auth.form.error.password.provide": "Harap berikan sandi Anda.",
  "Auth.form.error.ratelimit": "Terlalu banyak upaya, coba lagi dalam satu menit.",
  "Auth.form.error.user.not-exist": "Email ini tidak terdaftar.",
  "Auth.form.error.username.taken": "Nama pengguna sudah dipakai.",
  "Auth.form.firstname.label": "Nama depan",
  "Auth.form.firstname.placeholder": "Kai",
  "Auth.form.forgot-password.email.label": "Masukkan email Anda",
  "Auth.form.forgot-password.email.label.success": "Email berhasil dikirim ke",
  "Auth.form.lastname.label": "Nama belakang",
  "Auth.form.lastname.placeholder": "Doe",
  "Auth.form.register.news.label": "Terus kabari saya tentang fitur baru dan peningkatan yang akan datang (dengan melakukan ini, Anda menerima {syarat} dan {kebijakan}).",
  "Auth.form.rememberMe.label": "Ingat saya",
  "Auth.form.username.label": "Nama pengguna",
  "Auth.form.username.placeholder": "Kai Doe",
  "Auth.link.forgot-password": "Lupa kata sandi?",
  "Auth.link.ready": "Siap masuk?",
  "Auth.link.signin": "Masuk",
  "Auth.link.signin.account": "Sudak memiliki akun?",
  "Auth.privacy-policy-agreement.policy": "kebijakan privasi",
  "Auth.privacy-policy-agreement.terms": "istilah",
  "Content Manager": "Pengelola Konten",
  "Content Type Builder": "Pembuat Tipe Konten",
  Documentation,
  Email,
  "Files Upload": "Unggah File",
  "HomePage.head.title": "Beranda",
  "HomePage.roadmap": "Lihat roadmap kami",
  "HomePage.welcome.congrats": "Selamat!",
  "HomePage.welcome.congrats.content": "Anda masuk sebagai administrator pertama. Untuk mengetahui fitur-fitur canggih yang disediakan Strapi,",
  "HomePage.welcome.congrats.content.bold": "kami menyarankan Anda untuk membuat Jenis-Koleksi pertama Anda.",
  "Media Library": "Pustaka Media",
  "New entry": "Masukan baru",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "Peran & Izin",
  "Roles.ListPage.notification.delete-all-not-allowed": "Beberapa peran tidak dapat dihapus karena dikaitkan dengan pengguna",
  "Roles.ListPage.notification.delete-not-allowed": "Peran tidak dapat dihapus jika dikaitkan dengan pengguna",
  "Roles.components.List.empty.withSearch": "Tidak ada peran yang sesuai dengan pencarian ({search}) ...",
  "Settings.PageTitle": "Pengaturan - {name}",
  "Settings.error": "Error",
  "Settings.global": "Pengaturan Global",
  "Settings.permissions": "Panel administrasi",
  "Settings.permissions.category": "Setelan izin untuk {category}",
  "Settings.permissions.conditions.anytime": "Kapan saja",
  "Settings.permissions.conditions.apply": "Terapkan",
  "Settings.permissions.conditions.can": "Bisa",
  "Settings.permissions.conditions.conditions": "Tentukan kondisi",
  "Settings.permissions.conditions.links": "Tautan",
  "Settings.permissions.conditions.no-actions": "Tidak ada tindakan",
  "Settings.permissions.conditions.or": "ATAU",
  "Settings.permissions.conditions.when": "Ketika",
  "Settings.permissions.users.create": "Buat pengguna Baru",
  "Settings.permissions.users.email": "Email",
  "Settings.permissions.users.firstname": "Nama depan",
  "Settings.permissions.users.lastname": "Nama belakang",
  "Settings.roles.create.description": "Tentukan hak yang diberikan untuk peran tersebut",
  "Settings.roles.create.title": "Buat peran",
  "Settings.roles.created": "Peran dibuat",
  "Settings.roles.edit.title": "Ubah peran",
  "Settings.roles.form.button.users-with-role": "Users with this role",
  "Settings.roles.form.created": "Dibuat",
  "Settings.roles.form.description": "Nama dan deskripsi peran",
  "Settings.roles.form.permissions.attributesPermissions": "Izin bidang",
  "Settings.roles.form.permissions.create": "Buat",
  "Settings.roles.form.permissions.delete": "Hapus",
  "Settings.roles.form.permissions.publish": "Terbitkan",
  "Settings.roles.form.permissions.read": "Baca",
  "Settings.roles.form.permissions.update": "Perbarui",
  "Settings.roles.list.button.add": "Tambah peran baru",
  "Settings.roles.list.description": "Daftar peran",
  "Settings.roles.title.singular": "peran",
  "Settings.webhooks.create": "Buat webhook",
  "Settings.webhooks.create.header": "Buat tajuk baru",
  "Settings.webhooks.created": "Webhook dibuat",
  "Settings.webhooks.event.publish-tooltip": "Acara ini hanya ada untuk konten dengan sistem Draf / Terbit diaktifkan",
  "Settings.webhooks.events.create": "Buat",
  "Settings.webhooks.events.update": "Perbarui",
  "Settings.webhooks.form.events": "Acara",
  "Settings.webhooks.form.headers": "Header",
  "Settings.webhooks.form.url": "Url",
  "Settings.webhooks.key": "Kunci",
  "Settings.webhooks.list.button.add": "Buat webhook baru",
  "Settings.webhooks.list.description": "Dapatkan notifikasi perubahan POST.",
  "Settings.webhooks.list.empty.description": "Tambahkan yang pertama Anda ke daftar ini.",
  "Settings.webhooks.list.empty.link": "Lihat dokumentasi kami",
  "Settings.webhooks.list.empty.title": "Belum ada webhook",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "Webhooks",
  "Settings.webhooks.trigger": "Pemicu",
  "Settings.webhooks.trigger.cancel": "Batalkan pemicu",
  "Settings.webhooks.trigger.pending": "Menunggu…",
  "Settings.webhooks.trigger.save": "Harap simpan untuk memicu",
  "Settings.webhooks.trigger.success": "Sukses!",
  "Settings.webhooks.trigger.success.label": "Pemicu sukses",
  "Settings.webhooks.trigger.test": "Test-pemicu",
  "Settings.webhooks.trigger.title": "Simpan sebelum Memicu",
  "Settings.webhooks.value": "Isi",
  Username,
  Users,
  "Users & Permissions": "Peran & Ijin",
  "Users.components.List.empty": "Tidak ada pengguna...",
  "Users.components.List.empty.withFilters": "Tidak ada pengguna dengan filter yang diterapkan...",
  "Users.components.List.empty.withSearch": "Tidak ada pengguna yang sesuai dengan pencarian ({search})...",
  "app.components.BlockLink.code": "Contoh kode",
  "app.components.Button.cancel": "Batal",
  "app.components.Button.reset": "Atur ulang",
  "app.components.ComingSoonPage.comingSoon": "Segera hadir",
  "app.components.DownloadInfo.download": "Unduhan sedang berlangsung...",
  "app.components.DownloadInfo.text": "Ini bisa memakan waktu satu menit. Terima kasih atas kesabaran Anda.",
  "app.components.EmptyAttributes.title": "Belum ada bidang",
  "app.components.HomePage.button.blog": "LIHAT LEBIH BANYAK DI BLOG",
  "app.components.HomePage.community": "Temukan komunitas di web",
  "app.components.HomePage.community.content": "Diskusikan dengan anggota tim, kontributor dan pengembang di saluran yang berbeda.",
  "app.components.HomePage.create": "Buat Jenis Konten pertama Anda",
  "app.components.HomePage.welcome": "Selamat bergabung!",
  "app.components.HomePage.welcome.again": "Selamat ",
  "app.components.HomePage.welcomeBlock.content": "Kami senang Anda menjadi bagian dari komunitas. Kami terus mencari masukan, jadi jangan ragu untuk mengirimkan DM kepada kami ",
  "app.components.HomePage.welcomeBlock.content.again": "Kami berharap Anda membuat kemajuan dalam proyek Anda ... Jangan ragu untuk membaca berita terbaru tentang Strapi. Kami memberikan yang terbaik untuk meningkatkan produk berdasarkan umpan balik Anda.",
  "app.components.HomePage.welcomeBlock.content.issues": "masalah.",
  "app.components.HomePage.welcomeBlock.content.raise": " atau naikkan ",
  "app.components.ImgPreview.hint": "Tarik & lepas file Anda ke area ini atau {browse} file untuk diupload",
  "app.components.ImgPreview.hint.browse": "telusuri",
  "app.components.InputFile.newFile": "Tambahkan file baru",
  "app.components.InputFileDetails.open": "Buka di tab baru",
  "app.components.InputFileDetails.originalName": "Nama asli:",
  "app.components.InputFileDetails.remove": "Hapus file ini",
  "app.components.InputFileDetails.size": "Ukuran:",
  "app.components.InstallPluginPage.Download.description": "Mungkin perlu beberapa detik untuk mengunduh dan memasang plugin.",
  "app.components.InstallPluginPage.Download.title": "Mengunduh...",
  "app.components.InstallPluginPage.description": "Perluas aplikasi Anda dengan mudah.",
  "app.components.LeftMenuFooter.help": "Bantuan",
  "app.components.LeftMenuFooter.poweredBy": "Dipersembahkan oleh ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "Jenis Koleksi",
  "app.components.LeftMenuLinkContainer.configuration": "Konfigurasi",
  "app.components.LeftMenuLinkContainer.general": "Umum",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "Belum ada plugin yang terpasang",
  "app.components.LeftMenuLinkContainer.plugins": "Plugin",
  "app.components.LeftMenuLinkContainer.singleTypes": "Jenis Tunggal",
  "app.components.ListPluginsPage.deletePlugin.description": "Mungkin perlu beberapa detik untuk mencopot pemasangan plugin.",
  "app.components.ListPluginsPage.deletePlugin.title": "Menghapus instalasi",
  "app.components.ListPluginsPage.description": "Daftar plugin yang diinstal dalam proyek.",
  "app.components.ListPluginsPage.head.title": "Daftar plugin",
  "app.components.Logout.logout": "Keluar",
  "app.components.Logout.profile": "Profil",
  "app.components.NotFoundPage.back": "Kembali ke beranda",
  "app.components.NotFoundPage.description": "Tidak Ditemukan",
  "app.components.Official": "Resmi",
  "app.components.Onboarding.label.completed": "% selesai",
  "app.components.Onboarding.title": "Panduan Memulai",
  "app.components.PluginCard.Button.label.download": "Unduh",
  "app.components.PluginCard.Button.label.install": "Sudah terpasang",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "Fitur autoReload harus diaktifkan. Silakan mulai aplikasi Anda dengan `yarn develop`.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Saya mengeri!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "Untuk alasan keamanan, plugin hanya dapat diunduh di lingkungan pengembangan.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "Mendownload tidak mungkin",
  "app.components.PluginCard.compatible": "Kompatibel dengan aplikasi Anda",
  "app.components.PluginCard.compatibleCommunity": "Kompatibel dengan komunitas",
  "app.components.PluginCard.more-details": "Keterangan lebih lanjut",
  "app.components.Users.MagicLink.connect": "Kirim tautan ini ke pengguna agar mereka dapat terhubung.",
  "app.components.Users.ModalCreateBody.block-title.details": "Detail",
  "app.components.Users.ModalCreateBody.block-title.roles": "Peran pengguna",
  "app.components.Users.SortPicker.button-label": "Urutkan",
  "app.components.Users.SortPicker.sortby.email_asc": "Email (A to Z)",
  "app.components.Users.SortPicker.sortby.email_desc": "Email (Z to A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "Nama depan (A to Z)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "Nama depan (Z to A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "Nama belakang (A to Z)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "Nama belakang (Z to A)",
  "app.components.Users.SortPicker.sortby.username_asc": "Nama pengguna (A to Z)",
  "app.components.Users.SortPicker.sortby.username_desc": "Nama pengguna (Z to A)",
  "app.components.listPlugins.button": "Tambah Plugin Baru",
  "app.components.listPlugins.title.none": "Tidak ada plugin terpasang",
  "app.components.listPluginsPage.deletePlugin.error": "Terjadi kesalahan saat mencopot pengaya",
  "app.containers.App.notification.error.init": "Terjadi kesalahan saat meminta API",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "Jika Anda tidak menerima tautan ini, harap hubungi administrator Anda.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "Perlu waktu beberapa menit untuk menerima tautan pemulihan kata sandi Anda.",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "Email terkirim",
  "app.containers.Users.EditPage.form.active.label": "Aktif",
  "app.containers.Users.EditPage.header.label": "Ubah {name}",
  "app.containers.Users.EditPage.header.label-loading": "Ubah pengguna",
  "app.containers.Users.EditPage.roles-bloc-title": "Peran yang diatribusikan",
  "app.containers.Users.ModalForm.footer.button-success": "Buat pengguna",
  "app.links.configure-view": "Konfigurasi tampilan",
  "app.static.links.cheatsheet": "Contekan",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "Tambahkan filter",
  "app.utils.defaultMessage": " ",
  "app.utils.errors.file-too-big.message": "File terlalu besar",
  "app.utils.filters": "Filter",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "Terbit",
  "app.utils.select-all": "Pilih semua",
  "app.utils.unpublish": "Batal terbit",
  "component.Input.error.validation.integer": "Nilainya harus berupa bilangan bulat",
  "components.AutoReloadBlocker.description": "Jalankan Strapi dengan salah satu dari perintah berikut:",
  "components.AutoReloadBlocker.header": "Fitur muat ulang diperlukan untuk plugin ini.",
  "components.ErrorBoundary.title": "Ada yang salah...",
  "components.Input.error.attribute.key.taken": "Nilai ini sudah ada",
  "components.Input.error.attribute.sameKeyAndName": "Tidak bisa sama",
  "components.Input.error.attribute.taken": "Nama bidang ini sudah ada",
  "components.Input.error.contain.lowercase": "Kata sandi harus mengandung setidaknya satu karakter huruf kecil",
  "components.Input.error.contain.number": "Kata sandi harus mengandung setidaknya satu angka",
  "components.Input.error.contain.uppercase": "Kata sandi harus mengandung setidaknya satu karakter huruf besar",
  "components.Input.error.contentTypeName.taken": "Nama ini sudah ada",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "Sandi tidak cocok",
  "components.Input.error.validation.email": "Ini bukan email",
  "components.Input.error.validation.json": "Ini tidak cocok dengan format JSON",
  "components.Input.error.validation.max": "Nilainya terlalu tinggi {max}.",
  "components.Input.error.validation.maxLength": "Nilainya terlalu panjang {max}.",
  "components.Input.error.validation.min": "Nilainya terlalu rendah {min}.",
  "components.Input.error.validation.minLength": "Nilainya terlalu pendek {min}.",
  "components.Input.error.validation.minSupMax": "Tidak bisa lebih unggul",
  "components.Input.error.validation.regex": "Nilainya tidak cocok dengan regex.",
  "components.Input.error.validation.required": "Nilai ini wajib diisi.",
  "components.Input.error.validation.unique": "Nilai ini sudah digunakan.",
  "components.InputSelect.option.placeholder": "Pilih di sini",
  "components.ListRow.empty": "Tidak ada data untuk ditampilkan.",
  "components.OverlayBlocker.description": "Anda menggunakan fitur yang membutuhkan server untuk dimulai ulang. Harap tunggu sampai server habis.",
  "components.OverlayBlocker.description.serverError": "Server seharusnya telah dimulai ulang, harap periksa log Anda di terminal.",
  "components.OverlayBlocker.title": "Menunggu untuk restart ...",
  "components.OverlayBlocker.title.serverError": "Mulai ulang membutuhkan waktu lebih lama dari yang diharapkan",
  "components.PageFooter.select": "entri per halaman",
  "components.ProductionBlocker.description": "Untuk tujuan keamanan, kami harus menonaktifkan plugin ini di lingkungan lain.",
  "components.ProductionBlocker.header": "Plugin ini hanya tersedia dalam pengembangan.",
  "components.Search.placeholder": "Cari...",
  "components.Wysiwyg.collapse": "Gulung",
  "components.Wysiwyg.selectOptions.H1": "Judul H1",
  "components.Wysiwyg.selectOptions.H2": "Judul H2",
  "components.Wysiwyg.selectOptions.H3": "Judul H3",
  "components.Wysiwyg.selectOptions.H4": "Judul H4",
  "components.Wysiwyg.selectOptions.H5": "Judul H5",
  "components.Wysiwyg.selectOptions.H6": "Judul H6",
  "components.Wysiwyg.selectOptions.title": "Tambahkan judul",
  "components.WysiwygBottomControls.charactersIndicators": "karakter",
  "components.WysiwygBottomControls.fullscreen": "Perbesar",
  "components.WysiwygBottomControls.uploadFiles": "Tarik & lepas file, tempel dari clipboard atau {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "pilih mereka",
  "components.popUpWarning.button.cancel": "Tidak, batalkan",
  "components.popUpWarning.button.confirm": "Ya, konfirmasi",
  "components.popUpWarning.message": "Apa kamu yakin ingin menghapus ini?",
  "components.popUpWarning.title": "Mohon konfirmasi",
  "form.button.done": "Selesai",
  "global.prompt.unsaved": "Anda yakin ingin meninggalkan halaman ini? Semua modifikasi Anda akan hilang",
  "notification.contentType.relations.conflict": "Jenis konten memiliki hubungan yang saling bertentangan",
  "notification.error": "Terjadi kesalahan",
  "notification.error.layout": "Tidak dapat mengambil tata letak",
  "notification.form.error.fields": "Formulir tersebut mengandung beberapa kesalahan",
  "notification.form.success.fields": "Perubahan tersimpan",
  "notification.link-copied": "Tautan disalin ke papan klip",
  "notification.permission.not-allowed-read": "Anda tidak diizinkan untuk melihat dokumen ini",
  "notification.success.delete": "Item telah dihapus",
  "notification.success.saved": "Disimpan",
  "request.error.model.unknown": "Model ini tidak ada"
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  id as default
};
//# sourceMappingURL=id-cH3Ovozx-O2HTZ25C.js.map
