import {
  fn
} from "./chunk-UVVU2BYJ.js";
import {
  getTrad,
  pageSizes,
  pluginId,
  require_prop_types,
  sortOptions,
  useConfig,
  useIntl
} from "./chunk-S4GMEU6I.js";
import "./chunk-RPX6VIML.js";
import "./chunk-VOKTPSOW.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-KKUAHZGP.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  ConfirmDialog,
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  Page,
  require_isEqual,
  require_set,
  useNotification,
  useTracking
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Button,
  Dialog,
  Field,
  Grid,
  Link,
  SingleSelect,
  SingleSelectOption,
  require_get,
  require_lib
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  NavLink
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$4Z,
  ForwardRef$4p
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/upload/dist/_chunks/index-C_cWV2yS.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var import_qs = __toESM(require_lib(), 1);
var import_get = __toESM(require_get(), 1);
var import_set = __toESM(require_set(), 1);
var Settings = ({ sort = "", pageSize = 10, onChange: onChange2 }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(
    Box,
    {
      background: "neutral0",
      hasRadius: true,
      shadow: "tableShadow",
      paddingTop: 6,
      paddingBottom: 6,
      paddingLeft: 7,
      paddingRight: 7,
      children: (0, import_jsx_runtime.jsxs)(Grid.Root, { gap: 4, children: [
        (0, import_jsx_runtime.jsx)(Grid.Item, { s: 12, col: 6, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
          Field.Root,
          {
            hint: formatMessage({
              id: getTrad("config.entries.note"),
              defaultMessage: "Number of assets displayed by default in the Media Library"
            }),
            name: "pageSize",
            children: [
              (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                id: getTrad("config.entries.title"),
                defaultMessage: "Entries per page"
              }) }),
              (0, import_jsx_runtime.jsx)(
                SingleSelect,
                {
                  onChange: (value) => onChange2({ target: { name: "pageSize", value } }),
                  value: pageSize,
                  children: pageSizes.map((pageSize2) => (0, import_jsx_runtime.jsx)(SingleSelectOption, { value: pageSize2, children: pageSize2 }, pageSize2))
                }
              ),
              (0, import_jsx_runtime.jsx)(Field.Hint, {})
            ]
          }
        ) }),
        (0, import_jsx_runtime.jsx)(Grid.Item, { s: 12, col: 6, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
          Field.Root,
          {
            hint: formatMessage({
              id: getTrad("config.note"),
              defaultMessage: "Note: You can override this value in the media library."
            }),
            name: "sort",
            children: [
              (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                id: getTrad("config.sort.title"),
                defaultMessage: "Default sort order"
              }) }),
              (0, import_jsx_runtime.jsx)(
                SingleSelect,
                {
                  onChange: (value) => onChange2({ target: { name: "sort", value } }),
                  value: sort,
                  "test-sort": sort,
                  "data-testid": "sort-select",
                  children: sortOptions.map((filter) => (0, import_jsx_runtime.jsx)(
                    SingleSelectOption,
                    {
                      "data-testid": `sort-option-${filter.value}`,
                      value: filter.value,
                      children: formatMessage({ id: getTrad(filter.key), defaultMessage: `${filter.value}` })
                    },
                    filter.key
                  ))
                }
              ),
              (0, import_jsx_runtime.jsx)(Field.Hint, {})
            ]
          }
        ) })
      ] })
    }
  );
};
Settings.propTypes = {
  sort: import_prop_types.default.string.isRequired,
  pageSize: import_prop_types.default.number.isRequired,
  onChange: import_prop_types.default.func.isRequired
};
var ON_CHANGE = `${pluginId}/ON_CHANGE`;
var SET_LOADED = `${pluginId}/SET_LOADED`;
var onChange = ({ name, value }) => ({
  type: ON_CHANGE,
  keys: name,
  value
});
var setLoaded = () => ({
  type: SET_LOADED
});
var initialState = {
  initialData: {},
  modifiedData: {}
};
var init = (configData) => {
  return {
    ...initialState,
    initialData: configData,
    modifiedData: configData
  };
};
var reducer = (state = initialState, action = {
  type: ""
}) => (
  // eslint-disable-next-line consistent-return
  fn(state, (draftState) => {
    switch (action.type) {
      case ON_CHANGE: {
        (0, import_set.default)(draftState, ["modifiedData", ...action.keys.split(".")], action.value);
        break;
      }
      case SET_LOADED: {
        const reInitialise = init((0, import_get.default)(draftState, ["modifiedData"], {}));
        draftState.initialData = reInitialise.initialData;
        draftState.modifiedData = reInitialise.modifiedData;
        break;
      }
      default:
        return draftState;
    }
  })
);
var ConfigureTheView = ({ config }) => {
  const { trackUsage } = useTracking();
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { mutateConfig } = useConfig();
  const { isLoading: isSubmittingForm } = mutateConfig;
  const [showWarningSubmit, setWarningSubmit] = (0, import_react.useState)(false);
  const toggleWarningSubmit = () => setWarningSubmit((prevState) => !prevState);
  const [reducerState, dispatch] = (0, import_react.useReducer)(reducer, initialState, () => init(config));
  const { initialData, modifiedData } = reducerState;
  const handleSubmit = (e) => {
    e.preventDefault();
    toggleWarningSubmit();
  };
  const handleConfirm = async () => {
    trackUsage("willEditMediaLibraryConfig");
    await mutateConfig.mutateAsync(modifiedData);
    setWarningSubmit(false);
    dispatch(setLoaded());
    toggleNotification({
      type: "success",
      message: formatMessage({
        id: "notification.form.success.fields",
        defaultMessage: "Changes saved"
      })
    });
  };
  const handleChange = ({ target: { name, value } }) => {
    dispatch(onChange({ name, value }));
  };
  return (0, import_jsx_runtime.jsx)(Layouts.Root, { children: (0, import_jsx_runtime.jsx)(Page.Main, { "aria-busy": isSubmittingForm, children: (0, import_jsx_runtime.jsxs)("form", { onSubmit: handleSubmit, children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        navigationAction: (0, import_jsx_runtime.jsx)(
          Link,
          {
            tag: NavLink,
            startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4Z, {}),
            to: `/plugins/${pluginId}`,
            id: "go-back",
            children: formatMessage({ id: getTrad("config.back"), defaultMessage: "Back" })
          }
        ),
        primaryAction: (0, import_jsx_runtime.jsx)(
          Button,
          {
            size: "S",
            startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
            disabled: (0, import_isEqual.default)(modifiedData, initialData),
            type: "submit",
            children: formatMessage({ id: "global.save", defaultMessage: "Save" })
          }
        ),
        subtitle: formatMessage({
          id: getTrad("config.subtitle"),
          defaultMessage: "Define the view settings of the media library."
        }),
        title: formatMessage({
          id: getTrad("config.title"),
          defaultMessage: "Configure the view - Media Library"
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsx)(
      Settings,
      {
        "data-testid": "settings",
        pageSize: modifiedData.pageSize || "",
        sort: modifiedData.sort || "",
        onChange: handleChange
      }
    ) }),
    (0, import_jsx_runtime.jsx)(Dialog.Root, { open: showWarningSubmit, onOpenChange: toggleWarningSubmit, children: (0, import_jsx_runtime.jsx)(ConfirmDialog, { onConfirm: handleConfirm, variant: "default", children: formatMessage({
      id: getTrad("config.popUpWarning.warning.updateAllSettings"),
      defaultMessage: "This will modify all your settings"
    }) }) })
  ] }) }) });
};
ConfigureTheView.propTypes = {
  config: import_prop_types.default.shape({
    pageSize: import_prop_types.default.number,
    sort: import_prop_types.default.string
  }).isRequired
};
export {
  ConfigureTheView as default
};
//# sourceMappingURL=index-C_cWV2yS-YJ74FYOM.js.map
