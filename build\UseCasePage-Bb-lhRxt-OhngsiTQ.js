import{m as e,a as v,a5 as y,al as M,au as U,r as c,bV as L,bl as C,aW as S,aX as k,aY as _,w as l,a_ as B,J as d,s as P,x as o,a9 as R,aa as T,z as O,A as w,bI as A}from"./strapi-YzJfjJ2z.js";import{P as F}from"./PrivateRoute-gjOsMUaF-a4KqNTSG.js";const I=[{intlLabel:{id:"Usecase.front-end",defaultMessage:"Front-end developer"},value:"front_end_developer"},{intlLabel:{id:"Usecase.back-end",defaultMessage:"Back-end developer"},value:"back_end_developer"},{intlLabel:{id:"Usecase.full-stack",defaultMessage:"Full-stack developer"},value:"full_stack_developer"},{intlLabel:{id:"global.content-manager",defaultMessage:"Content Manager"},value:"content_manager"},{intlLabel:{id:"Usecase.content-creator",defaultMessage:"Content Creator"},value:"content_creator"},{intlLabel:{id:"Usecase.other",defaultMessage:"Other"},value:"other"}],N=()=>{const{toggleNotification:u}=v(),h=y(),g=M(),{formatMessage:s}=U(),[n,p]=c.useState(null),[i,f]=c.useState(""),{firstname:x,email:m}=L("UseCasePage",t=>t.user)??{},{hasAdmin:j}=C.parse(h.search,{ignoreQueryPrefix:!0}),b=n==="other",r=async(t,a)=>{t.preventDefault();try{await fetch("https://analytics.strapi.io/register",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:m,username:x,firstAdmin:!j,persona:{role:a?void 0:n,otherRole:a?void 0:i}})}),u({type:"success",message:s({id:"Usecase.notification.success.project-created",defaultMessage:"Project has been successfully created"})}),g("/")}catch{}};return e.jsx(S,{children:e.jsxs(k,{labelledBy:"usecase-title",children:[e.jsx(_,{children:e.jsxs("form",{onSubmit:t=>r(t,!1),children:[e.jsxs(l,{direction:"column",paddingBottom:7,children:[e.jsx(B,{}),e.jsx(d,{paddingTop:6,paddingBottom:1,width:"25rem",children:e.jsx(P,{textAlign:"center",variant:"alpha",tag:"h1",id:"usecase-title",children:s({id:"Usecase.title",defaultMessage:"Tell us a bit more about yourself"})})})]}),e.jsxs(l,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsxs(o.Root,{name:"usecase",children:[e.jsx(o.Label,{children:s({id:"Usecase.input.work-type",defaultMessage:"What type of work do you do?"})}),e.jsx(R,{onChange:t=>p(t),value:n,children:I.map(({intlLabel:t,value:a})=>e.jsx(T,{value:a,children:s(t)},a))})]}),b&&e.jsxs(o.Root,{name:"other",children:[e.jsx(o.Label,{children:s({id:"Usecase.other",defaultMessage:"Other"})}),e.jsx(O,{value:i,onChange:t=>f(t.target.value)})]}),e.jsx(w,{type:"submit",size:"L",fullWidth:!0,disabled:!n,children:s({id:"global.finish",defaultMessage:"Finish"})})]})]})}),e.jsx(l,{justifyContent:"center",children:e.jsx(d,{paddingTop:4,children:e.jsx(A,{onClick:t=>r(t,!0),children:s({id:"Usecase.button.skip",defaultMessage:"Skip this question"})})})})]})})},J=()=>e.jsx(F,{children:e.jsx(N,{})});export{J as PrivateUseCasePage,N as UseCasePage,I as options};
