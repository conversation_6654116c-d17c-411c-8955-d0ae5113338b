{"version": 3, "sources": ["../../../@strapi/content-type-builder/admin/src/components/BoxWrapper.tsx", "../../../@strapi/content-type-builder/admin/src/components/Tr.tsx", "../../../@strapi/content-type-builder/admin/src/components/ComponentList.tsx", "../../../@strapi/content-type-builder/admin/src/components/ComponentCard/ComponentIcon/ComponentIcon.tsx", "../../../@strapi/content-type-builder/admin/src/components/ComponentCard/ComponentCard.tsx", "../../../@strapi/content-type-builder/admin/src/components/DynamicZoneList.tsx", "../../../@strapi/content-type-builder/admin/src/components/NestedFooter.tsx", "../../../@strapi/content-type-builder/admin/src/components/List.tsx", "../../../@strapi/content-type-builder/admin/src/icons/Curve.tsx", "../../../@strapi/content-type-builder/admin/src/components/DisplayedType.tsx", "../../../@strapi/content-type-builder/admin/src/components/UpperFirst.tsx", "../../../@strapi/content-type-builder/admin/src/components/ListRow.tsx", "../../../@strapi/content-type-builder/admin/src/utils/getAttributeDisplayedType.ts", "../../../@strapi/content-type-builder/admin/src/pages/ListView/LinkToCMSettingsView.tsx", "../../../@strapi/content-type-builder/admin/src/pages/ListView/ListView.tsx"], "sourcesContent": ["import { Box } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nexport const BoxWrapper = styled(Box)`\n  table {\n    width: 100%;\n    white-space: nowrap;\n  }\n\n  thead {\n    border-bottom: 1px solid ${({ theme }) => theme.colors.neutral150};\n\n    tr {\n      border-top: 0;\n    }\n  }\n\n  tr {\n    border-top: 1px solid ${({ theme }) => theme.colors.neutral150};\n\n    & td,\n    & th {\n      padding: ${({ theme }) => theme.spaces[4]};\n    }\n\n    & td:first-of-type,\n    & th:first-of-type {\n      padding: 0 ${({ theme }) => theme.spaces[1]};\n    }\n  }\n\n  th,\n  td {\n    vertical-align: middle;\n    text-align: left;\n    color: ${({ theme }) => theme.colors.neutral600};\n    outline-offset: -4px;\n  }\n`;\n", "import { styled } from 'styled-components';\n\n// Keep component-row for css specificity\nexport const Tr = styled.tr<{\n  $isFromDynamicZone?: boolean;\n  $isChildOfDynamicZone?: boolean;\n}>`\n  &.component-row,\n  &.dynamiczone-row {\n    position: relative;\n    border-top: none !important;\n\n    table tr:first-child {\n      border-top: none;\n    }\n\n    > td:first-of-type {\n      padding: 0 0 0 2rem;\n      position: relative;\n\n      &::before {\n        content: '';\n        width: 0.4rem;\n        height: calc(100% - 40px);\n        position: absolute;\n        top: -7px;\n        left: 2.6rem;\n        border-radius: 4px;\n\n        ${({ $isFromDynamicZone, $isChildOfDynamicZone, theme }) => {\n          if ($isChildOfDynamicZone) {\n            return `background-color: ${theme.colors.primary200};`;\n          }\n\n          if ($isFromDynamicZone) {\n            return `background-color: ${theme.colors.primary200};`;\n          }\n\n          return `background: ${theme.colors.neutral150};`;\n        }}\n      }\n    }\n  }\n\n  &.dynamiczone-row > td:first-of-type {\n    padding: 0;\n  }\n`;\n", "import get from 'lodash/get';\n\nimport { useDataManager } from '../hooks/useDataManager';\n\nimport { List } from './List';\nimport { Tr } from './Tr';\n\nimport type { Internal } from '@strapi/types';\n\ninterface ComponentListProps {\n  component: Internal.UID.Component;\n  customRowComponent: any;\n  firstLoopComponentUid?: string;\n  isFromDynamicZone?: boolean;\n  isNestedInDZComponent?: boolean;\n}\n\nexport const ComponentList = ({\n  customRowComponent,\n  component,\n  isFromDynamicZone = false,\n  isNestedInDZComponent = false,\n  firstLoopComponentUid,\n}: ComponentListProps) => {\n  const { modifiedData } = useDataManager();\n  const {\n    schema: { attributes },\n  } = get(modifiedData, ['components', component], {\n    schema: { attributes: [] },\n  });\n\n  return (\n    <Tr $isChildOfDynamicZone={isFromDynamicZone} className=\"component-row\">\n      <td colSpan={12}>\n        <List\n          customRowComponent={customRowComponent}\n          items={attributes}\n          targetUid={component}\n          firstLoopComponentUid={firstLoopComponentUid || component}\n          editTarget=\"components\"\n          isFromDynamicZone={isFromDynamicZone}\n          isNestedInDZComponent={isNestedInDZComponent}\n          isSub\n          secondLoopComponentUid={firstLoopComponentUid ? component : null}\n        />\n      </td>\n    </Tr>\n  );\n};\n", "import { Flex } from '@strapi/design-system';\n\nimport { COMPONENT_ICONS } from '../../IconPicker/constants';\n\ninterface ComponentIconProps {\n  isActive?: boolean;\n  icon?: keyof typeof COMPONENT_ICONS;\n}\n\nexport const ComponentIcon = ({ isActive = false, icon = 'dashboard' }: ComponentIconProps) => {\n  const Icon = COMPONENT_ICONS[icon] || COMPONENT_ICONS.dashboard;\n\n  return (\n    <Flex\n      alignItems=\"center\"\n      background={isActive ? 'primary200' : 'neutral200'}\n      justifyContent=\"center\"\n      height={8}\n      width={8}\n      borderRadius=\"50%\"\n    >\n      <Icon height=\"2rem\" width=\"2rem\" />\n    </Flex>\n  );\n};\n", "import { Box, Flex, Typography } from '@strapi/design-system';\nimport { Cross } from '@strapi/icons';\nimport get from 'lodash/get';\nimport { styled } from 'styled-components';\n\nimport { useDataManager } from '../../hooks/useDataManager';\n\nimport { ComponentIcon } from './ComponentIcon';\n\ninterface ComponentCardProps {\n  component: string;\n  dzName: string;\n  index: number;\n  isActive?: boolean;\n  isInDevelopmentMode?: boolean;\n  onClick?: () => void;\n}\n\nconst CloseButton = styled(Box)`\n  position: absolute;\n  display: none;\n  top: 5px;\n  right: 0.8rem;\n\n  svg {\n    width: 1rem;\n    height: 1rem;\n\n    path {\n      fill: ${({ theme }) => theme.colors.primary600};\n    }\n  }\n`;\n\nconst ComponentBox = styled(Flex)`\n  width: 14rem;\n  height: 8rem;\n  position: relative;\n  border: 1px solid ${({ theme }) => theme.colors.neutral200};\n  background: ${({ theme }) => theme.colors.neutral100};\n  border-radius: ${({ theme }) => theme.borderRadius};\n  max-width: 100%;\n\n  &.active,\n  &:focus,\n  &:hover {\n    border: 1px solid ${({ theme }) => theme.colors.primary200};\n    background: ${({ theme }) => theme.colors.primary100};\n    color: ${({ theme }) => theme.colors.primary600};\n\n    ${CloseButton} {\n      display: block;\n    }\n\n    /* > ComponentIcon */\n    > div:first-child {\n      background: ${({ theme }) => theme.colors.primary200};\n      color: ${({ theme }) => theme.colors.primary600};\n\n      svg {\n        path {\n          fill: ${({ theme }) => theme.colors.primary600};\n        }\n      }\n    }\n  }\n`;\n\nexport const ComponentCard = ({\n  component,\n  dzName,\n  index,\n  isActive = false,\n  isInDevelopmentMode = false,\n  onClick,\n}: ComponentCardProps) => {\n  const { modifiedData, removeComponentFromDynamicZone } = useDataManager();\n  const {\n    schema: { icon, displayName },\n  } = get(modifiedData, ['components', component], { schema: {} });\n\n  const onClose = (e: any) => {\n    e.stopPropagation();\n    removeComponentFromDynamicZone(dzName, index);\n  };\n\n  return (\n    <ComponentBox\n      alignItems=\"center\"\n      direction=\"column\"\n      className={isActive ? 'active' : ''}\n      borderRadius=\"borderRadius\"\n      justifyContent=\"center\"\n      paddingLeft={4}\n      paddingRight={4}\n      shrink={0}\n      onClick={onClick}\n      role=\"tab\"\n      tabIndex={isActive ? 0 : -1}\n      cursor=\"pointer\"\n      aria-selected={isActive}\n      aria-controls={`dz-${dzName}-panel-${index}`}\n      id={`dz-${dzName}-tab-${index}`}\n    >\n      <ComponentIcon icon={icon} isActive={isActive} />\n\n      <Box marginTop={1} maxWidth=\"100%\">\n        <Typography variant=\"pi\" fontWeight=\"bold\" ellipsis>\n          {displayName}\n        </Typography>\n      </Box>\n\n      {isInDevelopmentMode && (\n        <CloseButton tag=\"button\" onClick={onClose}>\n          <Cross />\n        </CloseButton>\n      )}\n    </ComponentBox>\n  );\n};\n", "import { useState } from 'react';\n\nimport { Box, Flex, Typography } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useDataManager } from '../hooks/useDataManager';\nimport { getTrad } from '../utils/getTrad';\n\nimport { ComponentCard } from './ComponentCard';\nimport { ComponentList } from './ComponentList';\nimport { Tr } from './Tr';\n\nimport type { Internal } from '@strapi/types';\n\ninterface DynamicZoneListProps {\n  addComponent: (name?: string) => void;\n  components: Array<string>;\n  customRowComponent?: () => void;\n  name?: string;\n  targetUid: Internal.UID.Component;\n}\n\nconst StyledAddIcon = styled(Plus)`\n  width: 3.2rem;\n  height: 3.2rem;\n  padding: 0.9rem;\n  border-radius: 6.4rem;\n  background: ${({ theme }) => theme.colors.primary100};\n  path {\n    fill: ${({ theme }) => theme.colors.primary600};\n  }\n`;\n\nconst FixedBox = styled(Box)`\n  height: 9rem;\n  position: absolute;\n  width: 100%;\n  top: 0;\n  left: 0;\n`;\n\nconst ScrollableStack = styled(Flex)`\n  width: 100%;\n  overflow-x: auto;\n`;\n\nconst ComponentContentBox = styled(Box)`\n  padding-top: 9rem;\n`;\n\nconst ComponentStack = styled(Flex)`\n  flex-shrink: 0;\n  width: 14rem;\n  height: 8rem;\n  justify-content: center;\n  align-items: center;\n`;\n\nexport const DynamicZoneList = ({\n  customRowComponent,\n  components = [],\n  addComponent,\n  name,\n  targetUid,\n}: DynamicZoneListProps) => {\n  const { isInDevelopmentMode } = useDataManager();\n  const [activeTab, setActiveTab] = useState(0);\n  const { formatMessage } = useIntl();\n\n  const toggle = (tab: number) => {\n    if (activeTab !== tab) {\n      setActiveTab(tab);\n    }\n  };\n\n  const handleClickAdd = () => {\n    addComponent(name);\n  };\n\n  return (\n    <Tr className=\"dynamiczone-row\" $isFromDynamicZone>\n      <td colSpan={12}>\n        <FixedBox paddingLeft={8}>\n          <ScrollableStack gap={2}>\n            {isInDevelopmentMode && (\n              <button type=\"button\" onClick={handleClickAdd}>\n                <ComponentStack direction=\"column\" alignItems=\"stretch\" gap={1}>\n                  <StyledAddIcon />\n                  <Typography variant=\"pi\" fontWeight=\"bold\" textColor=\"primary600\">\n                    {formatMessage({\n                      id: getTrad('button.component.add'),\n                      defaultMessage: 'Add a component',\n                    })}\n                  </Typography>\n                </ComponentStack>\n              </button>\n            )}\n            <Flex role=\"tablist\" gap={2}>\n              {components.map((component, index) => {\n                return (\n                  <ComponentCard\n                    key={component}\n                    dzName={name || ''}\n                    index={index}\n                    component={component}\n                    isActive={activeTab === index}\n                    isInDevelopmentMode={isInDevelopmentMode}\n                    onClick={() => toggle(index)}\n                  />\n                );\n              })}\n            </Flex>\n          </ScrollableStack>\n        </FixedBox>\n        <ComponentContentBox>\n          {components.map((component, index) => {\n            const props = {\n              customRowComponent,\n              component,\n            };\n\n            return (\n              <Box\n                id={`dz-${name}-panel-${index}`}\n                role=\"tabpanel\"\n                aria-labelledby={`dz-${name}-tab-${index}`}\n                key={component}\n                style={{ display: activeTab === index ? 'block' : 'none' }}\n              >\n                <table>\n                  <tbody>\n                    <ComponentList\n                      {...props}\n                      isFromDynamicZone\n                      component={targetUid}\n                      key={component}\n                    />\n                  </tbody>\n                </table>\n              </Box>\n            );\n          })}\n        </ComponentContentBox>\n      </td>\n    </Tr>\n  );\n};\n", "import type { ReactNode } from 'react';\n\nimport { Box, Flex, Typography } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nconst IconBox = styled(Box)`\n  height: 2.4rem;\n  width: 2.4rem;\n  border-radius: 50%;\n  display: flex;\n  justify-content: center;\n  align-items: center;\n\n  svg {\n    height: 1rem;\n    width: 1rem;\n  }\n\n  svg path {\n    fill: ${({ theme, color }) => theme.colors[`${color}600`]};\n  }\n`;\n\nconst ButtonBox = styled(Box)`\n  border-radius: 0 0 ${({ theme }) => theme.borderRadius} ${({ theme }) => theme.borderRadius};\n  display: block;\n  width: 100%;\n  border: none;\n  position: relative;\n  left: -0.4rem;\n`;\n\ninterface NestedTFooterProps {\n  color: string;\n  children: ReactNode;\n  icon: ReactNode;\n  onClick?: () => void;\n}\n\nexport const NestedTFooter = ({ children, icon, color, ...props }: NestedTFooterProps) => {\n  return (\n    <ButtonBox paddingBottom={4} paddingTop={4} tag=\"button\" type=\"button\" {...props}>\n      <Flex>\n        <IconBox color={color} aria-hidden background={`${color}200`}>\n          {icon}\n        </IconBox>\n        <Box paddingLeft={3}>\n          <Typography variant=\"pi\" fontWeight=\"bold\" textColor={`${color}600`}>\n            {children}\n          </Typography>\n        </Box>\n      </Flex>\n    </ButtonBox>\n  );\n};\n", "import { ComponentType, Fragment } from 'react';\n\nimport { useTracking } from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Button,\n  EmptyStateLayout,\n  Table,\n  Tbody,\n  Td,\n  TFooter,\n  Th,\n  Thead,\n  Tr,\n  Typography,\n} from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nimport { useDataManager } from '../hooks/useDataManager';\nimport { useFormModalNavigation } from '../hooks/useFormModalNavigation';\nimport { getTrad } from '../utils/getTrad';\n\nimport { BoxWrapper } from './BoxWrapper';\nimport { ComponentList } from './ComponentList';\nimport { DynamicZoneList } from './DynamicZoneList';\nimport { NestedTFooter } from './NestedFooter';\n\nimport type { SchemaType } from '../types';\nimport type { Internal } from '@strapi/types';\n\ninterface ListProps {\n  addComponentToDZ?: () => void;\n  customRowComponent: ComponentType<any>;\n  editTarget: SchemaType;\n  firstLoopComponentUid?: string;\n  isFromDynamicZone?: boolean;\n  isNestedInDZComponent?: boolean;\n  isMain?: boolean;\n  items: any[];\n  secondLoopComponentUid?: string | null;\n  targetUid?: Internal.UID.Schema;\n  isSub?: boolean;\n}\n\nexport const List = ({\n  addComponentToDZ,\n  customRowComponent,\n  editTarget,\n  firstLoopComponentUid,\n  isFromDynamicZone = false,\n  isMain = false,\n  isNestedInDZComponent = false,\n  isSub = false,\n  items = [],\n  secondLoopComponentUid,\n  targetUid,\n}: ListProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { isInDevelopmentMode, modifiedData, isInContentTypeView } = useDataManager();\n\n  const { onOpenModalAddField } = useFormModalNavigation();\n  const onClickAddField = () => {\n    trackUsage('hasClickedCTBAddFieldBanner');\n\n    onOpenModalAddField({ forTarget: editTarget, targetUid });\n  };\n\n  if (!targetUid) {\n    return (\n      <Table colCount={2} rowCount={2}>\n        <Thead>\n          <Tr>\n            <Th>\n              <Typography variant=\"sigma\" textColor=\"neutral600\">\n                {formatMessage({ id: 'global.name', defaultMessage: 'Name' })}\n              </Typography>\n            </Th>\n            <Th>\n              <Typography variant=\"sigma\" textColor=\"neutral600\">\n                {formatMessage({ id: 'global.type', defaultMessage: 'Type' })}\n              </Typography>\n            </Th>\n          </Tr>\n        </Thead>\n        <Tbody>\n          <Tr>\n            <Td colSpan={2}>\n              <EmptyStateLayout\n                content={formatMessage({\n                  id: getTrad('table.content.create-first-content-type'),\n                  defaultMessage: 'Create your first Collection-Type',\n                })}\n                hasRadius\n                icon={<EmptyDocuments width=\"16rem\" />}\n              />\n            </Td>\n          </Tr>\n        </Tbody>\n      </Table>\n    );\n  }\n\n  if (items.length === 0 && isMain) {\n    return (\n      <Table colCount={2} rowCount={2}>\n        <Thead>\n          <Tr>\n            <Th>\n              <Typography variant=\"sigma\" textColor=\"neutral600\">\n                {formatMessage({ id: 'global.name', defaultMessage: 'Name' })}\n              </Typography>\n            </Th>\n            <Th>\n              <Typography variant=\"sigma\" textColor=\"neutral600\">\n                {formatMessage({ id: 'global.type', defaultMessage: 'Type' })}\n              </Typography>\n            </Th>\n          </Tr>\n        </Thead>\n        <Tbody>\n          <Tr>\n            <Td colSpan={2}>\n              <EmptyStateLayout\n                action={\n                  <Button\n                    onClick={onClickAddField}\n                    size=\"L\"\n                    startIcon={<Plus />}\n                    variant=\"secondary\"\n                  >\n                    {formatMessage({\n                      id: getTrad('table.button.no-fields'),\n                      defaultMessage: 'Add new field',\n                    })}\n                  </Button>\n                }\n                content={formatMessage(\n                  isInContentTypeView\n                    ? {\n                        id: getTrad('table.content.no-fields.collection-type'),\n                        defaultMessage: 'Add your first field to this Collection-Type',\n                      }\n                    : {\n                        id: getTrad('table.content.no-fields.component'),\n                        defaultMessage: 'Add your first field to this component',\n                      }\n                )}\n                hasRadius\n                icon={<EmptyDocuments width=\"16rem\" />}\n              />\n            </Td>\n          </Tr>\n        </Tbody>\n      </Table>\n    );\n  }\n\n  return (\n    <BoxWrapper>\n      <Box\n        paddingLeft={6}\n        paddingRight={isMain ? 6 : 0}\n        {...(isMain && { style: { overflowX: 'auto' } })}\n      >\n        <table>\n          {isMain && (\n            <thead>\n              <tr>\n                <th>\n                  <Typography variant=\"sigma\" textColor=\"neutral800\">\n                    {formatMessage({ id: 'global.name', defaultMessage: 'Name' })}\n                  </Typography>\n                </th>\n                <th colSpan={2}>\n                  <Typography variant=\"sigma\" textColor=\"neutral800\">\n                    {formatMessage({ id: 'global.type', defaultMessage: 'Type' })}\n                  </Typography>\n                </th>\n              </tr>\n            </thead>\n          )}\n          <tbody>\n            {items.map((item) => {\n              const { type } = item;\n              const CustomRow = customRowComponent;\n\n              return (\n                <Fragment key={item.name}>\n                  <CustomRow\n                    {...item}\n                    isNestedInDZComponent={isNestedInDZComponent}\n                    targetUid={targetUid}\n                    editTarget={editTarget}\n                    firstLoopComponentUid={firstLoopComponentUid}\n                    isFromDynamicZone={isFromDynamicZone}\n                    secondLoopComponentUid={secondLoopComponentUid}\n                  />\n\n                  {type === 'component' && (\n                    <ComponentList\n                      {...item}\n                      customRowComponent={customRowComponent}\n                      targetUid={targetUid}\n                      isNestedInDZComponent={isFromDynamicZone}\n                      editTarget={editTarget}\n                      firstLoopComponentUid={firstLoopComponentUid}\n                    />\n                  )}\n\n                  {type === 'dynamiczone' && (\n                    <DynamicZoneList\n                      {...item}\n                      customRowComponent={customRowComponent}\n                      addComponent={addComponentToDZ}\n                      targetUid={targetUid}\n                    />\n                  )}\n                </Fragment>\n              );\n            })}\n          </tbody>\n        </table>\n      </Box>\n\n      {isMain && isInDevelopmentMode && (\n        <TFooter icon={<Plus />} onClick={onClickAddField}>\n          {formatMessage({\n            id: getTrad(\n              `form.button.add.field.to.${\n                modifiedData.contentType\n                  ? modifiedData.contentType.schema.kind\n                  : editTarget || 'collectionType'\n              }`\n            ),\n            defaultMessage: 'Add another field',\n          })}\n        </TFooter>\n      )}\n      {isSub && isInDevelopmentMode && !isFromDynamicZone && (\n        <NestedTFooter\n          icon={<Plus />}\n          onClick={onClickAddField}\n          color={isFromDynamicZone ? 'primary' : 'neutral'}\n        >\n          {formatMessage({\n            id: getTrad(`form.button.add.field.to.component`),\n            defaultMessage: 'Add another field',\n          })}\n        </NestedTFooter>\n      )}\n    </BoxWrapper>\n  );\n};\n", "import { Box } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nconst StyledBox = styled(Box)`\n  position: absolute;\n  left: -1.8rem;\n  top: 0px;\n\n  &:before {\n    content: '';\n    width: 0.4rem;\n    height: 1.2rem;\n    background: ${({ theme, color }) => theme.colors[color!]};\n    display: block;\n  }\n`;\n\nconst Svg = styled.svg`\n  position: relative;\n  flex-shrink: 0;\n  transform: translate(-0.5px, -1px);\n\n  * {\n    fill: ${({ theme, color }) => theme.colors[color!]};\n  }\n`;\n\ninterface CurveProps {\n  color: string;\n}\n\nexport const Curve = (props: CurveProps) => (\n  <StyledBox>\n    <Svg\n      width=\"20\"\n      height=\"23\"\n      viewBox=\"0 0 20 23\"\n      fill=\"none\"\n      xmlns=\"http://www.w3.org/2000/svg\"\n      {...props}\n    >\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M7.02477 14.7513C8.65865 17.0594 11.6046 18.6059 17.5596 18.8856C18.6836 18.9384 19.5976 19.8435 19.5976 20.9688V20.9688C19.5976 22.0941 18.6841 23.0125 17.5599 22.9643C10.9409 22.6805 6.454 20.9387 3.75496 17.1258C0.937988 13.1464 0.486328 7.39309 0.486328 0.593262H4.50974C4.50974 7.54693 5.06394 11.9813 7.02477 14.7513Z\"\n      />\n    </Svg>\n  </StyledBox>\n);\n", "import { Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { getTrad } from '../utils/getTrad';\n\ntype DisplayedTypeProps = {\n  type: string;\n  customField?: string | null;\n  repeatable?: boolean;\n};\n\nexport const DisplayedType = ({\n  type,\n  customField = null,\n  repeatable = false,\n}: DisplayedTypeProps) => {\n  const { formatMessage } = useIntl();\n\n  let readableType = type;\n\n  if (['integer', 'biginteger', 'float', 'decimal'].includes(type)) {\n    readableType = 'number';\n  } else if (['string'].includes(type)) {\n    readableType = 'text';\n  }\n\n  if (customField) {\n    return (\n      <Typography>\n        {formatMessage({\n          id: getTrad('attribute.customField'),\n          defaultMessage: 'Custom field',\n        })}\n      </Typography>\n    );\n  }\n\n  return (\n    <Typography textColor=\"neutral800\">\n      {formatMessage({\n        id: getTrad(`attribute.${readableType}`),\n        defaultMessage: type,\n      })}\n      &nbsp;\n      {repeatable &&\n        formatMessage({\n          id: getTrad('component.repeatable'),\n          defaultMessage: '(repeatable)',\n        })}\n    </Typography>\n  );\n};\n", "import upperFirst from 'lodash/upperFirst';\n\nexport const UpperFirst = ({ content }: { content: string }) => <>{upperFirst(content)}</>;\n", "import { memo } from 'react';\n\nimport { Box, Flex, IconButton, Typography } from '@strapi/design-system';\nimport { Lock, Pencil, Trash } from '@strapi/icons';\nimport get from 'lodash/get';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useDataManager } from '../hooks/useDataManager';\nimport { Curve } from '../icons/Curve';\nimport { getTrad } from '../utils/getTrad';\n\nimport { AttributeIcon, IconByType } from './AttributeIcon';\nimport { DisplayedType } from './DisplayedType';\nimport { UpperFirst } from './UpperFirst';\n\nexport const BoxWrapper = styled(Box)`\n  position: relative;\n`;\n\ntype ListRowProps = {\n  configurable?: boolean;\n  customField?: string | null;\n  editTarget: string;\n  firstLoopComponentUid?: string | null;\n  isFromDynamicZone?: boolean;\n  name: string;\n  onClick: (\n    editTarget: string,\n    targetUid: string | null,\n    attributeName: string,\n    attributeType: string,\n    customField: string | null\n  ) => void;\n  relation?: string;\n  repeatable?: boolean;\n  secondLoopComponentUid?: string | null;\n  target?: string | null;\n  targetUid?: string | null;\n  type: IconByType;\n};\n\nexport const ListRow = memo(\n  ({\n    configurable = true,\n    customField = null,\n    editTarget,\n    firstLoopComponentUid = null,\n    isFromDynamicZone = false,\n    name,\n    onClick,\n    relation = '',\n    repeatable = false,\n    secondLoopComponentUid = null,\n    target = null,\n    targetUid = null,\n    type,\n  }: ListRowProps) => {\n    const { contentTypes, isInDevelopmentMode, removeAttribute } = useDataManager();\n    const { formatMessage } = useIntl();\n\n    const isMorph = type === 'relation' && relation.includes('morph');\n    const ico = ['integer', 'biginteger', 'float', 'decimal'].includes(type) ? 'number' : type;\n\n    const contentType = get(contentTypes, [target as keyof typeof contentTypes], {});\n    const contentTypeFriendlyName = get(contentType, ['schema', 'displayName'], '');\n    const isPluginContentType = get(contentType, 'plugin');\n\n    const src = target ? 'relation' : ico;\n\n    const handleClick = () => {\n      if (isMorph) {\n        return;\n      }\n\n      if (configurable !== false) {\n        const attrType = type;\n\n        onClick(\n          // Tells where the attribute is located in the main modifiedData object : contentType, component or components\n          editTarget,\n          // main data type uid\n          secondLoopComponentUid || firstLoopComponentUid || targetUid,\n          // Name of the attribute\n          name,\n          // Type of the attribute\n          attrType,\n          customField\n        );\n      }\n    };\n    let loopNumber;\n\n    if (secondLoopComponentUid && firstLoopComponentUid) {\n      loopNumber = 2;\n    } else if (firstLoopComponentUid) {\n      loopNumber = 1;\n    } else {\n      loopNumber = 0;\n    }\n\n    return (\n      <BoxWrapper\n        tag=\"tr\"\n        onClick={isInDevelopmentMode && configurable && !isMorph ? handleClick : undefined}\n      >\n        <td style={{ position: 'relative' }}>\n          {loopNumber !== 0 && <Curve color={isFromDynamicZone ? 'primary200' : 'neutral150'} />}\n          <Flex paddingLeft={2} gap={4}>\n            <AttributeIcon type={src} customField={customField} />\n            <Typography textColor=\"neutral800\" fontWeight=\"bold\">\n              {name}\n            </Typography>\n          </Flex>\n        </td>\n        <td>\n          {target ? (\n            <Typography textColor=\"neutral800\">\n              {formatMessage({\n                id: getTrad(\n                  `modelPage.attribute.${isMorph ? 'relation-polymorphic' : 'relationWith'}`\n                ),\n                defaultMessage: 'Relation with',\n              })}\n              &nbsp;\n              <span style={{ fontStyle: 'italic' }}>\n                <UpperFirst content={contentTypeFriendlyName} />\n                &nbsp;\n                {isPluginContentType &&\n                  `(${formatMessage({\n                    id: getTrad(`from`),\n                    defaultMessage: 'from',\n                  })}: ${isPluginContentType})`}\n              </span>\n            </Typography>\n          ) : (\n            <DisplayedType type={type} customField={customField} repeatable={repeatable} />\n          )}\n        </td>\n        <td>\n          {isInDevelopmentMode ? (\n            <Flex justifyContent=\"flex-end\" onClick={(e) => e.stopPropagation()}>\n              {configurable ? (\n                <Flex gap={1}>\n                  {!isMorph && (\n                    <IconButton\n                      onClick={handleClick}\n                      label={`${formatMessage({\n                        id: 'app.utils.edit',\n                        defaultMessage: 'Edit',\n                      })} ${name}`}\n                      variant=\"ghost\"\n                    >\n                      <Pencil />\n                    </IconButton>\n                  )}\n                  <IconButton\n                    onClick={(e) => {\n                      e.stopPropagation();\n                      removeAttribute(\n                        editTarget,\n                        name,\n                        secondLoopComponentUid || firstLoopComponentUid || ''\n                      );\n                    }}\n                    label={`${formatMessage({\n                      id: 'global.delete',\n                      defaultMessage: 'Delete',\n                    })} ${name}`}\n                    variant=\"ghost\"\n                  >\n                    <Trash />\n                  </IconButton>\n                </Flex>\n              ) : (\n                <Lock />\n              )}\n            </Flex>\n          ) : (\n            /*\n            In production mode the edit icons aren't visible, therefore\n            we need to reserve the same space, otherwise the height of the\n            row might collapse, leading to bad positioned curve icons\n          */\n            <Box height=\"3.2rem\" />\n          )}\n        </td>\n      </BoxWrapper>\n    );\n  }\n);\n", "export const getAttributeDisplayedType = (type: string) => {\n  let displayedType;\n\n  switch (type) {\n    case 'date':\n    case 'datetime':\n    case 'time':\n    case 'timestamp':\n      displayedType = 'date';\n      break;\n    case 'integer':\n    case 'biginteger':\n    case 'decimal':\n    case 'float':\n      displayedType = 'number';\n      break;\n    case 'string':\n    case 'text':\n      displayedType = 'text';\n      break;\n    case '':\n      displayedType = 'relation';\n      break;\n    default:\n      displayedType = type;\n  }\n\n  return displayedType;\n};\n", "import { memo } from 'react';\n\nimport { type Permission, useRBAC } from '@strapi/admin/strapi-admin';\nimport { Button } from '@strapi/design-system';\nimport { ListPlus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useNavigate } from 'react-router-dom';\n\nconst cmPermissions: Record<string, Permission[]> = {\n  collectionTypesConfigurations: [\n    {\n      action: 'plugin::content-manager.collection-types.configure-view',\n      subject: null,\n    },\n  ],\n  componentsConfigurations: [\n    {\n      action: 'plugin::content-manager.components.configure-layout',\n      subject: null,\n    },\n  ],\n  singleTypesConfigurations: [\n    {\n      action: 'plugin::content-manager.single-types.configure-view',\n      subject: null,\n    },\n  ],\n};\n\ninterface LinkToCMSettingsViewProps {\n  disabled: boolean;\n  contentTypeKind?: string;\n  isInContentTypeView?: boolean;\n  isTemporary?: boolean;\n  targetUid?: string;\n}\n\nexport const LinkToCMSettingsView = memo(\n  ({\n    disabled,\n    isTemporary = false,\n    isInContentTypeView = true,\n    contentTypeKind = 'collectionType',\n    targetUid = '',\n  }: LinkToCMSettingsViewProps) => {\n    const { formatMessage } = useIntl();\n    const navigate = useNavigate();\n    const { collectionTypesConfigurations, componentsConfigurations, singleTypesConfigurations } =\n      cmPermissions;\n    const label = formatMessage({\n      id: 'content-type-builder.form.button.configure-view',\n      defaultMessage: 'Configure the view',\n    });\n    let permissionsToApply = collectionTypesConfigurations;\n\n    const handleClick = () => {\n      if (isTemporary) {\n        return false;\n      }\n\n      if (isInContentTypeView) {\n        navigate(`/content-manager/collection-types/${targetUid}/configurations/edit`);\n      } else {\n        navigate(`/content-manager/components/${targetUid}/configurations/edit`);\n      }\n\n      return false;\n    };\n\n    if (isInContentTypeView && contentTypeKind === 'singleType') {\n      permissionsToApply = singleTypesConfigurations;\n    }\n\n    if (!isInContentTypeView) {\n      permissionsToApply = componentsConfigurations;\n    }\n    const { isLoading, allowedActions } = useRBAC({\n      viewConfig: permissionsToApply,\n    });\n\n    if (isLoading) {\n      return null;\n    }\n\n    if (!allowedActions.canConfigureView) {\n      return null;\n    }\n\n    return (\n      <Button\n        startIcon={<ListPlus />}\n        variant=\"tertiary\"\n        onClick={handleClick}\n        disabled={isTemporary || disabled}\n      >\n        {label}\n      </Button>\n    );\n  }\n);\n", "import { BackButton, useTracking, Layouts } from '@strapi/admin/strapi-admin';\nimport { Box, Button, Flex } from '@strapi/design-system';\nimport { Check, Pencil, Plus } from '@strapi/icons';\nimport get from 'lodash/get';\nimport has from 'lodash/has';\nimport isEqual from 'lodash/isEqual';\nimport upperFirst from 'lodash/upperFirst';\nimport { useIntl } from 'react-intl';\nimport { unstable_usePrompt as usePrompt, useMatch } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { List } from '../../components/List';\nimport { ListRow } from '../../components/ListRow';\nimport { useDataManager } from '../../hooks/useDataManager';\nimport { useFormModalNavigation } from '../../hooks/useFormModalNavigation';\nimport { getAttributeDisplayedType } from '../../utils/getAttributeDisplayedType';\nimport { getTrad } from '../../utils/getTrad';\n\nimport { LinkToCMSettingsView } from './LinkToCMSettingsView';\n\n/* eslint-disable indent */\n\nconst LayoutsHeaderCustom = styled(Layouts.Header)`\n  overflow-wrap: anywhere;\n`;\n\nconst ListView = () => {\n  const { initialData, modifiedData, isInDevelopmentMode, isInContentTypeView, submitData } =\n    useDataManager();\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n\n  const match = useMatch('/plugins/content-type-builder/:kind/:currentUID');\n\n  const {\n    onOpenModalAddComponentsToDZ,\n    onOpenModalAddField,\n    onOpenModalEditField,\n    onOpenModalEditSchema,\n    onOpenModalEditCustomField,\n  } = useFormModalNavigation();\n\n  const firstMainDataPath = isInContentTypeView ? 'contentType' : 'component';\n  const mainDataTypeAttributesPath = [firstMainDataPath, 'schema', 'attributes'];\n  const targetUid = get(modifiedData, [firstMainDataPath, 'uid']);\n  const isTemporary = get(modifiedData, [firstMainDataPath, 'isTemporary'], false);\n  const contentTypeKind = get(modifiedData, [firstMainDataPath, 'schema', 'kind'], null);\n\n  const attributes = get(modifiedData, mainDataTypeAttributesPath, []);\n  const isFromPlugin = has(initialData, [firstMainDataPath, 'plugin']);\n  const hasModelBeenModified = !isEqual(modifiedData, initialData);\n\n  const forTarget = isInContentTypeView ? 'contentType' : 'component';\n\n  const handleClickAddComponentToDZ = (dynamicZoneTarget?: string) => {\n    onOpenModalAddComponentsToDZ({ dynamicZoneTarget, targetUid });\n  };\n\n  const handleClickEditField = async (\n    forTarget: string,\n    targetUid: string,\n    attributeName: string,\n    type: string,\n    customField: any\n  ) => {\n    const attributeType = getAttributeDisplayedType(type);\n    const step = type === 'component' ? '2' : null;\n\n    if (customField) {\n      onOpenModalEditCustomField({\n        forTarget,\n        targetUid,\n        attributeName,\n        attributeType,\n        customFieldUid: customField,\n      });\n    } else {\n      onOpenModalEditField({\n        forTarget,\n        targetUid,\n        attributeName,\n        attributeType,\n        step,\n      });\n    }\n  };\n\n  let label = get(modifiedData, [firstMainDataPath, 'schema', 'displayName'], '');\n  const kind = get(modifiedData, [firstMainDataPath, 'schema', 'kind'], '');\n\n  const isCreatingFirstContentType = match?.params.currentUID === 'create-content-type';\n\n  if (!label && isCreatingFirstContentType) {\n    label = formatMessage({\n      id: getTrad('button.model.create'),\n      defaultMessage: 'Create new collection type',\n    });\n  }\n\n  const onEdit = () => {\n    const contentType = kind || firstMainDataPath;\n\n    if (contentType === 'collectionType') {\n      trackUsage('willEditNameOfContentType');\n    }\n    if (contentType === 'singleType') {\n      trackUsage('willEditNameOfSingleType');\n    }\n\n    onOpenModalEditSchema({\n      modalType: firstMainDataPath,\n      forTarget: firstMainDataPath,\n      targetUid,\n      kind: contentType,\n    });\n  };\n\n  usePrompt({\n    when: hasModelBeenModified,\n    message: formatMessage({ id: getTrad('prompt.unsaved'), defaultMessage: 'Are you sure?' }),\n  });\n\n  return (\n    <>\n      <LayoutsHeaderCustom\n        id=\"title\"\n        primaryAction={\n          isInDevelopmentMode && (\n            <Flex gap={2} marginLeft={2}>\n              {/* DON'T display the add field button when the content type has not been created */}\n              {!isCreatingFirstContentType && (\n                <Button\n                  startIcon={<Plus />}\n                  variant=\"secondary\"\n                  minWidth=\"max-content\"\n                  onClick={() => {\n                    onOpenModalAddField({ forTarget, targetUid });\n                  }}\n                >\n                  {formatMessage({\n                    id: getTrad('button.attributes.add.another'),\n                    defaultMessage: 'Add another field',\n                  })}\n                </Button>\n              )}\n              <Button\n                startIcon={<Check />}\n                onClick={async () => await submitData()}\n                type=\"submit\"\n                disabled={isEqual(modifiedData, initialData)}\n              >\n                {formatMessage({\n                  id: 'global.save',\n                  defaultMessage: 'Save',\n                })}\n              </Button>\n            </Flex>\n          )\n        }\n        secondaryAction={\n          isInDevelopmentMode &&\n          !isFromPlugin &&\n          !isCreatingFirstContentType && (\n            <Button startIcon={<Pencil />} variant=\"tertiary\" onClick={onEdit}>\n              {formatMessage({\n                id: 'app.utils.edit',\n                defaultMessage: 'Edit',\n              })}\n            </Button>\n          )\n        }\n        title={upperFirst(label)}\n        subtitle={formatMessage({\n          id: getTrad('listView.headerLayout.description'),\n          defaultMessage: 'Build the data architecture of your content',\n        })}\n        navigationAction={<BackButton />}\n      />\n      <Layouts.Content>\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n          <Flex justifyContent=\"flex-end\">\n            <Flex gap={2}>\n              <LinkToCMSettingsView\n                key=\"link-to-cm-settings-view\"\n                targetUid={targetUid}\n                isTemporary={isTemporary}\n                isInContentTypeView={isInContentTypeView}\n                contentTypeKind={contentTypeKind}\n                disabled={isCreatingFirstContentType}\n              />\n            </Flex>\n          </Flex>\n          <Box background=\"neutral0\" shadow=\"filterShadow\" hasRadius>\n            <List\n              items={attributes}\n              customRowComponent={(props) => <ListRow {...props} onClick={handleClickEditField} />}\n              addComponentToDZ={handleClickAddComponentToDZ}\n              targetUid={targetUid}\n              editTarget={forTarget}\n              isMain\n            />\n          </Box>\n        </Flex>\n      </Layouts.Content>\n    </>\n  );\n};\n\n// eslint-disable-next-line import/no-default-export\nexport default ListView;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGa,IAAAA,eAAa,GAAO,GAAG;;;;;;;+BAOL,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;;;;;4BAQzC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;iBAIjD,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;;;;mBAK5B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;;;;;;;aAQpC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;AChC5C,IAAMC,MAAK,GAAO;;;;;;;;;;;;;;;;;;;;;;;UA0Bf,CAAC,EAAE,oBAAoB,uBAAuB,MAAA,MAAY;AAC1D,MAAI,uBAAuB;AAClB,WAAA,qBAAqB,MAAM,OAAO,UAAU;EACrD;AAEA,MAAI,oBAAoB;AACf,WAAA,qBAAqB,MAAM,OAAO,UAAU;EACrD;AAEO,SAAA,eAAe,MAAM,OAAO,UAAU;AAC/C,CAAC;;;;;;;;;ACtBF,IAAM,gBAAgB,CAAC;EAC5B;EACA;EACA,oBAAoB;EACpB,wBAAwB;EACxB;AACF,MAA0B;AAClB,QAAA,EAAE,aAAA,IAAiB,eAAA;AACnB,QAAA;IACJ,QAAQ,EAAE,WAAW;EAAA,QACnB,WAAAC,SAAI,cAAc,CAAC,cAAc,SAAS,GAAG;IAC/C,QAAQ,EAAE,YAAY,CAAA,EAAG;EAAA,CAC1B;AAGC,aAAA,wBAACD,KAAA,EAAG,uBAAuB,mBAAmB,WAAU,iBACtD,cAAA,wBAAC,MAAG,EAAA,SAAS,IACX,cAAA;IAAC;IAAA;MACC;MACA,OAAO;MACP,WAAW;MACX,uBAAuB,yBAAyB;MAChD,YAAW;MACX;MACA;MACA,OAAK;MACL,wBAAwB,wBAAwB,YAAY;IAAA;EAAA,EAEhE,CAAA,EACF,CAAA;AAEJ;ACvCO,IAAM,gBAAgB,CAAC,EAAE,WAAW,OAAO,OAAO,YAAA,MAAsC;AAC7F,QAAM,OAAO,gBAAgB,IAAI,KAAK,gBAAgB;AAGpD,aAAA;IAAC;IAAA;MACC,YAAW;MACX,YAAY,WAAW,eAAe;MACtC,gBAAe;MACf,QAAQ;MACR,OAAO;MACP,cAAa;MAEb,cAAC,wBAAA,MAAA,EAAK,QAAO,QAAO,OAAM,OAAA,CAAO;IAAA;EAAA;AAGvC;ACNA,IAAM,cAAc,GAAO,GAAG;;;;;;;;;;;cAWhB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;AAKpD,IAAM,eAAe,GAAO,IAAI;;;;sBAIV,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;gBAC5C,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;mBACnC,CAAC,EAAE,MAAA,MAAY,MAAM,YAAY;;;;;;wBAM5B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;kBAC5C,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;aAC3C,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;MAE7C,WAAW;;;;;;oBAMG,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;eAC3C,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;kBAInC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;;;AAOjD,IAAM,gBAAgB,CAAC;EAC5B;EACA;EACA;EACA,WAAW;EACX,sBAAsB;EACtB;AACF,MAA0B;AACxB,QAAM,EAAE,cAAc,+BAA+B,IAAI,eAAe;AAClE,QAAA;IACJ,QAAQ,EAAE,MAAM,YAAY;EAAA,QAC1B,WAAAC,SAAI,cAAc,CAAC,cAAc,SAAS,GAAG,EAAE,QAAQ,CAAC,EAAA,CAAG;AAEzD,QAAA,UAAU,CAAC,MAAW;AAC1B,MAAE,gBAAgB;AAClB,mCAA+B,QAAQ,KAAK;EAAA;AAI5C,aAAA;IAAC;IAAA;MACC,YAAW;MACX,WAAU;MACV,WAAW,WAAW,WAAW;MACjC,cAAa;MACb,gBAAe;MACf,aAAa;MACb,cAAc;MACd,QAAQ;MACR;MACA,MAAK;MACL,UAAU,WAAW,IAAI;MACzB,QAAO;MACP,iBAAe;MACf,iBAAe,MAAM,MAAM,UAAU,KAAK;MAC1C,IAAI,MAAM,MAAM,QAAQ,KAAK;MAE7B,UAAA;YAAC,wBAAA,eAAA,EAAc,MAAY,SAAoB,CAAA;YAE9C,wBAAA,KAAA,EAAI,WAAW,GAAG,UAAS,QAC1B,cAAA,wBAAC,YAAW,EAAA,SAAQ,MAAK,YAAW,QAAO,UAAQ,MAChD,UAAA,YACH,CAAA,EAAA,CACF;QAEC,2BAAA,wBACE,aAAY,EAAA,KAAI,UAAS,SAAS,SACjC,cAAC,wBAAA,eAAA,CAAA,CAAM,EACT,CAAA;MAAA;IAAA;EAAA;AAIR;AC/FA,IAAM,gBAAgB,GAAO,aAAI;;;;;gBAKjB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;YAE1C,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;AAIlD,IAAM,WAAW,GAAO,GAAG;;;;;;;AAQ3B,IAAM,kBAAkB,GAAO,IAAI;;;;AAKnC,IAAM,sBAAsB,GAAO,GAAG;;;AAItC,IAAM,iBAAiB,GAAO,IAAI;;;;;;;AAQ3B,IAAM,kBAAkB,CAAC;EAC9B;EACA,aAAa,CAAC;EACd;EACA;EACA;AACF,MAA4B;AACpB,QAAA,EAAE,oBAAA,IAAwB,eAAA;AAChC,QAAM,CAAC,WAAW,YAAY,QAAI,uBAAS,CAAC;AACtC,QAAA,EAAE,cAAA,IAAkB,QAAA;AAEpB,QAAA,SAAS,CAAC,QAAgB;AAC9B,QAAI,cAAc,KAAK;AACrB,mBAAa,GAAG;IAClB;EAAA;AAGF,QAAM,iBAAiB,MAAM;AAC3B,iBAAa,IAAI;EAAA;AAIjB,aAAA,wBAACD,KAAA,EAAG,WAAU,mBAAkB,oBAAkB,MAChD,cAAA,yBAAC,MAAG,EAAA,SAAS,IACX,UAAA;QAAA,wBAAC,UAAA,EAAS,aAAa,GACrB,cAAC,yBAAA,iBAAA,EAAgB,KAAK,GACnB,UAAA;MAAA,2BACE,wBAAA,UAAA,EAAO,MAAK,UAAS,SAAS,gBAC7B,cAAC,yBAAA,gBAAA,EAAe,WAAU,UAAS,YAAW,WAAU,KAAK,GAC3D,UAAA;YAAA,wBAAC,eAAc,CAAA,CAAA;YACf,wBAAC,YAAA,EAAW,SAAQ,MAAK,YAAW,QAAO,WAAU,cAClD,UAAc,cAAA;UACb,IAAI,QAAQ,sBAAsB;UAClC,gBAAgB;QACjB,CAAA,EAAA,CACH;MAAA,EAAA,CACF,EACF,CAAA;UAEF,wBAAC,MAAK,EAAA,MAAK,WAAU,KAAK,GACvB,UAAW,WAAA,IAAI,CAAC,WAAW,UAAU;AAElC,mBAAA;UAAC;UAAA;YAEC,QAAQ,QAAQ;YAChB;YACA;YACA,UAAU,cAAc;YACxB;YACA,SAAS,MAAM,OAAO,KAAK;UAAA;UANtB;QAAA;MASV,CAAA,EAAA,CACH;IAAA,EAAA,CACF,EACF,CAAA;QAAA,wBACC,qBACE,EAAA,UAAA,WAAW,IAAI,CAAC,WAAW,UAAU;AACpC,YAAM,QAAQ;QACZ;QACA;MAAA;AAIA,iBAAA;QAAC;QAAA;UACC,IAAI,MAAM,IAAI,UAAU,KAAK;UAC7B,MAAK;UACL,mBAAiB,MAAM,IAAI,QAAQ,KAAK;UAExC,OAAO,EAAE,SAAS,cAAc,QAAQ,UAAU,OAAO;UAEzD,cAAA,wBAAC,SACC,EAAA,cAAA,wBAAC,SACC,EAAA,cAAA;YAAC;YAAA;cACE,GAAG;cACJ,mBAAiB;cACjB,WAAW;cACX,KAAK;YAAA;UAAA,EAAA,CAET,EACF,CAAA;QAAA;QAZK;MAAA;IAeV,CAAA,EAAA,CACH;EAAA,EACF,CAAA,EACF,CAAA;AAEJ;AC/IA,IAAM,UAAU,GAAO,GAAG;;;;;;;;;;;;;;YAcd,CAAC,EAAE,OAAO,MAAA,MAAY,MAAM,OAAO,GAAG,KAAK,KAAK,CAAC;;;AAI7D,IAAM,YAAY,GAAO,GAAG;uBACL,CAAC,EAAE,MAAA,MAAY,MAAM,YAAY,IAAI,CAAC,EAAE,MAAA,MAAY,MAAM,YAAY;;;;;;;AAehF,IAAA,gBAAgB,CAAC,EAAE,UAAU,MAAM,OAAO,GAAG,MAAA,MAAgC;AACxF,aACG,wBAAA,WAAA,EAAU,eAAe,GAAG,YAAY,GAAG,KAAI,UAAS,MAAK,UAAU,GAAG,OACzE,cAAA,yBAAC,MACC,EAAA,UAAA;QAAC,wBAAA,SAAA,EAAQ,OAAc,eAAW,MAAC,YAAY,GAAG,KAAK,OACpD,UACH,KAAA,CAAA;QACC,wBAAA,KAAA,EAAI,aAAa,GAChB,cAAA,wBAAC,YAAW,EAAA,SAAQ,MAAK,YAAW,QAAO,WAAW,GAAG,KAAK,OAC3D,SACH,CAAA,EAAA,CACF;EAAA,EACF,CAAA,EACF,CAAA;AAEJ;ACRO,IAAM,OAAO,CAAC;EACnB;EACA;EACA;EACA;EACA,oBAAoB;EACpB,SAAS;EACT,wBAAwB;EACxB,QAAQ;EACR,QAAQ,CAAC;EACT;EACA;AACF,MAAiB;AACT,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,EAAE,qBAAqB,cAAc,oBAAA,IAAwB,eAAe;AAE5E,QAAA,EAAE,oBAAA,IAAwB,uBAAA;AAChC,QAAM,kBAAkB,MAAM;AAC5B,eAAW,6BAA6B;AAExC,wBAAoB,EAAE,WAAW,YAAY,UAAW,CAAA;EAAA;AAG1D,MAAI,CAAC,WAAW;AACd,eACG,yBAAA,OAAA,EAAM,UAAU,GAAG,UAAU,GAC5B,UAAA;UAAC,wBAAA,OAAA,EACC,cAAA,yBAACA,IACC,EAAA,UAAA;YAAA,wBAAC,IACC,EAAA,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA,EAAE,IAAI,eAAe,gBAAgB,OAAO,CAAC,EAC9D,CAAA,EAAA,CACF;YAAA,wBACC,IACC,EAAA,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA,EAAE,IAAI,eAAe,gBAAgB,OAAO,CAAC,EAC9D,CAAA,EAAA,CACF;MAAA,EAAA,CACF,EACF,CAAA;UAAA,wBACC,OACC,EAAA,cAAA,wBAACA,IAAAA,EACC,cAAC,wBAAA,IAAA,EAAG,SAAS,GACX,cAAA;QAAC;QAAA;UACC,SAAS,cAAc;YACrB,IAAI,QAAQ,yCAAyC;YACrD,gBAAgB;UAAA,CACjB;UACD,WAAS;UACT,UAAM,wBAAC,cAAe,EAAA,OAAM,QAAQ,CAAA;QAAA;MAAA,EAExC,CAAA,EACF,CAAA,EAAA,CACF;IACF,EAAA,CAAA;EAEJ;AAEI,MAAA,MAAM,WAAW,KAAK,QAAQ;AAChC,eACG,yBAAA,OAAA,EAAM,UAAU,GAAG,UAAU,GAC5B,UAAA;UAAC,wBAAA,OAAA,EACC,cAAA,yBAACA,IACC,EAAA,UAAA;YAAA,wBAAC,IACC,EAAA,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA,EAAE,IAAI,eAAe,gBAAgB,OAAO,CAAC,EAC9D,CAAA,EAAA,CACF;YAAA,wBACC,IACC,EAAA,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA,EAAE,IAAI,eAAe,gBAAgB,OAAO,CAAC,EAC9D,CAAA,EAAA,CACF;MAAA,EAAA,CACF,EACF,CAAA;UAAA,wBACC,OACC,EAAA,cAAA,wBAACA,IAAAA,EACC,cAAC,wBAAA,IAAA,EAAG,SAAS,GACX,cAAA;QAAC;QAAA;UACC,YACE;YAAC;YAAA;cACC,SAAS;cACT,MAAK;cACL,eAAA,wBAAY,eAAK,CAAA,CAAA;cACjB,SAAQ;cAEP,UAAc,cAAA;gBACb,IAAI,QAAQ,wBAAwB;gBACpC,gBAAgB;cAAA,CACjB;YAAA;UACH;UAEF,SAAS;YACP,sBACI;cACE,IAAI,QAAQ,yCAAyC;cACrD,gBAAgB;YAAA,IAElB;cACE,IAAI,QAAQ,mCAAmC;cAC/C,gBAAgB;YAClB;UACN;UACA,WAAS;UACT,UAAM,wBAAC,cAAe,EAAA,OAAM,QAAQ,CAAA;QAAA;MAAA,EAExC,CAAA,EACF,CAAA,EAAA,CACF;IACF,EAAA,CAAA;EAEJ;AAEA,aAAA,yBACGD,cACC,EAAA,UAAA;QAAA;MAAC;MAAA;QACC,aAAa;QACb,cAAc,SAAS,IAAI;QAC1B,GAAI,UAAU,EAAE,OAAO,EAAE,WAAW,OAAA,EAAS;QAE9C,cAAA,yBAAC,SACE,EAAA,UAAA;UACC,cAAA,wBAAC,SACC,EAAA,cAAA,yBAAC,MACC,EAAA,UAAA;gBAAA,wBAAC,MACC,EAAA,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA,EAAE,IAAI,eAAe,gBAAgB,OAAO,CAAC,EAC9D,CAAA,EAAA,CACF;gBAAA,wBACC,MAAG,EAAA,SAAS,GACX,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAA,cAAc,EAAE,IAAI,eAAe,gBAAgB,OAAO,CAAC,EAC9D,CAAA,EAAA,CACF;UAAA,EAAA,CACF,EACF,CAAA;cAED,wBAAA,SAAA,EACE,UAAM,MAAA,IAAI,CAAC,SAAS;AACb,kBAAA,EAAE,KAAS,IAAA;AACjB,kBAAM,YAAY;AAElB,uBAAA,yBACG,uBACC,EAAA,UAAA;kBAAA;gBAAC;gBAAA;kBACE,GAAG;kBACJ;kBACA;kBACA;kBACA;kBACA;kBACA;gBAAA;cACF;cAEC,SAAS,mBACR;gBAAC;gBAAA;kBACE,GAAG;kBACJ;kBACA;kBACA,uBAAuB;kBACvB;kBACA;gBAAA;cACF;cAGD,SAAS,qBACR;gBAAC;gBAAA;kBACE,GAAG;kBACJ;kBACA,cAAc;kBACd;gBAAA;cACF;YAAA,EAAA,GA5BW,KAAK,IA8BpB;UAEH,CAAA,EAAA,CACH;QAAA,EAAA,CACF;MAAA;IACF;IAEC,UAAU,2BACT,wBAAC,SAAQ,EAAA,UAAA,wBAAO,eAAK,CAAA,CAAA,GAAI,SAAS,iBAC/B,UAAc,cAAA;MACb,IAAI;QACF,4BACE,aAAa,cACT,aAAa,YAAY,OAAO,OAChC,cAAc,gBACpB;MACF;MACA,gBAAgB;IACjB,CAAA,EAAA,CACH;IAED,SAAS,uBAAuB,CAAC,yBAChC;MAAC;MAAA;QACC,UAAA,wBAAO,eAAK,CAAA,CAAA;QACZ,SAAS;QACT,OAAO,oBAAoB,YAAY;QAEtC,UAAc,cAAA;UACb,IAAI,QAAQ,oCAAoC;UAChD,gBAAgB;QAAA,CACjB;MAAA;IACH;EAEJ,EAAA,CAAA;AAEJ;AC5PA,IAAM,YAAY,GAAO,GAAG;;;;;;;;;kBASV,CAAC,EAAE,OAAO,MAAA,MAAY,MAAM,OAAO,KAAM,CAAC;;;;AAK5D,IAAM,MAAM,GAAO;;;;;;YAMP,CAAC,EAAE,OAAO,MAAA,MAAY,MAAM,OAAO,KAAM,CAAC;;;AAQ/C,IAAM,QAAQ,CAAC,cACpB,wBAAC,WACC,EAAA,cAAA;EAAC;EAAA;IACC,OAAM;IACN,QAAO;IACP,SAAQ;IACR,MAAK;IACL,OAAM;IACL,GAAG;IAEJ,cAAA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;MAAA;IACJ;EAAA;AACF,EAAA,CACF;ACpCK,IAAM,gBAAgB,CAAC;EAC5B;EACA,cAAc;EACd,aAAa;AACf,MAA0B;AAClB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,MAAI,eAAe;AAEf,MAAA,CAAC,WAAW,cAAc,SAAS,SAAS,EAAE,SAAS,IAAI,GAAG;AACjD,mBAAA;EAAA,WACN,CAAC,QAAQ,EAAE,SAAS,IAAI,GAAG;AACrB,mBAAA;EACjB;AAEA,MAAI,aAAa;AAEb,eAAA,wBAAC,YAAA,EACE,UAAc,cAAA;MACb,IAAI,QAAQ,uBAAuB;MACnC,gBAAgB;IACjB,CAAA,EACH,CAAA;EAEJ;AAGE,aAAA,yBAAC,YAAW,EAAA,WAAU,cACnB,UAAA;IAAc,cAAA;MACb,IAAI,QAAQ,aAAa,YAAY,EAAE;MACvC,gBAAgB;IAAA,CACjB;IAAE;IAEF,cACC,cAAc;MACZ,IAAI,QAAQ,sBAAsB;MAClC,gBAAgB;IAAA,CACjB;EACL,EAAA,CAAA;AAEJ;ACjDa,IAAA,aAAa,CAAC,EAAE,QAAA,UAAsC,wBAAAG,mBAAAA,UAAA,EAAA,cAAA,kBAAAC,SAAW,OAAO,EAAA,CAAE;ACc1E,IAAA,aAAa,GAAO,GAAG;;;AA0B7B,IAAM,cAAU;EACrB,CAAC;IACC,eAAe;IACf,cAAc;IACd;IACA,wBAAwB;IACxB,oBAAoB;IACpB;IACA;IACA,WAAW;IACX,aAAa;IACb,yBAAyB;IACzB,SAAS;IACT,YAAY;IACZ;EAAA,MACkB;AAClB,UAAM,EAAE,cAAc,qBAAqB,gBAAA,IAAoB,eAAe;AACxE,UAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,UAAM,UAAU,SAAS,cAAc,SAAS,SAAS,OAAO;AAC1D,UAAA,MAAM,CAAC,WAAW,cAAc,SAAS,SAAS,EAAE,SAAS,IAAI,IAAI,WAAW;AAEtF,UAAM,kBAAc,WAAAF,SAAI,cAAc,CAAC,MAAmC,GAAG,CAAA,CAAE;AAC/E,UAAM,8BAA0B,WAAAA,SAAI,aAAa,CAAC,UAAU,aAAa,GAAG,EAAE;AACxE,UAAA,0BAAsB,WAAAA,SAAI,aAAa,QAAQ;AAE/C,UAAA,MAAM,SAAS,aAAa;AAElC,UAAM,cAAc,MAAM;AACxB,UAAI,SAAS;AACX;MACF;AAEA,UAAI,iBAAiB,OAAO;AAC1B,cAAM,WAAW;AAEjB;;UAEE;;UAEA,0BAA0B,yBAAyB;;UAEnD;;UAEA;UACA;QAAA;MAEJ;IAAA;AAEE,QAAA;AAEJ,QAAI,0BAA0B,uBAAuB;AACtC,mBAAA;IAAA,WACJ,uBAAuB;AACnB,mBAAA;IAAA,OACR;AACQ,mBAAA;IACf;AAGE,eAAA;MAAC;MAAA;QACC,KAAI;QACJ,SAAS,uBAAuB,gBAAgB,CAAC,UAAU,cAAc;QAEzE,UAAA;cAAA,yBAAC,MAAG,EAAA,OAAO,EAAE,UAAU,WACpB,GAAA,UAAA;YAAA,eAAe,SAAM,wBAAA,OAAA,EAAM,OAAO,oBAAoB,eAAe,aAAA,CAAc;gBACnF,yBAAA,MAAA,EAAK,aAAa,GAAG,KAAK,GACzB,UAAA;kBAAC,wBAAA,eAAA,EAAc,MAAM,KAAK,YAA0B,CAAA;kBAAA,wBACnD,YAAW,EAAA,WAAU,cAAa,YAAW,QAC3C,UACH,KAAA,CAAA;YAAA,EAAA,CACF;UAAA,EAAA,CACF;cAAA,wBACC,MACE,EAAA,UAAA,aACE,yBAAA,YAAA,EAAW,WAAU,cACnB,UAAA;YAAc,cAAA;cACb,IAAI;gBACF,uBAAuB,UAAU,yBAAyB,cAAc;cAC1E;cACA,gBAAgB;YAAA,CACjB;YAAE;gBAAA,yBAEF,QAAK,EAAA,OAAO,EAAE,WAAW,SACxB,GAAA,UAAA;kBAAC,wBAAA,YAAA,EAAW,SAAS,wBAAyB,CAAA;cAAE;cAE/C,uBACC,IAAI,cAAc;gBAChB,IAAI,QAAQ,MAAM;gBAClB,gBAAgB;cAAA,CACjB,CAAC,KAAK,mBAAmB;YAAA,EAAA,CAC9B;UAAA,EAAA,CACF,QAEC,wBAAA,eAAA,EAAc,MAAY,aAA0B,WAAwB,CAAA,EAAA,CAEjF;cAAA,wBACC,MACE,EAAA,UAAA,0BACE,wBAAA,MAAA,EAAK,gBAAe,YAAW,SAAS,CAAC,MAAM,EAAE,gBAAA,GAC/C,UAAA,mBACE,yBAAA,MAAA,EAAK,KAAK,GACR,UAAA;YAAA,CAAC,eACA;cAAC;cAAA;gBACC,SAAS;gBACT,OAAO,GAAG,cAAc;kBACtB,IAAI;kBACJ,gBAAgB;gBAAA,CACjB,CAAC,IAAI,IAAI;gBACV,SAAQ;gBAER,cAAA,wBAAC,eAAO,CAAA,CAAA;cAAA;YACV;gBAEF;cAAC;cAAA;gBACC,SAAS,CAAC,MAAM;AACd,oBAAE,gBAAgB;AAClB;oBACE;oBACA;oBACA,0BAA0B,yBAAyB;kBAAA;gBAEvD;gBACA,OAAO,GAAG,cAAc;kBACtB,IAAI;kBACJ,gBAAgB;gBAAA,CACjB,CAAC,IAAI,IAAI;gBACV,SAAQ;gBAER,cAAA,wBAAC,cAAM,CAAA,CAAA;cAAA;YACT;UACF,EAAA,CAAA,QAEC,wBAAA,eAAA,CAAA,CAAK,EAEV,CAAA;;;;;;gBAOA,wBAAC,KAAI,EAAA,QAAO,SAAS,CAAA;YAAA,CAEzB;QAAA;MAAA;IAAA;EAGN;AACF;AC9La,IAAA,4BAA4B,CAAC,SAAiB;AACrD,MAAA;AAEJ,UAAQ,MAAM;IACZ,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACa,sBAAA;AAChB;IACF,KAAK;IACL,KAAK;IACL,KAAK;IACL,KAAK;AACa,sBAAA;AAChB;IACF,KAAK;IACL,KAAK;AACa,sBAAA;AAChB;IACF,KAAK;AACa,sBAAA;AAChB;IACF;AACkB,sBAAA;EACpB;AAEO,SAAA;AACT;ACpBA,IAAM,gBAA8C;EAClD,+BAA+B;IAC7B;MACE,QAAQ;MACR,SAAS;IACX;EACF;EACA,0BAA0B;IACxB;MACE,QAAQ;MACR,SAAS;IACX;EACF;EACA,2BAA2B;IACzB;MACE,QAAQ;MACR,SAAS;IACX;EACF;AACF;AAUO,IAAM,2BAAuB;EAClC,CAAC;IACC;IACA,cAAc;IACd,sBAAsB;IACtB,kBAAkB;IAClB,YAAY;EAAA,MACmB;AACzB,UAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,UAAM,WAAW,YAAA;AACjB,UAAM,EAAE,+BAA+B,0BAA0B,0BAAA,IAC/D;AACF,UAAM,QAAQ,cAAc;MAC1B,IAAI;MACJ,gBAAgB;IAAA,CACjB;AACD,QAAI,qBAAqB;AAEzB,UAAM,cAAc,MAAM;AACxB,UAAI,aAAa;AACR,eAAA;MACT;AAEA,UAAI,qBAAqB;AACd,iBAAA,qCAAqC,SAAS,sBAAsB;MAAA,OACxE;AACI,iBAAA,+BAA+B,SAAS,sBAAsB;MACzE;AAEO,aAAA;IAAA;AAGL,QAAA,uBAAuB,oBAAoB,cAAc;AACtC,2BAAA;IACvB;AAEA,QAAI,CAAC,qBAAqB;AACH,2BAAA;IACvB;AACA,UAAM,EAAE,WAAW,eAAe,IAAI,QAAQ;MAC5C,YAAY;IAAA,CACb;AAED,QAAI,WAAW;AACN,aAAA;IACT;AAEI,QAAA,CAAC,eAAe,kBAAkB;AAC7B,aAAA;IACT;AAGE,eAAA;MAAC;MAAA;QACC,eAAA,wBAAY,eAAS,CAAA,CAAA;QACrB,SAAQ;QACR,SAAS;QACT,UAAU,eAAe;QAExB,UAAA;MAAA;IAAA;EAGP;AACF;AC7EA,IAAM,sBAAsB,GAAO,QAAQ,MAAM;;;AAIjD,IAAM,WAAW,MAAM;AACrB,QAAM,EAAE,aAAa,cAAc,qBAAqB,qBAAqB,WAAA,IAC3E,eAAA;AACI,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,WAAA,IAAe,YAAA;AAEjB,QAAA,QAAQ,SAAS,iDAAiD;AAElE,QAAA;IACJ;IACA;IACA;IACA;IACA;EAAA,IACE,uBAAuB;AAErB,QAAA,oBAAoB,sBAAsB,gBAAgB;AAChE,QAAM,6BAA6B,CAAC,mBAAmB,UAAU,YAAY;AAC7E,QAAM,gBAAY,WAAAA,SAAI,cAAc,CAAC,mBAAmB,KAAK,CAAC;AAC9D,QAAM,kBAAc,WAAAA,SAAI,cAAc,CAAC,mBAAmB,aAAa,GAAG,KAAK;AACzE,QAAA,sBAAkB,WAAAA,SAAI,cAAc,CAAC,mBAAmB,UAAU,MAAM,GAAG,IAAI;AAErF,QAAM,iBAAa,WAAAA,SAAI,cAAc,4BAA4B,CAAE,CAAA;AACnE,QAAM,mBAAe,WAAAG,SAAI,aAAa,CAAC,mBAAmB,QAAQ,CAAC;AACnE,QAAM,uBAAuB,KAAC,eAAAC,SAAQ,cAAc,WAAW;AAEzD,QAAA,YAAY,sBAAsB,gBAAgB;AAElD,QAAA,8BAA8B,CAAC,sBAA+B;AACrC,iCAAA,EAAE,mBAAmB,UAAA,CAAW;EAAA;AAG/D,QAAM,uBAAuB,OAC3BC,YACAC,YACA,eACA,MACA,gBACG;AACG,UAAA,gBAAgB,0BAA0B,IAAI;AAC9C,UAAA,OAAO,SAAS,cAAc,MAAM;AAE1C,QAAI,aAAa;AACY,iCAAA;QACzB,WAAAD;QACA,WAAAC;QACA;QACA;QACA,gBAAgB;MAAA,CACjB;IAAA,OACI;AACgB,2BAAA;QACnB,WAAAD;QACA,WAAAC;QACA;QACA;QACA;MAAA,CACD;IACH;EAAA;AAGE,MAAA,YAAQ,WAAAN,SAAI,cAAc,CAAC,mBAAmB,UAAU,aAAa,GAAG,EAAE;AACxE,QAAA,WAAO,WAAAA,SAAI,cAAc,CAAC,mBAAmB,UAAU,MAAM,GAAG,EAAE;AAElE,QAAA,8BAA6B,+BAAO,OAAO,gBAAe;AAE5D,MAAA,CAAC,SAAS,4BAA4B;AACxC,YAAQ,cAAc;MACpB,IAAI,QAAQ,qBAAqB;MACjC,gBAAgB;IAAA,CACjB;EACH;AAEA,QAAM,SAAS,MAAM;AACnB,UAAM,cAAc,QAAQ;AAE5B,QAAI,gBAAgB,kBAAkB;AACpC,iBAAW,2BAA2B;IACxC;AACA,QAAI,gBAAgB,cAAc;AAChC,iBAAW,0BAA0B;IACvC;AAEsB,0BAAA;MACpB,WAAW;MACX,WAAW;MACX;MACA,MAAM;IAAA,CACP;EAAA;AAGOO,YAAA;IACR,MAAM;IACN,SAAS,cAAc,EAAE,IAAI,QAAQ,gBAAgB,GAAG,gBAAgB,gBAAA,CAAiB;EAAA,CAC1F;AAED,aAEI,yBAAAN,mBAAAA,UAAA,EAAA,UAAA;QAAA;MAAC;MAAA;QACC,IAAG;QACH,eACE,2BACE,yBAAC,MAAA,EAAK,KAAK,GAAG,YAAY,GAEvB,UAAA;UAAA,CAAC,kCACA;YAAC;YAAA;cACC,eAAA,wBAAY,eAAK,CAAA,CAAA;cACjB,SAAQ;cACR,UAAS;cACT,SAAS,MAAM;AACO,oCAAA,EAAE,WAAW,UAAA,CAAW;cAC9C;cAEC,UAAc,cAAA;gBACb,IAAI,QAAQ,+BAA+B;gBAC3C,gBAAgB;cAAA,CACjB;YAAA;UACH;cAEF;YAAC;YAAA;cACC,eAAA,wBAAY,eAAM,CAAA,CAAA;cAClB,SAAS,YAAY,MAAM,WAAW;cACtC,MAAK;cACL,cAAU,eAAAG,SAAQ,cAAc,WAAW;cAE1C,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA;UACH;QAAA,EAAA,CACF;QAGJ,iBACE,uBACA,CAAC,gBACD,CAAC,kCACE,wBAAA,QAAA,EAAO,eAAW,wBAAC,eAAA,CAAA,CAAO,GAAI,SAAQ,YAAW,SAAS,QACxD,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QACjB,CAAA,EAAA,CACH;QAGJ,WAAO,kBAAAF,SAAW,KAAK;QACvB,UAAU,cAAc;UACtB,IAAI,QAAQ,mCAAmC;UAC/C,gBAAgB;QAAA,CACjB;QACD,sBAAA,wBAAmB,YAAW,CAAA,CAAA;MAAA;IAChC;QACA,wBAAC,QAAQ,SAAR,EACC,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;UAAA,wBAAC,MAAA,EAAK,gBAAe,YACnB,cAAC,wBAAA,MAAA,EAAK,KAAK,GACT,cAAA;QAAC;QAAA;UAEC;UACA;UACA;UACA;UACA,UAAU;QAAA;QALN;MAAA,EAAA,CAOR,EACF,CAAA;UAAA,wBACC,KAAI,EAAA,YAAW,YAAW,QAAO,gBAAe,WAAS,MACxD,cAAA;QAAC;QAAA;UACC,OAAO;UACP,oBAAoB,CAAC,cAAU,wBAAC,SAAA,EAAS,GAAG,OAAO,SAAS,qBAAA,CAAsB;UAClF,kBAAkB;UAClB;UACA,YAAY;UACZ,QAAM;QAAA;MAAA,EAAA,CAEV;IAAA,EAAA,CACF,EACF,CAAA;EACF,EAAA,CAAA;AAEJ;", "names": ["BoxWrapper", "Tr", "get", "Fragment", "upperFirst", "has", "isEqual", "for<PERSON><PERSON><PERSON>", "targetUid", "usePrompt"]}