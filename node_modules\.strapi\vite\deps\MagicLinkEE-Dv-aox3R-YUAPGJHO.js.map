{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/Users/<USER>/MagicLinkEE.tsx"], "sourcesContent": ["import { useIntl } from 'react-intl';\n\nimport { getBasename } from '../../../../../../../../admin/src/core/utils/basename';\nimport { MagicLinkWrapper } from '../../../../../../../../admin/src/pages/Settings/pages/Users/<USER>/MagicLinkWrapper';\n\nimport type { MagicLinkCEProps } from '../../../../../../../../admin/src/pages/Settings/pages/Users/<USER>/MagicLinkCE';\n\n// FIXME replace with parts compo when ready\nexport const MagicLinkEE = ({ registrationToken }: MagicLinkCEProps) => {\n  const { formatMessage } = useIntl();\n\n  if (registrationToken) {\n    return (\n      <MagicLinkWrapper\n        target={`${\n          window.location.origin\n        }${getBasename()}/auth/register?registrationToken=${registrationToken}`}\n      >\n        {formatMessage({\n          id: 'app.components.Users.MagicLink.connect',\n          defaultMessage: 'Copy and share this link to give access to this user',\n        })}\n      </MagicLinkWrapper>\n    );\n  }\n\n  return (\n    <MagicLinkWrapper target={`${window.location.origin}${getBasename()}/auth/login`}>\n      {formatMessage({\n        id: 'app.components.Users.MagicLink.connect.sso',\n        defaultMessage:\n          'Send this link to the user, the first login can be made via a SSO provider.',\n      })}\n    </MagicLinkWrapper>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;AAQO,IAAM,cAAc,CAAC,EAAE,kBAAA,MAA0C;AAChE,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,MAAI,mBAAmB;AAEnB,eAAA;MAAC;MAAA;QACC,QAAQ,GACN,OAAO,SAAS,MAClB,GAAG,YAAA,CAAa,oCAAoC,iBAAiB;QAEpE,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IAAA;EACH;AAKF,aAAA,wBAAC,kBAAiB,EAAA,QAAQ,GAAG,OAAO,SAAS,MAAM,GAAG,YAAA,CAAa,eAChE,UAAc,cAAA;IACb,IAAI;IACJ,gBACE;EAAA,CACH,EACH,CAAA;AAEJ;", "names": []}