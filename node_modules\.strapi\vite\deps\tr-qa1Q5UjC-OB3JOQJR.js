import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/tr-qa1Q5UjC.mjs
var tr = {
  "BoundRoute.title": "Bağlı rota",
  "EditForm.inputSelect.description.role": "Yeni kimliği doğrulanmış kullanıcıyı seçilen rolü ekler.",
  "EditForm.inputSelect.label.role": "<PERSON>liği doğrulanmış kullanıcılar için varsayılan rol",
  "EditForm.inputToggle.description.email": "Kullanıcıyı, farklı kimlik doğrulama sağlayıcılarıyla aynı e-posta adresini kullanarak birden fazla hesap oluşturmasına izin vermeyin.",
  "EditForm.inputToggle.description.email-confirmation": "Etkinleştirildiğinde yeni kayıtlı kullanıcılar bir onay e-postası alır.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "E-postanızı onayladıktan sonra, yönlendirileceğiniz yeri seçin.",
  "EditForm.inputToggle.description.sign-up": "Devre dışı bırakıldığında (KAPALI), kayıt işlemi yasaktır. Artık kullanılan sağlayıcı ne olursa olsun hiç kimse abone olamaz.",
  "EditForm.inputToggle.label.email": "E-posta adresi başına bir hesap",
  "EditForm.inputToggle.label.email-confirmation": "E-posta onayını etkinleştir",
  "EditForm.inputToggle.label.email-confirmation-redirection": "Yönlendirme URL'si",
  "EditForm.inputToggle.label.sign-up": "Kayıtları etkinleştir",
  "EditForm.inputToggle.placeholder.email-confirmation-redirection": "ör: https://yourfrontend.com/email-confirmation-redirection",
  "EditForm.inputToggle.placeholder.email-reset-password": "ör: https://yourfrontend.com/reset-password",
  "EditPage.form.roles": "Rol detayları",
  "Email.template.data.loaded": "E-posta şablonları yüklendi",
  "Email.template.email_confirmation": "E-posta adresi doğrulaması",
  "Email.template.form.edit.label": "Şablonu düzenle",
  "Email.template.table.action.label": "eylem",
  "Email.template.table.icon.label": "ikon",
  "Email.template.table.name.label": "ad",
  "Form.advancedSettings.data.loaded": "Gelişmiş ayarlar verisi yüklendi",
  "HeaderNav.link.advancedSettings": "Gelişmiş Ayarlar",
  "HeaderNav.link.emailTemplates": "E-posta Şablonları",
  "HeaderNav.link.providers": "Sağlayıcıları",
  "Plugin.permissions.plugins.description": "{name} eklentisi için izin verilen tüm eylemleri tanımlayın.",
  "Plugins.header.description": "Yalnızca bir güzergahla sınırlandırılan işlemler aşağıda listelenmiştir.",
  "Plugins.header.title": "İzinler",
  "Policies.header.hint": "Uygulamanın eylemlerini veya eklentinin eylemlerini seçin ve bağlı rotayı görüntülemek için dişli çark simgesini tıklayın",
  "Policies.header.title": "Gelişmiş Ayarlar",
  "PopUpForm.Email.email_templates.inputDescription": "Değişkenleri nasıl kullanacağınızdan emin değilseniz, {link}",
  "PopUpForm.Email.link.documentation": "dokümantasyonu kontrol et.",
  "PopUpForm.Email.options.from.email.label": "Gönderenin E-posta",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Gönderenin adı",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "Mesaj",
  "PopUpForm.Email.options.object.label": "Konu",
  "PopUpForm.Email.options.object.placeholder": "%APP_NAME% için e-posta adresini doğrula",
  "PopUpForm.Email.options.response_email.label": "Yanıt e-postası",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Devre dışı bırakıldıysa kullanıcılar bu sağlayıcıyı kullanamaz.",
  "PopUpForm.Providers.enabled.label": "Etkinleştirme",
  "PopUpForm.Providers.key.label": "Web istemcisi ID",
  "PopUpForm.Providers.key.placeholder": "METİN",
  "PopUpForm.Providers.redirectURL.front-end.label": "Arayüz uygulamanızın yönlendirme URL'si",
  "PopUpForm.Providers.redirectURL.label": "{provider} uygulama ayarlarına ekleyeceğin yönlendirme URLi",
  "PopUpForm.Providers.secret.label": "Web istemcisi Secret",
  "PopUpForm.Providers.secret.placeholder": "METİN",
  "PopUpForm.Providers.subdomain.label": "Host URI (Subdomain)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "E-posta Şablonlarını Düzenle",
  "PopUpForm.header.edit.providers": "Sağlayıcıyı Düzenle",
  "Providers.data.loaded": "Sağlayıcılar yüklendi",
  "Providers.image": "Görsel",
  "Providers.status": "Durum",
  "Roles.empty": "Henüz hiç rolün yok.",
  "Roles.empty.search": "Aramaya uygun rol bulunmadı.",
  "Settings.roles.deleted": "Rol silindi",
  "Settings.roles.edited": "Rol düzenlendi",
  "Settings.section-label": "Kullanıcılar ve İzinler eklentisi",
  "components.Input.error.validation.email": "Bu geçersiz bir e-posta",
  "components.Input.error.validation.json": "Bu JSON biçimine uymuyor",
  "components.Input.error.validation.max": "Değer çok yüksek.",
  "components.Input.error.validation.maxLength": "Değer çok uzun.",
  "components.Input.error.validation.min": "Değer çok düşük.",
  "components.Input.error.validation.minLength": "Değer çok kısa.",
  "components.Input.error.validation.minSupMax": "Üst olamaz",
  "components.Input.error.validation.regex": "Değer RegExp'e uymuyor.",
  "components.Input.error.validation.required": "Değer gerekli.",
  "components.Input.error.validation.unique": "Değer zaten kullanılıyor.",
  "notification.success.submit": "Ayarlar güncellendi",
  "page.title": "Ayarlar - Roller",
  "plugin.description.long": "Servisinizi JWT'ye dayalı tam bir kimlik doğrulama işlemi ile koruyun. Bu eklenti, kullanıcı grupları arasındaki izinleri yönetmenize izin veren bir ACL stratejisiyle de gelir.",
  "plugin.description.short": "Servisinizi JWT'ye dayalı tam bir kimlik doğrulama işlemi ile koruyun",
  "plugin.name": "Roller ve İzinler",
  "popUpWarning.button.cancel": "İptal Et",
  "popUpWarning.button.confirm": "Onayla",
  "popUpWarning.title": "Lütfen onayla",
  "popUpWarning.warning.cancel": "Değişiklikleri iptal etmek istediğinden emin misin?"
};
export {
  tr as default
};
//# sourceMappingURL=tr-qa1Q5UjC-OB3JOQJR.js.map
