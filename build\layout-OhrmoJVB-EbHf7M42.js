import{at as B,a as Q,c9 as G,H as V,cG as N,c as Y,eK as T,bV as K,fj as U,r as m,fo as q,bl as E,cM as J,eI as Z,eP as X,fp as ee,a5 as te,bO as se,m as e,n as k,aV as D,L as ne,J as M,w as y,I as P,f4 as F,s as A,cQ as ae,bE as ie,S as O,am as re,b3 as oe,cb as le,ay as ce,eQ as de,fq as ue,d as pe,cc as ge,cd as he,ce as fe,cf as me,cg as ye,_ as Te}from"./strapi-YzJfjJ2z.js";import{C as xe}from"./CardDragPreview-DOxamsuj-B5-jyj7W.js";import{F as je,L as Le,D as be}from"./Relations-7ItTFWp7-CaFM264H.js";import{a as Se,u as v}from"./hooks-E5u1mcgM-Ce79Lxm5.js";import{I as S}from"./useDragAndDrop-DdHgKsqq-DDLMDz6i.js";import{u as Ce}from"./useDragLayer-Bta-luUM.js";import"./relations-sRERvWmr-drnKn_J5.js";import"./getEmptyImage-CjqolaH3.js";function Ie(s,a,n){if(!s||!a||!n)return{display:"none"};const{x:t,y:p}=n;return{transform:`translate(${t}px, ${p}px)`}}const Me=({renderItem:s})=>{const{itemType:a,isDragging:n,item:t,initialOffset:p,currentOffset:l,mouseOffset:g}=Ce(i=>({item:i.getItem(),itemType:i.getItemType(),initialOffset:i.getInitialSourceClientOffset(),currentOffset:i.getSourceClientOffset(),isDragging:i.isDragging(),mouseOffset:i.getClientOffset()}));return n?e.jsx(M,{height:"100%",left:0,position:"fixed",pointerEvents:"none",top:0,zIndex:100,width:"100%",children:e.jsx(M,{style:Ie(p,l,g),children:s({type:a,item:t})})}):null},we=({displayedValue:s})=>e.jsxs(y,{background:"neutral0",borderColor:"neutral200",justifyContent:"space-between",gap:3,padding:3,width:"30rem",children:[e.jsx(De,{type:"button",children:e.jsxs(y,{gap:6,children:[e.jsx(ke,{alignItems:"center",justifyContent:"center",background:"neutral200",height:"3.2rem",width:"3.2rem",children:e.jsx(re,{})}),e.jsx(y,{maxWidth:"15rem",children:e.jsx(A,{textColor:"neutral700",ellipsis:!0,children:s})})]})}),e.jsxs(y,{gap:2,children:[e.jsx(P,{withTooltip:!1,label:"",variant:"ghost",children:e.jsx(oe,{})}),e.jsx(P,{withTooltip:!1,label:"",variant:"ghost",children:e.jsx(F,{})})]})]}),ke=O(y)`
  border-radius: 50%;

  svg {
    height: 0.6rem;
    width: 1.1rem;
    > path {
      fill: ${({theme:s})=>s.colors.neutral600};
    }
  }
`,De=O.button`
  border: none;
  background: transparent;
  display: block;
  width: 100%;
  text-align: unset;
  padding: 0;
`,Ee=({status:s,displayedValue:a,width:n})=>e.jsx(M,{style:{width:n},children:e.jsxs(y,{paddingTop:2,paddingBottom:2,paddingLeft:2,paddingRight:4,hasRadius:!0,borderWidth:1,background:"neutral0",borderColor:"neutral200",justifyContent:"space-between",gap:4,children:[e.jsxs(je,{gap:1,children:[e.jsx(P,{withTooltip:!1,label:"",variant:"ghost",children:e.jsx(F,{})}),e.jsxs(y,{width:"100%",minWidth:0,justifyContent:"space-between",children:[e.jsx(M,{minWidth:0,paddingTop:1,paddingBottom:1,paddingRight:4,children:e.jsx(Le,{href:"",children:e.jsx(A,{textColor:"primary600",ellipsis:!0,children:a})})}),s?e.jsx(ae,{status:s}):null]})]}),e.jsx(be,{type:"button",children:e.jsx(ie,{width:"12px"})})]})}),Pe=O(ye)`
  div {
    width: inherit;
    span:nth-child(2) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: inherit;
    }
  }
`,ve=()=>{const[s,a]=m.useState(""),[{query:n}]=ce(),{formatMessage:t,locale:p}=N(),l=v(o=>o["content-manager"].app.collectionTypeLinks),g=v(o=>o["content-manager"].app.singleTypeLinks),{schemas:i}=de(),{startsWith:r}=ue(p,{sensitivity:"base"}),c=pe(p,{sensitivity:"base"}),x=m.useMemo(()=>[{id:"collectionTypes",title:t({id:T("components.LeftMenu.collection-types"),defaultMessage:"Collection Types"}),searchable:!0,links:l},{id:"singleTypes",title:t({id:T("components.LeftMenu.single-types"),defaultMessage:"Single Types"}),searchable:!0,links:g}].map(o=>({...o,links:o.links.filter(d=>r(d.title,s)).sort((d,j)=>c.compare(d.title,j.title)).map(d=>({...d,title:t({id:d.title,defaultMessage:d.title})}))})),[l,s,g,r,t,c]),u=()=>{a("")},h=({target:{value:o}})=>{a(o)},C=t({id:T("header.name"),defaultMessage:"Content Manager"}),w=o=>{const j=!!i.find(L=>L.uid===o.uid)?.pluginOptions?.i18n?.localized;if(n.plugins&&"i18n"in n.plugins){const{i18n:L,...I}=n.plugins;return j?{i18n:L,...I}:I}return n.plugins};return e.jsxs(ge,{"aria-label":C,children:[e.jsx(he,{label:C,searchable:!0,value:s,onChange:h,onClear:u,searchLabel:t({id:"content-manager.components.LeftMenu.Search.label",defaultMessage:"Search for a content type"})}),e.jsx(fe,{children:x.map(o=>e.jsx(me,{label:o.title,badgeLabel:o.links.length.toString(),children:o.links.map(d=>e.jsx(Pe,{tag:Te,to:{pathname:d.to,search:E.stringify({...E.parse(d.search??""),plugins:w(d)})},width:"100%",children:d.title},d.uid))},o.id))})]})},{MUTATE_COLLECTION_TYPES_LINKS:Ne,MUTATE_SINGLE_TYPES_LINKS:Oe}=X,_e=()=>{const{toggleNotification:s}=Q(),a=Se(),n=G("useContentManagerInitData",u=>u.runHookWaterfall),{notifyStatus:t}=V(),{formatMessage:p}=N(),{_unstableFormatAPIError:l}=Y(T),g=K("useContentManagerInitData",u=>u.checkUserHasPermissions),i=v(u=>u["content-manager"].app),r=U(void 0,{refetchOnMountOrArgChange:!0});m.useEffect(()=>{r.data&&t(p({id:T("App.schemas.data-loaded"),defaultMessage:"The schemas have been successfully loaded."}))},[p,r.data,t]),m.useEffect(()=>{r.error&&s({type:"danger",message:l(r.error)})},[l,r.error,s]);const c=q();m.useEffect(()=>{c.error&&s({type:"danger",message:l(c.error)})},[l,c.error,s]);const x=async(u,h,C,w)=>{const{collectionType:o,singleType:d}=h.reduce((f,b)=>(f[b.kind].push(b),f),{collectionType:[],singleType:[]}),j=_(o,"collectionTypes",w),L=_(d,"singleTypes"),I=await Promise.all(j.map(({permissions:f})=>g(f))),R=j.filter((f,b)=>I[b].length>0),$=await Promise.all(L.map(({permissions:f})=>g(f))),z=L.filter((f,b)=>$[b].length>0),{ctLinks:H}=n(Ne,{ctLinks:R,models:h}),{stLinks:W}=n(Oe,{stLinks:z,models:h});a(ee({authorizedCollectionTypeLinks:H,authorizedSingleTypeLinks:W,components:u,contentTypeSchemas:h,fieldSizes:C}))};return m.useEffect(()=>{r.data&&c.data&&x(r.data.components,r.data.contentTypes,r.data.fieldSizes,c.data)},[r.data,c.data]),{...i}},_=(s,a,n=[])=>s.filter(t=>t.isDisplayed).map(t=>{const p=[{action:"plugin::content-manager.explorer.create",subject:t.uid},{action:"plugin::content-manager.explorer.read",subject:t.uid}],l=[{action:"plugin::content-manager.explorer.read",subject:t.uid}],g=a==="collectionTypes"?p:l,i=n.find(({uid:c})=>c===t.uid);let r=null;if(i){const c={page:1,pageSize:i.settings.pageSize,sort:`${i.settings.defaultSortBy}:${i.settings.defaultSortOrder}`};r=E.stringify(c,{encode:!1})}return{permissions:g,search:r,kind:t.kind,title:t.info.displayName,to:`/content-manager/${t.kind==="collectionType"?J:Z}/${t.uid}`,uid:t.uid,name:t.uid,isDisplayed:t.isDisplayed}}),Ge=()=>{const s=B("/content-manager/:kind/:uid/*"),{isLoading:a,collectionTypeLinks:n,models:t,singleTypeLinks:p}=_e(),l=[...n,...p].sort((u,h)=>u.title.localeCompare(h.title)),{pathname:g}=te(),{formatMessage:i}=N(),r=se("Layout",u=>u.startSection),c=m.useRef(r);if(m.useEffect(()=>{c.current&&c.current("contentManager")},[]),a)return e.jsxs(e.Fragment,{children:[e.jsx(k.Title,{children:i({id:T("plugin.name"),defaultMessage:"Content Manager"})}),e.jsx(k.Loading,{})]});const x=t.filter(({isDisplayed:u})=>u);return l.length===0&&x.length>0&&g!=="/content-manager/403"?e.jsx(D,{to:"/403"}):x.length===0&&g!=="/no-content-types"?e.jsx(D,{to:"/no-content-types"}):!s&&l.length>0?e.jsx(D,{to:{pathname:l[0].to,search:l[0].search??""},replace:!0}):e.jsxs(e.Fragment,{children:[e.jsx(k.Title,{children:i({id:T("plugin.name"),defaultMessage:"Content Manager"})}),e.jsxs(ne.Root,{sideNav:e.jsx(ve,{}),children:[e.jsx(Me,{renderItem:Fe}),e.jsx(le,{})]})]})};function Fe({type:s,item:a}){if(!s||s&&typeof s!="string")return null;const[n]=s.split("_");switch(n){case S.EDIT_FIELD:case S.FIELD:return e.jsx(xe,{label:a.label});case S.COMPONENT:case S.DYNAMIC_ZONE:return e.jsx(we,{displayedValue:a.displayedValue});case S.RELATION:return e.jsx(Ee,{...a});default:return null}}export{Ge as Layout};
