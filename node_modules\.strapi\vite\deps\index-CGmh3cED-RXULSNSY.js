import {
  <PERSON><PERSON>,
  <PERSON>,
  useReviewWorkflows
} from "./chunk-AQWMSGXK.js";
import {
  CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME,
  LimitsModal,
  useGetContentTypesQuery,
  useTypedSelector
} from "./chunk-5ZFC5BBE.js";
import {
  useIntl
} from "./chunk-CH6LMMF3.js";
import "./chunk-C7H2BX76.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-D63J2BWQ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-VOKTPSOW.js";
import {
  useLicenseLimits
} from "./chunk-N562NCQ4.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  ConfirmDialog,
  Table
} from "./chunk-ELTZWS66.js";
import {
  Page,
  useRBAC,
  useTracking
} from "./chunk-4C2ZQ5OG.js";
import {
  Dialog,
  Flex,
  IconButton,
  LinkButton,
  TFooter,
  Typography
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  Link,
  NavLink,
  useNavigate
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1d,
  ForwardRef$1r,
  ForwardRef$j
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/review-workflows/dist/_chunks/index-CGmh3cED.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var ReviewWorkflowsListView = () => {
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const { trackUsage } = useTracking();
  const [workflowToDelete, setWorkflowToDelete] = React.useState(null);
  const [showLimitModal, setShowLimitModal] = React.useState(false);
  const { data, isLoading: isLoadingModels } = useGetContentTypesQuery();
  const { meta, workflows, isLoading, delete: deleteAction } = useReviewWorkflows();
  const { getFeature, isLoading: isLicenseLoading } = useLicenseLimits();
  const permissions = useTypedSelector(
    (state) => {
      var _a;
      return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["review-workflows"];
    }
  );
  const {
    allowedActions: { canCreate, canRead, canUpdate, canDelete }
  } = useRBAC(permissions);
  const limits = getFeature("review-workflows");
  const numberOfWorkflows = limits == null ? void 0 : limits[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME];
  const handleDeleteWorkflow = (workflowId) => {
    setWorkflowToDelete(workflowId);
  };
  const toggleConfirmDeleteDialog = () => {
    setWorkflowToDelete(null);
  };
  const handleConfirmDeleteDialog = async () => {
    if (!workflowToDelete)
      return;
    await deleteAction(workflowToDelete);
    setWorkflowToDelete(null);
  };
  const handleCreateClick = (event) => {
    event.preventDefault();
    if (numberOfWorkflows && meta && (meta == null ? void 0 : meta.workflowCount) >= parseInt(numberOfWorkflows, 10)) {
      event.preventDefault();
      setShowLimitModal(true);
    } else {
      navigate("create");
      trackUsage("willCreateWorkflow");
    }
  };
  React.useEffect(() => {
    if (!isLoading && !isLicenseLoading) {
      if (numberOfWorkflows && meta && (meta == null ? void 0 : meta.workflowCount) > parseInt(numberOfWorkflows, 10)) {
        setShowLimitModal(true);
      }
    }
  }, [isLicenseLoading, isLoading, meta, meta == null ? void 0 : meta.workflowCount, numberOfWorkflows]);
  const headers = [
    {
      label: formatMessage({
        id: "Settings.review-workflows.list.page.list.column.name.title",
        defaultMessage: "Name"
      }),
      name: "name"
    },
    {
      label: formatMessage({
        id: "Settings.review-workflows.list.page.list.column.stages.title",
        defaultMessage: "Stages"
      }),
      name: "stages"
    },
    {
      label: formatMessage({
        id: "Settings.review-workflows.list.page.list.column.contentTypes.title",
        defaultMessage: "Content Types"
      }),
      name: "content-types"
    }
  ];
  if (isLoading || isLoadingModels) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  const contentTypes = Object.values(data ?? {}).reduce((acc, curr) => {
    acc.push(...curr);
    return acc;
  }, []);
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(
      Header,
      {
        primaryAction: canCreate ? (0, import_jsx_runtime.jsx)(
          LinkButton,
          {
            startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
            size: "S",
            tag: NavLink,
            to: "create",
            onClick: handleCreateClick,
            children: formatMessage({
              id: "Settings.review-workflows.list.page.create",
              defaultMessage: "Create new workflow"
            })
          }
        ) : null,
        subtitle: formatMessage({
          id: "Settings.review-workflows.list.page.subtitle",
          defaultMessage: "Manage your content review process"
        }),
        title: formatMessage({
          id: "Settings.review-workflows.list.page.title",
          defaultMessage: "Review Workflows"
        })
      }
    ),
    (0, import_jsx_runtime.jsxs)(Root, { children: [
      (0, import_jsx_runtime.jsx)(
        Table.Root,
        {
          isLoading,
          rows: workflows,
          footer: canCreate ? (0, import_jsx_runtime.jsx)(TFooter, { icon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}), onClick: handleCreateClick, children: formatMessage({
            id: "Settings.review-workflows.list.page.create",
            defaultMessage: "Create new workflow"
          }) }) : null,
          headers,
          children: (0, import_jsx_runtime.jsxs)(Table.Content, { children: [
            (0, import_jsx_runtime.jsx)(Table.Head, { children: headers.map((head) => (0, import_jsx_runtime.jsx)(Table.HeaderCell, { ...head }, head.name)) }),
            (0, import_jsx_runtime.jsx)(Table.Body, { children: workflows.map((workflow) => (0, import_jsx_runtime.jsxs)(
              Table.Row,
              {
                onClick: () => {
                  navigate(`${workflow.id}`);
                },
                children: [
                  (0, import_jsx_runtime.jsx)(Table.Cell, { width: "25rem", children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", fontWeight: "bold", ellipsis: true, children: workflow.name }) }),
                  (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: workflow.stages.length }) }),
                  (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: workflow.contentTypes.map((uid) => {
                    const contentType = contentTypes.find(
                      (contentType2) => contentType2.uid === uid
                    );
                    return (contentType == null ? void 0 : contentType.info.displayName) ?? "";
                  }).join(", ") }) }),
                  (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsxs)(Flex, { alignItems: "center", justifyContent: "end", children: [
                    canRead || canUpdate ? (0, import_jsx_runtime.jsx)(
                      IconButton,
                      {
                        tag: Link,
                        to: workflow.id.toString(),
                        label: formatMessage(
                          {
                            id: "Settings.review-workflows.list.page.list.column.actions.edit.label",
                            defaultMessage: "Edit {name}"
                          },
                          { name: workflow.name }
                        ),
                        variant: "ghost",
                        children: (0, import_jsx_runtime.jsx)(ForwardRef$1r, {})
                      }
                    ) : null,
                    workflows.length > 1 && canDelete ? (0, import_jsx_runtime.jsx)(
                      IconButton,
                      {
                        withTooltip: false,
                        label: formatMessage(
                          {
                            id: "Settings.review-workflows.list.page.list.column.actions.delete.label",
                            defaultMessage: "Delete {name}"
                          },
                          { name: "Default workflow" }
                        ),
                        variant: "ghost",
                        onClick: (e) => {
                          e.stopPropagation();
                          handleDeleteWorkflow(String(workflow.id));
                        },
                        children: (0, import_jsx_runtime.jsx)(ForwardRef$j, {})
                      }
                    ) : null
                  ] }) })
                ]
              },
              workflow.id
            )) })
          ] })
        }
      ),
      (0, import_jsx_runtime.jsx)(Dialog.Root, { open: !!workflowToDelete, onOpenChange: toggleConfirmDeleteDialog, children: (0, import_jsx_runtime.jsx)(ConfirmDialog, { onConfirm: handleConfirmDeleteDialog, children: formatMessage({
        id: "Settings.review-workflows.list.page.delete.confirm.body",
        defaultMessage: "If you remove this worfklow, all stage-related information will be removed for this content-type. Are you sure you want to remove it?"
      }) }) }),
      (0, import_jsx_runtime.jsxs)(LimitsModal.Root, { open: showLimitModal, onOpenChange: () => setShowLimitModal(false), children: [
        (0, import_jsx_runtime.jsx)(LimitsModal.Title, { children: formatMessage({
          id: "Settings.review-workflows.list.page.workflows.limit.title",
          defaultMessage: "You’ve reached the limit of workflows in your plan"
        }) }),
        (0, import_jsx_runtime.jsx)(LimitsModal.Body, { children: formatMessage({
          id: "Settings.review-workflows.list.page.workflows.limit.body",
          defaultMessage: "Delete a workflow or contact Sales to enable more workflows."
        }) })
      ] })
    ] })
  ] });
};
var ProtectedListPage = () => {
  const permissions = useTypedSelector(
    (state) => {
      var _a, _b;
      return (_b = (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["review-workflows"]) == null ? void 0 : _b.main;
    }
  );
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions, children: (0, import_jsx_runtime.jsx)(ReviewWorkflowsListView, {}) });
};
export {
  ProtectedListPage,
  ReviewWorkflowsListView
};
//# sourceMappingURL=index-CGmh3cED-RXULSNSY.js.map
