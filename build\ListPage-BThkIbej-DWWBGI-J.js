import{au as L,ax as _,c as B,a as N,r as M,ay as $,e as V,al as q,a$ as W,b as H,m as e,n as f,L as b,A as U,b0 as w,b1 as z,T as J,o as Q,p as O,q as x,s as i,V as G,t as K,v as p,w as X,J as Y,I as Z,b2 as ee,F as se,b3 as te,b4 as ae,b5 as ne,b6 as oe,b7 as le}from"./strapi-YzJfjJ2z.js";import{u as ie}from"./useAdminRoles-Bd2N7J7A-CIPQp8aL.js";import{s as re}from"./selectors-ZSFBgSp8-CGZgPRVm.js";const de=({id:s,name:o,description:a,usersCount:d,icons:c,rowIndex:m,canUpdate:u})=>{const{formatMessage:g}=L(),[,r]=c,j=g({id:"Roles.RoleRow.user-count",defaultMessage:"{number, plural, =0 {#  user} one {#  user} other {# users}}"},{number:d});return e.jsxs(O,{"aria-rowindex":m,onClick:u?r.onClick:void 0,children:[e.jsx(p,{maxWidth:"13rem",children:e.jsx(i,{ellipsis:!0,textColor:"neutral800",children:o})}),e.jsx(p,{maxWidth:"25rem",children:e.jsx(i,{ellipsis:!0,textColor:"neutral800",children:a})}),e.jsx(p,{children:e.jsx(i,{textColor:"neutral800",children:j})}),e.jsx(p,{children:e.jsx(X,{justifyContent:"flex-end",onClick:n=>n.stopPropagation(),children:c.map((n,h)=>n?e.jsx(Y,{paddingLeft:h===0?0:1,children:e.jsx(Z,{...n,variant:"ghost"})},n.label):null)})})]},s)},ce=()=>{const{formatMessage:s}=L(),o=_(re),{formatAPIError:a}=B(),{toggleNotification:d}=N(),[c,m]=M.useState(!1),[{query:u}]=$(),{isLoading:g,allowedActions:{canCreate:r,canDelete:j,canRead:n,canUpdate:h}}=V(o.settings?.roles),{roles:T,refetch:A}=ie({filters:u?._q?{name:{$containsi:u._q}}:void 0},{refetchOnMountOrArgChange:!0,skip:g||!n}),E=q(),[{roleToDelete:y},C]=M.useReducer(ge,ue),{post:k}=H(),P=async()=>{try{C({type:"ON_REMOVE_ROLES"}),await k("/admin/roles/batch-delete",{ids:[y]}),await A(),C({type:"RESET_DATA_TO_DELETE"})}catch(t){le(t)&&d({type:"danger",message:a(t)})}},R=()=>E("new"),D=()=>m(t=>!t),S=t=>l=>{l.preventDefault(),l.stopPropagation(),t.usersCount?d({type:"info",message:s({id:"Roles.ListPage.notification.delete-not-allowed"})}):(C({type:"SET_ROLE_TO_DELETE",id:t.id}),D())},v=t=>l=>{l.preventDefault(),l.stopPropagation(),E(`duplicate/${t.id}`)},F=T.length+1,I=6;return g?e.jsx(f.Loading,{}):e.jsxs(f.Main,{children:[e.jsx(f.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(b.Header,{primaryAction:r?e.jsx(U,{onClick:R,startIcon:e.jsx(w,{}),size:"S",children:s({id:"Settings.roles.list.button.add",defaultMessage:"Add new role"})}):null,title:s({id:"global.roles",defaultMessage:"roles"}),subtitle:s({id:"Settings.roles.list.description",defaultMessage:"List of roles"})}),n&&e.jsx(b.Action,{startActions:e.jsx(z,{label:s({id:"app.component.search.label",defaultMessage:"Search for {target}"},{target:s({id:"global.roles",defaultMessage:"roles"})})})}),n&&e.jsx(b.Content,{children:e.jsxs(J,{colCount:I,rowCount:F,footer:r?e.jsx(ae,{onClick:R,icon:e.jsx(w,{}),children:s({id:"Settings.roles.list.button.add",defaultMessage:"Add new role"})}):null,children:[e.jsx(Q,{children:e.jsxs(O,{"aria-rowindex":1,children:[e.jsx(x,{children:e.jsx(i,{variant:"sigma",textColor:"neutral600",children:s({id:"global.name",defaultMessage:"Name"})})}),e.jsx(x,{children:e.jsx(i,{variant:"sigma",textColor:"neutral600",children:s({id:"global.description",defaultMessage:"Description"})})}),e.jsx(x,{children:e.jsx(i,{variant:"sigma",textColor:"neutral600",children:s({id:"global.users",defaultMessage:"Users"})})}),e.jsx(x,{children:e.jsx(G,{children:s({id:"global.actions",defaultMessage:"Actions"})})})]})}),e.jsx(K,{children:T?.map((t,l)=>e.jsx(de,{id:t.id,name:t.name,description:t.description,usersCount:t.usersCount,icons:[r&&{onClick:v(t),label:s({id:"app.utils.duplicate",defaultMessage:"Duplicate"}),children:e.jsx(ee,{})},h&&{onClick:()=>E(t.id.toString()),label:s({id:"app.utils.edit",defaultMessage:"Edit"}),children:e.jsx(se,{})},j&&{onClick:S(t),label:s({id:"global.delete",defaultMessage:"Delete"}),children:e.jsx(te,{})}].filter(Boolean),rowIndex:l+2,canUpdate:h},t.id))})]})}),e.jsx(ne.Root,{open:c,onOpenChange:D,children:e.jsx(oe,{onConfirm:P})})]})},ue={roleToDelete:null,showModalConfirmButtonLoading:!1,shouldRefetchData:!1},ge=(s,o)=>W(s,a=>{switch(o.type){case"ON_REMOVE_ROLES":{a.showModalConfirmButtonLoading=!0;break}case"ON_REMOVE_ROLES_SUCCEEDED":{a.shouldRefetchData=!0,a.roleToDelete=null;break}case"RESET_DATA_TO_DELETE":{a.shouldRefetchData=!1,a.roleToDelete=null,a.showModalConfirmButtonLoading=!1;break}case"SET_ROLE_TO_DELETE":{a.roleToDelete=o.id;break}default:return a}}),fe=()=>{const s=_(o=>o.admin_app.permissions.settings?.roles.read);return e.jsx(f.Protect,{permissions:s,children:e.jsx(ce,{})})};export{ce as ListPage,fe as ProtectedListPage};
