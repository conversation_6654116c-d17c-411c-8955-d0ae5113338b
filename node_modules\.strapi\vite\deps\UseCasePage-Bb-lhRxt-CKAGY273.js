import {
  PrivateRoute
} from "./chunk-7D54EQG2.js";
import {
  Layout<PERSON>ontent,
  Logo,
  UnauthenticatedLayout
} from "./chunk-ELTZWS66.js";
import {
  useAuth,
  useNotification
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Button,
  Field,
  Flex,
  Main,
  SingleSelect,
  SingleSelectOption,
  TextButton,
  TextInput,
  Typography,
  require_lib,
  useIntl
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  useLocation,
  useNavigate
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/UseCasePage-Bb-lhRxt.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
var options = [
  {
    intlLabel: {
      id: "Usecase.front-end",
      defaultMessage: "Front-end developer"
    },
    value: "front_end_developer"
  },
  {
    intlLabel: {
      id: "Usecase.back-end",
      defaultMessage: "Back-end developer"
    },
    value: "back_end_developer"
  },
  {
    intlLabel: {
      id: "Usecase.full-stack",
      defaultMessage: "Full-stack developer"
    },
    value: "full_stack_developer"
  },
  {
    intlLabel: {
      id: "global.content-manager",
      defaultMessage: "Content Manager"
    },
    value: "content_manager"
  },
  {
    intlLabel: {
      id: "Usecase.content-creator",
      defaultMessage: "Content Creator"
    },
    value: "content_creator"
  },
  {
    intlLabel: {
      id: "Usecase.other",
      defaultMessage: "Other"
    },
    value: "other"
  }
];
var UseCasePage = () => {
  const { toggleNotification } = useNotification();
  const location = useLocation();
  const navigate = useNavigate();
  const { formatMessage } = useIntl();
  const [role, setRole] = React.useState(null);
  const [otherRole, setOtherRole] = React.useState("");
  const { firstname, email } = useAuth("UseCasePage", (state) => state.user) ?? {};
  const { hasAdmin } = (0, import_qs.parse)(location.search, { ignoreQueryPrefix: true });
  const isOther = role === "other";
  const handleSubmit = async (event, skipPersona) => {
    event.preventDefault();
    try {
      await fetch("https://analytics.strapi.io/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email,
          username: firstname,
          firstAdmin: Boolean(!hasAdmin),
          persona: {
            role: skipPersona ? void 0 : role,
            otherRole: skipPersona ? void 0 : otherRole
          }
        })
      });
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "Usecase.notification.success.project-created",
          defaultMessage: "Project has been successfully created"
        })
      });
      navigate("/");
    } catch (err) {
    }
  };
  return (0, import_jsx_runtime.jsx)(UnauthenticatedLayout, { children: (0, import_jsx_runtime.jsxs)(Main, { labelledBy: "usecase-title", children: [
    (0, import_jsx_runtime.jsx)(LayoutContent, { children: (0, import_jsx_runtime.jsxs)("form", { onSubmit: (e) => handleSubmit(e, false), children: [
      (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", paddingBottom: 7, children: [
        (0, import_jsx_runtime.jsx)(Logo, {}),
        (0, import_jsx_runtime.jsx)(Box, { paddingTop: 6, paddingBottom: 1, width: `25rem`, children: (0, import_jsx_runtime.jsx)(Typography, { textAlign: "center", variant: "alpha", tag: "h1", id: "usecase-title", children: formatMessage({
          id: "Usecase.title",
          defaultMessage: "Tell us a bit more about yourself"
        }) }) })
      ] }),
      (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 6, children: [
        (0, import_jsx_runtime.jsxs)(Field.Root, { name: "usecase", children: [
          (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
            id: "Usecase.input.work-type",
            defaultMessage: "What type of work do you do?"
          }) }),
          (0, import_jsx_runtime.jsx)(SingleSelect, { onChange: (value) => setRole(value), value: role, children: options.map(({ intlLabel, value }) => (0, import_jsx_runtime.jsx)(SingleSelectOption, { value, children: formatMessage(intlLabel) }, value)) })
        ] }),
        isOther && (0, import_jsx_runtime.jsxs)(Field.Root, { name: "other", children: [
          (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({ id: "Usecase.other", defaultMessage: "Other" }) }),
          (0, import_jsx_runtime.jsx)(TextInput, { value: otherRole, onChange: (e) => setOtherRole(e.target.value) })
        ] }),
        (0, import_jsx_runtime.jsx)(Button, { type: "submit", size: "L", fullWidth: true, disabled: !role, children: formatMessage({ id: "global.finish", defaultMessage: "Finish" }) })
      ] })
    ] }) }),
    (0, import_jsx_runtime.jsx)(Flex, { justifyContent: "center", children: (0, import_jsx_runtime.jsx)(Box, { paddingTop: 4, children: (0, import_jsx_runtime.jsx)(
      TextButton,
      {
        onClick: (event) => handleSubmit(event, true),
        children: formatMessage({
          id: "Usecase.button.skip",
          defaultMessage: "Skip this question"
        })
      }
    ) }) })
  ] }) });
};
var PrivateUseCasePage = () => {
  return (0, import_jsx_runtime.jsx)(PrivateRoute, { children: (0, import_jsx_runtime.jsx)(UseCasePage, {}) });
};
export {
  PrivateUseCasePage,
  UseCasePage,
  options
};
//# sourceMappingURL=UseCasePage-Bb-lhRxt-CKAGY273.js.map
