{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/NoPermissionsPage.tsx"], "sourcesContent": ["import { Page, Layouts } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\n\nimport { getTranslation } from '../utils/translations';\n\nconst NoPermissions = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <>\n      <Layouts.Header\n        title={formatMessage({\n          id: getTranslation('header.name'),\n          defaultMessage: 'Content',\n        })}\n      />\n      <Layouts.Content>\n        <Page.NoPermissions />\n      </Layouts.Content>\n    </>\n  );\n};\n\nexport { NoPermissions };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,IAAM,gBAAgB,MAAM;AACpB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc;UACnB,IAAI,eAAe,aAAa;UAChC,gBAAgB;QAAA,CACjB;MAAA;IACH;QACA,wBAAC,QAAQ,SAAR,EACC,cAAA,wBAAC,KAAK,eAAL,CAAmB,CAAA,EAAA,CACtB;EACF,EAAA,CAAA;AAEJ;", "names": []}