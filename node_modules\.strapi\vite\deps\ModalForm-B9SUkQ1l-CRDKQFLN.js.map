{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/Users/<USER>/ModalForm.tsx"], "sourcesContent": ["import type { FormLayoutInputProps } from '../../../../../../../../admin/src/types/forms';\n\nexport const FORM_INITIAL_VALUES = {\n  ...(window.strapi.features.isEnabled(window.strapi.features.SSO)\n    ? {\n        useSSORegistration: true,\n      }\n    : {}),\n};\n\nexport const ROLE_LAYOUT = [\n  ...(window.strapi.features.isEnabled(window.strapi.features.SSO)\n    ? [\n        [\n          {\n            label: {\n              id: 'Settings.permissions.users.form.sso',\n              defaultMessage: 'Connect with SSO',\n            },\n            name: 'useSSORegistration',\n            type: 'boolean' as const,\n            size: 6,\n          },\n        ],\n      ]\n    : []),\n] satisfies FormLayoutInputProps[][];\n"], "mappings": ";;;AAEO,IAAM,sBAAsB;EACjC,GAAI,OAAO,OAAO,SAAS,UAAU,OAAO,OAAO,SAAS,GAAG,IAC3D;IACE,oBAAoB;EAAA,IAEtB,CAAA;AACN;AAEO,IAAM,cAAc;EACzB,GAAI,OAAO,OAAO,SAAS,UAAU,OAAO,OAAO,SAAS,GAAG,IAC3D;IACE;MACE;QACE,OAAO;UACL,IAAI;UACJ,gBAAgB;QAAA;QAElB,MAAM;QACN,MAAM;QACN,MAAM;MAAA;IACR;EACF,IAEF,CAAA;AACN;", "names": []}