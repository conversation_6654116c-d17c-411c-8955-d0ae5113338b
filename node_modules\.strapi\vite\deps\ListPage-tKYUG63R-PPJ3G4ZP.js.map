{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Webhooks/ListPage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  useNotifyAT,\n  Checkbox,\n  Button,\n  EmptyStateLayout,\n  Flex,\n  IconButton,\n  Switch,\n  Table,\n  T<PERSON>,\n  T<PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>r,\n  <PERSON><PERSON><PERSON>,\n  <PERSON>ly<PERSON><PERSON><PERSON>,\n  LinkButton,\n  Dialog,\n} from '@strapi/design-system';\nimport { Pencil, Plus, Trash } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useNavigate } from 'react-router-dom';\n\nimport { UpdateWebhook } from '../../../../../../shared/contracts/webhooks';\nimport { ConfirmDialog } from '../../../../components/ConfirmDialog';\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useRBAC } from '../../../../hooks/useRBAC';\n\nimport { useWebhooks } from './hooks/useWebhooks';\n\n/* -------------------------------------------------------------------------------------------------\n * ListPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ListPage = () => {\n  const [showModal, setShowModal] = React.useState(false);\n  const [webhooksToDelete, setWebhooksToDelete] = React.useState<string[]>([]);\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings?.webhooks);\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const navigate = useNavigate();\n\n  const {\n    isLoading: isRBACLoading,\n    allowedActions: { canCreate, canUpdate, canDelete },\n  } = useRBAC(permissions);\n  const { notifyStatus } = useNotifyAT();\n\n  const {\n    isLoading: isWebhooksLoading,\n    webhooks,\n    error: webhooksError,\n    updateWebhook,\n    deleteManyWebhooks,\n  } = useWebhooks();\n\n  React.useEffect(() => {\n    if (webhooksError) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(webhooksError),\n      });\n\n      return;\n    }\n    if (webhooks) {\n      notifyStatus(\n        formatMessage({\n          id: 'Settings.webhooks.list.loading.success',\n          defaultMessage: 'Webhooks have been loaded',\n        })\n      );\n    }\n  }, [webhooks, webhooksError, toggleNotification, formatMessage, notifyStatus, formatAPIError]);\n\n  const enableWebhook = async (body: UpdateWebhook.Request['body'] & UpdateWebhook.Params) => {\n    try {\n      const res = await updateWebhook(body);\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    }\n  };\n\n  const deleteWebhook = async (id: string) => {\n    try {\n      const res = await deleteManyWebhooks({\n        ids: [id],\n      });\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n\n        return;\n      }\n\n      setWebhooksToDelete((prev) => prev.filter((webhookId) => webhookId !== id));\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    }\n  };\n\n  const confirmBulkDelete = async () => {\n    try {\n      const res = await deleteManyWebhooks({\n        ids: webhooksToDelete,\n      });\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n\n        return;\n      }\n\n      setWebhooksToDelete([]);\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    } finally {\n      setShowModal(false);\n    }\n  };\n\n  const selectAllCheckbox = (selected: boolean) =>\n    selected\n      ? setWebhooksToDelete(webhooks?.map((webhook) => webhook.id) ?? [])\n      : setWebhooksToDelete([]);\n\n  const selectOneCheckbox = (selected: boolean, id: string) =>\n    selected\n      ? setWebhooksToDelete((prev) => [...prev, id])\n      : setWebhooksToDelete((prev) => prev.filter((webhookId) => webhookId !== id));\n\n  const isLoading = isRBACLoading || isWebhooksLoading;\n  const numberOfWebhooks = webhooks?.length ?? 0;\n  const webhooksToDeleteLength = webhooksToDelete.length;\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Webhooks',\n          }\n        )}\n      </Page.Title>\n      <Page.Main aria-busy={isLoading}>\n        <Layouts.Header\n          title={formatMessage({ id: 'Settings.webhooks.title', defaultMessage: 'Webhooks' })}\n          subtitle={formatMessage({\n            id: 'Settings.webhooks.list.description',\n            defaultMessage: 'Get POST changes notifications',\n          })}\n          primaryAction={\n            canCreate &&\n            !isLoading && (\n              <LinkButton tag={NavLink} startIcon={<Plus />} variant=\"default\" to=\"create\" size=\"S\">\n                {formatMessage({\n                  id: 'Settings.webhooks.list.button.add',\n                  defaultMessage: 'Create new webhook',\n                })}\n              </LinkButton>\n            )\n          }\n        />\n        {webhooksToDeleteLength > 0 && canDelete && (\n          <Layouts.Action\n            startActions={\n              <>\n                <Typography variant=\"epsilon\" textColor=\"neutral600\">\n                  {formatMessage(\n                    {\n                      id: 'Settings.webhooks.to.delete',\n                      defaultMessage:\n                        '{webhooksToDeleteLength, plural, one {# webhook} other {# webhooks}} selected',\n                    },\n                    { webhooksToDeleteLength }\n                  )}\n                </Typography>\n                <Button\n                  onClick={() => setShowModal(true)}\n                  startIcon={<Trash />}\n                  size=\"L\"\n                  variant=\"danger-light\"\n                >\n                  {formatMessage({\n                    id: 'global.delete',\n                    defaultMessage: 'Delete',\n                  })}\n                </Button>\n              </>\n            }\n          />\n        )}\n        <Layouts.Content>\n          {numberOfWebhooks > 0 ? (\n            <Table\n              colCount={5}\n              rowCount={numberOfWebhooks + 1}\n              footer={\n                <TFooter\n                  onClick={() => {\n                    if (canCreate) {\n                      navigate('create');\n                    }\n                  }}\n                  icon={<Plus />}\n                >\n                  {formatMessage({\n                    id: 'Settings.webhooks.list.button.add',\n                    defaultMessage: 'Create new webhook',\n                  })}\n                </TFooter>\n              }\n            >\n              <Thead>\n                <Tr>\n                  <Th>\n                    <Checkbox\n                      aria-label={formatMessage({\n                        id: 'global.select-all-entries',\n                        defaultMessage: 'Select all entries',\n                      })}\n                      checked={\n                        webhooksToDeleteLength > 0 && webhooksToDeleteLength < numberOfWebhooks\n                          ? 'indeterminate'\n                          : webhooksToDeleteLength === numberOfWebhooks\n                      }\n                      onCheckedChange={selectAllCheckbox}\n                    />\n                  </Th>\n                  <Th width=\"20%\">\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'global.name',\n                        defaultMessage: 'Name',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th width=\"60%\">\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'Settings.webhooks.form.url',\n                        defaultMessage: 'URL',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th width=\"20%\">\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'Settings.webhooks.list.th.status',\n                        defaultMessage: 'Status',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th>\n                    <VisuallyHidden>\n                      {formatMessage({\n                        id: 'Settings.webhooks.list.th.actions',\n                        defaultMessage: 'Actions',\n                      })}\n                    </VisuallyHidden>\n                  </Th>\n                </Tr>\n              </Thead>\n              <Tbody>\n                {webhooks?.map((webhook) => (\n                  <Tr\n                    key={webhook.id}\n                    onClick={() => {\n                      if (canUpdate) {\n                        navigate(webhook.id);\n                      }\n                    }}\n                    style={{ cursor: canUpdate ? 'pointer' : 'default' }}\n                  >\n                    <Td onClick={(e) => e.stopPropagation()}>\n                      <Checkbox\n                        aria-label={`${formatMessage({\n                          id: 'global.select',\n                          defaultMessage: 'Select',\n                        })} ${webhook.name}`}\n                        checked={webhooksToDelete?.includes(webhook.id)}\n                        onCheckedChange={(selected) => selectOneCheckbox(!!selected, webhook.id)}\n                        name=\"select\"\n                      />\n                    </Td>\n                    <Td>\n                      <Typography fontWeight=\"semiBold\" textColor=\"neutral800\">\n                        {webhook.name}\n                      </Typography>\n                    </Td>\n                    <Td>\n                      <Typography textColor=\"neutral800\">{webhook.url}</Typography>\n                    </Td>\n                    <Td onClick={(e) => e.stopPropagation()}>\n                      <Flex>\n                        <Switch\n                          onLabel={formatMessage({\n                            id: 'global.enabled',\n                            defaultMessage: 'Enabled',\n                          })}\n                          offLabel={formatMessage({\n                            id: 'global.disabled',\n                            defaultMessage: 'Disabled',\n                          })}\n                          aria-label={`${webhook.name} ${formatMessage({\n                            id: 'Settings.webhooks.list.th.status',\n                            defaultMessage: 'Status',\n                          })}`}\n                          checked={webhook.isEnabled}\n                          onCheckedChange={(enabled) => {\n                            enableWebhook({\n                              ...webhook,\n                              isEnabled: enabled,\n                            });\n                          }}\n                          visibleLabels\n                        />\n                      </Flex>\n                    </Td>\n                    <Td>\n                      <Flex gap={1}>\n                        {canUpdate && (\n                          <IconButton\n                            label={formatMessage({\n                              id: 'Settings.webhooks.events.update',\n                              defaultMessage: 'Update',\n                            })}\n                            variant=\"ghost\"\n                          >\n                            <Pencil />\n                          </IconButton>\n                        )}\n                        {canDelete && (\n                          <DeleteActionButton\n                            onDelete={() => {\n                              deleteWebhook(webhook.id);\n                            }}\n                          />\n                        )}\n                      </Flex>\n                    </Td>\n                  </Tr>\n                ))}\n              </Tbody>\n            </Table>\n          ) : (\n            <EmptyStateLayout\n              icon={<EmptyDocuments width=\"160px\" />}\n              content={formatMessage({\n                id: 'Settings.webhooks.list.empty.description',\n                defaultMessage: 'No webhooks found',\n              })}\n              action={\n                canCreate ? (\n                  <LinkButton variant=\"secondary\" startIcon={<Plus />} tag={NavLink} to=\"create\">\n                    {formatMessage({\n                      id: 'Settings.webhooks.list.button.add',\n                      defaultMessage: 'Create new webhook',\n                    })}\n                  </LinkButton>\n                ) : null\n              }\n            />\n          )}\n        </Layouts.Content>\n      </Page.Main>\n      <Dialog.Root open={showModal} onOpenChange={setShowModal}>\n        <ConfirmDialog onConfirm={confirmBulkDelete} />\n      </Dialog.Root>\n    </Layouts.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DeleteActionButton\n * -----------------------------------------------------------------------------------------------*/\n\ntype DeleteActionButtonProps = {\n  onDelete: () => void;\n};\n\nconst DeleteActionButton = ({ onDelete }: DeleteActionButtonProps) => {\n  const [showModal, setShowModal] = React.useState(false);\n  const { formatMessage } = useIntl();\n\n  return (\n    <>\n      <IconButton\n        onClick={(e) => {\n          e.stopPropagation();\n          setShowModal(true);\n        }}\n        label={formatMessage({\n          id: 'Settings.webhooks.events.delete',\n          defaultMessage: 'Delete webhook',\n        })}\n        variant=\"ghost\"\n      >\n        <Trash />\n      </IconButton>\n\n      <Dialog.Root open={showModal} onOpenChange={setShowModal}>\n        <ConfirmDialog\n          onConfirm={(e) => {\n            e?.stopPropagation();\n            onDelete();\n          }}\n        />\n      </Dialog.Root>\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedListView\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedListPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.webhooks.main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListPage />\n    </Page.Protect>\n  );\n};\n\nexport { ListPage, ProtectedListPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,IAAM,WAAW,MAAM;AACrB,QAAM,CAAC,WAAW,YAAY,IAAU,eAAS,KAAK;AACtD,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAAmB,CAAA,CAAE;AACrE,QAAA,cAAc,iBAAiB,CAAC,UAAA;;AAAU,uBAAM,UAAU,YAAY,aAA5B,mBAAsC;GAAQ;AACxF,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AACjE,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,WAAW,YAAY;AAEvB,QAAA;IACJ,WAAW;IACX,gBAAgB,EAAE,WAAW,WAAW,UAAU;EAAA,IAChD,QAAQ,WAAW;AACjB,QAAA,EAAE,aAAa,IAAI,YAAY;AAE/B,QAAA;IACJ,WAAW;IACX;IACA,OAAO;IACP;IACA;EAAA,IACE,YAAY;AAEhB,EAAM,gBAAU,MAAM;AACpB,QAAI,eAAe;AACE,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,aAAa;MAAA,CACtC;AAED;IAAA;AAEF,QAAI,UAAU;AACZ;QACE,cAAc;UACZ,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH;EACF,GACC,CAAC,UAAU,eAAe,oBAAoB,eAAe,cAAc,cAAc,CAAC;AAEvF,QAAA,gBAAgB,OAAO,SAA+D;AACtF,QAAA;AACI,YAAA,MAAM,MAAM,cAAc,IAAI;AAEpC,UAAI,WAAW,KAAK;AACC,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,IAAI,KAAK;QAAA,CAClC;MAAA;IACH,QACM;AACa,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IAAA;EACH;AAGI,QAAA,gBAAgB,OAAO,OAAe;AACtC,QAAA;AACI,YAAA,MAAM,MAAM,mBAAmB;QACnC,KAAK,CAAC,EAAE;MAAA,CACT;AAED,UAAI,WAAW,KAAK;AACC,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,IAAI,KAAK;QAAA,CAClC;AAED;MAAA;AAGkB,0BAAA,CAAC,SAAS,KAAK,OAAO,CAAC,cAAc,cAAc,EAAE,CAAC;IAAA,QACpE;AACa,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IAAA;EACH;AAGF,QAAM,oBAAoB,YAAY;AAChC,QAAA;AACI,YAAA,MAAM,MAAM,mBAAmB;QACnC,KAAK;MAAA,CACN;AAED,UAAI,WAAW,KAAK;AACC,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,IAAI,KAAK;QAAA,CAClC;AAED;MAAA;AAGF,0BAAoB,CAAA,CAAE;IAAA,QAChB;AACa,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IAAA,UACD;AACA,mBAAa,KAAK;IAAA;EACpB;AAGF,QAAM,oBAAoB,CAAC,aACzB,WACI,qBAAoB,qCAAU,IAAI,CAAC,YAAY,QAAQ,QAAO,CAAA,CAAE,IAChE,oBAAoB,CAAA,CAAE;AAEtB,QAAA,oBAAoB,CAAC,UAAmB,OAC5C,WACI,oBAAoB,CAAC,SAAS,CAAC,GAAG,MAAM,EAAE,CAAC,IAC3C,oBAAoB,CAAC,SAAS,KAAK,OAAO,CAAC,cAAc,cAAc,EAAE,CAAC;AAEhF,QAAM,YAAY,iBAAiB;AAC7B,QAAA,oBAAmB,qCAAU,WAAU;AAC7C,QAAM,yBAAyB,iBAAiB;AAEhD,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAA,CAAa;EAAA;AAIrB,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM;MAAA;IACR,EAEJ,CAAA;QACC,yBAAA,KAAK,MAAL,EAAU,aAAW,WACpB,UAAA;UAAA;QAAC,QAAQ;QAAR;UACC,OAAO,cAAc,EAAE,IAAI,2BAA2B,gBAAgB,WAAA,CAAY;UAClF,UAAU,cAAc;YACtB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,eACE,aACA,CAAC,iBACE,wBAAA,YAAA,EAAW,KAAK,SAAS,eAAY,wBAAA,eAAA,CAAA,CAAK,GAAI,SAAQ,WAAU,IAAG,UAAS,MAAK,KAC/E,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EACH,CAAA;QAAA;MAAA;MAIL,yBAAyB,KAAK,iBAC7B;QAAC,QAAQ;QAAR;UACC,kBAEI,yBAAA,6BAAA,EAAA,UAAA;gBAAA,wBAAC,YAAW,EAAA,SAAQ,WAAU,WAAU,cACrC,UAAA;cACC;gBACE,IAAI;gBACJ,gBACE;cAAA;cAEJ,EAAE,uBAAuB;YAAA,EAE7B,CAAA;gBACA;cAAC;cAAA;gBACC,SAAS,MAAM,aAAa,IAAI;gBAChC,eAAA,wBAAY,cAAM,CAAA,CAAA;gBAClB,MAAK;gBACL,SAAQ;gBAEP,UAAc,cAAA;kBACb,IAAI;kBACJ,gBAAgB;gBAAA,CACjB;cAAA;YAAA;UACH,EACF,CAAA;QAAA;MAAA;UAIL,wBAAA,QAAQ,SAAR,EACE,UAAA,mBAAmB,QAClB;QAAC;QAAA;UACC,UAAU;UACV,UAAU,mBAAmB;UAC7B,YACE;YAAC;YAAA;cACC,SAAS,MAAM;AACb,oBAAI,WAAW;AACb,2BAAS,QAAQ;gBAAA;cACnB;cAEF,UAAA,wBAAO,eAAK,CAAA,CAAA;cAEX,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA;UAAA;UAIL,UAAA;gBAAC,wBAAA,OAAA,EACC,cAAA,yBAAC,IACC,EAAA,UAAA;kBAAA,wBAAC,IACC,EAAA,cAAA;gBAAC;gBAAA;kBACC,cAAY,cAAc;oBACxB,IAAI;oBACJ,gBAAgB;kBAAA,CACjB;kBACD,SACE,yBAAyB,KAAK,yBAAyB,mBACnD,kBACA,2BAA2B;kBAEjC,iBAAiB;gBAAA;cAAA,EAErB,CAAA;kBACA,wBAAC,IAAG,EAAA,OAAM,OACR,cAAA,wBAAC,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB,EAAA,CACH,EACF,CAAA;kBACA,wBAAC,IAAG,EAAA,OAAM,OACR,cAAA,wBAAC,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB,EAAA,CACH,EACF,CAAA;kBACA,wBAAC,IAAG,EAAA,OAAM,OACR,cAAA,wBAAC,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB,EAAA,CACH,EACF,CAAA;kBACC,wBAAA,IAAA,EACC,cAAC,wBAAA,gBAAA,EACE,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB,EAAA,CACH,EACF,CAAA;YAAA,EAAA,CACF,EACF,CAAA;gBACC,wBAAA,OAAA,EACE,UAAU,qCAAA,IAAI,CAAC,gBACd;cAAC;cAAA;gBAEC,SAAS,MAAM;AACb,sBAAI,WAAW;AACb,6BAAS,QAAQ,EAAE;kBAAA;gBACrB;gBAEF,OAAO,EAAE,QAAQ,YAAY,YAAY,UAAU;gBAEnD,UAAA;sBAAA,wBAAC,IAAA,EAAG,SAAS,CAAC,MAAM,EAAE,gBAAA,GACpB,cAAA;oBAAC;oBAAA;sBACC,cAAY,GAAG,cAAc;wBAC3B,IAAI;wBACJ,gBAAgB;sBAAA,CACjB,CAAC,IAAI,QAAQ,IAAI;sBAClB,SAAS,qDAAkB,SAAS,QAAQ;sBAC5C,iBAAiB,CAAC,aAAa,kBAAkB,CAAC,CAAC,UAAU,QAAQ,EAAE;sBACvE,MAAK;oBAAA;kBAAA,EAET,CAAA;sBACA,wBAAC,IACC,EAAA,cAAA,wBAAC,YAAW,EAAA,YAAW,YAAW,WAAU,cACzC,UAAQ,QAAA,KAAA,CACX,EACF,CAAA;sBACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,WAAU,cAAc,UAAA,QAAQ,IAAA,CAAI,EAClD,CAAA;sBACA,wBAAC,IAAA,EAAG,SAAS,CAAC,MAAM,EAAE,gBAAA,GACpB,cAAA,wBAAC,MACC,EAAA,cAAA;oBAAC;oBAAA;sBACC,SAAS,cAAc;wBACrB,IAAI;wBACJ,gBAAgB;sBAAA,CACjB;sBACD,UAAU,cAAc;wBACtB,IAAI;wBACJ,gBAAgB;sBAAA,CACjB;sBACD,cAAY,GAAG,QAAQ,IAAI,IAAI,cAAc;wBAC3C,IAAI;wBACJ,gBAAgB;sBAAA,CACjB,CAAC;sBACF,SAAS,QAAQ;sBACjB,iBAAiB,CAAC,YAAY;AACd,sCAAA;0BACZ,GAAG;0BACH,WAAW;wBAAA,CACZ;sBAAA;sBAEH,eAAa;oBAAA;kBAAA,EAAA,CAEjB,EACF,CAAA;sBACC,wBAAA,IAAA,EACC,cAAC,yBAAA,MAAA,EAAK,KAAK,GACR,UAAA;oBACC,iBAAA;sBAAC;sBAAA;wBACC,OAAO,cAAc;0BACnB,IAAI;0BACJ,gBAAgB;wBAAA,CACjB;wBACD,SAAQ;wBAER,cAAA,wBAAC,eAAO,CAAA,CAAA;sBAAA;oBAAA;oBAGX,iBACC;sBAAC;sBAAA;wBACC,UAAU,MAAM;AACd,wCAAc,QAAQ,EAAE;wBAAA;sBAC1B;oBAAA;kBACF,EAAA,CAEJ,EACF,CAAA;gBAAA;cAAA;cA1EK,QAAQ;YAAA,GA6EnB,CAAA;UAAA;QAAA;MAAA,QAGF;QAAC;QAAA;UACC,UAAM,wBAAC,cAAe,EAAA,OAAM,QAAQ,CAAA;UACpC,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,QACE,gBACG,wBAAA,YAAA,EAAW,SAAQ,aAAY,eAAY,wBAAA,eAAA,CAAA,CAAK,GAAI,KAAK,SAAS,IAAG,UACnE,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EAAA,CACH,IACE;QAAA;MAAA,EAIZ,CAAA;IAAA,EACF,CAAA;QACC,wBAAA,OAAO,MAAP,EAAY,MAAM,WAAW,cAAc,cAC1C,cAAC,wBAAA,eAAA,EAAc,WAAW,kBAAA,CAAmB,EAC/C,CAAA;EAAA,EACF,CAAA;AAEJ;AAUA,IAAM,qBAAqB,CAAC,EAAE,SAAA,MAAwC;AACpE,QAAM,CAAC,WAAW,YAAY,IAAU,eAAS,KAAK;AAChD,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAA;MAAC;MAAA;QACC,SAAS,CAAC,MAAM;AACd,YAAE,gBAAgB;AAClB,uBAAa,IAAI;QAAA;QAEnB,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,SAAQ;QAER,cAAA,wBAAC,cAAM,CAAA,CAAA;MAAA;IAAA;QACT,wBAEC,OAAO,MAAP,EAAY,MAAM,WAAW,cAAc,cAC1C,cAAA;MAAC;MAAA;QACC,WAAW,CAAC,MAAM;AAChB,iCAAG;AACM,mBAAA;QAAA;MACX;IAAA,EAEJ,CAAA;EAAA,EACF,CAAA;AAEJ;AAMA,IAAM,oBAAoB,MAAM;AAC9B,QAAM,cAAc;IAClB,CAAC,UAAU;;AAAA,yBAAM,UAAU,YAAY,aAA5B,mBAAsC,SAAS;;EAAA;AAG5D,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,UAAA,CAAA,CAAS,EACZ,CAAA;AAEJ;", "names": []}