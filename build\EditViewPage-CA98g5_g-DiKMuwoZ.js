import{a5 as J,ay as Q,cG as C,a as U,cN as D,r as M,cO as W,cI as Y,eI as q,m as e,n as u,aX as H,O as K,eJ as X,cn as p,S as _,eK as S,G as m,w as E,J as Z,eL as ee,eM as te,eN as P,b9 as se,e as ae,cE as ne,cF as ie}from"./strapi-YzJfjJ2z.js";import{a as oe,f as re,t as ce,g as T}from"./Field-B2C1tq9_-CuRzLj73.js";import"./Relations-7ItTFWp7-CaFM264H.js";import"./useDragAndDrop-DdHgKsqq-DDLMDz6i.js";import"./relations-sRERvWmr-drnKn_J5.js";import"./getEmptyImage-CjqolaH3.js";import"./ComponentIcon-u4bIXTFY-BYfvWXgU.js";import"./objects-D6yBsdmx-8Q5QgcI9.js";import"./useDebounce-DmuSJIF3-BaTG1oIL.js";const de=a=>M.useEffect(a,le),le=[],v=({layout:a})=>{const{formatMessage:o}=C(),{model:c}=D();return e.jsx(E,{direction:"column",alignItems:"stretch",gap:6,children:a.map((r,d)=>{if(r.some(t=>t.some(i=>i.type==="dynamiczone"))){const[t]=r,[i]=t,g={...i,label:o({id:`content-manager.content-types.${c}.${i.name}`,defaultMessage:i.label})};return e.jsx(m.Root,{gap:4,children:e.jsx(m.Item,{col:12,s:12,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(T,{...g})})},i.name)}return e.jsx(Z,{hasRadius:!0,background:"neutral0",shadow:"tableShadow",paddingLeft:6,paddingRight:6,paddingTop:6,paddingBottom:6,borderColor:"neutral150",children:e.jsx(E,{direction:"column",alignItems:"stretch",gap:6,children:r.map((t,i)=>e.jsx(m.Root,{gap:4,children:t.map(({size:g,...s})=>{const l={...s,label:o({id:`content-manager.content-types.${c}.${s.name}`,defaultMessage:s.label})};return e.jsx(m.Item,{col:g,s:12,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(T,{...l})},s.name)})},i))})},d)})})},ue=()=>{const a=J(),[{query:{status:o}},c]=Q({status:"draft"}),{formatMessage:r}=C(),{toggleNotification:d}=U(),{document:t,meta:i,isLoading:g,schema:s,components:l,collectionType:A,id:F,model:N,hasError:w}=D(),j=s?.options?.draftAndPublish??!1;de(()=>{a?.state&&"error"in a.state&&d({type:"danger",message:a.state.error,timeout:5e3})});const B=W("EditViewPage",n=>n.isLoading),h=A===q,f=!F&&!h,{isLoading:V,edit:{layout:I,settings:{mainField:b}}}=Y(N),{isLazyLoading:O}=oe([]),z=B||g||V||O,y=M.useMemo(()=>{if(!t&&!f&&!h||!s)return;const n=t?.id?t:re(s,l);return ce(s,l)(n)},[t,f,h,s,l]);if(w)return e.jsx(u.Error,{});if(z&&!t?.documentId)return e.jsx(u.Loading,{});if(!y)return e.jsx(u.Error,{});const G=n=>{(n==="published"||n==="draft")&&c({status:n},"push",!0)};let x="Untitled";b!=="id"&&t?.[b]?x=t[b]:h&&s?.info.displayName&&(x=s.info.displayName);const $=(n,L)=>P(s?.attributes,l,{status:o,...L}).validateSync(n,{abortEarly:!1});return e.jsxs(H,{paddingLeft:10,paddingRight:10,children:[e.jsx(u.Title,{children:x}),e.jsx(K,{disabled:j&&o==="published",initialValues:y,method:f?"POST":"PUT",validate:(n,L)=>P(s?.attributes,l,{status:o,...L}).validate(n,{abortEarly:!1}),initialErrors:a?.state?.forceValidation?$(y,{}):{},children:({resetForm:n})=>e.jsxs(e.Fragment,{children:[e.jsx(X,{isCreating:f,status:j?me(t,i):void 0,title:x}),e.jsxs(p.Root,{variant:"simple",value:o,onValueChange:G,children:[e.jsx(p.List,{"aria-label":r({id:S("containers.edit.tabs.label"),defaultMessage:"Document status"}),children:j?e.jsxs(e.Fragment,{children:[e.jsx(R,{value:"draft",children:r({id:S("containers.edit.tabs.draft"),defaultMessage:"draft"})}),e.jsx(R,{disabled:!i||i.availableStatus.length===0,value:"published",children:r({id:S("containers.edit.tabs.published"),defaultMessage:"published"})})]}):null}),e.jsxs(m.Root,{paddingTop:8,gap:4,children:[e.jsxs(m.Item,{col:9,s:12,direction:"column",alignItems:"stretch",children:[e.jsx(p.Content,{value:"draft",children:e.jsx(v,{layout:I})}),e.jsx(p.Content,{value:"published",children:e.jsx(v,{layout:I})})]}),e.jsx(m.Item,{col:3,s:12,direction:"column",alignItems:"stretch",children:e.jsx(ee,{})})]})]}),e.jsx(te,{onProceed:n})]})})]})},R=_(p.Trigger)`
  text-transform: uppercase;
`,me=(a,o)=>{const c=a?.status,r=o?.availableStatus??[];return c?c==="draft"&&r.find(d=>d.publishedAt!==null)?"published":c:"draft"},Se=()=>{const{slug:a=""}=se(),{permissions:o=[],isLoading:c,error:r}=ae(ne.map(d=>({action:d,subject:a})));return c?e.jsx(u.Loading,{}):r||!a?e.jsx(u.Error,{}):e.jsx(u.Protect,{permissions:o,children:({permissions:d})=>e.jsx(ie,{permissions:d,children:e.jsx(ue,{})})})};export{ue as EditViewPage,Se as ProtectedEditViewPage,me as getDocumentStatus};
