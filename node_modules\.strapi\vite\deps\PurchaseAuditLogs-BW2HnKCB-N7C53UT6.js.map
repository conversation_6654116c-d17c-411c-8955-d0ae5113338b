{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/PurchaseAuditLogs.tsx"], "sourcesContent": ["import { Box, Main, EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { ExternalLink } from '@strapi/icons';\nimport { EmptyPermissions } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nimport { Layouts } from '../../../components/Layouts/Layout';\n\nconst PurchaseAuditLogs = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Layouts.Root>\n      <Main>\n        <Layouts.Header\n          title={formatMessage({ id: 'global.auditLogs', defaultMessage: 'Audit Logs' })}\n          subtitle={formatMessage({\n            id: 'Settings.permissions.auditLogs.listview.header.subtitle',\n            defaultMessage: 'Logs of all the activities that happened in your environment',\n          })}\n        />\n        <Box paddingLeft={10} paddingRight={10}>\n          <EmptyStateLayout\n            icon={<EmptyPermissions width=\"16rem\" />}\n            content={formatMessage({\n              id: 'Settings.permissions.auditLogs.not-available',\n              defaultMessage:\n                'Audit Logs is only available as part of a paid plan. Upgrade to get a searchable and filterable display of all activities.',\n            })}\n            action={\n              <LinkButton\n                variant=\"default\"\n                endIcon={<ExternalLink />}\n                href=\"https://strp.cc/45mbAdF\"\n                isExternal\n                target=\"_blank\"\n              >\n                {formatMessage({\n                  id: 'global.learn-more',\n                  defaultMessage: 'Learn more',\n                })}\n              </LinkButton>\n            }\n          />\n        </Box>\n      </Main>\n    </Layouts.Root>\n  );\n};\n\nexport { PurchaseAuditLogs };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,oBAAoB,MAAM;AACxB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,aACG,wBAAA,QAAQ,MAAR,EACC,cAAA,yBAAC,MACC,EAAA,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc,EAAE,IAAI,oBAAoB,gBAAgB,aAAA,CAAc;QAC7E,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IAAA;QAEF,wBAAA,KAAA,EAAI,aAAa,IAAI,cAAc,IAClC,cAAA;MAAC;MAAA;QACC,UAAM,wBAAC,cAAiB,EAAA,OAAM,QAAQ,CAAA;QACtC,SAAS,cAAc;UACrB,IAAI;UACJ,gBACE;QAAA,CACH;QACD,YACE;UAAC;UAAA;YACC,SAAQ;YACR,aAAA,wBAAU,eAAa,CAAA,CAAA;YACvB,MAAK;YACL,YAAU;YACV,QAAO;YAEN,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QAAA;MACH;IAAA,EAGN,CAAA;EAAA,EAAA,CACF,EACF,CAAA;AAEJ;", "names": []}