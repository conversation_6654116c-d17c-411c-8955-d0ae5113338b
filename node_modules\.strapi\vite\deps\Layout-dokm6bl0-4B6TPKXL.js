import {
  require_sortBy
} from "./chunk-RBFGK6LJ.js";
import "./chunk-U7XWYUVC.js";
import {
  selectAdminPermissions
} from "./chunk-4SE2T7NO.js";
import "./chunk-HYNTGZ4P.js";
import {
  Layouts,
  SETTINGS_LINKS_CE,
  useEnterprise
} from "./chunk-ELTZWS66.js";
import {
  Page,
  useAppInfo,
  useAuth,
  useStrapiApp,
  useTracking
} from "./chunk-4C2ZQ5OG.js";
import {
  SubNav,
  SubNavHeader,
  SubNavLink,
  SubNavSection,
  SubNavSections,
  useIntl,
  useSelector
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  NavLink,
  Navigate,
  Outlet,
  useLocation,
  useMatch
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$2n
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/Layout-dokm6bl0.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_sortBy = __toESM(require_sortBy(), 1);
var formatLinks = (menu) => menu.map((menuSection) => {
  const formattedLinks = menuSection.links.map((link) => ({
    ...link,
    isDisplayed: false
  }));
  return { ...menuSection, links: formattedLinks };
});
var useSettingsMenu = () => {
  const [{ isLoading, menu }, setData] = React.useState({
    isLoading: true,
    menu: []
  });
  const checkUserHasPermission = useAuth(
    "useSettingsMenu",
    (state) => state.checkUserHasPermissions
  );
  const shouldUpdateStrapi = useAppInfo("useSettingsMenu", (state) => state.shouldUpdateStrapi);
  const settings = useStrapiApp("useSettingsMenu", (state) => state.settings);
  const permissions = useSelector(selectAdminPermissions);
  const ceLinks = React.useMemo(() => SETTINGS_LINKS_CE(), []);
  const { admin: adminLinks, global: globalLinks } = useEnterprise(
    ceLinks,
    async () => (await import("./index-iLKTBveZ-7VSUXGGU.js").then((n) => n.K)).SETTINGS_LINKS_EE(),
    {
      combine(ceLinks2, eeLinks) {
        return {
          admin: [...eeLinks.admin, ...ceLinks2.admin],
          global: [...ceLinks2.global, ...eeLinks.global]
        };
      },
      defaultValue: {
        admin: [],
        global: []
      }
    }
  );
  const addPermissions = React.useCallback(
    (link) => {
      var _a, _b;
      if (!link.id) {
        throw new Error("The settings menu item must have an id attribute.");
      }
      return {
        ...link,
        permissions: ((_b = (_a = permissions.settings) == null ? void 0 : _a[link.id]) == null ? void 0 : _b.main) ?? []
      };
    },
    [permissions.settings]
  );
  React.useEffect(() => {
    const getData = async () => {
      const buildMenuPermissions = (sections2) => Promise.all(
        sections2.reduce((acc, section, sectionIndex) => {
          const linksWithPermissions = section.links.map(async (link, linkIndex) => ({
            hasPermission: (await checkUserHasPermission(link.permissions)).length > 0,
            sectionIndex,
            linkIndex
          }));
          return [...acc, ...linksWithPermissions];
        }, [])
      );
      const menuPermissions = await buildMenuPermissions(sections);
      setData((prev) => {
        return {
          ...prev,
          isLoading: false,
          menu: sections.map((section, sectionIndex) => ({
            ...section,
            links: section.links.map((link, linkIndex) => {
              const permission = menuPermissions.find(
                (permission2) => permission2.sectionIndex === sectionIndex && permission2.linkIndex === linkIndex
              );
              return {
                ...link,
                isDisplayed: Boolean(permission == null ? void 0 : permission.hasPermission)
              };
            })
          }))
        };
      });
    };
    const { global, ...otherSections } = settings;
    const sections = formatLinks([
      {
        ...global,
        links: (0, import_sortBy.default)([...global.links, ...globalLinks.map(addPermissions)], (link) => link.id).map(
          (link) => ({
            ...link,
            hasNotification: link.id === "000-application-infos" && shouldUpdateStrapi
          })
        )
      },
      {
        id: "permissions",
        intlLabel: { id: "Settings.permissions", defaultMessage: "Administration Panel" },
        links: adminLinks.map(addPermissions)
      },
      ...Object.values(otherSections)
    ]);
    getData();
  }, [
    adminLinks,
    globalLinks,
    settings,
    shouldUpdateStrapi,
    addPermissions,
    checkUserHasPermission
  ]);
  return {
    isLoading,
    menu: menu.map((menuItem) => ({
      ...menuItem,
      links: menuItem.links.filter((link) => link.isDisplayed)
    }))
  };
};
var CustomIcon = dt(ForwardRef$2n)`
  right: 15px;
  position: absolute;
  bottom: 50%;
  transform: translateY(50%);

  path {
    fill: ${({ theme }) => theme.colors.warning500};
  }
`;
var Link = dt(SubNavLink)`
  &.active ${CustomIcon} {
    right: 13px;
  }
`;
var SettingsNav = ({ menu }) => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { pathname } = useLocation();
  const filteredMenu = menu.filter(
    (section) => !section.links.every((link) => link.isDisplayed === false)
  );
  const sections = filteredMenu.map((section) => {
    return {
      ...section,
      title: section.intlLabel,
      links: section.links.map((link) => {
        return {
          ...link,
          title: link.intlLabel,
          name: link.id
        };
      })
    };
  });
  const label = formatMessage({
    id: "global.settings",
    defaultMessage: "Settings"
  });
  const handleClickOnLink = (destination) => () => {
    trackUsage("willNavigate", { from: pathname, to: destination });
  };
  return (0, import_jsx_runtime.jsxs)(SubNav, { "aria-label": label, children: [
    (0, import_jsx_runtime.jsx)(SubNavHeader, { label }),
    (0, import_jsx_runtime.jsx)(SubNavSections, { children: sections.map((section) => (0, import_jsx_runtime.jsx)(SubNavSection, { label: formatMessage(section.intlLabel), children: section.links.map((link) => {
      return (0, import_jsx_runtime.jsxs)(
        Link,
        {
          tag: NavLink,
          withBullet: link.hasNotification,
          to: link.to,
          onClick: handleClickOnLink(link.to),
          position: "relative",
          children: [
            formatMessage(link.intlLabel),
            (link == null ? void 0 : link.licenseOnly) && (0, import_jsx_runtime.jsx)(CustomIcon, { width: "1.5rem", height: "1.5rem" })
          ]
        },
        link.id
      );
    }) }, section.id)) })
  ] });
};
var Layout = () => {
  const match = useMatch("/settings/:settingId/*");
  const { formatMessage } = useIntl();
  const { isLoading, menu } = useSettingsMenu();
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  if (!(match == null ? void 0 : match.params.settingId)) {
    return (0, import_jsx_runtime.jsx)(Navigate, { to: "application-infos" });
  }
  return (0, import_jsx_runtime.jsxs)(Layouts.Root, { sideNav: (0, import_jsx_runtime.jsx)(SettingsNav, { menu }), children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage({
      id: "global.settings",
      defaultMessage: "Settings"
    }) }),
    (0, import_jsx_runtime.jsx)(Outlet, {})
  ] });
};
export {
  Layout
};
//# sourceMappingURL=Layout-dokm6bl0-4B6TPKXL.js.map
