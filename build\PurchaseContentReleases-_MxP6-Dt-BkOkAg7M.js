import{gj as t,m as e,L as s,aX as n,J as r,bM as l,bK as o,bT as i,bU as d}from"./strapi-YzJfjJ2z.js";const c=()=>{const{formatMessage:a}=t();return e.jsx(s.Root,{children:e.jsxs(n,{children:[e.jsx(s.<PERSON><PERSON>,{title:a({id:"content-releases.pages.Releases.title",defaultMessage:"Releases"}),subtitle:a({id:"content-releases.pages.PurchaseRelease.subTitle",defaultMessage:"Manage content updates and releases."})}),e.jsx(r,{paddingLeft:10,paddingRight:10,children:e.jsx(l,{icon:e.jsx(d,{width:"16rem"}),content:a({id:"content-releases.pages.PurchaseRelease.not-available",defaultMessage:"Releases is only available as part of a paid plan. Upgrade to create and manage releases."}),action:e.jsx(o,{variant:"default",endIcon:e.jsx(i,{}),href:"https://strapi.io/pricing-self-hosted?utm_campaign=Growth-Experiments&utm_source=In-Product&utm_medium=Releases",isExternal:!0,target:"_blank",children:a({id:"global.learn-more",defaultMessage:"Learn more"})})})})]})})};export{c as PurchaseContentReleases};
