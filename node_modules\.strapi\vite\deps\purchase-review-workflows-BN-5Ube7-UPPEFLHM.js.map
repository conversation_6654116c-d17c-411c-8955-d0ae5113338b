{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/routes/purchase-review-workflows.tsx"], "sourcesContent": ["import { Layouts } from '@strapi/admin/strapi-admin';\nimport { Box, Main, EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { ExternalLink } from '@strapi/icons';\nimport { EmptyPermissions } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nconst PurchaseReviewWorkflows = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Layouts.Root>\n      <Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: 'Settings.review-workflows.list.page.title',\n            defaultMessage: 'Review Workflows',\n          })}\n          subtitle={formatMessage({\n            id: 'Settings.review-workflows.list.page.subtitle',\n            defaultMessage: 'Manage your content review process',\n          })}\n        />\n        <Box paddingLeft={10} paddingRight={10}>\n          <EmptyStateLayout\n            icon={<EmptyPermissions width=\"16rem\" />}\n            content={formatMessage({\n              id: 'Settings.review-workflows.not-available',\n              defaultMessage:\n                'Review Workflows is only available as part of a paid plan. Upgrade to create and manage workflows.',\n            })}\n            action={\n              <LinkButton\n                variant=\"default\"\n                endIcon={<ExternalLink />}\n                href=\"https://strp.cc/3tdNfJq\"\n                isExternal\n                target=\"_blank\"\n              >\n                {formatMessage({\n                  id: 'global.learn-more',\n                  defaultMessage: 'Learn more',\n                })}\n              </LinkButton>\n            }\n          />\n        </Box>\n      </Main>\n    </Layouts.Root>\n  );\n};\n\nexport { PurchaseReviewWorkflows };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,0BAA0B,MAAM;AAC9B,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,aACG,wBAAA,QAAQ,MAAR,EACC,cAAA,yBAAC,MACC,EAAA,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH;QACC,wBAAA,KAAA,EAAI,aAAa,IAAI,cAAc,IAClC,cAAA;MAAC;MAAA;QACC,UAAM,wBAAC,cAAiB,EAAA,OAAM,QAAQ,CAAA;QACtC,SAAS,cAAc;UACrB,IAAI;UACJ,gBACE;QAAA,CACH;QACD,YACE;UAAC;UAAA;YACC,SAAQ;YACR,aAAA,wBAAU,eAAa,CAAA,CAAA;YACvB,MAAK;YACL,YAAU;YACV,QAAO;YAEN,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QACH;MAAA;IAAA,EAAA,CAGN;EAAA,EACF,CAAA,EACF,CAAA;AAEJ;", "names": []}