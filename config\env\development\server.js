module.exports = ({ env }) => ({
  host: env('HOST', '0.0.0.0'),
  port: env.int('PORT', 1437),
  url: env('PUBLIC_URL', 'http://localhost:1437'),
  app: {
    keys: env.array('APP_KEYS'),
  },
  webhooks: {
    populateRelations: env.bool('WEBHOOKS_POPULATE_RELATIONS', false),
  },
  // 开发环境特定配置
  autoReload: env.bool('STRAPI_AUTO_RELOAD', true),
  watchIgnoreFiles: [
    './data/**',
    './.tmp/**',
    './logs/**',
    './node_modules/**',
    './.git/**',
    './.cache/**',
    './.next/**',
    './public/uploads/**'
  ],
});
