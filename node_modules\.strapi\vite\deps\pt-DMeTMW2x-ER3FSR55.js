import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-type-builder/dist/_chunks/pt-DMeTMW2x.mjs
var from = "de";
var pt = {
  "attribute.boolean": "Booleano",
  "attribute.date": "Data",
  "attribute.email": "Email",
  "attribute.enumeration": "Enumeração",
  "attribute.json": "JSON",
  "attribute.media": "Media",
  "attribute.password": "Palavra-passe",
  "attribute.relation": "Relação",
  "attribute.text": "Texto",
  "form.attribute.item.customColumnName": "Nomes de colunas personalizadas",
  "form.attribute.item.customColumnName.description": "Isto é útil para renomear os nomes das colunas da base de dados num formato mais abrangente para as respostas da API",
  "form.attribute.item.defineRelation.fieldName": "Nome do campo",
  "form.attribute.item.enumeration.graphql": "Substituição de nome para o GraphQL",
  "form.attribute.item.enumeration.graphql.description": "Permite-lhe a substituição do nome predefinido para o GraphQL",
  "form.attribute.item.enumeration.placeholder": "Ex:\nmanhã\ntarde\nnoite",
  "form.attribute.item.enumeration.rules": "Valores (um valor por linha)",
  "form.attribute.item.maximum": "Valor máximo",
  "form.attribute.item.maximumLength": "Comprimento máximo",
  "form.attribute.item.minimum": "Valor mínimo",
  "form.attribute.item.minimumLength": "Comprimento mínimo",
  "form.attribute.item.number.type": "Formato numérico",
  "form.attribute.item.number.type.decimal": "decimal (ex: 2.22)",
  "form.attribute.item.number.type.float": "real (ex: 3.33333333)",
  "form.attribute.item.number.type.integer": "inteiro (ex: 10)",
  "form.attribute.item.requiredField": "Campo obrigatório",
  "form.attribute.item.requiredField.description": "Não será capaz de criar uma entrada se este campo estiver vazio",
  "form.attribute.item.uniqueField": "Campo único",
  "form.attribute.item.uniqueField.description": "Não será capaz de criar uma entrada se houver uma entrada existente com conteúdo idêntico",
  "form.attribute.settings.default": "Valor predefinido",
  "form.button.cancel": "Cancelar",
  from,
  "modelPage.attribute.relationWith": "Relação com",
  "plugin.description.long": "Modele a estrutura de dados da sua API. Crie novos campos e relações em apenas um minuto. Os ficheiros são automaticamente criados e actualizados no seu projecto.",
  "plugin.description.short": "Modele a estrutura de dados da sua API.",
  "popUpForm.navContainer.advanced": "Definições Avançadas",
  "popUpForm.navContainer.base": "Definições Básicas",
  "popUpWarning.bodyMessage.contentType.delete": "Tem a certeza que pretende apagar este Tipo de Conteúdo?",
  "relation.attributeName.placeholder": "Ex: autor, categoria, tag",
  "relation.manyToMany": "tem e pertence a vários",
  "relation.manyToOne": "tem vários",
  "relation.oneToMany": "pertence a vários",
  "relation.oneToOne": "tem e pertence a um",
  "relation.oneWay": "tem um"
};
export {
  pt as default,
  from
};
//# sourceMappingURL=pt-DMeTMW2x-ER3FSR55.js.map
