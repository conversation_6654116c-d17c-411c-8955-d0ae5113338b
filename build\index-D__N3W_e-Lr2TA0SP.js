import{H as J,a as K,u as W,b as X,c as Y,r as w,e as Z,P as L,f as ee,h as s,g as se,m as e,n as g,L as S,T as ae,o as ie,p as j,q as b,V as T,s as E,t as te,v as p,J as R,K as le,I as v,F as k,N as re,M as o,B as de,C as I,O as ne,G as U,Q as oe,A as _,i as M,l as u,k as m,E as i}from"./strapi-YzJfjJ2z.js";import{u as me}from"./index-iLKTBveZ-C1E7ADbJ-rp1KcFbW.js";import{u as y}from"./index-dwdMDrmv.js";import"./immer.esm-DiDhQEzX.js";const pe=M().shape({options:M().shape({from:M().shape({name:u().required({id:m.required.id,defaultMessage:"This field is required"}),email:u().email(m.email).required({id:m.required.id,defaultMessage:"This field is required"})}).required(),response_email:u().email(m.email),object:u().required({id:m.required.id,defaultMessage:"This field is required"}),message:u().required({id:m.required.id,defaultMessage:"This field is required"})}).required(m.required.id)}),P=({template:a={},onToggle:d,open:t,onSubmit:n})=>{const{formatMessage:l}=y();return e.jsx(o.Root,{open:t,onOpenChange:d,children:e.jsxs(o.Content,{children:[e.jsxs(o.Header,{children:[e.jsxs(de,{label:`${l({id:s("PopUpForm.header.edit.email-templates"),defaultMessage:"Edit email template"})}, ${a.display?l({id:s(a.display),defaultMessage:a.display}):""}`,children:[e.jsx(I,{children:l({id:s("PopUpForm.header.edit.email-templates"),defaultMessage:"Edit email template"})}),e.jsx(I,{isCurrent:!0,children:a.display?l({id:s(a.display),defaultMessage:a.display}):""})]}),e.jsx(T,{children:e.jsx(o.Title,{children:`${l({id:s("PopUpForm.header.edit.email-templates"),defaultMessage:"Edit email template"})}, ${a.display?l({id:s(a.display),defaultMessage:a.display}):""}`})})]}),e.jsx(ne,{onSubmit:n,initialValues:a,validationSchema:pe,children:({isSubmitting:h})=>e.jsxs(e.Fragment,{children:[e.jsx(o.Body,{children:e.jsx(U.Root,{gap:5,children:[{label:l({id:s("PopUpForm.Email.options.from.name.label"),defaultMessage:"Shipper name"}),name:"options.from.name",size:6,type:"string"},{label:l({id:s("PopUpForm.Email.options.from.email.label"),defaultMessage:"Shipper email"}),name:"options.from.email",size:6,type:"string"},{label:l({id:s("PopUpForm.Email.options.response_email.label"),defaultMessage:"Response email"}),name:"options.response_email",size:6,type:"string"},{label:l({id:s("PopUpForm.Email.options.object.label"),defaultMessage:"Subject"}),name:"options.object",size:6,type:"string"},{label:l({id:s("PopUpForm.Email.options.message.label"),defaultMessage:"Message"}),name:"options.message",size:12,type:"text"}].map(({size:f,...c})=>e.jsx(U.Item,{col:f,direction:"column",alignItems:"stretch",children:e.jsx(oe,{...c})},c.name))})}),e.jsxs(o.Footer,{children:[e.jsx(o.Close,{children:e.jsx(_,{variant:"tertiary",children:"Cancel"})}),e.jsx(_,{loading:h,type:"submit",children:"Finish"})]})]})})]})})};P.defaultProps={template:{}};P.propTypes={template:i.shape({display:i.string,icon:i.string,options:i.shape({from:i.shape({name:i.string,email:i.string}),message:i.string,object:i.string,response_email:i.string})}),open:i.bool.isRequired,onSubmit:i.func.isRequired,onToggle:i.func.isRequired};const $=({canUpdate:a,onEditClick:d})=>{const{formatMessage:t}=y();return e.jsxs(ae,{colCount:3,rowCount:3,children:[e.jsx(ie,{children:e.jsxs(j,{children:[e.jsx(b,{width:"1%",children:e.jsx(T,{children:t({id:s("Email.template.table.icon.label"),defaultMessage:"icon"})})}),e.jsx(b,{children:e.jsx(E,{variant:"sigma",textColor:"neutral600",children:t({id:s("Email.template.table.name.label"),defaultMessage:"name"})})}),e.jsx(b,{width:"1%",children:e.jsx(T,{children:t({id:s("Email.template.table.action.label"),defaultMessage:"action"})})})]})}),e.jsxs(te,{children:[e.jsxs(j,{onClick:()=>d("reset_password"),children:[e.jsx(p,{children:e.jsx(R,{width:"3.2rem",height:"3.2rem",padding:"0.8rem",children:e.jsx(le,{"aria-label":t({id:"global.reset-password",defaultMessage:"Reset password"})})})}),e.jsx(p,{children:e.jsx(E,{children:t({id:"global.reset-password",defaultMessage:"Reset password"})})}),e.jsx(p,{onClick:n=>n.stopPropagation(),children:e.jsx(v,{onClick:()=>d("reset_password"),label:t({id:s("Email.template.form.edit.label"),defaultMessage:"Edit a template"}),variant:"ghost",disabled:!a,children:e.jsx(k,{})})})]}),e.jsxs(j,{onClick:()=>d("email_confirmation"),children:[e.jsx(p,{children:e.jsx(R,{width:"3.2rem",height:"3.2rem",padding:"0.8rem",children:e.jsx(re,{"aria-label":t({id:s("Email.template.email_confirmation"),defaultMessage:"Email address confirmation"})})})}),e.jsx(p,{children:e.jsx(E,{children:t({id:s("Email.template.email_confirmation"),defaultMessage:"Email address confirmation"})})}),e.jsx(p,{onClick:n=>n.stopPropagation(),children:e.jsx(v,{onClick:()=>d("email_confirmation"),label:t({id:s("Email.template.form.edit.label"),defaultMessage:"Edit a template"}),variant:"ghost",disabled:!a,children:e.jsx(k,{})})})]})]})]})};$.propTypes={canUpdate:i.bool.isRequired,onEditClick:i.func.isRequired};const xe=()=>e.jsx(g.Protect,{permissions:L.readEmailTemplates,children:e.jsx(ce,{})}),ce=()=>{const{formatMessage:a}=y(),{trackUsage:d}=me(),{notifyStatus:t}=J(),{toggleNotification:n}=K(),l=W(),{get:h,put:f}=X(),{formatAPIError:c}=Y(),[A,B]=w.useState(!1),[q,H]=w.useState(null),{isLoading:N,allowedActions:{canUpdate:z}}=Z({update:L.updateEmailTemplates}),{isLoading:O,data:C}=ee(["users-permissions","email-templates"],async()=>{const{data:r}=await h("/users-permissions/email-templates");return r},{onSuccess(){t(a({id:s("Email.template.data.loaded"),defaultMessage:"Email templates has been loaded"}))},onError(r){n({type:"danger",message:c(r)})}}),Q=N||O,x=()=>{B(r=>!r)},V=r=>{H(r),x()},F=se(r=>f("/users-permissions/email-templates",{"email-templates":r}),{async onSuccess(){await l.invalidateQueries(["users-permissions","email-templates"]),n({type:"success",message:a({id:"notification.success.saved",defaultMessage:"Saved"})}),d("didEditEmailTemplates"),x()},onError(r){n({type:"danger",message:c(r)})},refetchActive:!0}),G=r=>{d("willEditEmailTemplates");const D={...C,[q]:r};F.mutate(D)};return Q?e.jsx(g.Loading,{}):e.jsxs(g.Main,{"aria-busy":F.isLoading,children:[e.jsx(g.Title,{children:a({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:a({id:s("HeaderNav.link.emailTemplates"),defaultMessage:"Email templates"})})}),e.jsx(S.Header,{title:a({id:s("HeaderNav.link.emailTemplates"),defaultMessage:"Email templates"})}),e.jsxs(S.Content,{children:[e.jsx($,{onEditClick:V,canUpdate:z}),e.jsx(P,{template:C[q],onToggle:x,open:A,onSubmit:G})]})]})};export{ce as EmailTemplatesPage,xe as ProtectedEmailTemplatesPage};
