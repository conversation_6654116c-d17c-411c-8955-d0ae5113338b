import{ax as s,m as t,n as o}from"./strapi-YzJfjJ2z.js";import{EditView as i}from"./EditView-DWRBGzO8-DWyGfVxp.js";import"./transferTokens-CXTFej3W-kOI9Cp4M.js";import"./constants-CRj0ViV1-Q2dfXdfa.js";import"./TokenTypeSelect-Cr8pc-yV-C65ZkGgP.js";import"./index-CJ4ZBILS.js";import"./index-BRVyLNfZ.js";const f=()=>{const e=s(r=>r.admin_app.permissions.settings?.["transfer-tokens"].create);return t.jsx(o.Protect,{permissions:e,children:t.jsx(i,{})})};export{f as ProtectedCreateView};
