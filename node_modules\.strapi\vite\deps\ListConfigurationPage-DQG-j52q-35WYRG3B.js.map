{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/Header.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/Settings.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/EditFieldForm.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/DraggableCard.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/components/SortDisplayedFields.tsx", "../../../@strapi/content-manager/admin/src/pages/ListConfiguration/ListConfigurationPage.tsx"], "sourcesContent": ["import { useForm, BackButton, Layouts } from '@strapi/admin/strapi-admin';\nimport { Button } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { capitalise } from '../../../utils/strings';\nimport { getTranslation } from '../../../utils/translations';\n\ninterface HeaderProps {\n  collectionType: string;\n  name: string;\n  model: string;\n}\n\nconst Header = ({ name }: HeaderProps) => {\n  const { formatMessage } = useIntl();\n\n  const modified = useForm('Header', (state) => state.modified);\n  const isSubmitting = useForm('Header', (state) => state.isSubmitting);\n\n  return (\n    <Layouts.Header\n      navigationAction={<BackButton />}\n      primaryAction={\n        <Button size=\"S\" disabled={!modified} type=\"submit\" loading={isSubmitting}>\n          {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n        </Button>\n      }\n      subtitle={formatMessage({\n        id: getTranslation('components.SettingsViewWrapper.pluginHeader.description.list-settings'),\n        defaultMessage: 'Define the settings of the list view.',\n      })}\n      title={formatMessage(\n        {\n          id: getTranslation('components.SettingsViewWrapper.pluginHeader.title'),\n          defaultMessage: 'Configure the view - {name}',\n        },\n        { name: capitalise(name) }\n      )}\n    />\n  );\n};\n\nexport { Header };\nexport type { HeaderProps };\n", "import * as React from 'react';\n\nimport { useForm, InputRenderer, type InputProps } from '@strapi/admin/strapi-admin';\nimport { Flex, Grid, Typography, useCollator } from '@strapi/design-system';\nimport { type MessageDescriptor, useIntl } from 'react-intl';\n\nimport { useDoc } from '../../../hooks/useDocument';\nimport { type EditFieldLayout } from '../../../hooks/useDocumentLayout';\nimport { getTranslation } from '../../../utils/translations';\nimport { type FormData } from '../ListConfigurationPage';\n\nimport type { DistributiveOmit } from 'react-redux';\n\nexport type InputPropsWithMessageDescriptors = DistributiveOmit<\n  InputProps,\n  'hint' | 'label' | 'placeholder'\n> & {\n  hint?: MessageDescriptor;\n  label: MessageDescriptor;\n  placeholder?: MessageDescriptor;\n};\n\n/**\n * @internal\n * @description Form inputs are always displayed in a grid, so we need\n * to use the size property to determine how many columns the input should\n * take up.\n */\nexport type FormLayoutInputProps = InputPropsWithMessageDescriptors & { size: number };\n\nconst EXCLUDED_SORT_ATTRIBUTE_TYPES = [\n  'media',\n  'richtext',\n  'dynamiczone',\n  'relation',\n  'component',\n  'json',\n  'blocks',\n];\n\ninterface SortOption {\n  value: string;\n  label: string;\n}\n\nconst Settings = () => {\n  const { formatMessage, locale } = useIntl();\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n  const { schema } = useDoc();\n\n  const layout = useForm<FormData['layout']>('Settings', (state) => state.values.layout ?? []);\n  const currentSortBy = useForm<FormData['settings']['defaultSortBy']>(\n    'Settings',\n    (state) => state.values.settings.defaultSortBy\n  );\n  const onChange = useForm('Settings', (state) => state.onChange);\n\n  const sortOptions = React.useMemo(\n    () =>\n      Object.values(layout).reduce<SortOption[]>((acc, field) => {\n        if (schema && !EXCLUDED_SORT_ATTRIBUTE_TYPES.includes(schema.attributes[field.name].type)) {\n          acc.push({\n            value: field.name,\n            label: typeof field.label !== 'string' ? formatMessage(field.label) : field.label,\n          });\n        }\n\n        return acc;\n      }, []),\n    [formatMessage, layout, schema]\n  );\n\n  const sortOptionsSorted = sortOptions.sort((a, b) => formatter.compare(a.label, b.label));\n\n  React.useEffect(() => {\n    if (sortOptionsSorted.findIndex((opt) => opt.value === currentSortBy) === -1) {\n      onChange('settings.defaultSortBy', sortOptionsSorted[0]?.value);\n    }\n  }, [currentSortBy, onChange, sortOptionsSorted]);\n\n  const formLayout = React.useMemo(\n    () =>\n      SETTINGS_FORM_LAYOUT.map((row) =>\n        row.map((field) => {\n          if (field.type === 'enumeration') {\n            return {\n              ...field,\n              hint: field.hint ? formatMessage(field.hint) : undefined,\n              label: formatMessage(field.label),\n              options: field.name === 'settings.defaultSortBy' ? sortOptionsSorted : field.options,\n            };\n          } else {\n            return {\n              ...field,\n              hint: field.hint ? formatMessage(field.hint) : undefined,\n              label: formatMessage(field.label),\n            };\n          }\n        })\n      ) as [top: EditFieldLayout[], bottom: EditFieldLayout[]],\n    [formatMessage, sortOptionsSorted]\n  );\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n      <Typography variant=\"delta\" tag=\"h2\">\n        {formatMessage({\n          id: getTranslation('containers.SettingPage.settings'),\n          defaultMessage: 'Settings',\n        })}\n      </Typography>\n      <Grid.Root key=\"bottom\" gap={4}>\n        {formLayout.map((row) =>\n          row.map(({ size, ...field }) => (\n            <Grid.Item key={field.name} s={12} col={size} direction=\"column\" alignItems=\"stretch\">\n              {/* @ts-expect-error – issue with EnumerationProps conflicting with InputProps */}\n              <InputRenderer {...field} />\n            </Grid.Item>\n          ))\n        )}\n      </Grid.Root>\n    </Flex>\n  );\n};\n\nconst SETTINGS_FORM_LAYOUT: FormLayoutInputProps[][] = [\n  [\n    {\n      label: {\n        id: getTranslation('form.Input.search'),\n        defaultMessage: 'Enable search',\n      },\n      name: 'settings.searchable',\n      size: 4,\n      type: 'boolean' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.filters'),\n        defaultMessage: 'Enable filters',\n      },\n      name: 'settings.filterable',\n      size: 4,\n      type: 'boolean' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.bulkActions'),\n        defaultMessage: 'Enable bulk actions',\n      },\n      name: 'settings.bulkable',\n      size: 4,\n      type: 'boolean' as const,\n    },\n  ],\n  [\n    {\n      hint: {\n        id: getTranslation('form.Input.pageEntries.inputDescription'),\n        defaultMessage: 'Note: You can override this value in the Collection Type settings page.',\n      },\n      label: {\n        id: getTranslation('form.Input.pageEntries'),\n        defaultMessage: 'Entries per page',\n      },\n      name: 'settings.pageSize',\n      options: ['10', '20', '50', '100'].map((value) => ({ value, label: value })),\n      size: 6,\n      type: 'enumeration' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.defaultSort'),\n        defaultMessage: 'Default sort attribute',\n      },\n      name: 'settings.defaultSortBy',\n      options: [],\n      size: 3,\n      type: 'enumeration' as const,\n    },\n    {\n      label: {\n        id: getTranslation('form.Input.sort.order'),\n        defaultMessage: 'Default sort order',\n      },\n      name: 'settings.defaultSortOrder',\n      options: ['ASC', 'DESC'].map((value) => ({ value, label: value })),\n      size: 3,\n      type: 'enumeration' as const,\n    },\n  ],\n];\n\nexport { Settings };\n", "import { Form, useField, InputRenderer, useNotification } from '@strapi/admin/strapi-admin';\nimport { Button, Flex, FlexComponent, Grid, Modal } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\nimport * as yup from 'yup';\n\nimport { FieldTypeIcon } from '../../../components/FieldTypeIcon';\nimport { capitalise } from '../../../utils/strings';\nimport { getTranslation } from '../../../utils/translations';\n\nimport type { ListFieldLayout } from '../../../hooks/useDocumentLayout';\nimport type { FormData } from '../ListConfigurationPage';\n\ninterface EditFieldFormProps extends Pick<ListFieldLayout, 'attribute'> {\n  name: string;\n  onClose: () => void;\n}\n\nconst FIELD_SCHEMA = yup.object().shape({\n  label: yup.string().required(),\n  sortable: yup.boolean(),\n});\n\nconst EditFieldForm = ({ attribute, name, onClose }: EditFieldFormProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n\n  const { value, onChange } = useField<FormData['layout'][number]>(name);\n\n  if (!value) {\n    // This is very unlikely to happen, but it ensures the form is not opened without a value.\n    console.error(\n      \"You've opened a field to edit without it being part of the form, this is likely a bug with Strapi. Please open an issue.\"\n    );\n\n    toggleNotification({\n      message: formatMessage({\n        id: 'content-manager.containers.list-settings.modal-form.error',\n        defaultMessage: 'An error occurred while trying to open the form.',\n      }),\n      type: 'danger',\n    });\n\n    return null;\n  }\n\n  let shouldDisplaySortToggle = !['media', 'relation'].includes(attribute.type);\n\n  if ('relation' in attribute && ['oneWay', 'oneToOne', 'manyToOne'].includes(attribute.relation)) {\n    shouldDisplaySortToggle = true;\n  }\n\n  return (\n    <Modal.Content>\n      <Form\n        method=\"PUT\"\n        initialValues={value}\n        validationSchema={FIELD_SCHEMA}\n        onSubmit={(data) => {\n          onChange(name, data);\n          onClose();\n        }}\n      >\n        <Modal.Header>\n          <HeaderContainer>\n            {/* @ts-expect-error attribute.type === custom does not work here */}\n            <FieldTypeIcon type={attribute.type} />\n            <Modal.Title>\n              {formatMessage(\n                {\n                  id: getTranslation('containers.list-settings.modal-form.label'),\n                  defaultMessage: 'Edit {fieldName}',\n                },\n                { fieldName: capitalise(value.label) }\n              )}\n            </Modal.Title>\n          </HeaderContainer>\n        </Modal.Header>\n        <Modal.Body>\n          <Grid.Root gap={4}>\n            {[\n              {\n                name: 'label',\n                label: formatMessage({\n                  id: getTranslation('form.Input.label'),\n                  defaultMessage: 'Label',\n                }),\n                hint: formatMessage({\n                  id: getTranslation('form.Input.label.inputDescription'),\n                  defaultMessage: \"This value overrides the label displayed in the table's head\",\n                }),\n                size: 6,\n                type: 'string' as const,\n              },\n              {\n                label: formatMessage({\n                  id: getTranslation('form.Input.sort.field'),\n                  defaultMessage: 'Enable sort on this field',\n                }),\n                name: 'sortable',\n                size: 6,\n                type: 'boolean' as const,\n              },\n            ]\n              .filter(\n                (field) =>\n                  field.name !== 'sortable' ||\n                  (field.name === 'sortable' && shouldDisplaySortToggle)\n              )\n              .map(({ size, ...field }) => (\n                <Grid.Item\n                  key={field.name}\n                  s={12}\n                  col={size}\n                  direction=\"column\"\n                  alignItems=\"stretch\"\n                >\n                  <InputRenderer {...field} />\n                </Grid.Item>\n              ))}\n          </Grid.Root>\n        </Modal.Body>\n        <Modal.Footer>\n          <Button onClick={onClose} variant=\"tertiary\">\n            {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n          </Button>\n          <Button type=\"submit\">\n            {formatMessage({ id: 'global.finish', defaultMessage: 'Finish' })}\n          </Button>\n        </Modal.Footer>\n      </Form>\n    </Modal.Content>\n  );\n};\n\nconst HeaderContainer = styled<FlexComponent>(Flex)`\n  svg {\n    width: 3.2rem;\n    margin-right: ${({ theme }) => theme.spaces[3]};\n  }\n`;\n\nexport { EditFieldForm };\nexport type { EditFieldFormProps };\n", "import * as React from 'react';\n\nimport {\n  Box,\n  BoxComponent,\n  Flex,\n  FlexComponent,\n  Modal,\n  Typography,\n  useComposedRefs,\n} from '@strapi/design-system';\nimport { Cross, Drag, Pencil } from '@strapi/icons';\nimport { getEmptyImage } from 'react-dnd-html5-backend';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { CardDragPreview } from '../../../components/DragPreviews/CardDragPreview';\nimport { ItemTypes } from '../../../constants/dragAndDrop';\nimport { useDragAndDrop } from '../../../hooks/useDragAndDrop';\nimport { getTranslation } from '../../../utils/translations';\n\nimport { EditFieldForm } from './EditFieldForm';\n\nimport type { ListFieldLayout } from '../../../hooks/useDocumentLayout';\n\ntype DraggableCardProps = Omit<ListFieldLayout, 'label'> & {\n  label: string;\n  index: number;\n  isDraggingSibling: boolean;\n  onMoveField: (dragIndex: number, hoverIndex: number) => void;\n  onRemoveField: () => void;\n  setIsDraggingSibling: (isDragging: boolean) => void;\n};\n\nconst DraggableCard = ({\n  attribute,\n  index,\n  isDraggingSibling,\n  label,\n  name,\n  onMoveField,\n  onRemoveField,\n  setIsDraggingSibling,\n}: DraggableCardProps) => {\n  const [isModalOpen, setIsModalOpen] = React.useState(false);\n  const { formatMessage } = useIntl();\n  const [, forceRerenderAfterDnd] = React.useState(false);\n\n  const [{ isDragging }, objectRef, dropRef, dragRef, dragPreviewRef] = useDragAndDrop(true, {\n    type: ItemTypes.FIELD,\n    item: { index, label, name },\n    index,\n    onMoveItem: onMoveField,\n    onEnd: () => setIsDraggingSibling(false),\n  });\n\n  React.useEffect(() => {\n    dragPreviewRef(getEmptyImage(), { captureDraggingState: false });\n  }, [dragPreviewRef]);\n\n  React.useEffect(() => {\n    if (isDragging) {\n      setIsDraggingSibling(true);\n    }\n  }, [isDragging, setIsDraggingSibling]);\n\n  // Effect in order to force a rerender after reordering the components\n  // Since we are removing the Accordion when doing the DnD  we are losing the dragRef, therefore the replaced element cannot be dragged\n  // anymore, this hack forces a rerender in order to apply the dragRef\n  React.useEffect(() => {\n    if (!isDraggingSibling) {\n      forceRerenderAfterDnd((prev) => !prev);\n    }\n  }, [isDraggingSibling]);\n\n  const composedRefs = useComposedRefs<HTMLButtonElement>(\n    dropRef,\n    objectRef as React.RefObject<HTMLButtonElement>\n  );\n\n  return (\n    <FieldWrapper ref={composedRefs}>\n      {isDragging && <CardDragPreview label={label} />}\n      {!isDragging && isDraggingSibling && <CardDragPreview isSibling label={label} />}\n\n      {!isDragging && !isDraggingSibling && (\n        <FieldContainer\n          borderColor=\"neutral150\"\n          background=\"neutral100\"\n          hasRadius\n          justifyContent=\"space-between\"\n          onClick={() => setIsModalOpen(true)}\n        >\n          <Flex gap={3}>\n            <DragButton\n              ref={dragRef}\n              aria-label={formatMessage(\n                {\n                  id: getTranslation('components.DraggableCard.move.field'),\n                  defaultMessage: 'Move {item}',\n                },\n                { item: label }\n              )}\n              onClick={(e) => e.stopPropagation()}\n            >\n              <Drag />\n            </DragButton>\n            <Typography fontWeight=\"bold\">{label}</Typography>\n          </Flex>\n          <Flex paddingLeft={3} onClick={(e) => e.stopPropagation()}>\n            <Modal.Root open={isModalOpen} onOpenChange={setIsModalOpen}>\n              <Modal.Trigger>\n                <ActionButton\n                  onClick={(e) => {\n                    e.stopPropagation();\n                  }}\n                  aria-label={formatMessage(\n                    {\n                      id: getTranslation('components.DraggableCard.edit.field'),\n                      defaultMessage: 'Edit {item}',\n                    },\n                    { item: label }\n                  )}\n                  type=\"button\"\n                >\n                  <Pencil width=\"1.2rem\" height=\"1.2rem\" />\n                </ActionButton>\n              </Modal.Trigger>\n              <EditFieldForm\n                attribute={attribute}\n                name={`layout.${index}`}\n                onClose={() => {\n                  setIsModalOpen(false);\n                }}\n              />\n            </Modal.Root>\n            <ActionButton\n              onClick={onRemoveField}\n              data-testid={`delete-${name}`}\n              aria-label={formatMessage(\n                {\n                  id: getTranslation('components.DraggableCard.delete.field'),\n                  defaultMessage: 'Delete {item}',\n                },\n                { item: label }\n              )}\n              type=\"button\"\n            >\n              <Cross width=\"1.2rem\" height=\"1.2rem\" />\n            </ActionButton>\n          </Flex>\n        </FieldContainer>\n      )}\n    </FieldWrapper>\n  );\n};\n\nconst ActionButton = styled.button`\n  display: flex;\n  align-items: center;\n  height: ${({ theme }) => theme.spaces[7]};\n  color: ${({ theme }) => theme.colors.neutral600};\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.neutral700};\n  }\n\n  &:last-child {\n    padding: 0 ${({ theme }) => theme.spaces[3]};\n  }\n`;\n\nconst DragButton = styled(ActionButton)`\n  padding: 0 ${({ theme }) => theme.spaces[3]};\n  border-right: 1px solid ${({ theme }) => theme.colors.neutral150};\n  cursor: all-scroll;\n`;\n\nconst FieldContainer = styled<FlexComponent>(Flex)`\n  max-height: 3.2rem;\n  cursor: pointer;\n`;\n\nconst FieldWrapper = styled<BoxComponent>(Box)`\n  &:last-child {\n    padding-right: ${({ theme }) => theme.spaces[3]};\n  }\n`;\n\nexport { DraggableCard };\nexport type { DraggableCardProps };\n", "import * as React from 'react';\n\nimport { useForm } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, VisuallyHidden, Typography, Menu } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { useDoc } from '../../../hooks/useDocument';\nimport { useGetContentTypeConfigurationQuery } from '../../../services/contentTypes';\nimport { checkIfAttributeIsDisplayable } from '../../../utils/attributes';\nimport { getTranslation } from '../../../utils/translations';\n\nimport { DraggableCard, DraggableCardProps } from './DraggableCard';\n\nimport type { ListLayout } from '../../../hooks/useDocumentLayout';\nimport type { FormData } from '../ListConfigurationPage';\n\ninterface SortDisplayedFieldsProps extends Pick<ListLayout, 'layout'> {}\n\nconst SortDisplayedFields = () => {\n  const { formatMessage } = useIntl();\n  const { model, schema } = useDoc();\n  const [isDraggingSibling, setIsDraggingSibling] = React.useState(false);\n  const [lastAction, setLastAction] = React.useState<string | null>(null);\n  const scrollableContainerRef = React.useRef<HTMLDivElement>(null);\n\n  const values = useForm<FormData['layout']>(\n    'SortDisplayedFields',\n    (state) => state.values.layout ?? []\n  );\n  const addFieldRow = useForm('SortDisplayedFields', (state) => state.addFieldRow);\n  const removeFieldRow = useForm('SortDisplayedFields', (state) => state.removeFieldRow);\n  const moveFieldRow = useForm('SortDisplayedFields', (state) => state.moveFieldRow);\n\n  const { metadata: allMetadata } = useGetContentTypeConfigurationQuery(model, {\n    selectFromResult: ({ data }) => ({ metadata: data?.contentType.metadatas ?? {} }),\n  });\n\n  /**\n   * This is our list of fields that are not displayed in the current layout\n   * so we create their default state to be added to the layout.\n   */\n  const nonDisplayedFields = React.useMemo(() => {\n    if (!schema) {\n      return [];\n    }\n\n    const displayedFieldNames = values.map((field) => field.name);\n\n    return Object.entries(schema.attributes).reduce<Array<FormData['layout'][number]>>(\n      (acc, [name, attribute]) => {\n        if (!displayedFieldNames.includes(name) && checkIfAttributeIsDisplayable(attribute)) {\n          const { list: metadata } = allMetadata[name];\n\n          acc.push({\n            name,\n            label: metadata.label || name,\n            sortable: metadata.sortable,\n          });\n        }\n\n        return acc;\n      },\n      []\n    );\n  }, [allMetadata, values, schema]);\n\n  const handleAddField = (field: FormData['layout'][number]) => {\n    setLastAction('add');\n    addFieldRow('layout', field);\n  };\n\n  const handleRemoveField = (index: number) => {\n    setLastAction('remove');\n    removeFieldRow('layout', index);\n  };\n\n  const handleMoveField: DraggableCardProps['onMoveField'] = (dragIndex, hoverIndex) => {\n    moveFieldRow('layout', dragIndex, hoverIndex);\n  };\n\n  React.useEffect(() => {\n    if (lastAction === 'add' && scrollableContainerRef?.current) {\n      scrollableContainerRef.current.scrollLeft = scrollableContainerRef.current.scrollWidth;\n    }\n  }, [lastAction]);\n\n  return (\n    <Flex alignItems=\"stretch\" direction=\"column\" gap={4}>\n      <Typography variant=\"delta\" tag=\"h2\">\n        {formatMessage({\n          id: getTranslation('containers.SettingPage.view'),\n          defaultMessage: 'View',\n        })}\n      </Typography>\n\n      <Flex padding={4} borderColor=\"neutral300\" borderStyle=\"dashed\" borderWidth=\"1px\" hasRadius>\n        <Box flex=\"1\" overflow=\"auto hidden\" ref={scrollableContainerRef}>\n          <Flex gap={3}>\n            {values.map((field, index) => (\n              <DraggableCard\n                key={field.name}\n                index={index}\n                isDraggingSibling={isDraggingSibling}\n                onMoveField={handleMoveField}\n                onRemoveField={() => handleRemoveField(index)}\n                setIsDraggingSibling={setIsDraggingSibling}\n                {...field}\n                attribute={schema!.attributes[field.name]}\n                label={typeof field.label === 'object' ? formatMessage(field.label) : field.label}\n              />\n            ))}\n          </Flex>\n        </Box>\n\n        <Menu.Root>\n          <Menu.Trigger\n            paddingLeft={2}\n            paddingRight={2}\n            justifyContent=\"center\"\n            endIcon={null}\n            disabled={nonDisplayedFields.length === 0}\n            variant=\"tertiary\"\n          >\n            <VisuallyHidden tag=\"span\">\n              {formatMessage({\n                id: getTranslation('components.FieldSelect.label'),\n                defaultMessage: 'Add a field',\n              })}\n            </VisuallyHidden>\n            <Plus aria-hidden focusable={false} style={{ position: 'relative', top: 2 }} />\n          </Menu.Trigger>\n          <Menu.Content>\n            {nonDisplayedFields.map((field) => (\n              <Menu.Item key={field.name} onSelect={() => handleAddField(field)}>\n                {typeof field.label === 'object' ? formatMessage(field.label) : field.label}\n              </Menu.Item>\n            ))}\n          </Menu.Content>\n        </Menu.Root>\n      </Flex>\n    </Flex>\n  );\n};\n\nexport { SortDisplayedFields };\nexport type { SortDisplayedFieldsProps };\n", "import * as React from 'react';\n\nimport {\n  Form,\n  type FormProps,\n  useNotification,\n  useTracking,\n  useAPIError<PERSON>andler,\n  Page,\n  Layouts,\n} from '@strapi/admin/strapi-admin';\nimport { Divider, Flex, Main } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { Navigate } from 'react-router-dom';\n\nimport { SINGLE_TYPES } from '../../constants/collections';\nimport { useDoc } from '../../hooks/useDocument';\nimport { ListFieldLayout, ListLayout, useDocLayout } from '../../hooks/useDocumentLayout';\nimport { useTypedSelector } from '../../modules/hooks';\nimport { useUpdateContentTypeConfigurationMutation } from '../../services/contentTypes';\nimport { setIn } from '../../utils/objects';\n\nimport { Header } from './components/Header';\nimport { Settings } from './components/Settings';\nimport { SortDisplayedFields } from './components/SortDisplayedFields';\n\nimport type { Metadatas } from '../../../../shared/contracts/content-types';\n\ninterface FormData extends Pick<ListLayout, 'settings'> {\n  layout: Array<Pick<ListFieldLayout, 'sortable' | 'name'> & { label: string }>;\n}\n\nconst ListConfiguration = () => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { model, collectionType } = useDoc();\n\n  const { isLoading: isLoadingLayout, list, edit } = useDocLayout();\n\n  const [updateContentTypeConfiguration] = useUpdateContentTypeConfigurationMutation();\n  const handleSubmit: FormProps<FormData>['onSubmit'] = async (data) => {\n    try {\n      trackUsage('willSaveContentTypeLayout');\n      const layoutData = data.layout ?? [];\n      /**\n       * We reconstruct the metadatas object by taking the existing edit metadatas\n       * and re-merging that by attribute name with the current list metadatas, whilst overwriting\n       * the data from the form we've built.\n       */\n      const meta = Object.entries(edit.metadatas).reduce<Metadatas>((acc, [name, editMeta]) => {\n        const { mainField: _mainField, ...listMeta } = list.metadatas[name];\n\n        const { label, sortable } = layoutData.find((field) => field.name === name) ?? {};\n\n        acc[name] = {\n          edit: editMeta,\n          list: {\n            ...listMeta,\n            label: label || listMeta.label,\n            sortable: sortable || listMeta.sortable,\n          },\n        };\n\n        return acc;\n      }, {});\n\n      const res = await updateContentTypeConfiguration({\n        layouts: {\n          edit: edit.layout.flatMap((panel) =>\n            panel.map((row) => row.map(({ name, size }) => ({ name, size })))\n          ),\n          list: layoutData.map((field) => field.name),\n        },\n        settings: setIn(data.settings, 'displayName', undefined),\n        metadatas: meta,\n        uid: model,\n      });\n\n      if ('data' in res) {\n        trackUsage('didEditListSettings');\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({ id: 'notification.success.saved', defaultMessage: 'Saved' }),\n        });\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n      }\n    } catch (err) {\n      console.error(err);\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  const initialValues = React.useMemo(() => {\n    return {\n      layout: list.layout.map(({ label, sortable, name }) => ({\n        label: typeof label === 'string' ? label : formatMessage(label),\n        sortable,\n        name,\n      })),\n      settings: list.settings,\n    } satisfies FormData;\n  }, [formatMessage, list.layout, list.settings]);\n\n  if (collectionType === SINGLE_TYPES) {\n    return <Navigate to={`/single-types/${model}`} />;\n  }\n\n  if (isLoadingLayout) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Title>{`Configure ${list.settings.displayName} List View`}</Page.Title>\n      <Main>\n        <Form initialValues={initialValues} onSubmit={handleSubmit} method=\"PUT\">\n          <Header\n            collectionType={collectionType}\n            model={model}\n            name={list.settings.displayName ?? ''}\n          />\n          <Layouts.Content>\n            <Flex\n              alignItems=\"stretch\"\n              background=\"neutral0\"\n              direction=\"column\"\n              gap={6}\n              hasRadius\n              shadow=\"tableShadow\"\n              paddingTop={6}\n              paddingBottom={6}\n              paddingLeft={7}\n              paddingRight={7}\n            >\n              <Settings />\n              <Divider />\n              <SortDisplayedFields />\n            </Flex>\n          </Layouts.Content>\n        </Form>\n      </Main>\n    </Layouts.Root>\n  );\n};\n\nconst ProtectedListConfiguration = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.contentManager?.collectionTypesConfigurations\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListConfiguration />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedListConfiguration, ListConfiguration };\nexport type { FormData };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAaA,IAAM,SAAS,CAAC,EAAE,KAAA,MAAwB;AAClC,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,QAAM,WAAW,QAAQ,UAAU,CAAC,UAAU,MAAM,QAAQ;AAC5D,QAAM,eAAe,QAAQ,UAAU,CAAC,UAAU,MAAM,YAAY;AAGlE,aAAA;IAAC,QAAQ;IAAR;MACC,sBAAA,wBAAmB,YAAW,CAAA,CAAA;MAC9B,mBACG,wBAAA,QAAA,EAAO,MAAK,KAAI,UAAU,CAAC,UAAU,MAAK,UAAS,SAAS,cAC1D,UAAA,cAAc,EAAE,IAAI,eAAe,gBAAgB,OAAQ,CAAA,EAAA,CAC9D;MAEF,UAAU,cAAc;QACtB,IAAI,eAAe,uEAAuE;QAC1F,gBAAgB;MAAA,CACjB;MACD,OAAO;QACL;UACE,IAAI,eAAe,mDAAmD;UACtE,gBAAgB;QAClB;QACA,EAAE,MAAM,WAAW,IAAI,EAAE;MAC3B;IAAA;EAAA;AAGN;ACVA,IAAM,gCAAgC;EACpC;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AAOA,IAAM,WAAW,MAAM;AACrB,QAAM,EAAE,eAAe,OAAO,IAAI,QAAQ;AACpC,QAAA,YAAY,YAAY,QAAQ;IACpC,aAAa;EAAA,CACd;AACK,QAAA,EAAE,OAAA,IAAW,OAAA;AAEb,QAAA,SAAS,QAA4B,YAAY,CAAC,UAAU,MAAM,OAAO,UAAU,CAAA,CAAE;AAC3F,QAAM,gBAAgB;IACpB;IACA,CAAC,UAAU,MAAM,OAAO,SAAS;EAAA;AAEnC,QAAM,WAAW,QAAQ,YAAY,CAAC,UAAU,MAAM,QAAQ;AAE9D,QAAM,cAAoB;IACxB,MACE,OAAO,OAAO,MAAM,EAAE,OAAqB,CAAC,KAAK,UAAU;AACrD,UAAA,UAAU,CAAC,8BAA8B,SAAS,OAAO,WAAW,MAAM,IAAI,EAAE,IAAI,GAAG;AACzF,YAAI,KAAK;UACP,OAAO,MAAM;UACb,OAAO,OAAO,MAAM,UAAU,WAAW,cAAc,MAAM,KAAK,IAAI,MAAM;QAAA,CAC7E;MACH;AAEO,aAAA;IACT,GAAG,CAAA,CAAE;IACP,CAAC,eAAe,QAAQ,MAAM;EAAA;AAGhC,QAAM,oBAAoB,YAAY,KAAK,CAAC,GAAG,MAAM,UAAU,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC;AAExF,EAAM,gBAAU,MAAM;;AAChB,QAAA,kBAAkB,UAAU,CAAC,QAAQ,IAAI,UAAU,aAAa,MAAM,IAAI;AAC5E,eAAS,2BAA0B,uBAAkB,CAAC,MAAnB,mBAAsB,KAAK;IAChE;EACC,GAAA,CAAC,eAAe,UAAU,iBAAiB,CAAC;AAE/C,QAAM,aAAmB;IACvB,MACE,qBAAqB;MAAI,CAAC,QACxB,IAAI,IAAI,CAAC,UAAU;AACb,YAAA,MAAM,SAAS,eAAe;AACzB,iBAAA;YACL,GAAG;YACH,MAAM,MAAM,OAAO,cAAc,MAAM,IAAI,IAAI;YAC/C,OAAO,cAAc,MAAM,KAAK;YAChC,SAAS,MAAM,SAAS,2BAA2B,oBAAoB,MAAM;UAAA;QAC/E,OACK;AACE,iBAAA;YACL,GAAG;YACH,MAAM,MAAM,OAAO,cAAc,MAAM,IAAI,IAAI;YAC/C,OAAO,cAAc,MAAM,KAAK;UAAA;QAEpC;MAAA,CACD;IACH;IACF,CAAC,eAAe,iBAAiB;EAAA;AAGnC,aAAA,yBACG,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;QAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;MACb,IAAI,eAAe,iCAAiC;MACpD,gBAAgB;IACjB,CAAA,EAAA,CACH;QAAA,wBACC,KAAK,MAAL,EAAuB,KAAK,GAC1B,UAAW,WAAA;MAAI,CAAC,QACf,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,UACvB,wBAAA,KAAK,MAAL,EAA2B,GAAG,IAAI,KAAK,MAAM,WAAU,UAAS,YAAW,WAE1E,cAAC,wBAAA,uBAAA,EAAe,GAAG,MAAO,CAAA,EAFZ,GAAA,MAAM,IAGtB,CACD;IAAA,EAAA,GAPU,QASf;EACF,EAAA,CAAA;AAEJ;AAEA,IAAM,uBAAiD;EACrD;IACE;MACE,OAAO;QACL,IAAI,eAAe,mBAAmB;QACtC,gBAAgB;MAClB;MACA,MAAM;MACN,MAAM;MACN,MAAM;IACR;IACA;MACE,OAAO;QACL,IAAI,eAAe,oBAAoB;QACvC,gBAAgB;MAClB;MACA,MAAM;MACN,MAAM;MACN,MAAM;IACR;IACA;MACE,OAAO;QACL,IAAI,eAAe,wBAAwB;QAC3C,gBAAgB;MAClB;MACA,MAAM;MACN,MAAM;MACN,MAAM;IACR;EACF;EACA;IACE;MACE,MAAM;QACJ,IAAI,eAAe,yCAAyC;QAC5D,gBAAgB;MAClB;MACA,OAAO;QACL,IAAI,eAAe,wBAAwB;QAC3C,gBAAgB;MAClB;MACA,MAAM;MACN,SAAS,CAAC,MAAM,MAAM,MAAM,KAAK,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,OAAO,MAAQ,EAAA;MAC3E,MAAM;MACN,MAAM;IACR;IACA;MACE,OAAO;QACL,IAAI,eAAe,wBAAwB;QAC3C,gBAAgB;MAClB;MACA,MAAM;MACN,SAAS,CAAC;MACV,MAAM;MACN,MAAM;IACR;IACA;MACE,OAAO;QACL,IAAI,eAAe,uBAAuB;QAC1C,gBAAgB;MAClB;MACA,MAAM;MACN,SAAS,CAAC,OAAO,MAAM,EAAE,IAAI,CAAC,WAAW,EAAE,OAAO,OAAO,MAAQ,EAAA;MACjE,MAAM;MACN,MAAM;IACR;EACF;AACF;AC/KA,IAAM,eAAmBA,QAAO,EAAE,MAAM;EACtC,OAAWA,QAAO,EAAE,SAAS;EAC7B,UAAc,OAAQ;AACxB,CAAC;AAED,IAAM,gBAAgB,CAAC,EAAE,WAAW,MAAM,QAAA,MAAkC;AACpE,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAE/B,QAAM,EAAE,OAAO,SAAS,IAAI,SAAqC,IAAI;AAErE,MAAI,CAAC,OAAO;AAEF,YAAA;MACN;IAAA;AAGiB,uBAAA;MACjB,SAAS,cAAc;QACrB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,MAAM;IAAA,CACP;AAEM,WAAA;EACT;AAEI,MAAA,0BAA0B,CAAC,CAAC,SAAS,UAAU,EAAE,SAAS,UAAU,IAAI;AAExE,MAAA,cAAc,aAAa,CAAC,UAAU,YAAY,WAAW,EAAE,SAAS,UAAU,QAAQ,GAAG;AACrE,8BAAA;EAC5B;AAGE,aAAA,wBAAC,MAAM,SAAN,EACC,cAAA;IAAC;IAAA;MACC,QAAO;MACP,eAAe;MACf,kBAAkB;MAClB,UAAU,CAAC,SAAS;AAClB,iBAAS,MAAM,IAAI;AACX,gBAAA;MACV;MAEA,UAAA;YAAA,wBAAC,MAAM,QAAN,EACC,cAAA,yBAAC,iBAEC,EAAA,UAAA;cAAC,wBAAA,eAAA,EAAc,MAAM,UAAU,KAAM,CAAA;cACrC,wBAAC,MAAM,OAAN,EACE,UAAA;YACC;cACE,IAAI,eAAe,2CAA2C;cAC9D,gBAAgB;YAClB;YACA,EAAE,WAAW,WAAW,MAAM,KAAK,EAAE;UAAA,EAAA,CAEzC;QAAA,EAAA,CACF,EACF,CAAA;YACA,wBAAC,MAAM,MAAN,EACC,cAAA,wBAAC,KAAK,MAAL,EAAU,KAAK,GACb,UAAA;UACC;YACE,MAAM;YACN,OAAO,cAAc;cACnB,IAAI,eAAe,kBAAkB;cACrC,gBAAgB;YAAA,CACjB;YACD,MAAM,cAAc;cAClB,IAAI,eAAe,mCAAmC;cACtD,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,MAAM;UACR;UACA;YACE,OAAO,cAAc;cACnB,IAAI,eAAe,uBAAuB;cAC1C,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,MAAM;YACN,MAAM;UACR;QAAA,EAEC;UACC,CAAC,UACC,MAAM,SAAS,cACd,MAAM,SAAS,cAAc;QAAA,EAEjC,IAAI,CAAC,EAAE,MAAM,GAAG,MACf,UAAA;UAAC,KAAK;UAAL;YAEC,GAAG;YACH,KAAK;YACL,WAAU;YACV,YAAW;YAEX,cAAA,wBAAC,uBAAe,EAAA,GAAG,MAAO,CAAA;UAAA;UANrB,MAAM;QAAA,CAQd,EAAA,CACL,EACF,CAAA;YACA,yBAAC,MAAM,QAAN,EACC,UAAA;cAAA,wBAAC,QAAO,EAAA,SAAS,SAAS,SAAQ,YAC/B,UAAA,cAAc,EAAE,IAAI,gCAAgC,gBAAgB,SAAS,CAAC,EACjF,CAAA;cACA,wBAAC,QAAO,EAAA,MAAK,UACV,UAAA,cAAc,EAAE,IAAI,iBAAiB,gBAAgB,SAAS,CAAC,EAClE,CAAA;QAAA,EAAA,CACF;MAAA;IAAA;EAEJ,EAAA,CAAA;AAEJ;AAEA,IAAM,kBAAkB,GAAsB,IAAI;;;oBAG9B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;;ACxGlD,IAAM,gBAAgB,CAAC;EACrB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MAA0B;AACxB,QAAM,CAAC,aAAa,cAAc,IAAU,eAAS,KAAK;AACpD,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,CAAG,EAAA,qBAAqB,IAAU,eAAS,KAAK;AAEhD,QAAA,CAAC,EAAE,WAAc,GAAA,WAAW,SAAS,SAAS,cAAc,IAAI,eAAe,MAAM;IACzF,MAAM,UAAU;IAChB,MAAM,EAAE,OAAO,OAAO,KAAK;IAC3B;IACA,YAAY;IACZ,OAAO,MAAM,qBAAqB,KAAK;EAAA,CACxC;AAED,EAAM,gBAAU,MAAM;AACpB,mBAAe,cAAc,GAAG,EAAE,sBAAsB,MAAO,CAAA;EAAA,GAC9D,CAAC,cAAc,CAAC;AAEnB,EAAM,gBAAU,MAAM;AACpB,QAAI,YAAY;AACd,2BAAqB,IAAI;IAC3B;EAAA,GACC,CAAC,YAAY,oBAAoB,CAAC;AAKrC,EAAM,gBAAU,MAAM;AACpB,QAAI,CAAC,mBAAmB;AACA,4BAAA,CAAC,SAAS,CAAC,IAAI;IACvC;EAAA,GACC,CAAC,iBAAiB,CAAC;AAEtB,QAAM,eAAe;IACnB;IACA;EAAA;AAIA,aAAA,yBAAC,cAAa,EAAA,KAAK,cAChB,UAAA;IAAc,kBAAA,wBAAC,iBAAA,EAAgB,MAAc,CAAA;IAC7C,CAAC,cAAc,yBAAA,wBAAsB,iBAAgB,EAAA,WAAS,MAAC,MAAA,CAAc;IAE7E,CAAC,cAAc,CAAC,yBACf;MAAC;MAAA;QACC,aAAY;QACZ,YAAW;QACX,WAAS;QACT,gBAAe;QACf,SAAS,MAAM,eAAe,IAAI;QAElC,UAAA;cAAC,yBAAA,MAAA,EAAK,KAAK,GACT,UAAA;gBAAA;cAAC;cAAA;gBACC,KAAK;gBACL,cAAY;kBACV;oBACE,IAAI,eAAe,qCAAqC;oBACxD,gBAAgB;kBAClB;kBACA,EAAE,MAAM,MAAM;gBAChB;gBACA,SAAS,CAAC,MAAM,EAAE,gBAAgB;gBAElC,cAAA,wBAAC,eAAK,CAAA,CAAA;cAAA;YACR;gBACC,wBAAA,YAAA,EAAW,YAAW,QAAQ,UAAM,MAAA,CAAA;UAAA,EAAA,CACvC;cACA,yBAAC,MAAA,EAAK,aAAa,GAAG,SAAS,CAAC,MAAM,EAAE,gBACtC,GAAA,UAAA;gBAAA,yBAAC,MAAM,MAAN,EAAW,MAAM,aAAa,cAAc,gBAC3C,UAAA;kBAAC,wBAAA,MAAM,SAAN,EACC,cAAA;gBAAC;gBAAA;kBACC,SAAS,CAAC,MAAM;AACd,sBAAE,gBAAgB;kBACpB;kBACA,cAAY;oBACV;sBACE,IAAI,eAAe,qCAAqC;sBACxD,gBAAgB;oBAClB;oBACA,EAAE,MAAM,MAAM;kBAChB;kBACA,MAAK;kBAEL,cAAC,wBAAA,eAAA,EAAO,OAAM,UAAS,QAAO,SAAA,CAAS;gBAAA;cAAA,EAAA,CAE3C;kBACA;gBAAC;gBAAA;kBACC;kBACA,MAAM,UAAU,KAAK;kBACrB,SAAS,MAAM;AACb,mCAAe,KAAK;kBACtB;gBAAA;cACF;YAAA,EAAA,CACF;gBACA;cAAC;cAAA;gBACC,SAAS;gBACT,eAAa,UAAU,IAAI;gBAC3B,cAAY;kBACV;oBACE,IAAI,eAAe,uCAAuC;oBAC1D,gBAAgB;kBAClB;kBACA,EAAE,MAAM,MAAM;gBAChB;gBACA,MAAK;gBAEL,cAAC,wBAAA,eAAA,EAAM,OAAM,UAAS,QAAO,SAAA,CAAS;cAAA;YACxC;UAAA,EAAA,CACF;QAAA;MAAA;IACF;EAEJ,EAAA,CAAA;AAEJ;AAEA,IAAM,eAAe,GAAO;;;YAGhB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;WAC/B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;aAGpC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;iBAIlC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;;AAI/C,IAAM,aAAa,GAAO,YAAY;eACvB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;4BACjB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;AAIlE,IAAM,iBAAiB,GAAsB,IAAI;;;;AAKjD,IAAM,eAAe,GAAqB,GAAG;;qBAExB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;;ACtKnD,IAAM,sBAAsB,MAAM;AAC1B,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,OAAO,OAAO,IAAI,OAAO;AACjC,QAAM,CAAC,mBAAmB,oBAAoB,IAAU,eAAS,KAAK;AACtE,QAAM,CAAC,YAAY,aAAa,IAAU,eAAwB,IAAI;AAChE,QAAA,yBAA+B,aAAuB,IAAI;AAEhE,QAAM,SAAS;IACb;IACA,CAAC,UAAU,MAAM,OAAO,UAAU,CAAC;EAAA;AAErC,QAAM,cAAc,QAAQ,uBAAuB,CAAC,UAAU,MAAM,WAAW;AAC/E,QAAM,iBAAiB,QAAQ,uBAAuB,CAAC,UAAU,MAAM,cAAc;AACrF,QAAM,eAAe,QAAQ,uBAAuB,CAAC,UAAU,MAAM,YAAY;AAEjF,QAAM,EAAE,UAAU,YAAA,IAAgB,oCAAoC,OAAO;IAC3E,kBAAkB,CAAC,EAAE,KAAK,OAAO,EAAE,WAAU,6BAAM,YAAY,cAAa,CAAA,EAAA;EAAG,CAChF;AAMK,QAAA,qBAA2B,cAAQ,MAAM;AAC7C,QAAI,CAAC,QAAQ;AACX,aAAO,CAAA;IACT;AAEA,UAAM,sBAAsB,OAAO,IAAI,CAAC,UAAU,MAAM,IAAI;AAE5D,WAAO,OAAO,QAAQ,OAAO,UAAU,EAAE;MACvC,CAAC,KAAK,CAAC,MAAM,SAAS,MAAM;AAC1B,YAAI,CAAC,oBAAoB,SAAS,IAAI,KAAK,8BAA8B,SAAS,GAAG;AACnF,gBAAM,EAAE,MAAM,SAAS,IAAI,YAAY,IAAI;AAE3C,cAAI,KAAK;YACP;YACA,OAAO,SAAS,SAAS;YACzB,UAAU,SAAS;UAAA,CACpB;QACH;AAEO,eAAA;MACT;MACA,CAAC;IAAA;EAEF,GAAA,CAAC,aAAa,QAAQ,MAAM,CAAC;AAE1B,QAAA,iBAAiB,CAAC,UAAsC;AAC5D,kBAAc,KAAK;AACnB,gBAAY,UAAU,KAAK;EAAA;AAGvB,QAAA,oBAAoB,CAAC,UAAkB;AAC3C,kBAAc,QAAQ;AACtB,mBAAe,UAAU,KAAK;EAAA;AAG1B,QAAA,kBAAqD,CAAC,WAAW,eAAe;AACvE,iBAAA,UAAU,WAAW,UAAU;EAAA;AAG9C,EAAM,gBAAU,MAAM;AAChB,QAAA,eAAe,UAAS,iEAAwB,UAAS;AACpC,6BAAA,QAAQ,aAAa,uBAAuB,QAAQ;IAC7E;EAAA,GACC,CAAC,UAAU,CAAC;AAEf,aAAA,yBACG,MAAK,EAAA,YAAW,WAAU,WAAU,UAAS,KAAK,GACjD,UAAA;QAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;MACb,IAAI,eAAe,6BAA6B;MAChD,gBAAgB;IACjB,CAAA,EAAA,CACH;QAEA,yBAAC,MAAK,EAAA,SAAS,GAAG,aAAY,cAAa,aAAY,UAAS,aAAY,OAAM,WAAS,MACzF,UAAA;UAAA,wBAAC,KAAI,EAAA,MAAK,KAAI,UAAS,eAAc,KAAK,wBACxC,cAAC,wBAAA,MAAA,EAAK,KAAK,GACR,UAAA,OAAO,IAAI,CAAC,OAAO,cAClB;QAAC;QAAA;UAEC;UACA;UACA,aAAa;UACb,eAAe,MAAM,kBAAkB,KAAK;UAC5C;UACC,GAAG;UACJ,WAAW,OAAQ,WAAW,MAAM,IAAI;UACxC,OAAO,OAAO,MAAM,UAAU,WAAW,cAAc,MAAM,KAAK,IAAI,MAAM;QAAA;QARvE,MAAM;MAAA,CAUd,EAAA,CACH,EACF,CAAA;UAEA,yBAAC,KAAK,MAAL,EACC,UAAA;YAAA;UAAC,KAAK;UAAL;YACC,aAAa;YACb,cAAc;YACd,gBAAe;YACf,SAAS;YACT,UAAU,mBAAmB,WAAW;YACxC,SAAQ;YAER,UAAA;kBAAC,wBAAA,gBAAA,EAAe,KAAI,QACjB,UAAc,cAAA;gBACb,IAAI,eAAe,8BAA8B;gBACjD,gBAAgB;cACjB,CAAA,EAAA,CACH;kBACC,wBAAA,eAAA,EAAK,eAAW,MAAC,WAAW,OAAO,OAAO,EAAE,UAAU,YAAY,KAAK,EAAK,EAAA,CAAA;YAAA;UAAA;QAC/E;YACC,wBAAA,KAAK,SAAL,EACE,UAAmB,mBAAA,IAAI,CAAC,cACtB,wBAAA,KAAK,MAAL,EAA2B,UAAU,MAAM,eAAe,KAAK,GAC7D,UAAO,OAAA,MAAM,UAAU,WAAW,cAAc,MAAM,KAAK,IAAI,MAAM,MAAA,GADxD,MAAM,IAEtB,CACD,EACH,CAAA;MAAA,EAAA,CACF;IAAA,EAAA,CACF;EACF,EAAA,CAAA;AAEJ;AC/GA,IAAM,oBAAoB,MAAM;AACxB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,WAAA,IAAe,YAAA;AACjB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AAEvE,QAAM,EAAE,OAAO,eAAe,IAAI,OAAO;AAEzC,QAAM,EAAE,WAAW,iBAAiB,MAAM,KAAA,IAAS,aAAA;AAE7C,QAAA,CAAC,8BAA8B,IAAI,0CAAA;AACnC,QAAA,eAAgD,OAAO,SAAS;AAChE,QAAA;AACF,iBAAW,2BAA2B;AAChC,YAAA,aAAa,KAAK,UAAU,CAAA;AAMlC,YAAM,OAAO,OAAO,QAAQ,KAAK,SAAS,EAAE,OAAkB,CAAC,KAAK,CAAC,MAAM,QAAQ,MAAM;AACjF,cAAA,EAAE,WAAW,YAAY,GAAG,SAAa,IAAA,KAAK,UAAU,IAAI;AAElE,cAAM,EAAE,OAAO,SAAS,IAAI,WAAW,KAAK,CAAC,UAAU,MAAM,SAAS,IAAI,KAAK,CAAA;AAE/E,YAAI,IAAI,IAAI;UACV,MAAM;UACN,MAAM;YACJ,GAAG;YACH,OAAO,SAAS,SAAS;YACzB,UAAU,YAAY,SAAS;UACjC;QAAA;AAGK,eAAA;MACT,GAAG,CAAE,CAAA;AAEC,YAAA,MAAM,MAAM,+BAA+B;QAC/C,SAAS;UACP,MAAM,KAAK,OAAO;YAAQ,CAAC,UACzB,MAAM,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE,MAAM,KAAK,OAAO,EAAE,MAAM,KAAA,EAAO,CAAC;UAClE;UACA,MAAM,WAAW,IAAI,CAAC,UAAU,MAAM,IAAI;QAC5C;QACA,UAAU,MAAM,KAAK,UAAU,eAAe,MAAS;QACvD,WAAW;QACX,KAAK;MAAA,CACN;AAED,UAAI,UAAU,KAAK;AACjB,mBAAW,qBAAqB;AACb,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc,EAAE,IAAI,8BAA8B,gBAAgB,QAAA,CAAS;QAAA,CACrF;MAAA,OACI;AACc,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,IAAI,KAAK;QAAA,CAClC;MACH;IAAA,SACO,KAAK;AACZ,cAAQ,MAAM,GAAG;AACE,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,sBAAsB,gBAAgB,oBAAA,CAAqB;MAAA,CACzF;IACH;EAAA;AAGI,QAAA,gBAAsB,cAAQ,MAAM;AACjC,WAAA;MACL,QAAQ,KAAK,OAAO,IAAI,CAAC,EAAE,OAAO,UAAU,KAAA,OAAY;QACtD,OAAO,OAAO,UAAU,WAAW,QAAQ,cAAc,KAAK;QAC9D;QACA;MAAA,EACA;MACF,UAAU,KAAK;IAAA;EACjB,GACC,CAAC,eAAe,KAAK,QAAQ,KAAK,QAAQ,CAAC;AAE9C,MAAI,mBAAmB,cAAc;AACnC,eAAQ,wBAAA,UAAA,EAAS,IAAI,iBAAiB,KAAK,GAAI,CAAA;EACjD;AAEA,MAAI,iBAAiB;AACZ,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAGE,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAA,wBAAC,KAAK,OAAL,EAAY,UAAA,aAAa,KAAK,SAAS,WAAW,aAAa,CAAA;QAChE,wBAAC,MAAA,EACC,cAAC,yBAAA,MAAA,EAAK,eAA8B,UAAU,cAAc,QAAO,OACjE,UAAA;UAAA;QAAC;QAAA;UACC;UACA;UACA,MAAM,KAAK,SAAS,eAAe;QAAA;MACrC;UACA,wBAAC,QAAQ,SAAR,EACC,cAAA;QAAC;QAAA;UACC,YAAW;UACX,YAAW;UACX,WAAU;UACV,KAAK;UACL,WAAS;UACT,QAAO;UACP,YAAY;UACZ,eAAe;UACf,aAAa;UACb,cAAc;UAEd,UAAA;gBAAA,wBAAC,UAAS,CAAA,CAAA;gBAAA,wBACT,SAAQ,CAAA,CAAA;gBAAA,wBACR,qBAAoB,CAAA,CAAA;UAAA;QAAA;MAAA,EAAA,CAEzB;IAAA,EAAA,CACF,EACF,CAAA;EACF,EAAA,CAAA;AAEJ;AAEA,IAAM,6BAA6B,MAAM;AACvC,QAAM,cAAc;IAClB,CAAC,UAAA;;AAAU,yBAAM,UAAU,YAAY,mBAA5B,mBAA4C;;EAAA;AAGzD,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,mBAAA,CAAA,CAAkB,EACrB,CAAA;AAEJ;", "names": ["create"]}