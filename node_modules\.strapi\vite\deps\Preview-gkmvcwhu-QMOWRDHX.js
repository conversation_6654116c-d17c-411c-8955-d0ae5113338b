import {
  getDocumentStatus
} from "./chunk-NVEXVXFK.js";
import "./chunk-7MU5PDWU.js";
import "./chunk-KDCDBLW6.js";
import "./chunk-I66PR7OD.js";
import "./chunk-YV7PSTIW.js";
import "./chunk-3O7FGDSC.js";
import "./chunk-JEXFZZG6.js";
import "./chunk-Y4KMKP2D.js";
import "./chunk-3TBNZ2ES.js";
import {
  COLLECTION_TYPES,
  DocumentRBAC,
  buildValidParams,
  useDocument,
  useDocumentLayout,
  useGetPreviewUrlQuery,
  useIntl
} from "./chunk-D63J2BWQ.js";
import "./chunk-M5EKOYPZ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-VOKTPSOW.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  useClipboard,
  useHistory
} from "./chunk-ELTZWS66.js";
import {
  Page,
  createContext,
  useNotification,
  useQueryParams,
  useRBAC
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Flex,
  FocusTrap,
  IconButton,
  Portal$1,
  Typography,
  require_lib
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  Link,
  useNavigate,
  useParams
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$2l,
  ForwardRef$3V
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/Preview-gkmvcwhu.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
var PreviewContent = () => {
  const previewUrl = usePreviewContext("PreviewContent", (state) => state.url);
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(
    Box,
    {
      src: previewUrl,
      title: formatMessage({
        id: "content-manager.preview.panel.title",
        defaultMessage: "Preview"
      }),
      width: "100%",
      height: "100%",
      borderWidth: 0,
      tag: "iframe"
    }
  );
};
var ClosePreviewButton = () => {
  const [{ query }] = useQueryParams();
  const navigate = useNavigate();
  const { formatMessage } = useIntl();
  const canGoBack = useHistory("BackButton", (state) => state.canGoBack);
  const goBack = useHistory("BackButton", (state) => state.goBack);
  const history = useHistory("BackButton", (state) => state.history);
  const fallbackUrl = {
    pathname: "..",
    search: (0, import_qs.stringify)(query, { encode: false })
  };
  const handleClick = (e) => {
    e.preventDefault();
    if (canGoBack) {
      goBack();
    } else {
      navigate(fallbackUrl);
    }
  };
  return (0, import_jsx_runtime.jsx)(
    IconButton,
    {
      tag: Link,
      to: history.at(-1) ?? fallbackUrl,
      onClick: handleClick,
      label: formatMessage({
        id: "content-manager.preview.header.close",
        defaultMessage: "Close preview"
      }),
      children: (0, import_jsx_runtime.jsx)(ForwardRef$3V, {})
    }
  );
};
var getStatusData = (status) => {
  switch (status) {
    case "draft":
      return {
        background: "secondary100",
        border: "secondary200",
        text: "secondary700",
        message: {
          id: "content-manager.containers.List.draft",
          defaultMessage: "Draft"
        }
      };
    case "modified":
      return {
        background: "alternative100",
        border: "alternative200",
        text: "alternative700",
        message: {
          id: "content-manager.containers.List.modified",
          defaultMessage: "Modified"
        }
      };
    case "published":
    default:
      return {
        background: "success100",
        border: "success200",
        text: "success700",
        message: {
          id: "content-manager.containers.List.published",
          defaultMessage: "Published"
        }
      };
  }
};
var DocumentStatus = () => {
  var _a;
  const { formatMessage } = useIntl();
  const document = usePreviewContext("PreviewHeader", (state) => state.document);
  const schema = usePreviewContext("PreviewHeader", (state) => state.schema);
  const meta = usePreviewContext("PreviewHeader", (state) => state.meta);
  const hasDraftAndPublished = ((_a = schema == null ? void 0 : schema.options) == null ? void 0 : _a.draftAndPublish) ?? false;
  const status = getDocumentStatus(document, meta);
  const statusData = getStatusData(status);
  if (!hasDraftAndPublished) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(
    Box,
    {
      background: statusData.background,
      borderStyle: "solid",
      borderWidth: "1px",
      borderColor: statusData.border,
      hasRadius: true,
      paddingLeft: "6px",
      paddingRight: "6px",
      paddingTop: "2px",
      paddingBottom: "2px",
      children: (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", fontWeight: "bold", textColor: statusData.text, children: formatMessage(statusData.message) })
    }
  );
};
var PreviewHeader = () => {
  const mainField = usePreviewContext("PreviewHeader", (state) => state.mainField);
  const document = usePreviewContext("PreviewHeader", (state) => state.document);
  const title = document[mainField];
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { copy } = useClipboard();
  const handleCopyLink = () => {
    copy(window.location.href);
    toggleNotification({
      message: formatMessage({
        id: "content-manager.preview.copy.success",
        defaultMessage: "Copied preview link"
      }),
      type: "success"
    });
  };
  return (0, import_jsx_runtime.jsxs)(
    Flex,
    {
      justifyContent: "space-between",
      background: "neutral0",
      padding: 2,
      borderColor: "neutral150",
      tag: "header",
      children: [
        (0, import_jsx_runtime.jsxs)(Flex, { gap: 3, children: [
          (0, import_jsx_runtime.jsx)(ClosePreviewButton, {}),
          (0, import_jsx_runtime.jsx)(Typography, { tag: "h1", fontWeight: 600, fontSize: 2, children: title }),
          (0, import_jsx_runtime.jsx)(DocumentStatus, {})
        ] }),
        (0, import_jsx_runtime.jsx)(
          IconButton,
          {
            type: "button",
            label: formatMessage({
              id: "preview.copy.label",
              defaultMessage: "Copy preview link"
            }),
            onClick: handleCopyLink,
            children: (0, import_jsx_runtime.jsx)(ForwardRef$2l, {})
          }
        )
      ]
    }
  );
};
var [PreviewProvider, usePreviewContext] = createContext("PreviewPage");
var PreviewPage = () => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const {
    slug: model,
    id: documentId,
    collectionType
  } = useParams();
  const [{ query }] = useQueryParams();
  const params = React.useMemo(() => buildValidParams(query), [query]);
  if (!collectionType) {
    throw new Error("Could not find collectionType in url params");
  }
  if (!model) {
    throw new Error("Could not find model in url params");
  }
  if (collectionType === COLLECTION_TYPES && !documentId) {
    throw new Error("Could not find documentId in url params");
  }
  const previewUrlResponse = useGetPreviewUrlQuery({
    params: {
      contentType: model
    },
    query: {
      documentId,
      locale: params.locale,
      status: params.status
    }
  });
  const documentResponse = useDocument({
    model,
    collectionType,
    documentId,
    params
  });
  const documentLayoutResponse = useDocumentLayout(model);
  if (documentResponse.isLoading || previewUrlResponse.isLoading || documentLayoutResponse.isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  if (previewUrlResponse.error || documentLayoutResponse.error || !documentResponse.document || !documentResponse.meta || !documentResponse.schema) {
    return (0, import_jsx_runtime.jsx)(Page.Error, {});
  }
  if (!((_b = (_a = previewUrlResponse.data) == null ? void 0 : _a.data) == null ? void 0 : _b.url)) {
    return (0, import_jsx_runtime.jsx)(Page.NoData, {});
  }
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      {
        id: "content-manager.preview.page-title",
        defaultMessage: "{contentType} preview"
      },
      {
        contentType: documentLayoutResponse.edit.settings.displayName
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      PreviewProvider,
      {
        url: previewUrlResponse.data.data.url,
        mainField: documentLayoutResponse.edit.settings.mainField,
        document: documentResponse.document,
        meta: documentResponse.meta,
        schema: documentResponse.schema,
        children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", height: "100%", alignItems: "stretch", children: [
          (0, import_jsx_runtime.jsx)(PreviewHeader, {}),
          (0, import_jsx_runtime.jsx)(PreviewContent, {})
        ] })
      }
    )
  ] });
};
var ProtectedPreviewPageImpl = () => {
  const { slug: model } = useParams();
  const {
    permissions = [],
    isLoading,
    error
  } = useRBAC([{ action: "plugin::content-manager.explorer.read", subject: model }]);
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  if (error || !model) {
    return (0, import_jsx_runtime.jsx)(
      Box,
      {
        height: "100vh",
        width: "100vw",
        position: "fixed",
        top: 0,
        left: 0,
        zIndex: 2,
        background: "neutral0",
        children: (0, import_jsx_runtime.jsx)(Page.Error, {})
      }
    );
  }
  return (0, import_jsx_runtime.jsx)(
    Box,
    {
      height: "100vh",
      width: "100vw",
      position: "fixed",
      top: 0,
      left: 0,
      zIndex: 2,
      background: "neutral0",
      children: (0, import_jsx_runtime.jsx)(Page.Protect, { permissions, children: ({ permissions: permissions2 }) => (0, import_jsx_runtime.jsx)(DocumentRBAC, { permissions: permissions2, children: (0, import_jsx_runtime.jsx)(PreviewPage, {}) }) })
    }
  );
};
var ProtectedPreviewPage = () => {
  return (0, import_jsx_runtime.jsx)(Portal$1, { children: (0, import_jsx_runtime.jsx)(FocusTrap, { children: (0, import_jsx_runtime.jsx)(ProtectedPreviewPageImpl, {}) }) });
};
export {
  ProtectedPreviewPage,
  usePreviewContext
};
//# sourceMappingURL=Preview-gkmvcwhu-QMOWRDHX.js.map
