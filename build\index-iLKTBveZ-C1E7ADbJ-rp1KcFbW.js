import{r as C,R as Vo,m as <PERSON>,S as ye,J as me,U as <PERSON>,w as He,s as ht,W as Os,X as Ts,Y as Ps,Z as As,_ as Is,$ as xs,a0 as $s,a1 as Ht,a2 as Go,a3 as Sa,a4 as ve,a5 as <PERSON>,x as F,y as Ms,a6 as Cs,a7 as ks,a8 as _s,z as Dr,a9 as Ds,aa as qs,ab as Ls,ac as Fs,ad as Ns,ae as js,af as Us,ag as Bs,i as qr,j as Qs,l as de,ah as Wn,ai as zs,aj as Hs,A as Ws,ak as Vs,al as Ks,am as Gs,an as Rr,ao as Js,ap as Ys,aq as Zs}from"./strapi-YzJfjJ2z.js";import{u as Ke}from"./index-dwdMDrmv.js";import{F as Xs,t as Vn,f as Ot,r as Yo,T as eu,c as Zo,p as Ea,e as tu}from"./immer.esm-DiDhQEzX.js";var Ra=typeof globalThis<"u"?globalThis:typeof window<"u"?window:typeof global<"u"?global:typeof self<"u"?self:{};function ru(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function nu(e){if(e.__esModule)return e;var t=e.default;if(typeof t=="function"){var r=function n(){return this instanceof n?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};r.prototype=t.prototype}else r={};return Object.defineProperty(r,"__esModule",{value:!0}),Object.keys(e).forEach(function(n){var a=Object.getOwnPropertyDescriptor(e,n);Object.defineProperty(r,n,a.get?a:{enumerable:!0,get:function(){return e[n]}})}),r}var au=Error,ou=EvalError,iu=RangeError,su=ReferenceError,Xo=SyntaxError,tr=TypeError,uu=URIError,lu=function(){if(typeof Symbol!="function"||typeof Object.getOwnPropertySymbols!="function")return!1;if(typeof Symbol.iterator=="symbol")return!0;var t={},r=Symbol("test"),n=Object(r);if(typeof r=="string"||Object.prototype.toString.call(r)!=="[object Symbol]"||Object.prototype.toString.call(n)!=="[object Symbol]")return!1;var a=42;t[r]=a;for(r in t)return!1;if(typeof Object.keys=="function"&&Object.keys(t).length!==0||typeof Object.getOwnPropertyNames=="function"&&Object.getOwnPropertyNames(t).length!==0)return!1;var o=Object.getOwnPropertySymbols(t);if(o.length!==1||o[0]!==r||!Object.prototype.propertyIsEnumerable.call(t,r))return!1;if(typeof Object.getOwnPropertyDescriptor=="function"){var i=Object.getOwnPropertyDescriptor(t,r);if(i.value!==a||i.enumerable!==!0)return!1}return!0},Oa=typeof Symbol<"u"&&Symbol,cu=lu,fu=function(){return typeof Oa!="function"||typeof Symbol!="function"||typeof Oa("foo")!="symbol"||typeof Symbol("bar")!="symbol"?!1:cu()},Ta={foo:{}},du=Object,pu=function(){return{__proto__:Ta}.foo===Ta.foo&&!({__proto__:null}instanceof du)},hu="Function.prototype.bind called on incompatible ",yu=Object.prototype.toString,vu=Math.max,mu="[object Function]",Pa=function(t,r){for(var n=[],a=0;a<t.length;a+=1)n[a]=t[a];for(var o=0;o<r.length;o+=1)n[o+t.length]=r[o];return n},gu=function(t,r){for(var n=[],a=r,o=0;a<t.length;a+=1,o+=1)n[o]=t[a];return n},bu=function(e,t){for(var r="",n=0;n<e.length;n+=1)r+=e[n],n+1<e.length&&(r+=t);return r},wu=function(t){var r=this;if(typeof r!="function"||yu.apply(r)!==mu)throw new TypeError(hu+r);for(var n=gu(arguments,1),a,o=function(){if(this instanceof a){var c=r.apply(this,Pa(n,arguments));return Object(c)===c?c:this}return r.apply(t,Pa(n,arguments))},i=vu(0,r.length-n.length),s=[],l=0;l<i;l++)s[l]="$"+l;if(a=Function("binder","return function ("+bu(s,",")+"){ return binder.apply(this,arguments); }")(o),r.prototype){var u=function(){};u.prototype=r.prototype,a.prototype=new u,u.prototype=null}return a},Su=wu,Kn=Function.prototype.bind||Su,Eu=Function.prototype.call,Ru=Object.prototype.hasOwnProperty,Ou=Kn,Tu=Ou.call(Eu,Ru),H,Pu=au,Au=ou,Iu=iu,xu=su,mt=Xo,vt=tr,$u=uu,ei=Function,Jr=function(e){try{return ei('"use strict"; return ('+e+").constructor;")()}catch{}},tt=Object.getOwnPropertyDescriptor;if(tt)try{tt({},"")}catch{tt=null}var Yr=function(){throw new vt},Mu=tt?function(){try{return arguments.callee,Yr}catch{try{return tt(arguments,"callee").get}catch{return Yr}}}():Yr,ct=fu(),Cu=pu(),ie=Object.getPrototypeOf||(Cu?function(e){return e.__proto__}:null),pt={},ku=typeof Uint8Array>"u"||!ie?H:ie(Uint8Array),rt={__proto__:null,"%AggregateError%":typeof AggregateError>"u"?H:AggregateError,"%Array%":Array,"%ArrayBuffer%":typeof ArrayBuffer>"u"?H:ArrayBuffer,"%ArrayIteratorPrototype%":ct&&ie?ie([][Symbol.iterator]()):H,"%AsyncFromSyncIteratorPrototype%":H,"%AsyncFunction%":pt,"%AsyncGenerator%":pt,"%AsyncGeneratorFunction%":pt,"%AsyncIteratorPrototype%":pt,"%Atomics%":typeof Atomics>"u"?H:Atomics,"%BigInt%":typeof BigInt>"u"?H:BigInt,"%BigInt64Array%":typeof BigInt64Array>"u"?H:BigInt64Array,"%BigUint64Array%":typeof BigUint64Array>"u"?H:BigUint64Array,"%Boolean%":Boolean,"%DataView%":typeof DataView>"u"?H:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":Pu,"%eval%":eval,"%EvalError%":Au,"%Float32Array%":typeof Float32Array>"u"?H:Float32Array,"%Float64Array%":typeof Float64Array>"u"?H:Float64Array,"%FinalizationRegistry%":typeof FinalizationRegistry>"u"?H:FinalizationRegistry,"%Function%":ei,"%GeneratorFunction%":pt,"%Int8Array%":typeof Int8Array>"u"?H:Int8Array,"%Int16Array%":typeof Int16Array>"u"?H:Int16Array,"%Int32Array%":typeof Int32Array>"u"?H:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":ct&&ie?ie(ie([][Symbol.iterator]())):H,"%JSON%":typeof JSON=="object"?JSON:H,"%Map%":typeof Map>"u"?H:Map,"%MapIteratorPrototype%":typeof Map>"u"||!ct||!ie?H:ie(new Map()[Symbol.iterator]()),"%Math%":Math,"%Number%":Number,"%Object%":Object,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":typeof Promise>"u"?H:Promise,"%Proxy%":typeof Proxy>"u"?H:Proxy,"%RangeError%":Iu,"%ReferenceError%":xu,"%Reflect%":typeof Reflect>"u"?H:Reflect,"%RegExp%":RegExp,"%Set%":typeof Set>"u"?H:Set,"%SetIteratorPrototype%":typeof Set>"u"||!ct||!ie?H:ie(new Set()[Symbol.iterator]()),"%SharedArrayBuffer%":typeof SharedArrayBuffer>"u"?H:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":ct&&ie?ie(""[Symbol.iterator]()):H,"%Symbol%":ct?Symbol:H,"%SyntaxError%":mt,"%ThrowTypeError%":Mu,"%TypedArray%":ku,"%TypeError%":vt,"%Uint8Array%":typeof Uint8Array>"u"?H:Uint8Array,"%Uint8ClampedArray%":typeof Uint8ClampedArray>"u"?H:Uint8ClampedArray,"%Uint16Array%":typeof Uint16Array>"u"?H:Uint16Array,"%Uint32Array%":typeof Uint32Array>"u"?H:Uint32Array,"%URIError%":$u,"%WeakMap%":typeof WeakMap>"u"?H:WeakMap,"%WeakRef%":typeof WeakRef>"u"?H:WeakRef,"%WeakSet%":typeof WeakSet>"u"?H:WeakSet};if(ie)try{null.error}catch(e){var _u=ie(ie(e));rt["%Error.prototype%"]=_u}var Du=function e(t){var r;if(t==="%AsyncFunction%")r=Jr("async function () {}");else if(t==="%GeneratorFunction%")r=Jr("function* () {}");else if(t==="%AsyncGeneratorFunction%")r=Jr("async function* () {}");else if(t==="%AsyncGenerator%"){var n=e("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if(t==="%AsyncIteratorPrototype%"){var a=e("%AsyncGenerator%");a&&ie&&(r=ie(a.prototype))}return rt[t]=r,r},Aa={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},rr=Kn,Or=Tu,qu=rr.call(Function.call,Array.prototype.concat),Lu=rr.call(Function.apply,Array.prototype.splice),Ia=rr.call(Function.call,String.prototype.replace),Tr=rr.call(Function.call,String.prototype.slice),Fu=rr.call(Function.call,RegExp.prototype.exec),Nu=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,ju=/\\(\\)?/g,Uu=function(t){var r=Tr(t,0,1),n=Tr(t,-1);if(r==="%"&&n!=="%")throw new mt("invalid intrinsic syntax, expected closing `%`");if(n==="%"&&r!=="%")throw new mt("invalid intrinsic syntax, expected opening `%`");var a=[];return Ia(t,Nu,function(o,i,s,l){a[a.length]=s?Ia(l,ju,"$1"):i||o}),a},Bu=function(t,r){var n=t,a;if(Or(Aa,n)&&(a=Aa[n],n="%"+a[0]+"%"),Or(rt,n)){var o=rt[n];if(o===pt&&(o=Du(n)),typeof o>"u"&&!r)throw new vt("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:a,name:n,value:o}}throw new mt("intrinsic "+t+" does not exist!")},Tt=function(t,r){if(typeof t!="string"||t.length===0)throw new vt("intrinsic name must be a non-empty string");if(arguments.length>1&&typeof r!="boolean")throw new vt('"allowMissing" argument must be a boolean');if(Fu(/^%?[^%]*%?$/,t)===null)throw new mt("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var n=Uu(t),a=n.length>0?n[0]:"",o=Bu("%"+a+"%",r),i=o.name,s=o.value,l=!1,u=o.alias;u&&(a=u[0],Lu(n,qu([0,1],u)));for(var c=1,h=!0;c<n.length;c+=1){var f=n[c],p=Tr(f,0,1),d=Tr(f,-1);if((p==='"'||p==="'"||p==="`"||d==='"'||d==="'"||d==="`")&&p!==d)throw new mt("property names with quotes must have matching quotes");if((f==="constructor"||!h)&&(l=!0),a+="."+f,i="%"+a+"%",Or(rt,i))s=rt[i];else if(s!=null){if(!(f in s)){if(!r)throw new vt("base intrinsic for "+t+" exists, but the property is not available.");return}if(tt&&c+1>=n.length){var v=tt(s,f);h=!!v,h&&"get"in v&&!("originalValue"in v.get)?s=v.get:s=s[f]}else h=Or(s,f),s=s[f];h&&!l&&(rt[i]=s)}}return s},ti={exports:{}},Zr,xa;function Gn(){if(xa)return Zr;xa=1;var e=Tt,t=e("%Object.defineProperty%",!0)||!1;if(t)try{t({},"a",{value:1})}catch{t=!1}return Zr=t,Zr}var Qu=Tt,vr=Qu("%Object.getOwnPropertyDescriptor%",!0);if(vr)try{vr([],"length")}catch{vr=null}var ri=vr,$a=Gn(),zu=Xo,ft=tr,Ma=ri,Hu=function(t,r,n){if(!t||typeof t!="object"&&typeof t!="function")throw new ft("`obj` must be an object or a function`");if(typeof r!="string"&&typeof r!="symbol")throw new ft("`property` must be a string or a symbol`");if(arguments.length>3&&typeof arguments[3]!="boolean"&&arguments[3]!==null)throw new ft("`nonEnumerable`, if provided, must be a boolean or null");if(arguments.length>4&&typeof arguments[4]!="boolean"&&arguments[4]!==null)throw new ft("`nonWritable`, if provided, must be a boolean or null");if(arguments.length>5&&typeof arguments[5]!="boolean"&&arguments[5]!==null)throw new ft("`nonConfigurable`, if provided, must be a boolean or null");if(arguments.length>6&&typeof arguments[6]!="boolean")throw new ft("`loose`, if provided, must be a boolean");var a=arguments.length>3?arguments[3]:null,o=arguments.length>4?arguments[4]:null,i=arguments.length>5?arguments[5]:null,s=arguments.length>6?arguments[6]:!1,l=!!Ma&&Ma(t,r);if($a)$a(t,r,{configurable:i===null&&l?l.configurable:!i,enumerable:a===null&&l?l.enumerable:!a,value:n,writable:o===null&&l?l.writable:!o});else if(s||!a&&!o&&!i)t[r]=n;else throw new zu("This environment does not support defining a property as non-configurable, non-writable, or non-enumerable.")},Rn=Gn(),ni=function(){return!!Rn};ni.hasArrayLengthDefineBug=function(){if(!Rn)return null;try{return Rn([],"length",{value:1}).length!==1}catch{return!0}};var Wu=ni,Vu=Tt,Ca=Hu,Ku=Wu(),ka=ri,_a=tr,Gu=Vu("%Math.floor%"),Ju=function(t,r){if(typeof t!="function")throw new _a("`fn` is not a function");if(typeof r!="number"||r<0||r>4294967295||Gu(r)!==r)throw new _a("`length` must be a positive 32-bit integer");var n=arguments.length>2&&!!arguments[2],a=!0,o=!0;if("length"in t&&ka){var i=ka(t,"length");i&&!i.configurable&&(a=!1),i&&!i.writable&&(o=!1)}return(a||o||!n)&&(Ku?Ca(t,"length",r,!0,!0):Ca(t,"length",r)),t};(function(e){var t=Kn,r=Tt,n=Ju,a=tr,o=r("%Function.prototype.apply%"),i=r("%Function.prototype.call%"),s=r("%Reflect.apply%",!0)||t.call(i,o),l=Gn(),u=r("%Math.max%");e.exports=function(f){if(typeof f!="function")throw new a("a function is required");var p=s(t,i,arguments);return n(p,1+u(0,f.length-(arguments.length-1)),!0)};var c=function(){return s(t,o,arguments)};l?l(e.exports,"apply",{value:c}):e.exports.apply=c})(ti);var Yu=ti.exports,ai=Tt,oi=Yu,Zu=oi(ai("String.prototype.indexOf")),Xu=function(t,r){var n=ai(t,!!r);return typeof n=="function"&&Zu(t,".prototype.")>-1?oi(n):n};const el={},tl=Object.freeze(Object.defineProperty({__proto__:null,default:el},Symbol.toStringTag,{value:"Module"})),rl=nu(tl);var Jn=typeof Map=="function"&&Map.prototype,Xr=Object.getOwnPropertyDescriptor&&Jn?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,Pr=Jn&&Xr&&typeof Xr.get=="function"?Xr.get:null,Da=Jn&&Map.prototype.forEach,Yn=typeof Set=="function"&&Set.prototype,en=Object.getOwnPropertyDescriptor&&Yn?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,Ar=Yn&&en&&typeof en.get=="function"?en.get:null,qa=Yn&&Set.prototype.forEach,nl=typeof WeakMap=="function"&&WeakMap.prototype,Nt=nl?WeakMap.prototype.has:null,al=typeof WeakSet=="function"&&WeakSet.prototype,jt=al?WeakSet.prototype.has:null,ol=typeof WeakRef=="function"&&WeakRef.prototype,La=ol?WeakRef.prototype.deref:null,il=Boolean.prototype.valueOf,sl=Object.prototype.toString,ul=Function.prototype.toString,ll=String.prototype.match,Zn=String.prototype.slice,We=String.prototype.replace,cl=String.prototype.toUpperCase,Fa=String.prototype.toLowerCase,ii=RegExp.prototype.test,Na=Array.prototype.concat,Pe=Array.prototype.join,fl=Array.prototype.slice,ja=Math.floor,On=typeof BigInt=="function"?BigInt.prototype.valueOf:null,tn=Object.getOwnPropertySymbols,Tn=typeof Symbol=="function"&&typeof Symbol.iterator=="symbol"?Symbol.prototype.toString:null,gt=typeof Symbol=="function"&&typeof Symbol.iterator=="object",le=typeof Symbol=="function"&&Symbol.toStringTag&&(typeof Symbol.toStringTag===gt||!0)?Symbol.toStringTag:null,si=Object.prototype.propertyIsEnumerable,Ua=(typeof Reflect=="function"?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(e){return e.__proto__}:null);function Ba(e,t){if(e===1/0||e===-1/0||e!==e||e&&e>-1e3&&e<1e3||ii.call(/e/,t))return t;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if(typeof e=="number"){var n=e<0?-ja(-e):ja(e);if(n!==e){var a=String(n),o=Zn.call(t,a.length+1);return We.call(a,r,"$&_")+"."+We.call(We.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return We.call(t,r,"$&_")}var Pn=rl,Qa=Pn.custom,za=li(Qa)?Qa:null,dl=function e(t,r,n,a){var o=r||{};if(Qe(o,"quoteStyle")&&o.quoteStyle!=="single"&&o.quoteStyle!=="double")throw new TypeError('option "quoteStyle" must be "single" or "double"');if(Qe(o,"maxStringLength")&&(typeof o.maxStringLength=="number"?o.maxStringLength<0&&o.maxStringLength!==1/0:o.maxStringLength!==null))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var i=Qe(o,"customInspect")?o.customInspect:!0;if(typeof i!="boolean"&&i!=="symbol")throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(Qe(o,"indent")&&o.indent!==null&&o.indent!=="	"&&!(parseInt(o.indent,10)===o.indent&&o.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(Qe(o,"numericSeparator")&&typeof o.numericSeparator!="boolean")throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var s=o.numericSeparator;if(typeof t>"u")return"undefined";if(t===null)return"null";if(typeof t=="boolean")return t?"true":"false";if(typeof t=="string")return fi(t,o);if(typeof t=="number"){if(t===0)return 1/0/t>0?"0":"-0";var l=String(t);return s?Ba(t,l):l}if(typeof t=="bigint"){var u=String(t)+"n";return s?Ba(t,u):u}var c=typeof o.depth>"u"?5:o.depth;if(typeof n>"u"&&(n=0),n>=c&&c>0&&typeof t=="object")return An(t)?"[Array]":"[Object]";var h=$l(o,n);if(typeof a>"u")a=[];else if(ci(a,t)>=0)return"[Circular]";function f($,k,_){if(k&&(a=fl.call(a),a.push(k)),_){var q={depth:o.depth};return Qe(o,"quoteStyle")&&(q.quoteStyle=o.quoteStyle),e($,q,n+1,a)}return e($,o,n+1,a)}if(typeof t=="function"&&!Ha(t)){var p=Sl(t),d=sr(t,f);return"[Function"+(p?": "+p:" (anonymous)")+"]"+(d.length>0?" { "+Pe.call(d,", ")+" }":"")}if(li(t)){var v=gt?We.call(String(t),/^(Symbol\(.*\))_[^)]*$/,"$1"):Tn.call(t);return typeof t=="object"&&!gt?Mt(v):v}if(Al(t)){for(var m="<"+Fa.call(String(t.nodeName)),O=t.attributes||[],A=0;A<O.length;A++)m+=" "+O[A].name+"="+ui(pl(O[A].value),"double",o);return m+=">",t.childNodes&&t.childNodes.length&&(m+="..."),m+="</"+Fa.call(String(t.nodeName))+">",m}if(An(t)){if(t.length===0)return"[]";var R=sr(t,f);return h&&!xl(R)?"["+In(R,h)+"]":"[ "+Pe.call(R,", ")+" ]"}if(yl(t)){var w=sr(t,f);return!("cause"in Error.prototype)&&"cause"in t&&!si.call(t,"cause")?"{ ["+String(t)+"] "+Pe.call(Na.call("[cause]: "+f(t.cause),w),", ")+" }":w.length===0?"["+String(t)+"]":"{ ["+String(t)+"] "+Pe.call(w,", ")+" }"}if(typeof t=="object"&&i){if(za&&typeof t[za]=="function"&&Pn)return Pn(t,{depth:c-n});if(i!=="symbol"&&typeof t.inspect=="function")return t.inspect()}if(El(t)){var S=[];return Da&&Da.call(t,function($,k){S.push(f(k,t,!0)+" => "+f($,t))}),Wa("Map",Pr.call(t),S,h)}if(Tl(t)){var y=[];return qa&&qa.call(t,function($){y.push(f($,t))}),Wa("Set",Ar.call(t),y,h)}if(Rl(t))return rn("WeakMap");if(Pl(t))return rn("WeakSet");if(Ol(t))return rn("WeakRef");if(ml(t))return Mt(f(Number(t)));if(bl(t))return Mt(f(On.call(t)));if(gl(t))return Mt(il.call(t));if(vl(t))return Mt(f(String(t)));if(typeof window<"u"&&t===window)return"{ [object Window] }";if(typeof globalThis<"u"&&t===globalThis||typeof Ra<"u"&&t===Ra)return"{ [object globalThis] }";if(!hl(t)&&!Ha(t)){var b=sr(t,f),g=Ua?Ua(t)===Object.prototype:t instanceof Object||t.constructor===Object,T=t instanceof Object?"":"null prototype",E=!g&&le&&Object(t)===t&&le in t?Zn.call(Ge(t),8,-1):T?"Object":"",I=g||typeof t.constructor!="function"?"":t.constructor.name?t.constructor.name+" ":"",x=I+(E||T?"["+Pe.call(Na.call([],E||[],T||[]),": ")+"] ":"");return b.length===0?x+"{}":h?x+"{"+In(b,h)+"}":x+"{ "+Pe.call(b,", ")+" }"}return String(t)};function ui(e,t,r){var n=(r.quoteStyle||t)==="double"?'"':"'";return n+e+n}function pl(e){return We.call(String(e),/"/g,"&quot;")}function An(e){return Ge(e)==="[object Array]"&&(!le||!(typeof e=="object"&&le in e))}function hl(e){return Ge(e)==="[object Date]"&&(!le||!(typeof e=="object"&&le in e))}function Ha(e){return Ge(e)==="[object RegExp]"&&(!le||!(typeof e=="object"&&le in e))}function yl(e){return Ge(e)==="[object Error]"&&(!le||!(typeof e=="object"&&le in e))}function vl(e){return Ge(e)==="[object String]"&&(!le||!(typeof e=="object"&&le in e))}function ml(e){return Ge(e)==="[object Number]"&&(!le||!(typeof e=="object"&&le in e))}function gl(e){return Ge(e)==="[object Boolean]"&&(!le||!(typeof e=="object"&&le in e))}function li(e){if(gt)return e&&typeof e=="object"&&e instanceof Symbol;if(typeof e=="symbol")return!0;if(!e||typeof e!="object"||!Tn)return!1;try{return Tn.call(e),!0}catch{}return!1}function bl(e){if(!e||typeof e!="object"||!On)return!1;try{return On.call(e),!0}catch{}return!1}var wl=Object.prototype.hasOwnProperty||function(e){return e in this};function Qe(e,t){return wl.call(e,t)}function Ge(e){return sl.call(e)}function Sl(e){if(e.name)return e.name;var t=ll.call(ul.call(e),/^function\s*([\w$]+)/);return t?t[1]:null}function ci(e,t){if(e.indexOf)return e.indexOf(t);for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function El(e){if(!Pr||!e||typeof e!="object")return!1;try{Pr.call(e);try{Ar.call(e)}catch{return!0}return e instanceof Map}catch{}return!1}function Rl(e){if(!Nt||!e||typeof e!="object")return!1;try{Nt.call(e,Nt);try{jt.call(e,jt)}catch{return!0}return e instanceof WeakMap}catch{}return!1}function Ol(e){if(!La||!e||typeof e!="object")return!1;try{return La.call(e),!0}catch{}return!1}function Tl(e){if(!Ar||!e||typeof e!="object")return!1;try{Ar.call(e);try{Pr.call(e)}catch{return!0}return e instanceof Set}catch{}return!1}function Pl(e){if(!jt||!e||typeof e!="object")return!1;try{jt.call(e,jt);try{Nt.call(e,Nt)}catch{return!0}return e instanceof WeakSet}catch{}return!1}function Al(e){return!e||typeof e!="object"?!1:typeof HTMLElement<"u"&&e instanceof HTMLElement?!0:typeof e.nodeName=="string"&&typeof e.getAttribute=="function"}function fi(e,t){if(e.length>t.maxStringLength){var r=e.length-t.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return fi(Zn.call(e,0,t.maxStringLength),t)+n}var a=We.call(We.call(e,/(['\\])/g,"\\$1"),/[\x00-\x1f]/g,Il);return ui(a,"single",t)}function Il(e){var t=e.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[t];return r?"\\"+r:"\\x"+(t<16?"0":"")+cl.call(t.toString(16))}function Mt(e){return"Object("+e+")"}function rn(e){return e+" { ? }"}function Wa(e,t,r,n){var a=n?In(r,n):Pe.call(r,", ");return e+" ("+t+") {"+a+"}"}function xl(e){for(var t=0;t<e.length;t++)if(ci(e[t],`
`)>=0)return!1;return!0}function $l(e,t){var r;if(e.indent==="	")r="	";else if(typeof e.indent=="number"&&e.indent>0)r=Pe.call(Array(e.indent+1)," ");else return null;return{base:r,prev:Pe.call(Array(t+1),r)}}function In(e,t){if(e.length===0)return"";var r=`
`+t.prev+t.base;return r+Pe.call(e,","+r)+`
`+t.prev}function sr(e,t){var r=An(e),n=[];if(r){n.length=e.length;for(var a=0;a<e.length;a++)n[a]=Qe(e,a)?t(e[a],e):""}var o=typeof tn=="function"?tn(e):[],i;if(gt){i={};for(var s=0;s<o.length;s++)i["$"+o[s]]=o[s]}for(var l in e)Qe(e,l)&&(r&&String(Number(l))===l&&l<e.length||gt&&i["$"+l]instanceof Symbol||(ii.call(/[^\w$]/,l)?n.push(t(l,e)+": "+t(e[l],e)):n.push(l+": "+t(e[l],e))));if(typeof tn=="function")for(var u=0;u<o.length;u++)si.call(e,o[u])&&n.push("["+t(o[u])+"]: "+t(e[o[u]],e));return n}var di=Tt,Pt=Xu,Ml=dl,Cl=tr,ur=di("%WeakMap%",!0),lr=di("%Map%",!0),kl=Pt("WeakMap.prototype.get",!0),_l=Pt("WeakMap.prototype.set",!0),Dl=Pt("WeakMap.prototype.has",!0),ql=Pt("Map.prototype.get",!0),Ll=Pt("Map.prototype.set",!0),Fl=Pt("Map.prototype.has",!0),Xn=function(e,t){for(var r=e,n;(n=r.next)!==null;r=n)if(n.key===t)return r.next=n.next,n.next=e.next,e.next=n,n},Nl=function(e,t){var r=Xn(e,t);return r&&r.value},jl=function(e,t,r){var n=Xn(e,t);n?n.value=r:e.next={key:t,next:e.next,value:r}},Ul=function(e,t){return!!Xn(e,t)},Bl=function(){var t,r,n,a={assert:function(o){if(!a.has(o))throw new Cl("Side channel does not contain "+Ml(o))},get:function(o){if(ur&&o&&(typeof o=="object"||typeof o=="function")){if(t)return kl(t,o)}else if(lr){if(r)return ql(r,o)}else if(n)return Nl(n,o)},has:function(o){if(ur&&o&&(typeof o=="object"||typeof o=="function")){if(t)return Dl(t,o)}else if(lr){if(r)return Fl(r,o)}else if(n)return Ul(n,o);return!1},set:function(o,i){ur&&o&&(typeof o=="object"||typeof o=="function")?(t||(t=new ur),_l(t,o,i)):lr?(r||(r=new lr),Ll(r,o,i)):(n||(n={key:{},next:null}),jl(n,o,i))}};return a},Ql=String.prototype.replace,zl=/%20/g,nn={RFC1738:"RFC1738",RFC3986:"RFC3986"},ea={default:nn.RFC3986,formatters:{RFC1738:function(e){return Ql.call(e,zl,"+")},RFC3986:function(e){return String(e)}},RFC1738:nn.RFC1738,RFC3986:nn.RFC3986},Hl=ea,an=Object.prototype.hasOwnProperty,Ze=Array.isArray,Te=function(){for(var e=[],t=0;t<256;++t)e.push("%"+((t<16?"0":"")+t.toString(16)).toUpperCase());return e}(),Wl=function(t){for(;t.length>1;){var r=t.pop(),n=r.obj[r.prop];if(Ze(n)){for(var a=[],o=0;o<n.length;++o)typeof n[o]<"u"&&a.push(n[o]);r.obj[r.prop]=a}}},pi=function(t,r){for(var n=r&&r.plainObjects?Object.create(null):{},a=0;a<t.length;++a)typeof t[a]<"u"&&(n[a]=t[a]);return n},Vl=function e(t,r,n){if(!r)return t;if(typeof r!="object"){if(Ze(t))t.push(r);else if(t&&typeof t=="object")(n&&(n.plainObjects||n.allowPrototypes)||!an.call(Object.prototype,r))&&(t[r]=!0);else return[t,r];return t}if(!t||typeof t!="object")return[t].concat(r);var a=t;return Ze(t)&&!Ze(r)&&(a=pi(t,n)),Ze(t)&&Ze(r)?(r.forEach(function(o,i){if(an.call(t,i)){var s=t[i];s&&typeof s=="object"&&o&&typeof o=="object"?t[i]=e(s,o,n):t.push(o)}else t[i]=o}),t):Object.keys(r).reduce(function(o,i){var s=r[i];return an.call(o,i)?o[i]=e(o[i],s,n):o[i]=s,o},a)},Kl=function(t,r){return Object.keys(r).reduce(function(n,a){return n[a]=r[a],n},t)},Gl=function(e,t,r){var n=e.replace(/\+/g," ");if(r==="iso-8859-1")return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch{return n}},Jl=function(t,r,n,a,o){if(t.length===0)return t;var i=t;if(typeof t=="symbol"?i=Symbol.prototype.toString.call(t):typeof t!="string"&&(i=String(t)),n==="iso-8859-1")return escape(i).replace(/%u[0-9a-f]{4}/gi,function(c){return"%26%23"+parseInt(c.slice(2),16)+"%3B"});for(var s="",l=0;l<i.length;++l){var u=i.charCodeAt(l);if(u===45||u===46||u===95||u===126||u>=48&&u<=57||u>=65&&u<=90||u>=97&&u<=122||o===Hl.RFC1738&&(u===40||u===41)){s+=i.charAt(l);continue}if(u<128){s=s+Te[u];continue}if(u<2048){s=s+(Te[192|u>>6]+Te[128|u&63]);continue}if(u<55296||u>=57344){s=s+(Te[224|u>>12]+Te[128|u>>6&63]+Te[128|u&63]);continue}l+=1,u=65536+((u&1023)<<10|i.charCodeAt(l)&1023),s+=Te[240|u>>18]+Te[128|u>>12&63]+Te[128|u>>6&63]+Te[128|u&63]}return s},Yl=function(t){for(var r=[{obj:{o:t},prop:"o"}],n=[],a=0;a<r.length;++a)for(var o=r[a],i=o.obj[o.prop],s=Object.keys(i),l=0;l<s.length;++l){var u=s[l],c=i[u];typeof c=="object"&&c!==null&&n.indexOf(c)===-1&&(r.push({obj:i,prop:u}),n.push(c))}return Wl(r),t},Zl=function(t){return Object.prototype.toString.call(t)==="[object RegExp]"},Xl=function(t){return!t||typeof t!="object"?!1:!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},ec=function(t,r){return[].concat(t,r)},tc=function(t,r){if(Ze(t)){for(var n=[],a=0;a<t.length;a+=1)n.push(r(t[a]));return n}return r(t)},hi={arrayToObject:pi,assign:Kl,combine:ec,compact:Yl,decode:Gl,encode:Jl,isBuffer:Xl,isRegExp:Zl,maybeMap:tc,merge:Vl},yi=Bl,mr=hi,Ut=ea,rc=Object.prototype.hasOwnProperty,Va={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,r){return t+"["+r+"]"},repeat:function(t){return t}},_e=Array.isArray,nc=Array.prototype.push,vi=function(e,t){nc.apply(e,_e(t)?t:[t])},ac=Date.prototype.toISOString,Ka=Ut.default,ue={addQueryPrefix:!1,allowDots:!1,charset:"utf-8",charsetSentinel:!1,delimiter:"&",encode:!0,encoder:mr.encode,encodeValuesOnly:!1,format:Ka,formatter:Ut.formatters[Ka],indices:!1,serializeDate:function(t){return ac.call(t)},skipNulls:!1,strictNullHandling:!1},oc=function(t){return typeof t=="string"||typeof t=="number"||typeof t=="boolean"||typeof t=="symbol"||typeof t=="bigint"},on={},ic=function e(t,r,n,a,o,i,s,l,u,c,h,f,p,d,v,m){for(var O=t,A=m,R=0,w=!1;(A=A.get(on))!==void 0&&!w;){var S=A.get(t);if(R+=1,typeof S<"u"){if(S===R)throw new RangeError("Cyclic object value");w=!0}typeof A.get(on)>"u"&&(R=0)}if(typeof l=="function"?O=l(r,O):O instanceof Date?O=h(O):n==="comma"&&_e(O)&&(O=mr.maybeMap(O,function(q){return q instanceof Date?h(q):q})),O===null){if(o)return s&&!d?s(r,ue.encoder,v,"key",f):r;O=""}if(oc(O)||mr.isBuffer(O)){if(s){var y=d?r:s(r,ue.encoder,v,"key",f);return[p(y)+"="+p(s(O,ue.encoder,v,"value",f))]}return[p(r)+"="+p(String(O))]}var b=[];if(typeof O>"u")return b;var g;if(n==="comma"&&_e(O))d&&s&&(O=mr.maybeMap(O,s)),g=[{value:O.length>0?O.join(",")||null:void 0}];else if(_e(l))g=l;else{var T=Object.keys(O);g=u?T.sort(u):T}for(var E=a&&_e(O)&&O.length===1?r+"[]":r,I=0;I<g.length;++I){var x=g[I],$=typeof x=="object"&&typeof x.value<"u"?x.value:O[x];if(!(i&&$===null)){var k=_e(O)?typeof n=="function"?n(E,x):E:E+(c?"."+x:"["+x+"]");m.set(t,R);var _=yi();_.set(on,m),vi(b,e($,k,n,a,o,i,n==="comma"&&d&&_e(O)?null:s,l,u,c,h,f,p,d,v,_))}}return b},sc=function(t){if(!t)return ue;if(t.encoder!==null&&typeof t.encoder<"u"&&typeof t.encoder!="function")throw new TypeError("Encoder has to be a function.");var r=t.charset||ue.charset;if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var n=Ut.default;if(typeof t.format<"u"){if(!rc.call(Ut.formatters,t.format))throw new TypeError("Unknown format option provided.");n=t.format}var a=Ut.formatters[n],o=ue.filter;return(typeof t.filter=="function"||_e(t.filter))&&(o=t.filter),{addQueryPrefix:typeof t.addQueryPrefix=="boolean"?t.addQueryPrefix:ue.addQueryPrefix,allowDots:typeof t.allowDots>"u"?ue.allowDots:!!t.allowDots,charset:r,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:ue.charsetSentinel,delimiter:typeof t.delimiter>"u"?ue.delimiter:t.delimiter,encode:typeof t.encode=="boolean"?t.encode:ue.encode,encoder:typeof t.encoder=="function"?t.encoder:ue.encoder,encodeValuesOnly:typeof t.encodeValuesOnly=="boolean"?t.encodeValuesOnly:ue.encodeValuesOnly,filter:o,format:n,formatter:a,serializeDate:typeof t.serializeDate=="function"?t.serializeDate:ue.serializeDate,skipNulls:typeof t.skipNulls=="boolean"?t.skipNulls:ue.skipNulls,sort:typeof t.sort=="function"?t.sort:null,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:ue.strictNullHandling}},uc=function(e,t){var r=e,n=sc(t),a,o;typeof n.filter=="function"?(o=n.filter,r=o("",r)):_e(n.filter)&&(o=n.filter,a=o);var i=[];if(typeof r!="object"||r===null)return"";var s;t&&t.arrayFormat in Va?s=t.arrayFormat:t&&"indices"in t?s=t.indices?"indices":"repeat":s="indices";var l=Va[s];if(t&&"commaRoundTrip"in t&&typeof t.commaRoundTrip!="boolean")throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var u=l==="comma"&&t&&t.commaRoundTrip;a||(a=Object.keys(r)),n.sort&&a.sort(n.sort);for(var c=yi(),h=0;h<a.length;++h){var f=a[h];n.skipNulls&&r[f]===null||vi(i,ic(r[f],f,l,u,n.strictNullHandling,n.skipNulls,n.encode?n.encoder:null,n.filter,n.sort,n.allowDots,n.serializeDate,n.format,n.formatter,n.encodeValuesOnly,n.charset,c))}var p=i.join(n.delimiter),d=n.addQueryPrefix===!0?"?":"";return n.charsetSentinel&&(n.charset==="iso-8859-1"?d+="utf8=%26%2310003%3B&":d+="utf8=%E2%9C%93&"),p.length>0?d+p:""},bt=hi,xn=Object.prototype.hasOwnProperty,lc=Array.isArray,oe={allowDots:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decoder:bt.decode,delimiter:"&",depth:5,ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictNullHandling:!1},cc=function(e){return e.replace(/&#(\d+);/g,function(t,r){return String.fromCharCode(parseInt(r,10))})},mi=function(e,t){return e&&typeof e=="string"&&t.comma&&e.indexOf(",")>-1?e.split(","):e},fc="utf8=%26%2310003%3B",dc="utf8=%E2%9C%93",pc=function(t,r){var n={},a=r.ignoreQueryPrefix?t.replace(/^\?/,""):t,o=r.parameterLimit===1/0?void 0:r.parameterLimit,i=a.split(r.delimiter,o),s=-1,l,u=r.charset;if(r.charsetSentinel)for(l=0;l<i.length;++l)i[l].indexOf("utf8=")===0&&(i[l]===dc?u="utf-8":i[l]===fc&&(u="iso-8859-1"),s=l,l=i.length);for(l=0;l<i.length;++l)if(l!==s){var c=i[l],h=c.indexOf("]="),f=h===-1?c.indexOf("="):h+1,p,d;f===-1?(p=r.decoder(c,oe.decoder,u,"key"),d=r.strictNullHandling?null:""):(p=r.decoder(c.slice(0,f),oe.decoder,u,"key"),d=bt.maybeMap(mi(c.slice(f+1),r),function(v){return r.decoder(v,oe.decoder,u,"value")})),d&&r.interpretNumericEntities&&u==="iso-8859-1"&&(d=cc(d)),c.indexOf("[]=")>-1&&(d=lc(d)?[d]:d),xn.call(n,p)?n[p]=bt.combine(n[p],d):n[p]=d}return n},hc=function(e,t,r,n){for(var a=n?t:mi(t,r),o=e.length-1;o>=0;--o){var i,s=e[o];if(s==="[]"&&r.parseArrays)i=[].concat(a);else{i=r.plainObjects?Object.create(null):{};var l=s.charAt(0)==="["&&s.charAt(s.length-1)==="]"?s.slice(1,-1):s,u=parseInt(l,10);!r.parseArrays&&l===""?i={0:a}:!isNaN(u)&&s!==l&&String(u)===l&&u>=0&&r.parseArrays&&u<=r.arrayLimit?(i=[],i[u]=a):l!=="__proto__"&&(i[l]=a)}a=i}return a},yc=function(t,r,n,a){if(t){var o=n.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,i=/(\[[^[\]]*])/,s=/(\[[^[\]]*])/g,l=n.depth>0&&i.exec(o),u=l?o.slice(0,l.index):o,c=[];if(u){if(!n.plainObjects&&xn.call(Object.prototype,u)&&!n.allowPrototypes)return;c.push(u)}for(var h=0;n.depth>0&&(l=s.exec(o))!==null&&h<n.depth;){if(h+=1,!n.plainObjects&&xn.call(Object.prototype,l[1].slice(1,-1))&&!n.allowPrototypes)return;c.push(l[1])}return l&&c.push("["+o.slice(l.index)+"]"),hc(c,r,n,a)}},vc=function(t){if(!t)return oe;if(t.decoder!==null&&t.decoder!==void 0&&typeof t.decoder!="function")throw new TypeError("Decoder has to be a function.");if(typeof t.charset<"u"&&t.charset!=="utf-8"&&t.charset!=="iso-8859-1")throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=typeof t.charset>"u"?oe.charset:t.charset;return{allowDots:typeof t.allowDots>"u"?oe.allowDots:!!t.allowDots,allowPrototypes:typeof t.allowPrototypes=="boolean"?t.allowPrototypes:oe.allowPrototypes,allowSparse:typeof t.allowSparse=="boolean"?t.allowSparse:oe.allowSparse,arrayLimit:typeof t.arrayLimit=="number"?t.arrayLimit:oe.arrayLimit,charset:r,charsetSentinel:typeof t.charsetSentinel=="boolean"?t.charsetSentinel:oe.charsetSentinel,comma:typeof t.comma=="boolean"?t.comma:oe.comma,decoder:typeof t.decoder=="function"?t.decoder:oe.decoder,delimiter:typeof t.delimiter=="string"||bt.isRegExp(t.delimiter)?t.delimiter:oe.delimiter,depth:typeof t.depth=="number"||t.depth===!1?+t.depth:oe.depth,ignoreQueryPrefix:t.ignoreQueryPrefix===!0,interpretNumericEntities:typeof t.interpretNumericEntities=="boolean"?t.interpretNumericEntities:oe.interpretNumericEntities,parameterLimit:typeof t.parameterLimit=="number"?t.parameterLimit:oe.parameterLimit,parseArrays:t.parseArrays!==!1,plainObjects:typeof t.plainObjects=="boolean"?t.plainObjects:oe.plainObjects,strictNullHandling:typeof t.strictNullHandling=="boolean"?t.strictNullHandling:oe.strictNullHandling}},mc=function(e,t){var r=vc(t);if(e===""||e===null||typeof e>"u")return r.plainObjects?Object.create(null):{};for(var n=typeof e=="string"?pc(e,r):e,a=r.plainObjects?Object.create(null):{},o=Object.keys(n),i=0;i<o.length;++i){var s=o[i],l=yc(s,n[s],r,typeof e=="string");a=bt.merge(a,l,r)}return r.allowSparse===!0?a:bt.compact(a)},gc=uc,bc=mc,wc=ea,$n={formats:wc,parse:bc,stringify:gc};const Sc=ru($n);var sn=function(){return Math.random().toString(36).substring(7).split("").join(".")},Bt={INIT:"@@redux/INIT"+sn(),REPLACE:"@@redux/REPLACE"+sn(),PROBE_UNKNOWN_ACTION:function(){return"@@redux/PROBE_UNKNOWN_ACTION"+sn()}};function Ec(e){if(typeof e!="object"||e===null)return!1;for(var t=e;Object.getPrototypeOf(t)!==null;)t=Object.getPrototypeOf(t);return Object.getPrototypeOf(e)===t}function Rc(e){if(e===void 0)return"undefined";if(e===null)return"null";var t=typeof e;switch(t){case"boolean":case"string":case"number":case"symbol":case"function":return t}if(Array.isArray(e))return"array";if(Pc(e))return"date";if(Tc(e))return"error";var r=Oc(e);switch(r){case"Symbol":case"Promise":case"WeakMap":case"WeakSet":case"Map":case"Set":return r}return t.slice(8,-1).toLowerCase().replace(/\s/g,"")}function Oc(e){return typeof e.constructor=="function"?e.constructor.name:null}function Tc(e){return e instanceof Error||typeof e.message=="string"&&e.constructor&&typeof e.constructor.stackTraceLimit=="number"}function Pc(e){return e instanceof Date?!0:typeof e.toDateString=="function"&&typeof e.getDate=="function"&&typeof e.setDate=="function"}function Ac(e){var t=typeof e;return t=Rc(e),t}function Ga(e){typeof console<"u"&&typeof console.error=="function"&&console.error(e);try{throw new Error(e)}catch{}}function Ic(e,t,r,n){var a=Object.keys(t),o=r&&r.type===Bt.INIT?"preloadedState argument passed to createStore":"previous state received by the reducer";if(a.length===0)return"Store does not have a valid reducer. Make sure the argument passed to combineReducers is an object whose values are reducers.";if(!Ec(e))return"The "+o+' has unexpected type of "'+Ac(e)+'". Expected argument to be an object with the following '+('keys: "'+a.join('", "')+'"');var i=Object.keys(e).filter(function(s){return!t.hasOwnProperty(s)&&!n[s]});if(i.forEach(function(s){n[s]=!0}),!(r&&r.type===Bt.REPLACE)&&i.length>0)return"Unexpected "+(i.length>1?"keys":"key")+" "+('"'+i.join('", "')+'" found in '+o+". ")+"Expected to find one of the known reducer keys instead: "+('"'+a.join('", "')+'". Unexpected keys will be ignored.')}function xc(e){Object.keys(e).forEach(function(t){var r=e[t],n=r(void 0,{type:Bt.INIT});if(typeof n>"u")throw new Error('The slice reducer for key "'+t+`" returned undefined during initialization. If the state passed to the reducer is undefined, you must explicitly return the initial state. The initial state may not be undefined. If you don't want to set a value for this reducer, you can use null instead of undefined.`);if(typeof r(void 0,{type:Bt.PROBE_UNKNOWN_ACTION()})>"u")throw new Error('The slice reducer for key "'+t+'" returned undefined when probed with a random type. '+("Don't try to handle '"+Bt.INIT+`' or other actions in "redux/*" `)+"namespace. They are considered private. Instead, you must return the current state for any unknown actions, unless it is undefined, in which case you must return the initial state, regardless of the action type. The initial state may not be undefined, but can be null.")})}function $c(e){for(var t=Object.keys(e),r={},n=0;n<t.length;n++){var a=t[n];typeof e[a]>"u"&&Ga('No reducer provided for key "'+a+'"'),typeof e[a]=="function"&&(r[a]=e[a])}var o=Object.keys(r),i;i={};var s;try{xc(r)}catch(l){s=l}return function(u,c){if(u===void 0&&(u={}),s)throw s;{var h=Ic(u,r,c,i);h&&Ga(h)}for(var f=!1,p={},d=0;d<o.length;d++){var v=o[d],m=r[v],O=u[v],A=m(O,c);if(typeof A>"u"){var R=c&&c.type;throw new Error("When called with an action of type "+(R?'"'+String(R)+'"':"(unknown type)")+', the slice reducer for key "'+v+'" returned undefined. To ignore an action, you must explicitly return the previous state. If you want this reducer to hold no value, you can return null instead of undefined.')}p[v]=A,f=f||A!==O}return f=f||o.length!==Object.keys(u).length,f?p:u}}var Ir="NOT_FOUND";function Mc(e){var t;return{get:function(n){return t&&e(t.key,n)?t.value:Ir},put:function(n,a){t={key:n,value:a}},getEntries:function(){return t?[t]:[]},clear:function(){t=void 0}}}function Cc(e,t){var r=[];function n(s){var l=r.findIndex(function(c){return t(s,c.key)});if(l>-1){var u=r[l];return l>0&&(r.splice(l,1),r.unshift(u)),u.value}return Ir}function a(s,l){n(s)===Ir&&(r.unshift({key:s,value:l}),r.length>e&&r.pop())}function o(){return r}function i(){r=[]}return{get:n,put:a,getEntries:o,clear:i}}var kc=function(t,r){return t===r};function _c(e){return function(r,n){if(r===null||n===null||r.length!==n.length)return!1;for(var a=r.length,o=0;o<a;o++)if(!e(r[o],n[o]))return!1;return!0}}function Mn(e,t){var r=typeof t=="object"?t:{equalityCheck:t},n=r.equalityCheck,a=n===void 0?kc:n,o=r.maxSize,i=o===void 0?1:o,s=r.resultEqualityCheck,l=_c(a),u=i===1?Mc(l):Cc(i,l);function c(){var h=u.get(arguments);if(h===Ir){if(h=e.apply(null,arguments),s){var f=u.getEntries(),p=f.find(function(d){return s(d.value,h)});p&&(h=p.value)}u.put(arguments,h)}return h}return c.clearCache=function(){return u.clear()},c}function Dc(e){var t=Array.isArray(e[0])?e[0]:e;if(!t.every(function(n){return typeof n=="function"})){var r=t.map(function(n){return typeof n=="function"?"function "+(n.name||"unnamed")+"()":typeof n}).join(", ");throw new Error("createSelector expects all input-selectors to be functions, but received the following types: ["+r+"]")}return t}function qc(e){for(var t=arguments.length,r=new Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];var a=function(){for(var i=arguments.length,s=new Array(i),l=0;l<i;l++)s[l]=arguments[l];var u=0,c,h={memoizeOptions:void 0},f=s.pop();if(typeof f=="object"&&(h=f,f=s.pop()),typeof f!="function")throw new Error("createSelector expects an output function after the inputs, but received: ["+typeof f+"]");var p=h,d=p.memoizeOptions,v=d===void 0?r:d,m=Array.isArray(v)?v:[v],O=Dc(s),A=e.apply(void 0,[function(){return u++,f.apply(null,arguments)}].concat(m)),R=e(function(){for(var S=[],y=O.length,b=0;b<y;b++)S.push(O[b].apply(null,arguments));return c=A.apply(null,S),c});return Object.assign(R,{resultFunc:f,memoizedResultFunc:A,dependencies:O,lastResult:function(){return c},recomputations:function(){return u},resetRecomputations:function(){return u=0}}),R};return a}var Qt=qc(Mn),gi=function(){var e=function(t,r){return e=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(n,a){n.__proto__=a}||function(n,a){for(var o in a)Object.prototype.hasOwnProperty.call(a,o)&&(n[o]=a[o])},e(t,r)};return function(t,r){if(typeof r!="function"&&r!==null)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");e(t,r);function n(){this.constructor=t}t.prototype=r===null?Object.create(r):(n.prototype=r.prototype,new n)}}(),Lc=function(e,t){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},n,a,o,i;return i={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function s(u){return function(c){return l([u,c])}}function l(u){if(n)throw new TypeError("Generator is already executing.");for(;r;)try{if(n=1,a&&(o=u[0]&2?a.return:u[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,u[1])).done)return o;switch(a=0,o&&(u=[u[0]&2,o.value]),u[0]){case 0:case 1:o=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,a=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!o||u[1]>o[0]&&u[1]<o[3])){r.label=u[1];break}if(u[0]===6&&r.label<o[1]){r.label=o[1],o=u;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(u);break}o[2]&&r.ops.pop(),r.trys.pop();continue}u=t.call(e,r)}catch(c){u=[6,c],a=0}finally{n=o=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}},Wt=function(e,t){for(var r=0,n=t.length,a=e.length;r<n;r++,a++)e[a]=t[r];return e},Fc=Object.defineProperty,Nc=Object.defineProperties,jc=Object.getOwnPropertyDescriptors,Ja=Object.getOwnPropertySymbols,Uc=Object.prototype.hasOwnProperty,Bc=Object.prototype.propertyIsEnumerable,Ya=function(e,t,r){return t in e?Fc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r},nt=function(e,t){for(var r in t||(t={}))Uc.call(t,r)&&Ya(e,r,t[r]);if(Ja)for(var n=0,a=Ja(t);n<a.length;n++){var r=a[n];Bc.call(t,r)&&Ya(e,r,t[r])}return e},un=function(e,t){return Nc(e,jc(t))},Qc=function(e,t,r){return new Promise(function(n,a){var o=function(l){try{s(r.next(l))}catch(u){a(u)}},i=function(l){try{s(r.throw(l))}catch(u){a(u)}},s=function(l){return l.done?n(l.value):Promise.resolve(l.value).then(o,i)};s((r=r.apply(e,t)).next())})};function Vt(e){if(typeof e!="object"||e===null)return!1;var t=Object.getPrototypeOf(e);if(t===null)return!0;for(var r=t;Object.getPrototypeOf(r)!==null;)r=Object.getPrototypeOf(r);return t===r}var zc=function(e){return e&&typeof e.match=="function"};function pe(e,t){function r(){for(var n=[],a=0;a<arguments.length;a++)n[a]=arguments[a];if(t){var o=t.apply(void 0,n);if(!o)throw new Error("prepareAction did not return an object");return nt(nt({type:e,payload:o.payload},"meta"in o&&{meta:o.meta}),"error"in o&&{error:o.error})}return{type:e,payload:n[0]}}return r.toString=function(){return""+e},r.type=e,r.match=function(n){return n.type===e},r}(function(e){gi(t,e);function t(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var a=e.apply(this,r)||this;return Object.setPrototypeOf(a,t.prototype),a}return Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return e.prototype.concat.apply(this,r)},t.prototype.prepend=function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return r.length===1&&Array.isArray(r[0])?new(t.bind.apply(t,Wt([void 0],r[0].concat(this)))):new(t.bind.apply(t,Wt([void 0],r.concat(this))))},t})(Array);(function(e){gi(t,e);function t(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];var a=e.apply(this,r)||this;return Object.setPrototypeOf(a,t.prototype),a}return Object.defineProperty(t,Symbol.species,{get:function(){return t},enumerable:!1,configurable:!0}),t.prototype.concat=function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return e.prototype.concat.apply(this,r)},t.prototype.prepend=function(){for(var r=[],n=0;n<arguments.length;n++)r[n]=arguments[n];return r.length===1&&Array.isArray(r[0])?new(t.bind.apply(t,Wt([void 0],r[0].concat(this)))):new(t.bind.apply(t,Wt([void 0],r.concat(this))))},t})(Array);function Cn(e){return Vn(e)?Ot(e,function(){}):e}function bi(e){var t={},r=[],n,a={addCase:function(o,i){{if(r.length>0)throw new Error("`builder.addCase` should only be called before calling `builder.addMatcher`");if(n)throw new Error("`builder.addCase` should only be called before calling `builder.addDefaultCase`")}var s=typeof o=="string"?o:o.type;if(!s)throw new Error("`builder.addCase` cannot be called with an empty action type");if(s in t)throw new Error("`builder.addCase` cannot be called with two reducers for the same action type");return t[s]=i,a},addMatcher:function(o,i){if(n)throw new Error("`builder.addMatcher` should only be called before calling `builder.addDefaultCase`");return r.push({matcher:o,reducer:i}),a},addDefaultCase:function(o){if(n)throw new Error("`builder.addDefaultCase` can only be called once");return n=o,a}};return e(a),[t,r,n]}function Hc(e){return typeof e=="function"}var Za=!1;function Wc(e,t,r,n){r===void 0&&(r=[]),typeof t=="object"&&(Za||(Za=!0,console.warn("The object notation for `createReducer` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createReducer")));var a=typeof t=="function"?bi(t):[t,r,n],o=a[0],i=a[1],s=a[2],l;if(Hc(e))l=function(){return Cn(e())};else{var u=Cn(e);l=function(){return u}}function c(h,f){h===void 0&&(h=l());var p=Wt([o[f.type]],i.filter(function(d){var v=d.matcher;return v(f)}).map(function(d){var v=d.reducer;return v}));return p.filter(function(d){return!!d}).length===0&&(p=[s]),p.reduce(function(d,v){if(v)if(Yo(d)){var m=d,O=v(m,f);return O===void 0?d:O}else{if(Vn(d))return Ot(d,function(A){return v(A,f)});var O=v(d,f);if(O===void 0){if(d===null)return d;throw Error("A case reducer on a non-draftable value must not return undefined")}return O}return d},h)}return c.getInitialState=l,c}var Xa=!1;function Vc(e,t){return e+"/"+t}function Ye(e){var t=e.name;if(!t)throw new Error("`name` is a required option for createSlice");typeof process<"u"&&e.initialState===void 0&&console.error("You must provide an `initialState` value that is not `undefined`. You may have misspelled `initialState`");var r=typeof e.initialState=="function"?e.initialState:Cn(e.initialState),n=e.reducers||{},a=Object.keys(n),o={},i={},s={};a.forEach(function(c){var h=n[c],f=Vc(t,c),p,d;"reducer"in h?(p=h.reducer,d=h.prepare):p=h,o[c]=p,i[f]=p,s[c]=d?pe(f,d):pe(f)});function l(){typeof e.extraReducers=="object"&&(Xa||(Xa=!0,console.warn("The object notation for `createSlice.extraReducers` is deprecated, and will be removed in RTK 2.0. Please use the 'builder callback' notation instead: https://redux-toolkit.js.org/api/createSlice")));var c=typeof e.extraReducers=="function"?bi(e.extraReducers):[e.extraReducers],h=c[0],f=h===void 0?{}:h,p=c[1],d=p===void 0?[]:p,v=c[2],m=v===void 0?void 0:v,O=nt(nt({},f),i);return Wc(r,function(A){for(var R in O)A.addCase(R,O[R]);for(var w=0,S=d;w<S.length;w++){var y=S[w];A.addMatcher(y.matcher,y.reducer)}m&&A.addDefaultCase(m)})}var u;return{name:t,reducer:function(c,h){return u||(u=l()),u(c,h)},actions:s,caseReducers:o,getInitialState:function(){return u||(u=l()),u.getInitialState()}}}var Kc="ModuleSymbhasOwnPr-0123456789ABCDEFGHNRVfgctiUvz_KqYTJkLxpZXIjQW",wi=function(e){e===void 0&&(e=21);for(var t="",r=e;r--;)t+=Kc[Math.random()*64|0];return t},Gc=["name","message","stack","code"],ln=function(){function e(t,r){this.payload=t,this.meta=r}return e}(),eo=function(){function e(t,r){this.payload=t,this.meta=r}return e}(),Jc=function(e){if(typeof e=="object"&&e!==null){for(var t={},r=0,n=Gc;r<n.length;r++){var a=n[r];typeof e[a]=="string"&&(t[a]=e[a])}return t}return{message:String(e)}},to=function(){function e(t,r,n){var a=pe(t+"/fulfilled",function(c,h,f,p){return{payload:c,meta:un(nt({},p||{}),{arg:f,requestId:h,requestStatus:"fulfilled"})}}),o=pe(t+"/pending",function(c,h,f){return{payload:void 0,meta:un(nt({},f||{}),{arg:h,requestId:c,requestStatus:"pending"})}}),i=pe(t+"/rejected",function(c,h,f,p,d){return{payload:p,error:(n&&n.serializeError||Jc)(c||"Rejected"),meta:un(nt({},d||{}),{arg:f,requestId:h,rejectedWithValue:!!p,requestStatus:"rejected",aborted:c?.name==="AbortError",condition:c?.name==="ConditionError"})}}),s=!1,l=typeof AbortController<"u"?AbortController:function(){function c(){this.signal={aborted:!1,addEventListener:function(){},dispatchEvent:function(){return!1},onabort:function(){},removeEventListener:function(){},reason:void 0,throwIfAborted:function(){}}}return c.prototype.abort=function(){s||(s=!0,console.info("This platform does not implement AbortController. \nIf you want to use the AbortController to react to `abort` events, please consider importing a polyfill like 'abortcontroller-polyfill/dist/abortcontroller-polyfill-only'."))},c}();function u(c){return function(h,f,p){var d=n?.idGenerator?n.idGenerator(c):wi(),v=new l,m;function O(R){m=R,v.abort()}var A=function(){return Qc(this,null,function(){var R,w,S,y,b,g,T;return Lc(this,function(E){switch(E.label){case 0:return E.trys.push([0,4,,5]),y=(R=n?.condition)==null?void 0:R.call(n,c,{getState:f,extra:p}),Zc(y)?[4,y]:[3,2];case 1:y=E.sent(),E.label=2;case 2:if(y===!1||v.signal.aborted)throw{name:"ConditionError",message:"Aborted due to condition callback returning false."};return b=new Promise(function(I,x){return v.signal.addEventListener("abort",function(){return x({name:"AbortError",message:m||"Aborted"})})}),h(o(d,c,(w=n?.getPendingMeta)==null?void 0:w.call(n,{requestId:d,arg:c},{getState:f,extra:p}))),[4,Promise.race([b,Promise.resolve(r(c,{dispatch:h,getState:f,extra:p,requestId:d,signal:v.signal,abort:O,rejectWithValue:function(I,x){return new ln(I,x)},fulfillWithValue:function(I,x){return new eo(I,x)}})).then(function(I){if(I instanceof ln)throw I;return I instanceof eo?a(I.payload,d,c,I.meta):a(I,d,c)})])];case 3:return S=E.sent(),[3,5];case 4:return g=E.sent(),S=g instanceof ln?i(null,d,c,g.payload,g.meta):i(g,d,c),[3,5];case 5:return T=n&&!n.dispatchConditionRejection&&i.match(S)&&S.meta.condition,T||h(S),[2,S]}})})}();return Object.assign(A,{abort:O,requestId:d,arg:c,unwrap:function(){return A.then(Yc)}})}}return Object.assign(u,{pending:o,rejected:i,fulfilled:a,typePrefix:t})}return e.withTypes=function(){return e},e}();function Yc(e){if(e.meta&&e.meta.rejectedWithValue)throw e.payload;if(e.error)throw e.error;return e.payload}function Zc(e){return e!==null&&typeof e=="object"&&typeof e.then=="function"}var Si=function(e,t){return zc(e)?e.match(t):e(t)};function At(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(r){return e.some(function(n){return Si(n,r)})}}function zt(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(r){return e.every(function(n){return Si(n,r)})}}function Lr(e,t){if(!e||!e.meta)return!1;var r=typeof e.meta.requestId=="string",n=t.indexOf(e.meta.requestStatus)>-1;return r&&n}function nr(e){return typeof e[0]=="function"&&"pending"in e[0]&&"fulfilled"in e[0]&&"rejected"in e[0]}function ta(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length===0?function(r){return Lr(r,["pending"])}:nr(e)?function(r){var n=e.map(function(o){return o.pending}),a=At.apply(void 0,n);return a(r)}:ta()(e[0])}function Kt(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length===0?function(r){return Lr(r,["rejected"])}:nr(e)?function(r){var n=e.map(function(o){return o.rejected}),a=At.apply(void 0,n);return a(r)}:Kt()(e[0])}function Fr(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var r=function(n){return n&&n.meta&&n.meta.rejectedWithValue};return e.length===0?function(n){var a=zt(Kt.apply(void 0,e),r);return a(n)}:nr(e)?function(n){var a=zt(Kt.apply(void 0,e),r);return a(n)}:Fr()(e[0])}function st(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length===0?function(r){return Lr(r,["fulfilled"])}:nr(e)?function(r){var n=e.map(function(o){return o.fulfilled}),a=At.apply(void 0,n);return a(r)}:st()(e[0])}function kn(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return e.length===0?function(r){return Lr(r,["pending","fulfilled","rejected"])}:nr(e)?function(r){for(var n=[],a=0,o=e;a<o.length;a++){var i=o[a];n.push(i.pending,i.rejected,i.fulfilled)}var s=At.apply(void 0,n);return s(r)}:kn()(e[0])}var ra="listenerMiddleware";pe(ra+"/add");pe(ra+"/removeAll");pe(ra+"/remove");var Lt="RTK_autoBatch",Ct=function(){return function(e){var t;return{payload:e,meta:(t={},t[Lt]=!0,t)}}},ro;typeof queueMicrotask=="function"&&queueMicrotask.bind(typeof window<"u"?window:typeof global<"u"?global:globalThis);Xs();var Ei=function(e,t){var r={label:0,sent:function(){if(o[0]&1)throw o[1];return o[1]},trys:[],ops:[]},n,a,o,i;return i={next:s(0),throw:s(1),return:s(2)},typeof Symbol=="function"&&(i[Symbol.iterator]=function(){return this}),i;function s(u){return function(c){return l([u,c])}}function l(u){if(n)throw new TypeError("Generator is already executing.");for(;r;)try{if(n=1,a&&(o=u[0]&2?a.return:u[0]?a.throw||((o=a.return)&&o.call(a),0):a.next)&&!(o=o.call(a,u[1])).done)return o;switch(a=0,o&&(u=[u[0]&2,o.value]),u[0]){case 0:case 1:o=u;break;case 4:return r.label++,{value:u[1],done:!1};case 5:r.label++,a=u[1],u=[0];continue;case 7:u=r.ops.pop(),r.trys.pop();continue;default:if(o=r.trys,!(o=o.length>0&&o[o.length-1])&&(u[0]===6||u[0]===2)){r=0;continue}if(u[0]===3&&(!o||u[1]>o[0]&&u[1]<o[3])){r.label=u[1];break}if(u[0]===6&&r.label<o[1]){r.label=o[1],o=u;break}if(o&&r.label<o[2]){r.label=o[2],r.ops.push(u);break}o[2]&&r.ops.pop(),r.trys.pop();continue}u=t.call(e,r)}catch(c){u=[6,c],a=0}finally{n=o=0}if(u[0]&5)throw u[1];return{value:u[0]?u[1]:void 0,done:!0}}},xr=function(e,t){for(var r=0,n=t.length,a=e.length;r<n;r++,a++)e[a]=t[r];return e},Xc=Object.defineProperty,ef=Object.defineProperties,tf=Object.getOwnPropertyDescriptors,no=Object.getOwnPropertySymbols,rf=Object.prototype.hasOwnProperty,nf=Object.prototype.propertyIsEnumerable,ao=function(e,t,r){return t in e?Xc(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r},re=function(e,t){for(var r in t||(t={}))rf.call(t,r)&&ao(e,r,t[r]);if(no)for(var n=0,a=no(t);n<a.length;n++){var r=a[n];nf.call(t,r)&&ao(e,r,t[r])}return e},qe=function(e,t){return ef(e,tf(t))},Ri=function(e,t,r){return new Promise(function(n,a){var o=function(l){try{s(r.next(l))}catch(u){a(u)}},i=function(l){try{s(r.throw(l))}catch(u){a(u)}},s=function(l){return l.done?n(l.value):Promise.resolve(l.value).then(o,i)};s((r=r.apply(e,t)).next())})},X;(function(e){e.uninitialized="uninitialized",e.pending="pending",e.fulfilled="fulfilled",e.rejected="rejected"})(X||(X={}));function af(e){return{status:e,isUninitialized:e===X.uninitialized,isLoading:e===X.pending,isSuccess:e===X.fulfilled,isError:e===X.rejected}}var oo=function(e){return[].concat.apply([],e)};function of(){return typeof navigator>"u"||navigator.onLine===void 0?!0:navigator.onLine}function sf(){return typeof document>"u"?!0:document.visibilityState!=="hidden"}var io=Vt;function Oi(e,t){if(e===t||!(io(e)&&io(t)||Array.isArray(e)&&Array.isArray(t)))return t;for(var r=Object.keys(t),n=Object.keys(e),a=r.length===n.length,o=Array.isArray(t)?[]:{},i=0,s=r;i<s.length;i++){var l=s[i];o[l]=Oi(e[l],t[l]),a&&(a=e[l]===o[l])}return a?e:o}var so=function(){function e(t,r){r===void 0&&(r=void 0),this.value=t,this.meta=r}return e}(),na=pe("__rtkq/focused"),Ti=pe("__rtkq/unfocused"),aa=pe("__rtkq/online"),Pi=pe("__rtkq/offline"),xe;(function(e){e.query="query",e.mutation="mutation"})(xe||(xe={}));function Ai(e){return e.type===xe.query}function uf(e){return e.type===xe.mutation}function oa(e,t,r,n,a,o){return lf(e)?e(t,r,n,a).map(_n).map(o):Array.isArray(e)?e.map(_n).map(o):[]}function lf(e){return typeof e=="function"}function _n(e){return typeof e=="string"?{type:e}:e}function cn(e){return e!=null}var Gt=Symbol("forceQueryFn"),Dn=function(e){return typeof e[Gt]=="function"};function cf(e){var t=e.serializeQueryArgs,r=e.queryThunk,n=e.mutationThunk,a=e.api,o=e.context,i=new Map,s=new Map,l=a.internalActions,u=l.unsubscribeQueryResult,c=l.removeMutationResult,h=l.updateSubscriptionOptions;return{buildInitiateQuery:R,buildInitiateMutation:w,getRunningQueryThunk:d,getRunningMutationThunk:v,getRunningQueriesThunk:m,getRunningMutationsThunk:O,getRunningOperationPromises:p,removalWarning:f};function f(){throw new Error(`This method had to be removed due to a conceptual bug in RTK.
       Please see https://github.com/reduxjs/redux-toolkit/pull/2481 for details.
       See https://redux-toolkit.js.org/rtk-query/usage/server-side-rendering for new guidance on SSR.`)}function p(){if(typeof process<"u")f();else{var S=function(y){return Array.from(y.values()).flatMap(function(b){return b?Object.values(b):[]})};return xr(xr([],S(i)),S(s)).filter(cn)}}function d(S,y){return function(b){var g,T=o.endpointDefinitions[S],E=t({queryArgs:y,endpointDefinition:T,endpointName:S});return(g=i.get(b))==null?void 0:g[E]}}function v(S,y){return function(b){var g;return(g=s.get(b))==null?void 0:g[y]}}function m(){return function(S){return Object.values(i.get(S)||{}).filter(cn)}}function O(){return function(S){return Object.values(s.get(S)||{}).filter(cn)}}function A(S){{if(A.triggered)return;var y=S(a.internalActions.internal_probeSubscription({queryCacheKey:"DOES_NOT_EXIST",requestId:"DUMMY_REQUEST_ID"}));if(A.triggered=!0,typeof y!="boolean")throw new Error('Warning: Middleware for RTK-Query API at reducerPath "'+a.reducerPath+`" has not been added to the store.
You must add the middleware for RTK-Query to function correctly!`)}}function R(S,y){var b=function(g,T){var E=T===void 0?{}:T,I=E.subscribe,x=I===void 0?!0:I,$=E.forceRefetch,k=E.subscriptionOptions,_=Gt,q=E[_];return function(L,N){var V,z,B=t({queryArgs:g,endpointDefinition:y,endpointName:S}),K=r((V={type:"query",subscribe:x,forceRefetch:$,subscriptionOptions:k,endpointName:S,originalArgs:g,queryCacheKey:B},V[Gt]=q,V)),Q=a.endpoints[S].select(g),G=L(K),J=Q(N());A(L);var Y=G.requestId,ne=G.abort,Ne=J.requestId!==Y,Je=(z=i.get(L))==null?void 0:z[B],ut=function(){return Q(N())},Oe=Object.assign(q?G.then(ut):Ne&&!Je?Promise.resolve(J):Promise.all([Je,G]).then(ut),{arg:g,requestId:Y,subscriptionOptions:k,queryCacheKey:B,abort:ne,unwrap:function(){return Ri(this,null,function(){var Z;return Ei(this,function($e){switch($e.label){case 0:return[4,Oe];case 1:if(Z=$e.sent(),Z.isError)throw Z.error;return[2,Z.data]}})})},refetch:function(){return L(b(g,{subscribe:!1,forceRefetch:!0}))},unsubscribe:function(){x&&L(u({queryCacheKey:B,requestId:Y}))},updateSubscriptionOptions:function(Z){Oe.subscriptionOptions=Z,L(h({endpointName:S,requestId:Y,queryCacheKey:B,options:Z}))}});if(!Je&&!Ne&&!q){var je=i.get(L)||{};je[B]=Oe,i.set(L,je),Oe.then(function(){delete je[B],Object.keys(je).length||i.delete(L)})}return Oe}};return b}function w(S){return function(y,b){var g=b===void 0?{}:b,T=g.track,E=T===void 0?!0:T,I=g.fixedCacheKey;return function(x,$){var k=n({type:"mutation",endpointName:S,originalArgs:y,track:E,fixedCacheKey:I}),_=x(k);A(x);var q=_.requestId,L=_.abort,N=_.unwrap,V=_.unwrap().then(function(Q){return{data:Q}}).catch(function(Q){return{error:Q}}),z=function(){x(c({requestId:q,fixedCacheKey:I}))},B=Object.assign(V,{arg:_.arg,requestId:q,abort:L,unwrap:N,unsubscribe:z,reset:z}),K=s.get(x)||{};return s.set(x,K),K[q]=B,B.then(function(){delete K[q],Object.keys(K).length||s.delete(x)}),I&&(K[I]=B,B.then(function(){K[I]===B&&(delete K[I],Object.keys(K).length||s.delete(x))})),B}}}}function uo(e){return e}function ff(e){var t=this,r=e.reducerPath,n=e.baseQuery,a=e.context.endpointDefinitions,o=e.serializeQueryArgs,i=e.api,s=e.assertTagType,l=function(w,S,y,b){return function(g,T){var E=a[w],I=o({queryArgs:S,endpointDefinition:E,endpointName:w});if(g(i.internalActions.queryResultPatched({queryCacheKey:I,patches:y})),!!b){var x=i.endpoints[w].select(S)(T()),$=oa(E.providesTags,x.data,void 0,S,{},s);g(i.internalActions.updateProvidedBy({queryCacheKey:I,providedTags:$}))}}},u=function(w,S,y,b){return b===void 0&&(b=!0),function(g,T){var E,I,x=i.endpoints[w],$=x.select(S)(T()),k={patches:[],inversePatches:[],undo:function(){return g(i.util.patchQueryData(w,S,k.inversePatches,b))}};if($.status===X.uninitialized)return k;var _;if("data"in $)if(Vn($.data)){var q=Zo($.data,y),L=q[0],N=q[1],V=q[2];(E=k.patches).push.apply(E,N),(I=k.inversePatches).push.apply(I,V),_=L}else _=y($.data),k.patches.push({op:"replace",path:[],value:_}),k.inversePatches.push({op:"replace",path:[],value:$.data});return g(i.util.patchQueryData(w,S,k.patches,b)),k}},c=function(w,S,y){return function(b){var g;return b(i.endpoints[w].initiate(S,(g={subscribe:!1,forceRefetch:!0},g[Gt]=function(){return{data:y}},g)))}},h=function(w,S){return Ri(t,[w,S],function(y,b){var g,T,E,I,x,$,k,_,q,L,N,V,z,B,K,Q,G,J,Y=b.signal,ne=b.abort,Ne=b.rejectWithValue,Je=b.fulfillWithValue,ut=b.dispatch,Oe=b.getState,je=b.extra;return Ei(this,function(Z){switch(Z.label){case 0:g=a[y.endpointName],Z.label=1;case 1:return Z.trys.push([1,8,,13]),T=uo,E=void 0,I={signal:Y,abort:ne,dispatch:ut,getState:Oe,extra:je,endpoint:y.endpointName,type:y.type,forced:y.type==="query"?f(y,Oe()):void 0},x=y.type==="query"?y[Gt]:void 0,x?(E=x(),[3,6]):[3,2];case 2:return g.query?[4,n(g.query(y.originalArgs),I,g.extraOptions)]:[3,4];case 3:return E=Z.sent(),g.transformResponse&&(T=g.transformResponse),[3,6];case 4:return[4,g.queryFn(y.originalArgs,I,g.extraOptions,function($e){return n($e,I,g.extraOptions)})];case 5:E=Z.sent(),Z.label=6;case 6:if(typeof process<"u"){if($=g.query?"`baseQuery`":"`queryFn`",k=void 0,!E)k=$+" did not return anything.";else if(typeof E!="object")k=$+" did not return an object.";else if(E.error&&E.data)k=$+" returned an object containing both `error` and `result`.";else if(E.error===void 0&&E.data===void 0)k=$+" returned an object containing neither a valid `error` and `result`. At least one of them should not be `undefined`";else for(_=0,q=Object.keys(E);_<q.length;_++)if(L=q[_],L!=="error"&&L!=="data"&&L!=="meta"){k="The object returned by "+$+" has the unknown property "+L+".";break}k&&console.error("Error encountered handling the endpoint "+y.endpointName+`.
              `+k+"\n              It needs to return an object with either the shape `{ data: <value> }` or `{ error: <value> }` that may contain an optional `meta` property.\n              Object returned was:",E)}if(E.error)throw new so(E.error,E.meta);return N=Je,[4,T(E.data,E.meta,y.originalArgs)];case 7:return[2,N.apply(void 0,[Z.sent(),(G={fulfilledTimeStamp:Date.now(),baseQueryMeta:E.meta},G[Lt]=!0,G)])];case 8:if(V=Z.sent(),z=V,!(z instanceof so))return[3,12];B=uo,g.query&&g.transformErrorResponse&&(B=g.transformErrorResponse),Z.label=9;case 9:return Z.trys.push([9,11,,12]),K=Ne,[4,B(z.value,z.meta,y.originalArgs)];case 10:return[2,K.apply(void 0,[Z.sent(),(J={baseQueryMeta:z.meta},J[Lt]=!0,J)])];case 11:return Q=Z.sent(),z=Q,[3,12];case 12:throw typeof process<"u"?console.error('An unhandled error occurred processing a request for the endpoint "'+y.endpointName+`".
In the case of an unhandled error, no tags will be "provided" or "invalidated".`,z):console.error(z),z;case 13:return[2]}})})};function f(w,S){var y,b,g,T,E=(b=(y=S[r])==null?void 0:y.queries)==null?void 0:b[w.queryCacheKey],I=(g=S[r])==null?void 0:g.config.refetchOnMountOrArgChange,x=E?.fulfilledTimeStamp,$=(T=w.forceRefetch)!=null?T:w.subscribe&&I;return $?$===!0||(Number(new Date)-Number(x))/1e3>=$:!1}var p=to(r+"/executeQuery",h,{getPendingMeta:function(){var w;return w={startedTimeStamp:Date.now()},w[Lt]=!0,w},condition:function(w,S){var y=S.getState,b,g,T,E=y(),I=(g=(b=E[r])==null?void 0:b.queries)==null?void 0:g[w.queryCacheKey],x=I?.fulfilledTimeStamp,$=w.originalArgs,k=I?.originalArgs,_=a[w.endpointName];return Dn(w)?!0:I?.status==="pending"?!1:f(w,E)||Ai(_)&&((T=_?.forceRefetch)!=null&&T.call(_,{currentArg:$,previousArg:k,endpointState:I,state:E}))?!0:!x},dispatchConditionRejection:!0}),d=to(r+"/executeMutation",h,{getPendingMeta:function(){var w;return w={startedTimeStamp:Date.now()},w[Lt]=!0,w}}),v=function(w){return"force"in w},m=function(w){return"ifOlderThan"in w},O=function(w,S,y){return function(b,g){var T=v(y)&&y.force,E=m(y)&&y.ifOlderThan,I=function(_){return _===void 0&&(_=!0),i.endpoints[w].initiate(S,{forceRefetch:_})},x=i.endpoints[w].select(S)(g());if(T)b(I());else if(E){var $=x?.fulfilledTimeStamp;if(!$){b(I());return}var k=(Number(new Date)-Number(new Date($)))/1e3>=E;k&&b(I())}else b(I(!1))}};function A(w){return function(S){var y,b;return((b=(y=S?.meta)==null?void 0:y.arg)==null?void 0:b.endpointName)===w}}function R(w,S){return{matchPending:zt(ta(w),A(S)),matchFulfilled:zt(st(w),A(S)),matchRejected:zt(Kt(w),A(S))}}return{queryThunk:p,mutationThunk:d,prefetch:O,updateQueryData:u,upsertQueryData:c,patchQueryData:l,buildMatchThunkActions:R}}function Ii(e,t,r,n){return oa(r[e.meta.arg.endpointName][t],st(e)?e.payload:void 0,Fr(e)?e.payload:void 0,e.meta.arg.originalArgs,"baseQueryMeta"in e.meta?e.meta.baseQueryMeta:void 0,n)}function cr(e,t,r){var n=e[t];n&&r(n)}function Jt(e){var t;return(t="arg"in e?e.arg.fixedCacheKey:e.fixedCacheKey)!=null?t:e.requestId}function lo(e,t,r){var n=e[Jt(t)];n&&r(n)}var kt={};function df(e){var t=e.reducerPath,r=e.queryThunk,n=e.mutationThunk,a=e.context,o=a.endpointDefinitions,i=a.apiUid,s=a.extractRehydrationInfo,l=a.hasRehydrationInfo,u=e.assertTagType,c=e.config,h=pe(t+"/resetApiState"),f=Ye({name:t+"/queries",initialState:kt,reducers:{removeQueryResult:{reducer:function(S,y){var b=y.payload.queryCacheKey;delete S[b]},prepare:Ct()},queryResultPatched:{reducer:function(S,y){var b=y.payload,g=b.queryCacheKey,T=b.patches;cr(S,g,function(E){E.data=Ea(E.data,T.concat())})},prepare:Ct()}},extraReducers:function(S){S.addCase(r.pending,function(y,b){var g=b.meta,T=b.meta.arg,E,I,x=Dn(T);(T.subscribe||x)&&((I=y[E=T.queryCacheKey])!=null||(y[E]={status:X.uninitialized,endpointName:T.endpointName})),cr(y,T.queryCacheKey,function($){$.status=X.pending,$.requestId=x&&$.requestId?$.requestId:g.requestId,T.originalArgs!==void 0&&($.originalArgs=T.originalArgs),$.startedTimeStamp=g.startedTimeStamp})}).addCase(r.fulfilled,function(y,b){var g=b.meta,T=b.payload;cr(y,g.arg.queryCacheKey,function(E){var I;if(!(E.requestId!==g.requestId&&!Dn(g.arg))){var x=o[g.arg.endpointName].merge;if(E.status=X.fulfilled,x)if(E.data!==void 0){var $=g.fulfilledTimeStamp,k=g.arg,_=g.baseQueryMeta,q=g.requestId,L=Ot(E.data,function(N){return x(N,T,{arg:k.originalArgs,baseQueryMeta:_,fulfilledTimeStamp:$,requestId:q})});E.data=L}else E.data=T;else E.data=(I=o[g.arg.endpointName].structuralSharing)==null||I?Oi(Yo(E.data)?tu(E.data):E.data,T):T;delete E.error,E.fulfilledTimeStamp=g.fulfilledTimeStamp}})}).addCase(r.rejected,function(y,b){var g=b.meta,T=g.condition,E=g.arg,I=g.requestId,x=b.error,$=b.payload;cr(y,E.queryCacheKey,function(k){if(!T){if(k.requestId!==I)return;k.status=X.rejected,k.error=$??x}})}).addMatcher(l,function(y,b){for(var g=s(b).queries,T=0,E=Object.entries(g);T<E.length;T++){var I=E[T],x=I[0],$=I[1];($?.status===X.fulfilled||$?.status===X.rejected)&&(y[x]=$)}})}}),p=Ye({name:t+"/mutations",initialState:kt,reducers:{removeMutationResult:{reducer:function(S,y){var b=y.payload,g=Jt(b);g in S&&delete S[g]},prepare:Ct()}},extraReducers:function(S){S.addCase(n.pending,function(y,b){var g=b.meta,T=b.meta,E=T.requestId,I=T.arg,x=T.startedTimeStamp;I.track&&(y[Jt(g)]={requestId:E,status:X.pending,endpointName:I.endpointName,startedTimeStamp:x})}).addCase(n.fulfilled,function(y,b){var g=b.payload,T=b.meta;T.arg.track&&lo(y,T,function(E){E.requestId===T.requestId&&(E.status=X.fulfilled,E.data=g,E.fulfilledTimeStamp=T.fulfilledTimeStamp)})}).addCase(n.rejected,function(y,b){var g=b.payload,T=b.error,E=b.meta;E.arg.track&&lo(y,E,function(I){I.requestId===E.requestId&&(I.status=X.rejected,I.error=g??T)})}).addMatcher(l,function(y,b){for(var g=s(b).mutations,T=0,E=Object.entries(g);T<E.length;T++){var I=E[T],x=I[0],$=I[1];($?.status===X.fulfilled||$?.status===X.rejected)&&x!==$?.requestId&&(y[x]=$)}})}}),d=Ye({name:t+"/invalidation",initialState:kt,reducers:{updateProvidedBy:{reducer:function(S,y){for(var b,g,T,E,I=y.payload,x=I.queryCacheKey,$=I.providedTags,k=0,_=Object.values(S);k<_.length;k++)for(var q=_[k],L=0,N=Object.values(q);L<N.length;L++){var V=N[L],z=V.indexOf(x);z!==-1&&V.splice(z,1)}for(var B=0,K=$;B<K.length;B++){var Q=K[B],G=Q.type,J=Q.id,Y=(E=(g=(b=S[G])!=null?b:S[G]={})[T=J||"__internal_without_id"])!=null?E:g[T]=[],ne=Y.includes(x);ne||Y.push(x)}},prepare:Ct()}},extraReducers:function(S){S.addCase(f.actions.removeQueryResult,function(y,b){for(var g=b.payload.queryCacheKey,T=0,E=Object.values(y);T<E.length;T++)for(var I=E[T],x=0,$=Object.values(I);x<$.length;x++){var k=$[x],_=k.indexOf(g);_!==-1&&k.splice(_,1)}}).addMatcher(l,function(y,b){for(var g,T,E,I,x=s(b).provided,$=0,k=Object.entries(x);$<k.length;$++)for(var _=k[$],q=_[0],L=_[1],N=0,V=Object.entries(L);N<V.length;N++)for(var z=V[N],B=z[0],K=z[1],Q=(I=(T=(g=y[q])!=null?g:y[q]={})[E=B||"__internal_without_id"])!=null?I:T[E]=[],G=0,J=K;G<J.length;G++){var Y=J[G],ne=Q.includes(Y);ne||Q.push(Y)}}).addMatcher(At(st(r),Fr(r)),function(y,b){var g=Ii(b,"providesTags",o,u),T=b.meta.arg.queryCacheKey;d.caseReducers.updateProvidedBy(y,d.actions.updateProvidedBy({queryCacheKey:T,providedTags:g}))})}}),v=Ye({name:t+"/subscriptions",initialState:kt,reducers:{updateSubscriptionOptions:function(S,y){},unsubscribeQueryResult:function(S,y){},internal_probeSubscription:function(S,y){}}}),m=Ye({name:t+"/internalSubscriptions",initialState:kt,reducers:{subscriptionsUpdated:{reducer:function(S,y){return Ea(S,y.payload)},prepare:Ct()}}}),O=Ye({name:t+"/config",initialState:re({online:of(),focused:sf(),middlewareRegistered:!1},c),reducers:{middlewareRegistered:function(S,y){var b=y.payload;S.middlewareRegistered=S.middlewareRegistered==="conflict"||i!==b?"conflict":!0}},extraReducers:function(S){S.addCase(aa,function(y){y.online=!0}).addCase(Pi,function(y){y.online=!1}).addCase(na,function(y){y.focused=!0}).addCase(Ti,function(y){y.focused=!1}).addMatcher(l,function(y){return re({},y)})}}),A=$c({queries:f.reducer,mutations:p.reducer,provided:d.reducer,subscriptions:m.reducer,config:O.reducer}),R=function(S,y){return A(h.match(y)?void 0:S,y)},w=qe(re(re(re(re(re(re({},O.actions),f.actions),v.actions),m.actions),p.actions),d.actions),{unsubscribeMutationResult:p.actions.removeMutationResult,resetApiState:h});return{reducer:R,actions:w}}var Xe=Symbol.for("RTKQ/skipToken"),xi={status:X.uninitialized},co=Ot(xi,function(){}),fo=Ot(xi,function(){});function pf(e){var t=e.serializeQueryArgs,r=e.reducerPath,n=function(c){return co},a=function(c){return fo};return{buildQuerySelector:s,buildMutationSelector:l,selectInvalidatedBy:u};function o(c){return re(re({},c),af(c.status))}function i(c){var h=c[r];if(!h){if(i.triggered)return h;i.triggered=!0,console.error("Error: No data found at `state."+r+"`. Did you forget to add the reducer to the store?")}return h}function s(c,h){return function(f){var p=t({queryArgs:f,endpointDefinition:h,endpointName:c}),d=function(m){var O,A,R;return(R=(A=(O=i(m))==null?void 0:O.queries)==null?void 0:A[p])!=null?R:co},v=f===Xe?n:d;return Qt(v,o)}}function l(){return function(c){var h,f;typeof c=="object"?f=(h=Jt(c))!=null?h:Xe:f=c;var p=function(v){var m,O,A;return(A=(O=(m=i(v))==null?void 0:m.mutations)==null?void 0:O[f])!=null?A:fo},d=f===Xe?a:p;return Qt(d,o)}}function u(c,h){for(var f,p=c[r],d=new Set,v=0,m=h.map(_n);v<m.length;v++){var O=m[v],A=p.provided[O.type];if(A)for(var R=(f=O.id!==void 0?A[O.id]:oo(Object.values(A)))!=null?f:[],w=0,S=R;w<S.length;w++){var y=S[w];d.add(y)}}return oo(Array.from(d.values()).map(function(b){var g=p.queries[b];return g?[{queryCacheKey:b,endpointName:g.endpointName,originalArgs:g.originalArgs}]:[]}))}}var fr=WeakMap?new WeakMap:void 0,po=function(e){var t=e.endpointName,r=e.queryArgs,n="",a=fr?.get(r);if(typeof a=="string")n=a;else{var o=JSON.stringify(r,function(i,s){return Vt(s)?Object.keys(s).sort().reduce(function(l,u){return l[u]=s[u],l},{}):s});Vt(r)&&fr?.set(r,o),n=o}return t+"("+n+")"};function hf(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];return function(n){var a=Mn(function(c){var h,f;return(f=n.extractRehydrationInfo)==null?void 0:f.call(n,c,{reducerPath:(h=n.reducerPath)!=null?h:"api"})}),o=qe(re({reducerPath:"api",keepUnusedDataFor:60,refetchOnMountOrArgChange:!1,refetchOnFocus:!1,refetchOnReconnect:!1},n),{extractRehydrationInfo:a,serializeQueryArgs:function(c){var h=po;if("serializeQueryArgs"in c.endpointDefinition){var f=c.endpointDefinition.serializeQueryArgs;h=function(p){var d=f(p);return typeof d=="string"?d:po(qe(re({},p),{queryArgs:d}))}}else n.serializeQueryArgs&&(h=n.serializeQueryArgs);return h(c)},tagTypes:xr([],n.tagTypes||[])}),i={endpointDefinitions:{},batch:function(c){c()},apiUid:wi(),extractRehydrationInfo:a,hasRehydrationInfo:Mn(function(c){return a(c)!=null})},s={injectEndpoints:u,enhanceEndpoints:function(c){var h=c.addTagTypes,f=c.endpoints;if(h)for(var p=0,d=h;p<d.length;p++){var v=d[p];o.tagTypes.includes(v)||o.tagTypes.push(v)}if(f)for(var m=0,O=Object.entries(f);m<O.length;m++){var A=O[m],R=A[0],w=A[1];typeof w=="function"?w(i.endpointDefinitions[R]):Object.assign(i.endpointDefinitions[R]||{},w)}return s}},l=e.map(function(c){return c.init(s,o,i)});function u(c){for(var h=c.endpoints({query:function(w){return qe(re({},w),{type:xe.query})},mutation:function(w){return qe(re({},w),{type:xe.mutation})}}),f=0,p=Object.entries(h);f<p.length;f++){var d=p[f],v=d[0],m=d[1];if(!c.overrideExisting&&v in i.endpointDefinitions){typeof process<"u"&&console.error("called `injectEndpoints` to override already-existing endpointName "+v+" without specifying `overrideExisting: true`");continue}i.endpointDefinitions[v]=m;for(var O=0,A=l;O<A.length;O++){var R=A[O];R.injectEndpoint(v,m)}}return s}return s.injectEndpoints({endpoints:n.endpoints})}}function yf(e){for(var t in e)return!1;return!0}var vf=2147483647/1e3-1,mf=function(e){var t=e.reducerPath,r=e.api,n=e.context,a=e.internalState,o=r.internalActions,i=o.removeQueryResult,s=o.unsubscribeQueryResult;function l(f){var p=a.currentSubscriptions[f];return!!p&&!yf(p)}var u={},c=function(f,p,d){var v;if(s.match(f)){var m=p.getState()[t],O=f.payload.queryCacheKey;h(O,(v=m.queries[O])==null?void 0:v.endpointName,p,m.config)}if(r.util.resetApiState.match(f))for(var A=0,R=Object.entries(u);A<R.length;A++){var w=R[A],S=w[0],y=w[1];y&&clearTimeout(y),delete u[S]}if(n.hasRehydrationInfo(f))for(var m=p.getState()[t],b=n.extractRehydrationInfo(f).queries,g=0,T=Object.entries(b);g<T.length;g++){var E=T[g],O=E[0],I=E[1];h(O,I?.endpointName,p,m.config)}};function h(f,p,d,v){var m,O=n.endpointDefinitions[p],A=(m=O?.keepUnusedDataFor)!=null?m:v.keepUnusedDataFor;if(A!==1/0){var R=Math.max(0,Math.min(A,vf));if(!l(f)){var w=u[f];w&&clearTimeout(w),u[f]=setTimeout(function(){l(f)||d.dispatch(i({queryCacheKey:f})),delete u[f]},R*1e3)}}}return c},gf=function(e){var t=e.reducerPath,r=e.context,n=e.context.endpointDefinitions,a=e.mutationThunk,o=e.api,i=e.assertTagType,s=e.refetchQuery,l=o.internalActions.removeQueryResult,u=At(st(a),Fr(a)),c=function(f,p){u(f)&&h(Ii(f,"invalidatesTags",n,i),p),o.util.invalidateTags.match(f)&&h(oa(f.payload,void 0,void 0,void 0,void 0,i),p)};function h(f,p){var d=p.getState(),v=d[t],m=o.util.selectInvalidatedBy(d,f);r.batch(function(){for(var O,A=Array.from(m.values()),R=0,w=A;R<w.length;R++){var S=w[R].queryCacheKey,y=v.queries[S],b=(O=v.subscriptions[S])!=null?O:{};y&&(Object.keys(b).length===0?p.dispatch(l({queryCacheKey:S})):y.status!==X.uninitialized&&p.dispatch(s(y,S)))}})}return c},bf=function(e){var t=e.reducerPath,r=e.queryThunk,n=e.api,a=e.refetchQuery,o=e.internalState,i={},s=function(p,d){(n.internalActions.updateSubscriptionOptions.match(p)||n.internalActions.unsubscribeQueryResult.match(p))&&u(p.payload,d),(r.pending.match(p)||r.rejected.match(p)&&p.meta.condition)&&u(p.meta.arg,d),(r.fulfilled.match(p)||r.rejected.match(p)&&!p.meta.condition)&&l(p.meta.arg,d),n.util.resetApiState.match(p)&&h()};function l(p,d){var v=p.queryCacheKey,m=d.getState()[t],O=m.queries[v],A=o.currentSubscriptions[v];if(!(!O||O.status===X.uninitialized)){var R=f(A);if(Number.isFinite(R)){var w=i[v];w?.timeout&&(clearTimeout(w.timeout),w.timeout=void 0);var S=Date.now()+R,y=i[v]={nextPollTimestamp:S,pollingInterval:R,timeout:setTimeout(function(){y.timeout=void 0,d.dispatch(a(O,v))},R)}}}}function u(p,d){var v=p.queryCacheKey,m=d.getState()[t],O=m.queries[v],A=o.currentSubscriptions[v];if(!(!O||O.status===X.uninitialized)){var R=f(A);if(!Number.isFinite(R)){c(v);return}var w=i[v],S=Date.now()+R;(!w||S<w.nextPollTimestamp)&&l({queryCacheKey:v},d)}}function c(p){var d=i[p];d?.timeout&&clearTimeout(d.timeout),delete i[p]}function h(){for(var p=0,d=Object.keys(i);p<d.length;p++){var v=d[p];c(v)}}function f(p){p===void 0&&(p={});var d=Number.POSITIVE_INFINITY;for(var v in p)p[v].pollingInterval&&(d=Math.min(p[v].pollingInterval,d));return d}return s},wf=function(e){var t=e.reducerPath,r=e.context,n=e.api,a=e.refetchQuery,o=e.internalState,i=n.internalActions.removeQueryResult,s=function(u,c){na.match(u)&&l(c,"refetchOnFocus"),aa.match(u)&&l(c,"refetchOnReconnect")};function l(u,c){var h=u.getState()[t],f=h.queries,p=o.currentSubscriptions;r.batch(function(){for(var d=0,v=Object.keys(p);d<v.length;d++){var m=v[d],O=f[m],A=p[m];if(!(!A||!O)){var R=Object.values(A).some(function(w){return w[c]===!0})||Object.values(A).every(function(w){return w[c]===void 0})&&h.config[c];R&&(Object.keys(A).length===0?u.dispatch(i({queryCacheKey:m})):O.status!==X.uninitialized&&u.dispatch(a(O,m)))}}})}return s},ho=new Error("Promise never resolved before cacheEntryRemoved."),Sf=function(e){var t=e.api,r=e.reducerPath,n=e.context,a=e.queryThunk,o=e.mutationThunk;e.internalState;var i=kn(a),s=kn(o),l=st(a,o),u={},c=function(p,d,v){var m=h(p);if(a.pending.match(p)){var O=v[r].queries[m],A=d.getState()[r].queries[m];!O&&A&&f(p.meta.arg.endpointName,p.meta.arg.originalArgs,m,d,p.meta.requestId)}else if(o.pending.match(p)){var A=d.getState()[r].mutations[m];A&&f(p.meta.arg.endpointName,p.meta.arg.originalArgs,m,d,p.meta.requestId)}else if(l(p)){var R=u[m];R?.valueResolved&&(R.valueResolved({data:p.payload,meta:p.meta.baseQueryMeta}),delete R.valueResolved)}else if(t.internalActions.removeQueryResult.match(p)||t.internalActions.removeMutationResult.match(p)){var R=u[m];R&&(delete u[m],R.cacheEntryRemoved())}else if(t.util.resetApiState.match(p))for(var w=0,S=Object.entries(u);w<S.length;w++){var y=S[w],b=y[0],R=y[1];delete u[b],R.cacheEntryRemoved()}};function h(p){return i(p)?p.meta.arg.queryCacheKey:s(p)?p.meta.requestId:t.internalActions.removeQueryResult.match(p)?p.payload.queryCacheKey:t.internalActions.removeMutationResult.match(p)?Jt(p.payload):""}function f(p,d,v,m,O){var A=n.endpointDefinitions[p],R=A?.onCacheEntryAdded;if(R){var w={},S=new Promise(function(I){w.cacheEntryRemoved=I}),y=Promise.race([new Promise(function(I){w.valueResolved=I}),S.then(function(){throw ho})]);y.catch(function(){}),u[v]=w;var b=t.endpoints[p].select(A.type===xe.query?d:v),g=m.dispatch(function(I,x,$){return $}),T=qe(re({},m),{getCacheEntry:function(){return b(m.getState())},requestId:O,extra:g,updateCachedData:A.type===xe.query?function(I){return m.dispatch(t.util.updateQueryData(p,d,I))}:void 0,cacheDataLoaded:y,cacheEntryRemoved:S}),E=R(d,T);Promise.resolve(E).catch(function(I){if(I!==ho)throw I})}}return c},Ef=function(e){var t=e.api,r=e.context,n=e.queryThunk,a=e.mutationThunk,o=ta(n,a),i=Kt(n,a),s=st(n,a),l={},u=function(c,h){var f,p,d;if(o(c)){var v=c.meta,m=v.requestId,O=v.arg,A=O.endpointName,R=O.originalArgs,w=r.endpointDefinitions[A],S=w?.onQueryStarted;if(S){var y={},b=new Promise(function(_,q){y.resolve=_,y.reject=q});b.catch(function(){}),l[m]=y;var g=t.endpoints[A].select(w.type===xe.query?R:m),T=h.dispatch(function(_,q,L){return L}),E=qe(re({},h),{getCacheEntry:function(){return g(h.getState())},requestId:m,extra:T,updateCachedData:w.type===xe.query?function(_){return h.dispatch(t.util.updateQueryData(A,R,_))}:void 0,queryFulfilled:b});S(R,E)}}else if(s(c)){var I=c.meta,m=I.requestId,x=I.baseQueryMeta;(f=l[m])==null||f.resolve({data:c.payload,meta:x}),delete l[m]}else if(i(c)){var $=c.meta,m=$.requestId,k=$.rejectedWithValue,x=$.baseQueryMeta;(d=l[m])==null||d.reject({error:(p=c.payload)!=null?p:c.error,isUnhandledError:!k,meta:x}),delete l[m]}};return u},Rf=function(e){var t=e.api,r=e.context.apiUid,n=e.reducerPath;return function(a,o){var i,s;t.util.resetApiState.match(a)&&o.dispatch(t.internalActions.middlewareRegistered(r)),typeof process<"u"&&t.internalActions.middlewareRegistered.match(a)&&a.payload===r&&((s=(i=o.getState()[n])==null?void 0:i.config)==null?void 0:s.middlewareRegistered)==="conflict"&&console.warn('There is a mismatch between slice and middleware for the reducerPath "'+n+`".
You can only have one api per reducer path, this will lead to crashes in various situations!`+(n==="api"?`
If you have multiple apis, you *have* to specify the reducerPath option when using createApi!`:""))}},yo,Of=typeof queueMicrotask=="function"?queueMicrotask.bind(typeof window<"u"?window:typeof global<"u"?global:globalThis):function(e){return(yo||(yo=Promise.resolve())).then(e).catch(function(t){return setTimeout(function(){throw t},0)})},Tf=function(e){var t=e.api,r=e.queryThunk,n=e.internalState,a=t.reducerPath+"/subscriptions",o=null,i=!1,s=t.internalActions,l=s.updateSubscriptionOptions,u=s.unsubscribeQueryResult,c=function(h,f){var p,d,v,m,O,A,R,w,S;if(l.match(f)){var y=f.payload,b=y.queryCacheKey,g=y.requestId,T=y.options;return(p=h?.[b])!=null&&p[g]&&(h[b][g]=T),!0}if(u.match(f)){var E=f.payload,b=E.queryCacheKey,g=E.requestId;return h[b]&&delete h[b][g],!0}if(t.internalActions.removeQueryResult.match(f))return delete h[f.payload.queryCacheKey],!0;if(r.pending.match(f)){var I=f.meta,x=I.arg,g=I.requestId;if(x.subscribe){var $=(v=h[d=x.queryCacheKey])!=null?v:h[d]={};return $[g]=(O=(m=x.subscriptionOptions)!=null?m:$[g])!=null?O:{},!0}}if(r.rejected.match(f)){var k=f.meta,_=k.condition,x=k.arg,g=k.requestId;if(_&&x.subscribe){var $=(R=h[A=x.queryCacheKey])!=null?R:h[A]={};return $[g]=(S=(w=x.subscriptionOptions)!=null?w:$[g])!=null?S:{},!0}}return!1};return function(h,f){var p,d;if(o||(o=JSON.parse(JSON.stringify(n.currentSubscriptions))),t.util.resetApiState.match(h))return o=n.currentSubscriptions={},[!0,!1];if(t.internalActions.internal_probeSubscription.match(h)){var v=h.payload,m=v.queryCacheKey,O=v.requestId,A=!!((p=n.currentSubscriptions[m])!=null&&p[O]);return[!1,A]}var R=c(n.currentSubscriptions,h);if(R){i||(Of(function(){var b=JSON.parse(JSON.stringify(n.currentSubscriptions)),g=Zo(o,function(){return b}),T=g[1];f.next(t.internalActions.subscriptionsUpdated(T)),o=b,i=!1}),i=!0);var w=!!((d=h.type)!=null&&d.startsWith(a)),S=r.rejected.match(h)&&h.meta.condition&&!!h.meta.arg.subscribe,y=!w&&!S;return[y,!1]}return[!0,!1]}};function Pf(e){var t=e.reducerPath,r=e.queryThunk,n=e.api,a=e.context,o=a.apiUid,i={invalidateTags:pe(t+"/invalidateTags")},s=function(h){return!!h&&typeof h.type=="string"&&h.type.startsWith(t+"/")},l=[Rf,mf,gf,bf,Sf,Ef],u=function(h){var f=!1,p={currentSubscriptions:{}},d=qe(re({},e),{internalState:p,refetchQuery:c}),v=l.map(function(A){return A(d)}),m=Tf(d),O=wf(d);return function(A){return function(R){f||(f=!0,h.dispatch(n.internalActions.middlewareRegistered(o)));var w=qe(re({},h),{next:A}),S=h.getState(),y=m(R,w,S),b=y[0],g=y[1],T;if(b?T=A(R):T=g,h.getState()[t]&&(O(R,w,S),s(R)||a.hasRehydrationInfo(R)))for(var E=0,I=v;E<I.length;E++){var x=I[E];x(R,w,S)}return T}}};return{middleware:u,actions:i};function c(h,f,p){return p===void 0&&(p={}),r(re({type:"query",endpointName:h.endpointName,originalArgs:h.originalArgs,subscribe:!1,forceRefetch:!0,queryCacheKey:f},p))}}function Ue(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];Object.assign.apply(Object,xr([e],t))}var vo=Symbol(),Af=function(){return{name:vo,init:function(e,t,r){var n=t.baseQuery,a=t.tagTypes,o=t.reducerPath,i=t.serializeQueryArgs,s=t.keepUnusedDataFor,l=t.refetchOnMountOrArgChange,u=t.refetchOnFocus,c=t.refetchOnReconnect;eu();var h=function(Q){return typeof process<"u"&&(a.includes(Q.type)||console.error("Tag type '"+Q.type+"' was used, but not specified in `tagTypes`!")),Q};Object.assign(e,{reducerPath:o,endpoints:{},internalActions:{onOnline:aa,onOffline:Pi,onFocus:na,onFocusLost:Ti},util:{}});var f=ff({baseQuery:n,reducerPath:o,context:r,api:e,serializeQueryArgs:i,assertTagType:h}),p=f.queryThunk,d=f.mutationThunk,v=f.patchQueryData,m=f.updateQueryData,O=f.upsertQueryData,A=f.prefetch,R=f.buildMatchThunkActions,w=df({context:r,queryThunk:p,mutationThunk:d,reducerPath:o,assertTagType:h,config:{refetchOnFocus:u,refetchOnReconnect:c,refetchOnMountOrArgChange:l,keepUnusedDataFor:s,reducerPath:o}}),S=w.reducer,y=w.actions;Ue(e.util,{patchQueryData:v,updateQueryData:m,upsertQueryData:O,prefetch:A,resetApiState:y.resetApiState}),Ue(e.internalActions,y);var b=Pf({reducerPath:o,context:r,queryThunk:p,mutationThunk:d,api:e,assertTagType:h}),g=b.middleware,T=b.actions;Ue(e.util,T),Ue(e,{reducer:S,middleware:g});var E=pf({serializeQueryArgs:i,reducerPath:o}),I=E.buildQuerySelector,x=E.buildMutationSelector,$=E.selectInvalidatedBy;Ue(e.util,{selectInvalidatedBy:$});var k=cf({queryThunk:p,mutationThunk:d,api:e,serializeQueryArgs:i,context:r}),_=k.buildInitiateQuery,q=k.buildInitiateMutation,L=k.getRunningMutationThunk,N=k.getRunningMutationsThunk,V=k.getRunningQueriesThunk,z=k.getRunningQueryThunk,B=k.getRunningOperationPromises,K=k.removalWarning;return Ue(e.util,{getRunningOperationPromises:B,getRunningOperationPromise:K,getRunningMutationThunk:L,getRunningMutationsThunk:N,getRunningQueryThunk:z,getRunningQueriesThunk:V}),{name:vo,injectEndpoint:function(Q,G){var J,Y,ne=e;(Y=(J=ne.endpoints)[Q])!=null||(J[Q]={}),Ai(G)?Ue(ne.endpoints[Q],{name:Q,select:I(Q,G),initiate:_(Q,G)},R(p,Q)):uf(G)&&Ue(ne.endpoints[Q],{name:Q,select:x(),initiate:q(Q)},R(d,Q))}}}}},If=function(e,t){for(var r=0,n=t.length,a=e.length;r<n;r++,a++)e[a]=t[r];return e},xf=Object.defineProperty,$f=Object.defineProperties,Mf=Object.getOwnPropertyDescriptors,mo=Object.getOwnPropertySymbols,Cf=Object.prototype.hasOwnProperty,kf=Object.prototype.propertyIsEnumerable,go=function(e,t,r){return t in e?xf(e,t,{enumerable:!0,configurable:!0,writable:!0,value:r}):e[t]=r},ke=function(e,t){for(var r in t||(t={}))Cf.call(t,r)&&go(e,r,t[r]);if(mo)for(var n=0,a=mo(t);n<a.length;n++){var r=a[n];kf.call(t,r)&&go(e,r,t[r])}return e},gr=function(e,t){return $f(e,Mf(t))};function bo(e,t,r,n){var a=C.useMemo(function(){return{queryArgs:e,serialized:typeof e=="object"?t({queryArgs:e,endpointDefinition:r,endpointName:n}):e}},[e,t,r,n]),o=C.useRef(a);return C.useEffect(function(){o.current.serialized!==a.serialized&&(o.current=a)},[a]),o.current.serialized===a.serialized?o.current.queryArgs:e}var fn=Symbol();function dn(e){var t=C.useRef(e);return C.useEffect(function(){Rr(t.current,e)||(t.current=e)},[e]),Rr(t.current,e)?t.current:e}var dr=WeakMap?new WeakMap:void 0,_f=function(e){var t=e.endpointName,r=e.queryArgs,n="",a=dr?.get(r);if(typeof a=="string")n=a;else{var o=JSON.stringify(r,function(i,s){return Vt(s)?Object.keys(s).sort().reduce(function(l,u){return l[u]=s[u],l},{}):s});Vt(r)&&dr?.set(r,o),n=o}return t+"("+n+")"},Df=typeof window<"u"&&window.document&&window.document.createElement?C.useLayoutEffect:C.useEffect,qf=function(e){return e},Lf=function(e){return e.isUninitialized?gr(ke({},e),{isUninitialized:!1,isFetching:!0,isLoading:e.data===void 0,status:X.pending}):e};function Ff(e){var t=e.api,r=e.moduleOptions,n=r.batch,a=r.useDispatch,o=r.useSelector,i=r.useStore,s=r.unstable__sideEffectsInRender,l=e.serializeQueryArgs,u=e.context,c=s?function(v){return v()}:C.useEffect;return{buildQueryHooks:p,buildMutationHook:d,usePrefetch:f};function h(v,m,O){if(m?.endpointName&&v.isUninitialized){var A=m.endpointName,R=u.endpointDefinitions[A];l({queryArgs:m.originalArgs,endpointDefinition:R,endpointName:A})===l({queryArgs:O,endpointDefinition:R,endpointName:A})&&(m=void 0)}var w=v.isSuccess?v.data:m?.data;w===void 0&&(w=v.data);var S=w!==void 0,y=v.isLoading,b=!S&&y,g=v.isSuccess||y&&S;return gr(ke({},v),{data:w,currentData:v.data,isFetching:y,isLoading:b,isSuccess:g})}function f(v,m){var O=a(),A=dn(m);return C.useCallback(function(R,w){return O(t.util.prefetch(v,R,ke(ke({},A),w)))},[v,O,A])}function p(v){var m=function(R,w){var S=w===void 0?{}:w,y=S.refetchOnReconnect,b=S.refetchOnFocus,g=S.refetchOnMountOrArgChange,T=S.skip,E=T===void 0?!1:T,I=S.pollingInterval,x=I===void 0?0:I,$=t.endpoints[v].initiate,k=a(),_=bo(E?Xe:R,_f,u.endpointDefinitions[v],v),q=dn({refetchOnReconnect:y,refetchOnFocus:b,pollingInterval:x}),L=C.useRef(!1),N=C.useRef(),V=N.current||{},z=V.queryCacheKey,B=V.requestId,K=!1;if(z&&B){var Q=k(t.internalActions.internal_probeSubscription({queryCacheKey:z,requestId:B}));if(typeof Q!="boolean")throw new Error('Warning: Middleware for RTK-Query API at reducerPath "'+t.reducerPath+`" has not been added to the store.
    You must add the middleware for RTK-Query to function correctly!`);K=!!Q}var G=!K&&L.current;return c(function(){L.current=K}),c(function(){G&&(N.current=void 0)},[G]),c(function(){var J,Y=N.current;if(typeof process<"u",_===Xe){Y?.unsubscribe(),N.current=void 0;return}var ne=(J=N.current)==null?void 0:J.subscriptionOptions;if(!Y||Y.arg!==_){Y?.unsubscribe();var Ne=k($(_,{subscriptionOptions:q,forceRefetch:g}));N.current=Ne}else q!==ne&&Y.updateSubscriptionOptions(q)},[k,$,g,_,q,G]),C.useEffect(function(){return function(){var J;(J=N.current)==null||J.unsubscribe(),N.current=void 0}},[]),C.useMemo(function(){return{refetch:function(){var J;if(!N.current)throw new Error("Cannot refetch a query that has not been started yet.");return(J=N.current)==null?void 0:J.refetch()}}},[])},O=function(R){var w=R===void 0?{}:R,S=w.refetchOnReconnect,y=w.refetchOnFocus,b=w.pollingInterval,g=b===void 0?0:b,T=t.endpoints[v].initiate,E=a(),I=C.useState(fn),x=I[0],$=I[1],k=C.useRef(),_=dn({refetchOnReconnect:S,refetchOnFocus:y,pollingInterval:g});c(function(){var N,V,z=(N=k.current)==null?void 0:N.subscriptionOptions;_!==z&&((V=k.current)==null||V.updateSubscriptionOptions(_))},[_]);var q=C.useRef(_);c(function(){q.current=_},[_]);var L=C.useCallback(function(N,V){V===void 0&&(V=!1);var z;return n(function(){var B;(B=k.current)==null||B.unsubscribe(),k.current=z=E(T(N,{subscriptionOptions:q.current,forceRefetch:!V})),$(N)}),z},[E,T]);return C.useEffect(function(){return function(){var N;(N=k?.current)==null||N.unsubscribe()}},[]),C.useEffect(function(){x!==fn&&!k.current&&L(x,!0)},[x,L]),C.useMemo(function(){return[L,x]},[L,x])},A=function(R,w){var S=w===void 0?{}:w,y=S.skip,b=y===void 0?!1:y,g=S.selectFromResult,T=t.endpoints[v].select,E=bo(b?Xe:R,l,u.endpointDefinitions[v],v),I=C.useRef(),x=C.useMemo(function(){return Qt([T(E),function(L,N){return N},function(L){return E}],h)},[T,E]),$=C.useMemo(function(){return g?Qt([x],g):x},[x,g]),k=o(function(L){return $(L,I.current)},Rr),_=i(),q=x(_.getState(),I.current);return Df(function(){I.current=q},[q]),k};return{useQueryState:A,useQuerySubscription:m,useLazyQuerySubscription:O,useLazyQuery:function(R){var w=O(R),S=w[0],y=w[1],b=A(y,gr(ke({},R),{skip:y===fn})),g=C.useMemo(function(){return{lastArg:y}},[y]);return C.useMemo(function(){return[S,b,g]},[S,b,g])},useQuery:function(R,w){var S=m(R,w),y=A(R,ke({selectFromResult:R===Xe||w?.skip?void 0:Lf},w)),b=y.data,g=y.status,T=y.isLoading,E=y.isSuccess,I=y.isError,x=y.error;return C.useDebugValue({data:b,status:g,isLoading:T,isSuccess:E,isError:I,error:x}),C.useMemo(function(){return ke(ke({},y),S)},[y,S])}}}function d(v){return function(m){var O=m===void 0?{}:m,A=O.selectFromResult,R=A===void 0?qf:A,w=O.fixedCacheKey,S=t.endpoints[v],y=S.select,b=S.initiate,g=a(),T=C.useState(),E=T[0],I=T[1];C.useEffect(function(){return function(){E?.arg.fixedCacheKey||E?.reset()}},[E]);var x=C.useCallback(function(Y){var ne=g(b(Y,{fixedCacheKey:w}));return I(ne),ne},[g,b,w]),$=(E||{}).requestId,k=C.useMemo(function(){return Qt([y({fixedCacheKey:w,requestId:E?.requestId})],R)},[y,E,R,w]),_=o(k,Rr),q=w==null?E?.arg.originalArgs:void 0,L=C.useCallback(function(){n(function(){E&&I(void 0),w&&g(t.internalActions.removeMutationResult({requestId:$,fixedCacheKey:w}))})},[g,w,E,$]),N=_.endpointName,V=_.data,z=_.status,B=_.isLoading,K=_.isSuccess,Q=_.isError,G=_.error;C.useDebugValue({endpointName:N,data:V,status:z,isLoading:B,isSuccess:K,isError:Q,error:G});var J=C.useMemo(function(){return gr(ke({},_),{originalArgs:q,reset:L})},[_,q,L]);return C.useMemo(function(){return[x,J]},[x,J])}}}var $r;(function(e){e.query="query",e.mutation="mutation"})($r||($r={}));function Nf(e){return e.type===$r.query}function jf(e){return e.type===$r.mutation}function pn(e){return e.replace(e[0],e[0].toUpperCase())}function pr(e){for(var t=[],r=1;r<arguments.length;r++)t[r-1]=arguments[r];Object.assign.apply(Object,If([e],t))}var Uf=Symbol(),Bf=function(e){var t={},r=t.batch,n=r===void 0?Vo.unstable_batchedUpdates:r,a=t.useDispatch,o=a===void 0?Js:a,i=t.useSelector,s=i===void 0?Ys:i,l=t.useStore,u=l===void 0?Zs:l,c=t.unstable__sideEffectsInRender,h=c===void 0?!1:c;return{name:Uf,init:function(f,p,d){var v=p.serializeQueryArgs,m=f,O=Ff({api:f,moduleOptions:{batch:n,useDispatch:o,useSelector:s,useStore:u,unstable__sideEffectsInRender:h},serializeQueryArgs:v,context:d}),A=O.buildQueryHooks,R=O.buildMutationHook,w=O.usePrefetch;return pr(m,{usePrefetch:w}),pr(d,{batch:n}),{injectEndpoint:function(S,y){if(Nf(y)){var b=A(S),g=b.useQuery,T=b.useLazyQuery,E=b.useLazyQuerySubscription,I=b.useQueryState,x=b.useQuerySubscription;pr(m.endpoints[S],{useQuery:g,useLazyQuery:T,useLazyQuerySubscription:E,useQueryState:I,useQuerySubscription:x}),f["use"+pn(S)+"Query"]=g,f["useLazy"+pn(S)+"Query"]=T}else if(jf(y)){var $=R(S);pr(m.endpoints[S],{useMutation:$}),f["use"+pn(S)+"Mutation"]=$}}}}}},Qf=hf(Af(),Bf());const wo={TOKEN:"jwtToken"};class Yt extends Error{name;message;response;code;status;constructor(t,r){super(t),this.name="FetchError",this.message=t,this.response=r,this.code=r?.data?.error?.status,this.status=r?.data?.error?.status,Error.captureStackTrace&&Error.captureStackTrace(this,Yt)}}const zf=e=>e instanceof Yt,Hf=()=>JSON.parse(localStorage.getItem(wo.TOKEN)??sessionStorage.getItem(wo.TOKEN)??'""'),Wf=(e={})=>{const t=window.strapi.backendURL,r={Accept:"application/json","Content-Type":"application/json",Authorization:`Bearer ${Hf()}`},n=f=>f instanceof FormData,a=f=>f.charAt(0)!=="/"?`/${f}`:f,o=f=>new RegExp("^(?:[a-z+]+:)?//","i").test(f),i=f=>o(f)?f:a(f),s=async(f,p)=>{try{const d=await f.json();if(!f.ok&&d.error&&!p?.(f.status))throw new Yt(d.error.message,{data:d});if(!f.ok&&!p?.(f.status))throw new Yt("Unknown Server Error");return{data:d}}catch(d){if(d instanceof SyntaxError&&f.ok)return{data:[],status:f.status};throw d}},l=f=>p=>{if(f){const d=Sc.stringify(f,{encode:!1});return`${p}?${d}`}return p},u=f=>`${t}${f}`,c=f=>Os(i,u,l(f?.params));return{get:async(f,p)=>{const d=new Headers({...r,...p?.headers}),v=c(p),m=await fetch(v(f),{signal:p?.signal??e.signal,method:"GET",headers:d});return s(m,p?.validateStatus)},post:async(f,p,d)=>{const v=new Headers({...r,...d?.headers}),m=c(d);n(p)&&v.delete("Content-Type");const O=await fetch(m(f),{signal:d?.signal??e.signal,method:"POST",headers:v,body:n(p)?p:JSON.stringify(p)});return s(O,d?.validateStatus)},put:async(f,p,d)=>{const v=new Headers({...r,...d?.headers}),m=c(d);n(p)&&v.delete("Content-Type");const O=await fetch(m(f),{signal:d?.signal??e.signal,method:"PUT",headers:v,body:n(p)?p:JSON.stringify(p)});return s(O,d?.validateStatus)},del:async(f,p)=>{const d=new Headers({...r,...p?.headers}),v=c(p),m=await fetch(v(f),{signal:p?.signal??e.signal,method:"DELETE",headers:d});return s(m,p?.validateStatus)}}},Vf=()=>async(e,{signal:t})=>{try{const{get:r,post:n,del:a,put:o}=Wf();if(typeof e=="string")return{data:(await r(e,{signal:t})).data};{const{url:i,method:s="GET",data:l,config:u}=e;return s==="POST"?{data:(await n(i,l,{...u,signal:t})).data}:s==="DELETE"?{data:(await a(i,{...u,signal:t})).data}:s==="PUT"?{data:(await o(i,l,{...u,signal:t})).data}:{data:(await r(i,{...u,signal:t})).data}}}catch(r){if(zf(r))return typeof r.response?.data=="object"&&r.response?.data!==null&&"error"in r.response?.data?{data:void 0,error:r.response?.data.error}:{data:void 0,error:{name:"UnknownError",message:r.message,details:r.response,status:r.status}};const n=r;return{data:void 0,error:{name:n.name,message:n.message,stack:n.stack}}}},Zt=Qf({reducerPath:"adminApi",baseQuery:Vf(),tagTypes:[],endpoints:()=>({})}),So=e=>e&&e.startsWith("/")?`${window.strapi.backendURL}${e}`:e;Zt.enhanceEndpoints({addTagTypes:["ProjectSettings","LicenseLimits"]}).injectEndpoints({endpoints:e=>({init:e.query({query:()=>({url:"/admin/init",method:"GET"}),transformResponse(t){return t.data}}),information:e.query({query:()=>({url:"/admin/information",method:"GET"}),transformResponse(t){return t.data}}),telemetryProperties:e.query({query:()=>({url:"/admin/telemetry-properties",method:"GET",config:{validateStatus:t=>t<500}}),transformResponse(t){return t.data}}),projectSettings:e.query({query:()=>({url:"/admin/project-settings",method:"GET"}),providesTags:["ProjectSettings"],transformResponse(t){return{authLogo:t.authLogo?{name:t.authLogo.name,url:So(t.authLogo.url)}:void 0,menuLogo:t.menuLogo?{name:t.menuLogo.name,url:So(t.menuLogo.url)}:void 0}}}),updateProjectSettings:e.mutation({query:t=>({url:"/admin/project-settings",method:"POST",data:t,config:{headers:{"Content-Type":"multipart/form-data"}}}),invalidatesTags:["ProjectSettings"]}),getPlugins:e.query({query:()=>({url:"/admin/plugins",method:"GET"})}),getLicenseLimits:e.query({query:()=>({url:"/admin/license-limit-information",method:"GET"}),providesTags:["LicenseLimits"]})}),overrideExisting:!1});function $i(e,t){return function(){return e.apply(t,arguments)}}const{toString:Kf}=Object.prototype,{getPrototypeOf:ia}=Object,Nr=(e=>t=>{const r=Kf.call(t);return e[r]||(e[r]=r.slice(8,-1).toLowerCase())})(Object.create(null)),Re=e=>(e=e.toLowerCase(),t=>Nr(t)===e),jr=e=>t=>typeof t===e,{isArray:It}=Array,Xt=jr("undefined");function Gf(e){return e!==null&&!Xt(e)&&e.constructor!==null&&!Xt(e.constructor)&&ge(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Mi=Re("ArrayBuffer");function Jf(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Mi(e.buffer),t}const Yf=jr("string"),ge=jr("function"),Ci=jr("number"),Ur=e=>e!==null&&typeof e=="object",Zf=e=>e===!0||e===!1,br=e=>{if(Nr(e)!=="object")return!1;const t=ia(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Symbol.toStringTag in e)&&!(Symbol.iterator in e)},Xf=Re("Date"),ed=Re("File"),td=Re("Blob"),rd=Re("FileList"),nd=e=>Ur(e)&&ge(e.pipe),ad=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||ge(e.append)&&((t=Nr(e))==="formdata"||t==="object"&&ge(e.toString)&&e.toString()==="[object FormData]"))},od=Re("URLSearchParams"),[id,sd,ud,ld]=["ReadableStream","Request","Response","Headers"].map(Re),cd=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function ar(e,t,{allOwnKeys:r=!1}={}){if(e===null||typeof e>"u")return;let n,a;if(typeof e!="object"&&(e=[e]),It(e))for(n=0,a=e.length;n<a;n++)t.call(null,e[n],n,e);else{const o=r?Object.getOwnPropertyNames(e):Object.keys(e),i=o.length;let s;for(n=0;n<i;n++)s=o[n],t.call(null,e[s],s,e)}}function ki(e,t){t=t.toLowerCase();const r=Object.keys(e);let n=r.length,a;for(;n-- >0;)if(a=r[n],t===a.toLowerCase())return a;return null}const et=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,_i=e=>!Xt(e)&&e!==et;function qn(){const{caseless:e}=_i(this)&&this||{},t={},r=(n,a)=>{const o=e&&ki(t,a)||a;br(t[o])&&br(n)?t[o]=qn(t[o],n):br(n)?t[o]=qn({},n):It(n)?t[o]=n.slice():t[o]=n};for(let n=0,a=arguments.length;n<a;n++)arguments[n]&&ar(arguments[n],r);return t}const fd=(e,t,r,{allOwnKeys:n}={})=>(ar(t,(a,o)=>{r&&ge(a)?e[o]=$i(a,r):e[o]=a},{allOwnKeys:n}),e),dd=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),pd=(e,t,r,n)=>{e.prototype=Object.create(t.prototype,n),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),r&&Object.assign(e.prototype,r)},hd=(e,t,r,n)=>{let a,o,i;const s={};if(t=t||{},e==null)return t;do{for(a=Object.getOwnPropertyNames(e),o=a.length;o-- >0;)i=a[o],(!n||n(i,e,t))&&!s[i]&&(t[i]=e[i],s[i]=!0);e=r!==!1&&ia(e)}while(e&&(!r||r(e,t))&&e!==Object.prototype);return t},yd=(e,t,r)=>{e=String(e),(r===void 0||r>e.length)&&(r=e.length),r-=t.length;const n=e.indexOf(t,r);return n!==-1&&n===r},vd=e=>{if(!e)return null;if(It(e))return e;let t=e.length;if(!Ci(t))return null;const r=new Array(t);for(;t-- >0;)r[t]=e[t];return r},md=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&ia(Uint8Array)),gd=(e,t)=>{const n=(e&&e[Symbol.iterator]).call(e);let a;for(;(a=n.next())&&!a.done;){const o=a.value;t.call(e,o[0],o[1])}},bd=(e,t)=>{let r;const n=[];for(;(r=e.exec(t))!==null;)n.push(r);return n},wd=Re("HTMLFormElement"),Sd=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(r,n,a){return n.toUpperCase()+a}),Eo=(({hasOwnProperty:e})=>(t,r)=>e.call(t,r))(Object.prototype),Ed=Re("RegExp"),Di=(e,t)=>{const r=Object.getOwnPropertyDescriptors(e),n={};ar(r,(a,o)=>{let i;(i=t(a,o,e))!==!1&&(n[o]=i||a)}),Object.defineProperties(e,n)},Rd=e=>{Di(e,(t,r)=>{if(ge(e)&&["arguments","caller","callee"].indexOf(r)!==-1)return!1;const n=e[r];if(ge(n)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+r+"'")})}})},Od=(e,t)=>{const r={},n=a=>{a.forEach(o=>{r[o]=!0})};return It(e)?n(e):n(String(e).split(t)),r},Td=()=>{},Pd=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t,hn="abcdefghijklmnopqrstuvwxyz",Ro="0123456789",qi={DIGIT:Ro,ALPHA:hn,ALPHA_DIGIT:hn+hn.toUpperCase()+Ro},Ad=(e=16,t=qi.ALPHA_DIGIT)=>{let r="";const{length:n}=t;for(;e--;)r+=t[Math.random()*n|0];return r};function Id(e){return!!(e&&ge(e.append)&&e[Symbol.toStringTag]==="FormData"&&e[Symbol.iterator])}const xd=e=>{const t=new Array(10),r=(n,a)=>{if(Ur(n)){if(t.indexOf(n)>=0)return;if(!("toJSON"in n)){t[a]=n;const o=It(n)?[]:{};return ar(n,(i,s)=>{const l=r(i,a+1);!Xt(l)&&(o[s]=l)}),t[a]=void 0,o}}return n};return r(e,0)},$d=Re("AsyncFunction"),Md=e=>e&&(Ur(e)||ge(e))&&ge(e.then)&&ge(e.catch),Li=((e,t)=>e?setImmediate:t?((r,n)=>(et.addEventListener("message",({source:a,data:o})=>{a===et&&o===r&&n.length&&n.shift()()},!1),a=>{n.push(a),et.postMessage(r,"*")}))(`axios@${Math.random()}`,[]):r=>setTimeout(r))(typeof setImmediate=="function",ge(et.postMessage)),Cd=typeof queueMicrotask<"u"?queueMicrotask.bind(et):typeof process<"u"&&process.nextTick||Li,P={isArray:It,isArrayBuffer:Mi,isBuffer:Gf,isFormData:ad,isArrayBufferView:Jf,isString:Yf,isNumber:Ci,isBoolean:Zf,isObject:Ur,isPlainObject:br,isReadableStream:id,isRequest:sd,isResponse:ud,isHeaders:ld,isUndefined:Xt,isDate:Xf,isFile:ed,isBlob:td,isRegExp:Ed,isFunction:ge,isStream:nd,isURLSearchParams:od,isTypedArray:md,isFileList:rd,forEach:ar,merge:qn,extend:fd,trim:cd,stripBOM:dd,inherits:pd,toFlatObject:hd,kindOf:Nr,kindOfTest:Re,endsWith:yd,toArray:vd,forEachEntry:gd,matchAll:bd,isHTMLForm:wd,hasOwnProperty:Eo,hasOwnProp:Eo,reduceDescriptors:Di,freezeMethods:Rd,toObjectSet:Od,toCamelCase:Sd,noop:Td,toFiniteNumber:Pd,findKey:ki,global:et,isContextDefined:_i,ALPHABET:qi,generateString:Ad,isSpecCompliantForm:Id,toJSONObject:xd,isAsyncFn:$d,isThenable:Md,setImmediate:Li,asap:Cd};function U(e,t,r,n,a){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),r&&(this.config=r),n&&(this.request=n),a&&(this.response=a)}P.inherits(U,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:P.toJSONObject(this.config),code:this.code,status:this.response&&this.response.status?this.response.status:null}}});const Fi=U.prototype,Ni={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Ni[e]={value:e}});Object.defineProperties(U,Ni);Object.defineProperty(Fi,"isAxiosError",{value:!0});U.from=(e,t,r,n,a,o)=>{const i=Object.create(Fi);return P.toFlatObject(e,i,function(l){return l!==Error.prototype},s=>s!=="isAxiosError"),U.call(i,e.message,t,r,n,a),i.cause=e,i.name=e.name,o&&Object.assign(i,o),i};const kd=null;function Ln(e){return P.isPlainObject(e)||P.isArray(e)}function ji(e){return P.endsWith(e,"[]")?e.slice(0,-2):e}function Oo(e,t,r){return e?e.concat(t).map(function(a,o){return a=ji(a),!r&&o?"["+a+"]":a}).join(r?".":""):t}function _d(e){return P.isArray(e)&&!e.some(Ln)}const Dd=P.toFlatObject(P,{},null,function(t){return/^is[A-Z]/.test(t)});function Br(e,t,r){if(!P.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,r=P.toFlatObject(r,{metaTokens:!0,dots:!1,indexes:!1},!1,function(v,m){return!P.isUndefined(m[v])});const n=r.metaTokens,a=r.visitor||c,o=r.dots,i=r.indexes,l=(r.Blob||typeof Blob<"u"&&Blob)&&P.isSpecCompliantForm(t);if(!P.isFunction(a))throw new TypeError("visitor must be a function");function u(d){if(d===null)return"";if(P.isDate(d))return d.toISOString();if(!l&&P.isBlob(d))throw new U("Blob is not supported. Use a Buffer instead.");return P.isArrayBuffer(d)||P.isTypedArray(d)?l&&typeof Blob=="function"?new Blob([d]):Buffer.from(d):d}function c(d,v,m){let O=d;if(d&&!m&&typeof d=="object"){if(P.endsWith(v,"{}"))v=n?v:v.slice(0,-2),d=JSON.stringify(d);else if(P.isArray(d)&&_d(d)||(P.isFileList(d)||P.endsWith(v,"[]"))&&(O=P.toArray(d)))return v=ji(v),O.forEach(function(R,w){!(P.isUndefined(R)||R===null)&&t.append(i===!0?Oo([v],w,o):i===null?v:v+"[]",u(R))}),!1}return Ln(d)?!0:(t.append(Oo(m,v,o),u(d)),!1)}const h=[],f=Object.assign(Dd,{defaultVisitor:c,convertValue:u,isVisitable:Ln});function p(d,v){if(!P.isUndefined(d)){if(h.indexOf(d)!==-1)throw Error("Circular reference detected in "+v.join("."));h.push(d),P.forEach(d,function(O,A){(!(P.isUndefined(O)||O===null)&&a.call(t,O,P.isString(A)?A.trim():A,v,f))===!0&&p(O,v?v.concat(A):[A])}),h.pop()}}if(!P.isObject(e))throw new TypeError("data must be an object");return p(e),t}function To(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(n){return t[n]})}function sa(e,t){this._pairs=[],e&&Br(e,this,t)}const Ui=sa.prototype;Ui.append=function(t,r){this._pairs.push([t,r])};Ui.toString=function(t){const r=t?function(n){return t.call(this,n,To)}:To;return this._pairs.map(function(a){return r(a[0])+"="+r(a[1])},"").join("&")};function qd(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Bi(e,t,r){if(!t)return e;const n=r&&r.encode||qd,a=r&&r.serialize;let o;if(a?o=a(t,r):o=P.isURLSearchParams(t)?t.toString():new sa(t,r).toString(n),o){const i=e.indexOf("#");i!==-1&&(e=e.slice(0,i)),e+=(e.indexOf("?")===-1?"?":"&")+o}return e}class Po{constructor(){this.handlers=[]}use(t,r,n){return this.handlers.push({fulfilled:t,rejected:r,synchronous:n?n.synchronous:!1,runWhen:n?n.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){P.forEach(this.handlers,function(n){n!==null&&t(n)})}}const Qi={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Ld=typeof URLSearchParams<"u"?URLSearchParams:sa,Fd=typeof FormData<"u"?FormData:null,Nd=typeof Blob<"u"?Blob:null,jd={isBrowser:!0,classes:{URLSearchParams:Ld,FormData:Fd,Blob:Nd},protocols:["http","https","file","blob","url","data"]},ua=typeof window<"u"&&typeof document<"u",Ud=(e=>ua&&["ReactNative","NativeScript","NS"].indexOf(e)<0)(typeof navigator<"u"&&navigator.product),Bd=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Qd=ua&&window.location.href||"http://localhost",zd=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:ua,hasStandardBrowserEnv:Ud,hasStandardBrowserWebWorkerEnv:Bd,origin:Qd},Symbol.toStringTag,{value:"Module"})),Ee={...zd,...jd};function Hd(e,t){return Br(e,new Ee.classes.URLSearchParams,Object.assign({visitor:function(r,n,a,o){return Ee.isNode&&P.isBuffer(r)?(this.append(n,r.toString("base64")),!1):o.defaultVisitor.apply(this,arguments)}},t))}function Wd(e){return P.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Vd(e){const t={},r=Object.keys(e);let n;const a=r.length;let o;for(n=0;n<a;n++)o=r[n],t[o]=e[o];return t}function zi(e){function t(r,n,a,o){let i=r[o++];if(i==="__proto__")return!0;const s=Number.isFinite(+i),l=o>=r.length;return i=!i&&P.isArray(a)?a.length:i,l?(P.hasOwnProp(a,i)?a[i]=[a[i],n]:a[i]=n,!s):((!a[i]||!P.isObject(a[i]))&&(a[i]=[]),t(r,n,a[i],o)&&P.isArray(a[i])&&(a[i]=Vd(a[i])),!s)}if(P.isFormData(e)&&P.isFunction(e.entries)){const r={};return P.forEachEntry(e,(n,a)=>{t(Wd(n),a,r,0)}),r}return null}function Kd(e,t,r){if(P.isString(e))try{return(t||JSON.parse)(e),P.trim(e)}catch(n){if(n.name!=="SyntaxError")throw n}return(r||JSON.stringify)(e)}const or={transitional:Qi,adapter:["xhr","http","fetch"],transformRequest:[function(t,r){const n=r.getContentType()||"",a=n.indexOf("application/json")>-1,o=P.isObject(t);if(o&&P.isHTMLForm(t)&&(t=new FormData(t)),P.isFormData(t))return a?JSON.stringify(zi(t)):t;if(P.isArrayBuffer(t)||P.isBuffer(t)||P.isStream(t)||P.isFile(t)||P.isBlob(t)||P.isReadableStream(t))return t;if(P.isArrayBufferView(t))return t.buffer;if(P.isURLSearchParams(t))return r.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let s;if(o){if(n.indexOf("application/x-www-form-urlencoded")>-1)return Hd(t,this.formSerializer).toString();if((s=P.isFileList(t))||n.indexOf("multipart/form-data")>-1){const l=this.env&&this.env.FormData;return Br(s?{"files[]":t}:t,l&&new l,this.formSerializer)}}return o||a?(r.setContentType("application/json",!1),Kd(t)):t}],transformResponse:[function(t){const r=this.transitional||or.transitional,n=r&&r.forcedJSONParsing,a=this.responseType==="json";if(P.isResponse(t)||P.isReadableStream(t))return t;if(t&&P.isString(t)&&(n&&!this.responseType||a)){const i=!(r&&r.silentJSONParsing)&&a;try{return JSON.parse(t)}catch(s){if(i)throw s.name==="SyntaxError"?U.from(s,U.ERR_BAD_RESPONSE,this,null,this.response):s}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:Ee.classes.FormData,Blob:Ee.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};P.forEach(["delete","get","head","post","put","patch"],e=>{or.headers[e]={}});const Gd=P.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Jd=e=>{const t={};let r,n,a;return e&&e.split(`
`).forEach(function(i){a=i.indexOf(":"),r=i.substring(0,a).trim().toLowerCase(),n=i.substring(a+1).trim(),!(!r||t[r]&&Gd[r])&&(r==="set-cookie"?t[r]?t[r].push(n):t[r]=[n]:t[r]=t[r]?t[r]+", "+n:n)}),t},Ao=Symbol("internals");function _t(e){return e&&String(e).trim().toLowerCase()}function wr(e){return e===!1||e==null?e:P.isArray(e)?e.map(wr):String(e)}function Yd(e){const t=Object.create(null),r=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let n;for(;n=r.exec(e);)t[n[1]]=n[2];return t}const Zd=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function yn(e,t,r,n,a){if(P.isFunction(n))return n.call(this,t,r);if(a&&(t=r),!!P.isString(t)){if(P.isString(n))return t.indexOf(n)!==-1;if(P.isRegExp(n))return n.test(t)}}function Xd(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,r,n)=>r.toUpperCase()+n)}function ep(e,t){const r=P.toCamelCase(" "+t);["get","set","has"].forEach(n=>{Object.defineProperty(e,n+r,{value:function(a,o,i){return this[n].call(this,t,a,o,i)},configurable:!0})})}class he{constructor(t){t&&this.set(t)}set(t,r,n){const a=this;function o(s,l,u){const c=_t(l);if(!c)throw new Error("header name must be a non-empty string");const h=P.findKey(a,c);(!h||a[h]===void 0||u===!0||u===void 0&&a[h]!==!1)&&(a[h||l]=wr(s))}const i=(s,l)=>P.forEach(s,(u,c)=>o(u,c,l));if(P.isPlainObject(t)||t instanceof this.constructor)i(t,r);else if(P.isString(t)&&(t=t.trim())&&!Zd(t))i(Jd(t),r);else if(P.isHeaders(t))for(const[s,l]of t.entries())o(l,s,n);else t!=null&&o(r,t,n);return this}get(t,r){if(t=_t(t),t){const n=P.findKey(this,t);if(n){const a=this[n];if(!r)return a;if(r===!0)return Yd(a);if(P.isFunction(r))return r.call(this,a,n);if(P.isRegExp(r))return r.exec(a);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,r){if(t=_t(t),t){const n=P.findKey(this,t);return!!(n&&this[n]!==void 0&&(!r||yn(this,this[n],n,r)))}return!1}delete(t,r){const n=this;let a=!1;function o(i){if(i=_t(i),i){const s=P.findKey(n,i);s&&(!r||yn(n,n[s],s,r))&&(delete n[s],a=!0)}}return P.isArray(t)?t.forEach(o):o(t),a}clear(t){const r=Object.keys(this);let n=r.length,a=!1;for(;n--;){const o=r[n];(!t||yn(this,this[o],o,t,!0))&&(delete this[o],a=!0)}return a}normalize(t){const r=this,n={};return P.forEach(this,(a,o)=>{const i=P.findKey(n,o);if(i){r[i]=wr(a),delete r[o];return}const s=t?Xd(o):String(o).trim();s!==o&&delete r[o],r[s]=wr(a),n[s]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const r=Object.create(null);return P.forEach(this,(n,a)=>{n!=null&&n!==!1&&(r[a]=t&&P.isArray(n)?n.join(", "):n)}),r}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,r])=>t+": "+r).join(`
`)}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...r){const n=new this(t);return r.forEach(a=>n.set(a)),n}static accessor(t){const n=(this[Ao]=this[Ao]={accessors:{}}).accessors,a=this.prototype;function o(i){const s=_t(i);n[s]||(ep(a,i),n[s]=!0)}return P.isArray(t)?t.forEach(o):o(t),this}}he.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);P.reduceDescriptors(he.prototype,({value:e},t)=>{let r=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(n){this[r]=n}}});P.freezeMethods(he);function vn(e,t){const r=this||or,n=t||r,a=he.from(n.headers);let o=n.data;return P.forEach(e,function(s){o=s.call(r,o,a.normalize(),t?t.status:void 0)}),a.normalize(),o}function Hi(e){return!!(e&&e.__CANCEL__)}function xt(e,t,r){U.call(this,e??"canceled",U.ERR_CANCELED,t,r),this.name="CanceledError"}P.inherits(xt,U,{__CANCEL__:!0});function Wi(e,t,r){const n=r.config.validateStatus;!r.status||!n||n(r.status)?e(r):t(new U("Request failed with status code "+r.status,[U.ERR_BAD_REQUEST,U.ERR_BAD_RESPONSE][Math.floor(r.status/100)-4],r.config,r.request,r))}function tp(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function rp(e,t){e=e||10;const r=new Array(e),n=new Array(e);let a=0,o=0,i;return t=t!==void 0?t:1e3,function(l){const u=Date.now(),c=n[o];i||(i=u),r[a]=l,n[a]=u;let h=o,f=0;for(;h!==a;)f+=r[h++],h=h%e;if(a=(a+1)%e,a===o&&(o=(o+1)%e),u-i<t)return;const p=c&&u-c;return p?Math.round(f*1e3/p):void 0}}function np(e,t){let r=0,n=1e3/t,a,o;const i=(u,c=Date.now())=>{r=c,a=null,o&&(clearTimeout(o),o=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),h=c-r;h>=n?i(u,c):(a=u,o||(o=setTimeout(()=>{o=null,i(a)},n-h)))},()=>a&&i(a)]}const Mr=(e,t,r=3)=>{let n=0;const a=rp(50,250);return np(o=>{const i=o.loaded,s=o.lengthComputable?o.total:void 0,l=i-n,u=a(l),c=i<=s;n=i;const h={loaded:i,total:s,progress:s?i/s:void 0,bytes:l,rate:u||void 0,estimated:u&&s&&c?(s-i)/u:void 0,event:o,lengthComputable:s!=null,[t?"download":"upload"]:!0};e(h)},r)},Io=(e,t)=>{const r=e!=null;return[n=>t[0]({lengthComputable:r,total:e,loaded:n}),t[1]]},xo=e=>(...t)=>P.asap(()=>e(...t)),ap=Ee.hasStandardBrowserEnv?function(){const t=/(msie|trident)/i.test(navigator.userAgent),r=document.createElement("a");let n;function a(o){let i=o;return t&&(r.setAttribute("href",i),i=r.href),r.setAttribute("href",i),{href:r.href,protocol:r.protocol?r.protocol.replace(/:$/,""):"",host:r.host,search:r.search?r.search.replace(/^\?/,""):"",hash:r.hash?r.hash.replace(/^#/,""):"",hostname:r.hostname,port:r.port,pathname:r.pathname.charAt(0)==="/"?r.pathname:"/"+r.pathname}}return n=a(window.location.href),function(i){const s=P.isString(i)?a(i):i;return s.protocol===n.protocol&&s.host===n.host}}():function(){return function(){return!0}}(),op=Ee.hasStandardBrowserEnv?{write(e,t,r,n,a,o){const i=[e+"="+encodeURIComponent(t)];P.isNumber(r)&&i.push("expires="+new Date(r).toGMTString()),P.isString(n)&&i.push("path="+n),P.isString(a)&&i.push("domain="+a),o===!0&&i.push("secure"),document.cookie=i.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function ip(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function sp(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Vi(e,t){return e&&!ip(t)?sp(e,t):t}const $o=e=>e instanceof he?{...e}:e;function ot(e,t){t=t||{};const r={};function n(u,c,h){return P.isPlainObject(u)&&P.isPlainObject(c)?P.merge.call({caseless:h},u,c):P.isPlainObject(c)?P.merge({},c):P.isArray(c)?c.slice():c}function a(u,c,h){if(P.isUndefined(c)){if(!P.isUndefined(u))return n(void 0,u,h)}else return n(u,c,h)}function o(u,c){if(!P.isUndefined(c))return n(void 0,c)}function i(u,c){if(P.isUndefined(c)){if(!P.isUndefined(u))return n(void 0,u)}else return n(void 0,c)}function s(u,c,h){if(h in t)return n(u,c);if(h in e)return n(void 0,u)}const l={url:o,method:o,data:o,baseURL:i,transformRequest:i,transformResponse:i,paramsSerializer:i,timeout:i,timeoutMessage:i,withCredentials:i,withXSRFToken:i,adapter:i,responseType:i,xsrfCookieName:i,xsrfHeaderName:i,onUploadProgress:i,onDownloadProgress:i,decompress:i,maxContentLength:i,maxBodyLength:i,beforeRedirect:i,transport:i,httpAgent:i,httpsAgent:i,cancelToken:i,socketPath:i,responseEncoding:i,validateStatus:s,headers:(u,c)=>a($o(u),$o(c),!0)};return P.forEach(Object.keys(Object.assign({},e,t)),function(c){const h=l[c]||a,f=h(e[c],t[c],c);P.isUndefined(f)&&h!==s||(r[c]=f)}),r}const Ki=e=>{const t=ot({},e);let{data:r,withXSRFToken:n,xsrfHeaderName:a,xsrfCookieName:o,headers:i,auth:s}=t;t.headers=i=he.from(i),t.url=Bi(Vi(t.baseURL,t.url),e.params,e.paramsSerializer),s&&i.set("Authorization","Basic "+btoa((s.username||"")+":"+(s.password?unescape(encodeURIComponent(s.password)):"")));let l;if(P.isFormData(r)){if(Ee.hasStandardBrowserEnv||Ee.hasStandardBrowserWebWorkerEnv)i.setContentType(void 0);else if((l=i.getContentType())!==!1){const[u,...c]=l?l.split(";").map(h=>h.trim()).filter(Boolean):[];i.setContentType([u||"multipart/form-data",...c].join("; "))}}if(Ee.hasStandardBrowserEnv&&(n&&P.isFunction(n)&&(n=n(t)),n||n!==!1&&ap(t.url))){const u=a&&o&&op.read(o);u&&i.set(a,u)}return t},up=typeof XMLHttpRequest<"u",lp=up&&function(e){return new Promise(function(r,n){const a=Ki(e);let o=a.data;const i=he.from(a.headers).normalize();let{responseType:s,onUploadProgress:l,onDownloadProgress:u}=a,c,h,f,p,d;function v(){p&&p(),d&&d(),a.cancelToken&&a.cancelToken.unsubscribe(c),a.signal&&a.signal.removeEventListener("abort",c)}let m=new XMLHttpRequest;m.open(a.method.toUpperCase(),a.url,!0),m.timeout=a.timeout;function O(){if(!m)return;const R=he.from("getAllResponseHeaders"in m&&m.getAllResponseHeaders()),S={data:!s||s==="text"||s==="json"?m.responseText:m.response,status:m.status,statusText:m.statusText,headers:R,config:e,request:m};Wi(function(b){r(b),v()},function(b){n(b),v()},S),m=null}"onloadend"in m?m.onloadend=O:m.onreadystatechange=function(){!m||m.readyState!==4||m.status===0&&!(m.responseURL&&m.responseURL.indexOf("file:")===0)||setTimeout(O)},m.onabort=function(){m&&(n(new U("Request aborted",U.ECONNABORTED,e,m)),m=null)},m.onerror=function(){n(new U("Network Error",U.ERR_NETWORK,e,m)),m=null},m.ontimeout=function(){let w=a.timeout?"timeout of "+a.timeout+"ms exceeded":"timeout exceeded";const S=a.transitional||Qi;a.timeoutErrorMessage&&(w=a.timeoutErrorMessage),n(new U(w,S.clarifyTimeoutError?U.ETIMEDOUT:U.ECONNABORTED,e,m)),m=null},o===void 0&&i.setContentType(null),"setRequestHeader"in m&&P.forEach(i.toJSON(),function(w,S){m.setRequestHeader(S,w)}),P.isUndefined(a.withCredentials)||(m.withCredentials=!!a.withCredentials),s&&s!=="json"&&(m.responseType=a.responseType),u&&([f,d]=Mr(u,!0),m.addEventListener("progress",f)),l&&m.upload&&([h,p]=Mr(l),m.upload.addEventListener("progress",h),m.upload.addEventListener("loadend",p)),(a.cancelToken||a.signal)&&(c=R=>{m&&(n(!R||R.type?new xt(null,e,m):R),m.abort(),m=null)},a.cancelToken&&a.cancelToken.subscribe(c),a.signal&&(a.signal.aborted?c():a.signal.addEventListener("abort",c)));const A=tp(a.url);if(A&&Ee.protocols.indexOf(A)===-1){n(new U("Unsupported protocol "+A+":",U.ERR_BAD_REQUEST,e));return}m.send(o||null)})},cp=(e,t)=>{let r=new AbortController,n;const a=function(l){if(!n){n=!0,i();const u=l instanceof Error?l:this.reason;r.abort(u instanceof U?u:new xt(u instanceof Error?u.message:u))}};let o=t&&setTimeout(()=>{a(new U(`timeout ${t} of ms exceeded`,U.ETIMEDOUT))},t);const i=()=>{e&&(o&&clearTimeout(o),o=null,e.forEach(l=>{l&&(l.removeEventListener?l.removeEventListener("abort",a):l.unsubscribe(a))}),e=null)};e.forEach(l=>l&&l.addEventListener&&l.addEventListener("abort",a));const{signal:s}=r;return s.unsubscribe=i,[s,()=>{o&&clearTimeout(o),o=null}]},fp=function*(e,t){let r=e.byteLength;if(r<t){yield e;return}let n=0,a;for(;n<r;)a=n+t,yield e.slice(n,a),n=a},dp=async function*(e,t,r){for await(const n of e)yield*fp(ArrayBuffer.isView(n)?n:await r(String(n)),t)},Mo=(e,t,r,n,a)=>{const o=dp(e,t,a);let i=0,s,l=u=>{s||(s=!0,n&&n(u))};return new ReadableStream({async pull(u){try{const{done:c,value:h}=await o.next();if(c){l(),u.close();return}let f=h.byteLength;if(r){let p=i+=f;r(p)}u.enqueue(new Uint8Array(h))}catch(c){throw l(c),c}},cancel(u){return l(u),o.return()}},{highWaterMark:2})},Qr=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Gi=Qr&&typeof ReadableStream=="function",Fn=Qr&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),Ji=(e,...t)=>{try{return!!e(...t)}catch{return!1}},pp=Gi&&Ji(()=>{let e=!1;const t=new Request(Ee.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Co=64*1024,Nn=Gi&&Ji(()=>P.isReadableStream(new Response("").body)),Cr={stream:Nn&&(e=>e.body)};Qr&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!Cr[t]&&(Cr[t]=P.isFunction(e[t])?r=>r[t]():(r,n)=>{throw new U(`Response type '${t}' is not supported`,U.ERR_NOT_SUPPORT,n)})})})(new Response);const hp=async e=>{if(e==null)return 0;if(P.isBlob(e))return e.size;if(P.isSpecCompliantForm(e))return(await new Request(e).arrayBuffer()).byteLength;if(P.isArrayBufferView(e)||P.isArrayBuffer(e))return e.byteLength;if(P.isURLSearchParams(e)&&(e=e+""),P.isString(e))return(await Fn(e)).byteLength},yp=async(e,t)=>{const r=P.toFiniteNumber(e.getContentLength());return r??hp(t)},vp=Qr&&(async e=>{let{url:t,method:r,data:n,signal:a,cancelToken:o,timeout:i,onDownloadProgress:s,onUploadProgress:l,responseType:u,headers:c,withCredentials:h="same-origin",fetchOptions:f}=Ki(e);u=u?(u+"").toLowerCase():"text";let[p,d]=a||o||i?cp([a,o],i):[],v,m;const O=()=>{!v&&setTimeout(()=>{p&&p.unsubscribe()}),v=!0};let A;try{if(l&&pp&&r!=="get"&&r!=="head"&&(A=await yp(c,n))!==0){let y=new Request(t,{method:"POST",body:n,duplex:"half"}),b;if(P.isFormData(n)&&(b=y.headers.get("content-type"))&&c.setContentType(b),y.body){const[g,T]=Io(A,Mr(xo(l)));n=Mo(y.body,Co,g,T,Fn)}}P.isString(h)||(h=h?"include":"omit"),m=new Request(t,{...f,signal:p,method:r.toUpperCase(),headers:c.normalize().toJSON(),body:n,duplex:"half",credentials:h});let R=await fetch(m);const w=Nn&&(u==="stream"||u==="response");if(Nn&&(s||w)){const y={};["status","statusText","headers"].forEach(E=>{y[E]=R[E]});const b=P.toFiniteNumber(R.headers.get("content-length")),[g,T]=s&&Io(b,Mr(xo(s),!0))||[];R=new Response(Mo(R.body,Co,g,()=>{T&&T(),w&&O()},Fn),y)}u=u||"text";let S=await Cr[P.findKey(Cr,u)||"text"](R,e);return!w&&O(),d&&d(),await new Promise((y,b)=>{Wi(y,b,{data:S,headers:he.from(R.headers),status:R.status,statusText:R.statusText,config:e,request:m})})}catch(R){throw O(),R&&R.name==="TypeError"&&/fetch/i.test(R.message)?Object.assign(new U("Network Error",U.ERR_NETWORK,e,m),{cause:R.cause||R}):U.from(R,R&&R.code,e,m)}}),jn={http:kd,xhr:lp,fetch:vp};P.forEach(jn,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const ko=e=>`- ${e}`,mp=e=>P.isFunction(e)||e===null||e===!1,Yi={getAdapter:e=>{e=P.isArray(e)?e:[e];const{length:t}=e;let r,n;const a={};for(let o=0;o<t;o++){r=e[o];let i;if(n=r,!mp(r)&&(n=jn[(i=String(r)).toLowerCase()],n===void 0))throw new U(`Unknown adapter '${i}'`);if(n)break;a[i||"#"+o]=n}if(!n){const o=Object.entries(a).map(([s,l])=>`adapter ${s} `+(l===!1?"is not supported by the environment":"is not available in the build"));let i=t?o.length>1?`since :
`+o.map(ko).join(`
`):" "+ko(o[0]):"as no adapter specified";throw new U("There is no suitable adapter to dispatch the request "+i,"ERR_NOT_SUPPORT")}return n},adapters:jn};function mn(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new xt(null,e)}function _o(e){return mn(e),e.headers=he.from(e.headers),e.data=vn.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),Yi.getAdapter(e.adapter||or.adapter)(e).then(function(n){return mn(e),n.data=vn.call(e,e.transformResponse,n),n.headers=he.from(n.headers),n},function(n){return Hi(n)||(mn(e),n&&n.response&&(n.response.data=vn.call(e,e.transformResponse,n.response),n.response.headers=he.from(n.response.headers))),Promise.reject(n)})}const Zi="1.7.4",la={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{la[e]=function(n){return typeof n===e||"a"+(t<1?"n ":" ")+e}});const Do={};la.transitional=function(t,r,n){function a(o,i){return"[Axios v"+Zi+"] Transitional option '"+o+"'"+i+(n?". "+n:"")}return(o,i,s)=>{if(t===!1)throw new U(a(i," has been removed"+(r?" in "+r:"")),U.ERR_DEPRECATED);return r&&!Do[i]&&(Do[i]=!0,console.warn(a(i," has been deprecated since v"+r+" and will be removed in the near future"))),t?t(o,i,s):!0}};function gp(e,t,r){if(typeof e!="object")throw new U("options must be an object",U.ERR_BAD_OPTION_VALUE);const n=Object.keys(e);let a=n.length;for(;a-- >0;){const o=n[a],i=t[o];if(i){const s=e[o],l=s===void 0||i(s,o,e);if(l!==!0)throw new U("option "+o+" must be "+l,U.ERR_BAD_OPTION_VALUE);continue}if(r!==!0)throw new U("Unknown option "+o,U.ERR_BAD_OPTION)}}const Un={assertOptions:gp,validators:la},Be=Un.validators;class at{constructor(t){this.defaults=t,this.interceptors={request:new Po,response:new Po}}async request(t,r){try{return await this._request(t,r)}catch(n){if(n instanceof Error){let a;Error.captureStackTrace?Error.captureStackTrace(a={}):a=new Error;const o=a.stack?a.stack.replace(/^.+\n/,""):"";try{n.stack?o&&!String(n.stack).endsWith(o.replace(/^.+\n.+\n/,""))&&(n.stack+=`
`+o):n.stack=o}catch{}}throw n}}_request(t,r){typeof t=="string"?(r=r||{},r.url=t):r=t||{},r=ot(this.defaults,r);const{transitional:n,paramsSerializer:a,headers:o}=r;n!==void 0&&Un.assertOptions(n,{silentJSONParsing:Be.transitional(Be.boolean),forcedJSONParsing:Be.transitional(Be.boolean),clarifyTimeoutError:Be.transitional(Be.boolean)},!1),a!=null&&(P.isFunction(a)?r.paramsSerializer={serialize:a}:Un.assertOptions(a,{encode:Be.function,serialize:Be.function},!0)),r.method=(r.method||this.defaults.method||"get").toLowerCase();let i=o&&P.merge(o.common,o[r.method]);o&&P.forEach(["delete","get","head","post","put","patch","common"],d=>{delete o[d]}),r.headers=he.concat(i,o);const s=[];let l=!0;this.interceptors.request.forEach(function(v){typeof v.runWhen=="function"&&v.runWhen(r)===!1||(l=l&&v.synchronous,s.unshift(v.fulfilled,v.rejected))});const u=[];this.interceptors.response.forEach(function(v){u.push(v.fulfilled,v.rejected)});let c,h=0,f;if(!l){const d=[_o.bind(this),void 0];for(d.unshift.apply(d,s),d.push.apply(d,u),f=d.length,c=Promise.resolve(r);h<f;)c=c.then(d[h++],d[h++]);return c}f=s.length;let p=r;for(h=0;h<f;){const d=s[h++],v=s[h++];try{p=d(p)}catch(m){v.call(this,m);break}}try{c=_o.call(this,p)}catch(d){return Promise.reject(d)}for(h=0,f=u.length;h<f;)c=c.then(u[h++],u[h++]);return c}getUri(t){t=ot(this.defaults,t);const r=Vi(t.baseURL,t.url);return Bi(r,t.params,t.paramsSerializer)}}P.forEach(["delete","get","head","options"],function(t){at.prototype[t]=function(r,n){return this.request(ot(n||{},{method:t,url:r,data:(n||{}).data}))}});P.forEach(["post","put","patch"],function(t){function r(n){return function(o,i,s){return this.request(ot(s||{},{method:t,headers:n?{"Content-Type":"multipart/form-data"}:{},url:o,data:i}))}}at.prototype[t]=r(),at.prototype[t+"Form"]=r(!0)});class ca{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let r;this.promise=new Promise(function(o){r=o});const n=this;this.promise.then(a=>{if(!n._listeners)return;let o=n._listeners.length;for(;o-- >0;)n._listeners[o](a);n._listeners=null}),this.promise.then=a=>{let o;const i=new Promise(s=>{n.subscribe(s),o=s}).then(a);return i.cancel=function(){n.unsubscribe(o)},i},t(function(o,i,s){n.reason||(n.reason=new xt(o,i,s),r(n.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const r=this._listeners.indexOf(t);r!==-1&&this._listeners.splice(r,1)}static source(){let t;return{token:new ca(function(a){t=a}),cancel:t}}}function bp(e){return function(r){return e.apply(null,r)}}function wp(e){return P.isObject(e)&&e.isAxiosError===!0}const Bn={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Bn).forEach(([e,t])=>{Bn[t]=e});function Xi(e){const t=new at(e),r=$i(at.prototype.request,t);return P.extend(r,at.prototype,t,{allOwnKeys:!0}),P.extend(r,t,null,{allOwnKeys:!0}),r.create=function(a){return Xi(ot(e,a))},r}const te=Xi(or);te.Axios=at;te.CanceledError=xt;te.CancelToken=ca;te.isCancel=Hi;te.VERSION=Zi;te.toFormData=Br;te.AxiosError=U;te.Cancel=te.CanceledError;te.all=function(t){return Promise.all(t)};te.spread=bp;te.isAxiosError=wp;te.mergeConfig=ot;te.AxiosHeaders=he;te.formToJSON=e=>zi(P.isHTMLForm(e)?new FormData(e):e);te.getAdapter=Yi.getAdapter;te.HttpStatusCode=Bn;te.default=te;var es={exports:{}},gn={};/**
 * @license React
 * scheduler.development.js
 *
 * Copyright (c) Facebook, Inc. and its affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qo;function Sp(){return qo||(qo=1,function(e){(function(){typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStart(new Error);var t=!1,r=!1,n=5;function a(D,j){var W=D.length;D.push(j),s(D,j,W)}function o(D){return D.length===0?null:D[0]}function i(D){if(D.length===0)return null;var j=D[0],W=D.pop();return W!==j&&(D[0]=W,l(D,W,0)),j}function s(D,j,W){for(var ee=W;ee>0;){var ae=ee-1>>>1,Se=D[ae];if(u(Se,j)>0)D[ae]=j,D[ee]=Se,ee=ae;else return}}function l(D,j,W){for(var ee=W,ae=D.length,Se=ae>>>1;ee<Se;){var ce=(ee+1)*2-1,lt=D[ce],fe=ce+1,ir=D[fe];if(u(lt,j)<0)fe<ae&&u(ir,lt)<0?(D[ee]=ir,D[fe]=j,ee=fe):(D[ee]=lt,D[ce]=j,ee=ce);else if(fe<ae&&u(ir,j)<0)D[ee]=ir,D[fe]=j,ee=fe;else return}}function u(D,j){var W=D.sortIndex-j.sortIndex;return W!==0?W:D.id-j.id}var c=1,h=2,f=3,p=4,d=5,v=typeof performance=="object"&&typeof performance.now=="function";if(v){var m=performance;e.unstable_now=function(){return m.now()}}else{var O=Date,A=O.now();e.unstable_now=function(){return O.now()-A}}var R=1073741823,w=-1,S=250,y=5e3,b=1e4,g=R,T=[],E=[],I=1,x=null,$=f,k=!1,_=!1,q=!1,L=typeof setTimeout=="function"?setTimeout:null,N=typeof clearTimeout=="function"?clearTimeout:null,V=typeof setImmediate<"u"?setImmediate:null;typeof navigator<"u"&&navigator.scheduling!==void 0&&navigator.scheduling.isInputPending!==void 0&&navigator.scheduling.isInputPending.bind(navigator.scheduling);function z(D){for(var j=o(E);j!==null;){if(j.callback===null)i(E);else if(j.startTime<=D)i(E),j.sortIndex=j.expirationTime,a(T,j);else return;j=o(E)}}function B(D){if(q=!1,z(D),!_)if(o(T)!==null)_=!0,Kr(K);else{var j=o(E);j!==null&&Gr(B,j.startTime-D)}}function K(D,j){_=!1,q&&(q=!1,wa()),k=!0;var W=$;try{var ee;if(!r)return Q(D,j)}finally{x=null,$=W,k=!1}}function Q(D,j){var W=j;for(z(W),x=o(T);x!==null&&!t&&!(x.expirationTime>W&&(!D||ga()));){var ee=x.callback;if(typeof ee=="function"){x.callback=null,$=x.priorityLevel;var ae=x.expirationTime<=W,Se=ee(ae);W=e.unstable_now(),typeof Se=="function"?x.callback=Se:x===o(T)&&i(T),z(W)}else i(T);x=o(T)}if(x!==null)return!0;var ce=o(E);return ce!==null&&Gr(B,ce.startTime-W),!1}function G(D,j){switch(D){case c:case h:case f:case p:case d:break;default:D=f}var W=$;$=D;try{return j()}finally{$=W}}function J(D){var j;switch($){case c:case h:case f:j=f;break;default:j=$;break}var W=$;$=j;try{return D()}finally{$=W}}function Y(D){var j=$;return function(){var W=$;$=j;try{return D.apply(this,arguments)}finally{$=W}}}function ne(D,j,W){var ee=e.unstable_now(),ae;if(typeof W=="object"&&W!==null){var Se=W.delay;typeof Se=="number"&&Se>0?ae=ee+Se:ae=ee}else ae=ee;var ce;switch(D){case c:ce=w;break;case h:ce=S;break;case d:ce=g;break;case p:ce=b;break;case f:default:ce=y;break}var lt=ae+ce,fe={id:I++,callback:j,priorityLevel:D,startTime:ae,expirationTime:lt,sortIndex:-1};return ae>ee?(fe.sortIndex=ae,a(E,fe),o(T)===null&&fe===o(E)&&(q?wa():q=!0,Gr(B,ae-ee))):(fe.sortIndex=lt,a(T,fe),!_&&!k&&(_=!0,Kr(K))),fe}function Ne(){}function Je(){!_&&!k&&(_=!0,Kr(K))}function ut(){return o(T)}function Oe(D){D.callback=null}function je(){return $}var Z=!1,$e=null,Hr=-1,Wr=n,ma=-1;function ga(){var D=e.unstable_now()-ma;return!(D<Wr)}function bs(){}function ws(D){if(D<0||D>125){console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported");return}D>0?Wr=Math.floor(1e3/D):Wr=n}var Vr=function(){if($e!==null){var D=e.unstable_now();ma=D;var j=!0,W=!0;try{W=$e(j,D)}finally{W?$t():(Z=!1,$e=null)}}else Z=!1},$t;if(typeof V=="function")$t=function(){V(Vr)};else if(typeof MessageChannel<"u"){var ba=new MessageChannel,Ss=ba.port2;ba.port1.onmessage=Vr,$t=function(){Ss.postMessage(null)}}else $t=function(){L(Vr,0)};function Kr(D){$e=D,Z||(Z=!0,$t())}function Gr(D,j){Hr=L(function(){D(e.unstable_now())},j)}function wa(){N(Hr),Hr=-1}var Es=bs,Rs=null;e.unstable_IdlePriority=d,e.unstable_ImmediatePriority=c,e.unstable_LowPriority=p,e.unstable_NormalPriority=f,e.unstable_Profiling=Rs,e.unstable_UserBlockingPriority=h,e.unstable_cancelCallback=Oe,e.unstable_continueExecution=Je,e.unstable_forceFrameRate=ws,e.unstable_getCurrentPriorityLevel=je,e.unstable_getFirstCallbackNode=ut,e.unstable_next=J,e.unstable_pauseExecution=Ne,e.unstable_requestPaint=Es,e.unstable_runWithPriority=G,e.unstable_scheduleCallback=ne,e.unstable_shouldYield=ga,e.unstable_wrapCallback=Y,typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"&&typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop=="function"&&__REACT_DEVTOOLS_GLOBAL_HOOK__.registerInternalModuleStop(new Error)})()}(gn)),gn}es.exports=Sp();var bn=es.exports;const Sr=Symbol(),Ep=Symbol(),ts=typeof window>"u"||/ServerSideRendering/.test(window.navigator&&window.navigator.userAgent)?C.useEffect:C.useLayoutEffect,Rp=bn.unstable_runWithPriority?e=>bn.unstable_runWithPriority(bn.unstable_NormalPriority,e):e=>e();function Op(e){const t=C.createContext({[Sr]:{v:{current:e},n:{current:-1},l:new Set,u:n=>n()}});var r;return t[Ep]=t.Provider,t.Provider=(r=t.Provider,({value:n,children:a})=>{const o=C.useRef(n),i=C.useRef(0),[s,l]=C.useState(null);s&&(s(n),l(null));const u=C.useRef();if(!u.current){const c=new Set,h=(f,p)=>{Vo.unstable_batchedUpdates(()=>{i.current+=1;const d={n:i.current};p!=null&&p.suspense&&(d.n*=-1,d.p=new Promise(v=>{l(()=>m=>{d.v=m,delete d.p,v(m)})})),c.forEach(v=>v(d)),f()})};u.current={[Sr]:{v:o,n:i,l:c,u:h}}}return ts(()=>{o.current=n,i.current+=1,Rp(()=>{u.current[Sr].l.forEach(c=>{c({n:i.current,v:n})})})},[n]),C.createElement(r,{value:u.current},a)}),delete t.Consumer,t}function Tp(e,t){const r=C.useContext(e)[Sr];if(typeof process=="object"&&!r)throw new Error("useContextSelector requires special context");const{v:{current:n},n:{current:a},l:o}=r,i=t(n),[s,l]=C.useReducer((u,c)=>{if(!c)return[n,i];if("p"in c)throw c.p;if(c.n===a)return Object.is(u[1],i)?u:[n,i];try{if("v"in c){if(Object.is(u[0],c.v))return u;const h=t(c.v);return Object.is(u[1],h)?u:[c.v,h]}}catch{}return[...u]},[n,i]);return Object.is(s[1],i)||l(),ts(()=>(o.add(l),()=>{o.delete(l)}),[o]),s[1]}function Fe(e,t){const r=Op(t),n=o=>{const{children:i,...s}=o,l=C.useMemo(()=>s,Object.values(s));return M.jsx(r.Provider,{value:l,children:i})},a=(o,i)=>Tp(r,s=>{if(s)return i(s);throw new Error(`\`${o}\` must be used within \`${e}\``)});return n.displayName=e+"Provider",[n,a]}Fe("StrapiApp");const Pp=e=>{const{search:t}=Jo(),r=Ks(),n=C.useMemo(()=>{const o=t.startsWith("?")?t.slice(1):t;return!t&&e?e:{...e,...$n.parse(o)}},[t,e]),a=C.useCallback((o,i="push",s=!1)=>{let l={...n};i==="remove"?Object.keys(o).forEach(u=>{Object.prototype.hasOwnProperty.call(l,u)&&delete l[u]}):l={...n,...o},r({search:$n.stringify(l,{encode:!1})},{replace:s})},[r,n]);return[{query:n,rawQuery:t},a]},dt={TOKEN:"jwtToken",USER:"userInfo"},Lo="STRAPI_THEME",Ap="strapi-admin-language",Ip=Ye({name:"admin",initialState:()=>({language:{locale:"en",localeNames:{en:"English"}},permissions:{},theme:{availableThemes:[],currentTheme:localStorage.getItem(Lo)||"system"},token:null}),reducers:{setAppTheme(e,t){e.theme.currentTheme=t.payload,window.localStorage.setItem(Lo,t.payload)},setAvailableThemes(e,t){e.theme.availableThemes=t.payload},setLocale(e,t){e.language.locale=t.payload,window.localStorage.setItem(Ap,t.payload),document.documentElement.setAttribute("lang",t.payload)},setToken(e,t){e.token=t.payload},login(e,t){const{token:r,persist:n}=t.payload;n?window.localStorage.setItem(dt.TOKEN,JSON.stringify(r)):window.sessionStorage.setItem(dt.TOKEN,JSON.stringify(r)),e.token=r},logout(e){e.token=null,window.localStorage.removeItem(dt.TOKEN),window.localStorage.removeItem(dt.USER),window.sessionStorage.removeItem(dt.TOKEN),window.sessionStorage.removeItem(dt.USER)}}}),xp=Ip.reducer;Zt.enhanceEndpoints({addTagTypes:["User","Me","ProvidersOptions"]}).injectEndpoints({endpoints:e=>({getMe:e.query({query:()=>({method:"GET",url:"/admin/users/me"}),transformResponse(t){return t.data},providesTags:t=>t?["Me",{type:"User",id:t.id}]:["Me"]}),getMyPermissions:e.query({query:()=>({method:"GET",url:"/admin/users/me/permissions"}),transformResponse(t){return t.data}}),updateMe:e.mutation({query:t=>({method:"PUT",url:"/admin/users/me",data:t}),transformResponse(t){return t.data},invalidatesTags:["Me"]}),checkPermissions:e.query({query:t=>({method:"POST",url:"/admin/permissions/check",data:t})}),login:e.mutation({query:t=>({method:"POST",url:"/admin/login",data:t}),transformResponse(t){return t.data},invalidatesTags:["Me"]}),logout:e.mutation({query:()=>({method:"POST",url:"/admin/logout"})}),resetPassword:e.mutation({query:t=>({method:"POST",url:"/admin/reset-password",data:t}),transformResponse(t){return t.data}}),renewToken:e.mutation({query:t=>({method:"POST",url:"/admin/renew-token",data:t}),transformResponse(t){return t.data}}),getRegistrationInfo:e.query({query:t=>({url:"/admin/registration-info",method:"GET",config:{params:{registrationToken:t}}}),transformResponse(t){return t.data}}),registerAdmin:e.mutation({query:t=>({method:"POST",url:"/admin/register-admin",data:t}),transformResponse(t){return t.data}}),registerUser:e.mutation({query:t=>({method:"POST",url:"/admin/register",data:t}),transformResponse(t){return t.data}}),forgotPassword:e.mutation({query:t=>({url:"/admin/forgot-password",method:"POST",data:t})}),isSSOLocked:e.query({query:()=>({url:"/admin/providers/isSSOLocked",method:"GET"}),transformResponse(t){return t.data}}),getProviders:e.query({query:()=>({url:"/admin/providers",method:"GET"})}),getProviderOptions:e.query({query:()=>({url:"/admin/providers/options",method:"GET"}),transformResponse(t){return t.data},providesTags:["ProvidersOptions"]}),updateProviderOptions:e.mutation({query:t=>({url:"/admin/providers/options",method:"PUT",data:t}),transformResponse(t){return t.data},invalidatesTags:["ProvidersOptions"]})}),overrideExisting:!1});Fe("Auth");C.createContext({toggleNotification:()=>{}});function Ae(e,t,r,n=0){const a=Go(t);for(;e&&n<a.length;)e=e[a[n++]];return n!==a.length&&!e||e===void 0?r:e}const $p=e=>e!==null&&typeof e=="object"&&!Array.isArray(e),Mp=e=>String(Math.floor(Number(e)))===e;function ze(e,t,r){const n=Sa(e);let a=n,o=0;const i=Go(t);for(;o<i.length-1;o++){const s=i[o],l=Ae(e,i.slice(0,o+1));if(l&&($p(l)||Array.isArray(l)))a=a[s]=Sa(l);else{const u=i[o+1];a=a[s]=Mp(u)&&Number(u)>=0?[]:{}}}return(o===0?e:a)[i[o]]===r?e:(r===void 0?delete a[i[o]]:a[i[o]]=r,o===0&&r===void 0&&delete n[i[o]],n)}const[Jh,Cp]=Fe("AppInfo",{}),kp=C.createContext({uuid:!1}),Yh=()=>{const{uuid:e,telemetryProperties:t}=C.useContext(kp),r=Cp("useTracking",a=>a.userId);return{trackUsage:C.useCallback(async(a,o)=>{try{if(e&&!window.strapi.telemetryDisabled)return await te.post("https://analytics.strapi.io/api/v2/track",{event:a,userId:r,eventProperties:{...o},userProperties:{},groupProperties:{...t,projectId:e,projectType:window.strapi.projectType}},{headers:{"Content-Type":"application/json","X-Strapi-Event":a}})}catch{}return null},[t,r,e])}};Fe("GuidedTour");Ts`
  body {
    background: ${({theme:e})=>e.colors.neutral100};
  }
`;const rs="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz";function yt(e,t,r){const n=r[0];if(t!=null&&e>=t)throw new Error(e+" >= "+t);if(e.slice(-1)===n||t&&t.slice(-1)===n)throw new Error("trailing zero");if(t){let i=0;for(;(e[i]||n)===t[i];)i++;if(i>0)return t.slice(0,i)+yt(e.slice(i),t.slice(i),r)}const a=e?r.indexOf(e[0]):0,o=t!=null?r.indexOf(t[0]):r.length;if(o-a>1){const i=Math.round(.5*(a+o));return r[i]}else return t&&t.length>1?t.slice(0,1):r[a]+yt(e.slice(1),null,r)}function ns(e){if(e.length!==as(e[0]))throw new Error("invalid integer part of order key: "+e)}function as(e){if(e>="a"&&e<="z")return e.charCodeAt(0)-97+2;if(e>="A"&&e<="Z")return 90-e.charCodeAt(0)+2;throw new Error("invalid order key head: "+e)}function Ft(e){const t=as(e[0]);if(t>e.length)throw new Error("invalid order key: "+e);return e.slice(0,t)}function Fo(e,t){if(e==="A"+t[0].repeat(26))throw new Error("invalid order key: "+e);const r=Ft(e);if(e.slice(r.length).slice(-1)===t[0])throw new Error("invalid order key: "+e)}function No(e,t){ns(e);const[r,...n]=e.split("");let a=!0;for(let o=n.length-1;a&&o>=0;o--){const i=t.indexOf(n[o])+1;i===t.length?n[o]=t[0]:(n[o]=t[i],a=!1)}if(a){if(r==="Z")return"a"+t[0];if(r==="z")return null;const o=String.fromCharCode(r.charCodeAt(0)+1);return o>"a"?n.push(t[0]):n.pop(),o+n.join("")}else return r+n.join("")}function _p(e,t){ns(e);const[r,...n]=e.split("");let a=!0;for(let o=n.length-1;a&&o>=0;o--){const i=t.indexOf(n[o])-1;i===-1?n[o]=t.slice(-1):(n[o]=t[i],a=!1)}if(a){if(r==="a")return"Z"+t.slice(-1);if(r==="A")return null;const o=String.fromCharCode(r.charCodeAt(0)-1);return o<"Z"?n.push(t.slice(-1)):n.pop(),o+n.join("")}else return r+n.join("")}function Dp(e,t,r=rs){if(e!=null&&Fo(e,r),t!=null&&Fo(t,r),e!=null&&t!=null&&e>=t)throw new Error(e+" >= "+t);if(e==null){if(t==null)return"a"+r[0];const l=Ft(t),u=t.slice(l.length);if(l==="A"+r[0].repeat(26))return l+yt("",u,r);if(l<t)return l;const c=_p(l,r);if(c==null)throw new Error("cannot decrement any more");return c}if(t==null){const l=Ft(e),u=e.slice(l.length),c=No(l,r);return c??l+yt(u,null,r)}const n=Ft(e),a=e.slice(n.length),o=Ft(t),i=t.slice(o.length);if(n===o)return n+yt(a,i,r);const s=No(n,r);if(s==null)throw new Error("cannot increment any more");return s<t?s:n+yt(a,null,r)}function jo(e,t,r,n=rs){return[Dp(e,t,n)]}function wn(e,t){return e-t*Math.floor(e/t)}const os=1721426;function hr(e,t,r,n){t=fa(e,t);let a=t-1,o=-2;return r<=2?o=0:Er(t)&&(o=-1),os-1+365*a+Math.floor(a/4)-Math.floor(a/100)+Math.floor(a/400)+Math.floor((367*r-362)/12+o+n)}function Er(e){return e%4===0&&(e%100!==0||e%400===0)}function fa(e,t){return e==="BC"?1-t:t}function qp(e){let t="AD";return e<=0&&(t="BC",e=1-e),[t,e]}const Lp={standard:[31,28,31,30,31,30,31,31,30,31,30,31],leapyear:[31,29,31,30,31,30,31,31,30,31,30,31]};class wt{fromJulianDay(t){let r=t,n=r-os,a=Math.floor(n/146097),o=wn(n,146097),i=Math.floor(o/36524),s=wn(o,36524),l=Math.floor(s/1461),u=wn(s,1461),c=Math.floor(u/365),h=a*400+i*100+l*4+c+(i!==4&&c!==4?1:0),[f,p]=qp(h),d=r-hr(f,p,1,1),v=2;r<hr(f,p,3,1)?v=0:Er(p)&&(v=1);let m=Math.floor(((d+v)*12+373)/367),O=r-hr(f,p,m,1)+1;return new er(f,p,m,O)}toJulianDay(t){return hr(t.era,t.year,t.month,t.day)}getDaysInMonth(t){return Lp[Er(t.year)?"leapyear":"standard"][t.month-1]}getMonthsInYear(t){return 12}getDaysInYear(t){return Er(t.year)?366:365}getYearsInEra(t){return 9999}getEras(){return["BC","AD"]}isInverseEra(t){return t.era==="BC"}balanceDate(t){t.year<=0&&(t.era=t.era==="BC"?"AD":"BC",t.year=1-t.year)}constructor(){this.identifier="gregory"}}function is(e,t){return e.calendar.toJulianDay(e)-t.calendar.toJulianDay(t)}function Fp(e,t){return Uo(e)-Uo(t)}function Uo(e){return e.hour*36e5+e.minute*6e4+e.second*1e3+e.millisecond}let Sn=null;function da(){return Sn==null&&(Sn=new Intl.DateTimeFormat().resolvedOptions().timeZone),Sn}function St(e){e=Ie(e,new wt);let t=fa(e.era,e.year);return ss(t,e.month,e.day,e.hour,e.minute,e.second,e.millisecond)}function ss(e,t,r,n,a,o,i){let s=new Date;return s.setUTCHours(n,a,o,i),s.setUTCFullYear(e,t-1,r),s.getTime()}function Qn(e,t){if(t==="UTC")return 0;if(e>0&&t===da())return new Date(e).getTimezoneOffset()*-6e4;let{year:r,month:n,day:a,hour:o,minute:i,second:s}=us(e,t);return ss(r,n,a,o,i,s,0)-Math.floor(e/1e3)*1e3}const Bo=new Map;function us(e,t){let r=Bo.get(t);r||(r=new Intl.DateTimeFormat("en-US",{timeZone:t,hour12:!1,era:"short",year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric",second:"numeric"}),Bo.set(t,r));let n=r.formatToParts(new Date(e)),a={};for(let o of n)o.type!=="literal"&&(a[o.type]=o.value);return{year:a.era==="BC"||a.era==="B"?-a.year+1:+a.year,month:+a.month,day:+a.day,hour:a.hour==="24"?0:+a.hour,minute:+a.minute,second:+a.second}}const Qo=864e5;function Np(e,t,r,n){return(r===n?[r]:[r,n]).filter(o=>jp(e,t,o))}function jp(e,t,r){let n=us(r,t);return e.year===n.year&&e.month===n.month&&e.day===n.day&&e.hour===n.hour&&e.minute===n.minute&&e.second===n.second}function De(e,t,r="compatible"){let n=Et(e);if(t==="UTC")return St(n);if(t===da()&&r==="compatible"){n=Ie(n,new wt);let l=new Date,u=fa(n.era,n.year);return l.setFullYear(u,n.month-1,n.day),l.setHours(n.hour,n.minute,n.second,n.millisecond),l.getTime()}let a=St(n),o=Qn(a-Qo,t),i=Qn(a+Qo,t),s=Np(n,t,a-o,a-i);if(s.length===1)return s[0];if(s.length>1)switch(r){case"compatible":case"earlier":return s[0];case"later":return s[s.length-1];case"reject":throw new RangeError("Multiple possible absolute times found")}switch(r){case"earlier":return Math.min(a-o,a-i);case"compatible":case"later":return Math.max(a-o,a-i);case"reject":throw new RangeError("No such absolute time found")}}function ls(e,t,r="compatible"){return new Date(De(e,t,r))}function Ve(e,t){let r=Qn(e,t),n=new Date(e+r),a=n.getUTCFullYear(),o=n.getUTCMonth()+1,i=n.getUTCDate(),s=n.getUTCHours(),l=n.getUTCMinutes(),u=n.getUTCSeconds(),c=n.getUTCMilliseconds();return new Rt(a,o,i,t,r,s,l,u,c)}function Up(e){return new er(e.calendar,e.era,e.year,e.month,e.day)}function Et(e,t){let r=0,n=0,a=0,o=0;if("timeZone"in e)({hour:r,minute:n,second:a,millisecond:o}=e);else if("hour"in e&&!t)return e;return new _r(e.calendar,e.era,e.year,e.month,e.day,r,n,a,o)}function Ie(e,t){if(e.calendar.identifier===t.identifier)return e;let r=t.fromJulianDay(e.calendar.toJulianDay(e)),n=e.copy();return n.calendar=t,n.era=r.era,n.year=r.year,n.month=r.month,n.day=r.day,it(n),n}function Bp(e,t,r){if(e instanceof Rt)return e.timeZone===t?e:cs(e,t);let n=De(e,t,r);return Ve(n,t)}function Qp(e){let t=St(e)-e.offset;return new Date(t)}function cs(e,t){let r=St(e)-e.offset;return Ie(Ve(r,t),e.calendar)}const Dt=36e5;function zr(e,t){let r=e.copy(),n="hour"in r?Vp(r,t):0;zn(r,t.years||0),r.calendar.balanceYearMonth&&r.calendar.balanceYearMonth(r,e),r.month+=t.months||0,Hn(r),fs(r),r.day+=(t.weeks||0)*7,r.day+=t.days||0,r.day+=n,zp(r),r.calendar.balanceDate&&r.calendar.balanceDate(r),r.year<1&&(r.year=1,r.month=1,r.day=1);let a=r.calendar.getYearsInEra(r);if(r.year>a){var o,i;let l=(o=(i=r.calendar).isInverseEra)===null||o===void 0?void 0:o.call(i,r);r.year=a,r.month=l?1:r.calendar.getMonthsInYear(r),r.day=l?1:r.calendar.getDaysInMonth(r)}r.month<1&&(r.month=1,r.day=1);let s=r.calendar.getMonthsInYear(r);return r.month>s&&(r.month=s,r.day=r.calendar.getDaysInMonth(r)),r.day=Math.max(1,Math.min(r.calendar.getDaysInMonth(r),r.day)),r}function zn(e,t){var r,n;!((r=(n=e.calendar).isInverseEra)===null||r===void 0)&&r.call(n,e)&&(t=-t),e.year+=t}function Hn(e){for(;e.month<1;)zn(e,-1),e.month+=e.calendar.getMonthsInYear(e);let t=0;for(;e.month>(t=e.calendar.getMonthsInYear(e));)e.month-=t,zn(e,1)}function zp(e){for(;e.day<1;)e.month--,Hn(e),e.day+=e.calendar.getDaysInMonth(e);for(;e.day>e.calendar.getDaysInMonth(e);)e.day-=e.calendar.getDaysInMonth(e),e.month++,Hn(e)}function fs(e){e.month=Math.max(1,Math.min(e.calendar.getMonthsInYear(e),e.month)),e.day=Math.max(1,Math.min(e.calendar.getDaysInMonth(e),e.day))}function it(e){e.calendar.constrainDate&&e.calendar.constrainDate(e),e.year=Math.max(1,Math.min(e.calendar.getYearsInEra(e),e.year)),fs(e)}function ds(e){let t={};for(let r in e)typeof e[r]=="number"&&(t[r]=-e[r]);return t}function ps(e,t){return zr(e,ds(t))}function pa(e,t){let r=e.copy();return t.era!=null&&(r.era=t.era),t.year!=null&&(r.year=t.year),t.month!=null&&(r.month=t.month),t.day!=null&&(r.day=t.day),it(r),r}function kr(e,t){let r=e.copy();return t.hour!=null&&(r.hour=t.hour),t.minute!=null&&(r.minute=t.minute),t.second!=null&&(r.second=t.second),t.millisecond!=null&&(r.millisecond=t.millisecond),Wp(r),r}function Hp(e){e.second+=Math.floor(e.millisecond/1e3),e.millisecond=yr(e.millisecond,1e3),e.minute+=Math.floor(e.second/60),e.second=yr(e.second,60),e.hour+=Math.floor(e.minute/60),e.minute=yr(e.minute,60);let t=Math.floor(e.hour/24);return e.hour=yr(e.hour,24),t}function Wp(e){e.millisecond=Math.max(0,Math.min(e.millisecond,1e3)),e.second=Math.max(0,Math.min(e.second,59)),e.minute=Math.max(0,Math.min(e.minute,59)),e.hour=Math.max(0,Math.min(e.hour,23))}function yr(e,t){let r=e%t;return r<0&&(r+=t),r}function Vp(e,t){return e.hour+=t.hours||0,e.minute+=t.minutes||0,e.second+=t.seconds||0,e.millisecond+=t.milliseconds||0,Hp(e)}function ha(e,t,r,n){let a=e.copy();switch(t){case"era":{let s=e.calendar.getEras(),l=s.indexOf(e.era);if(l<0)throw new Error("Invalid era: "+e.era);l=Le(l,r,0,s.length-1,n?.round),a.era=s[l],it(a);break}case"year":var o,i;!((o=(i=a.calendar).isInverseEra)===null||o===void 0)&&o.call(i,a)&&(r=-r),a.year=Le(e.year,r,-1/0,9999,n?.round),a.year===-1/0&&(a.year=1),a.calendar.balanceYearMonth&&a.calendar.balanceYearMonth(a,e);break;case"month":a.month=Le(e.month,r,1,e.calendar.getMonthsInYear(e),n?.round);break;case"day":a.day=Le(e.day,r,1,e.calendar.getDaysInMonth(e),n?.round);break;default:throw new Error("Unsupported field "+t)}return e.calendar.balanceDate&&e.calendar.balanceDate(a),it(a),a}function hs(e,t,r,n){let a=e.copy();switch(t){case"hour":{let o=e.hour,i=0,s=23;if(n?.hourCycle===12){let l=o>=12;i=l?12:0,s=l?23:11}a.hour=Le(o,r,i,s,n?.round);break}case"minute":a.minute=Le(e.minute,r,0,59,n?.round);break;case"second":a.second=Le(e.second,r,0,59,n?.round);break;case"millisecond":a.millisecond=Le(e.millisecond,r,0,999,n?.round);break;default:throw new Error("Unsupported field "+t)}return a}function Le(e,t,r,n,a=!1){if(a){e+=Math.sign(t),e<r&&(e=n);let o=Math.abs(t);t>0?e=Math.ceil(e/o)*o:e=Math.floor(e/o)*o,e>n&&(e=r)}else e+=t,e<r?e=n-(r-e-1):e>n&&(e=r+(e-n-1));return e}function ys(e,t){let r;if(t.years!=null&&t.years!==0||t.months!=null&&t.months!==0||t.weeks!=null&&t.weeks!==0||t.days!=null&&t.days!==0){let a=zr(Et(e),{years:t.years,months:t.months,weeks:t.weeks,days:t.days});r=De(a,e.timeZone)}else r=St(e)-e.offset;r+=t.milliseconds||0,r+=(t.seconds||0)*1e3,r+=(t.minutes||0)*6e4,r+=(t.hours||0)*36e5;let n=Ve(r,e.timeZone);return Ie(n,e.calendar)}function Kp(e,t){return ys(e,ds(t))}function Gp(e,t,r,n){switch(t){case"hour":{let a=0,o=23;if(n?.hourCycle===12){let d=e.hour>=12;a=d?12:0,o=d?23:11}let i=Et(e),s=Ie(kr(i,{hour:a}),new wt),l=[De(s,e.timeZone,"earlier"),De(s,e.timeZone,"later")].filter(d=>Ve(d,e.timeZone).day===s.day)[0],u=Ie(kr(i,{hour:o}),new wt),c=[De(u,e.timeZone,"earlier"),De(u,e.timeZone,"later")].filter(d=>Ve(d,e.timeZone).day===u.day).pop(),h=St(e)-e.offset,f=Math.floor(h/Dt),p=h%Dt;return h=Le(f,r,Math.floor(l/Dt),Math.floor(c/Dt),n?.round)*Dt+p,Ie(Ve(h,e.timeZone),e.calendar)}case"minute":case"second":case"millisecond":return hs(e,t,r,n);case"era":case"year":case"month":case"day":{let a=ha(Et(e),t,r,n),o=De(a,e.timeZone);return Ie(Ve(o,e.timeZone),e.calendar)}default:throw new Error("Unsupported field "+t)}}function Jp(e,t,r){let n=Et(e),a=kr(pa(n,t),t);if(a.compare(n)===0)return e;let o=De(a,e.timeZone,r);return Ie(Ve(o,e.timeZone),e.calendar)}const Yp=/^(\d{4})-(\d{2})-(\d{2})(?:T(\d{2}))?(?::(\d{2}))?(?::(\d{2}))?(\.\d+)?(?:(?:([+-]\d{2})(?::?(\d{2}))?)|Z)$/;function Zp(e,t){let r=e.match(Yp);if(!r)throw new Error("Invalid ISO 8601 date time string: "+e);let n=new Rt(Me(r[1],1,9999),Me(r[2],1,12),1,t,0,r[4]?Me(r[4],0,23):0,r[5]?Me(r[5],0,59):0,r[6]?Me(r[6],0,59):0,r[7]?Me(r[7],0,1/0)*1e3:0);n.day=Me(r[3],0,n.calendar.getDaysInMonth(n));var a;return r[8]&&(n.offset=Me(r[8],-23,23)*36e5+Me((a=r[9])!==null&&a!==void 0?a:"0",0,59)*6e4),cs(n,t)}function Me(e,t,r){let n=Number(e);if(n<t||n>r)throw new RangeError(`Value out of range: ${t} <= ${n} <= ${r}`);return n}function Xp(e){return`${String(e.hour).padStart(2,"0")}:${String(e.minute).padStart(2,"0")}:${String(e.second).padStart(2,"0")}${e.millisecond?String(e.millisecond/1e3).slice(1):""}`}function vs(e){let t=Ie(e,new wt);return`${String(t.year).padStart(4,"0")}-${String(t.month).padStart(2,"0")}-${String(t.day).padStart(2,"0")}`}function ms(e){return`${vs(e)}T${Xp(e)}`}function eh(e){let t=Math.sign(e)<0?"-":"+";e=Math.abs(e);let r=Math.floor(e/36e5),n=e%36e5/6e4;return`${t}${String(r).padStart(2,"0")}:${String(n).padStart(2,"0")}`}function th(e){return`${ms(e)}${eh(e.offset)}[${e.timeZone}]`}function rh(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function ya(e,t,r){rh(e,t),t.set(e,r)}function va(e){let t=typeof e[0]=="object"?e.shift():new wt,r;if(typeof e[0]=="string")r=e.shift();else{let i=t.getEras();r=i[i.length-1]}let n=e.shift(),a=e.shift(),o=e.shift();return[t,r,n,a,o]}var nh=new WeakMap;class er{copy(){return this.era?new er(this.calendar,this.era,this.year,this.month,this.day):new er(this.calendar,this.year,this.month,this.day)}add(t){return zr(this,t)}subtract(t){return ps(this,t)}set(t){return pa(this,t)}cycle(t,r,n){return ha(this,t,r,n)}toDate(t){return ls(this,t)}toString(){return vs(this)}compare(t){return is(this,t)}constructor(...t){ya(this,nh,{writable:!0,value:void 0});let[r,n,a,o,i]=va(t);this.calendar=r,this.era=n,this.year=a,this.month=o,this.day=i,it(this)}}var ah=new WeakMap;class _r{copy(){return this.era?new _r(this.calendar,this.era,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond):new _r(this.calendar,this.year,this.month,this.day,this.hour,this.minute,this.second,this.millisecond)}add(t){return zr(this,t)}subtract(t){return ps(this,t)}set(t){return pa(kr(this,t),t)}cycle(t,r,n){switch(t){case"era":case"year":case"month":case"day":return ha(this,t,r,n);default:return hs(this,t,r,n)}}toDate(t,r){return ls(this,t,r)}toString(){return ms(this)}compare(t){let r=is(this,t);return r===0?Fp(this,Et(t)):r}constructor(...t){ya(this,ah,{writable:!0,value:void 0});let[r,n,a,o,i]=va(t);this.calendar=r,this.era=n,this.year=a,this.month=o,this.day=i,this.hour=t.shift()||0,this.minute=t.shift()||0,this.second=t.shift()||0,this.millisecond=t.shift()||0,it(this)}}var oh=new WeakMap;class Rt{copy(){return this.era?new Rt(this.calendar,this.era,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond):new Rt(this.calendar,this.year,this.month,this.day,this.timeZone,this.offset,this.hour,this.minute,this.second,this.millisecond)}add(t){return ys(this,t)}subtract(t){return Kp(this,t)}set(t,r){return Jp(this,t,r)}cycle(t,r,n){return Gp(this,t,r,n)}toDate(){return Qp(this)}toString(){return th(this)}toAbsoluteString(){return this.toDate().toISOString()}compare(t){return this.toDate().getTime()-Bp(t,this.timeZone).toDate().getTime()}constructor(...t){ya(this,oh,{writable:!0,value:void 0});let[r,n,a,o,i]=va(t),s=t.shift(),l=t.shift();this.calendar=r,this.era=n,this.year=a,this.month=o,this.day=i,this.timeZone=s,this.offset=l,this.hour=t.shift()||0,this.minute=t.shift()||0,this.second=t.shift()||0,this.millisecond=t.shift()||0,it(this)}}const[Zh,En]=Fe("History",{history:[],currentLocationIndex:0,currentLocation:"",canGoBack:!1,pushState:()=>{throw new Error("You must use the `HistoryProvider` to access the `pushState` function.")},goBack:()=>{throw new Error("You must use the `HistoryProvider` to access the `goBack` function.")}});C.forwardRef(({disabled:e},t)=>{const{formatMessage:r}=Ke(),n=En("BackButton",s=>s.canGoBack),a=En("BackButton",s=>s.goBack),o=En("BackButton",s=>s.history),i=s=>{s.preventDefault(),a()};return M.jsx(Ps,{ref:t,tag:Is,to:o.at(-1)??"",onClick:i,disabled:e||!n,"aria-disabled":e||!n,startIcon:M.jsx(As,{}),children:r({id:"global.back",defaultMessage:"Back"})})});new xs({defaultOptions:{queries:{refetchOnWindowFocus:!1}}});ye($s)`
  & > div:first-child {
    display: none;
  }

  & > button {
    display: none;
  }
`;ye(ht)`
  word-break: break-all;
  color: ${({theme:e})=>e.colors.danger600};
`;const ih=({children:e})=>M.jsx(me,{paddingLeft:10,paddingRight:10,children:e}),sh={S:180,M:250};ye(me)`
  display: grid;
  grid-template-columns: repeat(
    auto-fit,
    minmax(${({$size:e})=>`${sh[e]}px`}, 1fr)
  );
  grid-gap: ${({theme:e})=>e.spaces[4]};
`;const zo=C.forwardRef(({navigationAction:e,primaryAction:t,secondaryAction:r,subtitle:n,title:a,sticky:o,width:i,...s},l)=>{const u=typeof n=="string";return o?M.jsx(me,{paddingLeft:6,paddingRight:6,paddingTop:3,paddingBottom:3,position:"fixed",top:0,right:0,background:"neutral0",shadow:"tableShadow",width:`${i}px`,zIndex:1,"data-strapi-header-sticky":!0,children:M.jsxs(He,{justifyContent:"space-between",children:[M.jsxs(He,{children:[e&&M.jsx(me,{paddingRight:3,children:e}),M.jsxs(me,{children:[M.jsx(ht,{variant:"beta",tag:"h1",...s,children:a}),u?M.jsx(ht,{variant:"pi",textColor:"neutral600",children:n}):n]}),r?M.jsx(me,{paddingLeft:4,children:r}):null]}),M.jsx(He,{children:t?M.jsx(me,{paddingLeft:2,children:t}):void 0})]})}):M.jsxs(me,{ref:l,paddingLeft:10,paddingRight:10,paddingBottom:8,paddingTop:e?6:8,background:"neutral100","data-strapi-header":!0,children:[e?M.jsx(me,{paddingBottom:2,children:e}):null,M.jsxs(He,{justifyContent:"space-between",children:[M.jsxs(He,{minWidth:0,children:[M.jsx(ht,{tag:"h1",variant:"alpha",...s,children:a}),r?M.jsx(me,{paddingLeft:4,children:r}):null]}),t]}),u?M.jsx(ht,{variant:"epsilon",textColor:"neutral600",tag:"p",children:n}):n]})}),gs=e=>{const t=C.useRef(null),[r,n]=C.useState(null),[a,o]=uh({root:null,rootMargin:"0px",threshold:0});return lh(a,()=>{a.current&&n(a.current.getBoundingClientRect())}),C.useEffect(()=>{t.current&&n(t.current.getBoundingClientRect())},[t]),M.jsxs(M.Fragment,{children:[M.jsx("div",{style:{height:r?.height},ref:a,children:o&&M.jsx(zo,{ref:t,...e})}),!o&&M.jsx(zo,{...e,sticky:!0,width:r?.width})]})};gs.displayName="HeaderLayout";const uh=e=>{const t=C.useRef(null),[r,n]=C.useState(!0),a=([o])=>{n(o.isIntersecting)};return C.useEffect(()=>{const o=t.current,i=new IntersectionObserver(a,e);return o&&i.observe(t.current),()=>{o&&i.disconnect()}},[t,e]),[t,r]},lh=(e,t)=>{const r=Ko(t);C.useLayoutEffect(()=>{const n=new ResizeObserver(r);return Array.isArray(e)?e.forEach(a=>{a.current&&n.observe(a.current)}):e.current&&n.observe(e.current),()=>{n.disconnect()}},[e,r])},ch=ye(me)`
  display: grid;
  grid-template-columns: ${({$hasSideNav:e})=>e?"auto 1fr":"1fr"};
`,fh=ye(me)`
  overflow-x: hidden;
`,dh=({sideNav:e,children:t})=>M.jsxs(ch,{$hasSideNav:!!e,children:[e,M.jsx(fh,{paddingBottom:10,children:t})]}),Xh={Root:dh,Header:gs,Content:ih},Ce="The Form Component has not been initialised, ensure you are using this hook within a Form component",[ph,qt]=Fe("Form",{disabled:!1,errors:{},initialValues:{},isSubmitting:!1,modified:!1,addFieldRow:()=>{throw new Error(Ce)},moveFieldRow:()=>{throw new Error(Ce)},onChange:()=>{throw new Error(Ce)},removeFieldRow:()=>{throw new Error(Ce)},resetForm:()=>{throw new Error(Ce)},setErrors:()=>{throw new Error(Ce)},setValues:()=>{throw new Error(Ce)},setSubmitting:()=>{throw new Error(Ce)},validate:async()=>{throw new Error(Ce)},values:{}});C.forwardRef(({disabled:e=!1,method:t,onSubmit:r,initialErrors:n,...a},o)=>{const i=C.useRef(null),s=C.useRef(a.initialValues??{}),[l,u]=C.useReducer(vh,{errors:n??{},isSubmitting:!1,values:a.initialValues??{}});C.useEffect(()=>{Ht(s.current,a.initialValues)||(s.current=a.initialValues??{},u({type:"SET_INITIAL_VALUES",payload:a.initialValues??{}}))},[a.initialValues]);const c=C.useCallback(y=>{u({type:"SET_ERRORS",payload:y})},[]),h=C.useCallback(y=>{u({type:"SET_VALUES",payload:y})},[]);C.useEffect(()=>{if(Object.keys(l.errors).length===0)return;const y=setTimeout(()=>{const[b]=i.current.querySelectorAll("[data-strapi-field-error]");if(b){const g=b.getAttribute("id"),T=i.current.querySelector(`[aria-describedby="${g}"]`);T&&T instanceof HTMLElement&&T.focus()}});return()=>clearTimeout(y)},[l.errors]);const f=C.useCallback(async(y=!0,b={})=>{if(c({}),!a.validationSchema&&!a.validate)return{data:l.values};try{let g;if(a.validationSchema)g=await a.validationSchema.validate(l.values,{abortEarly:!1});else if(a.validate)g=await a.validate(l.values,b);else throw new Error("No validation schema or validate function provided");return{data:g}}catch(g){if(hh(g)){const T=yh(g);return y&&c(T),{errors:T}}else throw console.warn("Warning: An unhandled error was caught during validation in <Form validationSchema />",g),g}},[a,c,l.values]),p=async y=>{if(y.stopPropagation(),y.preventDefault(),!!r){u({type:"SUBMIT_ATTEMPT"});try{const{data:b,errors:g}=await f();if(g)throw c(g),new Error("Submission failed");await r(b,{setErrors:c,setValues:h,resetForm:R}),u({type:"SUBMIT_SUCCESS"})}catch(b){if(u({type:"SUBMIT_FAILURE"}),b instanceof Error&&b.message==="Submission failed")return}}},d=C.useMemo(()=>!Ht(s.current,l.values),[l.values]),v=Ko((y,b)=>{if(typeof y=="string"){u({type:"SET_FIELD_VALUE",payload:{field:y,value:b}});return}const g=y.target||y.currentTarget,{type:T,name:E,id:I,value:x,options:$,multiple:k}=g,_=E||I;_||console.warn("`onChange` was called with an event, but you forgot to pass a `name` or `id'` attribute to your input. The field to update cannot be determined");let q;if(/number|range/.test(T)){const L=parseFloat(x);q=isNaN(L)?"":L}else/checkbox/.test(T)?q=!Ae(l.values,_):$&&k?q=Array.from($).filter(L=>L.selected).map(L=>L.value):x===""?q=null:q=x;_&&u({type:"SET_FIELD_VALUE",payload:{field:_,value:q}})}),m=C.useCallback((y,b,g)=>{u({type:"ADD_FIELD_ROW",payload:{field:y,value:b,addAtIndex:g}})},[]),O=C.useCallback((y,b)=>{u({type:"REMOVE_FIELD_ROW",payload:{field:y,removeAtIndex:b}})},[]),A=C.useCallback((y,b,g)=>{u({type:"MOVE_FIELD_ROW",payload:{field:y,fromIndex:b,toIndex:g}})},[]),R=C.useCallback(()=>{u({type:"RESET_FORM",payload:{errors:{},isSubmitting:!1,values:s.current}})},[]),w=C.useCallback(y=>{u({type:"SET_ISSUBMITTING",payload:y})},[]),S=ve(i,o);return M.jsx("form",{ref:S,method:t,noValidate:!0,onSubmit:p,children:M.jsx(ph,{disabled:e,onChange:v,initialValues:s.current,modified:d,addFieldRow:m,moveFieldRow:A,removeFieldRow:O,resetForm:R,setErrors:c,setValues:h,setSubmitting:w,validate:f,...l,children:typeof a.children=="function"?a.children({modified:d,disabled:e,onChange:v,...l,setErrors:c,resetForm:R}):a.children})})});const hh=e=>typeof e=="object"&&e!==null&&"name"in e&&typeof e.name=="string"&&e.name==="ValidationError",yh=e=>{let t={};if(e.inner){if(e.inner.length===0)return ze(t,e.path,e.message);for(const r of e.inner)Ae(t,r.path)||(t=ze(t,r.path,r.message))}return t},vh=(e,t)=>Ot(e,r=>{switch(t.type){case"SET_INITIAL_VALUES":r.values=t.payload;break;case"SET_VALUES":r.values=t.payload;break;case"SUBMIT_ATTEMPT":r.isSubmitting=!0;break;case"SUBMIT_FAILURE":r.isSubmitting=!1;break;case"SUBMIT_SUCCESS":r.isSubmitting=!1;break;case"SET_FIELD_VALUE":r.values=ze(e.values,t.payload.field,t.payload.value);break;case"ADD_FIELD_ROW":{const n=Ae(e.values,t.payload.field,[]);let a=t.payload.addAtIndex;a===void 0?a=n.length:a<0&&(a=0);const[o]=jo(n.at(a-1)?.__temp_key__,n.at(a)?.__temp_key__);r.values=ze(e.values,t.payload.field,ze(n,a.toString(),{...t.payload.value,__temp_key__:o}));break}case"MOVE_FIELD_ROW":{const{field:n,fromIndex:a,toIndex:o}=t.payload,i=[...Ae(e.values,n,[])],s=i[a],l=a>o?i[o-1]?.__temp_key__:i[o]?.__temp_key__,u=a>o?i[o]?.__temp_key__:i[o+1]?.__temp_key__,[c]=jo(l,u);i.splice(a,1),i.splice(o,0,{...s,__temp_key__:c}),r.values=ze(e.values,n,i);break}case"REMOVE_FIELD_ROW":{const n=Ae(e.values,t.payload.field,[]);let a=t.payload.removeAtIndex;a===void 0?a=n.length-1:a<0&&(a=0);const o=ze(n,a.toString(),void 0).filter(i=>i);r.values=ze(e.values,t.payload.field,o.length>0?o:[]);break}case"SET_ERRORS":Ht(e.errors,t.payload)||(r.errors=t.payload);break;case"SET_ISSUBMITTING":r.isSubmitting=t.payload;break;case"RESET_FORM":r.values=t.payload.values,r.errors=t.payload.errors,r.isSubmitting=t.payload.isSubmitting;break}}),be=e=>{const{formatMessage:t}=Ke(),r=qt("useField",s=>Ae(s.initialValues,e)),n=qt("useField",s=>Ae(s.values,e)),a=qt("useField",s=>s.onChange),o=qt("useField",s=>Ae(s.errors,e)),i=qt("useField",s=>{const l=Ae(s.errors,e);if(Ho(l)){const{values:u,...c}=l;return t(c,u)}return l});return{initialValue:r,rawError:o,error:Ho(i)?t({id:i.id,defaultMessage:i.defaultMessage},i.values):typeof i=="string"?i:void 0,onChange:a,value:n}},Ho=e=>typeof e=="object"&&e!==null&&!Array.isArray(e)&&"id"in e&&"defaultMessage"in e,we=e=>{const{search:t}=Jo(),r=C.useMemo(()=>new URLSearchParams(t),[t]),[n,a]=C.useState(null);return C.useEffect(()=>{r.has("field")&&r.get("field")===e&&n&&(n.focus(),n.scrollIntoView({block:"center"}))},[r,e,n]),a},mh=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,...o},i)=>{const{formatMessage:s}=Ke(),l=be(e),u=we(e),c=ve(i,u);return M.jsxs(F.Root,{error:l.error,name:e,hint:n,required:t,maxWidth:"320px",children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(Ms,{ref:c,checked:l.value===null?null:l.value||!1,offLabel:s({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"False"}),onLabel:s({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"True"}),onChange:l.onChange,...o}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),gh=C.memo(mh),bh=C.forwardRef(({name:e,required:t,label:r,hint:n,type:a,...o},i)=>{const s=be(e),l=we(e),u=ve(i,l);return M.jsxs(F.Root,{error:s.error,name:e,hint:n,required:t,children:[M.jsx(Cs,{onCheckedChange:c=>s.onChange(e,!!c),ref:u,checked:s.value,...o,children:r||o["aria-label"]}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),wh=C.memo(bh),Sh=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,type:o,...i},s)=>{const{formatMessage:l}=Ke(),u=be(e),c=we(e),h=ve(s,c),f=typeof u.value=="string"?new Date(u.value):u.value;return M.jsxs(F.Root,{error:u.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(ks,{ref:h,clearLabel:l({id:"clearLabel",defaultMessage:"Clear"}),onChange:p=>{u.onChange(e,p?Wo(p).toISOString():null)},onClear:()=>u.onChange(e,null),value:f&&Wo(f),...i}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),Wo=e=>{const t=e.toISOString(),r=da(),n=Zp(t,r);return Up(n).toDate("UTC")},Eh=C.memo(Sh),Rh=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,...o},i)=>{const{formatMessage:s}=Ke(),l=be(e),u=we(e),c=ve(i,u),h=typeof l.value=="string"?new Date(l.value):l.value;return M.jsxs(F.Root,{error:l.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(_s,{ref:c,clearLabel:s({id:"clearLabel",defaultMessage:"Clear"}),onChange:f=>{l.onChange(e,f?f.toISOString():null)},onClear:()=>l.onChange(e,null),value:h,...o}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),Oh=C.memo(Rh),Th=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,...o},i)=>{const s=be(e),l=we(e),u=ve(i,l);return M.jsxs(F.Root,{error:s.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(Dr,{ref:u,autoComplete:"email",onChange:s.onChange,value:s.value,...o,type:"email"}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),Ph=C.memo(Th),Ah=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,options:o=[],...i},s)=>{const l=be(e),u=we(e),c=ve(s,u);return M.jsxs(F.Root,{error:l.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(Ds,{ref:c,onChange:h=>{l.onChange(e,h)},value:l.value,...i,children:o.map(({value:h,label:f,disabled:p,hidden:d})=>M.jsx(qs,{value:h,disabled:p,hidden:d,children:f??h},h))}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),Ih=C.memo(Ah),xh=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,...o},i)=>{const s=be(e),l=we(e),u=ve(i,l);return M.jsxs(F.Root,{error:s.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(Ls,{ref:u,value:typeof s.value=="object"?JSON.stringify(s.value,null,2):s.value,onChange:c=>{const h=t&&!c.length?null:c;s.onChange(e,h)},minHeight:"25.2rem",maxHeight:"50.4rem",...o}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),$h=C.memo(xh),Mh=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,type:o,...i},s)=>{const l=be(e),u=we(e),c=ve(s,u);return M.jsxs(F.Root,{error:l.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(Fs,{ref:c,onValueChange:h=>{l.onChange(e,h)},step:o==="float"||o=="decimal"?.01:1,value:l.value,...i}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),Ch=C.memo(Mh),kh=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,...o},i)=>{const[s,l]=C.useState(!1),{formatMessage:u}=Ke(),c=be(e),h=we(e),f=ve(i,h);return M.jsxs(F.Root,{error:c.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(Dr,{ref:f,autoComplete:"password",endAction:M.jsx(F.Action,{label:u({id:"Auth.form.password.show-password",defaultMessage:"Show password"}),onClick:()=>{l(p=>!p)},children:s?M.jsx(Ns,{fill:"neutral500"}):M.jsx(js,{fill:"neutral500"})}),onChange:c.onChange,value:c.value,...o,type:s?"text":"password"}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),_h=C.memo(kh),Dh=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,...o},i)=>{const s=be(e),l=we(e),u=ve(i,l);return M.jsxs(F.Root,{error:s.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(Dr,{ref:u,onChange:s.onChange,value:s.value??"",...o}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),qh=C.memo(Dh),Lh=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,...o},i)=>{const s=be(e),l=we(e),u=ve(i,l);return M.jsxs(F.Root,{error:s.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(Us,{ref:u,onChange:s.onChange,value:s.value??"",...o}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),Fh=C.memo(Lh),Nh=C.forwardRef(({name:e,required:t,label:r,hint:n,labelAction:a,...o},i)=>{const{formatMessage:s}=Ke(),l=be(e),u=we(e),c=ve(i,u);return M.jsxs(F.Root,{error:l.error,name:e,hint:n,required:t,children:[M.jsx(F.Label,{action:a,children:r}),M.jsx(Bs,{ref:c,clearLabel:s({id:"clearLabel",defaultMessage:"Clear"}),onChange:h=>{l.onChange(e,`${h}:00.000`)},onClear:()=>l.onChange(e,void 0),value:l.value??"",...o}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})}),jh=C.memo(Nh),Uh=C.memo(C.forwardRef((e,t)=>{switch(e.type){case"biginteger":case"timestamp":case"string":case"uid":return M.jsx(qh,{ref:t,...e});case"boolean":return M.jsx(gh,{ref:t,...e});case"checkbox":return M.jsx(wh,{ref:t,...e});case"datetime":return M.jsx(Oh,{ref:t,...e});case"date":return M.jsx(Eh,{ref:t,...e});case"decimal":case"float":case"integer":return M.jsx(Ch,{ref:t,...e});case"json":return M.jsx($h,{ref:t,...e});case"email":return M.jsx(Ph,{ref:t,...e});case"enumeration":return M.jsx(Ih,{ref:t,...e});case"password":return M.jsx(_h,{ref:t,...e});case"text":return M.jsx(Fh,{ref:t,...e});case"time":return M.jsx(jh,{ref:t,...e});default:return M.jsx(Bh,{ref:t,...e})}})),Bh=C.forwardRef(({label:e,hint:t,name:r,required:n,type:a,labelAction:o},i)=>{const{error:s}=be(r),l=we(r),u=ve(i,l);return M.jsxs(F.Root,{error:s,name:r,hint:t,required:n,children:[M.jsx(F.Label,{action:o,children:e}),M.jsx(Dr,{ref:u,disabled:!0,placeholder:`Unsupported field type: ${a}`,required:n,type:"text",value:""}),M.jsx(F.Hint,{}),M.jsx(F.Error,{})]})});C.memo(Uh);ye.img`
  height: 7.2rem;
`;ye(me)`
  margin: 0 auto;
  width: 552px;
`;ye(He)`
  flex-direction: column;
`;const se={email:{id:"components.Input.error.validation.email"},lowercase:{id:"components.Input.error.validation.lowercase"},minLength:{id:"components.Input.error.validation.minLength"},required:{id:"components.Input.error.validation.required",defaultMessage:"This value is required."}};qr().shape({email:de().nullable().email({id:se.email.id,defaultMessage:"Not a valid email"}).required(se.required),password:de().required(se.required).nullable(),rememberMe:Qs().nullable()});ye(F.Root)`
  height: 3.2rem;
  width: 3.2rem;

  > label,
  ~ input {
    display: block;
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
  }

  > label {
    color: inherit;
    cursor: pointer;
    padding: ${({theme:e})=>e.spaces[2]};
    text-align: center;
    vertical-align: middle;
  }

  &:hover,
  &:focus-within {
    background-color: ${({theme:e})=>e.colors.neutral0};
  }

  &:active,
  &.selected {
    color: ${({theme:e})=>e.colors.primary700};
    background-color: ${({theme:e})=>e.colors.neutral0};
    border-color: ${({theme:e})=>e.colors.primary700};
  }
`;[...Array(11).keys()];qr().shape({firstname:de().trim().required(se.required).nullable(),lastname:de().nullable(),password:de().min(8,{id:se.minLength.id,defaultMessage:"Password must be at least 8 characters",values:{min:8}}).matches(/[a-z]/,{message:{id:"components.Input.error.contain.lowercase",defaultMessage:"Password must contain at least 1 lowercase letter"}}).matches(/[A-Z]/,{message:{id:"components.Input.error.contain.uppercase",defaultMessage:"Password must contain at least 1 uppercase letter"}}).matches(/\d/,{message:{id:"components.Input.error.contain.number",defaultMessage:"Password must contain at least 1 number"}}).required({id:se.required.id,defaultMessage:"Password is required"}).nullable(),confirmPassword:de().required({id:se.required.id,defaultMessage:"Confirm password is required"}).oneOf([Wn("password"),null],{id:"components.Input.error.password.noMatch",defaultMessage:"Passwords must match"}).nullable(),registrationToken:de().required({id:se.required.id,defaultMessage:"Registration token is required"})});qr().shape({firstname:de().trim().required({id:se.required.id,defaultMessage:"Firstname is required"}).nullable(),lastname:de().nullable(),password:de().min(8,{id:se.minLength.id,defaultMessage:"Password must be at least 8 characters",values:{min:8}}).matches(/[a-z]/,{message:{id:"components.Input.error.contain.lowercase",defaultMessage:"Password must contain at least 1 lowercase letter"}}).matches(/[A-Z]/,{message:{id:"components.Input.error.contain.uppercase",defaultMessage:"Password must contain at least 1 uppercase letter"}}).matches(/\d/,{message:{id:"components.Input.error.contain.number",defaultMessage:"Password must contain at least 1 number"}}).required({id:se.required.id,defaultMessage:"Password is required"}).nullable(),confirmPassword:de().required({id:se.required,defaultMessage:"Confirm password is required"}).nullable().oneOf([Wn("password"),null],{id:"components.Input.error.password.noMatch",defaultMessage:"Passwords must match"}),email:de().email({id:se.email.id,defaultMessage:"Not a valid email"}).strict().lowercase({id:se.lowercase.id,defaultMessage:"Email must be lowercase"}).required({id:se.required.id,defaultMessage:"Email is required"}).nullable()});ye.a`
  color: ${({theme:e})=>e.colors.primary600};
`;qr().shape({password:de().min(8,{id:se.minLength.id,defaultMessage:"Password must be at least 8 characters",values:{min:8}}).matches(/[a-z]/,{message:{id:"components.Input.error.contain.lowercase",defaultMessage:"Password must contain at least 1 lowercase letter"}}).matches(/[A-Z]/,{message:{id:"components.Input.error.contain.uppercase",defaultMessage:"Password must contain at least 1 uppercase letter"}}).matches(/\d/,{message:{id:"components.Input.error.contain.number",defaultMessage:"Password must contain at least 1 number"}}).required({id:se.required.id,defaultMessage:"Password is required"}).nullable(),confirmPassword:de().required({id:se.required.id,defaultMessage:"Confirm password is required"}).oneOf([Wn("password"),null],{id:"components.Input.error.password.noMatch",defaultMessage:"Passwords must match"}).nullable()});Zt.reducerPath+"",Zt.reducer;ye(zs)`
  width: 24px;
  height: 24px;

  path {
    fill: ${({theme:e})=>e.colors.danger600};
  }
`;C.memo(({description:e,id:t,props:r,update:n})=>{const a=e(r);return zh(()=>(n(t,a),()=>{n(t,null)}),a),null},(e,t)=>Ht(e.props,t.props));const Qh=e=>{const t=C.useRef(void 0);return Ht(e,t.current)||(t.current=e),[t.current]},zh=(e,t)=>{C.useEffect(e,Qh(t))},[ey,Hh]=Fe("Filters");C.forwardRef(({label:e},t)=>{const{formatMessage:r}=Ke(),n=Hh("Trigger",({disabled:a})=>a);return M.jsx(Hs.Trigger,{children:M.jsx(Ws,{variant:"tertiary",ref:t,startIcon:M.jsx(Vs,{}),size:"S",disabled:n,children:e||r({id:"app.utils.filters",defaultMessage:"Filters"})})})});const[Wh,ty]=Fe("Pagination");C.forwardRef(({children:e,defaultPageSize:t=10,pageCount:r=0,defaultPage:n=1,onPageSizeChange:a,total:o=0},i)=>{const[{query:s},l]=Pp({pageSize:t.toString(),page:n.toString()}),u=c=>{l({pageSize:c,page:"1"}),a&&a(c)};return M.jsx(He,{ref:i,paddingTop:4,paddingBottom:4,alignItems:"flex-end",justifyContent:"space-between",children:M.jsx(Wh,{currentQuery:s,page:s.page,pageSize:s.pageSize,pageCount:r.toString(),setPageSize:u,total:o,children:e})})});Fe("Table");ye(Gs)`
  transform: ${({$isUp:e})=>`rotate(${e?"180":"0"}deg)`};
`;ye(He)`
  margin-right: ${({theme:e})=>e.spaces[6]};

  svg {
    width: 3.2rem;
    height: 3.2rem;
  }
`;ye(ht)`
  color: ${({theme:e})=>e.colors.neutral800};
  word-break: break-all;
`;Zt.enhanceEndpoints({addTagTypes:["LicenseLimits","User","Role","RolePermissions"]}).injectEndpoints({endpoints:e=>({createUser:e.mutation({query:t=>({url:"/admin/users",method:"POST",data:t}),transformResponse:t=>t.data,invalidatesTags:["LicenseLimits",{type:"User",id:"LIST"}]}),updateUser:e.mutation({query:({id:t,...r})=>({url:`/admin/users/${t}`,method:"PUT",data:r}),invalidatesTags:(t,r,{id:n})=>[{type:"User",id:n},{type:"User",id:"LIST"}]}),getUsers:e.query({query:({id:t,...r}={})=>({url:`/admin/users/${t??""}`,method:"GET",config:{params:r}}),transformResponse:t=>{let r=[];return t.data&&("results"in t.data?Array.isArray(t.data.results)&&(r=t.data.results):r=[t.data]),{users:r,pagination:"pagination"in t.data?t.data.pagination:null}},providesTags:(t,r,n)=>typeof n=="object"&&"id"in n?[{type:"User",id:n.id}]:[...t?.users.map(({id:a})=>({type:"User",id:a}))??[],{type:"User",id:"LIST"}]}),deleteManyUsers:e.mutation({query:t=>({url:"/admin/users/batch-delete",method:"POST",data:t}),transformResponse:t=>t.data,invalidatesTags:["LicenseLimits",{type:"User",id:"LIST"}]}),createRole:e.mutation({query:t=>({url:"/admin/roles",method:"POST",data:t}),transformResponse:t=>t.data,invalidatesTags:[{type:"Role",id:"LIST"}]}),getRoles:e.query({query:({id:t,...r}={})=>({url:`/admin/roles/${t??""}`,method:"GET",config:{params:r}}),transformResponse:t=>{let r=[];return t.data&&(Array.isArray(t.data)?r=t.data:r=[t.data]),r},providesTags:(t,r,n)=>typeof n=="object"&&"id"in n?[{type:"Role",id:n.id}]:[...t?.map(({id:a})=>({type:"Role",id:a}))??[],{type:"Role",id:"LIST"}]}),updateRole:e.mutation({query:({id:t,...r})=>({url:`/admin/roles/${t}`,method:"PUT",data:r}),transformResponse:t=>t.data,invalidatesTags:(t,r,{id:n})=>[{type:"Role",id:n}]}),getRolePermissions:e.query({query:({id:t,...r})=>({url:`/admin/roles/${t}/permissions`,method:"GET",config:{params:r}}),transformResponse:t=>t.data,providesTags:(t,r,{id:n})=>[{type:"RolePermissions",id:n}]}),updateRolePermissions:e.mutation({query:({id:t,...r})=>({url:`/admin/roles/${t}/permissions`,method:"PUT",data:r}),transformResponse:t=>t.data,invalidatesTags:(t,r,{id:n})=>[{type:"RolePermissions",id:n}]}),getRolePermissionLayout:e.query({query:t=>({url:"/admin/permissions",method:"GET",config:{params:t}}),transformResponse:t=>t.data})}),overrideExisting:!1});export{Xh as L,Yh as u};
