{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/pages/ListView/components/Filters.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/CellValue.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/Components.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/Media.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/Relations.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/TableCells/CellContent.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/components/ViewSettingsMenu.tsx", "../../../@strapi/content-manager/admin/src/pages/ListView/ListViewPage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  Filters,\n  useField,\n  useAuth,\n  useTracking,\n  useQueryParams,\n  useAdminUsers,\n} from '@strapi/admin/strapi-admin';\nimport { Combobox, ComboboxOption, useCollator } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { CREATOR_FIELDS } from '../../../constants/attributes';\nimport { useContentTypeSchema } from '../../../hooks/useContentTypeSchema';\nimport { useDebounce } from '../../../hooks/useDebounce';\nimport { Schema } from '../../../hooks/useDocument';\nimport { useGetContentTypeConfigurationQuery } from '../../../services/contentTypes';\nimport { getMainField } from '../../../utils/attributes';\nimport { getDisplayName } from '../../../utils/users';\n\n/**\n * If new attributes are added, this list needs to be updated.\n */\nconst NOT_ALLOWED_FILTERS = [\n  'json',\n  'component',\n  'media',\n  'richtext',\n  'dynamiczone',\n  'password',\n  'blocks',\n];\nconst DEFAULT_ALLOWED_FILTERS = ['createdAt', 'updatedAt'];\nconst USER_FILTER_ATTRIBUTES = [...CREATOR_FIELDS, 'strapi_assignee'];\n\n/* -------------------------------------------------------------------------------------------------\n * Filters\n * -----------------------------------------------------------------------------------------------*/\ninterface FiltersProps {\n  disabled?: boolean;\n  schema: Schema;\n}\n\nconst FiltersImpl = ({ disabled, schema }: FiltersProps) => {\n  const { attributes, uid: model, options } = schema;\n  const { formatMessage, locale } = useIntl();\n  const { trackUsage } = useTracking();\n  const allPermissions = useAuth('FiltersImpl', (state) => state.permissions);\n  const [{ query }] = useQueryParams<Filters.Query>();\n  const { schemas } = useContentTypeSchema();\n\n  const canReadAdminUsers = React.useMemo(\n    () =>\n      allPermissions.filter(\n        (permission) => permission.action === 'admin::users.read' && permission.subject === null\n      ).length > 0,\n    [allPermissions]\n  );\n\n  const selectedUserIds = (query?.filters?.$and ?? []).reduce<string[]>((acc, filter) => {\n    const [key, value] = Object.entries(filter)[0];\n    if (typeof value.id !== 'object') {\n      return acc;\n    }\n\n    const id = value.id.$eq || value.id.$ne;\n\n    if (id && USER_FILTER_ATTRIBUTES.includes(key) && !acc.includes(id)) {\n      acc.push(id);\n    }\n\n    return acc;\n  }, []);\n\n  const { data: userData, isLoading: isLoadingAdminUsers } = useAdminUsers(\n    { filters: { id: { $in: selectedUserIds } } },\n    {\n      // fetch the list of admin users only if the filter contains users and the\n      // current user has permissions to display users\n      skip: selectedUserIds.length === 0 || !canReadAdminUsers,\n    }\n  );\n\n  const { users = [] } = userData ?? {};\n\n  const { metadata } = useGetContentTypeConfigurationQuery(model, {\n    selectFromResult: ({ data }) => ({ metadata: data?.contentType.metadatas ?? {} }),\n  });\n\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const displayedFilters = React.useMemo(() => {\n    const [{ properties: { fields = [] } = { fields: [] } }] = allPermissions.filter(\n      (permission) =>\n        permission.action === 'plugin::content-manager.explorer.read' &&\n        permission.subject === model\n    );\n\n    const allowedFields = fields.filter((field) => {\n      const attribute = attributes[field] ?? {};\n\n      return attribute.type && !NOT_ALLOWED_FILTERS.includes(attribute.type);\n    });\n\n    return (\n      [\n        'id',\n        ...allowedFields,\n        ...DEFAULT_ALLOWED_FILTERS,\n        ...(canReadAdminUsers ? CREATOR_FIELDS : []),\n      ]\n        .map((name) => {\n          const attribute = attributes[name];\n\n          if (NOT_ALLOWED_FILTERS.includes(attribute.type)) {\n            return null;\n          }\n\n          const { mainField: mainFieldName = '', label } = metadata[name].list;\n\n          let filter: Filters.Filter = {\n            name,\n            label: label ?? '',\n            mainField: getMainField(attribute, mainFieldName, { schemas, components: {} }),\n            // @ts-expect-error – TODO: this is filtered out above in the `allowedFields` call but TS complains, is there a better way to solve this?\n            type: attribute.type,\n          };\n\n          if (\n            attribute.type === 'relation' &&\n            'target' in attribute &&\n            attribute.target === 'admin::user'\n          ) {\n            filter = {\n              ...filter,\n              input: AdminUsersFilter,\n              options: users.map((user) => ({\n                label: getDisplayName(user),\n                value: user.id.toString(),\n              })),\n              operators: [\n                {\n                  label: formatMessage({\n                    id: 'components.FilterOptions.FILTER_TYPES.$eq',\n                    defaultMessage: 'is',\n                  }),\n                  value: '$eq',\n                },\n                {\n                  label: formatMessage({\n                    id: 'components.FilterOptions.FILTER_TYPES.$ne',\n                    defaultMessage: 'is not',\n                  }),\n                  value: '$ne',\n                },\n              ],\n              mainField: {\n                name: 'id',\n                type: 'integer',\n              },\n            };\n          }\n\n          if (attribute.type === 'enumeration') {\n            filter = {\n              ...filter,\n              options: attribute.enum.map((value) => ({\n                label: value,\n                value,\n              })),\n            };\n          }\n\n          return filter;\n        })\n        .filter(Boolean) as Filters.Filter[]\n    ).toSorted((a, b) => formatter.compare(a.label, b.label));\n  }, [\n    allPermissions,\n    canReadAdminUsers,\n    model,\n    attributes,\n    metadata,\n    schemas,\n    users,\n    formatMessage,\n    formatter,\n  ]);\n\n  const onOpenChange = (isOpen: boolean) => {\n    if (isOpen) {\n      trackUsage('willFilterEntries');\n    }\n  };\n\n  const handleFilterChange: Filters.Props['onChange'] = (data) => {\n    const attribute = attributes[data.name];\n\n    if (attribute) {\n      trackUsage('didFilterEntries', {\n        useRelation: attribute.type === 'relation',\n      });\n    }\n  };\n\n  return (\n    <Filters.Root\n      disabled={disabled}\n      options={displayedFilters}\n      onOpenChange={onOpenChange}\n      onChange={handleFilterChange}\n    >\n      <Filters.Trigger />\n      <Filters.Popover />\n      <Filters.List />\n    </Filters.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * AdminUsersFilter\n * -----------------------------------------------------------------------------------------------*/\n\nconst AdminUsersFilter = ({ name }: Filters.ValueInputProps) => {\n  const [pageSize, setPageSize] = React.useState(10);\n  const [search, setSearch] = React.useState('');\n  const { formatMessage } = useIntl();\n\n  const debouncedSearch = useDebounce(search, 300);\n\n  const { data, isLoading } = useAdminUsers({\n    pageSize,\n    _q: debouncedSearch,\n  });\n  const field = useField(name);\n\n  const handleOpenChange = (isOpen?: boolean) => {\n    if (!isOpen) {\n      setPageSize(10);\n    }\n  };\n\n  const { users = [], pagination } = data ?? {};\n  const { pageCount = 1, page = 1 } = pagination ?? {};\n\n  return (\n    <Combobox\n      value={field.value}\n      aria-label={formatMessage({\n        id: 'content-manager.components.Filters.usersSelect.label',\n        defaultMessage: 'Search and select a user to filter',\n      })}\n      onOpenChange={handleOpenChange}\n      onChange={(value) => field.onChange(name, value)}\n      loading={isLoading}\n      onLoadMore={() => setPageSize(pageSize + 10)}\n      hasMoreItems={page < pageCount}\n      onInputChange={(e: React.ChangeEvent<HTMLInputElement>) => {\n        setSearch(e.currentTarget.value);\n      }}\n    >\n      {users.map((user) => {\n        return (\n          <ComboboxOption key={user.id} value={user.id.toString()}>\n            {getDisplayName(user)}\n          </ComboboxOption>\n        );\n      })}\n    </Combobox>\n  );\n};\n\nexport { FiltersImpl as Filters };\nexport type { FiltersProps };\n", "import parseISO from 'date-fns/parseISO';\nimport toString from 'lodash/toString';\nimport { useIntl } from 'react-intl';\n\nimport type { Schema } from '@strapi/types';\n\ninterface CellValueProps {\n  type: Schema.Attribute.Kind | 'custom';\n  value: any;\n}\n\nconst CellValue = ({ type, value }: CellValueProps) => {\n  const { formatDate, formatTime, formatNumber } = useIntl();\n  let formattedValue = value;\n\n  if (type === 'date') {\n    formattedValue = formatDate(parseISO(value), { dateStyle: 'full' });\n  }\n\n  if (type === 'datetime') {\n    formattedValue = formatDate(value, { dateStyle: 'full', timeStyle: 'short' });\n  }\n\n  if (type === 'time') {\n    const [hour, minute, second] = value.split(':');\n    const date = new Date();\n    date.setHours(hour);\n    date.setMinutes(minute);\n    date.setSeconds(second);\n\n    formattedValue = formatTime(date, {\n      timeStyle: 'short',\n    });\n  }\n\n  if (['float', 'decimal'].includes(type)) {\n    formattedValue = formatNumber(value, {\n      // Should be kept in sync with the corresponding value\n      // in the design-system/NumberInput: https://github.com/strapi/design-system/blob/main/packages/strapi-design-system/src/NumberInput/NumberInput.js#L53\n      maximumFractionDigits: 20,\n    });\n  }\n\n  if (['integer', 'biginteger'].includes(type)) {\n    formattedValue = formatNumber(value, { maximumFractionDigits: 0 });\n  }\n\n  return toString(formattedValue);\n};\n\nexport { CellValue };\nexport type { CellValueProps };\n", "import { Bad<PERSON>, Tooltip, Typography, Menu } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { CellContentProps } from './CellContent';\nimport { CellValue } from './CellValue';\n\nimport type { Schema } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * SingleComponent\n * -----------------------------------------------------------------------------------------------*/\n\ninterface SingleComponentProps extends Pick<CellContentProps, 'mainField'> {\n  content: Schema.Attribute.Value<Schema.Attribute.Component<`${string}.${string}`, false>>;\n}\n\nconst SingleComponent = ({ content, mainField }: SingleComponentProps) => {\n  if (!mainField) {\n    return null;\n  }\n\n  return (\n    <Tooltip label={content[mainField.name]}>\n      <Typography maxWidth=\"25rem\" textColor=\"neutral800\" ellipsis>\n        <CellValue type={mainField.type} value={content[mainField.name]} />\n      </Typography>\n    </Tooltip>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * RepeatableComponent\n * -----------------------------------------------------------------------------------------------*/\n\ninterface RepeatableComponentProps extends Pick<CellContentProps, 'mainField'> {\n  content: Schema.Attribute.Value<Schema.Attribute.Component<`${string}.${string}`, true>>;\n}\n\nconst RepeatableComponent = ({ content, mainField }: RepeatableComponentProps) => {\n  const { formatMessage } = useIntl();\n\n  if (!mainField) {\n    return null;\n  }\n\n  return (\n    <Menu.Root>\n      <Menu.Trigger onClick={(e) => e.stopPropagation()}>\n        <Badge>{content.length}</Badge>\n        {formatMessage(\n          {\n            id: 'content-manager.containers.list.items',\n            defaultMessage: '{number, plural, =0 {items} one {item} other {items}}',\n          },\n          { number: content.length }\n        )}\n      </Menu.Trigger>\n      <Menu.Content>\n        {content.map((item) => (\n          <Menu.Item key={item.id} disabled>\n            <Typography maxWidth=\"50rem\" ellipsis>\n              <CellValue type={mainField.type} value={item[mainField.name]} />\n            </Typography>\n          </Menu.Item>\n        ))}\n      </Menu.Content>\n    </Menu.Root>\n  );\n};\n\nexport { SingleComponent, RepeatableComponent };\nexport type { SingleComponentProps, RepeatableComponentProps };\n", "import * as React from 'react';\n\nimport { <PERSON><PERSON>, Flex, Tooltip, Typography, TypographyComponent } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nimport { prefixFileUrlWithBackendUrl } from '../../../../utils/urls';\n\nimport type { Data } from '@strapi/types';\n\ninterface MediaFile {\n  id?: Data.ID;\n  alternativeText?: string;\n  ext: string;\n  formats: {\n    thumbnail?: {\n      url?: string;\n    };\n  };\n  mime: string;\n  name: string;\n  url: string;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Media\n * -----------------------------------------------------------------------------------------------*/\n\ninterface MediaSingleProps extends MediaFile {}\n\nconst getFileExtension = (ext: string) => (ext && ext[0] === '.' ? ext.substring(1) : ext);\n\nconst MediaSingle = ({ url, mime, alternativeText, name, ext, formats }: MediaSingleProps) => {\n  const fileURL = prefixFileUrlWithBackendUrl(url)!;\n\n  if (mime.includes('image')) {\n    const thumbnail = formats?.thumbnail?.url;\n    const mediaURL = prefixFileUrlWithBackendUrl(thumbnail) || fileURL;\n\n    return (\n      <Avatar.Item\n        src={mediaURL}\n        alt={alternativeText || name}\n        fallback={alternativeText || name}\n        preview\n      />\n    );\n  }\n\n  const fileExtension = getFileExtension(ext);\n  const fileName = name.length > 100 ? `${name.substring(0, 100)}...` : name;\n\n  return (\n    <Tooltip description={fileName}>\n      <FileWrapper>{fileExtension}</FileWrapper>\n    </Tooltip>\n  );\n};\n\nconst FileWrapper = ({ children }: { children: React.ReactNode }) => {\n  return (\n    <Flex\n      tag=\"span\"\n      position=\"relative\"\n      borderRadius=\"50%\"\n      width=\"26px\"\n      height=\"26px\"\n      borderColor=\"neutral200\"\n      background=\"neutral150\"\n      paddingLeft=\"1px\"\n      justifyContent=\"center\"\n      alignItems=\"center\"\n    >\n      <FileTypography variant=\"sigma\" textColor=\"neutral600\">\n        {children}\n      </FileTypography>\n    </Flex>\n  );\n};\n\nconst FileTypography = styled<TypographyComponent>(Typography)`\n  font-size: 0.9rem;\n  line-height: 0.9rem;\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * MediaMultiple\n * -----------------------------------------------------------------------------------------------*/\n\ninterface MediaMultipleProps {\n  content: MediaFile[];\n}\n\nconst MediaMultiple = ({ content }: MediaMultipleProps) => {\n  return (\n    <Avatar.Group>\n      {content.map((file, index) => {\n        const key = `${file.id}${index}`;\n\n        if (index === 3) {\n          const remainingFiles = `+${content.length - 3}`;\n\n          return <FileWrapper key={key}>{remainingFiles}</FileWrapper>;\n        }\n\n        if (index > 3) {\n          return null;\n        }\n\n        return <MediaSingle key={key} {...file} />;\n      })}\n    </Avatar.Group>\n  );\n};\n\nexport { MediaMultiple, MediaSingle };\nexport type { MediaMultipleProps, MediaSingleProps };\n", "import * as React from 'react';\n\nimport { Typography, Loader, useNotifyAT, Menu } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useDoc } from '../../../../hooks/useDocument';\nimport { useGetRelationsQuery } from '../../../../services/relations';\nimport { getRelationLabel } from '../../../../utils/relations';\nimport { getTranslation } from '../../../../utils/translations';\n\nimport type { CellContentProps } from './CellContent';\n\n/* -------------------------------------------------------------------------------------------------\n * RelationSingle\n * -----------------------------------------------------------------------------------------------*/\n\ninterface RelationSingleProps extends Pick<CellContentProps, 'mainField' | 'content'> {}\n\nconst RelationSingle = ({ mainField, content }: RelationSingleProps) => {\n  return (\n    <Typography maxWidth=\"50rem\" textColor=\"neutral800\" ellipsis>\n      {getRelationLabel(content, mainField)}\n    </Typography>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * RelationMultiple\n * -----------------------------------------------------------------------------------------------*/\n\ninterface RelationMultipleProps\n  extends Pick<CellContentProps, 'mainField' | 'content' | 'name' | 'rowId'> {}\n\n/**\n * TODO: fix this component – tracking issue https://strapi-inc.atlassian.net/browse/CONTENT-2184\n */\nconst RelationMultiple = ({ mainField, content, rowId, name }: RelationMultipleProps) => {\n  const { model } = useDoc();\n  const { formatMessage } = useIntl();\n  const { notifyStatus } = useNotifyAT();\n  const [isOpen, setIsOpen] = React.useState(false);\n\n  const [targetField] = name.split('.');\n\n  const { data, isLoading } = useGetRelationsQuery(\n    {\n      model,\n      id: rowId,\n      targetField,\n    },\n    {\n      skip: !isOpen,\n      refetchOnMountOrArgChange: true,\n    }\n  );\n\n  const contentCount = Array.isArray(content) ? content.length : content.count;\n\n  React.useEffect(() => {\n    if (data) {\n      notifyStatus(\n        formatMessage({\n          id: getTranslation('DynamicTable.relation-loaded'),\n          defaultMessage: 'Relations have been loaded',\n        })\n      );\n    }\n  }, [data, formatMessage, notifyStatus]);\n\n  return (\n    <Menu.Root onOpenChange={(isOpen) => setIsOpen(isOpen)}>\n      <Menu.Trigger onClick={(e) => e.stopPropagation()}>\n        <Typography style={{ cursor: 'pointer' }} textColor=\"neutral800\" fontWeight=\"regular\">\n          {contentCount > 0\n            ? formatMessage(\n                {\n                  id: 'content-manager.containers.list.items',\n                  defaultMessage: '{number} {number, plural, =0 {items} one {item} other {items}}',\n                },\n                { number: contentCount }\n              )\n            : '-'}\n        </Typography>\n      </Menu.Trigger>\n      <Menu.Content>\n        {isLoading && (\n          <Menu.Item disabled>\n            <Loader small>\n              {formatMessage({\n                id: getTranslation('ListViewTable.relation-loading'),\n                defaultMessage: 'Relations are loading',\n              })}\n            </Loader>\n          </Menu.Item>\n        )}\n        {data?.results && (\n          <>\n            {data.results.map((entry) => (\n              <Menu.Item key={entry.documentId} disabled>\n                <Typography maxWidth=\"50rem\" ellipsis>\n                  {getRelationLabel(entry, mainField)}\n                </Typography>\n              </Menu.Item>\n            ))}\n\n            {data?.pagination && data?.pagination.total > 10 && (\n              <Menu.Item\n                aria-disabled\n                aria-label={formatMessage({\n                  id: getTranslation('ListViewTable.relation-more'),\n                  defaultMessage: 'This relation contains more entities than displayed',\n                })}\n              >\n                <Typography>…</Typography>\n              </Menu.Item>\n            )}\n          </>\n        )}\n      </Menu.Content>\n    </Menu.Root>\n  );\n};\n\nexport { RelationSingle, RelationMultiple };\nexport type { RelationSingleProps, RelationMultipleProps };\n", "import { Tooltip, Typography } from '@strapi/design-system';\nimport isEmpty from 'lodash/isEmpty';\n\nimport { CellValue } from './CellValue';\nimport { SingleComponent, RepeatableComponent } from './Components';\nimport { MediaSingle, MediaMultiple } from './Media';\nimport { RelationMultiple, RelationSingle } from './Relations';\n\nimport type { ListFieldLayout } from '../../../../hooks/useDocumentLayout';\nimport type { Schema, Data } from '@strapi/types';\n\ninterface CellContentProps extends Omit<ListFieldLayout, 'cellFormatter'> {\n  content: Schema.Attribute.Value<Schema.Attribute.AnyAttribute>;\n  rowId: Data.ID;\n}\n\nconst CellContent = ({ content, mainField, attribute, rowId, name }: CellContentProps) => {\n  if (!hasContent(content, mainField, attribute)) {\n    return (\n      <Typography\n        textColor=\"neutral800\"\n        paddingLeft={attribute.type === ('relation' || 'component') ? '1.6rem' : 0}\n        paddingRight={attribute.type === ('relation' || 'component') ? '1.6rem' : 0}\n      >\n        -\n      </Typography>\n    );\n  }\n\n  switch (attribute.type) {\n    case 'media':\n      if (!attribute.multiple) {\n        return <MediaSingle {...content} />;\n      }\n\n      return <MediaMultiple content={content} />;\n\n    case 'relation': {\n      if (isSingleRelation(attribute.relation)) {\n        return <RelationSingle mainField={mainField} content={content} />;\n      }\n\n      return <RelationMultiple rowId={rowId} mainField={mainField} content={content} name={name} />;\n    }\n\n    case 'component':\n      if (attribute.repeatable) {\n        return <RepeatableComponent mainField={mainField} content={content} />;\n      }\n\n      return <SingleComponent mainField={mainField} content={content} />;\n\n    case 'string':\n      return (\n        <Tooltip description={content}>\n          <Typography maxWidth=\"30rem\" ellipsis textColor=\"neutral800\">\n            <CellValue type={attribute.type} value={content} />\n          </Typography>\n        </Tooltip>\n      );\n\n    default:\n      return (\n        <Typography maxWidth=\"30rem\" ellipsis textColor=\"neutral800\">\n          <CellValue type={attribute.type} value={content} />\n        </Typography>\n      );\n  }\n};\n\nconst hasContent = (\n  content: CellContentProps['content'],\n  mainField: CellContentProps['mainField'],\n  attribute: CellContentProps['attribute']\n) => {\n  if (attribute.type === 'component') {\n    // Repeatable fields show the ID as fallback, in case the mainField\n    // doesn't have any content\n    if (attribute.repeatable || !mainField) {\n      return content?.length > 0;\n    }\n\n    const value = content?.[mainField.name];\n\n    // relations, media ... show the id as fallback\n    if (mainField.name === 'id' && ![undefined, null].includes(value)) {\n      return true;\n    }\n\n    return !isEmpty(value);\n  }\n\n  if (attribute.type === 'relation') {\n    if (isSingleRelation(attribute.relation)) {\n      return !isEmpty(content);\n    }\n\n    if (Array.isArray(content)) {\n      return content.length > 0;\n    }\n\n    return content?.count > 0;\n  }\n\n  /*\n      Biginteger fields need to be treated as strings, as `isNumber`\n      doesn't deal with them.\n  */\n  if (['integer', 'decimal', 'float', 'number'].includes(attribute.type)) {\n    return typeof content === 'number';\n  }\n\n  if (attribute.type === 'boolean') {\n    return content !== null;\n  }\n\n  return !isEmpty(content);\n};\n\nconst isSingleRelation = (\n  type: Extract<CellContentProps['attribute'], { type: 'relation' }>['relation']\n) => ['oneToOne', 'manyToOne', 'oneToOneMorph'].includes(type);\n\nexport { CellContent };\nexport type { CellContentProps };\n", "import * as React from 'react';\n\nimport { useTracking, useRBAC, useQueryParams } from '@strapi/admin/strapi-admin';\nimport {\n  Flex,\n  IconButton,\n  Popover,\n  Checkbox,\n  TextButton,\n  Typography,\n  useCollator,\n  LinkButton,\n} from '@strapi/design-system';\nimport { Cog, ListPlus } from '@strapi/icons';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\n\nimport { useDoc } from '../../../hooks/useDocument';\nimport { useDocumentLayout } from '../../../hooks/useDocumentLayout';\nimport { useTypedSelector } from '../../../modules/hooks';\nimport { checkIfAttributeIsDisplayable } from '../../../utils/attributes';\n\ninterface ViewSettingsMenuProps extends FieldPickerProps {}\n\nconst ViewSettingsMenu = (props: ViewSettingsMenuProps) => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.contentManager?.collectionTypesConfigurations ?? []\n  );\n  const [{ query }] = useQueryParams<{ plugins?: Record<string, unknown> }>();\n  const { formatMessage } = useIntl();\n  const {\n    allowedActions: { canConfigureView },\n  } = useRBAC(permissions);\n\n  return (\n    <Popover.Root>\n      <Popover.Trigger>\n        <IconButton\n          label={formatMessage({\n            id: 'components.ViewSettings.tooltip',\n            defaultMessage: 'View Settings',\n          })}\n        >\n          <Cog />\n        </IconButton>\n      </Popover.Trigger>\n      <Popover.Content side=\"bottom\" align=\"end\" sideOffset={4}>\n        <Flex alignItems=\"stretch\" direction=\"column\" padding={3} gap={3}>\n          {canConfigureView ? (\n            <LinkButton\n              size=\"S\"\n              startIcon={<ListPlus />}\n              variant=\"secondary\"\n              tag={NavLink}\n              to={{\n                pathname: 'configurations/list',\n                search: query.plugins\n                  ? stringify({ plugins: query.plugins }, { encode: false })\n                  : '',\n              }}\n            >\n              {formatMessage({\n                id: 'app.links.configure-view',\n                defaultMessage: 'Configure the view',\n              })}\n            </LinkButton>\n          ) : null}\n          <FieldPicker {...props} />\n        </Flex>\n      </Popover.Content>\n    </Popover.Root>\n  );\n};\n\ninterface FieldPickerProps {\n  headers?: string[];\n  setHeaders: (headers: string[]) => void;\n  resetHeaders: () => void;\n}\n\nconst FieldPicker = ({ headers = [], resetHeaders, setHeaders }: FieldPickerProps) => {\n  const { trackUsage } = useTracking();\n  const { formatMessage, locale } = useIntl();\n\n  const { schema, model } = useDoc();\n  const { list } = useDocumentLayout(model);\n\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const attributes = schema?.attributes ?? {};\n\n  const columns = Object.keys(attributes)\n    .filter((name) => checkIfAttributeIsDisplayable(attributes[name]))\n    .map((name) => ({\n      name,\n      label: list.metadatas[name]?.label ?? '',\n    }))\n    .sort((a, b) => formatter.compare(a.label, b.label));\n\n  const handleChange = (name: string) => {\n    trackUsage('didChangeDisplayedFields');\n\n    /**\n     * create an array of the new headers, if the new name exists it should be removed,\n     * otherwise it should be added\n     */\n    const newHeaders = headers.includes(name)\n      ? headers.filter((header) => header !== name)\n      : [...headers, name];\n\n    setHeaders(newHeaders);\n  };\n\n  const handleReset = () => {\n    resetHeaders();\n  };\n\n  return (\n    <Flex tag=\"fieldset\" direction=\"column\" alignItems=\"stretch\" gap={3} borderWidth={0}>\n      <Flex justifyContent=\"space-between\">\n        <Typography tag=\"legend\" variant=\"pi\" fontWeight=\"bold\">\n          {formatMessage({\n            id: 'containers.list.displayedFields',\n            defaultMessage: 'Displayed fields',\n          })}\n        </Typography>\n\n        <TextButton onClick={handleReset}>\n          {formatMessage({\n            id: 'app.components.Button.reset',\n            defaultMessage: 'Reset',\n          })}\n        </TextButton>\n      </Flex>\n\n      <Flex direction=\"column\" alignItems=\"stretch\">\n        {columns.map((header) => {\n          const isActive = headers.includes(header.name);\n\n          return (\n            <Flex\n              wrap=\"wrap\"\n              gap={2}\n              background={isActive ? 'primary100' : 'transparent'}\n              hasRadius\n              padding={2}\n              key={header.name}\n            >\n              <Checkbox\n                onCheckedChange={() => handleChange(header.name)}\n                checked={isActive}\n                name={header.name}\n              >\n                <Typography fontSize={1}>{header.label}</Typography>\n              </Checkbox>\n            </Flex>\n          );\n        })}\n      </Flex>\n    </Flex>\n  );\n};\n\nexport { ViewSettingsMenu };\nexport type { ViewSettingsMenuProps, FieldPickerProps };\n", "import * as React from 'react';\n\nimport {\n  Page,\n  Pagination,\n  SearchInput,\n  Table,\n  BackButton,\n  useNotification,\n  useStrapiApp,\n  useTracking,\n  useAPIErrorHandler,\n  useQueryParams,\n  useRBAC,\n  Layouts,\n  useTable,\n} from '@strapi/admin/strapi-admin';\nimport { Button, Flex, Typography, ButtonProps } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport isEqual from 'lodash/isEqual';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useNavigate, Link as ReactRouterLink, useParams } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { InjectionZone } from '../../components/InjectionZone';\nimport { HOOKS } from '../../constants/hooks';\nimport { PERMISSIONS } from '../../constants/plugin';\nimport { DocumentRBAC, useDocumentRBAC } from '../../features/DocumentRBAC';\nimport { useDoc } from '../../hooks/useDocument';\nimport {\n  ListFieldLayout,\n  convertListLayoutToFieldLayouts,\n  useDocumentLayout,\n} from '../../hooks/useDocumentLayout';\nimport { usePrev } from '../../hooks/usePrev';\nimport { useGetAllDocumentsQuery } from '../../services/documents';\nimport { buildValidParams } from '../../utils/api';\nimport { getTranslation } from '../../utils/translations';\nimport { getDisplayName } from '../../utils/users';\nimport { DocumentStatus } from '../EditView/components/DocumentStatus';\n\nimport { BulkActionsRenderer } from './components/BulkActions/Actions';\nimport { Filters } from './components/Filters';\nimport { TableActions } from './components/TableActions';\nimport { CellContent } from './components/TableCells/CellContent';\nimport { ViewSettingsMenu } from './components/ViewSettingsMenu';\n\nimport type { Modules } from '@strapi/types';\n\nconst { INJECT_COLUMN_IN_TABLE } = HOOKS;\n\n/* -------------------------------------------------------------------------------------------------\n * ListViewPage\n * -----------------------------------------------------------------------------------------------*/\nconst LayoutsHeaderCustom = styled(Layouts.Header)`\n  overflow-wrap: anywhere;\n`;\n\nconst ListViewPage = () => {\n  const { trackUsage } = useTracking();\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler(getTranslation);\n\n  const { collectionType, model, schema } = useDoc();\n  const { list } = useDocumentLayout(model);\n\n  const [displayedHeaders, setDisplayedHeaders] = React.useState<ListFieldLayout[]>([]);\n\n  const listLayout = usePrev(list.layout);\n  React.useEffect(() => {\n    /**\n     * ONLY update the displayedHeaders if the document\n     * layout has actually changed in value.\n     */\n    if (!isEqual(listLayout, list.layout)) {\n      setDisplayedHeaders(list.layout);\n    }\n  }, [list.layout, listLayout]);\n\n  const handleSetHeaders = (headers: string[]) => {\n    setDisplayedHeaders(\n      convertListLayoutToFieldLayouts(headers, schema!.attributes, list.metadatas)\n    );\n  };\n\n  const [{ query }] = useQueryParams<{\n    plugins?: Record<string, unknown>;\n    page?: string;\n    pageSize?: string;\n    sort?: string;\n  }>({\n    page: '1',\n    pageSize: list.settings.pageSize.toString(),\n    sort: list.settings.defaultSortBy\n      ? `${list.settings.defaultSortBy}:${list.settings.defaultSortOrder}`\n      : '',\n  });\n\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n  const queryString = React.useMemo(\n    () => stringify(params, { encode: true, encodeValuesOnly: true }),\n    [params]\n  );\n  const paramObject = React.useMemo(() => {\n    const pairs = queryString.split('&').map((param) => {\n      const [key, value] = param.split('=');\n      return { [key]: value };\n    });\n    return Object.assign({}, ...pairs);\n  }, [queryString]);\n\n  const { data, error, isFetching } = useGetAllDocumentsQuery({\n    model,\n    params: paramObject,\n  });\n\n  /**\n   * If the API returns an error, display a notification\n   */\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  const { results = [], pagination } = data ?? {};\n\n  React.useEffect(() => {\n    if (pagination && pagination.pageCount > 0 && pagination.page > pagination.pageCount) {\n      navigate(\n        {\n          search: stringify({\n            ...query,\n            page: pagination.pageCount,\n          }),\n        },\n        { replace: true }\n      );\n    }\n  }, [pagination, formatMessage, query, navigate]);\n\n  const { canCreate } = useDocumentRBAC('ListViewPage', ({ canCreate }) => ({\n    canCreate,\n  }));\n\n  const runHookWaterfall = useStrapiApp('ListViewPage', ({ runHookWaterfall }) => runHookWaterfall);\n  /**\n   * Run the waterfall and then inject our additional table headers.\n   */\n  const tableHeaders = React.useMemo(() => {\n    const headers = runHookWaterfall(INJECT_COLUMN_IN_TABLE, {\n      displayedHeaders,\n      layout: list,\n    });\n\n    const formattedHeaders = headers.displayedHeaders.map<ListFieldLayout>((header) => {\n      /**\n       * When the header label is a string, it is an attribute on the current content-type:\n       * Use the attribute name value to compute the translation.\n       * Otherwise, it should be a  translation object coming from a plugin that injects into the table (ie i18n, content-releases, review-workflows):\n       * Use the translation object as is.\n       */\n      const translation =\n        typeof header.label === 'string'\n          ? {\n              id: `content-manager.content-types.${model}.${header.name}`,\n              defaultMessage: header.label,\n            }\n          : header.label;\n\n      return {\n        ...header,\n        label: formatMessage(translation),\n        name: `${header.name}${header.mainField?.name ? `.${header.mainField.name}` : ''}`,\n      };\n    });\n\n    if (schema?.options?.draftAndPublish) {\n      formattedHeaders.push({\n        attribute: {\n          type: 'custom',\n        },\n        name: 'status',\n        label: formatMessage({\n          id: getTranslation(`containers.list.table-headers.status`),\n          defaultMessage: 'status',\n        }),\n        searchable: false,\n        sortable: false,\n      } satisfies ListFieldLayout);\n    }\n\n    return formattedHeaders;\n  }, [\n    displayedHeaders,\n    formatMessage,\n    list,\n    runHookWaterfall,\n    schema?.options?.draftAndPublish,\n    model,\n  ]);\n\n  if (isFetching) {\n    return <Page.Loading />;\n  }\n\n  if (error) {\n    return <Page.Error />;\n  }\n\n  const contentTypeTitle = schema?.info.displayName ?? 'Untitled';\n\n  const handleRowClick = (id: Modules.Documents.ID) => () => {\n    trackUsage('willEditEntryFromList');\n    navigate({\n      pathname: id.toString(),\n      search: stringify({ plugins: query.plugins }),\n    });\n  };\n\n  return (\n    <Page.Main>\n      <Page.Title>{`${contentTypeTitle}`}</Page.Title>\n      <LayoutsHeaderCustom\n        primaryAction={canCreate ? <CreateButton /> : null}\n        subtitle={formatMessage(\n          {\n            id: getTranslation('pages.ListView.header-subtitle'),\n            defaultMessage:\n              '{number, plural, =0 {# entries} one {# entry} other {# entries}} found',\n          },\n          { number: pagination?.total }\n        )}\n        title={contentTypeTitle}\n        navigationAction={<BackButton />}\n      />\n      <Layouts.Action\n        endActions={\n          <>\n            <InjectionZone area=\"listView.actions\" />\n            <ViewSettingsMenu\n              setHeaders={handleSetHeaders}\n              resetHeaders={() => setDisplayedHeaders(list.layout)}\n              headers={displayedHeaders.map((header) => header.name)}\n            />\n          </>\n        }\n        startActions={\n          <>\n            {list.settings.searchable && (\n              <SearchInput\n                disabled={results.length === 0}\n                label={formatMessage(\n                  { id: 'app.component.search.label', defaultMessage: 'Search for {target}' },\n                  { target: contentTypeTitle }\n                )}\n                placeholder={formatMessage({\n                  id: 'global.search',\n                  defaultMessage: 'Search',\n                })}\n                trackedEvent=\"didSearch\"\n              />\n            )}\n            {list.settings.filterable && schema ? (\n              <Filters disabled={results.length === 0} schema={schema} />\n            ) : null}\n          </>\n        }\n      />\n      <Layouts.Content>\n        <Flex gap={4} direction=\"column\" alignItems=\"stretch\">\n          <Table.Root rows={results} headers={tableHeaders} isLoading={isFetching}>\n            <TableActionsBar />\n            <Table.Content>\n              <Table.Head>\n                <Table.HeaderCheckboxCell />\n                {tableHeaders.map((header: ListFieldLayout) => (\n                  <Table.HeaderCell key={header.name} {...header} />\n                ))}\n              </Table.Head>\n              <Table.Loading />\n              <Table.Empty action={canCreate ? <CreateButton variant=\"secondary\" /> : null} />\n              <Table.Body>\n                {results.map((row) => {\n                  return (\n                    <Table.Row\n                      cursor=\"pointer\"\n                      key={row.id}\n                      onClick={handleRowClick(row.documentId)}\n                    >\n                      <Table.CheckboxCell id={row.id} />\n                      {tableHeaders.map(({ cellFormatter, ...header }) => {\n                        if (header.name === 'status') {\n                          const { status } = row;\n\n                          return (\n                            <Table.Cell key={header.name}>\n                              <DocumentStatus status={status} maxWidth={'min-content'} />\n                            </Table.Cell>\n                          );\n                        }\n                        if (['createdBy', 'updatedBy'].includes(header.name.split('.')[0])) {\n                          // Display the users full name\n                          // Some entries doesn't have a user assigned as creator/updater (ex: entries created through content API)\n                          // In this case, we display a dash\n                          return (\n                            <Table.Cell key={header.name}>\n                              <Typography textColor=\"neutral800\">\n                                {row[header.name.split('.')[0]]\n                                  ? getDisplayName(row[header.name.split('.')[0]])\n                                  : '-'}\n                              </Typography>\n                            </Table.Cell>\n                          );\n                        }\n                        if (typeof cellFormatter === 'function') {\n                          return (\n                            <Table.Cell key={header.name}>\n                              {/* @ts-expect-error – TODO: fix this TS error */}\n                              {cellFormatter(row, header, { collectionType, model })}\n                            </Table.Cell>\n                          );\n                        }\n                        return (\n                          <Table.Cell key={header.name}>\n                            <CellContent\n                              content={row[header.name.split('.')[0]]}\n                              rowId={row.documentId}\n                              {...header}\n                            />\n                          </Table.Cell>\n                        );\n                      })}\n                      {/* we stop propogation here to allow the menu to trigger it's events without triggering the row redirect */}\n                      <ActionsCell onClick={(e) => e.stopPropagation()}>\n                        <TableActions document={row} />\n                      </ActionsCell>\n                    </Table.Row>\n                  );\n                })}\n              </Table.Body>\n            </Table.Content>\n          </Table.Root>\n          <Pagination.Root\n            {...pagination}\n            onPageSizeChange={() => trackUsage('willChangeNumberOfEntriesPerPage')}\n          >\n            <Pagination.PageSize />\n            <Pagination.Links />\n          </Pagination.Root>\n        </Flex>\n      </Layouts.Content>\n    </Page.Main>\n  );\n};\n\nconst ActionsCell = styled(Table.Cell)`\n  display: flex;\n  justify-content: flex-end;\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * TableActionsBar\n * -----------------------------------------------------------------------------------------------*/\n\nconst TableActionsBar = () => {\n  const selectRow = useTable('TableActionsBar', (state) => state.selectRow);\n  const [{ query }] = useQueryParams<{ plugins: { i18n: { locale: string } } }>();\n  const locale = query?.plugins?.i18n?.locale;\n  const prevLocale = usePrev(locale);\n\n  // TODO: find a better way to reset the selected rows when the locale changes across all the app\n  React.useEffect(() => {\n    if (prevLocale !== locale) {\n      selectRow([]);\n    }\n  }, [selectRow, prevLocale, locale]);\n\n  return (\n    <Table.ActionBar>\n      <BulkActionsRenderer />\n    </Table.ActionBar>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * CreateButton\n * -----------------------------------------------------------------------------------------------*/\n\ninterface CreateButtonProps extends Pick<ButtonProps, 'variant'> {}\n\nconst CreateButton = ({ variant }: CreateButtonProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const [{ query }] = useQueryParams<{ plugins: object }>();\n\n  return (\n    <Button\n      variant={variant}\n      tag={ReactRouterLink}\n      onClick={() => {\n        trackUsage('willCreateEntry', { status: 'draft' });\n      }}\n      startIcon={<Plus />}\n      style={{ textDecoration: 'none' }}\n      to={{\n        pathname: 'create',\n        search: stringify({ plugins: query.plugins }),\n      }}\n      minWidth=\"max-content\"\n      marginLeft={2}\n    >\n      {formatMessage({\n        id: getTranslation('HeaderLayout.button.label-add-entry'),\n        defaultMessage: 'Create new entry',\n      })}\n    </Button>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedListViewPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedListViewPage = () => {\n  const { slug = '' } = useParams<{\n    slug: string;\n  }>();\n  const {\n    permissions = [],\n    isLoading,\n    error,\n  } = useRBAC(\n    PERMISSIONS.map((action) => ({\n      action,\n      subject: slug,\n    }))\n  );\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (error || !slug) {\n    return <Page.Error />;\n  }\n\n  return (\n    <Page.Protect permissions={permissions}>\n      {({ permissions }) => (\n        <DocumentRBAC permissions={permissions}>\n          <ListViewPage />\n        </DocumentRBAC>\n      )}\n    </Page.Protect>\n  );\n};\n\nexport { ListViewPage, ProtectedListViewPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,IAAM,sBAAsB;EAC1B;EACA;EACA;EACA;EACA;EACA;EACA;AACF;AACA,IAAM,0BAA0B,CAAC,aAAa,WAAW;AACzD,IAAM,yBAAyB,CAAC,GAAG,gBAAgB,iBAAiB;AAUpE,IAAM,cAAc,CAAC,EAAE,UAAU,OAAA,MAA2B;;AAC1D,QAAM,EAAE,YAAY,KAAK,OAAO,QAAA,IAAY;AAC5C,QAAM,EAAE,eAAe,OAAO,IAAI,QAAQ;AACpC,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,iBAAiB,QAAQ,eAAe,CAAC,UAAU,MAAM,WAAW;AAC1E,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAA8B;AAC5C,QAAA,EAAE,QAAA,IAAY,qBAAA;AAEpB,QAAM,oBAA0B;IAC9B,MACE,eAAe;MACb,CAAC,eAAe,WAAW,WAAW,uBAAuB,WAAW,YAAY;IAAA,EACpF,SAAS;IACb,CAAC,cAAc;EAAA;AAGX,QAAA,qBAAmB,oCAAO,YAAP,mBAAgB,SAAQ,CAAA,GAAI,OAAiB,CAAC,KAAK,WAAW;AAC/E,UAAA,CAAC,KAAK,KAAK,IAAI,OAAO,QAAQ,MAAM,EAAE,CAAC;AACzC,QAAA,OAAO,MAAM,OAAO,UAAU;AACzB,aAAA;IACT;AAEA,UAAM,KAAK,MAAM,GAAG,OAAO,MAAM,GAAG;AAEhC,QAAA,MAAM,uBAAuB,SAAS,GAAG,KAAK,CAAC,IAAI,SAAS,EAAE,GAAG;AACnE,UAAI,KAAK,EAAE;IACb;AAEO,WAAA;EACT,GAAG,CAAE,CAAA;AAEL,QAAM,EAAE,MAAM,UAAU,WAAW,oBAAwB,IAAA;IACzD,EAAE,SAAS,EAAE,IAAI,EAAE,KAAK,gBAAA,EAAA,EAAoB;IAC5C;;;MAGE,MAAM,gBAAgB,WAAW,KAAK,CAAC;IACzC;EAAA;AAGF,QAAM,EAAE,QAAQ,CAAA,EAAA,IAAO,YAAY,CAAA;AAEnC,QAAM,EAAE,SAAA,IAAa,oCAAoC,OAAO;IAC9D,kBAAkB,CAAC,EAAE,KAAK,OAAO,EAAE,WAAU,6BAAM,YAAY,cAAa,CAAA,EAAA;EAAG,CAChF;AAEK,QAAA,YAAY,YAAY,QAAQ;IACpC,aAAa;EAAA,CACd;AAEK,QAAA,mBAAyB,cAAQ,MAAM;AAC3C,UAAM,CAAC,EAAE,YAAY,EAAE,SAAS,CAAC,EAAA,IAAM,EAAE,QAAQ,CAAC,EAAA,EAAA,CAAK,IAAI,eAAe;MACxE,CAAC,eACC,WAAW,WAAW,2CACtB,WAAW,YAAY;IAAA;AAG3B,UAAM,gBAAgB,OAAO,OAAO,CAAC,UAAU;AAC7C,YAAM,YAAY,WAAW,KAAK,KAAK,CAAA;AAEvC,aAAO,UAAU,QAAQ,CAAC,oBAAoB,SAAS,UAAU,IAAI;IAAA,CACtE;AAGC,WAAA;MACE;MACA,GAAG;MACH,GAAG;MACH,GAAI,oBAAoB,iBAAiB,CAAC;IAAA,EAEzC,IAAI,CAAC,SAAS;AACP,YAAA,YAAY,WAAW,IAAI;AAEjC,UAAI,oBAAoB,SAAS,UAAU,IAAI,GAAG;AACzC,eAAA;MACT;AAEM,YAAA,EAAE,WAAW,gBAAgB,IAAI,MAAA,IAAU,SAAS,IAAI,EAAE;AAEhE,UAAI,SAAyB;QAC3B;QACA,OAAO,SAAS;QAChB,WAAW,aAAa,WAAW,eAAe,EAAE,SAAS,YAAY,CAAA,EAAA,CAAI;;QAE7E,MAAM,UAAU;MAAA;AAGlB,UACE,UAAU,SAAS,cACnB,YAAY,aACZ,UAAU,WAAW,eACrB;AACS,iBAAA;UACP,GAAG;UACH,OAAO;UACP,SAAS,MAAM,IAAI,CAAC,UAAU;YAC5B,OAAO,eAAe,IAAI;YAC1B,OAAO,KAAK,GAAG,SAAS;UAAA,EACxB;UACF,WAAW;YACT;cACE,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,OAAO;YACT;YACA;cACE,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,OAAO;YACT;UACF;UACA,WAAW;YACT,MAAM;YACN,MAAM;UACR;QAAA;MAEJ;AAEI,UAAA,UAAU,SAAS,eAAe;AAC3B,iBAAA;UACP,GAAG;UACH,SAAS,UAAU,KAAK,IAAI,CAAC,WAAW;YACtC,OAAO;YACP;UAAA,EACA;QAAA;MAEN;AAEO,aAAA;IACR,CAAA,EACA,OAAO,OAAO,EACjB,SAAS,CAAC,GAAG,MAAM,UAAU,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC;EAAA,GACvD;IACD;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;AAEK,QAAA,eAAe,CAAC,WAAoB;AACxC,QAAI,QAAQ;AACV,iBAAW,mBAAmB;IAChC;EAAA;AAGI,QAAA,qBAAgD,CAAC,SAAS;AACxD,UAAA,YAAY,WAAW,KAAK,IAAI;AAEtC,QAAI,WAAW;AACb,iBAAW,oBAAoB;QAC7B,aAAa,UAAU,SAAS;MAAA,CACjC;IACH;EAAA;AAIA,aAAA;IAAC,QAAQ;IAAR;MACC;MACA,SAAS;MACT;MACA,UAAU;MAEV,UAAA;YAAC,wBAAA,QAAQ,SAAR,CAAA,CAAgB;YACjB,wBAAC,QAAQ,SAAR,CAAA,CAAgB;YACjB,wBAAC,QAAQ,MAAR,CAAA,CAAa;MAAA;IAAA;EAAA;AAGpB;AAMA,IAAM,mBAAmB,CAAC,EAAE,KAAA,MAAoC;AAC9D,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS,EAAE;AACjD,QAAM,CAAC,QAAQ,SAAS,IAAU,eAAS,EAAE;AACvC,QAAA,EAAE,cAAA,IAAkB,QAAA;AAEpB,QAAA,kBAAkB,YAAY,QAAQ,GAAG;AAE/C,QAAM,EAAE,MAAM,UAAU,IAAI,cAAc;IACxC;IACA,IAAI;EAAA,CACL;AACK,QAAA,QAAQ,SAAS,IAAI;AAErB,QAAA,mBAAmB,CAAC,WAAqB;AAC7C,QAAI,CAAC,QAAQ;AACX,kBAAY,EAAE;IAChB;EAAA;AAGF,QAAM,EAAE,QAAQ,CAAA,GAAI,WAAW,IAAI,QAAQ,CAAA;AAC3C,QAAM,EAAE,YAAY,GAAG,OAAO,EAAE,IAAI,cAAc,CAAA;AAGhD,aAAA;IAAC;IAAA;MACC,OAAO,MAAM;MACb,cAAY,cAAc;QACxB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,cAAc;MACd,UAAU,CAAC,UAAU,MAAM,SAAS,MAAM,KAAK;MAC/C,SAAS;MACT,YAAY,MAAM,YAAY,WAAW,EAAE;MAC3C,cAAc,OAAO;MACrB,eAAe,CAAC,MAA2C;AAC/C,kBAAA,EAAE,cAAc,KAAK;MACjC;MAEC,UAAA,MAAM,IAAI,CAAC,SAAS;AAEjB,mBAAA,wBAAC,QAA6B,EAAA,OAAO,KAAK,GAAG,SAAS,GACnD,UAAe,eAAA,IAAI,EADD,GAAA,KAAK,EAE1B;MAAA,CAEH;IAAA;EAAA;AAGP;ACtQA,IAAM,YAAY,CAAC,EAAE,MAAM,MAAA,MAA4B;AACrD,QAAM,EAAE,YAAY,YAAY,aAAA,IAAiB,QAAQ;AACzD,MAAI,iBAAiB;AAErB,MAAI,SAAS,QAAQ;AACnB,qBAAiB,WAAW,SAAS,KAAK,GAAG,EAAE,WAAW,OAAA,CAAQ;EACpE;AAEA,MAAI,SAAS,YAAY;AACvB,qBAAiB,WAAW,OAAO,EAAE,WAAW,QAAQ,WAAW,QAAA,CAAS;EAC9E;AAEA,MAAI,SAAS,QAAQ;AACnB,UAAM,CAAC,MAAM,QAAQ,MAAM,IAAI,MAAM,MAAM,GAAG;AACxC,UAAA,OAAA,oBAAW,KAAA;AACjB,SAAK,SAAS,IAAI;AAClB,SAAK,WAAW,MAAM;AACtB,SAAK,WAAW,MAAM;AAEtB,qBAAiB,WAAW,MAAM;MAChC,WAAW;IAAA,CACZ;EACH;AAEA,MAAI,CAAC,SAAS,SAAS,EAAE,SAAS,IAAI,GAAG;AACvC,qBAAiB,aAAa,OAAO;;;MAGnC,uBAAuB;IAAA,CACxB;EACH;AAEA,MAAI,CAAC,WAAW,YAAY,EAAE,SAAS,IAAI,GAAG;AAC5C,qBAAiB,aAAa,OAAO,EAAE,uBAAuB,EAAG,CAAA;EACnE;AAEA,aAAO,gBAAAA,SAAS,cAAc;AAChC;AC/BA,IAAM,kBAAkB,CAAC,EAAE,SAAS,UAAA,MAAsC;AACxE,MAAI,CAAC,WAAW;AACP,WAAA;EACT;AAGE,aAAA,wBAAC,aAAQ,EAAA,OAAO,QAAQ,UAAU,IAAI,GACpC,cAAC,wBAAA,YAAA,EAAW,UAAS,SAAQ,WAAU,cAAa,UAAQ,MAC1D,cAAA,wBAAC,WAAU,EAAA,MAAM,UAAU,MAAM,OAAO,QAAQ,UAAU,IAAI,EAAG,CAAA,EAAA,CACnE,EACF,CAAA;AAEJ;AAUA,IAAM,sBAAsB,CAAC,EAAE,SAAS,UAAA,MAA0C;AAC1E,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,MAAI,CAAC,WAAW;AACP,WAAA;EACT;AAGE,aAAA,yBAAC,KAAK,MAAL,EACC,UAAA;QAAC,yBAAA,KAAK,SAAL,EAAa,SAAS,CAAC,MAAM,EAAE,gBAC9B,GAAA,UAAA;UAAC,wBAAA,OAAA,EAAO,UAAA,QAAQ,OAAO,CAAA;MACtB;QACC;UACE,IAAI;UACJ,gBAAgB;QAClB;QACA,EAAE,QAAQ,QAAQ,OAAO;MAC3B;IAAA,EAAA,CACF;QACC,wBAAA,KAAK,SAAL,EACE,UAAA,QAAQ,IAAI,CAAC,aACZ,wBAAC,KAAK,MAAL,EAAwB,UAAQ,MAC/B,cAAA,wBAAC,YAAW,EAAA,UAAS,SAAQ,UAAQ,MACnC,cAAC,wBAAA,WAAA,EAAU,MAAM,UAAU,MAAM,OAAO,KAAK,UAAU,IAAI,EAAA,CAAG,EAChE,CAAA,EAAA,GAHc,KAAK,EAIrB,CACD,EAAA,CACH;EACF,EAAA,CAAA;AAEJ;ACxCA,IAAM,mBAAmB,CAAC,QAAiB,OAAO,IAAI,CAAC,MAAM,MAAM,IAAI,UAAU,CAAC,IAAI;AAEtF,IAAM,cAAc,CAAC,EAAE,KAAK,MAAM,iBAAiB,MAAM,KAAK,QAAA,MAAgC;;AACtF,QAAA,UAAU,4BAA4B,GAAG;AAE3C,MAAA,KAAK,SAAS,OAAO,GAAG;AACpB,UAAA,aAAY,wCAAS,cAAT,mBAAoB;AAChC,UAAA,WAAW,4BAA4B,SAAS,KAAK;AAGzD,eAAA;MAAC,OAAO;MAAP;QACC,KAAK;QACL,KAAK,mBAAmB;QACxB,UAAU,mBAAmB;QAC7B,SAAO;MAAA;IAAA;EAGb;AAEM,QAAA,gBAAgB,iBAAiB,GAAG;AACpC,QAAA,WAAW,KAAK,SAAS,MAAM,GAAG,KAAK,UAAU,GAAG,GAAG,CAAC,QAAQ;AAEtE,aAAA,wBACG,aAAQ,EAAA,aAAa,UACpB,cAAC,wBAAA,aAAA,EAAa,UAAA,cAAc,CAAA,EAC9B,CAAA;AAEJ;AAEA,IAAM,cAAc,CAAC,EAAE,SAAA,MAA8C;AAEjE,aAAA;IAAC;IAAA;MACC,KAAI;MACJ,UAAS;MACT,cAAa;MACb,OAAM;MACN,QAAO;MACP,aAAY;MACZ,YAAW;MACX,aAAY;MACZ,gBAAe;MACf,YAAW;MAEX,cAAA,wBAAC,gBAAe,EAAA,SAAQ,SAAQ,WAAU,cACvC,SAAA,CACH;IAAA;EAAA;AAGN;AAEA,IAAM,iBAAiB,GAA4B,UAAU;;;;AAa7D,IAAM,gBAAgB,CAAC,EAAE,QAAA,MAAkC;AAEvD,aAAA,wBAAC,OAAO,OAAP,EACE,UAAA,QAAQ,IAAI,CAAC,MAAM,UAAU;AAC5B,UAAM,MAAM,GAAG,KAAK,EAAE,GAAG,KAAK;AAE9B,QAAI,UAAU,GAAG;AACf,YAAM,iBAAiB,IAAI,QAAQ,SAAS,CAAC;AAEtC,iBAAA,wBAAC,aAAuB,EAAA,UAAA,eAAA,GAAN,GAAqB;IAChD;AAEA,QAAI,QAAQ,GAAG;AACN,aAAA;IACT;AAEA,eAAQ,wBAAA,aAAA,EAAuB,GAAG,KAAA,GAAT,GAAe;EACzC,CAAA,EACH,CAAA;AAEJ;AC9FA,IAAM,iBAAiB,CAAC,EAAE,WAAW,QAAA,MAAmC;AAEpE,aAAA,wBAAC,YAAW,EAAA,UAAS,SAAQ,WAAU,cAAa,UAAQ,MACzD,UAAA,iBAAiB,SAAS,SAAS,EACtC,CAAA;AAEJ;AAYA,IAAM,mBAAmB,CAAC,EAAE,WAAW,SAAS,OAAO,KAAA,MAAkC;AACjF,QAAA,EAAE,MAAA,IAAU,OAAA;AACZ,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,aAAA,IAAiB,YAAA;AACzB,QAAM,CAAC,QAAQ,SAAS,IAAU,eAAS,KAAK;AAEhD,QAAM,CAAC,WAAW,IAAI,KAAK,MAAM,GAAG;AAE9B,QAAA,EAAE,MAAM,UAAA,IAAc;IAC1B;MACE;MACA,IAAI;MACJ;IACF;IACA;MACE,MAAM,CAAC;MACP,2BAA2B;IAC7B;EAAA;AAGF,QAAM,eAAe,MAAM,QAAQ,OAAO,IAAI,QAAQ,SAAS,QAAQ;AAEvE,EAAM,gBAAU,MAAM;AACpB,QAAI,MAAM;AACR;QACE,cAAc;UACZ,IAAI,eAAe,8BAA8B;UACjD,gBAAgB;QAAA,CACjB;MAAA;IAEL;EACC,GAAA,CAAC,MAAM,eAAe,YAAY,CAAC;AAGpC,aAAA,yBAAC,KAAK,MAAL,EAAU,cAAc,CAACC,YAAW,UAAUA,OAAM,GACnD,UAAA;QAAC,wBAAA,KAAK,SAAL,EAAa,SAAS,CAAC,MAAM,EAAE,gBAAA,GAC9B,cAAA,wBAAC,YAAA,EAAW,OAAO,EAAE,QAAQ,UAAU,GAAG,WAAU,cAAa,YAAW,WACzE,UAAA,eAAe,IACZ;MACE;QACE,IAAI;QACJ,gBAAgB;MAClB;MACA,EAAE,QAAQ,aAAa;IAAA,IAEzB,IACN,CAAA,EAAA,CACF;QACA,yBAAC,KAAK,SAAL,EACE,UAAA;MACC,iBAAA,wBAAC,KAAK,MAAL,EAAU,UAAQ,MACjB,cAAC,wBAAA,QAAA,EAAO,OAAK,MACV,UAAc,cAAA;QACb,IAAI,eAAe,gCAAgC;QACnD,gBAAgB;MAAA,CACjB,EAAA,CACH,EACF,CAAA;OAED,6BAAM,gBAEF,yBAAA,6BAAA,EAAA,UAAA;QAAK,KAAA,QAAQ,IAAI,CAAC,cAAA,wBAChB,KAAK,MAAL,EAAiC,UAAQ,MACxC,cAAA,wBAAC,YAAA,EAAW,UAAS,SAAQ,UAAQ,MAClC,UAAiB,iBAAA,OAAO,SAAS,EACpC,CAAA,EAAA,GAHc,MAAM,UAItB,CACD;SAEA,6BAAM,gBAAc,6BAAM,WAAW,SAAQ,UAC5C;UAAC,KAAK;UAAL;YACC,iBAAa;YACb,cAAY,cAAc;cACxB,IAAI,eAAe,6BAA6B;cAChD,gBAAgB;YAAA,CACjB;YAED,cAAA,wBAAC,YAAA,EAAW,UAAC,IAAA,CAAA;UAAA;QACf;MAAA,EAAA,CAEJ;IAAA,EAAA,CAEJ;EACF,EAAA,CAAA;AAEJ;ACzGA,IAAM,cAAc,CAAC,EAAE,SAAS,WAAW,WAAW,OAAO,KAAA,MAA6B;AACxF,MAAI,CAAC,WAAW,SAAS,WAAW,SAAS,GAAG;AAE5C,eAAA;MAAC;MAAA;QACC,WAAU;QACV,aAAa,UAAU,SAAU,aAA6B,WAAW;QACzE,cAAc,UAAU,SAAU,aAA6B,WAAW;QAC3E,UAAA;MAAA;IAAA;EAIL;AAEA,UAAQ,UAAU,MAAM;IACtB,KAAK;AACC,UAAA,CAAC,UAAU,UAAU;AAChB,mBAAA,wBAAC,aAAa,EAAA,GAAG,QAAS,CAAA;MACnC;AAEO,iBAAA,wBAAC,eAAA,EAAc,QAAkB,CAAA;IAE1C,KAAK,YAAY;AACX,UAAA,iBAAiB,UAAU,QAAQ,GAAG;AACjC,mBAAA,wBAAC,gBAAe,EAAA,WAAsB,QAAkB,CAAA;MACjE;AAEA,iBAAQ,wBAAA,kBAAA,EAAiB,OAAc,WAAsB,SAAkB,KAAY,CAAA;IAC7F;IAEA,KAAK;AACH,UAAI,UAAU,YAAY;AACjB,mBAAA,wBAAC,qBAAoB,EAAA,WAAsB,QAAkB,CAAA;MACtE;AAEO,iBAAA,wBAAC,iBAAgB,EAAA,WAAsB,QAAkB,CAAA;IAElE,KAAK;AAED,iBAAA,wBAAC,aAAA,EAAQ,aAAa,SACpB,cAAA,wBAAC,YAAW,EAAA,UAAS,SAAQ,UAAQ,MAAC,WAAU,cAC9C,cAAA,wBAAC,WAAA,EAAU,MAAM,UAAU,MAAM,OAAO,QAAA,CAAS,EACnD,CAAA,EACF,CAAA;IAGJ;AACE,iBACG,wBAAA,YAAA,EAAW,UAAS,SAAQ,UAAQ,MAAC,WAAU,cAC9C,cAAA,wBAAC,WAAA,EAAU,MAAM,UAAU,MAAM,OAAO,QAAA,CAAS,EACnD,CAAA;EAEN;AACF;AAEA,IAAM,aAAa,CACjB,SACA,WACA,cACG;AACC,MAAA,UAAU,SAAS,aAAa;AAG9B,QAAA,UAAU,cAAc,CAAC,WAAW;AACtC,cAAO,mCAAS,UAAS;IAC3B;AAEM,UAAA,QAAQ,mCAAU,UAAU;AAG9B,QAAA,UAAU,SAAS,QAAQ,CAAC,CAAC,QAAW,IAAI,EAAE,SAAS,KAAK,GAAG;AAC1D,aAAA;IACT;AAEO,WAAA,KAAC,eAAAC,SAAQ,KAAK;EACvB;AAEI,MAAA,UAAU,SAAS,YAAY;AAC7B,QAAA,iBAAiB,UAAU,QAAQ,GAAG;AACjC,aAAA,KAAC,eAAAA,SAAQ,OAAO;IACzB;AAEI,QAAA,MAAM,QAAQ,OAAO,GAAG;AAC1B,aAAO,QAAQ,SAAS;IAC1B;AAEA,YAAO,mCAAS,SAAQ;EAC1B;AAMI,MAAA,CAAC,WAAW,WAAW,SAAS,QAAQ,EAAE,SAAS,UAAU,IAAI,GAAG;AACtE,WAAO,OAAO,YAAY;EAC5B;AAEI,MAAA,UAAU,SAAS,WAAW;AAChC,WAAO,YAAY;EACrB;AAEO,SAAA,KAAC,eAAAA,SAAQ,OAAO;AACzB;AAEA,IAAM,mBAAmB,CACvB,SACG,CAAC,YAAY,aAAa,eAAe,EAAE,SAAS,IAAI;AChG7D,IAAM,mBAAmB,CAAC,UAAiC;AACzD,QAAM,cAAc;IAClB,CAAC,UAAA;;AAAU,0BAAM,UAAU,YAAY,mBAA5B,mBAA4C,kCAAiC,CAAC;;EAAA;AAE3F,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAAsD;AACpE,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA;IACJ,gBAAgB,EAAE,iBAAiB;EAAA,IACjC,QAAQ,WAAW;AAGrB,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAC,wBAAA,QAAQ,SAAR,EACC,cAAA;MAAC;MAAA;QACC,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QAED,cAAA,wBAAC,eAAI,CAAA,CAAA;MAAA;IAAA,EAAA,CAET;QAAA,wBACC,QAAQ,SAAR,EAAgB,MAAK,UAAS,OAAM,OAAM,YAAY,GACrD,cAAC,yBAAA,MAAA,EAAK,YAAW,WAAU,WAAU,UAAS,SAAS,GAAG,KAAK,GAC5D,UAAA;MACC,uBAAA;QAAC;QAAA;UACC,MAAK;UACL,eAAA,wBAAY,eAAS,CAAA,CAAA;UACrB,SAAQ;UACR,KAAK;UACL,IAAI;YACF,UAAU;YACV,QAAQ,MAAM,cACV,qBAAU,EAAE,SAAS,MAAM,QAAW,GAAA,EAAE,QAAQ,MAAA,CAAO,IACvD;UACN;UAEC,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA;MAAA,IAED;UACJ,wBAAC,aAAa,EAAA,GAAG,MAAA,CAAO;IAAA,EAAA,CAC1B,EACF,CAAA;EACF,EAAA,CAAA;AAEJ;AAQA,IAAM,cAAc,CAAC,EAAE,UAAU,CAAI,GAAA,cAAc,WAAA,MAAmC;AAC9E,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,EAAE,eAAe,OAAO,IAAI,QAAQ;AAE1C,QAAM,EAAE,QAAQ,MAAM,IAAI,OAAO;AACjC,QAAM,EAAE,KAAA,IAAS,kBAAkB,KAAK;AAElC,QAAA,YAAY,YAAY,QAAQ;IACpC,aAAa;EAAA,CACd;AAEK,QAAA,cAAa,iCAAQ,eAAc,CAAA;AAEzC,QAAM,UAAU,OAAO,KAAK,UAAU,EACnC,OAAO,CAAC,SAAS,8BAA8B,WAAW,IAAI,CAAC,CAAC,EAChE,IAAI,CAAC,SAAA;;AAAU;MACd;MACA,SAAO,UAAK,UAAU,IAAI,MAAnB,mBAAsB,UAAS;IACtC;GAAA,EACD,KAAK,CAAC,GAAG,MAAM,UAAU,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC;AAE/C,QAAA,eAAe,CAAC,SAAiB;AACrC,eAAW,0BAA0B;AAMrC,UAAM,aAAa,QAAQ,SAAS,IAAI,IACpC,QAAQ,OAAO,CAAC,WAAW,WAAW,IAAI,IAC1C,CAAC,GAAG,SAAS,IAAI;AAErB,eAAW,UAAU;EAAA;AAGvB,QAAM,cAAc,MAAM;AACX,iBAAA;EAAA;AAIb,aAAA,yBAAC,MAAK,EAAA,KAAI,YAAW,WAAU,UAAS,YAAW,WAAU,KAAK,GAAG,aAAa,GAChF,UAAA;QAAC,yBAAA,MAAA,EAAK,gBAAe,iBACnB,UAAA;UAAA,wBAAC,YAAA,EAAW,KAAI,UAAS,SAAQ,MAAK,YAAW,QAC9C,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MACjB,CAAA,EAAA,CACH;UAEC,wBAAA,YAAA,EAAW,SAAS,aAClB,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MACjB,CAAA,EAAA,CACH;IAAA,EAAA,CACF;QAEA,wBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WACjC,UAAA,QAAQ,IAAI,CAAC,WAAW;AACvB,YAAM,WAAW,QAAQ,SAAS,OAAO,IAAI;AAG3C,iBAAA;QAAC;QAAA;UACC,MAAK;UACL,KAAK;UACL,YAAY,WAAW,eAAe;UACtC,WAAS;UACT,SAAS;UAGT,cAAA;YAAC;YAAA;cACC,iBAAiB,MAAM,aAAa,OAAO,IAAI;cAC/C,SAAS;cACT,MAAM,OAAO;cAEb,cAAC,wBAAA,YAAA,EAAW,UAAU,GAAI,UAAA,OAAO,MAAA,CAAM;YAAA;UACzC;QAAA;QARK,OAAO;MAAA;IAWjB,CAAA,EAAA,CACH;EACF,EAAA,CAAA;AAEJ;AClHA,IAAM,EAAE,uBAA2B,IAAA;AAKnC,IAAM,sBAAsB,GAAO,QAAQ,MAAM;;;AAIjD,IAAM,eAAe,MAAM;;AACnB,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,WAAW,YAAA;AACX,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB,cAAc;AAErF,QAAM,EAAE,gBAAgB,OAAO,OAAA,IAAW,OAAO;AACjD,QAAM,EAAE,KAAA,IAAS,kBAAkB,KAAK;AAExC,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAA4B,CAAA,CAAE;AAE9E,QAAA,aAAa,QAAQ,KAAK,MAAM;AACtC,EAAM,gBAAU,MAAM;AAKpB,QAAI,KAAC,eAAAC,SAAQ,YAAY,KAAK,MAAM,GAAG;AACrC,0BAAoB,KAAK,MAAM;IACjC;EACC,GAAA,CAAC,KAAK,QAAQ,UAAU,CAAC;AAEtB,QAAA,mBAAmB,CAAC,YAAsB;AAC9C;MACE,gCAAgC,SAAS,OAAQ,YAAY,KAAK,SAAS;IAAA;EAC7E;AAGF,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAKjB;IACD,MAAM;IACN,UAAU,KAAK,SAAS,SAAS,SAAS;IAC1C,MAAM,KAAK,SAAS,gBAChB,GAAG,KAAK,SAAS,aAAa,IAAI,KAAK,SAAS,gBAAgB,KAChE;EAAA,CACL;AAEK,QAAA,SAAe,cAAQ,MAAM,iBAAiB,KAAK,GAAG,CAAC,KAAK,CAAC;AACnE,QAAM,cAAoB;IACxB,UAAM,qBAAU,QAAQ,EAAE,QAAQ,MAAM,kBAAkB,KAAA,CAAM;IAChE,CAAC,MAAM;EAAA;AAEH,QAAA,cAAoB,cAAQ,MAAM;AACtC,UAAM,QAAQ,YAAY,MAAM,GAAG,EAAE,IAAI,CAAC,UAAU;AAClD,YAAM,CAAC,KAAK,KAAK,IAAI,MAAM,MAAM,GAAG;AACpC,aAAO,EAAE,CAAC,GAAG,GAAG,MAAA;IAAM,CACvB;AACD,WAAO,OAAO,OAAO,CAAA,GAAI,GAAG,KAAK;EAAA,GAChC,CAAC,WAAW,CAAC;AAEhB,QAAM,EAAE,MAAM,OAAO,WAAA,IAAe,wBAAwB;IAC1D;IACA,QAAQ;EAAA,CACT;AAKD,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACU,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IACH;EACC,GAAA,CAAC,OAAO,gBAAgB,kBAAkB,CAAC;AAE9C,QAAM,EAAE,UAAU,CAAA,GAAI,WAAW,IAAI,QAAQ,CAAA;AAE7C,EAAM,gBAAU,MAAM;AACpB,QAAI,cAAc,WAAW,YAAY,KAAK,WAAW,OAAO,WAAW,WAAW;AACpF;QACE;UACE,YAAQ,qBAAU;YAChB,GAAG;YACH,MAAM,WAAW;UAAA,CAClB;QACH;QACA,EAAE,SAAS,KAAK;MAAA;IAEpB;EAAA,GACC,CAAC,YAAY,eAAe,OAAO,QAAQ,CAAC;AAEzC,QAAA,EAAE,UAAA,IAAc,gBAAgB,gBAAgB,CAAC,EAAE,WAAAC,WAAAA,OAAiB;IACxE,WAAAA;EACA,EAAA;AAEI,QAAA,mBAAmB,aAAa,gBAAgB,CAAC,EAAE,kBAAAC,kBAAAA,MAAuBA,iBAAgB;AAI1F,QAAA,eAAqB,cAAQ,MAAM;;AACjC,UAAA,UAAU,iBAAiB,wBAAwB;MACvD;MACA,QAAQ;IAAA,CACT;AAED,UAAM,mBAAmB,QAAQ,iBAAiB,IAAqB,CAAC,WAAW;;AAOjF,YAAM,cACJ,OAAO,OAAO,UAAU,WACpB;QACE,IAAI,iCAAiC,KAAK,IAAI,OAAO,IAAI;QACzD,gBAAgB,OAAO;MAAA,IAEzB,OAAO;AAEN,aAAA;QACL,GAAG;QACH,OAAO,cAAc,WAAW;QAChC,MAAM,GAAG,OAAO,IAAI,KAAGC,MAAA,OAAO,cAAP,gBAAAA,IAAkB,QAAO,IAAI,OAAO,UAAU,IAAI,KAAK,EAAE;MAAA;IAClF,CACD;AAEG,SAAAA,MAAA,iCAAQ,YAAR,gBAAAA,IAAiB,iBAAiB;AACpC,uBAAiB,KAAK;QACpB,WAAW;UACT,MAAM;QACR;QACA,MAAM;QACN,OAAO,cAAc;UACnB,IAAI,eAAe,sCAAsC;UACzD,gBAAgB;QAAA,CACjB;QACD,YAAY;QACZ,UAAU;MAAA,CACe;IAC7B;AAEO,WAAA;EAAA,GACN;IACD;IACA;IACA;IACA;KACA,sCAAQ,YAAR,mBAAiB;IACjB;EAAA,CACD;AAED,MAAI,YAAY;AACP,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEA,MAAI,OAAO;AACF,eAAA,wBAAC,KAAK,OAAL,CAAW,CAAA;EACrB;AAEM,QAAA,oBAAmB,iCAAQ,KAAK,gBAAe;AAE/C,QAAA,iBAAiB,CAAC,OAA6B,MAAM;AACzD,eAAW,uBAAuB;AACzB,aAAA;MACP,UAAU,GAAG,SAAS;MACtB,YAAQ,qBAAU,EAAE,SAAS,MAAM,QAAA,CAAS;IAAA,CAC7C;EAAA;AAID,aAAA,yBAAC,KAAK,MAAL,EACC,UAAA;QAAA,wBAAC,KAAK,OAAL,EAAY,UAAA,GAAG,gBAAgB,GAAA,CAAG;QACnC;MAAC;MAAA;QACC,eAAe,gBAAa,wBAAA,cAAA,CAAA,CAAa,IAAK;QAC9C,UAAU;UACR;YACE,IAAI,eAAe,gCAAgC;YACnD,gBACE;UACJ;UACA,EAAE,QAAQ,yCAAY,MAAM;QAC9B;QACA,OAAO;QACP,sBAAA,wBAAmB,YAAW,CAAA,CAAA;MAAA;IAChC;QACA;MAAC,QAAQ;MAAR;QACC,gBAEI,yBAAA,6BAAA,EAAA,UAAA;cAAC,wBAAA,eAAA,EAAc,MAAK,mBAAmB,CAAA;cACvC;YAAC;YAAA;cACC,YAAY;cACZ,cAAc,MAAM,oBAAoB,KAAK,MAAM;cACnD,SAAS,iBAAiB,IAAI,CAAC,WAAW,OAAO,IAAI;YAAA;UACvD;QAAA,EAAA,CACF;QAEF,kBAEK,yBAAA,6BAAA,EAAA,UAAA;UAAA,KAAK,SAAS,kBACb;YAAC;YAAA;cACC,UAAU,QAAQ,WAAW;cAC7B,OAAO;gBACL,EAAE,IAAI,8BAA8B,gBAAgB,sBAAsB;gBAC1E,EAAE,QAAQ,iBAAiB;cAC7B;cACA,aAAa,cAAc;gBACzB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,cAAa;YAAA;UACf;UAED,KAAK,SAAS,cAAc,aAC3B,wBAACC,aAAQ,EAAA,UAAU,QAAQ,WAAW,GAAG,OAAA,CAAgB,IACvD;QAAA,EAAA,CACN;MAAA;IAEJ;QACA,wBAAC,QAAQ,SAAR,EACC,cAAA,yBAAC,MAAK,EAAA,KAAK,GAAG,WAAU,UAAS,YAAW,WAC1C,UAAA;UAAC,yBAAA,MAAM,MAAN,EAAW,MAAM,SAAS,SAAS,cAAc,WAAW,YAC3D,UAAA;YAAA,wBAAC,iBAAgB,CAAA,CAAA;YACjB,yBAAC,MAAM,SAAN,EACC,UAAA;cAAC,yBAAA,MAAM,MAAN,EACC,UAAA;gBAAC,wBAAA,MAAM,oBAAN,CAAA,CAAyB;YACzB,aAAa,IAAI,CAAC,eAChB,wBAAA,MAAM,YAAN,EAAoC,GAAG,OAAA,GAAjB,OAAO,IAAkB,CACjD;UAAA,EAAA,CACH;cACA,wBAAC,MAAM,SAAN,CAAA,CAAc;cACf,wBAAC,MAAM,OAAN,EAAY,QAAQ,gBAAY,wBAAC,cAAa,EAAA,SAAQ,YAAA,CAAY,IAAK,KAAM,CAAA;cAAA,wBAC7E,MAAM,MAAN,EACE,UAAQ,QAAA,IAAI,CAAC,QAAQ;AAElB,uBAAA;cAAC,MAAM;cAAN;gBACC,QAAO;gBAEP,SAAS,eAAe,IAAI,UAAU;gBAEtC,UAAA;sBAAA,wBAAC,MAAM,cAAN,EAAmB,IAAI,IAAI,GAAA,CAAI;kBAC/B,aAAa,IAAI,CAAC,EAAE,eAAe,GAAG,OAAA,MAAa;AAC9C,wBAAA,OAAO,SAAS,UAAU;AACtB,4BAAA,EAAE,OAAW,IAAA;AAGjB,iCAAA,wBAAC,MAAM,MAAN,EACC,cAAA,wBAAC,gBAAe,EAAA,QAAgB,UAAU,cAAe,CAAA,EAD1C,GAAA,OAAO,IAExB;oBAEJ;AACA,wBAAI,CAAC,aAAa,WAAW,EAAE,SAAS,OAAO,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,GAAG;AAIlE,iCACG,wBAAA,MAAM,MAAN,EACC,cAAC,wBAAA,YAAA,EAAW,WAAU,cACnB,UAAI,IAAA,OAAO,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,IAC1B,eAAe,IAAI,OAAO,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC,CAAC,IAC7C,IACN,CAAA,EAAA,GALe,OAAO,IAMxB;oBAEJ;AACI,wBAAA,OAAO,kBAAkB,YAAY;AACvC,iCACG,wBAAA,MAAM,MAAN,EAEE,UAAc,cAAA,KAAK,QAAQ,EAAE,gBAAgB,MAAO,CAAA,EAAA,GAFtC,OAAO,IAGxB;oBAEJ;AAEE,+BAAA,wBAAC,MAAM,MAAN,EACC,cAAA;sBAAC;sBAAA;wBACC,SAAS,IAAI,OAAO,KAAK,MAAM,GAAG,EAAE,CAAC,CAAC;wBACtC,OAAO,IAAI;wBACV,GAAG;sBAAA;oBAAA,EAAA,GAJS,OAAO,IAMxB;kBAAA,CAEH;sBAEA,wBAAA,aAAA,EAAY,SAAS,CAAC,MAAM,EAAE,gBAAgB,GAC7C,cAAC,wBAAA,cAAA,EAAa,UAAU,IAAA,CAAK,EAC/B,CAAA;gBAAA;cAAA;cAjDK,IAAI;YAAA;UAoDd,CAAA,EAAA,CACH;QAAA,EAAA,CACF;MAAA,EAAA,CACF;UACA;QAAC,WAAW;QAAX;UACE,GAAG;UACJ,kBAAkB,MAAM,WAAW,kCAAkC;UAErE,UAAA;gBAAC,wBAAA,WAAW,UAAX,CAAA,CAAoB;gBACrB,wBAAC,WAAW,OAAX,CAAA,CAAiB;UAAA;QAAA;MACpB;IAAA,EAAA,CACF,EACF,CAAA;EACF,EAAA,CAAA;AAEJ;AAEA,IAAM,cAAc,GAAO,MAAM,IAAI;;;;AASrC,IAAM,kBAAkB,MAAM;;AAC5B,QAAM,YAAY,SAAS,mBAAmB,CAAC,UAAU,MAAM,SAAS;AACxE,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAA0D;AACxE,QAAA,UAAS,0CAAO,YAAP,mBAAgB,SAAhB,mBAAsB;AAC/B,QAAA,aAAa,QAAQ,MAAM;AAGjC,EAAM,gBAAU,MAAM;AACpB,QAAI,eAAe,QAAQ;AACzB,gBAAU,CAAE,CAAA;IACd;EACC,GAAA,CAAC,WAAW,YAAY,MAAM,CAAC;AAElC,aAAA,wBACG,MAAM,WAAN,EACC,cAAA,wBAAC,qBAAA,CAAA,CAAoB,EACvB,CAAA;AAEJ;AAQA,IAAM,eAAe,CAAC,EAAE,QAAA,MAAiC;AACjD,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAAoC;AAGtD,aAAA;IAAC;IAAA;MACC;MACA,KAAKC;MACL,SAAS,MAAM;AACb,mBAAW,mBAAmB,EAAE,QAAQ,QAAS,CAAA;MACnD;MACA,eAAA,wBAAY,eAAK,CAAA,CAAA;MACjB,OAAO,EAAE,gBAAgB,OAAO;MAChC,IAAI;QACF,UAAU;QACV,YAAQ,qBAAU,EAAE,SAAS,MAAM,QAAA,CAAS;MAC9C;MACA,UAAS;MACT,YAAY;MAEX,UAAc,cAAA;QACb,IAAI,eAAe,qCAAqC;QACxD,gBAAgB;MAAA,CACjB;IAAA;EAAA;AAGP;AAMA,IAAM,wBAAwB,MAAM;AAClC,QAAM,EAAE,OAAO,GAAG,IAAI,UAEnB;AACG,QAAA;IACJ,cAAc,CAAC;IACf;IACA;EAAA,IACE;IACF,YAAY,IAAI,CAAC,YAAY;MAC3B;MACA,SAAS;IAAA,EACT;EAAA;AAGJ,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEI,MAAA,SAAS,CAAC,MAAM;AACX,eAAA,wBAAC,KAAK,OAAL,CAAW,CAAA;EACrB;AAEA,aAAA,wBACG,KAAK,SAAL,EAAa,aACX,UAAA,CAAC,EAAE,aAAAC,aAAAA,UAAAA,wBACD,cAAa,EAAA,aAAaA,cACzB,cAAC,wBAAA,cAAA,CAAa,CAAA,EAAA,CAChB,EAEJ,CAAA;AAEJ;", "names": ["toString", "isOpen", "isEmpty", "isEqual", "canCreate", "runHookWaterfall", "_a", "Filters", "ReactRouterLink", "permissions"]}