const e={"BoundRoute.title":"綁定路徑到","EditForm.inputSelect.description.role":"將新的驗證使用者加入此身份。","EditForm.inputSelect.label.role":"驗證使用者預設身份","EditForm.inputToggle.description.email":"禁止使用者使用同一個電子郵件地址 + 不同的驗證方式註冊多個帳號","EditForm.inputToggle.description.email-confirmation":"當啟用後，新註冊的使用者將會收到一封認證郵件。","EditForm.inputToggle.description.email-confirmation-redirection":"認證完後後，使用者被重新導向的網址。","EditForm.inputToggle.description.email-reset-password":"您的應用程式的重設密碼頁面的網址","EditForm.inputToggle.description.sign-up":"當停用後，不論使用任何驗證方式使用者將無法註冊。","EditForm.inputToggle.label.email":"電子郵件地址單一帳號限制","EditForm.inputToggle.label.email-confirmation":"啟用電子郵件地址驗證","EditForm.inputToggle.label.email-confirmation-redirection":"重新導向網址","EditForm.inputToggle.label.email-reset-password":"密碼重設頁面","EditForm.inputToggle.label.sign-up":"啟用註冊","EditForm.inputToggle.placeholder.email-confirmation-redirection":"例如：https://yourfrontend.com/email-confirmation-redirection","EditForm.inputToggle.placeholder.email-reset-password":"例如：https://yourfrontend.com/reset-password","EditPage.form.roles":"角色詳細資訊","Email.template.data.loaded":"已載入電子郵件範本","Email.template.email_confirmation":"電子郵件地址確認","Email.template.form.edit.label":"編輯範本","Email.template.table.action.label":"操作","Email.template.table.icon.label":"圖示","Email.template.table.name.label":"名稱","Form.advancedSettings.data.loaded":"已載入進階設定資料","HeaderNav.link.advancedSettings":"進階設定","HeaderNav.link.emailTemplates":"郵件範本","HeaderNav.link.providers":"驗證方式","Plugin.permissions.plugins.description":"為 {name} 擴充功能定義所有可用的操作","Plugins.header.description":"只有綁定路徑的操作會顯示在下方","Plugins.header.title":"權限","Policies.header.hint":"選取應用程式或擴充功能的操作然後點擊齒輪圖示以顯示綁定路徑","Policies.header.title":"進階設定","PopUpForm.Email.email_templates.inputDescription":"如果您不確定要怎麼使用變數，請 {link}","PopUpForm.Email.link.documentation":"查閱我們的說明文件。","PopUpForm.Email.options.from.email.label":"寄件人地址","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"寄件人名稱","PopUpForm.Email.options.from.name.placeholder":"Kai Doe","PopUpForm.Email.options.message.label":"訊息","PopUpForm.Email.options.object.label":"主旨","PopUpForm.Email.options.object.placeholder":"請驗證 %APP_NAME% 的電子郵件地址","PopUpForm.Email.options.response_email.label":"回覆地址","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"如果停用，使用者將無法使用這個驗證方式","PopUpForm.Providers.enabled.label":"啟用","PopUpForm.Providers.key.label":"客戶端 ID","PopUpForm.Providers.key.placeholder":"TEXT","PopUpForm.Providers.redirectURL.front-end.label":"您應用程式的前端頁面網址","PopUpForm.Providers.redirectURL.label":"The redirect URL to add in your {provider} application configurations","PopUpForm.Providers.secret.label":"客戶端密鑰","PopUpForm.Providers.secret.placeholder":"TEXT","PopUpForm.Providers.subdomain.label":"Host URI (子網域)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"編輯郵件範本","PopUpForm.header.edit.providers":"編輯供應者","Providers.data.loaded":"已載入供應者","Providers.status":"狀態","Roles.empty":"您目前沒有任何角色。","Roles.empty.search":"沒有符合搜尋的角色。","Settings.roles.deleted":"已刪除角色","Settings.roles.edited":"已編輯角色","Settings.section-label":"使用者與權限外掛程式","components.Input.error.validation.email":"此電子郵件地址無效","components.Input.error.validation.json":"不符合 JSON 格式","components.Input.error.validation.max":"數值過高。","components.Input.error.validation.maxLength":"數值過長。","components.Input.error.validation.min":"數值過低。","components.Input.error.validation.minLength":"數值過短。","components.Input.error.validation.minSupMax":"Can't be superior","components.Input.error.validation.regex":"數值與 regex 不符。","components.Input.error.validation.required":"此數值為必填。","components.Input.error.validation.unique":"此值已被使用。","notification.success.submit":"設定已更新","page.title":"設定 - 角色","plugin.description.long":"使用 JWT 認證保護您的 API。這個擴充功能也使用 ACL 來讓你管理不同群組使用者的權限。","plugin.description.short":"使用 JWT 認證保護您的 API","plugin.name":"身份與權限","popUpWarning.button.cancel":"取消","popUpWarning.button.confirm":"確認","popUpWarning.title":"請確認","popUpWarning.warning.cancel":"您確定要取消變更嗎？"};export{e as default};
