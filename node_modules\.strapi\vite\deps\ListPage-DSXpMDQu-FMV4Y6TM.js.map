{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/services/auditLogs.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/hooks/useFormatTimeStamp.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/utils/getActionTypesDefaultMessages.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/components/Modal.tsx", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/hooks/useAuditLogsData.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/components/ComboboxFilter.tsx", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/utils/getDisplayedFilters.ts", "../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/AuditLogs/ListPage.tsx"], "sourcesContent": ["import { adminApi } from '../../../../admin/src/services/api';\nimport * as AuditLogs from '../../../../shared/contracts/audit-logs';\n\nconst auditLogsService = adminApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getAuditLogs: builder.query<AuditLogs.GetAll.Response, AuditLogs.GetAll.Request['query']>({\n      query: (params) => ({\n        url: `/admin/audit-logs`,\n        config: {\n          params,\n        },\n      }),\n    }),\n    getAuditLog: builder.query<AuditLogs.Get.Response, AuditLogs.Get.Params['id']>({\n      query: (id) => `/admin/audit-logs/${id}`,\n    }),\n  }),\n  overrideExisting: false,\n});\n\nconst { useGetAuditLogsQuery, useGetAuditLogQuery } = auditLogsService;\n\nexport { useGetAuditLogsQuery, useGetAuditLogQuery };\n", "import parseISO from 'date-fns/parseISO';\nimport { useIntl } from 'react-intl';\n\nexport const useFormatTimeStamp = () => {\n  const { formatDate } = useIntl();\n\n  const formatTimeStamp = (value: string) => {\n    const date = parseISO(value);\n\n    const formattedDate = formatDate(date, {\n      dateStyle: 'long',\n    });\n    const formattedTime = formatDate(date, {\n      timeStyle: 'medium',\n      hourCycle: 'h24',\n    });\n\n    return `${formattedDate}, ${formattedTime}`;\n  };\n\n  return formatTimeStamp;\n};\n", "export const actionTypes = {\n  'entry.create': 'Create entry{model, select, undefined {} other { ({model})}}',\n  'entry.update': 'Update entry{model, select, undefined {} other { ({model})}}',\n  'entry.delete': 'Delete entry{model, select, undefined {} other { ({model})}}',\n  'entry.publish': 'Publish entry{model, select, undefined {} other { ({model})}}',\n  'entry.unpublish': 'Unpublish entry{model, select, undefined {} other { ({model})}}',\n  'media.create': 'Create media',\n  'media.update': 'Update media',\n  'media.delete': 'Delete media',\n  'media-folder.create': 'Create media folder',\n  'media-folder.update': 'Update media folder',\n  'media-folder.delete': 'Delete media folder',\n  'user.create': 'Create user',\n  'user.update': 'Update user',\n  'user.delete': 'Delete user',\n  'admin.auth.success': 'Admin login',\n  'admin.logout': 'Admin logout',\n  'content-type.create': 'Create content type',\n  'content-type.update': 'Update content type',\n  'content-type.delete': 'Delete content type',\n  'component.create': 'Create component',\n  'component.update': 'Update component',\n  'component.delete': 'Delete component',\n  'role.create': 'Create role',\n  'role.update': 'Update role',\n  'role.delete': 'Delete role',\n  'permission.create': 'Create permission',\n  'permission.update': 'Update permission',\n  'permission.delete': 'Delete permission',\n};\n\nexport const getDefaultMessage = (value: keyof typeof actionTypes) => {\n  return actionTypes[value] || value;\n};\n", "import * as React from 'react';\n\nimport {\n  Box,\n  Flex,\n  Grid,\n  JSONInput,\n  Loader,\n  Modal as DSModal,\n  Typography,\n  Breadcrumbs,\n  Crumb,\n  Field,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useNotification } from '../../../../../../../../admin/src/features/Notifications';\nimport { useAPIErrorHandler } from '../../../../../../../../admin/src/hooks/useAPIErrorHandler';\nimport { AuditLog } from '../../../../../../../../shared/contracts/audit-logs';\nimport { useGetAuditLogQuery } from '../../../../../services/auditLogs';\nimport { useFormatTimeStamp } from '../hooks/useFormatTimeStamp';\nimport { actionTypes, getDefaultMessage } from '../utils/getActionTypesDefaultMessages';\n\ninterface ModalProps {\n  handleClose: () => void;\n  logId: string;\n}\n\nexport const Modal = ({ handleClose, logId }: ModalProps) => {\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { data, error, isLoading } = useGetAuditLogQuery(logId);\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n      handleClose();\n    }\n  }, [error, formatAPIError, handleClose, toggleNotification]);\n\n  const formatTimeStamp = useFormatTimeStamp();\n  const formattedDate = data && 'date' in data ? formatTimeStamp(data.date) : '';\n\n  return (\n    <DSModal.Root defaultOpen onOpenChange={handleClose}>\n      <DSModal.Content>\n        <DSModal.Header>\n          {/**\n           * TODO: this is not semantically correct and should be amended.\n           */}\n          <Breadcrumbs label={formattedDate} id=\"title\">\n            <Crumb isCurrent>{formattedDate}</Crumb>\n          </Breadcrumbs>\n        </DSModal.Header>\n        <DSModal.Body>\n          <ActionBody isLoading={isLoading} data={data as AuditLog} formattedDate={formattedDate} />\n        </DSModal.Body>\n      </DSModal.Content>\n    </DSModal.Root>\n  );\n};\n\ninterface ActionBodyProps {\n  isLoading?: boolean;\n  data: AuditLog;\n  formattedDate: string;\n}\n\nconst ActionBody = ({ isLoading, data, formattedDate }: ActionBodyProps) => {\n  const { formatMessage } = useIntl();\n\n  if (isLoading) {\n    return (\n      <Flex padding={7} justifyContent=\"center\" alignItems=\"center\">\n        {/**\n         * TODO: this will need to be translated.\n         */}\n        <Loader>Loading content...</Loader>\n      </Flex>\n    );\n  }\n\n  const { action, user, payload } = data;\n\n  return (\n    <>\n      <Box marginBottom={3}>\n        <Typography variant=\"delta\" id=\"title\">\n          {formatMessage({\n            id: 'Settings.permissions.auditLogs.details',\n            defaultMessage: 'Log Details',\n          })}\n        </Typography>\n      </Box>\n      <Grid.Root\n        gap={4}\n        gridCols={2}\n        paddingTop={4}\n        paddingBottom={4}\n        paddingLeft={6}\n        paddingRight={6}\n        marginBottom={4}\n        background=\"neutral100\"\n        hasRadius\n      >\n        <ActionItem\n          actionLabel={formatMessage({\n            id: 'Settings.permissions.auditLogs.action',\n            defaultMessage: 'Action',\n          })}\n          actionName={formatMessage(\n            {\n              id: `Settings.permissions.auditLogs.${action}`,\n              defaultMessage: getDefaultMessage(action as keyof typeof actionTypes),\n            },\n            // @ts-expect-error - any\n            { model: payload?.model }\n          )}\n        />\n        <ActionItem\n          actionLabel={formatMessage({\n            id: 'Settings.permissions.auditLogs.date',\n            defaultMessage: 'Date',\n          })}\n          actionName={formattedDate}\n        />\n        <ActionItem\n          actionLabel={formatMessage({\n            id: 'Settings.permissions.auditLogs.user',\n            defaultMessage: 'User',\n          })}\n          actionName={user?.displayName || '-'}\n        />\n        <ActionItem\n          actionLabel={formatMessage({\n            id: 'Settings.permissions.auditLogs.userId',\n            defaultMessage: 'User ID',\n          })}\n          actionName={user?.id.toString() || '-'}\n        />\n      </Grid.Root>\n      <Field.Root>\n        <Field.Label>\n          {formatMessage({\n            id: 'Settings.permissions.auditLogs.payload',\n            defaultMessage: 'Payload',\n          })}\n        </Field.Label>\n        <Payload value={JSON.stringify(payload, null, 2)} disabled />\n      </Field.Root>\n    </>\n  );\n};\n\nconst Payload = styled(JSONInput)`\n  max-width: 100%;\n  overflow: scroll;\n`;\n\ninterface ActionItemProps {\n  actionLabel: string;\n  actionName: string;\n}\n\nconst ActionItem = ({ actionLabel, actionName }: ActionItemProps) => {\n  return (\n    <Flex direction=\"column\" alignItems=\"baseline\" gap={1}>\n      <Typography textColor=\"neutral600\" variant=\"sigma\">\n        {actionLabel}\n      </Typography>\n      <Typography textColor=\"neutral600\">{actionName}</Typography>\n    </Flex>\n  );\n};\n", "import * as React from 'react';\n\nimport { useNotification } from '../../../../../../../../admin/src/features/Notifications';\nimport { useAPIErrorHandler } from '../../../../../../../../admin/src/hooks/useAPIErrorHandler';\nimport { useQueryParams } from '../../../../../../../../admin/src/hooks/useQueryParams';\nimport { useAdminUsers } from '../../../../../../../../admin/src/services/users';\nimport { useGetAuditLogsQuery } from '../../../../../services/auditLogs';\n\nexport const useAuditLogsData = ({\n  canReadAuditLogs,\n  canReadUsers,\n}: {\n  canReadAuditLogs: boolean;\n  canReadUsers: boolean;\n}) => {\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n  const [{ query }] = useQueryParams();\n\n  const {\n    data,\n    error,\n    isError: isUsersError,\n    isLoading: isLoadingUsers,\n  } = useAdminUsers(\n    {},\n    {\n      skip: !canReadUsers,\n      refetchOnMountOrArgChange: true,\n    }\n  );\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({ type: 'danger', message: formatAPIError(error) });\n    }\n  }, [error, toggleNotification, formatAPIError]);\n\n  const {\n    data: auditLogs,\n    isLoading: isLoadingAuditLogs,\n    isError: isAuditLogsError,\n    error: auditLogsError,\n  } = useGetAuditLogsQuery(query, {\n    refetchOnMountOrArgChange: true,\n    skip: !canReadAuditLogs,\n  });\n\n  React.useEffect(() => {\n    if (auditLogsError) {\n      toggleNotification({ type: 'danger', message: formatAPIError(auditLogsError) });\n    }\n  }, [auditLogsError, toggleNotification, formatAPIError]);\n\n  return {\n    auditLogs,\n    users: data?.users ?? [],\n    isLoading: isLoadingUsers || isLoadingAuditLogs,\n    hasError: isAuditLogsError || isUsersError,\n  };\n};\n", "import { Combobox, ComboboxOption } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { Filters } from '../../../../../../../../admin/src/components/Filters';\nimport { useField } from '../../../../../../../../admin/src/components/Form';\n\nexport const ComboboxFilter = (props: Filters.ValueInputProps) => {\n  const { formatMessage } = useIntl();\n  const field = useField(props.name);\n  const ariaLabel = formatMessage({\n    id: 'Settings.permissions.auditLogs.filter.aria-label',\n    defaultMessage: 'Search and select an option to filter',\n  });\n\n  const handleChange = (value?: string) => {\n    field.onChange(props.name, value);\n  };\n\n  return (\n    <Combobox aria-label={ariaLabel} value={field.value} onChange={handleChange}>\n      {props.options?.map((opt) => {\n        const value = typeof opt === 'string' ? opt : opt.value;\n        const label = typeof opt === 'string' ? opt : opt.label;\n        return (\n          <ComboboxOption key={value} value={value}>\n            {label}\n          </ComboboxOption>\n        );\n      })}\n    </Combobox>\n  );\n};\n", "import { IntlShape } from 'react-intl';\n\nimport { Filters } from '../../../../../../../../admin/src/components/Filters';\nimport { getDisplayName } from '../../../../../../../../admin/src/utils/users';\nimport { SanitizedAdminUser } from '../../../../../../../../shared/contracts/shared';\nimport { ComboboxFilter } from '../components/ComboboxFilter';\n\nimport { actionTypes, getDefaultMessage } from './getActionTypesDefaultMessages';\n\nexport const getDisplayedFilters = ({\n  formatMessage,\n  users,\n  canReadUsers,\n}: {\n  formatMessage: IntlShape['formatMessage'];\n  users: SanitizedAdminUser[];\n  canReadUsers: boolean;\n}): Filters.Filter[] => {\n  const operators = [\n    {\n      label: formatMessage({\n        id: 'components.FilterOptions.FILTER_TYPES.$eq',\n        defaultMessage: 'is',\n      }),\n      value: '$eq',\n    },\n    {\n      label: formatMessage({\n        id: 'components.FilterOptions.FILTER_TYPES.$ne',\n        defaultMessage: 'is not',\n      }),\n      value: '$ne',\n    },\n  ] as NonNullable<Filters.Filter['operators']>;\n\n  const filters = [\n    {\n      input: ComboboxFilter,\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.action',\n        defaultMessage: 'Action',\n      }),\n      name: 'action',\n      operators,\n      options: (Object.keys(actionTypes) as (keyof typeof actionTypes)[]).map((action) => ({\n        label: formatMessage(\n          {\n            id: `Settings.permissions.auditLogs.${action}`,\n            defaultMessage: getDefaultMessage(action),\n          },\n          { model: undefined }\n        ),\n        value: action,\n      })),\n      type: 'enumeration',\n    },\n    {\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.date',\n        defaultMessage: 'Date',\n      }),\n      name: 'date',\n      type: 'datetime',\n    },\n  ] satisfies Filters.Filter[];\n\n  if (canReadUsers && users) {\n    return [\n      ...filters,\n      {\n        input: ComboboxFilter,\n        label: formatMessage({\n          id: 'Settings.permissions.auditLogs.user',\n          defaultMessage: 'User',\n        }),\n        mainField: { name: 'id', type: 'integer' },\n        name: 'user',\n        operators,\n        options: users.map((user) => ({\n          label: getDisplayName(user),\n          value: user.id.toString(),\n        })),\n        type: 'relation',\n      } satisfies Filters.Filter,\n    ];\n  }\n\n  return filters;\n};\n", "import { <PERSON><PERSON>, <PERSON><PERSON>B<PERSON>on, Typography } from '@strapi/design-system';\nimport { Eye } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport { Filters } from '../../../../../../../admin/src/components/Filters';\nimport { Layouts } from '../../../../../../../admin/src/components/Layouts/Layout';\nimport { Page } from '../../../../../../../admin/src/components/PageHelpers';\nimport { Pagination } from '../../../../../../../admin/src/components/Pagination';\nimport { Table } from '../../../../../../../admin/src/components/Table';\nimport { useTypedSelector } from '../../../../../../../admin/src/core/store/hooks';\nimport { useQueryParams } from '../../../../../../../admin/src/hooks/useQueryParams';\nimport { useRBAC } from '../../../../../../../admin/src/hooks/useRBAC';\nimport { AuditLog } from '../../../../../../../shared/contracts/audit-logs';\n\nimport { Modal } from './components/Modal';\nimport { useAuditLogsData } from './hooks/useAuditLogsData';\nimport { useFormatTimeStamp } from './hooks/useFormatTimeStamp';\nimport { getDefaultMessage } from './utils/getActionTypesDefaultMessages';\nimport { getDisplayedFilters } from './utils/getDisplayedFilters';\n\nconst ListPage = () => {\n  const { formatMessage } = useIntl();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings);\n\n  const {\n    allowedActions: { canRead: canReadAuditLogs, canReadUsers },\n    isLoading: isLoadingRBAC,\n  } = useRBAC({\n    ...permissions?.auditLogs,\n    readUsers: permissions?.users.read || [],\n  });\n\n  const [{ query }, setQuery] = useQueryParams<{ id?: AuditLog['id'] }>();\n  const {\n    auditLogs,\n    users,\n    isLoading: isLoadingData,\n    hasError,\n  } = useAuditLogsData({\n    canReadAuditLogs,\n    canReadUsers,\n  });\n\n  const formatTimeStamp = useFormatTimeStamp();\n\n  const displayedFilters = getDisplayedFilters({ formatMessage, users, canReadUsers });\n\n  const headers: Table.Header<AuditLog, object>[] = [\n    {\n      name: 'action',\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.action',\n        defaultMessage: 'Action',\n      }),\n      sortable: true,\n    },\n    {\n      name: 'date',\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.date',\n        defaultMessage: 'Date',\n      }),\n      sortable: true,\n    },\n    {\n      name: 'user',\n      label: formatMessage({\n        id: 'Settings.permissions.auditLogs.user',\n        defaultMessage: 'User',\n      }),\n      sortable: false,\n      // In this case, the passed parameter cannot and shouldn't be something else than User\n      cellFormatter: ({ user }) => (user ? user.displayName : ''),\n    },\n  ];\n\n  if (hasError) {\n    return <Page.Error />;\n  }\n\n  const isLoading = isLoadingData || isLoadingRBAC;\n\n  const { results = [] } = auditLogs ?? {};\n\n  return (\n    <Page.Main aria-busy={isLoading}>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: formatMessage({\n              id: 'global.auditLogs',\n              defaultMessage: 'Audit Logs',\n            }),\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        title={formatMessage({\n          id: 'global.auditLogs',\n          defaultMessage: 'Audit Logs',\n        })}\n        subtitle={formatMessage({\n          id: 'Settings.permissions.auditLogs.listview.header.subtitle',\n          defaultMessage: 'Logs of all the activities that happened in your environment',\n        })}\n      />\n      <Layouts.Action\n        startActions={\n          <Filters.Root options={displayedFilters}>\n            <Filters.Trigger />\n            <Filters.Popover />\n            <Filters.List />\n          </Filters.Root>\n        }\n      />\n      <Layouts.Content>\n        <Table.Root rows={results} headers={headers} isLoading={isLoading}>\n          <Table.Content>\n            <Table.Head>\n              {headers.map((header) => (\n                <Table.HeaderCell key={header.name} {...header} />\n              ))}\n            </Table.Head>\n            <Table.Empty />\n            <Table.Loading />\n            <Table.Body>\n              {results.map((log) => (\n                <Table.Row key={log.id} onClick={() => setQuery({ id: log.id })}>\n                  {headers.map((header) => {\n                    const { name, cellFormatter } = header;\n\n                    switch (name) {\n                      case 'action':\n                        return (\n                          <Table.Cell key={name}>\n                            <Typography textColor=\"neutral800\">\n                              {formatMessage(\n                                {\n                                  id: `Settings.permissions.auditLogs.${log.action}`,\n                                  // @ts-expect-error – getDefaultMessage probably doesn't benefit from being so strongly typed unless we just add string at the end.\n                                  defaultMessage: getDefaultMessage(log.action),\n                                },\n                                { model: (log.payload?.model as string) ?? '' }\n                              )}\n                            </Typography>\n                          </Table.Cell>\n                        );\n                      case 'date':\n                        return (\n                          <Table.Cell key={name}>\n                            <Typography textColor=\"neutral800\">\n                              {formatTimeStamp(log.date)}\n                            </Typography>\n                          </Table.Cell>\n                        );\n                      case 'user':\n                        return (\n                          <Table.Cell key={name}>\n                            <Typography textColor=\"neutral800\">\n                              {cellFormatter ? cellFormatter(log, header) : '-'}\n                            </Typography>\n                          </Table.Cell>\n                        );\n                      default:\n                        return (\n                          <Table.Cell key={name}>\n                            <Typography textColor=\"neutral800\">\n                              {(log[name as keyof AuditLog] as string) || '-'}\n                            </Typography>\n                          </Table.Cell>\n                        );\n                    }\n                  })}\n                  <Table.Cell onClick={(e) => e.stopPropagation()}>\n                    <Flex justifyContent=\"end\">\n                      <IconButton\n                        onClick={() => setQuery({ id: log.id })}\n                        withTooltip={false}\n                        label={formatMessage(\n                          { id: 'app.component.table.view', defaultMessage: '{target} details' },\n                          { target: `${log.action} action` }\n                        )}\n                        variant=\"ghost\"\n                      >\n                        <Eye />\n                      </IconButton>\n                    </Flex>\n                  </Table.Cell>\n                </Table.Row>\n              ))}\n            </Table.Body>\n          </Table.Content>\n        </Table.Root>\n\n        <Pagination.Root {...auditLogs?.pagination}>\n          <Pagination.PageSize />\n          <Pagination.Links />\n        </Pagination.Root>\n      </Layouts.Content>\n      {query?.id && (\n        <Modal handleClose={() => setQuery({ id: '' }, 'remove')} logId={query.id.toString()} />\n      )}\n    </Page.Main>\n  );\n};\n\nconst ProtectedListPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.auditLogs?.main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListPage />\n    </Page.Protect>\n  );\n};\n\nexport { ListPage, ProtectedListPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGA,IAAM,mBAAmB,SAAS,gBAAgB;EAChD,WAAW,CAAC,aAAa;IACvB,cAAc,QAAQ,MAAoE;MACxF,OAAO,CAAC,YAAY;QAClB,KAAK;QACL,QAAQ;UACN;QAAA;MACF;IACF,CACD;IACD,aAAa,QAAQ,MAA0D;MAC7E,OAAO,CAAC,OAAO,qBAAqB,EAAE;IAAA,CACvC;EAAA;EAEH,kBAAkB;AACpB,CAAC;AAED,IAAM,EAAE,sBAAsB,oBAAA,IAAwB;ACjB/C,IAAM,qBAAqB,MAAM;AAChC,QAAA,EAAE,WAAW,IAAI,QAAQ;AAEzB,QAAA,kBAAkB,CAAC,UAAkB;AACnC,UAAA,OAAO,SAAS,KAAK;AAErB,UAAA,gBAAgB,WAAW,MAAM;MACrC,WAAW;IAAA,CACZ;AACK,UAAA,gBAAgB,WAAW,MAAM;MACrC,WAAW;MACX,WAAW;IAAA,CACZ;AAEM,WAAA,GAAG,aAAa,KAAK,aAAa;EAAA;AAGpC,SAAA;AACT;ACrBO,IAAM,cAAc;EACzB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,iBAAiB;EACjB,mBAAmB;EACnB,gBAAgB;EAChB,gBAAgB;EAChB,gBAAgB;EAChB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;EACvB,eAAe;EACf,eAAe;EACf,eAAe;EACf,sBAAsB;EACtB,gBAAgB;EAChB,uBAAuB;EACvB,uBAAuB;EACvB,uBAAuB;EACvB,oBAAoB;EACpB,oBAAoB;EACpB,oBAAoB;EACpB,eAAe;EACf,eAAe;EACf,eAAe;EACf,qBAAqB;EACrB,qBAAqB;EACrB,qBAAqB;AACvB;AAEa,IAAA,oBAAoB,CAAC,UAAoC;AAC7D,SAAA,YAAY,KAAK,KAAK;AAC/B;ACJO,IAAMA,SAAQ,CAAC,EAAE,aAAa,MAAA,MAAwB;AACrD,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AAEvE,QAAM,EAAE,MAAM,OAAO,UAAU,IAAI,oBAAoB,KAAK;AAE5D,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACU,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;AACW,kBAAA;IAAA;EACd,GACC,CAAC,OAAO,gBAAgB,aAAa,kBAAkB,CAAC;AAE3D,QAAM,kBAAkB,mBAAmB;AAC3C,QAAM,gBAAgB,QAAQ,UAAU,OAAO,gBAAgB,KAAK,IAAI,IAAI;AAG1E,aAAA,wBAACC,MAAQ,MAAR,EAAa,aAAW,MAAC,cAAc,aACtC,cAAA,yBAACA,MAAQ,SAAR,EACC,UAAA;QAAA,wBAACA,MAAQ,QAAR,EAIC,cAAA,wBAAC,aAAA,EAAY,OAAO,eAAe,IAAG,SACpC,cAAA,wBAAC,OAAM,EAAA,WAAS,MAAE,UAAA,cAAA,CAAc,EAAA,CAClC,EACF,CAAA;QACA,wBAACA,MAAQ,MAAR,EACC,cAAA,wBAAC,YAAW,EAAA,WAAsB,MAAwB,cAAA,CAA8B,EAC1F,CAAA;EAAA,EAAA,CACF,EACF,CAAA;AAEJ;AAQA,IAAM,aAAa,CAAC,EAAE,WAAW,MAAM,cAAA,MAAqC;AACpE,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,MAAI,WAAW;AAEX,eAAA,wBAAC,MAAK,EAAA,SAAS,GAAG,gBAAe,UAAS,YAAW,UAInD,cAAA,wBAAC,QAAO,EAAA,UAAA,qBAAA,CAAkB,EAC5B,CAAA;EAAA;AAIJ,QAAM,EAAE,QAAQ,MAAM,QAAA,IAAY;AAElC,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAC,wBAAA,KAAA,EAAI,cAAc,GACjB,cAAA,wBAAC,YAAA,EAAW,SAAQ,SAAQ,IAAG,SAC5B,UAAc,cAAA;MACb,IAAI;MACJ,gBAAgB;IAAA,CACjB,EAAA,CACH,EACF,CAAA;QACA;MAAC,KAAK;MAAL;QACC,KAAK;QACL,UAAU;QACV,YAAY;QACZ,eAAe;QACf,aAAa;QACb,cAAc;QACd,cAAc;QACd,YAAW;QACX,WAAS;QAET,UAAA;cAAA;YAAC;YAAA;cACC,aAAa,cAAc;gBACzB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,YAAY;gBACV;kBACE,IAAI,kCAAkC,MAAM;kBAC5C,gBAAgB,kBAAkB,MAAkC;gBAAA;;gBAGtE,EAAE,OAAO,mCAAS,MAAM;cAAA;YAC1B;UAAA;cAEF;YAAC;YAAA;cACC,aAAa,cAAc;gBACzB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,YAAY;YAAA;UAAA;cAEd;YAAC;YAAA;cACC,aAAa,cAAc;gBACzB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,aAAY,6BAAM,gBAAe;YAAA;UAAA;cAEnC;YAAC;YAAA;cACC,aAAa,cAAc;gBACzB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,aAAY,6BAAM,GAAG,eAAc;YAAA;UAAA;QACrC;MAAA;IAAA;QAEF,yBAAC,MAAM,MAAN,EACC,UAAA;UAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;UACA,wBAAC,SAAQ,EAAA,OAAO,KAAK,UAAU,SAAS,MAAM,CAAC,GAAG,UAAQ,KAAC,CAAA;IAAA,EAC7D,CAAA;EAAA,EACF,CAAA;AAEJ;AAEA,IAAM,UAAU,GAAO,SAAS;;;;AAUhC,IAAM,aAAa,CAAC,EAAE,aAAa,WAAA,MAAkC;AACnE,aAAA,yBACG,MAAK,EAAA,WAAU,UAAS,YAAW,YAAW,KAAK,GAClD,UAAA;QAAA,wBAAC,YAAW,EAAA,WAAU,cAAa,SAAQ,SACxC,UACH,YAAA,CAAA;QACC,wBAAA,YAAA,EAAW,WAAU,cAAc,UAAW,WAAA,CAAA;EAAA,EACjD,CAAA;AAEJ;AC1KO,IAAM,mBAAmB,CAAC;EAC/B;EACA;AACF,MAGM;AACE,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AACvE,QAAM,CAAC,EAAE,MAAM,CAAC,IAAI,eAAe;AAE7B,QAAA;IACJ;IACA;IACA,SAAS;IACT,WAAW;EAAA,IACT;IACF,CAAA;IACA;MACE,MAAM,CAAC;MACP,2BAA2B;IAAA;EAC7B;AAGF,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACT,yBAAmB,EAAE,MAAM,UAAU,SAAS,eAAe,KAAK,EAAA,CAAG;IAAA;EACvE,GACC,CAAC,OAAO,oBAAoB,cAAc,CAAC;AAExC,QAAA;IACJ,MAAM;IACN,WAAW;IACX,SAAS;IACT,OAAO;EAAA,IACL,qBAAqB,OAAO;IAC9B,2BAA2B;IAC3B,MAAM,CAAC;EAAA,CACR;AAED,EAAM,gBAAU,MAAM;AACpB,QAAI,gBAAgB;AAClB,yBAAmB,EAAE,MAAM,UAAU,SAAS,eAAe,cAAc,EAAA,CAAG;IAAA;EAChF,GACC,CAAC,gBAAgB,oBAAoB,cAAc,CAAC;AAEhD,SAAA;IACL;IACA,QAAO,6BAAM,UAAS,CAAA;IACtB,WAAW,kBAAkB;IAC7B,UAAU,oBAAoB;EAAA;AAElC;ACtDa,IAAA,iBAAiB,CAAC,UAAmC;;AAC1D,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,QAAQ,SAAS,MAAM,IAAI;AACjC,QAAM,YAAY,cAAc;IAC9B,IAAI;IACJ,gBAAgB;EAAA,CACjB;AAEK,QAAA,eAAe,CAAC,UAAmB;AACjC,UAAA,SAAS,MAAM,MAAM,KAAK;EAAA;AAGlC,aACG,wBAAA,UAAA,EAAS,cAAY,WAAW,OAAO,MAAM,OAAO,UAAU,cAC5D,WAAA,WAAM,YAAN,mBAAe,IAAI,CAAC,QAAQ;AAC3B,UAAM,QAAQ,OAAO,QAAQ,WAAW,MAAM,IAAI;AAClD,UAAM,QAAQ,OAAO,QAAQ,WAAW,MAAM,IAAI;AAClD,eACG,wBAAA,QAAA,EAA2B,OACzB,UAAA,MAAA,GADkB,KAErB;EAAA,GAGN,CAAA;AAEJ;ACtBO,IAAM,sBAAsB,CAAC;EAClC;EACA;EACA;AACF,MAIwB;AACtB,QAAM,YAAY;IAChB;MACE,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,OAAO;IAAA;IAET;MACE,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,OAAO;IAAA;EACT;AAGF,QAAM,UAAU;IACd;MACE,OAAO;MACP,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,MAAM;MACN;MACA,SAAU,OAAO,KAAK,WAAW,EAAmC,IAAI,CAAC,YAAY;QACnF,OAAO;UACL;YACE,IAAI,kCAAkC,MAAM;YAC5C,gBAAgB,kBAAkB,MAAM;UAAA;UAE1C,EAAE,OAAO,OAAU;QAAA;QAErB,OAAO;MAAA,EACP;MACF,MAAM;IAAA;IAER;MACE,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,MAAM;MACN,MAAM;IAAA;EACR;AAGF,MAAI,gBAAgB,OAAO;AAClB,WAAA;MACL,GAAG;MACH;QACE,OAAO;QACP,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,WAAW,EAAE,MAAM,MAAM,MAAM,UAAU;QACzC,MAAM;QACN;QACA,SAAS,MAAM,IAAI,CAAC,UAAU;UAC5B,OAAO,eAAe,IAAI;UAC1B,OAAO,KAAK,GAAG,SAAS;QAAA,EACxB;QACF,MAAM;MAAA;IACR;EACF;AAGK,SAAA;AACT;ACpEA,IAAM,WAAW,MAAM;AACf,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,cAAc,iBAAiB,CAAC,UAAU,MAAM,UAAU,YAAY,QAAQ;AAE9E,QAAA;IACJ,gBAAgB,EAAE,SAAS,kBAAkB,aAAa;IAC1D,WAAW;EAAA,IACT,QAAQ;IACV,GAAG,2CAAa;IAChB,YAAW,2CAAa,MAAM,SAAQ,CAAA;EAAC,CACxC;AAED,QAAM,CAAC,EAAE,MAAA,GAAS,QAAQ,IAAI,eAAwC;AAChE,QAAA;IACJ;IACA;IACA,WAAW;IACX;EAAA,IACE,iBAAiB;IACnB;IACA;EAAA,CACD;AAED,QAAM,kBAAkB,mBAAmB;AAE3C,QAAM,mBAAmB,oBAAoB,EAAE,eAAe,OAAO,aAAA,CAAc;AAEnF,QAAM,UAA4C;IAChD;MACE,MAAM;MACN,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,UAAU;IAAA;IAEZ;MACE,MAAM;MACN,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,UAAU;IAAA;IAEZ;MACE,MAAM;MACN,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,UAAU;;MAEV,eAAe,CAAC,EAAE,KAAA,MAAY,OAAO,KAAK,cAAc;IAAA;EAC1D;AAGF,MAAI,UAAU;AACL,eAAA,wBAAC,KAAK,OAAL,CAAA,CAAW;EAAA;AAGrB,QAAM,YAAY,iBAAiB;AAEnC,QAAM,EAAE,UAAU,CAAA,EAAG,IAAI,aAAa,CAAA;AAEtC,aACG,yBAAA,KAAK,MAAL,EAAU,aAAW,WACpB,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM,cAAc;UAClB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH,EAEJ,CAAA;QACA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IAAA;QAEH;MAAC,QAAQ;MAAR;QACC,kBACG,yBAAA,QAAQ,MAAR,EAAa,SAAS,kBACrB,UAAA;cAAC,wBAAA,QAAQ,SAAR,CAAA,CAAgB;cACjB,wBAAC,QAAQ,SAAR,CAAA,CAAgB;cACjB,wBAAC,QAAQ,MAAR,CAAA,CAAa;QAAA,EAChB,CAAA;MAAA;IAAA;QAGJ,yBAAC,QAAQ,SAAR,EACC,UAAA;UAAC,wBAAA,MAAM,MAAN,EAAW,MAAM,SAAS,SAAkB,WAC3C,cAAA,yBAAC,MAAM,SAAN,EACC,UAAA;YAAA,wBAAC,MAAM,MAAN,EACE,UAAQ,QAAA,IAAI,CAAC,eACZ,wBAAC,MAAM,YAAN,EAAoC,GAAG,OAAA,GAAjB,OAAO,IAAkB,CACjD,EACH,CAAA;YACA,wBAAC,MAAM,OAAN,CAAA,CAAY;YACb,wBAAC,MAAM,SAAN,CAAA,CAAc;YACf,wBAAC,MAAM,MAAN,EACE,UAAA,QAAQ,IAAI,CAAC,YACX,yBAAA,MAAM,KAAN,EAAuB,SAAS,MAAM,SAAS,EAAE,IAAI,IAAI,GAAA,CAAI,GAC3D,UAAA;UAAQ,QAAA,IAAI,CAAC,WAAW;;AACjB,kBAAA,EAAE,MAAM,cAAA,IAAkB;AAEhC,oBAAQ,MAAM;cACZ,KAAK;AACH,2BAAA,wBACG,MAAM,MAAN,EACC,cAAC,wBAAA,YAAA,EAAW,WAAU,cACnB,UAAA;kBACC;oBACE,IAAI,kCAAkC,IAAI,MAAM;;oBAEhD,gBAAgB,kBAAkB,IAAI,MAAM;kBAAA;kBAE9C,EAAE,SAAQ,SAAI,YAAJ,mBAAa,UAAoB,GAAG;gBAAA,EAChD,CACF,EAAA,GAVe,IAWjB;cAEJ,KAAK;AACH,2BACG,wBAAA,MAAM,MAAN,EACC,cAAC,wBAAA,YAAA,EAAW,WAAU,cACnB,UAAgB,gBAAA,IAAI,IAAI,EAAA,CAC3B,EAAA,GAHe,IAIjB;cAEJ,KAAK;AACH,2BACG,wBAAA,MAAM,MAAN,EACC,cAAA,wBAAC,YAAW,EAAA,WAAU,cACnB,UAAA,gBAAgB,cAAc,KAAK,MAAM,IAAI,IAAA,CAChD,EAAA,GAHe,IAIjB;cAEJ;AACE,2BACG,wBAAA,MAAM,MAAN,EACC,cAAC,wBAAA,YAAA,EAAW,WAAU,cAClB,UAAI,IAAA,IAAsB,KAAgB,IAAA,CAC9C,EAAA,GAHe,IAIjB;YAAA;UAEN,CACD;cACA,wBAAA,MAAM,MAAN,EAAW,SAAS,CAAC,MAAM,EAAE,gBAAgB,GAC5C,cAAC,wBAAA,MAAA,EAAK,gBAAe,OACnB,cAAA;YAAC;YAAA;cACC,SAAS,MAAM,SAAS,EAAE,IAAI,IAAI,GAAA,CAAI;cACtC,aAAa;cACb,OAAO;gBACL,EAAE,IAAI,4BAA4B,gBAAgB,mBAAmB;gBACrE,EAAE,QAAQ,GAAG,IAAI,MAAM,UAAU;cAAA;cAEnC,SAAQ;cAER,cAAA,wBAAC,eAAI,CAAA,CAAA;YAAA;UAAA,EAAA,CAET,EACF,CAAA;QAAA,EA5Dc,GAAA,IAAI,EA6DpB,CACD,EACH,CAAA;MAAA,EAAA,CACF,EACF,CAAA;UAAA,yBAEC,WAAW,MAAX,EAAiB,GAAG,uCAAW,YAC9B,UAAA;YAAC,wBAAA,WAAW,UAAX,CAAA,CAAoB;YACrB,wBAAC,WAAW,OAAX,CAAA,CAAiB;MAAA,EACpB,CAAA;IAAA,EACF,CAAA;KACC,+BAAO,WACN,wBAACD,QAAM,EAAA,aAAa,MAAM,SAAS,EAAE,IAAI,GAAA,GAAM,QAAQ,GAAG,OAAO,MAAM,GAAG,SAAA,EAAY,CAAA;EAAA,EAE1F,CAAA;AAEJ;AAEA,IAAM,oBAAoB,MAAM;AAC9B,QAAM,cAAc;IAClB,CAAC,UAAU;;AAAA,+BAAM,UAAU,YAAY,aAA5B,mBAAsC,cAAtC,mBAAiD;;EAAA;AAG9D,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,UAAA,CAAA,CAAS,EACZ,CAAA;AAEJ;", "names": ["Modal", "DSModal"]}