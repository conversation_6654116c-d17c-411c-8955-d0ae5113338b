import {
  Route,
  Routes
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/review-workflows/dist/_chunks/router-Cr3nulh9.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var ProtectedListPage = (0, import_react.lazy)(
  () => import("./index-CGmh3cED-RXULSNSY.js").then((mod) => ({ default: mod.ProtectedListPage }))
);
var ProtectedEditPage = (0, import_react.lazy)(
  () => import("./id-CtcCl3zh-W3MKEEJR.js").then((mod) => ({ default: mod.ProtectedEditPage }))
);
var routes = [
  {
    path: "/",
    Component: ProtectedListPage
  },
  {
    path: ":id",
    Component: ProtectedEditPage
  }
];
var Router = () => (0, import_jsx_runtime.jsx)(Routes, { children: routes.map((route) => (0, import_jsx_runtime.jsx)(Route, { ...route }, route.path)) });
export {
  Router
};
//# sourceMappingURL=router-Cr3nulh9-AWQPWGQO.js.map
