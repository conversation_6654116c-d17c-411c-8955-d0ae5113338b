import{gw as fe,m as e,n as ae,b9 as Te,gn as $,c as Ce,al as Me,a as pe,e as Ae,r as D,go as Ee,gy as De,gE as Ie,cT as Oe,O as Pe,A as _e,N as Fe,bh as Le,w as P,G as Y,Q as he,gx as Ne,bF as W,aC as Q,d as qe,x as C,aL as we,gF as me,S as _,aM as be,s as H,a9 as ve,aa as ne,ba as ye,J,eE as We,eF as $e,a4 as Be,V as de,gG as U,e_ as Z,gH as Ge,gI as xe,I as ke,f4 as He,gJ as Ve,z as Ke,ae as ze,b5 as ie,b2 as Ue,b6 as je,gK as Ye,gL as ce,co as Qe,gM as ge,i as te,l as K,aN as re,bR as Je,gz as z}from"./strapi-YzJfjJ2z.js";import{u as Re,D as Xe,H as Ze,R as es,a as ss}from"./Layout-BIIxkAtf-C6D5VXd4.js";import{g as ts}from"./getEmptyImage-CjqolaH3.js";import"./useDragLayer-Bta-luUM.js";const rs=Ve.injectEndpoints({endpoints(s){return{getAdminRoles:s.query({query:()=>({url:"/admin/roles",method:"GET"}),transformResponse:t=>t.data})}}}),{useGetAdminRolesQuery:os}=rs,as=(s,t,{onCancel:r,onDropItem:n,onGrabItem:l,onMoveItem:d})=>{const[u,a]=D.useState(!1),v=m=>{u&&typeof t=="number"&&d&&(m==="UP"?d(t-1,t):m==="DOWN"&&d(t+1,t))},k=()=>{u?(n&&n(t),a(!1)):(l&&l(t),a(!0))},h=()=>{u&&(a(!1),r&&r(t))};return m=>{if(s&&!(m.key==="Tab"&&!u))switch(m.preventDefault(),m.key){case" ":case"Enter":k();break;case"Escape":h();break;case"ArrowDown":case"ArrowRight":v("DOWN");break;case"ArrowUp":case"ArrowLeft":v("UP");break}}},ue={UPWARD:"upward",DOWNWARD:"downward"},oe={REGULAR:"regular"},ns=(s,{type:t="STRAPI_DND",index:r,item:n,onStart:l,onEnd:d,onGrabItem:u,onDropItem:a,onCancel:v,onMoveItem:k,dropSensitivity:h=oe.REGULAR})=>{const g=D.useRef(null),[{handlerId:m,isOver:M},c]=We({accept:t,collect(i){return{handlerId:i.getHandlerId(),isOver:i.isOver({shallow:!0})}},drop(i){const w=i.index;M&&a&&a(w,r)},hover(i,w){if(!g.current||!k)return;const j=i.index,x=r,I=g.current?.getBoundingClientRect(),A=(I.bottom-I.top)/2,N=w.getClientOffset();if(!N)return;const q=N&&N.y-I.top;if(typeof j=="number"&&typeof x=="number"){if(j===x||h===oe.REGULAR&&(j<x&&q<A||j>x&&q>A))return;k(x,j),i.index=x}else{if(Array.isArray(j)&&Array.isArray(x)){const V=Math.min(j.length,x.length);let B=!0,G=!1,f=!1;for(let o=0;o<V;o++)if(j[o]<x[o]){G=!0,B=!1;break}else if(j[o]>x[o]){f=!0,B=!1;break}if(B&&j.length===x.length||h===oe.REGULAR&&(G&&!f&&q<A||f&&!G&&q>A))return}k(x,j),i.index=x}}}),b=i=>{if(i&&i.isDragging()&&!i.didDrop()&&i.getInitialClientOffset()&&i.getClientOffset()){const w=i.getInitialClientOffset().y-i.getClientOffset().y;return w>0?ue.UPWARD:w<0?ue.DOWNWARD:null}return null},[{isDragging:T,direction:y},p,F]=$e({type:t,item(){l&&l();const{width:i}=g.current?.getBoundingClientRect()??{};return{index:r,width:i,...n}},end(){d&&d()},canDrag:s,isDragging:n?.id?i=>n.id===i.getItem().id:void 0,collect:i=>({isDragging:i.isDragging(),initialOffset:i.getInitialClientOffset(),currentOffset:i.getClientOffset(),direction:b(i)})}),L=as(s,r,{onGrabItem:u,onDropItem:a,onCancel:v,onMoveItem:k});return[{handlerId:m,isDragging:T,handleKeyDown:L,isOverDropTarget:M,direction:y},g,c,p,F]},is=({children:s,...t})=>e.jsx(ls,{tag:"button",background:"neutral0",borderColor:"neutral150",paddingBottom:3,paddingLeft:4,paddingRight:4,paddingTop:3,shadow:"filterShadow",...t,children:e.jsx(H,{variant:"pi",fontWeight:"bold",children:e.jsxs(P,{tag:"span",gap:2,children:[e.jsx(Qe,{width:"2.4rem",height:"2.4rem","aria-hidden":!0}),s]})})}),ls=_(J)`
  border-radius: 26px;
  color: ${({theme:s})=>s.colors.neutral500};

  &:hover {
    color: ${({theme:s})=>s.colors.primary600};
  }

  &:active {
    color: ${({theme:s})=>s.colors.primary600};
  }
`,ds=({canDelete:s=!0,canUpdate:t=!0,isCreating:r})=>{const{formatMessage:n}=$(),{trackUsage:l}=ye(),d=W("Stages",a=>a.addFieldRow),{value:u=[]}=Q("stages");return e.jsxs(P,{direction:"column",gap:6,width:"100%",children:[e.jsxs(J,{position:"relative",width:"100%",children:[e.jsx(cs,{background:"neutral200",height:"100%",left:"50%",position:"absolute",top:"0",width:2}),e.jsx(P,{direction:"column",alignItems:"stretch",gap:6,position:"relative",tag:"ol",children:u.map((a,v)=>e.jsx(J,{tag:"li",children:e.jsx(gs,{index:v,canDelete:u.length>1&&s,canReorder:u.length>1,canUpdate:t,stagesCount:u.length,defaultOpen:!a.id,...a})},a.__temp_key__))})]}),t&&e.jsx(is,{type:"button",onClick:()=>{d("stages",{name:""}),l("willCreateStage")},children:n({id:"Settings.review-workflows.stage.add",defaultMessage:"Add new stage"})})]})},cs=_(J)`
  transform: translateX(-50%);
`,gs=({index:s,canDelete:t=!1,canReorder:r=!1,canUpdate:n=!1,stagesCount:l,name:d,permissions:u,color:a,defaultOpen:v})=>{const[k,h]=D.useState(),{formatMessage:g}=$(),{trackUsage:m}=ye(),c=W("Stages",o=>o.errors.stages)?.[s],b=W("Stage",o=>o.addFieldRow),T=W("Stage",o=>o.moveFieldRow),y=W("Stage",o=>o.removeFieldRow),p=o=>`${o+1} of ${l}`,F=o=>{h(g({id:"dnd.grab-item",defaultMessage:"{item}, grabbed. Current position in list: {position}. Press up and down arrow to change position, Spacebar to drop, Escape to cancel."},{item:d,position:p(o)}))},L=o=>{h(g({id:"dnd.drop-item",defaultMessage:"{item}, dropped. Final position in list: {position}."},{item:d,position:p(o)}))},i=()=>{h(g({id:"dnd.cancel-item",defaultMessage:"{item}, dropped. Re-order cancelled."},{item:d}))},w=(o,R)=>{h(g({id:"dnd.reorder",defaultMessage:"{item}, moved. New position in list: {position}."},{item:d,position:p(o)})),T("stages",R,o)},[{handlerId:j,isDragging:x,handleKeyDown:I},A,N,q,V]=ns(r,{index:s,item:{index:s,name:d},onGrabItem:F,onDropItem:L,onMoveItem:w,onCancel:i,type:ss.STAGE}),B=Be(A,N);D.useEffect(()=>{V(ts(),{captureDraggingState:!1})},[V,s]);const G=()=>{b("stages",{name:d,color:a,permissions:u})},f=D.useId();return e.jsxs(J,{ref:B,shadow:"tableShadow",children:[k&&e.jsx(de,{"aria-live":"assertive",children:k}),x?e.jsx(J,{background:"primary100",borderStyle:"dashed",borderColor:"primary600",borderWidth:"1px",display:"block",hasRadius:!0,padding:6}):e.jsx(us,{onValueChange:o=>{o&&m("willEditStage")},defaultValue:v?f:void 0,$error:Object.values(c??{}).length>0,children:e.jsxs(U.Item,{value:f,children:[e.jsxs(U.Header,{children:[e.jsx(U.Trigger,{children:d}),e.jsx(U.Actions,{children:t||n?e.jsxs(e.Fragment,{children:[e.jsxs(Z.Root,{children:[e.jsxs(ps,{size:"S",endIcon:null,paddingLeft:2,paddingRight:2,children:[e.jsx(Ge,{"aria-hidden":!0,focusable:!1}),e.jsx(de,{tag:"span",children:g({id:"[tbdb].components.DynamicZone.more-actions",defaultMessage:"More actions"})})]}),e.jsx(Z.Content,{popoverPlacement:"bottom-end",zIndex:2,children:e.jsxs(Z.SubRoot,{children:[n&&e.jsx(xe,{onClick:G,children:g({id:"Settings.review-workflows.stage.delete",defaultMessage:"Duplicate stage"})}),t&&e.jsx(fs,{onClick:()=>y("stages",s),children:g({id:"Settings.review-workflows.stage.delete",defaultMessage:"Delete"})})]})})]}),n&&e.jsx(ke,{background:"transparent",hasRadius:!0,variant:"ghost","data-handler-id":j,ref:q,label:g({id:"Settings.review-workflows.stage.drag",defaultMessage:"Drag"}),onClick:o=>o.stopPropagation(),onKeyDown:I,children:e.jsx(He,{})})]}):null})]}),e.jsx(U.Content,{children:e.jsx(Y.Root,{gap:4,padding:6,children:[{disabled:!n,label:g({id:"Settings.review-workflows.stage.name.label",defaultMessage:"Stage name"}),name:`stages.${s}.name`,required:!0,size:6,type:"string"},{disabled:!n,label:g({id:"content-manager.reviewWorkflows.stage.color",defaultMessage:"Color"}),name:`stages.${s}.color`,required:!0,size:6,type:"color"},{disabled:!n,label:g({id:"Settings.review-workflows.stage.permissions.label",defaultMessage:"Roles that can change this stage"}),name:`stages.${s}.permissions`,placeholder:g({id:"Settings.review-workflows.stage.permissions.placeholder",defaultMessage:"Select a role"}),required:!0,size:6,type:"permissions"}].map(({size:o,...R})=>e.jsx(Y.Item,{col:o,direction:"column",alignItems:"stretch",children:e.jsx(hs,{...R})},R.name))})})]})})]})},us=_(U.Root)`
  border: 1px solid
    ${({theme:s,$error:t})=>t?s.colors.danger600:s.colors.neutral200};
`,fs=_(xe)`
  color: ${({theme:s})=>s.colors.danger600};
`,ps=_(Z.Trigger)`
  :hover,
  :focus {
    background-color: ${({theme:s})=>s.colors.neutral100};
  }

  > span {
    font-size: 0;
  }
`,hs=s=>{switch(s.type){case"color":return e.jsx(ws,{...s});case"permissions":return e.jsx(ms,{...s});default:return e.jsx(he,{...s})}},ws=({disabled:s,label:t,name:r,required:n})=>{const{formatMessage:l}=$(),{value:d,error:u,onChange:a}=Q(r),v=Ye.map(({hex:h,name:g})=>({value:h,label:l({id:"Settings.review-workflows.stage.color.name",defaultMessage:"{name}"},{name:g}),color:h})),{themeColorName:k}=ce(d)??{};return e.jsxs(C.Root,{error:u,name:r,required:n,children:[e.jsx(C.Label,{children:t}),e.jsx(ve,{disabled:s,onChange:h=>{a(r,h.toString())},value:d?.toUpperCase(),startIcon:e.jsx(P,{tag:"span",height:2,background:d,borderColor:k==="neutral0"?"neutral150":"transparent",hasRadius:!0,shrink:0,width:2}),children:v.map(({value:h,label:g,color:m})=>{const{themeColorName:M}=ce(m)||{};return e.jsx(ne,{value:h,startIcon:e.jsx(P,{tag:"span",height:2,background:m,borderColor:M==="neutral0"?"neutral150":"transparent",hasRadius:!0,shrink:0,width:2}),children:g},h)})}),e.jsx(C.Error,{})]})},ms=({disabled:s,name:t,placeholder:r,required:n})=>{const{formatMessage:l}=$(),{toggleNotification:d}=pe(),[u,a]=D.useState(!1),{value:v=[],error:k,onChange:h}=Q(t),g=W("PermissionsField",p=>p.values.stages),m=W("PermissionsField",p=>p.onChange),M=D.useRef(0),{data:c=[],isLoading:b,error:T}=os(),y=c?.filter(p=>p.code!=="strapi-super-admin")??[];return D.useEffect(()=>{!b&&T&&"status"in T&&T.status==403&&M.current===0&&(M.current=1,d({blockTransition:!0,type:"danger",message:l({id:"review-workflows.stage.permissions.noPermissions.description",defaultMessage:"You don’t have the permission to see roles. Contact your administrator."})}))},[l,b,c,d,T]),!b&&y.length===0?e.jsxs(C.Root,{name:t,hint:l({id:"Settings.review-workflows.stage.permissions.noPermissions.description",defaultMessage:"You don’t have the permission to see roles"}),required:n,children:[e.jsx(C.Label,{children:l({id:"Settings.review-workflows.stage.permissions.label",defaultMessage:"Roles that can change this stage"})}),e.jsx(Ke,{disabled:!0,placeholder:l({id:"components.NotAllowedInput.text",defaultMessage:"No permissions to see this field"}),startAction:e.jsx(ze,{fill:"neutral600"}),type:"text",value:""}),e.jsx(C.Hint,{})]}):e.jsx(e.Fragment,{children:e.jsxs(P,{alignItems:"flex-end",gap:3,children:[e.jsx(vs,{grow:1,children:e.jsxs(C.Root,{error:k,name:t,required:!0,children:[e.jsx(C.Label,{children:l({id:"Settings.review-workflows.stage.permissions.label",defaultMessage:"Roles that can change this stage"})}),e.jsx(we,{disabled:s,onChange:p=>{const F=p.map(L=>({role:parseInt(L,10),action:"admin::review-workflows.stage.transition"}));h(t,F)},placeholder:r,value:v.map(p=>`${p.role}`),withTags:!0,children:e.jsx(me,{label:l({id:"Settings.review-workflows.stage.permissions.allRoles.label",defaultMessage:"All roles"}),values:y.map(p=>`${p.id}`),children:y.map(p=>e.jsx(bs,{value:`${p.id}`,children:p.name},p.id))})}),e.jsx(C.Error,{})]})}),e.jsxs(ie.Root,{open:u,onOpenChange:a,children:[e.jsx(ie.Trigger,{children:e.jsx(ke,{disabled:s,label:l({id:"Settings.review-workflows.stage.permissions.apply.label",defaultMessage:"Apply to all stages"}),size:"L",children:e.jsx(Ue,{})})}),e.jsx(je,{onConfirm:()=>{m("stages",g.map(p=>({...p,permissions:v}))),a(!1),d({type:"success",message:l({id:"Settings.review-workflows.page.edit.confirm.stages.permissions.copy.success",defaultMessage:"Applied roles to all other stages of the workflow"})})},variant:"default",children:l({id:"Settings.review-workflows.page.edit.confirm.stages.permissions.copy",defaultMessage:"Roles that can change that stage will be applied to all the other stages."})})]})]})})},bs=_(be)`
  padding-left: ${({theme:s})=>s.spaces[7]};
`,vs=_(P)`
  > * {
    flex-grow: 1;
  }
`,ys=({canUpdate:s=!0})=>{const{formatMessage:t}=$();return e.jsxs(Y.Root,{background:"neutral0",hasRadius:!0,gap:4,padding:6,shadow:"tableShadow",children:[e.jsx(Y.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsx(he,{disabled:!s,label:t({id:"Settings.review-workflows.workflow.name.label",defaultMessage:"Workflow Name"}),name:"name",required:!0,type:"string"})}),e.jsx(Y.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsx(xs,{disabled:!s})}),e.jsx(Y.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsx(Rs,{disabled:!s})})]})},xs=({disabled:s})=>{const{formatMessage:t,locale:r}=$(),{data:n,isLoading:l}=Ne(),{workflows:d}=Re(),u=W("ContentTypesSelector",c=>c.values),{error:a,value:v,onChange:k}=Q("contentTypes"),h=qe(r,{sensitivity:"base"}),g=s||l||!n||n.collectionType.length===0&&n.singleType.length===0,m=(n?.collectionType??[]).toSorted((c,b)=>h.compare(c.info.displayName,b.info.displayName)).map(c=>({label:c.info.displayName,value:c.uid})),M=(n?.singleType??[]).map(c=>({label:c.info.displayName,value:c.uid}));return e.jsxs(C.Root,{error:a,name:"contentTypes",children:[e.jsx(C.Label,{children:t({id:"Settings.review-workflows.workflow.contentTypes.label",defaultMessage:"Associated to"})}),e.jsx(we,{customizeContent:c=>t({id:"Settings.review-workflows.workflow.contentTypes.displayValue",defaultMessage:"{count} {count, plural, one {content type} other {content types}} selected"},{count:c?.length}),disabled:g,onChange:c=>{k("contentTypes",c)},value:v,placeholder:t({id:"Settings.review-workflows.workflow.contentTypes.placeholder",defaultMessage:"Select"}),children:[...m.length>0?[{label:t({id:"Settings.review-workflows.workflow.contentTypes.collectionTypes.label",defaultMessage:"Collection Types"}),children:m}]:[],...M.length>0?[{label:t({id:"Settings.review-workflows.workflow.contentTypes.singleTypes.label",defaultMessage:"Single Types"}),children:M}]:[]].map(c=>e.jsx(me,{label:c.label,values:c.children.map(b=>b.value.toString()),children:c.children.map(b=>{const{name:T}=d?.find(y=>(u&&y.id!==u.id||!u)&&y.contentTypes.includes(b.value))??{};return e.jsx(ks,{value:b.value,children:e.jsx(H,{children:t({id:"Settings.review-workflows.workflow.contentTypes.assigned.notice",defaultMessage:"{label} {name, select, undefined {} other {<i>(assigned to <em>{name}</em> workflow)</i>}}"},{label:b.label,name:T,em:(...y)=>e.jsx(H,{tag:"em",fontWeight:"bold",children:y}),i:(...y)=>e.jsx(js,{children:y})})})},b.value)})},c.label))})]})},ks=_(be)`
  padding-left: ${({theme:s})=>s.spaces[7]};
`,js=_(H)`
  font-style: italic;
`,Rs=({disabled:s})=>{const{value:t=[]}=Q("stages"),{formatMessage:r}=$(),{error:n,value:l,onChange:d}=Q("stageRequiredToPublish"),u=t.filter(a=>a.name);return e.jsxs(C.Root,{error:n,name:"stageRequiredToPublish",hint:r({id:"settings.review-workflows.workflow.stageRequiredToPublish.hint",defaultMessage:"Prevents entries from being published if they are not at the required stage."}),children:[e.jsx(C.Label,{children:r({id:"settings.review-workflows.workflow.stageRequiredToPublish.label",defaultMessage:"Required stage for publishing"})}),e.jsxs(ve,{disabled:s,onChange:a=>{d("stageRequiredToPublish",a)},value:l,children:[e.jsx(ne,{value:"",children:r({id:"settings.review-workflows.workflow.stageRequiredToPublish.any",defaultMessage:"Any stage"})}),u.map((a,v)=>e.jsx(ne,{value:a.id?.toString()||a.__temp_key__,children:a.name},`requiredToPublishStage-${a.id||a.__temp_key__}`))]}),e.jsx(C.Hint,{})]})},Ss=te({contentTypes:re().of(K()),name:K().max(255,{id:"review-workflows.validation.name.max-length",defaultMessage:"Name can not be longer than 255 characters"}).required().nullable(),stages:re().of(te().shape({name:K().nullable().required({id:"review-workflows.validation.stage.name",defaultMessage:"Name is required"}).max(255,{id:"review-workflows.validation.stage.max-length",defaultMessage:"Name can not be longer than 255 characters"}).test("unique-name",{id:"review-workflows.validation.stage.duplicate",defaultMessage:"Stage name must be unique"},(s,t)=>{const{stages:r}=t.from[1].value;return r.filter(n=>n.name===s).length===1}),color:K().nullable().required({id:"review-workflows.validation.stage.color",defaultMessage:"Color is required"}).matches(/^#(?:[0-9a-fA-F]{3}){1,2}$/i),permissions:re(te({role:Je().strict().typeError({id:"review-workflows.validation.stage.permissions.role.number",defaultMessage:"Role must be of type number"}).required(),action:K().required({id:"review-workflows.validation.stage.permissions.action.required",defaultMessage:"Action is a required argument"})})).strict()})).min(1),stageRequiredToPublish:K().nullable()}),Ts=()=>{const{id:s=""}=Te(),t=s==="create",{formatMessage:r}=$(),{_unstableFormatValidationErrors:n}=Ce(),l=Me(),{toggleNotification:d}=pe(),{isLoading:u,meta:a,workflows:v,error:k,update:h,create:g}=Re(),m=fe(f=>f.admin_app.permissions.settings?.["review-workflows"]),{allowedActions:{canDelete:M,canUpdate:c,canCreate:b}}=Ae(m),[T,y]=D.useState({}),{getFeature:p,isLoading:F}=Ee(),[L,i]=D.useState(null),w=v?.find(f=>f.id===parseInt(s,10)),j=v?.filter(f=>f.id!==parseInt(s,10)).flatMap(f=>f.contentTypes),x=p("review-workflows"),I=x?.[De],A=x?.[Ie],N=async(f,o)=>{try{const{stageRequiredToPublish:R,...O}=f,E=R===""?null:O.stages.find(S=>S.id===Number(R)||S.__temp_key__===R)?.name;if(t){const S=await g({...O,stageRequiredToPublishName:E});"error"in S&&ge(S.error)&&S.error.name==="ValidationError"?o.setErrors(n(S.error)):"data"in S&&l(`../${S.data.id}`,{replace:!0})}else{const S=await h(s,{...O,stages:O.stages.map(X=>{let le=!0;const ee=w?.stages?.find(se=>se.id===X?.id);return ee&&(le=ee.permissions?.length!==X.permissions?.length||!ee.permissions?.every(se=>!!X.permissions?.find(Se=>Se.role===se.role))),{...X,permissions:le?X.permissions:void 0}}),stageRequiredToPublishName:E});"error"in S&&ge(S.error)&&S.error.name==="ValidationError"&&o.setErrors(n(S.error))}}catch{d({type:"danger",message:r({id:"notification.error",defaultMessage:"An error occurred"})})}y({})},q=(f,o)=>async()=>{await N(f,o)},V=()=>{y({})},B=async(f,o)=>{const R=f.contentTypes.some(E=>j?.includes(E)),O=!t&&!w?.stages.every(E=>f.stages.some(S=>S.id===E.id));a&&I&&a?.workflowCount>parseInt(I,10)?i("workflow"):f.stages&&A&&f.stages.length>parseInt(A,10)?i("stage"):O||R?(O&&y(E=>({...E,hasDeletedServerStages:!0})),R&&y(E=>({...E,hasReassignedContentTypes:!0}))):await N(f,o)};D.useEffect(()=>{!u&&!F&&(a&&I&&a?.workflowCount>parseInt(I,10)?i("workflow"):w&&w.stages&&A&&w.stages.length>parseInt(A,10)&&i("stage"))},[w,F,u,x,a,I,A]);const G=D.useMemo(()=>t||!w?{name:"",stages:[],contentTypes:[],stageRequiredToPublish:""}:{name:w.name,stages:Cs(w.stages),contentTypes:w.contentTypes,stageRequiredToPublish:w.stageRequiredToPublish?.id.toString()??""},[w,t]);return u?e.jsx(ae.Loading,{}):k?e.jsx(ae.Error,{}):e.jsxs(e.Fragment,{children:[e.jsx(Xe,{}),e.jsx(Pe,{method:t?"POST":"PUT",initialValues:G,validationSchema:Ss,onSubmit:B,children:({modified:f,isSubmitting:o,values:R,setErrors:O})=>e.jsxs(e.Fragment,{children:[e.jsx(Ze,{navigationAction:e.jsx(Le,{}),primaryAction:c||b?e.jsx(_e,{startIcon:e.jsx(Fe,{}),type:"submit",disabled:!f||o||R.stages.length===0,loading:!(Object.keys(T).length>0)&&o,children:r({id:"global.save",defaultMessage:"Save"})}):null,subtitle:r({id:"review-workflows.page.subtitle",defaultMessage:"{count, plural, one {# stage} other {# stages}}"},{count:w?.stages?.length??0}),title:w?.name||r({id:"Settings.review-workflows.create.page.title",defaultMessage:"Create Review Workflow"})}),e.jsx(es,{children:e.jsxs(P,{alignItems:"stretch",direction:"column",gap:7,children:[e.jsx(ys,{canUpdate:c||b}),e.jsx(ds,{canDelete:M,canUpdate:c||b,isCreating:t})]})}),e.jsx(ie.Root,{open:Object.keys(T).length>0,onOpenChange:V,children:e.jsx(je,{onConfirm:q(R,{setErrors:O}),children:e.jsxs(P,{direction:"column",gap:5,children:[T.hasDeletedServerStages&&e.jsx(H,{textAlign:"center",variant:"omega",children:r({id:"review-workflows.page.delete.confirm.stages.body",defaultMessage:"All entries assigned to deleted stages will be moved to the previous stage."})}),T.hasReassignedContentTypes&&e.jsx(H,{textAlign:"center",variant:"omega",children:r({id:"review-workflows.page.delete.confirm.contentType.body",defaultMessage:"{count} {count, plural, one {content-type} other {content-types}} {count, plural, one {is} other {are}} already mapped to {count, plural, one {another workflow} other {other workflows}}. If you save changes, {count, plural, one {this} other {these}} {count, plural, one {content-type} other {{count} content-types}} will no more be mapped to the {count, plural, one {another workflow} other {other workflows}} and all corresponding information will be removed."},{count:j?.filter(E=>R.contentTypes.includes(E)).length??0})}),e.jsx(H,{textAlign:"center",variant:"omega",children:r({id:"review-workflows.page.delete.confirm.confirm",defaultMessage:"Are you sure you want to save?"})})]})})})]})}),e.jsxs(z.Root,{open:L==="workflow",onOpenChange:()=>i(null),children:[e.jsx(z.Title,{children:r({id:"review-workflows.edit.page.workflows.limit.title",defaultMessage:"You’ve reached the limit of workflows in your plan"})}),e.jsx(z.Body,{children:r({id:"review-workflows.edit.page.workflows.limit.body",defaultMessage:"Delete a workflow or contact Sales to enable more workflows."})})]}),e.jsxs(z.Root,{open:L==="stage",onOpenChange:()=>i(null),children:[e.jsx(z.Title,{children:r({id:"review-workflows.edit.page.stages.limit.title",defaultMessage:"You have reached the limit of stages for this workflow in your plan"})}),e.jsx(z.Body,{children:r({id:"review-workflows.edit.page.stages.limit.body",defaultMessage:"Try deleting some stages or contact Sales to enable more stages."})})]})]})},Cs=s=>{const t=Oe(void 0,void 0,s.length);return s.map((r,n)=>({...r,__temp_key__:t[n]}))},Is=()=>{const s=fe(t=>{const{create:r=[],update:n=[],read:l=[]}=t.admin_app.permissions.settings?.["review-workflows"]??{};return[...r,...n,...l]});return e.jsx(ae.Protect,{permissions:s,children:e.jsx(Ts,{})})};export{Is as ProtectedEditPage};
