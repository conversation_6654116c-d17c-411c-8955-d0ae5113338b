{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/preview/components/PreviewContent.tsx", "../../../@strapi/content-manager/admin/src/preview/components/PreviewHeader.tsx", "../../../@strapi/content-manager/admin/src/preview/pages/Preview.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Box } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { usePreviewContext } from '../pages/Preview';\n\nconst PreviewContent = () => {\n  const previewUrl = usePreviewContext('PreviewContent', (state) => state.url);\n\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box\n      src={previewUrl}\n      title={formatMessage({\n        id: 'content-manager.preview.panel.title',\n        defaultMessage: 'Preview',\n      })}\n      width=\"100%\"\n      height=\"100%\"\n      borderWidth={0}\n      tag=\"iframe\"\n    />\n  );\n};\n\nexport { PreviewContent };\n", "import * as React from 'react';\n\nimport {\n  useClipboard,\n  useHistory,\n  useNotification,\n  useQueryParams,\n} from '@strapi/admin/strapi-admin';\nimport { Box, type BoxProps, Flex, IconButton, Typography } from '@strapi/design-system';\nimport { Cross, Link as LinkIcon } from '@strapi/icons';\nimport { stringify } from 'qs';\nimport { type MessageDescriptor, useIntl } from 'react-intl';\nimport { Link, type To, useNavigate } from 'react-router-dom';\n\nimport { getDocumentStatus } from '../../pages/EditView/EditViewPage';\nimport { usePreviewContext } from '../pages/Preview';\n\n/* -------------------------------------------------------------------------------------------------\n * ClosePreviewButton\n * -----------------------------------------------------------------------------------------------*/\n\nconst ClosePreviewButton = () => {\n  const [{ query }] = useQueryParams();\n  const navigate = useNavigate();\n  const { formatMessage } = useIntl();\n\n  const canGoBack = useHistory('BackButton', (state) => state.canGoBack);\n  const goBack = useHistory('BackButton', (state) => state.goBack);\n  const history = useHistory('BackButton', (state) => state.history);\n\n  const fallbackUrl: To = {\n    pathname: '..',\n    search: stringify(query, { encode: false }),\n  };\n\n  const handleClick = (e: React.MouseEvent) => {\n    /**\n     * Prevent normal link behavior. We only make it an achor for accessibility reasons.\n     * The point of this logic is to act as the browser's back button when possible, and to fallback\n     * to a link behavior to the edit view when no history is available.\n     *  */\n    e.preventDefault();\n\n    if (canGoBack) {\n      goBack();\n    } else {\n      navigate(fallbackUrl);\n    }\n  };\n\n  return (\n    <IconButton\n      tag={Link}\n      to={history.at(-1) ?? fallbackUrl}\n      onClick={handleClick}\n      label={formatMessage({\n        id: 'content-manager.preview.header.close',\n        defaultMessage: 'Close preview',\n      })}\n    >\n      <Cross />\n    </IconButton>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * DocumentStatus\n * -----------------------------------------------------------------------------------------------*/\n\ninterface StatusData {\n  background: BoxProps['background'];\n  border: BoxProps['borderColor'];\n  text: BoxProps['color'];\n  message: MessageDescriptor;\n}\n\nconst getStatusData = (status: ReturnType<typeof getDocumentStatus>): StatusData => {\n  switch (status) {\n    case 'draft':\n      return {\n        background: 'secondary100',\n        border: 'secondary200',\n        text: 'secondary700',\n        message: {\n          id: 'content-manager.containers.List.draft',\n          defaultMessage: 'Draft',\n        },\n      };\n    case 'modified':\n      return {\n        background: 'alternative100',\n        border: 'alternative200',\n        text: 'alternative700',\n        message: {\n          id: 'content-manager.containers.List.modified',\n          defaultMessage: 'Modified',\n        },\n      };\n    case 'published':\n    default:\n      return {\n        background: 'success100',\n        border: 'success200',\n        text: 'success700',\n        message: {\n          id: 'content-manager.containers.List.published',\n          defaultMessage: 'Published',\n        },\n      };\n  }\n};\n\nconst DocumentStatus = () => {\n  const { formatMessage } = useIntl();\n\n  // Get status\n  const document = usePreviewContext('PreviewHeader', (state) => state.document);\n  const schema = usePreviewContext('PreviewHeader', (state) => state.schema);\n  const meta = usePreviewContext('PreviewHeader', (state) => state.meta);\n  const hasDraftAndPublished = schema?.options?.draftAndPublish ?? false;\n  const status = getDocumentStatus(document, meta);\n\n  const statusData = getStatusData(status);\n\n  if (!hasDraftAndPublished) {\n    return null;\n  }\n\n  /**\n   * TODO: Add an XS size to the Status component from the design system so that we can add\n   * a variant to the VersionsList component.\n   * Then we could reuse it both here and in history's VersionCard component.\n   */\n\n  return (\n    <Box\n      background={statusData.background}\n      borderStyle=\"solid\"\n      borderWidth=\"1px\"\n      borderColor={statusData.border}\n      hasRadius\n      paddingLeft=\"6px\"\n      paddingRight=\"6px\"\n      paddingTop=\"2px\"\n      paddingBottom=\"2px\"\n    >\n      <Typography variant=\"pi\" fontWeight=\"bold\" textColor={statusData.text}>\n        {formatMessage(statusData.message)}\n      </Typography>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PreviewHeader\n * -----------------------------------------------------------------------------------------------*/\n\nconst PreviewHeader = () => {\n  // Get main field\n  const mainField = usePreviewContext('PreviewHeader', (state) => state.mainField);\n  const document = usePreviewContext('PreviewHeader', (state) => state.document);\n  const title = document[mainField];\n\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { copy } = useClipboard();\n\n  const handleCopyLink = () => {\n    copy(window.location.href);\n    toggleNotification({\n      message: formatMessage({\n        id: 'content-manager.preview.copy.success',\n        defaultMessage: 'Copied preview link',\n      }),\n      type: 'success',\n    });\n  };\n\n  return (\n    <Flex\n      justifyContent=\"space-between\"\n      background=\"neutral0\"\n      padding={2}\n      borderColor=\"neutral150\"\n      tag=\"header\"\n    >\n      <Flex gap={3}>\n        <ClosePreviewButton />\n        <Typography tag=\"h1\" fontWeight={600} fontSize={2}>\n          {title}\n        </Typography>\n        <DocumentStatus />\n      </Flex>\n      <IconButton\n        type=\"button\"\n        label={formatMessage({\n          id: 'preview.copy.label',\n          defaultMessage: 'Copy preview link',\n        })}\n        onClick={handleCopyLink}\n      >\n        <LinkIcon />\n      </IconButton>\n    </Flex>\n  );\n};\n\nexport { PreviewHeader };\n", "import * as React from 'react';\n\nimport { Page, useQueryParams, useRBAC, createContext } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, FocusTrap, Portal } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { useParams } from 'react-router-dom';\n\nimport { GetPreviewUrl } from '../../../../shared/contracts/preview';\nimport { COLLECTION_TYPES } from '../../constants/collections';\nimport { DocumentRBAC } from '../../features/DocumentRBAC';\nimport { type UseDocument, useDocument } from '../../hooks/useDocument';\nimport { useDocumentLayout } from '../../hooks/useDocumentLayout';\nimport { buildValidParams } from '../../utils/api';\nimport { PreviewContent } from '../components/PreviewContent';\nimport { PreviewHeader } from '../components/PreviewHeader';\nimport { useGetPreviewUrlQuery } from '../services/preview';\n\nimport type { UID } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * PreviewProvider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PreviewContextValue {\n  url: string;\n  mainField: string;\n  document: NonNullable<ReturnType<UseDocument>['document']>;\n  meta: NonNullable<ReturnType<UseDocument>['meta']>;\n  schema: NonNullable<ReturnType<UseDocument>['schema']>;\n}\n\nconst [PreviewProvider, usePreviewContext] = createContext<PreviewContextValue>('PreviewPage');\n\n/* -------------------------------------------------------------------------------------------------\n * PreviewPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst PreviewPage = () => {\n  const { formatMessage } = useIntl();\n\n  // Read all the necessary data from the URL to find the right preview URL\n  const {\n    slug: model,\n    id: documentId,\n    collectionType,\n  } = useParams<{\n    slug: UID.ContentType;\n    id: string;\n    collectionType: string;\n  }>();\n  const [{ query }] = useQueryParams<{\n    plugins?: Record<string, unknown>;\n  }>();\n  const params = React.useMemo(() => buildValidParams(query), [query]);\n\n  if (!collectionType) {\n    throw new Error('Could not find collectionType in url params');\n  }\n\n  if (!model) {\n    throw new Error('Could not find model in url params');\n  }\n\n  // Only collection types must have a documentId\n  if (collectionType === COLLECTION_TYPES && !documentId) {\n    throw new Error('Could not find documentId in url params');\n  }\n\n  const previewUrlResponse = useGetPreviewUrlQuery({\n    params: {\n      contentType: model,\n    },\n    query: {\n      documentId,\n      locale: params.locale,\n      status: params.status as GetPreviewUrl.Request['query']['status'],\n    },\n  });\n\n  const documentResponse = useDocument({\n    model,\n    collectionType,\n    documentId,\n    params,\n  });\n\n  const documentLayoutResponse = useDocumentLayout(model);\n\n  if (\n    documentResponse.isLoading ||\n    previewUrlResponse.isLoading ||\n    documentLayoutResponse.isLoading\n  ) {\n    return <Page.Loading />;\n  }\n\n  if (\n    previewUrlResponse.error ||\n    documentLayoutResponse.error ||\n    !documentResponse.document ||\n    !documentResponse.meta ||\n    !documentResponse.schema\n  ) {\n    return <Page.Error />;\n  }\n\n  if (!previewUrlResponse.data?.data?.url) {\n    return <Page.NoData />;\n  }\n\n  return (\n    <>\n      <Page.Title>\n        {formatMessage(\n          {\n            id: 'content-manager.preview.page-title',\n            defaultMessage: '{contentType} preview',\n          },\n          {\n            contentType: documentLayoutResponse.edit.settings.displayName,\n          }\n        )}\n      </Page.Title>\n      <PreviewProvider\n        url={previewUrlResponse.data.data.url}\n        mainField={documentLayoutResponse.edit.settings.mainField}\n        document={documentResponse.document}\n        meta={documentResponse.meta}\n        schema={documentResponse.schema}\n      >\n        <Flex direction=\"column\" height=\"100%\" alignItems={'stretch'}>\n          <PreviewHeader />\n          <PreviewContent />\n        </Flex>\n      </PreviewProvider>\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedPreviewPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedPreviewPageImpl = () => {\n  const { slug: model } = useParams<{\n    slug: string;\n  }>();\n  const {\n    permissions = [],\n    isLoading,\n    error,\n  } = useRBAC([{ action: 'plugin::content-manager.explorer.read', subject: model }]);\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (error || !model) {\n    return (\n      <Box\n        height=\"100vh\"\n        width=\"100vw\"\n        position=\"fixed\"\n        top={0}\n        left={0}\n        zIndex={2}\n        background=\"neutral0\"\n      >\n        <Page.Error />\n      </Box>\n    );\n  }\n\n  return (\n    <Box\n      height=\"100vh\"\n      width=\"100vw\"\n      position=\"fixed\"\n      top={0}\n      left={0}\n      zIndex={2}\n      background=\"neutral0\"\n    >\n      <Page.Protect permissions={permissions}>\n        {({ permissions }) => (\n          <DocumentRBAC permissions={permissions}>\n            <PreviewPage />\n          </DocumentRBAC>\n        )}\n      </Page.Protect>\n    </Box>\n  );\n};\n\nconst ProtectedPreviewPage = () => {\n  return (\n    <Portal>\n      <FocusTrap>\n        <ProtectedPreviewPageImpl />\n      </FocusTrap>\n    </Portal>\n  );\n};\n\nexport { ProtectedPreviewPage, usePreviewContext };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,iBAAiB,MAAM;AAC3B,QAAM,aAAa,kBAAkB,kBAAkB,CAAC,UAAU,MAAM,GAAG;AAErE,QAAA,EAAE,cAAA,IAAkB,QAAA;AAGxB,aAAA;IAAC;IAAA;MACC,KAAK;MACL,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,OAAM;MACN,QAAO;MACP,aAAa;MACb,KAAI;IAAA;EAAA;AAGV;ACJA,IAAM,qBAAqB,MAAM;AAC/B,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAAe;AACnC,QAAM,WAAW,YAAA;AACX,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,QAAM,YAAY,WAAW,cAAc,CAAC,UAAU,MAAM,SAAS;AACrE,QAAM,SAAS,WAAW,cAAc,CAAC,UAAU,MAAM,MAAM;AAC/D,QAAM,UAAU,WAAW,cAAc,CAAC,UAAU,MAAM,OAAO;AAEjE,QAAM,cAAkB;IACtB,UAAU;IACV,YAAQ,qBAAU,OAAO,EAAE,QAAQ,MAAA,CAAO;EAAA;AAGtC,QAAA,cAAc,CAAC,MAAwB;AAM3C,MAAE,eAAe;AAEjB,QAAI,WAAW;AACN,aAAA;IAAA,OACF;AACL,eAAS,WAAW;IACtB;EAAA;AAIA,aAAA;IAAC;IAAA;MACC,KAAKA;MACL,IAAI,QAAQ,GAAG,EAAE,KAAK;MACtB,SAAS;MACT,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MAED,cAAA,wBAAC,eAAM,CAAA,CAAA;IAAA;EAAA;AAGb;AAaA,IAAM,gBAAgB,CAAC,WAA6D;AAClF,UAAQ,QAAQ;IACd,KAAK;AACI,aAAA;QACL,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,SAAS;UACP,IAAI;UACJ,gBAAgB;QAClB;MAAA;IAEJ,KAAK;AACI,aAAA;QACL,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,SAAS;UACP,IAAI;UACJ,gBAAgB;QAClB;MAAA;IAEJ,KAAK;IACL;AACS,aAAA;QACL,YAAY;QACZ,QAAQ;QACR,MAAM;QACN,SAAS;UACP,IAAI;UACJ,gBAAgB;QAClB;MAAA;EAEN;AACF;AAEA,IAAM,iBAAiB,MAAM;;AACrB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAG1B,QAAM,WAAW,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,QAAQ;AAC7E,QAAM,SAAS,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,MAAM;AACzE,QAAM,OAAO,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,IAAI;AAC/D,QAAA,yBAAuB,sCAAQ,YAAR,mBAAiB,oBAAmB;AAC3D,QAAA,SAAS,kBAAkB,UAAU,IAAI;AAEzC,QAAA,aAAa,cAAc,MAAM;AAEvC,MAAI,CAAC,sBAAsB;AAClB,WAAA;EACT;AASE,aAAA;IAAC;IAAA;MACC,YAAY,WAAW;MACvB,aAAY;MACZ,aAAY;MACZ,aAAa,WAAW;MACxB,WAAS;MACT,aAAY;MACZ,cAAa;MACb,YAAW;MACX,eAAc;MAEd,cAAC,wBAAA,YAAA,EAAW,SAAQ,MAAK,YAAW,QAAO,WAAW,WAAW,MAC9D,UAAA,cAAc,WAAW,OAAO,EAAA,CACnC;IAAA;EAAA;AAGN;AAMA,IAAM,gBAAgB,MAAM;AAE1B,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,SAAS;AAC/E,QAAM,WAAW,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,QAAQ;AACvE,QAAA,QAAQ,SAAS,SAAS;AAE1B,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA,EAAE,KAAA,IAAS,aAAA;AAEjB,QAAM,iBAAiB,MAAM;AACtB,SAAA,OAAO,SAAS,IAAI;AACN,uBAAA;MACjB,SAAS,cAAc;QACrB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,MAAM;IAAA,CACP;EAAA;AAID,aAAA;IAAC;IAAA;MACC,gBAAe;MACf,YAAW;MACX,SAAS;MACT,aAAY;MACZ,KAAI;MAEJ,UAAA;YAAC,yBAAA,MAAA,EAAK,KAAK,GACT,UAAA;cAAA,wBAAC,oBAAmB,CAAA,CAAA;cACpB,wBAAC,YAAA,EAAW,KAAI,MAAK,YAAY,KAAK,UAAU,GAC7C,UACH,MAAA,CAAA;cAAA,wBACC,gBAAe,CAAA,CAAA;QAAA,EAAA,CAClB;YACA;UAAC;UAAA;YACC,MAAK;YACL,OAAO,cAAc;cACnB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,SAAS;YAET,cAAA,wBAACC,eAAS,CAAA,CAAA;UAAA;QACZ;MAAA;IAAA;EAAA;AAGN;AC9KA,IAAM,CAAC,iBAAiB,iBAAiB,IAAI,cAAmC,aAAa;AAM7F,IAAM,cAAc,MAAM;;AAClB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAGpB,QAAA;IACJ,MAAM;IACN,IAAI;IACJ;EAAA,IACE,UAID;AACH,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAEjB;AACG,QAAA,SAAe,cAAQ,MAAM,iBAAiB,KAAK,GAAG,CAAC,KAAK,CAAC;AAEnE,MAAI,CAAC,gBAAgB;AACb,UAAA,IAAI,MAAM,6CAA6C;EAC/D;AAEA,MAAI,CAAC,OAAO;AACJ,UAAA,IAAI,MAAM,oCAAoC;EACtD;AAGI,MAAA,mBAAmB,oBAAoB,CAAC,YAAY;AAChD,UAAA,IAAI,MAAM,yCAAyC;EAC3D;AAEA,QAAM,qBAAqB,sBAAsB;IAC/C,QAAQ;MACN,aAAa;IACf;IACA,OAAO;MACL;MACA,QAAQ,OAAO;MACf,QAAQ,OAAO;IACjB;EAAA,CACD;AAED,QAAM,mBAAmB,YAAY;IACnC;IACA;IACA;IACA;EAAA,CACD;AAEK,QAAA,yBAAyB,kBAAkB,KAAK;AAEtD,MACE,iBAAiB,aACjB,mBAAmB,aACnB,uBAAuB,WACvB;AACO,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEA,MACE,mBAAmB,SACnB,uBAAuB,SACvB,CAAC,iBAAiB,YAClB,CAAC,iBAAiB,QAClB,CAAC,iBAAiB,QAClB;AACO,eAAA,wBAAC,KAAK,OAAL,CAAW,CAAA;EACrB;AAEA,MAAI,GAAC,8BAAmB,SAAnB,mBAAyB,SAAzB,mBAA+B,MAAK;AAChC,eAAA,wBAAC,KAAK,QAAL,CAAY,CAAA;EACtB;AAEA,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC;QACE,IAAI;QACJ,gBAAgB;MAClB;MACA;QACE,aAAa,uBAAuB,KAAK,SAAS;MACpD;IAAA,EAAA,CAEJ;QACA;MAAC;MAAA;QACC,KAAK,mBAAmB,KAAK,KAAK;QAClC,WAAW,uBAAuB,KAAK,SAAS;QAChD,UAAU,iBAAiB;QAC3B,MAAM,iBAAiB;QACvB,QAAQ,iBAAiB;QAEzB,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,QAAO,QAAO,YAAY,WACjD,UAAA;cAAA,wBAAC,eAAc,CAAA,CAAA;cAAA,wBACd,gBAAe,CAAA,CAAA;QAAA,EAAA,CAClB;MAAA;IACF;EACF,EAAA,CAAA;AAEJ;AAMA,IAAM,2BAA2B,MAAM;AACrC,QAAM,EAAE,MAAM,MAAM,IAAI,UAErB;AACG,QAAA;IACJ,cAAc,CAAC;IACf;IACA;EAAA,IACE,QAAQ,CAAC,EAAE,QAAQ,yCAAyC,SAAS,MAAO,CAAA,CAAC;AAEjF,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEI,MAAA,SAAS,CAAC,OAAO;AAEjB,eAAA;MAAC;MAAA;QACC,QAAO;QACP,OAAM;QACN,UAAS;QACT,KAAK;QACL,MAAM;QACN,QAAQ;QACR,YAAW;QAEX,cAAA,wBAAC,KAAK,OAAL,CAAA,CAAW;MAAA;IAAA;EAGlB;AAGE,aAAA;IAAC;IAAA;MACC,QAAO;MACP,OAAM;MACN,UAAS;MACT,KAAK;MACL,MAAM;MACN,QAAQ;MACR,YAAW;MAEX,cAAA,wBAAC,KAAK,SAAL,EAAa,aACX,UAAA,CAAC,EAAE,aAAAC,aAAY,UAAA,wBACb,cAAa,EAAA,aAAaA,cACzB,cAAC,wBAAA,aAAA,CAAA,CAAY,EACf,CAAA,EAAA,CAEJ;IAAA;EAAA;AAGN;AAEA,IAAM,uBAAuB,MAAM;AACjC,aAAA,wBACG,UACC,EAAA,cAAA,wBAAC,WAAA,EACC,cAAC,wBAAA,0BAAA,CAAyB,CAAA,EAC5B,CAAA,EACF,CAAA;AAEJ;", "names": ["Link", "LinkIcon", "permissions"]}