{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/ProfilePage.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { <PERSON>, Button, Flex, useNotifyAT, Grid, Typography } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport upperFirst from 'lodash/upperFirst';\nimport { useIntl } from 'react-intl';\nimport * as yup from 'yup';\n\nimport { Form, FormHelpers } from '../components/Form';\nimport { InputRenderer } from '../components/FormInputs/Renderer';\nimport { Layouts } from '../components/Layouts/Layout';\nimport { Page } from '../components/PageHelpers';\nimport { useTypedDispatch, useTypedSelector } from '../core/store/hooks';\nimport { useAuth } from '../features/Auth';\nimport { useNotification } from '../features/Notifications';\nimport { useTracking } from '../features/Tracking';\nimport { useAPIErrorHandler } from '../hooks/useAPIErrorHandler';\nimport { AppState, setAppTheme } from '../reducer';\nimport { useIsSSOLockedQuery, useUpdateMeMutation } from '../services/auth';\nimport { isBaseQueryError } from '../utils/baseQuery';\nimport { translatedErrors } from '../utils/translatedErrors';\nimport { getDisplayName } from '../utils/users';\n\nimport { COMMON_USER_SCHEMA } from './Settings/pages/Users/<USER>/validation';\n\nimport type { UpdateMe } from '../../../shared/contracts/users';\n\nconst PROFILE_VALIDTION_SCHEMA = yup.object().shape({\n  ...COMMON_USER_SCHEMA,\n  currentPassword: yup\n    .string()\n    // @ts-expect-error – no idea why this is failing.\n    .when(['password', 'confirmPassword'], (password, confirmPassword, passSchema) => {\n      return password || confirmPassword\n        ? passSchema\n            .required({\n              id: translatedErrors.required.id,\n              defaultMessage: 'This field is required',\n            })\n            .nullable()\n        : passSchema;\n    }),\n  preferedLanguage: yup.string().nullable(),\n});\n\n/* -------------------------------------------------------------------------------------------------\n * ProfilePage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProfilePage = () => {\n  const localeNames = useTypedSelector((state) => state.admin_app.language.localeNames);\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { toggleNotification } = useNotification();\n  const { notifyStatus } = useNotifyAT();\n  const currentTheme = useTypedSelector((state) => state.admin_app.theme.currentTheme);\n  const dispatch = useTypedDispatch();\n  const {\n    _unstableFormatValidationErrors: formatValidationErrors,\n    _unstableFormatAPIError: formatApiError,\n  } = useAPIErrorHandler();\n\n  const user = useAuth('ProfilePage', (state) => state.user);\n\n  React.useEffect(() => {\n    if (user) {\n      notifyStatus(\n        formatMessage({\n          id: 'Settings.profile.form.notify.data.loaded',\n          defaultMessage: 'Your profile data has been loaded',\n        })\n      );\n    } else {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n      });\n    }\n  }, [formatMessage, notifyStatus, toggleNotification, user]);\n\n  const [updateMe, { isLoading: isSubmittingForm }] = useUpdateMeMutation();\n\n  const {\n    isLoading,\n    data: dataSSO,\n    error,\n  } = useIsSSOLockedQuery(undefined, {\n    skip: !(window.strapi.isEE && window.strapi.features.isEnabled('sso')),\n  });\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'Settings.permissions.users.sso.provider.error' }),\n      });\n    }\n  }, [error, formatMessage, toggleNotification]);\n\n  type UpdateUsersMeBody = UpdateMe.Request['body'] & {\n    confirmPassword: string;\n    currentTheme: AppState['theme']['currentTheme'];\n  };\n\n  const handleSubmit = async (\n    body: UpdateUsersMeBody,\n    { setErrors }: FormHelpers<UpdateUsersMeBody>\n  ) => {\n    const { confirmPassword: _confirmPassword, currentTheme, ...bodyRest } = body;\n    let dataToSend = bodyRest;\n\n    // The password fields are optional. If the user didn't touch them, don't send any password\n    // to the API, because an empty string would throw a validation error\n    if (dataToSend.password === '') {\n      const {\n        password: _password,\n        currentPassword: _currentPassword,\n        ...passwordRequestBodyRest\n      } = dataToSend;\n      dataToSend = passwordRequestBodyRest;\n    }\n\n    const res = await updateMe(dataToSend);\n\n    if ('data' in res) {\n      dispatch(setAppTheme(currentTheme));\n\n      trackUsage('didChangeMode', { newMode: currentTheme });\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: 'notification.success.saved', defaultMessage: 'Saved' }),\n      });\n    }\n\n    if ('error' in res) {\n      if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n        setErrors(formatValidationErrors(res.error));\n      } else if (isBaseQueryError(res.error)) {\n        toggleNotification({\n          type: 'danger',\n          message: formatApiError(res.error),\n        });\n      } else {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n        });\n      }\n    }\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  const hasLockedRole = dataSSO?.isSSOLocked ?? false;\n  const { email, firstname, lastname, username, preferedLanguage } = user ?? {};\n  const initialData = {\n    email: email ?? '',\n    firstname: firstname ?? '',\n    lastname: lastname ?? '',\n    username: username ?? '',\n    preferedLanguage,\n    currentTheme,\n    confirmPassword: '',\n    password: '',\n  };\n\n  return (\n    <Page.Main aria-busy={isSubmittingForm}>\n      <Page.Title>\n        {formatMessage({\n          id: 'Settings.profile.form.section.head.title',\n          defaultMessage: 'User profile',\n        })}\n      </Page.Title>\n      <Form\n        method=\"PUT\"\n        onSubmit={handleSubmit}\n        initialValues={initialData}\n        validationSchema={PROFILE_VALIDTION_SCHEMA}\n      >\n        {({ isSubmitting, modified }) => (\n          <>\n            <Layouts.Header\n              title={getDisplayName(user)}\n              primaryAction={\n                <Button\n                  startIcon={<Check />}\n                  loading={isSubmitting}\n                  type=\"submit\"\n                  disabled={!modified}\n                >\n                  {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n                </Button>\n              }\n            />\n            <Box paddingBottom={10}>\n              <Layouts.Content>\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                  <UserInfoSection />\n                  {!hasLockedRole && <PasswordSection />}\n                  <PreferencesSection localeNames={localeNames} />\n                </Flex>\n              </Layouts.Content>\n            </Box>\n          </>\n        )}\n      </Form>\n    </Page.Main>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PasswordSection\n * -----------------------------------------------------------------------------------------------*/\n\nconst PasswordSection = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"filterShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Typography variant=\"delta\" tag=\"h2\">\n          {formatMessage({\n            id: 'global.change-password',\n            defaultMessage: 'Change password',\n          })}\n        </Typography>\n        {[\n          [\n            {\n              label: formatMessage({\n                id: 'Auth.form.currentPassword.label',\n                defaultMessage: 'Current Password',\n              }),\n              name: 'currentPassword',\n              size: 6,\n              type: 'password' as const,\n            },\n          ],\n          [\n            {\n              autoComplete: 'new-password',\n              label: formatMessage({\n                id: 'global.password',\n                defaultMessage: 'Password',\n              }),\n              name: 'password',\n              size: 6,\n              type: 'password' as const,\n            },\n            {\n              autoComplete: 'new-password',\n              label: formatMessage({\n                id: 'Auth.form.confirmPassword.label',\n                defaultMessage: 'Confirm Password',\n              }),\n              name: 'confirmPassword',\n              size: 6,\n              type: 'password' as const,\n            },\n          ],\n        ].map((row, index) => (\n          <Grid.Root key={index} gap={5}>\n            {row.map(({ size, ...field }) => (\n              <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n                <InputRenderer {...field} />\n              </Grid.Item>\n            ))}\n          </Grid.Root>\n        ))}\n      </Flex>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PreferencesSection\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PreferencesSectionProps {\n  localeNames: Record<string, string>;\n}\n\nconst PreferencesSection = ({ localeNames }: PreferencesSectionProps) => {\n  const { formatMessage } = useIntl();\n  const themesToDisplay = useTypedSelector((state) => state.admin_app.theme.availableThemes);\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"filterShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n          <Typography variant=\"delta\" tag=\"h2\">\n            {formatMessage({\n              id: 'Settings.profile.form.section.experience.title',\n              defaultMessage: 'Experience',\n            })}\n          </Typography>\n          <Typography>\n            {formatMessage(\n              {\n                id: 'Settings.profile.form.section.experience.interfaceLanguageHelp',\n                defaultMessage:\n                  'Preference changes will apply only to you. More information is available {here}.',\n              },\n              {\n                here: (\n                  <Box\n                    tag=\"a\"\n                    color=\"primary600\"\n                    target=\"_blank\"\n                    rel=\"noopener noreferrer\"\n                    href=\"https://docs.strapi.io/developer-docs/latest/development/admin-customization.html#locales\"\n                  >\n                    {formatMessage({\n                      id: 'Settings.profile.form.section.experience.here',\n                      defaultMessage: 'here',\n                    })}\n                  </Box>\n                ),\n              }\n            )}\n          </Typography>\n        </Flex>\n        <Grid.Root gap={5}>\n          {[\n            {\n              hint: formatMessage({\n                id: 'Settings.profile.form.section.experience.interfaceLanguage.hint',\n                defaultMessage: 'This will only display your own interface in the chosen language.',\n              }),\n              label: formatMessage({\n                id: 'Settings.profile.form.section.experience.interfaceLanguage',\n                defaultMessage: 'Interface language',\n              }),\n              name: 'preferedLanguage',\n              options: Object.entries(localeNames).map(([value, label]) => ({\n                label,\n                value,\n              })),\n              placeholder: formatMessage({\n                id: 'global.select',\n                defaultMessage: 'Select',\n              }),\n              size: 6,\n              type: 'enumeration' as const,\n            },\n            {\n              hint: formatMessage({\n                id: 'Settings.profile.form.section.experience.mode.hint',\n                defaultMessage: 'Displays your interface in the chosen mode.',\n              }),\n              label: formatMessage({\n                id: 'Settings.profile.form.section.experience.mode.label',\n                defaultMessage: 'Interface mode',\n              }),\n              name: 'currentTheme',\n              options: [\n                {\n                  label: formatMessage({\n                    id: 'Settings.profile.form.section.experience.mode.option-system-label',\n                    defaultMessage: 'Use system settings',\n                  }),\n                  value: 'system',\n                },\n                ...themesToDisplay.map((theme) => ({\n                  label: formatMessage(\n                    {\n                      id: 'Settings.profile.form.section.experience.mode.option-label',\n                      defaultMessage: '{name} mode',\n                    },\n                    {\n                      name: formatMessage({\n                        id: theme,\n                        defaultMessage: upperFirst(theme),\n                      }),\n                    }\n                  ),\n                  value: theme,\n                })),\n              ],\n              placeholder: formatMessage({\n                id: 'components.Select.placeholder',\n                defaultMessage: 'Select',\n              }),\n              size: 6,\n              type: 'enumeration' as const,\n            },\n          ].map(({ size, ...field }) => (\n            <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n              <InputRenderer {...field} />\n            </Grid.Item>\n          ))}\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * UserInfoSection\n * -----------------------------------------------------------------------------------------------*/\n\nconst UserInfoSection = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"filterShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Typography variant=\"delta\" tag=\"h2\">\n          {formatMessage({\n            id: 'global.profile',\n            defaultMessage: 'Profile',\n          })}\n        </Typography>\n        <Grid.Root gap={5}>\n          {[\n            {\n              label: formatMessage({\n                id: 'Auth.form.firstname.label',\n                defaultMessage: 'First name',\n              }),\n              name: 'firstname',\n              required: true,\n              size: 6,\n              type: 'string' as const,\n            },\n            {\n              label: formatMessage({\n                id: 'Auth.form.lastname.label',\n                defaultMessage: 'Last name',\n              }),\n              name: 'lastname',\n              size: 6,\n              type: 'string' as const,\n            },\n            {\n              label: formatMessage({\n                id: 'Auth.form.email.label',\n                defaultMessage: 'Email',\n              }),\n              name: 'email',\n              required: true,\n              size: 6,\n              type: 'email' as const,\n            },\n            {\n              label: formatMessage({\n                id: 'Auth.form.username.label',\n                defaultMessage: 'Username',\n              }),\n              name: 'username',\n              size: 6,\n              type: 'string' as const,\n            },\n          ].map(({ size, ...field }) => (\n            <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n              <InputRenderer {...field} />\n            </Grid.Item>\n          ))}\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n\nexport { ProfilePage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA2BA,IAAM,2BAA+BA,QAAO,EAAE,MAAM;EAClD,GAAG;EACH,iBACG,OAAO,EAEP,KAAK,CAAC,YAAY,iBAAiB,GAAG,CAAC,UAAU,iBAAiB,eAAe;AACzE,WAAA,YAAY,kBACf,WACG,SAAS;MACR,IAAIC,YAAiB,SAAS;MAC9B,gBAAgB;IAAA,CACjB,EACA,SAAA,IACH;EAAA,CACL;EACH,kBAAsB,OAAO,EAAE,SAAS;AAC1C,CAAC;AAMD,IAAM,cAAc,MAAM;AACxB,QAAM,cAAc,iBAAiB,CAAC,UAAU,MAAM,UAAU,SAAS,WAAW;AAC9E,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,WAAW,IAAI,YAAY;AAC7B,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AACzC,QAAA,EAAE,aAAa,IAAI,YAAY;AACrC,QAAM,eAAe,iBAAiB,CAAC,UAAU,MAAM,UAAU,MAAM,YAAY;AACnF,QAAM,WAAW,iBAAiB;AAC5B,QAAA;IACJ,iCAAiC;IACjC,yBAAyB;EAAA,IACvB,mBAAmB;AAEvB,QAAM,OAAO,QAAQ,eAAe,CAAC,UAAU,MAAM,IAAI;AAEzD,EAAM,gBAAU,MAAM;AACpB,QAAI,MAAM;AACR;QACE,cAAc;UACZ,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH,OACK;AACc,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,sBAAsB,gBAAgB,mBAAA,CAAoB;MAAA,CACxF;IAAA;EACH,GACC,CAAC,eAAe,cAAc,oBAAoB,IAAI,CAAC;AAE1D,QAAM,CAAC,UAAU,EAAE,WAAW,iBAAiB,CAAC,IAAI,oBAAoB;AAElE,QAAA;IACJ;IACA,MAAM;IACN;EAAA,IACE,oBAAoB,QAAW;IACjC,MAAM,EAAE,OAAO,OAAO,QAAQ,OAAO,OAAO,SAAS,UAAU,KAAK;EAAA,CACrE;AAED,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACU,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,gDAAA,CAAiD;MAAA,CAC/E;IAAA;EACH,GACC,CAAC,OAAO,eAAe,kBAAkB,CAAC;AAO7C,QAAM,eAAe,OACnB,MACA,EAAE,UAAA,MACC;AACH,UAAM,EAAE,iBAAiB,kBAAkB,cAAAC,eAAc,GAAG,SAAA,IAAa;AACzE,QAAI,aAAa;AAIb,QAAA,WAAW,aAAa,IAAI;AACxB,YAAA;QACJ,UAAU;QACV,iBAAiB;QACjB,GAAG;MAAA,IACD;AACS,mBAAA;IAAA;AAGT,UAAA,MAAM,MAAM,SAAS,UAAU;AAErC,QAAI,UAAU,KAAK;AACR,eAAA,YAAYA,aAAY,CAAC;AAElC,iBAAW,iBAAiB,EAAE,SAASA,cAAAA,CAAc;AAElC,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,8BAA8B,gBAAgB,QAAA,CAAS;MAAA,CACrF;IAAA;AAGH,QAAI,WAAW,KAAK;AAClB,UAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,MAAM,SAAS,mBAAmB;AAC7D,kBAAA,uBAAuB,IAAI,KAAK,CAAC;MAAA,WAClC,iBAAiB,IAAI,KAAK,GAAG;AACnB,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,IAAI,KAAK;QAAA,CAClC;MAAA,OACI;AACc,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc,EAAE,IAAI,sBAAsB,gBAAgB,mBAAA,CAAoB;QAAA,CACxF;MAAA;IACH;EACF;AAGF,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAA,CAAa;EAAA;AAGjB,QAAA,iBAAgB,mCAAS,gBAAe;AACxC,QAAA,EAAE,OAAO,WAAW,UAAU,UAAU,iBAAiB,IAAI,QAAQ,CAAA;AAC3E,QAAM,cAAc;IAClB,OAAO,SAAS;IAChB,WAAW,aAAa;IACxB,UAAU,YAAY;IACtB,UAAU,YAAY;IACtB;IACA;IACA,iBAAiB;IACjB,UAAU;EAAA;AAGZ,aACG,yBAAA,KAAK,MAAL,EAAU,aAAW,kBACpB,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAc,cAAA;MACb,IAAI;MACJ,gBAAgB;IAAA,CACjB,EACH,CAAA;QACA;MAAC;MAAA;QACC,QAAO;QACP,UAAU;QACV,eAAe;QACf,kBAAkB;QAEjB,UAAC,CAAA,EAAE,cAAc,SAAA,UAEd,yBAAA,6BAAA,EAAA,UAAA;cAAA;YAAC,QAAQ;YAAR;cACC,OAAO,eAAe,IAAI;cAC1B,mBACE;gBAAC;gBAAA;kBACC,eAAA,wBAAY,eAAM,CAAA,CAAA;kBAClB,SAAS;kBACT,MAAK;kBACL,UAAU,CAAC;kBAEV,UAAA,cAAc,EAAE,IAAI,eAAe,gBAAgB,OAAA,CAAQ;gBAAA;cAAA;YAC9D;UAAA;cAGH,wBAAA,KAAA,EAAI,eAAe,IAClB,cAAA,wBAAC,QAAQ,SAAR,EACC,cAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;gBAAA,wBAAC,iBAAgB,CAAA,CAAA;YAChB,CAAC,qBAAiB,wBAAC,iBAAgB,CAAA,CAAA;gBACpC,wBAAC,oBAAA,EAAmB,YAA0B,CAAA;UAAA,EAAA,CAChD,EAAA,CACF,EACF,CAAA;QAAA,EACF,CAAA;MAAA;IAAA;EAEJ,EACF,CAAA;AAEJ;AAMA,IAAM,kBAAkB,MAAM;AACtB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAGhC,aAAA;IAAC;IAAA;MACC,YAAW;MACX,WAAS;MACT,QAAO;MACP,YAAY;MACZ,eAAe;MACf,aAAa;MACb,cAAc;MAEd,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;YAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EACH,CAAA;QACC;UACC;YACE;cACE,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,MAAM;cACN,MAAM;cACN,MAAM;YAAA;UACR;UAEF;YACE;cACE,cAAc;cACd,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,MAAM;cACN,MAAM;cACN,MAAM;YAAA;YAER;cACE,cAAc;cACd,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,MAAM;cACN,MAAM;cACN,MAAM;YAAA;UACR;QACF,EACA,IAAI,CAAC,KAAK,cAAA,wBACT,KAAK,MAAL,EAAsB,KAAK,GACzB,UAAI,IAAA,IAAI,CAAC,EAAE,MAAM,GAAG,MAAM,UACxB,wBAAA,KAAK,MAAL,EAA2B,KAAK,MAAM,WAAU,UAAS,YAAW,WACnE,cAAA,wBAACC,uBAAe,EAAA,GAAG,MAAA,CAAO,EADZ,GAAA,MAAM,IAEtB,CACD,EAAA,GALa,KAMhB,CACD;MAAA,EACH,CAAA;IAAA;EAAA;AAGN;AAUA,IAAM,qBAAqB,CAAC,EAAE,YAAA,MAA2C;AACjE,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,UAAU,MAAM,eAAe;AAGvF,aAAA;IAAC;IAAA;MACC,YAAW;MACX,WAAS;MACT,QAAO;MACP,YAAY;MACZ,eAAe;MACf,aAAa;MACb,cAAc;MAEd,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;YAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EACH,CAAA;cAAA,wBACC,YACE,EAAA,UAAA;YACC;cACE,IAAI;cACJ,gBACE;YAAA;YAEJ;cACE,UACE;gBAAC;gBAAA;kBACC,KAAI;kBACJ,OAAM;kBACN,QAAO;kBACP,KAAI;kBACJ,MAAK;kBAEJ,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBAAA,CACjB;gBAAA;cAAA;YACH;UAEJ,EAEJ,CAAA;QAAA,EACF,CAAA;YACC,wBAAA,KAAK,MAAL,EAAU,KAAK,GACb,UAAA;UACC;YACE,MAAM,cAAc;cAClB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,OAAO,cAAc;cACnB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,SAAS,OAAO,QAAQ,WAAW,EAAE,IAAI,CAAC,CAAC,OAAO,KAAK,OAAO;cAC5D;cACA;YAAA,EACA;YACF,aAAa,cAAc;cACzB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,MAAM;UAAA;UAER;YACE,MAAM,cAAc;cAClB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,OAAO,cAAc;cACnB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,SAAS;cACP;gBACE,OAAO,cAAc;kBACnB,IAAI;kBACJ,gBAAgB;gBAAA,CACjB;gBACD,OAAO;cAAA;cAET,GAAG,gBAAgB,IAAI,CAAC,WAAW;gBACjC,OAAO;kBACL;oBACE,IAAI;oBACJ,gBAAgB;kBAAA;kBAElB;oBACE,MAAM,cAAc;sBAClB,IAAI;sBACJ,oBAAgB,kBAAAC,SAAW,KAAK;oBAAA,CACjC;kBAAA;gBACH;gBAEF,OAAO;cAAA,EACP;YAAA;YAEJ,aAAa,cAAc;cACzB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,MAAM;UAAA;QACR,EACA,IAAI,CAAC,EAAE,MAAM,GAAG,MAAA,UAChB,wBAAC,KAAK,MAAL,EAA2B,KAAK,MAAM,WAAU,UAAS,YAAW,WACnE,cAAC,wBAAAD,uBAAA,EAAe,GAAG,MAAA,CAAO,EADZ,GAAA,MAAM,IAEtB,CACD,EACH,CAAA;MAAA,EACF,CAAA;IAAA;EAAA;AAGN;AAMA,IAAM,kBAAkB,MAAM;AACtB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAGhC,aAAA;IAAC;IAAA;MACC,YAAW;MACX,WAAS;MACT,QAAO;MACP,YAAY;MACZ,eAAe;MACf,aAAa;MACb,cAAc;MAEd,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;YAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EACH,CAAA;YACC,wBAAA,KAAK,MAAL,EAAU,KAAK,GACb,UAAA;UACC;YACE,OAAO,cAAc;cACnB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,UAAU;YACV,MAAM;YACN,MAAM;UAAA;UAER;YACE,OAAO,cAAc;cACnB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,MAAM;YACN,MAAM;UAAA;UAER;YACE,OAAO,cAAc;cACnB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,UAAU;YACV,MAAM;YACN,MAAM;UAAA;UAER;YACE,OAAO,cAAc;cACnB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YACD,MAAM;YACN,MAAM;YACN,MAAM;UAAA;QACR,EACA,IAAI,CAAC,EAAE,MAAM,GAAG,MAAA,UAChB,wBAAC,KAAK,MAAL,EAA2B,KAAK,MAAM,WAAU,UAAS,YAAW,WACnE,cAAC,wBAAAA,uBAAA,EAAe,GAAG,MAAA,CAAO,EADZ,GAAA,MAAM,IAEtB,CACD,EACH,CAAA;MAAA,EACF,CAAA;IAAA;EAAA;AAGN;", "names": ["create", "translatedErrors", "currentTheme", "InputR<PERSON><PERSON>", "upperFirst"]}