import {
  Page,
  login,
  useTypedDispatch
} from "./chunk-4C2ZQ5OG.js";
import {
  useIntl
} from "./chunk-FGNN7I5W.js";
import {
  useMatch,
  useNavigate
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/AuthResponse-BKpQdQAD.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var getCookieValue = (name) => {
  let result = null;
  const cookieArray = document.cookie.split(";");
  cookieArray.forEach((cookie) => {
    const [key, value] = cookie.split("=").map((item) => item.trim());
    if (key.trim() === name) {
      result = decodeURIComponent(value);
    }
  });
  return result;
};
var deleteCookie = (name) => {
  document.cookie = name + "=; Path=/; Expires=Thu, 01 Jan 1970 00:00:01 GMT;";
};
var AuthResponse = () => {
  const match = useMatch("/auth/login/:authResponse");
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const dispatch = useTypedDispatch();
  const redirectToOops = React.useCallback(() => {
    navigate({
      pathname: "/auth/oops",
      search: `?info=${encodeURIComponent(
        formatMessage({
          id: "Auth.form.button.login.providers.error",
          defaultMessage: "We cannot connect you through the selected provider."
        })
      )}`
    });
  }, [navigate, formatMessage]);
  React.useEffect(() => {
    if ((match == null ? void 0 : match.params.authResponse) === "error") {
      redirectToOops();
    }
    if ((match == null ? void 0 : match.params.authResponse) === "success") {
      const jwtToken = getCookieValue("jwtToken");
      if (jwtToken) {
        dispatch(
          login({
            token: jwtToken
          })
        );
        deleteCookie("jwtToken");
        navigate("/auth/login");
      } else {
        redirectToOops();
      }
    }
  }, [dispatch, match, redirectToOops, navigate]);
  return (0, import_jsx_runtime.jsx)(Page.Loading, {});
};
export {
  AuthResponse
};
//# sourceMappingURL=AuthResponse-BKpQdQAD-V27ST6QT.js.map
