import{au as n,m as s,bs as t}from"./strapi-YzJfjJ2z.js";import{a}from"./SelectRoles-C1P9kRCc-DM4OhDyY.js";import"./useAdminRoles-Bd2N7J7A-CIPQp8aL.js";const g=({registrationToken:e})=>{const{formatMessage:i}=n();return e?s.jsx(a,{target:`${window.location.origin}${t()}/auth/register?registrationToken=${e}`,children:i({id:"app.components.Users.MagicLink.connect",defaultMessage:"Copy and share this link to give access to this user"})}):s.jsx(a,{target:`${window.location.origin}${t()}/auth/login`,children:i({id:"app.components.Users.MagicLink.connect.sso",defaultMessage:"Send this link to the user, the first login can be made via a SSO provider."})})};export{g as MagicLinkEE};
