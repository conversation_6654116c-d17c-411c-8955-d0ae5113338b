{"version": 3, "sources": ["../../../@strapi/content-manager/dist/_chunks/nl-D4R9gHx5.mjs"], "sourcesContent": ["const groups = \"Groepen\";\nconst models = \"Collectie Types\";\nconst pageNotFound = \"Pagina niet gevonden\";\nconst nl = {\n  \"App.schemas.data-loaded\": \"De sche<PERSON>'s zijn succesvol geladen\",\n  \"ListViewTable.relation-loaded\": \"De relaties zijn geladen\",\n  \"ListViewTable.relation-loading\": \"Relaties worden geladen\",\n  \"ListViewTable.relation-more\": \"Deze relatie bevat meer entiteiten dan weergegeven\",\n  \"EditRelations.title\": \"Gerelateerde data\",\n  \"HeaderLayout.button.label-add-entry\": \"Nieuwe invoer\",\n  \"api.id\": \"API ID\",\n  \"components.AddFilterCTA.add\": \"Filters\",\n  \"components.AddFilterCTA.hide\": \"Filters\",\n  \"components.DragHandle-label\": \"Sleep\",\n  \"components.DraggableAttr.edit\": \"Klik om aan te passen\",\n  \"components.DraggableCard.delete.field\": \"Verwijder {item}\",\n  \"components.DraggableCard.edit.field\": \"Pas {item} aan\",\n  \"components.DraggableCard.move.field\": \"Verplaats {item}\",\n  \"components.ListViewTable.row-line\": \"item regel {number}\",\n  \"components.DynamicZone.ComponentPicker-label\": \"Kies één component\",\n  \"components.DynamicZone.add-component\": \"Voeg een component toe aan {componentName}\",\n  \"components.DynamicZone.delete-label\": \"Verwijder {name}\",\n  \"components.DynamicZone.error-message\": \"Het component bevat fout(en)\",\n  \"components.DynamicZone.missing-components\": \"Er {number, plural, =0 {zijn # missende componenten} one {is één # missend component} other {zijn # missende componenten}}\",\n  \"components.DynamicZone.move-down-label\": \"Verplaats component naar beneden\",\n  \"components.DynamicZone.move-up-label\": \"Verplaats component naar boven\",\n  \"components.DynamicZone.pick-compo\": \"Kies één component\",\n  \"components.DynamicZone.required\": \"Component is vereist\",\n  \"components.EmptyAttributesBlock.button\": \"Ga naar instellingspagina\",\n  \"components.EmptyAttributesBlock.description\": \"Je kunt je instellingen wijzigen\",\n  \"components.FieldItem.linkToComponentLayout\": \"Stel de lay-out van het component in\",\n  \"components.FieldSelect.label\": \"Voeg veld toe\",\n  \"components.FilterOptions.button.apply\": \"Pas toe\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Pas toe\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Wis alles\",\n  \"components.FiltersPickWrapper.PluginHeader.description\": \"Stel de voorwaarden in om toe te passen voor het filteren van de items\",\n  \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filters\",\n  \"components.FiltersPickWrapper.hide\": \"Verberg\",\n  \"components.LeftMenu.Search.label\": \"Zoek een Content-Type\",\n  \"components.LeftMenu.collection-types\": \"Collectie Types\",\n  \"components.LeftMenu.single-types\": \"Enkele Types\",\n  \"components.LimitSelect.itemsPerPage\": \"Items per pagina\",\n  \"components.NotAllowedInput.text\": \"Geen rechten om dit veld te bekijken\",\n  \"components.RepeatableComponent.error-message\": \"Eén of meerdere componenten bevatten een fout\",\n  \"components.Search.placeholder\": \"Zoek een item...\",\n  \"components.Select.draft-info-title\": \"Status: Concept\",\n  \"components.Select.publish-info-title\": \"Status: Gepubliceerd\",\n  \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Pas aan hoe de bewerkingsweergave eruit zal zien.\",\n  \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Definieer de instellingen van de lijstweergave.\",\n  \"components.SettingsViewWrapper.pluginHeader.title\": \"Configureer de weergave - {name}\",\n  \"components.TableDelete.delete\": \"Verwijder alle\",\n  \"components.TableDelete.deleteSelected\": \"Verwijder geselecteerde\",\n  \"components.TableDelete.label\": \"{number, plural, one {# entry} other {# entries}} geselecteerd\",\n  \"components.TableEmpty.withFilters\": \"Er zijn geen {contentType} met de geselecteerde filters...\",\n  \"components.TableEmpty.withSearch\": \"Er zijn geen {contentType} die overeenkomen met de zoekopdracht ({search})...\",\n  \"components.TableEmpty.withoutFilter\": \"Er zijn geen {contentType}...\",\n  \"components.empty-repeatable\": \"Nog geen items. Klik op de knop hieronder om er een toe te voegen.\",\n  \"components.notification.info.maximum-requirement\": \"Je hebt het maximale aantal velden al bereikt\",\n  \"components.notification.info.minimum-requirement\": \"Een veld is toegevoegd om te voldoen aan de minimale voorwaarde\",\n  \"components.repeatable.reorder.error\": \"Er is een fout opgetreden bij het opnieuw sorteren van het veld van je component, probeer het a.u.b. opnieuw\",\n  \"components.reset-entry\": \"Reset invoer\",\n  \"components.uid.apply\": \"pas toe\",\n  \"components.uid.available\": \"Beschikbaar\",\n  \"components.uid.regenerate\": \"Regenereer\",\n  \"components.uid.suggested\": \"voorgesteld\",\n  \"components.uid.unavailable\": \"Onbeschikbaar\",\n  \"containers.Edit.Link.Layout\": \"Configureer de lay-out\",\n  \"containers.Edit.Link.Model\": \"Pas het collectie-type aan\",\n  \"containers.Edit.addAnItem\": \"Voeg een item toe...\",\n  \"containers.Edit.clickToJump\": \"Klik om naar de invoer te springen\",\n  \"containers.Edit.delete\": \"Verwijder\",\n  \"containers.Edit.delete-entry\": \"Verwijder deze invoer\",\n  \"containers.Edit.editing\": \"Aanpassen...\",\n  \"containers.Edit.information\": \"Informatie\",\n  \"containers.Edit.information.by\": \"Door\",\n  \"containers.Edit.information.created\": \"Gecreëerd\",\n  \"containers.Edit.information.draftVersion\": \"concept versie\",\n  \"containers.Edit.information.editing\": \"Aan het bewerken\",\n  \"containers.Edit.information.lastUpdate\": \"Laatste update\",\n  \"containers.Edit.information.publishedVersion\": \"gepubliceerde versie\",\n  \"containers.Edit.pluginHeader.title.new\": \"Voeg een invoer toe\",\n  \"containers.Edit.reset\": \"Resetten\",\n  \"containers.Edit.returnList\": \"Terug naar lijst\",\n  \"containers.Edit.seeDetails\": \"Details\",\n  \"containers.Edit.submit\": \"Opslaan\",\n  \"containers.EditSettingsView.modal-form.edit-field\": \"Veld aanpassen\",\n  \"containers.EditView.add.new-entry\": \"Voeg een invoer toe\",\n  \"containers.EditView.notification.errors\": \"Het formulier bevat fouten\",\n  \"containers.Home.introduction\": \"Om items aan te passen klik je op de link in het menu links boven. Deze plugin heeft nog geen goede manier om instellingen aan te passen en is nog in ontwikkeling.\",\n  \"containers.Home.pluginHeaderDescription\": \"Onderhoud je data via een krachtig en mooie interface.\",\n  \"containers.Home.pluginHeaderTitle\": \"Content Manager\",\n  \"containers.List.draft\": \"Concept\",\n  \"containers.List.errorFetchRecords\": \"Fout\",\n  \"containers.List.published\": \"Gepubliceerd\",\n  \"containers.list.displayedFields\": \"Weergegeven velden\",\n  \"containers.list.items\": \"{number, plural, =0 {items} one {item} other {items}}\",\n  \"containers.list.table-headers.publishedAt\": \"Status\",\n  \"containers.ListSettingsView.modal-form.edit-label\": \"Pas {fieldName} aan\",\n  \"containers.SettingPage.add.field\": \"Voeg nog een veld toe\",\n  \"containers.SettingPage.attributes\": \"Attribuut velden\",\n  \"containers.SettingPage.attributes.description\": \"Geef de volgorde van de attributen aan\",\n  \"containers.SettingPage.editSettings.description\": \"Klik & sleep de velden om de lay-out te bouwen\",\n  \"containers.SettingPage.editSettings.entry.title\": \"Invoer titel\",\n  \"containers.SettingPage.editSettings.entry.title.description\": \"Stel het weergegeven veld van je invoer in\",\n  \"containers.SettingPage.editSettings.relation-field.description\": \"Stel het weergegeven veld in voor zowel de bewerkings- als de lijstweergave\",\n  \"containers.SettingPage.editSettings.title\": \"Weergave aanpassen (instellingen)\",\n  \"containers.SettingPage.layout\": \"Lay-out\",\n  \"containers.SettingPage.listSettings.description\": \"Configureer de opties voor dit collectie type\",\n  \"containers.SettingPage.listSettings.title\": \"Lijstweergave (instellingen)\",\n  \"containers.SettingPage.pluginHeaderDescription\": \"Configureer de specifieke instellingen voor dit collectie type\",\n  \"containers.SettingPage.settings\": \"Instellingen\",\n  \"containers.SettingPage.view\": \"Bekijk\",\n  \"containers.SettingViewModel.pluginHeader.title\": \"Content Manager - {name}\",\n  \"containers.SettingsPage.Block.contentType.description\": \"Configureer de specifieke instellingen\",\n  \"containers.SettingsPage.Block.contentType.title\": \"Collectie Types\",\n  \"containers.SettingsPage.Block.generalSettings.description\": \"Configureer de standaard instellingen voor je Collection Types\",\n  \"containers.SettingsPage.Block.generalSettings.title\": \"Algemeen\",\n  \"containers.SettingsPage.pluginHeaderDescription\": \"Configureer de instellingen voor je collectie types en groepen\",\n  \"containers.SettingsView.list.subtitle\": \"Configureer de instellingen voor je collectie types en groepen\",\n  \"containers.SettingsView.list.title\": \"Geef configuraties weer\",\n  \"edit-settings-view.link-to-ctb.components\": \"Pas component aan\",\n  \"edit-settings-view.link-to-ctb.content-types\": \"Pas het Content-Type aan\",\n  \"emptyAttributes.button\": \"Ga naar collectie type bouwer\",\n  \"emptyAttributes.description\": \"Voeg je eerste veld toe aan je Collectie Type\",\n  \"emptyAttributes.title\": \"Er zijn nog geen velden\",\n  \"error.attribute.key.taken\": \"Deze waarde bestaat al.\",\n  \"error.attribute.sameKeyAndName\": \"Mag niet gelijk zijn.\",\n  \"error.attribute.taken\": \"Deze veld naam bestaat al.\",\n  \"error.contentTypeName.taken\": \"Deze naam bestaat al.\",\n  \"error.model.fetch\": \"Er is een fout opgetreden tijdens het ophalen van de modellen.\",\n  \"error.record.create\": \"Er is een fout opgetreden tijdens het maken van het item.\",\n  \"error.record.delete\": \"Er is een fout opgetreden tijdens het verwijderen van het item.\",\n  \"error.record.fetch\": \"Er is een fout opgetreden tijdens het ophalen van het item.\",\n  \"error.record.update\": \"Er is een fout opgetreden tijdens het opslaan van het item.\",\n  \"error.records.count\": \"Er is een fout opgetreden tijdens het tellen van de opgehaalde gegevens.\",\n  \"error.records.fetch\": \"Er is een fout opgetreden tijdens het ophalen van de gegevens.\",\n  \"error.schema.generation\": \"Er is een fout opgetreden tijdens het maken van het schema.\",\n  \"error.validation.json\": \"Dit is geen JSON.\",\n  \"error.validation.max\": \"De waarde is te hoog.\",\n  \"error.validation.maxLength\": \"De waarde is te lang.\",\n  \"error.validation.min\": \"De waarde is te laag.\",\n  \"error.validation.minLength\": \"De waarde is te kort.\",\n  \"error.validation.minSupMax\": \"Mag niet superieur zijn.\",\n  \"error.validation.regex\": \"De waarde is niet gelijk aan de regex.\",\n  \"error.validation.required\": \"Deze gegevens zijn verplicht.\",\n  \"form.Input.bulkActions\": \"Schakel bulkacties in\",\n  \"form.Input.defaultSort\": \"Standaard sorteringsattribuut\",\n  \"form.Input.description\": \"Beschrijving\",\n  \"form.Input.description.placeholder\": \"Weergavenaam in het profiel\",\n  \"form.Input.editable\": \"Aanpasbaar veld\",\n  \"form.Input.filters\": \"Schakel filters in\",\n  \"form.Input.label\": \"Label\",\n  \"form.Input.label.inputDescription\": \"Deze waarde overschrijft het label welke weergegeven wordt in het hoofd van de tabel\",\n  \"form.Input.pageEntries\": \"Items per pagina\",\n  \"form.Input.pageEntries.inputDescription\": \"Opmerking: Je kan deze waarde overschrijven in de Collectie Type instellingspagina\",\n  \"form.Input.placeholder\": \"Placeholder\",\n  \"form.Input.placeholder.placeholder\": \"Mijn geweldige waarde\",\n  \"form.Input.search\": \"Schakel zoeken in\",\n  \"form.Input.search.field\": \"Schakel zoeken in voor dit veld\",\n  \"form.Input.sort.field\": \"Schakel sorteren in voor dit veld\",\n  \"form.Input.sort.order\": \"Standaard sorteervolgorde\",\n  \"form.Input.wysiwyg\": \"Weergeef als WYSIWYG\",\n  \"global.displayedFields\": \"Weergegeven velden\",\n  groups,\n  \"groups.numbered\": \"Groepen ({number})\",\n  \"header.name\": \"Content\",\n  \"link-to-ctb\": \"Pas het model aan\",\n  models,\n  \"models.numbered\": \"Collectie Types ({number})\",\n  \"notification.error.displayedFields\": \"Je hebt op z'n minst één weergegeven veld nodig.\",\n  \"notification.error.relationship.fetch\": \"Er is een fout opgetreden tijdens het ophalen van de relaties.\",\n  \"notification.info.SettingPage.disableSort\": \"Je hebt op z'n minst één attribuut nodig waar sorteren toegestaan is.\",\n  \"notification.info.minimumFields\": \"Je hebt op z'n minst één weergegeven veld nodig.\",\n  \"notification.upload.error\": \"Er is een fout opgetreden tijdens het uploaden van je bestanden\",\n  pageNotFound,\n  \"pages.ListView.header-subtitle\": \"{number, plural, =0 {# entries} one {# entry} other {# entries}} gevonden\",\n  \"pages.NoContentType.button\": \"Creëer je eerste Content-Type\",\n  \"pages.NoContentType.text\": \"Je hebt nog geen content, we raden je aan je eerste Content-Type te creëeren.\",\n  \"permissions.not-allowed.create\": \"Je hebt niet de rechten om een document te maken\",\n  \"permissions.not-allowed.update\": \"Je hebt niet de rechten om dit document te zien\",\n  \"plugin.description.long\": \"Snelle manier om data te zien, aan te passen en te verwijderen in je database\",\n  \"plugin.description.short\": \"Snelle manier om data te zien, aan te passen en te verwijderen in je database.\",\n  \"popover.display-relations.label\": \"Geef gerelateerde content weer\",\n  \"select.currently.selected\": \"{count} nu geselecteerd\",\n  \"success.record.delete\": \"Verwijderd\",\n  \"success.record.publish\": \"Gepubliceerd\",\n  \"success.record.save\": \"Opgeslagen\",\n  \"success.record.unpublish\": \"Depubliceren\",\n  \"utils.data-loaded\": \"{number, plural, =1 {Het item is} other {De items zijn}} succesvol geladen!\",\n  \"apiError.This attribute must be unique\": \"{field} moet uniek zijn\",\n  \"popUpWarning.warning.has-draft-relations.title\": \"Bevestiging\",\n  \"popUpWarning.warning.publish-question\": \"Wil je toch publiceren?\",\n  \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Ja, publiceren\",\n  \"popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, one { relatie is } other { relaties zijn } }</b> nog niet gepubliceerd en kan leiden tot onverwacht gedrag.\"\n};\nexport {\n  nl as default,\n  groups,\n  models,\n  pageNotFound\n};\n//# sourceMappingURL=nl-D4R9gHx5.mjs.map\n"], "mappings": ";;;AAAA,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,KAAK;AAAA,EACT,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,2DAA2D;AAAA,EAC3D,oDAAoD;AACtD;", "names": []}