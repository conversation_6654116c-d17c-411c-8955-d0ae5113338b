import{au as s,m as e,L as a,aX as n,J as i,bM as o,bK as r,bT as l,bU as d}from"./strapi-YzJfjJ2z.js";const u=()=>{const{formatMessage:t}=s();return e.jsx(a.<PERSON>,{children:e.jsxs(n,{children:[e.jsx(a<PERSON>,{title:t({id:"Settings.sso.title",defaultMessage:"Single Sign-On"}),subtitle:t({id:"Settings.sso.subTitle",defaultMessage:"Configure the settings for the Single Sign-On feature."})}),e.jsx(i,{paddingLeft:10,paddingRight:10,children:e.jsx(o,{icon:e.jsx(d,{width:"16rem"}),content:t({id:"Settings.sso.not-available",defaultMessage:"SSO is only available as part of a paid plan. Upgrade to configure additional sign-in & sign-up methods for your administration panel."}),action:e.jsx(r,{variant:"default",endIcon:e.jsx(l,{}),href:"https://strp.cc/46Fk1BA",isExternal:!0,target:"_blank",children:t({id:"global.learn-more",defaultMessage:"Learn more"})})})})]})})};export{u as PurchaseSingleSignOn};
