{"version": 3, "sources": ["../../../lodash/_baseDifference.js", "../../../lodash/without.js", "../../../lodash/take.js", "../../../@strapi/plugin-users-permissions/admin/src/contexts/UsersPermissionsContext/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/utils/formatPluginName.js", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/init.js", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/PermissionRow/CheckboxWrapper.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/PermissionRow/SubCategory.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/PermissionRow/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/reducer.js", "../../../@strapi/plugin-users-permissions/admin/src/components/Permissions/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/BoundRoute/getMethodColor.js", "../../../@strapi/plugin-users-permissions/admin/src/components/BoundRoute/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/Policies/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/UsersPermissions/init.js", "../../../@strapi/plugin-users-permissions/admin/src/components/UsersPermissions/reducer.js", "../../../@strapi/plugin-users-permissions/admin/src/components/UsersPermissions/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/constants.js", "../../../@strapi/plugin-users-permissions/admin/src/utils/cleanPermissions.js", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/hooks/usePlugins.js", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/pages/CreatePage.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/pages/EditPage.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/pages/ListPage/components/TableBody.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/pages/ListPage/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Roles/index.jsx"], "sourcesContent": ["var SetCache = require('./_SetCache'),\n    arrayIncludes = require('./_arrayIncludes'),\n    arrayIncludesWith = require('./_arrayIncludesWith'),\n    arrayMap = require('./_arrayMap'),\n    baseUnary = require('./_baseUnary'),\n    cacheHas = require('./_cacheHas');\n\n/** Used as the size to enable large array optimizations. */\nvar LARGE_ARRAY_SIZE = 200;\n\n/**\n * The base implementation of methods like `_.difference` without support\n * for excluding multiple arrays or iteratee shorthands.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {Array} values The values to exclude.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns the new array of filtered values.\n */\nfunction baseDifference(array, values, iteratee, comparator) {\n  var index = -1,\n      includes = arrayIncludes,\n      isCommon = true,\n      length = array.length,\n      result = [],\n      valuesLength = values.length;\n\n  if (!length) {\n    return result;\n  }\n  if (iteratee) {\n    values = arrayMap(values, baseUnary(iteratee));\n  }\n  if (comparator) {\n    includes = arrayIncludesWith;\n    isCommon = false;\n  }\n  else if (values.length >= LARGE_ARRAY_SIZE) {\n    includes = cacheHas;\n    isCommon = false;\n    values = new SetCache(values);\n  }\n  outer:\n  while (++index < length) {\n    var value = array[index],\n        computed = iteratee == null ? value : iteratee(value);\n\n    value = (comparator || value !== 0) ? value : 0;\n    if (isCommon && computed === computed) {\n      var valuesIndex = valuesLength;\n      while (valuesIndex--) {\n        if (values[valuesIndex] === computed) {\n          continue outer;\n        }\n      }\n      result.push(value);\n    }\n    else if (!includes(values, computed, comparator)) {\n      result.push(value);\n    }\n  }\n  return result;\n}\n\nmodule.exports = baseDifference;\n", "var baseDifference = require('./_baseDifference'),\n    baseRest = require('./_baseRest'),\n    isArrayLikeObject = require('./isArrayLikeObject');\n\n/**\n * Creates an array excluding all given values using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * **Note:** Unlike `_.pull`, this method returns a new array.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to inspect.\n * @param {...*} [values] The values to exclude.\n * @returns {Array} Returns the new array of filtered values.\n * @see _.difference, _.xor\n * @example\n *\n * _.without([2, 1, 2, 3], 1, 2);\n * // => [3]\n */\nvar without = baseRest(function(array, values) {\n  return isArrayLikeObject(array)\n    ? baseDifference(array, values)\n    : [];\n});\n\nmodule.exports = without;\n", "var baseSlice = require('./_baseSlice'),\n    toInteger = require('./toInteger');\n\n/**\n * Creates a slice of `array` with `n` elements taken from the beginning.\n *\n * @static\n * @memberOf _\n * @since 0.1.0\n * @category Array\n * @param {Array} array The array to query.\n * @param {number} [n=1] The number of elements to take.\n * @param- {Object} [guard] Enables use as an iteratee for methods like `_.map`.\n * @returns {Array} Returns the slice of `array`.\n * @example\n *\n * _.take([1, 2, 3]);\n * // => [1]\n *\n * _.take([1, 2, 3], 2);\n * // => [1, 2]\n *\n * _.take([1, 2, 3], 5);\n * // => [1, 2, 3]\n *\n * _.take([1, 2, 3], 0);\n * // => []\n */\nfunction take(array, n, guard) {\n  if (!(array && array.length)) {\n    return [];\n  }\n  n = (guard || n === undefined) ? 1 : toInteger(n);\n  return baseSlice(array, 0, n < 0 ? 0 : n);\n}\n\nmodule.exports = take;\n", "import React, { createContext, useContext } from 'react';\n\nimport PropTypes from 'prop-types';\n\nconst UsersPermissions = createContext({});\n\nconst UsersPermissionsProvider = ({ children, value }) => {\n  return <UsersPermissions.Provider value={value}>{children}</UsersPermissions.Provider>;\n};\n\nconst useUsersPermissions = () => useContext(UsersPermissions);\n\nUsersPermissionsProvider.propTypes = {\n  children: PropTypes.node.isRequired,\n  value: PropTypes.object.isRequired,\n};\n\nexport { UsersPermissions, UsersPermissionsProvider, useUsersPermissions };\n", "import upperFirst from 'lodash/upperFirst';\n\nfunction formatPluginName(pluginSlug) {\n  switch (pluginSlug) {\n    case 'application':\n      return 'Application';\n    case 'plugin::content-manager':\n      return 'Content manager';\n    case 'plugin::content-type-builder':\n      return 'Content types builder';\n    case 'plugin::documentation':\n      return 'Documentation';\n    case 'plugin::email':\n      return 'Email';\n    case 'plugin::i18n':\n      return 'i18n';\n    case 'plugin::upload':\n      return 'Upload';\n    case 'plugin::users-permissions':\n      return 'Users-permissions';\n    default:\n      return upperFirst(pluginSlug.replace('api::', '').replace('plugin::', ''));\n  }\n}\n\nexport default formatPluginName;\n", "const init = (initialState, permissions) => {\n  const collapses = Object.keys(permissions)\n    .sort()\n    .map((name) => ({ name, isOpen: false }));\n\n  return { ...initialState, collapses };\n};\n\nexport default init;\n", "import { Box } from '@strapi/design-system';\nimport { styled, css } from 'styled-components';\n\nconst activeCheckboxWrapperStyles = css`\n  background: ${(props) => props.theme.colors.primary100};\n\n  #cog {\n    opacity: 1;\n  }\n`;\n\nconst CheckboxWrapper = styled(Box)`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  #cog {\n    opacity: 0;\n    path {\n      fill: ${(props) => props.theme.colors.primary600};\n    }\n  }\n\n  /* Show active style both on hover and when the action is selected */\n  ${(props) => props.isActive && activeCheckboxWrapperStyles}\n  &:hover {\n    ${activeCheckboxWrapperStyles}\n  }\n`;\n\nexport default CheckboxWrapper;\n", "import React, { useCallback, useMemo } from 'react';\n\nimport { Box, Checkbox, Flex, Typography, Grid, VisuallyHidden } from '@strapi/design-system';\nimport { Cog } from '@strapi/icons';\nimport get from 'lodash/get';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useUsersPermissions } from '../../../contexts/UsersPermissionsContext';\n\nimport CheckboxWrapper from './CheckboxWrapper';\n\nconst Border = styled.div`\n  flex: 1;\n  align-self: center;\n  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};\n`;\n\nconst SubCategory = ({ subCategory }) => {\n  const { formatMessage } = useIntl();\n  const { onChange, onChangeSelectAll, onSelectedAction, selectedAction, modifiedData } =\n    useUsersPermissions();\n\n  const currentScopedModifiedData = useMemo(() => {\n    return get(modifiedData, subCategory.name, {});\n  }, [modifiedData, subCategory]);\n\n  const hasAllActionsSelected = useMemo(() => {\n    return Object.values(currentScopedModifiedData).every((action) => action.enabled === true);\n  }, [currentScopedModifiedData]);\n\n  const hasSomeActionsSelected = useMemo(() => {\n    return (\n      Object.values(currentScopedModifiedData).some((action) => action.enabled === true) &&\n      !hasAllActionsSelected\n    );\n  }, [currentScopedModifiedData, hasAllActionsSelected]);\n\n  const handleChangeSelectAll = useCallback(\n    ({ target: { name } }) => {\n      onChangeSelectAll({ target: { name, value: !hasAllActionsSelected } });\n    },\n    [hasAllActionsSelected, onChangeSelectAll]\n  );\n\n  const isActionSelected = useCallback(\n    (actionName) => {\n      return selectedAction === actionName;\n    },\n    [selectedAction]\n  );\n\n  return (\n    <Box>\n      <Flex justifyContent=\"space-between\" alignItems=\"center\">\n        <Box paddingRight={4}>\n          <Typography variant=\"sigma\" textColor=\"neutral600\">\n            {subCategory.label}\n          </Typography>\n        </Box>\n        <Border />\n        <Box paddingLeft={4}>\n          <Checkbox\n            name={subCategory.name}\n            checked={hasSomeActionsSelected ? 'indeterminate' : hasAllActionsSelected}\n            onCheckedChange={(value) =>\n              handleChangeSelectAll({ target: { name: subCategory.name, value } })\n            }\n          >\n            {formatMessage({ id: 'app.utils.select-all', defaultMessage: 'Select all' })}\n          </Checkbox>\n        </Box>\n      </Flex>\n      <Flex paddingTop={6} paddingBottom={6}>\n        <Grid.Root gap={2} style={{ flex: 1 }}>\n          {subCategory.actions.map((action) => {\n            const name = `${action.name}.enabled`;\n\n            return (\n              <Grid.Item col={6} key={action.name} direction=\"column\" alignItems=\"stretch\">\n                <CheckboxWrapper isActive={isActionSelected(action.name)} padding={2} hasRadius>\n                  <Checkbox\n                    checked={get(modifiedData, name, false)}\n                    name={name}\n                    onCheckedChange={(value) => onChange({ target: { name, value } })}\n                  >\n                    {action.label}\n                  </Checkbox>\n                  <button\n                    type=\"button\"\n                    onClick={() => onSelectedAction(action.name)}\n                    style={{ display: 'inline-flex', alignItems: 'center' }}\n                  >\n                    <VisuallyHidden tag=\"span\">\n                      {formatMessage(\n                        {\n                          id: 'app.utils.show-bound-route',\n                          defaultMessage: 'Show bound route for {route}',\n                        },\n                        {\n                          route: action.name,\n                        }\n                      )}\n                    </VisuallyHidden>\n                    <Cog id=\"cog\" />\n                  </button>\n                </CheckboxWrapper>\n              </Grid.Item>\n            );\n          })}\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n\nSubCategory.propTypes = {\n  subCategory: PropTypes.object.isRequired,\n};\n\nexport default SubCategory;\n", "import React, { useMemo } from 'react';\n\nimport { Box } from '@strapi/design-system';\nimport sortBy from 'lodash/sortBy';\nimport PropTypes from 'prop-types';\n\nimport SubCategory from './SubCategory';\n\nconst PermissionRow = ({ name, permissions }) => {\n  const subCategories = useMemo(() => {\n    return sortBy(\n      Object.values(permissions.controllers).reduce((acc, curr, index) => {\n        const currentName = `${name}.controllers.${Object.keys(permissions.controllers)[index]}`;\n        const actions = sortBy(\n          Object.keys(curr).reduce((acc, current) => {\n            return [\n              ...acc,\n              {\n                ...curr[current],\n                label: current,\n                name: `${currentName}.${current}`,\n              },\n            ];\n          }, []),\n          'label'\n        );\n\n        return [\n          ...acc,\n          {\n            actions,\n            label: Object.keys(permissions.controllers)[index],\n            name: currentName,\n          },\n        ];\n      }, []),\n      'label'\n    );\n  }, [name, permissions]);\n\n  return (\n    <Box padding={6}>\n      {subCategories.map((subCategory) => (\n        <SubCategory key={subCategory.name} subCategory={subCategory} />\n      ))}\n    </Box>\n  );\n};\n\nPermissionRow.propTypes = {\n  name: PropTypes.string.isRequired,\n  permissions: PropTypes.object.isRequired,\n};\n\nexport default PermissionRow;\n", "import { produce } from 'immer';\n\nconst initialState = {\n  collapses: [],\n};\n\nconst reducer = (state, action) =>\n  // eslint-disable-next-line consistent-return\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'TOGGLE_COLLAPSE': {\n        draftState.collapses = state.collapses.map((collapse, index) => {\n          if (index === action.index) {\n            return { ...collapse, isOpen: !collapse.isOpen };\n          }\n\n          return { ...collapse, isOpen: false };\n        });\n\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n\nexport { initialState, reducer };\n", "import React, { useReducer } from 'react';\n\nimport { Accordion, Flex } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useUsersPermissions } from '../../contexts/UsersPermissionsContext';\nimport formatPluginName from '../../utils/formatPluginName';\n\nimport init from './init';\nimport PermissionRow from './PermissionRow';\nimport { initialState, reducer } from './reducer';\n\nconst Permissions = () => {\n  const { modifiedData } = useUsersPermissions();\n  const { formatMessage } = useIntl();\n  const [{ collapses }] = useReducer(reducer, initialState, (state) => init(state, modifiedData));\n\n  return (\n    <Accordion.Root size=\"M\">\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n        {collapses.map((collapse, index) => (\n          <Accordion.Item key={collapse.name} value={collapse.name}>\n            <Accordion.Header variant={index % 2 === 0 ? 'secondary' : undefined}>\n              <Accordion.Trigger\n                caretPosition=\"right\"\n                description={formatMessage(\n                  {\n                    id: 'users-permissions.Plugin.permissions.plugins.description',\n                    defaultMessage: 'Define all allowed actions for the {name} plugin.',\n                  },\n                  { name: collapse.name }\n                )}\n              >\n                {formatPluginName(collapse.name)}\n              </Accordion.Trigger>\n            </Accordion.Header>\n            <Accordion.Content>\n              <PermissionRow permissions={modifiedData[collapse.name]} name={collapse.name} />\n            </Accordion.Content>\n          </Accordion.Item>\n        ))}\n      </Flex>\n    </Accordion.Root>\n  );\n};\n\nexport default Permissions;\n", "const getMethodColor = (verb) => {\n  switch (verb) {\n    case 'POST': {\n      return {\n        text: 'success600',\n        border: 'success200',\n        background: 'success100',\n      };\n    }\n    case 'GET': {\n      return {\n        text: 'secondary600',\n        border: 'secondary200',\n        background: 'secondary100',\n      };\n    }\n    case 'PUT': {\n      return {\n        text: 'warning600',\n        border: 'warning200',\n        background: 'warning100',\n      };\n    }\n    case 'DELETE': {\n      return {\n        text: 'danger600',\n        border: 'danger200',\n        background: 'danger100',\n      };\n    }\n    default: {\n      return {\n        text: 'neutral600',\n        border: 'neutral200',\n        background: 'neutral100',\n      };\n    }\n  }\n};\n\nexport default getMethodColor;\n", "import React from 'react';\n\nimport { Box, Flex, Typography } from '@strapi/design-system';\nimport map from 'lodash/map';\nimport tail from 'lodash/tail';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport getMethodColor from './getMethodColor';\n\nconst MethodBox = styled(Box)`\n  margin: -1px;\n  border-radius: ${({ theme }) => theme.spaces[1]} 0 0 ${({ theme }) => theme.spaces[1]};\n`;\n\nfunction BoundRoute({ route }) {\n  const { formatMessage } = useIntl();\n\n  const { method, handler: title, path } = route;\n  const formattedRoute = path ? tail(path.split('/')) : [];\n  const [controller = '', action = ''] = title ? title.split('.') : [];\n  const colors = getMethodColor(route.method);\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n      <Typography variant=\"delta\" tag=\"h3\">\n        {formatMessage({\n          id: 'users-permissions.BoundRoute.title',\n          defaultMessage: 'Bound route to',\n        })}\n        &nbsp;\n        <span>{controller}</span>\n        <Typography variant=\"delta\" textColor=\"primary600\">\n          .{action}\n        </Typography>\n      </Typography>\n      <Flex hasRadius background=\"neutral0\" borderColor=\"neutral200\" gap={0}>\n        <MethodBox background={colors.background} borderColor={colors.border} padding={2}>\n          <Typography fontWeight=\"bold\" textColor={colors.text}>\n            {method}\n          </Typography>\n        </MethodBox>\n        <Box paddingLeft={2} paddingRight={2}>\n          {map(formattedRoute, (value) => (\n            <Typography key={value} textColor={value.includes(':') ? 'neutral600' : 'neutral900'}>\n              /{value}\n            </Typography>\n          ))}\n        </Box>\n      </Flex>\n    </Flex>\n  );\n}\n\nBoundRoute.defaultProps = {\n  route: {\n    handler: 'Nocontroller.error',\n    method: 'GET',\n    path: '/there-is-no-path',\n  },\n};\n\nBoundRoute.propTypes = {\n  route: PropTypes.shape({\n    handler: PropTypes.string,\n    method: PropTypes.string,\n    path: PropTypes.string,\n  }),\n};\n\nexport default BoundRoute;\n", "import React from 'react';\n\nimport { Flex, Grid, Typography } from '@strapi/design-system';\nimport get from 'lodash/get';\nimport isEmpty from 'lodash/isEmpty';\nimport without from 'lodash/without';\nimport { useIntl } from 'react-intl';\n\nimport { useUsersPermissions } from '../../contexts/UsersPermissionsContext';\nimport BoundRoute from '../BoundRoute';\n\nconst Policies = () => {\n  const { formatMessage } = useIntl();\n  const { selectedAction, routes } = useUsersPermissions();\n\n  const path = without(selectedAction.split('.'), 'controllers');\n  const controllerRoutes = get(routes, path[0]);\n  const pathResolved = path.slice(1).join('.');\n\n  const displayedRoutes = isEmpty(controllerRoutes)\n    ? []\n    : controllerRoutes.filter((o) => o.handler.endsWith(pathResolved));\n\n  return (\n    <Grid.Item\n      col={5}\n      background=\"neutral150\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n      style={{ minHeight: '100%' }}\n      direction=\"column\"\n      alignItems=\"stretch\"\n    >\n      {selectedAction ? (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          {displayedRoutes.map((route, key) => (\n            // eslint-disable-next-line react/no-array-index-key\n            <BoundRoute key={key} route={route} />\n          ))}\n        </Flex>\n      ) : (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          <Typography variant=\"delta\" tag=\"h3\">\n            {formatMessage({\n              id: 'users-permissions.Policies.header.title',\n              defaultMessage: 'Advanced settings',\n            })}\n          </Typography>\n          <Typography tag=\"p\" textColor=\"neutral600\">\n            {formatMessage({\n              id: 'users-permissions.Policies.header.hint',\n              defaultMessage:\n                \"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route\",\n            })}\n          </Typography>\n        </Flex>\n      )}\n    </Grid.Item>\n  );\n};\n\nexport default Policies;\n", "const init = (state, permissions, routes) => {\n  return {\n    ...state,\n    initialData: permissions,\n    modifiedData: permissions,\n    routes,\n  };\n};\n\nexport default init;\n", "/* eslint-disable consistent-return */\nimport { produce } from 'immer';\nimport get from 'lodash/get';\nimport set from 'lodash/set';\nimport take from 'lodash/take';\n\nexport const initialState = {\n  initialData: {},\n  modifiedData: {},\n  routes: {},\n  selectedAction: '',\n  policies: [],\n};\n\nconst reducer = (state, action) =>\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'ON_CHANGE': {\n        const keysLength = action.keys.length;\n        const isChangingCheckbox = action.keys[keysLength - 1] === 'enabled';\n\n        if (action.value && isChangingCheckbox) {\n          const selectedAction = take(action.keys, keysLength - 1).join('.');\n          draftState.selectedAction = selectedAction;\n        }\n\n        set(draftState, ['modifiedData', ...action.keys], action.value);\n        break;\n      }\n      case 'ON_CHANGE_SELECT_ALL': {\n        const pathToValue = ['modifiedData', ...action.keys];\n        const oldValues = get(state, pathToValue, {});\n        const updatedValues = Object.keys(oldValues).reduce((acc, current) => {\n          acc[current] = { ...oldValues[current], enabled: action.value };\n\n          return acc;\n        }, {});\n\n        set(draftState, pathToValue, updatedValues);\n\n        break;\n      }\n      case 'ON_RESET': {\n        draftState.modifiedData = state.initialData;\n        break;\n      }\n      case 'ON_SUBMIT_SUCCEEDED': {\n        draftState.initialData = state.modifiedData;\n        break;\n      }\n\n      case 'SELECT_ACTION': {\n        const { actionToSelect } = action;\n        draftState.selectedAction = actionToSelect === state.selectedAction ? '' : actionToSelect;\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n\nexport default reducer;\n", "import React, { forwardRef, memo, useImperativeHandle, useReducer } from 'react';\n\nimport { Flex, Grid, Typography } from '@strapi/design-system';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport { UsersPermissionsProvider } from '../../contexts/UsersPermissionsContext';\nimport getTrad from '../../utils/getTrad';\nimport Permissions from '../Permissions';\nimport Policies from '../Policies';\n\nimport init from './init';\nimport reducer, { initialState } from './reducer';\n\nconst UsersPermissions = forwardRef(({ permissions, routes }, ref) => {\n  const { formatMessage } = useIntl();\n  const [state, dispatch] = useReducer(reducer, initialState, (state) =>\n    init(state, permissions, routes)\n  );\n\n  useImperativeHandle(ref, () => ({\n    getPermissions() {\n      return {\n        permissions: state.modifiedData,\n      };\n    },\n    resetForm() {\n      dispatch({ type: 'ON_RESET' });\n    },\n    setFormAfterSubmit() {\n      dispatch({ type: 'ON_SUBMIT_SUCCEEDED' });\n    },\n  }));\n\n  const handleChange = ({ target: { name, value } }) =>\n    dispatch({\n      type: 'ON_CHANGE',\n      keys: name.split('.'),\n      value: value === 'empty__string_value' ? '' : value,\n    });\n\n  const handleChangeSelectAll = ({ target: { name, value } }) =>\n    dispatch({\n      type: 'ON_CHANGE_SELECT_ALL',\n      keys: name.split('.'),\n      value,\n    });\n\n  const handleSelectedAction = (actionToSelect) =>\n    dispatch({\n      type: 'SELECT_ACTION',\n      actionToSelect,\n    });\n\n  const providerValue = {\n    ...state,\n    onChange: handleChange,\n    onChangeSelectAll: handleChangeSelectAll,\n    onSelectedAction: handleSelectedAction,\n  };\n\n  return (\n    <UsersPermissionsProvider value={providerValue}>\n      <Grid.Root gap={0} shadow=\"filterShadow\" hasRadius background=\"neutral0\">\n        <Grid.Item\n          col={7}\n          paddingTop={6}\n          paddingBottom={6}\n          paddingLeft={7}\n          paddingRight={7}\n          direction=\"column\"\n          alignItems=\"stretch\"\n        >\n          <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n              <Typography variant=\"delta\" tag=\"h2\">\n                {formatMessage({\n                  id: getTrad('Plugins.header.title'),\n                  defaultMessage: 'Permissions',\n                })}\n              </Typography>\n              <Typography tag=\"p\" textColor=\"neutral600\">\n                {formatMessage({\n                  id: getTrad('Plugins.header.description'),\n                  defaultMessage: 'Only actions bound by a route are listed below.',\n                })}\n              </Typography>\n            </Flex>\n            <Permissions />\n          </Flex>\n        </Grid.Item>\n        <Policies />\n      </Grid.Root>\n    </UsersPermissionsProvider>\n  );\n});\n\nUsersPermissions.propTypes = {\n  permissions: PropTypes.object.isRequired,\n  routes: PropTypes.object.isRequired,\n};\n\nexport default memo(UsersPermissions);\n", "import { translatedErrors } from '@strapi/strapi/admin';\nimport * as yup from 'yup';\n\nexport const createRoleSchema = yup.object().shape({\n  name: yup.string().required(translatedErrors.required.id),\n  description: yup.string().required(translatedErrors.required.id),\n});\n", "import isEmpty from 'lodash/isEmpty';\n\nconst cleanPermissions = (permissions) =>\n  Object.keys(permissions).reduce((acc, current) => {\n    const currentPermission = permissions[current].controllers;\n    const cleanedControllers = Object.keys(currentPermission).reduce((acc2, curr) => {\n      if (isEmpty(currentPermission[curr])) {\n        return acc2;\n      }\n\n      acc2[curr] = currentPermission[curr];\n\n      return acc2;\n    }, {});\n\n    if (isEmpty(cleanedControllers)) {\n      return acc;\n    }\n\n    acc[current] = { controllers: cleanedControllers };\n\n    return acc;\n  }, {});\n\nexport default cleanPermissions;\n", "import { useEffect } from 'react';\n\nimport { useAPIErrorHandler, useNotification, useFetchClient } from '@strapi/strapi/admin';\nimport { useQueries } from 'react-query';\n\nimport { cleanPermissions, getTrad } from '../../../utils';\n\nexport const usePlugins = () => {\n  const { toggleNotification } = useNotification();\n  const { get } = useFetchClient();\n  const { formatAPIError } = useAPIErrorHandler(getTrad);\n\n  const [\n    {\n      data: permissions,\n      isLoading: isLoadingPermissions,\n      error: permissionsError,\n      refetch: refetchPermissions,\n    },\n    { data: routes, isLoading: isLoadingRoutes, error: routesError, refetch: refetchRoutes },\n  ] = useQueries([\n    {\n      queryKey: ['users-permissions', 'permissions'],\n      async queryFn() {\n        const {\n          data: { permissions },\n        } = await get(`/users-permissions/permissions`);\n\n        return permissions;\n      },\n    },\n    {\n      queryKey: ['users-permissions', 'routes'],\n      async queryFn() {\n        const {\n          data: { routes },\n        } = await get(`/users-permissions/routes`);\n\n        return routes;\n      },\n    },\n  ]);\n\n  const refetchQueries = async () => {\n    await Promise.all([refetchPermissions(), refetchRoutes()]);\n  };\n\n  useEffect(() => {\n    if (permissionsError) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(permissionsError),\n      });\n    }\n  }, [toggleNotification, permissionsError, formatAPIError]);\n\n  useEffect(() => {\n    if (routesError) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(routesError),\n      });\n    }\n  }, [toggleNotification, routesError, formatAPIError]);\n\n  const isLoading = isLoadingPermissions || isLoadingRoutes;\n\n  return {\n    // TODO: these return values need to be memoized, otherwise\n    // they will create infinite rendering loops when used as\n    // effect dependencies\n    permissions: permissions ? cleanPermissions(permissions) : {},\n    routes: routes ?? {},\n\n    getData: refetchQueries,\n    isLoading,\n  };\n};\n", "import * as React from 'react';\n\nimport {\n  Button,\n  Flex,\n  Grid,\n  Main,\n  Textarea,\n  TextInput,\n  Typography,\n  Field,\n} from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { Page, useTracking, useNotification, useFetchClient, Layouts } from '@strapi/strapi/admin';\nimport { Formik, Form } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { useMutation } from 'react-query';\nimport { useNavigate } from 'react-router-dom';\n\nimport UsersPermissions from '../../../components/UsersPermissions';\nimport { PERMISSIONS } from '../../../constants';\nimport getTrad from '../../../utils/getTrad';\nimport { createRoleSchema } from '../constants';\nimport { usePlugins } from '../hooks/usePlugins';\n\nexport const CreatePage = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const navigate = useNavigate();\n  const { isLoading: isLoadingPlugins, permissions, routes } = usePlugins();\n  const { trackUsage } = useTracking();\n  const permissionsRef = React.useRef();\n  const { post } = useFetchClient();\n  const mutation = useMutation((body) => post(`/users-permissions/roles`, body), {\n    onError() {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    },\n\n    onSuccess() {\n      trackUsage('didCreateRole');\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTrad('Settings.roles.created'),\n          defaultMessage: 'Role created',\n        }),\n      });\n\n      // Forcing redirecting since we don't have the id in the response\n      navigate(-1);\n    },\n  });\n\n  const handleCreateRoleSubmit = async (data) => {\n    // TODO: refactor. Child -> parent component communication is evil;\n    // We should either move the provider one level up or move the state\n    // straight into redux.\n    const permissions = permissionsRef.current.getPermissions();\n\n    await mutation.mutate({ ...data, ...permissions, users: [] });\n  };\n\n  return (\n    <Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          { name: 'Roles' }\n        )}\n      </Page.Title>\n      <Formik\n        enableReinitialize\n        initialValues={{ name: '', description: '' }}\n        onSubmit={handleCreateRoleSubmit}\n        validationSchema={createRoleSchema}\n      >\n        {({ handleSubmit, values, handleChange, errors }) => (\n          <Form noValidate onSubmit={handleSubmit}>\n            <Layouts.Header\n              primaryAction={\n                !isLoadingPlugins && (\n                  <Button type=\"submit\" loading={mutation.isLoading} startIcon={<Check />}>\n                    {formatMessage({\n                      id: 'global.save',\n                      defaultMessage: 'Save',\n                    })}\n                  </Button>\n                )\n              }\n              title={formatMessage({\n                id: 'Settings.roles.create.title',\n                defaultMessage: 'Create a role',\n              })}\n              subtitle={formatMessage({\n                id: 'Settings.roles.create.description',\n                defaultMessage: 'Define the rights given to the role',\n              })}\n            />\n            <Layouts.Content>\n              <Flex\n                background=\"neutral0\"\n                direction=\"column\"\n                alignItems=\"stretch\"\n                gap={7}\n                hasRadius\n                paddingTop={6}\n                paddingBottom={6}\n                paddingLeft={7}\n                paddingRight={7}\n                shadow=\"filterShadow\"\n              >\n                <Flex direction=\"column\" alignItems=\"stretch\">\n                  <Typography variant=\"delta\" tag=\"h2\">\n                    {formatMessage({\n                      id: getTrad('EditPage.form.roles'),\n                      defaultMessage: 'Role details',\n                    })}\n                  </Typography>\n\n                  <Grid.Root gap={4}>\n                    <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        name=\"name\"\n                        error={\n                          errors?.name\n                            ? formatMessage({ id: errors.name, defaultMessage: 'Name is required' })\n                            : false\n                        }\n                        required\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'global.name',\n                            defaultMessage: 'Name',\n                          })}\n                        </Field.Label>\n                        <TextInput value={values.name || ''} onChange={handleChange} />\n                        <Field.Error />\n                      </Field.Root>\n                    </Grid.Item>\n                    <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        name=\"description\"\n                        error={\n                          errors?.description\n                            ? formatMessage({\n                                id: errors.description,\n                                defaultMessage: 'Description is required',\n                              })\n                            : false\n                        }\n                        required\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'global.description',\n                            defaultMessage: 'Description',\n                          })}\n                        </Field.Label>\n                        <Textarea value={values.description || ''} onChange={handleChange} />\n                        <Field.Error />\n                      </Field.Root>\n                    </Grid.Item>\n                  </Grid.Root>\n                </Flex>\n\n                {!isLoadingPlugins && (\n                  <UsersPermissions\n                    ref={permissionsRef}\n                    permissions={permissions}\n                    routes={routes}\n                  />\n                )}\n              </Flex>\n            </Layouts.Content>\n          </Form>\n        )}\n      </Formik>\n    </Main>\n  );\n};\n\nexport const ProtectedRolesCreatePage = () => (\n  <Page.Protect permissions={PERMISSIONS.createRole}>\n    <CreatePage />\n  </Page.Protect>\n);\n", "import * as React from 'react';\n\nimport {\n  Main,\n  Button,\n  Flex,\n  TextInput,\n  Textarea,\n  Typography,\n  Grid,\n  Field,\n} from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport {\n  <PERSON>,\n  BackButton,\n  useAPIError<PERSON>and<PERSON>,\n  useNotification,\n  useFetchClient,\n  Layouts,\n} from '@strapi/strapi/admin';\nimport { Formik, Form } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { useQuery, useMutation } from 'react-query';\nimport { useMatch } from 'react-router-dom';\n\nimport UsersPermissions from '../../../components/UsersPermissions';\nimport { PERMISSIONS } from '../../../constants';\nimport getTrad from '../../../utils/getTrad';\nimport { createRoleSchema } from '../constants';\nimport { usePlugins } from '../hooks/usePlugins';\n\nexport const EditPage = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const {\n    params: { id },\n  } = useMatch(`/settings/users-permissions/roles/:id`);\n  const { get } = useFetchClient();\n  const { isLoading: isLoadingPlugins, routes } = usePlugins();\n  const {\n    data: role,\n    isLoading: isLoadingRole,\n    refetch: refetchRole,\n  } = useQuery(['users-permissions', 'role', id], async () => {\n    // TODO: why doesn't this endpoint follow the admin API conventions?\n    const {\n      data: { role },\n    } = await get(`/users-permissions/roles/${id}`);\n\n    return role;\n  });\n\n  const permissionsRef = React.useRef();\n  const { put } = useFetchClient();\n  const { formatAPIError } = useAPIErrorHandler();\n  const mutation = useMutation((body) => put(`/users-permissions/roles/${id}`, body), {\n    onError(error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    },\n\n    async onSuccess() {\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTrad('Settings.roles.created'),\n          defaultMessage: 'Role edited',\n        }),\n      });\n\n      await refetchRole();\n    },\n  });\n\n  const handleEditRoleSubmit = async (data) => {\n    const permissions = permissionsRef.current.getPermissions();\n\n    await mutation.mutate({ ...data, ...permissions, users: [] });\n  };\n\n  if (isLoadingRole) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          { name: 'Roles' }\n        )}\n      </Page.Title>\n      <Formik\n        enableReinitialize\n        initialValues={{ name: role.name, description: role.description }}\n        onSubmit={handleEditRoleSubmit}\n        validationSchema={createRoleSchema}\n      >\n        {({ handleSubmit, values, handleChange, errors }) => (\n          <Form noValidate onSubmit={handleSubmit}>\n            <Layouts.Header\n              primaryAction={\n                !isLoadingPlugins ? (\n                  <Button\n                    disabled={role.code === 'strapi-super-admin'}\n                    type=\"submit\"\n                    loading={mutation.isLoading}\n                    startIcon={<Check />}\n                  >\n                    {formatMessage({\n                      id: 'global.save',\n                      defaultMessage: 'Save',\n                    })}\n                  </Button>\n                ) : null\n              }\n              title={role.name}\n              subtitle={role.description}\n              navigationAction={<BackButton />}\n            />\n            <Layouts.Content>\n              <Flex\n                background=\"neutral0\"\n                direction=\"column\"\n                alignItems=\"stretch\"\n                gap={7}\n                hasRadius\n                paddingTop={6}\n                paddingBottom={6}\n                paddingLeft={7}\n                paddingRight={7}\n                shadow=\"filterShadow\"\n              >\n                <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                  <Typography variant=\"delta\" tag=\"h2\">\n                    {formatMessage({\n                      id: getTrad('EditPage.form.roles'),\n                      defaultMessage: 'Role details',\n                    })}\n                  </Typography>\n\n                  <Grid.Root gap={4}>\n                    <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        name=\"name\"\n                        error={\n                          errors?.name\n                            ? formatMessage({\n                                id: errors.name,\n                                defaultMessage: 'Name is required',\n                              })\n                            : false\n                        }\n                        required\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'global.name',\n                            defaultMessage: 'Name',\n                          })}\n                        </Field.Label>\n                        <TextInput value={values.name || ''} onChange={handleChange} />\n                        <Field.Error />\n                      </Field.Root>\n                    </Grid.Item>\n                    <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n                      <Field.Root\n                        name=\"description\"\n                        error={\n                          errors?.description\n                            ? formatMessage({\n                                id: errors.description,\n                                defaultMessage: 'Description is required',\n                              })\n                            : false\n                        }\n                        required\n                      >\n                        <Field.Label>\n                          {formatMessage({\n                            id: 'global.description',\n                            defaultMessage: 'Description',\n                          })}\n                        </Field.Label>\n                        <Textarea value={values.description || ''} onChange={handleChange} />\n                        <Field.Error />\n                      </Field.Root>\n                    </Grid.Item>\n                  </Grid.Root>\n                </Flex>\n\n                {!isLoadingPlugins && (\n                  <UsersPermissions\n                    ref={permissionsRef}\n                    permissions={role.permissions}\n                    routes={routes}\n                  />\n                )}\n              </Flex>\n            </Layouts.Content>\n          </Form>\n        )}\n      </Formik>\n    </Main>\n  );\n};\n\nexport const ProtectedRolesEditPage = () => (\n  <Page.Protect permissions={PERMISSIONS.updateRole}>\n    <EditPage />\n  </Page.Protect>\n);\n", "import React from 'react';\n\nimport { Flex, I<PERSON><PERSON><PERSON>on, <PERSON>, Tbody, Td, Tr, Typography } from '@strapi/design-system';\nimport { Pencil, Trash } from '@strapi/icons';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\nimport { useNavigate, NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nconst EditLink = styled(Link)`\n  align-items: center;\n  height: 3.2rem;\n  width: 3.2rem;\n  display: flex;\n  justify-content: center;\n  padding: ${({ theme }) => `${theme.spaces[2]}`};\n\n  svg {\n    height: 1.6rem;\n    width: 1.6rem;\n\n    path {\n      fill: ${({ theme }) => theme.colors.neutral500};\n    }\n  }\n\n  &:hover,\n  &:focus {\n    svg {\n      path {\n        fill: ${({ theme }) => theme.colors.neutral800};\n      }\n    }\n  }\n`;\n\nconst TableBody = ({ sortedRoles, canDelete, canUpdate, setRoleToDelete, onDelete }) => {\n  const { formatMessage } = useIntl();\n  const navigate = useNavigate();\n  const [showConfirmDelete, setShowConfirmDelete] = onDelete;\n\n  const checkCanDeleteRole = (role) =>\n    canDelete && !['public', 'authenticated'].includes(role.type);\n\n  const handleClickDelete = (id) => {\n    setRoleToDelete(id);\n    setShowConfirmDelete(!showConfirmDelete);\n  };\n\n  return (\n    <Tbody>\n      {sortedRoles?.map((role) => (\n        <Tr key={role.name} onClick={() => navigate(role.id.toString())}>\n          <Td width=\"20%\">\n            <Typography>{role.name}</Typography>\n          </Td>\n          <Td width=\"50%\">\n            <Typography>{role.description}</Typography>\n          </Td>\n          <Td width=\"30%\">\n            <Typography>\n              {formatMessage(\n                {\n                  id: 'Roles.RoleRow.user-count',\n                  defaultMessage: '{number, plural, =0 {# user} one {# user} other {# users}}',\n                },\n                { number: role.nb_users }\n              )}\n            </Typography>\n          </Td>\n          <Td>\n            <Flex justifyContent=\"end\" onClick={(e) => e.stopPropagation()}>\n              {canUpdate ? (\n                <EditLink\n                  tag={NavLink}\n                  to={role.id.toString()}\n                  aria-label={formatMessage(\n                    { id: 'app.component.table.edit', defaultMessage: 'Edit {target}' },\n                    { target: `${role.name}` }\n                  )}\n                >\n                  <Pencil />\n                </EditLink>\n              ) : null}\n\n              {checkCanDeleteRole(role) && (\n                <IconButton\n                  onClick={() => handleClickDelete(role.id.toString())}\n                  variant=\"ghost\"\n                  label={formatMessage(\n                    { id: 'global.delete-target', defaultMessage: 'Delete {target}' },\n                    { target: `${role.name}` }\n                  )}\n                >\n                  <Trash />\n                </IconButton>\n              )}\n            </Flex>\n          </Td>\n        </Tr>\n      ))}\n    </Tbody>\n  );\n};\n\nexport default TableBody;\n\nTableBody.defaultProps = {\n  canDelete: false,\n  canUpdate: false,\n};\n\nTableBody.propTypes = {\n  onDelete: PropTypes.array.isRequired,\n  setRoleToDelete: PropTypes.func.isRequired,\n  sortedRoles: PropTypes.array.isRequired,\n  canDelete: PropTypes.bool,\n  canUpdate: PropTypes.bool,\n};\n", "import React, { useState } from 'react';\n\nimport {\n  Table,\n  Th,\n  Thead,\n  Tr,\n  Typography,\n  useNotifyAT,\n  VisuallyHidden,\n  EmptyStateLayout,\n  useCollator,\n  useFilter,\n  LinkButton,\n  Dialog,\n} from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport {\n  ConfirmDialog,\n  useTracking,\n  Page,\n  SearchInput,\n  useNotification,\n  useQueryParams,\n  useFetchClient,\n  useRBAC,\n  Layouts,\n} from '@strapi/strapi/admin';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQuery } from 'react-query';\nimport { NavLink } from 'react-router-dom';\n\nimport { PERMISSIONS } from '../../../../constants';\nimport { getTrad } from '../../../../utils';\n\nimport TableBody from './components/TableBody';\n\nexport const RolesListPage = () => {\n  const { trackUsage } = useTracking();\n  const { formatMessage, locale } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { notifyStatus } = useNotifyAT();\n  const [{ query }] = useQueryParams();\n  const _q = query?._q || '';\n  const [showConfirmDelete, setShowConfirmDelete] = useState(false);\n  const [roleToDelete, setRoleToDelete] = useState();\n  const { del, get } = useFetchClient();\n\n  const {\n    isLoading: isLoadingForPermissions,\n    allowedActions: { canRead, canDelete, canCreate, canUpdate },\n  } = useRBAC({\n    create: PERMISSIONS.createRole,\n    read: PERMISSIONS.readRoles,\n    update: PERMISSIONS.updateRole,\n    delete: PERMISSIONS.deleteRole,\n  });\n\n  const {\n    isLoading: isLoadingForData,\n    data: { roles },\n    isFetching,\n    refetch,\n  } = useQuery('get-roles', () => fetchData(toggleNotification, formatMessage, notifyStatus), {\n    initialData: {},\n    enabled: canRead,\n  });\n\n  const { contains } = useFilter(locale, {\n    sensitivity: 'base',\n  });\n\n  /**\n   * @type {Intl.Collator}\n   */\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const isLoading = isLoadingForData || isFetching || isLoadingForPermissions;\n\n  const handleShowConfirmDelete = () => {\n    setShowConfirmDelete(!showConfirmDelete);\n  };\n\n  const deleteData = async (id, formatMessage, toggleNotification) => {\n    try {\n      await del(`/users-permissions/roles/${id}`);\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occured' }),\n      });\n    }\n  };\n\n  const fetchData = async (toggleNotification, formatMessage, notifyStatus) => {\n    try {\n      const { data } = await get('/users-permissions/roles');\n      notifyStatus('The roles have loaded successfully');\n\n      return data;\n    } catch (err) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n\n      throw new Error(err);\n    }\n  };\n\n  const emptyLayout = {\n    roles: {\n      id: getTrad('Roles.empty'),\n      defaultMessage: \"You don't have any roles yet.\",\n    },\n    search: {\n      id: getTrad('Roles.empty.search'),\n      defaultMessage: 'No roles match the search.',\n    },\n  };\n\n  const pageTitle = formatMessage({\n    id: 'global.roles',\n    defaultMessage: 'Roles',\n  });\n\n  const deleteMutation = useMutation((id) => deleteData(id, formatMessage, toggleNotification), {\n    async onSuccess() {\n      await refetch();\n    },\n  });\n\n  const handleConfirmDelete = async () => {\n    await deleteMutation.mutateAsync(roleToDelete);\n    setShowConfirmDelete(!showConfirmDelete);\n  };\n\n  const sortedRoles = (roles || [])\n    .filter((role) => contains(role.name, _q) || contains(role.description, _q))\n    .sort(\n      (a, b) => formatter.compare(a.name, b.name) || formatter.compare(a.description, b.description)\n    );\n\n  const emptyContent = _q && !sortedRoles.length ? 'search' : 'roles';\n\n  const colCount = 4;\n  const rowCount = (roles?.length || 0) + 1;\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          { name: pageTitle }\n        )}\n      </Page.Title>\n      <Page.Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: 'global.roles',\n            defaultMessage: 'Roles',\n          })}\n          subtitle={formatMessage({\n            id: 'Settings.roles.list.description',\n            defaultMessage: 'List of roles',\n          })}\n          primaryAction={\n            canCreate ? (\n              <LinkButton\n                to=\"new\"\n                tag={NavLink}\n                onClick={() => trackUsage('willCreateRole')}\n                startIcon={<Plus />}\n                size=\"S\"\n              >\n                {formatMessage({\n                  id: getTrad('List.button.roles'),\n                  defaultMessage: 'Add new role',\n                })}\n              </LinkButton>\n            ) : null\n          }\n        />\n\n        <Layouts.Action\n          startActions={\n            <SearchInput\n              label={formatMessage({\n                id: 'app.component.search.label',\n                defaultMessage: 'Search',\n              })}\n            />\n          }\n        />\n\n        <Layouts.Content>\n          {!canRead && <Page.NoPermissions />}\n          {canRead && sortedRoles && sortedRoles?.length ? (\n            <Table colCount={colCount} rowCount={rowCount}>\n              <Thead>\n                <Tr>\n                  <Th>\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({ id: 'global.name', defaultMessage: 'Name' })}\n                    </Typography>\n                  </Th>\n                  <Th>\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'global.description',\n                        defaultMessage: 'Description',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th>\n                    <Typography variant=\"sigma\" textColor=\"neutral600\">\n                      {formatMessage({\n                        id: 'global.users',\n                        defaultMessage: 'Users',\n                      })}\n                    </Typography>\n                  </Th>\n                  <Th>\n                    <VisuallyHidden>\n                      {formatMessage({\n                        id: 'global.actions',\n                        defaultMessage: 'Actions',\n                      })}\n                    </VisuallyHidden>\n                  </Th>\n                </Tr>\n              </Thead>\n              <TableBody\n                sortedRoles={sortedRoles}\n                canDelete={canDelete}\n                canUpdate={canUpdate}\n                permissions={PERMISSIONS}\n                setRoleToDelete={setRoleToDelete}\n                onDelete={[showConfirmDelete, setShowConfirmDelete]}\n              />\n            </Table>\n          ) : (\n            <EmptyStateLayout content={formatMessage(emptyLayout[emptyContent])} />\n          )}\n        </Layouts.Content>\n        <Dialog.Root open={showConfirmDelete} onOpenChange={handleShowConfirmDelete}>\n          <ConfirmDialog onConfirm={handleConfirmDelete} />\n        </Dialog.Root>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\nexport const ProtectedRolesListPage = () => {\n  return (\n    <Page.Protect permissions={PERMISSIONS.accessRoles}>\n      <RolesListPage />\n    </Page.Protect>\n  );\n};\n", "import React from 'react';\n\nimport { Page } from '@strapi/strapi/admin';\nimport { Route, Routes } from 'react-router-dom';\n\nimport { PERMISSIONS } from '../../constants';\n\nimport { ProtectedRolesCreatePage } from './pages/CreatePage';\nimport { ProtectedRolesEditPage } from './pages/EditPage';\nimport { ProtectedRolesListPage } from './pages/ListPage';\n\nconst Roles = () => {\n  return (\n    <Page.Protect permissions={PERMISSIONS.accessRoles}>\n      <Routes>\n        <Route index element={<ProtectedRolesListPage />} />\n        <Route path=\"new\" element={<ProtectedRolesCreatePage />} />\n        <Route path=\":id\" element={<ProtectedRolesEditPage />} />\n      </Routes>\n    </Page.Protect>\n  );\n};\n\nexport default Roles;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,gBAAgB;AADpB,QAEI,oBAAoB;AAFxB,QAGI,WAAW;AAHf,QAII,YAAY;AAJhB,QAKI,WAAW;AAGf,QAAI,mBAAmB;AAavB,aAAS,eAAe,OAAO,QAAQ,UAAU,YAAY;AAC3D,UAAI,QAAQ,IACR,WAAW,eACX,WAAW,MACX,SAAS,MAAM,QACf,SAAS,CAAC,GACV,eAAe,OAAO;AAE1B,UAAI,CAAC,QAAQ;AACX,eAAO;AAAA,MACT;AACA,UAAI,UAAU;AACZ,iBAAS,SAAS,QAAQ,UAAU,QAAQ,CAAC;AAAA,MAC/C;AACA,UAAI,YAAY;AACd,mBAAW;AACX,mBAAW;AAAA,MACb,WACS,OAAO,UAAU,kBAAkB;AAC1C,mBAAW;AACX,mBAAW;AACX,iBAAS,IAAI,SAAS,MAAM;AAAA,MAC9B;AACA;AACA,eAAO,EAAE,QAAQ,QAAQ;AACvB,cAAI,QAAQ,MAAM,KAAK,GACnB,WAAW,YAAY,OAAO,QAAQ,SAAS,KAAK;AAExD,kBAAS,cAAc,UAAU,IAAK,QAAQ;AAC9C,cAAI,YAAY,aAAa,UAAU;AACrC,gBAAI,cAAc;AAClB,mBAAO,eAAe;AACpB,kBAAI,OAAO,WAAW,MAAM,UAAU;AACpC,yBAAS;AAAA,cACX;AAAA,YACF;AACA,mBAAO,KAAK,KAAK;AAAA,UACnB,WACS,CAAC,SAAS,QAAQ,UAAU,UAAU,GAAG;AAChD,mBAAO,KAAK,KAAK;AAAA,UACnB;AAAA,QACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClEjB;AAAA;AAAA,QAAI,iBAAiB;AAArB,QACI,WAAW;AADf,QAEI,oBAAoB;AAsBxB,QAAIA,WAAU,SAAS,SAAS,OAAO,QAAQ;AAC7C,aAAO,kBAAkB,KAAK,IAC1B,eAAe,OAAO,MAAM,IAC5B,CAAC;AAAA,IACP,CAAC;AAED,WAAO,UAAUA;AAAA;AAAA;;;AC9BjB;AAAA;AAAA,QAAI,YAAY;AAAhB,QACI,YAAY;AA2BhB,aAASC,MAAK,OAAO,GAAG,OAAO;AAC7B,UAAI,EAAE,SAAS,MAAM,SAAS;AAC5B,eAAO,CAAC;AAAA,MACV;AACA,UAAK,SAAS,MAAM,SAAa,IAAI,UAAU,CAAC;AAChD,aAAO,UAAU,OAAO,GAAG,IAAI,IAAI,IAAI,CAAC;AAAA,IAC1C;AAEA,WAAO,UAAUA;AAAA;AAAA;;;;;;;;;;;;;;;;AChCjB,IAAMC,yBAAmB,4BAAc,CAAA,CAAE;AAEzC,IAAM,2BAA2B,CAAC,EAAE,UAAU,MAAA,MAAY;AACxD,aAAQ,wBAAAA,mBAAiB,UAAjB,EAA0B,OAAe,SAAS,CAAA;AAC5D;AAEA,IAAM,sBAAsB,UAAM,yBAAWA,kBAAgB;AAE7D,yBAAyB,YAAY;EACnC,UAAU,kBAAAC,QAAU,KAAK;EACzB,OAAO,kBAAAA,QAAU,OAAO;AAC1B;ACbA,SAAS,iBAAiB,YAAY;AACpC,UAAQ,YAAU;IAChB,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT,KAAK;AACH,aAAO;IACT;AACE,iBAAO,kBAAAC,SAAW,WAAW,QAAQ,SAAS,EAAE,EAAE,QAAQ,YAAY,EAAE,CAAC;EAC5E;AACH;ACvBA,IAAMC,SAAO,CAACC,eAAc,gBAAgB;AAC1C,QAAM,YAAY,OAAO,KAAK,WAAW,EACtC,KAAM,EACN,IAAI,CAAC,UAAU,EAAE,MAAM,QAAQ,MAAO,EAAC;AAE1C,SAAO,EAAE,GAAGA,eAAc,UAAA;AAC5B;ACHA,IAAM,8BAA8B;gBACpB,CAAC,UAAU,MAAM,MAAM,OAAO,UAAU;;;;;;AAOxD,IAAM,kBAAkB,GAAO,GAAG;;;;;;;;cAQpB,CAAC,UAAU,MAAM,MAAM,OAAO,UAAU;;;;;IAKlD,CAAC,UAAU,MAAM,YAAY,2BAA2B;;MAEtD,2BAA2B;;;ACbjC,IAAM,SAAS,GAAO;;;0BAGI,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;AAGhE,IAAM,cAAc,CAAC,EAAE,YAAA,MAAkB;AACjC,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,UAAU,mBAAmB,kBAAkB,gBAAgB,aAAA,IACrE,oBAAA;AAEI,QAAA,gCAA4B,sBAAQ,MAAM;AAC9C,eAAO,WAAAC,SAAI,cAAc,YAAY,MAAM,CAAE,CAAA;EAAA,GAC5C,CAAC,cAAc,WAAW,CAAC;AAExB,QAAA,4BAAwB,sBAAQ,MAAM;AACnC,WAAA,OAAO,OAAO,yBAAyB,EAAE,MAAM,CAAC,WAAW,OAAO,YAAY,IAAI;EAAA,GACxF,CAAC,yBAAyB,CAAC;AAExB,QAAA,6BAAyB,sBAAQ,MAAM;AAEzC,WAAA,OAAO,OAAO,yBAAyB,EAAE,KAAK,CAAC,WAAW,OAAO,YAAY,IAAI,KACjF,CAAC;EAAA,GAEF,CAAC,2BAA2B,qBAAqB,CAAC;AAErD,QAAM,4BAAwB;IAC5B,CAAC,EAAE,QAAQ,EAAE,KAAA,EAAA,MAAa;AACN,wBAAA,EAAE,QAAQ,EAAE,MAAM,OAAO,CAAC,sBAAA,EAAA,CAAyB;IACvE;IACA,CAAC,uBAAuB,iBAAiB;EAAA;AAG3C,QAAM,uBAAmB;IACvB,CAAC,eAAe;AACd,aAAO,mBAAmB;IAC5B;IACA,CAAC,cAAc;EAAA;AAGjB,aAAA,yBACG,KACC,EAAA,UAAA;QAAA,yBAAC,MAAK,EAAA,gBAAe,iBAAgB,YAAW,UAC9C,UAAA;UAAC,wBAAA,KAAA,EAAI,cAAc,GACjB,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAY,YAAA,MACf,CAAA,EAAA,CACF;UAAA,wBACC,QAAO,CAAA,CAAA;UACR,wBAAC,KAAI,EAAA,aAAa,GAChB,cAAA;QAAC;QAAA;UACC,MAAM,YAAY;UAClB,SAAS,yBAAyB,kBAAkB;UACpD,iBAAiB,CAAC,UAChB,sBAAsB,EAAE,QAAQ,EAAE,MAAM,YAAY,MAAM,MAAM,EAAA,CAAG;UAGpE,UAAA,cAAc,EAAE,IAAI,wBAAwB,gBAAgB,aAAA,CAAc;QAAA;MAAA,EAAA,CAE/E;IAAA,EAAA,CACF;QACA,wBAAC,MAAA,EAAK,YAAY,GAAG,eAAe,GAClC,cAAA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,OAAO,EAAE,MAAM,EAAA,GAC/B,UAAY,YAAA,QAAQ,IAAI,CAAC,WAAW;AAC7B,YAAA,OAAO,GAAG,OAAO,IAAI;AAGzB,iBAAA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAqB,WAAU,UAAS,YAAW,WACjE,cAAA,yBAAC,iBAAgB,EAAA,UAAU,iBAAiB,OAAO,IAAI,GAAG,SAAS,GAAG,WAAS,MAC7E,UAAA;YAAA;UAAC;UAAA;YACC,aAAS,WAAAA,SAAI,cAAc,MAAM,KAAK;YACtC;YACA,iBAAiB,CAAC,UAAU,SAAS,EAAE,QAAQ,EAAE,MAAM,MAAM,EAAA,CAAG;YAE/D,UAAO,OAAA;UAAA;QACV;YACA;UAAC;UAAA;YACC,MAAK;YACL,SAAS,MAAM,iBAAiB,OAAO,IAAI;YAC3C,OAAO,EAAE,SAAS,eAAe,YAAY,SAAS;YAEtD,UAAA;kBAAC,wBAAA,gBAAA,EAAe,KAAI,QACjB,UAAA;gBACC;kBACE,IAAI;kBACJ,gBAAgB;gBAClB;gBACA;kBACE,OAAO,OAAO;gBAChB;cAAA,EAAA,CAEJ;kBACA,wBAAC,eAAI,EAAA,IAAG,MAAM,CAAA;YAAA;UAAA;QAChB;MACF,EAAA,CAAA,EAAA,GA3BsB,OAAO,IA4B/B;IAAA,CAEH,EAAA,CACH,EACF,CAAA;EACF,EAAA,CAAA;AAEJ;AAEA,YAAY,YAAY;EACtB,aAAa,kBAAAJ,QAAU,OAAO;AAChC;AC/GA,IAAM,gBAAgB,CAAC,EAAE,MAAM,YAAA,MAAkB;AACzC,QAAA,oBAAgB,sBAAQ,MAAM;AAC3B,eAAA,cAAAK;MACL,OAAO,OAAO,YAAY,WAAW,EAAE,OAAO,CAAC,KAAK,MAAM,UAAU;AAC5D,cAAA,cAAc,GAAG,IAAI,gBAAgB,OAAO,KAAK,YAAY,WAAW,EAAE,KAAK,CAAC;AACtF,cAAM,cAAU,cAAAA;UACd,OAAO,KAAK,IAAI,EAAE,OAAO,CAACC,MAAK,YAAY;AAClC,mBAAA;cACL,GAAGA;cACH;gBACE,GAAG,KAAK,OAAO;gBACf,OAAO;gBACP,MAAM,GAAG,WAAW,IAAI,OAAO;cACjC;YAAA;UAEJ,GAAG,CAAA,CAAE;UACL;QAAA;AAGK,eAAA;UACL,GAAG;UACH;YACE;YACA,OAAO,OAAO,KAAK,YAAY,WAAW,EAAE,KAAK;YACjD,MAAM;UACR;QAAA;MAEJ,GAAG,CAAA,CAAE;MACL;IAAA;EACF,GACC,CAAC,MAAM,WAAW,CAAC;AAEtB,aACG,wBAAA,KAAA,EAAI,SAAS,GACX,UAAA,cAAc,IAAI,CAAC,oBAClB,wBAAC,aAAmC,EAAA,YAAA,GAAlB,YAAY,IAAgC,CAC/D,EACH,CAAA;AAEJ;AAEA,cAAc,YAAY;EACxB,MAAM,kBAAAN,QAAU,OAAO;EACvB,aAAa,kBAAAA,QAAU,OAAO;AAChC;AClDA,IAAMG,iBAAe;EACnB,WAAW,CAAE;AACf;AAEA,IAAMI,YAAU,CAAC,OAAO;;EAEtB,GAAQ,OAAO,CAAC,eAAe;AAC7B,YAAQ,OAAO,MAAI;MACjB,KAAK,mBAAmB;AACtB,mBAAW,YAAY,MAAM,UAAU,IAAI,CAAC,UAAU,UAAU;AAC9D,cAAI,UAAU,OAAO,OAAO;AAC1B,mBAAO,EAAE,GAAG,UAAU,QAAQ,CAAC,SAAS,OAAM;UAC/C;AAED,iBAAO,EAAE,GAAG,UAAU,QAAQ,MAAK;QAC7C,CAAS;AAED;MACD;MACD;AACE,eAAO;IACV;EACL,CAAG;;ACZH,IAAM,cAAc,MAAM;AAClB,QAAA,EAAE,aAAA,IAAiB,oBAAA;AACnB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,CAAC,EAAE,UAAA,CAAW,QAAI,yBAAWA,WAASJ,gBAAc,CAAC,UAAUD,OAAK,OAAO,YAAY,CAAC;AAG5F,aAAA,wBAAC,UAAU,MAAV,EAAe,MAAK,KACnB,cAAC,wBAAA,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GAChD,UAAA,UAAU,IAAI,CAAC,UAAU,cACvB,yBAAA,UAAU,MAAV,EAAmC,OAAO,SAAS,MAClD,UAAA;QAAC,wBAAA,UAAU,QAAV,EAAiB,SAAS,QAAQ,MAAM,IAAI,cAAc,QACzD,cAAA;MAAC,UAAU;MAAV;QACC,eAAc;QACd,aAAa;UACX;YACE,IAAI;YACJ,gBAAgB;UAClB;UACA,EAAE,MAAM,SAAS,KAAK;QACxB;QAEC,UAAA,iBAAiB,SAAS,IAAI;MAAA;IAAA,EAAA,CAEnC;QACC,wBAAA,UAAU,SAAV,EACC,cAAA,wBAAC,eAAc,EAAA,aAAa,aAAa,SAAS,IAAI,GAAG,MAAM,SAAS,KAAM,CAAA,EAAA,CAChF;EAAA,EAAA,GAjBmB,SAAS,IAkB9B,CACD,EACH,CAAA,EACF,CAAA;AAEJ;AC5CA,IAAM,iBAAiB,CAAC,SAAS;AAC/B,UAAQ,MAAI;IACV,KAAK,QAAQ;AACX,aAAO;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MACpB;IACK;IACD,KAAK,OAAO;AACV,aAAO;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MACpB;IACK;IACD,KAAK,OAAO;AACV,aAAO;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MACpB;IACK;IACD,KAAK,UAAU;AACb,aAAO;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MACpB;IACK;IACD,SAAS;AACP,aAAO;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MACpB;IACK;EACF;AACH;AC3BA,IAAM,YAAY,GAAO,GAAG;;mBAET,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM,OAAO,CAAC,CAAC;;AAGvF,SAAS,WAAW,EAAE,MAAA,GAAS;AACvB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,QAAM,EAAE,QAAQ,SAAS,OAAO,KAAA,IAAS;AACnC,QAAA,iBAAiB,WAAO,YAAAM,SAAK,KAAK,MAAM,GAAG,CAAC,IAAI,CAAA;AAChD,QAAA,CAAC,aAAa,IAAI,SAAS,EAAE,IAAI,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAA;AAC5D,QAAA,SAAS,eAAe,MAAM,MAAM;AAE1C,aAAA,yBACG,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;QAAA,yBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAA;MAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB;MAAE;UAEH,wBAAC,QAAA,EAAM,UAAW,WAAA,CAAA;UACjB,yBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cAAa,UAAA;QAAA;QAC/C;MAAA,EAAA,CACJ;IAAA,EAAA,CACF;QACA,yBAAC,MAAA,EAAK,WAAS,MAAC,YAAW,YAAW,aAAY,cAAa,KAAK,GAClE,UAAA;UAAA,wBAAC,WAAA,EAAU,YAAY,OAAO,YAAY,aAAa,OAAO,QAAQ,SAAS,GAC7E,cAAA,wBAAC,YAAA,EAAW,YAAW,QAAO,WAAW,OAAO,MAC7C,UAAA,OACH,CAAA,EAAA,CACF;UAAA,wBACC,KAAI,EAAA,aAAa,GAAG,cAAc,GAChC,cAAA,WAAAC,SAAI,gBAAgB,CAAC,cACpB,yBAAC,YAAA,EAAuB,WAAW,MAAM,SAAS,GAAG,IAAI,eAAe,cAAc,UAAA;QAAA;QAClF;MAAA,EAAA,GADa,KAEjB,CACD,EAAA,CACH;IAAA,EAAA,CACF;EACF,EAAA,CAAA;AAEJ;AAEA,WAAW,eAAe;EACxB,OAAO;IACL,SAAS;IACT,QAAQ;IACR,MAAM;EACR;AACF;AAEA,WAAW,YAAY;EACrB,OAAO,kBAAAT,QAAU,MAAM;IACrB,SAAS,kBAAAA,QAAU;IACnB,QAAQ,kBAAAA,QAAU;IAClB,MAAM,kBAAAA,QAAU;EAAA,CACjB;AACH;AC1DA,IAAM,WAAW,MAAM;AACf,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,gBAAgB,OAAO,IAAI,oBAAoB;AAEvD,QAAM,WAAO,eAAAU,SAAQ,eAAe,MAAM,GAAG,GAAG,aAAa;AAC7D,QAAM,uBAAmB,WAAAN,SAAI,QAAQ,KAAK,CAAC,CAAC;AAC5C,QAAM,eAAe,KAAK,MAAM,CAAC,EAAE,KAAK,GAAG;AAE3C,QAAM,sBAAkB,eAAAO,SAAQ,gBAAgB,IAC5C,CAAA,IACA,iBAAiB,OAAO,CAAC,MAAM,EAAE,QAAQ,SAAS,YAAY,CAAC;AAGjE,aAAA;IAAC,KAAK;IAAL;MACC,KAAK;MACL,YAAW;MACX,YAAY;MACZ,eAAe;MACf,aAAa;MACb,cAAc;MACd,OAAO,EAAE,WAAW,OAAO;MAC3B,WAAU;MACV,YAAW;MAEV,UACC,qBAAA,wBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GAChD,UAAA,gBAAgB,IAAI,CAAC,OAAO;;YAE3B,wBAAC,YAAqB,EAAA,MAAA,GAAL,GAAmB;OACrC,EAAA,CACH,QAEC,yBAAA,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;YAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QACjB,CAAA,EAAA,CACH;YAAA,wBACC,YAAW,EAAA,KAAI,KAAI,WAAU,cAC3B,UAAc,cAAA;UACb,IAAI;UACJ,gBACE;QACH,CAAA,EAAA,CACH;MAAA,EAAA,CACF;IAAA;EAAA;AAIR;AC7DA,IAAM,OAAO,CAAC,OAAO,aAAa,WAAW;AAC3C,SAAO;IACL,GAAG;IACH,aAAa;IACb,cAAc;IACd;EACJ;AACA;ACDO,IAAM,eAAe;EAC1B,aAAa,CAAE;EACf,cAAc,CAAE;EAChB,QAAQ,CAAE;EACV,gBAAgB;EAChB,UAAU,CAAE;AACd;AAEA,IAAM,UAAU,CAAC,OAAO,WACtB,GAAQ,OAAO,CAAC,eAAe;AAC7B,UAAQ,OAAO,MAAI;IACjB,KAAK,aAAa;AAChB,YAAM,aAAa,OAAO,KAAK;AAC/B,YAAM,qBAAqB,OAAO,KAAK,aAAa,CAAC,MAAM;AAE3D,UAAI,OAAO,SAAS,oBAAoB;AACtC,cAAM,qBAAiB,YAAAC,SAAK,OAAO,MAAM,aAAa,CAAC,EAAE,KAAK,GAAG;AACjE,mBAAW,iBAAiB;MAC7B;AAED,qBAAAC,SAAI,YAAY,CAAC,gBAAgB,GAAG,OAAO,IAAI,GAAG,OAAO,KAAK;AAC9D;IACD;IACD,KAAK,wBAAwB;AAC3B,YAAM,cAAc,CAAC,gBAAgB,GAAG,OAAO,IAAI;AACnD,YAAM,gBAAY,WAAAT,SAAI,OAAO,aAAa,CAAE,CAAA;AAC5C,YAAM,gBAAgB,OAAO,KAAK,SAAS,EAAE,OAAO,CAAC,KAAK,YAAY;AACpE,YAAI,OAAO,IAAI,EAAE,GAAG,UAAU,OAAO,GAAG,SAAS,OAAO,MAAA;AAExD,eAAO;MACR,GAAE,CAAE,CAAA;AAEL,qBAAAS,SAAI,YAAY,aAAa,aAAa;AAE1C;IACD;IACD,KAAK,YAAY;AACf,iBAAW,eAAe,MAAM;AAChC;IACD;IACD,KAAK,uBAAuB;AAC1B,iBAAW,cAAc,MAAM;AAC/B;IACD;IAED,KAAK,iBAAiB;AACpB,YAAM,EAAE,eAAgB,IAAG;AAC3B,iBAAW,iBAAiB,mBAAmB,MAAM,iBAAiB,KAAK;AAC3E;IACD;IACD;AACE,aAAO;EACV;AACL,CAAG;AC7CH,IAAM,uBAAmB,yBAAW,CAAC,EAAE,aAAa,OAAA,GAAU,QAAQ;AAC9D,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,CAAC,OAAO,QAAQ,QAAI;IAAW;IAAS;IAAc,CAACC,WAC3D,KAAKA,QAAO,aAAa,MAAM;EAAA;AAGjC,wCAAoB,KAAK,OAAO;IAC9B,iBAAiB;AACR,aAAA;QACL,aAAa,MAAM;MAAA;IAEvB;IACA,YAAY;AACD,eAAA,EAAE,MAAM,WAAA,CAAY;IAC/B;IACA,qBAAqB;AACV,eAAA,EAAE,MAAM,sBAAA,CAAuB;IAC1C;EACA,EAAA;AAEI,QAAA,eAAe,CAAC,EAAE,QAAQ,EAAE,MAAM,MAAA,EAAQ,MAC9C,SAAS;IACP,MAAM;IACN,MAAM,KAAK,MAAM,GAAG;IACpB,OAAO,UAAU,wBAAwB,KAAK;EAAA,CAC/C;AAEG,QAAA,wBAAwB,CAAC,EAAE,QAAQ,EAAE,MAAM,MAAA,EAAQ,MACvD,SAAS;IACP,MAAM;IACN,MAAM,KAAK,MAAM,GAAG;IACpB;EAAA,CACD;AAEG,QAAA,uBAAuB,CAAC,mBAC5B,SAAS;IACP,MAAM;IACN;EAAA,CACD;AAEH,QAAM,gBAAgB;IACpB,GAAG;IACH,UAAU;IACV,mBAAmB;IACnB,kBAAkB;EAAA;AAGpB,aACG,wBAAA,0BAAA,EAAyB,OAAO,eAC/B,cAAA,yBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,QAAO,gBAAe,WAAS,MAAC,YAAW,YAC5D,UAAA;QAAA;MAAC,KAAK;MAAL;QACC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,cAAc;QACd,WAAU;QACV,YAAW;QAEX,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;cAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;gBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;cACb,IAAI,QAAQ,sBAAsB;cAClC,gBAAgB;YACjB,CAAA,EAAA,CACH;gBAAA,wBACC,YAAW,EAAA,KAAI,KAAI,WAAU,cAC3B,UAAc,cAAA;cACb,IAAI,QAAQ,4BAA4B;cACxC,gBAAgB;YACjB,CAAA,EAAA,CACH;UAAA,EAAA,CACF;cAAA,wBACC,aAAY,CAAA,CAAA;QAAA,EAAA,CACf;MAAA;IACF;QAAA,wBACC,UAAS,CAAA,CAAA;EAAA,EACZ,CAAA,EACF,CAAA;AAEJ,CAAC;AAED,iBAAiB,YAAY;EAC3B,aAAa,kBAAAd,QAAU,OAAO;EAC9B,QAAQ,kBAAAA,QAAU,OAAO;AAC3B;AAEA,IAAe,yBAAA,mBAAK,gBAAgB;ACnG7B,IAAM,mBAAuBe,QAAQ,EAAC,MAAM;EACjD,MAAU,OAAQ,EAAC,SAAS,YAAiB,SAAS,EAAE;EACxD,aAAiB,OAAQ,EAAC,SAAS,YAAiB,SAAS,EAAE;AACjE,CAAC;ACJD,IAAM,mBAAmB,CAAC,gBACxB,OAAO,KAAK,WAAW,EAAE,OAAO,CAAC,KAAK,YAAY;AAChD,QAAM,oBAAoB,YAAY,OAAO,EAAE;AAC/C,QAAM,qBAAqB,OAAO,KAAK,iBAAiB,EAAE,OAAO,CAAC,MAAM,SAAS;AAC/E,YAAI,eAAAJ,SAAQ,kBAAkB,IAAI,CAAC,GAAG;AACpC,aAAO;IACR;AAED,SAAK,IAAI,IAAI,kBAAkB,IAAI;AAEnC,WAAO;EACR,GAAE,CAAE,CAAA;AAEL,UAAI,eAAAA,SAAQ,kBAAkB,GAAG;AAC/B,WAAO;EACR;AAED,MAAI,OAAO,IAAI,EAAE,aAAa,mBAAkB;AAEhD,SAAO;AACR,GAAE,CAAA,CAAE;ACfA,IAAM,aAAa,MAAM;AAC9B,QAAM,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,EAAE,KAAAP,KAAAA,IAAQ,eAAA;AAChB,QAAM,EAAE,eAAc,IAAK,mBAAmB,OAAO;AAErD,QAAM;IACJ;MACE,MAAM;MACN,WAAW;MACX,OAAO;MACP,SAAS;IACV;IACD,EAAE,MAAM,QAAQ,WAAW,iBAAiB,OAAO,aAAa,SAAS,cAAe;EACzF,IAAG,WAAW;IACb;MACE,UAAU,CAAC,qBAAqB,aAAa;MAC7C,MAAM,UAAU;AACd,cAAM;UACJ,MAAM,EAAE,aAAAY,aAAa;QACtB,IAAG,MAAMZ,KAAI,gCAAgC;AAE9C,eAAOY;MACR;IACF;IACD;MACE,UAAU,CAAC,qBAAqB,QAAQ;MACxC,MAAM,UAAU;AACd,cAAM;UACJ,MAAM,EAAE,QAAAC,QAAQ;QACjB,IAAG,MAAMb,KAAI,2BAA2B;AAEzC,eAAOa;MACR;IACF;EACL,CAAG;AAED,QAAM,iBAAiB,YAAY;AACjC,UAAM,QAAQ,IAAI,CAAC,mBAAkB,GAAI,cAAe,CAAA,CAAC;EAC7D;AAEE,8BAAU,MAAM;AACd,QAAI,kBAAkB;AACpB,yBAAmB;QACjB,MAAM;QACN,SAAS,eAAe,gBAAgB;MAChD,CAAO;IACF;EACF,GAAE,CAAC,oBAAoB,kBAAkB,cAAc,CAAC;AAEzD,8BAAU,MAAM;AACd,QAAI,aAAa;AACf,yBAAmB;QACjB,MAAM;QACN,SAAS,eAAe,WAAW;MAC3C,CAAO;IACF;EACF,GAAE,CAAC,oBAAoB,aAAa,cAAc,CAAC;AAEpD,QAAM,YAAY,wBAAwB;AAE1C,SAAO;;;;IAIL,aAAa,cAAc,iBAAiB,WAAW,IAAI,CAAE;IAC7D,QAAQ,UAAU,CAAE;IAEpB,SAAS;IACT;EACJ;AACA;ACpDO,IAAM,aAAa,MAAM;AACxB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,WAAW,YAAA;AACjB,QAAM,EAAE,WAAW,kBAAkB,aAAa,OAAA,IAAW,WAAA;AACvD,QAAA,EAAE,WAAA,IAAe,YAAA;AACjB,QAAA,iBAAuB,aAAA;AACvB,QAAA,EAAE,KAAA,IAAS,eAAA;AACjB,QAAM,WAAW,YAAY,CAAC,SAAS,KAAK,4BAA4B,IAAI,GAAG;IAC7E,UAAU;AACW,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IACH;IAEA,YAAY;AACV,iBAAW,eAAe;AAEP,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI,QAAQ,wBAAwB;UACpC,gBAAgB;QAAA,CACjB;MAAA,CACF;AAGD,eAAS,EAAE;IACb;EAAA,CACD;AAEK,QAAA,yBAAyB,OAAO,SAAS;AAIvCD,UAAAA,eAAc,eAAe,QAAQ,eAAe;AAEpD,UAAA,SAAS,OAAO,EAAE,GAAG,MAAM,GAAGA,cAAa,OAAO,CAAC,EAAA,CAAG;EAAA;AAG9D,aAAA,yBACG,MACC,EAAA,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE,EAAE,MAAM,QAAQ;IAAA,EAAA,CAEpB;QACA;MAAC;MAAA;QACC,oBAAkB;QAClB,eAAe,EAAE,MAAM,IAAI,aAAa,GAAG;QAC3C,UAAU;QACV,kBAAkB;QAEjB,UAAC,CAAA,EAAE,cAAc,QAAQ,cAAc,OACtC,UAAA,yBAAC,MAAK,EAAA,YAAU,MAAC,UAAU,cACzB,UAAA;cAAA;YAAC,QAAQ;YAAR;cACC,eACE,CAAC,wBACC,wBAAC,QAAA,EAAO,MAAK,UAAS,SAAS,SAAS,WAAW,eAAY,wBAAA,eAAA,CAAA,CAAM,GAClE,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cACjB,CAAA,EAAA,CACH;cAGJ,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,UAAU,cAAc;gBACtB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA;UACH;cACA,wBAAC,QAAQ,SAAR,EACC,cAAA;YAAC;YAAA;cACC,YAAW;cACX,WAAU;cACV,YAAW;cACX,KAAK;cACL,WAAS;cACT,YAAY;cACZ,eAAe;cACf,aAAa;cACb,cAAc;cACd,QAAO;cAEP,UAAA;oBAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAClC,UAAA;sBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;oBACb,IAAI,QAAQ,qBAAqB;oBACjC,gBAAgB;kBACjB,CAAA,EAAA,CACH;sBAEC,yBAAA,KAAK,MAAL,EAAU,KAAK,GACd,UAAA;wBAAC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,WAAU,UAAS,YAAW,WAC/C,cAAA;sBAAC,MAAM;sBAAN;wBACC,MAAK;wBACL,QACE,iCAAQ,QACJ,cAAc,EAAE,IAAI,OAAO,MAAM,gBAAgB,mBAAmB,CAAC,IACrE;wBAEN,UAAQ;wBAER,UAAA;8BAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;4BACb,IAAI;4BACJ,gBAAgB;0BACjB,CAAA,EAAA,CACH;8BAAA,wBACC,WAAU,EAAA,OAAO,OAAO,QAAQ,IAAI,UAAU,aAAA,CAAc;8BAC7D,wBAAC,MAAM,OAAN,CAAA,CAAY;wBAAA;sBAAA;oBAAA,EAAA,CAEjB;wBACA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,WAAU,UAAS,YAAW,WAC/C,cAAA;sBAAC,MAAM;sBAAN;wBACC,MAAK;wBACL,QACE,iCAAQ,eACJ,cAAc;0BACZ,IAAI,OAAO;0BACX,gBAAgB;wBACjB,CAAA,IACD;wBAEN,UAAQ;wBAER,UAAA;8BAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;4BACb,IAAI;4BACJ,gBAAgB;0BACjB,CAAA,EAAA,CACH;8BAAA,wBACC,UAAS,EAAA,OAAO,OAAO,eAAe,IAAI,UAAU,aAAA,CAAc;8BACnE,wBAAC,MAAM,OAAN,CAAA,CAAY;wBAAA;sBAAA;oBAAA,EAAA,CAEjB;kBAAA,EAAA,CACF;gBAAA,EAAA,CACF;gBAEC,CAAC,wBACA;kBAACjB;kBAAA;oBACC,KAAK;oBACL;oBACA;kBAAA;gBACF;cAAA;YAAA;UAAA,EAAA,CAGN;QAAA,EAAA,CACF;MAAA;IAEJ;EACF,EAAA,CAAA;AAEJ;AAEa,IAAA,2BAA2B,UACtC,wBAAC,KAAK,SAAL,EAAa,aAAa,YAAY,YACrC,cAAC,wBAAA,YAAA,CAAA,CAAW,EACd,CAAA;AChKK,IAAM,WAAW,MAAM;AACtB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA;IACJ,QAAQ,EAAE,GAAG;EAAA,IACX,SAAS,uCAAuC;AAC9C,QAAA,EAAE,KAAAK,KAAAA,IAAQ,eAAA;AAChB,QAAM,EAAE,WAAW,kBAAkB,OAAA,IAAW,WAAW;AACrD,QAAA;IACJ,MAAM;IACN,WAAW;IACX,SAAS;EAAA,IACP,SAAS,CAAC,qBAAqB,QAAQ,EAAE,GAAG,YAAY;AAEpD,UAAA;MACJ,MAAM,EAAE,MAAAc,MAAK;IACX,IAAA,MAAMd,KAAI,4BAA4B,EAAE,EAAE;AAEvCc,WAAAA;EAAA,CACR;AAEK,QAAA,iBAAuB,aAAA;AACvB,QAAA,EAAE,IAAA,IAAQ,eAAA;AACV,QAAA,EAAE,eAAA,IAAmB,mBAAA;AACrB,QAAA,WAAW,YAAY,CAAC,SAAS,IAAI,4BAA4B,EAAE,IAAI,IAAI,GAAG;IAClF,QAAQ,OAAO;AACM,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IACH;IAEA,MAAM,YAAY;AACG,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI,QAAQ,wBAAwB;UACpC,gBAAgB;QAAA,CACjB;MAAA,CACF;AAED,YAAM,YAAY;IACpB;EAAA,CACD;AAEK,QAAA,uBAAuB,OAAO,SAAS;AACrC,UAAA,cAAc,eAAe,QAAQ,eAAe;AAEpD,UAAA,SAAS,OAAO,EAAE,GAAG,MAAM,GAAG,aAAa,OAAO,CAAC,EAAA,CAAG;EAAA;AAG9D,MAAI,eAAe;AACV,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEA,aAAA,yBACG,MACC,EAAA,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE,EAAE,MAAM,QAAQ;IAAA,EAAA,CAEpB;QACA;MAAC;MAAA;QACC,oBAAkB;QAClB,eAAe,EAAE,MAAM,KAAK,MAAM,aAAa,KAAK,YAAY;QAChE,UAAU;QACV,kBAAkB;QAEjB,UAAC,CAAA,EAAE,cAAc,QAAQ,cAAc,OACtC,UAAA,yBAAC,MAAK,EAAA,YAAU,MAAC,UAAU,cACzB,UAAA;cAAA;YAAC,QAAQ;YAAR;cACC,eACE,CAAC,uBACC;gBAAC;gBAAA;kBACC,UAAU,KAAK,SAAS;kBACxB,MAAK;kBACL,SAAS,SAAS;kBAClB,eAAA,wBAAY,eAAM,CAAA,CAAA;kBAEjB,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBAAA,CACjB;gBAAA;cAAA,IAED;cAEN,OAAO,KAAK;cACZ,UAAU,KAAK;cACf,sBAAA,wBAAmB,YAAW,CAAA,CAAA;YAAA;UAChC;cACA,wBAAC,QAAQ,SAAR,EACC,cAAA;YAAC;YAAA;cACC,YAAW;cACX,WAAU;cACV,YAAW;cACX,KAAK;cACL,WAAS;cACT,YAAY;cACZ,eAAe;cACf,aAAa;cACb,cAAc;cACd,QAAO;cAEP,UAAA;oBAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;sBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;oBACb,IAAI,QAAQ,qBAAqB;oBACjC,gBAAgB;kBACjB,CAAA,EAAA,CACH;sBAEC,yBAAA,KAAK,MAAL,EAAU,KAAK,GACd,UAAA;wBAAC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,WAAU,UAAS,YAAW,WAC/C,cAAA;sBAAC,MAAM;sBAAN;wBACC,MAAK;wBACL,QACE,iCAAQ,QACJ,cAAc;0BACZ,IAAI,OAAO;0BACX,gBAAgB;wBACjB,CAAA,IACD;wBAEN,UAAQ;wBAER,UAAA;8BAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;4BACb,IAAI;4BACJ,gBAAgB;0BACjB,CAAA,EAAA,CACH;8BAAA,wBACC,WAAU,EAAA,OAAO,OAAO,QAAQ,IAAI,UAAU,aAAA,CAAc;8BAC7D,wBAAC,MAAM,OAAN,CAAA,CAAY;wBAAA;sBAAA;oBAAA,EAAA,CAEjB;wBACA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,WAAU,UAAS,YAAW,WAC/C,cAAA;sBAAC,MAAM;sBAAN;wBACC,MAAK;wBACL,QACE,iCAAQ,eACJ,cAAc;0BACZ,IAAI,OAAO;0BACX,gBAAgB;wBACjB,CAAA,IACD;wBAEN,UAAQ;wBAER,UAAA;8BAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;4BACb,IAAI;4BACJ,gBAAgB;0BACjB,CAAA,EAAA,CACH;8BAAA,wBACC,UAAS,EAAA,OAAO,OAAO,eAAe,IAAI,UAAU,aAAA,CAAc;8BACnE,wBAAC,MAAM,OAAN,CAAA,CAAY;wBAAA;sBAAA;oBAAA,EAAA,CAEjB;kBAAA,EAAA,CACF;gBAAA,EAAA,CACF;gBAEC,CAAC,wBACA;kBAACnB;kBAAA;oBACC,KAAK;oBACL,aAAa,KAAK;oBAClB;kBAAA;gBACF;cAAA;YAAA;UAAA,EAAA,CAGN;QAAA,EAAA,CACF;MAAA;IAEJ;EACF,EAAA,CAAA;AAEJ;AAEa,IAAA,yBAAyB,UACpC,wBAAC,KAAK,SAAL,EAAa,aAAa,YAAY,YACrC,cAAC,wBAAA,UAAA,CAAA,CAAS,EACZ,CAAA;AC5MF,IAAM,WAAW,GAAO,IAAI;;;;;;aAMf,CAAC,EAAE,MAAM,MAAM,GAAG,MAAM,OAAO,CAAC,CAAC,EAAE;;;;;;;cAOlC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;;;;;gBAQpC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;;AAMtD,IAAM,YAAY,CAAC,EAAE,aAAa,WAAW,WAAW,iBAAiB,SAAA,MAAe;AAChF,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,WAAW,YAAA;AACX,QAAA,CAAC,mBAAmB,oBAAoB,IAAI;AAE5C,QAAA,qBAAqB,CAAC,SAC1B,aAAa,CAAC,CAAC,UAAU,eAAe,EAAE,SAAS,KAAK,IAAI;AAExD,QAAA,oBAAoB,CAAC,OAAO;AAChC,oBAAgB,EAAE;AAClB,yBAAqB,CAAC,iBAAiB;EAAA;AAGzC,aACG,wBAAA,OAAA,EACE,UAAa,2CAAA,IAAI,CAAC,aACjB,yBAAC,IAAmB,EAAA,SAAS,MAAM,SAAS,KAAK,GAAG,SAAA,CAAU,GAC5D,UAAA;QAAA,wBAAC,IAAA,EAAG,OAAM,OACR,cAAA,wBAAC,YAAY,EAAA,UAAA,KAAK,KAAA,CAAK,EACzB,CAAA;QACA,wBAAC,IAAA,EAAG,OAAM,OACR,cAAA,wBAAC,YAAY,EAAA,UAAA,KAAK,YAAA,CAAY,EAChC,CAAA;QACC,wBAAA,IAAA,EAAG,OAAM,OACR,cAAA,wBAAC,YACE,EAAA,UAAA;MACC;QACE,IAAI;QACJ,gBAAgB;MAClB;MACA,EAAE,QAAQ,KAAK,SAAS;IAAA,EAAA,CAE5B,EACF,CAAA;QACA,wBAAC,IACC,EAAA,cAAA,yBAAC,MAAK,EAAA,gBAAe,OAAM,SAAS,CAAC,MAAM,EAAE,gBAAA,GAC1C,UAAA;MACC,gBAAA;QAAC;QAAA;UACC,KAAK;UACL,IAAI,KAAK,GAAG,SAAS;UACrB,cAAY;YACV,EAAE,IAAI,4BAA4B,gBAAgB,gBAAgB;YAClE,EAAE,QAAQ,GAAG,KAAK,IAAI,GAAG;UAC3B;UAEA,cAAA,wBAAC,eAAO,CAAA,CAAA;QAAA;MAAA,IAER;MAEH,mBAAmB,IAAI,SACtB;QAAC;QAAA;UACC,SAAS,MAAM,kBAAkB,KAAK,GAAG,SAAA,CAAU;UACnD,SAAQ;UACR,OAAO;YACL,EAAE,IAAI,wBAAwB,gBAAgB,kBAAkB;YAChE,EAAE,QAAQ,GAAG,KAAK,IAAI,GAAG;UAC3B;UAEA,cAAA,wBAAC,cAAM,CAAA,CAAA;QAAA;MACT;IAAA,EAAA,CAEJ,EACF,CAAA;EA9CO,EAAA,GAAA,KAAK,IA+Cd,GAEJ,CAAA;AAEJ;AAIA,UAAU,eAAe;EACvB,WAAW;EACX,WAAW;AACb;AAEA,UAAU,YAAY;EACpB,UAAU,kBAAAC,QAAU,MAAM;EAC1B,iBAAiB,kBAAAA,QAAU,KAAK;EAChC,aAAa,kBAAAA,QAAU,MAAM;EAC7B,WAAW,kBAAAA,QAAU;EACrB,WAAW,kBAAAA,QAAU;AACvB;ACjFO,IAAM,gBAAgB,MAAM;AAC3B,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,EAAE,eAAe,OAAO,IAAI,QAAQ;AACpC,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA,EAAE,aAAA,IAAiB,YAAA;AACzB,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAAe;AAC7B,QAAA,MAAK,+BAAO,OAAM;AACxB,QAAM,CAAC,mBAAmB,oBAAoB,QAAI,uBAAS,KAAK;AAChE,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS;AACjD,QAAM,EAAE,KAAK,KAAAI,KAAI,IAAI,eAAe;AAE9B,QAAA;IACJ,WAAW;IACX,gBAAgB,EAAE,SAAS,WAAW,WAAW,UAAU;EAAA,IACzD,QAAQ;IACV,QAAQ,YAAY;IACpB,MAAM,YAAY;IAClB,QAAQ,YAAY;IACpB,QAAQ,YAAY;EAAA,CACrB;AAEK,QAAA;IACJ,WAAW;IACX,MAAM,EAAE,MAAM;IACd;IACA;EAAA,IACE,SAAS,aAAa,MAAM,UAAU,oBAAoB,eAAe,YAAY,GAAG;IAC1F,aAAa,CAAC;IACd,SAAS;EAAA,CACV;AAED,QAAM,EAAE,SAAA,IAAa,UAAU,QAAQ;IACrC,aAAa;EAAA,CACd;AAKK,QAAA,YAAY,YAAY,QAAQ;IACpC,aAAa;EAAA,CACd;AAEK,QAAA,YAAY,oBAAoB,cAAc;AAEpD,QAAM,0BAA0B,MAAM;AACpC,yBAAqB,CAAC,iBAAiB;EAAA;AAGzC,QAAM,aAAa,OAAO,IAAIe,gBAAeC,wBAAuB;AAC9D,QAAA;AACI,YAAA,IAAI,4BAA4B,EAAE,EAAE;IAAA,SACnC,OAAO;AACdA,0BAAmB;QACjB,MAAM;QACN,SAASD,eAAc,EAAE,IAAI,sBAAsB,gBAAgB,mBAAA,CAAoB;MAAA,CACxF;IACH;EAAA;AAGF,QAAM,YAAY,OAAOC,qBAAoBD,gBAAeE,kBAAiB;AACvE,QAAA;AACF,YAAM,EAAE,KAAS,IAAA,MAAMjB,KAAI,0BAA0B;AACrDiB,oBAAa,oCAAoC;AAE1C,aAAA;IAAA,SACA,KAAK;AACZD,0BAAmB;QACjB,MAAM;QACN,SAASD,eAAc,EAAE,IAAI,sBAAsB,gBAAgB,oBAAA,CAAqB;MAAA,CACzF;AAEK,YAAA,IAAI,MAAM,GAAG;IACrB;EAAA;AAGF,QAAM,cAAc;IAClB,OAAO;MACL,IAAI,QAAQ,aAAa;MACzB,gBAAgB;IAClB;IACA,QAAQ;MACN,IAAI,QAAQ,oBAAoB;MAChC,gBAAgB;IAClB;EAAA;AAGF,QAAM,YAAY,cAAc;IAC9B,IAAI;IACJ,gBAAgB;EAAA,CACjB;AAEK,QAAA,iBAAiB,YAAY,CAAC,OAAO,WAAW,IAAI,eAAe,kBAAkB,GAAG;IAC5F,MAAM,YAAY;AAChB,YAAM,QAAQ;IAChB;EAAA,CACD;AAED,QAAM,sBAAsB,YAAY;AAChC,UAAA,eAAe,YAAY,YAAY;AAC7C,yBAAqB,CAAC,iBAAiB;EAAA;AAGzC,QAAM,eAAe,SAAS,CAAA,GAC3B,OAAO,CAAC,SAAS,SAAS,KAAK,MAAM,EAAE,KAAK,SAAS,KAAK,aAAa,EAAE,CAAC,EAC1E;IACC,CAAC,GAAG,MAAM,UAAU,QAAQ,EAAE,MAAM,EAAE,IAAI,KAAK,UAAU,QAAQ,EAAE,aAAa,EAAE,WAAW;EAAA;AAGjG,QAAM,eAAe,MAAM,CAAC,YAAY,SAAS,WAAW;AAE5D,QAAM,WAAW;AACX,QAAA,aAAY,+BAAO,WAAU,KAAK;AAExC,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAGE,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE,EAAE,MAAM,UAAU;IAAA,EAAA,CAEtB;QACA,yBAAC,KAAK,MAAL,EACC,UAAA;UAAA;QAAC,QAAQ;QAAR;UACC,OAAO,cAAc;YACnB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,UAAU,cAAc;YACtB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,eACE,gBACE;YAAC;YAAA;cACC,IAAG;cACH,KAAK;cACL,SAAS,MAAM,WAAW,gBAAgB;cAC1C,eAAA,wBAAY,eAAK,CAAA,CAAA;cACjB,MAAK;cAEJ,UAAc,cAAA;gBACb,IAAI,QAAQ,mBAAmB;gBAC/B,gBAAgB;cAAA,CACjB;YAAA;UAAA,IAED;QAAA;MAER;UAEA;QAAC,QAAQ;QAAR;UACC,kBACE;YAAC;YAAA;cACC,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA;UACH;QAAA;MAEJ;UAEA,yBAAC,QAAQ,SAAR,EACE,UAAA;QAAA,CAAC,eAAW,wBAAC,KAAK,eAAL,CAAA,CAAmB;QAChC,WAAW,gBAAe,2CAAa,cACrC,yBAAA,OAAA,EAAM,UAAoB,UACzB,UAAA;cAAC,wBAAA,OAAA,EACC,cAAA,yBAAC,IACC,EAAA,UAAA;gBAAA,wBAAC,IACC,EAAA,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA,EAAE,IAAI,eAAe,gBAAgB,OAAO,CAAC,EAC9D,CAAA,EAAA,CACF;gBACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EAAA,CACH,EACF,CAAA;gBACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EAAA,CACH,EACF,CAAA;gBACC,wBAAA,IAAA,EACC,cAAC,wBAAA,gBAAA,EACE,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EAAA,CACH,EACF,CAAA;UAAA,EAAA,CACF,EACF,CAAA;cACA;YAAC;YAAA;cACC;cACA;cACA;cACA,aAAa;cACb;cACA,UAAU,CAAC,mBAAmB,oBAAoB;YAAA;UACpD;QAAA,EAAA,CACF,QAAA,wBAEC,kBAAiB,EAAA,SAAS,cAAc,YAAY,YAAY,CAAC,EAAA,CAAG;MAAA,EAAA,CAEzE;UACC,wBAAA,OAAO,MAAP,EAAY,MAAM,mBAAmB,cAAc,yBAClD,cAAC,wBAAA,eAAA,EAAc,WAAW,oBAAA,CAAqB,EACjD,CAAA;IAAA,EAAA,CACF;EACF,EAAA,CAAA;AAEJ;AAEO,IAAM,yBAAyB,MAAM;AAExC,aAAA,wBAAC,KAAK,SAAL,EAAa,aAAa,YAAY,aACrC,cAAC,wBAAA,eAAA,CAAc,CAAA,EACjB,CAAA;AAEJ;AC9PA,IAAM,QAAQ,MAAM;AAEhB,aAAA,wBAAC,KAAK,SAAL,EAAa,aAAa,YAAY,aACrC,cAAA,yBAAC,QACC,EAAA,UAAA;QAAA,wBAAC,OAAA,EAAM,OAAK,MAAC,aAAS,wBAAC,wBAAA,CAAuB,CAAA,EAAA,CAAI;QAAA,wBACjD,OAAM,EAAA,MAAK,OAAM,aAAS,wBAAC,0BAAA,CAAyB,CAAA,EAAA,CAAI;QAAA,wBACxD,OAAM,EAAA,MAAK,OAAM,aAAS,wBAAC,wBAAA,CAAuB,CAAA,EAAA,CAAI;EAAA,EACzD,CAAA,EACF,CAAA;AAEJ;", "names": ["without", "take", "UsersPermissions", "PropTypes", "upperFirst", "init", "initialState", "get", "sortBy", "acc", "reducer", "tail", "map", "without", "isEmpty", "take", "set", "state", "create", "permissions", "routes", "role", "formatMessage", "toggleNotification", "notify<PERSON><PERSON><PERSON>"]}