{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/_chunks/pt-BR-f0p23AQZ.mjs"], "sourcesContent": ["const ptBR = {\n  \"BoundRoute.title\": \"Rota definida para\",\n  \"EditForm.inputSelect.description.role\": \"Ele anexará o novo usuário autenticado ao nível selecionado.\",\n  \"EditForm.inputSelect.label.role\": \"<PERSON><PERSON>vel padrão para usuários autenticados\",\n  \"EditForm.inputToggle.description.email\": \"Não permitir que o usuário crie várias contas usando o mesmo endereço de e-mail com diferentes provedores de autenticação.\",\n  \"EditForm.inputToggle.description.sign-up\": \"Quando desativado (OFF), o processo de registro é proibido. Nenhum novo usuário poderá se registrar, não importa o provedor usado.\",\n  \"EditForm.inputToggle.label.email\": \"Limitar 1 conta por endereço de email\",\n  \"EditForm.inputToggle.label.sign-up\": \"Ativar registro de usuários\",\n  \"HeaderNav.link.advancedSettings\": \"Configurações avançadas\",\n  \"HeaderNav.link.emailTemplates\": \"Modelos de email\",\n  \"HeaderNav.link.providers\": \"Provedores\",\n  \"Plugin.permissions.plugins.description\": \"Defina todas as ações permitidas para o plugin {name}.\",\n  \"Plugins.header.description\": \"Somente ações vinculadas por uma rota estão listadas abaixo.\",\n  \"Plugins.header.title\": \"Permissões\",\n  \"Policies.header.hint\": \"Selecione as ações do aplicativo ou as ações do plugin e clique no ícone do cog para exibir a rota\",\n  \"Policies.header.title\": \"Configurações avançadas\",\n  \"PopUpForm.Email.email_templates.inputDescription\": \"Se não tiver certeza de como usar variáveis, {link}\",\n  \"PopUpForm.Email.options.from.email.label\": \"Email do remetente\",\n  \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Email.options.from.name.label\": \"Nome do remetente\",\n  \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n  \"PopUpForm.Email.options.message.label\": \"Mensagem\",\n  \"PopUpForm.Email.options.object.label\": \"Assunto\",\n  \"PopUpForm.Email.options.response_email.label\": \"Email de resposta\",\n  \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Providers.enabled.description\": \"Se desativado, os usuários não poderão usar este provedor\",\n  \"PopUpForm.Providers.enabled.label\": \"Ativar\",\n  \"PopUpForm.Providers.key.label\": \"ID do cliente\",\n  \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.redirectURL.front-end.label\": \"O URL de redirecionamento para seu aplicativo front-end\",\n  \"PopUpForm.Providers.secret.label\": \"Segredo do Cliente\",\n  \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n  \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n  \"PopUpForm.header.edit.email-templates\": \"Editar modelos de email\",\n  \"notification.success.submit\": \"As configurações foram atualizadas\",\n  \"plugin.description.long\": \"Proteja sua API com um processo de autenticação completo baseado no JWT. Esse plugin também vem com uma estratégia de ACL que permite gerenciar as permissões entre os grupos de usuários.\",\n  \"plugin.description.short\": \"Proteja sua API com um processo de autenticação completo baseado no JWT\",\n  \"plugin.name\": \"Papéis e permissões\"\n};\nexport {\n  ptBR as default\n};\n//# sourceMappingURL=pt-BR-f0p23AQZ.mjs.map\n"], "mappings": ";;;AAAA,IAAM,OAAO;AAAA,EACX,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACjB;", "names": []}