{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/router.tsx"], "sourcesContent": ["/* eslint-disable check-file/filename-naming-convention */\nimport { lazy } from 'react';\n\nimport { Routes, Route, PathRouteProps } from 'react-router-dom';\n\nconst ProtectedListPage = lazy(() =>\n  import('./routes/settings').then((mod) => ({ default: mod.ProtectedListPage }))\n);\nconst ProtectedEditPage = lazy(() =>\n  import('./routes/settings/id').then((mod) => ({ default: mod.ProtectedEditPage }))\n);\n\nconst routes: PathRouteProps[] = [\n  {\n    path: '/',\n    Component: ProtectedListPage,\n  },\n  {\n    path: ':id',\n    Component: ProtectedEditPage,\n  },\n];\n\nconst Router = () => (\n  <Routes>\n    {routes.map((route) => (\n      <Route key={route.path} {...route} />\n    ))}\n  </Routes>\n);\n\nexport { Router };\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAKA,IAAM,wBAAoB;EAAK,MAC7B,OAAO,8BAAmB,EAAE,KAAK,CAAC,SAAS,EAAE,SAAS,IAAI,kBAAA,EAAoB;AAChF;AACA,IAAM,wBAAoB;EAAK,MAC7B,OAAO,2BAAsB,EAAE,KAAK,CAAC,SAAS,EAAE,SAAS,IAAI,kBAAA,EAAoB;AACnF;AAEA,IAAM,SAA2B;EAC/B;IACE,MAAM;IACN,WAAW;EACb;EACA;IACE,MAAM;IACN,WAAW;EACb;AACF;AAEA,IAAM,SAAS,UACZ,wBAAA,QAAA,EACE,UAAA,OAAO,IAAI,CAAC,cACX,wBAAC,OAAA,EAAwB,GAAG,MAAA,GAAhB,MAAM,IAAiB,CACpC,EACH,CAAA;", "names": []}