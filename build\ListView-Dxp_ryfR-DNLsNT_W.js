import{au as I,a as C,ax as S,e as N,al as D,ba as V,bO as F,c as R,r as o,bl as _,m as e,n as d,L as T,bK as A,b0 as b,aU as h,bM as y,bN as x}from"./strapi-YzJfjJ2z.js";import{u as U}from"./useOnce-NHeEacbN-BN84kb-5.js";import{u as v,a as B}from"./apiTokens-BOJ6JDtg-_9662LTw.js";import{A as r}from"./constants-CRj0ViV1-Q2dfXdfa.js";import{T as H}from"./Table-C6ZlhkWY-DbvwIyjV.js";const O=[{name:"name",label:{id:"Settings.apiTokens.ListView.headers.name",defaultMessage:"Name"},sortable:!0},{name:"description",label:{id:"Settings.apiTokens.ListView.headers.description",defaultMessage:"Description"},sortable:!1},{name:"createdAt",label:{id:"Settings.apiTokens.ListView.headers.createdAt",defaultMessage:"Created at"},sortable:!1},{name:"lastUsedAt",label:{id:"Settings.apiTokens.ListView.headers.lastUsedAt",defaultMessage:"Last used"},sortable:!1}],G=()=>{const{formatMessage:t}=I(),{toggleNotification:a}=C(),w=S(s=>s.admin_app.permissions.settings?.["api-tokens"]),{allowedActions:{canRead:u,canCreate:c,canDelete:L,canUpdate:j}}=N(w),p=D(),{trackUsage:i}=V(),m=F("ListView",s=>s.startSection),{_unstableFormatAPIError:l}=R();o.useEffect(()=>{m("apiTokens")},[m]),o.useEffect(()=>{p({search:_.stringify({sort:"name:ASC"},{encode:!1})})},[p]);const P=O.map(s=>({...s,label:t(s.label)}));U(()=>{i("willAccessTokenList",{tokenType:r})});const{data:n=[],isLoading:f,error:g}=v();o.useEffect(()=>{g&&a({type:"danger",message:l(g)})},[g,l,a]),o.useEffect(()=>{i("didAccessTokenList",{number:n.length,tokenType:r})},[n,i]);const[M]=B(),E=async s=>{try{const k=await M(s);if("error"in k){a({type:"danger",message:l(k.error)});return}i("didDeleteToken")}catch{a({type:"danger",message:t({id:"notification.error",defaultMessage:"Something went wrong"})})}};return e.jsxs(e.Fragment,{children:[e.jsx(d.Title,{children:t({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"API Tokens"})}),e.jsx(T.Header,{title:t({id:"Settings.apiTokens.title",defaultMessage:"API Tokens"}),subtitle:t({id:"Settings.apiTokens.description",defaultMessage:"List of generated tokens to consume the API"}),primaryAction:c&&e.jsx(A,{tag:h,"data-testid":"create-api-token-button",startIcon:e.jsx(b,{}),size:"S",onClick:()=>i("willAddTokenFromList",{tokenType:r}),to:"/settings/api-tokens/create",children:t({id:"Settings.apiTokens.create",defaultMessage:"Create new API Token"})})}),u?e.jsx(d.Main,{"aria-busy":f,children:e.jsxs(T.Content,{children:[n.length>0&&e.jsx(H,{permissions:{canRead:u,canDelete:L,canUpdate:j},headers:P,isLoading:f,onConfirmDelete:E,tokens:n,tokenType:r}),c&&n.length===0?e.jsx(y,{icon:e.jsx(x,{width:"16rem"}),content:t({id:"Settings.apiTokens.addFirstToken",defaultMessage:"Add your first API Token"}),action:e.jsx(A,{tag:h,variant:"secondary",startIcon:e.jsx(b,{}),to:"/settings/api-tokens/create",children:t({id:"Settings.apiTokens.addNewToken",defaultMessage:"Add new API Token"})})}):null,!c&&n.length===0?e.jsx(y,{icon:e.jsx(x,{width:"16rem"}),content:t({id:"Settings.apiTokens.emptyStateLayout",defaultMessage:"You don’t have any content yet..."})}):null]})}):e.jsx(d.NoPermissions,{})]})},Q=()=>{const t=S(a=>a.admin_app.permissions.settings?.["api-tokens"].main);return e.jsx(d.Protect,{permissions:t,children:e.jsx(G,{})})};export{G as ListView,Q as ProtectedListView};
