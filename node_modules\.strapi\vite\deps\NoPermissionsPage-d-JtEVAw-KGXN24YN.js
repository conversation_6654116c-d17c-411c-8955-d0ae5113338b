import {
  getTranslation,
  useIntl
} from "./chunk-D63J2BWQ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  Page
} from "./chunk-4C2ZQ5OG.js";
import "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/NoPermissionsPage-d-JtEVAw.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var NoPermissions = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: getTranslation("header.name"),
          defaultMessage: "Content"
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsx)(Page.NoPermissions, {}) })
  ] });
};
export {
  NoPermissions
};
//# sourceMappingURL=NoPermissionsPage-d-JtEVAw-KGXN24YN.js.map
