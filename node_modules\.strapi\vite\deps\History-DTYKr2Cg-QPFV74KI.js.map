{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/history/components/VersionInputRenderer.tsx", "../../../@strapi/content-manager/admin/src/history/components/VersionContent.tsx", "../../../@strapi/content-manager/admin/src/history/services/historyVersion.ts", "../../../@strapi/content-manager/admin/src/history/components/VersionHeader.tsx", "../../../@strapi/content-manager/admin/src/history/components/VersionsList.tsx", "../../../@strapi/content-manager/admin/src/history/pages/History.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  useStra<PERSON><PERSON><PERSON>,\n  useForm,\n  InputRenderer as FormInput<PERSON><PERSON>er,\n  useField,\n  Form,\n} from '@strapi/admin/strapi-admin';\nimport { Al<PERSON>, Box, Field, Flex, Link, Tooltip, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { COLLECTION_TYPES } from '../../constants/collections';\nimport { useDocumentRBAC } from '../../features/DocumentRBAC';\nimport { useDoc } from '../../hooks/useDocument';\nimport { useDocLayout } from '../../hooks/useDocumentLayout';\nimport { useLazyComponents } from '../../hooks/useLazyComponents';\nimport { useTypedSelector } from '../../modules/hooks';\nimport { DocumentStatus } from '../../pages/EditView/components/DocumentStatus';\nimport { BlocksInput } from '../../pages/EditView/components/FormInputs/BlocksInput/BlocksInput';\nimport { ComponentInput } from '../../pages/EditView/components/FormInputs/Component/Input';\nimport {\n  DynamicZone,\n  useDynamicZone,\n} from '../../pages/EditView/components/FormInputs/DynamicZone/Field';\nimport { NotAllowedInput } from '../../pages/EditView/components/FormInputs/NotAllowed';\nimport { UIDInput } from '../../pages/EditView/components/FormInputs/UID';\nimport { Wysiwyg } from '../../pages/EditView/components/FormInputs/Wysiwyg/Field';\nimport { useFieldHint } from '../../pages/EditView/components/InputRenderer';\nimport { getRelationLabel } from '../../utils/relations';\nimport { useHistoryContext } from '../pages/History';\n\nimport { getRemaingFieldsLayout } from './VersionContent';\n\nimport type { EditFieldLayout } from '../../hooks/useDocumentLayout';\nimport type { RelationsFieldProps } from '../../pages/EditView/components/FormInputs/Relations';\nimport type { RelationResult } from '../../services/relations';\nimport type { Schema } from '@strapi/types';\nimport type { DistributiveOmit } from 'react-redux';\n\nconst StyledAlert = styled(Alert).attrs({ closeLabel: 'Close', onClose: () => {}, shadow: 'none' })`\n  button {\n    display: none;\n  }\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * CustomRelationInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst LinkEllipsis = styled(Link)`\n  display: block;\n\n  & > span {\n    white-space: nowrap;\n    overflow: hidden;\n    text-overflow: ellipsis;\n    display: block;\n  }\n`;\n\nconst CustomRelationInput = (props: RelationsFieldProps) => {\n  const { formatMessage } = useIntl();\n  const field = useField<\n    { results: RelationResult[]; meta: { missingCount: number } } | RelationResult[]\n  >(props.name);\n\n  /**\n   * Ideally the server would return the correct shape, however, for admin user relations\n   * it sanitizes everything out when it finds an object for the relation value.\n   */\n  let formattedFieldValue;\n  if (field) {\n    formattedFieldValue = Array.isArray(field.value)\n      ? { results: field.value, meta: { missingCount: 0 } }\n      : field.value;\n  }\n\n  if (\n    !formattedFieldValue ||\n    (formattedFieldValue.results.length === 0 && formattedFieldValue.meta.missingCount === 0)\n  ) {\n    return (\n      <>\n        <Field.Label action={props.labelAction}>{props.label}</Field.Label>\n        <Box marginTop={1}>\n          {/* @ts-expect-error – we dont need closeLabel */}\n          <StyledAlert variant=\"default\">\n            {formatMessage({\n              id: 'content-manager.history.content.no-relations',\n              defaultMessage: 'No relations.',\n            })}\n          </StyledAlert>\n        </Box>\n      </>\n    );\n  }\n\n  const { results, meta } = formattedFieldValue;\n\n  return (\n    <Box>\n      <Field.Label>{props.label}</Field.Label>\n      {results.length > 0 && (\n        <Flex direction=\"column\" gap={2} marginTop={1} alignItems=\"stretch\">\n          {results.map((relationData) => {\n            // @ts-expect-error - targetModel does exist on the attribute. But it's not typed.\n            const { targetModel } = props.attribute;\n            const href = `../${COLLECTION_TYPES}/${targetModel}/${relationData.documentId}`;\n            const label = getRelationLabel(relationData, props.mainField);\n            const isAdminUserRelation = targetModel === 'admin::user';\n\n            return (\n              <Flex\n                key={relationData.documentId ?? relationData.id}\n                paddingTop={2}\n                paddingBottom={2}\n                paddingLeft={4}\n                paddingRight={4}\n                hasRadius\n                borderColor=\"neutral200\"\n                background=\"neutral150\"\n                justifyContent=\"space-between\"\n              >\n                <Box minWidth={0} paddingTop={1} paddingBottom={1} paddingRight={4}>\n                  <Tooltip label={label}>\n                    {isAdminUserRelation ? (\n                      <Typography>{label}</Typography>\n                    ) : (\n                      <LinkEllipsis tag={NavLink} to={href}>\n                        {label}\n                      </LinkEllipsis>\n                    )}\n                  </Tooltip>\n                </Box>\n                <DocumentStatus status={relationData.status as string} />\n              </Flex>\n            );\n          })}\n        </Flex>\n      )}\n      {meta.missingCount > 0 && (\n        /* @ts-expect-error – we dont need closeLabel */\n        <StyledAlert\n          marginTop={1}\n          variant=\"warning\"\n          title={formatMessage(\n            {\n              id: 'content-manager.history.content.missing-relations.title',\n              defaultMessage:\n                '{number, plural, =1 {Missing relation} other {{number} missing relations}}',\n            },\n            { number: meta.missingCount }\n          )}\n        >\n          {formatMessage(\n            {\n              id: 'content-manager.history.content.missing-relations.message',\n              defaultMessage:\n                \"{number, plural, =1 {It has} other {They have}} been deleted and can't be restored.\",\n            },\n            { number: meta.missingCount }\n          )}\n        </StyledAlert>\n      )}\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * CustomMediaInput\n * -----------------------------------------------------------------------------------------------*/\n\nconst CustomMediaInput = (props: VersionInputRendererProps) => {\n  const { value } = useField(props.name);\n  const results = value ? value.results : [];\n  const meta = value ? value.meta : { missingCount: 0 };\n  const { formatMessage } = useIntl();\n\n  const fields = useStrapiApp('CustomMediaInput', (state) => state.fields);\n  const MediaLibrary = fields.media as React.ComponentType<\n    VersionInputRendererProps & { multiple: boolean }\n  >;\n  return (\n    <Flex direction=\"column\" gap={2} alignItems=\"stretch\">\n      <Form method=\"PUT\" disabled={true} initialValues={{ [props.name]: results }}>\n        <MediaLibrary {...props} disabled={true} multiple={results.length > 1} />\n      </Form>\n      {meta.missingCount > 0 && (\n        <StyledAlert\n          variant=\"warning\"\n          closeLabel=\"Close\"\n          onClose={() => {}}\n          title={formatMessage(\n            {\n              id: 'content-manager.history.content.missing-assets.title',\n              defaultMessage:\n                '{number, plural, =1 {Missing asset} other {{number} missing assets}}',\n            },\n            { number: meta.missingCount }\n          )}\n        >\n          {formatMessage(\n            {\n              id: 'content-manager.history.content.missing-assets.message',\n              defaultMessage:\n                \"{number, plural, =1 {It has} other {They have}} been deleted in the Media Library and can't be restored.\",\n            },\n            { number: meta.missingCount }\n          )}\n        </StyledAlert>\n      )}\n    </Flex>\n  );\n};\n\ntype VersionInputRendererProps = DistributiveOmit<EditFieldLayout, 'size'> & {\n  /**\n   * In the context of content history, deleted fields need to ignore RBAC\n   * @default false\n   */\n  shouldIgnoreRBAC?: boolean;\n};\n\n/**\n * Checks if the i18n plugin added a label action to the field and modifies it\n * to adapt the wording for the history page.\n */\nconst getLabelAction = (labelAction: VersionInputRendererProps['labelAction']) => {\n  if (!React.isValidElement(labelAction)) {\n    return labelAction;\n  }\n\n  // TODO: find a better way to do this rather than access internals\n  const labelActionTitleId = labelAction.props.title.id;\n\n  if (labelActionTitleId === 'i18n.Field.localized') {\n    return React.cloneElement(labelAction, {\n      ...labelAction.props,\n      title: {\n        id: 'history.content.localized',\n        defaultMessage:\n          'This value is specific to this locale. If you restore this version, the content will not be replaced for other locales.',\n      },\n    });\n  }\n\n  if (labelActionTitleId === 'i18n.Field.not-localized') {\n    return React.cloneElement(labelAction, {\n      ...labelAction.props,\n      title: {\n        id: 'history.content.not-localized',\n        defaultMessage:\n          'This value is common to all locales. If you restore this version and save the changes, the content will be replaced for all locales.',\n      },\n    });\n  }\n\n  // Label action is unrelated to i18n, don't touch it.\n  return labelAction;\n};\n\n/**\n * @internal\n *\n * @description An abstraction around the regular form input renderer designed specifically\n * to be used on the History page in the content-manager. It understands how to render specific\n * inputs within the context of a history version (i.e. relations, media, ignored RBAC, etc...)\n */\nconst VersionInputRenderer = ({\n  visible,\n  hint: providedHint,\n  shouldIgnoreRBAC = false,\n  labelAction,\n  ...props\n}: VersionInputRendererProps) => {\n  const customLabelAction = getLabelAction(labelAction);\n\n  const { formatMessage } = useIntl();\n  const version = useHistoryContext('VersionContent', (state) => state.selectedVersion);\n  const configuration = useHistoryContext('VersionContent', (state) => state.configuration);\n  const fieldSizes = useTypedSelector((state) => state['content-manager'].app.fieldSizes);\n\n  const { id, components } = useDoc();\n  const isFormDisabled = useForm('InputRenderer', (state) => state.disabled);\n\n  const isInDynamicZone = useDynamicZone('isInDynamicZone', (state) => state.isInDynamicZone);\n\n  const canCreateFields = useDocumentRBAC('InputRenderer', (rbac) => rbac.canCreateFields);\n  const canReadFields = useDocumentRBAC('InputRenderer', (rbac) => rbac.canReadFields);\n  const canUpdateFields = useDocumentRBAC('InputRenderer', (rbac) => rbac.canUpdateFields);\n  const canUserAction = useDocumentRBAC('InputRenderer', (rbac) => rbac.canUserAction);\n\n  const editableFields = id ? canUpdateFields : canCreateFields;\n  const readableFields = id ? canReadFields : canCreateFields;\n  /**\n   * Component fields are always readable and editable,\n   * however the fields within them may not be.\n   */\n  const canUserReadField = canUserAction(props.name, readableFields, props.type);\n  const canUserEditField = canUserAction(props.name, editableFields, props.type);\n\n  const fields = useStrapiApp('InputRenderer', (app) => app.fields);\n  const { lazyComponentStore } = useLazyComponents(\n    attributeHasCustomFieldProperty(props.attribute) ? [props.attribute.customField] : undefined\n  );\n\n  const hint = useFieldHint(providedHint, props.attribute);\n  const {\n    edit: { components: componentsLayout },\n  } = useDocLayout();\n\n  if (!visible) {\n    return null;\n  }\n\n  /**\n   * Don't render the field if the user can't read it.\n   */\n  if (!shouldIgnoreRBAC && !canUserReadField && !isInDynamicZone) {\n    return <NotAllowedInput hint={hint} {...props} />;\n  }\n\n  const fieldIsDisabled =\n    (!canUserEditField && !isInDynamicZone) || props.disabled || isFormDisabled;\n\n  /**\n   * Attributes found on the current content-type schema cannot be restored. We handle\n   * this by displaying a warning alert to the user instead of the input for that field type.\n   */\n  const addedAttributes = version.meta.unknownAttributes.added;\n  if (Object.keys(addedAttributes).includes(props.name)) {\n    return (\n      <Flex direction=\"column\" alignItems=\"flex-start\" gap={1}>\n        <Field.Label>{props.label}</Field.Label>\n        <StyledAlert\n          width=\"100%\"\n          closeLabel=\"Close\"\n          onClose={() => {}}\n          variant=\"warning\"\n          title={formatMessage({\n            id: 'content-manager.history.content.new-field.title',\n            defaultMessage: 'New field',\n          })}\n        >\n          {formatMessage({\n            id: 'content-manager.history.content.new-field.message',\n            defaultMessage:\n              \"This field didn't exist when this version was saved. If you restore this version, it will be empty.\",\n          })}\n        </StyledAlert>\n      </Flex>\n    );\n  }\n\n  /**\n   * Because a custom field has a unique prop but the type could be confused with either\n   * the useField hook or the type of the field we need to handle it separately and first.\n   */\n  if (attributeHasCustomFieldProperty(props.attribute)) {\n    const CustomInput = lazyComponentStore[props.attribute.customField];\n\n    if (CustomInput) {\n      return (\n        <CustomInput\n          {...props}\n          // @ts-expect-error – TODO: fix this type error in the useLazyComponents hook.\n          hint={hint}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        />\n      );\n    }\n\n    return (\n      <FormInputRenderer\n        {...props}\n        hint={hint}\n        labelAction={customLabelAction}\n        // @ts-expect-error – this workaround lets us display that the custom field is missing.\n        type={props.attribute.customField}\n        disabled={fieldIsDisabled}\n      />\n    );\n  }\n\n  /**\n   * Since media fields use a custom input via the upload plugin provided by the useLibrary hook,\n   * we need to handle the them before other custom inputs coming from the useLibrary hook.\n   */\n  if (props.type === 'media') {\n    return (\n      <CustomMediaInput {...props} labelAction={customLabelAction} disabled={fieldIsDisabled} />\n    );\n  }\n  /**\n   * This is where we handle ONLY the fields from the `useLibrary` hook.\n   */\n  const addedInputTypes = Object.keys(fields);\n  if (!attributeHasCustomFieldProperty(props.attribute) && addedInputTypes.includes(props.type)) {\n    const CustomInput = fields[props.type];\n    return (\n      <CustomInput\n        {...props}\n        // @ts-expect-error – TODO: fix this type error in the useLibrary hook.\n        hint={hint}\n        labelAction={customLabelAction}\n        disabled={fieldIsDisabled}\n      />\n    );\n  }\n\n  /**\n   * These include the content-manager specific fields, failing that we fall back\n   * to the more generic form input renderer.\n   */\n  switch (props.type) {\n    case 'blocks':\n      return <BlocksInput {...props} hint={hint} type={props.type} disabled={fieldIsDisabled} />;\n    case 'component':\n      const { layout } = componentsLayout[props.attribute.component];\n      // Components can only have one panel, so only save the first layout item\n      const [remainingFieldsLayout] = getRemaingFieldsLayout({\n        layout: [layout],\n        metadatas: configuration.components[props.attribute.component].metadatas,\n        fieldSizes,\n        schemaAttributes: components[props.attribute.component].attributes,\n      });\n\n      return (\n        <ComponentInput\n          {...props}\n          layout={[...layout, ...(remainingFieldsLayout || [])]}\n          hint={hint}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        >\n          {(inputProps) => <VersionInputRenderer {...inputProps} shouldIgnoreRBAC={true} />}\n        </ComponentInput>\n      );\n    case 'dynamiczone':\n      return (\n        <DynamicZone\n          {...props}\n          hint={hint}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        />\n      );\n    case 'relation':\n      return (\n        <CustomRelationInput\n          {...props}\n          hint={hint}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        />\n      );\n    case 'richtext':\n      return (\n        <Wysiwyg\n          {...props}\n          hint={hint}\n          type={props.type}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        />\n      );\n    case 'uid':\n      return (\n        <UIDInput\n          {...props}\n          hint={hint}\n          type={props.type}\n          labelAction={customLabelAction}\n          disabled={fieldIsDisabled}\n        />\n      );\n    /**\n     * Enumerations are a special case because they require options.\n     */\n    case 'enumeration':\n      return (\n        <FormInputRenderer\n          {...props}\n          hint={hint}\n          labelAction={customLabelAction}\n          options={props.attribute.enum.map((value) => ({ value }))}\n          // @ts-expect-error – Temp workaround so we don't forget custom-fields don't work!\n          type={props.customField ? 'custom-field' : props.type}\n          disabled={fieldIsDisabled}\n        />\n      );\n    default:\n      // These props are not needed for the generic form input renderer.\n      const { unique: _unique, mainField: _mainField, ...restProps } = props;\n      return (\n        <FormInputRenderer\n          {...restProps}\n          hint={hint}\n          labelAction={customLabelAction}\n          // @ts-expect-error – Temp workaround so we don't forget custom-fields don't work!\n          type={props.customField ? 'custom-field' : props.type}\n          disabled={fieldIsDisabled}\n        />\n      );\n  }\n};\n\nconst attributeHasCustomFieldProperty = (\n  attribute: Schema.Attribute.AnyAttribute\n): attribute is Schema.Attribute.AnyAttribute & Schema.Attribute.CustomField<string> =>\n  'customField' in attribute && typeof attribute.customField === 'string';\n\nexport type { VersionInputRendererProps };\nexport { VersionInputRenderer };\n", "import * as React from 'react';\n\nimport { Form, Layouts } from '@strapi/admin/strapi-admin';\nimport { Box, Divider, Flex, Grid, Typography } from '@strapi/design-system';\nimport { Schema } from '@strapi/types';\nimport pipe from 'lodash/fp/pipe';\nimport { useIntl } from 'react-intl';\n\nimport { useDoc } from '../../hooks/useDocument';\nimport { useTypedSelector } from '../../modules/hooks';\nimport {\n  prepareTempKeys,\n  removeFieldsThatDontExistOnSchema,\n} from '../../pages/EditView/utils/data';\nimport { HistoryContextValue, useHistoryContext } from '../pages/History';\n\nimport { VersionInputRenderer } from './VersionInputRenderer';\n\nimport type { Metadatas } from '../../../../shared/contracts/content-types';\nimport type { GetInitData } from '../../../../shared/contracts/init';\nimport type { ComponentsDictionary, Document } from '../../hooks/useDocument';\nimport type { EditFieldLayout } from '../../hooks/useDocumentLayout';\n\nconst createLayoutFromFields = <T extends EditFieldLayout | UnknownField>(fields: T[]) => {\n  return (\n    fields\n      .reduce<Array<T[]>>((rows, field) => {\n        if (field.type === 'dynamiczone') {\n          // Dynamic zones take up all the columns in a row\n          rows.push([field]);\n\n          return rows;\n        }\n\n        if (!rows[rows.length - 1]) {\n          // Create a new row if there isn't one available\n          rows.push([]);\n        }\n\n        // Push fields to the current row, they wrap and handle their own column size\n        rows[rows.length - 1].push(field);\n\n        return rows;\n      }, [])\n      // Map the rows to panels\n      .map((row) => [row])\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * getRemainingFieldsLayout\n * -----------------------------------------------------------------------------------------------*/\n\ninterface GetRemainingFieldsLayoutOptions\n  extends Pick<HistoryContextValue, 'layout'>,\n    Pick<GetInitData.Response['data'], 'fieldSizes'> {\n  schemaAttributes: HistoryContextValue['schema']['attributes'];\n  metadatas: Metadatas;\n}\n\n/**\n * Build a layout for the fields that are were deleted from the edit view layout\n * via the configure the view page. This layout will be merged with the main one.\n * Those fields would be restored if the user restores the history version, which is why it's\n * important to show them, even if they're not in the normal layout.\n */\nfunction getRemaingFieldsLayout({\n  layout,\n  metadatas,\n  schemaAttributes,\n  fieldSizes,\n}: GetRemainingFieldsLayoutOptions) {\n  const fieldsInLayout = layout.flatMap((panel) =>\n    panel.flatMap((row) => row.flatMap((field) => field.name))\n  );\n  const remainingFields = Object.entries(metadatas).reduce<EditFieldLayout[]>(\n    (currentRemainingFields, [name, field]) => {\n      // Make sure we do not fields that are not visible, e.g. \"id\"\n      if (!fieldsInLayout.includes(name) && field.edit.visible === true) {\n        const attribute = schemaAttributes[name];\n        // @ts-expect-error not sure why attribute causes type error\n        currentRemainingFields.push({\n          attribute,\n          type: attribute.type,\n          visible: true,\n          disabled: true,\n          label: field.edit.label || name,\n          name: name,\n          size: fieldSizes[attribute.type].default ?? 12,\n        });\n      }\n\n      return currentRemainingFields;\n    },\n    []\n  );\n\n  return createLayoutFromFields(remainingFields);\n}\n\n/* -------------------------------------------------------------------------------------------------\n * FormPanel\n * -----------------------------------------------------------------------------------------------*/\n\nconst FormPanel = ({ panel }: { panel: EditFieldLayout[][] }) => {\n  if (panel.some((row) => row.some((field) => field.type === 'dynamiczone'))) {\n    const [row] = panel;\n    const [field] = row;\n\n    return (\n      <Grid.Root key={field.name} gap={4}>\n        <Grid.Item col={12} s={12} xs={12} direction=\"column\" alignItems=\"stretch\">\n          <VersionInputRenderer {...field} />\n        </Grid.Item>\n      </Grid.Root>\n    );\n  }\n\n  return (\n    <Box\n      hasRadius\n      background=\"neutral0\"\n      shadow=\"tableShadow\"\n      paddingLeft={6}\n      paddingRight={6}\n      paddingTop={6}\n      paddingBottom={6}\n      borderColor=\"neutral150\"\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n        {panel.map((row, gridRowIndex) => (\n          <Grid.Root key={gridRowIndex} gap={4}>\n            {row.map(({ size, ...field }) => {\n              return (\n                <Grid.Item\n                  col={size}\n                  key={field.name}\n                  s={12}\n                  xs={12}\n                  direction=\"column\"\n                  alignItems=\"stretch\"\n                >\n                  <VersionInputRenderer {...field} />\n                </Grid.Item>\n              );\n            })}\n          </Grid.Root>\n        ))}\n      </Flex>\n    </Box>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * VersionContent\n * -----------------------------------------------------------------------------------------------*/\n\ntype UnknownField = EditFieldLayout & { shouldIgnoreRBAC: boolean };\n\nconst VersionContent = () => {\n  const { formatMessage } = useIntl();\n  const { fieldSizes } = useTypedSelector((state) => state['content-manager'].app);\n  const version = useHistoryContext('VersionContent', (state) => state.selectedVersion);\n  const layout = useHistoryContext('VersionContent', (state) => state.layout);\n  const configuration = useHistoryContext('VersionContent', (state) => state.configuration);\n  const schema = useHistoryContext('VersionContent', (state) => state.schema);\n\n  // Build a layout for the unknown fields section\n  const removedAttributes = version.meta.unknownAttributes.removed;\n  const removedAttributesAsFields = Object.entries(removedAttributes).map(\n    ([attributeName, attribute]) => {\n      const field = {\n        attribute,\n        shouldIgnoreRBAC: true,\n        type: attribute.type,\n        visible: true,\n        disabled: true,\n        label: attributeName,\n        name: attributeName,\n        size: fieldSizes[attribute.type].default ?? 12,\n      } as UnknownField;\n\n      return field;\n    }\n  );\n  const unknownFieldsLayout = createLayoutFromFields(removedAttributesAsFields);\n\n  // Build a layout for the fields that are were deleted from the layout\n  const remainingFieldsLayout = getRemaingFieldsLayout({\n    metadatas: configuration.contentType.metadatas,\n    layout,\n    schemaAttributes: schema.attributes,\n    fieldSizes,\n  });\n\n  const { components } = useDoc();\n\n  /**\n   * Transform the data before passing it to the form so that each field\n   * has a uniquely generated key\n   */\n  const transformedData = React.useMemo(() => {\n    const transform =\n      (schemaAttributes: Schema.Attributes, components: ComponentsDictionary = {}) =>\n      (document: Omit<Document, 'id'>) => {\n        const schema = { attributes: schemaAttributes };\n        const transformations = pipe(\n          removeFieldsThatDontExistOnSchema(schema),\n          prepareTempKeys(schema, components)\n        );\n        return transformations(document);\n      };\n\n    return transform(version.schema, components)(version.data);\n  }, [components, version.data, version.schema]);\n\n  return (\n    <Layouts.Content>\n      <Box paddingBottom={8}>\n        <Form disabled={true} method=\"PUT\" initialValues={transformedData}>\n          <Flex direction=\"column\" alignItems=\"stretch\" gap={6} position=\"relative\">\n            {[...layout, ...remainingFieldsLayout].map((panel, index) => {\n              return <FormPanel key={index} panel={panel} />;\n            })}\n          </Flex>\n        </Form>\n      </Box>\n      {removedAttributesAsFields.length > 0 && (\n        <>\n          <Divider />\n          <Box paddingTop={8}>\n            <Flex direction=\"column\" alignItems=\"flex-start\" paddingBottom={6} gap={1}>\n              <Typography variant=\"delta\">\n                {formatMessage({\n                  id: 'content-manager.history.content.unknown-fields.title',\n                  defaultMessage: 'Unknown fields',\n                })}\n              </Typography>\n              <Typography variant=\"pi\">\n                {formatMessage(\n                  {\n                    id: 'content-manager.history.content.unknown-fields.message',\n                    defaultMessage:\n                      'These fields have been deleted or renamed in the Content-Type Builder. <b>These fields will not be restored.</b>',\n                  },\n                  {\n                    b: (chunks: React.ReactNode) => (\n                      <Typography variant=\"pi\" fontWeight=\"bold\">\n                        {chunks}\n                      </Typography>\n                    ),\n                  }\n                )}\n              </Typography>\n            </Flex>\n            <Form disabled={true} method=\"PUT\" initialValues={version.data}>\n              <Flex direction=\"column\" alignItems=\"stretch\" gap={6} position=\"relative\">\n                {unknownFieldsLayout.map((panel, index) => {\n                  return <FormPanel key={index} panel={panel} />;\n                })}\n              </Flex>\n            </Form>\n          </Box>\n        </>\n      )}\n    </Layouts.Content>\n  );\n};\n\nexport { VersionContent, getRemaingFieldsLayout };\n", "import { Data } from '@strapi/types';\n\nimport {\n  GetHistoryVersions,\n  RestoreHistoryVersion,\n} from '../../../../shared/contracts/history-versions';\nimport { COLLECTION_TYPES } from '../../constants/collections';\nimport { contentManagerApi } from '../../services/api';\n\ninterface RestoreVersion extends RestoreHistoryVersion.Request {\n  documentId: Data.ID;\n  collectionType?: string;\n}\n\nconst historyVersionsApi = contentManagerApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getHistoryVersions: builder.query<\n      GetHistoryVersions.Response,\n      GetHistoryVersions.Request['query']\n    >({\n      query(params) {\n        return {\n          url: `/content-manager/history-versions`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        };\n      },\n      providesTags: ['HistoryVersion'],\n    }),\n    restoreVersion: builder.mutation<RestoreHistoryVersion.Response, RestoreVersion>({\n      query({ params, body }) {\n        return {\n          url: `/content-manager/history-versions/${params.versionId}/restore`,\n          method: 'PUT',\n          data: body,\n        };\n      },\n      invalidatesTags: (_res, _error, { documentId, collectionType, params }) => {\n        return [\n          'HistoryVersion',\n          {\n            type: 'Document',\n            id:\n              collectionType === COLLECTION_TYPES\n                ? `${params.contentType}_${documentId}`\n                : params.contentType,\n          },\n        ];\n      },\n    }),\n  }),\n});\n\nconst { useGetHistoryVersionsQuery, useRestoreVersionMutation } = historyVersionsApi;\n\nexport { useGetHistoryVersionsQuery, useRestoreVersionMutation };\n", "import * as React from 'react';\n\nimport {\n  ConfirmDialog,\n  useNotification,\n  useQueryParams,\n  useRBAC,\n  Layouts,\n} from '@strapi/admin/strapi-admin';\nimport { Button, Typography, Flex, Link, Dialog } from '@strapi/design-system';\nimport { ArrowLeft, WarningCircle } from '@strapi/icons';\nimport { UID } from '@strapi/types';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useNavigate, useParams, type To } from 'react-router-dom';\n\nimport { PERMISSIONS } from '../../constants/plugin';\nimport { useHistoryContext } from '../pages/History';\nimport { useRestoreVersionMutation } from '../services/historyVersion';\n\ninterface VersionHeaderProps {\n  headerId: string;\n}\n\nexport const VersionHeader = ({ headerId }: VersionHeaderProps) => {\n  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = React.useState(false);\n  const navigate = useNavigate();\n  const { formatMessage, formatDate } = useIntl();\n  const { toggleNotification } = useNotification();\n  const [{ query }] = useQueryParams<{\n    plugins?: Record<string, unknown>;\n  }>();\n  const { collectionType, slug } = useParams<{ collectionType: string; slug: UID.ContentType }>();\n  const [restoreVersion, { isLoading }] = useRestoreVersionMutation();\n  const { allowedActions } = useRBAC(PERMISSIONS.map((action) => ({ action, subject: slug })));\n\n  const version = useHistoryContext('VersionHeader', (state) => state.selectedVersion);\n  const mainField = useHistoryContext('VersionHeader', (state) => state.mainField);\n  const schema = useHistoryContext('VersionHeader', (state) => state.schema);\n  const isCurrentVersion = useHistoryContext(\n    'VersionHeader',\n    (state) => state.page === 1 && state.versions.data[0].id === state.selectedVersion.id\n  );\n\n  const mainFieldValue = version.data[mainField];\n\n  const getNextNavigation = (): To => {\n    const pluginsQueryParams = stringify({ plugins: query.plugins }, { encode: false });\n\n    return {\n      pathname: '..',\n      search: pluginsQueryParams,\n    };\n  };\n\n  const handleRestore = async () => {\n    try {\n      const response = await restoreVersion({\n        documentId: version.relatedDocumentId,\n        collectionType,\n        params: {\n          versionId: version.id,\n          contentType: version.contentType,\n        },\n        body: { contentType: version.contentType },\n      });\n\n      if ('data' in response) {\n        navigate(getNextNavigation(), { relative: 'path' });\n\n        toggleNotification({\n          type: 'success',\n          title: formatMessage({\n            id: 'content-manager.restore.success.title',\n            defaultMessage: 'Version restored.',\n          }),\n          message: formatMessage({\n            id: 'content-manager.restore.success.message',\n            defaultMessage: 'The content of the restored version is not published yet.',\n          }),\n        });\n      }\n\n      if ('error' in response) {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({\n            id: 'content-manager.history.restore.error.message',\n            defaultMessage: 'Could not restore version.',\n          }),\n        });\n      }\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({ id: 'notification.error', defaultMessage: 'An error occurred' }),\n      });\n    }\n  };\n\n  return (\n    <Dialog.Root open={isConfirmDialogOpen} onOpenChange={setIsConfirmDialogOpen}>\n      <Layouts.BaseHeader\n        id={headerId}\n        title={formatDate(new Date(version.createdAt), {\n          year: 'numeric',\n          month: 'numeric',\n          day: 'numeric',\n          hour: 'numeric',\n          minute: 'numeric',\n        })}\n        subtitle={\n          <Typography variant=\"epsilon\" textColor=\"neutral600\">\n            {formatMessage(\n              {\n                id: 'content-manager.history.version.subtitle',\n                defaultMessage:\n                  '{hasLocale, select, true {{subtitle}, in {locale}} other {{subtitle}}}',\n              },\n              {\n                hasLocale: Boolean(version.locale),\n                subtitle: `${mainFieldValue || ''} (${schema.info.singularName})`.trim(),\n                locale: version.locale?.name,\n              }\n            )}\n          </Typography>\n        }\n        navigationAction={\n          <Link\n            startIcon={<ArrowLeft />}\n            tag={NavLink}\n            to={getNextNavigation()}\n            relative=\"path\"\n            isExternal={false}\n          >\n            {formatMessage({\n              id: 'global.back',\n              defaultMessage: 'Back',\n            })}\n          </Link>\n        }\n        sticky={false}\n        primaryAction={\n          <Dialog.Trigger>\n            <Button\n              disabled={!allowedActions.canUpdate || isCurrentVersion}\n              onClick={() => {\n                setIsConfirmDialogOpen(true);\n              }}\n            >\n              {formatMessage({\n                id: 'content-manager.history.restore.confirm.button',\n                defaultMessage: 'Restore',\n              })}\n            </Button>\n          </Dialog.Trigger>\n        }\n      />\n      <ConfirmDialog\n        onConfirm={handleRestore}\n        endAction={\n          <Button variant=\"secondary\" onClick={handleRestore} loading={isLoading}>\n            {formatMessage({\n              id: 'content-manager.history.restore.confirm.button',\n              defaultMessage: 'Restore',\n            })}\n          </Button>\n        }\n      >\n        <Flex\n          direction=\"column\"\n          alignItems=\"center\"\n          justifyContent=\"center\"\n          gap={2}\n          textAlign=\"center\"\n        >\n          <Flex justifyContent=\"center\">\n            <WarningCircle width=\"24px\" height=\"24px\" fill=\"danger600\" />\n          </Flex>\n          <Typography>\n            {formatMessage({\n              id: 'content-manager.history.restore.confirm.title',\n              defaultMessage: 'Are you sure you want to restore this version?',\n            })}\n          </Typography>\n          <Typography>\n            {formatMessage(\n              {\n                id: 'content-manager.history.restore.confirm.message',\n                defaultMessage:\n                  \"{isDraft, select, true {The restored content will override your draft.} other {The restored content won't be published, it will override the draft and be saved as pending changes. You'll be able to publish the changes at anytime.}}\",\n              },\n              {\n                isDraft: version.status === 'draft',\n              }\n            )}\n          </Typography>\n        </Flex>\n      </ConfirmDialog>\n    </Dialog.Root>\n  );\n};\n", "import * as React from 'react';\n\nimport { useQueryParams } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, Typography, type BoxProps } from '@strapi/design-system';\nimport { stringify } from 'qs';\nimport { type MessageDescriptor, useIntl } from 'react-intl';\nimport { Link } from 'react-router-dom';\n\nimport { RelativeTime } from '../../components/RelativeTime';\nimport { getDisplayName } from '../../utils/users';\nimport { useHistoryContext } from '../pages/History';\n\nimport type { HistoryVersions } from '../../../../shared/contracts';\n\n/* -------------------------------------------------------------------------------------------------\n * BlueText\n * -----------------------------------------------------------------------------------------------*/\n\nconst BlueText = (children: React.ReactNode) => (\n  <Typography textColor=\"primary600\" variant=\"pi\">\n    {children}\n  </Typography>\n);\n\n/* -------------------------------------------------------------------------------------------------\n * VersionCard\n * -----------------------------------------------------------------------------------------------*/\n\ninterface StatusData {\n  background: BoxProps['background'];\n  border: BoxProps['borderColor'];\n  text: BoxProps['color'];\n  message: MessageDescriptor;\n}\n\ninterface VersionCardProps {\n  version: HistoryVersions.HistoryVersionDataResponse;\n  isCurrent: boolean;\n}\n\nconst VersionCard = ({ version, isCurrent }: VersionCardProps) => {\n  const { formatDate, formatMessage } = useIntl();\n  const [{ query }] = useQueryParams<{ id?: string }>();\n\n  const statusData = ((): StatusData => {\n    switch (version.status) {\n      case 'draft':\n        return {\n          background: 'secondary100',\n          border: 'secondary200',\n          text: 'secondary700',\n          message: {\n            id: 'content-manager.containers.List.draft',\n            defaultMessage: 'Draft',\n          },\n        };\n      case 'modified':\n        return {\n          background: 'alternative100',\n          border: 'alternative200',\n          text: 'alternative700',\n          message: {\n            id: 'content-manager.containers.List.modified',\n            defaultMessage: 'Modified',\n          },\n        };\n      case 'published':\n      default:\n        return {\n          background: 'success100',\n          border: 'success200',\n          text: 'success700',\n          message: {\n            id: 'content-manager.containers.List.published',\n            defaultMessage: 'Published',\n          },\n        };\n    }\n  })();\n  const isActive = query.id === version.id.toString();\n  const author = version.createdBy && getDisplayName(version.createdBy);\n\n  return (\n    <Flex\n      direction=\"column\"\n      alignItems=\"flex-start\"\n      gap={3}\n      hasRadius\n      borderWidth=\"1px\"\n      borderStyle=\"solid\"\n      borderColor={isActive ? 'primary600' : 'neutral200'}\n      color=\"neutral800\"\n      padding={5}\n      tag={Link}\n      to={`?${stringify({ ...query, id: version.id })}`}\n      style={{ textDecoration: 'none' }}\n    >\n      <Flex direction=\"column\" gap={1} alignItems=\"flex-start\">\n        <Typography tag=\"h3\" fontWeight=\"semiBold\">\n          {formatDate(version.createdAt, {\n            day: 'numeric',\n            month: 'numeric',\n            year: 'numeric',\n            hour: '2-digit',\n            minute: '2-digit',\n          })}\n        </Typography>\n        <Typography tag=\"p\" variant=\"pi\" textColor=\"neutral600\">\n          {formatMessage(\n            {\n              id: 'content-manager.history.sidebar.versionDescription',\n              defaultMessage:\n                '{distanceToNow}{isAnonymous, select, true {} other { by {author}}}{isCurrent, select, true { <b>(current)</b>} other {}}',\n            },\n            {\n              distanceToNow: <RelativeTime timestamp={new Date(version.createdAt)} />,\n              author,\n              isAnonymous: !Boolean(version.createdBy),\n              isCurrent,\n              b: BlueText,\n            }\n          )}\n        </Typography>\n      </Flex>\n      {version.status && (\n        <Box\n          background={statusData.background}\n          borderStyle=\"solid\"\n          borderWidth=\"1px\"\n          borderColor={statusData.border}\n          hasRadius\n          paddingLeft=\"6px\"\n          paddingRight=\"6px\"\n          paddingTop=\"2px\"\n          paddingBottom=\"2px\"\n        >\n          <Typography variant=\"pi\" fontWeight=\"bold\" textColor={statusData.text}>\n            {formatMessage(statusData.message)}\n          </Typography>\n        </Box>\n      )}\n    </Flex>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PaginationButton\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PaginationButtonProps {\n  page: number;\n  children: React.ReactNode;\n}\n\nconst PaginationButton = ({ page, children }: PaginationButtonProps) => {\n  const [{ query }] = useQueryParams<{ id?: string }>();\n\n  // Remove the id from the pagination link, so that the history page can redirect\n  // to the id of the first history version in the new page once it's loaded\n  const { id: _id, ...queryRest } = query;\n\n  return (\n    <Link to={{ search: stringify({ ...queryRest, page }) }} style={{ textDecoration: 'none' }}>\n      <Typography variant=\"omega\" textColor=\"primary600\">\n        {children}\n      </Typography>\n    </Link>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * VersionsList\n * -----------------------------------------------------------------------------------------------*/\n\nconst VersionsList = () => {\n  const { formatMessage } = useIntl();\n  const { versions, page } = useHistoryContext('VersionsList', (state) => ({\n    versions: state.versions,\n    page: state.page,\n  }));\n\n  return (\n    <Flex\n      shrink={0}\n      direction=\"column\"\n      alignItems=\"stretch\"\n      width=\"320px\"\n      height=\"100vh\"\n      background=\"neutral0\"\n      borderColor=\"neutral200\"\n      borderWidth=\"0 0 0 1px\"\n      borderStyle=\"solid\"\n      tag=\"aside\"\n    >\n      <Flex\n        direction=\"row\"\n        justifyContent=\"space-between\"\n        padding={4}\n        borderColor=\"neutral200\"\n        borderWidth=\"0 0 1px\"\n        borderStyle=\"solid\"\n        tag=\"header\"\n      >\n        <Typography tag=\"h2\" variant=\"omega\" fontWeight=\"semiBold\">\n          {formatMessage({\n            id: 'content-manager.history.sidebar.title',\n            defaultMessage: 'Versions',\n          })}\n        </Typography>\n        <Box background=\"neutral150\" hasRadius padding={1}>\n          <Typography variant=\"sigma\" textColor=\"neutral600\">\n            {versions.meta.pagination.total}\n          </Typography>\n        </Box>\n      </Flex>\n      <Box flex={1} overflow=\"auto\">\n        {versions.meta.pagination.page > 1 && (\n          <Box paddingTop={4} textAlign=\"center\">\n            <PaginationButton page={page - 1}>\n              {formatMessage({\n                id: 'content-manager.history.sidebar.show-newer',\n                defaultMessage: 'Show newer versions',\n              })}\n            </PaginationButton>\n          </Box>\n        )}\n        <Flex direction=\"column\" gap={3} padding={4} tag=\"ul\" alignItems=\"stretch\">\n          {versions.data.map((version, index) => (\n            <li\n              key={version.id}\n              aria-label={formatMessage({\n                id: 'content-manager.history.sidebar.title.version-card.aria-label',\n                defaultMessage: 'Version card',\n              })}\n            >\n              <VersionCard version={version} isCurrent={page === 1 && index === 0} />\n            </li>\n          ))}\n        </Flex>\n        {versions.meta.pagination.page < versions.meta.pagination.pageCount && (\n          <Box paddingBottom={4} textAlign=\"center\">\n            <PaginationButton page={page + 1}>\n              {formatMessage({\n                id: 'content-manager.history.sidebar.show-older',\n                defaultMessage: 'Show older versions',\n              })}\n            </PaginationButton>\n          </Box>\n        )}\n      </Box>\n    </Flex>\n  );\n};\n\nexport { VersionsList };\n", "import * as React from 'react';\n\nimport { useQ<PERSON>yParams, Page, createContext, useRBAC } from '@strapi/admin/strapi-admin';\nimport { Box, Flex, FocusTrap, Main, Portal, Link } from '@strapi/design-system';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Navigate, useParams, NavLink } from 'react-router-dom';\n\nimport { COLLECTION_TYPES } from '../../constants/collections';\nimport { PERMISSIONS } from '../../constants/plugin';\nimport { DocumentRBAC } from '../../features/DocumentRBAC';\nimport { useDocument } from '../../hooks/useDocument';\nimport { type EditLayout, useDocumentLayout } from '../../hooks/useDocumentLayout';\nimport { useGetContentTypeConfigurationQuery } from '../../services/contentTypes';\nimport { buildValidParams } from '../../utils/api';\nimport { VersionContent } from '../components/VersionContent';\nimport { VersionHeader } from '../components/VersionHeader';\nimport { VersionsList } from '../components/VersionsList';\nimport { useGetHistoryVersionsQuery } from '../services/historyVersion';\n\nimport type {\n  ContentType,\n  FindContentTypeConfiguration,\n} from '../../../../shared/contracts/content-types';\nimport type {\n  HistoryVersionDataResponse,\n  GetHistoryVersions,\n} from '../../../../shared/contracts/history-versions';\nimport type { UID } from '@strapi/types';\n\n/* -------------------------------------------------------------------------------------------------\n * HistoryProvider\n * -----------------------------------------------------------------------------------------------*/\n\ninterface HistoryContextValue {\n  contentType: UID.ContentType;\n  id?: string; // null for single types\n  layout: EditLayout['layout'];\n  configuration: FindContentTypeConfiguration.Response['data'];\n  selectedVersion: HistoryVersionDataResponse;\n  // Errors are handled outside of the provider, so we exclude errors from the response type\n  versions: Extract<GetHistoryVersions.Response, { data: Array<HistoryVersionDataResponse> }>;\n  page: number;\n  mainField: string;\n  schema: ContentType;\n}\n\nconst [HistoryProvider, useHistoryContext] = createContext<HistoryContextValue>('HistoryPage');\n\n/* -------------------------------------------------------------------------------------------------\n * HistoryPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst HistoryPage = () => {\n  const headerId = React.useId();\n  const { formatMessage } = useIntl();\n  const {\n    slug,\n    id: documentId,\n    collectionType,\n  } = useParams<{\n    collectionType: string;\n    slug: UID.ContentType;\n    id: string;\n  }>();\n\n  const { isLoading: isLoadingDocument, schema } = useDocument({\n    collectionType: collectionType!,\n    model: slug!,\n  });\n\n  const {\n    isLoading: isLoadingLayout,\n    edit: {\n      layout,\n      settings: { displayName, mainField },\n    },\n  } = useDocumentLayout(slug!);\n  const { data: configuration, isLoading: isLoadingConfiguration } =\n    useGetContentTypeConfigurationQuery(slug!);\n\n  // Parse state from query params\n  const [{ query }] = useQueryParams<{\n    page?: number;\n    id?: string;\n    plugins?: Record<string, unknown>;\n  }>();\n  const { id: selectedVersionId, ...queryWithoutId } = query;\n  const validQueryParamsWithoutId = buildValidParams(queryWithoutId);\n  const page = validQueryParamsWithoutId.page ? Number(validQueryParamsWithoutId.page) : 1;\n\n  const versionsResponse = useGetHistoryVersionsQuery(\n    {\n      contentType: slug!,\n      ...(documentId ? { documentId } : {}),\n      // Omit id since it's not needed by the endpoint and caused extra refetches\n      ...validQueryParamsWithoutId,\n    },\n    { refetchOnMountOrArgChange: true }\n  );\n\n  /**\n   * When the page is first mounted, if there's already data in the cache, RTK has a fullfilled\n   * status for the first render, right before it triggers a new request. This means the code\n   * briefly reaches the part that redirects to the first history version (if none is set).\n   * But since that data is stale, that means auto-selecting a version that may not be the most\n   * recent. To avoid this, we identify through requestId if the query is stale despite the\n   * fullfilled status, and show the loader in that case.\n   * This means we essentially don't want cache. We always refetch when the page mounts, and\n   * we always show the loader until we have the most recent data. That's fine for this page.\n   */\n  const initialRequestId = React.useRef(versionsResponse.requestId);\n  const isStaleRequest = versionsResponse.requestId === initialRequestId.current;\n\n  /**\n   * Ensure that we have the necessary data to render the page:\n   * - slug for single types\n   * - slug _and_ documentId for collection types\n   */\n  if (!slug || (collectionType === COLLECTION_TYPES && !documentId)) {\n    return <Navigate to=\"/content-manager\" />;\n  }\n\n  if (\n    isLoadingDocument ||\n    isLoadingLayout ||\n    versionsResponse.isFetching ||\n    isStaleRequest ||\n    isLoadingConfiguration\n  ) {\n    return <Page.Loading />;\n  }\n\n  // It was a success, handle empty data\n  if (!versionsResponse.isError && !versionsResponse.data?.data?.length) {\n    return (\n      <>\n        <Page.NoData\n          action={\n            <Link\n              tag={NavLink}\n              to={`/content-manager/${collectionType}/${slug}${documentId ? `/${documentId}` : ''}`}\n            >\n              {formatMessage({\n                id: 'global.back',\n                defaultMessage: 'Back',\n              })}\n            </Link>\n          }\n        />\n      </>\n    );\n  }\n\n  // We have data, handle selected version\n  if (versionsResponse.data?.data?.length && !selectedVersionId) {\n    return (\n      <Navigate\n        to={{ search: stringify({ ...query, id: versionsResponse.data.data[0].id }) }}\n        replace\n      />\n    );\n  }\n\n  const selectedVersion = versionsResponse.data?.data?.find(\n    (version) => version.id.toString() === selectedVersionId\n  );\n  if (\n    versionsResponse.isError ||\n    !layout ||\n    !schema ||\n    !selectedVersion ||\n    !configuration ||\n    // This should not happen as it's covered by versionsResponse.isError, but we need it for TS\n    versionsResponse.data.error\n  ) {\n    return <Page.Error />;\n  }\n\n  return (\n    <>\n      <Page.Title>\n        {formatMessage(\n          {\n            id: 'content-manager.history.page-title',\n            defaultMessage: '{contentType} history',\n          },\n          {\n            contentType: displayName,\n          }\n        )}\n      </Page.Title>\n      <HistoryProvider\n        contentType={slug}\n        id={documentId}\n        schema={schema}\n        layout={layout}\n        configuration={configuration}\n        selectedVersion={selectedVersion}\n        versions={versionsResponse.data}\n        page={page}\n        mainField={mainField}\n      >\n        <Flex direction=\"row\" alignItems=\"flex-start\">\n          <Main\n            grow={1}\n            height=\"100vh\"\n            background=\"neutral100\"\n            paddingBottom={6}\n            overflow=\"auto\"\n            labelledBy={headerId}\n          >\n            <VersionHeader headerId={headerId} />\n            <VersionContent />\n          </Main>\n          <VersionsList />\n        </Flex>\n      </HistoryProvider>\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedHistoryPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedHistoryPageImpl = () => {\n  const { slug } = useParams<{\n    slug: string;\n  }>();\n  const {\n    permissions = [],\n    isLoading,\n    error,\n  } = useRBAC(PERMISSIONS.map((action) => ({ action, subject: slug })));\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (error || !slug) {\n    return (\n      <Box\n        height=\"100vh\"\n        width=\"100vw\"\n        position=\"fixed\"\n        top={0}\n        left={0}\n        zIndex={2}\n        background=\"neutral0\"\n      >\n        <Page.Error />\n      </Box>\n    );\n  }\n\n  return (\n    <Box\n      height=\"100vh\"\n      width=\"100vw\"\n      position=\"fixed\"\n      top={0}\n      left={0}\n      zIndex={2}\n      background=\"neutral0\"\n    >\n      <Page.Protect permissions={permissions}>\n        {({ permissions }) => (\n          <DocumentRBAC permissions={permissions}>\n            <HistoryPage />\n          </DocumentRBAC>\n        )}\n      </Page.Protect>\n    </Box>\n  );\n};\n\nconst ProtectedHistoryPage = () => {\n  return (\n    <Portal>\n      <FocusTrap>\n        <ProtectedHistoryPageImpl />\n      </FocusTrap>\n    </Portal>\n  );\n};\n\nexport { ProtectedHistoryPage, HistoryProvider, useHistoryContext };\nexport type { HistoryContextValue };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA0CA,IAAM,cAAc,GAAO,KAAK,EAAE,MAAM,EAAE,YAAY,SAAS,SAAS,MAAM;AAAC,GAAG,QAAQ,OAAA,CAAQ;;;;;AAUlG,IAAM,eAAe,GAAOA,KAAI;;;;;;;;;;AAWhC,IAAM,sBAAsB,CAAC,UAA+B;AACpD,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,QAAQ,SAEZ,MAAM,IAAI;AAMR,MAAA;AACJ,MAAI,OAAO;AACT,0BAAsB,MAAM,QAAQ,MAAM,KAAK,IAC3C,EAAE,SAAS,MAAM,OAAO,MAAM,EAAE,cAAc,EAAE,EAAA,IAChD,MAAM;EACZ;AAGE,MAAA,CAAC,uBACA,oBAAoB,QAAQ,WAAW,KAAK,oBAAoB,KAAK,iBAAiB,GACvF;AACA,eAEI,yBAAA,6BAAA,EAAA,UAAA;UAAA,wBAAC,MAAM,OAAN,EAAY,QAAQ,MAAM,aAAc,UAAA,MAAM,MAAA,CAAM;UACrD,wBAAC,KAAA,EAAI,WAAW,GAEd,cAAA,wBAAC,aAAY,EAAA,SAAQ,WAClB,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EAAA,CACH,EACF,CAAA;IACF,EAAA,CAAA;EAEJ;AAEM,QAAA,EAAE,SAAS,KAAS,IAAA;AAE1B,aAAA,yBACG,KACC,EAAA,UAAA;QAAA,wBAAC,MAAM,OAAN,EAAa,UAAA,MAAM,MAAA,CAAM;IACzB,QAAQ,SAAS,SACf,wBAAA,MAAA,EAAK,WAAU,UAAS,KAAK,GAAG,WAAW,GAAG,YAAW,WACvD,UAAQ,QAAA,IAAI,CAAC,iBAAiB;AAEvB,YAAA,EAAE,YAAY,IAAI,MAAM;AAC9B,YAAM,OAAO,MAAM,gBAAgB,IAAI,WAAW,IAAI,aAAa,UAAU;AAC7E,YAAM,QAAQ,iBAAiB,cAAc,MAAM,SAAS;AAC5D,YAAM,sBAAsB,gBAAgB;AAG1C,iBAAA;QAAC;QAAA;UAEC,YAAY;UACZ,eAAe;UACf,aAAa;UACb,cAAc;UACd,WAAS;UACT,aAAY;UACZ,YAAW;UACX,gBAAe;UAEf,UAAA;gBAAC,wBAAA,KAAA,EAAI,UAAU,GAAG,YAAY,GAAG,eAAe,GAAG,cAAc,GAC/D,cAAA,wBAAC,aAAQ,EAAA,OACN,UAAA,0BACE,wBAAA,YAAA,EAAY,UAAM,MAAA,CAAA,QAElB,wBAAA,cAAA,EAAa,KAAK,SAAS,IAAI,MAC7B,UACH,MAAA,CAAA,EAEJ,CAAA,EAAA,CACF;gBACC,wBAAA,gBAAA,EAAe,QAAQ,aAAa,OAAkB,CAAA;UAAA;QAAA;QArBlD,aAAa,cAAc,aAAa;MAAA;IAwBlD,CAAA,EAAA,CACH;IAED,KAAK,eAAe;QAEnB;MAAC;MAAA;QACC,WAAW;QACX,SAAQ;QACR,OAAO;UACL;YACE,IAAI;YACJ,gBACE;UACJ;UACA,EAAE,QAAQ,KAAK,aAAa;QAC9B;QAEC,UAAA;UACC;YACE,IAAI;YACJ,gBACE;UACJ;UACA,EAAE,QAAQ,KAAK,aAAa;QAC9B;MAAA;IACF;EAEJ,EAAA,CAAA;AAEJ;AAMA,IAAM,mBAAmB,CAAC,UAAqC;AAC7D,QAAM,EAAE,MAAU,IAAA,SAAS,MAAM,IAAI;AACrC,QAAM,UAAU,QAAQ,MAAM,UAAU,CAAA;AACxC,QAAM,OAAO,QAAQ,MAAM,OAAO,EAAE,cAAc,EAAA;AAC5C,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,QAAM,SAAS,aAAa,oBAAoB,CAAC,UAAU,MAAM,MAAM;AACvE,QAAM,eAAe,OAAO;AAG5B,aAAA,yBACG,MAAK,EAAA,WAAU,UAAS,KAAK,GAAG,YAAW,WAC1C,UAAA;QAAC,wBAAA,MAAA,EAAK,QAAO,OAAM,UAAU,MAAM,eAAe,EAAE,CAAC,MAAM,IAAI,GAAG,QAChE,GAAA,cAAA,wBAAC,cAAc,EAAA,GAAG,OAAO,UAAU,MAAM,UAAU,QAAQ,SAAS,EAAA,CAAG,EACzE,CAAA;IACC,KAAK,eAAe,SACnB;MAAC;MAAA;QACC,SAAQ;QACR,YAAW;QACX,SAAS,MAAM;QAAC;QAChB,OAAO;UACL;YACE,IAAI;YACJ,gBACE;UACJ;UACA,EAAE,QAAQ,KAAK,aAAa;QAC9B;QAEC,UAAA;UACC;YACE,IAAI;YACJ,gBACE;UACJ;UACA,EAAE,QAAQ,KAAK,aAAa;QAC9B;MAAA;IACF;EAEJ,EAAA,CAAA;AAEJ;AAcA,IAAM,iBAAiB,CAAC,gBAA0D;AAChF,MAAI,CAAO,qBAAe,WAAW,GAAG;AAC/B,WAAA;EACT;AAGM,QAAA,qBAAqB,YAAY,MAAM,MAAM;AAEnD,MAAI,uBAAuB,wBAAwB;AAC1C,WAAM,mBAAa,aAAa;MACrC,GAAG,YAAY;MACf,OAAO;QACL,IAAI;QACJ,gBACE;MACJ;IAAA,CACD;EACH;AAEA,MAAI,uBAAuB,4BAA4B;AAC9C,WAAM,mBAAa,aAAa;MACrC,GAAG,YAAY;MACf,OAAO;QACL,IAAI;QACJ,gBACE;MACJ;IAAA,CACD;EACH;AAGO,SAAA;AACT;AASA,IAAM,uBAAuB,CAAC;EAC5B;EACA,MAAM;EACN,mBAAmB;EACnB;EACA,GAAG;AACL,MAAiC;AACzB,QAAA,oBAAoB,eAAe,WAAW;AAE9C,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,UAAU,kBAAkB,kBAAkB,CAAC,UAAU,MAAM,eAAe;AACpF,QAAM,gBAAgB,kBAAkB,kBAAkB,CAAC,UAAU,MAAM,aAAa;AAClF,QAAA,aAAa,iBAAiB,CAAC,UAAU,MAAM,iBAAiB,EAAE,IAAI,UAAU;AAEtF,QAAM,EAAE,IAAI,WAAW,IAAI,OAAO;AAClC,QAAM,iBAAiB,QAAQ,iBAAiB,CAAC,UAAU,MAAM,QAAQ;AAEzE,QAAM,kBAAkB,eAAe,mBAAmB,CAAC,UAAU,MAAM,eAAe;AAE1F,QAAM,kBAAkB,gBAAgB,iBAAiB,CAAC,SAAS,KAAK,eAAe;AACvF,QAAM,gBAAgB,gBAAgB,iBAAiB,CAAC,SAAS,KAAK,aAAa;AACnF,QAAM,kBAAkB,gBAAgB,iBAAiB,CAAC,SAAS,KAAK,eAAe;AACvF,QAAM,gBAAgB,gBAAgB,iBAAiB,CAAC,SAAS,KAAK,aAAa;AAE7E,QAAA,iBAAiB,KAAK,kBAAkB;AACxC,QAAA,iBAAiB,KAAK,gBAAgB;AAK5C,QAAM,mBAAmB,cAAc,MAAM,MAAM,gBAAgB,MAAM,IAAI;AAC7E,QAAM,mBAAmB,cAAc,MAAM,MAAM,gBAAgB,MAAM,IAAI;AAE7E,QAAM,SAAS,aAAa,iBAAiB,CAAC,QAAQ,IAAI,MAAM;AAC1D,QAAA,EAAE,mBAAA,IAAuB;IAC7B,gCAAgC,MAAM,SAAS,IAAI,CAAC,MAAM,UAAU,WAAW,IAAI;EAAA;AAGrF,QAAM,OAAO,aAAa,cAAc,MAAM,SAAS;AACjD,QAAA;IACJ,MAAM,EAAE,YAAY,iBAAiB;EAAA,IACnC,aAAa;AAEjB,MAAI,CAAC,SAAS;AACL,WAAA;EACT;AAKA,MAAI,CAAC,oBAAoB,CAAC,oBAAoB,CAAC,iBAAiB;AAC9D,eAAQ,wBAAA,iBAAA,EAAgB,MAAa,GAAG,MAAO,CAAA;EACjD;AAEA,QAAM,kBACH,CAAC,oBAAoB,CAAC,mBAAoB,MAAM,YAAY;AAMzD,QAAA,kBAAkB,QAAQ,KAAK,kBAAkB;AACvD,MAAI,OAAO,KAAK,eAAe,EAAE,SAAS,MAAM,IAAI,GAAG;AACrD,eAAA,yBACG,MAAK,EAAA,WAAU,UAAS,YAAW,cAAa,KAAK,GACpD,UAAA;UAAA,wBAAC,MAAM,OAAN,EAAa,UAAA,MAAM,MAAA,CAAM;UAC1B;QAAC;QAAA;UACC,OAAM;UACN,YAAW;UACX,SAAS,MAAM;UAAC;UAChB,SAAQ;UACR,OAAO,cAAc;YACnB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UAEA,UAAc,cAAA;YACb,IAAI;YACJ,gBACE;UAAA,CACH;QAAA;MACH;IACF,EAAA,CAAA;EAEJ;AAMI,MAAA,gCAAgC,MAAM,SAAS,GAAG;AACpD,UAAM,cAAc,mBAAmB,MAAM,UAAU,WAAW;AAElE,QAAI,aAAa;AAEb,iBAAA;QAAC;QAAA;UACE,GAAG;UAEJ;UACA,aAAa;UACb,UAAU;QAAA;MAAA;IAGhB;AAGE,eAAA;MAACC;MAAA;QACE,GAAG;QACJ;QACA,aAAa;QAEb,MAAM,MAAM,UAAU;QACtB,UAAU;MAAA;IAAA;EAGhB;AAMI,MAAA,MAAM,SAAS,SAAS;AAC1B,eAAA,wBACG,kBAAkB,EAAA,GAAG,OAAO,aAAa,mBAAmB,UAAU,gBAAiB,CAAA;EAE5F;AAIM,QAAA,kBAAkB,OAAO,KAAK,MAAM;AACtC,MAAA,CAAC,gCAAgC,MAAM,SAAS,KAAK,gBAAgB,SAAS,MAAM,IAAI,GAAG;AACvF,UAAA,cAAc,OAAO,MAAM,IAAI;AAEnC,eAAA;MAAC;MAAA;QACE,GAAG;QAEJ;QACA,aAAa;QACb,UAAU;MAAA;IAAA;EAGhB;AAMA,UAAQ,MAAM,MAAM;IAClB,KAAK;AACI,iBAAA,wBAACC,qBAAAA,EAAa,GAAG,OAAO,MAAY,MAAM,MAAM,MAAM,UAAU,gBAAiB,CAAA;IAC1F,KAAK;AACH,YAAM,EAAE,OAAO,IAAI,iBAAiB,MAAM,UAAU,SAAS;AAEvD,YAAA,CAAC,qBAAqB,IAAI,uBAAuB;QACrD,QAAQ,CAAC,MAAM;QACf,WAAW,cAAc,WAAW,MAAM,UAAU,SAAS,EAAE;QAC/D;QACA,kBAAkB,WAAW,MAAM,UAAU,SAAS,EAAE;MAAA,CACzD;AAGC,iBAAA;QAACC;QAAA;UACE,GAAG;UACJ,QAAQ,CAAC,GAAG,QAAQ,GAAI,yBAAyB,CAAA,CAAG;UACpD;UACA,aAAa;UACb,UAAU;UAET,UAAA,CAAC,mBAAe,wBAAC,sBAAA,EAAsB,GAAG,YAAY,kBAAkB,KAAA,CAAM;QAAA;MAAA;IAGrF,KAAK;AAED,iBAAA;QAAC;QAAA;UACE,GAAG;UACJ;UACA,aAAa;UACb,UAAU;QAAA;MAAA;IAGhB,KAAK;AAED,iBAAA;QAAC;QAAA;UACE,GAAG;UACJ;UACA,aAAa;UACb,UAAU;QAAA;MAAA;IAGhB,KAAK;AAED,iBAAA;QAACC;QAAA;UACE,GAAG;UACJ;UACA,MAAM,MAAM;UACZ,aAAa;UACb,UAAU;QAAA;MAAA;IAGhB,KAAK;AAED,iBAAA;QAACC;QAAA;UACE,GAAG;UACJ;UACA,MAAM,MAAM;UACZ,aAAa;UACb,UAAU;QAAA;MAAA;IAMhB,KAAK;AAED,iBAAA;QAACJ;QAAA;UACE,GAAG;UACJ;UACA,aAAa;UACb,SAAS,MAAM,UAAU,KAAK,IAAI,CAAC,WAAW,EAAE,MAAA,EAAQ;UAExD,MAAM,MAAM,cAAc,iBAAiB,MAAM;UACjD,UAAU;QAAA;MAAA;IAGhB;AAEE,YAAM,EAAE,QAAQ,SAAS,WAAW,YAAY,GAAG,UAAc,IAAA;AAE/D,iBAAA;QAACA;QAAA;UACE,GAAG;UACJ;UACA,aAAa;UAEb,MAAM,MAAM,cAAc,iBAAiB,MAAM;UACjD,UAAU;QAAA;MAAA;EAGlB;AACF;AAEA,IAAM,kCAAkC,CACtC,cAEA,iBAAiB,aAAa,OAAO,UAAU,gBAAgB;AC3ejE,IAAM,yBAAyB,CAA2C,WAAgB;AACxF,SACE,OACG,OAAmB,CAAC,MAAM,UAAU;AAC/B,QAAA,MAAM,SAAS,eAAe;AAE3B,WAAA,KAAK,CAAC,KAAK,CAAC;AAEV,aAAA;IACT;AAEA,QAAI,CAAC,KAAK,KAAK,SAAS,CAAC,GAAG;AAErB,WAAA,KAAK,CAAA,CAAE;IACd;AAGA,SAAK,KAAK,SAAS,CAAC,EAAE,KAAK,KAAK;AAEzB,WAAA;EAAA,GACN,CAAA,CAAE,EAEJ,IAAI,CAAC,QAAQ,CAAC,GAAG,CAAC;AAEzB;AAmBA,SAAS,uBAAuB;EAC9B;EACA;EACA;EACA;AACF,GAAoC;AAClC,QAAM,iBAAiB,OAAO;IAAQ,CAAC,UACrC,MAAM,QAAQ,CAAC,QAAQ,IAAI,QAAQ,CAAC,UAAU,MAAM,IAAI,CAAC;EAAA;AAE3D,QAAM,kBAAkB,OAAO,QAAQ,SAAS,EAAE;IAChD,CAAC,wBAAwB,CAAC,MAAM,KAAK,MAAM;AAErC,UAAA,CAAC,eAAe,SAAS,IAAI,KAAK,MAAM,KAAK,YAAY,MAAM;AAC3D,cAAA,YAAY,iBAAiB,IAAI;AAEvC,+BAAuB,KAAK;UAC1B;UACA,MAAM,UAAU;UAChB,SAAS;UACT,UAAU;UACV,OAAO,MAAM,KAAK,SAAS;UAC3B;UACA,MAAM,WAAW,UAAU,IAAI,EAAE,WAAW;QAAA,CAC7C;MACH;AAEO,aAAA;IACT;IACA,CAAC;EAAA;AAGH,SAAO,uBAAuB,eAAe;AAC/C;AAMA,IAAM,YAAY,CAAC,EAAE,MAAA,MAA4C;AAC/D,MAAI,MAAM,KAAK,CAAC,QAAQ,IAAI,KAAK,CAAC,UAAU,MAAM,SAAS,aAAa,CAAC,GAAG;AACpE,UAAA,CAAC,GAAG,IAAI;AACR,UAAA,CAAC,KAAK,IAAI;AAGd,eAAA,wBAAC,KAAK,MAAL,EAA2B,KAAK,GAC/B,cAAA,wBAAC,KAAK,MAAL,EAAU,KAAK,IAAI,GAAG,IAAI,IAAI,IAAI,WAAU,UAAS,YAAW,WAC/D,cAAA,wBAAC,sBAAsB,EAAA,GAAG,MAAO,CAAA,EACnC,CAAA,EAHc,GAAA,MAAM,IAItB;EAEJ;AAGE,aAAA;IAAC;IAAA;MACC,WAAS;MACT,YAAW;MACX,QAAO;MACP,aAAa;MACb,cAAc;MACd,YAAY;MACZ,eAAe;MACf,aAAY;MAEZ,cAAA,wBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GAChD,UAAA,MAAM,IAAI,CAAC,KAAK,qBAAA,wBACd,KAAK,MAAL,EAA6B,KAAK,GAChC,UAAA,IAAI,IAAI,CAAC,EAAE,MAAM,GAAG,MAAA,MAAY;AAE7B,mBAAA;UAAC,KAAK;UAAL;YACC,KAAK;YAEL,GAAG;YACH,IAAI;YACJ,WAAU;YACV,YAAW;YAEX,cAAA,wBAAC,sBAAsB,EAAA,GAAG,MAAO,CAAA;UAAA;UAN5B,MAAM;QAAA;MAOb,CAEH,EAAA,GAda,YAehB,CACD,EAAA,CACH;IAAA;EAAA;AAGN;AAQA,IAAM,iBAAiB,MAAM;AACrB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,WAAe,IAAA,iBAAiB,CAAC,UAAU,MAAM,iBAAiB,EAAE,GAAG;AAC/E,QAAM,UAAU,kBAAkB,kBAAkB,CAAC,UAAU,MAAM,eAAe;AACpF,QAAM,SAAS,kBAAkB,kBAAkB,CAAC,UAAU,MAAM,MAAM;AAC1E,QAAM,gBAAgB,kBAAkB,kBAAkB,CAAC,UAAU,MAAM,aAAa;AACxF,QAAM,SAAS,kBAAkB,kBAAkB,CAAC,UAAU,MAAM,MAAM;AAGpE,QAAA,oBAAoB,QAAQ,KAAK,kBAAkB;AACzD,QAAM,4BAA4B,OAAO,QAAQ,iBAAiB,EAAE;IAClE,CAAC,CAAC,eAAe,SAAS,MAAM;AAC9B,YAAM,QAAQ;QACZ;QACA,kBAAkB;QAClB,MAAM,UAAU;QAChB,SAAS;QACT,UAAU;QACV,OAAO;QACP,MAAM;QACN,MAAM,WAAW,UAAU,IAAI,EAAE,WAAW;MAAA;AAGvC,aAAA;IACT;EAAA;AAEI,QAAA,sBAAsB,uBAAuB,yBAAyB;AAG5E,QAAM,wBAAwB,uBAAuB;IACnD,WAAW,cAAc,YAAY;IACrC;IACA,kBAAkB,OAAO;IACzB;EAAA,CACD;AAEK,QAAA,EAAE,WAAA,IAAe,OAAA;AAMjB,QAAA,kBAAwB,cAAQ,MAAM;AAC1C,UAAM,YACJ,CAAC,kBAAqCK,cAAmC,CAAC,MAC1E,CAAC,aAAmC;AAC5BC,YAAAA,UAAS,EAAE,YAAY,iBAAA;AAC7B,YAAM,sBAAkB,YAAAC;QACtB,kCAAkCD,OAAM;QACxC,gBAAgBA,SAAQD,WAAU;MAAA;AAEpC,aAAO,gBAAgB,QAAQ;IAAA;AAGnC,WAAO,UAAU,QAAQ,QAAQ,UAAU,EAAE,QAAQ,IAAI;EAAA,GACxD,CAAC,YAAY,QAAQ,MAAM,QAAQ,MAAM,CAAC;AAG3C,aAAA,yBAAC,QAAQ,SAAR,EACC,UAAA;QAAA,wBAAC,KAAI,EAAA,eAAe,GAClB,cAAA,wBAAC,MAAK,EAAA,UAAU,MAAM,QAAO,OAAM,eAAe,iBAChD,cAAA,wBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GAAG,UAAS,YAC5D,UAAC,CAAA,GAAG,QAAQ,GAAG,qBAAqB,EAAE,IAAI,CAAC,OAAO,UAAU;AACpD,iBAAA,wBAAC,WAAsB,EAAA,MAAA,GAAP,KAAqB;IAAA,CAC7C,EACH,CAAA,EACF,CAAA,EAAA,CACF;IACC,0BAA0B,SAAS,SAEhC,yBAAA,6BAAA,EAAA,UAAA;UAAA,wBAAC,SAAQ,CAAA,CAAA;UACT,yBAAC,KAAI,EAAA,YAAY,GACf,UAAA;YAAC,yBAAA,MAAA,EAAK,WAAU,UAAS,YAAW,cAAa,eAAe,GAAG,KAAK,GACtE,UAAA;cAAC,wBAAA,YAAA,EAAW,SAAQ,SACjB,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UACjB,CAAA,EAAA,CACH;cACA,wBAAC,YAAW,EAAA,SAAQ,MACjB,UAAA;YACC;cACE,IAAI;cACJ,gBACE;YACJ;YACA;cACE,GAAG,CAAC,eACF,wBAAC,YAAA,EAAW,SAAQ,MAAK,YAAW,QACjC,UACH,OAAA,CAAA;YAEJ;UAAA,EAAA,CAEJ;QAAA,EAAA,CACF;YACA,wBAAC,MAAA,EAAK,UAAU,MAAM,QAAO,OAAM,eAAe,QAAQ,MACxD,cAAC,wBAAA,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GAAG,UAAS,YAC5D,UAAoB,oBAAA,IAAI,CAAC,OAAO,UAAU;AAClC,qBAAA,wBAAC,WAAsB,EAAA,MAAA,GAAP,KAAqB;QAAA,CAC7C,EAAA,CACH,EACF,CAAA;MAAA,EAAA,CACF;IAAA,EAAA,CACF;EAEJ,EAAA,CAAA;AAEJ;AC7PA,IAAM,qBAAqB,kBAAkB,gBAAgB;EAC3D,WAAW,CAAC,aAAa;IACvB,oBAAoB,QAAQ,MAG1B;MACA,MAAM,QAAQ;AACL,eAAA;UACL,KAAK;UACL,QAAQ;UACR,QAAQ;YACN;UACF;QAAA;MAEJ;MACA,cAAc,CAAC,gBAAgB;IAAA,CAChC;IACD,gBAAgB,QAAQ,SAAyD;MAC/E,MAAM,EAAE,QAAQ,KAAA,GAAQ;AACf,eAAA;UACL,KAAK,qCAAqC,OAAO,SAAS;UAC1D,QAAQ;UACR,MAAM;QAAA;MAEV;MACA,iBAAiB,CAAC,MAAM,QAAQ,EAAE,YAAY,gBAAgB,OAAA,MAAa;AAClE,eAAA;UACL;UACA;YACE,MAAM;YACN,IACE,mBAAmB,mBACf,GAAG,OAAO,WAAW,IAAI,UAAU,KACnC,OAAO;UACf;QAAA;MAEJ;IAAA,CACD;EAAA;AAEL,CAAC;AAED,IAAM,EAAE,4BAA4B,0BAAA,IAA8B;AC/B3D,IAAM,gBAAgB,CAAC,EAAE,SAAA,MAAmC;;AACjE,QAAM,CAAC,qBAAqB,sBAAsB,IAAU,eAAS,KAAK;AAC1E,QAAM,WAAW,YAAA;AACjB,QAAM,EAAE,eAAe,WAAW,IAAI,QAAQ;AACxC,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAEjB;AACH,QAAM,EAAE,gBAAgB,KAAK,IAAI,UAA6D;AAC9F,QAAM,CAAC,gBAAgB,EAAE,UAAU,CAAC,IAAI,0BAA0B;AAClE,QAAM,EAAE,eAAmB,IAAA,QAAQ,YAAY,IAAI,CAAC,YAAY,EAAE,QAAQ,SAAS,KAAA,EAAO,CAAC;AAE3F,QAAM,UAAU,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,eAAe;AACnF,QAAM,YAAY,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,SAAS;AAC/E,QAAM,SAAS,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,MAAM;AACzE,QAAM,mBAAmB;IACvB;IACA,CAAC,UAAU,MAAM,SAAS,KAAK,MAAM,SAAS,KAAK,CAAC,EAAE,OAAO,MAAM,gBAAgB;EAAA;AAG/E,QAAA,iBAAiB,QAAQ,KAAK,SAAS;AAE7C,QAAM,oBAAoB,MAAU;AAC5B,UAAA,yBAAqB,qBAAU,EAAE,SAAS,MAAM,QAAA,GAAW,EAAE,QAAQ,MAAA,CAAO;AAE3E,WAAA;MACL,UAAU;MACV,QAAQ;IAAA;EACV;AAGF,QAAM,gBAAgB,YAAY;AAC5B,QAAA;AACI,YAAA,WAAW,MAAM,eAAe;QACpC,YAAY,QAAQ;QACpB;QACA,QAAQ;UACN,WAAW,QAAQ;UACnB,aAAa,QAAQ;QACvB;QACA,MAAM,EAAE,aAAa,QAAQ,YAAY;MAAA,CAC1C;AAED,UAAI,UAAU,UAAU;AACtB,iBAAS,kBAAkB,GAAG,EAAE,UAAU,OAAQ,CAAA;AAE/B,2BAAA;UACjB,MAAM;UACN,OAAO,cAAc;YACnB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA,CACF;MACH;AAEA,UAAI,WAAW,UAAU;AACJ,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA,CACF;MACH;IAAA,SACO,OAAO;AACK,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,sBAAsB,gBAAgB,oBAAA,CAAqB;MAAA,CACzF;IACH;EAAA;AAGF,aAAA,yBACG,OAAO,MAAP,EAAY,MAAM,qBAAqB,cAAc,wBACpD,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,IAAI;QACJ,OAAO,WAAW,IAAI,KAAK,QAAQ,SAAS,GAAG;UAC7C,MAAM;UACN,OAAO;UACP,KAAK;UACL,MAAM;UACN,QAAQ;QAAA,CACT;QACD,cACG,wBAAA,YAAA,EAAW,SAAQ,WAAU,WAAU,cACrC,UAAA;UACC;YACE,IAAI;YACJ,gBACE;UACJ;UACA;YACE,WAAW,QAAQ,QAAQ,MAAM;YACjC,UAAU,GAAG,kBAAkB,EAAE,KAAK,OAAO,KAAK,YAAY,IAAI,KAAK;YACvE,SAAQ,aAAQ,WAAR,mBAAgB;UAC1B;QAAA,EAAA,CAEJ;QAEF,sBACE;UAACN;UAAA;YACC,eAAA,wBAAY,eAAU,CAAA,CAAA;YACtB,KAAK;YACL,IAAI,kBAAkB;YACtB,UAAS;YACT,YAAY;YAEX,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QACH;QAEF,QAAQ;QACR,mBACE,wBAAC,OAAO,SAAP,EACC,cAAA;UAAC;UAAA;YACC,UAAU,CAAC,eAAe,aAAa;YACvC,SAAS,MAAM;AACb,qCAAuB,IAAI;YAC7B;YAEC,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QAAA,EAAA,CAEL;MAAA;IAEJ;QACA;MAAC;MAAA;QACC,WAAW;QACX,eAAA,wBACG,QAAO,EAAA,SAAQ,aAAY,SAAS,eAAe,SAAS,WAC1D,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QACjB,CAAA,EAAA,CACH;QAGF,cAAA;UAAC;UAAA;YACC,WAAU;YACV,YAAW;YACX,gBAAe;YACf,KAAK;YACL,WAAU;YAEV,UAAA;kBAAC,wBAAA,MAAA,EAAK,gBAAe,UACnB,cAAC,wBAAA,cAAA,EAAc,OAAM,QAAO,QAAO,QAAO,MAAK,YAAY,CAAA,EAAA,CAC7D;kBACA,wBAAC,YAAA,EACE,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cACjB,CAAA,EAAA,CACH;kBAAA,wBACC,YACE,EAAA,UAAA;gBACC;kBACE,IAAI;kBACJ,gBACE;gBACJ;gBACA;kBACE,SAAS,QAAQ,WAAW;gBAC9B;cAAA,EAAA,CAEJ;YAAA;UAAA;QACF;MAAA;IACF;EACF,EAAA,CAAA;AAEJ;ACvLA,IAAM,WAAW,CAAC,iBAChB,wBAAC,YAAA,EAAW,WAAU,cAAa,SAAQ,MACxC,SACH,CAAA;AAmBF,IAAM,cAAc,CAAC,EAAE,SAAS,UAAA,MAAkC;AAChE,QAAM,EAAE,YAAY,cAAc,IAAI,QAAQ;AAC9C,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAAgC;AAEpD,QAAM,cAAc,MAAkB;AACpC,YAAQ,QAAQ,QAAQ;MACtB,KAAK;AACI,eAAA;UACL,YAAY;UACZ,QAAQ;UACR,MAAM;UACN,SAAS;YACP,IAAI;YACJ,gBAAgB;UAClB;QAAA;MAEJ,KAAK;AACI,eAAA;UACL,YAAY;UACZ,QAAQ;UACR,MAAM;UACN,SAAS;YACP,IAAI;YACJ,gBAAgB;UAClB;QAAA;MAEJ,KAAK;MACL;AACS,eAAA;UACL,YAAY;UACZ,QAAQ;UACR,MAAM;UACN,SAAS;YACP,IAAI;YACJ,gBAAgB;UAClB;QAAA;IAEN;EAAA,GAAA;AAEF,QAAM,WAAW,MAAM,OAAO,QAAQ,GAAG,SAAA;AACzC,QAAM,SAAS,QAAQ,aAAa,eAAe,QAAQ,SAAS;AAGlE,aAAA;IAAC;IAAA;MACC,WAAU;MACV,YAAW;MACX,KAAK;MACL,WAAS;MACT,aAAY;MACZ,aAAY;MACZ,aAAa,WAAW,eAAe;MACvC,OAAM;MACN,SAAS;MACT,KAAKA;MACL,IAAI,QAAI,qBAAU,EAAE,GAAG,OAAO,IAAI,QAAQ,GAAI,CAAA,CAAC;MAC/C,OAAO,EAAE,gBAAgB,OAAO;MAEhC,UAAA;YAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,KAAK,GAAG,YAAW,cAC1C,UAAA;cAAA,wBAAC,YAAA,EAAW,KAAI,MAAK,YAAW,YAC7B,UAAA,WAAW,QAAQ,WAAW;YAC7B,KAAK;YACL,OAAO;YACP,MAAM;YACN,MAAM;YACN,QAAQ;UACT,CAAA,EAAA,CACH;cAAA,wBACC,YAAW,EAAA,KAAI,KAAI,SAAQ,MAAK,WAAU,cACxC,UAAA;YACC;cACE,IAAI;cACJ,gBACE;YACJ;YACA;cACE,mBAAA,wBAAgB,cAAa,EAAA,WAAW,IAAI,KAAK,QAAQ,SAAS,EAAA,CAAG;cACrE;cACA,aAAa,CAAC,QAAQ,QAAQ,SAAS;cACvC;cACA,GAAG;YACL;UAAA,EAAA,CAEJ;QAAA,EAAA,CACF;QACC,QAAQ,cACP;UAAC;UAAA;YACC,YAAY,WAAW;YACvB,aAAY;YACZ,aAAY;YACZ,aAAa,WAAW;YACxB,WAAS;YACT,aAAY;YACZ,cAAa;YACb,YAAW;YACX,eAAc;YAEd,cAAC,wBAAA,YAAA,EAAW,SAAQ,MAAK,YAAW,QAAO,WAAW,WAAW,MAC9D,UAAA,cAAc,WAAW,OAAO,EAAA,CACnC;UAAA;QACF;MAAA;IAAA;EAAA;AAIR;AAWA,IAAM,mBAAmB,CAAC,EAAE,MAAM,SAAA,MAAsC;AACtE,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAAgC;AAIpD,QAAM,EAAE,IAAI,KAAK,GAAG,UAAA,IAAc;AAGhC,aAAA,wBAACA,MAAK,EAAA,IAAI,EAAE,YAAQ,qBAAU,EAAE,GAAG,WAAW,KAAA,CAAM,EAAA,GAAK,OAAO,EAAE,gBAAgB,OAAA,GAChF,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,SACH,CAAA,EACF,CAAA;AAEJ;AAMA,IAAM,eAAe,MAAM;AACnB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,UAAU,KAAA,IAAS,kBAAkB,gBAAgB,CAAC,WAAW;IACvE,UAAU,MAAM;IAChB,MAAM,MAAM;EACZ,EAAA;AAGA,aAAA;IAAC;IAAA;MACC,QAAQ;MACR,WAAU;MACV,YAAW;MACX,OAAM;MACN,QAAO;MACP,YAAW;MACX,aAAY;MACZ,aAAY;MACZ,aAAY;MACZ,KAAI;MAEJ,UAAA;YAAA;UAAC;UAAA;YACC,WAAU;YACV,gBAAe;YACf,SAAS;YACT,aAAY;YACZ,aAAY;YACZ,aAAY;YACZ,KAAI;YAEJ,UAAA;kBAAA,wBAAC,YAAA,EAAW,KAAI,MAAK,SAAQ,SAAQ,YAAW,YAC7C,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cACjB,CAAA,EAAA,CACH;kBAAA,wBACC,KAAI,EAAA,YAAW,cAAa,WAAS,MAAC,SAAS,GAC9C,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAA,SAAS,KAAK,WAAW,MAC5B,CAAA,EAAA,CACF;YAAA;UAAA;QACF;YACC,yBAAA,KAAA,EAAI,MAAM,GAAG,UAAS,QACpB,UAAA;UAAA,SAAS,KAAK,WAAW,OAAO,SAAA,wBAC9B,KAAI,EAAA,YAAY,GAAG,WAAU,UAC5B,cAAC,wBAAA,kBAAA,EAAiB,MAAM,OAAO,GAC5B,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EAAA,CACH,EACF,CAAA;cAAA,wBAED,MAAK,EAAA,WAAU,UAAS,KAAK,GAAG,SAAS,GAAG,KAAI,MAAK,YAAW,WAC9D,UAAA,SAAS,KAAK,IAAI,CAAC,SAAS,cAC3B;YAAC;YAAA;cAEC,cAAY,cAAc;gBACxB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cAED,cAAA,wBAAC,aAAY,EAAA,SAAkB,WAAW,SAAS,KAAK,UAAU,EAAA,CAAG;YAAA;YANhE,QAAQ;UAQhB,CAAA,EAAA,CACH;UACC,SAAS,KAAK,WAAW,OAAO,SAAS,KAAK,WAAW,iBACxD,wBAAC,KAAA,EAAI,eAAe,GAAG,WAAU,UAC/B,cAAA,wBAAC,kBAAA,EAAiB,MAAM,OAAO,GAC5B,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EAAA,CACH,EACF,CAAA;QAAA,EAAA,CAEJ;MAAA;IAAA;EAAA;AAGN;AC7MA,IAAM,CAAC,iBAAiB,iBAAiB,IAAI,cAAmC,aAAa;AAM7F,IAAM,cAAc,MAAM;;AAClB,QAAA,WAAiB,YAAA;AACjB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA;IACJ;IACA,IAAI;IACJ;EAAA,IACE,UAID;AAEH,QAAM,EAAE,WAAW,mBAAmB,OAAA,IAAW,YAAY;IAC3D;IACA,OAAO;EAAA,CACR;AAEK,QAAA;IACJ,WAAW;IACX,MAAM;MACJ;MACA,UAAU,EAAE,aAAa,UAAU;IACrC;EAAA,IACE,kBAAkB,IAAK;AAC3B,QAAM,EAAE,MAAM,eAAe,WAAW,uBAAA,IACtC,oCAAoC,IAAK;AAG3C,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAIjB;AACH,QAAM,EAAE,IAAI,mBAAmB,GAAG,eAAA,IAAmB;AAC/C,QAAA,4BAA4B,iBAAiB,cAAc;AACjE,QAAM,OAAO,0BAA0B,OAAO,OAAO,0BAA0B,IAAI,IAAI;AAEvF,QAAM,mBAAmB;IACvB;MACE,aAAa;MACb,GAAI,aAAa,EAAE,WAAA,IAAe,CAAC;;MAEnC,GAAG;IACL;IACA,EAAE,2BAA2B,KAAK;EAAA;AAapC,QAAM,mBAAyB,aAAO,iBAAiB,SAAS;AAC1D,QAAA,iBAAiB,iBAAiB,cAAc,iBAAiB;AAOvE,MAAI,CAAC,QAAS,mBAAmB,oBAAoB,CAAC,YAAa;AAC1D,eAAA,wBAAC,UAAS,EAAA,IAAG,mBAAmB,CAAA;EACzC;AAEA,MACE,qBACA,mBACA,iBAAiB,cACjB,kBACA,wBACA;AACO,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAGA,MAAI,CAAC,iBAAiB,WAAW,GAAC,4BAAiB,SAAjB,mBAAuB,SAAvB,mBAA6B,SAAQ;AACrE,eAEI,wBAAA,6BAAA,EAAA,cAAA;MAAC,KAAK;MAAL;QACC,YACE;UAACA;UAAA;YACC,KAAK;YACL,IAAI,oBAAoB,cAAc,IAAI,IAAI,GAAG,aAAa,IAAI,UAAU,KAAK,EAAE;YAElF,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QACH;MAAA;IAGN,EAAA,CAAA;EAEJ;AAGA,QAAI,4BAAiB,SAAjB,mBAAuB,SAAvB,mBAA6B,WAAU,CAAC,mBAAmB;AAE3D,eAAA;MAAC;MAAA;QACC,IAAI,EAAE,YAAQ,qBAAU,EAAE,GAAG,OAAO,IAAI,iBAAiB,KAAK,KAAK,CAAC,EAAE,GAAI,CAAA,EAAE;QAC5E,SAAO;MAAA;IAAA;EAGb;AAEM,QAAA,mBAAkB,4BAAiB,SAAjB,mBAAuB,SAAvB,mBAA6B;IACnD,CAAC,YAAY,QAAQ,GAAG,SAAe,MAAA;;AAGvC,MAAA,iBAAiB,WACjB,CAAC,UACD,CAAC,UACD,CAAC,mBACD,CAAC;EAED,iBAAiB,KAAK,OACtB;AACO,eAAA,wBAAC,KAAK,OAAL,CAAW,CAAA;EACrB;AAEA,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC;QACE,IAAI;QACJ,gBAAgB;MAClB;MACA;QACE,aAAa;MACf;IAAA,EAAA,CAEJ;QACA;MAAC;MAAA;QACC,aAAa;QACb,IAAI;QACJ;QACA;QACA;QACA;QACA,UAAU,iBAAiB;QAC3B;QACA;QAEA,cAAC,yBAAA,MAAA,EAAK,WAAU,OAAM,YAAW,cAC/B,UAAA;cAAA;YAAC;YAAA;cACC,MAAM;cACN,QAAO;cACP,YAAW;cACX,eAAe;cACf,UAAS;cACT,YAAY;cAEZ,UAAA;oBAAA,wBAAC,eAAA,EAAc,SAAA,CAAoB;oBAAA,wBAClC,gBAAe,CAAA,CAAA;cAAA;YAAA;UAClB;cAAA,wBACC,cAAa,CAAA,CAAA;QAAA,EAAA,CAChB;MAAA;IACF;EACF,EAAA,CAAA;AAEJ;AAMA,IAAM,2BAA2B,MAAM;AAC/B,QAAA,EAAE,KAAA,IAAS,UAAA;AAGX,QAAA;IACJ,cAAc,CAAC;IACf;IACA;EACF,IAAI,QAAQ,YAAY,IAAI,CAAC,YAAY,EAAE,QAAQ,SAAS,KAAA,EAAO,CAAC;AAEpE,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEI,MAAA,SAAS,CAAC,MAAM;AAEhB,eAAA;MAAC;MAAA;QACC,QAAO;QACP,OAAM;QACN,UAAS;QACT,KAAK;QACL,MAAM;QACN,QAAQ;QACR,YAAW;QAEX,cAAA,wBAAC,KAAK,OAAL,CAAA,CAAW;MAAA;IAAA;EAGlB;AAGE,aAAA;IAAC;IAAA;MACC,QAAO;MACP,OAAM;MACN,UAAS;MACT,KAAK;MACL,MAAM;MACN,QAAQ;MACR,YAAW;MAEX,cAAA,wBAAC,KAAK,SAAL,EAAa,aACX,UAAA,CAAC,EAAE,aAAAS,aAAY,UAAA,wBACb,cAAa,EAAA,aAAaA,cACzB,cAAC,wBAAA,aAAA,CAAA,CAAY,EACf,CAAA,EAAA,CAEJ;IAAA;EAAA;AAGN;AAEA,IAAM,uBAAuB,MAAM;AACjC,aAAA,wBACG,UACC,EAAA,cAAA,wBAAC,WAAA,EACC,cAAC,wBAAA,0BAAA,CAAyB,CAAA,EAC5B,CAAA,EACF,CAAA;AAEJ;", "names": ["Link", "FormInput<PERSON><PERSON>er", "BlocksInput", "ComponentInput", "Wysiwyg", "UIDInput", "components", "schema", "pipe", "permissions"]}