import{r as d,au as m,go as u,m as e,w as c,aT as p,ai as f,A as g,bm as x}from"./strapi-YzJfjJ2z.js";import{i as h}from"./isNil-D4cgUxqE.js";const E=d.forwardRef((i,a)=>{const{formatMessage:s}=m(),{license:r,isError:o,isLoading:n}=u(),{permittedSeats:l,shouldStopCreate:t}=r??{};return o||n?null:e.jsxs(c,{gap:2,children:[!h(l)&&t&&e.jsx(p,{label:s({id:"Settings.application.admin-seats.at-limit-tooltip",defaultMessage:"At limit: add seats to invite more users"}),side:"left",children:e.jsx(f,{width:"1.4rem",height:"1.4rem",fill:"danger500"})}),e.jsx(g,{ref:a,"data-testid":"create-user-button",startIcon:e.jsx(x,{}),size:"S",disabled:t,...i,children:s({id:"Settings.permissions.users.create",defaultMessage:"Invite new user"})})]})});export{E as CreateActionEE};
