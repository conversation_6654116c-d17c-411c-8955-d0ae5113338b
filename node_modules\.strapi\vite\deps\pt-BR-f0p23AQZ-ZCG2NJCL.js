import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/pt-BR-f0p23AQZ.mjs
var ptBR = {
  "BoundRoute.title": "Rota definida para",
  "EditForm.inputSelect.description.role": "Ele anexará o novo usuário autenticado ao nível selecionado.",
  "EditForm.inputSelect.label.role": "Nível padrão para usuários autenticados",
  "EditForm.inputToggle.description.email": "Não permitir que o usuário crie várias contas usando o mesmo endereço de e-mail com diferentes provedores de autenticação.",
  "EditForm.inputToggle.description.sign-up": "Quando desativado (OFF), o processo de registro é proibido. Nenhum novo usuário poderá se registrar, não importa o provedor usado.",
  "EditForm.inputToggle.label.email": "Limitar 1 conta por endereço de email",
  "EditForm.inputToggle.label.sign-up": "Ativar registro de usuários",
  "HeaderNav.link.advancedSettings": "Configurações avançadas",
  "HeaderNav.link.emailTemplates": "Modelos de email",
  "HeaderNav.link.providers": "Provedores",
  "Plugin.permissions.plugins.description": "Defina todas as ações permitidas para o plugin {name}.",
  "Plugins.header.description": "Somente ações vinculadas por uma rota estão listadas abaixo.",
  "Plugins.header.title": "Permissões",
  "Policies.header.hint": "Selecione as ações do aplicativo ou as ações do plugin e clique no ícone do cog para exibir a rota",
  "Policies.header.title": "Configurações avançadas",
  "PopUpForm.Email.email_templates.inputDescription": "Se não tiver certeza de como usar variáveis, {link}",
  "PopUpForm.Email.options.from.email.label": "Email do remetente",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Nome do remetente",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "Mensagem",
  "PopUpForm.Email.options.object.label": "Assunto",
  "PopUpForm.Email.options.response_email.label": "Email de resposta",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Se desativado, os usuários não poderão usar este provedor",
  "PopUpForm.Providers.enabled.label": "Ativar",
  "PopUpForm.Providers.key.label": "ID do cliente",
  "PopUpForm.Providers.key.placeholder": "TEXT",
  "PopUpForm.Providers.redirectURL.front-end.label": "O URL de redirecionamento para seu aplicativo front-end",
  "PopUpForm.Providers.secret.label": "Segredo do Cliente",
  "PopUpForm.Providers.secret.placeholder": "TEXT",
  "PopUpForm.Providers.subdomain.label": "Host URI (Subdomain)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "Editar modelos de email",
  "notification.success.submit": "As configurações foram atualizadas",
  "plugin.description.long": "Proteja sua API com um processo de autenticação completo baseado no JWT. Esse plugin também vem com uma estratégia de ACL que permite gerenciar as permissões entre os grupos de usuários.",
  "plugin.description.short": "Proteja sua API com um processo de autenticação completo baseado no JWT",
  "plugin.name": "Papéis e permissões"
};
export {
  ptBR as default
};
//# sourceMappingURL=pt-BR-f0p23AQZ-ZCG2NJCL.js.map
