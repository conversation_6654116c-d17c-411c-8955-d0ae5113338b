{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/components/DragLayer.tsx", "../../../@strapi/content-manager/admin/src/components/DragPreviews/ComponentDragPreview.tsx", "../../../@strapi/content-manager/admin/src/components/DragPreviews/RelationDragPreview.tsx", "../../../@strapi/content-manager/admin/src/components/LeftMenu.tsx", "../../../@strapi/content-manager/admin/src/hooks/useContentManagerInitData.ts", "../../../@strapi/content-manager/admin/src/layout.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Box } from '@strapi/design-system';\nimport { DragLayerMonitor, XYCoord, useDragLayer } from 'react-dnd';\n\nfunction getStyle(\n  initialOffset: XYCoord | null,\n  currentOffset: XYCoord | null,\n  mouseOffset: XYCoord | null\n) {\n  if (!initialOffset || !currentOffset || !mouseOffset) {\n    return { display: 'none' };\n  }\n\n  const { x, y } = mouseOffset;\n\n  return {\n    transform: `translate(${x}px, ${y}px)`,\n  };\n}\n\nexport interface DragLayerProps {\n  renderItem: (item: {\n    /**\n     * TODO: it'd be great if we could make this a union where the type infers the item.\n     */\n    item: any;\n    type: ReturnType<DragLayerMonitor['getItemType']>;\n  }) => React.ReactNode;\n}\n\nconst DragLayer = ({ renderItem }: DragLayerProps) => {\n  const { itemType, isDragging, item, initialOffset, currentOffset, mouseOffset } = useDragLayer(\n    (monitor) => ({\n      item: monitor.getItem(),\n      itemType: monitor.getItemType(),\n      initialOffset: monitor.getInitialSourceClientOffset(),\n      currentOffset: monitor.getSourceClientOffset(),\n      isDragging: monitor.isDragging(),\n      mouseOffset: monitor.getClientOffset(),\n    })\n  );\n\n  if (!isDragging) {\n    return null;\n  }\n\n  return (\n    <Box\n      height=\"100%\"\n      left={0}\n      position=\"fixed\"\n      pointerEvents=\"none\"\n      top={0}\n      zIndex={100}\n      width=\"100%\"\n    >\n      <Box style={getStyle(initialOffset, currentOffset, mouseOffset)}>\n        {renderItem({ type: itemType, item })}\n      </Box>\n    </Box>\n  );\n};\n\nexport { DragLayer };\n", "import { Flex, FlexComponent, IconButton, Typography } from '@strapi/design-system';\nimport { CaretDown, Drag, Trash } from '@strapi/icons';\nimport { styled } from 'styled-components';\n\ninterface ComponentDragPreviewProps {\n  displayedValue: string;\n}\n\nconst ComponentDragPreview = ({ displayedValue }: ComponentDragPreviewProps) => {\n  return (\n    <Flex\n      background=\"neutral0\"\n      borderColor=\"neutral200\"\n      justifyContent=\"space-between\"\n      gap={3}\n      padding={3}\n      width=\"30rem\"\n    >\n      <ToggleButton type=\"button\">\n        <Flex gap={6}>\n          <DropdownIconWrapper\n            alignItems=\"center\"\n            justifyContent=\"center\"\n            background=\"neutral200\"\n            height=\"3.2rem\"\n            width=\"3.2rem\"\n          >\n            <CaretDown />\n          </DropdownIconWrapper>\n\n          <Flex maxWidth=\"15rem\">\n            <Typography textColor=\"neutral700\" ellipsis>\n              {displayedValue}\n            </Typography>\n          </Flex>\n        </Flex>\n      </ToggleButton>\n\n      <Flex gap={2}>\n        <IconButton withTooltip={false} label=\"\" variant=\"ghost\">\n          <Trash />\n        </IconButton>\n\n        <IconButton withTooltip={false} label=\"\" variant=\"ghost\">\n          <Drag />\n        </IconButton>\n      </Flex>\n    </Flex>\n  );\n};\n\nconst DropdownIconWrapper = styled<FlexComponent>(Flex)`\n  border-radius: 50%;\n\n  svg {\n    height: 0.6rem;\n    width: 1.1rem;\n    > path {\n      fill: ${({ theme }) => theme.colors.neutral600};\n    }\n  }\n`;\n\n// TODO: we shouldn't have to reset a whole button\nconst ToggleButton = styled.button`\n  border: none;\n  background: transparent;\n  display: block;\n  width: 100%;\n  text-align: unset;\n  padding: 0;\n`;\n\nexport { ComponentDragPreview };\nexport type { ComponentDragPreviewProps };\n", "import { Box, Flex, IconButton, Typography } from '@strapi/design-system';\nimport { Cross, Drag } from '@strapi/icons';\n\nimport { DocumentStatus } from '../../pages/EditView/components/DocumentStatus';\nimport {\n  Disconnect<PERSON>utton,\n  LinkEllipsis,\n  FlexWrapper,\n} from '../../pages/EditView/components/FormInputs/Relations';\n\nimport type { Data } from '@strapi/types';\n\ninterface RelationDragPreviewProps {\n  status?: string;\n  displayedValue: string;\n  id: Data.ID;\n  index: number;\n  width: number;\n}\n\nconst RelationDragPreview = ({ status, displayedValue, width }: RelationDragPreviewProps) => {\n  return (\n    <Box style={{ width }}>\n      <Flex\n        paddingTop={2}\n        paddingBottom={2}\n        paddingLeft={2}\n        paddingRight={4}\n        hasRadius\n        borderWidth={1}\n        background=\"neutral0\"\n        borderColor=\"neutral200\"\n        justifyContent=\"space-between\"\n        gap={4}\n      >\n        <FlexWrapper gap={1}>\n          <IconButton withTooltip={false} label=\"\" variant=\"ghost\">\n            <Drag />\n          </IconButton>\n          <Flex width=\"100%\" minWidth={0} justifyContent=\"space-between\">\n            <Box minWidth={0} paddingTop={1} paddingBottom={1} paddingRight={4}>\n              <LinkEllipsis href=\"\">\n                <Typography textColor=\"primary600\" ellipsis>\n                  {displayedValue}\n                </Typography>\n              </LinkEllipsis>\n            </Box>\n            {status ? <DocumentStatus status={status} /> : null}\n          </Flex>\n        </FlexWrapper>\n        <DisconnectButton type=\"button\">\n          <Cross width=\"12px\" />\n        </DisconnectButton>\n      </Flex>\n    </Box>\n  );\n};\n\nexport { RelationDragPreview };\nexport type { RelationDragPreviewProps };\n", "import * as React from 'react';\n\nimport { useQueryParams } from '@strapi/admin/strapi-admin';\nimport {\n  useCollator,\n  useFilter,\n  SubNav,\n  SubNavHeader,\n  SubNavLink,\n  SubNavSection,\n  SubNavSections,\n} from '@strapi/design-system';\nimport { parse, stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { useContentTypeSchema } from '../hooks/useContentTypeSchema';\nimport { useTypedSelector } from '../modules/hooks';\nimport { getTranslation } from '../utils/translations';\n\nimport type { ContentManagerLink } from '../hooks/useContentManagerInitData';\n\nconst SubNavLinkCustom = styled(SubNavLink)`\n  div {\n    width: inherit;\n    span:nth-child(2) {\n      white-space: nowrap;\n      overflow: hidden;\n      text-overflow: ellipsis;\n      width: inherit;\n    }\n  }\n`;\n\nconst LeftMenu = () => {\n  const [search, setSearch] = React.useState('');\n  const [{ query }] = useQueryParams<{ plugins?: object }>();\n  const { formatMessage, locale } = useIntl();\n\n  const collectionTypeLinks = useTypedSelector(\n    (state) => state['content-manager'].app.collectionTypeLinks\n  );\n\n  const singleTypeLinks = useTypedSelector((state) => state['content-manager'].app.singleTypeLinks);\n  const { schemas } = useContentTypeSchema();\n\n  const { startsWith } = useFilter(locale, {\n    sensitivity: 'base',\n  });\n\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const menu = React.useMemo(\n    () =>\n      [\n        {\n          id: 'collectionTypes',\n          title: formatMessage({\n            id: getTranslation('components.LeftMenu.collection-types'),\n            defaultMessage: 'Collection Types',\n          }),\n          searchable: true,\n          links: collectionTypeLinks,\n        },\n        {\n          id: 'singleTypes',\n          title: formatMessage({\n            id: getTranslation('components.LeftMenu.single-types'),\n            defaultMessage: 'Single Types',\n          }),\n          searchable: true,\n          links: singleTypeLinks,\n        },\n      ].map((section) => ({\n        ...section,\n        links: section.links\n          /**\n           * Filter by the search value\n           */\n          .filter((link) => startsWith(link.title, search))\n          /**\n           * Sort correctly using the language\n           */\n          .sort((a, b) => formatter.compare(a.title, b.title))\n          /**\n           * Apply the formated strings to the links from react-intl\n           */\n          .map((link) => {\n            return {\n              ...link,\n              title: formatMessage({ id: link.title, defaultMessage: link.title }),\n            };\n          }),\n      })),\n    [collectionTypeLinks, search, singleTypeLinks, startsWith, formatMessage, formatter]\n  );\n\n  const handleClear = () => {\n    setSearch('');\n  };\n\n  const handleChangeSearch = ({ target: { value } }: { target: { value: string } }) => {\n    setSearch(value);\n  };\n\n  const label = formatMessage({\n    id: getTranslation('header.name'),\n    defaultMessage: 'Content Manager',\n  });\n\n  const getPluginsParamsForLink = (link: ContentManagerLink) => {\n    const schema = schemas.find((schema) => schema.uid === link.uid);\n    const isI18nEnabled = Boolean((schema?.pluginOptions?.i18n as any)?.localized);\n\n    // The search params have the i18n plugin\n    if (query.plugins && 'i18n' in query.plugins) {\n      // Prepare removal of i18n from the plugins search params\n      const { i18n, ...restPlugins } = query.plugins;\n\n      // i18n is not enabled, remove it from the plugins search params\n      if (!isI18nEnabled) {\n        return restPlugins;\n      }\n\n      // i18n is enabled, put the plugins search params back together\n      return { i18n, ...restPlugins };\n    }\n\n    return query.plugins;\n  };\n\n  return (\n    <SubNav aria-label={label}>\n      <SubNavHeader\n        label={label}\n        searchable\n        value={search}\n        onChange={handleChangeSearch}\n        onClear={handleClear}\n        searchLabel={formatMessage({\n          id: 'content-manager.components.LeftMenu.Search.label',\n          defaultMessage: 'Search for a content type',\n        })}\n      />\n      <SubNavSections>\n        {menu.map((section) => {\n          return (\n            <SubNavSection\n              key={section.id}\n              label={section.title}\n              badgeLabel={section.links.length.toString()}\n            >\n              {section.links.map((link) => {\n                return (\n                  <SubNavLinkCustom\n                    tag={NavLink}\n                    key={link.uid}\n                    to={{\n                      pathname: link.to,\n                      search: stringify({\n                        ...parse(link.search ?? ''),\n                        plugins: getPluginsParamsForLink(link),\n                      }),\n                    }}\n                    width=\"100%\"\n                  >\n                    {link.title}\n                  </SubNavLinkCustom>\n                );\n              })}\n            </SubNavSection>\n          );\n        })}\n      </SubNavSections>\n    </SubNav>\n  );\n};\n\nexport { LeftMenu };\n", "import { useEffect } from 'react';\n\nimport {\n  useAuth,\n  type Permission,\n  useNotification,\n  useS<PERSON><PERSON><PERSON><PERSON>,\n  use<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,\n} from '@strapi/admin/strapi-admin';\nimport { useNotifyAT } from '@strapi/design-system';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\n\nimport { COLLECTION_TYPES, SINGLE_TYPES } from '../constants/collections';\nimport { HOOKS } from '../constants/hooks';\nimport { AppState, setInitialData } from '../modules/app';\nimport { useTypedDispatch, useTypedSelector } from '../modules/hooks';\nimport { useGetAllContentTypeSettingsQuery } from '../services/contentTypes';\nimport { useGetInitialDataQuery } from '../services/init';\nimport { getTranslation } from '../utils/translations';\n\nimport type { Component } from '../../../shared/contracts/components';\nimport type {\n  ContentType,\n  FindContentTypesSettings,\n} from '../../../shared/contracts/content-types';\nimport type { GetInitData } from '../../../shared/contracts/init';\n\nconst { MUTATE_COLLECTION_TYPES_LINKS, MUTATE_SINGLE_TYPES_LINKS } = HOOKS;\n\ninterface ContentManagerLink {\n  permissions: Permission[];\n  search: string | null;\n  kind: string;\n  title: string;\n  to: string;\n  uid: string;\n  name: string;\n  isDisplayed: boolean;\n}\n\nconst useContentManagerInitData = (): AppState => {\n  const { toggleNotification } = useNotification();\n  const dispatch = useTypedDispatch();\n  const runHookWaterfall = useStrapiApp(\n    'useContentManagerInitData',\n    (state) => state.runHookWaterfall\n  );\n  const { notifyStatus } = useNotifyAT();\n  const { formatMessage } = useIntl();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler(getTranslation);\n  const checkUserHasPermissions = useAuth(\n    'useContentManagerInitData',\n    (state) => state.checkUserHasPermissions\n  );\n\n  const state = useTypedSelector((state) => state['content-manager'].app);\n\n  const initialDataQuery = useGetInitialDataQuery(undefined, {\n    /**\n     * TODO: remove this when the CTB has been refactored to use redux-toolkit-query\n     * and it can invalidate the cache on mutation\n     */\n    refetchOnMountOrArgChange: true,\n  });\n\n  useEffect(() => {\n    if (initialDataQuery.data) {\n      notifyStatus(\n        formatMessage({\n          id: getTranslation('App.schemas.data-loaded'),\n          defaultMessage: 'The schemas have been successfully loaded.',\n        })\n      );\n    }\n  }, [formatMessage, initialDataQuery.data, notifyStatus]);\n\n  useEffect(() => {\n    if (initialDataQuery.error) {\n      toggleNotification({ type: 'danger', message: formatAPIError(initialDataQuery.error) });\n    }\n  }, [formatAPIError, initialDataQuery.error, toggleNotification]);\n\n  const contentTypeSettingsQuery = useGetAllContentTypeSettingsQuery();\n\n  useEffect(() => {\n    if (contentTypeSettingsQuery.error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(contentTypeSettingsQuery.error),\n      });\n    }\n  }, [formatAPIError, contentTypeSettingsQuery.error, toggleNotification]);\n\n  const formatData = async (\n    components: Component[],\n    contentTypes: ContentType[],\n    fieldSizes: GetInitData.Response['data']['fieldSizes'],\n    contentTypeConfigurations: FindContentTypesSettings.Response['data']\n  ) => {\n    /**\n     * We group these by the two types we support. We do with an object because we can use default\n     * values of arrays to make sure we always have an array to manipulate further on if, for example,\n     * a user has not made any single types.\n     *\n     * This means we have to manually add new content types to this hook if we add a new type – but\n     * the safety is worth it.\n     */\n    const { collectionType: collectionTypeLinks, singleType: singleTypeLinks } =\n      contentTypes.reduce<{\n        collectionType: ContentType[];\n        singleType: ContentType[];\n      }>(\n        (acc, model) => {\n          acc[model.kind].push(model);\n          return acc;\n        },\n        {\n          collectionType: [],\n          singleType: [],\n        }\n      );\n    const collectionTypeSectionLinks = generateLinks(\n      collectionTypeLinks,\n      'collectionTypes',\n      contentTypeConfigurations\n    );\n    const singleTypeSectionLinks = generateLinks(singleTypeLinks, 'singleTypes');\n\n    // Collection Types verifications\n    const collectionTypeLinksPermissions = await Promise.all(\n      collectionTypeSectionLinks.map(({ permissions }) => checkUserHasPermissions(permissions))\n    );\n\n    const authorizedCollectionTypeLinks = collectionTypeSectionLinks.filter(\n      (_, index) => collectionTypeLinksPermissions[index].length > 0\n    );\n\n    // Single Types verifications\n    const singleTypeLinksPermissions = await Promise.all(\n      singleTypeSectionLinks.map(({ permissions }) => checkUserHasPermissions(permissions))\n    );\n    const authorizedSingleTypeLinks = singleTypeSectionLinks.filter(\n      (_, index) => singleTypeLinksPermissions[index].length > 0\n    );\n    const { ctLinks } = runHookWaterfall(MUTATE_COLLECTION_TYPES_LINKS, {\n      ctLinks: authorizedCollectionTypeLinks,\n      models: contentTypes,\n    });\n    const { stLinks } = runHookWaterfall(MUTATE_SINGLE_TYPES_LINKS, {\n      stLinks: authorizedSingleTypeLinks,\n      models: contentTypes,\n    });\n\n    dispatch(\n      setInitialData({\n        authorizedCollectionTypeLinks: ctLinks,\n        authorizedSingleTypeLinks: stLinks,\n        components,\n        contentTypeSchemas: contentTypes,\n        fieldSizes,\n      })\n    );\n  };\n\n  useEffect(() => {\n    if (initialDataQuery.data && contentTypeSettingsQuery.data) {\n      formatData(\n        initialDataQuery.data.components,\n        initialDataQuery.data.contentTypes,\n        initialDataQuery.data.fieldSizes,\n        contentTypeSettingsQuery.data\n      );\n    }\n  }, [initialDataQuery.data, contentTypeSettingsQuery.data]);\n\n  return { ...state };\n};\n\nconst generateLinks = (\n  links: ContentType[],\n  type: 'collectionTypes' | 'singleTypes',\n  configurations: FindContentTypesSettings.Response['data'] = []\n) => {\n  return links\n    .filter((link) => link.isDisplayed)\n    .map((link) => {\n      const collectionTypesPermissions = [\n        { action: 'plugin::content-manager.explorer.create', subject: link.uid },\n        { action: 'plugin::content-manager.explorer.read', subject: link.uid },\n      ];\n      const singleTypesPermissions = [\n        { action: 'plugin::content-manager.explorer.read', subject: link.uid },\n      ];\n      const permissions =\n        type === 'collectionTypes' ? collectionTypesPermissions : singleTypesPermissions;\n\n      const currentContentTypeConfig = configurations.find(({ uid }) => uid === link.uid);\n\n      let search = null;\n\n      if (currentContentTypeConfig) {\n        const searchParams = {\n          page: 1,\n          pageSize: currentContentTypeConfig.settings.pageSize,\n          sort: `${currentContentTypeConfig.settings.defaultSortBy}:${currentContentTypeConfig.settings.defaultSortOrder}`,\n        };\n\n        search = stringify(searchParams, { encode: false });\n      }\n\n      return {\n        permissions,\n        search,\n        kind: link.kind,\n        title: link.info.displayName,\n        to: `/content-manager/${link.kind === 'collectionType' ? COLLECTION_TYPES : SINGLE_TYPES}/${\n          link.uid\n        }`,\n        uid: link.uid,\n        // Used for the list item key in the helper plugin\n        name: link.uid,\n        isDisplayed: link.isDisplayed,\n      } satisfies ContentManagerLink;\n    });\n};\n\nexport { useContentManagerInitData };\nexport type { ContentManagerLink };\n", "/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport { Page, useGuidedTour, Layouts } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\nimport { Navigate, Outlet, useLocation, useMatch } from 'react-router-dom';\n\nimport { DragLayer, DragLayerProps } from './components/DragLayer';\nimport { CardDragPreview } from './components/DragPreviews/CardDragPreview';\nimport { ComponentDragPreview } from './components/DragPreviews/ComponentDragPreview';\nimport { RelationDragPreview } from './components/DragPreviews/RelationDragPreview';\nimport { LeftMenu } from './components/LeftMenu';\nimport { ItemTypes } from './constants/dragAndDrop';\nimport { useContentManagerInitData } from './hooks/useContentManagerInitData';\nimport { getTranslation } from './utils/translations';\n\n/* -------------------------------------------------------------------------------------------------\n * Layout\n * -----------------------------------------------------------------------------------------------*/\n\nconst Layout = () => {\n  const contentTypeMatch = useMatch('/content-manager/:kind/:uid/*');\n\n  const { isLoading, collectionTypeLinks, models, singleTypeLinks } = useContentManagerInitData();\n  const authorisedModels = [...collectionTypeLinks, ...singleTypeLinks].sort((a, b) =>\n    a.title.localeCompare(b.title)\n  );\n\n  const { pathname } = useLocation();\n  const { formatMessage } = useIntl();\n  const startSection = useGuidedTour('Layout', (state) => state.startSection);\n  const startSectionRef = React.useRef(startSection);\n\n  React.useEffect(() => {\n    if (startSectionRef.current) {\n      startSectionRef.current('contentManager');\n    }\n  }, []);\n\n  if (isLoading) {\n    return (\n      <>\n        <Page.Title>\n          {formatMessage({\n            id: getTranslation('plugin.name'),\n            defaultMessage: 'Content Manager',\n          })}\n        </Page.Title>\n        <Page.Loading />\n      </>\n    );\n  }\n\n  // Array of models that are displayed in the content manager\n  const supportedModelsToDisplay = models.filter(({ isDisplayed }) => isDisplayed);\n\n  // Redirect the user to the 403 page\n  if (\n    authorisedModels.length === 0 &&\n    supportedModelsToDisplay.length > 0 &&\n    pathname !== '/content-manager/403'\n  ) {\n    return <Navigate to=\"/403\" />;\n  }\n\n  // Redirect the user to the create content type page\n  if (supportedModelsToDisplay.length === 0 && pathname !== '/no-content-types') {\n    return <Navigate to=\"/no-content-types\" />;\n  }\n\n  if (!contentTypeMatch && authorisedModels.length > 0) {\n    return (\n      <Navigate\n        to={{\n          pathname: authorisedModels[0].to,\n          search: authorisedModels[0].search ?? '',\n        }}\n        replace\n      />\n    );\n  }\n\n  return (\n    <>\n      <Page.Title>\n        {formatMessage({\n          id: getTranslation('plugin.name'),\n          defaultMessage: 'Content Manager',\n        })}\n      </Page.Title>\n      <Layouts.Root sideNav={<LeftMenu />}>\n        <DragLayer renderItem={renderDraglayerItem} />\n        <Outlet />\n      </Layouts.Root>\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * renderDraglayerItem\n * -----------------------------------------------------------------------------------------------*/\n\nfunction renderDraglayerItem({ type, item }: Parameters<DragLayerProps['renderItem']>[0]) {\n  if (!type || (type && typeof type !== 'string')) {\n    return null;\n  }\n\n  /**\n   * Because a user may have multiple relations / dynamic zones / repeable fields in the same content type,\n   * we append the fieldName for the item type to make them unique, however, we then want to extract that\n   * first type to apply the correct preview.\n   */\n  const [actualType] = type.split('_');\n\n  switch (actualType) {\n    case ItemTypes.EDIT_FIELD:\n    case ItemTypes.FIELD:\n      return <CardDragPreview label={item.label} />;\n    case ItemTypes.COMPONENT:\n    case ItemTypes.DYNAMIC_ZONE:\n      return <ComponentDragPreview displayedValue={item.displayedValue} />;\n\n    case ItemTypes.RELATION:\n      return <RelationDragPreview {...item} />;\n\n    default:\n      return null;\n  }\n}\n\nexport { Layout };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAKA,SAAS,SACP,eACA,eACA,aACA;AACA,MAAI,CAAC,iBAAiB,CAAC,iBAAiB,CAAC,aAAa;AAC7C,WAAA,EAAE,SAAS,OAAA;EACpB;AAEM,QAAA,EAAE,GAAG,EAAM,IAAA;AAEV,SAAA;IACL,WAAW,aAAa,CAAC,OAAO,CAAC;EAAA;AAErC;AAYA,IAAM,YAAY,CAAC,EAAE,WAAA,MAAiC;AACpD,QAAM,EAAE,UAAU,YAAY,MAAM,eAAe,eAAe,YAAA,IAAgB;IAChF,CAAC,aAAa;MACZ,MAAM,QAAQ,QAAQ;MACtB,UAAU,QAAQ,YAAY;MAC9B,eAAe,QAAQ,6BAA6B;MACpD,eAAe,QAAQ,sBAAsB;MAC7C,YAAY,QAAQ,WAAW;MAC/B,aAAa,QAAQ,gBAAgB;IAAA;EACvC;AAGF,MAAI,CAAC,YAAY;AACR,WAAA;EACT;AAGE,aAAA;IAAC;IAAA;MACC,QAAO;MACP,MAAM;MACN,UAAS;MACT,eAAc;MACd,KAAK;MACL,QAAQ;MACR,OAAM;MAEN,cAAC,wBAAA,KAAA,EAAI,OAAO,SAAS,eAAe,eAAe,WAAW,GAC3D,UAAA,WAAW,EAAE,MAAM,UAAU,KAAM,CAAA,EAAA,CACtC;IAAA;EAAA;AAGN;ACtDA,IAAM,uBAAuB,CAAC,EAAE,eAAA,MAAgD;AAE5E,aAAA;IAAC;IAAA;MACC,YAAW;MACX,aAAY;MACZ,gBAAe;MACf,KAAK;MACL,SAAS;MACT,OAAM;MAEN,UAAA;YAAA,wBAAC,cAAA,EAAa,MAAK,UACjB,cAAC,yBAAA,MAAA,EAAK,KAAK,GACT,UAAA;cAAA;YAAC;YAAA;cACC,YAAW;cACX,gBAAe;cACf,YAAW;cACX,QAAO;cACP,OAAM;cAEN,cAAA,wBAAC,eAAU,CAAA,CAAA;YAAA;UACb;cAEA,wBAAC,MAAK,EAAA,UAAS,SACb,cAAA,wBAAC,YAAW,EAAA,WAAU,cAAa,UAAQ,MACxC,UAAA,eACH,CAAA,EAAA,CACF;QAAA,EAAA,CACF,EACF,CAAA;YAEA,yBAAC,MAAK,EAAA,KAAK,GACT,UAAA;cAAC,wBAAA,YAAA,EAAW,aAAa,OAAO,OAAM,IAAG,SAAQ,SAC/C,cAAC,wBAAA,cAAA,CAAA,CAAM,EACT,CAAA;cAEA,wBAAC,YAAW,EAAA,aAAa,OAAO,OAAM,IAAG,SAAQ,SAC/C,cAAC,wBAAA,eAAA,CAAA,CAAK,EACR,CAAA;QAAA,EAAA,CACF;MAAA;IAAA;EAAA;AAGN;AAEA,IAAM,sBAAsB,GAAsB,IAAI;;;;;;;cAOxC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;AAMpD,IAAM,eAAe,GAAO;;;;;;;;AC5C5B,IAAM,sBAAsB,CAAC,EAAE,QAAQ,gBAAgB,MAAA,MAAsC;AAC3F,aACG,wBAAA,KAAA,EAAI,OAAO,EAAE,MAAA,GACZ,cAAA;IAAC;IAAA;MACC,YAAY;MACZ,eAAe;MACf,aAAa;MACb,cAAc;MACd,WAAS;MACT,aAAa;MACb,YAAW;MACX,aAAY;MACZ,gBAAe;MACf,KAAK;MAEL,UAAA;YAAC,yBAAA,aAAA,EAAY,KAAK,GAChB,UAAA;cAAC,wBAAA,YAAA,EAAW,aAAa,OAAO,OAAM,IAAG,SAAQ,SAC/C,cAAC,wBAAA,eAAA,CAAA,CAAK,EACR,CAAA;cAAA,yBACC,MAAK,EAAA,OAAM,QAAO,UAAU,GAAG,gBAAe,iBAC7C,UAAA;gBAAC,wBAAA,KAAA,EAAI,UAAU,GAAG,YAAY,GAAG,eAAe,GAAG,cAAc,GAC/D,cAAA,wBAAC,cAAA,EAAa,MAAK,IACjB,cAAA,wBAAC,YAAW,EAAA,WAAU,cAAa,UAAQ,MACxC,UACH,eAAA,CAAA,EAAA,CACF,EACF,CAAA;YACC,aAAS,wBAAC,gBAAe,EAAA,OAAA,CAAgB,IAAK;UAAA,EAAA,CACjD;QAAA,EAAA,CACF;YACA,wBAAC,kBAAA,EAAiB,MAAK,UACrB,cAAA,wBAAC,eAAM,EAAA,OAAM,OAAA,CAAO,EACtB,CAAA;MAAA;IAAA;EAEJ,EAAA,CAAA;AAEJ;ACjCA,IAAM,mBAAmB,GAAO,UAAU;;;;;;;;;;;AAY1C,IAAM,WAAW,MAAM;AACrB,QAAM,CAAC,QAAQ,SAAS,IAAU,eAAS,EAAE;AAC7C,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAAqC;AACzD,QAAM,EAAE,eAAe,OAAO,IAAI,QAAQ;AAE1C,QAAM,sBAAsB;IAC1B,CAAC,UAAU,MAAM,iBAAiB,EAAE,IAAI;EAAA;AAGpC,QAAA,kBAAkB,iBAAiB,CAAC,UAAU,MAAM,iBAAiB,EAAE,IAAI,eAAe;AAC1F,QAAA,EAAE,QAAA,IAAY,qBAAA;AAEpB,QAAM,EAAE,WAAA,IAAe,UAAU,QAAQ;IACvC,aAAa;EAAA,CACd;AAEK,QAAA,YAAY,YAAY,QAAQ;IACpC,aAAa;EAAA,CACd;AAED,QAAM,OAAa;IACjB,MACE;MACE;QACE,IAAI;QACJ,OAAO,cAAc;UACnB,IAAI,eAAe,sCAAsC;UACzD,gBAAgB;QAAA,CACjB;QACD,YAAY;QACZ,OAAO;MACT;MACA;QACE,IAAI;QACJ,OAAO,cAAc;UACnB,IAAI,eAAe,kCAAkC;UACrD,gBAAgB;QAAA,CACjB;QACD,YAAY;QACZ,OAAO;MACT;IAAA,EACA,IAAI,CAAC,aAAa;MAClB,GAAG;MACH,OAAO,QAAQ,MAIZ,OAAO,CAAC,SAAS,WAAW,KAAK,OAAO,MAAM,CAAC,EAI/C,KAAK,CAAC,GAAG,MAAM,UAAU,QAAQ,EAAE,OAAO,EAAE,KAAK,CAAC,EAIlD,IAAI,CAAC,SAAS;AACN,eAAA;UACL,GAAG;UACH,OAAO,cAAc,EAAE,IAAI,KAAK,OAAO,gBAAgB,KAAK,MAAA,CAAO;QAAA;MACrE,CACD;IAAA,EACH;IACJ,CAAC,qBAAqB,QAAQ,iBAAiB,YAAY,eAAe,SAAS;EAAA;AAGrF,QAAM,cAAc,MAAM;AACxB,cAAU,EAAE;EAAA;AAGd,QAAM,qBAAqB,CAAC,EAAE,QAAQ,EAAE,MAAA,EAAA,MAA6C;AACnF,cAAU,KAAK;EAAA;AAGjB,QAAM,QAAQ,cAAc;IAC1B,IAAI,eAAe,aAAa;IAChC,gBAAgB;EAAA,CACjB;AAEK,QAAA,0BAA0B,CAAC,SAA6B;;AACtD,UAAA,SAAS,QAAQ,KAAK,CAACA,YAAWA,QAAO,QAAQ,KAAK,GAAG;AAC/D,UAAM,gBAAgB,SAAS,4CAAQ,kBAAR,mBAAuB,SAAvB,mBAAqC,SAAS;AAG7E,QAAI,MAAM,WAAW,UAAU,MAAM,SAAS;AAE5C,YAAM,EAAE,MAAM,GAAG,YAAA,IAAgB,MAAM;AAGvC,UAAI,CAAC,eAAe;AACX,eAAA;MACT;AAGO,aAAA,EAAE,MAAM,GAAG,YAAA;IACpB;AAEA,WAAO,MAAM;EAAA;AAIb,aAAA,yBAAC,QAAO,EAAA,cAAY,OAClB,UAAA;QAAA;MAAC;MAAA;QACC;QACA,YAAU;QACV,OAAO;QACP,UAAU;QACV,SAAS;QACT,aAAa,cAAc;UACzB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH;QACC,wBAAA,gBAAA,EACE,UAAK,KAAA,IAAI,CAAC,YAAY;AAEnB,iBAAA;QAAC;QAAA;UAEC,OAAO,QAAQ;UACf,YAAY,QAAQ,MAAM,OAAO,SAAS;UAEzC,UAAQ,QAAA,MAAM,IAAI,CAAC,SAAS;AAEzB,uBAAA;cAAC;cAAA;gBACC,KAAK;gBAEL,IAAI;kBACF,UAAU,KAAK;kBACf,YAAQ,qBAAU;oBAChB,OAAG,iBAAM,KAAK,UAAU,EAAE;oBAC1B,SAAS,wBAAwB,IAAI;kBAAA,CACtC;gBACH;gBACA,OAAM;gBAEL,UAAK,KAAA;cAAA;cAVD,KAAK;YAAA;UAWZ,CAEH;QAAA;QArBI,QAAQ;MAAA;IAwBlB,CAAA,EAAA,CACH;EACF,EAAA,CAAA;AAEJ;ACvJA,IAAM,EAAE,+BAA+B,0BAA8B,IAAA;AAarE,IAAM,4BAA4B,MAAgB;AAC1C,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,WAAW,iBAAA;AACjB,QAAM,mBAAmB;IACvB;IACA,CAACC,WAAUA,OAAM;EAAA;AAEb,QAAA,EAAE,aAAA,IAAiB,YAAA;AACnB,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB,cAAc;AACrF,QAAM,0BAA0B;IAC9B;IACA,CAACA,WAAUA,OAAM;EAAA;AAGnB,QAAM,QAAQ,iBAAiB,CAACA,WAAUA,OAAM,iBAAiB,EAAE,GAAG;AAEhE,QAAA,mBAAmB,uBAAuB,QAAW;;;;;IAKzD,2BAA2B;EAAA,CAC5B;AAED,8BAAU,MAAM;AACd,QAAI,iBAAiB,MAAM;AACzB;QACE,cAAc;UACZ,IAAI,eAAe,yBAAyB;UAC5C,gBAAgB;QAAA,CACjB;MAAA;IAEL;EAAA,GACC,CAAC,eAAe,iBAAiB,MAAM,YAAY,CAAC;AAEvD,8BAAU,MAAM;AACd,QAAI,iBAAiB,OAAO;AACP,yBAAA,EAAE,MAAM,UAAU,SAAS,eAAe,iBAAiB,KAAK,EAAA,CAAG;IACxF;EAAA,GACC,CAAC,gBAAgB,iBAAiB,OAAO,kBAAkB,CAAC;AAE/D,QAAM,2BAA2B,kCAAA;AAEjC,8BAAU,MAAM;AACd,QAAI,yBAAyB,OAAO;AACf,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,yBAAyB,KAAK;MAAA,CACvD;IACH;EAAA,GACC,CAAC,gBAAgB,yBAAyB,OAAO,kBAAkB,CAAC;AAEvE,QAAM,aAAa,OACjB,YACA,cACA,YACA,8BACG;AASH,UAAM,EAAE,gBAAgB,qBAAqB,YAAY,gBAAA,IACvD,aAAa;MAIX,CAAC,KAAK,UAAU;AACd,YAAI,MAAM,IAAI,EAAE,KAAK,KAAK;AACnB,eAAA;MACT;MACA;QACE,gBAAgB,CAAC;QACjB,YAAY,CAAC;MACf;IAAA;AAEJ,UAAM,6BAA6B;MACjC;MACA;MACA;IAAA;AAEI,UAAA,yBAAyB,cAAc,iBAAiB,aAAa;AAGrE,UAAA,iCAAiC,MAAM,QAAQ;MACnD,2BAA2B,IAAI,CAAC,EAAE,YAAA,MAAkB,wBAAwB,WAAW,CAAC;IAAA;AAG1F,UAAM,gCAAgC,2BAA2B;MAC/D,CAAC,GAAG,UAAU,+BAA+B,KAAK,EAAE,SAAS;IAAA;AAIzD,UAAA,6BAA6B,MAAM,QAAQ;MAC/C,uBAAuB,IAAI,CAAC,EAAE,YAAA,MAAkB,wBAAwB,WAAW,CAAC;IAAA;AAEtF,UAAM,4BAA4B,uBAAuB;MACvD,CAAC,GAAG,UAAU,2BAA2B,KAAK,EAAE,SAAS;IAAA;AAE3D,UAAM,EAAE,QAAA,IAAY,iBAAiB,+BAA+B;MAClE,SAAS;MACT,QAAQ;IAAA,CACT;AACD,UAAM,EAAE,QAAA,IAAY,iBAAiB,2BAA2B;MAC9D,SAAS;MACT,QAAQ;IAAA,CACT;AAED;MACE,eAAe;QACb,+BAA+B;QAC/B,2BAA2B;QAC3B;QACA,oBAAoB;QACpB;MAAA,CACD;IAAA;EACH;AAGF,8BAAU,MAAM;AACV,QAAA,iBAAiB,QAAQ,yBAAyB,MAAM;AAC1D;QACE,iBAAiB,KAAK;QACtB,iBAAiB,KAAK;QACtB,iBAAiB,KAAK;QACtB,yBAAyB;MAAA;IAE7B;EAAA,GACC,CAAC,iBAAiB,MAAM,yBAAyB,IAAI,CAAC;AAElD,SAAA,EAAE,GAAG,MAAA;AACd;AAEA,IAAM,gBAAgB,CACpB,OACA,MACA,iBAA4D,CAAA,MACzD;AACI,SAAA,MACJ,OAAO,CAAC,SAAS,KAAK,WAAW,EACjC,IAAI,CAAC,SAAS;AACb,UAAM,6BAA6B;MACjC,EAAE,QAAQ,2CAA2C,SAAS,KAAK,IAAI;MACvE,EAAE,QAAQ,yCAAyC,SAAS,KAAK,IAAI;IAAA;AAEvE,UAAM,yBAAyB;MAC7B,EAAE,QAAQ,yCAAyC,SAAS,KAAK,IAAI;IAAA;AAEjE,UAAA,cACJ,SAAS,oBAAoB,6BAA6B;AAEtD,UAAA,2BAA2B,eAAe,KAAK,CAAC,EAAE,IAAI,MAAM,QAAQ,KAAK,GAAG;AAElF,QAAI,SAAS;AAEb,QAAI,0BAA0B;AAC5B,YAAM,eAAe;QACnB,MAAM;QACN,UAAU,yBAAyB,SAAS;QAC5C,MAAM,GAAG,yBAAyB,SAAS,aAAa,IAAI,yBAAyB,SAAS,gBAAgB;MAAA;AAGhH,mBAAS,qBAAU,cAAc,EAAE,QAAQ,MAAO,CAAA;IACpD;AAEO,WAAA;MACL;MACA;MACA,MAAM,KAAK;MACX,OAAO,KAAK,KAAK;MACjB,IAAI,oBAAoB,KAAK,SAAS,mBAAmB,mBAAmB,YAAY,IACtF,KAAK,GACP;MACA,KAAK,KAAK;;MAEV,MAAM,KAAK;MACX,aAAa,KAAK;IAAA;EACpB,CACD;AACL;AC7MA,IAAM,SAAS,MAAM;AACb,QAAA,mBAAmB,SAAS,+BAA+B;AAEjE,QAAM,EAAE,WAAW,qBAAqB,QAAQ,gBAAA,IAAoB,0BAAA;AACpE,QAAM,mBAAmB,CAAC,GAAG,qBAAqB,GAAG,eAAe,EAAE;IAAK,CAAC,GAAG,MAC7E,EAAE,MAAM,cAAc,EAAE,KAAK;EAAA;AAGzB,QAAA,EAAE,SAAA,IAAa,YAAA;AACf,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,eAAe,cAAc,UAAU,CAAC,UAAU,MAAM,YAAY;AACpE,QAAA,kBAAwB,aAAO,YAAY;AAEjD,EAAM,gBAAU,MAAM;AACpB,QAAI,gBAAgB,SAAS;AAC3B,sBAAgB,QAAQ,gBAAgB;IAC1C;EACF,GAAG,CAAE,CAAA;AAEL,MAAI,WAAW;AACb,eAEI,yBAAA,6BAAA,EAAA,UAAA;UAAC,wBAAA,KAAK,OAAL,EACE,UAAc,cAAA;QACb,IAAI,eAAe,aAAa;QAChC,gBAAgB;MACjB,CAAA,EAAA,CACH;UACA,wBAAC,KAAK,SAAL,CAAA,CAAa;IAChB,EAAA,CAAA;EAEJ;AAGA,QAAM,2BAA2B,OAAO,OAAO,CAAC,EAAE,YAAA,MAAkB,WAAW;AAG/E,MACE,iBAAiB,WAAW,KAC5B,yBAAyB,SAAS,KAClC,aAAa,wBACb;AACO,eAAA,wBAAC,UAAS,EAAA,IAAG,OAAO,CAAA;EAC7B;AAGA,MAAI,yBAAyB,WAAW,KAAK,aAAa,qBAAqB;AACtE,eAAA,wBAAC,UAAS,EAAA,IAAG,oBAAoB,CAAA;EAC1C;AAEA,MAAI,CAAC,oBAAoB,iBAAiB,SAAS,GAAG;AAElD,eAAA;MAAC;MAAA;QACC,IAAI;UACF,UAAU,iBAAiB,CAAC,EAAE;UAC9B,QAAQ,iBAAiB,CAAC,EAAE,UAAU;QACxC;QACA,SAAO;MAAA;IAAA;EAGb;AAEA,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAc,cAAA;MACb,IAAI,eAAe,aAAa;MAChC,gBAAgB;IACjB,CAAA,EAAA,CACH;QAAA,yBACC,QAAQ,MAAR,EAAa,aAAS,wBAAC,UAAA,CAAA,CAAS,GAC/B,UAAA;UAAC,wBAAA,WAAA,EAAU,YAAY,oBAAqB,CAAA;UAAA,wBAC3C,QAAO,CAAA,CAAA;IAAA,EAAA,CACV;EACF,EAAA,CAAA;AAEJ;AAMA,SAAS,oBAAoB,EAAE,MAAM,KAAA,GAAqD;AACxF,MAAI,CAAC,QAAS,QAAQ,OAAO,SAAS,UAAW;AACxC,WAAA;EACT;AAOA,QAAM,CAAC,UAAU,IAAI,KAAK,MAAM,GAAG;AAEnC,UAAQ,YAAY;IAClB,KAAK,UAAU;IACf,KAAK,UAAU;AACb,iBAAQ,wBAAA,iBAAA,EAAgB,OAAO,KAAK,MAAO,CAAA;IAC7C,KAAK,UAAU;IACf,KAAK,UAAU;AACb,iBAAQ,wBAAA,sBAAA,EAAqB,gBAAgB,KAAK,eAAgB,CAAA;IAEpE,KAAK,UAAU;AACN,iBAAA,wBAAC,qBAAqB,EAAA,GAAG,KAAM,CAAA;IAExC;AACS,aAAA;EACX;AACF;", "names": ["schema", "state"]}