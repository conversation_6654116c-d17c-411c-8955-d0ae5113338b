import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/hi-DW2CutdA.mjs
var Analytics = "वैश्लेषिकी";
var Documentation = "प्रलेखन";
var Email = "ईमेल";
var Password = "पासवर्ड";
var Provider = "प्रदाता";
var ResetPasswordToken = "पासवर्ड रीसेट करें टोकन";
var Role = "भूमिका";
var Username = "उपयोगकर्ता नाम";
var Users = "उपयोगकर्ता ";
var anErrorOccurred = "ओह! कुछ गलत हो गया। कृपया पुन: प्रयास करें।";
var clearLabel = "साफ़";
var or = "या";
var skipToContent = "इसे छोड़कर सामग्री पर बढ़ने के लिए";
var submit = "प्रस्तुत करना";
var hi = {
  Analytics,
  "Auth.components.Oops.text": "आपके खाते को निलंबित कर दिया गया है.",
  "Auth.components.Oops.text.admin": "अगर यह गलती है, तो कृपया अपने व्यवस्थापक से संपर्क करें.",
  "Auth.components.Oops.title": "उफ़...",
  "Auth.form.button.forgot-password": "ईमेल भेजें",
  "Auth.form.button.go-home": "वापीस घर जाओ",
  "Auth.form.button.login": "लॉग इन करें",
  "Auth.form.button.login.providers.error": "हम आपको चयनित प्रदाता के माध्यम से कनेक्ट नहीं कर सकते हैं।",
  "Auth.form.button.login.strapi": "Strapi . के माध्यम से लॉग इन करें",
  "Auth.form.button.password-recovery": "पासवर्ड की दोबारा प्राप्ति",
  "Auth.form.button.register": "चलो शुरू करते हैं",
  "Auth.form.confirmPassword.label": "पुष्टिकरण पासवर्ड",
  "Auth.form.currentPassword.label": "वर्तमान पासवर्ड",
  "Auth.form.email.label": "ईमेल",
  "Auth.form.email.placeholder": "e.g. <EMAIL>",
  "Auth.form.error.blocked": "आपका खाता व्यवस्थापक द्वारा अवरुद्ध कर दिया गया है",
  "Auth.form.error.code.provide": "गलत कोड प्रदान किया गया।",
  "Auth.form.error.confirmed": "आपके खाते के ईमेल की पुष्टि नहीं हुई है।",
  "Auth.form.error.email.invalid": "ईमेल अवैध है।",
  "Auth.form.error.email.provide": "कृपया अपना उपयोगकर्ता नाम या अपना ईमेल प्रदान करें।",
  "Auth.form.error.email.taken": " ईमेल पहले से ही लिया जा चुका है।",
  "Auth.form.error.invalid": "पहचानकर्ता या पासवर्ड अमान्य।",
  "Auth.form.error.params.provide": "गलत पैरामीटर प्रदान किए गए।",
  "Auth.form.error.password.format": "आपके पासवर्ड में `$` का प्रतीक तीन बार से अधिक नहीं हो सकता।",
  "Auth.form.error.password.local": "इस उपयोगकर्ता ने कभी भी स्थानीय पासवर्ड सेट नहीं किया है, कृपया खाता निर्माण के दौरान उपयोग किए गए प्रदाता के माध्यम से लॉगिन करें।",
  "Auth.form.error.password.matching": "पासवर्ड मेल नहीं खाते।",
  "Auth.form.error.password.provide": " कृपया अपना पासवर्ड प्रदान करें। ",
  "Auth.form.error.ratelimit": "बहुत अधिक प्रयास, कृपया एक मिनट में पुन: प्रयास करें।",
  "Auth.form.error.user.not-exist": "यह ईमेल मौजूद नहीं है।",
  "Auth.form.error.username.taken": "उपयोगकर्ता का नाम पहले से लिया है।",
  "Auth.form.firstname.label": "संतोष",
  "Auth.form.firstname.placeholder": "जैसे काई",
  "Auth.form.forgot-password.email.label": "अपना ईमेल दर्ज करें",
  "Auth.form.forgot-password.email.label.success": "ईमेल सफलतापूर्वक भेजा गया",
  "Auth.form.lastname.label": "उपनाम",
  "Auth.form.lastname.placeholder": "जैसे हरिणी",
  "Auth.form.password.hide-password": "पासवर्ड छिपाएं",
  "Auth.form.password.hint": "कम से कम 8 अक्षर, 1 अपरकेस, 1 लोअरकेस और 1 नंबर होना चाहिए",
  "Auth.form.password.show-password": "पासवर्ड दिखाए",
  "Auth.form.register.news.label": "मुझे नई सुविधाओं और आगामी सुधारों के बारे में अपडेट रखें (ऐसा करने से आप {शर्तें} और {नीति} स्वीकार करते हैं)।",
  "Auth.form.register.subtitle": "क्रेडेंशियल का उपयोग केवल Strapi में प्रमाणित करने के लिए किया जाता है। सभी सहेजे गए डेटा आपके डेटाबेस में संग्रहीत किए जाएंगे।",
  "Auth.form.rememberMe.label": "पहचाना की नहीं",
  "Auth.form.username.label": "उपयोगकर्ता नाम",
  "Auth.form.username.placeholder": "जैसे काई_डो",
  "Auth.form.welcome.subtitle": "अपने Strapi खाते में लॉग इन करें",
  "Auth.form.welcome.title": "स्ट्रैपी में आपका स्वागत है!",
  "Auth.link.forgot-password": "पासवर्ड भूल गए हैं?",
  "Auth.link.ready": "साइन इन करने के लिए तैयार हैं?",
  "Auth.link.signin": "साइन इन करें",
  "Auth.link.signin.account": "क्या आपके पास पहले से एक खाता मौजूद है?",
  "Auth.login.sso.divider": "या लॉगिन करें",
  "Auth.login.sso.loading": "प्रदाता लोड हो रहे हैं...",
  "Auth.login.sso.subtitle": "SSO के माध्यम से अपने खाते में लॉगिन करें",
  "Auth.privacy-policy-agreement.policy": "गोपनीयता नीति",
  "Auth.privacy-policy-agreement.terms": "शर्तें",
  "Auth.reset-password.title": "पासवर्ड रीसेट",
  "Content Manager": "सामग्री प्रबंधक",
  "Content Type Builder": "सामग्री-प्रकार बिल्डर",
  Documentation,
  Email,
  "Files Upload": "फ़ाइलें अपलोड",
  "HomePage.head.title": "होमपेज",
  "HomePage.roadmap": "हमारा रोडमैप देखें",
  "HomePage.welcome.congrats": "बधाई!",
  "HomePage.welcome.congrats.content": "आप पहले व्यवस्थापक के रूप में लॉग इन हैं। Strapi द्वारा प्रदान की गई शक्तिशाली विशेषताओं की खोज करने के लिए,",
  "HomePage.welcome.congrats.content.bold": "हम आपको अपना पहला संग्रह-प्रकार बनाने की सलाह देते हैं।",
  "Media Library": "मीडिया लाइब्रेरी",
  "New entry": "नविन प्रवेश",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "भूमिकाएं और अनुमतियां",
  "Roles.ListPage.notification.delete-all-not-allowed": "कुछ भूमिकाओं को हटाया नहीं जा सका क्योंकि वे उपयोगकर्ताओं से संबद्ध हैं",
  "Roles.ListPage.notification.delete-not-allowed": "उपयोगकर्ताओं के साथ संबद्ध होने पर भूमिका को हटाया नहीं जा सकता",
  "Roles.RoleRow.select-all": "बल्क कार्रवाइयों के लिए {नाम} चुनें",
  "Roles.RoleRow.user-count": "{संख्या, बहुवचन, =0 {# उपयोगकर्ता} एक {# उपयोगकर्ता} अन्य {# उपयोगकर्ता}}",
  "Roles.components.List.empty.withSearch": "खोज से संबंधित कोई भूमिका नहीं है ({खोज})...",
  "Settings.PageTitle": "सेटिंग्स - {नाम}",
  "Settings.apiTokens.addFirstToken": "अपना पहला एपीआई टोकन जोड़ें",
  "Settings.apiTokens.addNewToken": "नया एपीआई टोकन जोड़ें",
  "Settings.tokens.copy.editMessage": "सुरक्षा कारणों से, आप अपना टोकन केवल एक बार देख सकते हैं.",
  "Settings.tokens.copy.editTitle": "यह टोकन अब उपलब्ध नहीं है।",
  "Settings.tokens.copy.lastWarning": "इस टोकन को कॉपी करना सुनिश्चित करें, आप इसे दोबारा नहीं देख पाएंगे!",
  "Settings.apiTokens.create": "नया एपीआई टोकन बनाएं",
  "Settings.apiTokens.description": "एपीआई का उपभोग करने के लिए उत्पन्न टोकन की सूची",
  "Settings.apiTokens.emptyStateLayout": "आपके पास अभी तक कोई सामग्री नहीं है...",
  "Settings.tokens.notification.copied": "टोकन को क्लिपबोर्ड पर कॉपी किया गया",
  "Settings.apiTokens.title": "एपीआई टोकन",
  "Settings.tokens.types.full-access": "पूर्ण पहुँच",
  "Settings.tokens.types.read-only": "केवल पढ़ने के लिए",
  "Settings.application.description": "व्यवस्थापन पैनल की वैश्विक जानकारी",
  "Settings.application.edition-title": "वर्तमान योजना",
  "Settings.application.get-help": "मदद लें",
  "Settings.application.link-pricing": "सभी मूल्य निर्धारण योजनाएं देखें",
  "Settings.application.link-upgrade": "अपना व्यवस्थापक पैनल अपग्रेड करें",
  "Settings.application.node-version": "नोड संस्करण",
  "Settings.application.strapi-version": "स्ट्रैपी संस्करण",
  "Settings.application.strapiVersion": "स्ट्रैपी संस्करण",
  "Settings.application.title": "अवलोकन",
  "Settings.error": "गलती",
  "Settings.global": "वैश्विक सेटिंग्स",
  "Settings.permissions": "प्रशासन पैनल",
  "Settings.permissions.category": "{श्रेणी} के लिए अनुमतियां सेटिंग",
  "Settings.permissions.category.plugins": "{श्रेणी} प्लगइन के लिए अनुमतियाँ सेटिंग",
  "Settings.permissions.conditions.anytime": "किसी भी समय",
  "Settings.permissions.conditions.apply": "आवेदन करना",
  "Settings.permissions.conditions.can": "कर सकना",
  "Settings.permissions.conditions.conditions": "शर्तों को परिभाषित करें",
  "Settings.permissions.conditions.links": "लिंक",
  "Settings.permissions.conditions.no-actions": "उन पर शर्तों को परिभाषित करने से पहले आपको पहले क्रियाओं का चयन करना होगा (बनाएं, पढ़ें, अपडेट करें, ...)",
  "Settings.permissions.conditions.none-selected": "किसी भी समय",
  "Settings.permissions.conditions.or": "या",
  "Settings.permissions.conditions.when": "कब",
  "Settings.permissions.select-all-by-permission": "सभी {लेबल} अनुमतियों का चयन करें",
  "Settings.permissions.select-by-permission": "{लेबल} अनुमति चुनें",
  "Settings.permissions.users.create": "नए उपयोगकर्ता को आमंत्रित करें",
  "Settings.permissions.users.email": "ईमेल",
  "Settings.permissions.users.firstname": "प्रथम नाम",
  "Settings.permissions.users.lastname": "उपनाम",
  "Settings.permissions.users.form.sso": "एसएसओ से जुड़ें",
  "Settings.permissions.users.form.sso.description": "सक्षम (चालू) होने पर, उपयोगकर्ता एसएसओ के माध्यम से लॉगिन कर सकते हैं",
  "Settings.permissions.users.listview.header.subtitle": "वे सभी उपयोगकर्ता जिनके पास Strapi व्यवस्थापक पैनल तक पहुंच है",
  "Settings.permissions.users.tabs.label": "टैब अनुमतियां",
  "Settings.profile.form.notify.data.loaded": "आपका प्रोफ़ाइल डेटा लोड कर दिया गया है",
  "Settings.profile.form.section.experience.clear.select": "चयनित इंटरफ़ेस भाषा साफ़ करें",
  "Settings.profile.form.section.experience.here": "यहां",
  "Settings.profile.form.section.experience.interfaceLanguage": "अंतरफलक भाषा",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "यह केवल आपके स्वयं के इंटरफ़ेस को चुनी हुई भाषा में प्रदर्शित करेगा।",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "वरीयता परिवर्तन केवल आप पर लागू होंगे। यहां अधिक जानकारी उपलब्ध है}।",
  "Settings.profile.form.section.experience.mode.label": "इंटरफ़ेस मोड",
  "Settings.profile.form.section.experience.mode.hint": "आपके इंटरफ़ेस को चुने हुए मोड में प्रदर्शित करता है।",
  "Settings.profile.form.section.experience.mode.option-label": "{नाम} मोड",
  "Settings.profile.form.section.experience.title": "अनुभव",
  "Settings.profile.form.section.head.title": "उपयोगकर्ता रूपरेखा",
  "Settings.profile.form.section.profile.page.title": "प्रोफ़ाइल पृष्ठ",
  "Settings.roles.create.description": "भूमिका को दिए गए अधिकारों को परिभाषित करें",
  "Settings.roles.create.title": "एक भूमिका बनाएँ",
  "Settings.roles.created": "भूमिका सृजित",
  "Settings.roles.edit.title": "भूमिका संपादित करें",
  "Settings.roles.form.button.users-with-role": "{संख्या, बहुवचन, =0 {# उपयोगकर्ता} एक {# उपयोगकर्ता} अन्य {# उपयोगकर्ता}} इस भूमिका के साथ",
  "Settings.roles.form.created": "बनाया था",
  "Settings.roles.form.description": "भूमिका का नाम और विवरण",
  "Settings.roles.form.permission.property-label": "{लेबल} अनुमतियां",
  "Settings.roles.form.permissions.attributesPermissions": "फ़ील्ड अनुमतियाँ",
  "Settings.roles.form.permissions.create": "सृजन करना",
  "Settings.roles.form.permissions.delete": "मिटाना",
  "Settings.roles.form.permissions.publish": "प्रकाशित करना",
  "Settings.roles.form.permissions.read": "पढ़ना",
  "Settings.roles.form.permissions.update": "अद्यतन",
  "Settings.roles.list.button.add": "नई भूमिका जोड़ें",
  "Settings.roles.list.description": "भूमिकाओं की सूची",
  "Settings.roles.title.singular": "भूमिका",
  "Settings.sso.description": "एकल साइन-ऑन सुविधा के लिए सेटिंग्स कॉन्फ़िगर करें।",
  "Settings.sso.form.defaultRole.description": "यह नए प्रमाणित उपयोगकर्ता को चयनित भूमिका से जोड़ देगा",
  "Settings.sso.form.defaultRole.description-not-allowed": "आपको व्यवस्थापक भूमिकाओं को पढ़ने की अनुमति की आवश्यकता है",
  "Settings.sso.form.defaultRole.label": "डिफ़ॉल्ट भूमिका",
  "Settings.sso.form.registration.description": "यदि कोई खाता मौजूद नहीं है तो SSO लॉगिन पर नया उपयोगकर्ता बनाएं",
  "Settings.sso.form.registration.label": "स्वतः पंजीकरण",
  "Settings.sso.title": "एक बार दर्ज करना",
  "Settings.webhooks.create": "एक वेबहुक बनाएं",
  "Settings.webhooks.create.header": "नया हेडर बनाएं",
  "Settings.webhooks.created": "वेबहुक बनाया गया",
  "Settings.webhooks.event.publish-tooltip": "यह घटना केवल उन सामग्रियों के लिए मौजूद है जिनमें ड्राफ्ट/प्रकाशन प्रणाली सक्षम है",
  "Settings.webhooks.events.create": "सृजन करना",
  "Settings.webhooks.events.update": "अद्यतन",
  "Settings.webhooks.form.events": "आयोजन",
  "Settings.webhooks.form.headers": "हेडर",
  "Settings.webhooks.form.url": "यूआरएल",
  "Settings.webhooks.headers.remove": "शीर्षलेख पंक्ति निकालें {संख्या}",
  "Settings.webhooks.key": "चाभी",
  "Settings.webhooks.list.button.add": "नया वेबहुक बनाएं",
  "Settings.webhooks.list.description": "POST परिवर्तन सूचनाएं प्राप्त करें",
  "Settings.webhooks.list.empty.description": "कोई वेबहुक नहीं मिला",
  "Settings.webhooks.list.empty.link": "हमारे दस्तावेज़ देखें",
  "Settings.webhooks.list.empty.title": "अभी तक कोई वेबहुक नहीं है",
  "Settings.webhooks.list.th.actions": "कार्रवाई",
  "Settings.webhooks.list.th.status": "दर्जा",
  "Settings.webhooks.singular": " वेबहुक",
  "Settings.webhooks.title": " वेबहुक",
  "Settings.webhooks.to.delete": "{webhooksToDeleteLength, बहुवचन, एक {# संपत्ति} अन्य {# संपत्ति}} चयनित",
  "Settings.webhooks.trigger": "चालू कर देना",
  "Settings.webhooks.trigger.cancel": "ट्रिगर रद्द करें",
  "Settings.webhooks.trigger.pending": "लंबित…",
  "Settings.webhooks.trigger.save": "कृपया ट्रिगर करने के लिए सहेजें",
  "Settings.webhooks.trigger.success": "सफलता!",
  "Settings.webhooks.trigger.success.label": "ट्रिगर सफल हुआ",
  "Settings.webhooks.trigger.test": "टेस्ट-ट्रिगर",
  "Settings.webhooks.trigger.title": "ट्रिगर से पहले सहेजें",
  "Settings.webhooks.value": "मूल्य",
  "Usecase.back-end": "बैक-एंड डेवलपर",
  "Usecase.button.skip": "इस प्रश्न को छोड़ें",
  "Usecase.content-creator": "सामग्री निर्माता",
  "Usecase.front-end": "फ़्रंट एंड डेवलपर",
  "Usecase.full-stack": "पूरी स्टैक बनानेवाला",
  "Usecase.input.work-type": "आप किस तरह का काम करते है?",
  "Usecase.notification.success.project-created": "परियोजना सफलतापूर्वक बनाई गई है",
  "Usecase.other": "अन्य",
  "Usecase.title": "अपने बारे में कुछ और बताएं",
  Username,
  Users,
  "Users & Permissions": "उपयोगकर्ता और अनुमतियां",
  "Users.components.List.empty": "कोई उपयोगकर्ता नहीं है ...",
  "Users.components.List.empty.withFilters": "लागू फ़िल्टर वाले कोई उपयोगकर्ता नहीं हैं...",
  "Users.components.List.empty.withSearch": "खोज से संबंधित कोई उपयोगकर्ता नहीं है ({search})...",
  "admin.pages.MarketPlacePage.head": "बाज़ार - प्लगइन्स",
  "admin.pages.MarketPlacePage.offline.title": "आप संपर्क में नहीं हैं",
  "admin.pages.MarketPlacePage.offline.subtitle": "Strapi Market तक पहुंचने के लिए आपको इंटरनेट से कनेक्ट होने की आवश्यकता है।",
  "admin.pages.MarketPlacePage.plugin.copy": "कॉपी इंस्टॉल कमांड",
  "admin.pages.MarketPlacePage.plugin.copy.success": "अपने टर्मिनल में चिपकाने के लिए तैयार कमांड स्थापित करें",
  "admin.pages.MarketPlacePage.plugin.info": "और अधिक जानें",
  "admin.pages.MarketPlacePage.plugin.info.label": "{प्लगइननाम} के बारे में अधिक जानें",
  "admin.pages.MarketPlacePage.plugin.info.text": "और अधिक जानें",
  "admin.pages.MarketPlacePage.plugin.installed": "स्थापित",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "Strapi . द्वारा निर्मित",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "Strapi द्वारा सत्यापित प्लगइन",
  "admin.pages.MarketPlacePage.search.clear": "प्लगइन खोज साफ़ करें",
  "admin.pages.MarketPlacePage.search.empty": '"{target}" के लिए कोई परिणाम नहीं',
  "admin.pages.MarketPlacePage.search.placeholder": "एक प्लगइन खोजें",
  "admin.pages.MarketPlacePage.submit.plugin.link": "अपना प्लगइन सबमिट करें",
  "admin.pages.MarketPlacePage.subtitle": "Strapi . से अधिक लाभ उठाएं",
  "admin.pages.MarketPlacePage.missingPlugin.title": "एक प्लगइन गुम है?",
  "admin.pages.MarketPlacePage.missingPlugin.description": "हमें बताएं कि आप किस प्लगइन की तलाश कर रहे हैं और अगर वे प्रेरणा की तलाश में हैं तो हम अपने समुदाय प्लगइन डेवलपर्स को बताएंगे!",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "क्लिपबोर्ड पर कॉपी करें",
  "app.component.search.label": "{लक्ष्य} खोजें",
  "app.component.table.duplicate": "डुप्लीकेट {लक्ष्य}",
  "app.component.table.edit": "संपादित करें {लक्ष्य}",
  "app.component.table.select.one-entry": "{लक्ष्य} चुनें",
  "app.components.BlockLink.blog": "ब्लॉग",
  "app.components.BlockLink.blog.content": "Strapi और पारिस्थितिकी तंत्र के बारे में नवीनतम समाचार पढ़ें।",
  "app.components.BlockLink.code": "कोड उदाहरण",
  "app.components.BlockLink.code.content": "वास्तविक परियोजनाओं का परीक्षण करके जानें समुदाय का विकास हुआ।",
  "app.components.BlockLink.documentation.content": "आवश्यक अवधारणाओं, गाइडों और निर्देशों की खोज करें।",
  "app.components.BlockLink.tutorial": "ट्यूटोरियल",
  "app.components.BlockLink.tutorial.content": "Strapi का उपयोग और कस्टमाइज़ करने के लिए चरण-दर-चरण निर्देशों का पालन करें।",
  "app.components.Button.cancel": "रद्द करना",
  "app.components.Button.confirm": "पुष्टि करें",
  "app.components.Button.reset": "रीसेट",
  "app.components.ComingSoonPage.comingSoon": "जल्द आ रहा है",
  "app.components.ConfirmDialog.title": "पुष्टीकरण",
  "app.components.DownloadInfo.download": "डाउनलोड जारी है...",
  "app.components.DownloadInfo.text": "इसमें एक मिनट लग सकता है। आपके धैर्य के लिए धन्यवाद।",
  "app.components.EmptyAttributes.title": "अभी तक कोई फ़ील्ड नहीं है",
  "app.components.EmptyStateLayout.content-document": "कोई सामग्री नहीं मिली",
  "app.components.EmptyStateLayout.content-permissions": "आपके पास उस सामग्री तक पहुंचने की अनुमति नहीं है",
  "app.components.GuidedTour.CM.create.content": "<p>कंटेंट मैनेजर में यहां सभी सामग्री बनाएं और प्रबंधित करें।</p><p>पूर्व: ब्लॉग वेबसाइट के उदाहरण को आगे बढ़ाते हुए, कोई एक लेख लिख सकता है, उसे सहेज सकता है और अपनी पसंद के अनुसार प्रकाशित कर सकता है।</p>< p>💡 त्वरित युक्ति - आपके द्वारा बनाई गई सामग्री पर प्रकाशित करें हिट करना न भूलें।</p>",
  "app.components.GuidedTour.CM.create.title": "️ सामग्री बनाएं",
  "app.components.GuidedTour.CM.success.content": "<p>बहुत बढ़िया, एक आखिरी कदम!</p><b>🚀 सामग्री को क्रियान्वित देखें</b>",
  "app.components.GuidedTour.CM.success.cta.title": "एपीआई का परीक्षण करें",
  "app.components.GuidedTour.CM.success.title": "चरण 2: पूर्ण",
  "app.components.GuidedTour.CTB.create.content": "<p>संग्रह प्रकार आपको कई प्रविष्टियों को प्रबंधित करने में मदद करते हैं, एकल प्रकार केवल एक प्रविष्टि को प्रबंधित करने के लिए उपयुक्त होते हैं।</p> <p>उदा: एक ब्लॉग वेबसाइट के लिए, लेख एक संग्रह प्रकार होगा जबकि एक मुखपृष्ठ एकल प्रकार होगा। </p>",
  "app.components.GuidedTour.CTB.create.cta.title": "एक संग्रह प्रकार बनाएँ",
  "app.components.GuidedTour.CTB.create.title": "🧠 पहला संग्रह प्रकार बनाएं",
  "app.components.GuidedTour.CTB.success.content": "<p>अच्छा चल रहा है!</p><b>⚡️ आप दुनिया के साथ क्या साझा करना चाहेंगे?</b>",
  "app.components.GuidedTour.CTB.success.title": "चरण 1: पूर्ण✅",
  "app.components.GuidedTour.apiTokens.create.content": "<p>यहां एक प्रमाणीकरण टोकन जेनरेट करें और आपके द्वारा अभी-अभी बनाई गई सामग्री को पुनः प्राप्त करें।</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "एक एपीआई टोकन उत्पन्न करें",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 कार्रवाई में सामग्री देखें",
  "app.components.GuidedTour.apiTokens.success.content": "<p>HTTP अनुरोध करके सामग्री को क्रियान्वित देखें:</p><ul><li><p>इस URL पर: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT '>'</light></p></li><p>शीर्षक के साथ: <light>प्राधिकरण: वाहक '<'Your_API_TOKEN'>'</light></p></li> </ul><p>सामग्री के साथ इंटरैक्ट करने के अधिक तरीकों के लिए, <दस्तावेज़ीकरणलिंक>दस्तावेज़ीकरण</दस्तावेज़ीकरणलिंक> देखें।</p>",
  "app.components.GuidedTour.apiTokens.success.cta.title": "होमपेज पर वापस जाएं",
  "app.components.GuidedTour.apiTokens.success.title": "चरण 3: पूर्ण✅",
  "app.components.GuidedTour.create-content": "सामग्री बनाएं",
  "app.components.GuidedTour.home.CM.title": "⚡️ आप दुनिया के साथ क्या साझा करना चाहेंगे?",
  "app.components.GuidedTour.home.CTB.cta.title": "सामग्री प्रकार बिल्डर पर जाएं",
  "app.components.GuidedTour.home.CTB.title": "🧠 सामग्री संरचना बनाएँ",
  "app.components.GuidedTour.home.apiTokens.cta.title": "एपीआई का परीक्षण करें",
  "app.components.GuidedTour.skip": "टूर छोड़ें",
  "app.components.GuidedTour.title": "आरंभ करने के लिए 3 कदम",
  "app.components.HomePage.button.blog": "ब्लॉग पर और देखें",
  "app.components.HomePage.community": "समुदाय में शामिल हों",
  "app.components.HomePage.community.content": "विभिन्न चैनलों पर टीम के सदस्यों, योगदानकर्ताओं और डेवलपर्स के साथ चर्चा करें।",
  "app.components.HomePage.create": "अपना पहला सामग्री प्रकार बनाएं",
  "app.components.HomePage.roadmap": "हमारा रोडमैप देखें",
  "app.components.HomePage.welcome": "बोर्ड पर आपका स्वागत है 👋",
  "app.components.HomePage.welcome.again": "स्वागत है",
  "app.components.HomePage.welcomeBlock.content": "बधाई! आप पहले व्यवस्थापक के रूप में लॉग इन हैं। Strapi द्वारा प्रदान की गई शक्तिशाली सुविधाओं को खोजने के लिए, हम आपको अपना पहला सामग्री प्रकार बनाने की सलाह देते हैं!",
  "app.components.HomePage.welcomeBlock.content.again": "हमें उम्मीद है कि आप अपनी परियोजना पर प्रगति कर रहे हैं! Strapi के बारे में नवीनतम समाचार पढ़ने के लिए स्वतंत्र महसूस करें। हम आपकी प्रतिक्रिया के आधार पर उत्पाद को बेहतर बनाने के लिए अपना सर्वश्रेष्ठ दे रहे हैं।",
  "app.components.HomePage.welcomeBlock.content.issues": "मुद्दे।",
  "app.components.HomePage.welcomeBlock.content.raise": " या बढ़ाओ",
  "app.components.ImgPreview.hint": "अपनी फ़ाइल को इस क्षेत्र में खींचें और छोड़ें या किसी फ़ाइल को अपलोड करने के लिए {ब्राउज़ करें}",
  "app.components.ImgPreview.hint.browse": "ब्राउज़",
  "app.components.InputFile.newFile": "नई फ़ाइल जोड़ें",
  "app.components.InputFileDetails.open": "एक नए टैब में खोलें",
  "app.components.InputFileDetails.originalName": "मूल नाम:",
  "app.components.InputFileDetails.remove": "इस फ़ाइल को हटाएं",
  "app.components.InputFileDetails.size": "आकार:",
  "app.components.InstallPluginPage.Download.description": "प्लगइन को डाउनलोड और इंस्टॉल करने में कुछ सेकंड लग सकते हैं।",
  "app.components.InstallPluginPage.Download.title": "डाउनलोड हो रहा है...",
  "app.components.InstallPluginPage.description": "अपने ऐप को आसानी से बढ़ाएं।",
  "app.components.LeftMenu.collapse": "नेवबार को संक्षिप्त करें",
  "app.components.LeftMenu.expand": "नवबार का विस्तार करें",
  "app.components.LeftMenu.logout": "लॉग आउट",
  "app.components.LeftMenu.navbrand.title": "स्ट्रैपी डैशबोर्ड",
  "app.components.LeftMenu.navbrand.workplace": "कार्यस्थल",
  "app.components.LeftMenuFooter.help": "मदद करना",
  "app.components.LeftMenuFooter.poweredBy": "द्वारा संचालित ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "संग्रह प्रकार",
  "app.components.LeftMenuLinkContainer.configuration": "विन्यास",
  "app.components.LeftMenuLinkContainer.general": "सामान्य",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "अभी तक कोई प्लग इन इंस्टॉल नहीं है",
  "app.components.LeftMenuLinkContainer.plugins": "प्लग-इन",
  "app.components.LeftMenuLinkContainer.singleTypes": "एकल प्रकार",
  "app.components.ListPluginsPage.deletePlugin.description": "प्लगइन को अनइंस्टॉल करने में कुछ सेकंड लग सकते हैं।",
  "app.components.ListPluginsPage.deletePlugin.title": "की स्थापना रद्द",
  "app.components.ListPluginsPage.description": "परियोजना में स्थापित प्लगइन्स की सूची।",
  "app.components.ListPluginsPage.head.title": "सूची प्लगइन्स",
  "app.components.Logout.logout": "लॉग आउट",
  "app.components.Logout.profile": "प्रोफ़ाइल",
  "app.components.MarketplaceBanner": "Strapi Awesome पर समुदाय द्वारा बनाए गए प्लग इन और अपने प्रोजेक्ट को किकस्टार्ट करने के लिए कई और शानदार चीज़ें खोजें।",
  "app.components.MarketplaceBanner.image.alt": "एक स्ट्रैपी रॉकेट लोगो",
  "app.components.MarketplaceBanner.link": "अब इसे जांचें",
  "app.components.NotFoundPage.back": "मुखपृष्ठ पर वापस",
  "app.components.NotFoundPage.description": "पता नहीं चला",
  "app.components.Official": "अधिकारी",
  "app.components.Onboarding.help.button": "सहायता बटन",
  "app.components.Onboarding.label.completed": "% पूरा किया हुआ",
  "app.components.Onboarding.title": "वीडियो शुरू करें",
  "app.components.PluginCard.Button.label.download": "डाउनलोड",
  "app.components.PluginCard.Button.label.install": "पहले से ही इनस्टाल्ड है",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "AutoReload सुविधा को सक्षम करने की आवश्यकता है। कृपया अपना ऐप `यार्न डिवेलप` के साथ शुरू करें।",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "मै समझता हुँ!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "सुरक्षा कारणों से, एक प्लगइन केवल विकास के माहौल में ही डाउनलोड किया जा सकता है।",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "डाउनलोड करना असंभव है",
  "app.components.PluginCard.compatible": "आपके ऐप के साथ संगत",
  "app.components.PluginCard.compatibleCommunity": "समुदाय के साथ संगत",
  "app.components.PluginCard.more-details": "अधिक जानकारी",
  "app.components.ToggleCheckbox.off-label": "असत्य",
  "app.components.ToggleCheckbox.on-label": "सत्य",
  "app.components.Users.MagicLink.connect": "इस उपयोगकर्ता को एक्सेस देने के लिए इस लिंक को कॉपी और शेयर करें",
  "app.components.Users.MagicLink.connect.sso": "उपयोगकर्ता को यह लिंक भेजें, पहला लॉगिन एसएसओ प्रदाता के माध्यम से किया जा सकता है",
  "app.components.Users.ModalCreateBody.block-title.details": "उपयोगकर्ता की जानकारी",
  "app.components.Users.ModalCreateBody.block-title.roles": "उपयोगकर्ता की भूमिकाएँ",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "एक उपयोगकर्ता की एक या कई भूमिकाएँ हो सकती हैं",
  "app.components.Users.SortPicker.button-label": "इसके अनुसार क्रमबद्ध करें",
  "app.components.Users.SortPicker.sortby.email_asc": "ईमेल (A से  Z)",
  "app.components.Users.SortPicker.sortby.email_desc": "ईमेल (Z से A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "प्रथम नाम  (A से  Z)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "प्रथम नाम (Z से A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "उपनाम(A से  Z)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "उपनाम (Z से A)",
  "app.components.Users.SortPicker.sortby.username_asc": "उपयोगकर्ता नाम(A से  Z)",
  "app.components.Users.SortPicker.sortby.username_desc": "उपयोगकर्ता नाम (Z से A))",
  "app.components.listPlugins.button": "नया प्लगइन जोड़ें",
  "app.components.listPlugins.title.none": "कोई प्लग इन इंस्टॉल नहीं है",
  "app.components.listPluginsPage.deletePlugin.error": "प्लगइन की स्थापना रद्द करते समय एक त्रुटि हुई",
  "app.containers.App.notification.error.init": "एपीआई का अनुरोध करते समय एक त्रुटि हुई",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "यदि आपको यह लिंक नहीं मिलता है, तो कृपया अपने व्यवस्थापक से संपर्क करें।",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "आपका पासवर्ड पुनर्प्राप्ति लिंक प्राप्त करने में कुछ मिनट लग सकते हैं।",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "ईमेल भेजा",
  "app.containers.Users.EditPage.form.active.label": "सक्रिय",
  "app.containers.Users.EditPage.header.label": "नाम संपादित करें",
  "app.containers.Users.EditPage.header.label-loading": "यूजर को संपादित करो",
  "app.containers.Users.EditPage.roles-bloc-title": "जिम्मेदार भूमिकाएं",
  "app.containers.Users.ModalForm.footer.button-success": "उपयोगकर्ता को आमंत्रित करें",
  "app.links.configure-view": "दृश्य कॉन्फ़िगर करें",
  "app.page.not.found": "उफ़! आप जिस पेज की तलाश कर रहे हैं, वह हमें नहीं मिल रहा है...",
  "app.static.links.cheatsheet": "प्रवंचक पत्रक",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "फ़िल्टर जोड़ें",
  "app.utils.close-label": "बंद करना",
  "app.utils.defaultMessage": " ",
  "app.utils.duplicate": "डुप्लिकेट",
  "app.utils.edit": "संपादन करना",
  "app.utils.errors.file-too-big.message": "फ़ाइल बहुत बड़ी है",
  "app.utils.filter-value": "फ़िल्टर मान",
  "app.utils.filters": "फ़िल्टर",
  "app.utils.notify.data-loaded": "{target} लोड हो गया है",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "प्रकाशित करना",
  "app.utils.select-all": "सभी का चयन करे",
  "app.utils.select-field": "फ़ील्ड चुनें",
  "app.utils.select-filter": "फ़िल्टर चुनें",
  "app.utils.unpublish": "अप्रकाशित",
  clearLabel,
  "coming.soon": "यह सामग्री वर्तमान में निर्माणाधीन है और कुछ हफ्तों में वापस आ जाएगी!",
  "component.Input.error.validation.integer": "मान एक पूर्णांक होना चाहिए",
  "components.AutoReloadBlocker.description": "निम्न आदेशों में से एक के साथ Strapi चलाएँ:",
  "components.AutoReloadBlocker.header": "इस प्लगइन के लिए रीलोड फीचर की आवश्यकता है।",
  "components.ErrorBoundary.title": "कुछ गलत हो गया...",
  "components.FilterOptions.FILTER_TYPES.$contains": "शामिल है",
  "components.FilterOptions.FILTER_TYPES.$containsi": "शामिल है (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "इसी के साथ समाप्त होता है",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "इसी के साथ समाप्त होता है (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$eq": "है",
  "components.FilterOptions.FILTER_TYPES.$eqi": "है (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$gt": "से बड़ा है",
  "components.FilterOptions.FILTER_TYPES.$gte": "से अधिक या बराबर है",
  "components.FilterOptions.FILTER_TYPES.$lt": "से कम है",
  "components.FilterOptions.FILTER_TYPES.$lte": "से कम या उसके बराबर है",
  "components.FilterOptions.FILTER_TYPES.$ne": "नहीं है",
  "components.FilterOptions.FILTER_TYPES.$nei": "नहीं है (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "शामिल नहीं है",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "शामिल नहीं है (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "निरर्थक नहीं है",
  "components.FilterOptions.FILTER_TYPES.$null": "निरर्थक है",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "इसके साथ आरंभ होता है",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "इसके साथ आरंभ होता है (case insensitive)",
  "components.Input.error.attribute.key.taken": "यह मान पहले से मौजूद है",
  "components.Input.error.attribute.sameKeyAndName": "बराबर नहीं हो सकता",
  "components.Input.error.attribute.taken": "यह फ़ील्ड नाम पहले से मौजूद है",
  "components.Input.error.contain.lowercase": "पासवर्ड में कम से कम एक लोअरकेस वर्ण होना चाहिए",
  "components.Input.error.contain.number": "सांकेतिक शब्द में कम से कम एक संख्या शामिल होना चाहिए",
  "components.Input.error.contain.uppercase": "पासवर्ड में कम से कम एक बड़ा अक्षर होना चाहिए",
  "components.Input.error.contentTypeName.taken": "यह नाम पहले से ही मौजूद है",
  "components.Input.error.custom-error": "{त्रुटि संदेश} ",
  "components.Input.error.password.noMatch": "पासवर्ड मेल नहीं खाते",
  "components.Input.error.validation.email": "यह एक अमान्य ईमेल है",
  "components.Input.error.validation.json": "यह JSON प्रारूप से मेल नहीं खाता",
  "components.Input.error.validation.lowercase": "मान एक लोअरकेस स्ट्रिंग होना चाहिए",
  "components.Input.error.validation.max": "मान बहुत ज़्यादा है {max}.",
  "components.Input.error.validation.maxLength": "मान बहुत लंबा है {max}.",
  "components.Input.error.validation.min": "मान बहुत कम है {min}.",
  "components.Input.error.validation.minLength": "मान बहुत छोटा है {min}.",
  "components.Input.error.validation.minSupMax": "श्रेष्ठ नहीं हो सकता",
  "components.Input.error.validation.regex": "मान रेगेक्स से मेल नहीं खाता।",
  "components.Input.error.validation.required": "यह मान आवश्यक है।",
  "components.Input.error.validation.unique": "यह मान पहले ही उपयोग किया जा चुका है।",
  "components.InputSelect.option.placeholder": "यहां चुनें",
  "components.ListRow.empty": "दिखाने के लिए कोई डेटा नहीं है।",
  "components.NotAllowedInput.text": "इस फ़ील्ड को देखने की अनुमति नहीं है",
  "components.OverlayBlocker.description": "आप एक ऐसी सुविधा का उपयोग कर रहे हैं जिसके लिए सर्वर को पुनरारंभ करने की आवश्यकता है। कृपया सर्वर चालू होने तक प्रतीक्षा करें.",
  "components.OverlayBlocker.description.serverError": "सर्वर को पुनरारंभ होना चाहिए था, कृपया टर्मिनल में अपने लॉग जांचें।",
  "components.OverlayBlocker.title": "पुनः आरंभ की प्रतीक्षा कर रहा है...",
  "components.OverlayBlocker.title.serverError": "पुनरारंभ अपेक्षा से अधिक समय ले रहा है",
  "components.PageFooter.select": "प्रति पृष्ठ प्रविष्टियाँ",
  "components.ProductionBlocker.description": "सुरक्षा उद्देश्यों के लिए हमें इस प्लगइन को अन्य वातावरणों में अक्षम करना होगा।",
  "components.ProductionBlocker.header": "यह प्लगइन केवल विकास में उपलब्ध है।",
  "components.Search.placeholder": "खोज...",
  "components.TableHeader.sort": "{लेबल} पर छाँटें",
  "components.Wysiwyg.ToggleMode.markdown-mode": "मार्कडाउन मोड",
  "components.Wysiwyg.ToggleMode.preview-mode": "पूर्वावलोकन मोड",
  "components.Wysiwyg.collapse": "गिर जाना",
  "components.Wysiwyg.selectOptions.H1": "शीर्षक H1",
  "components.Wysiwyg.selectOptions.H2": "शीर्षक H2",
  "components.Wysiwyg.selectOptions.H3": "शीर्षक H3",
  "components.Wysiwyg.selectOptions.H4": "शीर्षक H4",
  "components.Wysiwyg.selectOptions.H5": "शीर्षक H5",
  "components.Wysiwyg.selectOptions.H6": "शीर्षक H6",
  "components.Wysiwyg.selectOptions.title": "एक शीर्षक जोड़ें",
  "components.WysiwygBottomControls.charactersIndicators": "पात्र",
  "components.WysiwygBottomControls.fullscreen": "बढ़ाना",
  "components.WysiwygBottomControls.uploadFiles": "फ़ाइलें खींचें और छोड़ें, क्लिपबोर्ड से चिपकाएं या {ब्राउज़ करें}।",
  "components.WysiwygBottomControls.uploadFiles.browse": "उन्हें चुनें",
  "components.pagination.go-to": "पेज {पेज} पर जाएं",
  "components.pagination.go-to-next": "अगले पेज पर जाएं",
  "components.pagination.go-to-previous": "पिछले पृष्ठ पर जाएं",
  "components.pagination.remaining-links": "और {नंबर} अन्य लिंक",
  "components.popUpWarning.button.cancel": "नहीं, रद्द करें",
  "components.popUpWarning.button.confirm": "हाँ, पुष्टि करें",
  "components.popUpWarning.message": "क्या आप वाकई इसे हटाना चाहते हैं?",
  "components.popUpWarning.title": "कृपया पुष्टि करें",
  "form.button.continue": "जारी रखना",
  "form.button.done": "पूर्ण",
  "global.actions": "कार्रवाई",
  "global.back": "पीछे",
  "global.change-password": "पासवर्ड बदलें",
  "global.content-manager": "सामग्री प्रबंधक",
  "global.continue": "जारी रखना",
  "global.delete": "मिटाना",
  "global.delete-target": "{target} मिटाएं",
  "global.description": "विवरण",
  "global.details": "विवरण",
  "global.disabled": "अक्षम",
  "global.documentation": "प्रलेखन",
  "global.enabled": "सक्षम",
  "global.finish": "खत्म करना",
  "global.marketplace": "बाजार",
  "global.name": "नाम",
  "global.none": "कोई भी नहीं",
  "global.password": "पासवर्ड",
  "global.plugins": "प्लग-इन",
  "global.profile": "प्रोफ़ाइल",
  "global.prompt.unsaved": "क्या आप वाकई इस पेज को छोड़ना चाहते हैं? आपके सभी संशोधन नष्ट हो जाएंगे",
  "global.reset-password": "पासवर्ड रीसेट",
  "global.roles": "भूमिकाएँ",
  "global.save": "बचाना",
  "global.see-more": "और देखें",
  "global.select": "चुनना",
  "global.select-all-entries": "सभी प्रविष्टियों का चयन करें",
  "global.settings": "समायोजन",
  "global.type": "टाइप",
  "global.users": "उपयोगकर्ताओं",
  "notification.contentType.relations.conflict": "सामग्री प्रकार में परस्पर विरोधी संबंध हैं",
  "notification.default.title": "जानकारी:",
  "notification.error": "एक त्रुटि पाई गई",
  "notification.error.layout": "लेआउट पुनर्प्राप्त नहीं किया जा सका",
  "notification.form.error.fields": "प्रपत्र में कुछ त्रुटियां हैं",
  "notification.form.success.fields": "बदलाव सुरक्षित किया गया",
  "notification.link-copied": "लिंक को क्लिपबोर्ड में कॉपी किया गया",
  "notification.permission.not-allowed-read": "आपको यह दस्तावेज़ देखने की अनुमति नहीं है",
  "notification.success.delete": "आइटम हटा दिया गया है",
  "notification.success.saved": "बचाया",
  "notification.success.title": "सफलता:",
  "notification.version.update.message": "Strapi का एक नया संस्करण उपलब्ध है!",
  "notification.warning.title": "चेतावनी:",
  or,
  "request.error.model.unknown": "यह मॉडल मौजूद नहीं है",
  skipToContent,
  submit
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  hi as default,
  or,
  skipToContent,
  submit
};
//# sourceMappingURL=hi-DW2CutdA-GRTOSTXU.js.map
