import{au as P,a as R,ax as h,e as F,al as v,ba as I,c as V,r as g,bl as D,m as e,n as i,L as T,bK as p,b0 as b,aU as y,bM as x,bN as L}from"./strapi-YzJfjJ2z.js";import{u as U}from"./useOnce-NHeEacbN-BN84kb-5.js";import{c as _,d as B}from"./transferTokens-CXTFej3W-kOI9Cp4M.js";import{T as o}from"./constants-CRj0ViV1-Q2dfXdfa.js";import{T as H}from"./Table-C6ZlhkWY-DbvwIyjV.js";const K=[{name:"name",label:{id:"Settings.tokens.ListView.headers.name",defaultMessage:"Name"},sortable:!0},{name:"description",label:{id:"Settings.tokens.ListView.headers.description",defaultMessage:"Description"},sortable:!1},{name:"createdAt",label:{id:"Settings.tokens.ListView.headers.createdAt",defaultMessage:"Created at"},sortable:!1},{name:"lastUsedAt",label:{id:"Settings.tokens.ListView.headers.lastUsedAt",defaultMessage:"Last used"},sortable:!1}],O=()=>{const{formatMessage:s}=P(),{toggleNotification:a}=R(),A=h(n=>n.admin_app.permissions.settings?.["transfer-tokens"]),{isLoading:j,allowedActions:{canCreate:d,canDelete:S,canUpdate:w,canRead:l}}=F(A),u=v(),{trackUsage:r}=I(),{_unstableFormatAPIError:c}=V();g.useEffect(()=>{u({search:D.stringify({sort:"name:ASC"},{encode:!1})})},[u]),U(()=>{r("willAccessTokenList",{tokenType:o})});const M=K.map(n=>({...n,label:s(n.label)})),{data:t=[],isLoading:E,error:f}=_(void 0,{skip:!l});g.useEffect(()=>{t&&r("didAccessTokenList",{number:t.length,tokenType:o})},[r,t]),g.useEffect(()=>{f&&a({type:"danger",message:c(f)})},[f,c,a]);const[C]=B(),N=async n=>{try{const k=await C(n);"error"in k&&a({type:"danger",message:c(k.error)})}catch{a({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occured"})})}},m=E||j;return e.jsxs(e.Fragment,{children:[e.jsx(i.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Transfer Tokens"})}),e.jsx(T.Header,{title:s({id:"Settings.transferTokens.title",defaultMessage:"Transfer Tokens"}),subtitle:s({id:"Settings.transferTokens.description",defaultMessage:'"List of generated transfer tokens"'}),primaryAction:d?e.jsx(p,{role:"button",tag:y,"data-testid":"create-transfer-token-button",startIcon:e.jsx(b,{}),size:"S",onClick:()=>r("willAddTokenFromList",{tokenType:o}),to:"/settings/transfer-tokens/create",children:s({id:"Settings.transferTokens.create",defaultMessage:"Create new Transfer Token"})}):void 0}),l?e.jsx(i.Main,{"aria-busy":m,children:e.jsxs(T.Content,{children:[t.length>0&&e.jsx(H,{permissions:{canRead:l,canDelete:S,canUpdate:w},headers:M,isLoading:m,onConfirmDelete:N,tokens:t,tokenType:o}),d&&t.length===0?e.jsx(x,{action:e.jsx(p,{tag:y,variant:"secondary",startIcon:e.jsx(b,{}),to:"/settings/transfer-tokens/create",children:s({id:"Settings.transferTokens.addNewToken",defaultMessage:"Add new Transfer Token"})}),icon:e.jsx(L,{width:"16rem"}),content:s({id:"Settings.transferTokens.addFirstToken",defaultMessage:"Add your first Transfer Token"})}):null,!d&&t.length===0?e.jsx(x,{icon:e.jsx(L,{width:"16rem"}),content:s({id:"Settings.transferTokens.emptyStateLayout",defaultMessage:"You don’t have any content yet..."})}):null]})}):e.jsx(i.NoPermissions,{})]})},Q=()=>{const s=h(a=>a.admin_app.permissions.settings?.["transfer-tokens"].main);return e.jsx(i.Protect,{permissions:s,children:e.jsx(O,{})})};export{O as ListView,Q as ProtectedListView};
