import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/id-BtwA9WJT.mjs
var groups = "Grup";
var models = "Tipe Koleksi";
var pageNotFound = "Halaman tidak ditemukan";
var id = {
  "EditRelations.title": "Data relasional",
  "api.id": "API ID",
  "components.AddFilterCTA.add": "Filter",
  "components.AddFilterCTA.hide": "Filter",
  "components.DraggableAttr.edit": "Klik untuk mengedit",
  "components.DynamicZone.pick-compo": "Pilih satu komponen",
  "components.DynamicZone.required": "Komponen diperlukan",
  "components.EmptyAttributesBlock.button": "Masuk ke halaman pengaturan",
  "components.EmptyAttributesBlock.description": "Anda dapat mengubah pengaturan Anda",
  "components.FieldItem.linkToComponentLayout": "Mengatur tata letak komponen",
  "components.FilterOptions.button.apply": "Terapkan",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Terapkan",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Bersihkan semua",
  "components.FiltersPickWrapper.PluginHeader.description": "Tetapkan ketentuan yang akan diterapkan untuk memfilter entri",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filter",
  "components.FiltersPickWrapper.hide": "Sembunyikan",
  "components.LimitSelect.itemsPerPage": "Item per halaman",
  "components.NotAllowedInput.text": "Tidak ada izin untuk melihat bidang ini",
  "components.Search.placeholder": "Telusuri entri ...",
  "components.Select.draft-info-title": "Status: Draf",
  "components.Select.publish-info-title": "Status: Diterbitkan",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Sesuaikan bagaimana tampilan edit akan terlihat.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Tentukan pengaturan tampilan daftar.",
  "components.SettingsViewWrapper.pluginHeader.title": "Konfigurasi tampilan - {name}",
  "components.TableDelete.delete": "Hapus semua",
  "components.TableDelete.deleteSelected": "Hapus terpilih",
  "components.TableEmpty.withFilters": "Tidak ada {contentType} dengan filter yang diterapkan ...",
  "components.TableEmpty.withSearch": "Tidak ada {contentType} yang sesuai dengan pencarian ({search}) ...",
  "components.TableEmpty.withoutFilter": "Tidak ada {contentType} ...",
  "components.empty-repeatable": "Belum ada entri. Klik tombol di bawah untuk menambahkannya.",
  "components.notification.info.maximum-requirement": "Anda telah mencapai jumlah bidang maksimum",
  "components.notification.info.minimum-requirement": "Bidang telah ditambahkan agar sesuai dengan persyaratan minimum",
  "components.reset-entry": "Atur ulang entri",
  "components.uid.apply": "menerapkan",
  "components.uid.available": "tersedia",
  "components.uid.regenerate": "diperbarui",
  "components.uid.suggested": "disarankan",
  "components.uid.unavailable": "tidak tersedia",
  "containers.Edit.Link.Layout": "Konfigurasi tata letaknya",
  "containers.Edit.Link.Model": "Edit collection-type",
  "containers.Edit.addAnItem": "Tambahkan item...",
  "containers.Edit.clickToJump": "Klik untuk melompat ke entri",
  "containers.Edit.delete": "Hapus",
  "containers.Edit.delete-entry": "Hapus entri ini",
  "containers.Edit.editing": "Mengedit...",
  "containers.Edit.information": "Informasi",
  "containers.Edit.information.by": "Oleh",
  "containers.Edit.information.draftVersion": "versi draf",
  "containers.Edit.information.editing": "Mengedit",
  "containers.Edit.information.lastUpdate": "Terakhir diperbarui",
  "containers.Edit.information.publishedVersion": "versi publikasi",
  "containers.Edit.pluginHeader.title.new": "Buat entri",
  "containers.Edit.reset": "Atur ulang",
  "containers.Edit.returnList": "Kembali ke list",
  "containers.Edit.seeDetails": "Selengkapnya",
  "containers.Edit.submit": "Simpan",
  "containers.EditSettingsView.modal-form.edit-field": "Edit bidang",
  "containers.EditView.notification.errors": "Form tersebut mengandung beberapa kesalahan",
  "containers.Home.introduction": "Untuk mengedit entri Anda, buka tautan khusus di menu sebelah kiri. Plugin ini tidak memiliki cara yang tepat untuk mengedit pengaturan dan masih dalam pengembangan aktif.",
  "containers.Home.pluginHeaderDescription": "Kelola entri Anda melalui antarmuka yang kuat dan indah.",
  "containers.Home.pluginHeaderTitle": "Pengelola Konten",
  "containers.List.draft": "Draf",
  "containers.List.errorFetchRecords": "Eror",
  "containers.List.published": "Dipublikasi",
  "containers.list.displayedFields": "Bidang Ditampilkan",
  "containers.list.table-headers.publishedAt": "Status",
  "containers.ListSettingsView.modal-form.edit-label": "Edit label",
  "containers.SettingPage.add.field": "Tambah bidang lain",
  "containers.SettingPage.attributes": "Atribut bidang",
  "containers.SettingPage.attributes.description": "Tentukan urutan bidang",
  "containers.SettingPage.editSettings.description": "Tarik & lepas bidang untuk membentuk layout",
  "containers.SettingPage.editSettings.entry.title": "Judul entri",
  "containers.SettingPage.editSettings.entry.title.description": "Atur bidang yang ditampilkan di entri mu",
  "containers.SettingPage.editSettings.title": "Edit tampilan (pengaturan)",
  "containers.SettingPage.layout": "Layout",
  "containers.SettingPage.listSettings.description": "Atur opsi untuk collection type ini",
  "containers.SettingPage.listSettings.title": "Tampilan list (pengaturan)",
  "containers.SettingPage.pluginHeaderDescription": "Atur pengaturan spesifik untuk Collection Type ini",
  "containers.SettingPage.settings": "Pengaturan",
  "containers.SettingPage.view": "Tampilan",
  "containers.SettingViewModel.pluginHeader.title": "Pengelola Konten - {name}",
  "containers.SettingsPage.Block.contentType.description": "Atur pengaturan spesifik",
  "containers.SettingsPage.Block.contentType.title": "Collection Types",
  "containers.SettingsPage.Block.generalSettings.description": "Atur opsi default Collection Types anda",
  "containers.SettingsPage.Block.generalSettings.title": "UUmum",
  "containers.SettingsPage.pluginHeaderDescription": "Konfigurasikan pengaturan untuk semua jenis dan Grup Koleksi Anda",
  "containers.SettingsView.list.subtitle": "Konfigurasikan tata letak dan tampilan jenis dan grup Koleksi Anda",
  "containers.SettingsView.list.title": "Konfigurasi tampilan",
  "emptyAttributes.button": "Buka pembuat tipe koleksi",
  "emptyAttributes.description": "Tambahkan bidang pertama Anda ke Jenis Koleksi Anda",
  "emptyAttributes.title": "Belum ada bidang",
  "error.attribute.key.taken": "Nilai ini sudah ada",
  "error.attribute.sameKeyAndName": "Tidak boleh sama",
  "error.attribute.taken": "Nama bidang ini sudah ada",
  "error.contentTypeName.taken": "Nama ini sudah ada",
  "error.model.fetch": "Terjadi kesalahan selama pengambilan konfigurasi model.",
  "error.record.create": "Terjadi kesalahan selama pembuatan rekaman.",
  "error.record.delete": "Terjadi kesalahan selama penghapusan catatan.",
  "error.record.fetch": "Terjadi kesalahan selama pengambilan rekaman.",
  "error.record.update": "Terjadi kesalahan selama pembaruan catatan.",
  "error.records.count": "Terjadi kesalahan selama pengambilan catatan hitungan.",
  "error.records.fetch": "Terjadi kesalahan selama pengambilan catatan.",
  "error.schema.generation": "Terjadi kesalahan selama pembuatan skema.",
  "error.validation.json": "Ini bukan JSON",
  "error.validation.max": "Nilainya terlalu tinggi.",
  "error.validation.maxLength": "Nilainya terlalu panjang.",
  "error.validation.min": "Nilainya terlalu rendah.",
  "error.validation.minLength": "The value is too short.",
  "error.validation.minSupMax": "Tidak bisa lebih unggul.",
  "error.validation.regex": "Nilainya tidak cocok dengan regex.",
  "error.validation.required": "Input nilai ini diperlukan.",
  "form.Input.bulkActions": "Aktifkan tindakan massal",
  "form.Input.defaultSort": "Atribut sortir default",
  "form.Input.description": "Deskripsi",
  "form.Input.description.placeholder": "Nama tampilan di profil",
  "form.Input.editable": "Bidang yang dapat diedit",
  "form.Input.filters": "Aktifkan filter",
  "form.Input.label": "Label",
  "form.Input.label.inputDescription": "Nilai ini menggantikan label yang ditampilkan di kepala tabel",
  "form.Input.pageEntries": "Entri per halaman",
  "form.Input.pageEntries.inputDescription": "Catatan: Anda dapat mengganti nilai ini di halaman pengaturan Jenis Koleksi.",
  "form.Input.placeholder": "Placeholder",
  "form.Input.placeholder.placeholder": "My awesome value",
  "form.Input.search": "Aktifkan pencarian",
  "form.Input.search.field": "Aktifkan pencarian di bidang ini",
  "form.Input.sort.field": "Aktifkan urutkan di bidang ini",
  "form.Input.wysiwyg": "Tampil sebagai WYSIWYG",
  "global.displayedFields": "Bidang yang Ditampilkan",
  groups,
  "groups.numbered": "Grup ({number})",
  models,
  "models.numbered": "Tipe Koleksi ({number})",
  "notification.error.displayedFields": "Anda membutuhkan setidaknya satu bidang yang ditampilkan",
  "notification.error.relationship.fetch": "Terjadi kesalahan selama pengambilan hubungan.",
  "notification.info.SettingPage.disableSort": "Anda harus memiliki satu atribut dengan pengurutan yang diperbolehkan",
  "notification.info.minimumFields": "Anda harus memiliki setidaknya satu bidang yang ditampilkan",
  "notification.upload.error": "Terjadi kesalahan saat mengupload file Anda",
  pageNotFound,
  "permissions.not-allowed.create": "Anda tidak diizinkan membuat dokumen",
  "permissions.not-allowed.update": "Anda tidak diizinkan untuk melihat dokumen ini",
  "plugin.description.long": "Cara cepat untuk melihat, mengedit, dan menghapus data di database Anda.",
  "plugin.description.short": "Cara cepat untuk melihat, mengedit, dan menghapus data di database Anda.",
  "success.record.delete": "Dihapus",
  "success.record.publish": "Diterbitkan",
  "success.record.save": "Disimpan",
  "success.record.unpublish": "Didraf",
  "popUpWarning.warning.publish-question": "Apakah Anda masih ingin menerbitkannya?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Ya, terbitkan"
};
export {
  id as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=id-BtwA9WJT-URAQWJAP.js.map
