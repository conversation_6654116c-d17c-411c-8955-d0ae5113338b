import {
  useLicenseLimitNotification
} from "./chunk-IGODYTCM.js";
import "./chunk-ZYM532OH.js";
import {
  ListPageCE
} from "./chunk-ER4PVOWZ.js";
import "./chunk-RGUY4FUD.js";
import "./chunk-N562NCQ4.js";
import "./chunk-2E5FOOCQ.js";
import "./chunk-FUOVX7ZC.js";
import "./chunk-ELTZWS66.js";
import "./chunk-4C2ZQ5OG.js";
import "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/ListPage-feNYkeWT.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var UserListPageEE = () => {
  useLicenseLimitNotification();
  return (0, import_jsx_runtime.jsx)(ListPageCE, {});
};
export {
  UserListPageEE
};
//# sourceMappingURL=ListPage-feNYkeWT-CH3BKIT6.js.map
