import {
  Table
} from "./chunk-Q6W7NBRE.js";
import {
  useOnce
} from "./chunk-HXEM7AIZ.js";
import {
  useDeleteAPITokenMutation,
  useGetAPITokensQuery
} from "./chunk-CY7OP4S2.js";
import {
  API_TOKEN_TYPE
} from "./chunk-P2CO6HJH.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler,
  useGuidedTour,
  useNotification,
  useRBAC,
  useTracking,
  useTypedSelector
} from "./chunk-4C2ZQ5OG.js";
import {
  EmptyStateLayout,
  LinkButton,
  require_lib,
  useIntl
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  Link,
  useNavigate
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1d
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/ListView-Dxp_ryfR.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var qs = __toESM(require_lib(), 1);
var TABLE_HEADERS = [
  {
    name: "name",
    label: {
      id: "Settings.apiTokens.ListView.headers.name",
      defaultMessage: "Name"
    },
    sortable: true
  },
  {
    name: "description",
    label: {
      id: "Settings.apiTokens.ListView.headers.description",
      defaultMessage: "Description"
    },
    sortable: false
  },
  {
    name: "createdAt",
    label: {
      id: "Settings.apiTokens.ListView.headers.createdAt",
      defaultMessage: "Created at"
    },
    sortable: false
  },
  {
    name: "lastUsedAt",
    label: {
      id: "Settings.apiTokens.ListView.headers.lastUsedAt",
      defaultMessage: "Last used"
    },
    sortable: false
  }
];
var ListView = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const permissions = useTypedSelector(
    (state) => {
      var _a;
      return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["api-tokens"];
    }
  );
  const {
    allowedActions: { canRead, canCreate, canDelete, canUpdate }
  } = useRBAC(permissions);
  const navigate = useNavigate();
  const { trackUsage } = useTracking();
  const startSection = useGuidedTour("ListView", (state) => state.startSection);
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  React.useEffect(() => {
    startSection("apiTokens");
  }, [startSection]);
  React.useEffect(() => {
    navigate({ search: qs.stringify({ sort: "name:ASC" }, { encode: false }) });
  }, [navigate]);
  const headers = TABLE_HEADERS.map((header) => ({
    ...header,
    label: formatMessage(header.label)
  }));
  useOnce(() => {
    trackUsage("willAccessTokenList", {
      tokenType: API_TOKEN_TYPE
    });
  });
  const { data: apiTokens = [], isLoading, error } = useGetAPITokensQuery();
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [error, formatAPIError, toggleNotification]);
  React.useEffect(() => {
    trackUsage("didAccessTokenList", { number: apiTokens.length, tokenType: API_TOKEN_TYPE });
  }, [apiTokens, trackUsage]);
  const [deleteToken] = useDeleteAPITokenMutation();
  const handleDelete = async (id) => {
    try {
      const res = await deleteToken(id);
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
        return;
      }
      trackUsage("didDeleteToken");
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "Something went wrong"
        })
      });
    }
  };
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      { name: "API Tokens" }
    ) }),
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({ id: "Settings.apiTokens.title", defaultMessage: "API Tokens" }),
        subtitle: formatMessage({
          id: "Settings.apiTokens.description",
          defaultMessage: "List of generated tokens to consume the API"
        }),
        primaryAction: canCreate && (0, import_jsx_runtime.jsx)(
          LinkButton,
          {
            tag: Link,
            "data-testid": "create-api-token-button",
            startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
            size: "S",
            onClick: () => trackUsage("willAddTokenFromList", {
              tokenType: API_TOKEN_TYPE
            }),
            to: "/settings/api-tokens/create",
            children: formatMessage({
              id: "Settings.apiTokens.create",
              defaultMessage: "Create new API Token"
            })
          }
        )
      }
    ),
    !canRead ? (0, import_jsx_runtime.jsx)(Page.NoPermissions, {}) : (0, import_jsx_runtime.jsx)(Page.Main, { "aria-busy": isLoading, children: (0, import_jsx_runtime.jsxs)(Layouts.Content, { children: [
      apiTokens.length > 0 && (0, import_jsx_runtime.jsx)(
        Table,
        {
          permissions: { canRead, canDelete, canUpdate },
          headers,
          isLoading,
          onConfirmDelete: handleDelete,
          tokens: apiTokens,
          tokenType: API_TOKEN_TYPE
        }
      ),
      canCreate && apiTokens.length === 0 ? (0, import_jsx_runtime.jsx)(
        EmptyStateLayout,
        {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: "16rem" }),
          content: formatMessage({
            id: "Settings.apiTokens.addFirstToken",
            defaultMessage: "Add your first API Token"
          }),
          action: (0, import_jsx_runtime.jsx)(
            LinkButton,
            {
              tag: Link,
              variant: "secondary",
              startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
              to: "/settings/api-tokens/create",
              children: formatMessage({
                id: "Settings.apiTokens.addNewToken",
                defaultMessage: "Add new API Token"
              })
            }
          )
        }
      ) : null,
      !canCreate && apiTokens.length === 0 ? (0, import_jsx_runtime.jsx)(
        EmptyStateLayout,
        {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: "16rem" }),
          content: formatMessage({
            id: "Settings.apiTokens.emptyStateLayout",
            defaultMessage: "You don’t have any content yet..."
          })
        }
      ) : null
    ] }) })
  ] });
};
var ProtectedListView = () => {
  const permissions = useTypedSelector(
    (state) => {
      var _a;
      return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["api-tokens"].main;
    }
  );
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions, children: (0, import_jsx_runtime.jsx)(ListView, {}) });
};
export {
  ListView,
  ProtectedListView
};
//# sourceMappingURL=ListView-Dxp_ryfR-DNOJHT7S.js.map
