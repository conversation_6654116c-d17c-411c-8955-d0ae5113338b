import {
  COMMON_USER_SCHEMA
} from "./chunk-W2WB3HOA.js";
import {
  getDisplayName
} from "./chunk-2E5FOOCQ.js";
import {
  Form2 as Form,
  Layouts,
  MemoizedInputRenderer,
  create4 as create,
  create6 as create2,
  errorsTrads,
  require_upperFirst
} from "./chunk-ELTZWS66.js";
import {
  Page,
  setAppTheme,
  useAPIE<PERSON>r<PERSON><PERSON><PERSON>,
  useAuth,
  useIsSSOLockedQuery,
  useNotification,
  useTracking,
  useTypedDispatch,
  useTypedSelector,
  useUpdateMeMutation
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Button,
  Flex,
  Grid,
  Typography,
  isBaseQueryError,
  useIntl,
  useNotifyAT
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$4p
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/ProfilePage-C4CKIkBe.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_upperFirst = __toESM(require_upperFirst(), 1);
var PROFILE_VALIDTION_SCHEMA = create2().shape({
  ...COMMON_USER_SCHEMA,
  currentPassword: create().when(["password", "confirmPassword"], (password, confirmPassword, passSchema) => {
    return password || confirmPassword ? passSchema.required({
      id: errorsTrads.required.id,
      defaultMessage: "This field is required"
    }).nullable() : passSchema;
  }),
  preferedLanguage: create().nullable()
});
var ProfilePage = () => {
  const localeNames = useTypedSelector((state) => state.admin_app.language.localeNames);
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { toggleNotification } = useNotification();
  const { notifyStatus } = useNotifyAT();
  const currentTheme = useTypedSelector((state) => state.admin_app.theme.currentTheme);
  const dispatch = useTypedDispatch();
  const {
    _unstableFormatValidationErrors: formatValidationErrors,
    _unstableFormatAPIError: formatApiError
  } = useAPIErrorHandler();
  const user = useAuth("ProfilePage", (state) => state.user);
  React.useEffect(() => {
    if (user) {
      notifyStatus(
        formatMessage({
          id: "Settings.profile.form.notify.data.loaded",
          defaultMessage: "Your profile data has been loaded"
        })
      );
    } else {
      toggleNotification({
        type: "danger",
        message: formatMessage({ id: "notification.error", defaultMessage: "An error occured" })
      });
    }
  }, [formatMessage, notifyStatus, toggleNotification, user]);
  const [updateMe, { isLoading: isSubmittingForm }] = useUpdateMeMutation();
  const {
    isLoading,
    data: dataSSO,
    error
  } = useIsSSOLockedQuery(void 0, {
    skip: !(window.strapi.isEE && window.strapi.features.isEnabled("sso"))
  });
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatMessage({ id: "Settings.permissions.users.sso.provider.error" })
      });
    }
  }, [error, formatMessage, toggleNotification]);
  const handleSubmit = async (body, { setErrors }) => {
    const { confirmPassword: _confirmPassword, currentTheme: currentTheme2, ...bodyRest } = body;
    let dataToSend = bodyRest;
    if (dataToSend.password === "") {
      const {
        password: _password,
        currentPassword: _currentPassword,
        ...passwordRequestBodyRest
      } = dataToSend;
      dataToSend = passwordRequestBodyRest;
    }
    const res = await updateMe(dataToSend);
    if ("data" in res) {
      dispatch(setAppTheme(currentTheme2));
      trackUsage("didChangeMode", { newMode: currentTheme2 });
      toggleNotification({
        type: "success",
        message: formatMessage({ id: "notification.success.saved", defaultMessage: "Saved" })
      });
    }
    if ("error" in res) {
      if (isBaseQueryError(res.error) && res.error.name === "ValidationError") {
        setErrors(formatValidationErrors(res.error));
      } else if (isBaseQueryError(res.error)) {
        toggleNotification({
          type: "danger",
          message: formatApiError(res.error)
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatMessage({ id: "notification.error", defaultMessage: "An error occured" })
        });
      }
    }
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  const hasLockedRole = (dataSSO == null ? void 0 : dataSSO.isSSOLocked) ?? false;
  const { email, firstname, lastname, username, preferedLanguage } = user ?? {};
  const initialData = {
    email: email ?? "",
    firstname: firstname ?? "",
    lastname: lastname ?? "",
    username: username ?? "",
    preferedLanguage,
    currentTheme,
    confirmPassword: "",
    password: ""
  };
  return (0, import_jsx_runtime.jsxs)(Page.Main, { "aria-busy": isSubmittingForm, children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage({
      id: "Settings.profile.form.section.head.title",
      defaultMessage: "User profile"
    }) }),
    (0, import_jsx_runtime.jsx)(
      Form,
      {
        method: "PUT",
        onSubmit: handleSubmit,
        initialValues: initialData,
        validationSchema: PROFILE_VALIDTION_SCHEMA,
        children: ({ isSubmitting, modified }) => (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
          (0, import_jsx_runtime.jsx)(
            Layouts.Header,
            {
              title: getDisplayName(user),
              primaryAction: (0, import_jsx_runtime.jsx)(
                Button,
                {
                  startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
                  loading: isSubmitting,
                  type: "submit",
                  disabled: !modified,
                  children: formatMessage({ id: "global.save", defaultMessage: "Save" })
                }
              )
            }
          ),
          (0, import_jsx_runtime.jsx)(Box, { paddingBottom: 10, children: (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 6, children: [
            (0, import_jsx_runtime.jsx)(UserInfoSection, {}),
            !hasLockedRole && (0, import_jsx_runtime.jsx)(PasswordSection, {}),
            (0, import_jsx_runtime.jsx)(PreferencesSection, { localeNames })
          ] }) }) })
        ] })
      }
    )
  ] });
};
var PasswordSection = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(
    Box,
    {
      background: "neutral0",
      hasRadius: true,
      shadow: "filterShadow",
      paddingTop: 6,
      paddingBottom: 6,
      paddingLeft: 7,
      paddingRight: 7,
      children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
        (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
          id: "global.change-password",
          defaultMessage: "Change password"
        }) }),
        [
          [
            {
              label: formatMessage({
                id: "Auth.form.currentPassword.label",
                defaultMessage: "Current Password"
              }),
              name: "currentPassword",
              size: 6,
              type: "password"
            }
          ],
          [
            {
              autoComplete: "new-password",
              label: formatMessage({
                id: "global.password",
                defaultMessage: "Password"
              }),
              name: "password",
              size: 6,
              type: "password"
            },
            {
              autoComplete: "new-password",
              label: formatMessage({
                id: "Auth.form.confirmPassword.label",
                defaultMessage: "Confirm Password"
              }),
              name: "confirmPassword",
              size: 6,
              type: "password"
            }
          ]
        ].map((row, index) => (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 5, children: row.map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, { col: size, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, { ...field }) }, field.name)) }, index))
      ] })
    }
  );
};
var PreferencesSection = ({ localeNames }) => {
  const { formatMessage } = useIntl();
  const themesToDisplay = useTypedSelector((state) => state.admin_app.theme.availableThemes);
  return (0, import_jsx_runtime.jsx)(
    Box,
    {
      background: "neutral0",
      hasRadius: true,
      shadow: "filterShadow",
      paddingTop: 6,
      paddingBottom: 6,
      paddingLeft: 7,
      paddingRight: 7,
      children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
        (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 1, children: [
          (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
            id: "Settings.profile.form.section.experience.title",
            defaultMessage: "Experience"
          }) }),
          (0, import_jsx_runtime.jsx)(Typography, { children: formatMessage(
            {
              id: "Settings.profile.form.section.experience.interfaceLanguageHelp",
              defaultMessage: "Preference changes will apply only to you. More information is available {here}."
            },
            {
              here: (0, import_jsx_runtime.jsx)(
                Box,
                {
                  tag: "a",
                  color: "primary600",
                  target: "_blank",
                  rel: "noopener noreferrer",
                  href: "https://docs.strapi.io/developer-docs/latest/development/admin-customization.html#locales",
                  children: formatMessage({
                    id: "Settings.profile.form.section.experience.here",
                    defaultMessage: "here"
                  })
                }
              )
            }
          ) })
        ] }),
        (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 5, children: [
          {
            hint: formatMessage({
              id: "Settings.profile.form.section.experience.interfaceLanguage.hint",
              defaultMessage: "This will only display your own interface in the chosen language."
            }),
            label: formatMessage({
              id: "Settings.profile.form.section.experience.interfaceLanguage",
              defaultMessage: "Interface language"
            }),
            name: "preferedLanguage",
            options: Object.entries(localeNames).map(([value, label]) => ({
              label,
              value
            })),
            placeholder: formatMessage({
              id: "global.select",
              defaultMessage: "Select"
            }),
            size: 6,
            type: "enumeration"
          },
          {
            hint: formatMessage({
              id: "Settings.profile.form.section.experience.mode.hint",
              defaultMessage: "Displays your interface in the chosen mode."
            }),
            label: formatMessage({
              id: "Settings.profile.form.section.experience.mode.label",
              defaultMessage: "Interface mode"
            }),
            name: "currentTheme",
            options: [
              {
                label: formatMessage({
                  id: "Settings.profile.form.section.experience.mode.option-system-label",
                  defaultMessage: "Use system settings"
                }),
                value: "system"
              },
              ...themesToDisplay.map((theme) => ({
                label: formatMessage(
                  {
                    id: "Settings.profile.form.section.experience.mode.option-label",
                    defaultMessage: "{name} mode"
                  },
                  {
                    name: formatMessage({
                      id: theme,
                      defaultMessage: (0, import_upperFirst.default)(theme)
                    })
                  }
                ),
                value: theme
              }))
            ],
            placeholder: formatMessage({
              id: "components.Select.placeholder",
              defaultMessage: "Select"
            }),
            size: 6,
            type: "enumeration"
          }
        ].map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, { col: size, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, { ...field }) }, field.name)) })
      ] })
    }
  );
};
var UserInfoSection = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(
    Box,
    {
      background: "neutral0",
      hasRadius: true,
      shadow: "filterShadow",
      paddingTop: 6,
      paddingBottom: 6,
      paddingLeft: 7,
      paddingRight: 7,
      children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
        (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
          id: "global.profile",
          defaultMessage: "Profile"
        }) }),
        (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 5, children: [
          {
            label: formatMessage({
              id: "Auth.form.firstname.label",
              defaultMessage: "First name"
            }),
            name: "firstname",
            required: true,
            size: 6,
            type: "string"
          },
          {
            label: formatMessage({
              id: "Auth.form.lastname.label",
              defaultMessage: "Last name"
            }),
            name: "lastname",
            size: 6,
            type: "string"
          },
          {
            label: formatMessage({
              id: "Auth.form.email.label",
              defaultMessage: "Email"
            }),
            name: "email",
            required: true,
            size: 6,
            type: "email"
          },
          {
            label: formatMessage({
              id: "Auth.form.username.label",
              defaultMessage: "Username"
            }),
            name: "username",
            size: 6,
            type: "string"
          }
        ].map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, { col: size, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, { ...field }) }, field.name)) })
      ] })
    }
  );
};
export {
  ProfilePage
};
//# sourceMappingURL=ProfilePage-C4CKIkBe-GXWRTLJG.js.map
