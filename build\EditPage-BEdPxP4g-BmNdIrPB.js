import{a as B,au as w,at as D,r as U,ba as _,c as G,bb as q,bc as H,bi as Q,be as z,m as e,aV as $,n as b,aX as J,bf as W,L as P,bh as X,w as p,A,N as K,J as u,s as S,G as j,x as l,z as Y,af as Z,i as ee,l as L,k as se,aO as I,ax as re}from"./strapi-YzJfjJ2z.js";import{u as te}from"./useAdminRoles-Bd2N7J7A-CIPQp8aL.js";import{P as ie}from"./Permissions-p_cREK0b-DUEXRPxk.js";import"./groupBy-BivwhTv9.js";import"./_baseEach-BkkNIx9z.js";const ae=({disabled:i,role:s,values:h,errors:r,onChange:n,onBlur:m})=>{const{formatMessage:t}=w();return e.jsx(u,{background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:e.jsxs(p,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsxs(p,{justifyContent:"space-between",children:[e.jsxs(u,{children:[e.jsx(u,{children:e.jsx(S,{fontWeight:"bold",children:s?s.name:t({id:"global.details",defaultMessage:"Details"})})}),e.jsx(u,{children:e.jsx(S,{textColor:"neutral500",variant:"pi",children:s?s.description:t({id:"Settings.roles.form.description",defaultMessage:"Name and description of the role"})})})]}),e.jsx(A,{disabled:!0,variant:"secondary",children:t({id:"Settings.roles.form.button.users-with-role",defaultMessage:"{number, plural, =0 {# users} one {# user} other {# users}} with this role"},{number:s.usersCount})})]}),e.jsxs(j.Root,{gap:4,children:[e.jsx(j.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(l.Root,{name:"name",error:r.name&&t({id:r.name}),required:!0,children:[e.jsx(l.Label,{children:t({id:"global.name",defaultMessage:"Name"})}),e.jsx(Y,{disabled:i,onChange:n,onBlur:m,value:h.name||""}),e.jsx(l.Error,{})]})}),e.jsx(j.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(l.Root,{name:"description",error:r.description&&t({id:r.description}),children:[e.jsx(l.Label,{children:t({id:"global.description",defaultMessage:"Description"})}),e.jsx(Z,{disabled:i,onChange:n,onBlur:m,value:h.description}),e.jsx(l.Error,{})]})})]})]})})},oe=ee().shape({name:L().required(se.required.id),description:L().optional()}),ne=()=>{const{toggleNotification:i}=B(),{formatMessage:s}=w(),r=D("/settings/roles/:id")?.params.id,n=U.useRef(null),{trackUsage:m}=_(),{_unstableFormatAPIError:t,_unstableFormatValidationErrors:R}=G(),{isLoading:v,data:y}=q({role:r??""}),{roles:C,isLoading:E,refetch:T}=te({id:r},{refetchOnMountOrArgChange:!0}),d=C[0]??{},{data:F,isLoading:O}=H({id:r},{skip:!r,refetchOnMountOrArgChange:!0}),[k]=Q(),[N]=z();if(!r)return e.jsx($,{to:"/settings/roles"});const V=async(f,g)=>{try{const{permissionsToSend:c,didUpdateConditions:x}=n.current?.getPermissions()??{},a=await k({id:r,...f});if("error"in a){I(a.error)&&a.error.name==="ValidationError"?g.setErrors(R(a.error)):i({type:"danger",message:t(a.error)});return}if(d.code!=="strapi-super-admin"&&c){const o=await N({id:a.data.id,permissions:c});if("error"in o){I(o.error)&&o.error.name==="ValidationError"?g.setErrors(R(o.error)):i({type:"danger",message:t(o.error)});return}x&&m("didUpdateConditions")}n.current?.setFormAfterSubmit(),await T(),i({type:"success",message:s({id:"notification.success.saved"})})}catch{i({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})}},M=!E&&d.code==="strapi-super-admin";return v||E||O||!y?e.jsx(b.Loading,{}):e.jsxs(J,{children:[e.jsx(b.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(W,{enableReinitialize:!0,initialValues:{name:d.name??"",description:d.description??""},onSubmit:V,validationSchema:oe,validateOnChange:!1,children:({handleSubmit:f,values:g,errors:c,handleChange:x,handleBlur:a,isSubmitting:o})=>e.jsxs("form",{onSubmit:f,children:[e.jsx(P.Header,{primaryAction:e.jsx(p,{gap:2,children:e.jsx(A,{type:"submit",startIcon:e.jsx(K,{}),disabled:d.code==="strapi-super-admin",loading:o,children:s({id:"global.save",defaultMessage:"Save"})})}),title:s({id:"Settings.roles.edit.title",defaultMessage:"Edit a role"}),subtitle:s({id:"Settings.roles.create.description",defaultMessage:"Define the rights given to the role"}),navigationAction:e.jsx(X,{})}),e.jsx(P.Content,{children:e.jsxs(p,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsx(ae,{disabled:M,errors:c,values:g,onChange:x,onBlur:a,role:d}),e.jsx(u,{shadow:"filterShadow",hasRadius:!0,children:e.jsx(ie,{isFormDisabled:M,permissions:F,ref:n,layout:y})})]})})]})})]})},ge=()=>{const i=re(s=>s.admin_app.permissions.settings?.roles.update);return e.jsx(b.Protect,{permissions:i,children:e.jsx(ne,{})})};export{ne as EditPage,ge as ProtectedEditPage};
