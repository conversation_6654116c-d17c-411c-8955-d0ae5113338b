import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/ca-f06Q0InB.mjs
var Analytics = "Analítica";
var Documentation = "Documentació";
var Email = "Email";
var Password = "Contrasenya";
var Provider = "Proveïdor";
var ResetPasswordToken = "Restablir Token de Contrasenya";
var Role = "Rol";
var Username = "Nom d'usuari";
var Users = "Usuaris";
var anErrorOccurred = "Ups! Alguna cosa va sortir malament. Intenta-ho de nou.";
var clearLabel = "Netejar";
var or = "O";
var skipToContent = "Saltar al contingut";
var submit = "Enviar";
var ca = {
  Analytics,
  "Auth.components.Oops.text": "El teu compte ha estat suspès",
  "Auth.components.Oops.text.admin": "Si es tracta d'un error, comuniqueu-ho amb el vostre administrador.",
  "Auth.components.Oops.title": "Ups...",
  "Auth.form.button.forgot-password": "Enviar Email",
  "Auth.form.button.go-home": "TORNAR A CASA",
  "Auth.form.button.login": "Iniciar sessió",
  "Auth.form.button.login.Strapi": "Iniciar sessió a través de Strapi",
  "Auth.form.button.login.providers.error": "No es pot connectar a través del proveïdor seleccionat.",
  "Auth.form.button.password-recovery": "Recuperació de contrasenya",
  "Auth.form.button.register": "Llest per començar",
  "Auth.form.confirmPassword.label": "Confirmació de contrasenya",
  "Auth.form.currentPassword.label": "Contrasenya actual",
  "Auth.form.email.label": "Email",
  "Auth.form.email.placeholder": "<EMAIL>",
  "Auth.form.error.blocked": "El seu compte ha estat bloquejat per l'administrador.",
  "Auth.form.error.code.provide": "Codi incorrecte.",
  "Auth.form.error.confirmed": "El vostre compte de correu ha estat confirmat.",
  "Auth.form.error.email.invalid": "Aquest email és invàlid.",
  "Auth.form.error.email.provide": "Si us plau, escriviu el vostre nom d'usuari o email.",
  "Auth.form.error.email.taken": "L'email ja està registrat",
  "Auth.form.error.invalid": "Identificador o contrasenya invàlids.",
  "Auth.form.error.params.provide": "Parametres incorrectes.",
  "Auth.form.error.password.format": "La vostra contrasenya no pot tenir el símbol `$` més de tres vegades.",
  "Auth.form.error.password.local": "Aquest usuari mai no va establir una contrasenya local, si us plau, vas accedir a través del proveïdor que va fer servir durant la creació del compte.",
  "Auth.form.error.password.matching": "Les contrasenyes no coincideixen.",
  "Auth.form.error.password.provide": "Si us plau, escriviu la contrasenya.",
  "Auth.form.error.ratelimit": "Massa intents. Torneu a intentar-ho en un minut.",
  "Auth.form.error.user.not-exist": "El correu electrònic no existeix.",
  "Auth.form.error.username.taken": "El nom d'usuari ja està registrat",
  "Auth.form.firstname.label": "Nom",
  "Auth.form.firstname.placeholder": "Nom",
  "Auth.form.forgot-password.email.label": "Escriu el teu email",
  "Auth.form.forgot-password.email.label.success": "Email enviat amb èxit a",
  "Auth.form.lastname.label": "Cognoms",
  "Auth.form.lastname.placeholder": "Vila",
  "Auth.form.password.hide-password": "Amagar contrasenya",
  "Auth.form.password.hint": "La contrasenya ha de contenir almenys 8 caràcters, 1 majúscula, 1 minúscula i 1 número",
  "Auth.form.password.show-password": "Mostra contrasenya",
  "Auth.form.register.news.label": "Mantenir-me informat sobre les noves funcions i les properes millores (en fer això, accepta les {terms} i la {policy}).",
  "Auth.form.register.subtitle": "Les credencials només s'utilitzen per autenticar-se al panell d'administració. Totes les dades desades s'emmagatzemaran a la vostra base de dades.",
  "Auth.form.rememberMe.label": "Recorda'm",
  "Auth.form.username.label": "Usuari",
  "Auth.form.username.placeholder": "Nom d'usuari",
  "Auth.form.welcome.subtitle": "Inicieu sessió al vostre compte de Strapi",
  "Auth.form.welcome.title": "Benvingut!",
  "Auth.link.forgot-password": "Heu oblidat la contrasenya?",
  "Auth.link.ready": "A punt per iniciar sessió?",
  "Auth.link.signin": "Iniciar sessió",
  "Auth.link.signin.account": "Ja tens un compte?",
  "Auth.login.sso.divider": "O inicia sessió amb",
  "Auth.login.sso.loading": "Carregant proveïdors...",
  "Auth.login.sso.subtitle": "Inicieu sessió al vostre compte mitjançant SSO",
  "Auth.privacy-policy-agreement.policy": "política de privadesa",
  "Auth.privacy-policy-agreement.terms": "condicions",
  "Auth.reset-password.title": "Restablir la contrasenya",
  "Content Manager": "Gestor de Continguts",
  "Content Type Builder": "Constructor de Tipus de Contingut",
  Documentation,
  Email,
  "Files Upload": "Pujada de fitxers",
  "HomePage.head.title": "Pàgina principal",
  "HomePage.roadmap": "Vegeu el nostre full de ruta",
  "HomePage.welcome.congrats": "Felicitats!",
  "HomePage.welcome.congrats.content": "Està registrat com a primer administrador. Per descobrir les potents funcions que ofereix Strapi,",
  "HomePage.welcome.congrats.content.bold": "us recomanem que creeu el vostre primer tipus de col·lecció.",
  "Media Library": "Biblioteca de Multimèdia",
  "New entry": "Entrada nova",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "Rols i Permisos",
  "Roles.ListPage.notification.delete-all-not-allowed": "Alguns rols no es van poder eliminar perquè estan associats a usuaris",
  "Roles.ListPage.notification.delete-not-allowed": "No es pot suprimir un rol si està associat a usuaris",
  "Roles.RoleRow.select-all": "Seleccioneu {name} per accions en bloc",
  "Roles.RoleRow.user-count": "{nombre, plural, =0 {# usuari} un {# usuari} altre {# usuaris}}",
  "Roles.components.List.empty.withSearch": "No hi ha rol corresponent a la cerca ({search})...",
  "Settings.PageTitle": "Configuració - {name}",
  "Settings.apiTokens.addFirstToken": "Afegeix el teu primer token d'API",
  "Settings.apiTokens.addNewToken": "Afegir nou token d'API",
  "Settings.tokens.copy.editMessage": "Per raons de seguretat, només podeu veure el seu token una vegada.",
  "Settings.tokens.copy.editTitle": "Aquest token ja no és accessible.",
  "Settings.tokens.copy.lastWarning": "Assegureu-vos de copiar aquest token, no podreu tornar-lo a veure!",
  "Settings.apiTokens.create": "Afegir entrada",
  "Settings.apiTokens.description": "Llista de tokens generats per consumir l'API",
  "Settings.apiTokens.emptyStateLayout": "Encara no tens cap contingut...",
  "Settings.tokens.notification.copied": "Token copiat al porta-retalls.",
  "Settings.apiTokens.title": "Tokens d'API",
  "Settings.apiTokens.lastHour": "darrera hora",
  "Settings.tokens.types.full-access": "Accés complet",
  "Settings.tokens.types.read-only": "Només lectura",
  "Settings.application.Strapi-version": "versió de Strapi",
  "Settings.application.StrapiVersion": "versió de Strapi",
  "Settings.application.description": "Informació global del panell d'administració",
  "Settings.application.edition-title": "pla actual",
  "Settings.application.get-help": "Aconsegueix ajuda",
  "Settings.application.link-pricing": "Veure tots els plans",
  "Settings.application.link-upgrade": "Actualitza el panell d'administració",
  "Settings.application.node-version": "versió de node",
  "Settings.application.title": "Descripció general",
  "Settings.error": "Error",
  "Settings.global": "Configuració global",
  "Settings.permissions": "Panell d'administració",
  "Settings.permissions.category": "Configuració de permisos per a {category}",
  "Settings.permissions.category.plugins": "Configuració de permisos per l'extensió de {category}",
  "Settings.permissions.conditions.anytime": "En qualsevol moment",
  "Settings.permissions.conditions.apply": "Aplicar",
  "Settings.permissions.conditions.can": "Poder",
  "Settings.permissions.conditions.conditions": "Definir condicions",
  "Settings.permissions.conditions.links": "Enllaços",
  "Settings.permissions.conditions.no-actions": "No hi ha acció",
  "Settings.permissions.conditions.none-selected": "En qualsevol moment",
  "Settings.permissions.conditions.or": "O",
  "Settings.permissions.conditions.when": "Quan",
  "Settings.permissions.select-all-by-permission": "Seleccioneu tots els permisos de {label}",
  "Settings.permissions.select-by-permission": "Seleccionar permís de {label}",
  "Settings.permissions.users.create": "Crear nou usuari",
  "Settings.permissions.users.email": "Email",
  "Settings.permissions.users.firstname": "Nom",
  "Settings.permissions.users.lastname": "Cognom",
  "Settings.permissions.users.form.sso": "connecta amb SSO",
  "Settings.permissions.users.form.sso.description": "quan està disponible, els usuaris poden accedir via SSO",
  "Settings.permissions.users.listview.header.subtitle": "Tots els usuaris que tenen accés al panell d'administració de Strapi",
  "Settings.permissions.users.tabs.label": "Permisos de pestanyes",
  "Settings.profile.form.notify.data.loaded": "S'han carregat les dades del vostre perfil",
  "Settings.profile.form.section.experience.clear.select": "Esborrar l'idioma d'interfície seleccionat",
  "Settings.profile.form.section.experience.here": "documentació",
  "Settings.profile.form.section.experience.interfaceLanguage": "Idioma d'interfície",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "Això només mostrarà la vostra pròpia interfície en l'idioma escollit.",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "La selecció canviarà l'idioma de la interfície només per a vosaltres. Consulteu aquesta {here} perquè altres idiomes estiguin disponibles per al vostre ordinador.",
  "Settings.profile.form.section.experience.mode.hint": "Mostra la vostra interfície en el mode escollit.",
  "Settings.profile.form.section.experience.mode.label": "Mode d'interfície",
  "Settings.profile.form.section.experience.mode.option-label": "mode {nom}",
  "Settings.profile.form.section.experience.title": "Experiència",
  "Settings.profile.form.section.head.title": "Perfil dusuari",
  "Settings.profile.form.section.profile.page.title": "Pàgina de perfil",
  "Settings.roles.create.description": "Definir els drets atorgats al rol",
  "Settings.roles.create.title": "Crea un rol",
  "Settings.roles.created": "Rol creat",
  "Settings.roles.edit.title": "Editar un rol",
  "Settings.roles.form.button.users-with-role": "Usuaris amb aquest rol",
  "Settings.roles.form.created": "Creat",
  "Settings.roles.form.description": "Nom i descripció del rol",
  "Settings.roles.form.permission.property-label": "permisos de {label}",
  "Settings.roles.form.permissions.attributesPermissions": "Permisos dels camps",
  "Settings.roles.form.permissions.create": "Crear",
  "Settings.roles.form.permissions.delete": "Eliminar",
  "Settings.roles.form.permissions.publish": "Publicar",
  "Settings.roles.form.permissions.read": "Llegir",
  "Settings.roles.form.permissions.update": "Actualitzar",
  "Settings.roles.list.button.add": "Afegir nou rol",
  "Settings.roles.list.description": "Llista de rols",
  "Settings.roles.title.singular": "rol",
  "Settings.sso.description": "Configureu els paràmetres per a la funció d'inici de sessió únic (SSO).",
  "Settings.sso.form.defaultRole.description": "Associarà el nou usuari autenticat al rol seleccionat",
  "Settings.sso.form.defaultRole.description-not-allowed": "Heu de tenir permís per llegir els rols d'administrador.",
  "Settings.sso.form.defaultRole.label": "Rol per defecte",
  "Settings.sso.form.registration.description": "Crear un nou usuari a l'inici de sessió (SSO) si no existeix un compte",
  "Settings.sso.form.registration.label": "Autoregistre",
  "Settings.sso.title": "Inici de sessió únic (SSO)",
  "Settings.webhooks.create": "Crea un webhook",
  "Settings.webhooks.create.header": "Crea una nova capçalera",
  "Settings.webhooks.created": "Webhook creat",
  "Settings.webhooks.event.publish-tooltip": "Aquest esdeveniment només existeix per a continguts amb el sistema Esborrany/Publicació habilitat",
  "Settings.webhooks.events.create": "Crear",
  "Settings.webhooks.events.update": "Actualitzar",
  "Settings.webhooks.form.events": "Esdeveniments",
  "Settings.webhooks.form.headers": "Capçaleres",
  "Settings.webhooks.form.url": "Url",
  "Settings.webhooks.headers.remove": "eliminar fila de capçalera {number}",
  "Settings.webhooks.key": "Clau",
  "Settings.webhooks.list.button.add": "Afegir nou webhook",
  "Settings.webhooks.list.description": "Rep notificacions de canvis POST.",
  "Settings.webhooks.list.empty.description": "Afegiu el primer a aquesta llista.",
  "Settings.webhooks.list.empty.link": "Veure la nostra documentació",
  "Settings.webhooks.list.empty.title": "Encara no hi ha webhooks",
  "Settings.webhooks.list.th.actions": "accions",
  "Settings.webhooks.list.th.status": "estat",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "Webhooks",
  "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# recurs seleccionat} other {# recursos seleccionats}}",
  "Settings.webhooks.trigger": "Activador",
  "Settings.webhooks.trigger.cancel": "Cancel·lar activador",
  "Settings.webhooks.trigger.pending": "Pendent…",
  "Settings.webhooks.trigger.save": "Deseu activador",
  "Settings.webhooks.trigger.success": "Correcte!",
  "Settings.webhooks.trigger.success.label": "Activador correcte",
  "Settings.webhooks.trigger.test": "Provar activador",
  "Settings.webhooks.trigger.title": "Desa abans de activar",
  "Settings.webhooks.value": "Valor",
  "Usecase.back-end": "Desenvolupador back-end",
  "Usecase.button.skip": "Omet aquesta pregunta",
  "Usecase.content-creator": "Creador de contingut",
  "Usecase.front-end": "Desenvolupador front-end",
  "Usecase.full-stack": "Desenvolupador full-stack",
  "Usecase.input.work-type": "Quin tipus de feina feu?",
  "Usecase.notification.success.project-created": "El projecte s'ha creat amb èxit",
  "Usecase.other": "Altres",
  "Usecase.title": "Explica'ns una mica més sobre tu",
  Username,
  Users,
  "Users & Permissions": "Usuaris i permisos",
  "Users.components.List.empty": "No hi ha usuaris...",
  "Users.components.List.empty.withFilters": "No hi ha usuaris amb els filtres aplicats...",
  "Users.components.List.empty.withSearch": "No hi ha usuaris corresponents a la cerca ({search})...",
  "admin.pages.MarketPlacePage.head": "Boting - Extensions",
  "admin.pages.MarketPlacePage.missingPlugin.description": "Digueu-nos quina extensió esteu buscant i ho farem saber als desenvolupadors de extensions de la nostra comunitat en cas que busquen inspiració!",
  "admin.pages.MarketPlacePage.missingPlugin.title": "Trobeu a faltar una extensió?",
  "admin.pages.MarketPlacePage.offline.subtitle": "Cal estar connectat a Internet per accedir a la botiga Strapi.",
  "admin.pages.MarketPlacePage.offline.title": "Estàs fora de línia",
  "admin.pages.MarketPlacePage.plugin.copy": "Copia l'ordre d'instal·lació",
  "admin.pages.MarketPlacePage.plugin.copy.success": "L'ordre és a punt per ser enganxada al vostre terminal",
  "admin.pages.MarketPlacePage.plugin.info": "Aprèn més",
  "admin.pages.MarketPlacePage.plugin.info.label": "Més informació sobre {pluginName}",
  "admin.pages.MarketPlacePage.plugin.info.text": "Aprèn més",
  "admin.pages.MarketPlacePage.plugin.installed": "Instal·lat",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "Fet per Strapi",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "Extensió verificada per Strapi",
  "admin.pages.MarketPlacePage.search.clear": "Netegeu la cerca de l'extensió",
  "admin.pages.MarketPlacePage.search.empty": 'Cap resultat per a "{target}"',
  "admin.pages.MarketPlacePage.search.placeholder": "Cerca una extensió",
  "admin.pages.MarketPlacePage.submit.plugin.link": "Envieu la vostra extensió",
  "admin.pages.MarketPlacePage.subtitle": "Treu més partit a Strapi",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "Copiar al porta-retalls",
  "app.component.search.label": "Cerca {target}",
  "app.component.table.duplicate": "Copiar {target}",
  "app.component.table.edit": "Edita {target}",
  "app.component.table.select.one-entry": "Seleccioneu {target}",
  "app.components.BlockLink.blog": "Bloc",
  "app.components.BlockLink.blog.content": "Llegiu les últimes notícies sobre Strapi i l'ecosistema.",
  "app.components.BlockLink.code": "Exemples de codi",
  "app.components.BlockLink.code.content": "Apreneu provant projectes reals desenvolupats per la comunitat.",
  "app.components.BlockLink.documentation.content": "Descobriu els conceptes essencials, guies i instruccions.",
  "app.components.BlockLink.tutorial": "Tutorials",
  "app.components.BlockLink.tutorial.content": "Seguiu les instruccions pas a pas per utilitzar i personalitzar Strapi.",
  "app.components.Button.cancel": "Cancel·lar",
  "app.components.Button.confirm": "Confirmar",
  "app.components.Button.reset": "Reiniciar",
  "app.components.ComingSoonPage.comingSoon": "Properament",
  "app.components.ConfirmDialog.title": "Confirmació",
  "app.components.DownloadInfo.download": "Descàrrega en curs...",
  "app.components.DownloadInfo.text": "Això pot trigar un minut. Gràcies per la vostra paciència.",
  "app.components.EmptyAttributes.title": "Encara no hi ha camps",
  "app.components.EmptyStateLayout.content-document": "No s'ha trobat contingut",
  "app.components.EmptyStateLayout.content-permissions": "No tens els permisos per accedir a aquest contingut.",
  "app.components.GuidedTour.CM.create.content": "<p>Creeu i gestioneu tot el contingut aquí al Gestor de continguts.</p><p>Ex.: Si fem l'exemple del lloc web del bloc més enllà, es pot escriure un article, desar-lo i publicar-lo com vulgui.</p>< p>💡 Consell ràpid: no us oblideu de fer clic a Publicar al contingut que creeu.</p>",
  "app.components.GuidedTour.CM.create.title": "⚡️ Crea contingut",
  "app.components.GuidedTour.CM.success.content": "<p>Increïble, un últim pas per fer!</p><b>🚀 Veure contingut en acció</b>",
  "app.components.GuidedTour.CM.success.cta.title": "Prova l'API",
  "app.components.GuidedTour.CM.success.title": "Pas 2: completat ✅",
  "app.components.GuidedTour.CTB.create.content": "<p>Els tipus de col·lecció us ajuden a gestionar diverses entrades, els tipus únics són adequats per gestionar només una entrada.</p> <p>Ex: per a un lloc web de bloc, els articles serien un tipus de col·lecció mentre que una pàgina d'inici seria un tipus únic. </p>",
  "app.components.GuidedTour.CTB.create.cta.title": "Creeu un tipus de col·lecció",
  "app.components.GuidedTour.CTB.create.title": "🧠 Crea un primer tipus de col·lecció",
  "app.components.GuidedTour.CTB.success.content": "<p>Que vagi bé!</p><b>⚡️ Què t'agradaria compartir amb el món?</b>",
  "app.components.GuidedTour.CTB.success.title": "Pas 1: Completat ✅",
  "app.components.GuidedTour.apiTokens.create.content": "<p>Genereu aquí un testimoni d'autenticació i recupereu el contingut que acabeu de crear.</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "Genereu un testimoni API",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 Veure contingut en acció",
  "app.components.GuidedTour.apiTokens.success.content": "<p>Consulteu el contingut en acció fent una sol·licitud HTTP:</p><ul><li><p>A aquest URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<' YOUR_CT'>'</light></p></li><li><p>Amb la capçalera: <light>Autorització: portador '<'YOUR_API_TOKEN'>'</light></p></li ></ul><p>Per obtenir més maneres d'interaccionar amb el contingut, consulteu la <documentationLink>documentació</documentationLink>.</p>",
  "app.components.GuidedTour.apiTokens.success.cta.title": "Torna a la pàgina d'inici",
  "app.components.GuidedTour.apiTokens.success.title": "Pas 3: Completat ✅",
  "app.components.GuidedTour.create-content": "Crea contingut",
  "app.components.GuidedTour.home.CM.title": "⚡️ Què t'agradaria compartir amb el món?",
  "app.components.GuidedTour.home.CTB.cta.title": "Aneu al Creador de tipus de contingut",
  "app.components.GuidedTour.home.CTB.title": "🧠 Construeix l'estructura del contingut",
  "app.components.GuidedTour.home.apiTokens.cta.title": "Prova l'API",
  "app.components.GuidedTour.skip": "Omet el tutorial",
  "app.components.GuidedTour.title": "3 passos per començar",
  "app.components.HomePage.button.blog": "VEURE MÉS AL BLOC",
  "app.components.HomePage.community": "Trobeu la comunitat a la web",
  "app.components.HomePage.community.content": "Parleu amb els membres de l'equip, col·laboradors i desenvolupadors en diferents canals.",
  "app.components.HomePage.create": "Crea el teu primer tipus de contingut",
  "app.components.HomePage.roadmap": "Vegeu els nostres propers objectius",
  "app.components.HomePage.welcome": "Benvingut a bord!",
  "app.components.HomePage.welcome.again": "Benvingut",
  "app.components.HomePage.welcomeBlock.content": "Estem feliços de tenir-lo com a membre de la comunitat. Estem constantment a la recerca de comentaris així que no dubti en enviar-nos un missatge a",
  "app.components.HomePage.welcomeBlock.content.again": "Esperem que estiguis progressant en el teu projecte... Senti's lliure de llegir les últimes novetats sobre Strapi. Estem donant el millor de nosaltres mateixos per millorar el producte basant-nos en els vostres comentaris.",
  "app.components.HomePage.welcomeBlock.content.issues": "problema.",
  "app.components.HomePage.welcomeBlock.content.raise": "o reportar qualsevol",
  "app.components.ImgPreview.hint": "Arrossegueu i deixeu anar el fitxer en aquesta àrea o {browse} per pujar un fitxer.",
  "app.components.ImgPreview.hint.browse": "cerca",
  "app.components.InputFile.newFile": "Afegir nou fitxer",
  "app.components.InputFileDetails.open": "Obrir en una nova pestanya",
  "app.components.InputFileDetails.originalName": "Nom original:",
  "app.components.InputFileDetails.remove": "Eliminar aquest fitxer",
  "app.components.InputFileDetails.size": "Mida:",
  "app.components.InstallPluginPage.Download.description": "La descàrrega i instal·lació de l'extensió podria portar uns segons.",
  "app.components.InstallPluginPage.Download.title": "Descarregant...",
  "app.components.InstallPluginPage.description": "Esteneu la seva aplicació sense esforç.",
  "app.components.LeftMenu.collapse": "Contreure la barra de navegació",
  "app.components.LeftMenu.expand": "Expandir la barra de navegació",
  "app.components.LeftMenu.logout": "Tancar sessió",
  "app.components.LeftMenu.navbrand.title": "Panell de Strapi",
  "app.components.LeftMenu.navbrand.workplace": "Lloc de treball",
  "app.components.LeftMenuFooter.help": "Ajuda",
  "app.components.LeftMenuFooter.poweredBy": "Potenciat per",
  "app.components.LeftMenuLinkContainer.collectionTypes": "Tipus de Col·lecció",
  "app.components.LeftMenuLinkContainer.configuration": "Configuracions",
  "app.components.LeftMenuLinkContainer.general": "General",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "No hi ha extensions instal·lades encara",
  "app.components.LeftMenuLinkContainer.plugins": "Extensions",
  "app.components.LeftMenuLinkContainer.singleTypes": "Tipus Únics",
  "app.components.ListPluginsPage.deletePlugin.description": "És possible que la desinstal·lació de l'extensió trigui uns segons.",
  "app.components.ListPluginsPage.deletePlugin.title": "Desinstal·lar",
  "app.components.ListPluginsPage.description": "Llista d'extensions instal·lades al projecte.",
  "app.components.ListPluginsPage.head.title": "Llista d'extensions'",
  "app.components.Logout.logout": "Tancar sessió",
  "app.components.Logout.profile": "Perfil",
  "app.components.MarketplaceBanner": "Descobriu les extensions creades per la comunitat i moltes més coses increïbles per impulsar el seu projecte, a Strapi Awesome.",
  "app.components.MarketplaceBanner.image.alt": "un logo de coet Strapi",
  "app.components.MarketplaceBanner.link": "Fes-li una ullada ara",
  "app.components.NotFoundPage.back": "Tornar a la pàgina d'inici",
  "app.components.NotFoundPage.description": "No trobat",
  "app.components.Official": "Oficial",
  "app.components.Onboarding.help.button": "Botó d'ajuda",
  "app.components.Onboarding.label.completed": "% completat",
  "app.components.Onboarding.title": "Vídeos introductoris",
  "app.components.PluginCard.Button.label.download": "Descarregar",
  "app.components.PluginCard.Button.label.install": "Ja instal·lat",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "La funció de recàrrega automàtica ha d'estar desactivada. Si us plau, inicieu la vostra aplicació amb `yarn develop`.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Entès!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "Per motius de seguretat, l'extensió només es pot descarregar en entorn de desenvolupament.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "Impossible descarregar",
  "app.components.PluginCard.compatible": "Compatible amb la seva aplicació",
  "app.components.PluginCard.compatibleCommunity": "Compatible amb la comunitat",
  "app.components.PluginCard.more-details": "Més detalls",
  "app.components.ToggleCheckbox.off-label": "Apagat",
  "app.components.ToggleCheckbox.on-label": "Encès",
  "app.components.Users.MagicLink.connect": "Envieu aquest enllaç a l'usuari perquè es connecti.",
  "app.components.Users.MagicLink.connect.sso": "Envieu aquest enllaç a l'usuari, el primer inici de sessió es pot fer a través d'un proveïdor de SSO",
  "app.components.Users.ModalCreateBody.block-title.details": "Detalls",
  "app.components.Users.ModalCreateBody.block-title.roles": "Rols de l'usuari",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "Un usuari pot tenir un o més rols.",
  "app.components.Users.SortPicker.button-label": "Ordenar per",
  "app.components.Users.SortPicker.sortby.email_asc": "Email (de la A a la Z)",
  "app.components.Users.SortPicker.sortby.email_desc": "Email (de la Z a l'A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "Nom (de la A a la Z)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "Nom (de la Z a l'A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "Cognom (de la A a la Z)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "Cognom (de la Z a l'A)",
  "app.components.Users.SortPicker.sortby.username_asc": "Nom d'usuari (de la A a la Z)",
  "app.components.Users.SortPicker.sortby.username_desc": "Nom d'usuari (de la Z a l'A)",
  "app.components.listPlugins.button": "Afegeix una nova extensió",
  "app.components.listPlugins.title.none": "No hi ha extensions instal·lades",
  "app.components.listPluginsPage.deletePlugin.error": "S'ha produït un error en desinstal·lar l'extensió",
  "app.containers.App.notification.error.init": "S'ha produït un error en sol·licitar l'API",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "Si no rebeu aquest enllaç, poseu-vos en contacte amb el vostre administrador.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "És possible que trigui uns minuts a rebre l'enllaç de recuperació de contrasenya.",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "Email enviat",
  "app.containers.Users.EditPage.form.active.label": "Actiu",
  "app.containers.Users.EditPage.header.label": "Edita {name}",
  "app.containers.Users.EditPage.header.label-loading": "Editar usuari",
  "app.containers.Users.EditPage.roles-bloc-title": "Rols atribuïts",
  "app.containers.Users.ModalForm.footer.button-success": "Crear usuari",
  "app.links.configure-view": "Configurar la vista",
  "app.page.not.found": "Vaja! Sembla que no podem trobar la pàgina que estàs buscant...",
  "app.static.links.cheatsheet": "Full de trucs",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "Afegir filtre",
  "app.utils.close-label": "Tancar",
  "app.utils.defaultMessage": " ",
  "app.utils.duplicate": "Duplicar",
  "app.utils.edit": "Edita",
  "app.utils.errors.file-too-big.message": "El fitxer és massa gran",
  "app.utils.filter-value": "Filtre",
  "app.utils.filters": "Filtres",
  "app.utils.notify.data-loaded": "{target} s'ha carregat",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "Publicar",
  "app.utils.select-all": "Selecciona-ho tot",
  "app.utils.select-field": "Seleccionar camp",
  "app.utils.select-filter": "Seleccionar filtre",
  "app.utils.unpublish": "Anul·lar publicació",
  clearLabel,
  "coming.soon": "Aquest contingut està actualment en construcció i estarà de tornada en unes setmanes!",
  "component.Input.error.validation.integer": "El valor ha de ser un nombre enter",
  "components.AutoReloadBlocker.description": "Inicia Strapi amb una de les ordres següents:",
  "components.AutoReloadBlocker.header": "Cal recarregar aquesta extensió.",
  "components.ErrorBoundary.title": "Alguna cosa ha sortit malament...",
  "components.FilterOptions.FILTER_TYPES.$contains": "conté",
  "components.FilterOptions.FILTER_TYPES.$containsi": "conté (no distingeix entre majúscules i minúscules)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "acaba amb",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "acaba amb (no distingeix entre majúscules i minúscules)",
  "components.FilterOptions.FILTER_TYPES.$eq": "és",
  "components.FilterOptions.FILTER_TYPES.$eqi": "és (no distingeix entre majúscules i minúscules)",
  "components.FilterOptions.FILTER_TYPES.$gt": "és més gran que",
  "components.FilterOptions.FILTER_TYPES.$gte": "és més gran o igual a",
  "components.FilterOptions.FILTER_TYPES.$lt": "és menor que",
  "components.FilterOptions.FILTER_TYPES.$lte": "és menor o igual a",
  "components.FilterOptions.FILTER_TYPES.$ne": "no és",
  "components.FilterOptions.FILTER_TYPES.$nei": "no és (no distingeix entre majúscules i minúscules)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "no conté",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "no conté (no distingeix entre majúscules i minúscules)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "no és nul",
  "components.FilterOptions.FILTER_TYPES.$null": "és nul",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "comença amb",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "comença amb (no distingeix entre majúscules i minúscules)",
  "components.Input.error.attribute.key.taken": "Aquest valor ja existeix",
  "components.Input.error.attribute.sameKeyAndName": "No pot ser igual",
  "components.Input.error.attribute.taken": "Aquest nom de camp ja existeix",
  "components.Input.error.contain.lowercase": "La contrasenya ha de contenir com a mínim un caràcter en minúscula",
  "components.Input.error.contain.number": "La contrasenya ha de contenir almenys un número",
  "components.Input.error.contain.uppercase": "La contrasenya ha de contenir com a mínim un caràcter en majúscula",
  "components.Input.error.contentTypeName.taken": "Aquest nom ja existeix",
  "components.Input.error.custom-error": "{errorMessage}",
  "components.Input.error.password.noMatch": "Les contrasenyes no coincideixen",
  "components.Input.error.validation.email": "Això no és un email",
  "components.Input.error.validation.json": "Això no coincideix amb el format JSON",
  "components.Input.error.validation.lowercase": "El valor ha de ser una cadena en minúscules",
  "components.Input.error.validation.max": "El valor és massa alt {max}.",
  "components.Input.error.validation.maxLength": "El valor és massa llarg {max}.",
  "components.Input.error.validation.min": "El valor és massa baix {min}.",
  "components.Input.error.validation.minLength": "El valor és massa curt {min}.",
  "components.Input.error.validation.minSupMax": "No pot ser superior",
  "components.Input.error.validation.regex": "El valor no coincideix amb el de regex.",
  "components.Input.error.validation.required": "Aquest valor és obligatori.",
  "components.Input.error.validation.unique": "Aquest valor ja es fa servir.",
  "components.InputSelect.option.placeholder": "Trieu aquí",
  "components.ListRow.empty": "No hi ha dades per mostrar.",
  "components.NotAllowedInput.text": "Sense permisos per veure aquest camp.",
  "components.OverlayBlocker.description": "Esteu utilitzant una funció que necessita que el servidor es reiniciï. Si us plau, espereu fins que el servidor estigui llest.",
  "components.OverlayBlocker.description.serverError": "El servidor s'hauria d'haver reiniciat, comproveu els vostres logs al terminal.",
  "components.OverlayBlocker.title": "Esperant el reinici...",
  "components.OverlayBlocker.title.serverError": "El reinici està portant més temps del que s'esperava",
  "components.PageFooter.select": "entrades per pàgina",
  "components.ProductionBlocker.description": "Per raons de seguretat hem de desactivar aquesta extensió en altres entorns.",
  "components.ProductionBlocker.header": "Aquesta extensió només està disponible en entorns de desenvolupament.",
  "components.Search.placeholder": "Cerca...",
  "components.TableHeader.sort": "Ordenar per {label}",
  "components.Wysiwyg.ToggleMode.markdown-mode": "Mode de Markdown",
  "components.Wysiwyg.ToggleMode.preview-mode": "Mode de vista prèvia",
  "components.Wysiwyg.collapse": "Contreure menú",
  "components.Wysiwyg.selectOptions.H1": "Títol H1",
  "components.Wysiwyg.selectOptions.H2": "Títol H2",
  "components.Wysiwyg.selectOptions.H3": "Títol H3",
  "components.Wysiwyg.selectOptions.H4": "Títol H4",
  "components.Wysiwyg.selectOptions.H5": "Títol H5",
  "components.Wysiwyg.selectOptions.H6": "Títol H6",
  "components.Wysiwyg.selectOptions.title": "Afegir un títol",
  "components.WysiwygBottomControls.charactersIndicators": "caràcters",
  "components.WysiwygBottomControls.fullscreen": "Expandir",
  "components.WysiwygBottomControls.uploadFiles": "Arrossegar i deixar anar arxius, enganxar des del porta-retalls o {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "seleccionar-los",
  "components.pagination.go-to": "Anar a la pàgina {page}",
  "components.pagination.go-to-next": "Anar a la pàgina següent",
  "components.pagination.go-to-previous": "Tornar a la pàgina anterior",
  "components.pagination.remaining-links": "I {number} enllaços més",
  "components.popUpWarning.button.cancel": "No, cancel·lar",
  "components.popUpWarning.button.confirm": "Sí, confirmar",
  "components.popUpWarning.message": "Segur que vol esborrar això?",
  "components.popUpWarning.title": "Si us plau, confirmeu",
  "form.button.continue": "Continua",
  "form.button.done": "Fet",
  "global.actions": "Accions",
  "global.back": "torna",
  "global.change-password": "Canvia la contrasenya",
  "global.content-manager": "Gestor de continguts",
  "global.continue": "Continua",
  "global.delete": "Suprimeix",
  "global.delete-target": "Suprimeix {target}",
  "global.description": "Descripció",
  "global.details": "Detalls",
  "global.disabled": "Inhabilitat",
  "global.documentation": "Documentació",
  "global.enabled": "Habilitat",
  "global.finish": "Finalitzar",
  "global.marketplace": "Botiga",
  "global.name": "Nom",
  "global.none": "Cap",
  "global.password": "Contrasenya",
  "global.plugins": "Extensions",
  "global.profile": "Perfil",
  "global.prompt.unsaved": "Esteu segur que voleu sortir d'aquesta pàgina? Totes les seves modificacions es perdran",
  "global.reset-password": "Restablir la contrasenya",
  "global.roles": "Rols",
  "global.save": "Desa",
  "global.see-more": "Veure més",
  "global.select": "Seleccioneu",
  "global.select-all-entries": "Seleccioneu totes les entrades",
  "global.settings": "Configuració",
  "global.type": "Tipus",
  "global.users": "Usuaris",
  "notification.contentType.relations.conflict": "El tipus de contingut té relacions conflictives",
  "notification.default.title": "Informació:",
  "notification.error": "S'ha produït un error",
  "notification.error.layout": "No s'ha pogut recuperar el disseny",
  "notification.form.error.fields": "El formulari conté alguns errors",
  "notification.form.success.fields": "Canvis desats",
  "notification.link-copied": "Enllaç copiat al porta-retalls",
  "notification.permission.not-allowed-read": "No tens permís per veure aquest document",
  "notification.success.delete": "L'element ha estat eliminat",
  "notification.success.saved": "Desat",
  "notification.success.title": "Èxit:",
  "notification.version.update.message": "Hi ha una nova versió de Strapi disponible!",
  "notification.warning.title": "Advertència:",
  or,
  "request.error.model.unknown": "Aquest model no existeix",
  skipToContent,
  submit
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  ca as default,
  or,
  skipToContent,
  submit
};
//# sourceMappingURL=ca-f06Q0InB-6KZHA5TO.js.map
