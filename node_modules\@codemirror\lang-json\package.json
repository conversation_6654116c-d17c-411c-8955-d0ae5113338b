{"name": "@codemirror/lang-json", "version": "6.0.1", "description": "JSON language support for the CodeMirror code editor", "scripts": {"test": "cm-runtests", "prepare": "cm-buildhelper src/json.ts"}, "keywords": ["editor", "code"], "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "http://marijnhaverbeke.nl"}, "type": "module", "main": "dist/index.cjs", "exports": {"import": "./dist/index.js", "require": "./dist/index.cjs"}, "types": "dist/index.d.ts", "module": "dist/index.js", "sideEffects": false, "license": "MIT", "dependencies": {"@codemirror/language": "^6.0.0", "@lezer/json": "^1.0.0"}, "devDependencies": {"@codemirror/buildhelper": "^0.1.0"}, "repository": {"type": "git", "url": "https://github.com/codemirror/lang-json.git"}}