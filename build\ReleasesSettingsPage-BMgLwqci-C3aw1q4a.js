import{ap as R,m as e,n as c,gj as f,c as v,a as C,gk as w,gl as A,e as h,gm as x,L as u,O as P,A as I,N as L,w as E,s as F,G as p,aC as D,x as d,aD as B,aE as G,b7 as H}from"./strapi-YzJfjJ2z.js";import{S as N}from"./schemas-DdA2ic2U-C96YG4Sw.js";const m=R,U=()=>{const{formatMessage:s}=f(),{formatAPIError:r}=v(),{toggleNotification:n}=C(),{data:g,isLoading:a}=w(),[t,{isLoading:S}]=A(),j=m(i=>i.admin_app.permissions.settings?.releases),{allowedActions:{canUpdate:T}}=h(j),{timezoneList:y}=x(new Date),z=async i=>{const{defaultTimezone:l}=i,b=y.some(o=>o.value===l),M=!l||!b?{defaultTimezone:null}:{...i};try{const o=await t(M);"data"in o?n({type:"success",message:s({id:"content-releases.pages.Settings.releases.setting.default-timezone-notification-success",defaultMessage:"Default timezone updated."})}):H(o.error)?n({type:"danger",message:r(o.error)}):n({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})}catch{n({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})}};return a?e.jsx(c.Loading,{}):e.jsxs(u.Root,{children:[e.jsx(c.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Releases"})}),e.jsx(c.Main,{"aria-busy":a,tabIndex:-1,children:e.jsx(P,{method:"PUT",initialValues:{defaultTimezone:g?.data.defaultTimezone},onSubmit:z,validationSchema:N,children:({modified:i,isSubmitting:l})=>e.jsxs(e.Fragment,{children:[e.jsx(u.Header,{primaryAction:T?e.jsx(I,{disabled:!i||S,loading:l,startIcon:e.jsx(L,{}),type:"submit",children:s({id:"global.save",defaultMessage:"Save"})}):null,title:s({id:"content-releases.pages.Settings.releases.title",defaultMessage:"Releases"}),subtitle:s({id:"content-releases.pages.Settings.releases.description",defaultMessage:"Create and manage content updates"})}),e.jsx(u.Content,{children:e.jsxs(E,{direction:"column",background:"neutral0",alignItems:"stretch",padding:6,gap:6,shadow:"filterShadow",hasRadius:!0,children:[e.jsx(F,{variant:"delta",tag:"h2",children:s({id:"content-releases.pages.Settings.releases.preferences.title",defaultMessage:"Preferences"})}),e.jsx(p.Root,{children:e.jsx(p.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsx(_,{})})})]})})]})})})]})},_=()=>{const s=m(t=>t.admin_app.permissions.settings?.releases),{allowedActions:{canUpdate:r}}=h(s),{formatMessage:n}=f(),{timezoneList:g}=x(new Date),a=D("defaultTimezone");return e.jsxs(d.Root,{name:"defaultTimezone",hint:n({id:"content-releases.pages.Settings.releases.timezone.hint",defaultMessage:"The timezone of every release can still be changed individually. "}),error:a.error,children:[e.jsx(d.Label,{children:n({id:"content-releases.pages.Settings.releases.timezone.label",defaultMessage:"Default timezone"})}),e.jsx(B,{autocomplete:{type:"list",filter:"contains"},onChange:t=>a.onChange("defaultTimezone",t),onTextValueChange:t=>a.onChange("defaultTimezone",t),onClear:()=>a.onChange("defaultTimezone",""),value:a.value,disabled:!r,children:g.map(t=>e.jsx(G,{value:t.value,children:t.value.replace(/&/," ")},t.value))}),e.jsx(d.Hint,{}),e.jsx(d.Error,{})]})},O=()=>{const s=m(r=>r.admin_app.permissions.settings?.releases?.read);return e.jsx(c.Protect,{permissions:s,children:e.jsx(U,{})})};export{O as ProtectedReleasesSettingsPage};
