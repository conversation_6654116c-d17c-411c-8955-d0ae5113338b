{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/sk-DQ2qpsG2.mjs"], "sourcesContent": ["const Analytics = \"Analytika\";\nconst Documentation = \"Dokumentácia\";\nconst Email = \"E-mailová adresa\";\nconst Password = \"<PERSON><PERSON><PERSON>\";\nconst Provider = \"Poskytovateľ\";\nconst ResetPasswordToken = \"Token pre obnovu hesla\";\nconst Role = \"Rola\";\nconst light = \"Svetlý\";\nconst dark = \"Tmavý\";\nconst Username = \"Používateľské meno\";\nconst Users = \"Používatelia\";\nconst anErrorOccurred = \"Hups! Niečo sa pokazilo. Prosím, skúste znovu.\";\nconst clearLabel = \"Vyčistiť\";\nconst or = \"ALEBO\";\nconst skipToContent = \"Prejsť na obsah\";\nconst submit = \"Odoslať\";\nconst sk = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"V<PERSON><PERSON> ú<PERSON>et bol pozastavený\",\n\t\"Auth.components.Oops.text.admin\": \"Ak je toto chyba, kontaktuje prosím administrátora.\",\n\t\"Auth.components.Oops.title\": \"Hups...\",\n\t\"Auth.form.active.label\": \"Aktívny\",\n\t\"Auth.form.button.forgot-password\": \"Poslať e-mail\",\n\t\"Auth.form.button.go-home\": \"NASPÄŤ DOMOV\",\n\t\"Auth.form.button.login\": \"Prihlásiť sa\",\n\t\"Auth.form.button.login.providers.error\": \"Cez vybraného poskytovateľa sa nám nepodarilo vás prepojiť.\",\n\t\"Auth.form.button.login.strapi\": \"Prihlásiť sa cez Strapi\",\n\t\"Auth.form.button.password-recovery\": \"Obnovenie hesla\",\n\t\"Auth.form.button.register\": \"Registrovať sa\",\n\t\"Auth.form.confirmPassword.label\": \"Potvrdenie hesla\",\n\t\"Auth.form.currentPassword.label\": \"Aktuálne heslo\",\n\t\"Auth.form.email.label\": \"E-mailová adresa\",\n\t\"Auth.form.email.placeholder\": \"napr. <EMAIL>\",\n\t\"Auth.form.error.blocked\": \"Váš účet bol zablokovaný administrátorom.\",\n\t\"Auth.form.error.code.provide\": \"Zadaný kód je neplatný.\",\n\t\"Auth.form.error.confirmed\": \"Táto e-mailová adresa nie je overená.\",\n\t\"Auth.form.error.email.invalid\": \"Táto e-mailová adresa je neplatná.\",\n\t\"Auth.form.error.email.provide\": \"Prosím zadajte vaše používateľské meno alebo e-mailovú adresu.\",\n\t\"Auth.form.error.email.taken\": \"E-mailová adresa je už registrovaná.\",\n\t\"Auth.form.error.invalid\": \"Používateľské meno alebo heslo je nesprávne.\",\n\t\"Auth.form.error.params.provide\": \"Zadané hodnoty sú nesprávne.\",\n\t\"Auth.form.error.password.format\": \"Vaše heslo obsahuje znak `$` viac ako trikrát.\",\n\t\"Auth.form.error.password.local\": \"Tento používateľ si nikdy nenastavil heslo, prosím prihláste sa cez poskytovateľa použitého pri registrácii.\",\n\t\"Auth.form.error.password.matching\": \"Heslá sa nezhodujú.\",\n\t\"Auth.form.error.password.provide\": \"Prosím zadajte vaše heslo.\",\n\t\"Auth.form.error.ratelimit\": \"Príliš veľa pokusov, voľbu opakujte neskôr.\",\n\t\"Auth.form.error.user.not-exist\": \"E-mailová adresa neexistuje.\",\n\t\"Auth.form.error.username.taken\": \"Zvolené používateľské meno už je registrované\",\n\t\"Auth.form.firstname.label\": \"Krstné meno\",\n\t\"Auth.form.firstname.placeholder\": \"Janko\",\n\t\"Auth.form.forgot-password.email.label\": \"Zadajte e-mailovú adresu\",\n\t\"Auth.form.forgot-password.email.label.success\": \"Email bol odoslaný na zadanú adresu\",\n\t\"Auth.form.lastname.label\": \"Priezvisko\",\n\t\"Auth.form.lastname.placeholder\": \"Hraško\",\n\t\"Auth.form.password.hide-password\": \"Schovať heslo\",\n\t\"Auth.form.password.hint\": \"Musí obsahovať aspoň 8 znakov, veľké a malé písmeno a číslo\",\n\t\"Auth.form.password.show-password\": \"Zobraziť heslo\",\n\t\"Auth.form.register.news.label\": \"Informujte ma o nových funkciách a pripravovaných vylepšeniach (týmto akceptujete {terms} a {policy}).\",\n\t\"Auth.form.rememberMe.label\": \"Zapamätať si\",\n\t\"Auth.form.username.label\": \"Používateľské meno\",\n\t\"Auth.form.username.placeholder\": \"Janko Hraško\",\n\t\"Auth.form.welcome.subtitle\": \"Prihláste sa do Vášho Strapi účtu\",\n\t\"Auth.form.welcome.title\": \"Vitajte v Strapi!\",\n\t\"Auth.link.forgot-password\": \"Zabudli ste heslo?\",\n\t\"Auth.link.ready\": \"Chcete sa prihlásiť?\",\n\t\"Auth.link.signin\": \"Prihlásiť sa\",\n\t\"Auth.link.signin.account\": \"Máte už vytvorený účet?\",\n\t\"Auth.login.sso.divider\": \"Alebo sa prihláste pomocou\",\n\t\"Auth.login.sso.loading\": \"Načítavam poskytovateľov...\",\n\t\"Auth.login.sso.subtitle\": \"Prihláste sa pomocou SSO\",\n\t\"Auth.privacy-policy-agreement.policy\": \"zásady ochrany osobných údajov\",\n\t\"Auth.privacy-policy-agreement.terms\": \"podmienky používania\",\n\t\"Auth.reset-password.title\": \"Obnoviť heslo\",\n\t\"Content Manager\": \"Správca obsahu\",\n\t\"Content Type Builder\": \"Tvorca obsahových typov\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Nahrať súbory\",\n\t\"HomePage.head.title\": \"Úvodná stránka\",\n\t\"HomePage.roadmap\": \"Pozrite sa na smerovanie projektu\",\n\t\"HomePage.welcome.congrats\": \"Hurá!\",\n\t\"HomePage.welcome.congrats.content\": \"Ste prihlásený ako prvý administrátor. Na zoznámenie sa s úžasnými funkciami Strapi\",\n\t\"HomePage.welcome.congrats.content.bold\": \"Vám odporúčame pokračovať vytvorením prvej kolekcie\",\n\t\"Media Library\": \"Knižnica súborov\",\n\t\"New entry\": \"Nový záznam\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"Roly a oprávnenia\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"Niektoré roly nebolo možné odstrániť, pretože sú spojené s používateľmi\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"Rola nemôže byť zmazaná, pretože je spojená s používateľmi.\",\n\t\"Roles.components.List.empty.withSearch\": \"Neexistuje žiadna rola, ktorá zodpovedá vyhľadávaniu ({search})...\",\n\t\"Settings.PageTitle\": \"Nastavenia - {name}\",\n\t\"Settings.application.title\": \"Prehľad\",\n\t\"Settings.application.customization\": \"Prispôsobenie\",\n\t\"Settings.application.customization.carousel.title\": \"Logo\",\n\t\"Settings.application.customization.carousel.change-action\": \"Zmeniť logo\",\n\t\"Settings.application.customization.carousel.reset-action\": \"Obnoviť logo\",\n\t\"Settings.application.customization.carousel-hint\": \"Zmeniť logo admin panelu (max. rozmery: {dimension}x{dimension}, max. veľkosť: {size}KB)\",\n\t\"Settings.application.customization.modal.cancel\": \"Zrušiť\",\n\t\"Settings.application.customization.modal.upload\": \"Nahrať logo\",\n\t\"Settings.application.customization.modal.tab.label\": \"Ako si prajete nahrať vaše súbory?\",\n\t\"Settings.application.customization.modal.upload.from-computer\": \"Z počítača\",\n\t\"Settings.application.customization.modal.upload.file-validation\": \"max. rozmery: {dimension}x{dimension}, max. veľkosť: {size}KB\",\n\t\"Settings.application.customization.modal.upload.error-format\": \"Zlý formát (podorované formáty: jpeg, jpg, png, svg).\",\n\t\"Settings.application.customization.modal.upload.error-size\": \"Nahrávaný súbor je príliš veľký (max. rozmery: {dimension}x{dimension}, max. veľkosť: {size}KB)\",\n\t\"Settings.application.customization.modal.upload.error-network\": \"Chyba pripojenia\",\n\t\"Settings.application.customization.modal.upload.cta.browse\": \"Prehľadávať súbory\",\n\t\"Settings.application.customization.modal.upload.drag-drop\": \"Drag & Drop alebo\",\n\t\"Settings.application.customization.modal.upload.from-url\": \"Z url\",\n\t\"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n\t\"Settings.application.customization.modal.upload.next\": \"Ďalej\",\n\t\"Settings.application.customization.modal.pending\": \"Čakajúce logo\",\n\t\"Settings.application.customization.modal.pending.choose-another\": \"Vybrať iné logo\",\n\t\"Settings.application.customization.modal.pending.title\": \"Logo je pripravené na nahratie\",\n\t\"Settings.application.customization.modal.pending.subtitle\": \"Spravovať vybrané logo pred nahratím\",\n\t\"Settings.application.customization.modal.pending.upload\": \"Nahrať logo\",\n\t\"Settings.application.customization.modal.pending.card-badge\": \"obrázok\",\n\t\"Settings.error\": \"Chyba\",\n\t\"Settings.global\": \"Globálne nastavenia\",\n\t\"Settings.permissions\": \"Oprávnenia\",\n\t\"Settings.permissions.category\": \"Nastavenie oprávnení pre {category}\",\n\t\"Settings.permissions.category.plugins\": \"Nastavenie oprávnení pre {category} plugin\",\n\t\"Settings.permissions.conditions.anytime\": \"Kedykoľvek\",\n\t\"Settings.permissions.conditions.apply\": \"Použiť\",\n\t\"Settings.permissions.conditions.can\": \"Môcť\",\n\t\"Settings.permissions.conditions.conditions\": \"Zadajte podmienky\",\n\t\"Settings.permissions.conditions.links\": \"Odkazy\",\n\t\"Settings.permissions.conditions.no-actions\": \"Akcia neexistuje\",\n\t\"Settings.permissions.conditions.none-selected\": \"Kedykoľvek\",\n\t\"Settings.permissions.conditions.or\": \"ALEBO\",\n\t\"Settings.permissions.conditions.when\": \"Kedy\",\n\t\"Settings.permissions.select-all-by-permission\": \"Vybrať všetky {label} oprávnenia\",\n\t\"Settings.permissions.select-by-permission\": \"Vybrať {label} oprávnenie\",\n\t\"Settings.permissions.users.create\": \"Vytvoriť používateľa\",\n\t\"Settings.permissions.users.email\": \"Email\",\n\t\"Settings.permissions.users.firstname\": \"Krstné meno\",\n\t\"Settings.permissions.users.lastname\": \"Priezvisko\",\n\t\"Settings.permissions.users.user-status\": \"Status\",\n\t\"Settings.permissions.users.roles\": \"Roly\",\n\t\"Settings.permissions.users.username\": \"Používateľské meno\",\n\t\"Settings.permissions.users.active\": \"Aktívny\",\n\t\"Settings.permissions.users.inactive\": \"Neaktívny\",\n\t\"Settings.permissions.users.form.sso\": \"Prepojiť s SSO\",\n\t\"Settings.permissions.users.form.sso.description\": \"Ak je dostupný (ON), používatelia sa môžu prihlásiť pomocou SSO\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"Všetci používatelia, ktorí majú prístup do Strapi admin panelu\",\n\t\"Settings.permissions.users.tabs.label\": \"Tabs Oprávnenia\",\n\t\"Settings.permissions.users.strapi-super-admin\": \"Super Admin\",\n\t\"Settings.permissions.users.strapi-editor\": \"Editor\",\n\t\"Settings.permissions.users.strapi-author\": \"Autor\",\n\t\"Settings.profile.form.notify.data.loaded\": \"Váš profil bol načítaný\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"Odstrániť vybraný jazyk rozhrania\",\n\t\"Settings.profile.form.section.experience.here\": \"tu\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"Jazyk rozhrania\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Jazyk bude zmenený iba pre Vaše rozhranie.\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Tieto nastavenia sa vzťahujú iba na Vás. Viac informácií nájdete {here}.\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"Mód rozhrania\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"Mód rozhrania bude zmenený iba pre Vás.\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{name} mód\",\n\tlight: light,\n\tdark: dark,\n\t\"Settings.profile.form.section.experience.title\": \"Používateľské rozhranie\",\n\t\"Settings.profile.form.section.head.title\": \"Profil používateľa\",\n\t\"Settings.profile.form.section.profile.page.title\": \"Profil\",\n\t\"Settings.roles.create.description\": \"Definujte práva priradené role\",\n\t\"Settings.roles.create.title\": \"Votvoriť rolu\",\n\t\"Settings.roles.created\": \"Rola bola vytvorená\",\n\t\"Settings.roles.edit.title\": \"Upraviť rolu\",\n\t\"Settings.roles.form.button.users-with-role\": \"Používatelia s touto rolou\",\n\t\"Settings.roles.form.created\": \"Vytvorené\",\n\t\"Settings.roles.form.description\": \"Názov a popis roly\",\n\t\"Settings.roles.form.permission.property-label\": \"{label} oprávnenia\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"Oprávnenia polí\",\n\t\"Settings.roles.form.permissions.create\": \"Vytvoriť\",\n\t\"Settings.roles.form.permissions.delete\": \"Vymazať\",\n\t\"Settings.roles.form.permissions.publish\": \"Publikovať\",\n\t\"Settings.roles.form.permissions.read\": \"Čítať\",\n\t\"Settings.roles.form.permissions.update\": \"Zmeniť\",\n\t\"Settings.roles.list.button.add\": \"Pridať novú rolu\",\n\t\"Settings.roles.list.description\": \"Zoznam rolí\",\n\t\"Settings.roles.title.singular\": \"Rola\",\n\t\"Settings.sso.description\": \"Upravte nastavenia pre Single Sign-On.\",\n\t\"Settings.sso.form.defaultRole.description\": \"Prepojí nového autentifikovaného používateľa s vybranou rolou\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"Pre čítanie admin rolí potrebujete mať potrebné oprávnenia\",\n\t\"Settings.sso.form.defaultRole.label\": \"Predvolená rola\",\n\t\"Settings.sso.form.registration.description\": \"Vytvoriť nového používateľa pri SSO prihlásení, ak taký účet neexistuje\",\n\t\"Settings.sso.form.registration.label\": \"Auto-registrácia\",\n\t\"Settings.sso.title\": \"Single Sign-On\",\n\t\"Settings.webhooks.create\": \"Vytvoriť Webhook\",\n\t\"Settings.webhooks.create.header\": \"Vytvoriť novú hlavičku\",\n\t\"Settings.webhooks.created\": \"Webhook bol vytvorený\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"Táto udalosť existuje iba pre obsah s povoleným Draft/Publish systémom\",\n\t\"Settings.webhooks.events.create\": \"Vytvoriť\",\n\t\"Settings.webhooks.events.update\": \"Upraviť\",\n\t\"Settings.webhooks.form.events\": \"Eventy\",\n\t\"Settings.webhooks.form.headers\": \"Hlavičky\",\n\t\"Settings.webhooks.form.url\": \"Url\",\n\t\"Settings.webhooks.headers.remove\": \"Odstrániť riadok hlavičky č. {number}\",\n\t\"Settings.webhooks.key\": \"Klúč\",\n\t\"Settings.webhooks.list.button.add\": \"Pridať nový Webhook\",\n\t\"Settings.webhooks.list.description\": \"Získajte upozornenia na POST request zmeny.\",\n\t\"Settings.webhooks.list.empty.description\": \"Pridajte Váš prvý webhook do zoznamu.\",\n\t\"Settings.webhooks.list.empty.link\": \"Pozrieť si našu dokumentáciu\",\n\t\"Settings.webhooks.list.empty.title\": \"Zatiaľ neexistujú žiadne Webhooky\",\n\t\"Settings.webhooks.list.th.actions\": \"akcie\",\n\t\"Settings.webhooks.list.th.status\": \"stav\",\n\t\"Settings.webhooks.singular\": \"Webhook\",\n\t\"Settings.webhooks.title\": \"Webhooky\",\n\t\"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# vybraný záznam} few {# vybrané záznamy} other {# vybraných záznamov}}\",\n\t\"Settings.webhooks.trigger\": \"Spustiť\",\n\t\"Settings.webhooks.trigger.cancel\": \"Zrušiť\",\n\t\"Settings.webhooks.trigger.pending\": \"Čakám…\",\n\t\"Settings.webhooks.trigger.save\": \"Pre spustenie najskôr uložte\",\n\t\"Settings.webhooks.trigger.success\": \"Podarilo sa!\",\n\t\"Settings.webhooks.trigger.success.label\": \"Trigger bol úspešný\",\n\t\"Settings.webhooks.trigger.test\": \"Testovacie spustenier\",\n\t\"Settings.webhooks.trigger.title\": \"Pred spustením najskôr uložte\",\n\t\"Settings.webhooks.value\": \"Hodnota\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"Používatelia a oprávnenia\",\n\t\"Users.components.List.empty\": \"Neexistujú žiadny používatelia...\",\n\t\"Users.components.List.empty.withFilters\": \"Neexistujú žiadny používatelia so zvolenými filtrami...\",\n\t\"Users.components.List.empty.withSearch\": \"Neexistujú žiadny používatelia zodpovedajúci vyhľadávaniu ({search})...\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"Skopírovať do schránky\",\n\t\"app.component.search.label\": \"Vyhľadať {target}\",\n\t\"app.component.table.duplicate\": \"Duplikovať {target}\",\n\t\"app.component.table.edit\": \"Upraviť {target}\",\n\t\"app.component.table.select.one-entry\": \"Vybrať {target}\",\n\t\"app.components.Button.cancel\": \"Zrušiť\",\n\t\"app.components.Button.confirm\": \"Potvrdiť\",\n\t\"app.components.Button.reset\": \"Obnoviť\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"Už čoskoro\",\n\t\"app.components.ConfirmDialog.title\": \"Potvrdenie\",\n\t\"app.components.DownloadInfo.download\": \"Prebieha sťahovanie...\",\n\t\"app.components.DownloadInfo.text\": \"Toto bude chvíľu trvať, prosíme o trpezlivosť.\",\n\t\"app.components.EmptyAttributes.title\": \"Zatiaľ tu nie sú žiadne políčka\",\n\t\"app.components.EmptyStateLayout.content-document\": \"Žiadny nájdený obsah\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"Nemáte oprávnenia pre prístup k tomuto obsahu\",\n\t\"app.components.HomePage.button.blog\": \"Čítať na blogu\",\n\t\"app.components.HomePage.community\": \"Nájdite komunitu na webe\",\n\t\"app.components.HomePage.community.content\": \"Komunikujte s členmi tímu a vývojármi, zdieľajte vaše problémy a nápady.\",\n\t\"app.components.HomePage.create\": \"Vytvorte váš prvý obsahový typ\",\n\t\"app.components.HomePage.roadmap\": \"Pozrite si našu roadmapu\",\n\t\"app.components.HomePage.welcome\": \"Vitajte na palube 👋\",\n\t\"app.components.HomePage.welcome.again\": \"Vitajte 👋\",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Sme radi, že ste súčasťou komunity. Vždy sa tešíme na spätnú väzbu, preto nás neváhajte kontaktovať správou\",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"Dúfame, že sa vám darí s vaším projektom. Pozrite si čo je nové v Strapi. Vždy sa snažíme vylepšovať produkt na základe spätnej väzby.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"problém.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" alebo nahláste \",\n\t\"app.components.ImgPreview.hint\": \"Pretiahnite súbory alebo {browse}\",\n\t\"app.components.ImgPreview.hint.browse\": \"vyberte\",\n\t\"app.components.InputFile.newFile\": \"Pridať súbor\",\n\t\"app.components.InputFileDetails.open\": \"Otvoriť v novom okne\",\n\t\"app.components.InputFileDetails.originalName\": \"Pôvodný názov:\",\n\t\"app.components.InputFileDetails.remove\": \"Odstrániť tento súbor\",\n\t\"app.components.InputFileDetails.size\": \"Veľkosť:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"Môže to chvíľu trvať, kým sa plugin stiahne a nainštaluje.\",\n\t\"app.components.InstallPluginPage.Download.title\": \"Sťahovanie...\",\n\t\"app.components.InstallPluginPage.description\": \"Rozšírte možnosti aplikácie bez námahy.\",\n\t\"app.components.LeftMenu.collapse\": \"Zbaliť navigačný panel\",\n\t\"app.components.LeftMenu.expand\": \"Rozbaliť navigačný panel\",\n\t\"app.components.LeftMenu.general\": \"Všeoecné\",\n\t\"app.components.LeftMenu.logout\": \"Odhlásiť sa\",\n\t\"app.components.LeftMenu.logo.alt\": \"Logo aplikácie\",\n\t\"app.components.LeftMenu.plugins\": \"Pluginy\",\n\t\"app.components.LeftMenu.navbrand.title\": \"Strapi Dashboard\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"Workplace\",\n\t\"app.components.LeftMenuFooter.help\": \"Pomoc\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Poháňané \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Kolekcie\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Konfigurácia\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Všeobecné\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Žiadne pluginy nie sú zatiaľ nainštalované\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Pluginy\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Jednoduché typy\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"Odinštalovanie pluginu môže trvať niekoľko sekúnd.\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"Odstraňujem plugin\",\n\t\"app.components.ListPluginsPage.description\": \"Zoznam nainštalovaných pluginov v tomto projekte.\",\n\t\"app.components.ListPluginsPage.head.title\": \"Zoznam pluginov\",\n\t\"app.components.Logout.logout\": \"Odhlásiť sa\",\n\t\"app.components.Logout.profile\": \"Profil\",\n\t\"app.components.NotFoundPage.back\": \"Naspať na úvodnú stránku\",\n\t\"app.components.NotFoundPage.description\": \"Nenájdené\",\n\t\"app.components.Official\": \"Oficiálne\",\n\t\"app.components.Onboarding.help.button\": \"Tlačidlo pomoci\",\n\t\"app.components.Onboarding.label.completed\": \"% dokončené\",\n\t\"app.components.Onboarding.title\": \"Začíname\",\n\t\"app.components.PluginCard.Button.label.download\": \"Stiahnuť\",\n\t\"app.components.PluginCard.Button.label.install\": \"Už nainštalované\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Funkcia autoReload musí byť vypnutá. Prosím zapnite aplikíciu cez `yarn develop`.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Rozumiem!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Kvôli bezpečnosti môže byť plugin nainštalovaný iba v development prostredí.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Nie je možné stiahnuť\",\n\t\"app.components.PluginCard.compatible\": \"Kompatibilné s vašou aplikáciou\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Kompatibilné s komunitou\",\n\t\"app.components.PluginCard.more-details\": \"Viac detailov\",\n\t\"app.components.ToggleCheckbox.off-label\": \"False\",\n\t\"app.components.ToggleCheckbox.on-label\": \"True\",\n\t\"app.components.Users.MagicLink.connect\": \"Skopírujte a zazdieľajte tento link používateľovi pre pihlásenie.\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"Odošlite tento link používateľovi. Prvé prihlásenie môže byť vykonané cez SSO poskytovateľa\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"Detaily\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"Používateľové roly\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Používateľ môže mať jednu alebo viacer rolí\",\n\t\"app.components.Users.SortPicker.button-label\": \"Zoradiť podľa\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"Email (A do Z)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"Email (Z do A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"Krstné meno (A do Z)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"Krstné meno (Z do A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"Priezvisko (A do Z)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"Priezvisko (Z do A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"Používateľské meno (A do Z)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"Používateľské meno (Z do A)\",\n\t\"app.components.listPlugins.button\": \"Pridať nový plugin\",\n\t\"app.components.listPlugins.title.none\": \"Nie sú nainštalované žiadne pluginy\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Nastala chyba pri odinštalovávaní pluginu\",\n\t\"app.containers.App.notification.error.init\": \"Nastala chyba pri komunikácii s API serverom\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Ak ste neobdržali tento link, tak prosím kontaktujte administrátora.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Obdržanie odkazu na obnovenie hesla môže trvať pár minút.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email bol odoslaný\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"Aktívny\",\n\t\"app.containers.Users.EditPage.header.label\": \"Upraviť {name}\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"Upraviť používateľa\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"Priradené roly\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"Vytvoriť používateľa\",\n\t\"app.links.configure-view\": \"Upraviť zobrazenie\",\n\t\"app.page.not.found\": \"Hups! Vyzerá to tak, že stránku, ktorú hľadáte, nevieme nájsť...\",\n\t\"app.static.links.cheatsheet\": \"CheatSheet\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"Pridať filter\",\n\t\"app.utils.close-label\": \"Zavrieť\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.duplicate\": \"Duplikovať\",\n\t\"app.utils.edit\": \"Upraviť\",\n\t\"app.utils.delete\": \"Vymazať\",\n\t\"app.utils.errors.file-too-big.message\": \"Súbor je príliš veľký\",\n\t\"app.utils.filter-value\": \"Hodnota filtra\",\n\t\"app.utils.filters\": \"Filtre\",\n\t\"app.utils.notify.data-loaded\": \"{target} sa načítal\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"Publikovať\",\n\t\"app.utils.select-all\": \"Zvoliť všetky\",\n\t\"app.utils.select-field\": \"Zvoliť políčko\",\n\t\"app.utils.select-filter\": \"Zvoliť filter\",\n\t\"app.utils.unpublish\": \"Odpublikovať\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"Tento obsah sa práve pripravuje a bude dostupný už o niekoľko týždňov!\",\n\t\"component.Input.error.validation.integer\": \"Táto hodnota musí byť číslo\",\n\t\"components.AutoReloadBlocker.description\": \"Spustite Strapi s jedným z nasledujúcich príkazov:\",\n\t\"components.AutoReloadBlocker.header\": \"Pre tento plugin je požadované opätovné načítanie stránky.\",\n\t\"components.ErrorBoundary.title\": \"Niečo sa pokazilo...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"obsahuje\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"obsahuje (nerozlišujú sa malé a veľké písmená)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"končí na\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"končí na (nerozlišujú sa malé a veľké písmená)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"je\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"je (nerozlišujú sa malé a veľké písmená)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"je väčší ako\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"je väčší alebo rovný ako\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"je menší ako\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"je menčí alebo rovný ako\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"sa nerovná\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"sa nerovná (nerozlišujú sa malé a veľké písmená)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"neobsahuje\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"neobsahuje (nerozlišujú sa malé a veľké písmená)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"nie je null\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"je null\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"začína na\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"začína na (nerozlišujú sa malé a veľké písmená)\",\n\t\"components.Input.error.attribute.key.taken\": \"Táto hodnota už existuje\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Nemôže sa rovnať\",\n\t\"components.Input.error.attribute.taken\": \"Plíčko s týmto názvom už existuje\",\n\t\"components.Input.error.contain.lowercase\": \"Heslo musí obsahovať aspoň jedno malé písmeno\",\n\t\"components.Input.error.contain.number\": \"Heslo musí obsahovať aspoň jedno číslo\",\n\t\"components.Input.error.contain.uppercase\": \"Heslo musí obsahovať aspoň jedo veľké písmeno\",\n\t\"components.Input.error.contentTypeName.taken\": \"Obsahový typ s týmto názvom už existuje\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"Heslá sa nezhodujú\",\n\t\"components.Input.error.validation.email\": \"Neplatná e-mailová adresa\",\n\t\"components.Input.error.validation.json\": \"Táto hodnota nespĺňa JSON formát\",\n\t\"components.Input.error.validation.lowercase\": \"Táto hodnota môže obrahovať iba malé písmená\",\n\t\"components.Input.error.validation.max\": \"Táto hodnota je príliš vysoká {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"Táto hodnota je príliš dlhá {max}.\",\n\t\"components.Input.error.validation.min\": \"Táto hodnota je príliš nízka {min}.\",\n\t\"components.Input.error.validation.minLength\": \"Táto hodnota je príliš krátka {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"Nemôže byť nadriadený\",\n\t\"components.Input.error.validation.regex\": \"Táto hodnota nespĺňa požadovaný vzor (regex).\",\n\t\"components.Input.error.validation.required\": \"Táto hodnota je povinná.\",\n\t\"components.Input.error.validation.unique\": \"Táto hodnota sa už používa.\",\n\t\"components.InputSelect.option.placeholder\": \"Vyberte\",\n\t\"components.ListRow.empty\": \"Žiadne dáta na zobrazenie.\",\n\t\"components.NotAllowedInput.text\": \"Nedostatočé oprávenia na zobrazenie tohto poľa\",\n\t\"components.OverlayBlocker.description\": \"Používate funkciu, ktorá vyžaduje reštart servera. Počkajte prosím, kým bude server pripravený.\",\n\t\"components.OverlayBlocker.description.serverError\": \"Server by sa mal teraz reštartovať. Skontrolujte logy v terminály.\",\n\t\"components.OverlayBlocker.title\": \"Čaká sa na reštart...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Reštart servera trvá dlhšie ako sa očakávalo\",\n\t\"components.PageFooter.select\": \"výsledkov na stránku\",\n\t\"components.ProductionBlocker.description\": \"Z bezpečnostných dôvodov je tento plugin zablokovaný v iných prostrediach.\",\n\t\"components.ProductionBlocker.header\": \"Tento plugin je dostupný iba v development prostredí.\",\n\t\"components.Search.placeholder\": \"Hľadať...\",\n\t\"components.TableHeader.sort\": \"Zoradiť podľa {label}\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown mód\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"Mód náhľadu\",\n\t\"components.Wysiwyg.collapse\": \"Zbaliť\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"Nadpis H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"Nadpis H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"Nadpis H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"Nadpis H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"Nadpis H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"Nadpis H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Pridať názov\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"znaky\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Rozbaliť\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Pretiahnite súbory, vložte zo schránky alebo {browse}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"vyberte\",\n\t\"components.pagination.go-to\": \"Ísť na stranu {page}\",\n\t\"components.pagination.go-to-next\": \"Ísť na nasledujúcu stranu\",\n\t\"components.pagination.go-to-previous\": \"Ísť na predošlú stranu\",\n\t\"components.pagination.remaining-links\": \"Pridať {number} ďalších odkazov\",\n\t\"components.popUpWarning.button.cancel\": \"Nie, zrušiť\",\n\t\"components.popUpWarning.button.confirm\": \"Áno, potvrdiť\",\n\t\"components.popUpWarning.message\": \"Ste si istý, že to chcete odstrániť?\",\n\t\"components.popUpWarning.title\": \"Prosím potvrďte\",\n\t\"form.button.continue\": \"Pokračovať\",\n\t\"form.button.done\": \"Hotovo\",\n\t\"global.search\": \"Hľadať\",\n\t\"global.actions\": \"Akcie\",\n\t\"global.back\": \"Späť\",\n\t\"global.cancel\": \"Zrušiť\",\n\t\"global.change-password\": \"Zmeniť heslo\",\n\t\"global.content-manager\": \"Správca obsahu\",\n\t\"global.continue\": \"Pokračovať\",\n\t\"global.delete\": \"Vymazať\",\n\t\"global.delete-target\": \"Vymazať {target}\",\n\t\"global.description\": \"Popis\",\n\t\"global.details\": \"Detaily\",\n\t\"global.disabled\": \"Disabled\",\n\t\"global.documentation\": \"Dokumentácia\",\n\t\"global.enabled\": \"Enabled\",\n\t\"global.finish\": \"Dokončiť\",\n\t\"global.marketplace\": \"Marketplace\",\n\t\"global.name\": \"Meno\",\n\t\"global.none\": \"Žiadne\",\n\t\"global.password\": \"Heslo\",\n\t\"global.plugins\": \"Pluginy\",\n\t\"global.profile\": \"Profil\",\n\t\"global.prompt.unsaved\": \"Ak opustíte túto stránku všetky zmeny budú zahodené. Chcete pokračovať?\",\n\t\"global.reset-password\": \"Obnoviť heslo\",\n\t\"global.roles\": \"Roly\",\n\t\"global.save\": \"Uložiť\",\n\t\"global.see-more\": \"Zobraziť viac\",\n\t\"global.select\": \"Vybrať\",\n\t\"global.select-all-entries\": \"Vybrať všetky záznamy\",\n\t\"global.settings\": \"Nastavenia\",\n\t\"global.type\": \"Typ\",\n\t\"global.users\": \"Používatelia\",\n\t\"notification.contentType.relations.conflict\": \"Niektoré prepojenia v obsahovom type sú konfliktné\",\n\t\"notification.default.title\": \"Informácie:\",\n\t\"notification.error\": \"Nastala chyba\",\n\t\"notification.error.layout\": \"Nepodarilo sa načítať rozloženie\",\n\t\"notification.form.error.fields\": \"Formulár obsahuje chyby\",\n\t\"notification.form.success.fields\": \"Zmeny boli uložené\",\n\t\"notification.link-copied\": \"Odkaz bol skopírovaný\",\n\t\"notification.permission.not-allowed-read\": \"Nemáte povolený prístup k tomuto dokumentu\",\n\t\"notification.success.delete\": \"Položka bola odstránená\",\n\t\"notification.success.saved\": \"Uložené\",\n\t\"notification.success.title\": \"Podarilo sa:\",\n\t\"notification.success.apitokencreated\": \"API Token úspešne vytvorený\",\n\t\"notification.success.apitokenedited\": \"API Token úspešne upravený\",\n\t\"notification.error.tokennamenotunique\": \"Názov už je priradený inému tokenu\",\n\t\"notification.version.update.message\": \"Nová verzia Strapi je dostupná!\",\n\t\"notification.warning.title\": \"Upozornenie:\",\n\t\"notification.warning.404\": \"404 - nenájdené\",\n\tor: or,\n\t\"request.error.model.unknown\": \"Tento model neexistuje\",\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, sk as default, light, or, skipToContent, submit };\n//# sourceMappingURL=sk-DQ2qpsG2.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACD;", "names": []}