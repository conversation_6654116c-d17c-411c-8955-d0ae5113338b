{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/_chunks/ar-BYDB75EB.mjs"], "sourcesContent": ["const from = \"من\";\nconst ar = {\n  \"attribute.boolean\": \"منطقي\",\n  \"attribute.date\": \"تاريخ\",\n  \"attribute.email\": \"بريد الإكتروني\",\n  \"attribute.enumeration\": \"تعداد\",\n  \"attribute.json\": \"JSON\",\n  \"attribute.media\": \"وسائط\",\n  \"attribute.password\": \"كلمة سر\",\n  \"attribute.relation\": \"علاقة\",\n  \"attribute.text\": \"نص\",\n  \"form.attribute.item.customColumnName\": \"أسماء الأعمدة المخصصة\",\n  \"form.attribute.item.customColumnName.description\": \"يفيد ذلك في إعادة تسمية أسماء أعمدة قاعدة البيانات بتنسيق أكثر شمولاً لاستجابات واجهة برمجة التطبيقات ( API )\",\n  \"form.attribute.item.defineRelation.fieldName\": \"اسم الحقل\",\n  \"form.attribute.item.enumeration.graphql\": \"تجاوز الاسم لـ GraphQL\",\n  \"form.attribute.item.enumeration.graphql.description\": \"يسمح لك بتجاوز الاسم الذي تم إنشاؤه افتراضيًا لـ GraphQL\",\n  \"form.attribute.item.enumeration.rules\": \"Values (one line per value)\",\n  \"form.attribute.item.maximum\": \"اقصى قيمة\",\n  \"form.attribute.item.maximumLength\": \"أقصى طول\",\n  \"form.attribute.item.minimum\": \"أدنى قيمة\",\n  \"form.attribute.item.minimumLength\": \"أدنى طول\",\n  \"form.attribute.item.number.type\": \"تنسيق الرقم\",\n  \"form.attribute.item.number.type.decimal\": \"عدد عشري (مثال: 2.22)\",\n  \"form.attribute.item.number.type.float\": \"عدد عائم (مثال: 3.33333333)\",\n  \"form.attribute.item.number.type.integer\": \"عدد صحيح (مثال: 10)\",\n  \"form.attribute.item.requiredField\": \"الحقل مطلوب\",\n  \"form.attribute.item.requiredField.description\": \"لن تتمكن من إنشاء إدخال إذا كان هذا الحقل فارغًا\",\n  \"form.attribute.item.uniqueField\": \"حقل فريد\",\n  \"form.attribute.item.uniqueField.description\": \"لن تتمكن من إنشاء إدخال إذا كان هناك إدخال حالي بمحتوى متطابق\",\n  \"form.attribute.settings.default\": \"القيمة الأفتراضية\",\n  \"form.button.cancel\": \"الغاء\",\n  from,\n  \"modelPage.attribute.relationWith\": \"علاقة مع\",\n  \"plugin.description.long\": \"قم بتجميع هيكل البيانات الخاص بـ API الخاص بك. إنشاء حقول وعلاقات جديدة في دقيقة واحدة فقط. يتم إنشاء الملفات وتحديثها تلقائيًا في مشروعك.\",\n  \"plugin.description.short\": \"قم بتجميع هيكل البيانات الخاص بـ API الخاص بك.\",\n  \"popUpForm.navContainer.advanced\": \"إعدادات متقدمة\",\n  \"popUpForm.navContainer.base\": \"إعدادات القاعدة\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"هل أنت متأكد من أنك تريد حذف نوع المحتوى هذا؟\",\n  \"relation.attributeName.placeholder\": \"مثال: المؤلف, الفئة, الوسم\",\n  \"relation.manyToMany\": \"يملك وينتم للكثير\",\n  \"relation.manyToOne\": \"يملك الكثير\",\n  \"relation.oneToMany\": \"ينتمي للكثير\",\n  \"relation.oneToOne\": \"يتشارك بواحد\",\n  \"relation.oneWay\": \"يمتلك واحد\",\n  \"modalForm.header.back\": \"خلف\"\n};\nexport {\n  ar as default,\n  from\n};\n//# sourceMappingURL=ar-BYDB75EB.mjs.map\n"], "mappings": ";;;AAAA,IAAM,OAAO;AACb,IAAM,KAAK;AAAA,EACT,qBAAqB;AAAA,EACrB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,mCAAmC;AAAA,EACnC,sBAAsB;AAAA,EACtB;AAAA,EACA,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,yBAAyB;AAC3B;", "names": []}