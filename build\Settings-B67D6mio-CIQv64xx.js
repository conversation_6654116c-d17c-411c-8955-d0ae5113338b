import{hV as _,r as n,hW as w,m as e,n as v,a as D,b as k,f as O,g as B,i as V,l as G,k as F,L as M,w as x,J as Y,s as S,S as $,G as m,x as r,z as T,a9 as q,aa as H,A as W,bm as X,hY as Z,hZ as z}from"./strapi-YzJfjJ2z.js";function J(t,a,i){if(i===void 0&&(i=Error),!t)throw new i(a)}var Q=function(t){console.error(t)},U=function(t){console.warn(t)},K={formats:{},messages:{},timeZone:void 0,defaultLocale:"en",defaultFormats:{},fallbackOnEmptyString:!0,onError:Q,onWarn:U};function ee(t){J(t,"[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.")}_(_({},K),{textComponent:n.Fragment});var C=typeof window<"u"&&!window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__?window.__REACT_INTL_CONTEXT__||(window.__REACT_INTL_CONTEXT__=n.createContext(null)):n.createContext(null);C.Consumer;C.Provider;var te=C;function y(){var t=n.useContext(te);return ee(t),t}var b;(function(t){t.formatDate="FormattedDate",t.formatTime="FormattedTime",t.formatNumber="FormattedNumber",t.formatList="FormattedList",t.formatDisplayName="FormattedDisplayName"})(b||(b={}));var I;(function(t){t.formatDate="FormattedDateParts",t.formatTime="FormattedTimeParts",t.formatNumber="FormattedNumberParts",t.formatList="FormattedListParts"})(I||(I={}));function E(t){var a=function(i){var o=y(),s=i.value,u=i.children,c=w(i,["value","children"]),l=typeof s=="string"?new Date(s||0):s,g=t==="formatDate"?o.formatDateToParts(l,c):o.formatTimeToParts(l,c);return u(g)};return a.displayName=I[t],a}function h(t){var a=function(i){var o=y(),s=i.value,u=i.children,c=w(i,["value","children"]),l=o[t](s,c);if(typeof u=="function")return u(l);var g=o.textComponent||n.Fragment;return n.createElement(g,null,l)};return a.displayName=b[t],a}h("formatDate");h("formatTime");h("formatNumber");h("formatList");h("formatDisplayName");E("formatDate");E("formatTime");const ae=(t,a)=>!t||!a?{}:{[t]:a[t]},ie=t=>(t?.inner||[]).reduce((a,i)=>(i.path&&(a[i.path.split("[").join(".").split("]").join("")]={id:i.message,defaultMessage:i.message,values:ae(i?.type,i?.params)}),a),{}),R=V().shape({email:G().email(F.email.id).required(F.required.id)}),se=$.a`
  color: ${({theme:t})=>t.colors.primary600};
`,re=()=>e.jsx(v.Protect,{permissions:z.settings,children:e.jsx(ne,{})}),ne=()=>{const{toggleNotification:t}=D(),{formatMessage:a}=y(),{get:i,post:o}=k(),[s,u]=n.useState(""),[c,l]=n.useState(!1),[g,P]=n.useState({}),{data:f,isLoading:L}=O(["email","settings"],async()=>{const d=await i("/email/settings"),{data:{config:p}}=d;return p}),j=B(async d=>{await o("/email/test",d)},{onError(){t({type:"danger",message:a({id:"email.Settings.email.plugin.notification.test.error",defaultMessage:"Failed to send a test mail to {to}"},{to:s})})},onSuccess(){t({type:"success",message:a({id:"email.Settings.email.plugin.notification.test.success",defaultMessage:"Email test succeeded, check the {to} mailbox"},{to:s})})},retry:!1});n.useEffect(()=>{R.validate({email:s},{abortEarly:!1}).then(()=>l(!0)).catch(()=>l(!1))},[s]);const N=d=>{u(()=>d.target.value)},A=async d=>{d.preventDefault();try{await R.validate({email:s},{abortEarly:!1})}catch(p){p instanceof Z&&P(ie(p))}j.mutate({to:s})};return L?e.jsx(v.Loading,{}):e.jsxs(v.Main,{labelledBy:"title","aria-busy":L||j.isLoading,children:[e.jsx(v.Title,{children:a({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:a({id:"email.Settings.email.plugin.title",defaultMessage:"Configuration"})})}),e.jsx(M.Header,{id:"title",title:a({id:"email.Settings.email.plugin.title",defaultMessage:"Configuration"}),subtitle:a({id:"email.Settings.email.plugin.subTitle",defaultMessage:"Test the settings for the Email plugin"})}),e.jsx(M.Content,{children:f&&e.jsx("form",{onSubmit:A,children:e.jsxs(x,{direction:"column",alignItems:"stretch",gap:7,children:[e.jsx(Y,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(x,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsxs(x,{direction:"column",alignItems:"stretch",gap:1,children:[e.jsx(S,{variant:"delta",tag:"h2",children:a({id:"email.Settings.email.plugin.title.config",defaultMessage:"Configuration"})}),e.jsx(S,{children:a({id:"email.Settings.email.plugin.text.configuration",defaultMessage:"The plugin is configured through the {file} file, checkout this {link} for the documentation."},{file:"./config/plugins.js",link:e.jsx(se,{href:"https://docs.strapi.io/developer-docs/latest/plugins/email.html",target:"_blank",rel:"noopener noreferrer",children:a({id:"email.link",defaultMessage:"Link"})})})})]}),e.jsxs(m.Root,{gap:5,children:[e.jsx(m.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(r.Root,{name:"shipper-email",children:[e.jsx(r.Label,{children:a({id:"email.Settings.email.plugin.label.defaultFrom",defaultMessage:"Default sender email"})}),e.jsx(T,{placeholder:a({id:"email.Settings.email.plugin.placeholder.defaultFrom",defaultMessage:"ex: Strapi No-Reply '<'<EMAIL>'>'"}),disabled:!0,value:f.settings.defaultFrom})]})}),e.jsx(m.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(r.Root,{name:"response-email",children:[e.jsx(r.Label,{children:a({id:"email.Settings.email.plugin.label.defaultReplyTo",defaultMessage:"Default response email"})}),e.jsx(T,{placeholder:a({id:"email.Settings.email.plugin.placeholder.defaultReplyTo",defaultMessage:"ex: Strapi '<'<EMAIL>'>'"}),disabled:!0,value:f.settings.defaultReplyTo})]})}),e.jsx(m.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(r.Root,{name:"email-provider",children:[e.jsx(r.Label,{children:a({id:"email.Settings.email.plugin.label.provider",defaultMessage:"Email provider"})}),e.jsx(q,{disabled:!0,value:f.provider,children:e.jsx(H,{value:f.provider,children:f.provider})})]})})]})]})}),e.jsxs(x,{alignItems:"stretch",background:"neutral0",direction:"column",gap:4,hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:[e.jsx(S,{variant:"delta",tag:"h2",children:a({id:"email.Settings.email.plugin.title.test",defaultMessage:"Test email delivery"})}),e.jsxs(m.Root,{gap:5,children:[e.jsx(m.Item,{col:6,s:12,direction:"column",alignItems:"stretch",children:e.jsxs(r.Root,{name:"test-address",error:g.email?.id&&a({id:`email.${g.email?.id}`,defaultMessage:"This is not a valid email"}),children:[e.jsx(r.Label,{children:a({id:"email.Settings.email.plugin.label.testAddress",defaultMessage:"Recipient email"})}),e.jsx(T,{onChange:N,value:s,placeholder:a({id:"email.Settings.email.plugin.placeholder.testAddress",defaultMessage:"ex: <EMAIL>"})})]})}),e.jsx(m.Item,{col:7,s:12,direction:"column",alignItems:"start",children:e.jsx(W,{loading:j.isLoading,disabled:!c,type:"submit",startIcon:e.jsx(X,{}),children:a({id:"email.Settings.email.plugin.button.test-email",defaultMessage:"Send test email"})})})]})]})]})})})]})};export{re as ProtectedSettingsPage};
