import{al as h,au as c,aQ as u,m as s,aV as x,aW as g,aX as p,aY as j,aZ as f,a_ as m,J as a,s as t,w as n,aI as v,S as w,aS as S,A as L,Y as C,_ as y}from"./strapi-YzJfjJ2z.js";import{S as k}from"./SSOProviders-BD7LHrkI-G0ZYqapf.js";const A=()=>{const d=h(),{formatMessage:i}=c(),{isLoading:e,data:r=[]}=u(void 0,{skip:!window.strapi.features.isEnabled(window.strapi.features.SSO)}),l=()=>{d("/auth/login")};return!window.strapi.features.isEnabled(window.strapi.features.SSO)||!e&&r.length===0?s.jsx(x,{to:"/auth/login"}):s.jsx(g,{children:s.jsxs(p,{children:[s.jsxs(j,{children:[s.jsxs(f,{children:[s.jsx(m,{}),s.jsx(a,{paddingTop:6,paddingBottom:1,children:s.jsx(t,{tag:"h1",variant:"alpha",children:i({id:"Auth.form.welcome.title"})})}),s.jsx(a,{paddingBottom:7,children:s.jsx(t,{variant:"epsilon",textColor:"neutral600",children:i({id:"Auth.login.sso.subtitle"})})})]}),s.jsxs(n,{direction:"column",alignItems:"stretch",gap:7,children:[e?s.jsx(n,{justifyContent:"center",children:s.jsx(v,{children:i({id:"Auth.login.sso.loading"})})}):s.jsx(k,{providers:r}),s.jsxs(n,{children:[s.jsx(o,{}),s.jsx(a,{paddingLeft:3,paddingRight:3,children:s.jsx(t,{variant:"sigma",textColor:"neutral600",children:i({id:"or"})})}),s.jsx(o,{})]}),s.jsx(L,{fullWidth:!0,size:"L",onClick:l,children:i({id:"Auth.form.button.login.strapi"})})]})]}),s.jsx(n,{justifyContent:"center",children:s.jsx(a,{paddingTop:4,children:s.jsx(C,{tag:y,to:"/auth/forgot-password",children:s.jsx(t,{variant:"pi",children:i({id:"Auth.link.forgot-password"})})})})})]})})},o=w(S)`
  flex: 1;
`,O={providers:A};export{O as FORMS};
