import{jf as g,jg as qe,r as T,m as o,jh as Ve,bO as Oe,au as C,bV as P,ci as q,ji as Ye,ba as Z,c9 as xe,ax as Ke,dS as Ze,da as Qe,ej as Je,h2 as et,n as Ee,jj as tt,jk as nt,a$ as rt,g7 as st,J as E,jl as it,w as m,a5 as ot,d as at,S as y,V as Q,aS as Ie,ch as ct,aT as ut,_ as Ce,jm as lt,e$ as dt,al as ft,e_ as M,f0 as gt,e4 as ht,cb as pt,gr as mt,cC as vt,cD as bt,I as St,bE as we,s as x,bK as yt,bW as ae,A as J,b$ as Dt,aj as H,dl as Tt,bD as Ot,ev as xt,du as Et,dD as It}from"./strapi-YzJfjJ2z.js";import{e as ce,f as Ct}from"./lt-Cs1sdUvq.js";import{L as wt,V as W,S as j,N as ue}from"./Ornaments-BFThxr2U-y-JuiLXt.js";import{h as jt,g as Lt}from"./users-8N93LH7R-MOwOr-tf.js";import{P as Nt}from"./PrivateRoute-gjOsMUaF-a4KqNTSG.js";import{u as kt}from"./useOnce-NHeEacbN-BN84kb-5.js";function Pt(t,e,n){return e.split(".").reduce((s,r)=>s&&s[r]?s[r]:n||null,t)}function Rt(t,e){return t.filter(n=>n!==e)}function je(t){return typeof t=="object"}function At(t,e){const n=new Map,s=i=>{n.set(i,n.has(i)?n.get(i)+1:1)};t.forEach(s),e.forEach(s);const r=[];return n.forEach((i,a)=>{i===1&&r.push(a)}),r}function Mt(t,e){return t.filter(n=>e.indexOf(n)>-1)}const ee="dnd-core/INIT_COORDS",X="dnd-core/BEGIN_DRAG",te="dnd-core/PUBLISH_DRAG_SOURCE",_="dnd-core/HOVER",U="dnd-core/DROP",F="dnd-core/END_DRAG";function le(t,e){return{type:ee,payload:{sourceClientOffset:e||null,clientOffset:t||null}}}const Xt={type:ee,payload:{clientOffset:null,sourceClientOffset:null}};function _t(t){return function(n=[],s={publishSource:!0}){const{publishSource:r=!0,clientOffset:i,getSourceClientOffset:a}=s,c=t.getMonitor(),d=t.getRegistry();t.dispatch(le(i)),Ut(n,c,d);const l=Ht(n,c);if(l==null){t.dispatch(Xt);return}let f=null;if(i){if(!a)throw new Error("getSourceClientOffset must be defined");Ft(a),f=a(l)}t.dispatch(le(i,f));const p=d.getSource(l).beginDrag(c,l);if(p==null)return;Bt(p),d.pinSource(l);const u=d.getSourceType(l);return{type:X,payload:{itemType:u,item:p,sourceId:l,clientOffset:i||null,sourceClientOffset:f||null,isSourcePublic:!!r}}}}function Ut(t,e,n){g(!e.isDragging(),"Cannot call beginDrag while dragging."),t.forEach(function(s){g(n.getSource(s),"Expected sourceIds to be registered.")})}function Ft(t){g(typeof t=="function","When clientOffset is provided, getSourceClientOffset must be a function.")}function Bt(t){g(je(t),"Item must be an object.")}function Ht(t,e){let n=null;for(let s=t.length-1;s>=0;s--)if(e.canDragSource(t[s])){n=t[s];break}return n}function Wt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Gt(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){Wt(t,r,n[r])})}return t}function zt(t){return function(n={}){const s=t.getMonitor(),r=t.getRegistry();$t(s),Yt(s).forEach((a,c)=>{const d=qt(a,c,r,s),l={type:U,payload:{dropResult:Gt({},n,d)}};t.dispatch(l)})}}function $t(t){g(t.isDragging(),"Cannot call drop while not dragging."),g(!t.didDrop(),"Cannot call drop twice during one drag operation.")}function qt(t,e,n,s){const r=n.getTarget(t);let i=r?r.drop(s,t):void 0;return Vt(i),typeof i>"u"&&(i=e===0?{}:s.getDropResult()),i}function Vt(t){g(typeof t>"u"||je(t),"Drop result must either be an object or undefined.")}function Yt(t){const e=t.getTargetIds().filter(t.canDropOnTarget,t);return e.reverse(),e}function Kt(t){return function(){const n=t.getMonitor(),s=t.getRegistry();Zt(n);const r=n.getSourceId();return r!=null&&(s.getSource(r,!0).endDrag(n,r),s.unpinSource()),{type:F}}}function Zt(t){g(t.isDragging(),"Cannot call endDrag while not dragging.")}function V(t,e){return e===null?t===null:Array.isArray(t)?t.some(n=>n===e):t===e}function Qt(t){return function(n,{clientOffset:s}={}){Jt(n);const r=n.slice(0),i=t.getMonitor(),a=t.getRegistry(),c=i.getItemType();return tn(r,a,c),en(r,i,a),nn(r,i,a),{type:_,payload:{targetIds:r,clientOffset:s||null}}}}function Jt(t){g(Array.isArray(t),"Expected targetIds to be an array.")}function en(t,e,n){g(e.isDragging(),"Cannot call hover while not dragging."),g(!e.didDrop(),"Cannot call hover after drop.");for(let s=0;s<t.length;s++){const r=t[s];g(t.lastIndexOf(r)===s,"Expected targetIds to be unique in the passed array.");const i=n.getTarget(r);g(i,"Expected targetIds to be registered.")}}function tn(t,e,n){for(let s=t.length-1;s>=0;s--){const r=t[s],i=e.getTargetType(r);V(i,n)||t.splice(s,1)}}function nn(t,e,n){t.forEach(function(s){n.getTarget(s).hover(e,s)})}function rn(t){return function(){if(t.getMonitor().isDragging())return{type:te}}}function sn(t){return{beginDrag:_t(t),publishDragSource:rn(t),hover:Qt(t),drop:zt(t),endDrag:Kt(t)}}class on{receiveBackend(e){this.backend=e}getMonitor(){return this.monitor}getBackend(){return this.backend}getRegistry(){return this.monitor.registry}getActions(){const e=this,{dispatch:n}=this.store;function s(i){return(...a)=>{const c=i.apply(e,a);typeof c<"u"&&n(c)}}const r=sn(this);return Object.keys(r).reduce((i,a)=>{const c=r[a];return i[a]=s(c),i},{})}dispatch(e){this.store.dispatch(e)}constructor(e,n){this.isSetUp=!1,this.handleRefCountChange=()=>{const s=this.store.getState().refCount>0;this.backend&&(s&&!this.isSetUp?(this.backend.setup(),this.isSetUp=!0):!s&&this.isSetUp&&(this.backend.teardown(),this.isSetUp=!1))},this.store=e,this.monitor=n,e.subscribe(this.handleRefCountChange)}}function an(t,e){return{x:t.x+e.x,y:t.y+e.y}}function Le(t,e){return{x:t.x-e.x,y:t.y-e.y}}function cn(t){const{clientOffset:e,initialClientOffset:n,initialSourceClientOffset:s}=t;return!e||!n||!s?null:Le(an(e,s),n)}function un(t){const{clientOffset:e,initialClientOffset:n}=t;return!e||!n?null:Le(e,n)}const N=[],ne=[];N.__IS_NONE__=!0;ne.__IS_ALL__=!0;function ln(t,e){return t===N?!1:t===ne||typeof e>"u"?!0:Mt(e,t).length>0}class dn{subscribeToStateChange(e,n={}){const{handlerIds:s}=n;g(typeof e=="function","listener must be a function."),g(typeof s>"u"||Array.isArray(s),"handlerIds, when specified, must be an array of strings.");let r=this.store.getState().stateId;const i=()=>{const a=this.store.getState(),c=a.stateId;try{c===r||c===r+1&&!ln(a.dirtyHandlerIds,s)||e()}finally{r=c}};return this.store.subscribe(i)}subscribeToOffsetChange(e){g(typeof e=="function","listener must be a function.");let n=this.store.getState().dragOffset;const s=()=>{const r=this.store.getState().dragOffset;r!==n&&(n=r,e())};return this.store.subscribe(s)}canDragSource(e){if(!e)return!1;const n=this.registry.getSource(e);return g(n,`Expected to find a valid source. sourceId=${e}`),this.isDragging()?!1:n.canDrag(this,e)}canDropOnTarget(e){if(!e)return!1;const n=this.registry.getTarget(e);if(g(n,`Expected to find a valid target. targetId=${e}`),!this.isDragging()||this.didDrop())return!1;const s=this.registry.getTargetType(e),r=this.getItemType();return V(s,r)&&n.canDrop(this,e)}isDragging(){return!!this.getItemType()}isDraggingSource(e){if(!e)return!1;const n=this.registry.getSource(e,!0);if(g(n,`Expected to find a valid source. sourceId=${e}`),!this.isDragging()||!this.isSourcePublic())return!1;const s=this.registry.getSourceType(e),r=this.getItemType();return s!==r?!1:n.isDragging(this,e)}isOverTarget(e,n={shallow:!1}){if(!e)return!1;const{shallow:s}=n;if(!this.isDragging())return!1;const r=this.registry.getTargetType(e),i=this.getItemType();if(i&&!V(r,i))return!1;const a=this.getTargetIds();if(!a.length)return!1;const c=a.indexOf(e);return s?c===a.length-1:c>-1}getItemType(){return this.store.getState().dragOperation.itemType}getItem(){return this.store.getState().dragOperation.item}getSourceId(){return this.store.getState().dragOperation.sourceId}getTargetIds(){return this.store.getState().dragOperation.targetIds}getDropResult(){return this.store.getState().dragOperation.dropResult}didDrop(){return this.store.getState().dragOperation.didDrop}isSourcePublic(){return!!this.store.getState().dragOperation.isSourcePublic}getInitialClientOffset(){return this.store.getState().dragOffset.initialClientOffset}getInitialSourceClientOffset(){return this.store.getState().dragOffset.initialSourceClientOffset}getClientOffset(){return this.store.getState().dragOffset.clientOffset}getSourceClientOffset(){return cn(this.store.getState().dragOffset)}getDifferenceFromInitialOffset(){return un(this.store.getState().dragOffset)}constructor(e,n){this.store=e,this.registry=n}}const de=typeof global<"u"?global:self,Ne=de.MutationObserver||de.WebKitMutationObserver;function ke(t){return function(){const n=setTimeout(r,0),s=setInterval(r,50);function r(){clearTimeout(n),clearInterval(s),t()}}}function fn(t){let e=1;const n=new Ne(t),s=document.createTextNode("");return n.observe(s,{characterData:!0}),function(){e=-e,s.data=e}}const gn=typeof Ne=="function"?fn:ke;class hn{enqueueTask(e){const{queue:n,requestFlush:s}=this;n.length||(s(),this.flushing=!0),n[n.length]=e}constructor(){this.queue=[],this.pendingErrors=[],this.flushing=!1,this.index=0,this.capacity=1024,this.flush=()=>{const{queue:e}=this;for(;this.index<e.length;){const n=this.index;if(this.index++,e[n].call(),this.index>this.capacity){for(let s=0,r=e.length-this.index;s<r;s++)e[s]=e[s+this.index];e.length-=this.index,this.index=0}}e.length=0,this.index=0,this.flushing=!1},this.registerPendingError=e=>{this.pendingErrors.push(e),this.requestErrorThrow()},this.requestFlush=gn(this.flush),this.requestErrorThrow=ke(()=>{if(this.pendingErrors.length)throw this.pendingErrors.shift()})}}class pn{call(){try{this.task&&this.task()}catch(e){this.onError(e)}finally{this.task=null,this.release(this)}}constructor(e,n){this.onError=e,this.release=n,this.task=null}}class mn{create(e){const n=this.freeTasks,s=n.length?n.pop():new pn(this.onError,r=>n[n.length]=r);return s.task=e,s}constructor(e){this.onError=e,this.freeTasks=[]}}const Pe=new hn,vn=new mn(Pe.registerPendingError);function bn(t){Pe.enqueueTask(vn.create(t))}const re="dnd-core/ADD_SOURCE",se="dnd-core/ADD_TARGET",ie="dnd-core/REMOVE_SOURCE",B="dnd-core/REMOVE_TARGET";function Sn(t){return{type:re,payload:{sourceId:t}}}function yn(t){return{type:se,payload:{targetId:t}}}function Dn(t){return{type:ie,payload:{sourceId:t}}}function Tn(t){return{type:B,payload:{targetId:t}}}function On(t){g(typeof t.canDrag=="function","Expected canDrag to be a function."),g(typeof t.beginDrag=="function","Expected beginDrag to be a function."),g(typeof t.endDrag=="function","Expected endDrag to be a function.")}function xn(t){g(typeof t.canDrop=="function","Expected canDrop to be a function."),g(typeof t.hover=="function","Expected hover to be a function."),g(typeof t.drop=="function","Expected beginDrag to be a function.")}function Y(t,e){if(e&&Array.isArray(t)){t.forEach(n=>Y(n,!1));return}g(typeof t=="string"||typeof t=="symbol",e?"Type can only be a string, a symbol, or an array of either.":"Type can only be a string or a symbol.")}var O;(function(t){t.SOURCE="SOURCE",t.TARGET="TARGET"})(O||(O={}));let En=0;function In(){return En++}function Cn(t){const e=In().toString();switch(t){case O.SOURCE:return`S${e}`;case O.TARGET:return`T${e}`;default:throw new Error(`Unknown Handler Role: ${t}`)}}function fe(t){switch(t[0]){case"S":return O.SOURCE;case"T":return O.TARGET;default:throw new Error(`Cannot parse handler ID: ${t}`)}}function ge(t,e){const n=t.entries();let s=!1;do{const{done:r,value:[,i]}=n.next();if(i===e)return!0;s=!!r}while(!s);return!1}class wn{addSource(e,n){Y(e),On(n);const s=this.addHandler(O.SOURCE,e,n);return this.store.dispatch(Sn(s)),s}addTarget(e,n){Y(e,!0),xn(n);const s=this.addHandler(O.TARGET,e,n);return this.store.dispatch(yn(s)),s}containsHandler(e){return ge(this.dragSources,e)||ge(this.dropTargets,e)}getSource(e,n=!1){return g(this.isSourceId(e),"Expected a valid source ID."),n&&e===this.pinnedSourceId?this.pinnedSource:this.dragSources.get(e)}getTarget(e){return g(this.isTargetId(e),"Expected a valid target ID."),this.dropTargets.get(e)}getSourceType(e){return g(this.isSourceId(e),"Expected a valid source ID."),this.types.get(e)}getTargetType(e){return g(this.isTargetId(e),"Expected a valid target ID."),this.types.get(e)}isSourceId(e){return fe(e)===O.SOURCE}isTargetId(e){return fe(e)===O.TARGET}removeSource(e){g(this.getSource(e),"Expected an existing source."),this.store.dispatch(Dn(e)),bn(()=>{this.dragSources.delete(e),this.types.delete(e)})}removeTarget(e){g(this.getTarget(e),"Expected an existing target."),this.store.dispatch(Tn(e)),this.dropTargets.delete(e),this.types.delete(e)}pinSource(e){const n=this.getSource(e);g(n,"Expected an existing source."),this.pinnedSourceId=e,this.pinnedSource=n}unpinSource(){g(this.pinnedSource,"No source is pinned at the time."),this.pinnedSourceId=null,this.pinnedSource=null}addHandler(e,n,s){const r=Cn(e);return this.types.set(r,n),e===O.SOURCE?this.dragSources.set(r,s):e===O.TARGET&&this.dropTargets.set(r,s),r}constructor(e){this.types=new Map,this.dragSources=new Map,this.dropTargets=new Map,this.pinnedSourceId=null,this.pinnedSource=null,this.store=e}}const jn=(t,e)=>t===e;function Ln(t,e){return!t&&!e?!0:!t||!e?!1:t.x===e.x&&t.y===e.y}function Nn(t,e,n=jn){if(t.length!==e.length)return!1;for(let s=0;s<t.length;++s)if(!n(t[s],e[s]))return!1;return!0}function kn(t=N,e){switch(e.type){case _:break;case re:case se:case B:case ie:return N;case X:case te:case F:case U:default:return ne}const{targetIds:n=[],prevTargetIds:s=[]}=e.payload,r=At(n,s);if(!(r.length>0||!Nn(n,s)))return N;const a=s[s.length-1],c=n[n.length-1];return a!==c&&(a&&r.push(a),c&&r.push(c)),r}function Pn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Rn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){Pn(t,r,n[r])})}return t}const he={initialSourceClientOffset:null,initialClientOffset:null,clientOffset:null};function An(t=he,e){const{payload:n}=e;switch(e.type){case ee:case X:return{initialSourceClientOffset:n.sourceClientOffset,initialClientOffset:n.clientOffset,clientOffset:n.clientOffset};case _:return Ln(t.clientOffset,n.clientOffset)?t:Rn({},t,{clientOffset:n.clientOffset});case F:case U:return he;default:return t}}function Mn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function L(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){Mn(t,r,n[r])})}return t}const Xn={itemType:null,item:null,sourceId:null,targetIds:[],dropResult:null,didDrop:!1,isSourcePublic:null};function _n(t=Xn,e){const{payload:n}=e;switch(e.type){case X:return L({},t,{itemType:n.itemType,item:n.item,sourceId:n.sourceId,isSourcePublic:n.isSourcePublic,dropResult:null,didDrop:!1});case te:return L({},t,{isSourcePublic:!0});case _:return L({},t,{targetIds:n.targetIds});case B:return t.targetIds.indexOf(n.targetId)===-1?t:L({},t,{targetIds:Rt(t.targetIds,n.targetId)});case U:return L({},t,{dropResult:n.dropResult,didDrop:!0,targetIds:[]});case F:return L({},t,{itemType:null,item:null,sourceId:null,dropResult:null,didDrop:!1,isSourcePublic:null,targetIds:[]});default:return t}}function Un(t=0,e){switch(e.type){case re:case se:return t+1;case ie:case B:return t-1;default:return t}}function Fn(t=0){return t+1}function Bn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Hn(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){Bn(t,r,n[r])})}return t}function Wn(t={},e){return{dirtyHandlerIds:kn(t.dirtyHandlerIds,{type:e.type,payload:Hn({},e.payload,{prevTargetIds:Pt(t,"dragOperation.targetIds",[])})}),dragOffset:An(t.dragOffset,e),refCount:Un(t.refCount,e),dragOperation:_n(t.dragOperation,e),stateId:Fn(t.stateId)}}function Gn(t,e=void 0,n={},s=!1){const r=zn(s),i=new dn(r,new wn(r)),a=new on(r,i),c=t(a,e,n);return a.receiveBackend(c),a}function zn(t){const e=typeof window<"u"&&window.__REDUX_DEVTOOLS_EXTENSION__;return qe(Wn,t&&e&&e({name:"dnd-core",instanceId:"dnd-core"}))}function $n(t,e){if(t==null)return{};var n=qn(t,e),s,r;if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)s=i[r],!(e.indexOf(s)>=0)&&Object.prototype.propertyIsEnumerable.call(t,s)&&(n[s]=t[s])}return n}function qn(t,e){if(t==null)return{};var n={},s=Object.keys(t),r,i;for(i=0;i<s.length;i++)r=s[i],!(e.indexOf(r)>=0)&&(n[r]=t[r]);return n}let pe=0;const A=Symbol.for("__REACT_DND_CONTEXT_INSTANCE__");var Vn=T.memo(function(e){var{children:n}=e,s=$n(e,["children"]);const[r,i]=Yn(s);return T.useEffect(()=>{if(i){const a=Re();return++pe,()=>{--pe===0&&(a[A]=null)}}},[]),o.jsx(Ve.Provider,{value:r,children:n})});function Yn(t){if("manager"in t)return[{dragDropManager:t.manager},!1];const e=Kn(t.backend,t.context,t.options,t.debugMode),n=!t.context;return[e,n]}function Kn(t,e=Re(),n,s){const r=e;return r[A]||(r[A]={dragDropManager:Gn(t,e,n,s)}),r[A]}function Re(){return typeof global<"u"?global:window}function Ae(t){let e=null;return()=>(e==null&&(e=t()),e)}function Zn(t,e){return t.filter(n=>n!==e)}function Qn(t,e){const n=new Set,s=i=>n.add(i);t.forEach(s),e.forEach(s);const r=[];return n.forEach(i=>r.push(i)),r}class Jn{enter(e){const n=this.entered.length,s=r=>this.isNodeInDocument(r)&&(!r.contains||r.contains(e));return this.entered=Qn(this.entered.filter(s),[e]),n===0&&this.entered.length>0}leave(e){const n=this.entered.length;return this.entered=Zn(this.entered.filter(this.isNodeInDocument),e),n>0&&this.entered.length===0}reset(){this.entered=[]}constructor(e){this.entered=[],this.isNodeInDocument=e}}class er{initializeExposedProperties(){Object.keys(this.config.exposeProperties).forEach(e=>{Object.defineProperty(this.item,e,{configurable:!0,enumerable:!0,get(){return console.warn(`Browser doesn't allow reading "${e}" until the drop event.`),null}})})}loadDataTransfer(e){if(e){const n={};Object.keys(this.config.exposeProperties).forEach(s=>{const r=this.config.exposeProperties[s];r!=null&&(n[s]={value:r(e,this.config.matchesTypes),configurable:!0,enumerable:!0})}),Object.defineProperties(this.item,n)}}canDrag(){return!0}beginDrag(){return this.item}isDragging(e,n){return n===e.getSourceId()}endDrag(){}constructor(e){this.config=e,this.item={},this.initializeExposedProperties()}}const Me="__NATIVE_FILE__",Xe="__NATIVE_URL__",_e="__NATIVE_TEXT__",Ue="__NATIVE_HTML__",me=Object.freeze(Object.defineProperty({__proto__:null,FILE:Me,HTML:Ue,TEXT:_e,URL:Xe},Symbol.toStringTag,{value:"Module"}));function G(t,e,n){const s=e.reduce((r,i)=>r||t.getData(i),"");return s??n}const K={[Me]:{exposeProperties:{files:t=>Array.prototype.slice.call(t.files),items:t=>t.items,dataTransfer:t=>t},matchesTypes:["Files"]},[Ue]:{exposeProperties:{html:(t,e)=>G(t,e,""),dataTransfer:t=>t},matchesTypes:["Html","text/html"]},[Xe]:{exposeProperties:{urls:(t,e)=>G(t,e,"").split(`
`),dataTransfer:t=>t},matchesTypes:["Url","text/uri-list"]},[_e]:{exposeProperties:{text:(t,e)=>G(t,e,""),dataTransfer:t=>t},matchesTypes:["Text","text/plain"]}};function tr(t,e){const n=K[t];if(!n)throw new Error(`native type ${t} has no configuration`);const s=new er(n);return s.loadDataTransfer(e),s}function z(t){if(!t)return null;const e=Array.prototype.slice.call(t.types||[]);return Object.keys(K).filter(n=>{const s=K[n];return s?.matchesTypes?s.matchesTypes.some(r=>e.indexOf(r)>-1):!1})[0]||null}const nr=Ae(()=>/firefox/i.test(navigator.userAgent)),Fe=Ae(()=>!!window.safari);class ve{interpolate(e){const{xs:n,ys:s,c1s:r,c2s:i,c3s:a}=this;let c=n.length-1;if(e===n[c])return s[c];let d=0,l=a.length-1,f;for(;d<=l;){f=Math.floor(.5*(d+l));const u=n[f];if(u<e)d=f+1;else if(u>e)l=f-1;else return s[f]}c=Math.max(0,l);const S=e-n[c],p=S*S;return s[c]+r[c]*S+i[c]*p+a[c]*S*p}constructor(e,n){const{length:s}=e,r=[];for(let u=0;u<s;u++)r.push(u);r.sort((u,v)=>e[u]<e[v]?-1:1);const i=[],a=[];let c,d;for(let u=0;u<s-1;u++)c=e[u+1]-e[u],d=n[u+1]-n[u],i.push(c),a.push(d/c);const l=[a[0]];for(let u=0;u<i.length-1;u++){const v=a[u],D=a[u+1];if(v*D<=0)l.push(0);else{c=i[u];const h=i[u+1],b=c+h;l.push(3*b/((b+h)/v+(b+c)/D))}}l.push(a[a.length-1]);const f=[],S=[];let p;for(let u=0;u<l.length-1;u++){p=a[u];const v=l[u],D=1/i[u],h=v+l[u+1]-p-p;f.push((p-v-h)*D),S.push(h*D*D)}this.xs=e,this.ys=n,this.c1s=l,this.c2s=f,this.c3s=S}}const rr=1;function Be(t){const e=t.nodeType===rr?t:t.parentElement;if(!e)return null;const{top:n,left:s}=e.getBoundingClientRect();return{x:s,y:n}}function R(t){return{x:t.clientX,y:t.clientY}}function sr(t){var e;return t.nodeName==="IMG"&&(nr()||!(!((e=document.documentElement)===null||e===void 0)&&e.contains(t)))}function ir(t,e,n,s){let r=t?e.width:n,i=t?e.height:s;return Fe()&&t&&(i/=window.devicePixelRatio,r/=window.devicePixelRatio),{dragPreviewWidth:r,dragPreviewHeight:i}}function or(t,e,n,s,r){const i=sr(e),c=Be(i?t:e),d={x:n.x-c.x,y:n.y-c.y},{offsetWidth:l,offsetHeight:f}=t,{anchorX:S,anchorY:p}=s,{dragPreviewWidth:u,dragPreviewHeight:v}=ir(i,e,l,f),D=()=>{let oe=new ve([0,.5,1],[d.y,d.y/f*v,d.y+v-f]).interpolate(p);return Fe()&&i&&(oe+=(window.devicePixelRatio-1)*v),oe},h=()=>new ve([0,.5,1],[d.x,d.x/l*u,d.x+u-l]).interpolate(S),{offsetX:b,offsetY:w}=r,I=b===0||b,ze=w===0||w;return{x:I?b:h(),y:ze?w:D()}}class ar{get window(){if(this.globalContext)return this.globalContext;if(typeof window<"u")return window}get document(){var e;return!((e=this.globalContext)===null||e===void 0)&&e.document?this.globalContext.document:this.window?this.window.document:void 0}get rootElement(){var e;return((e=this.optionsArgs)===null||e===void 0?void 0:e.rootElement)||this.window}constructor(e,n){this.ownerDocument=null,this.globalContext=e,this.optionsArgs=n}}function cr(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function be(t){for(var e=1;e<arguments.length;e++){var n=arguments[e]!=null?arguments[e]:{},s=Object.keys(n);typeof Object.getOwnPropertySymbols=="function"&&(s=s.concat(Object.getOwnPropertySymbols(n).filter(function(r){return Object.getOwnPropertyDescriptor(n,r).enumerable}))),s.forEach(function(r){cr(t,r,n[r])})}return t}class ur{profile(){var e,n;return{sourcePreviewNodes:this.sourcePreviewNodes.size,sourcePreviewNodeOptions:this.sourcePreviewNodeOptions.size,sourceNodeOptions:this.sourceNodeOptions.size,sourceNodes:this.sourceNodes.size,dragStartSourceIds:((e=this.dragStartSourceIds)===null||e===void 0?void 0:e.length)||0,dropTargetIds:this.dropTargetIds.length,dragEnterTargetIds:this.dragEnterTargetIds.length,dragOverTargetIds:((n=this.dragOverTargetIds)===null||n===void 0?void 0:n.length)||0}}get window(){return this.options.window}get document(){return this.options.document}get rootElement(){return this.options.rootElement}setup(){const e=this.rootElement;if(e!==void 0){if(e.__isReactDndBackendSetUp)throw new Error("Cannot have two HTML5 backends at the same time.");e.__isReactDndBackendSetUp=!0,this.addEventListeners(e)}}teardown(){const e=this.rootElement;if(e!==void 0&&(e.__isReactDndBackendSetUp=!1,this.removeEventListeners(this.rootElement),this.clearCurrentDragSourceNode(),this.asyncEndDragFrameId)){var n;(n=this.window)===null||n===void 0||n.cancelAnimationFrame(this.asyncEndDragFrameId)}}connectDragPreview(e,n,s){return this.sourcePreviewNodeOptions.set(e,s),this.sourcePreviewNodes.set(e,n),()=>{this.sourcePreviewNodes.delete(e),this.sourcePreviewNodeOptions.delete(e)}}connectDragSource(e,n,s){this.sourceNodes.set(e,n),this.sourceNodeOptions.set(e,s);const r=a=>this.handleDragStart(a,e),i=a=>this.handleSelectStart(a);return n.setAttribute("draggable","true"),n.addEventListener("dragstart",r),n.addEventListener("selectstart",i),()=>{this.sourceNodes.delete(e),this.sourceNodeOptions.delete(e),n.removeEventListener("dragstart",r),n.removeEventListener("selectstart",i),n.setAttribute("draggable","false")}}connectDropTarget(e,n){const s=a=>this.handleDragEnter(a,e),r=a=>this.handleDragOver(a,e),i=a=>this.handleDrop(a,e);return n.addEventListener("dragenter",s),n.addEventListener("dragover",r),n.addEventListener("drop",i),()=>{n.removeEventListener("dragenter",s),n.removeEventListener("dragover",r),n.removeEventListener("drop",i)}}addEventListeners(e){e.addEventListener&&(e.addEventListener("dragstart",this.handleTopDragStart),e.addEventListener("dragstart",this.handleTopDragStartCapture,!0),e.addEventListener("dragend",this.handleTopDragEndCapture,!0),e.addEventListener("dragenter",this.handleTopDragEnter),e.addEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.addEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.addEventListener("dragover",this.handleTopDragOver),e.addEventListener("dragover",this.handleTopDragOverCapture,!0),e.addEventListener("drop",this.handleTopDrop),e.addEventListener("drop",this.handleTopDropCapture,!0))}removeEventListeners(e){e.removeEventListener&&(e.removeEventListener("dragstart",this.handleTopDragStart),e.removeEventListener("dragstart",this.handleTopDragStartCapture,!0),e.removeEventListener("dragend",this.handleTopDragEndCapture,!0),e.removeEventListener("dragenter",this.handleTopDragEnter),e.removeEventListener("dragenter",this.handleTopDragEnterCapture,!0),e.removeEventListener("dragleave",this.handleTopDragLeaveCapture,!0),e.removeEventListener("dragover",this.handleTopDragOver),e.removeEventListener("dragover",this.handleTopDragOverCapture,!0),e.removeEventListener("drop",this.handleTopDrop),e.removeEventListener("drop",this.handleTopDropCapture,!0))}getCurrentSourceNodeOptions(){const e=this.monitor.getSourceId(),n=this.sourceNodeOptions.get(e);return be({dropEffect:this.altKeyPressed?"copy":"move"},n||{})}getCurrentDropEffect(){return this.isDraggingNativeItem()?"copy":this.getCurrentSourceNodeOptions().dropEffect}getCurrentSourcePreviewNodeOptions(){const e=this.monitor.getSourceId(),n=this.sourcePreviewNodeOptions.get(e);return be({anchorX:.5,anchorY:.5,captureDraggingState:!1},n||{})}isDraggingNativeItem(){const e=this.monitor.getItemType();return Object.keys(me).some(n=>me[n]===e)}beginDragNativeItem(e,n){this.clearCurrentDragSourceNode(),this.currentNativeSource=tr(e,n),this.currentNativeHandle=this.registry.addSource(e,this.currentNativeSource),this.actions.beginDrag([this.currentNativeHandle])}setCurrentDragSourceNode(e){this.clearCurrentDragSourceNode(),this.currentDragSourceNode=e;const n=1e3;this.mouseMoveTimeoutTimer=setTimeout(()=>{var s;return(s=this.rootElement)===null||s===void 0?void 0:s.addEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)},n)}clearCurrentDragSourceNode(){if(this.currentDragSourceNode){if(this.currentDragSourceNode=null,this.rootElement){var e;(e=this.window)===null||e===void 0||e.clearTimeout(this.mouseMoveTimeoutTimer||void 0),this.rootElement.removeEventListener("mousemove",this.endDragIfSourceWasRemovedFromDOM,!0)}return this.mouseMoveTimeoutTimer=null,!0}return!1}handleDragStart(e,n){e.defaultPrevented||(this.dragStartSourceIds||(this.dragStartSourceIds=[]),this.dragStartSourceIds.unshift(n))}handleDragEnter(e,n){this.dragEnterTargetIds.unshift(n)}handleDragOver(e,n){this.dragOverTargetIds===null&&(this.dragOverTargetIds=[]),this.dragOverTargetIds.unshift(n)}handleDrop(e,n){this.dropTargetIds.unshift(n)}constructor(e,n,s){this.sourcePreviewNodes=new Map,this.sourcePreviewNodeOptions=new Map,this.sourceNodes=new Map,this.sourceNodeOptions=new Map,this.dragStartSourceIds=null,this.dropTargetIds=[],this.dragEnterTargetIds=[],this.currentNativeSource=null,this.currentNativeHandle=null,this.currentDragSourceNode=null,this.altKeyPressed=!1,this.mouseMoveTimeoutTimer=null,this.asyncEndDragFrameId=null,this.dragOverTargetIds=null,this.lastClientOffset=null,this.hoverRafId=null,this.getSourceClientOffset=r=>{const i=this.sourceNodes.get(r);return i&&Be(i)||null},this.endDragNativeItem=()=>{this.isDraggingNativeItem()&&(this.actions.endDrag(),this.currentNativeHandle&&this.registry.removeSource(this.currentNativeHandle),this.currentNativeHandle=null,this.currentNativeSource=null)},this.isNodeInDocument=r=>!!(r&&this.document&&this.document.body&&this.document.body.contains(r)),this.endDragIfSourceWasRemovedFromDOM=()=>{const r=this.currentDragSourceNode;r==null||this.isNodeInDocument(r)||(this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover())},this.scheduleHover=r=>{this.hoverRafId===null&&typeof requestAnimationFrame<"u"&&(this.hoverRafId=requestAnimationFrame(()=>{this.monitor.isDragging()&&this.actions.hover(r||[],{clientOffset:this.lastClientOffset}),this.hoverRafId=null}))},this.cancelHover=()=>{this.hoverRafId!==null&&typeof cancelAnimationFrame<"u"&&(cancelAnimationFrame(this.hoverRafId),this.hoverRafId=null)},this.handleTopDragStartCapture=()=>{this.clearCurrentDragSourceNode(),this.dragStartSourceIds=[]},this.handleTopDragStart=r=>{if(r.defaultPrevented)return;const{dragStartSourceIds:i}=this;this.dragStartSourceIds=null;const a=R(r);this.monitor.isDragging()&&(this.actions.endDrag(),this.cancelHover()),this.actions.beginDrag(i||[],{publishSource:!1,getSourceClientOffset:this.getSourceClientOffset,clientOffset:a});const{dataTransfer:c}=r,d=z(c);if(this.monitor.isDragging()){if(c&&typeof c.setDragImage=="function"){const f=this.monitor.getSourceId(),S=this.sourceNodes.get(f),p=this.sourcePreviewNodes.get(f)||S;if(p){const{anchorX:u,anchorY:v,offsetX:D,offsetY:h}=this.getCurrentSourcePreviewNodeOptions(),I=or(S,p,a,{anchorX:u,anchorY:v},{offsetX:D,offsetY:h});c.setDragImage(p,I.x,I.y)}}try{c?.setData("application/json",{})}catch{}this.setCurrentDragSourceNode(r.target);const{captureDraggingState:l}=this.getCurrentSourcePreviewNodeOptions();l?this.actions.publishDragSource():setTimeout(()=>this.actions.publishDragSource(),0)}else if(d)this.beginDragNativeItem(d);else{if(c&&!c.types&&(r.target&&!r.target.hasAttribute||!r.target.hasAttribute("draggable")))return;r.preventDefault()}},this.handleTopDragEndCapture=()=>{this.clearCurrentDragSourceNode()&&this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleTopDragEnterCapture=r=>{if(this.dragEnterTargetIds=[],this.isDraggingNativeItem()){var i;(i=this.currentNativeSource)===null||i===void 0||i.loadDataTransfer(r.dataTransfer)}if(!this.enterLeaveCounter.enter(r.target)||this.monitor.isDragging())return;const{dataTransfer:c}=r,d=z(c);d&&this.beginDragNativeItem(d,c)},this.handleTopDragEnter=r=>{const{dragEnterTargetIds:i}=this;if(this.dragEnterTargetIds=[],!this.monitor.isDragging())return;this.altKeyPressed=r.altKey,i.length>0&&this.actions.hover(i,{clientOffset:R(r)}),i.some(c=>this.monitor.canDropOnTarget(c))&&(r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect=this.getCurrentDropEffect()))},this.handleTopDragOverCapture=r=>{if(this.dragOverTargetIds=[],this.isDraggingNativeItem()){var i;(i=this.currentNativeSource)===null||i===void 0||i.loadDataTransfer(r.dataTransfer)}},this.handleTopDragOver=r=>{const{dragOverTargetIds:i}=this;if(this.dragOverTargetIds=[],!this.monitor.isDragging()){r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect="none");return}this.altKeyPressed=r.altKey,this.lastClientOffset=R(r),this.scheduleHover(i),(i||[]).some(c=>this.monitor.canDropOnTarget(c))?(r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect=this.getCurrentDropEffect())):this.isDraggingNativeItem()?r.preventDefault():(r.preventDefault(),r.dataTransfer&&(r.dataTransfer.dropEffect="none"))},this.handleTopDragLeaveCapture=r=>{this.isDraggingNativeItem()&&r.preventDefault(),this.enterLeaveCounter.leave(r.target)&&(this.isDraggingNativeItem()&&setTimeout(()=>this.endDragNativeItem(),0),this.cancelHover())},this.handleTopDropCapture=r=>{if(this.dropTargetIds=[],this.isDraggingNativeItem()){var i;r.preventDefault(),(i=this.currentNativeSource)===null||i===void 0||i.loadDataTransfer(r.dataTransfer)}else z(r.dataTransfer)&&r.preventDefault();this.enterLeaveCounter.reset()},this.handleTopDrop=r=>{const{dropTargetIds:i}=this;this.dropTargetIds=[],this.actions.hover(i,{clientOffset:R(r)}),this.actions.drop({dropEffect:this.getCurrentDropEffect()}),this.isDraggingNativeItem()?this.endDragNativeItem():this.monitor.isDragging()&&this.actions.endDrag(),this.cancelHover()},this.handleSelectStart=r=>{const i=r.target;typeof i.dragDrop=="function"&&(i.tagName==="INPUT"||i.tagName==="SELECT"||i.tagName==="TEXTAREA"||i.isContentEditable||(r.preventDefault(),i.dragDrop()))},this.options=new ar(n,s),this.actions=e.getActions(),this.monitor=e.getMonitor(),this.registry=e.getRegistry(),this.enterLeaveCounter=new Jn(this.isNodeInDocument)}}const lr=function(e,n,s){return new ur(e,n,s)},dr="5.2.0",fr={version:dr},gr=()=>{const t=Oe("GuidedTourModal",I=>I),{currentStep:e,guidedTourState:n,setCurrentStep:s,setStepState:r,isGuidedTourVisible:i,setSkipped:a}=t,{formatMessage:c}=C(),{trackUsage:d}=Z();if(!e||!i)return null;const l=mt(wt,e),f=Object.keys(n),[S,p]=e.split("."),u=f.indexOf(S),v=Object.keys(n[S]).indexOf(p),D=u<f.length-1,h=v<Object.keys(n[S]).length-1,b=()=>{r(e,!0),l&&d(l.trackingEvent),s(null)},w=()=>{a(!0),s(null),d("didSkipGuidedtour")};return o.jsx(vt,{children:o.jsx(hr,{onClick:b,padding:8,justifyContent:"center",children:o.jsx(bt,{onEscape:b,children:o.jsxs(m,{direction:"column",alignItems:"stretch",background:"neutral0",width:"66rem",shadow:"popupShadow",hasRadius:!0,padding:4,gap:8,role:"dialog","aria-modal":!0,onClick:I=>I.stopPropagation(),children:[o.jsx(m,{justifyContent:"flex-end",children:o.jsx(St,{onClick:b,withTooltip:!1,label:c({id:"app.utils.close-label",defaultMessage:"Close"}),children:o.jsx(we,{})})}),o.jsx(E,{paddingLeft:7,paddingRight:7,paddingBottom:!h&&!D?8:0,children:o.jsx(pr,{title:l&&"title"in l?l.title:void 0,cta:l&&"cta"in l?l.cta:void 0,onCtaClick:b,sectionIndex:u,stepIndex:v,hasSectionAfter:D,children:l&&"content"in l&&o.jsx(mr,{...l.content})})}),!(!h&&!D)&&o.jsx(m,{justifyContent:"flex-end",children:o.jsx(J,{variant:"tertiary",onClick:w,children:c({id:"app.components.GuidedTour.skip",defaultMessage:"Skip the tour"})})})]})})})})},hr=y(m)`
  position: fixed;
  z-index: 4;
  inset: 0;
  /* this is theme.colors.neutral800 with opacity */
  background: ${({theme:t})=>`${t.colors.neutral800}1F`};
`,pr=({title:t,children:e,cta:n,onCtaClick:s,sectionIndex:r,stepIndex:i,hasSectionAfter:a})=>{const{formatMessage:c}=C(),d=r>0,l=i>0,f=r+1;return o.jsxs(o.Fragment,{children:[o.jsxs(m,{alignItems:"stretch",children:[o.jsx(m,{marginRight:8,justifyContent:"center",minWidth:"3rem",children:d&&o.jsx(W,{state:j.IS_DONE,minHeight:"2.4rem"})}),o.jsx(x,{variant:"sigma",textColor:"primary600",children:c({id:"app.components.GuidedTour.title",defaultMessage:"3 steps to get started"})})]}),o.jsxs(m,{children:[o.jsx(m,{marginRight:8,minWidth:"3rem",children:o.jsx(ue,{state:l?j.IS_DONE:j.IS_ACTIVE,paddingTop:3,paddingBottom:3,children:r+1})}),t&&o.jsx(x,{variant:"alpha",fontWeight:"bold",textColor:"neutral800",tag:"h3",id:"title",children:c(t)})]}),o.jsxs(m,{alignItems:"stretch",children:[o.jsx(m,{marginRight:8,direction:"column",justifyContent:"center",minWidth:"3rem",children:a&&o.jsxs(o.Fragment,{children:[o.jsx(W,{state:j.IS_DONE}),l&&o.jsx(ue,{state:j.IS_ACTIVE,paddingTop:3,children:f+1})]})}),o.jsxs(E,{children:[e,n&&(n.target?o.jsx(yt,{tag:Ce,endIcon:o.jsx(ae,{}),onClick:s,to:n.target,children:c(n.title)}):o.jsx(J,{endIcon:o.jsx(ae,{}),onClick:s,children:c(n.title)}))]})]}),l&&a&&o.jsx(E,{paddingTop:3,children:o.jsx(m,{marginRight:8,justifyContent:"center",width:"3rem",children:o.jsx(W,{state:j.IS_DONE,minHeight:"2.4rem"})})})]})},mr=({id:t,defaultMessage:e})=>{const{formatMessage:n}=C();return o.jsx(m,{direction:"column",alignItems:"stretch",gap:4,paddingBottom:6,children:n({id:t,defaultMessage:e},{documentationLink:vr,b:br,p:Sr,light:yr,ul:Dr,li:Or})})},vr=t=>o.jsx(x,{tag:"a",textColor:"primary600",target:"_blank",rel:"noopener noreferrer",href:"https://docs.strapi.io/developer-docs/latest/developer-resources/database-apis-reference/rest-api.html#api-parameters",children:t}),br=t=>o.jsx(x,{fontWeight:"semiBold",children:t}),Sr=t=>o.jsx(x,{children:t}),yr=t=>o.jsx(x,{textColor:"neutral600",children:t}),Dr=t=>o.jsx(E,{paddingLeft:6,children:o.jsx("ul",{children:t})}),Tr=y.li`
  list-style: disc;
  &::marker {
    color: ${({theme:t})=>t.colors.neutral800};
  }
`,Or=t=>o.jsx(Tr,{children:t}),xr=y(m)`
  border-right: 1px solid ${({theme:t})=>t.colors.neutral150};
`,Er=t=>o.jsx(xr,{alignItems:"normal",tag:"nav",background:"neutral0",direction:"column",height:"100vh",position:"sticky",top:0,zIndex:2,width:10,...t}),Ir=y(m)`
  svg,
  img {
    border-radius: ${({theme:t})=>t.borderRadius};
    object-fit: contain;
    height: 2.4rem;
    width: 2.4rem;
  }
`,Cr=()=>{const{formatMessage:t}=C(),{logos:{menu:e}}=q("LeftMenu");return o.jsx(E,{padding:3,children:o.jsxs(Ir,{direction:"column",justifyContent:"center",width:"3.2rem",height:"3.2rem",children:[o.jsx("img",{src:e.custom?.url||e.default,alt:t({id:"app.components.LeftMenu.logo.alt",defaultMessage:"Application logo"}),width:"100%",height:"100%"}),o.jsxs(Q,{children:[o.jsx("span",{children:t({id:"app.components.LeftMenu.navbrand.title",defaultMessage:"Strapi Dashboard"})}),o.jsx("span",{children:t({id:"app.components.LeftMenu.navbrand.workplace",defaultMessage:"Workplace"})})]})]})})},wr=y(Ce)`
  text-decoration: none;
  display: flex;
  border-radius: ${({theme:t})=>t.borderRadius};
  background: ${({theme:t})=>t.colors.neutral0};
  color: ${({theme:t})=>t.colors.neutral500};
  position: relative;
  width: fit-content;
  padding-block: 0.6rem;
  padding-inline: 0.6rem;

  &:hover,
  &.active {
    background: ${({theme:t})=>t.colors.neutral100};
  }

  &:hover {
    svg path {
      fill: ${({theme:t})=>t.colors.neutral600};
    }
    color: ${({theme:t})=>t.colors.neutral700};
  }

  &.active {
    svg path {
      fill: ${({theme:t})=>t.colors.primary600};
    }

    color: ${({theme:t})=>t.colors.primary600};
    font-weight: 500;
  }
`,jr=({children:t,...e})=>o.jsx(wr,{...e,children:t}),Lr=({children:t,label:e,position:n="right"})=>o.jsx(ut,{side:n,label:e,delayDuration:0,children:o.jsx("span",{children:t})}),Nr=({label:t,children:e})=>e?o.jsx(lt,{label:t,children:e}):null,kr=y(dt)`
  /* override default badge styles to change the border radius of the Base element in the Design System */
  border-radius: ${({theme:t})=>t.spaces[10]};
  height: 2rem;
`,Pr=({children:t,label:e,...n})=>t?o.jsx(kr,{position:"absolute",top:"-0.8rem",left:"1.7rem","aria-label":e,active:!1,...n,children:t}):null,k={Link:jr,Tooltip:Lr,Icon:Nr,Badge:Pr},Rr=y(M.Trigger)`
  height: 100%;
  border-radius: 0;
  border-width: 1px 0 0 0;
  border-color: ${({theme:t})=>t.colors.neutral150};
  border-style: solid;
  padding: ${({theme:t})=>t.spaces[3]};
  // padding 12px - 1px border width
  padding-top: 11px;
  // Prevent empty pixel from appearing below the main nav
  overflow: hidden;
`,Ar=y(M.Content)`
  left: ${({theme:t})=>t.spaces[5]};
`,Se=y(M.Item)`
  & > span {
    width: 100%;
    display: flex;
    align-items: center;
    gap: ${({theme:t})=>t.spaces[3]};
    justify-content: space-between;
  }
`,Mr=({children:t,initials:e,...n})=>{const{formatMessage:s}=C(),r=ft(),i=P("Logout",d=>d.logout),a=()=>{r("/me")},c=()=>{i(),r("/auth/login")};return o.jsx(m,{justifyContent:"center",...n,children:o.jsxs(M.Root,{children:[o.jsxs(Rr,{endIcon:null,fullWidth:!0,justifyContent:"center",children:[o.jsx(gt.Item,{delayMs:0,fallback:e}),o.jsx(Q,{tag:"span",children:t})]}),o.jsxs(Ar,{popoverPlacement:"top-center",zIndex:3,children:[o.jsx(Se,{onSelect:a,children:s({id:"global.profile",defaultMessage:"Profile"})}),o.jsxs(Se,{onSelect:c,color:"danger600",children:[s({id:"app.components.LeftMenu.logout",defaultMessage:"Logout"}),o.jsx(ht,{})]})]})]})})},Xr=t=>t.sort((e,n)=>{const s=e.position??6,r=n.position??6;return s<r?-1:1}),_r=y(k.Badge)`
  span {
    color: ${({theme:t})=>t.colors.neutral0};
  }
`,Ur=y(k.Badge)`
  background-color: transparent;
`,Fr=y(m)`
  overflow-y: auto;
`,Br=({generalSectionLinks:t,pluginsSectionLinks:e})=>{const n=P("AuthenticatedApp",u=>u.user),{trackUsage:s}=Z(),{pathname:r}=ot(),i=Lt(n),{formatMessage:a,locale:c}=C(),d=at(c,{sensitivity:"base"}),l=i.split(" ").map(u=>u.substring(0,1)).join("").substring(0,2),f=u=>{s("willNavigate",{from:r,to:u})},S=[...e,...t].sort((u,v)=>d.compare(a(u.intlLabel),a(v.intlLabel))),p=Xr(S);return o.jsxs(Er,{children:[o.jsx(Cr,{}),o.jsx(Ie,{}),o.jsx(Fr,{tag:"ul",gap:3,direction:"column",flex:1,paddingTop:3,paddingBottom:3,children:p.length>0?p.map(u=>{const v=u.icon,D=u?.licenseOnly?o.jsx(ct,{fill:"warning500"}):void 0,h=u.notificationsCount&&u.notificationsCount>0?u.notificationsCount.toString():void 0,b=a(u.intlLabel);return o.jsx(m,{tag:"li",children:o.jsx(k.Tooltip,{label:b,children:o.jsxs(k.Link,{to:u.to,onClick:()=>f(u.to),"aria-label":b,children:[o.jsx(k.Icon,{label:b,children:o.jsx(v,{width:"20",height:"20",fill:"neutral500"})}),D?o.jsx(Ur,{label:"locked",textColor:"neutral500",paddingLeft:0,paddingRight:0,children:D}):h?o.jsx(_r,{label:h,backgroundColor:"primary600",width:"2.3rem",color:"neutral0",children:h}):null]})})},u.to)}):null}),o.jsx(Mr,{initials:l,children:i})]})},Hr="data:image/png;base64,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",Wr=()=>{const[t,e]=T.useState(!1),{formatMessage:n}=C(),s=Dt("Onboarding",a=>a.communityEdition),r=[...Kr,{label:{id:"Settings.application.get-help",defaultMessage:"Get help"},icon:It,href:s?"https://discord.strapi.io":"https://support.strapi.io/support/home"}],i=t?we:Tt;return o.jsx(H.Root,{onOpenChange:e,children:o.jsxs(E,{position:"fixed",bottom:2,right:2,children:[o.jsx(H.Trigger,{children:o.jsx(Gr,{"aria-label":n(t?{id:"app.components.Onboarding.help.button-close",defaultMessage:"Close help menu"}:{id:"app.components.Onboarding.help.button",defaultMessage:"Open help menu"}),children:o.jsx(i,{fill:"buttonNeutral0"})})}),o.jsxs(H.Content,{align:"end",side:"top",sideOffset:12,children:[o.jsxs(m,{justifyContent:"space-between",paddingBottom:5,paddingRight:6,paddingLeft:6,paddingTop:6,children:[o.jsx(Vr,{fontWeight:"bold",children:n({id:"app.components.Onboarding.title",defaultMessage:"Get started videos"})}),o.jsx(ye,{tag:"a",href:De.href,target:"_blank",rel:"noreferrer noopener",variant:"pi",textColor:"primary600",children:n(De.label)})]}),o.jsx(Ie,{}),Yr.map(({href:a,duration:c,label:d},l)=>o.jsxs($r,{tag:"a",href:a,target:"_blank",rel:"noreferrer noopener",hasRadius:!0,paddingTop:4,paddingBottom:4,paddingLeft:6,paddingRight:11,children:[o.jsx(E,{paddingRight:5,children:o.jsx(He,{textColor:"neutral200",variant:"alpha",children:l+1})}),o.jsxs(E,{position:"relative",children:[o.jsx(qr,{src:Hr,alt:""}),o.jsx(zr,{position:"absolute",top:"50%",left:"50%",background:"primary600",borderRadius:"50%",justifyContent:"center",width:6,height:6,children:o.jsx(Ot,{fill:"buttonNeutral0",width:"12",height:"12"})})]}),o.jsxs(m,{direction:"column",alignItems:"start",paddingLeft:4,children:[o.jsx(We,{fontWeight:"bold",children:n(d)}),o.jsx(Q,{children:":"}),o.jsx(x,{textColor:"neutral600",variant:"pi",children:c})]})]},a)),o.jsx(m,{direction:"column",alignItems:"stretch",gap:2,paddingLeft:5,paddingTop:2,paddingBottom:5,children:r.map(({label:a,href:c,icon:d})=>o.jsxs(m,{gap:3,children:[o.jsx(d,{fill:"primary600"}),o.jsx(ye,{tag:"a",href:c,target:"_blank",rel:"noreferrer noopener",variant:"sigma",textColor:"primary700",children:n(a)})]},c))})]})]})})},Gr=y(J)`
  border-radius: 50%;
  padding: ${({theme:t})=>t.spaces[3]};
  /* Resetting 2rem height defined by Button component */
  height: unset;
  width: unset;

  & > span {
    display: flex;

    svg {
      width: 1.6rem;
      height: 1.6rem;
    }
  }
`,zr=y(m)`
  transform: translate(-50%, -50%);
`,He=y(x)``,We=y(x)``,$r=y(m)`
  text-decoration: none;

  :focus-visible {
    outline-offset: ${({theme:t})=>`-${t.spaces[1]}`};
  }

  :hover {
    background: ${({theme:t})=>t.colors.primary100};

    /* Hover style for the number displayed */
    ${He} {
      color: ${({theme:t})=>t.colors.primary500};
    }

    /* Hover style for the label */
    ${We} {
      color: ${({theme:t})=>t.colors.primary600};
    }
  }
`,qr=y.img`
  width: ${({theme:t})=>t.spaces[10]};
  height: ${({theme:t})=>t.spaces[8]};
  /* Same overlay used in ModalLayout */
  background: ${({theme:t})=>`${t.colors.neutral800}1F`};
  border-radius: ${({theme:t})=>t.borderRadius};
`,Vr=y(x)`
  /* line height of label and watch more to 1 so they can be better aligned visually */
  line-height: 1;
`,ye=y(x)`
  text-decoration: none;
  line-height: 1;

  :hover {
    text-decoration: underline;
  }
`,Yr=[{label:{id:"app.components.Onboarding.link.build-content",defaultMessage:"Build a content architecture"},href:"https://www.youtube.com/watch?v=G9GjN0RxhkE",duration:"5:48"},{label:{id:"app.components.Onboarding.link.manage-content",defaultMessage:"Add & manage content"},href:"https://www.youtube.com/watch?v=DEZw4KbybAI",duration:"3:18"},{label:{id:"app.components.Onboarding.link.manage-media",defaultMessage:"Manage media"},href:"https://www.youtube.com/watch?v=-61MuiMQb38",duration:"3:41"}],De={href:"https://www.youtube.com/playlist?list=PL7Q0DQYATmvidz6lEmwE5nIcOAYagxWqq",label:{id:"app.components.Onboarding.link.more-videos",defaultMessage:"Watch more videos"}},Kr=[{label:{id:"global.documentation",defaultMessage:"documentation"},href:"https://docs.strapi.io",icon:xt},{label:{id:"app.static.links.cheatsheet",defaultMessage:"cheatsheet"},href:"https://strapi-showcase.s3-us-west-2.amazonaws.com/CheatSheet.pdf",icon:Et}],Zr=({children:t})=>{const e=xe("PluginsInitializer",a=>a.plugins),[{plugins:n},s]=T.useReducer(Qr,Ge,()=>Jr(e)),r=T.useRef(a=>{s({type:"SET_PLUGIN_READY",pluginId:a})});if(Object.keys(n).some(a=>n[a].isReady===!1)){const a=Object.keys(n).reduce((c,d)=>{const l=n[d].initializer;if(l){const f=n[d].pluginId;c.push(o.jsx(l,{setPlugin:r.current},f))}return c},[]);return o.jsxs(o.Fragment,{children:[a,o.jsx(Ee.Loading,{})]})}return t},Ge={plugins:{}},Qr=(t=Ge,e)=>rt(t,n=>{switch(e.type){case"SET_PLUGIN_READY":{st(n,["plugins",e.pluginId,"isReady"],!0);break}default:return n}}),Jr=t=>({plugins:t}),es=t=>{const e=P("useMenu",c=>c.checkUserHasPermissions),n=xe("useMenu",c=>c.menu),s=Ke(c=>c.admin_app.permissions),[r,i]=T.useState({generalSectionLinks:[{icon:Ze,intlLabel:{id:"global.home",defaultMessage:"Home"},to:"/",permissions:[],position:0},{icon:Qe,intlLabel:{id:"global.marketplace",defaultMessage:"Marketplace"},to:"/marketplace",permissions:s.marketplace?.main??[],position:7},{icon:Je,intlLabel:{id:"global.settings",defaultMessage:"Settings"},to:"/settings",permissions:[],notificationsCount:0,position:9}],pluginsSectionLinks:[],isLoading:!0}),a=T.useRef(r.generalSectionLinks);return T.useEffect(()=>{async function c(){const d=await ns(n,e),l=await ts(a.current,t,e);i(f=>({...f,generalSectionLinks:l,pluginsSectionLinks:d,isLoading:!1}))}c()},[i,a,n,s,t,e]),r},ts=async(t,e=!1,n)=>{const s=await Promise.all(t.map(({permissions:c})=>n(c))),r=t.filter((c,d)=>s[d].length>0),i=r.findIndex(c=>c.to==="/settings");if(i===-1)return[];const a=et(r);return a[i].notificationsCount=e?1:0,a},ns=async(t,e)=>{const n=await Promise.all(t.map(({permissions:r})=>e(r)));return t.filter((r,i)=>n[i].length>0)},$=fr.version,rs=()=>{const t=Oe("AdminLayout",h=>h.setGuidedTourVisibility),{formatMessage:e}=C(),n=P("AuthenticatedApp",h=>h.user),[s,r]=T.useState(),{showReleaseNotification:i}=q("AuthenticatedApp"),{data:a,isLoading:c}=Ye(),[d,l]=T.useState($);T.useEffect(()=>{i&&fetch("https://api.github.com/repos/strapi/strapi/releases/latest").then(async h=>{if(!h.ok)return;const b=await h.json();if(!b.tag_name)throw new Error;l(b.tag_name)}).catch(()=>{})},[i]);const f=P("AuthenticatedApp",h=>h.user?.roles);T.useEffect(()=>{f&&f.find(({code:b})=>b==="strapi-super-admin")&&a?.autoReload&&t(!0)},[f,a?.autoReload,t]),T.useEffect(()=>{jt(n).then(h=>{h&&r(h)})},[n]);const{trackUsage:S}=Z(),{isLoading:p,generalSectionLinks:u,pluginsSectionLinks:v}=es(Te($,d)),{showTutorials:D}=q("Admin");return kt(()=>{S("didAccessAuthenticatedAdministration")}),p||c?o.jsx(Ee.Loading,{}):o.jsxs(tt,{...a,userId:s,latestStrapiReleaseTag:d,shouldUpdateStrapi:Te($,d),children:[o.jsx(nt,{}),o.jsx(Zr,{children:o.jsx(Vn,{backend:lr,children:o.jsxs(E,{background:"neutral100",children:[o.jsx(it,{children:e({id:"skipToContent",defaultMessage:"Skip to content"})}),o.jsxs(m,{alignItems:"flex-start",children:[o.jsx(Br,{generalSectionLinks:u,pluginsSectionLinks:v}),o.jsxs(E,{flex:1,children:[o.jsx(pt,{}),o.jsx(gr,{}),D&&o.jsx(Wr,{})]})]})]})})})]})},ls=()=>o.jsx(Nt,{children:o.jsx(rs,{})}),Te=(t,e="")=>!ce(t)||!ce(e)?!1:Ct(t,e);export{rs as AdminLayout,ls as PrivateAdminLayout};
