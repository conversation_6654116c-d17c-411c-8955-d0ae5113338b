import {
  useTracking
} from "./chunk-QEDS3BR4.js";
import "./chunk-SDRTZUB7.js";
import {
  useIntl
} from "./chunk-IKDDXE5L.js";
import "./chunk-MM3DOGUR.js";
import "./chunk-6NLSGXWT.js";
import "./chunk-DC3UNANX.js";
import "./chunk-5ZFC5BBE.js";
import "./chunk-CH6LMMF3.js";
import "./chunk-C7H2BX76.js";
import "./chunk-3UWI42TF.js";
import "./chunk-EGRHWZRV.js";
import {
  require_prop_types
} from "./chunk-S4GMEU6I.js";
import "./chunk-XPB4KQQT.js";
import "./chunk-D63J2BWQ.js";
import "./chunk-IZBVLW72.js";
import "./chunk-M5EKOYPZ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-O6QFUROF.js";
import "./chunk-VOKTPSOW.js";
import "./chunk-N562NCQ4.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import {
  require_isEmpty
} from "./chunk-KKUAHZGP.js";
import "./chunk-VUQGR7S3.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Form2 as Form,
  Layouts,
  MemoizedInputRenderer,
  create4 as create,
  create6 as create2,
  errorsTrads,
  useFetchClient,
  useMutation,
  useQuery,
  useQueryClient
} from "./chunk-ELTZWS66.js";
import {
  Page,
  useAPIErrorHandler,
  useNotification,
  useRBAC
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Breadcrumbs,
  Button,
  Crumb,
  Grid,
  IconButton,
  Modal,
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Typography,
  VisuallyHidden,
  useNotifyAT
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1r,
  ForwardRef$4p,
  ForwardRef$51
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  PERMISSIONS,
  getTrad
} from "./chunk-WE3SAIMN.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/index-D__N3W_e.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_isEmpty = __toESM(require_isEmpty(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var schema = create2().shape({
  options: create2().shape({
    from: create2().shape({
      name: create().required({
        id: errorsTrads.required.id,
        defaultMessage: "This field is required"
      }),
      email: create().email(errorsTrads.email).required({
        id: errorsTrads.required.id,
        defaultMessage: "This field is required"
      })
    }).required(),
    response_email: create().email(errorsTrads.email),
    object: create().required({
      id: errorsTrads.required.id,
      defaultMessage: "This field is required"
    }),
    message: create().required({
      id: errorsTrads.required.id,
      defaultMessage: "This field is required"
    })
  }).required(errorsTrads.required.id)
});
var EmailForm = ({ template = {}, onToggle, open, onSubmit }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Modal.Root, { open, onOpenChange: onToggle, children: (0, import_jsx_runtime.jsxs)(Modal.Content, { children: [
    (0, import_jsx_runtime.jsxs)(Modal.Header, { children: [
      (0, import_jsx_runtime.jsxs)(
        Breadcrumbs,
        {
          label: `${formatMessage({
            id: getTrad("PopUpForm.header.edit.email-templates"),
            defaultMessage: "Edit email template"
          })}, ${template.display ? formatMessage({
            id: getTrad(template.display),
            defaultMessage: template.display
          }) : ""}`,
          children: [
            (0, import_jsx_runtime.jsx)(Crumb, { children: formatMessage({
              id: getTrad("PopUpForm.header.edit.email-templates"),
              defaultMessage: "Edit email template"
            }) }),
            (0, import_jsx_runtime.jsx)(Crumb, { isCurrent: true, children: template.display ? formatMessage({ id: getTrad(template.display), defaultMessage: template.display }) : "" })
          ]
        }
      ),
      (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: (0, import_jsx_runtime.jsx)(Modal.Title, { children: `${formatMessage({
        id: getTrad("PopUpForm.header.edit.email-templates"),
        defaultMessage: "Edit email template"
      })}, ${template.display ? formatMessage({ id: getTrad(template.display), defaultMessage: template.display }) : ""}` }) })
    ] }),
    (0, import_jsx_runtime.jsx)(Form, { onSubmit, initialValues: template, validationSchema: schema, children: ({ isSubmitting }) => {
      return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
        (0, import_jsx_runtime.jsx)(Modal.Body, { children: (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 5, children: [
          {
            label: formatMessage({
              id: getTrad("PopUpForm.Email.options.from.name.label"),
              defaultMessage: "Shipper name"
            }),
            name: "options.from.name",
            size: 6,
            type: "string"
          },
          {
            label: formatMessage({
              id: getTrad("PopUpForm.Email.options.from.email.label"),
              defaultMessage: "Shipper email"
            }),
            name: "options.from.email",
            size: 6,
            type: "string"
          },
          {
            label: formatMessage({
              id: getTrad("PopUpForm.Email.options.response_email.label"),
              defaultMessage: "Response email"
            }),
            name: "options.response_email",
            size: 6,
            type: "string"
          },
          {
            label: formatMessage({
              id: getTrad("PopUpForm.Email.options.object.label"),
              defaultMessage: "Subject"
            }),
            name: "options.object",
            size: 6,
            type: "string"
          },
          {
            label: formatMessage({
              id: getTrad("PopUpForm.Email.options.message.label"),
              defaultMessage: "Message"
            }),
            name: "options.message",
            size: 12,
            type: "text"
          }
        ].map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(
          Grid.Item,
          {
            col: size,
            direction: "column",
            alignItems: "stretch",
            children: (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, { ...field })
          },
          field.name
        )) }) }),
        (0, import_jsx_runtime.jsxs)(Modal.Footer, { children: [
          (0, import_jsx_runtime.jsx)(Modal.Close, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "tertiary", children: "Cancel" }) }),
          (0, import_jsx_runtime.jsx)(Button, { loading: isSubmitting, type: "submit", children: "Finish" })
        ] })
      ] });
    } })
  ] }) });
};
EmailForm.defaultProps = {
  template: {}
};
EmailForm.propTypes = {
  template: import_prop_types.default.shape({
    display: import_prop_types.default.string,
    icon: import_prop_types.default.string,
    options: import_prop_types.default.shape({
      from: import_prop_types.default.shape({
        name: import_prop_types.default.string,
        email: import_prop_types.default.string
      }),
      message: import_prop_types.default.string,
      object: import_prop_types.default.string,
      response_email: import_prop_types.default.string
    })
  }),
  open: import_prop_types.default.bool.isRequired,
  onSubmit: import_prop_types.default.func.isRequired,
  onToggle: import_prop_types.default.func.isRequired
};
var EmailTable = ({ canUpdate, onEditClick }) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Table, { colCount: 3, rowCount: 3, children: [
    (0, import_jsx_runtime.jsx)(Thead, { children: (0, import_jsx_runtime.jsxs)(Tr, { children: [
      (0, import_jsx_runtime.jsx)(Th, { width: "1%", children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: formatMessage({
        id: getTrad("Email.template.table.icon.label"),
        defaultMessage: "icon"
      }) }) }),
      (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({
        id: getTrad("Email.template.table.name.label"),
        defaultMessage: "name"
      }) }) }),
      (0, import_jsx_runtime.jsx)(Th, { width: "1%", children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: formatMessage({
        id: getTrad("Email.template.table.action.label"),
        defaultMessage: "action"
      }) }) })
    ] }) }),
    (0, import_jsx_runtime.jsxs)(Tbody, { children: [
      (0, import_jsx_runtime.jsxs)(Tr, { onClick: () => onEditClick("reset_password"), children: [
        (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Box, { width: "3.2rem", height: "3.2rem", padding: "0.8rem", children: (0, import_jsx_runtime.jsx)(
          ForwardRef$51,
          {
            "aria-label": formatMessage({
              id: "global.reset-password",
              defaultMessage: "Reset password"
            })
          }
        ) }) }),
        (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Typography, { children: formatMessage({
          id: "global.reset-password",
          defaultMessage: "Reset password"
        }) }) }),
        (0, import_jsx_runtime.jsx)(Td, { onClick: (e) => e.stopPropagation(), children: (0, import_jsx_runtime.jsx)(
          IconButton,
          {
            onClick: () => onEditClick("reset_password"),
            label: formatMessage({
              id: getTrad("Email.template.form.edit.label"),
              defaultMessage: "Edit a template"
            }),
            variant: "ghost",
            disabled: !canUpdate,
            children: (0, import_jsx_runtime.jsx)(ForwardRef$1r, {})
          }
        ) })
      ] }),
      (0, import_jsx_runtime.jsxs)(Tr, { onClick: () => onEditClick("email_confirmation"), children: [
        (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Box, { width: "3.2rem", height: "3.2rem", padding: "0.8rem", children: (0, import_jsx_runtime.jsx)(
          ForwardRef$4p,
          {
            "aria-label": formatMessage({
              id: getTrad("Email.template.email_confirmation"),
              defaultMessage: "Email address confirmation"
            })
          }
        ) }) }),
        (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Typography, { children: formatMessage({
          id: getTrad("Email.template.email_confirmation"),
          defaultMessage: "Email address confirmation"
        }) }) }),
        (0, import_jsx_runtime.jsx)(Td, { onClick: (e) => e.stopPropagation(), children: (0, import_jsx_runtime.jsx)(
          IconButton,
          {
            onClick: () => onEditClick("email_confirmation"),
            label: formatMessage({
              id: getTrad("Email.template.form.edit.label"),
              defaultMessage: "Edit a template"
            }),
            variant: "ghost",
            disabled: !canUpdate,
            children: (0, import_jsx_runtime.jsx)(ForwardRef$1r, {})
          }
        ) })
      ] })
    ] })
  ] });
};
EmailTable.propTypes = {
  canUpdate: import_prop_types.default.bool.isRequired,
  onEditClick: import_prop_types.default.func.isRequired
};
var ProtectedEmailTemplatesPage = () => (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.readEmailTemplates, children: (0, import_jsx_runtime.jsx)(EmailTemplatesPage, {}) });
var EmailTemplatesPage = () => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { notifyStatus } = useNotifyAT();
  const { toggleNotification } = useNotification();
  const queryClient = useQueryClient();
  const { get, put } = useFetchClient();
  const { formatAPIError } = useAPIErrorHandler();
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [templateToEdit, setTemplateToEdit] = React.useState(null);
  const {
    isLoading: isLoadingForPermissions,
    allowedActions: { canUpdate }
  } = useRBAC({ update: PERMISSIONS.updateEmailTemplates });
  const { isLoading: isLoadingData, data } = useQuery(
    ["users-permissions", "email-templates"],
    async () => {
      const { data: data2 } = await get("/users-permissions/email-templates");
      return data2;
    },
    {
      onSuccess() {
        notifyStatus(
          formatMessage({
            id: getTrad("Email.template.data.loaded"),
            defaultMessage: "Email templates has been loaded"
          })
        );
      },
      onError(error) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(error)
        });
      }
    }
  );
  const isLoading = isLoadingForPermissions || isLoadingData;
  const handleToggle = () => {
    setIsModalOpen((prev) => !prev);
  };
  const handleEditClick = (template) => {
    setTemplateToEdit(template);
    handleToggle();
  };
  const submitMutation = useMutation(
    (body) => put("/users-permissions/email-templates", { "email-templates": body }),
    {
      async onSuccess() {
        await queryClient.invalidateQueries(["users-permissions", "email-templates"]);
        toggleNotification({
          type: "success",
          message: formatMessage({ id: "notification.success.saved", defaultMessage: "Saved" })
        });
        trackUsage("didEditEmailTemplates");
        handleToggle();
      },
      onError(error) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(error)
        });
      },
      refetchActive: true
    }
  );
  const handleSubmit = (body) => {
    trackUsage("willEditEmailTemplates");
    const editedTemplates = { ...data, [templateToEdit]: body };
    submitMutation.mutate(editedTemplates);
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Page.Main, { "aria-busy": submitMutation.isLoading, children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      {
        name: formatMessage({
          id: getTrad("HeaderNav.link.emailTemplates"),
          defaultMessage: "Email templates"
        })
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: getTrad("HeaderNav.link.emailTemplates"),
          defaultMessage: "Email templates"
        })
      }
    ),
    (0, import_jsx_runtime.jsxs)(Layouts.Content, { children: [
      (0, import_jsx_runtime.jsx)(EmailTable, { onEditClick: handleEditClick, canUpdate }),
      (0, import_jsx_runtime.jsx)(
        EmailForm,
        {
          template: data[templateToEdit],
          onToggle: handleToggle,
          open: isModalOpen,
          onSubmit: handleSubmit
        }
      )
    ] })
  ] });
};
export {
  EmailTemplatesPage,
  ProtectedEmailTemplatesPage
};
//# sourceMappingURL=index-D__N3W_e-7TN5YUJ3.js.map
