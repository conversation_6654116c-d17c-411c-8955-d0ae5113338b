import{cG as n,m as e,n as o,L as a,eK as s,bM as r,bN as d,bK as i,b0 as c,_ as u}from"./strapi-YzJfjJ2z.js";const y=()=>{const{formatMessage:t}=n();return e.jsxs(o.Main,{children:[e.jsx(a<PERSON>,{title:t({id:s("header.name"),defaultMessage:"Content"})}),e.jsx(a.Content,{children:e.jsx(r,{action:e.jsx(i,{tag:u,variant:"secondary",startIcon:e.jsx(c,{}),to:"/plugins/content-type-builder/content-types/create-content-type",children:t({id:"app.components.HomePage.create",defaultMessage:"Create your first Content-type"})}),content:t({id:"content-manager.pages.NoContentType.text",defaultMessage:"You don't have any content yet, we recommend you to create your first Content-Type."}),hasRadius:!0,icon:e.jsx(d,{width:"16rem"}),shadow:"tableShadow"})})]})};export{y as NoContentType};
