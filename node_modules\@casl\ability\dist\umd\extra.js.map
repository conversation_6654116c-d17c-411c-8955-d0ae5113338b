{"version": 3, "file": "extra.js", "sources": ["../../src/utils.ts", "../../src/extra.ts"], "sourcesContent": ["import { AnyObject, Subject, SubjectType, SubjectClass, ForcedSubject, AliasesMap } from './types';\n\nexport function wrapArray<T>(value: T[] | T): T[] {\n  return Array.isArray(value) ? value : [value];\n}\n\nexport function setByPath(object: AnyObject, path: string, value: unknown): void {\n  let ref = object;\n  let lastKey = path;\n\n  if (path.indexOf('.') !== -1) {\n    const keys = path.split('.');\n\n    lastKey = keys.pop()!;\n    ref = keys.reduce((res, prop) => {\n      res[prop] = res[prop] || {};\n      return res[prop] as AnyObject;\n    }, object);\n  }\n\n  ref[lastKey] = value;\n}\n\nconst hasOwnProperty = (Object as any).hasOwn\n  || Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);\n\nconst TYPE_FIELD = '__caslSubjectType__';\nexport function setSubjectType<\n  T extends string,\n  U extends Record<PropertyKey, any>\n>(type: T, object: U): U & ForcedSubject<T> {\n  if (object) {\n    if (!hasOwnProperty(object, TYPE_FIELD)) {\n      Object.defineProperty(object, TYPE_FIELD, { value: type });\n    } else if (type !== object[TYPE_FIELD]) {\n      throw new Error(`Trying to cast object to subject type ${type} but previously it was casted to ${object[TYPE_FIELD]}`);\n    }\n  }\n\n  return object as U & ForcedSubject<T>;\n}\n\nexport const isSubjectType = (value: unknown): value is SubjectType => {\n  const type = typeof value;\n  return type === 'string' || type === 'function';\n};\n\nconst getSubjectClassName = (value: SubjectClass) => value.modelName || value.name;\nexport const getSubjectTypeName = (value: SubjectType) => {\n  return typeof value === 'string' ? value : getSubjectClassName(value);\n};\n\nexport function detectSubjectType(subject: Exclude<Subject, SubjectType>): string {\n  if (hasOwnProperty(subject, TYPE_FIELD)) {\n    return subject[TYPE_FIELD];\n  }\n\n  return getSubjectClassName(subject.constructor as SubjectClass);\n}\n\ntype AliasMerge = (actions: string[], action: string | string[]) => string[];\nfunction expandActions(aliasMap: AliasesMap, rawActions: string | string[], merge: AliasMerge) {\n  let actions = wrapArray(rawActions);\n  let i = 0;\n\n  while (i < actions.length) {\n    const action = actions[i++];\n\n    if (hasOwnProperty(aliasMap, action)) {\n      actions = merge(actions, aliasMap[action]);\n    }\n  }\n\n  return actions;\n}\n\nfunction findDuplicate(actions: string[], actionToFind: string | string[]) {\n  if (typeof actionToFind === 'string' && actions.indexOf(actionToFind) !== -1) {\n    return actionToFind;\n  }\n\n  for (let i = 0; i < actionToFind.length; i++) {\n    if (actions.indexOf(actionToFind[i]) !== -1) return actionToFind[i];\n  }\n\n  return null;\n}\n\nconst defaultAliasMerge: AliasMerge = (actions, action) => actions.concat(action);\nfunction validateForCycles(aliasMap: AliasesMap, reservedAction: string) {\n  if (reservedAction in aliasMap) {\n    throw new Error(`Cannot use \"${reservedAction}\" as an alias because it's reserved action.`);\n  }\n\n  const keys = Object.keys(aliasMap);\n  const mergeAliasesAndDetectCycles: AliasMerge = (actions, action) => {\n    const duplicate = findDuplicate(actions, action);\n    if (duplicate) throw new Error(`Detected cycle ${duplicate} -> ${actions.join(', ')}`);\n\n    const isUsingReservedAction = typeof action === 'string' && action === reservedAction\n      || actions.indexOf(reservedAction) !== -1\n      || Array.isArray(action) && action.indexOf(reservedAction) !== -1;\n    if (isUsingReservedAction) throw new Error(`Cannot make an alias to \"${reservedAction}\" because this is reserved action`);\n\n    return actions.concat(action);\n  };\n\n  for (let i = 0; i < keys.length; i++) {\n    expandActions(aliasMap, keys[i], mergeAliasesAndDetectCycles);\n  }\n}\n\nexport type AliasResolverOptions = { skipValidate?: boolean; anyAction?: string };\nexport function createAliasResolver(aliasMap: AliasesMap, options?: AliasResolverOptions) {\n  if (!options || options.skipValidate !== false) {\n    validateForCycles(aliasMap, options && options.anyAction || 'manage');\n  }\n\n  return (action: string | string[]) => expandActions(aliasMap, action, defaultAliasMerge);\n}\n\nfunction copyArrayTo<T>(dest: T[], target: T[], start: number) {\n  for (let i = start; i < target.length; i++) {\n    dest.push(target[i]);\n  }\n}\n\nexport function mergePrioritized<T extends { priority: number }>(\n  array?: T[],\n  anotherArray?: T[]\n): T[] {\n  if (!array || !array.length) {\n    return anotherArray || [];\n  }\n\n  if (!anotherArray || !anotherArray.length) {\n    return array || [];\n  }\n\n  let i = 0;\n  let j = 0;\n  const merged: T[] = [];\n\n  while (i < array.length && j < anotherArray.length) {\n    if (array[i].priority < anotherArray[j].priority) {\n      merged.push(array[i]);\n      i++;\n    } else {\n      merged.push(anotherArray[j]);\n      j++;\n    }\n  }\n\n  copyArrayTo(merged, array, i);\n  copyArrayTo(merged, anotherArray, j);\n\n  return merged;\n}\n\nexport function getOrDefault<K, V>(map: Map<K, V>, key: K, defaultValue: () => V) {\n  let value = map.get(key);\n\n  if (!value) {\n    value = defaultValue();\n    map.set(key, value);\n  }\n\n  return value;\n}\n\nexport const identity = <T>(x: T) => x;\n", "import { Condition, buildAnd, buildOr, CompoundCondition } from '@ucast/mongo2js';\nimport { PureAbility, AnyAbility } from './PureAbility';\nimport { RuleOf } from './RuleIndex';\nimport { RawRule } from './RawRule';\nimport { Rule } from './Rule';\nimport { setByPath, wrapArray } from './utils';\nimport { AnyObject, SubjectType, ExtractSubjectType } from './types';\n\nexport type RuleToQueryConverter<T extends AnyAbility> = (rule: RuleOf<T>) => object;\nexport interface AbilityQuery<T = object> {\n  $or?: T[]\n  $and?: T[]\n}\n\nexport function rulesToQuery<T extends AnyAbility>(\n  ability: T,\n  action: Parameters<T['rulesFor']>[0],\n  subjectType: ExtractSubjectType<Parameters<T['rulesFor']>[1]>,\n  convert: RuleToQueryConverter<T>\n): AbilityQuery | null {\n  const query: AbilityQuery = {};\n  const rules = ability.rulesFor(action, subjectType);\n\n  for (let i = 0; i < rules.length; i++) {\n    const rule = rules[i];\n    const op = rule.inverted ? '$and' : '$or';\n\n    if (!rule.conditions) {\n      if (rule.inverted) {\n        break;\n      } else {\n        delete query[op];\n        return query;\n      }\n    } else {\n      query[op] = query[op] || [];\n      query[op]!.push(convert(rule));\n    }\n  }\n\n  return query.$or ? query : null;\n}\n\nfunction ruleToAST(rule: RuleOf<AnyAbility>): Condition {\n  if (!rule.ast) {\n    throw new Error(`Ability rule \"${JSON.stringify(rule)}\" does not have \"ast\" property. So, cannot be used to generate AST`);\n  }\n\n  return rule.inverted ? new CompoundCondition('not', [rule.ast]) : rule.ast;\n}\n\nexport function rulesToAST<T extends AnyAbility>(\n  ability: T,\n  action: Parameters<T['rulesFor']>[0],\n  subjectType: ExtractSubjectType<Parameters<T['rulesFor']>[1]>,\n): Condition | null {\n  const query = rulesToQuery(ability, action, subjectType, ruleToAST) as AbilityQuery<Condition>;\n\n  if (query === null) {\n    return null;\n  }\n\n  if (!query.$and) {\n    return query.$or ? buildOr(query.$or) : buildAnd([]);\n  }\n\n  if (query.$or) {\n    query.$and.push(buildOr(query.$or));\n  }\n\n  return buildAnd(query.$and);\n}\n\nexport function rulesToFields<T extends PureAbility<any, AnyObject>>(\n  ability: T,\n  action: Parameters<T['rulesFor']>[0],\n  subjectType: ExtractSubjectType<Parameters<T['rulesFor']>[1]>,\n): AnyObject {\n  return ability.rulesFor(action, subjectType)\n    .reduce((values, rule) => {\n      if (rule.inverted || !rule.conditions) {\n        return values;\n      }\n\n      return Object.keys(rule.conditions).reduce((fields, fieldName) => {\n        const value = rule.conditions![fieldName];\n\n        if (!value || (value as any).constructor !== Object) {\n          setByPath(fields, fieldName, value);\n        }\n\n        return fields;\n      }, values);\n    }, {} as AnyObject);\n}\n\nexport type GetRuleFields<R extends Rule<any, any>> = (rule: R) => string[];\n\nexport interface PermittedFieldsOptions<T extends AnyAbility> {\n  fieldsFrom: GetRuleFields<RuleOf<T>>\n}\n\nexport function permittedFieldsOf<T extends AnyAbility>(\n  ability: T,\n  action: Parameters<T['can']>[0],\n  subject: Parameters<T['can']>[1],\n  options: PermittedFieldsOptions<T>\n): string[] {\n  const subjectType = ability.detectSubjectType(subject);\n  const rules = ability.possibleRulesFor(action, subjectType);\n  const uniqueFields = new Set<string>();\n  const deleteItem = uniqueFields.delete.bind(uniqueFields);\n  const addItem = uniqueFields.add.bind(uniqueFields);\n  let i = rules.length;\n\n  while (i--) {\n    const rule = rules[i];\n    if (rule.matchesConditions(subject)) {\n      const toggle = rule.inverted ? deleteItem : addItem;\n      options.fieldsFrom(rule).forEach(toggle);\n    }\n  }\n\n  return Array.from(uniqueFields);\n}\n\nconst joinIfArray = (value: string | string[]) => Array.isArray(value) ? value.join(',') : value;\n\nexport type PackRule<T extends RawRule<any, any>> =\n  [string, string] |\n  [string, string, T['conditions']] |\n  [string, string, T['conditions'] | 0, 1] |\n  [string, string, T['conditions'] | 0, 1 | 0, string] |\n  [string, string, T['conditions'] | 0, 1 | 0, string | 0, string];\n\nexport type PackSubjectType<T extends SubjectType> = (type: T) => string;\n\nexport function packRules<T extends RawRule<any, any>>(\n  rules: T[],\n  packSubject?: PackSubjectType<T['subject']>\n): PackRule<T>[] {\n  return rules.map((rule) => { // eslint-disable-line\n    const packedRule: PackRule<T> = [\n      joinIfArray((rule as any).action || (rule as any).actions),\n      typeof packSubject === 'function'\n        ? wrapArray(rule.subject).map(packSubject).join(',')\n        : joinIfArray(rule.subject),\n      rule.conditions || 0,\n      rule.inverted ? 1 : 0,\n      rule.fields ? joinIfArray(rule.fields) : 0,\n      rule.reason || ''\n    ];\n\n    while (packedRule.length > 0 && !packedRule[packedRule.length - 1]) packedRule.pop();\n\n    return packedRule;\n  });\n}\n\nexport type UnpackSubjectType<T extends SubjectType> = (type: string) => T;\n\nexport function unpackRules<T extends RawRule<any, any>>(\n  rules: PackRule<T>[],\n  unpackSubject?: UnpackSubjectType<T['subject']>\n): T[] {\n  return rules.map(([action, subject, conditions, inverted, fields, reason]) => {\n    const subjects = subject.split(',');\n    const rule = {\n      inverted: !!inverted,\n      action: action.split(','),\n      subject: typeof unpackSubject === 'function'\n        ? subjects.map(unpackSubject)\n        : subjects\n    } as T;\n\n    if (conditions) rule.conditions = conditions;\n    if (fields) rule.fields = fields.split(',');\n    if (reason) rule.reason = reason;\n\n    return rule;\n  });\n}\n"], "names": ["wrapArray", "value", "Array", "isArray", "set<PERSON>y<PERSON><PERSON>", "object", "path", "ref", "last<PERSON>ey", "indexOf", "keys", "split", "pop", "reduce", "res", "prop", "Object", "hasOwn", "prototype", "hasOwnProperty", "call", "bind", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ability", "action", "subjectType", "convert", "query", "rules", "rulesFor", "i", "length", "rule", "op", "inverted", "conditions", "push", "$or", "ruleToAST", "ast", "Error", "JSON", "stringify", "CompoundCondition", "rulesToAST", "$and", "buildOr", "buildAnd", "rulesToFields", "values", "fields", "fieldName", "constructor", "permittedFieldsOf", "subject", "options", "detectSubjectType", "possibleRulesFor", "uniqueFields", "Set", "deleteItem", "delete", "addItem", "add", "matchesConditions", "toggle", "fieldsFrom", "for<PERSON>ach", "from", "joinIfArray", "join", "packRules", "packSubject", "map", "packedRule", "actions", "reason", "unpackRules", "unpackSubject", "_ref", "subjects"], "mappings": "6UAEO,SAASA,EAAaC,GAC3B,OAAOC,MAAMC,QAAQF,GAASA,EAAQ,CAACA,EACzC,CAEO,SAASG,EAAUC,EAAmBC,EAAcL,GACzD,IAAIM,EAAMF,EACV,IAAIG,EAAUF,EAEd,IAA2B,IAAvBA,EAAKG,QAAQ,KAAa,CAC5B,IAAMC,EAAOJ,EAAKK,MAAM,KAExBH,EAAUE,EAAKE,MACfL,EAAMG,EAAKG,QAAO,SAACC,EAAKC,GACtBD,EAAIC,GAAQD,EAAIC,IAAS,CAAA,EACzB,OAAOD,EAAIC,EACZ,GAAEV,EACL,CAEAE,EAAIC,GAAWP,CACjB,CAEwBe,OAAeC,QAClCD,OAAOE,UAAUC,eAAeC,KAAKC,KAAKL,OAAOE,UAAUC,gBCVzD,SAASG,EACdC,EACAC,EACAC,EACAC,GAEA,IAAMC,EAAsB,CAAA,EAC5B,IAAMC,EAAQL,EAAQM,SAASL,EAAQC,GAEvC,IAAK,IAAIK,EAAI,EAAGA,EAAIF,EAAMG,OAAQD,IAAK,CACrC,IAAME,EAAOJ,EAAME,GACnB,IAAMG,EAAKD,EAAKE,SAAW,OAAS,MAEpC,IAAKF,EAAKG,WACR,GAAIH,EAAKE,SACP,UACK,QACEP,EAAMM,GACb,OAAON,CACT,KACK,CACLA,EAAMM,GAAMN,EAAMM,IAAO,GACzBN,EAAMM,GAAKG,KAAKV,EAAQM,GAC1B,CACF,CAEA,OAAOL,EAAMU,IAAMV,EAAQ,IAC7B,CAEA,SAASW,EAAUN,GACjB,IAAKA,EAAKO,IACR,MAAM,IAAIC,MAAuBC,iBAAAA,KAAKC,UAAUV,GAA0E,sEAG5H,OAAOA,EAAKE,SAAW,IAAIS,EAAAA,kBAAkB,MAAO,CAACX,EAAKO,MAAQP,EAAKO,GACzE,CAEO,SAASK,EACdrB,EACAC,EACAC,GAEA,IAAME,EAAQL,EAAaC,EAASC,EAAQC,EAAaa,GAEzD,GAAc,OAAVX,EACF,OAAO,KAGT,IAAKA,EAAMkB,KACT,OAAOlB,EAAMU,IAAMS,UAAQnB,EAAMU,KAAOU,EAAAA,SAAS,IAGnD,GAAIpB,EAAMU,IACRV,EAAMkB,KAAKT,KAAKU,EAAOA,QAACnB,EAAMU,MAGhC,OAAOU,EAAQA,SAACpB,EAAMkB,KACxB,CAEO,SAASG,EACdzB,EACAC,EACAC,GAEA,OAAOF,EAAQM,SAASL,EAAQC,GAC7BZ,QAAO,SAACoC,EAAQjB,GACf,GAAIA,EAAKE,WAAaF,EAAKG,WACzB,OAAOc,EAGT,OAAOjC,OAAON,KAAKsB,EAAKG,YAAYtB,QAAO,SAACqC,EAAQC,GAClD,IAAMlD,EAAQ+B,EAAKG,WAAYgB,GAE/B,IAAKlD,GAAUA,EAAcmD,cAAgBpC,OAC3CZ,EAAU8C,EAAQC,EAAWlD,GAG/B,OAAOiD,CACR,GAAED,EACJ,GAAE,CAAE,EACT,CAQO,SAASI,EACd9B,EACAC,EACA8B,EACAC,GAEA,IAAM9B,EAAcF,EAAQiC,kBAAkBF,GAC9C,IAAM1B,EAAQL,EAAQkC,iBAAiBjC,EAAQC,GAC/C,IAAMiC,EAAe,IAAIC,IACzB,IAAMC,EAAaF,EAAaG,OAAOxC,KAAKqC,GAC5C,IAAMI,EAAUJ,EAAaK,IAAI1C,KAAKqC,GACtC,IAAI5B,EAAIF,EAAMG,OAEd,MAAOD,IAAK,CACV,IAAME,EAAOJ,EAAME,GACnB,GAAIE,EAAKgC,kBAAkBV,GAAU,CACnC,IAAMW,EAASjC,EAAKE,SAAW0B,EAAaE,EAC5CP,EAAQW,WAAWlC,GAAMmC,QAAQF,EACnC,CACF,CAEA,OAAO/D,MAAMkE,KAAKV,EACpB,CAEA,IAAMW,EAAc,SAAdA,EAAepE,GAAwB,OAAKC,MAAMC,QAAQF,GAASA,EAAMqE,KAAK,KAAOrE,CAAK,EAWzF,SAASsE,EACd3C,EACA4C,GAEA,OAAO5C,EAAM6C,KAAI,SAACzC,GAChB,IAAM0C,EAA0B,CAC9BL,EAAarC,EAAaR,QAAWQ,EAAa2C,SAC3B,oBAAhBH,EACHxE,EAAUgC,EAAKsB,SAASmB,IAAID,GAAaF,KAAK,KAC9CD,EAAYrC,EAAKsB,SACrBtB,EAAKG,YAAc,EACnBH,EAAKE,SAAW,EAAI,EACpBF,EAAKkB,OAASmB,EAAYrC,EAAKkB,QAAU,EACzClB,EAAK4C,QAAU,IAGjB,MAAOF,EAAW3C,OAAS,IAAM2C,EAAWA,EAAW3C,OAAS,GAAI2C,EAAW9D,MAE/E,OAAO8D,CACT,GACF,CAIO,SAASG,EACdjD,EACAkD,GAEA,OAAOlD,EAAM6C,KAAI,SAAAM,GAA6D,IAA3DvD,EAAMuD,EAAA,GAAEzB,EAAOyB,EAAA,GAAE5C,EAAU4C,EAAA,GAAE7C,EAAQ6C,EAAA,GAAE7B,EAAM6B,EAAA,GAAEH,EAAMG,EAAA,GACtE,IAAMC,EAAW1B,EAAQ3C,MAAM,KAC/B,IAAMqB,EAAO,CACXE,WAAYA,EACZV,OAAQA,EAAOb,MAAM,KACrB2C,QAAkC,oBAAlBwB,EACZE,EAASP,IAAIK,GACbE,GAGN,GAAI7C,EAAYH,EAAKG,WAAaA,EAClC,GAAIe,EAAQlB,EAAKkB,OAASA,EAAOvC,MAAM,KACvC,GAAIiE,EAAQ5C,EAAK4C,OAASA,EAE1B,OAAO5C,CACT,GACF"}