{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/components/RoleRow.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/Roles/ListPage.tsx"], "sourcesContent": ["import { Box, Flex, IconButton, IconButtonProps, Td, Tr, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport type { AdminRole } from '../../../../../hooks/useAdminRoles';\n\ninterface RoleRowProps extends Pick<AdminRole, 'id' | 'name' | 'description' | 'usersCount'> {\n  icons: Array<Required<Pick<IconButtonProps, 'children' | 'label' | 'onClick'>>>;\n  rowIndex: number;\n  canUpdate?: boolean;\n}\n\nconst RoleRow = ({\n  id,\n  name,\n  description,\n  usersCount,\n  icons,\n  rowIndex,\n  canUpdate,\n}: RoleRowProps) => {\n  const { formatMessage } = useIntl();\n  const [, editObject] = icons;\n\n  const usersCountText = formatMessage(\n    {\n      id: `Roles.RoleRow.user-count`,\n      defaultMessage: '{number, plural, =0 {#  user} one {#  user} other {# users}}',\n    },\n    { number: usersCount }\n  );\n\n  return (\n    <Tr\n      aria-rowindex={rowIndex}\n      key={id}\n      // @ts-expect-error – the prop uses `HTMLButtonElement` but we just specify `HTMLElement`\n      onClick={canUpdate ? editObject.onClick : undefined}\n    >\n      <Td maxWidth={`13rem`}>\n        <Typography ellipsis textColor=\"neutral800\">\n          {name}\n        </Typography>\n      </Td>\n      <Td maxWidth={`25rem`}>\n        <Typography ellipsis textColor=\"neutral800\">\n          {description}\n        </Typography>\n      </Td>\n      <Td>\n        <Typography textColor=\"neutral800\">{usersCountText}</Typography>\n      </Td>\n      <Td>\n        <Flex justifyContent=\"flex-end\" onClick={(e) => e.stopPropagation()}>\n          {icons.map((icon, i) => {\n            if (icon) {\n              return (\n                <Box key={icon.label} paddingLeft={i === 0 ? 0 : 1}>\n                  <IconButton {...icon} variant=\"ghost\" />\n                </Box>\n              );\n            }\n\n            return null;\n          })}\n        </Flex>\n      </Td>\n    </Tr>\n  );\n};\n\nexport { RoleRow };\nexport type { RoleRowProps };\n", "import * as React from 'react';\n\nimport {\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON><PERSON><PERSON>,\n  <PERSON><PERSON>,\n  <PERSON><PERSON>,\n  Tr,\n  <PERSON>po<PERSON>,\n  <PERSON>ly<PERSON><PERSON><PERSON>,\n} from '@strapi/design-system';\nimport { Duplicate, Pencil, Plus, Trash } from '@strapi/icons';\nimport { produce } from 'immer';\nimport { useIntl } from 'react-intl';\nimport { useNavigate } from 'react-router-dom';\n\nimport { ConfirmDialog } from '../../../../components/ConfirmDialog';\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { SearchInput } from '../../../../components/SearchInput';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useAdminRoles, AdminRole } from '../../../../hooks/useAdminRoles';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useFetchClient } from '../../../../hooks/useFetchClient';\nimport { useQueryParams } from '../../../../hooks/useQueryParams';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport { selectAdminPermissions } from '../../../../selectors';\nimport { isFetchError } from '../../../../utils/getFetchClient';\n\nimport { RoleRow, RoleRowProps } from './components/RoleRow';\n\nconst ListPage = () => {\n  const { formatMessage } = useIntl();\n  const permissions = useTypedSelector(selectAdminPermissions);\n  const { formatAPIError } = useAPIErrorHandler();\n  const { toggleNotification } = useNotification();\n  const [isWarningDeleteAllOpened, setIsWarningDeleteAllOpenend] = React.useState(false);\n  const [{ query }] = useQueryParams<{ _q?: string }>();\n  const {\n    isLoading: isLoadingForPermissions,\n    allowedActions: { canCreate, canDelete, canRead, canUpdate },\n  } = useRBAC(permissions.settings?.roles);\n\n  const { roles, refetch: refetchRoles } = useAdminRoles(\n    { filters: query?._q ? { name: { $containsi: query._q } } : undefined },\n    {\n      refetchOnMountOrArgChange: true,\n      skip: isLoadingForPermissions || !canRead,\n    }\n  );\n\n  const navigate = useNavigate();\n  const [{ roleToDelete }, dispatch] = React.useReducer(reducer, initialState);\n  const { post } = useFetchClient();\n\n  const handleDeleteData = async () => {\n    try {\n      dispatch({\n        type: 'ON_REMOVE_ROLES',\n      });\n\n      await post('/admin/roles/batch-delete', {\n        ids: [roleToDelete],\n      });\n\n      await refetchRoles();\n\n      dispatch({\n        type: 'RESET_DATA_TO_DELETE',\n      });\n    } catch (error) {\n      if (isFetchError(error)) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(error),\n        });\n      }\n    }\n  };\n\n  const handleNewRoleClick = () => navigate('new');\n\n  const handleToggleModal = () => setIsWarningDeleteAllOpenend((prev) => !prev);\n\n  const handleClickDelete = (role: AdminRole) => (e: React.MouseEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    if (role.usersCount) {\n      toggleNotification({\n        type: 'info',\n        message: formatMessage({ id: 'Roles.ListPage.notification.delete-not-allowed' }),\n      });\n    } else {\n      dispatch({\n        type: 'SET_ROLE_TO_DELETE',\n        id: role.id,\n      });\n\n      handleToggleModal();\n    }\n  };\n\n  const handleClickDuplicate = (role: AdminRole) => (e: React.MouseEvent<HTMLButtonElement>) => {\n    e.preventDefault();\n    e.stopPropagation();\n\n    navigate(`duplicate/${role.id}`);\n  };\n\n  const rowCount = roles.length + 1;\n  const colCount = 6;\n\n  if (isLoadingForPermissions) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Page.Main>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: 'Roles',\n          }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        primaryAction={\n          canCreate ? (\n            <Button onClick={handleNewRoleClick} startIcon={<Plus />} size=\"S\">\n              {formatMessage({\n                id: 'Settings.roles.list.button.add',\n                defaultMessage: 'Add new role',\n              })}\n            </Button>\n          ) : null\n        }\n        title={formatMessage({\n          id: 'global.roles',\n          defaultMessage: 'roles',\n        })}\n        subtitle={formatMessage({\n          id: 'Settings.roles.list.description',\n          defaultMessage: 'List of roles',\n        })}\n      />\n      {canRead && (\n        <Layouts.Action\n          startActions={\n            <SearchInput\n              label={formatMessage(\n                { id: 'app.component.search.label', defaultMessage: 'Search for {target}' },\n                {\n                  target: formatMessage({\n                    id: 'global.roles',\n                    defaultMessage: 'roles',\n                  }),\n                }\n              )}\n            />\n          }\n        />\n      )}\n      {canRead && (\n        <Layouts.Content>\n          <Table\n            colCount={colCount}\n            rowCount={rowCount}\n            footer={\n              canCreate ? (\n                <TFooter onClick={handleNewRoleClick} icon={<Plus />}>\n                  {formatMessage({\n                    id: 'Settings.roles.list.button.add',\n                    defaultMessage: 'Add new role',\n                  })}\n                </TFooter>\n              ) : null\n            }\n          >\n            <Thead>\n              <Tr aria-rowindex={1}>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.name',\n                      defaultMessage: 'Name',\n                    })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.description',\n                      defaultMessage: 'Description',\n                    })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.users',\n                      defaultMessage: 'Users',\n                    })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <VisuallyHidden>\n                    {formatMessage({\n                      id: 'global.actions',\n                      defaultMessage: 'Actions',\n                    })}\n                  </VisuallyHidden>\n                </Th>\n              </Tr>\n            </Thead>\n            <Tbody>\n              {roles?.map((role, index) => (\n                <RoleRow\n                  key={role.id}\n                  id={role.id}\n                  name={role.name}\n                  description={role.description}\n                  usersCount={role.usersCount}\n                  icons={\n                    [\n                      canCreate &&\n                        ({\n                          onClick: handleClickDuplicate(role),\n                          label: formatMessage({\n                            id: 'app.utils.duplicate',\n                            defaultMessage: 'Duplicate',\n                          }),\n                          children: <Duplicate />,\n                        } satisfies RoleRowProps['icons'][number]),\n                      canUpdate &&\n                        ({\n                          onClick: () => navigate(role.id.toString()),\n                          label: formatMessage({ id: 'app.utils.edit', defaultMessage: 'Edit' }),\n                          children: <Pencil />,\n                        } satisfies RoleRowProps['icons'][number]),\n                      canDelete &&\n                        ({\n                          onClick: handleClickDelete(role),\n                          label: formatMessage({ id: 'global.delete', defaultMessage: 'Delete' }),\n                          children: <Trash />,\n                        } satisfies RoleRowProps['icons'][number]),\n                    ].filter(Boolean) as RoleRowProps['icons']\n                  }\n                  rowIndex={index + 2}\n                  canUpdate={canUpdate}\n                />\n              ))}\n            </Tbody>\n          </Table>\n        </Layouts.Content>\n      )}\n      <Dialog.Root open={isWarningDeleteAllOpened} onOpenChange={handleToggleModal}>\n        <ConfirmDialog onConfirm={handleDeleteData} />\n      </Dialog.Root>\n    </Page.Main>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Reducer\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * TODO: do we actually need this reducer? It's not doing a lot...\n */\n\ninterface State {\n  roleToDelete: null | AdminRole['id'];\n  showModalConfirmButtonLoading: boolean;\n  shouldRefetchData: boolean;\n}\n\nconst initialState = {\n  roleToDelete: null,\n  showModalConfirmButtonLoading: false,\n  shouldRefetchData: false,\n} satisfies State;\n\ninterface SetRoleToDeleteAction extends Pick<AdminRole, 'id'> {\n  type: 'SET_ROLE_TO_DELETE';\n}\n\ninterface ResetDataToDeleteAction {\n  type: 'RESET_DATA_TO_DELETE';\n}\n\ninterface OnRemoveRolesAction {\n  type: 'ON_REMOVE_ROLES';\n}\n\ninterface OnRemoveRolesSucceededAction {\n  type: 'ON_REMOVE_ROLES_SUCCEEDED';\n}\n\ntype Action =\n  | SetRoleToDeleteAction\n  | ResetDataToDeleteAction\n  | OnRemoveRolesAction\n  | OnRemoveRolesSucceededAction;\n\nconst reducer = (state: State, action: Action) =>\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'ON_REMOVE_ROLES': {\n        draftState.showModalConfirmButtonLoading = true;\n        break;\n      }\n      case 'ON_REMOVE_ROLES_SUCCEEDED': {\n        draftState.shouldRefetchData = true;\n        draftState.roleToDelete = null;\n        break;\n      }\n      case 'RESET_DATA_TO_DELETE': {\n        draftState.shouldRefetchData = false;\n        draftState.roleToDelete = null;\n        draftState.showModalConfirmButtonLoading = false;\n        break;\n      }\n      case 'SET_ROLE_TO_DELETE': {\n        draftState.roleToDelete = action.id;\n\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedListPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedListPage = () => {\n  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings?.roles.read);\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedListPage, ListPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWA,IAAM,UAAU,CAAC;EACf;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MAAoB;AACZ,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,CAAA,EAAG,UAAU,IAAI;AAEvB,QAAM,iBAAiB;IACrB;MACE,IAAI;MACJ,gBAAgB;IAAA;IAElB,EAAE,QAAQ,WAAW;EAAA;AAIrB,aAAA;IAAC;IAAA;MACC,iBAAe;MAGf,SAAS,YAAY,WAAW,UAAU;MAE1C,UAAA;YAAC,wBAAA,IAAA,EAAG,UAAU,SACZ,cAAC,wBAAA,YAAA,EAAW,UAAQ,MAAC,WAAU,cAC5B,UAAA,KAAA,CACH,EACF,CAAA;YACA,wBAAC,IAAG,EAAA,UAAU,SACZ,cAAA,wBAAC,YAAW,EAAA,UAAQ,MAAC,WAAU,cAC5B,UAAA,YAAA,CACH,EACF,CAAA;YAAA,wBACC,IACC,EAAA,cAAA,wBAAC,YAAA,EAAW,WAAU,cAAc,UAAA,eAAA,CAAe,EACrD,CAAA;YAAA,wBACC,IACC,EAAA,cAAA,wBAAC,MAAK,EAAA,gBAAe,YAAW,SAAS,CAAC,MAAM,EAAE,gBAAA,GAC/C,UAAA,MAAM,IAAI,CAAC,MAAM,MAAM;AACtB,cAAI,MAAM;AACR,uBACG,wBAAA,KAAA,EAAqB,aAAa,MAAM,IAAI,IAAI,GAC/C,cAAC,wBAAA,YAAA,EAAY,GAAG,MAAM,SAAQ,QAAQ,CAAA,EAAA,GAD9B,KAAK,KAEf;UAAA;AAIG,iBAAA;QAAA,CACR,EAAA,CACH,EACF,CAAA;MAAA;IAAA;IA/BK;EAAA;AAkCX;ACjCA,IAAM,WAAW,MAAM;;AACf,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,cAAc,iBAAiB,sBAAsB;AACrD,QAAA,EAAE,eAAe,IAAI,mBAAmB;AACxC,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,CAAC,0BAA0B,4BAA4B,IAAU,eAAS,KAAK;AACrF,QAAM,CAAC,EAAE,MAAM,CAAC,IAAI,eAAgC;AAC9C,QAAA;IACJ,WAAW;IACX,gBAAgB,EAAE,WAAW,WAAW,SAAS,UAAU;EAAA,IACzD,SAAQ,iBAAY,aAAZ,mBAAsB,KAAK;AAEvC,QAAM,EAAE,OAAO,SAAS,aAAA,IAAiB;IACvC,EAAE,UAAS,+BAAO,MAAK,EAAE,MAAM,EAAE,YAAY,MAAM,GAAA,EAAK,IAAI,OAAU;IACtE;MACE,2BAA2B;MAC3B,MAAM,2BAA2B,CAAC;IAAA;EACpC;AAGF,QAAM,WAAW,YAAY;AACvB,QAAA,CAAC,EAAE,aAAa,GAAG,QAAQ,IAAU,iBAAW,SAAS,YAAY;AACrE,QAAA,EAAE,KAAK,IAAI,eAAe;AAEhC,QAAM,mBAAmB,YAAY;AAC/B,QAAA;AACO,eAAA;QACP,MAAM;MAAA,CACP;AAED,YAAM,KAAK,6BAA6B;QACtC,KAAK,CAAC,YAAY;MAAA,CACnB;AAED,YAAM,aAAa;AAEV,eAAA;QACP,MAAM;MAAA,CACP;IAAA,SACM,OAAO;AACV,UAAA,aAAa,KAAK,GAAG;AACJ,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,KAAK;QAAA,CAC9B;MAAA;IACH;EACF;AAGI,QAAA,qBAAqB,MAAM,SAAS,KAAK;AAE/C,QAAM,oBAAoB,MAAM,6BAA6B,CAAC,SAAS,CAAC,IAAI;AAE5E,QAAM,oBAAoB,CAAC,SAAoB,CAAC,MAA2C;AACzF,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAElB,QAAI,KAAK,YAAY;AACA,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,iDAAA,CAAkD;MAAA,CAChF;IAAA,OACI;AACI,eAAA;QACP,MAAM;QACN,IAAI,KAAK;MAAA,CACV;AAEiB,wBAAA;IAAA;EACpB;AAGF,QAAM,uBAAuB,CAAC,SAAoB,CAAC,MAA2C;AAC5F,MAAE,eAAe;AACjB,MAAE,gBAAgB;AAET,aAAA,aAAa,KAAK,EAAE,EAAE;EAAA;AAG3B,QAAA,WAAW,MAAM,SAAS;AAChC,QAAM,WAAW;AAEjB,MAAI,yBAAyB;AACpB,eAAA,wBAAC,KAAK,SAAL,CAAA,CAAa;EAAA;AAIrB,aAAA,yBAAC,KAAK,MAAL,EACC,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM;MAAA;IACR,EAEJ,CAAA;QACA;MAAC,QAAQ;MAAR;QACC,eACE,gBACG,wBAAA,QAAA,EAAO,SAAS,oBAAoB,eAAW,wBAAC,eAAK,CAAA,CAAA,GAAI,MAAK,KAC5D,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EAAA,CACH,IACE;QAEN,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IAAA;IAEF,eACC;MAAC,QAAQ;MAAR;QACC,kBACE;UAAC;UAAA;YACC,OAAO;cACL,EAAE,IAAI,8BAA8B,gBAAgB,sBAAsB;cAC1E;gBACE,QAAQ,cAAc;kBACpB,IAAI;kBACJ,gBAAgB;gBAAA,CACjB;cAAA;YACH;UACF;QAAA;MACF;IAAA;IAIL,eACC,wBAAC,QAAQ,SAAR,EACC,cAAA;MAAC;MAAA;QACC;QACA;QACA,QACE,gBACE,wBAAC,SAAQ,EAAA,SAAS,oBAAoB,UAAM,wBAAC,eAAK,CAAA,CAAA,GAC/C,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EAAA,CACH,IACE;QAGN,UAAA;cAAA,wBAAC,OACC,EAAA,cAAA,yBAAC,IAAG,EAAA,iBAAe,GACjB,UAAA;gBAAA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EAAA,CACH,EACF,CAAA;gBACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EAAA,CACH,EACF,CAAA;gBACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EAAA,CACH,EACF,CAAA;gBACC,wBAAA,IAAA,EACC,cAAC,wBAAA,gBAAA,EACE,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EAAA,CACH,EACF,CAAA;UAAA,EAAA,CACF,EACF,CAAA;cAAA,wBACC,OACE,EAAA,UAAA,+BAAO,IAAI,CAAC,MAAM,cACjB;YAAC;YAAA;cAEC,IAAI,KAAK;cACT,MAAM,KAAK;cACX,aAAa,KAAK;cAClB,YAAY,KAAK;cACjB,OACE;gBACE,aACG;kBACC,SAAS,qBAAqB,IAAI;kBAClC,OAAO,cAAc;oBACnB,IAAI;oBACJ,gBAAgB;kBAAA,CACjB;kBACD,cAAA,wBAAW,eAAU,CAAA,CAAA;gBAAA;gBAEzB,aACG;kBACC,SAAS,MAAM,SAAS,KAAK,GAAG,SAAA,CAAU;kBAC1C,OAAO,cAAc,EAAE,IAAI,kBAAkB,gBAAgB,OAAA,CAAQ;kBACrE,cAAA,wBAAW,eAAO,CAAA,CAAA;gBAAA;gBAEtB,aACG;kBACC,SAAS,kBAAkB,IAAI;kBAC/B,OAAO,cAAc,EAAE,IAAI,iBAAiB,gBAAgB,SAAA,CAAU;kBACtE,cAAA,wBAAW,cAAM,CAAA,CAAA;gBAAA;cACnB,EACF,OAAO,OAAO;cAElB,UAAU,QAAQ;cAClB;YAAA;YA/BK,KAAK;UAAA,GAkChB,CAAA;QAAA;MAAA;IAAA,EAEJ,CAAA;QAED,wBAAA,OAAO,MAAP,EAAY,MAAM,0BAA0B,cAAc,mBACzD,cAAC,wBAAA,eAAA,EAAc,WAAW,iBAAA,CAAkB,EAC9C,CAAA;EAAA,EACF,CAAA;AAEJ;AAgBA,IAAM,eAAe;EACnB,cAAc;EACd,+BAA+B;EAC/B,mBAAmB;AACrB;AAwBA,IAAM,UAAU,CAAC,OAAc,WAC7B,GAAQ,OAAO,CAAC,eAAe;AAC7B,UAAQ,OAAO,MAAM;IACnB,KAAK,mBAAmB;AACtB,iBAAW,gCAAgC;AAC3C;IAAA;IAEF,KAAK,6BAA6B;AAChC,iBAAW,oBAAoB;AAC/B,iBAAW,eAAe;AAC1B;IAAA;IAEF,KAAK,wBAAwB;AAC3B,iBAAW,oBAAoB;AAC/B,iBAAW,eAAe;AAC1B,iBAAW,gCAAgC;AAC3C;IAAA;IAEF,KAAK,sBAAsB;AACzB,iBAAW,eAAe,OAAO;AAEjC;IAAA;IAEF;AACS,aAAA;EAAA;AAEb,CAAC;AAMH,IAAM,oBAAoB,MAAM;AACxB,QAAA,cAAc,iBAAiB,CAAC,UAAA;;AAAU,uBAAM,UAAU,YAAY,aAA5B,mBAAsC,MAAM;GAAI;AAEhG,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,UAAA,CAAA,CAAS,EACZ,CAAA;AAEJ;", "names": []}