{"version": 3, "sources": ["../../../lodash/_baseIndexOfWith.js", "../../../lodash/_basePullAll.js", "../../../lodash/pullAll.js", "../../../lodash/pull.js", "../../../@strapi/admin/admin/src/services/contentApi.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/apiTokenPermissions.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/FormApiTokenContainer.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/BoundRoute.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/ActionBoundRoutes.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/CollapsableContentType.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/ContentTypesSection.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/components/Permissions.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/constants.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/utils/transformPermissionsData.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/reducer.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/EditView/EditViewPage.tsx"], "sourcesContent": ["/**\n * This function is like `baseIndexOf` except that it accepts a comparator.\n *\n * @private\n * @param {Array} array The array to inspect.\n * @param {*} value The value to search for.\n * @param {number} fromIndex The index to search from.\n * @param {Function} comparator The comparator invoked per element.\n * @returns {number} Returns the index of the matched value, else `-1`.\n */\nfunction baseIndexOfWith(array, value, fromIndex, comparator) {\n  var index = fromIndex - 1,\n      length = array.length;\n\n  while (++index < length) {\n    if (comparator(array[index], value)) {\n      return index;\n    }\n  }\n  return -1;\n}\n\nmodule.exports = baseIndexOfWith;\n", "var arrayMap = require('./_arrayMap'),\n    baseIndexOf = require('./_baseIndexOf'),\n    baseIndexOfWith = require('./_baseIndexOfWith'),\n    baseUnary = require('./_baseUnary'),\n    copyArray = require('./_copyArray');\n\n/** Used for built-in method references. */\nvar arrayProto = Array.prototype;\n\n/** Built-in value references. */\nvar splice = arrayProto.splice;\n\n/**\n * The base implementation of `_.pullAllBy` without support for iteratee\n * shorthands.\n *\n * @private\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @param {Function} [iteratee] The iteratee invoked per element.\n * @param {Function} [comparator] The comparator invoked per element.\n * @returns {Array} Returns `array`.\n */\nfunction basePullAll(array, values, iteratee, comparator) {\n  var indexOf = comparator ? baseIndexOfWith : baseIndexOf,\n      index = -1,\n      length = values.length,\n      seen = array;\n\n  if (array === values) {\n    values = copyArray(values);\n  }\n  if (iteratee) {\n    seen = arrayMap(array, baseUnary(iteratee));\n  }\n  while (++index < length) {\n    var fromIndex = 0,\n        value = values[index],\n        computed = iteratee ? iteratee(value) : value;\n\n    while ((fromIndex = indexOf(seen, computed, fromIndex, comparator)) > -1) {\n      if (seen !== array) {\n        splice.call(seen, fromIndex, 1);\n      }\n      splice.call(array, fromIndex, 1);\n    }\n  }\n  return array;\n}\n\nmodule.exports = basePullAll;\n", "var basePullAll = require('./_basePullAll');\n\n/**\n * This method is like `_.pull` except that it accepts an array of values to remove.\n *\n * **Note:** Unlike `_.difference`, this method mutates `array`.\n *\n * @static\n * @memberOf _\n * @since 4.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {Array} values The values to remove.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = ['a', 'b', 'c', 'a', 'b', 'c'];\n *\n * _.pullAll(array, ['a', 'c']);\n * console.log(array);\n * // => ['b', 'b']\n */\nfunction pullAll(array, values) {\n  return (array && array.length && values && values.length)\n    ? basePullAll(array, values)\n    : array;\n}\n\nmodule.exports = pullAll;\n", "var baseRest = require('./_baseRest'),\n    pullAll = require('./pullAll');\n\n/**\n * Removes all given values from `array` using\n * [`SameValueZero`](http://ecma-international.org/ecma-262/7.0/#sec-samevaluezero)\n * for equality comparisons.\n *\n * **Note:** Unlike `_.without`, this method mutates `array`. Use `_.remove`\n * to remove elements from an array by predicate.\n *\n * @static\n * @memberOf _\n * @since 2.0.0\n * @category Array\n * @param {Array} array The array to modify.\n * @param {...*} [values] The values to remove.\n * @returns {Array} Returns `array`.\n * @example\n *\n * var array = ['a', 'b', 'c', 'a', 'b', 'c'];\n *\n * _.pull(array, 'a', 'c');\n * console.log(array);\n * // => ['b', 'b']\n */\nvar pull = baseRest(pullAll);\n\nmodule.exports = pull;\n", "import { adminApi } from './api';\n\nimport type { List as ListContentApiPermissions } from '../../../shared/contracts/content-api/permissions';\nimport type { List as ListContentApiRoutes } from '../../../shared/contracts/content-api/routes';\n\nconst contentApiService = adminApi.injectEndpoints({\n  endpoints: (builder) => ({\n    getPermissions: builder.query<ListContentApiPermissions.Response['data'], void>({\n      query: () => '/admin/content-api/permissions',\n      transformResponse: (response: ListContentApiPermissions.Response) => response.data,\n    }),\n    getRoutes: builder.query<ListContentApiRoutes.Response['data'], void>({\n      query: () => '/admin/content-api/routes',\n      transformResponse: (response: ListContentApiRoutes.Response) => response.data,\n    }),\n  }),\n  overrideExisting: false,\n});\n\nconst { useGetPermissionsQuery, useGetRoutesQuery } = contentApiService;\n\nexport { useGetPermissionsQuery, useGetRoutesQuery };\n", "/* eslint-disable check-file/filename-naming-convention */\n\nimport * as React from 'react';\n\nimport { createContext } from '@radix-ui/react-context';\n\nimport { List as ListContentApiPermissions } from '../../../../../../../shared/contracts/content-api/permissions';\nimport { List as ListContentApiRoutes } from '../../../../../../../shared/contracts/content-api/routes';\n\nexport interface PseudoEvent {\n  target: { value: string };\n}\n\ninterface ApiTokenPermissionsContextValue {\n  value: {\n    selectedAction: string | null;\n    routes: ListContentApiRoutes.Response['data'];\n    selectedActions: string[];\n    data: {\n      allActionsIds: string[];\n      permissions: ListContentApiPermissions.Response['data'][];\n    };\n    onChange: ({ target: { value } }: PseudoEvent) => void;\n    onChangeSelectAll: ({\n      target: { value },\n    }: {\n      target: { value: { action: string; actionId: string }[] };\n    }) => void;\n    setSelectedAction: ({ target: { value } }: PseudoEvent) => void;\n  };\n}\n\ninterface ApiTokenPermissionsContextProviderProps extends ApiTokenPermissionsContextValue {\n  children: React.ReactNode | React.ReactNode[];\n}\n\nconst [ApiTokenPermissionsContextProvider, useApiTokenPermissionsContext] =\n  createContext<ApiTokenPermissionsContextValue>('ApiTokenPermissionsContext');\n\nconst ApiTokenPermissionsProvider = ({\n  children,\n  ...rest\n}: ApiTokenPermissionsContextProviderProps) => {\n  return (\n    <ApiTokenPermissionsContextProvider {...rest}>{children}</ApiTokenPermissionsContextProvider>\n  );\n};\n\nconst useApiTokenPermissions = () => useApiTokenPermissionsContext('useApiTokenPermissions');\n\nexport { ApiTokenPermissionsProvider, useApiTokenPermissions };\nexport type { ApiTokenPermissionsContextValue, ApiTokenPermissionsContextProviderProps };\n", "import * as React from 'react';\n\nimport { Box, Flex, Grid, Typography } from '@strapi/design-system';\nimport { FormikErrors } from 'formik';\nimport { useIntl } from 'react-intl';\n\nimport { LifeSpanInput } from '../../../../components/Tokens/LifeSpanInput';\nimport { TokenDescription } from '../../../../components/Tokens/TokenDescription';\nimport { TokenName } from '../../../../components/Tokens/TokenName';\nimport { TokenTypeSelect } from '../../../../components/Tokens/TokenTypeSelect';\n\nimport type { ApiToken } from '../../../../../../../../shared/contracts/api-token';\n\ninterface FormApiTokenContainerProps {\n  errors?: FormikErrors<Pick<ApiToken, 'name' | 'description' | 'lifespan' | 'type'>>;\n  onChange: ({ target: { name, value } }: { target: { name: string; value: string } }) => void;\n  canEditInputs: boolean;\n  values?: Partial<Pick<ApiToken, 'name' | 'description' | 'lifespan' | 'type'>>;\n  isCreating: boolean;\n  apiToken?: null | Partial<ApiToken>;\n  onDispatch: React.Dispatch<any>;\n  setHasChangedPermissions: (hasChanged: boolean) => void;\n}\n\nexport const FormApiTokenContainer = ({\n  errors = {},\n  onChange,\n  canEditInputs,\n  isCreating,\n  values = {},\n  apiToken = {},\n  onDispatch,\n  setHasChangedPermissions,\n}: FormApiTokenContainerProps) => {\n  const { formatMessage } = useIntl();\n\n  const handleChangeSelectApiTokenType = ({ target: { value } }: { target: { value: string } }) => {\n    setHasChangedPermissions(false);\n\n    if (value === 'full-access') {\n      onDispatch({\n        type: 'SELECT_ALL_ACTIONS',\n      });\n    }\n    if (value === 'read-only') {\n      onDispatch({\n        type: 'ON_CHANGE_READ_ONLY',\n      });\n    }\n  };\n\n  const typeOptions = [\n    {\n      value: 'read-only',\n      label: {\n        id: 'Settings.tokens.types.read-only',\n        defaultMessage: 'Read-only',\n      },\n    },\n    {\n      value: 'full-access',\n      label: {\n        id: 'Settings.tokens.types.full-access',\n        defaultMessage: 'Full access',\n      },\n    },\n    {\n      value: 'custom',\n      label: {\n        id: 'Settings.tokens.types.custom',\n        defaultMessage: 'Custom',\n      },\n    },\n  ];\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"filterShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n        <Typography variant=\"delta\" tag=\"h2\">\n          {formatMessage({\n            id: 'global.details',\n            defaultMessage: 'Details',\n          })}\n        </Typography>\n        <Grid.Root gap={5}>\n          <Grid.Item key=\"name\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <TokenName\n              error={errors['name']}\n              value={values['name']}\n              canEditInputs={canEditInputs}\n              onChange={onChange}\n            />\n          </Grid.Item>\n          <Grid.Item key=\"description\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <TokenDescription\n              error={errors['description']}\n              value={values['description']}\n              canEditInputs={canEditInputs}\n              onChange={onChange}\n            />\n          </Grid.Item>\n          <Grid.Item key=\"lifespan\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <LifeSpanInput\n              isCreating={isCreating}\n              error={errors['lifespan']}\n              value={values['lifespan']}\n              onChange={onChange}\n              token={apiToken}\n            />\n          </Grid.Item>\n\n          <Grid.Item key=\"type\" col={6} xs={12} direction=\"column\" alignItems=\"stretch\">\n            <TokenTypeSelect\n              value={values['type']}\n              error={errors['type']}\n              label={{\n                id: 'Settings.tokens.form.type',\n                defaultMessage: 'Token type',\n              }}\n              onChange={(value) => {\n                // @ts-expect-error – DS Select supports numbers & strings, will be removed in V2\n                handleChangeSelectApiTokenType({ target: { value } });\n\n                // @ts-expect-error – DS Select supports numbers & strings, will be removed in V2\n                onChange({ target: { name: 'type', value } });\n              }}\n              options={typeOptions}\n              canEditInputs={canEditInputs}\n            />\n          </Grid.Item>\n        </Grid.Root>\n      </Flex>\n    </Box>\n  );\n};\n", "import React from 'react';\n\nimport { Box, BoxComponent, Flex, Typography } from '@strapi/design-system';\nimport map from 'lodash/map';\nimport tail from 'lodash/tail';\nimport { useIntl } from 'react-intl';\nimport { styled, DefaultTheme } from 'styled-components';\n\ntype HttpVerb = 'POST' | 'GET' | 'PUT' | 'DELETE';\n\ntype MethodColor = {\n  text: keyof DefaultTheme['colors'];\n  border: keyof DefaultTheme['colors'];\n  background: keyof DefaultTheme['colors'];\n};\n\nconst getMethodColor = (verb: HttpVerb): MethodColor => {\n  switch (verb) {\n    case 'POST': {\n      return {\n        text: 'success600',\n        border: 'success200',\n        background: 'success100',\n      };\n    }\n    case 'GET': {\n      return {\n        text: 'secondary600',\n        border: 'secondary200',\n        background: 'secondary100',\n      };\n    }\n    case 'PUT': {\n      return {\n        text: 'warning600',\n        border: 'warning200',\n        background: 'warning100',\n      };\n    }\n    case 'DELETE': {\n      return {\n        text: 'danger600',\n        border: 'danger200',\n        background: 'danger100',\n      };\n    }\n    default: {\n      return {\n        text: 'neutral600',\n        border: 'neutral200',\n        background: 'neutral100',\n      };\n    }\n  }\n};\n\nconst MethodBox = styled<BoxComponent>(Box)`\n  margin: -1px;\n  border-radius: ${({ theme }) => theme.spaces[1]} 0 0 ${({ theme }) => theme.spaces[1]};\n`;\n\ninterface BoundRouteProps {\n  route: {\n    handler: string;\n    method: HttpVerb;\n    path: string;\n  };\n}\n\nexport const BoundRoute = ({\n  route = {\n    handler: 'Nocontroller.error',\n    method: 'GET',\n    path: '/there-is-no-path',\n  },\n}: BoundRouteProps) => {\n  const { formatMessage } = useIntl();\n\n  const { method, handler: title, path } = route;\n  const formattedRoute = path ? tail(path.split('/')) : [];\n  const [controller = '', action = ''] = title ? title.split('.') : [];\n  const colors = getMethodColor(route.method);\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n      <Typography variant=\"delta\" tag=\"h3\">\n        {formatMessage({\n          id: 'Settings.apiTokens.createPage.BoundRoute.title',\n          defaultMessage: 'Bound route to',\n        })}\n        &nbsp;\n        <span>{controller}</span>\n        <Typography variant=\"delta\" textColor=\"primary600\">\n          .{action}\n        </Typography>\n      </Typography>\n      <Flex hasRadius background=\"neutral0\" borderColor=\"neutral200\" gap={0}>\n        <MethodBox background={colors.background} borderColor={colors.border} padding={2}>\n          <Typography fontWeight=\"bold\" textColor={colors.text}>\n            {method}\n          </Typography>\n        </MethodBox>\n        <Box paddingLeft={2} paddingRight={2}>\n          {map(formattedRoute, (value) => (\n            <Typography key={value} textColor={value.includes(':') ? 'neutral600' : 'neutral900'}>\n              /{value}\n            </Typography>\n          ))}\n        </Box>\n      </Flex>\n    </Flex>\n  );\n};\n", "import { Grid, Flex, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useApiTokenPermissions } from '../apiTokenPermissions';\n\nimport { BoundRoute } from './BoundRoute';\n\nexport const ActionBoundRoutes = () => {\n  const {\n    value: { selectedAction, routes },\n  } = useApiTokenPermissions();\n  const { formatMessage } = useIntl();\n  const actionSection = selectedAction?.split('.')[0];\n\n  return (\n    <Grid.Item\n      col={5}\n      background=\"neutral150\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n      style={{ minHeight: '100%' }}\n      direction=\"column\"\n      alignItems=\"stretch\"\n    >\n      {selectedAction ? (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          {actionSection &&\n            actionSection in routes &&\n            routes[actionSection].map((route) => {\n              return route.config.auth?.scope?.includes(selectedAction) ||\n                route.handler === selectedAction ? (\n                <BoundRoute key={route.handler} route={route} />\n              ) : null;\n            })}\n        </Flex>\n      ) : (\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          <Typography variant=\"delta\" tag=\"h3\">\n            {formatMessage({\n              id: 'Settings.apiTokens.createPage.permissions.header.title',\n              defaultMessage: 'Advanced settings',\n            })}\n          </Typography>\n          <Typography tag=\"p\" textColor=\"neutral600\">\n            {formatMessage({\n              id: 'Settings.apiTokens.createPage.permissions.header.hint',\n              defaultMessage:\n                \"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route\",\n            })}\n          </Typography>\n        </Flex>\n      )}\n    </Grid.Item>\n  );\n};\n", "import * as React from 'react';\n\nimport {\n  Accordion,\n  Box,\n  BoxComponent,\n  Checkbox,\n  Flex,\n  Grid,\n  Typography,\n} from '@strapi/design-system';\nimport { Cog } from '@strapi/icons';\nimport capitalize from 'lodash/capitalize';\nimport { useIntl } from 'react-intl';\nimport { styled, css } from 'styled-components';\n\nimport { ContentApiPermission } from '../../../../../../../../shared/contracts/content-api/permissions';\nimport { useApiTokenPermissions } from '../apiTokenPermissions';\n\nconst activeCheckboxWrapperStyles = css`\n  background: ${(props) => props.theme.colors.primary100};\n\n  #cog {\n    opacity: 1;\n  }\n`;\n\nconst CheckboxWrapper = styled<BoxComponent>(Box)<{ $isActive: boolean }>`\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n\n  #cog {\n    opacity: 0;\n    path {\n      fill: ${(props) => props.theme.colors.primary600};\n    }\n  }\n\n  /* Show active style both on hover and when the action is selected */\n  ${(props) => props.$isActive && activeCheckboxWrapperStyles}\n  &:hover {\n    ${activeCheckboxWrapperStyles}\n  }\n`;\n\nconst Border = styled.div`\n  flex: 1;\n  align-self: center;\n  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};\n`;\n\ninterface CollapsableContentTypeProps {\n  controllers?: ContentApiPermission['controllers'];\n  label: ContentApiPermission['label'];\n  orderNumber?: number;\n  disabled?: boolean;\n}\n\nexport const CollapsableContentType = ({\n  controllers = [],\n  label,\n  orderNumber = 0,\n  disabled = false,\n}: CollapsableContentTypeProps) => {\n  const {\n    value: { onChangeSelectAll, onChange, selectedActions, setSelectedAction, selectedAction },\n  } = useApiTokenPermissions();\n  const { formatMessage } = useIntl();\n\n  const isActionSelected = (actionId: string) => actionId === selectedAction;\n\n  return (\n    <Accordion.Item value={`${label}-${orderNumber}`}>\n      <Accordion.Header variant={orderNumber % 2 ? 'primary' : 'secondary'}>\n        <Accordion.Trigger>{capitalize(label)}</Accordion.Trigger>\n      </Accordion.Header>\n      <Accordion.Content>\n        {controllers?.map((controller) => {\n          const allActionsSelected = controller.actions.every((action) =>\n            selectedActions.includes(action.actionId)\n          );\n\n          const someActionsSelected = controller.actions.some((action) =>\n            selectedActions.includes(action.actionId)\n          );\n\n          return (\n            <Box key={`${label}.${controller?.controller}`}>\n              <Flex justifyContent=\"space-between\" alignItems=\"center\" padding={4}>\n                <Box paddingRight={4}>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {controller?.controller}\n                  </Typography>\n                </Box>\n                <Border />\n                <Box paddingLeft={4}>\n                  <Checkbox\n                    checked={\n                      !allActionsSelected && someActionsSelected\n                        ? 'indeterminate'\n                        : allActionsSelected\n                    }\n                    onCheckedChange={() => {\n                      onChangeSelectAll({ target: { value: [...controller.actions] } });\n                    }}\n                    disabled={disabled}\n                  >\n                    {formatMessage({ id: 'app.utils.select-all', defaultMessage: 'Select all' })}\n                  </Checkbox>\n                </Box>\n              </Flex>\n              <Grid.Root gap={4} padding={4}>\n                {controller?.actions &&\n                  controller?.actions.map((action) => {\n                    return (\n                      <Grid.Item\n                        col={6}\n                        key={action.actionId}\n                        direction=\"column\"\n                        alignItems=\"stretch\"\n                      >\n                        <CheckboxWrapper\n                          $isActive={isActionSelected(action.actionId)}\n                          padding={2}\n                          hasRadius\n                        >\n                          <Checkbox\n                            checked={selectedActions.includes(action.actionId)}\n                            name={action.actionId}\n                            onCheckedChange={() => {\n                              onChange({ target: { value: action.actionId } });\n                            }}\n                            disabled={disabled}\n                          >\n                            {action.action}\n                          </Checkbox>\n                          <button\n                            type=\"button\"\n                            data-testid=\"action-cog\"\n                            onClick={() =>\n                              setSelectedAction({ target: { value: action.actionId } })\n                            }\n                            style={{ display: 'inline-flex', alignItems: 'center' }}\n                          >\n                            <Cog id=\"cog\" />\n                          </button>\n                        </CheckboxWrapper>\n                      </Grid.Item>\n                    );\n                  })}\n              </Grid.Root>\n            </Box>\n          );\n        })}\n      </Accordion.Content>\n    </Accordion.Item>\n  );\n};\n", "import { Accordion, Box } from '@strapi/design-system';\n\nimport { ContentApiPermission } from '../../../../../../../../shared/contracts/content-api/permissions';\n\nimport { CollapsableContentType } from './CollapsableContentType';\n\ninterface ContentTypesSectionProps {\n  section: ContentApiPermission[] | null;\n}\n\nexport const ContentTypesSection = ({ section = null, ...props }: ContentTypesSectionProps) => {\n  return (\n    <Box padding={4} background=\"neutral0\">\n      <Accordion.Root size=\"M\">\n        {section &&\n          section.map((api, index) => (\n            <CollapsableContentType\n              key={api.apiId}\n              label={api.label}\n              controllers={api.controllers}\n              orderNumber={index}\n              {...props}\n            />\n          ))}\n      </Accordion.Root>\n    </Box>\n  );\n};\n", "import { Flex, Grid, Typography } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { useApiTokenPermissions } from '../apiTokenPermissions';\n\nimport { ActionBoundRoutes } from './ActionBoundRoutes';\nimport { ContentTypesSection } from './ContentTypesSection';\n\nexport const Permissions = ({ ...props }) => {\n  const {\n    value: { data },\n  } = useApiTokenPermissions();\n  const { formatMessage } = useIntl();\n\n  return (\n    <Grid.Root gap={0} shadow=\"filterShadow\" hasRadius background=\"neutral0\">\n      <Grid.Item\n        col={7}\n        paddingTop={6}\n        paddingBottom={6}\n        paddingLeft={7}\n        paddingRight={7}\n        direction=\"column\"\n        alignItems=\"stretch\"\n      >\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n          <Typography variant=\"delta\" tag=\"h2\">\n            {formatMessage({\n              id: 'Settings.apiTokens.createPage.permissions.title',\n              defaultMessage: 'Permissions',\n            })}\n          </Typography>\n          <Typography tag=\"p\" textColor=\"neutral600\">\n            {formatMessage({\n              id: 'Settings.apiTokens.createPage.permissions.description',\n              defaultMessage: 'Only actions bound by a route are listed below.',\n            })}\n          </Typography>\n        </Flex>\n        {data?.permissions && <ContentTypesSection section={data?.permissions} {...props} />}\n      </Grid.Item>\n      <ActionBoundRoutes />\n    </Grid.Root>\n  );\n};\n", "import * as yup from 'yup';\n\nimport { translatedErrors } from '../../../../../utils/translatedErrors';\n\nexport const schema = yup.object().shape({\n  name: yup.string().max(100).required(translatedErrors.required.id),\n  type: yup\n    .string()\n    .oneOf(['read-only', 'full-access', 'custom'])\n    .required(translatedErrors.required.id),\n  description: yup.string().nullable(),\n  lifespan: yup.number().integer().min(0).nullable().defined(translatedErrors.required.id),\n});\n", "import { ContentApiPermission } from '../../../../../../../../shared/contracts/content-api/permissions';\n\ninterface Layout {\n  allActionsIds: string[];\n  permissions: {\n    apiId: string;\n    label: string;\n    controllers: { controller: string; actions: { action: string; actionId: string }[] }[];\n  }[];\n}\n\nexport const transformPermissionsData = (data: ContentApiPermission) => {\n  const layout: Layout = {\n    allActionsIds: [],\n    permissions: [],\n  };\n\n  layout.permissions = Object.entries(data).map(([apiId, permission]) => ({\n    apiId,\n    label: apiId.split('::')[1],\n    controllers: Object.keys(permission.controllers)\n      .map((controller) => ({\n        controller,\n        actions:\n          controller in permission.controllers\n            ? permission.controllers[controller]\n                .map((action: ContentApiPermission['controllers']) => {\n                  const actionId = `${apiId}.${controller}.${action}`;\n\n                  if (apiId.includes('api::')) {\n                    layout.allActionsIds.push(actionId);\n                  }\n\n                  return {\n                    action,\n                    actionId,\n                  };\n                })\n                .flat()\n            : [],\n      }))\n      .flat(),\n  }));\n\n  return layout;\n};\n", "/* eslint-disable consistent-return */\nimport { produce } from 'immer';\nimport pull from 'lodash/pull';\n\nimport { ContentApiPermission } from '../../../../../../../shared/contracts/content-api/permissions';\n\nimport { ApiTokenPermissionsContextValue } from './apiTokenPermissions';\nimport { transformPermissionsData } from './utils/transformPermissionsData';\n\ntype InitialState = Pick<\n  ApiTokenPermissionsContextValue['value'],\n  'data' | 'routes' | 'selectedAction' | 'selectedActions'\n>;\n\ninterface ActionOnChange {\n  type: 'ON_CHANGE';\n  value: string;\n}\n\ninterface ActionSelectAllInPermission {\n  type: 'SELECT_ALL_IN_PERMISSION';\n  value: { action: string; actionId: string }[];\n}\n\ninterface ActionSelectAllActions {\n  type: 'SELECT_ALL_ACTIONS';\n}\n\ninterface ActionOnChangeReadOnly {\n  type: 'ON_CHANGE_READ_ONLY';\n}\n\ninterface ActionUpdatePermissionsLayout {\n  type: 'UPDATE_PERMISSIONS_LAYOUT';\n  value: ContentApiPermission;\n}\n\ninterface ActionUpdateRoutes {\n  type: 'UPDATE_ROUTES';\n  value: ApiTokenPermissionsContextValue['value']['routes'] | undefined;\n}\n\ninterface ActionUpdatePermissions {\n  type: 'UPDATE_PERMISSIONS';\n  value: any[];\n}\n\ninterface ActionSetSelectedAction {\n  type: 'SET_SELECTED_ACTION';\n  value: string;\n}\n\ntype Action =\n  | ActionOnChange\n  | ActionSelectAllInPermission\n  | ActionSelectAllActions\n  | ActionOnChangeReadOnly\n  | ActionUpdatePermissionsLayout\n  | ActionUpdateRoutes\n  | ActionUpdatePermissions\n  | ActionSetSelectedAction;\n\nexport const initialState: InitialState = {\n  data: {\n    allActionsIds: [],\n    permissions: [],\n  },\n  routes: {},\n  selectedAction: '',\n  selectedActions: [],\n};\n\nexport const reducer = (state: InitialState, action: Action) =>\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'ON_CHANGE': {\n        if (draftState.selectedActions.includes(action.value)) {\n          pull(draftState.selectedActions, action.value);\n        } else {\n          draftState.selectedActions.push(action.value);\n        }\n        break;\n      }\n      case 'SELECT_ALL_IN_PERMISSION': {\n        const areAllSelected = action.value.every((item) =>\n          draftState.selectedActions.includes(item.actionId)\n        );\n\n        if (areAllSelected) {\n          action.value.forEach((item) => {\n            pull(draftState.selectedActions, item.actionId);\n          });\n        } else {\n          action.value.forEach((item) => {\n            draftState.selectedActions.push(item.actionId);\n          });\n        }\n        break;\n      }\n\n      case 'SELECT_ALL_ACTIONS': {\n        draftState.selectedActions = [...draftState.data.allActionsIds];\n\n        break;\n      }\n      case 'ON_CHANGE_READ_ONLY': {\n        const onlyReadOnlyActions = draftState.data.allActionsIds.filter(\n          (actionId) => actionId.includes('find') || actionId.includes('findOne')\n        );\n        draftState.selectedActions = [...onlyReadOnlyActions];\n        break;\n      }\n      case 'UPDATE_PERMISSIONS_LAYOUT': {\n        draftState.data = transformPermissionsData(action.value);\n        break;\n      }\n      case 'UPDATE_ROUTES': {\n        draftState.routes = { ...action.value };\n        break;\n      }\n      case 'UPDATE_PERMISSIONS': {\n        draftState.selectedActions = [...action.value];\n        break;\n      }\n      case 'SET_SELECTED_ACTION': {\n        draftState.selectedAction = action.value;\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n", "import * as React from 'react';\n\nimport { Flex } from '@strapi/design-system';\nimport { Formik, Form, FormikHelpers } from 'formik';\nimport { useIntl } from 'react-intl';\nimport { useLocation, useMatch, useNavigate } from 'react-router-dom';\n\nimport { useGuidedTour } from '../../../../../components/GuidedTour/Provider';\nimport { Layouts } from '../../../../../components/Layouts/Layout';\nimport { Page } from '../../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../../core/store/hooks';\nimport { useNotification } from '../../../../../features/Notifications';\nimport { useTracking } from '../../../../../features/Tracking';\nimport { useAPIErrorHandler } from '../../../../../hooks/useAPIErrorHandler';\nimport { useRBAC } from '../../../../../hooks/useRBAC';\nimport {\n  useCreateAPITokenMutation,\n  useGetAPITokenQuery,\n  useUpdateAPITokenMutation,\n} from '../../../../../services/apiTokens';\nimport { useGetPermissionsQuery, useGetRoutesQuery } from '../../../../../services/contentApi';\nimport { isBaseQueryError } from '../../../../../utils/baseQuery';\nimport { API_TOKEN_TYPE } from '../../../components/Tokens/constants';\nimport { FormHead } from '../../../components/Tokens/FormHead';\nimport { TokenBox } from '../../../components/Tokens/TokenBox';\n\nimport {\n  ApiTokenPermissionsContextValue,\n  ApiTokenPermissionsProvider,\n} from './apiTokenPermissions';\nimport { FormApiTokenContainer } from './components/FormApiTokenContainer';\nimport { Permissions } from './components/Permissions';\nimport { schema } from './constants';\nimport { initialState, reducer } from './reducer';\n\nimport type { Get, ApiToken } from '../../../../../../../shared/contracts/api-token';\n\n/**\n * TODO: this could definitely be refactored to avoid using redux and instead just use the\n * server response as the source of the truth for the data.\n */\nexport const EditView = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { state: locationState } = useLocation();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n  const [apiToken, setApiToken] = React.useState<ApiToken | null>(\n    locationState?.apiToken?.accessKey\n      ? {\n          ...locationState.apiToken,\n        }\n      : null\n  );\n  const { trackUsage } = useTracking();\n  const setCurrentStep = useGuidedTour('EditView', (state) => state.setCurrentStep);\n  const {\n    allowedActions: { canCreate, canUpdate, canRegenerate },\n  } = useRBAC(permissions.settings?.['api-tokens']);\n  const [state, dispatch] = React.useReducer(reducer, initialState);\n  const match = useMatch('/settings/api-tokens/:id');\n  const id = match?.params?.id;\n  const isCreating = id === 'create';\n  const {\n    _unstableFormatAPIError: formatAPIError,\n    _unstableFormatValidationErrors: formatValidtionErrors,\n  } = useAPIErrorHandler();\n\n  const navigate = useNavigate();\n\n  const contentAPIPermissionsQuery = useGetPermissionsQuery();\n  const contentAPIRoutesQuery = useGetRoutesQuery();\n\n  /**\n   * Separate effects otherwise we could end\n   * up duplicating the same notification.\n   */\n  React.useEffect(() => {\n    if (contentAPIPermissionsQuery.error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(contentAPIPermissionsQuery.error),\n      });\n    }\n  }, [contentAPIPermissionsQuery.error, formatAPIError, toggleNotification]);\n\n  React.useEffect(() => {\n    if (contentAPIRoutesQuery.error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(contentAPIRoutesQuery.error),\n      });\n    }\n  }, [contentAPIRoutesQuery.error, formatAPIError, toggleNotification]);\n\n  React.useEffect(() => {\n    if (contentAPIPermissionsQuery.data) {\n      dispatch({\n        type: 'UPDATE_PERMISSIONS_LAYOUT',\n        value: contentAPIPermissionsQuery.data,\n      });\n    }\n  }, [contentAPIPermissionsQuery.data]);\n\n  React.useEffect(() => {\n    if (contentAPIRoutesQuery.data) {\n      dispatch({\n        type: 'UPDATE_ROUTES',\n        value: contentAPIRoutesQuery.data,\n      });\n    }\n  }, [contentAPIRoutesQuery.data]);\n\n  React.useEffect(() => {\n    if (apiToken) {\n      if (apiToken.type === 'read-only') {\n        dispatch({\n          type: 'ON_CHANGE_READ_ONLY',\n        });\n      }\n      if (apiToken.type === 'full-access') {\n        dispatch({\n          type: 'SELECT_ALL_ACTIONS',\n        });\n      }\n      if (apiToken.type === 'custom') {\n        dispatch({\n          type: 'UPDATE_PERMISSIONS',\n          value: apiToken?.permissions,\n        });\n      }\n    }\n  }, [apiToken]);\n\n  React.useEffect(() => {\n    trackUsage(isCreating ? 'didAddTokenFromList' : 'didEditTokenFromList', {\n      tokenType: API_TOKEN_TYPE,\n    });\n  }, [isCreating, trackUsage]);\n\n  const { data, error, isLoading } = useGetAPITokenQuery(id!, {\n    skip: !id || isCreating || !!apiToken,\n  });\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  React.useEffect(() => {\n    if (data) {\n      setApiToken(data);\n\n      if (data.type === 'read-only') {\n        dispatch({\n          type: 'ON_CHANGE_READ_ONLY',\n        });\n      }\n      if (data.type === 'full-access') {\n        dispatch({\n          type: 'SELECT_ALL_ACTIONS',\n        });\n      }\n      if (data.type === 'custom') {\n        dispatch({\n          type: 'UPDATE_PERMISSIONS',\n          value: data?.permissions,\n        });\n      }\n    }\n  }, [data]);\n\n  const [createToken] = useCreateAPITokenMutation();\n  const [updateToken] = useUpdateAPITokenMutation();\n\n  interface FormValues extends Pick<Get.Response['data'], 'name' | 'description'> {\n    lifespan: Get.Response['data']['lifespan'] | undefined;\n    type: Get.Response['data']['type'] | undefined;\n  }\n\n  const handleSubmit = async (body: FormValues, formik: FormikHelpers<FormValues>) => {\n    trackUsage(isCreating ? 'willCreateToken' : 'willEditToken', {\n      tokenType: API_TOKEN_TYPE,\n    });\n\n    try {\n      if (isCreating) {\n        const res = await createToken({\n          ...body,\n          // lifespan must be \"null\" for unlimited (0 would mean instantly expired and isn't accepted)\n          lifespan:\n            body?.lifespan && body.lifespan !== '0' ? parseInt(body.lifespan.toString(), 10) : null,\n          permissions: body.type === 'custom' ? state.selectedActions : null,\n        });\n\n        if ('error' in res) {\n          if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n            formik.setErrors(formatValidtionErrors(res.error));\n          } else {\n            toggleNotification({\n              type: 'danger',\n              message: formatAPIError(res.error),\n            });\n          }\n\n          return;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({\n            id: 'notification.success.apitokencreated',\n            defaultMessage: 'API Token successfully created',\n          }),\n        });\n\n        trackUsage('didCreateToken', {\n          type: res.data.type,\n          tokenType: API_TOKEN_TYPE,\n        });\n\n        navigate(`../api-tokens/${res.data.id.toString()}`, {\n          state: { apiToken: res.data },\n          replace: true,\n        });\n        setCurrentStep('apiTokens.success');\n      } else {\n        const res = await updateToken({\n          id: id!,\n          name: body.name,\n          description: body.description,\n          type: body.type,\n          permissions: body.type === 'custom' ? state.selectedActions : null,\n        });\n\n        if ('error' in res) {\n          if (isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n            formik.setErrors(formatValidtionErrors(res.error));\n          } else {\n            toggleNotification({\n              type: 'danger',\n              message: formatAPIError(res.error),\n            });\n          }\n\n          return;\n        }\n\n        toggleNotification({\n          type: 'success',\n          message: formatMessage({\n            id: 'notification.success.apitokenedited',\n            defaultMessage: 'API Token successfully edited',\n          }),\n        });\n\n        trackUsage('didEditToken', {\n          type: res.data.type,\n          tokenType: API_TOKEN_TYPE,\n        });\n      }\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'Something went wrong',\n        }),\n      });\n    }\n  };\n\n  const [hasChangedPermissions, setHasChangedPermissions] = React.useState(false);\n\n  const handleChangeCheckbox = ({\n    target: { value },\n  }: Parameters<ApiTokenPermissionsContextValue['value']['onChange']>[0]) => {\n    setHasChangedPermissions(true);\n    dispatch({\n      type: 'ON_CHANGE',\n      value,\n    });\n  };\n\n  const handleChangeSelectAllCheckbox = ({\n    target: { value },\n  }: Parameters<ApiTokenPermissionsContextValue['value']['onChangeSelectAll']>[0]) => {\n    setHasChangedPermissions(true);\n    dispatch({\n      type: 'SELECT_ALL_IN_PERMISSION',\n      value,\n    });\n  };\n\n  const setSelectedAction = ({\n    target: { value },\n  }: Parameters<ApiTokenPermissionsContextValue['value']['setSelectedAction']>[0]) => {\n    dispatch({\n      type: 'SET_SELECTED_ACTION',\n      value,\n    });\n  };\n\n  const providerValue = {\n    ...state,\n    onChange: handleChangeCheckbox,\n    onChangeSelectAll: handleChangeSelectAllCheckbox,\n    setSelectedAction,\n  };\n\n  const canEditInputs = (canUpdate && !isCreating) || (canCreate && isCreating);\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <ApiTokenPermissionsProvider value={providerValue}>\n      <Page.Main>\n        <Page.Title>\n          {formatMessage(\n            { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n            { name: 'API Tokens' }\n          )}\n        </Page.Title>\n        <Formik\n          validationSchema={schema}\n          validateOnChange={false}\n          initialValues={{\n            name: apiToken?.name || '',\n            description: apiToken?.description || '',\n            type: apiToken?.type,\n            lifespan: apiToken?.lifespan,\n          }}\n          enableReinitialize\n          onSubmit={(body, actions) => handleSubmit(body, actions)}\n        >\n          {({ errors, handleChange, isSubmitting, values, setFieldValue }) => {\n            if (hasChangedPermissions && values?.type !== 'custom') {\n              setFieldValue('type', 'custom');\n            }\n\n            return (\n              <Form>\n                <FormHead\n                  title={{\n                    id: 'Settings.apiTokens.createPage.title',\n                    defaultMessage: 'Create API Token',\n                  }}\n                  token={apiToken}\n                  setToken={setApiToken}\n                  canEditInputs={canEditInputs}\n                  canRegenerate={canRegenerate}\n                  isSubmitting={isSubmitting}\n                  regenerateUrl=\"/admin/api-tokens/\"\n                />\n\n                <Layouts.Content>\n                  <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n                    {Boolean(apiToken?.name) && (\n                      <TokenBox token={apiToken?.accessKey} tokenType={API_TOKEN_TYPE} />\n                    )}\n                    <FormApiTokenContainer\n                      errors={errors}\n                      onChange={handleChange}\n                      canEditInputs={canEditInputs}\n                      isCreating={isCreating}\n                      values={values}\n                      apiToken={apiToken}\n                      onDispatch={dispatch}\n                      setHasChangedPermissions={setHasChangedPermissions}\n                    />\n                    <Permissions\n                      disabled={\n                        !canEditInputs ||\n                        values?.type === 'read-only' ||\n                        values?.type === 'full-access'\n                      }\n                    />\n                  </Flex>\n                </Layouts.Content>\n              </Form>\n            );\n          }}\n        </Formik>\n      </Page.Main>\n    </ApiTokenPermissionsProvider>\n  );\n};\n\nexport const ProtectedEditView = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['api-tokens'].read\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditView />\n    </Page.Protect>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAUA,aAAS,gBAAgB,OAAO,OAAO,WAAW,YAAY;AAC5D,UAAI,QAAQ,YAAY,GACpB,SAAS,MAAM;AAEnB,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,WAAW,MAAM,KAAK,GAAG,KAAK,GAAG;AACnC,iBAAO;AAAA,QACT;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;ACtBjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,cAAc;AADlB,QAEI,kBAAkB;AAFtB,QAGI,YAAY;AAHhB,QAII,YAAY;AAGhB,QAAI,aAAa,MAAM;AAGvB,QAAI,SAAS,WAAW;AAaxB,aAAS,YAAY,OAAO,QAAQ,UAAU,YAAY;AACxD,UAAI,UAAU,aAAa,kBAAkB,aACzC,QAAQ,IACR,SAAS,OAAO,QAChB,OAAO;AAEX,UAAI,UAAU,QAAQ;AACpB,iBAAS,UAAU,MAAM;AAAA,MAC3B;AACA,UAAI,UAAU;AACZ,eAAO,SAAS,OAAO,UAAU,QAAQ,CAAC;AAAA,MAC5C;AACA,aAAO,EAAE,QAAQ,QAAQ;AACvB,YAAI,YAAY,GACZ,QAAQ,OAAO,KAAK,GACpB,WAAW,WAAW,SAAS,KAAK,IAAI;AAE5C,gBAAQ,YAAY,QAAQ,MAAM,UAAU,WAAW,UAAU,KAAK,IAAI;AACxE,cAAI,SAAS,OAAO;AAClB,mBAAO,KAAK,MAAM,WAAW,CAAC;AAAA,UAChC;AACA,iBAAO,KAAK,OAAO,WAAW,CAAC;AAAA,QACjC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAEA,WAAO,UAAU;AAAA;AAAA;;;AClDjB;AAAA;AAAA,QAAI,cAAc;AAsBlB,aAAS,QAAQ,OAAO,QAAQ;AAC9B,aAAQ,SAAS,MAAM,UAAU,UAAU,OAAO,SAC9C,YAAY,OAAO,MAAM,IACzB;AAAA,IACN;AAEA,WAAO,UAAU;AAAA;AAAA;;;AC5BjB;AAAA;AAAA,QAAI,WAAW;AAAf,QACI,UAAU;AAyBd,QAAIA,QAAO,SAAS,OAAO;AAE3B,WAAO,UAAUA;AAAA;AAAA;;;;;;;;;ACvBjB,IAAM,oBAAoB,SAAS,gBAAgB;EACjD,WAAW,CAAC,aAAa;IACvB,gBAAgB,QAAQ,MAAwD;MAC9E,OAAO,MAAM;MACb,mBAAmB,CAAC,aAAiD,SAAS;IAAA,CAC/E;IACD,WAAW,QAAQ,MAAmD;MACpE,OAAO,MAAM;MACb,mBAAmB,CAAC,aAA4C,SAAS;IAAA,CAC1E;EAAA;EAEH,kBAAkB;AACpB,CAAC;AAED,IAAM,EAAE,wBAAwB,kBAAA,IAAsB;ACiBtD,IAAM,CAAC,oCAAoC,6BAA6B,IACtE,0CAA+C,4BAA4B;AAE7E,IAAM,8BAA8B,CAAC;EACnC;EACA,GAAG;AACL,MAA+C;AAC7C,aACG,wBAAA,oCAAA,EAAoC,GAAG,MAAO,SAAS,CAAA;AAE5D;AAEA,IAAM,yBAAyB,MAAM,8BAA8B,wBAAwB;ACxBpF,IAAM,wBAAwB,CAAC;EACpC,SAAS,CAAA;EACT;EACA;EACA;EACA,SAAS,CAAA;EACT,WAAW,CAAA;EACX;EACA;AACF,MAAkC;AAC1B,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,QAAM,iCAAiC,CAAC,EAAE,QAAQ,EAAE,MAAA,EAAA,MAA6C;AAC/F,6BAAyB,KAAK;AAE9B,QAAI,UAAU,eAAe;AAChB,iBAAA;QACT,MAAM;MAAA,CACP;IAAA;AAEH,QAAI,UAAU,aAAa;AACd,iBAAA;QACT,MAAM;MAAA,CACP;IAAA;EACH;AAGF,QAAM,cAAc;IAClB;MACE,OAAO;MACP,OAAO;QACL,IAAI;QACJ,gBAAgB;MAAA;IAClB;IAEF;MACE,OAAO;MACP,OAAO;QACL,IAAI;QACJ,gBAAgB;MAAA;IAClB;IAEF;MACE,OAAO;MACP,OAAO;QACL,IAAI;QACJ,gBAAgB;MAAA;IAClB;EACF;AAIA,aAAA;IAAC;IAAA;MACC,YAAW;MACX,WAAS;MACT,QAAO;MACP,YAAY;MACZ,eAAe;MACf,aAAa;MACb,cAAc;MAEd,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;YAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EACH,CAAA;YACC,yBAAA,KAAK,MAAL,EAAU,KAAK,GACd,UAAA;cAAC,wBAAA,KAAK,MAAL,EAAqB,KAAK,GAAG,IAAI,IAAI,WAAU,UAAS,YAAW,WAClE,cAAA;YAAC;YAAA;cACC,OAAO,OAAO,MAAM;cACpB,OAAO,OAAO,MAAM;cACpB;cACA;YAAA;UAAA,EAAA,GALW,MAOf;cACA,wBAAC,KAAK,MAAL,EAA4B,KAAK,GAAG,IAAI,IAAI,WAAU,UAAS,YAAW,WACzE,cAAA;YAAC;YAAA;cACC,OAAO,OAAO,aAAa;cAC3B,OAAO,OAAO,aAAa;cAC3B;cACA;YAAA;UAAA,EAAA,GALW,aAOf;cACA,wBAAC,KAAK,MAAL,EAAyB,KAAK,GAAG,IAAI,IAAI,WAAU,UAAS,YAAW,WACtE,cAAA;YAAC;YAAA;cACC;cACA,OAAO,OAAO,UAAU;cACxB,OAAO,OAAO,UAAU;cACxB;cACA,OAAO;YAAA;UAAA,EAAA,GANI,UAQf;cAEA,wBAAC,KAAK,MAAL,EAAqB,KAAK,GAAG,IAAI,IAAI,WAAU,UAAS,YAAW,WAClE,cAAA;YAAC;YAAA;cACC,OAAO,OAAO,MAAM;cACpB,OAAO,OAAO,MAAM;cACpB,OAAO;gBACL,IAAI;gBACJ,gBAAgB;cAAA;cAElB,UAAU,CAAC,UAAU;AAEnB,+CAA+B,EAAE,QAAQ,EAAE,MAAA,EAAA,CAAS;AAGpD,yBAAS,EAAE,QAAQ,EAAE,MAAM,QAAQ,MAAA,EAAA,CAAS;cAAA;cAE9C,SAAS;cACT;YAAA;UAAA,EAAA,GAhBW,MAkBf;QAAA,EACF,CAAA;MAAA,EACF,CAAA;IAAA;EAAA;AAGN;AC9HA,IAAM,iBAAiB,CAAC,SAAgC;AACtD,UAAQ,MAAM;IACZ,KAAK,QAAQ;AACJ,aAAA;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MAAA;IACd;IAEF,KAAK,OAAO;AACH,aAAA;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MAAA;IACd;IAEF,KAAK,OAAO;AACH,aAAA;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MAAA;IACd;IAEF,KAAK,UAAU;AACN,aAAA;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MAAA;IACd;IAEF,SAAS;AACA,aAAA;QACL,MAAM;QACN,QAAQ;QACR,YAAY;MAAA;IACd;EACF;AAEJ;AAEA,IAAM,YAAY,GAAqB,GAAG;;mBAEvB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC,QAAQ,CAAC,EAAE,MAAM,MAAM,MAAM,OAAO,CAAC,CAAC;;AAWhF,IAAM,aAAa,CAAC;EACzB,QAAQ;IACN,SAAS;IACT,QAAQ;IACR,MAAM;EAAA;AAEV,MAAuB;AACf,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,QAAM,EAAE,QAAQ,SAAS,OAAO,KAAA,IAAS;AACnC,QAAA,iBAAiB,WAAO,YAAAC,SAAK,KAAK,MAAM,GAAG,CAAC,IAAI,CAAA;AAChD,QAAA,CAAC,aAAa,IAAI,SAAS,EAAE,IAAI,QAAQ,MAAM,MAAM,GAAG,IAAI,CAAA;AAC5D,QAAA,SAAS,eAAe,MAAM,MAAM;AAE1C,aAAA,yBACG,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;QAAA,yBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAA;MAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB;MAAE;UAEH,wBAAC,QAAA,EAAM,UAAW,WAAA,CAAA;UACjB,yBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cAAa,UAAA;QAAA;QAC/C;MAAA,EACJ,CAAA;IAAA,EACF,CAAA;QACA,yBAAC,MAAA,EAAK,WAAS,MAAC,YAAW,YAAW,aAAY,cAAa,KAAK,GAClE,UAAA;UAAA,wBAAC,WAAA,EAAU,YAAY,OAAO,YAAY,aAAa,OAAO,QAAQ,SAAS,GAC7E,cAAA,wBAAC,YAAA,EAAW,YAAW,QAAO,WAAW,OAAO,MAC7C,UAAA,OAAA,CACH,EACF,CAAA;UAAA,wBACC,KAAI,EAAA,aAAa,GAAG,cAAc,GAChC,cAAA,WAAAC,SAAI,gBAAgB,CAAC,cACpB,yBAAC,YAAA,EAAuB,WAAW,MAAM,SAAS,GAAG,IAAI,eAAe,cAAc,UAAA;QAAA;QAClF;MAAA,EADa,GAAA,KAEjB,CACD,EACH,CAAA;IAAA,EACF,CAAA;EAAA,EACF,CAAA;AAEJ;ACzGO,IAAM,oBAAoB,MAAM;AAC/B,QAAA;IACJ,OAAO,EAAE,gBAAgB,OAAO;EAAA,IAC9B,uBAAuB;AACrB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,gBAAgB,iDAAgB,MAAM,KAAK;AAG/C,aAAA;IAAC,KAAK;IAAL;MACC,KAAK;MACL,YAAW;MACX,YAAY;MACZ,eAAe;MACf,aAAa;MACb,cAAc;MACd,OAAO,EAAE,WAAW,OAAO;MAC3B,WAAU;MACV,YAAW;MAEV,UAAA,qBACE,wBAAA,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GAChD,UAAA,iBACC,iBAAiB,UACjB,OAAO,aAAa,EAAE,IAAI,CAAC,UAAU;;AACnC,iBAAO,iBAAM,OAAO,SAAb,mBAAmB,UAAnB,mBAA0B,SAAS,oBACxC,MAAM,YAAY,qBACjB,wBAAA,YAAA,EAA+B,MAAf,GAAA,MAAM,OAAuB,IAC5C;MAAA,CACL,EACL,CAAA,QAEC,yBAAA,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;YAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EACH,CAAA;YAAA,wBACC,YAAW,EAAA,KAAI,KAAI,WAAU,cAC3B,UAAc,cAAA;UACb,IAAI;UACJ,gBACE;QAAA,CACH,EACH,CAAA;MAAA,EACF,CAAA;IAAA;EAAA;AAIR;ACrCA,IAAM,8BAA8B;gBACpB,CAAC,UAAU,MAAM,MAAM,OAAO,UAAU;;;;;;AAOxD,IAAM,kBAAkB,GAAqB,GAAG;;;;;;;;cAQlC,CAAC,UAAU,MAAM,MAAM,OAAO,UAAU;;;;;IAKlD,CAAC,UAAU,MAAM,aAAa,2BAA2B;;MAEvD,2BAA2B;;;AAIjC,IAAM,SAAS,GAAO;;;0BAGI,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;AAUzD,IAAM,yBAAyB,CAAC;EACrC,cAAc,CAAA;EACd;EACA,cAAc;EACd,WAAW;AACb,MAAmC;AAC3B,QAAA;IACJ,OAAO,EAAE,mBAAmB,UAAU,iBAAiB,mBAAmB,eAAe;EAAA,IACvF,uBAAuB;AACrB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAE5B,QAAA,mBAAmB,CAAC,aAAqB,aAAa;AAG1D,aAAA,yBAAC,UAAU,MAAV,EAAe,OAAO,GAAG,KAAK,IAAI,WAAW,IAC5C,UAAA;QAAA,wBAAC,UAAU,QAAV,EAAiB,SAAS,cAAc,IAAI,YAAY,aACvD,cAAA,wBAAC,UAAU,SAAV,EAAmB,cAAW,kBAAAC,SAAA,KAAK,EAAA,CAAE,EACxC,CAAA;QAAA,wBACC,UAAU,SAAV,EACE,UAAa,2CAAA,IAAI,CAAC,eAAe;AAC1B,YAAA,qBAAqB,WAAW,QAAQ;QAAM,CAAC,WACnD,gBAAgB,SAAS,OAAO,QAAQ;MAAA;AAGpC,YAAA,sBAAsB,WAAW,QAAQ;QAAK,CAAC,WACnD,gBAAgB,SAAS,OAAO,QAAQ;MAAA;AAG1C,iBAAA,yBACG,KACC,EAAA,UAAA;YAAA,yBAAC,MAAA,EAAK,gBAAe,iBAAgB,YAAW,UAAS,SAAS,GAChE,UAAA;cAAC,wBAAA,KAAA,EAAI,cAAc,GACjB,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAY,yCAAA,WAAA,CACf,EACF,CAAA;cAAA,wBACC,QAAO,CAAA,CAAA;cACR,wBAAC,KAAI,EAAA,aAAa,GAChB,cAAA;YAAC;YAAA;cACC,SACE,CAAC,sBAAsB,sBACnB,kBACA;cAEN,iBAAiB,MAAM;AACH,kCAAA,EAAE,QAAQ,EAAE,OAAO,CAAC,GAAG,WAAW,OAAO,EAAE,EAAA,CAAG;cAAA;cAElE;cAEC,UAAA,cAAc,EAAE,IAAI,wBAAwB,gBAAgB,aAAA,CAAc;YAAA;UAAA,EAE/E,CAAA;QAAA,EACF,CAAA;YACC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,SAAS,GACzB,WAAA,yCAAY,aACX,yCAAY,QAAQ,IAAI,CAAC,WAAW;AAEhC,qBAAA;YAAC,KAAK;YAAL;cACC,KAAK;cAEL,WAAU;cACV,YAAW;cAEX,cAAA;gBAAC;gBAAA;kBACC,WAAW,iBAAiB,OAAO,QAAQ;kBAC3C,SAAS;kBACT,WAAS;kBAET,UAAA;wBAAA;sBAAC;sBAAA;wBACC,SAAS,gBAAgB,SAAS,OAAO,QAAQ;wBACjD,MAAM,OAAO;wBACb,iBAAiB,MAAM;AACrB,mCAAS,EAAE,QAAQ,EAAE,OAAO,OAAO,SAAA,EAAA,CAAY;wBAAA;wBAEjD;wBAEC,UAAO,OAAA;sBAAA;oBAAA;wBAEV;sBAAC;sBAAA;wBACC,MAAK;wBACL,eAAY;wBACZ,SAAS,MACP,kBAAkB,EAAE,QAAQ,EAAE,OAAO,OAAO,SAAS,EAAA,CAAG;wBAE1D,OAAO,EAAE,SAAS,eAAe,YAAY,SAAS;wBAEtD,cAAA,wBAAC,eAAI,EAAA,IAAG,MAAM,CAAA;sBAAA;oBAAA;kBAChB;gBAAA;cAAA;YACF;YA7BK,OAAO;UAAA;QA8Bd,IAGR,CAAA;MAAA,EAAA,GA/DQ,GAAG,KAAK,IAAI,yCAAY,UAAU,EAgE5C;IAAA,GAGN,CAAA;EAAA,EACF,CAAA;AAEJ;ACpJO,IAAM,sBAAsB,CAAC,EAAE,UAAU,MAAM,GAAG,MAAA,MAAsC;AAC7F,aAAA,wBACG,KAAI,EAAA,SAAS,GAAG,YAAW,YAC1B,cAAC,wBAAA,UAAU,MAAV,EAAe,MAAK,KAClB,UAAA,WACC,QAAQ,IAAI,CAAC,KAAK,cAChB;IAAC;IAAA;MAEC,OAAO,IAAI;MACX,aAAa,IAAI;MACjB,aAAa;MACZ,GAAG;IAAA;IAJC,IAAI;EAAA,CAMZ,EAAA,CACL,EACF,CAAA;AAEJ;ACnBO,IAAM,cAAc,CAAC,EAAE,GAAG,MAAA,MAAY;AACrC,QAAA;IACJ,OAAO,EAAE,KAAK;EAAA,IACZ,uBAAuB;AACrB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAGhC,aAAA,yBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,QAAO,gBAAe,WAAS,MAAC,YAAW,YAC5D,UAAA;QAAA;MAAC,KAAK;MAAL;QACC,KAAK;QACL,YAAY;QACZ,eAAe;QACf,aAAa;QACb,cAAc;QACd,WAAU;QACV,YAAW;QAEX,UAAA;cAAA,yBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;gBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EACH,CAAA;gBAAA,wBACC,YAAW,EAAA,KAAI,KAAI,WAAU,cAC3B,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EACH,CAAA;UAAA,EACF,CAAA;WACC,6BAAM,oBAAgB,wBAAA,qBAAA,EAAoB,SAAS,6BAAM,aAAc,GAAG,MAAO,CAAA;QAAA;MAAA;IAAA;QACpF,wBACC,mBAAkB,CAAA,CAAA;EAAA,EACrB,CAAA;AAEJ;ACxCO,IAAM,SAAaC,QAAO,EAAE,MAAM;EACvC,MAAU,OAAA,EAAS,IAAI,GAAG,EAAE,SAASC,YAAiB,SAAS,EAAE;EACjE,MACG,OAAO,EACP,MAAM,CAAC,aAAa,eAAe,QAAQ,CAAC,EAC5C,SAASA,YAAiB,SAAS,EAAE;EACxC,aAAiB,OAAO,EAAE,SAAS;EACnC,UAAcD,QAAO,EAAE,QAAA,EAAU,IAAI,CAAC,EAAE,SAAS,EAAE,QAAQC,YAAiB,SAAS,EAAE;AACzF,CAAC;ACDY,IAAA,2BAA2B,CAAC,SAA+B;AACtE,QAAM,SAAiB;IACrB,eAAe,CAAA;IACf,aAAa,CAAA;EAAC;AAGT,SAAA,cAAc,OAAO,QAAQ,IAAI,EAAE,IAAI,CAAC,CAAC,OAAO,UAAU,OAAO;IACtE;IACA,OAAO,MAAM,MAAM,IAAI,EAAE,CAAC;IAC1B,aAAa,OAAO,KAAK,WAAW,WAAW,EAC5C,IAAI,CAAC,gBAAgB;MACpB;MACA,SACE,cAAc,WAAW,cACrB,WAAW,YAAY,UAAU,EAC9B,IAAI,CAAC,WAAgD;AACpD,cAAM,WAAW,GAAG,KAAK,IAAI,UAAU,IAAI,MAAM;AAE7C,YAAA,MAAM,SAAS,OAAO,GAAG;AACpB,iBAAA,cAAc,KAAK,QAAQ;QAAA;AAG7B,eAAA;UACL;UACA;QAAA;MACF,CACD,EACA,KAAK,IACR,CAAA;IAAC,EACP,EACD,KAAK;EAAA,EACR;AAEK,SAAA;AACT;ACiBO,IAAM,eAA6B;EACxC,MAAM;IACJ,eAAe,CAAA;IACf,aAAa,CAAA;EAAC;EAEhB,QAAQ,CAAA;EACR,gBAAgB;EAChB,iBAAiB,CAAA;AACnB;AAEO,IAAM,UAAU,CAAC,OAAqB,WAC3C,GAAQ,OAAO,CAAC,eAAe;AAC7B,UAAQ,OAAO,MAAM;IACnB,KAAK,aAAa;AAChB,UAAI,WAAW,gBAAgB,SAAS,OAAO,KAAK,GAAG;AAChD,wBAAAC,SAAA,WAAW,iBAAiB,OAAO,KAAK;MAAA,OACxC;AACM,mBAAA,gBAAgB,KAAK,OAAO,KAAK;MAAA;AAE9C;IAAA;IAEF,KAAK,4BAA4B;AACzB,YAAA,iBAAiB,OAAO,MAAM;QAAM,CAAC,SACzC,WAAW,gBAAgB,SAAS,KAAK,QAAQ;MAAA;AAGnD,UAAI,gBAAgB;AACX,eAAA,MAAM,QAAQ,CAAC,SAAS;AACxB,0BAAAA,SAAA,WAAW,iBAAiB,KAAK,QAAQ;QAAA,CAC/C;MAAA,OACI;AACE,eAAA,MAAM,QAAQ,CAAC,SAAS;AAClB,qBAAA,gBAAgB,KAAK,KAAK,QAAQ;QAAA,CAC9C;MAAA;AAEH;IAAA;IAGF,KAAK,sBAAsB;AACzB,iBAAW,kBAAkB,CAAC,GAAG,WAAW,KAAK,aAAa;AAE9D;IAAA;IAEF,KAAK,uBAAuB;AACpB,YAAA,sBAAsB,WAAW,KAAK,cAAc;QACxD,CAAC,aAAa,SAAS,SAAS,MAAM,KAAK,SAAS,SAAS,SAAS;MAAA;AAE7D,iBAAA,kBAAkB,CAAC,GAAG,mBAAmB;AACpD;IAAA;IAEF,KAAK,6BAA6B;AACrB,iBAAA,OAAO,yBAAyB,OAAO,KAAK;AACvD;IAAA;IAEF,KAAK,iBAAiB;AACpB,iBAAW,SAAS,EAAE,GAAG,OAAO,MAAM;AACtC;IAAA;IAEF,KAAK,sBAAsB;AACzB,iBAAW,kBAAkB,CAAC,GAAG,OAAO,KAAK;AAC7C;IAAA;IAEF,KAAK,uBAAuB;AAC1B,iBAAW,iBAAiB,OAAO;AACnC;IAAA;IAEF;AACS,aAAA;EAAA;AAEb,CAAC;AC1FI,IAAM,WAAW,MAAM;;AACtB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,EAAE,OAAO,cAAc,IAAI,YAAY;AAC7C,QAAM,cAAc,iBAAiB,CAACC,WAAUA,OAAM,UAAU,WAAW;AAC3E,QAAM,CAAC,UAAU,WAAW,IAAU;MACpC,oDAAe,aAAf,mBAAyB,aACrB;MACE,GAAG,cAAc;IAAA,IAEnB;EAAA;AAEA,QAAA,EAAE,WAAW,IAAI,YAAY;AACnC,QAAM,iBAAiB,cAAc,YAAY,CAACA,WAAUA,OAAM,cAAc;AAC1E,QAAA;IACJ,gBAAgB,EAAE,WAAW,WAAW,cAAc;EAAA,IACpD,SAAQ,iBAAY,aAAZ,mBAAuB,aAAa;AAChD,QAAM,CAAC,OAAO,QAAQ,IAAU,iBAAW,SAAS,YAAY;AAC1D,QAAA,QAAQ,SAAS,0BAA0B;AAC3C,QAAA,MAAK,oCAAO,WAAP,mBAAe;AAC1B,QAAM,aAAa,OAAO;AACpB,QAAA;IACJ,yBAAyB;IACzB,iCAAiC;EAAA,IAC/B,mBAAmB;AAEvB,QAAM,WAAW,YAAY;AAE7B,QAAM,6BAA6B,uBAAuB;AAC1D,QAAM,wBAAwB,kBAAkB;AAMhD,EAAM,gBAAU,MAAM;AACpB,QAAI,2BAA2B,OAAO;AACjB,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,2BAA2B,KAAK;MAAA,CACzD;IAAA;EACH,GACC,CAAC,2BAA2B,OAAO,gBAAgB,kBAAkB,CAAC;AAEzE,EAAM,gBAAU,MAAM;AACpB,QAAI,sBAAsB,OAAO;AACZ,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,sBAAsB,KAAK;MAAA,CACpD;IAAA;EACH,GACC,CAAC,sBAAsB,OAAO,gBAAgB,kBAAkB,CAAC;AAEpE,EAAM,gBAAU,MAAM;AACpB,QAAI,2BAA2B,MAAM;AAC1B,eAAA;QACP,MAAM;QACN,OAAO,2BAA2B;MAAA,CACnC;IAAA;EACH,GACC,CAAC,2BAA2B,IAAI,CAAC;AAEpC,EAAM,gBAAU,MAAM;AACpB,QAAI,sBAAsB,MAAM;AACrB,eAAA;QACP,MAAM;QACN,OAAO,sBAAsB;MAAA,CAC9B;IAAA;EACH,GACC,CAAC,sBAAsB,IAAI,CAAC;AAE/B,EAAM,gBAAU,MAAM;AACpB,QAAI,UAAU;AACR,UAAA,SAAS,SAAS,aAAa;AACxB,iBAAA;UACP,MAAM;QAAA,CACP;MAAA;AAEC,UAAA,SAAS,SAAS,eAAe;AAC1B,iBAAA;UACP,MAAM;QAAA,CACP;MAAA;AAEC,UAAA,SAAS,SAAS,UAAU;AACrB,iBAAA;UACP,MAAM;UACN,OAAO,qCAAU;QAAA,CAClB;MAAA;IACH;EACF,GACC,CAAC,QAAQ,CAAC;AAEb,EAAM,gBAAU,MAAM;AACT,eAAA,aAAa,wBAAwB,wBAAwB;MACtE,WAAW;IAAA,CACZ;EAAA,GACA,CAAC,YAAY,UAAU,CAAC;AAE3B,QAAM,EAAE,MAAM,OAAO,UAAU,IAAI,oBAAoB,IAAK;IAC1D,MAAM,CAAC,MAAM,cAAc,CAAC,CAAC;EAAA,CAC9B;AAED,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACU,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IAAA;EACH,GACC,CAAC,OAAO,gBAAgB,kBAAkB,CAAC;AAE9C,EAAM,gBAAU,MAAM;AACpB,QAAI,MAAM;AACR,kBAAY,IAAI;AAEZ,UAAA,KAAK,SAAS,aAAa;AACpB,iBAAA;UACP,MAAM;QAAA,CACP;MAAA;AAEC,UAAA,KAAK,SAAS,eAAe;AACtB,iBAAA;UACP,MAAM;QAAA,CACP;MAAA;AAEC,UAAA,KAAK,SAAS,UAAU;AACjB,iBAAA;UACP,MAAM;UACN,OAAO,6BAAM;QAAA,CACd;MAAA;IACH;EACF,GACC,CAAC,IAAI,CAAC;AAEH,QAAA,CAAC,WAAW,IAAI,0BAA0B;AAC1C,QAAA,CAAC,WAAW,IAAI,0BAA0B;AAO1C,QAAA,eAAe,OAAO,MAAkB,WAAsC;AACvE,eAAA,aAAa,oBAAoB,iBAAiB;MAC3D,WAAW;IAAA,CACZ;AAEG,QAAA;AACF,UAAI,YAAY;AACR,cAAA,MAAM,MAAM,YAAY;UAC5B,GAAG;;UAEH,WACE,6BAAM,aAAY,KAAK,aAAa,MAAM,SAAS,KAAK,SAAS,SAAA,GAAY,EAAE,IAAI;UACrF,aAAa,KAAK,SAAS,WAAW,MAAM,kBAAkB;QAAA,CAC/D;AAED,YAAI,WAAW,KAAK;AAClB,cAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,MAAM,SAAS,mBAAmB;AACvE,mBAAO,UAAU,sBAAsB,IAAI,KAAK,CAAC;UAAA,OAC5C;AACc,+BAAA;cACjB,MAAM;cACN,SAAS,eAAe,IAAI,KAAK;YAAA,CAClC;UAAA;AAGH;QAAA;AAGiB,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA,CACF;AAED,mBAAW,kBAAkB;UAC3B,MAAM,IAAI,KAAK;UACf,WAAW;QAAA,CACZ;AAED,iBAAS,iBAAiB,IAAI,KAAK,GAAG,SAAA,CAAU,IAAI;UAClD,OAAO,EAAE,UAAU,IAAI,KAAK;UAC5B,SAAS;QAAA,CACV;AACD,uBAAe,mBAAmB;MAAA,OAC7B;AACC,cAAA,MAAM,MAAM,YAAY;UAC5B;UACA,MAAM,KAAK;UACX,aAAa,KAAK;UAClB,MAAM,KAAK;UACX,aAAa,KAAK,SAAS,WAAW,MAAM,kBAAkB;QAAA,CAC/D;AAED,YAAI,WAAW,KAAK;AAClB,cAAI,iBAAiB,IAAI,KAAK,KAAK,IAAI,MAAM,SAAS,mBAAmB;AACvE,mBAAO,UAAU,sBAAsB,IAAI,KAAK,CAAC;UAAA,OAC5C;AACc,+BAAA;cACjB,MAAM;cACN,SAAS,eAAe,IAAI,KAAK;YAAA,CAClC;UAAA;AAGH;QAAA;AAGiB,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA,CACF;AAED,mBAAW,gBAAgB;UACzB,MAAM,IAAI,KAAK;UACf,WAAW;QAAA,CACZ;MAAA;IACH,QACM;AACa,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IAAA;EACH;AAGF,QAAM,CAAC,uBAAuB,wBAAwB,IAAU,eAAS,KAAK;AAE9E,QAAM,uBAAuB,CAAC;IAC5B,QAAQ,EAAE,MAAM;EAAA,MACyD;AACzE,6BAAyB,IAAI;AACpB,aAAA;MACP,MAAM;MACN;IAAA,CACD;EAAA;AAGH,QAAM,gCAAgC,CAAC;IACrC,QAAQ,EAAE,MAAM;EAAA,MACkE;AAClF,6BAAyB,IAAI;AACpB,aAAA;MACP,MAAM;MACN;IAAA,CACD;EAAA;AAGH,QAAM,oBAAoB,CAAC;IACzB,QAAQ,EAAE,MAAM;EAAA,MACkE;AACzE,aAAA;MACP,MAAM;MACN;IAAA,CACD;EAAA;AAGH,QAAM,gBAAgB;IACpB,GAAG;IACH,UAAU;IACV,mBAAmB;IACnB;EAAA;AAGF,QAAM,gBAAiB,aAAa,CAAC,cAAgB,aAAa;AAElE,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAA,CAAa;EAAA;AAGvB,aAAA,wBACG,6BAA4B,EAAA,OAAO,eAClC,cAAC,yBAAA,KAAK,MAAL,EACC,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE,EAAE,MAAM,aAAa;IAAA,EAEzB,CAAA;QACA;MAAC;MAAA;QACC,kBAAkB;QAClB,kBAAkB;QAClB,eAAe;UACb,OAAM,qCAAU,SAAQ;UACxB,cAAa,qCAAU,gBAAe;UACtC,MAAM,qCAAU;UAChB,UAAU,qCAAU;QAAA;QAEtB,oBAAkB;QAClB,UAAU,CAAC,MAAM,YAAY,aAAa,MAAM,OAAO;QAEtD,UAAA,CAAC,EAAE,QAAQ,cAAc,cAAc,QAAQ,cAAA,MAAoB;AAC9D,cAAA,0BAAyB,iCAAQ,UAAS,UAAU;AACtD,0BAAc,QAAQ,QAAQ;UAAA;AAGhC,qBAAA,yBACG,MACC,EAAA,UAAA;gBAAA;cAAC;cAAA;gBACC,OAAO;kBACL,IAAI;kBACJ,gBAAgB;gBAAA;gBAElB,OAAO;gBACP,UAAU;gBACV;gBACA;gBACA;gBACA,eAAc;cAAA;YAAA;gBAGhB,wBAAC,QAAQ,SAAR,EACC,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GAChD,UAAA;cAAQ,QAAA,qCAAU,IAAI,SACrB,wBAAC,UAAA,EAAS,OAAO,qCAAU,WAAW,WAAW,eAAgB,CAAA;kBAEnE;gBAAC;gBAAA;kBACC;kBACA,UAAU;kBACV;kBACA;kBACA;kBACA;kBACA,YAAY;kBACZ;gBAAA;cAAA;kBAEF;gBAAC;gBAAA;kBACC,UACE,CAAC,kBACD,iCAAQ,UAAS,gBACjB,iCAAQ,UAAS;gBAAA;cAAA;YAErB,EAAA,CACF,EACF,CAAA;UAAA,EACF,CAAA;QAAA;MAEJ;IAAA;EACF,EAAA,CACF,EACF,CAAA;AAEJ;AAEO,IAAM,oBAAoB,MAAM;AACrC,QAAM,cAAc;IAClB,CAAC,UAAU;;AAAA,yBAAM,UAAU,YAAY,aAA5B,mBAAuC,cAAc;;EAAA;AAGlE,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,UAAA,CAAA,CAAS,EACZ,CAAA;AAEJ;", "names": ["pull", "tail", "map", "capitalize", "create", "translatedErrors", "pull", "state"]}