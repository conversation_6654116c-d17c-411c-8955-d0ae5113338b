# subtrans-auth 邮箱验证注册测试指南

## 🎯 测试目标
测试 `subtrans-auth` API 的两个主要功能：
1. 发送激活邮件 (`sendActivation`)
2. 激活账户 (`activateAccount`)

## 📋 前提条件

### 1. 确保 Strapi 服务器正在运行
```bash
npm run dev
```
服务器应该在 `http://localhost:1437` 上运行

### 2. 检查邮箱配置
确保 `.env` 文件中的邮箱配置正确：
```env
SMTP_HOST=smtp.163.com
SMTP_PORT=465
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=AJjSX5ptg3BVPJhp
```

### 3. 更新 plugins.js 配置
编辑 `config/plugins.js`，确保 `defaultFrom` 使用正确的邮箱：
```javascript
settings: {
  defaultFrom: '<EMAIL>',  // 使用实际的发送邮箱
  defaultReplyTo: '<EMAIL>',
},
```

## 🧪 测试方法

### 方法一：使用 PowerShell 脚本

1. 编辑 `test_subtrans_auth.ps1` 文件
2. 修改第6行的邮箱地址为您的真实邮箱：
   ```powershell
   $TestEmail = "<EMAIL>"
   ```
3. 运行测试：
   ```powershell
   .\test_subtrans_auth.ps1
   ```

### 方法二：使用 curl 命令

#### 测试1：发送激活邮件
```bash
curl -X POST http://localhost:1437/api/subtrans-auth/send-activation \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>"}'
```

**预期响应：**
```json
{
  "message": "Activation email sent. Please check your inbox."
}
```

#### 测试2：测试无效邮箱格式
```bash
curl -X POST http://localhost:1437/api/subtrans-auth/send-activation \
  -H "Content-Type: application/json" \
  -d '{"email":"invalid-email"}'
```

**预期响应（400错误）：**
```json
{
  "data": null,
  "error": {
    "status": 400,
    "name": "BadRequestError",
    "message": "Invalid email format"
  }
}
```

#### 测试3：激活账户
首先从邮件中获取 token，然后：
```bash
curl -X POST http://localhost:1437/api/subtrans-auth/activate-account \
  -H "Content-Type: application/json" \
  -d '{"token":"YOUR_TOKEN_FROM_EMAIL","password":"TestPassword123!"}'
```

**预期响应：**
```json
{
  "message": "Account activated successfully",
  "user": {
    "id": 1,
    "email": "<EMAIL>",
    "username": "<EMAIL>"
  }
}
```

### 方法三：使用 PowerShell 直接测试

```powershell
# 测试发送激活邮件
$body = '{"email":"<EMAIL>"}'
$response = Invoke-RestMethod -Uri "http://localhost:1437/api/subtrans-auth/send-activation" -Method POST -Body $body -ContentType "application/json"
$response

# 测试激活账户（需要先从邮件获取token）
$body = '{"token":"YOUR_TOKEN_HERE","password":"TestPassword123!"}'
$response = Invoke-RestMethod -Uri "http://localhost:1437/api/subtrans-auth/activate-account" -Method POST -Body $body -ContentType "application/json"
$response
```

## 🔍 测试场景

### 场景1：正常流程
1. ✅ 发送激活邮件到有效邮箱
2. ✅ 检查邮件收件箱
3. ✅ 从邮件中提取 token
4. ✅ 使用 token 激活账户
5. ✅ 验证用户创建成功

### 场景2：错误处理
1. ❌ 发送到无效邮箱格式
2. ❌ 使用无效 token 激活
3. ❌ 缺少必需参数
4. ❌ 令牌过期（15分钟后）
5. ❌ 重复邮箱注册

### 场景3：边界测试
1. 📧 同一邮箱多次发送激活
2. ⏰ 令牌过期测试
3. 🔐 弱密码验证
4. 📝 特殊字符邮箱

## 📊 预期结果

### 成功案例：
- **发送激活邮件**: HTTP 200, 消息确认
- **激活账户**: HTTP 200, 用户信息返回
- **邮件发送**: 收到包含激活链接的邮件

### 错误案例：
- **无效邮箱**: HTTP 400, "Invalid email format"
- **重复邮箱**: HTTP 400, "Email is already registered"
- **无效令牌**: HTTP 400, "Invalid token"
- **令牌过期**: HTTP 400, "Token has expired"
- **缺少参数**: HTTP 400, 相应错误信息

## 🐛 常见问题

### 1. 邮件未收到
- 检查垃圾邮件文件夹
- 确认 SMTP 配置正确
- 检查 `defaultFrom` 邮箱地址

### 2. 服务器连接失败
- 确认 Strapi 服务器正在运行
- 检查端口 1437 是否可访问
- 查看服务器日志

### 3. 令牌相关错误
- 令牌有效期为 15 分钟
- 确保令牌完整且正确
- 检查数据库中的激活令牌记录

## 📝 测试日志

记录测试结果：

| 测试项目 | 状态 | 备注 |
|---------|------|------|
| 发送激活邮件 | ✅/❌ |  |
| 邮件接收 | ✅/❌ |  |
| 令牌提取 | ✅/❌ |  |
| 账户激活 | ✅/❌ |  |
| 错误处理 | ✅/❌ |  |

---
*测试完成后，请清理测试数据，删除测试用户和激活令牌*
