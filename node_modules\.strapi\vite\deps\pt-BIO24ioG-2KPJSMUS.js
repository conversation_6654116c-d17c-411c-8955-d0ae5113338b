import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/pt-BIO24ioG.mjs
var pt = {
  "BoundRoute.title": "Ligar rota a",
  "EditForm.inputSelect.description.role": "Vai atribuir o grupo selecionado ao novo utilizador autenticado.",
  "EditForm.inputSelect.label.role": "Grupo por defeito para utilizadores autenticados",
  "EditForm.inputToggle.description.email": "Proibir a criação de múltiplas contas com o mesmo email por serviços de autenticação diferentes.",
  "EditForm.inputToggle.description.email-confirmation": "<PERSON>uando ativado (ON), os novos utilizadores recebem um email de confirmação.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "Após confirmar o seu email, escolha para onde vai ser redirecionado.",
  "EditForm.inputToggle.description.sign-up": "<PERSON>uan<PERSON> desativado (OFF), o processo de registo está proibido. Ninguém se consegue registar mais, independentemente do serviço de authenticação.",
  "EditForm.inputToggle.label.email": "Uma conta por endereço de email",
  "EditForm.inputToggle.label.email-confirmation": "Ativar email de confirmação",
  "EditForm.inputToggle.label.email-confirmation-redirection": "Endereço de redirecionamento (URL)",
  "EditForm.inputToggle.label.sign-up": "Ativar registos",
  "HeaderNav.link.advancedSettings": "Configurações avançadas",
  "HeaderNav.link.emailTemplates": "Modelos de email",
  "HeaderNav.link.providers": "Serviços de autenticação",
  "Plugin.permissions.plugins.description": "Defina todas as ações permitidas para o plugin {name}.",
  "Plugins.header.description": "Todas as ações associadas a uma rota estão listadas abaixo.",
  "Plugins.header.title": "Permissões",
  "Policies.header.hint": "Selecione as ações da aplicação ou dos plugins e clique no ícone para mostrar as rotas associadas",
  "Policies.header.title": "Configurações avançadas",
  "PopUpForm.Email.email_templates.inputDescription": "Se não tem a certeza de como usar as variáveis, {link}",
  "PopUpForm.Email.options.from.email.label": "Shipper email",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Shipper name",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "Mensagem",
  "PopUpForm.Email.options.object.label": "Assunto",
  "PopUpForm.Email.options.response_email.label": "Email de resposta",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Se desativado, os utilizadores não conseguirão utilizar este serviço de autenticação.",
  "PopUpForm.Providers.enabled.label": "Ativar",
  "PopUpForm.Providers.key.label": "ID de Client",
  "PopUpForm.Providers.key.placeholder": "TEXTO",
  "PopUpForm.Providers.redirectURL.front-end.label": "Endereço de redirecionamento para a sua aplicação de front-end",
  "PopUpForm.Providers.secret.label": "Segredo de cliente",
  "PopUpForm.Providers.secret.placeholder": "TEXTO",
  "PopUpForm.Providers.subdomain.label": "Host URI (Subdomain)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "Editar Modelos de Email",
  "notification.success.submit": "As configurações foram atualizadas",
  "plugin.description.long": "Proteja a sua API com um processo completo de autenticação baseado em JWT. Este plugin também vem com estratégia de ACL que permite gerir permissões entre grupos de utilizadores.",
  "plugin.description.short": "Proteja a sua API com um processo completo de autenticação baseado em JWT",
  "plugin.name": "Grupos & Permissões"
};
export {
  pt as default
};
//# sourceMappingURL=pt-BIO24ioG-2KPJSMUS.js.map
