{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/admin/src/pages/AdvancedSettings/utils/layout.js", "../../../@strapi/plugin-users-permissions/admin/src/pages/AdvancedSettings/utils/schema.js", "../../../@strapi/plugin-users-permissions/admin/src/pages/AdvancedSettings/index.jsx"], "sourcesContent": ["import { getTrad } from '../../../utils';\n\nconst layout = [\n  {\n    label: {\n      id: getTrad('EditForm.inputToggle.label.email'),\n      defaultMessage: 'One account per email address',\n    },\n    hint: {\n      id: getTrad('EditForm.inputToggle.description.email'),\n      defaultMessage:\n        'Disallow the user to create multiple accounts using the same email address with different authentication providers.',\n    },\n    name: 'unique_email',\n    type: 'boolean',\n    size: 12,\n  },\n  {\n    label: {\n      id: getTrad('EditForm.inputToggle.label.sign-up'),\n      defaultMessage: 'Enable sign-ups',\n    },\n    hint: {\n      id: getTrad('EditForm.inputToggle.description.sign-up'),\n      defaultMessage:\n        'When disabled (OFF), the registration process is forbidden. No one can subscribe anymore no matter the used provider.',\n    },\n    name: 'allow_register',\n    type: 'boolean',\n    size: 12,\n  },\n  {\n    label: {\n      id: getTrad('EditForm.inputToggle.label.email-reset-password'),\n      defaultMessage: 'Reset password page',\n    },\n    hint: {\n      id: getTrad('EditForm.inputToggle.description.email-reset-password'),\n      defaultMessage: \"URL of your application's reset password page.\",\n    },\n    placeholder: {\n      id: getTrad('EditForm.inputToggle.placeholder.email-reset-password'),\n      defaultMessage: 'ex: https://youtfrontend.com/reset-password',\n    },\n    name: 'email_reset_password',\n    type: 'string',\n    size: 12,\n  },\n  {\n    label: {\n      id: getTrad('EditForm.inputToggle.label.email-confirmation'),\n      defaultMessage: 'Enable email confirmation',\n    },\n    hint: {\n      id: getTrad('EditForm.inputToggle.description.email-confirmation'),\n      defaultMessage: 'When enabled (ON), new registered users receive a confirmation email.',\n    },\n    name: 'email_confirmation',\n    type: 'boolean',\n    size: 12,\n  },\n  {\n    label: {\n      id: getTrad('EditForm.inputToggle.label.email-confirmation-redirection'),\n      defaultMessage: 'Redirection url',\n    },\n    hint: {\n      id: getTrad('EditForm.inputToggle.description.email-confirmation-redirection'),\n      defaultMessage: 'After you confirmed your email, choose where you will be redirected.',\n    },\n    placeholder: {\n      id: getTrad('EditForm.inputToggle.placeholder.email-confirmation-redirection'),\n      defaultMessage: 'ex: https://youtfrontend.com/email-confirmation',\n    },\n    name: 'email_confirmation_redirection',\n    type: 'string',\n    size: 12,\n  },\n];\n\nexport default layout;\n", "import { translatedErrors } from '@strapi/strapi/admin';\nimport * as yup from 'yup';\n\n// eslint-disable-next-line prefer-regex-literals\nconst URL_REGEX = new RegExp('(^$)|((.+:\\\\/\\\\/.*)(d*)\\\\/?(.*))');\n\nconst schema = yup.object().shape({\n  email_confirmation_redirection: yup.mixed().when('email_confirmation', {\n    is: true,\n    then: yup.string().matches(URL_REGEX).required(),\n    otherwise: yup.string().nullable(),\n  }),\n  email_reset_password: yup\n    .string(translatedErrors.string)\n    .matches(URL_REGEX, {\n      id: translatedErrors.regex.id,\n      defaultMessage: 'This is not a valid URL',\n    })\n    .nullable(),\n});\n\nexport default schema;\n", "import React from 'react';\n\nimport { <PERSON>, <PERSON>ton, Flex, Grid, Typography, useNotifyAT } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport {\n  useAPIErrorHandler,\n  Page,\n  Form,\n  InputRenderer,\n  useNotification,\n  useFetchClient,\n  useRBAC,\n  Layouts,\n} from '@strapi/strapi/admin';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\n\nimport { PERMISSIONS } from '../../constants';\nimport { getTrad } from '../../utils';\n\nimport layout from './utils/layout';\nimport schema from './utils/schema';\n\nconst ProtectedAdvancedSettingsPage = () => (\n  <Page.Protect permissions={PERMISSIONS.readAdvancedSettings}>\n    <AdvancedSettingsPage />\n  </Page.Protect>\n);\n\nconst AdvancedSettingsPage = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { notifyStatus } = useNotifyAT();\n  const queryClient = useQueryClient();\n  const { get, put } = useFetchClient();\n  const { formatAPIError } = useAPIErrorHandler();\n\n  const {\n    isLoading: isLoadingForPermissions,\n    allowedActions: { canUpdate },\n  } = useRBAC({ update: PERMISSIONS.updateAdvancedSettings });\n\n  const { isLoading: isLoadingData, data } = useQuery(\n    ['users-permissions', 'advanced'],\n    async () => {\n      const { data } = await get('/users-permissions/advanced');\n\n      return data;\n    },\n    {\n      onSuccess() {\n        notifyStatus(\n          formatMessage({\n            id: getTrad('Form.advancedSettings.data.loaded'),\n            defaultMessage: 'Advanced settings data has been loaded',\n          })\n        );\n      },\n      onError() {\n        toggleNotification({\n          type: 'danger',\n          message: formatMessage({\n            id: getTrad('notification.error'),\n            defaultMessage: 'An error occured',\n          }),\n        });\n      },\n    }\n  );\n\n  const isLoading = isLoadingForPermissions || isLoadingData;\n\n  const submitMutation = useMutation((body) => put('/users-permissions/advanced', body), {\n    async onSuccess() {\n      await queryClient.invalidateQueries(['users-permissions', 'advanced']);\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTrad('notification.success.saved'),\n          defaultMessage: 'Saved',\n        }),\n      });\n    },\n    onError(error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    },\n    refetchActive: true,\n  });\n\n  const { isLoading: isSubmittingForm } = submitMutation;\n\n  const handleSubmit = async (body) => {\n    submitMutation.mutate({\n      ...body,\n      email_confirmation_redirection: body.email_confirmation\n        ? body.email_confirmation_redirection\n        : '',\n    });\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Page.Main aria-busy={isSubmittingForm}>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: formatMessage({\n              id: getTrad('HeaderNav.link.advancedSettings'),\n              defaultMessage: 'Advanced Settings',\n            }),\n          }\n        )}\n      </Page.Title>\n      <Form onSubmit={handleSubmit} initialValues={data.settings} validationSchema={schema}>\n        {({ values, isSubmitting, modified }) => {\n          return (\n            <>\n              <Layouts.Header\n                title={formatMessage({\n                  id: getTrad('HeaderNav.link.advancedSettings'),\n                  defaultMessage: 'Advanced Settings',\n                })}\n                primaryAction={\n                  <Button\n                    loading={isSubmitting}\n                    type=\"submit\"\n                    disabled={!modified || !canUpdate}\n                    startIcon={<Check />}\n                    size=\"S\"\n                  >\n                    {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n                  </Button>\n                }\n              />\n              <Layouts.Content>\n                <Box\n                  background=\"neutral0\"\n                  hasRadius\n                  shadow=\"filterShadow\"\n                  paddingTop={6}\n                  paddingBottom={6}\n                  paddingLeft={7}\n                  paddingRight={7}\n                >\n                  <Flex direction=\"column\" alignItems=\"stretch\" gap={4}>\n                    <Typography variant=\"delta\" tag=\"h2\">\n                      {formatMessage({\n                        id: 'global.settings',\n                        defaultMessage: 'Settings',\n                      })}\n                    </Typography>\n                    <Grid.Root gap={6}>\n                      {[\n                        {\n                          label: {\n                            id: getTrad('EditForm.inputSelect.label.role'),\n                            defaultMessage: 'Default role for authenticated users',\n                          },\n                          hint: {\n                            id: getTrad('EditForm.inputSelect.description.role'),\n                            defaultMessage:\n                              'It will attach the new authenticated user to the selected role.',\n                          },\n                          options: data.roles.map((role) => ({\n                            label: role.name,\n                            value: role.type,\n                          })),\n                          name: 'default_role',\n                          size: 6,\n                          type: 'enumeration',\n                        },\n                        ...layout,\n                      ].map(({ size, ...field }) => (\n                        <Grid.Item\n                          key={field.name}\n                          col={size}\n                          direction=\"column\"\n                          alignItems=\"stretch\"\n                        >\n                          <InputRenderer\n                            {...field}\n                            disabled={\n                              field.name === 'email_confirmation_redirection' &&\n                              values.email_confirmation === false\n                            }\n                            label={formatMessage(field.label)}\n                            hint={field.hint ? formatMessage(field.hint) : undefined}\n                            placeholder={\n                              field.placeholder ? formatMessage(field.placeholder) : undefined\n                            }\n                          />\n                        </Grid.Item>\n                      ))}\n                    </Grid.Root>\n                  </Flex>\n                </Box>\n              </Layouts.Content>\n            </>\n          );\n        }}\n      </Form>\n    </Page.Main>\n  );\n};\n\nexport { ProtectedAdvancedSettingsPage, AdvancedSettingsPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAM,SAAS;EACb;IACE,OAAO;MACL,IAAI,QAAQ,kCAAkC;MAC9C,gBAAgB;IACjB;IACD,MAAM;MACJ,IAAI,QAAQ,wCAAwC;MACpD,gBACE;IACH;IACD,MAAM;IACN,MAAM;IACN,MAAM;EACP;EACD;IACE,OAAO;MACL,IAAI,QAAQ,oCAAoC;MAChD,gBAAgB;IACjB;IACD,MAAM;MACJ,IAAI,QAAQ,0CAA0C;MACtD,gBACE;IACH;IACD,MAAM;IACN,MAAM;IACN,MAAM;EACP;EACD;IACE,OAAO;MACL,IAAI,QAAQ,iDAAiD;MAC7D,gBAAgB;IACjB;IACD,MAAM;MACJ,IAAI,QAAQ,uDAAuD;MACnE,gBAAgB;IACjB;IACD,aAAa;MACX,IAAI,QAAQ,uDAAuD;MACnE,gBAAgB;IACjB;IACD,MAAM;IACN,MAAM;IACN,MAAM;EACP;EACD;IACE,OAAO;MACL,IAAI,QAAQ,+CAA+C;MAC3D,gBAAgB;IACjB;IACD,MAAM;MACJ,IAAI,QAAQ,qDAAqD;MACjE,gBAAgB;IACjB;IACD,MAAM;IACN,MAAM;IACN,MAAM;EACP;EACD;IACE,OAAO;MACL,IAAI,QAAQ,2DAA2D;MACvE,gBAAgB;IACjB;IACD,MAAM;MACJ,IAAI,QAAQ,iEAAiE;MAC7E,gBAAgB;IACjB;IACD,aAAa;MACX,IAAI,QAAQ,iEAAiE;MAC7E,gBAAgB;IACjB;IACD,MAAM;IACN,MAAM;IACN,MAAM;EACP;AACH;AC1EA,IAAM,YAAY,IAAI,OAAO,kCAAkC;AAE/D,IAAM,SAAaA,QAAQ,EAAC,MAAM;EAChC,gCAAoC,OAAA,EAAQ,KAAK,sBAAsB;IACrE,IAAI;IACJ,MAAUA,QAAM,EAAG,QAAQ,SAAS,EAAE,SAAU;IAChD,WAAeA,QAAQ,EAAC,SAAU;EACtC,CAAG;EACD,sBACGA,QAAO,YAAiB,MAAM,EAC9B,QAAQ,WAAW;IAClB,IAAI,YAAiB,MAAM;IAC3B,gBAAgB;EACtB,CAAK,EACA,SAAU;AACf,CAAC;ACIK,IAAA,gCAAgC,UACpC,wBAAC,KAAK,SAAL,EAAa,aAAa,YAAY,sBACrC,cAAC,wBAAA,sBAAA,CAAA,CAAqB,EACxB,CAAA;AAGF,IAAM,uBAAuB,MAAM;AAC3B,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA,EAAE,aAAA,IAAiB,YAAA;AACzB,QAAM,cAAc,eAAA;AACpB,QAAM,EAAE,KAAK,IAAI,IAAI,eAAe;AAC9B,QAAA,EAAE,eAAA,IAAmB,mBAAA;AAErB,QAAA;IACJ,WAAW;IACX,gBAAgB,EAAE,UAAU;EAAA,IAC1B,QAAQ,EAAE,QAAQ,YAAY,uBAAwB,CAAA;AAE1D,QAAM,EAAE,WAAW,eAAe,KAAS,IAAA;IACzC,CAAC,qBAAqB,UAAU;IAChC,YAAY;AACV,YAAM,EAAE,MAAAC,MAAAA,IAAS,MAAM,IAAI,6BAA6B;AAEjDA,aAAAA;IACT;IACA;MACE,YAAY;AACV;UACE,cAAc;YACZ,IAAI,QAAQ,mCAAmC;YAC/C,gBAAgB;UAAA,CACjB;QAAA;MAEL;MACA,UAAU;AACW,2BAAA;UACjB,MAAM;UACN,SAAS,cAAc;YACrB,IAAI,QAAQ,oBAAoB;YAChC,gBAAgB;UAAA,CACjB;QAAA,CACF;MACH;IACF;EAAA;AAGF,QAAM,YAAY,2BAA2B;AAE7C,QAAM,iBAAiB,YAAY,CAAC,SAAS,IAAI,+BAA+B,IAAI,GAAG;IACrF,MAAM,YAAY;AAChB,YAAM,YAAY,kBAAkB,CAAC,qBAAqB,UAAU,CAAC;AAElD,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI,QAAQ,4BAA4B;UACxC,gBAAgB;QAAA,CACjB;MAAA,CACF;IACH;IACA,QAAQ,OAAO;AACM,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IACH;IACA,eAAe;EAAA,CAChB;AAEK,QAAA,EAAE,WAAW,iBAAqB,IAAA;AAElC,QAAA,eAAe,OAAO,SAAS;AACnC,mBAAe,OAAO;MACpB,GAAG;MACH,gCAAgC,KAAK,qBACjC,KAAK,iCACL;IAAA,CACL;EAAA;AAGH,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEA,aACG,yBAAA,KAAK,MAAL,EAAU,aAAW,kBACpB,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM,cAAc;UAClB,IAAI,QAAQ,iCAAiC;UAC7C,gBAAgB;QAAA,CACjB;MACH;IAAA,EAAA,CAEJ;QACC,wBAAA,MAAA,EAAK,UAAU,cAAc,eAAe,KAAK,UAAU,kBAAkB,QAC3E,UAAC,CAAA,EAAE,QAAQ,cAAc,SAAA,MAAe;AACvC,iBAEI,yBAAA,6BAAA,EAAA,UAAA;YAAA;UAAC,QAAQ;UAAR;YACC,OAAO,cAAc;cACnB,IAAI,QAAQ,iCAAiC;cAC7C,gBAAgB;YAAA,CACjB;YACD,mBACE;cAAC;cAAA;gBACC,SAAS;gBACT,MAAK;gBACL,UAAU,CAAC,YAAY,CAAC;gBACxB,eAAA,wBAAY,eAAM,CAAA,CAAA;gBAClB,MAAK;gBAEJ,UAAA,cAAc,EAAE,IAAI,eAAe,gBAAgB,OAAA,CAAQ;cAAA;YAC9D;UAAA;QAEJ;YACA,wBAAC,QAAQ,SAAR,EACC,cAAA;UAAC;UAAA;YACC,YAAW;YACX,WAAS;YACT,QAAO;YACP,YAAY;YACZ,eAAe;YACf,aAAa;YACb,cAAc;YAEd,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;kBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cACjB,CAAA,EAAA,CACH;kBACC,wBAAA,KAAK,MAAL,EAAU,KAAK,GACb,UAAA;gBACC;kBACE,OAAO;oBACL,IAAI,QAAQ,iCAAiC;oBAC7C,gBAAgB;kBAClB;kBACA,MAAM;oBACJ,IAAI,QAAQ,uCAAuC;oBACnD,gBACE;kBACJ;kBACA,SAAS,KAAK,MAAM,IAAI,CAAC,UAAU;oBACjC,OAAO,KAAK;oBACZ,OAAO,KAAK;kBAAA,EACZ;kBACF,MAAM;kBACN,MAAM;kBACN,MAAM;gBACR;gBACA,GAAG;cAAA,EACH,IAAI,CAAC,EAAE,MAAM,GAAG,MAChB,UAAA;gBAAC,KAAK;gBAAL;kBAEC,KAAK;kBACL,WAAU;kBACV,YAAW;kBAEX,cAAA;oBAAC;oBAAA;sBACE,GAAG;sBACJ,UACE,MAAM,SAAS,oCACf,OAAO,uBAAuB;sBAEhC,OAAO,cAAc,MAAM,KAAK;sBAChC,MAAM,MAAM,OAAO,cAAc,MAAM,IAAI,IAAI;sBAC/C,aACE,MAAM,cAAc,cAAc,MAAM,WAAW,IAAI;oBAAA;kBAE3D;gBAAA;gBAhBK,MAAM;cAkBd,CAAA,EAAA,CACH;YAAA,EAAA,CACF;UAAA;QAAA,EAAA,CAEJ;MACF,EAAA,CAAA;IAAA,EAAA,CAGN;EACF,EAAA,CAAA;AAEJ;", "names": ["create", "data"]}