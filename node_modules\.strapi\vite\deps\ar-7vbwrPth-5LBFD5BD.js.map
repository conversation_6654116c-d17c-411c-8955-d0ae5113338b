{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/ar-7vbwrPth.mjs"], "sourcesContent": ["const Email = \"البريد الإلكتروني\";\nconst Password = \"كلمة السر\";\nconst Provider = \"مزود\";\nconst ResetPasswordToken = \"إعادة تعيين كلمة المرور\";\nconst Role = \"قاعدة\";\nconst Username = \"اسم المستخدم\";\nconst Users = \"المستخدمين\";\nconst Analytics = \"تحليلات\";\nconst anErrorOccurred = \"عذرًا! هناك خطأ ما. حاول مرة اخرى.\";\nconst clearLabel = \"حذف\";\nconst dark = \"داكن\";\nconst Documentation = \"توثيق\";\nconst light = \"فاتح\";\nconst or = \"أو\";\nconst selectButtonTitle = \"يختار\";\nconst skipToContent = \"تخطى الى المحتوى\";\nconst submit = \"يُقدِّم\";\nconst ar = {\n\t\"Auth.form.button.forgot-password\": \"إرسال للبريد\",\n\t\"Auth.form.button.login\": \"تسجيل دخول\",\n\t\"Auth.form.button.register\": \"مستعد للبدء\",\n\t\"Auth.form.email.label\": \"البريد الإكتروني\",\n\t\"Auth.form.email.placeholder\": \"<EMAIL>\",\n\t\"Auth.form.error.code.provide\": \"الرمز المقدم غير صحيح.\",\n\t\"Auth.form.error.email.invalid\": \"هذا البريد الاكتروني غير صالح.\",\n\t\"Auth.form.error.email.provide\": \"يرجى تقديم اسم المستخدم الخاص بك أو البريد الإلكتروني الخاص بك.\",\n\t\"Auth.form.error.email.taken\": \"البريد الإلكتروني مسجل بالفعل\",\n\t\"Auth.form.error.invalid\": \"المعرّف أو كلمة المرور غير صالحين.\",\n\t\"Auth.form.error.params.provide\": \"المعلومات المقدمة غير صحيحة.\",\n\t\"Auth.form.error.password.format\": \"لا يمكن أن تحتوي كلمة مرورك على الرمز `$` أكثر من ثلاث مرات.\",\n\t\"Auth.form.error.password.local\": \"لم يقم هذا المستخدم بتعيين كلمة مرور محلية مطلقًا ، الرجاء تسجيل الدخول عبر الموفر المستخدم أثناء إنشاء الحساب.\",\n\t\"Auth.form.error.password.matching\": \"كلمة المرور غير مطابقة.\",\n\t\"Auth.form.error.password.provide\": \"يرجى تقديم كلمة المرور الخاصة بك.\",\n\t\"Auth.form.error.user.not-exist\": \"هذا الإميل غير موجود.\",\n\t\"Auth.form.error.username.taken\": \"اسم المستخدم مسجل بالفعل\",\n\t\"Auth.form.forgot-password.email.label\": \"ادخل ايميلك\",\n\t\"Auth.form.forgot-password.email.label.success\": \"تم إرسال الرسالة بنجاح الى\",\n\t\"Auth.form.rememberMe.label\": \"تذكرني\",\n\t\"Auth.form.username.label\": \"اسم المستخدم\",\n\t\"Auth.form.username.placeholder\": \"اكتب اسمك هنا (مثل: خالد سالم)\",\n\t\"Auth.link.forgot-password\": \"هل نسيت كلمة السر الخاصة بك؟\",\n\t\"Auth.link.ready\": \"مستعد لتسجيل الدخول؟\",\n\t\"Content Manager\": \"مدير محتوى\",\n\tEmail: Email,\n\t\"Files Upload\": \"رفع الملفات\",\n\t\"New entry\": \"إدخال جديد\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"المستخدمين والصلاحيات\",\n\t\"app.components.BlockLink.code\": \"امثلة للشفرة\",\n\t\"app.components.Button.cancel\": \"الغاء\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"قادم قريبًا\",\n\t\"app.components.DownloadInfo.download\": \"التنزيل قيد التقدم...\",\n\t\"app.components.DownloadInfo.text\": \"قد يستغرق هذا دقيقة. شكرا لصبرك.\",\n\t\"app.components.EmptyAttributes.title\": \"لا يوجد اي حقول بعد\",\n\t\"app.components.HomePage.button.blog\": \"اظهار المزيد على المدونة\",\n\t\"app.components.HomePage.community\": \"البحث عن المجتمع في الويب\",\n\t\"app.components.HomePage.community.content\": \"ناقش مع أعضاء الفريق والمساهمين والمطورين على قنوات مختلفة.\",\n\t\"app.components.HomePage.welcome\": \"مرحبًا في لوحتك!\",\n\t\"app.components.HomePage.welcome.again\": \"مرحبًا \",\n\t\"app.components.HomePage.welcomeBlock.content\": \"نحن سعداء بوجودك كأحد أفراد المجتمع. نحن نبحث باستمرار عن ردود الفعل لا تتردد في مراسلتنا على الخاص \",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"نأمل أن تحقق تقدمًا في مشروعك ... لا تتردد في قراءة عن اخر إصدار جديد من Strapi. نحن نبذل قصارى جهدنا لتحسين المنتج بناء على ملاحظاتك.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"issues.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" أو رفع \",\n\t\"app.components.ImgPreview.hint\": \"اسحب الملف واسقطة في هذه المساحة او في {browse} لرفعة\",\n\t\"app.components.ImgPreview.hint.browse\": \"المتصفح\",\n\t\"app.components.InputFile.newFile\": \"إضافة ملف جديد\",\n\t\"app.components.InputFileDetails.open\": \"فتح في نافذة جديدة\",\n\t\"app.components.InputFileDetails.originalName\": \"الاسم الاصلي:\",\n\t\"app.components.InputFileDetails.remove\": \"حذف هذا الملف\",\n\t\"app.components.InputFileDetails.size\": \"الحجم:\",\n\t\"app.components.InstallPluginPage.description\": \"قم بتوسيع التطبيق الخاص بك دون عناء.\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"مندعوم من \",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"التهيئة\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"عام\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"لا توجد إضافات مثبته بعد\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"إضافات\",\n\t\"app.components.ListPluginsPage.description\": \"قائمة الإضافيات المثبتة في المشروع.\",\n\t\"app.components.ListPluginsPage.head.title\": \"قائمة الإضافات\",\n\t\"app.components.Logout.logout\": \"الخروج\",\n\t\"app.components.Logout.profile\": \"الملف الشخصي\",\n\t\"app.components.NotFoundPage.back\": \"العودة للرئيسية\",\n\t\"app.components.NotFoundPage.description\": \"لا يوجد\",\n\t\"app.components.Official\": \"الرسمية\",\n\t\"app.components.PluginCard.Button.label.download\": \"تنزيل\",\n\t\"app.components.PluginCard.Button.label.install\": \"مثبت\",\n\t\"app.components.PluginCard.compatible\": \"متوافق مع تطبيقك\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"متوافق مع المجتمع\",\n\t\"app.components.PluginCard.more-details\": \"المزيد من التفاصيل\",\n\t\"app.components.listPlugins.button\": \"إضافة إضافة جديدة\",\n\t\"app.components.listPlugins.title.none\": \"لا يوجد اي إضافات مثبته\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"حدث خطأ أثناء إلغاء تثبيت الإضافة\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"components.AutoReloadBlocker.header\": \"مطلوب ميزة إعادة التحميل لهذه الإضافة.\",\n\t\"components.ErrorBoundary.title\": \"هناك خطأ ما...\",\n\t\"components.Input.error.attribute.key.taken\": \"هذه القيمة موجودة مسبقًا\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"لا تتطابق\",\n\t\"components.Input.error.attribute.taken\": \"اسم الحقل هذا مستخدم مسبقًا\",\n\t\"components.Input.error.contentTypeName.taken\": \"هذه الاسم مستخدم مسبقًا\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"كلمات السر لا تتطابق\",\n\t\"components.Input.error.validation.email\": \"هذا ليس بريد الإكتروني\",\n\t\"components.Input.error.validation.json\": \"لا يتطابق مع صيغة JSON\",\n\t\"components.Input.error.validation.max\": \"هذه القيمة عالية جدًا {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"هذه القيمة طويلة جدًا {max}.\",\n\t\"components.Input.error.validation.min\": \"هذه القيمة قليلة جدًا {min}.\",\n\t\"components.Input.error.validation.minLength\": \"هذه القيمة قصيرة جدًا {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"لا يمكن أن تكون متفوقة\",\n\t\"components.Input.error.validation.regex\": \"هذه القمية لا تطابق regex.\",\n\t\"components.Input.error.validation.required\": \"هذه القيمة مطلوبة.\",\n\t\"components.ListRow.empty\": \"لا توجد بيانات ليتم عرضها.\",\n\t\"components.OverlayBlocker.description\": \"أنت تستخدم ميزة تحتاج إلى إعادة تشغيل الخادم. يرجى الانتظار حتى يعود الخادم.\",\n\t\"components.OverlayBlocker.title\": \"في انتظار إعادة التشغيل...\",\n\t\"components.PageFooter.select\": \"إدخالات لكل صفحة\",\n\t\"components.ProductionBlocker.description\": \"لأغراض السلامة ، يتعين علينا تعطيل هذه الإضافة في بيئات أخرى.\",\n\t\"components.ProductionBlocker.header\": \"هذه الإضافة متاحة فقط في التطوير.\",\n\t\"components.Wysiwyg.collapse\": \"تقليص\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"العنوان H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"العنوان H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"العنوان H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"العنوان H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"العنوان H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"العنوان H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"إضافة عنوان\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"الأحرف\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"توسيع\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"اسحب الملفات وأفلتها ، والصقها من الحافظة أو {browse}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"حددهم\",\n\t\"components.popUpWarning.message\": \"هل انت متاكد من حذف هذا؟\",\n\t\"components.popUpWarning.title\": \"ارجو التأكيد\",\n\t\"notification.error\": \"حدث خطأ\",\n\t\"notification.error.layout\": \"تعذّر استرداد التنسيق\",\n\t\"request.error.model.unknown\": \"هذا النموذج غير موجود\",\n\t\"admin.pages.MarketPlacePage.filters.categories\": \"فئات\",\n\t\"admin.pages.MarketPlacePage.filters.collections\": \"المجموعات\",\n\t\"admin.pages.MarketPlacePage.head\": \"السوق - الإضافات\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"أخبرنا ما هو المكون الإضافي الذي تبحث عنه وسنعلم مطوري المكونات الإضافية في مجتمعنا في حال كانوا يبحثون عن الإلهام!\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"هل فقدت مكونًا إضافيًا؟\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"يجب أن تكون متصلاً بالإنترنت للوصول إلى سوق سترابي\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"انت غير متصل\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"أمر نسخ التثبيت\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"قم بتثبيت الأمر جاهزًا ليتم لصقه في الجهاز الطرفي\",\n\t\"admin.pages.MarketPlacePage.plugin.downloads\": \"يتم تنزيل هذا المكون الإضافي {downloadsCount} أسبوعيًا\",\n\t\"admin.pages.MarketPlacePage.plugin.githubStars\": \"تم تمييز هذا المكون الإضافي بنجمة على GitHub\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"يتعلم أكثر\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"تعرف على المزيد حول {pluginName}\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"أكثر\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"المثبتة\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"صنع بواسطة ستربي\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Strapi تم التحقق من البرنامج المساعد من قبل \",\n\t\"admin.pages.MarketPlacePage.plugins\": \"الإضافات\",\n\t\"admin.pages.MarketPlacePage.provider.downloads\": \"هذا الموفر لديه {downloadsCount} من التنزيلات الأسبوعية\",\n\t\"admin.pages.MarketPlacePage.provider.githubStars\": \"{GitHub} على {starsCount} تم تميز هذا المزود \",\n\t\"admin.pages.MarketPlacePage.providers\": \"الموفرون\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"مسح البحث\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \" \\\"{target}\\\" لا توجد نتيجة ل\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"يبحث\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical\": \"ترتيب ابجدي\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"فرز حسب الترتيب الأبجدي\",\n\t\"admin.pages.MarketPlacePage.sort.githubStars\": \"GitHub عدد نجوم\",\n\t\"admin.pages.MarketPlacePage.sort.githubStars.selected\": \"GitHub الترتيب حسب نجوم\",\n\t\"admin.pages.MarketPlacePage.sort.newest\": \"الأحدث\",\n\t\"admin.pages.MarketPlacePage.sort.newest.selected\": \"ترتيب حسب الأحدث\",\n\t\"admin.pages.MarketPlacePage.sort.npmDownloads\": \"عدد التنزيلات\",\n\t\"admin.pages.MarketPlacePage.sort.npmDownloads.selected\": \"npm فرز حسب التنزيلات \",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"إرسال البرنامج المساعد\",\n\t\"admin.pages.MarketPlacePage.submit.provider.link\": \"إرسال مزود\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \" Strapi احصل على المزيد من\",\n\t\"admin.pages.MarketPlacePage.tab-group.label\": \"Strapi الإضافات ومقدمي \",\n\tAnalytics: Analytics,\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"نسخ إلى الحافظة\",\n\t\"app.component.search.label\": \"{target} بحث عن\",\n\t\"app.component.table.duplicate\": \"{target} ينسخ\",\n\t\"app.component.table.edit\": \"{target} يحرر\",\n\t\"app.component.table.read\": \"{target}يقرأ\",\n\t\"app.component.table.select.one-entry\": \"{target}يختار\",\n\t\"app.component.table.view\": \"{target} تفاصيل\",\n\t\"app.components.BlockLink.blog\": \"مدونة\",\n\t\"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count, plural, =0 {No categories} واحد {# category} آخر{# categories}} المحدد\",\n\t\"admin.pages.MarketPlacePage.plugin.version\": \" قم بتحديث إصدار الخاص بك  : \\\"{strapiAppVersion}\\\" ل: \\\"{versionRange}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.version.null\": \"تعذر التحقق من التوافق مع إصدار Strapi الخاص بك: \\\"{strapiAppVersion}\\\"\",\n\t\"app.components.BlockLink.blog.content\": \"اقرأ آخر الأخبار حول Strapi والنظام البيئي.\",\n\t\"app.components.BlockLink.cloud\": \"Strapi سحاب\",\n\t\"app.components.BlockLink.cloud.content\": \"نظام أساسي قابل للإنشاء والتعاون بشكل كامل لزيادة سرعة فريقك.\",\n\t\"app.components.BlockLink.code.content\": \"تعلم من خلال اختبار المشاريع الحقيقية التي طورها المجتمع.\",\n\t\"app.components.BlockLink.documentation.content\": \"اكتشف المفاهيم الأساسية والأدلة والتعليمات.\",\n\t\"app.components.BlockLink.tutorial\": \"دروس\",\n\t\"app.components.BlockLink.tutorial.content\": \"Strapi اتبع التعليمات خطوة بخطوة للاستخدام والتخصيص.\",\n\t\"app.components.Button.confirm\": \"يتأكد\",\n\t\"app.components.Button.reset\": \"إعادة ضبط\",\n\t\"app.components.ConfirmDialog.title\": \"تأكيد\",\n\t\"app.components.EmptyStateLayout.content-document\": \"لم يتم العثور على محتوى\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"ليس لديك أذونات للوصول إلى هذا المحتوى\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>قم بإنشاء رمز المصادقة هنا واسترجع المحتوى الذي أنشأته للتو.</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"API إنشاء رمز\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 مشاهدة المحتوى في العمل\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"العودة الى الصفحة الرئيسية\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"✅ الخطوة 3: اكتمل\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>قم بإنشاء وإدارة كل المحتوى هنا في إدارة المحتوى.</p><p>مثال: أخذ مثال موقع المدونة إلى أبعد من ذلك ، يمكن للمرء كتابة مقال وحفظه ونشره كما يحلو له.</p><p>💡 نصيحة سريعة - لا تنس النقر على ’نشر’ على المحتوى الذي تنشئه.</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"⚡️ أنشئ محتوى\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>رائع ، خطوة أخيرة يجب أن تبدأ بها!</p><b>🚀 مشاهدة المحتوى في العمل</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"API اختبر ملف\",\n\t\"app.components.GuidedTour.CM.success.title\": \"✅ الخطوة 2: اكتمل\",\n\t\"app.components.GuidedTour.create-content\": \"أنشئ محتوى\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>تساعدك أنواع المجموعات على إدارة عدة إدخالات ، والأنواع الفردية مناسبة لإدارة إدخال واحد فقط. </ p> <p> على سبيل المثال: بالنسبة إلى موقع مدونة ، ستكون المقالات من نوع المجموعة بينما تكون الصفحة الرئيسية من النوع الفردي.</p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"بناء نوع المجموعة\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 قم بإنشاء أول نوع مجموعة\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>جيد!</p><b>⚡️ ما الذي تود مشاركته مع العالم؟</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"الخطوة 1:  ✅ مكتمل\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"API اختبار \",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️ ما الذي تود مشاركته مع العالم؟\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"Content type Builder انتقل إلى\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 بناء هيكل المحتوى\",\n\t\"app.components.GuidedTour.skip\": \"تخطي الجولة\",\n\t\"app.components.GuidedTour.title\": \"خطوات للبدء ٣\",\n\t\"app.components.HomePage.create\": \"قم بإنشاء نوع المحتوى الأول الخاص بك\",\n\t\"app.components.HomePage.roadmap\": \"انظر خارطة الطريق لدينا\",\n\t\"app.components.InstallPluginPage.Download.description\": \"قد يستغرق تنزيل المكون الإضافي وتثبيته بضع ثوانٍ\",\n\t\"app.components.InstallPluginPage.Download.title\": \"جارى التحميل...\",\n\t\"app.components.LeftMenu.collapse\": \"تصغير شريط التنقل\",\n\t\"app.components.LeftMenu.expand\": \"قم بتوسيع شريط التنقل\",\n\t\"app.components.LeftMenu.general\": \"عام\",\n\t\"app.components.LeftMenu.logo.alt\": \"شعار التطبيق\",\n\t\"app.components.LeftMenu.logout\": \"تسجيل خروج\",\n\t\"app.components.LeftMenu.navbrand.title\": \"Strapi لوحة القيادة\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"مكان العمل\",\n\t\"app.components.LeftMenu.plugins\": \"الإضافات\",\n\t\"app.components.LeftMenuFooter.help\": \"يساعد\",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"أنواع المجموعات\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"أنواع مفردة\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"قد يستغرق الأمر بضع ثوان لإلغاء تثبيت المكون الإضافي\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"إلغاء التثبيت\",\n\t\"app.components.MarketplaceBanner\": \"اكتشف المكونات الإضافية التي أنشأها المجتمع ، والعديد من الأشياء الرائعة لبدء مشروعك ، في سوق Strapi.\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"شعار صاروخ Strapi\",\n\t\"app.components.MarketplaceBanner.link\": \"افحصه الآن\",\n\t\"app.components.Onboarding.help.button\": \"زر المساعدة\",\n\t\"app.components.Onboarding.label.completed\": \"% مكتمل\",\n\t\"app.components.Onboarding.link.build-content\": \"بناء بنية المحتوى\",\n\t\"app.components.Onboarding.link.manage-content\": \"إضافة وإدارة المحتوى\",\n\t\"app.components.Onboarding.link.manage-media\": \"إدارة الوسائط\",\n\t\"app.components.Onboarding.link.more-videos\": \"شاهد المزيد من مقاطع الفيديو\",\n\t\"app.components.Onboarding.title\": \"ابدأ مقاطع الفيديو\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"يجب تمكين ميزة AutoReload. `yarn develop` يرجى بدء تطبيقك بـ .\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"أفهم!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"لأسباب أمنية ، لا يمكن تنزيل المكون الإضافي إلا في بيئة التطوير.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"التنزيل مستحيل\",\n\t\"app.components.ToggleCheckbox.off-label\": \"خطأ\",\n\t\"app.components.ToggleCheckbox.on-label\": \"حقيقي\",\n\t\"app.components.Users.MagicLink.connect\": \"انسخ هذا الرابط وشاركه لمنح حق الوصول لهذا المستخدم\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"أرسل هذا الرابط إلى المستخدم ، حيث يمكن إجراء أول تسجيل دخول عبر موفر خدمة الدخول الموحد\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"بيانات المستخدم\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"أدوار المستخدم\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"يمكن للمستخدم أن يكون له دور واحد أو عدة أدوار\",\n\t\"app.components.Users.SortPicker.button-label\": \"ترتيب حسب\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"البريد الإلكتروني (من الألف إلى الياء)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"بريد إلكتروني (من ي إلى أ)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"الاسم الأول (من الألف إلى الياء)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"الاسم الأول (ي إلى أ)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"الاسم الأخير (من الألف إلى الياء)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"الاسم الأخير (ي إلى أ)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"اسم المستخدم (من الألف إلى الياء)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"اسم المستخدم (من ي إلى أ)\",\n\t\"app.containers.App.notification.error.init\": \"API حدث خطأ أثناء الطلب\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"إذا لم تستلم هذا الرابط ، فيرجى الاتصال بالمسؤول.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"قد يستغرق استلام رابط استعادة كلمة المرور بضع دقائق.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"أرسل البريد الإلكتروني\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"فعال\",\n\t\"app.containers.Users.EditPage.header.label\": \"تعديل {الاسم}\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"تحرير العضو\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"الأدوار المنسوبة\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"قم بدعوة المستخدم\",\n\t\"app.links.configure-view\": \"تكوين العرض\",\n\t\"app.page.not.found\": \"أُووبس! يبدو أنه لا يمكننا العثور على الصفحة التي تبحث عنها ...\",\n\t\"app.static.links.cheatsheet\": \"ورقة الغش\",\n\t\"app.utils.add-filter\": \"أضف عامل تصفية\",\n\t\"app.utils.close-label\": \"يغلق\",\n\t\"app.utils.delete\": \"يمسح\",\n\t\"app.utils.duplicate\": \"ينسخ\",\n\t\"app.utils.edit\": \"يحرر\",\n\t\"app.utils.errors.file-too-big.message\": \"الملف كبير جدًا\",\n\t\"app.utils.filter-value\": \"قيمة التصفية\",\n\t\"app.utils.filters\": \"المرشحات\",\n\t\"app.utils.notify.data-loaded\": \"{target} تم تحميل\",\n\t\"app.utils.publish\": \"أصدر\",\n\t\"app.utils.select-all\": \"اختر الكل\",\n\t\"app.utils.select-field\": \"حدد المجال\",\n\t\"app.utils.select-filter\": \"حدد عامل تصفية\",\n\t\"app.utils.unpublish\": \"إلغاء النشر\",\n\t\"Auth.components.Oops.text\": \"تم تعليق حسابك.\",\n\t\"Auth.components.Oops.text.admin\": \"إذا كان هذا خطأ ، يرجى الاتصال بالمسؤول.\",\n\t\"Auth.components.Oops.title\": \"أُووبس...\",\n\t\"Auth.form.active.label\": \"فعال\",\n\t\"Auth.form.button.go-home\": \"ارجع الى الصفحة الرئيسية\",\n\t\"Auth.form.button.login.providers.error\": \"لا يمكننا توصيلك من خلال المزود المحدد.\",\n\t\"Auth.form.button.login.strapi\": \"Strapi تسجيل الدخول عبر\",\n\t\"Auth.form.button.password-recovery\": \"استعادة كلمة السر\",\n\t\"Auth.form.confirmPassword.label\": \"تأكيد كلمة المرور\",\n\t\"Auth.form.currentPassword.label\": \"كلمة السر الحالية\",\n\t\"Auth.form.error.blocked\": \"تم حظر حسابك من قبل المسؤول.\",\n\t\"Auth.form.error.confirmed\": \"لم يتم تأكيد البريد الإلكتروني لحسابك.\",\n\t\"Auth.form.error.ratelimit\": \"محاولات كثيرة ، يرجى المحاولة مرة أخرى خلال دقيقة.\",\n\t\"Auth.form.firstname.label\": \"الاسم الأول\",\n\t\"Auth.form.firstname.placeholder\": \"على سبيل المثال سمر\",\n\t\"Auth.form.lastname.label\": \"اسم العائلة\",\n\t\"Auth.form.lastname.placeholder\": \"على سبيل المثال سامي\",\n\t\"Auth.form.password.hide-password\": \"اخفاء كلمة المرور\",\n\t\"Auth.form.password.hint\": \"يجب ألا يقل عدد الأحرف عن 8 أحرف ، وحرف كبير واحد ، ورقم واحد صغير ، ورقم واحد\",\n\t\"Auth.form.password.show-password\": \"عرض كلمة المرور\",\n\t\"Auth.form.register.news.label\": \"ابقني على اطلاع بالميزات الجديدة والتحسينات القادمة (من خلال القيام بذلك فأنت تقبل {terms} و ال {policy}).\",\n\t\"Auth.form.register.subtitle\": \"تُستخدم بيانات الاعتماد فقط للمصادقة في Strapi. سيتم تخزين جميع البيانات المحفوظة في قاعدة البيانات الخاصة بك.\",\n\t\"Auth.form.welcome.subtitle\": \"Strapi قم بتسجيل الدخول إلى حسابك على\",\n\t\"Auth.form.welcome.title\": \"Strapi! مرحبا بك في\",\n\t\"Auth.link.signin\": \"تسجيل الدخول\",\n\t\"Auth.link.signin.account\": \"هل لديك حساب؟\",\n\t\"Auth.login.sso.divider\": \"أو تسجيل الدخول باستخدام\",\n\t\"Auth.login.sso.loading\": \"تحميل الموفرين ...\",\n\t\"Auth.login.sso.subtitle\": \"SSO تسجيل الدخول إلى حسابك عبر\",\n\t\"Auth.privacy-policy-agreement.policy\": \"سياسة الخصوصية\",\n\t\"Auth.privacy-policy-agreement.terms\": \"شروط\",\n\t\"Auth.reset-password.title\": \"إعادة تعيين كلمة المرور\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"هذا المحتوى قيد الإنشاء حاليًا وسيعود في غضون أسابيع قليلة!\",\n\t\"component.Input.error.validation.integer\": \"يجب أن تكون القيمة عددًا صحيحًا\",\n\t\"components.AutoReloadBlocker.description\": \"Strapi قم بتشغيل  باستخدام أحد الأوامر التالية:\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"يحتوي على\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"يحتوي على (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"ينتهي بـ\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"ينتهي بـ (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"هو\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"هو (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"أكبر من\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"أكبر من أو يساوي\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"أقل من\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"أقل من أو يساوي\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"ليس\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"ليس (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"لا يحتوي على\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"لا يحتوي على (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"هو ليس لاشيء\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"هو لاشيء\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"يبدا ب\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"يبدا ب (case insensitive)\",\n\t\"components.Input.error.contain.lowercase\": \"يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل\",\n\t\"components.Input.error.contain.number\": \"يجب ان تحتوي كلمة المرور على الاقل رقما واحدا\",\n\t\"components.Input.error.contain.uppercase\": \"يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل\",\n\t\"components.Input.error.validation.lowercase\": \"يجب أن تكون القيمة سلسلة أحرف صغيرة\",\n\t\"components.Input.error.validation.unique\": \"هذه القيمة مستخدمة بالفعل.\",\n\t\"components.InputSelect.option.placeholder\": \"اختر هنا\",\n\t\"components.NotAllowedInput.text\": \"لا أذونات لرؤية هذا المجال\",\n\t\"components.OverlayBlocker.description.serverError\": \"يجب إعادة تشغيل الخادم ، يرجى التحقق من سجلاتك في المحطة.\",\n\t\"components.OverlayBlocker.title.serverError\": \"تستغرق إعادة التشغيل وقتًا أطول من المتوقع\",\n\t\"components.pagination.go-to\": \"{page} انتقل إلى صفحة\",\n\t\"components.pagination.go-to-next\": \"انتقل إلى الصفحة التالية\",\n\t\"components.pagination.go-to-previous\": \"الانتقال إلى الصفحة السابقة\",\n\t\"components.pagination.remaining-links\": \"روابط أخرى{number} و\",\n\t\"components.popUpWarning.button.cancel\": \"لا ، إلغاء\",\n\t\"components.popUpWarning.button.confirm\": \"نعم ، قم بالتأكيد\",\n\t\"components.Search.placeholder\": \"بحث...\",\n\t\"components.TableHeader.sort\": \"{label} الفرز على\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"وضع Markdown\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"وضعية المعاينة\",\n\t\"Content Type Builder\": \"منشئ أنواع المحتوى\",\n\t\"content-manager.api.id\": \"معرف API\",\n\t\"content-manager.apiError.This attribute must be unique\": \"{field} يجب أن يكون فريدًا\",\n\t\"content-manager.App.schemas.data-loaded\": \"تم تحميل المخططات بنجاح\",\n\t\"content-manager.components.DraggableCard.delete.field\": \"{item} حذف\",\n\t\"content-manager.components.DraggableCard.edit.field\": \"{item} حرر\",\n\t\"content-manager.components.DraggableCard.move.field\": \"{item} تحرك\",\n\t\"content-manager.components.DragHandle-label\": \"جر\",\n\t\"content-manager.components.DynamicTable.row-line\": \"{number} سطر البند\",\n\t\"content-manager.components.DynamicZone.add-component\": \"{componentName} أضف مكونًا إلى\",\n\t\"content-manager.components.DynamicZone.ComponentPicker-label\": \"اختر مكونًا واحدًا\",\n\t\"content-manager.components.DynamicZone.delete-label\": \"{name} حذف\",\n\t\"content-manager.components.DynamicZone.error-message\": \"يحتوي المكون على خطأ (أخطاء)\",\n\t\"content-manager.components.DynamicZone.missing-components\": \"There {number, plural, =0 {are # missing components} واحد {is # missing component} آخر {are # missing components}}\",\n\t\"content-manager.components.DynamicZone.move-down-label\": \"انقل المكون لأسفل\",\n\t\"content-manager.components.DynamicZone.move-up-label\": \"انقل المكون لأعلى\",\n\t\"content-manager.components.DynamicZone.pick-compo\": \"اختر مكونًا واحدًا\",\n\t\"content-manager.components.DynamicZone.required\": \"المكون مطلوب\",\n\t\"content-manager.components.empty-repeatable\": \"لا دخول حتى الان. انقر فوق الزر أدناه لإضافة واحد.\",\n\t\"content-manager.components.FieldItem.linkToComponentLayout\": \"قم بتعيين تخطيط المكون\",\n\t\"content-manager.components.FieldSelect.label\": \"أضف حقلاً\",\n\t\"content-manager.components.LeftMenu.collection-types\": \"أنواع المجموعات\",\n\t\"content-manager.components.LeftMenu.Search.label\": \"ابحث عن نوع المحتوى\",\n\t\"content-manager.components.LeftMenu.single-types\": \"أنواع مفردة\",\n\t\"content-manager.components.NotAllowedInput.text\": \"لا أذونات لرؤية هذا المجال\",\n\t\"content-manager.components.notification.info.maximum-requirement\": \"لقد وصلت بالفعل إلى الحد الأقصى لعدد الحقول\",\n\t\"content-manager.components.notification.info.minimum-requirement\": \"تمت إضافة حقل لمطابقة الحد الأدنى من المتطلبات\",\n\t\"content-manager.components.RelationInput.icon-button-aria-label\": \"جر\",\n\t\"content-manager.components.repeatable.reorder.error\": \"حدث خطأ أثناء إعادة ترتيب حقل المكون الخاص بك ، يرجى المحاولة مرة أخرى\",\n\t\"content-manager.components.RepeatableComponent.error-message\": \"يحتوي المكون (المكونات) على خطأ (أخطاء)\",\n\t\"content-manager.components.reset-entry\": \"إعادة الدخول\",\n\t\"content-manager.components.Select.draft-info-title\": \"الحالة: مسودة\",\n\t\"content-manager.components.Select.publish-info-title\": \"الحالة: منشور\",\n\t\"content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"تخصيص كيف سيبدو عرض التحرير.\",\n\t\"content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"حدد إعدادات عرض القائمة.\",\n\t\"content-manager.components.SettingsViewWrapper.pluginHeader.title\": \"{name} تكوين العرض -\",\n\t\"content-manager.components.TableDelete.label\": \"{number, plural, one {# entry} آخر {# entries}} selected\",\n\t\"content-manager.components.uid.apply\": \"طبق\",\n\t\"content-manager.components.uid.available\": \"متاح\",\n\t\"content-manager.components.uid.regenerate\": \"تجديد\",\n\t\"content-manager.components.uid.suggested\": \"مقترح\",\n\t\"content-manager.components.uid.unavailable\": \"غير متوفره\",\n\t\"content-manager.containers.Edit.delete-entry\": \"احذف هذا الإدخال\",\n\t\"content-manager.containers.Edit.information\": \"معلومة\",\n\t\"content-manager.containers.Edit.information.by\": \"بواسطة\",\n\t\"content-manager.containers.Edit.information.created\": \"أُنشء\",\n\t\"content-manager.containers.Edit.information.draftVersion\": \"نسخة المسودة\",\n\t\"content-manager.containers.Edit.information.editing\": \"التحرير\",\n\t\"content-manager.containers.Edit.information.lastUpdate\": \"اخر تحديث\",\n\t\"content-manager.containers.Edit.information.publishedVersion\": \"النسخة المنشورة\",\n\t\"content-manager.containers.Edit.Link.Layout\": \"تكوين التخطيط\",\n\t\"content-manager.containers.Edit.Link.Model\": \"تحرير نوع المجموعة\",\n\t\"content-manager.containers.Edit.pluginHeader.title.new\": \"قم بإنشاء إدخال\",\n\t\"content-manager.containers.EditSettingsView.modal-form.edit-field\": \"قم بتحرير الحقل\",\n\t\"content-manager.containers.EditView.add.new-entry\": \"أضف إدخالاً\",\n\t\"content-manager.containers.EditView.notification.errors\": \"النموذج يحتوي على بعض الأخطاء\",\n\t\"content-manager.containers.List.draft\": \"مسودة\",\n\t\"content-manager.containers.List.published\": \"نشرت\",\n\t\"content-manager.containers.list.items\": \"{number, plural, =0 {items} one {item} other {items}}\",\n\t\"content-manager.containers.list.table-headers.publishedAt\": \"State\",\n\t\"content-manager.containers.ListSettingsView.modal-form.edit-label\": \"{fieldName} تعديل\",\n\t\"content-manager.containers.SettingPage.add.field\": \"أدخل حقل آخر\",\n\t\"content-manager.containers.SettingPage.add.relational-field\": \"أدخل حقل آخر ذي صلة\",\n\t\"content-manager.containers.SettingPage.editSettings.entry.title\": \"عنوان الإدخال\",\n\t\"content-manager.containers.SettingPage.editSettings.entry.title.description\": \"اضبط الحقل المعروض لإدخالك\",\n\t\"content-manager.containers.SettingPage.editSettings.relation-field.description\": \"قم بتعيين الحقل المعروض في كل من طريقتي التحرير وعرض القائمة\",\n\t\"content-manager.containers.SettingPage.layout\": \"تَخطِيط\",\n\t\"content-manager.containers.SettingPage.listSettings.description\": \"تكوين الخيارات لنوع المجموعة هذا\",\n\t\"content-manager.containers.SettingPage.pluginHeaderDescription\": \"تكوين الإعدادات المحددة لنوع المجموعة هذا\",\n\t\"content-manager.containers.SettingPage.relations\": \"حقول ذات صله\",\n\t\"content-manager.containers.SettingPage.settings\": \"إعدادات\",\n\t\"content-manager.containers.SettingPage.view\": \"رؤية\",\n\t\"content-manager.containers.SettingsPage.Block.contentType.title\": \"أنواع المجموعات\",\n\t\"content-manager.containers.SettingsPage.Block.generalSettings.description\": \"تكوين الخيارات الافتراضية لأنواع المجموعة الخاصة بك\",\n\t\"content-manager.containers.SettingsPage.pluginHeaderDescription\": \"قم بتكوين الإعدادات لجميع أنواع المجموعات والمجموعات الخاصة بك\",\n\t\"content-manager.containers.SettingsView.list.subtitle\": \"تكوين تخطيط وعرض أنواع المجموعات والمجموعات الخاصة بك\",\n\t\"content-manager.containers.SettingsView.list.title\": \"تكوينات العرض\",\n\t\"content-manager.containers.SettingViewModel.pluginHeader.title\": \"{name} مدير محتوى -\",\n\t\"content-manager.dnd.cancel-item\": \"{item}, dropped. Re-order cancelled.\",\n\t\"content-manager.dnd.drop-item\": \"{item}, dropped. Final position in list: {position}.\",\n\t\"content-manager.dnd.grab-item\": \"{item}, grabbed. Current position in list: {position}. Press up and down arrow to change position, Spacebar to drop, Escape to cancel.\",\n\t\"content-manager.dnd.instructions\": \"اضغط على مفتاح المسافة للاستيلاء وإعادة الترتيب\",\n\t\"content-manager.dnd.reorder\": \"{item}, انتقل. منصب جديد في القائمة: {position}.\",\n\t\"content-manager.DynamicTable.relation-loaded\": \"تم تحميل العلاقات\",\n\t\"content-manager.DynamicTable.relation-loading\": \"يتم تحميل العلاقات\",\n\t\"content-manager.DynamicTable.relation-more\": \"تحتوي هذه العلاقة على كيانات أكثر من المعروضة\",\n\t\"content-manager.edit-settings-view.link-to-ctb.components\": \"قم بتحرير المكون\",\n\t\"content-manager.edit-settings-view.link-to-ctb.content-types\": \"قم بتحرير نوع المحتوى\",\n\t\"content-manager.emptyAttributes.button\": \"انتقل إلى منشئ نوع المجموعة\",\n\t\"content-manager.emptyAttributes.description\": \"أضف حقلك الأول إلى نوع المجموعة الخاصة بك\",\n\t\"content-manager.form.Input.hint.character.unit\": \"{maxValue, plural, one { character} other { characters}}\",\n\t\"content-manager.form.Input.hint.minMaxDivider\": \" / \",\n\t\"content-manager.form.Input.hint.text\": \"{min, select, undefined {} other {min. {min}}}{divider}{max, select, undefined {} other {max. {max}}}{unit}{br}{description}\",\n\t\"content-manager.form.Input.pageEntries.inputDescription\": \"ملاحظة: يمكنك تجاوز هذه القيمة في صفحة إعدادات نوع المجموعة.\",\n\t\"content-manager.form.Input.sort.order\": \"ترتيب الافتراضي\",\n\t\"content-manager.form.Input.wysiwyg\": \"WYSIWYG عرض كـ\",\n\t\"content-manager.global.displayedFields\": \"الحقول المعروضة\",\n\t\"content-manager.groups\": \"مجموعات\",\n\t\"content-manager.groups.numbered\": \"({number}) مجموعات\",\n\t\"content-manager.header.name\": \"محتوى\",\n\t\"content-manager.HeaderLayout.button.label-add-entry\": \"إنشاء إدخال جديد\",\n\t\"content-manager.link-to-ctb\": \"قم بتحرير النموذج\",\n\t\"content-manager.models\": \"أنواع المجموعات\",\n\t\"content-manager.models.numbered\": \"({number}) أنواع المجموعات\",\n\t\"content-manager.notification.info.minimumFields\": \"يجب أن يكون لديك حقل واحد على الأقل معروض\",\n\t\"content-manager.notification.upload.error\": \"حدث خطأ أثناء تحميل ملفاتك\",\n\t\"content-manager.pages.ListView.header-subtitle\": \"{number, plural, =0 {# entries} one {# entry} other {# entries}} found\",\n\t\"content-manager.pages.NoContentType.button\": \"قم بإنشاء نوع المحتوى الأول الخاص بك\",\n\t\"content-manager.pages.NoContentType.text\": \"ليس لديك أي محتوى حتى الآن ، نوصيك بإنشاء نوع المحتوى الأول الخاص بك.\",\n\t\"content-manager.permissions.not-allowed.create\": \"لا يسمح لك لإنشاء وثيقة\",\n\t\"content-manager.permissions.not-allowed.update\": \"لا يسمح لك أن ترى هذه الوثيقة\",\n\t\"content-manager.popover.display-relations.label\": \"عرض العلاقات\",\n\t\"content-manager.popUpwarning.warning.has-draft-relations.button-confirm\": \"نعم ، انشر\",\n\t\"content-manager.popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, one { relation is } آخر { relations are } }</b> لم تنشر بعد وقد تؤدي إلى سلوك غير متوقع.\",\n\t\"content-manager.popUpWarning.warning.has-draft-relations.title\": \"تأكيد\",\n\t\"content-manager.popUpWarning.warning.publish-question\": \"هل مازلت تريد النشر؟\",\n\t\"content-manager.popUpWarning.warning.unpublish\": \"إذا لم تنشر هذا المحتوى ، فسيتحول تلقائيًا إلى مسودة.\",\n\t\"content-manager.popUpWarning.warning.unpublish-question\": \"هل أنت متأكد أنك لا تريد نشره؟\",\n\t\"content-manager.relation.add\": \"أضف العلاقة\",\n\t\"content-manager.relation.disconnect\": \"نزع\",\n\t\"content-manager.relation.isLoading\": \"يتم تحميل العلاقات\",\n\t\"content-manager.relation.loadMore\": \"تحميل المزيد\",\n\t\"content-manager.relation.notAvailable\": \"لا توجد علاقات متاحة\",\n\t\"content-manager.relation.publicationState.draft\": \"مسودة\",\n\t\"content-manager.relation.publicationState.published\": \"منشور\",\n\t\"content-manager.select.currently.selected\": \"{count} المحدد حاليا\",\n\t\"content-manager.success.record.publish\": \"منشور\",\n\t\"content-manager.success.record.unpublish\": \"غير منشورة\",\n\t\"content-manager.utils.data-loaded\": \"The {number, plural, =1 {entry has} other {entries have}} successfully been loaded\",\n\tdark: dark,\n\tDocumentation: Documentation,\n\t\"form.button.continue\": \"واصل\",\n\t\"form.button.done\": \"منتهي\",\n\t\"global.actions\": \"أجراءات\",\n\t\"global.auditLogs\": \"سجلات التدقيق\",\n\t\"global.back\": \"الى الوراء\",\n\t\"global.cancel\": \"إلغاء\",\n\t\"global.change-password\": \"تغيير كلمة المرور\",\n\t\"global.content-manager\": \"مدير محتوى\",\n\t\"global.continue\": \"واصل\",\n\t\"global.delete\": \"مسح\",\n\t\"global.delete-target\": \"{target} مسح \",\n\t\"global.description\": \"وصف\",\n\t\"global.details\": \"تفاصيل\",\n\t\"global.disabled\": \"إبطال\",\n\t\"global.documentation\": \"توثيق\",\n\t\"global.enabled\": \"ممكن\",\n\t\"global.finish\": \"نهاية\",\n\t\"global.marketplace\": \"المتجر\",\n\t\"global.name\": \"اسم\",\n\t\"global.none\": \"لا أحد\",\n\t\"global.password\": \"كلمة المرور\",\n\t\"global.plugins\": \"الإضافات\",\n\t\"global.plugins.content-manager\": \"مدير محتوى\",\n\t\"global.plugins.content-manager.description\": \"طريقة سريعة لرؤية وتحرير وحذف البيانات في قاعدة البيانات الخاصة بك.\",\n\t\"global.plugins.content-type-builder\": \"منشئ نوع المحتوى\",\n\t\"global.plugins.content-type-builder.description\": \"قم بنمذجة بنية البيانات الخاصة بواجهة برمجة التطبيقات (API) الخاصة بك. إنشاء مجالات وعلاقات جديدة في دقيقة واحدة فقط. يتم إنشاء الملفات وتحديثها تلقائيًا في مشروعك.\",\n\t\"global.plugins.documentation\": \"توثيق\",\n\t\"global.plugins.documentation.description\": \"قم بإنشاء مستند OpenAPI وتصور API الخاص بك باستخدام SWAGGER UI.\",\n\t\"global.plugins.email\": \"بريد إلكتروني\",\n\t\"global.plugins.email.description\": \"تكوين التطبيق الخاص بك لإرسال رسائل البريد الإلكتروني.\",\n\t\"global.plugins.graphql\": \"GraphQL\",\n\t\"global.plugins.graphql.description\": \"يضيف نقطة نهاية GraphQL بأساليب واجهة برمجة التطبيقات الافتراضية.\",\n\t\"global.plugins.i18n\": \"تدويل\",\n\t\"global.plugins.i18n.description\": \"يمكّن هذا المكون الإضافي من إنشاء المحتوى وقراءته وتحديثه بلغات مختلفة ، سواء من لوحة الإدارة أو من واجهة برمجة التطبيقات.\",\n\t\"global.plugins.sentry\": \"Sentry\",\n\t\"global.plugins.sentry.description\": \"إرسال أحداث خطأ Strapi إلى Sentry.\",\n\t\"global.plugins.upload\": \"مكتبة الوسائط\",\n\t\"global.plugins.upload.description\": \"إدارة ملفات الوسائط.\",\n\t\"global.plugins.users-permissions\": \"الأدوار والأذونات\",\n\t\"global.plugins.users-permissions.description\": \"قم بحماية API الخاص بك من خلال عملية مصادقة كاملة تعتمد على JWT. يأتي هذا المكون الإضافي أيضًا مع إستراتيجية قائمة التحكم بالوصول (ACL) التي تتيح لك إدارة الأذونات بين مجموعات المستخدمين.\",\n\t\"global.profile\": \"حساب تعريفي\",\n\t\"global.prompt.unsaved\": \"هل أنت متأكد أنك تريد مغادرة هذه الصفحة؟ ستفقد كل تعديلاتك\",\n\t\"global.reset-password\": \"إعادة تعيين كلمة المرور\",\n\t\"global.roles\": \"الأدوار\",\n\t\"global.save\": \"يحفظ\",\n\t\"global.search\": \"يبحث\",\n\t\"global.see-more\": \"شاهد المزيد\",\n\t\"global.select\": \"اختار\",\n\t\"global.select-all-entries\": \"حدد كل الإدخالات\",\n\t\"global.settings\": \"إعدادات\",\n\t\"global.type\": \"نوع\",\n\t\"global.users\": \"المستخدمون\",\n\t\"HomePage.head.title\": \"الصفحة الرئيسية\",\n\t\"HomePage.roadmap\": \"انظر خارطة الطريق لدينا\",\n\t\"HomePage.welcome.congrats\": \"تهاني!\",\n\t\"HomePage.welcome.congrats.content\": \"Strapiلقد قمت بتسجيل الدخول باعتبارك المسؤول الأول. لاكتشاف الميزات القوية التي يوفرها\",\n\t\"HomePage.welcome.congrats.content.bold\": \"نوصيك بإنشاء أول نوع مجموعة خاص بك.\",\n\tlight: light,\n\t\"Media Library\": \"مكتبة الوسائط\",\n\t\"notification.contentType.relations.conflict\": \"نوع المحتوى له علاقات متضاربة\",\n\t\"notification.default.title\": \"معلومة:\",\n\t\"notification.ee.warning.at-seat-limit.title\": \"{LicenseLimitStatus ، حدد ، OVER_LIMIT {Over} AT_LIMIT {At}} حد المقاعد ({currentUserCount} / {allowedSeats})\",\n\t\"notification.ee.warning.over-.message\": \"أضف مقاعد إلى {LicenseLimitStatus ، حدد ، OVER_LIMIT {دعوة} AT_LIMIT {re-enable}} مستخدمين. إذا كنت قد فعلت ذلك بالفعل ولكن لم ينعكس في Strapi بعد ، فتأكد من إعادة تشغيل التطبيق الخاص بك.\",\n\t\"notification.error.invalid.configuration\": \"لديك تكوين غير صالح ، تحقق من سجل الخادم لمزيد من المعلومات.\",\n\t\"notification.error.tokennamenotunique\": \"تم تعيين الاسم بالفعل لرمز مميز آخر\",\n\t\"notification.form.error.fields\": \"النموذج يحتوي على بعض الأخطاء\",\n\t\"notification.form.success.fields\": \"تم حفظ التغييرات\",\n\t\"notification.link-copied\": \"تم نسخ الرابط في الحافظة\",\n\t\"notification.permission.not-allowed-read\": \"لا يسمح لك أن ترى هذه الوثيقة\",\n\t\"notification.success.apitokencreated\": \"تم إنشاء رمز API بنجاح\",\n\t\"notification.success.apitokenedited\": \"تم تحرير رمز API بنجاح\",\n\t\"notification.success.delete\": \"تم حذف العنصر\",\n\t\"notification.success.saved\": \"حفظ\",\n\t\"notification.success.title\": \"نجاح:\",\n\t\"notification.success.transfertokencreated\": \"تم إنشاء رمز النقل بنجاح\",\n\t\"notification.success.transfertokenedited\": \"تم تحرير رمز النقل بنجاح\",\n\t\"notification.version.update.message\": \"نسخة جديدة متاحة من ستربي!\",\n\t\"notification.warning.404\": \"404 غير موجود\",\n\t\"notification.warning.title\": \"تحذير:\",\n\tor: or,\n\t\"Roles & Permissions\": \"الأدوار والأذونات\",\n\t\"Roles.components.List.empty.withSearch\": \"لا يوجد دور مطابق للبحث ({search}) ...\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"تعذر حذف بعض الأدوار لأنها مرتبطة بالمستخدمين\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"لا يمكن حذف الدور إذا كان مرتبطًا بالمستخدمين\",\n\t\"Roles.RoleRow.select-all\": \"حدد {name} للإجراءات المجمعة\",\n\t\"Roles.RoleRow.user-count\": \"{number، plural، = 0 {# user} واحد {# user} آخر {# users}}\",\n\tselectButtonTitle: selectButtonTitle,\n\t\"Settings.apiTokens.addFirstToken\": \"أضف رمز API الأول الخاص بك\",\n\t\"Settings.apiTokens.addNewToken\": \"إضافة رمز API جديد\",\n\t\"Settings.apiTokens.create\": \"إنشاء رمز API جديد\",\n\t\"Settings.apiTokens.createPage.BoundRoute.title\": \"طريق منضم إلى\",\n\t\"Settings.apiTokens.createPage.permissions.description\": \"يتم سرد الإجراءات المرتبطة بالمسار فقط أدناه.\",\n\t\"Settings.apiTokens.createPage.permissions.header.hint\": \"حدد إجراءات التطبيق أو إجراءات البرنامج المساعد وانقر على أيقونة الترس لعرض المسار المنضم\",\n\t\"Settings.apiTokens.createPage.permissions.header.title\": \"إعدادات متقدمة\",\n\t\"Settings.apiTokens.createPage.permissions.title\": \"أذونات\",\n\t\"Settings.apiTokens.createPage.title\": \"إنشاء رمز API\",\n\t\"Settings.apiTokens.description\": \"قائمة الرموز التي تم إنشاؤها لاستهلاك API\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"ليس لديك أي محتوى حتى الآن ...\",\n\t\"Settings.apiTokens.ListView.headers.createdAt\": \"أنشئت في\",\n\t\"Settings.apiTokens.ListView.headers.description\": \"وصف\",\n\t\"Settings.apiTokens.ListView.headers.lastUsedAt\": \"آخر أستخدام\",\n\t\"Settings.apiTokens.ListView.headers.name\": \"اسم\",\n\t\"Settings.apiTokens.ListView.headers.type\": \"نوع الرمز\",\n\t\"Settings.apiTokens.regenerate\": \"تجديد\",\n\t\"Settings.apiTokens.title\": \"رموز API\",\n\t\"Settings.application.customization\": \"التخصيص\",\n\t\"Settings.application.customization.auth-logo.carousel-hint\": \"استبدل الشعار في صفحات المصادقة\",\n\t\"Settings.application.customization.carousel-hint\": \"تغيير شعار لوحة الإدارة (الحد الأقصى للبعد: {dimension} {dimension} ، الحد الأقصى لحجم الملف: {size} كيلوبايت)\",\n\t\"Settings.application.customization.carousel-slide.label\": \"شريحة الشعار\",\n\t\"Settings.application.customization.carousel.auth-logo.title\": \"شعار Auth\",\n\t\"Settings.application.customization.carousel.change-action\": \"تغيير الشعار\",\n\t\"Settings.application.customization.carousel.menu-logo.title\": \"شعار القائمة\",\n\t\"Settings.application.customization.carousel.reset-action\": \"إعادة تعيين الشعار\",\n\t\"Settings.application.customization.carousel.title\": \"شعار\",\n\t\"Settings.application.customization.menu-logo.carousel-hint\": \"استبدل الشعار في شريط التنقل الرئيسي\",\n\t\"Settings.application.customization.modal.cancel\": \"إلغاء\",\n\t\"Settings.application.customization.modal.pending\": \"شعار معلق\",\n\t\"Settings.application.customization.modal.pending.card-badge\": \"صورة\",\n\t\"Settings.application.customization.modal.pending.choose-another\": \"اختر شعارًا آخر\",\n\t\"Settings.application.customization.modal.pending.subtitle\": \"إدارة الشعار المختار قبل تحميله\",\n\t\"Settings.application.customization.modal.pending.title\": \"الشعار جاهز للتحميل\",\n\t\"Settings.application.customization.modal.pending.upload\": \"تحميل الشعار\",\n\t\"Settings.application.customization.modal.tab.label\": \"كيف تريد تحميل الأصول الخاصة بك؟\",\n\t\"Settings.application.customization.modal.upload\": \"تحميل الشعار\",\n\t\"Settings.application.customization.modal.upload.cta.browse\": \"تصفح ملفات\",\n\t\"Settings.application.customization.modal.upload.drag-drop\": \"قم بالسحب والإفلات هنا أو\",\n\t\"Settings.application.customization.modal.upload.error-format\": \"تم تحميل تنسيق خاطئ (التنسيقات المقبولة فقط: jpeg ، jpg ، png ، svg).\",\n\t\"Settings.application.customization.modal.upload.error-network\": \"خطأ في الشبكة\",\n\t\"Settings.application.customization.modal.upload.error-size\": \"الملف الذي تم تحميله كبير جدًا (الحد الأقصى للبعد: {dimension} x {dimension} ، الحد الأقصى لحجم الملف: {size} كيلوبايت)\",\n\t\"Settings.application.customization.modal.upload.file-validation\": \"أقصى بُعد: {dimension} x {dimension} ، الحد الأقصى للحجم: {size} كيلوبايت\",\n\t\"Settings.application.customization.modal.upload.from-computer\": \"من الكمبيوتر\",\n\t\"Settings.application.customization.modal.upload.from-url\": \"من URL\",\n\t\"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n\t\"Settings.application.customization.modal.upload.next\": \"التالي\",\n\t\"Settings.application.customization.size-details\": \"أقصى بُعد: {dimension} x {dimension} ، الحد الأقصى لحجم الملف: {size} كيلوبايت\",\n\t\"Settings.application.description\": \"المعلومات العالمية للوحة الإدارة\",\n\t\"Settings.application.edition-title\": \"الخطة الحالية\",\n\t\"Settings.application.ee-or-ce\": \"{communityEdition، select، true {Community Edition} أخرى {Enterprise Edition}}\",\n\t\"Settings.application.ee.admin-seats.add-seats\": \"{isHostedOnStrapiCloud، select، true {Add seat} other {Contact sales}}\",\n\t\"Settings.application.ee.admin-seats.at-limit-tooltip\": \"عند الحد: أضف مقاعد لدعوة المزيد من المستخدمين\",\n\t\"Settings.application.ee.admin-seats.count\": \"<text>{enforcementUserCount}</text>/{permittedSeats}\",\n\t\"Settings.application.get-help\": \"احصل على مساعدة\",\n\t\"Settings.application.link-pricing\": \"انظر جميع خطط التسعير\",\n\t\"Settings.application.link-upgrade\": \"قم بترقية لوحة الإدارة الخاصة بك\",\n\t\"Settings.application.node-version\": \"إصدار العقدة\",\n\t\"Settings.application.strapi-version\": \"نسخة ستربي\",\n\t\"Settings.application.strapiVersion\": \"Strapi نسخة\",\n\t\"Settings.application.title\": \"ملخص\",\n\t\"Settings.error\": \"خطأ\",\n\t\"Settings.global\": \"الاعدادات العامة\",\n\t\"Settings.PageTitle\": \"الإعدادات - {name}\",\n\t\"Settings.permissions\": \"لوحة الإدارة\",\n\t\"Settings.permissions.auditLogs.action\": \"فعل\",\n\t\"Settings.permissions.auditLogs.admin.auth.success\": \"دخول المشرف\",\n\t\"Settings.permissions.auditLogs.admin.logout\": \"خروج المسؤول\",\n\t\"Settings.permissions.auditLogs.component.create\": \"تكوين المكون\",\n\t\"Settings.permissions.auditLogs.component.delete\": \"حذف المكون\",\n\t\"Settings.permissions.auditLogs.component.update\": \"مكون التحديث\",\n\t\"Settings.permissions.auditLogs.content-type.create\": \"إنشاء نوع المحتوى\",\n\t\"Settings.permissions.auditLogs.content-type.delete\": \"حذف نوع المحتوى\",\n\t\"Settings.permissions.auditLogs.content-type.update\": \"تحديث نوع المحتوى\",\n\t\"Settings.permissions.auditLogs.date\": \"تاريخ\",\n\t\"Settings.permissions.auditLogs.details\": \"تفاصيل السجل\",\n\t\"Settings.permissions.auditLogs.entry.create\": \"إنشاء إدخال {model، select، undefined {} other {({model})}}\",\n\t\"Settings.permissions.auditLogs.entry.delete\": \"حذف الإدخال {model، select، undefined {} other {({model})}}\",\n\t\"Settings.permissions.auditLogs.entry.publish\": \"نشر الإدخال {model، select، undefined {} other {({model})}}\",\n\t\"Settings.permissions.auditLogs.entry.unpublish\": \"إلغاء نشر الإدخال {model، select، undefined {} other {({model})}}\",\n\t\"Settings.permissions.auditLogs.entry.update\": \"تحديث الإدخال {model، select، undefined {} other {({model})}}\",\n\t\"Settings.permissions.auditLogs.filters.combobox.aria-label\": \"ابحث وحدد خيارًا للتصفية\",\n\t\"Settings.permissions.auditLogs.listview.header.subtitle\": \"سجلات لجميع الأنشطة التي حدثت في بيئتك\",\n\t\"Settings.permissions.auditLogs.media.create\": \"قم بإنشاء وسائط\",\n\t\"Settings.permissions.auditLogs.media.delete\": \"حذف الوسائط\",\n\t\"Settings.permissions.auditLogs.media.update\": \"تحديث الوسائط\",\n\t\"Settings.permissions.auditLogs.payload\": \"الحمولة\",\n\t\"Settings.permissions.auditLogs.permission.create\": \"إنشاء إذن\",\n\t\"Settings.permissions.auditLogs.permission.delete\": \"حذف إذن\",\n\t\"Settings.permissions.auditLogs.permission.update\": \"إذن التحديث\",\n\t\"Settings.permissions.auditLogs.role.create\": \"خلق دور\",\n\t\"Settings.permissions.auditLogs.role.delete\": \"حذف الدور\",\n\t\"Settings.permissions.auditLogs.role.update\": \"تحديث الدور\",\n\t\"Settings.permissions.auditLogs.user\": \"مستخدم\",\n\t\"Settings.permissions.auditLogs.user.create\": \"إنشاء مستخدم\",\n\t\"Settings.permissions.auditLogs.user.delete\": \"مسح المستخدم\",\n\t\"Settings.permissions.auditLogs.user.fullname\": \"{firstname} {lastname}\",\n\t\"Settings.permissions.auditLogs.user.update\": \"تحديث المستخدم\",\n\t\"Settings.permissions.auditLogs.userId\": \"معرف المستخدم\",\n\t\"Settings.permissions.category\": \"إعدادات الأذونات لـ {category}\",\n\t\"Settings.permissions.category.plugins\": \"إعدادات الأذونات للمكوِّن الإضافي {category}\",\n\t\"Settings.permissions.conditions.anytime\": \"في أي وقت\",\n\t\"Settings.permissions.conditions.apply\": \"يتقدم\",\n\t\"Settings.permissions.conditions.can\": \"يستطيع\",\n\t\"Settings.permissions.conditions.conditions\": \"شروط\",\n\t\"Settings.permissions.conditions.define-conditions\": \"حدد الشروط\",\n\t\"Settings.permissions.conditions.links\": \"الروابط\",\n\t\"Settings.permissions.conditions.no-actions\": \"تحتاج أولاً إلى تحديد الإجراءات (إنشاء ، قراءة ، تحديث ، ...) قبل تحديد الشروط عليها.\",\n\t\"Settings.permissions.conditions.none-selected\": \"في أي وقت\",\n\t\"Settings.permissions.conditions.or\": \"أو\",\n\t\"Settings.permissions.conditions.when\": \"متى\",\n\t\"Settings.permissions.select-all-by-permission\": \"حدد كافة أذونات {label}\",\n\t\"Settings.permissions.select-by-permission\": \"اختار {label} إذن\",\n\t\"Settings.permissions.users.active\": \"نشيط\",\n\t\"Settings.permissions.users.create\": \"قم بدعوة مستخدم جديد\",\n\t\"Settings.permissions.users.email\": \"بريد إلكتروني\",\n\t\"Settings.permissions.users.firstname\": \"الاسم الأول\",\n\t\"Settings.permissions.users.form.sso\": \"تواصل مع SSO\",\n\t\"Settings.permissions.users.form.sso.description\": \"عند التمكين (ON) ، يمكن للمستخدمين تسجيل الدخول عبر SSO\",\n\t\"Settings.permissions.users.inactive\": \"غير نشط\",\n\t\"Settings.permissions.users.lastname\": \"اسم العائلة\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"جميع المستخدمين الذين لديهم حق الوصول إلى لوحة إدارة Strapi\",\n\t\"Settings.permissions.users.roles\": \"الأدوار\",\n\t\"Settings.permissions.users.strapi-author\": \"مؤلف\",\n\t\"Settings.permissions.users.strapi-editor\": \"محرر\",\n\t\"Settings.permissions.users.strapi-super-admin\": \"مشرف فائق\",\n\t\"Settings.permissions.users.tabs.label\": \"أذونات علامات التبويب\",\n\t\"Settings.permissions.users.user-status\": \"حالة المستخدم\",\n\t\"Settings.permissions.users.username\": \"اسم المستخدم\",\n\t\"Settings.profile.form.notify.data.loaded\": \"تم تحميل بيانات ملفك الشخصي\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"امسح لغة الواجهة المحددة\",\n\t\"Settings.profile.form.section.experience.here\": \"هنا\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"لغة الواجهة\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"سيعرض هذا فقط واجهتك الخاصة باللغة المختارة.\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"سيتم تطبيق تغييرات التفضيلات عليك فقط. يتوفر مزيد من المعلومات{here}.\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"يعرض واجهتك في الوضع المختار.\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"وضع الواجهة\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{name} وضع\",\n\t\"Settings.profile.form.section.experience.title\": \"خبرة\",\n\t\"Settings.profile.form.section.head.title\": \"ملف تعريفي للمستخدم\",\n\t\"Settings.profile.form.section.profile.page.title\": \"الصفحة الشخصية\",\n\t\"Settings.roles.create.description\": \"تحديد الحقوق الممنوحة للدور\",\n\t\"Settings.roles.create.title\": \"أنشئ دورًا\",\n\t\"Settings.roles.created\": \"تم إنشاء الدور\",\n\t\"Settings.roles.edit.title\": \"تحرير دور\",\n\t\"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# users} one {# user} other {# users}} with this role\",\n\t\"Settings.roles.form.created\": \"مكون\",\n\t\"Settings.roles.form.description\": \"اسم ووصف الدور\",\n\t\"Settings.roles.form.permission.property-label\": \"{label} أذونات\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"أذونات الحقول\",\n\t\"Settings.roles.form.permissions.create\": \"خلق\",\n\t\"Settings.roles.form.permissions.delete\": \"شطب\",\n\t\"Settings.roles.form.permissions.publish\": \"ينشر\",\n\t\"Settings.roles.form.permissions.read\": \"يقرأ\",\n\t\"Settings.roles.form.permissions.update\": \"تحديث\",\n\t\"Settings.roles.list.button.add\": \"أضف دورًا جديدًا\",\n\t\"Settings.roles.list.description\": \"قائمة الأدوار\",\n\t\"Settings.roles.title.singular\": \"دور\",\n\t\"Settings.sso.description\": \"قم بتكوين الإعدادات لميزة الدخول الموحد.\",\n\t\"Settings.sso.form.defaultRole.description\": \"سيقوم بإرفاق المستخدم الجديد المصادق عليه بالدور المحدد\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"تحتاج إلى الحصول على إذن لقراءة أدوار المسؤول\",\n\t\"Settings.sso.form.defaultRole.label\": \"الدور الافتراضي\",\n\t\"Settings.sso.form.registration.description\": \"أنشئ مستخدمًا جديدًا على تسجيل الدخول الموحّد (SSO) في حالة عدم وجود حساب\",\n\t\"Settings.sso.form.registration.label\": \"التسجيل التلقائي\",\n\t\"Settings.sso.title\": \"علامة واحدة على\",\n\t\"Settings.tokens.Button.cancel\": \"يلغي\",\n\t\"Settings.tokens.Button.regenerate\": \"تجديد\",\n\t\"Settings.tokens.copy.editMessage\": \"لأسباب تتعلق بالأمان ، لا يمكنك رؤية رمزك المميز إلا مرة واحدة.\",\n\t\"Settings.tokens.copy.editTitle\": \"لم يعد هذا الرمز المميز يمكن الوصول إليه.\",\n\t\"Settings.tokens.copy.lastWarning\": \"تأكد من نسخ هذا الرمز المميز ، فلن تتمكن من رؤيته مرة أخرى!\",\n\t\"Settings.tokens.duration.30-days\": \"30 يوما\",\n\t\"Settings.tokens.duration.7-days\": \"7 أيام\",\n\t\"Settings.tokens.duration.90-days\": \"90 يومًا\",\n\t\"Settings.tokens.duration.expiration-date\": \"تاريخ انتهاء الصلاحية\",\n\t\"Settings.tokens.duration.unlimited\": \"غير محدود\",\n\t\"Settings.tokens.form.description\": \"وصف\",\n\t\"Settings.tokens.form.duration\": \"مدة الرمز\",\n\t\"Settings.tokens.form.name\": \"اسم\",\n\t\"Settings.tokens.form.type\": \"نوع الرمز\",\n\t\"Settings.tokens.ListView.headers.createdAt\": \"أنشئت في\",\n\t\"Settings.tokens.ListView.headers.description\": \"وصف\",\n\t\"Settings.tokens.ListView.headers.lastUsedAt\": \"آخر أستخدام\",\n\t\"Settings.tokens.ListView.headers.name\": \"اسم\",\n\t\"Settings.tokens.notification.copied\": \"تم نسخ الرمز المميز إلى الحافظة.\",\n\t\"Settings.tokens.popUpWarning.message\": \"هل أنت متأكد أنك تريد إعادة إنشاء هذا الرمز المميز؟\",\n\t\"Settings.tokens.regenerate\": \"تجديد\",\n\t\"Settings.tokens.RegenerateDialog.title\": \"إعادة إنشاء الرمز المميز\",\n\t\"Settings.tokens.types.custom\": \"مخصص\",\n\t\"Settings.tokens.types.full-access\": \"الوصول الكامل\",\n\t\"Settings.tokens.types.read-only\": \"يقرأ فقط\",\n\t\"Settings.transferTokens.addFirstToken\": \"أضف أول رمز تحويل خاص بك\",\n\t\"Settings.transferTokens.addNewToken\": \"أضف رمز تحويل جديد\",\n\t\"Settings.transferTokens.create\": \"إنشاء رمز تحويل جديد\",\n\t\"Settings.transferTokens.createPage.title\": \"إنشاء رمز التحويل\",\n\t\"Settings.transferTokens.description\": \"قائمة برموز التحويل المُنشأة\",\n\t\"Settings.transferTokens.emptyStateLayout\": \"ليس لديك أي محتوى حتى الآن ...\",\n\t\"Settings.transferTokens.ListView.headers.type\": \"نوع الرمز\",\n\t\"Settings.transferTokens.title\": \"رموز التحويل\",\n\t\"Settings.webhooks.create\": \"إنشاء خطاف ويب\",\n\t\"Settings.webhooks.create.header\": \"إنشاء رأس جديد\",\n\t\"Settings.webhooks.created\": \"تم إنشاء الرد التلقائي على الويب\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"هذا الحدث موجود فقط للمحتويات مع تمكين نظام المسودة / النشر\",\n\t\"Settings.webhooks.events.create\": \"أخلق\",\n\t\"Settings.webhooks.events.update\": \"تحديث\",\n\t\"Settings.webhooks.form.events\": \"الأحداث\",\n\t\"Settings.webhooks.form.headers\": \"الرؤوس\",\n\t\"Settings.webhooks.form.url\": \"URL\",\n\t\"Settings.webhooks.headers.remove\": \"{number} قم بإزالة صف الرأس\",\n\t\"Settings.webhooks.key\": \"مفتاح\",\n\t\"Settings.webhooks.list.button.add\": \"إنشاء خطاف ويب جديد\",\n\t\"Settings.webhooks.list.description\": \"احصل على إخطارات التغييرات POST\",\n\t\"Settings.webhooks.list.empty.description\": \"لم يتم العثور على خطافات الويب\",\n\t\"Settings.webhooks.list.empty.link\": \"انظر وثائقنا\",\n\t\"Settings.webhooks.list.empty.title\": \"لا توجد خطاطيف ويب حتى الان\",\n\t\"Settings.webhooks.list.th.actions\": \"أجراءات\",\n\t\"Settings.webhooks.list.th.status\": \"حالة\",\n\t\"Settings.webhooks.singular\": \"الويب هوك\",\n\t\"Settings.webhooks.title\": \"ويب هوك\",\n\t\"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# asset} other {# assets}} selected\",\n\t\"Settings.webhooks.trigger\": \"مشغل\",\n\t\"Settings.webhooks.trigger.cancel\": \"إلغاء المشغل...\",\n\t\"Settings.webhooks.trigger.pending\": \"قيد الانتظار…\",\n\t\"Settings.webhooks.trigger.save\": \"يرجى الحفظ للتشغيل\",\n\t\"Settings.webhooks.trigger.success\": \"نجاح!\",\n\t\"Settings.webhooks.trigger.success.label\": \"نجح الزناد\",\n\t\"Settings.webhooks.trigger.test\": \"اختبار الزناد\",\n\t\"Settings.webhooks.trigger.title\": \"احفظ قبل تشغيل\",\n\t\"Settings.webhooks.value\": \"قيمة\",\n\tskipToContent: skipToContent,\n\tsubmit: submit,\n\t\"Usecase.back-end\": \"المطور الخلفي\",\n\t\"Usecase.button.skip\": \"تخطي هذا السؤال\",\n\t\"Usecase.content-creator\": \"صانع المحتوى\",\n\t\"Usecase.front-end\": \"مطور الواجهة الأمامية\",\n\t\"Usecase.full-stack\": \"مطور كامل المكدس\",\n\t\"Usecase.input.work-type\": \"ما نوع العمل الذي تفعله؟\",\n\t\"Usecase.notification.success.project-created\": \"تم إنشاء المشروع بنجاح\",\n\t\"Usecase.other\": \"آخر\",\n\t\"Usecase.title\": \"تخبرنا أكثر قليلا عن نفسك\",\n\t\"Users.components.List.empty\": \"لا يوجد مستخدمون ...\",\n\t\"Users.components.List.empty.withFilters\": \"لا يوجد مستخدمون لديهم عوامل التصفية المطبقة ...\",\n\t\"Users.components.List.empty.withSearch\": \"لا يوجد مستخدمون مطابقون للبحث({search})...\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, ar as default, light, or, selectButtonTitle, skipToContent, submit };\n//# sourceMappingURL=ar-7vbwrPth.mjs.map\n"], "mappings": ";;;AAAA,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,YAAY;AAClB,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,OAAO;AACb,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,KAAK;AACX,IAAM,oBAAoB;AAC1B,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,mBAAmB;AAAA,EACnB;AAAA,EACA,gBAAgB;AAAA,EAChB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,6CAA6C;AAAA,EAC7C,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,uCAAuC;AAAA,EACvC,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,2CAA2C;AAAA,EAC3C,oDAAoD;AAAA,EACpD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C;AAAA,EACA;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,4BAA4B;AAAA,EAC5B,iCAAiC;AAAA,EACjC,0DAA0D;AAAA,EAC1D,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,sCAAsC;AAAA,EACtC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,+CAA+C;AAAA,EAC/C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,0DAA0D;AAAA,EAC1D,2CAA2C;AAAA,EAC3C,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,oDAAoD;AAAA,EACpD,wDAAwD;AAAA,EACxD,gEAAgE;AAAA,EAChE,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,wDAAwD;AAAA,EACxD,qDAAqD;AAAA,EACrD,mDAAmD;AAAA,EACnD,+CAA+C;AAAA,EAC/C,8DAA8D;AAAA,EAC9D,gDAAgD;AAAA,EAChD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oEAAoE;AAAA,EACpE,oEAAoE;AAAA,EACpE,mEAAmE;AAAA,EACnE,uDAAuD;AAAA,EACvD,gEAAgE;AAAA,EAChE,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,yFAAyF;AAAA,EACzF,yFAAyF;AAAA,EACzF,qEAAqE;AAAA,EACrE,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4CAA4C;AAAA,EAC5C,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,kDAAkD;AAAA,EAClD,uDAAuD;AAAA,EACvD,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,0DAA0D;AAAA,EAC1D,gEAAgE;AAAA,EAChE,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,qEAAqE;AAAA,EACrE,qDAAqD;AAAA,EACrD,2DAA2D;AAAA,EAC3D,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,6DAA6D;AAAA,EAC7D,qEAAqE;AAAA,EACrE,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,+EAA+E;AAAA,EAC/E,kFAAkF;AAAA,EAClF,iDAAiD;AAAA,EACjD,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,+CAA+C;AAAA,EAC/C,mEAAmE;AAAA,EACnE,6EAA6E;AAAA,EAC7E,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,sDAAsD;AAAA,EACtD,kEAAkE;AAAA,EAClE,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,+BAA+B;AAAA,EAC/B,gDAAgD;AAAA,EAChD,iDAAiD;AAAA,EACjD,8CAA8C;AAAA,EAC9C,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,kDAAkD;AAAA,EAClD,iDAAiD;AAAA,EACjD,wCAAwC;AAAA,EACxC,2DAA2D;AAAA,EAC3D,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,0BAA0B;AAAA,EAC1B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,2EAA2E;AAAA,EAC3E,oEAAoE;AAAA,EACpE,kEAAkE;AAAA,EAClE,yDAAyD;AAAA,EACzD,kDAAkD;AAAA,EAClD,2DAA2D;AAAA,EAC3D,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC;AAAA,EACA;AAAA,EACA,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C;AAAA,EACA,iBAAiB;AAAA,EACjB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,6CAA6C;AAAA,EAC7C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B;AAAA,EACA,uBAAuB;AAAA,EACvB,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B;AAAA,EACA,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,yDAAyD;AAAA,EACzD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,sCAAsC;AAAA,EACtC,8DAA8D;AAAA,EAC9D,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,6DAA6D;AAAA,EAC7D,+DAA+D;AAAA,EAC/D,4DAA4D;AAAA,EAC5D,qDAAqD;AAAA,EACrD,8DAA8D;AAAA,EAC9D,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,+DAA+D;AAAA,EAC/D,mEAAmE;AAAA,EACnE,6DAA6D;AAAA,EAC7D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sDAAsD;AAAA,EACtD,mDAAmD;AAAA,EACnD,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,gEAAgE;AAAA,EAChE,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,iEAAiE;AAAA,EACjE,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,iDAAiD;AAAA,EACjD,wDAAwD;AAAA,EACxD,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,sBAAsB;AAAA,EACtB,wBAAwB;AAAA,EACxB,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,sDAAsD;AAAA,EACtD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,8DAA8D;AAAA,EAC9D,2DAA2D;AAAA,EAC3D,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,0CAA0C;AAAA,EAC1C,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,8BAA8B;AAAA,EAC9B,0CAA0C;AAAA,EAC1C,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAC3C;", "names": []}