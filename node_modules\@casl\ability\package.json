{"name": "@casl/ability", "version": "6.5.0", "description": "CASL is an isomorphic authorization JavaScript library which restricts what resources a given user is allowed to access", "funding": "https://github.com/stalniy/casl/blob/master/BACKERS.md", "main": "dist/es6c/index.js", "module": "dist/es5m/index.js", "es2015": "dist/es6m/index.mjs", "legacy": "dist/umd/index.js", "typings": "./index.d.ts", "exports": {".": {"types": "./dist/types/index.d.ts", "import": "./dist/es6m/index.mjs", "require": "./dist/es6c/index.js"}, "./extra": {"types": "./dist/types/extra.d.ts", "import": "./dist/es6m/extra.mjs", "require": "./dist/es6c/extra.js"}}, "sideEffects": false, "repository": {"type": "git", "url": "https://github.com/stalniy/casl.git", "directory": "packages/casl-ability"}, "homepage": "https://casl.js.org", "publishConfig": {"access": "public"}, "scripts": {"build.core": "dx rollup -n casl -g @ucast/mongo2js:ucast.mongo2js", "build.extra": "dx rollup -i src/extra.ts -n casl.extra -g @ucast/mongo2js:ucast.mongo2js", "build.types": "dx tsc && cp index.metadata.json dist/types", "prebuild": "rm -rf dist/*", "build": "npm run build.types && npm run build.core && npm run build.extra", "lint": "dx eslint src/ spec/", "test": "dx jest", "prerelease": "npm run lint && npm test && NODE_ENV=production npm run build", "release": "dx semantic-release", "preb": "echo 'pre'", "b": "echo 123"}, "keywords": ["permissions", "authorization", "acl", "abac", "rbac", "ibac", "cancan"], "author": "<PERSON><PERSON><PERSON> <<EMAIL>>", "license": "MIT", "devDependencies": {"@casl/dx": "workspace:^1.0.0", "@types/jest": "^29.0.0", "@types/node": "^18.0.0", "chai": "^4.1.0", "chai-spies": "^1.0.0", "expect-type": "^0.15.0"}, "files": ["dist", "*.d.ts", "extra"], "dependencies": {"@ucast/mongo2js": "^1.3.0"}}