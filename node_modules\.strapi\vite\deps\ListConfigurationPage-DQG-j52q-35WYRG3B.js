import {
  FieldTypeIcon
} from "./chunk-N4FK6APT.js";
import {
  CardDragPreview
} from "./chunk-PVIQSKRY.js";
import {
  setIn
} from "./chunk-JEXFZZG6.js";
import {
  ItemTypes,
  useDragAndDrop
} from "./chunk-Y4KMKP2D.js";
import {
  useTypedSelector
} from "./chunk-QLLK5WRM.js";
import {
  getEmptyImage
} from "./chunk-3TBNZ2ES.js";
import {
  SINGLE_TYPES,
  capitalise,
  checkIfAttributeIsDisplayable,
  getTranslation,
  useDoc,
  useDocLayout,
  useGetContentTypeConfigurationQuery,
  useIntl,
  useUpdateContentTypeConfigurationMutation
} from "./chunk-D63J2BWQ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-VOKTPSOW.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  BackButton,
  Form2 as Form,
  Layouts,
  MemoizedInputRenderer,
  create3 as create,
  create4 as create2,
  create6 as create3,
  useField,
  useForm
} from "./chunk-ELTZWS66.js";
import {
  Page,
  useAPIErrorHandler,
  useNotification,
  useTracking
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Button,
  Divider,
  Flex,
  Grid,
  Main,
  Menu,
  Modal,
  Typography,
  VisuallyHidden,
  useCollator,
  useComposedRefs
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  Navigate
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1d,
  ForwardRef$1r,
  ForwardRef$3H,
  ForwardRef$3V
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/ListConfigurationPage-DQG-j52q.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var Header = ({ name }) => {
  const { formatMessage } = useIntl();
  const modified = useForm("Header", (state) => state.modified);
  const isSubmitting = useForm("Header", (state) => state.isSubmitting);
  return (0, import_jsx_runtime.jsx)(
    Layouts.Header,
    {
      navigationAction: (0, import_jsx_runtime.jsx)(BackButton, {}),
      primaryAction: (0, import_jsx_runtime.jsx)(Button, { size: "S", disabled: !modified, type: "submit", loading: isSubmitting, children: formatMessage({ id: "global.save", defaultMessage: "Save" }) }),
      subtitle: formatMessage({
        id: getTranslation("components.SettingsViewWrapper.pluginHeader.description.list-settings"),
        defaultMessage: "Define the settings of the list view."
      }),
      title: formatMessage(
        {
          id: getTranslation("components.SettingsViewWrapper.pluginHeader.title"),
          defaultMessage: "Configure the view - {name}"
        },
        { name: capitalise(name) }
      )
    }
  );
};
var EXCLUDED_SORT_ATTRIBUTE_TYPES = [
  "media",
  "richtext",
  "dynamiczone",
  "relation",
  "component",
  "json",
  "blocks"
];
var Settings = () => {
  const { formatMessage, locale } = useIntl();
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const { schema } = useDoc();
  const layout = useForm("Settings", (state) => state.values.layout ?? []);
  const currentSortBy = useForm(
    "Settings",
    (state) => state.values.settings.defaultSortBy
  );
  const onChange = useForm("Settings", (state) => state.onChange);
  const sortOptions = React.useMemo(
    () => Object.values(layout).reduce((acc, field) => {
      if (schema && !EXCLUDED_SORT_ATTRIBUTE_TYPES.includes(schema.attributes[field.name].type)) {
        acc.push({
          value: field.name,
          label: typeof field.label !== "string" ? formatMessage(field.label) : field.label
        });
      }
      return acc;
    }, []),
    [formatMessage, layout, schema]
  );
  const sortOptionsSorted = sortOptions.sort((a, b) => formatter.compare(a.label, b.label));
  React.useEffect(() => {
    var _a;
    if (sortOptionsSorted.findIndex((opt) => opt.value === currentSortBy) === -1) {
      onChange("settings.defaultSortBy", (_a = sortOptionsSorted[0]) == null ? void 0 : _a.value);
    }
  }, [currentSortBy, onChange, sortOptionsSorted]);
  const formLayout = React.useMemo(
    () => SETTINGS_FORM_LAYOUT.map(
      (row) => row.map((field) => {
        if (field.type === "enumeration") {
          return {
            ...field,
            hint: field.hint ? formatMessage(field.hint) : void 0,
            label: formatMessage(field.label),
            options: field.name === "settings.defaultSortBy" ? sortOptionsSorted : field.options
          };
        } else {
          return {
            ...field,
            hint: field.hint ? formatMessage(field.hint) : void 0,
            label: formatMessage(field.label)
          };
        }
      })
    ),
    [formatMessage, sortOptionsSorted]
  );
  return (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
    (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
      id: getTranslation("containers.SettingPage.settings"),
      defaultMessage: "Settings"
    }) }),
    (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 4, children: formLayout.map(
      (row) => row.map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(Grid.Item, { s: 12, col: size, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, { ...field }) }, field.name))
    ) }, "bottom")
  ] });
};
var SETTINGS_FORM_LAYOUT = [
  [
    {
      label: {
        id: getTranslation("form.Input.search"),
        defaultMessage: "Enable search"
      },
      name: "settings.searchable",
      size: 4,
      type: "boolean"
    },
    {
      label: {
        id: getTranslation("form.Input.filters"),
        defaultMessage: "Enable filters"
      },
      name: "settings.filterable",
      size: 4,
      type: "boolean"
    },
    {
      label: {
        id: getTranslation("form.Input.bulkActions"),
        defaultMessage: "Enable bulk actions"
      },
      name: "settings.bulkable",
      size: 4,
      type: "boolean"
    }
  ],
  [
    {
      hint: {
        id: getTranslation("form.Input.pageEntries.inputDescription"),
        defaultMessage: "Note: You can override this value in the Collection Type settings page."
      },
      label: {
        id: getTranslation("form.Input.pageEntries"),
        defaultMessage: "Entries per page"
      },
      name: "settings.pageSize",
      options: ["10", "20", "50", "100"].map((value) => ({ value, label: value })),
      size: 6,
      type: "enumeration"
    },
    {
      label: {
        id: getTranslation("form.Input.defaultSort"),
        defaultMessage: "Default sort attribute"
      },
      name: "settings.defaultSortBy",
      options: [],
      size: 3,
      type: "enumeration"
    },
    {
      label: {
        id: getTranslation("form.Input.sort.order"),
        defaultMessage: "Default sort order"
      },
      name: "settings.defaultSortOrder",
      options: ["ASC", "DESC"].map((value) => ({ value, label: value })),
      size: 3,
      type: "enumeration"
    }
  ]
];
var FIELD_SCHEMA = create3().shape({
  label: create2().required(),
  sortable: create()
});
var EditFieldForm = ({ attribute, name, onClose }) => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { value, onChange } = useField(name);
  if (!value) {
    console.error(
      "You've opened a field to edit without it being part of the form, this is likely a bug with Strapi. Please open an issue."
    );
    toggleNotification({
      message: formatMessage({
        id: "content-manager.containers.list-settings.modal-form.error",
        defaultMessage: "An error occurred while trying to open the form."
      }),
      type: "danger"
    });
    return null;
  }
  let shouldDisplaySortToggle = !["media", "relation"].includes(attribute.type);
  if ("relation" in attribute && ["oneWay", "oneToOne", "manyToOne"].includes(attribute.relation)) {
    shouldDisplaySortToggle = true;
  }
  return (0, import_jsx_runtime.jsx)(Modal.Content, { children: (0, import_jsx_runtime.jsxs)(
    Form,
    {
      method: "PUT",
      initialValues: value,
      validationSchema: FIELD_SCHEMA,
      onSubmit: (data) => {
        onChange(name, data);
        onClose();
      },
      children: [
        (0, import_jsx_runtime.jsx)(Modal.Header, { children: (0, import_jsx_runtime.jsxs)(HeaderContainer, { children: [
          (0, import_jsx_runtime.jsx)(FieldTypeIcon, { type: attribute.type }),
          (0, import_jsx_runtime.jsx)(Modal.Title, { children: formatMessage(
            {
              id: getTranslation("containers.list-settings.modal-form.label"),
              defaultMessage: "Edit {fieldName}"
            },
            { fieldName: capitalise(value.label) }
          ) })
        ] }) }),
        (0, import_jsx_runtime.jsx)(Modal.Body, { children: (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 4, children: [
          {
            name: "label",
            label: formatMessage({
              id: getTranslation("form.Input.label"),
              defaultMessage: "Label"
            }),
            hint: formatMessage({
              id: getTranslation("form.Input.label.inputDescription"),
              defaultMessage: "This value overrides the label displayed in the table's head"
            }),
            size: 6,
            type: "string"
          },
          {
            label: formatMessage({
              id: getTranslation("form.Input.sort.field"),
              defaultMessage: "Enable sort on this field"
            }),
            name: "sortable",
            size: 6,
            type: "boolean"
          }
        ].filter(
          (field) => field.name !== "sortable" || field.name === "sortable" && shouldDisplaySortToggle
        ).map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(
          Grid.Item,
          {
            s: 12,
            col: size,
            direction: "column",
            alignItems: "stretch",
            children: (0, import_jsx_runtime.jsx)(MemoizedInputRenderer, { ...field })
          },
          field.name
        )) }) }),
        (0, import_jsx_runtime.jsxs)(Modal.Footer, { children: [
          (0, import_jsx_runtime.jsx)(Button, { onClick: onClose, variant: "tertiary", children: formatMessage({ id: "app.components.Button.cancel", defaultMessage: "Cancel" }) }),
          (0, import_jsx_runtime.jsx)(Button, { type: "submit", children: formatMessage({ id: "global.finish", defaultMessage: "Finish" }) })
        ] })
      ]
    }
  ) });
};
var HeaderContainer = dt(Flex)`
  svg {
    width: 3.2rem;
    margin-right: ${({ theme }) => theme.spaces[3]};
  }
`;
var DraggableCard = ({
  attribute,
  index,
  isDraggingSibling,
  label,
  name,
  onMoveField,
  onRemoveField,
  setIsDraggingSibling
}) => {
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const { formatMessage } = useIntl();
  const [, forceRerenderAfterDnd] = React.useState(false);
  const [{ isDragging }, objectRef, dropRef, dragRef, dragPreviewRef] = useDragAndDrop(true, {
    type: ItemTypes.FIELD,
    item: { index, label, name },
    index,
    onMoveItem: onMoveField,
    onEnd: () => setIsDraggingSibling(false)
  });
  React.useEffect(() => {
    dragPreviewRef(getEmptyImage(), { captureDraggingState: false });
  }, [dragPreviewRef]);
  React.useEffect(() => {
    if (isDragging) {
      setIsDraggingSibling(true);
    }
  }, [isDragging, setIsDraggingSibling]);
  React.useEffect(() => {
    if (!isDraggingSibling) {
      forceRerenderAfterDnd((prev) => !prev);
    }
  }, [isDraggingSibling]);
  const composedRefs = useComposedRefs(
    dropRef,
    objectRef
  );
  return (0, import_jsx_runtime.jsxs)(FieldWrapper, { ref: composedRefs, children: [
    isDragging && (0, import_jsx_runtime.jsx)(CardDragPreview, { label }),
    !isDragging && isDraggingSibling && (0, import_jsx_runtime.jsx)(CardDragPreview, { isSibling: true, label }),
    !isDragging && !isDraggingSibling && (0, import_jsx_runtime.jsxs)(
      FieldContainer,
      {
        borderColor: "neutral150",
        background: "neutral100",
        hasRadius: true,
        justifyContent: "space-between",
        onClick: () => setIsModalOpen(true),
        children: [
          (0, import_jsx_runtime.jsxs)(Flex, { gap: 3, children: [
            (0, import_jsx_runtime.jsx)(
              DragButton,
              {
                ref: dragRef,
                "aria-label": formatMessage(
                  {
                    id: getTranslation("components.DraggableCard.move.field"),
                    defaultMessage: "Move {item}"
                  },
                  { item: label }
                ),
                onClick: (e) => e.stopPropagation(),
                children: (0, import_jsx_runtime.jsx)(ForwardRef$3H, {})
              }
            ),
            (0, import_jsx_runtime.jsx)(Typography, { fontWeight: "bold", children: label })
          ] }),
          (0, import_jsx_runtime.jsxs)(Flex, { paddingLeft: 3, onClick: (e) => e.stopPropagation(), children: [
            (0, import_jsx_runtime.jsxs)(Modal.Root, { open: isModalOpen, onOpenChange: setIsModalOpen, children: [
              (0, import_jsx_runtime.jsx)(Modal.Trigger, { children: (0, import_jsx_runtime.jsx)(
                ActionButton,
                {
                  onClick: (e) => {
                    e.stopPropagation();
                  },
                  "aria-label": formatMessage(
                    {
                      id: getTranslation("components.DraggableCard.edit.field"),
                      defaultMessage: "Edit {item}"
                    },
                    { item: label }
                  ),
                  type: "button",
                  children: (0, import_jsx_runtime.jsx)(ForwardRef$1r, { width: "1.2rem", height: "1.2rem" })
                }
              ) }),
              (0, import_jsx_runtime.jsx)(
                EditFieldForm,
                {
                  attribute,
                  name: `layout.${index}`,
                  onClose: () => {
                    setIsModalOpen(false);
                  }
                }
              )
            ] }),
            (0, import_jsx_runtime.jsx)(
              ActionButton,
              {
                onClick: onRemoveField,
                "data-testid": `delete-${name}`,
                "aria-label": formatMessage(
                  {
                    id: getTranslation("components.DraggableCard.delete.field"),
                    defaultMessage: "Delete {item}"
                  },
                  { item: label }
                ),
                type: "button",
                children: (0, import_jsx_runtime.jsx)(ForwardRef$3V, { width: "1.2rem", height: "1.2rem" })
              }
            )
          ] })
        ]
      }
    )
  ] });
};
var ActionButton = dt.button`
  display: flex;
  align-items: center;
  height: ${({ theme }) => theme.spaces[7]};
  color: ${({ theme }) => theme.colors.neutral600};

  &:hover {
    color: ${({ theme }) => theme.colors.neutral700};
  }

  &:last-child {
    padding: 0 ${({ theme }) => theme.spaces[3]};
  }
`;
var DragButton = dt(ActionButton)`
  padding: 0 ${({ theme }) => theme.spaces[3]};
  border-right: 1px solid ${({ theme }) => theme.colors.neutral150};
  cursor: all-scroll;
`;
var FieldContainer = dt(Flex)`
  max-height: 3.2rem;
  cursor: pointer;
`;
var FieldWrapper = dt(Box)`
  &:last-child {
    padding-right: ${({ theme }) => theme.spaces[3]};
  }
`;
var SortDisplayedFields = () => {
  const { formatMessage } = useIntl();
  const { model, schema } = useDoc();
  const [isDraggingSibling, setIsDraggingSibling] = React.useState(false);
  const [lastAction, setLastAction] = React.useState(null);
  const scrollableContainerRef = React.useRef(null);
  const values = useForm(
    "SortDisplayedFields",
    (state) => state.values.layout ?? []
  );
  const addFieldRow = useForm("SortDisplayedFields", (state) => state.addFieldRow);
  const removeFieldRow = useForm("SortDisplayedFields", (state) => state.removeFieldRow);
  const moveFieldRow = useForm("SortDisplayedFields", (state) => state.moveFieldRow);
  const { metadata: allMetadata } = useGetContentTypeConfigurationQuery(model, {
    selectFromResult: ({ data }) => ({ metadata: (data == null ? void 0 : data.contentType.metadatas) ?? {} })
  });
  const nonDisplayedFields = React.useMemo(() => {
    if (!schema) {
      return [];
    }
    const displayedFieldNames = values.map((field) => field.name);
    return Object.entries(schema.attributes).reduce(
      (acc, [name, attribute]) => {
        if (!displayedFieldNames.includes(name) && checkIfAttributeIsDisplayable(attribute)) {
          const { list: metadata } = allMetadata[name];
          acc.push({
            name,
            label: metadata.label || name,
            sortable: metadata.sortable
          });
        }
        return acc;
      },
      []
    );
  }, [allMetadata, values, schema]);
  const handleAddField = (field) => {
    setLastAction("add");
    addFieldRow("layout", field);
  };
  const handleRemoveField = (index) => {
    setLastAction("remove");
    removeFieldRow("layout", index);
  };
  const handleMoveField = (dragIndex, hoverIndex) => {
    moveFieldRow("layout", dragIndex, hoverIndex);
  };
  React.useEffect(() => {
    if (lastAction === "add" && (scrollableContainerRef == null ? void 0 : scrollableContainerRef.current)) {
      scrollableContainerRef.current.scrollLeft = scrollableContainerRef.current.scrollWidth;
    }
  }, [lastAction]);
  return (0, import_jsx_runtime.jsxs)(Flex, { alignItems: "stretch", direction: "column", gap: 4, children: [
    (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
      id: getTranslation("containers.SettingPage.view"),
      defaultMessage: "View"
    }) }),
    (0, import_jsx_runtime.jsxs)(Flex, { padding: 4, borderColor: "neutral300", borderStyle: "dashed", borderWidth: "1px", hasRadius: true, children: [
      (0, import_jsx_runtime.jsx)(Box, { flex: "1", overflow: "auto hidden", ref: scrollableContainerRef, children: (0, import_jsx_runtime.jsx)(Flex, { gap: 3, children: values.map((field, index) => (0, import_jsx_runtime.jsx)(
        DraggableCard,
        {
          index,
          isDraggingSibling,
          onMoveField: handleMoveField,
          onRemoveField: () => handleRemoveField(index),
          setIsDraggingSibling,
          ...field,
          attribute: schema.attributes[field.name],
          label: typeof field.label === "object" ? formatMessage(field.label) : field.label
        },
        field.name
      )) }) }),
      (0, import_jsx_runtime.jsxs)(Menu.Root, { children: [
        (0, import_jsx_runtime.jsxs)(
          Menu.Trigger,
          {
            paddingLeft: 2,
            paddingRight: 2,
            justifyContent: "center",
            endIcon: null,
            disabled: nonDisplayedFields.length === 0,
            variant: "tertiary",
            children: [
              (0, import_jsx_runtime.jsx)(VisuallyHidden, { tag: "span", children: formatMessage({
                id: getTranslation("components.FieldSelect.label"),
                defaultMessage: "Add a field"
              }) }),
              (0, import_jsx_runtime.jsx)(ForwardRef$1d, { "aria-hidden": true, focusable: false, style: { position: "relative", top: 2 } })
            ]
          }
        ),
        (0, import_jsx_runtime.jsx)(Menu.Content, { children: nonDisplayedFields.map((field) => (0, import_jsx_runtime.jsx)(Menu.Item, { onSelect: () => handleAddField(field), children: typeof field.label === "object" ? formatMessage(field.label) : field.label }, field.name)) })
      ] })
    ] })
  ] });
};
var ListConfiguration = () => {
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { model, collectionType } = useDoc();
  const { isLoading: isLoadingLayout, list, edit } = useDocLayout();
  const [updateContentTypeConfiguration] = useUpdateContentTypeConfigurationMutation();
  const handleSubmit = async (data) => {
    try {
      trackUsage("willSaveContentTypeLayout");
      const layoutData = data.layout ?? [];
      const meta = Object.entries(edit.metadatas).reduce((acc, [name, editMeta]) => {
        const { mainField: _mainField, ...listMeta } = list.metadatas[name];
        const { label, sortable } = layoutData.find((field) => field.name === name) ?? {};
        acc[name] = {
          edit: editMeta,
          list: {
            ...listMeta,
            label: label || listMeta.label,
            sortable: sortable || listMeta.sortable
          }
        };
        return acc;
      }, {});
      const res = await updateContentTypeConfiguration({
        layouts: {
          edit: edit.layout.flatMap(
            (panel) => panel.map((row) => row.map(({ name, size }) => ({ name, size })))
          ),
          list: layoutData.map((field) => field.name)
        },
        settings: setIn(data.settings, "displayName", void 0),
        metadatas: meta,
        uid: model
      });
      if ("data" in res) {
        trackUsage("didEditListSettings");
        toggleNotification({
          type: "success",
          message: formatMessage({ id: "notification.success.saved", defaultMessage: "Saved" })
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
      }
    } catch (err) {
      console.error(err);
      toggleNotification({
        type: "danger",
        message: formatMessage({ id: "notification.error", defaultMessage: "An error occurred" })
      });
    }
  };
  const initialValues = React.useMemo(() => {
    return {
      layout: list.layout.map(({ label, sortable, name }) => ({
        label: typeof label === "string" ? label : formatMessage(label),
        sortable,
        name
      })),
      settings: list.settings
    };
  }, [formatMessage, list.layout, list.settings]);
  if (collectionType === SINGLE_TYPES) {
    return (0, import_jsx_runtime.jsx)(Navigate, { to: `/single-types/${model}` });
  }
  if (isLoadingLayout) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Layouts.Root, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: `Configure ${list.settings.displayName} List View` }),
    (0, import_jsx_runtime.jsx)(Main, { children: (0, import_jsx_runtime.jsxs)(Form, { initialValues, onSubmit: handleSubmit, method: "PUT", children: [
      (0, import_jsx_runtime.jsx)(
        Header,
        {
          collectionType,
          model,
          name: list.settings.displayName ?? ""
        }
      ),
      (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(
        Flex,
        {
          alignItems: "stretch",
          background: "neutral0",
          direction: "column",
          gap: 6,
          hasRadius: true,
          shadow: "tableShadow",
          paddingTop: 6,
          paddingBottom: 6,
          paddingLeft: 7,
          paddingRight: 7,
          children: [
            (0, import_jsx_runtime.jsx)(Settings, {}),
            (0, import_jsx_runtime.jsx)(Divider, {}),
            (0, import_jsx_runtime.jsx)(SortDisplayedFields, {})
          ]
        }
      ) })
    ] }) })
  ] });
};
var ProtectedListConfiguration = () => {
  const permissions = useTypedSelector(
    (state) => {
      var _a;
      return (_a = state.admin_app.permissions.contentManager) == null ? void 0 : _a.collectionTypesConfigurations;
    }
  );
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions, children: (0, import_jsx_runtime.jsx)(ListConfiguration, {}) });
};
export {
  ListConfiguration,
  ProtectedListConfiguration
};
//# sourceMappingURL=ListConfigurationPage-DQG-j52q-35WYRG3B.js.map
