{"version": 3, "sources": ["../../../@strapi/content-manager/dist/_chunks/vi-DUXIk_fw.mjs"], "sourcesContent": ["const groups = \"Nhóm\";\nconst pageNotFound = \"Không Tìm Thấy Trang\";\nconst vi = {\n  \"EditRelations.title\": \"Dữ Liệ<PERSON>uan <PERSON>\",\n  \"components.AddFilterCTA.add\": \"Lọc\",\n  \"components.AddFilterCTA.hide\": \"Lọc\",\n  \"components.DraggableAttr.edit\": \"Nhấn để chỉnh sửa\",\n  \"components.EmptyAttributesBlock.button\": \"Đến trang cài đặt\",\n  \"components.EmptyAttributesBlock.description\": \"Bạn có thể thay đổi cài đặt của bạn\",\n  \"components.FilterOptions.button.apply\": \"Áp dụng\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Áp dụng\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Xóa tất cả\",\n  \"components.FiltersPickWrapper.PluginHeader.description\": \"Cài đặt các điều kiện để áp dụng cho việc lọc các bản ghi\",\n  \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Các bộ lọc\",\n  \"components.FiltersPickWrapper.hide\": \"Ẩn đi\",\n  \"components.LimitSelect.itemsPerPage\": \"Số lượng bản ghi trong trang\",\n  \"components.Search.placeholder\": \"Tìm một bản ghi...\",\n  \"components.TableDelete.delete\": \"Xóa tất cả\",\n  \"components.TableDelete.deleteSelected\": \"Xóa đã chọn\",\n  \"components.TableEmpty.withFilters\": \"Không có {contentType} với bộ lọc được dùng\",\n  \"components.TableEmpty.withSearch\": \"Không có {contentType} tương ứng với tìm kiếm ({search})...\",\n  \"components.TableEmpty.withoutFilter\": \"Không có {contentType}...\",\n  \"containers.Edit.Link.Layout\": \"Cấu hình bố cục\",\n  \"containers.Edit.addAnItem\": \"Thêm một bản ghi...\",\n  \"containers.Edit.clickToJump\": \"Nhấn để nhảy vào bản ghi\",\n  \"containers.Edit.delete\": \"Xóa\",\n  \"containers.Edit.editing\": \"Đăng sửa...\",\n  \"containers.Edit.pluginHeader.title.new\": \"Tạo một Bản ghi\",\n  \"containers.Edit.reset\": \"Làm lại\",\n  \"containers.Edit.returnList\": \"Trở về danh sách\",\n  \"containers.Edit.seeDetails\": \"Chi tiết\",\n  \"containers.Edit.submit\": \"Lưu\",\n  \"containers.EditView.notification.errors\": \"Bảng nhập liệu có vài lỗi\",\n  \"containers.Home.introduction\": \"Để chỉnh sửa các bản ghi của bạn, đi đến liên kết ở menu bên trái. Plugin này chưa có cách thích hợp để chỉnh sửa các cài đặt và nó vẫn đang được phát triển.\",\n  \"containers.Home.pluginHeaderDescription\": \"Quản lý các bản ghi thông qua một giao diện mạnh và đẹp.\",\n  \"containers.Home.pluginHeaderTitle\": \"Quản Lý Nội Dung\",\n  \"containers.List.errorFetchRecords\": \"Lỗi\",\n  \"containers.list.displayedFields\": \"Các trường đã được trình bày\",\n  \"containers.SettingPage.attributes\": \"Các trường thuộc tính\",\n  \"containers.SettingPage.attributes.description\": \"Định nghĩa thứ tự các thuộc tính\",\n  \"containers.SettingPage.editSettings.description\": \"Kéo & thả các trường để xây dựng bố cục\",\n  \"containers.SettingPage.editSettings.entry.title\": \"Tên bản ghi\",\n  \"containers.SettingPage.editSettings.entry.title.description\": \"Cài đặt trường được trình bày trong bản ghi của bạn\",\n  \"containers.SettingPage.editSettings.title\": \"Chỉnh sửa hiển thị (các cài đặt)\",\n  \"containers.SettingPage.layout\": \"Bố cục\",\n  \"containers.SettingPage.listSettings.title\": \"Hiển thị danh sách (các cài đặt)\",\n  \"containers.SettingPage.settings\": \"Các cài đặt\",\n  \"containers.SettingViewModel.pluginHeader.title\": \"Quản Lý Nội Dung - {name}\",\n  \"containers.SettingsPage.Block.contentType.description\": \"Cấu hình các cài đặt riêng\",\n  \"containers.SettingsPage.Block.generalSettings.title\": \"Chung\",\n  \"containers.SettingsView.list.title\": \"Các cấu hình về Trình bày\",\n  \"emptyAttributes.title\": \"Chưa có trường nào hết\",\n  \"error.attribute.key.taken\": \"Giá trị này đã tồn tại\",\n  \"error.attribute.sameKeyAndName\": \"Không thể bằng nhau\",\n  \"error.attribute.taken\": \"Tên trường này đã tồn tại\",\n  \"error.contentTypeName.taken\": \"Tên này đã tồn tại\",\n  \"error.model.fetch\": \"Một lỗi đã xảy ra trong khi lấy về cấu hình nội dung.\",\n  \"error.record.create\": \"Một lỗi đã xảy ra trong khi tạo bản ghi.\",\n  \"error.record.delete\": \"Một lỗi đã xảy ra trong khi xoá bản ghi.\",\n  \"error.record.fetch\": \"Một lỗi đã xảy ra trong khi lấy về bản ghi.\",\n  \"error.record.update\": \"Một lỗi đã xảy ra trong khi cập nhật bản ghi.\",\n  \"error.records.count\": \"Một lỗi đã xảy ra trong khi lấy về số lượng bản ghi.\",\n  \"error.records.fetch\": \"Một lỗi đã xảy ra trong khi lấy về các bản ghi.\",\n  \"error.schema.generation\": \"Một lỗi đã xảy ra trong khi quá trình tạo ra lược đồ.\",\n  \"error.validation.json\": \"Đây không phải là định dạng JSON\",\n  \"error.validation.max\": \"Giá trị quá cao.\",\n  \"error.validation.maxLength\": \"Giá trị quá dài.\",\n  \"error.validation.min\": \"Giá trị quá thấp.\",\n  \"error.validation.minLength\": \"Giá trị quá ngắn.\",\n  \"error.validation.minSupMax\": \"Không thể là trên mũ\",\n  \"error.validation.regex\": \"Giá trị không khới với regex.\",\n  \"error.validation.required\": \"Giá trị này bắt buộc.\",\n  \"form.Input.bulkActions\": \"Kích hoạt hoạt động gộp\",\n  \"form.Input.defaultSort\": \"Thuộc tính sắp xếp mặc định\",\n  \"form.Input.description\": \"Mô tả\",\n  \"form.Input.description.placeholder\": \"Tên hiển thị trong hồ sơ\",\n  \"form.Input.editable\": \"Trường chỉnh sửa được\",\n  \"form.Input.filters\": \"Kích hoạt các bộ lọc\",\n  \"form.Input.label\": \"Nhãn\",\n  \"form.Input.label.inputDescription\": \"Giá trị này ghi đè lên nhãn được trình bày trong phần đầu của bảng\",\n  \"form.Input.pageEntries\": \"Bản ghi trong trang\",\n  \"form.Input.placeholder\": \"Chỗ chờ giá trị\",\n  \"form.Input.placeholder.placeholder\": \"Giá trị tuyệt vời của tôi\",\n  \"form.Input.search\": \"Kích hoạt tìm kiếm\",\n  \"form.Input.search.field\": \"Kích hoạt tìm kiếm cho trường này\",\n  \"form.Input.sort.field\": \"Kích hoạt sắp xếp trên trường này\",\n  \"form.Input.wysiwyg\": \"Trình bày như là WYSIWYG\",\n  \"global.displayedFields\": \"Các Trường Đã Được Trình Bày\",\n  groups,\n  \"groups.numbered\": \"Nhóm ({number})\",\n  \"notification.error.displayedFields\": \"Bạn cần trình bày ít nhất một trường\",\n  \"notification.error.relationship.fetch\": \"Một lỗi đã xảy ra trong khi lấy về mối quan hệ.\",\n  \"notification.info.SettingPage.disableSort\": \"Bạn cần có một thuộc tính được phép sắp xếp\",\n  \"notification.info.minimumFields\": \"Bạn cần hiển thị ít nhất một trường\",\n  \"notification.upload.error\": \"Một lỗi đã xảy ra trong khi tải lên các tập tin của bạn\",\n  pageNotFound,\n  \"plugin.description.long\": \"Cách nhanh để xem, sửa và xoá dữ liệu trong cơ sở dữ liệu của bạn.\",\n  \"plugin.description.short\": \"Cách nhanh để xem, sửa và xoá dữ liệu trong cơ sở dữ liệu của bạn.\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"Bạn có chắc là muốn xoá bản ghi này không?\",\n  \"popUpWarning.bodyMessage.contentType.delete.all\": \"Bạn có chắc là muốn xoá các bản ghi này không?\",\n  \"popUpWarning.warning.cancelAllSettings\": \"Bạn có chắc là muốn hủy bỏ các thay đổi của bạn?\",\n  \"popUpWarning.warning.updateAllSettings\": \"Nó sẽ thay đổi tất cả cài đặt của bạn\",\n  \"success.record.delete\": \"Đã xoá\",\n  \"success.record.save\": \"Đã lưu\"\n};\nexport {\n  vi as default,\n  groups,\n  pageNotFound\n};\n//# sourceMappingURL=vi-DUXIk_fw.mjs.map\n"], "mappings": ";;;AAAA,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,KAAK;AAAA,EACT,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,sCAAsC;AAAA,EACtC,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,uBAAuB;AACzB;", "names": []}