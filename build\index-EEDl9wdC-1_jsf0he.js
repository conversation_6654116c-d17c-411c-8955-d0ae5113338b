const __vite__fileDeps=["ListView-g5kB4f_y-Bsz8fZWQ.js","strapi-YzJfjJ2z.js","strapi-COJtagOC.css","groupBy-BivwhTv9.js","_baseEach-BkkNIx9z.js","index-rkzE4TV9.js","index-BRVyLNfZ.js","_arrayIncludesWith-BNzMLSv9.js","sortBy-vMk_GNbv.js","_baseMap-LzQFtWYw.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{gS as Qy,h_ as Dl,h$ as ql,i0 as eb,i1 as Pl,gp as Wa,as as tb,i2 as nb,i3 as rb,i4 as Ul,i5 as ab,hE as ob,gZ as ib,hU as sb,i6 as ub,i7 as lb,i8 as cb,i9 as db,ia as zl,ib as fb,hQ as pb,hR as mb,hS as hb,ic as Er,id as gb,c9 as qr,f8 as yb,fa as La,fb as rl,ie as bb,ig as vb,fc as xb,fd as _b,ff as Lr,fe as mi,f9 as al,fg as ol,fh as Cb,f6 as Tb,ih as il,f7 as wb,fi as jb,f5 as Mb,m as l,S as qt,J as Ne,cU as sl,cV as Ab,cW as Fb,cX as Rb,cY as kb,cZ as Sb,c_ as $b,c$ as Nb,c3 as Eb,d0 as Lb,d1 as Ib,d2 as Ob,d3 as Db,d4 as qb,d5 as Pb,d6 as Ub,d7 as zb,d8 as Bb,d9 as Wb,da as Vb,db as Hb,dc as Zb,dd as Gb,de as Yb,df as Bl,dg as Kb,dh as Jb,di as Xb,dj as Qb,K as ev,dk as tv,dl as nv,dm as rv,dn as av,b0 as wn,bD as ov,dp as iv,dq as sv,dr as uv,ds as lv,dt as cv,F as dv,du as fv,dv as hi,dw as Wl,dx as Vl,dy as Hl,dz as pv,dA as mv,dB as hv,dC as gv,dD as yv,dE as bv,dF as Zl,dG as Gl,dH as Yl,dI as vv,dJ as xv,dK as _v,dL as Cv,dM as Tv,dN as wv,dO as jv,dP as Mv,dQ as Av,dR as Fv,dS as Rv,dT as kv,dU as Sv,dV as $v,dW as Nv,dX as Ev,dY as Lv,dZ as Iv,d_ as Ov,d$ as Dv,ak as qv,e0 as Pv,e1 as Uv,e2 as zv,ad as Kl,e3 as Bv,e4 as Wv,bm as Vv,e5 as Hv,e6 as Zv,e7 as Gv,e8 as Yv,e9 as Kv,ea as Jv,eb as Xv,ec as Qv,ed as e0,ee as t0,ef as n0,eg as r0,eh as a0,ei as o0,ej as i0,ek as s0,el as u0,em as l0,N as c0,en as d0,eo as f0,ep as p0,eq as m0,er as h0,es as g0,et as y0,eu as b0,ev as v0,ew as x0,ex as _0,ey as C0,ez as T0,bW as w0,Z as Jl,eA as j0,eB as M0,eC as A0,eD as F0,r as pe,ii as He,gq as he,bO as Xl,n as Dr,ij as R0,ba as Va,ao as Ha,ap as Ql,ik as ec,il as k0,a as Za,im as S0,b$ as $0,bV as N0,a5 as E0,at as ul,b as L0,gr as G,io as I0,ip as O0,iq as D0,ir as q0,aV as P0,is as U0,an as z0,al as B0,it as Ia,iu as W0,g7 as tc,iv as V0,iw as H0,ix as Ir,iy as nc,iz as ll,iA as Z0,iB as rc,l as Te,k as we,i as Fe,iC as Ua,iD as qa,j as Pr,bR as Dt,bJ as G0,iE as ki,aN as cl,iF as gi,iG as Y0,M as jn,w as ue,Y as ac,B as K0,D as cn,C as J0,cn as $t,s as ze,aS as Si,iH as $i,G as zn,iI as X0,bN as Q0,bK as ex,bl as tx,aU as nx,gs as rx,x as re,z as Un,ag as ax,af as oc,a9 as Ur,aa as zr,ae as ox,ac as ic,a7 as ix,a8 as sx,a6 as Ni,y as ux,ab as lx,a1 as Ga,iJ as sc,b5 as cx,b6 as dx,A as qe,ha as uc,aD as fx,aE as px,e_ as Pa,iK as mx,iL as hx,iM as lc,iN as gx,I as dl,aT as yx,b3 as bx,V as vx,iO as xx,iP as _x,iQ as Cx,iR as Tx,iS as cc,h4 as dc,iT as wx,iU as jx,iV as Mx,iW as Ax,iX as Fx,iY as Rx,iZ as kx,i_ as Sx,i$ as $x,j0 as Nx,j1 as Ex,j2 as Lx,j3 as Ix,j4 as Ox,L as Dx,fw as fc,fx as Ti,bk as pc,fq as qx,d as Px,cc as Ux,cd as zx,ce as Bx,cf as Wx,j5 as Vx,cg as mc,_ as fl,bI as Hx,j6 as Zx}from"./strapi-YzJfjJ2z.js";import{g as Gx}from"./groupBy-BivwhTv9.js";import{f as Yx}from"./index-rkzE4TV9.js";import{_ as Kx}from"./_arrayIncludesWith-BNzMLSv9.js";import{s as Jx}from"./sortBy-vMk_GNbv.js";import"./_baseEach-BkkNIx9z.js";import"./index-BRVyLNfZ.js";import"./_baseMap-LzQFtWYw.js";var Xx=ql,Qx=Qy,e_=Dl,t_="[object String]";function n_(a){return typeof a=="string"||!Qx(a)&&e_(a)&&Xx(a)==t_}var r_=n_,a_=eb,o_=a_("length"),i_=o_,hc="\\ud800-\\udfff",s_="\\u0300-\\u036f",u_="\\ufe20-\\ufe2f",l_="\\u20d0-\\u20ff",c_=s_+u_+l_,d_="\\ufe0e\\ufe0f",f_="["+hc+"]",wi="["+c_+"]",ji="\\ud83c[\\udffb-\\udfff]",p_="(?:"+wi+"|"+ji+")",gc="[^"+hc+"]",yc="(?:\\ud83c[\\udde6-\\uddff]){2}",bc="[\\ud800-\\udbff][\\udc00-\\udfff]",m_="\\u200d",vc=p_+"?",xc="["+d_+"]?",h_="(?:"+m_+"(?:"+[gc,yc,bc].join("|")+")"+xc+vc+")*",g_=xc+vc+h_,y_="(?:"+[gc+wi+"?",wi,yc,bc,f_].join("|")+")",pl=RegExp(ji+"(?="+ji+")|"+y_+g_,"g");function b_(a){for(var s=pl.lastIndex=0;pl.test(a);)++s;return s}var v_=b_,x_=i_,__=Pl,C_=v_;function T_(a){return __(a)?C_(a):x_(a)}var _c=T_,w_=rb,j_=nb,M_=tb,A_=r_,F_=_c,R_="[object Map]",k_="[object Set]";function S_(a){if(a==null)return 0;if(M_(a))return A_(a)?F_(a):a.length;var s=j_(a);return s==R_||s==k_?a.size:w_(a).length}var $_=S_;const N_=Wa($_);var E_=Ul;function L_(a){return E_(a).toLowerCase()}var I_=L_;const ml=Wa(I_);var O_=ql,D_=Dl,q_="[object RegExp]";function P_(a){return D_(a)&&O_(a)==q_}var U_=P_,z_=U_,B_=ob,hl=ab,gl=hl&&hl.isRegExp,W_=gl?B_(gl):z_,V_=W_,yl=ub,H_=cb,Z_=Pl,G_=ib,Y_=V_,K_=_c,J_=lb,X_=sb,bl=Ul,Q_=30,e1="...",t1=/\w*$/;function n1(a,s){var u=Q_,p=e1;if(G_(s)){var f="separator"in s?s.separator:f;u="length"in s?X_(s.length):u,p="omission"in s?yl(s.omission):p}a=bl(a);var b=a.length;if(Z_(a)){var g=J_(a);b=g.length}if(u>=b)return a;var h=u-K_(p);if(h<1)return p;var _=g?H_(g,0,h).join(""):a.slice(0,h);if(f===void 0)return _+p;if(g&&(h+=_.length-h),Y_(f)){if(a.slice(h).search(f)){var j,C=_;for(f.global||(f=RegExp(f.source,bl(t1.exec(f))+"g")),f.lastIndex=0;j=f.exec(C);)var w=j.index;_=_.slice(0,w===void 0?h:w)}}else if(a.indexOf(yl(f),h)!=h){var O=_.lastIndexOf(f);O>-1&&(_=_.slice(0,O))}return _+p}var r1=n1;const vl=Wa(r1);var yi=db,a1=fb,o1=zl,i1=1/0,s1=yi&&1/o1(new yi([,-0]))[1]==i1?function(a){return new yi(a)}:a1,u1=s1,l1=pb,c1=mb,d1=Kx,f1=hb,p1=u1,m1=zl,h1=200;function g1(a,s,u){var p=-1,f=c1,b=a.length,g=!0,h=[],_=h;if(u)g=!1,f=d1;else if(b>=h1){var j=s?null:p1(a);if(j)return m1(j);g=!1,f=f1,_=new l1}else _=s?[]:h;e:for(;++p<b;){var C=a[p],w=s?s(C):C;if(C=u||C!==0?C:0,g&&w===w){for(var O=_.length;O--;)if(_[O]===w)continue e;s&&_.push(w),h.push(C)}else f(_,w,u)||(_!==h&&_.push(w),h.push(C))}return h}var y1=g1,b1=y1;function v1(a){return a&&a.length?b1(a):[]}var x1=v1;const _1=Wa(x1);var za={exports:{}};/**
 * @license
 * Lodash <https://lodash.com/>
 * Copyright OpenJS Foundation and other contributors <https://openjsf.org/>
 * Released under MIT license <https://lodash.com/license>
 * Based on Underscore.js 1.8.3 <http://underscorejs.org/LICENSE>
 * Copyright Jeremy Ashkenas, DocumentCloud and Investigative Reporters & Editors
 */za.exports;(function(a,s){(function(){function u(v,M,o){switch(o.length){case 0:return v.call(M);case 1:return v.call(M,o[0]);case 2:return v.call(M,o[0],o[1]);case 3:return v.call(M,o[0],o[1],o[2])}return v.apply(M,o)}function p(v,M,o,P){for(var Y=-1,U=v==null?0:v.length;++Y<U;){var ct=v[Y];M(P,ct,o(ct),v)}return P}function f(v,M){for(var o=-1,P=v==null?0:v.length;++o<P&&M(v[o],o,v)!==!1;);return v}function b(v,M){for(var o=v==null?0:v.length;o--&&M(v[o],o,v)!==!1;);return v}function g(v,M){for(var o=-1,P=v==null?0:v.length;++o<P;)if(!M(v[o],o,v))return!1;return!0}function h(v,M){for(var o=-1,P=v==null?0:v.length,Y=0,U=[];++o<P;){var ct=v[o];M(ct,o,v)&&(U[Y++]=ct)}return U}function _(v,M){return!!(v!=null&&v.length)&&S(v,M,0)>-1}function j(v,M,o){for(var P=-1,Y=v==null?0:v.length;++P<Y;)if(o(M,v[P]))return!0;return!1}function C(v,M){for(var o=-1,P=v==null?0:v.length,Y=Array(P);++o<P;)Y[o]=M(v[o],o,v);return Y}function w(v,M){for(var o=-1,P=M.length,Y=v.length;++o<P;)v[Y+o]=M[o];return v}function O(v,M,o,P){var Y=-1,U=v==null?0:v.length;for(P&&U&&(o=v[++Y]);++Y<U;)o=M(o,v[Y],Y,v);return o}function I(v,M,o,P){var Y=v==null?0:v.length;for(P&&Y&&(o=v[--Y]);Y--;)o=M(o,v[Y],Y,v);return o}function $(v,M){for(var o=-1,P=v==null?0:v.length;++o<P;)if(M(v[o],o,v))return!0;return!1}function z(v){return v.split("")}function X(v){return v.match(td)||[]}function ee(v,M,o){var P;return o(v,function(Y,U,ct){if(M(Y,U,ct))return P=U,!1}),P}function N(v,M,o,P){for(var Y=v.length,U=o+(P?1:-1);P?U--:++U<Y;)if(M(v[U],U,v))return U;return-1}function S(v,M,o){return M===M?fr(v,M,o):N(v,ie,o)}function le(v,M,o,P){for(var Y=o-1,U=v.length;++Y<U;)if(P(v[Y],M))return Y;return-1}function ie(v){return v!==v}function Re(v,M){var o=v==null?0:v.length;return o?ve(v,M)/o:Zn}function ae(v){return function(M){return M==null?c:M[v]}}function ke(v){return function(M){return v==null?c:v[M]}}function se(v,M,o,P,Y){return Y(v,function(U,ct,bo){o=P?(P=!1,U):M(o,U,ct,bo)}),o}function tt(v,M){var o=v.length;for(v.sort(M);o--;)v[o]=v[o].value;return v}function ve(v,M){for(var o,P=-1,Y=v.length;++P<Y;){var U=M(v[P]);U!==c&&(o=o===c?U:o+U)}return o}function xe(v,M){for(var o=-1,P=Array(v);++o<v;)P[o]=M(o);return P}function Le(v,M){return C(M,function(o){return[o,v[o]]})}function ge(v){return v&&v.slice(0,pn(v)+1).replace(lo,"")}function E(v){return function(M){return v(M)}}function D(v,M){return C(M,function(o){return v[o]})}function q(v,M){return v.has(M)}function Yt(v,M){for(var o=-1,P=v.length;++o<P&&S(M,v[o],0)>-1;);return o}function dn(v,M){for(var o=v.length;o--&&S(M,v[o],0)>-1;);return o}function lr(v,M){for(var o=v.length,P=0;o--;)v[o]===M&&++P;return P}function fn(v){return"\\"+Dd[v]}function Pt(v,M){return v==null?c:v[M]}function gt(v){return Sd.test(v)}function cr(v){return $d.test(v)}function Bn(v){for(var M,o=[];!(M=v.next()).done;)o.push(M.value);return o}function An(v){var M=-1,o=Array(v.size);return v.forEach(function(P,Y){o[++M]=[Y,P]}),o}function Wn(v,M){return function(o){return v(M(o))}}function _t(v,M){for(var o=-1,P=v.length,Y=0,U=[];++o<P;){var ct=v[o];ct!==M&&ct!==yt||(v[o]=yt,U[Y++]=o)}return U}function Pe(v){var M=-1,o=Array(v.size);return v.forEach(function(P){o[++M]=P}),o}function dr(v){var M=-1,o=Array(v.size);return v.forEach(function(P){o[++M]=[P,P]}),o}function fr(v,M,o){for(var P=o-1,Y=v.length;++P<Y;)if(v[P]===M)return P;return-1}function Vn(v,M,o){for(var P=o+1;P--;)if(v[P]===M)return P;return P}function Ut(v){return gt(v)?st(v):zd(v)}function Ue(v){return gt(v)?Je(v):z(v)}function pn(v){for(var M=v.length;M--&&Jc.test(v.charAt(M)););return M}function st(v){for(var M=ho.lastIndex=0;ho.test(v);)++M;return M}function Je(v){return v.match(ho)||[]}function V(v){return v.match(kd)||[]}var c,Q="4.17.21",Se=200,ut="Unsupported core-js use. Try https://npms.io/search?q=ponyfill.",je="Expected a function",Ct="Invalid `variable` option passed into `_.template`",Kt="__lodash_hash_undefined__",Fn=500,yt="__lodash_placeholder__",nt=1,pr=2,Jt=4,zt=1,mn=2,lt=1,Bt=2,rt=4,Tt=8,Xt=16,wt=32,hn=64,Nt=128,Rn=256,Hn=512,Ya=30,Br="...",Wr=800,Vr=16,Hr=1,Ka=2,Ja=3,Qt=1/0,Wt=9007199254740991,Xa=17976931348623157e292,Zn=NaN,J=4294967295,at=J-1,bt=J>>>1,ye=[["ary",Nt],["bind",lt],["bindKey",Bt],["curry",Tt],["curryRight",Xt],["flip",Hn],["partial",wt],["partialRight",hn],["rearg",Rn]],$e="[object Arguments]",Ze="[object Array]",Ge="[object AsyncFunction]",kn="[object Boolean]",Sn="[object Date]",Lc="[object DOMException]",Zr="[object Error]",Gr="[object Function]",Li="[object GeneratorFunction]",Et="[object Map]",mr="[object Number]",Ic="[object Null]",en="[object Object]",Ii="[object Promise]",Oc="[object Proxy]",hr="[object RegExp]",Lt="[object Set]",gr="[object String]",Yr="[object Symbol]",Dc="[object Undefined]",yr="[object WeakMap]",qc="[object WeakSet]",br="[object ArrayBuffer]",Gn="[object DataView]",Qa="[object Float32Array]",eo="[object Float64Array]",to="[object Int8Array]",no="[object Int16Array]",ro="[object Int32Array]",ao="[object Uint8Array]",oo="[object Uint8ClampedArray]",io="[object Uint16Array]",so="[object Uint32Array]",Pc=/\b__p \+= '';/g,Uc=/\b(__p \+=) '' \+/g,zc=/(__e\(.*?\)|\b__t\)) \+\n'';/g,Oi=/&(?:amp|lt|gt|quot|#39);/g,Di=/[&<>"']/g,Bc=RegExp(Oi.source),Wc=RegExp(Di.source),Vc=/<%-([\s\S]+?)%>/g,Hc=/<%([\s\S]+?)%>/g,qi=/<%=([\s\S]+?)%>/g,Zc=/\.|\[(?:[^[\]]*|(["'])(?:(?!\1)[^\\]|\\.)*?\1)\]/,Gc=/^\w*$/,Yc=/[^.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|$))/g,uo=/[\\^$.*+?()[\]{}|]/g,Kc=RegExp(uo.source),lo=/^\s+/,Jc=/\s/,Xc=/\{(?:\n\/\* \[wrapped with .+\] \*\/)?\n?/,Qc=/\{\n\/\* \[wrapped with (.+)\] \*/,ed=/,? & /,td=/[^\x00-\x2f\x3a-\x40\x5b-\x60\x7b-\x7f]+/g,nd=/[()=,{}\[\]\/\s]/,rd=/\\(\\)?/g,ad=/\$\{([^\\}]*(?:\\.[^\\}]*)*)\}/g,Pi=/\w*$/,od=/^[-+]0x[0-9a-f]+$/i,id=/^0b[01]+$/i,sd=/^\[object .+?Constructor\]$/,ud=/^0o[0-7]+$/i,ld=/^(?:0|[1-9]\d*)$/,cd=/[\xc0-\xd6\xd8-\xf6\xf8-\xff\u0100-\u017f]/g,Kr=/($^)/,dd=/['\n\r\u2028\u2029\\]/g,Jr="\\ud800-\\udfff",fd="\\u0300-\\u036f",pd="\\ufe20-\\ufe2f",md="\\u20d0-\\u20ff",Ui=fd+pd+md,zi="\\u2700-\\u27bf",Bi="a-z\\xdf-\\xf6\\xf8-\\xff",hd="\\xac\\xb1\\xd7\\xf7",gd="\\x00-\\x2f\\x3a-\\x40\\x5b-\\x60\\x7b-\\xbf",yd="\\u2000-\\u206f",bd=" \\t\\x0b\\f\\xa0\\ufeff\\n\\r\\u2028\\u2029\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u202f\\u205f\\u3000",Wi="A-Z\\xc0-\\xd6\\xd8-\\xde",Vi="\\ufe0e\\ufe0f",Hi=hd+gd+yd+bd,co="['’]",vd="["+Jr+"]",Zi="["+Hi+"]",Xr="["+Ui+"]",Gi="\\d+",xd="["+zi+"]",Yi="["+Bi+"]",Ki="[^"+Jr+Hi+Gi+zi+Bi+Wi+"]",fo="\\ud83c[\\udffb-\\udfff]",_d="(?:"+Xr+"|"+fo+")",Ji="[^"+Jr+"]",po="(?:\\ud83c[\\udde6-\\uddff]){2}",mo="[\\ud800-\\udbff][\\udc00-\\udfff]",Yn="["+Wi+"]",Xi="\\u200d",Qi="(?:"+Yi+"|"+Ki+")",Cd="(?:"+Yn+"|"+Ki+")",es="(?:"+co+"(?:d|ll|m|re|s|t|ve))?",ts="(?:"+co+"(?:D|LL|M|RE|S|T|VE))?",ns=_d+"?",rs="["+Vi+"]?",Td="(?:"+Xi+"(?:"+[Ji,po,mo].join("|")+")"+rs+ns+")*",wd="\\d*(?:1st|2nd|3rd|(?![123])\\dth)(?=\\b|[A-Z_])",jd="\\d*(?:1ST|2ND|3RD|(?![123])\\dTH)(?=\\b|[a-z_])",as=rs+ns+Td,Md="(?:"+[xd,po,mo].join("|")+")"+as,Ad="(?:"+[Ji+Xr+"?",Xr,po,mo,vd].join("|")+")",Fd=RegExp(co,"g"),Rd=RegExp(Xr,"g"),ho=RegExp(fo+"(?="+fo+")|"+Ad+as,"g"),kd=RegExp([Yn+"?"+Yi+"+"+es+"(?="+[Zi,Yn,"$"].join("|")+")",Cd+"+"+ts+"(?="+[Zi,Yn+Qi,"$"].join("|")+")",Yn+"?"+Qi+"+"+es,Yn+"+"+ts,jd,wd,Gi,Md].join("|"),"g"),Sd=RegExp("["+Xi+Jr+Ui+Vi+"]"),$d=/[a-z][A-Z]|[A-Z]{2}[a-z]|[0-9][a-zA-Z]|[a-zA-Z][0-9]|[^a-zA-Z0-9 ]/,Nd=["Array","Buffer","DataView","Date","Error","Float32Array","Float64Array","Function","Int8Array","Int16Array","Int32Array","Map","Math","Object","Promise","RegExp","Set","String","Symbol","TypeError","Uint8Array","Uint8ClampedArray","Uint16Array","Uint32Array","WeakMap","_","clearTimeout","isFinite","parseInt","setTimeout"],Ed=-1,Me={};Me[Qa]=Me[eo]=Me[to]=Me[no]=Me[ro]=Me[ao]=Me[oo]=Me[io]=Me[so]=!0,Me[$e]=Me[Ze]=Me[br]=Me[kn]=Me[Gn]=Me[Sn]=Me[Zr]=Me[Gr]=Me[Et]=Me[mr]=Me[en]=Me[hr]=Me[Lt]=Me[gr]=Me[yr]=!1;var Ce={};Ce[$e]=Ce[Ze]=Ce[br]=Ce[Gn]=Ce[kn]=Ce[Sn]=Ce[Qa]=Ce[eo]=Ce[to]=Ce[no]=Ce[ro]=Ce[Et]=Ce[mr]=Ce[en]=Ce[hr]=Ce[Lt]=Ce[gr]=Ce[Yr]=Ce[ao]=Ce[oo]=Ce[io]=Ce[so]=!0,Ce[Zr]=Ce[Gr]=Ce[yr]=!1;var Ld={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",Ç:"C",ç:"c",Ð:"D",ð:"d",È:"E",É:"E",Ê:"E",Ë:"E",è:"e",é:"e",ê:"e",ë:"e",Ì:"I",Í:"I",Î:"I",Ï:"I",ì:"i",í:"i",î:"i",ï:"i",Ñ:"N",ñ:"n",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",Ù:"U",Ú:"U",Û:"U",Ü:"U",ù:"u",ú:"u",û:"u",ü:"u",Ý:"Y",ý:"y",ÿ:"y",Æ:"Ae",æ:"ae",Þ:"Th",þ:"th",ß:"ss",Ā:"A",Ă:"A",Ą:"A",ā:"a",ă:"a",ą:"a",Ć:"C",Ĉ:"C",Ċ:"C",Č:"C",ć:"c",ĉ:"c",ċ:"c",č:"c",Ď:"D",Đ:"D",ď:"d",đ:"d",Ē:"E",Ĕ:"E",Ė:"E",Ę:"E",Ě:"E",ē:"e",ĕ:"e",ė:"e",ę:"e",ě:"e",Ĝ:"G",Ğ:"G",Ġ:"G",Ģ:"G",ĝ:"g",ğ:"g",ġ:"g",ģ:"g",Ĥ:"H",Ħ:"H",ĥ:"h",ħ:"h",Ĩ:"I",Ī:"I",Ĭ:"I",Į:"I",İ:"I",ĩ:"i",ī:"i",ĭ:"i",į:"i",ı:"i",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",ĸ:"k",Ĺ:"L",Ļ:"L",Ľ:"L",Ŀ:"L",Ł:"L",ĺ:"l",ļ:"l",ľ:"l",ŀ:"l",ł:"l",Ń:"N",Ņ:"N",Ň:"N",Ŋ:"N",ń:"n",ņ:"n",ň:"n",ŋ:"n",Ō:"O",Ŏ:"O",Ő:"O",ō:"o",ŏ:"o",ő:"o",Ŕ:"R",Ŗ:"R",Ř:"R",ŕ:"r",ŗ:"r",ř:"r",Ś:"S",Ŝ:"S",Ş:"S",Š:"S",ś:"s",ŝ:"s",ş:"s",š:"s",Ţ:"T",Ť:"T",Ŧ:"T",ţ:"t",ť:"t",ŧ:"t",Ũ:"U",Ū:"U",Ŭ:"U",Ů:"U",Ű:"U",Ų:"U",ũ:"u",ū:"u",ŭ:"u",ů:"u",ű:"u",ų:"u",Ŵ:"W",ŵ:"w",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Ź:"Z",Ż:"Z",Ž:"Z",ź:"z",ż:"z",ž:"z",Ĳ:"IJ",ĳ:"ij",Œ:"Oe",œ:"oe",ŉ:"'n",ſ:"s"},Id={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"},Od={"&amp;":"&","&lt;":"<","&gt;":">","&quot;":'"',"&#39;":"'"},Dd={"\\":"\\","'":"'","\n":"n","\r":"r","\u2028":"u2028","\u2029":"u2029"},qd=parseFloat,Pd=parseInt,os=typeof Er=="object"&&Er&&Er.Object===Object&&Er,Ud=typeof self=="object"&&self&&self.Object===Object&&self,Ye=os||Ud||Function("return this")(),go=s&&!s.nodeType&&s,$n=go&&!0&&a&&!a.nodeType&&a,is=$n&&$n.exports===go,yo=is&&os.process,jt=function(){try{var v=$n&&$n.require&&$n.require("util").types;return v||yo&&yo.binding&&yo.binding("util")}catch{}}(),ss=jt&&jt.isArrayBuffer,us=jt&&jt.isDate,ls=jt&&jt.isMap,cs=jt&&jt.isRegExp,ds=jt&&jt.isSet,fs=jt&&jt.isTypedArray,zd=ae("length"),Bd=ke(Ld),Wd=ke(Id),Vd=ke(Od),Hd=function v(M){function o(e){if(Ie(e)&&!te(e)&&!(e instanceof U)){if(e instanceof Y)return e;if(_e.call(e,"__wrapped__"))return iu(e)}return new Y(e)}function P(){}function Y(e,t){this.__wrapped__=e,this.__actions__=[],this.__chain__=!!t,this.__index__=0,this.__values__=c}function U(e){this.__wrapped__=e,this.__actions__=[],this.__dir__=1,this.__filtered__=!1,this.__iteratees__=[],this.__takeCount__=J,this.__views__=[]}function ct(){var e=new U(this.__wrapped__);return e.__actions__=dt(this.__actions__),e.__dir__=this.__dir__,e.__filtered__=this.__filtered__,e.__iteratees__=dt(this.__iteratees__),e.__takeCount__=this.__takeCount__,e.__views__=dt(this.__views__),e}function bo(){if(this.__filtered__){var e=new U(this);e.__dir__=-1,e.__filtered__=!0}else e=this.clone(),e.__dir__*=-1;return e}function Zd(){var e=this.__wrapped__.value(),t=this.__dir__,n=te(e),r=t<0,i=n?e.length:0,d=rp(0,i,this.__views__),m=d.start,y=d.end,x=y-m,F=r?y:m-1,A=this.__iteratees__,R=A.length,L=0,B=Xe(x,this.__takeCount__);if(!n||!r&&i==x&&B==x)return Ns(e,this.__actions__);var H=[];e:for(;x--&&L<B;){F+=t;for(var ne=-1,Z=e[F];++ne<R;){var de=A[ne],fe=de.iteratee,et=de.type,ht=fe(Z);if(et==Ka)Z=ht;else if(!ht){if(et==Hr)continue e;break e}}H[L++]=Z}return H}function Nn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Gd(){this.__data__=kr?kr(null):{},this.size=0}function Yd(e){var t=this.has(e)&&delete this.__data__[e];return this.size-=t?1:0,t}function Kd(e){var t=this.__data__;if(kr){var n=t[e];return n===Kt?c:n}return _e.call(t,e)?t[e]:c}function Jd(e){var t=this.__data__;return kr?t[e]!==c:_e.call(t,e)}function Xd(e,t){var n=this.__data__;return this.size+=this.has(e)?0:1,n[e]=kr&&t===c?Kt:t,this}function tn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function Qd(){this.__data__=[],this.size=0}function ef(e){var t=this.__data__,n=Qr(t,e);return!(n<0)&&(n==t.length-1?t.pop():Aa.call(t,n,1),--this.size,!0)}function tf(e){var t=this.__data__,n=Qr(t,e);return n<0?c:t[n][1]}function nf(e){return Qr(this.__data__,e)>-1}function rf(e,t){var n=this.__data__,r=Qr(n,e);return r<0?(++this.size,n.push([e,t])):n[r][1]=t,this}function nn(e){var t=-1,n=e==null?0:e.length;for(this.clear();++t<n;){var r=e[t];this.set(r[0],r[1])}}function af(){this.size=0,this.__data__={hash:new Nn,map:new(Fr||tn),string:new Nn}}function of(e){var t=da(this,e).delete(e);return this.size-=t?1:0,t}function sf(e){return da(this,e).get(e)}function uf(e){return da(this,e).has(e)}function lf(e,t){var n=da(this,e),r=n.size;return n.set(e,t),this.size+=n.size==r?0:1,this}function En(e){var t=-1,n=e==null?0:e.length;for(this.__data__=new nn;++t<n;)this.add(e[t])}function cf(e){return this.__data__.set(e,Kt),this}function df(e){return this.__data__.has(e)}function It(e){this.size=(this.__data__=new tn(e)).size}function ff(){this.__data__=new tn,this.size=0}function pf(e){var t=this.__data__,n=t.delete(e);return this.size=t.size,n}function mf(e){return this.__data__.get(e)}function hf(e){return this.__data__.has(e)}function gf(e,t){var n=this.__data__;if(n instanceof tn){var r=n.__data__;if(!Fr||r.length<Se-1)return r.push([e,t]),this.size=++n.size,this;n=this.__data__=new nn(r)}return n.set(e,t),this.size=n.size,this}function ps(e,t){var n=te(e),r=!n&&Pn(e),i=!n&&!r&&_n(e),d=!n&&!r&&!i&&ir(e),m=n||r||i||d,y=m?xe(e.length,fg):[],x=y.length;for(var F in e)!t&&!_e.call(e,F)||m&&(F=="length"||i&&(F=="offset"||F=="parent")||d&&(F=="buffer"||F=="byteLength"||F=="byteOffset")||sn(F,x))||y.push(F);return y}function ms(e){var t=e.length;return t?e[Fo(0,t-1)]:c}function yf(e,t){return fa(dt(e),Ln(t,0,e.length))}function bf(e){return fa(dt(e))}function vo(e,t,n){(n===c||Ot(e[t],n))&&(n!==c||t in e)||rn(e,t,n)}function vr(e,t,n){var r=e[t];_e.call(e,t)&&Ot(r,n)&&(n!==c||t in e)||rn(e,t,n)}function Qr(e,t){for(var n=e.length;n--;)if(Ot(e[n][0],t))return n;return-1}function vf(e,t,n,r){return xn(e,function(i,d,m){t(r,i,n(i),m)}),r}function hs(e,t){return e&&Ht(t,Ve(t),e)}function xf(e,t){return e&&Ht(t,pt(t),e)}function rn(e,t,n){t=="__proto__"&&Fa?Fa(e,t,{configurable:!0,enumerable:!0,value:n,writable:!0}):e[t]=n}function xo(e,t){for(var n=-1,r=t.length,i=Be(r),d=e==null;++n<r;)i[n]=d?c:Zo(e,t[n]);return i}function Ln(e,t,n){return e===e&&(n!==c&&(e=e<=n?e:n),t!==c&&(e=e>=t?e:t)),e}function Mt(e,t,n,r,i,d){var m,y=t&nt,x=t&pr,F=t&Jt;if(n&&(m=i?n(e,r,i,d):n(e)),m!==c)return m;if(!Ee(e))return e;var A=te(e);if(A){if(m=op(e),!y)return dt(e,m)}else{var R=Qe(e),L=R==Gr||R==Li;if(_n(e))return Ls(e,y);if(R==en||R==$e||L&&!i){if(m=x||L?{}:Qs(e),!y)return x?Yf(e,xf(m,e)):Gf(e,hs(m,e))}else{if(!Ce[R])return i?e:{};m=ip(e,R,y)}}d||(d=new It);var B=d.get(e);if(B)return B;d.set(e,m),Ku(e)?e.forEach(function(Z){m.add(Mt(Z,t,n,Z,e,d))}):Yu(e)&&e.forEach(function(Z,de){m.set(de,Mt(Z,t,n,de,e,d))});var H=F?x?qo:Do:x?pt:Ve,ne=A?c:H(e);return f(ne||e,function(Z,de){ne&&(de=Z,Z=e[de]),vr(m,de,Mt(Z,t,n,de,e,d))}),m}function _f(e){var t=Ve(e);return function(n){return gs(n,e,t)}}function gs(e,t,n){var r=n.length;if(e==null)return!r;for(e=Ae(e);r--;){var i=n[r],d=t[i],m=e[i];if(m===c&&!(i in e)||!d(m))return!1}return!0}function ys(e,t,n){if(typeof e!="function")throw new kt(je);return $r(function(){e.apply(c,n)},t)}function xr(e,t,n,r){var i=-1,d=_,m=!0,y=e.length,x=[],F=t.length;if(!y)return x;n&&(t=C(t,E(n))),r?(d=j,m=!1):t.length>=Se&&(d=q,m=!1,t=new En(t));e:for(;++i<y;){var A=e[i],R=n==null?A:n(A);if(A=r||A!==0?A:0,m&&R===R){for(var L=F;L--;)if(t[L]===R)continue e;x.push(A)}else d(t,R,r)||x.push(A)}return x}function Cf(e,t){var n=!0;return xn(e,function(r,i,d){return n=!!t(r,i,d)}),n}function ea(e,t,n){for(var r=-1,i=e.length;++r<i;){var d=e[r],m=t(d);if(m!=null&&(y===c?m===m&&!xt(m):n(m,y)))var y=m,x=d}return x}function Tf(e,t,n,r){var i=e.length;for(n=oe(n),n<0&&(n=-n>i?0:i+n),r=r===c||r>i?i:oe(r),r<0&&(r+=i),r=n>r?0:wu(r);n<r;)e[n++]=t;return e}function bs(e,t){var n=[];return xn(e,function(r,i,d){t(r,i,d)&&n.push(r)}),n}function Ke(e,t,n,r,i){var d=-1,m=e.length;for(n||(n=up),i||(i=[]);++d<m;){var y=e[d];t>0&&n(y)?t>1?Ke(y,t-1,n,r,i):w(i,y):r||(i[i.length]=y)}return i}function Vt(e,t){return e&&ii(e,t,Ve)}function _o(e,t){return e&&Uu(e,t,Ve)}function ta(e,t){return h(t,function(n){return un(e[n])})}function In(e,t){t=yn(t,e);for(var n=0,r=t.length;e!=null&&n<r;)e=e[Zt(t[n++])];return n&&n==r?e:c}function vs(e,t,n){var r=t(e);return te(e)?r:w(r,n(e))}function ot(e){return e==null?e===c?Dc:Ic:qn&&qn in Ae(e)?np(e):mp(e)}function Co(e,t){return e>t}function wf(e,t){return e!=null&&_e.call(e,t)}function jf(e,t){return e!=null&&t in Ae(e)}function Mf(e,t,n){return e>=Xe(t,n)&&e<We(t,n)}function To(e,t,n){for(var r=n?j:_,i=e[0].length,d=e.length,m=d,y=Be(d),x=1/0,F=[];m--;){var A=e[m];m&&t&&(A=C(A,E(t))),x=Xe(A.length,x),y[m]=!n&&(t||i>=120&&A.length>=120)?new En(m&&A):c}A=e[0];var R=-1,L=y[0];e:for(;++R<i&&F.length<x;){var B=A[R],H=t?t(B):B;if(B=n||B!==0?B:0,!(L?q(L,H):r(F,H,n))){for(m=d;--m;){var ne=y[m];if(!(ne?q(ne,H):r(e[m],H,n)))continue e}L&&L.push(H),F.push(B)}}return F}function Af(e,t,n,r){return Vt(e,function(i,d,m){t(r,n(i),d,m)}),r}function _r(e,t,n){t=yn(t,e),e=ru(e,t);var r=e==null?e:e[Zt(Ft(t))];return r==null?c:u(r,e,n)}function xs(e){return Ie(e)&&ot(e)==$e}function Ff(e){return Ie(e)&&ot(e)==br}function Rf(e){return Ie(e)&&ot(e)==Sn}function Cr(e,t,n,r,i){return e===t||(e==null||t==null||!Ie(e)&&!Ie(t)?e!==e&&t!==t:kf(e,t,n,r,Cr,i))}function kf(e,t,n,r,i,d){var m=te(e),y=te(t),x=m?Ze:Qe(e),F=y?Ze:Qe(t);x=x==$e?en:x,F=F==$e?en:F;var A=x==en,R=F==en,L=x==F;if(L&&_n(e)){if(!_n(t))return!1;m=!0,A=!1}if(L&&!A)return d||(d=new It),m||ir(e)?Js(e,t,n,r,i,d):ep(e,t,x,n,r,i,d);if(!(n&zt)){var B=A&&_e.call(e,"__wrapped__"),H=R&&_e.call(t,"__wrapped__");if(B||H){var ne=B?e.value():e,Z=H?t.value():t;return d||(d=new It),i(ne,Z,n,r,d)}}return!!L&&(d||(d=new It),tp(e,t,n,r,i,d))}function Sf(e){return Ie(e)&&Qe(e)==Et}function wo(e,t,n,r){var i=n.length,d=i,m=!r;if(e==null)return!d;for(e=Ae(e);i--;){var y=n[i];if(m&&y[2]?y[1]!==e[y[0]]:!(y[0]in e))return!1}for(;++i<d;){y=n[i];var x=y[0],F=e[x],A=y[1];if(m&&y[2]){if(F===c&&!(x in e))return!1}else{var R=new It;if(r)var L=r(F,A,x,e,t,R);if(!(L===c?Cr(A,F,zt|mn,r,R):L))return!1}}return!0}function _s(e){return!(!Ee(e)||cp(e))&&(un(e)?yg:sd).test(Dn(e))}function $f(e){return Ie(e)&&ot(e)==hr}function Nf(e){return Ie(e)&&Qe(e)==Lt}function Ef(e){return Ie(e)&&ya(e.length)&&!!Me[ot(e)]}function Cs(e){return typeof e=="function"?e:e==null?mt:typeof e=="object"?te(e)?js(e[0],e[1]):ws(e):ku(e)}function jo(e){if(!jr(e))return Tg(e);var t=[];for(var n in Ae(e))_e.call(e,n)&&n!="constructor"&&t.push(n);return t}function Lf(e){if(!Ee(e))return pp(e);var t=jr(e),n=[];for(var r in e)(r!="constructor"||!t&&_e.call(e,r))&&n.push(r);return n}function Mo(e,t){return e<t}function Ts(e,t){var n=-1,r=ft(e)?Be(e.length):[];return xn(e,function(i,d,m){r[++n]=t(i,d,m)}),r}function ws(e){var t=Po(e);return t.length==1&&t[0][2]?tu(t[0][0],t[0][1]):function(n){return n===e||wo(n,e,t)}}function js(e,t){return Uo(e)&&eu(t)?tu(Zt(e),t):function(n){var r=Zo(n,e);return r===c&&r===t?Go(n,e):Cr(t,r,zt|mn)}}function na(e,t,n,r,i){e!==t&&ii(t,function(d,m){if(i||(i=new It),Ee(d))If(e,t,m,n,na,r,i);else{var y=r?r(Bo(e,m),d,m+"",e,t,i):c;y===c&&(y=d),vo(e,m,y)}},pt)}function If(e,t,n,r,i,d,m){var y=Bo(e,n),x=Bo(t,n),F=m.get(x);if(F)return vo(e,n,F),c;var A=d?d(y,x,n+"",e,t,m):c,R=A===c;if(R){var L=te(x),B=!L&&_n(x),H=!L&&!B&&ir(x);A=x,L||B||H?te(y)?A=y:De(y)?A=dt(y):B?(R=!1,A=Ls(x,!0)):H?(R=!1,A=Is(x,!0)):A=[]:Mr(x)||Pn(x)?(A=y,Pn(y)?A=ju(y):Ee(y)&&!un(y)||(A=Qs(x))):R=!1}R&&(m.set(x,A),i(A,x,r,d,m),m.delete(x)),vo(e,n,A)}function Ms(e,t){var n=e.length;if(n)return t+=t<0?n:0,sn(t,n)?e[t]:c}function As(e,t,n){t=t.length?C(t,function(i){return te(i)?function(d){return In(d,i.length===1?i[0]:i)}:i}):[mt];var r=-1;return t=C(t,E(K())),tt(Ts(e,function(i,d,m){return{criteria:C(t,function(y){return y(i)}),index:++r,value:i}}),function(i,d){return Zf(i,d,n)})}function Of(e,t){return Fs(e,t,function(n,r){return Go(e,r)})}function Fs(e,t,n){for(var r=-1,i=t.length,d={};++r<i;){var m=t[r],y=In(e,m);n(y,m)&&Tr(d,yn(m,e),y)}return d}function Df(e){return function(t){return In(t,e)}}function Ao(e,t,n,r){var i=r?le:S,d=-1,m=t.length,y=e;for(e===t&&(t=dt(t)),n&&(y=C(e,E(n)));++d<m;)for(var x=0,F=t[d],A=n?n(F):F;(x=i(y,A,x,r))>-1;)y!==e&&Aa.call(y,x,1),Aa.call(e,x,1);return e}function Rs(e,t){for(var n=e?t.length:0,r=n-1;n--;){var i=t[n];if(n==r||i!==d){var d=i;sn(i)?Aa.call(e,i,1):So(e,i)}}return e}function Fo(e,t){return e+ka(Du()*(t-e+1))}function qf(e,t,n,r){for(var i=-1,d=We(Ra((t-e)/(n||1)),0),m=Be(d);d--;)m[r?d:++i]=e,e+=n;return m}function Ro(e,t){var n="";if(!e||t<1||t>Wt)return n;do t%2&&(n+=e),t=ka(t/2),t&&(e+=e);while(t);return n}function ce(e,t){return li(nu(e,t,mt),e+"")}function Pf(e){return ms(er(e))}function Uf(e,t){var n=er(e);return fa(n,Ln(t,0,n.length))}function Tr(e,t,n,r){if(!Ee(e))return e;t=yn(t,e);for(var i=-1,d=t.length,m=d-1,y=e;y!=null&&++i<d;){var x=Zt(t[i]),F=n;if(x==="__proto__"||x==="constructor"||x==="prototype")return e;if(i!=m){var A=y[x];F=r?r(A,x,y):c,F===c&&(F=Ee(A)?A:sn(t[i+1])?[]:{})}vr(y,x,F),y=y[x]}return e}function zf(e){return fa(er(e))}function At(e,t,n){var r=-1,i=e.length;t<0&&(t=-t>i?0:i+t),n=n>i?i:n,n<0&&(n+=i),i=t>n?0:n-t>>>0,t>>>=0;for(var d=Be(i);++r<i;)d[r]=e[r+t];return d}function Bf(e,t){var n;return xn(e,function(r,i,d){return n=t(r,i,d),!n}),!!n}function ra(e,t,n){var r=0,i=e==null?r:e.length;if(typeof t=="number"&&t===t&&i<=bt){for(;r<i;){var d=r+i>>>1,m=e[d];m!==null&&!xt(m)&&(n?m<=t:m<t)?r=d+1:i=d}return i}return ko(e,t,mt,n)}function ko(e,t,n,r){var i=0,d=e==null?0:e.length;if(d===0)return 0;t=n(t);for(var m=t!==t,y=t===null,x=xt(t),F=t===c;i<d;){var A=ka((i+d)/2),R=n(e[A]),L=R!==c,B=R===null,H=R===R,ne=xt(R);if(m)var Z=r||H;else Z=F?H&&(r||L):y?H&&L&&(r||!B):x?H&&L&&!B&&(r||!ne):!B&&!ne&&(r?R<=t:R<t);Z?i=A+1:d=A}return Xe(d,at)}function ks(e,t){for(var n=-1,r=e.length,i=0,d=[];++n<r;){var m=e[n],y=t?t(m):m;if(!n||!Ot(y,x)){var x=y;d[i++]=m===0?0:m}}return d}function Ss(e){return typeof e=="number"?e:xt(e)?Zn:+e}function vt(e){if(typeof e=="string")return e;if(te(e))return C(e,vt)+"";if(xt(e))return qu?qu.call(e):"";var t=e+"";return t=="0"&&1/e==-Qt?"-0":t}function gn(e,t,n){var r=-1,i=_,d=e.length,m=!0,y=[],x=y;if(n)m=!1,i=j;else if(d>=Se){var F=t?null:Eg(e);if(F)return Pe(F);m=!1,i=q,x=new En}else x=t?[]:y;e:for(;++r<d;){var A=e[r],R=t?t(A):A;if(A=n||A!==0?A:0,m&&R===R){for(var L=x.length;L--;)if(x[L]===R)continue e;t&&x.push(R),y.push(A)}else i(x,R,n)||(x!==y&&x.push(R),y.push(A))}return y}function So(e,t){return t=yn(t,e),e=ru(e,t),e==null||delete e[Zt(Ft(t))]}function $s(e,t,n,r){return Tr(e,t,n(In(e,t)),r)}function aa(e,t,n,r){for(var i=e.length,d=r?i:-1;(r?d--:++d<i)&&t(e[d],d,e););return n?At(e,r?0:d,r?d+1:i):At(e,r?d+1:0,r?i:d)}function Ns(e,t){var n=e;return n instanceof U&&(n=n.value()),O(t,function(r,i){return i.func.apply(i.thisArg,w([r],i.args))},n)}function $o(e,t,n){var r=e.length;if(r<2)return r?gn(e[0]):[];for(var i=-1,d=Be(r);++i<r;)for(var m=e[i],y=-1;++y<r;)y!=i&&(d[i]=xr(d[i]||m,e[y],t,n));return gn(Ke(d,1),t,n)}function Es(e,t,n){for(var r=-1,i=e.length,d=t.length,m={};++r<i;)n(m,e[r],r<d?t[r]:c);return m}function No(e){return De(e)?e:[]}function Eo(e){return typeof e=="function"?e:mt}function yn(e,t){return te(e)?e:Uo(e,t)?[e]:Hu(be(e))}function bn(e,t,n){var r=e.length;return n=n===c?r:n,!t&&n>=r?e:At(e,t,n)}function Ls(e,t){if(t)return e.slice();var n=e.length,r=Nu?Nu(n):new e.constructor(n);return e.copy(r),r}function Lo(e){var t=new e.constructor(e.byteLength);return new ja(t).set(new ja(e)),t}function Wf(e,t){return new e.constructor(t?Lo(e.buffer):e.buffer,e.byteOffset,e.byteLength)}function Vf(e){var t=new e.constructor(e.source,Pi.exec(e));return t.lastIndex=e.lastIndex,t}function Hf(e){return Sr?Ae(Sr.call(e)):{}}function Is(e,t){return new e.constructor(t?Lo(e.buffer):e.buffer,e.byteOffset,e.length)}function Os(e,t){if(e!==t){var n=e!==c,r=e===null,i=e===e,d=xt(e),m=t!==c,y=t===null,x=t===t,F=xt(t);if(!y&&!F&&!d&&e>t||d&&m&&x&&!y&&!F||r&&m&&x||!n&&x||!i)return 1;if(!r&&!d&&!F&&e<t||F&&n&&i&&!r&&!d||y&&n&&i||!m&&i||!x)return-1}return 0}function Zf(e,t,n){for(var r=-1,i=e.criteria,d=t.criteria,m=i.length,y=n.length;++r<m;){var x=Os(i[r],d[r]);if(x)return r>=y?x:x*(n[r]=="desc"?-1:1)}return e.index-t.index}function Ds(e,t,n,r){for(var i=-1,d=e.length,m=n.length,y=-1,x=t.length,F=We(d-m,0),A=Be(x+F),R=!r;++y<x;)A[y]=t[y];for(;++i<m;)(R||i<d)&&(A[n[i]]=e[i]);for(;F--;)A[y++]=e[i++];return A}function qs(e,t,n,r){for(var i=-1,d=e.length,m=-1,y=n.length,x=-1,F=t.length,A=We(d-y,0),R=Be(A+F),L=!r;++i<A;)R[i]=e[i];for(var B=i;++x<F;)R[B+x]=t[x];for(;++m<y;)(L||i<d)&&(R[B+n[m]]=e[i++]);return R}function dt(e,t){var n=-1,r=e.length;for(t||(t=Be(r));++n<r;)t[n]=e[n];return t}function Ht(e,t,n,r){var i=!n;n||(n={});for(var d=-1,m=t.length;++d<m;){var y=t[d],x=r?r(n[y],e[y],y,n,e):c;x===c&&(x=e[y]),i?rn(n,y,x):vr(n,y,x)}return n}function Gf(e,t){return Ht(e,ui(e),t)}function Yf(e,t){return Ht(e,Wu(e),t)}function oa(e,t){return function(n,r){var i=te(n)?p:vf,d=t?t():{};return i(n,e,K(r,2),d)}}function Jn(e){return ce(function(t,n){var r=-1,i=n.length,d=i>1?n[i-1]:c,m=i>2?n[2]:c;for(d=e.length>3&&typeof d=="function"?(i--,d):c,m&&it(n[0],n[1],m)&&(d=i<3?c:d,i=1),t=Ae(t);++r<i;){var y=n[r];y&&e(t,y,r,d)}return t})}function Ps(e,t){return function(n,r){if(n==null)return n;if(!ft(n))return e(n,r);for(var i=n.length,d=t?i:-1,m=Ae(n);(t?d--:++d<i)&&r(m[d],d,m)!==!1;);return n}}function Us(e){return function(t,n,r){for(var i=-1,d=Ae(t),m=r(t),y=m.length;y--;){var x=m[e?y:++i];if(n(d[x],x,d)===!1)break}return t}}function Kf(e,t,n){function r(){return(this&&this!==Ye&&this instanceof r?d:e).apply(i?n:this,arguments)}var i=t&lt,d=wr(e);return r}function zs(e){return function(t){t=be(t);var n=gt(t)?Ue(t):c,r=n?n[0]:t.charAt(0),i=n?bn(n,1).join(""):t.slice(1);return r[e]()+i}}function Xn(e){return function(t){return O(Ru(Fu(t).replace(Fd,"")),e,"")}}function wr(e){return function(){var t=arguments;switch(t.length){case 0:return new e;case 1:return new e(t[0]);case 2:return new e(t[0],t[1]);case 3:return new e(t[0],t[1],t[2]);case 4:return new e(t[0],t[1],t[2],t[3]);case 5:return new e(t[0],t[1],t[2],t[3],t[4]);case 6:return new e(t[0],t[1],t[2],t[3],t[4],t[5]);case 7:return new e(t[0],t[1],t[2],t[3],t[4],t[5],t[6])}var n=or(e.prototype),r=e.apply(n,t);return Ee(r)?r:n}}function Jf(e,t,n){function r(){for(var d=arguments.length,m=Be(d),y=d,x=Qn(r);y--;)m[y]=arguments[y];var F=d<3&&m[0]!==x&&m[d-1]!==x?[]:_t(m,x);return d-=F.length,d<n?Zs(e,t,ia,r.placeholder,c,m,F,c,c,n-d):u(this&&this!==Ye&&this instanceof r?i:e,this,m)}var i=wr(e);return r}function Bs(e){return function(t,n,r){var i=Ae(t);if(!ft(t)){var d=K(n,3);t=Ve(t),n=function(y){return d(i[y],y,i)}}var m=e(t,n,r);return m>-1?i[d?t[m]:m]:c}}function Ws(e){return on(function(t){var n=t.length,r=n,i=Y.prototype.thru;for(e&&t.reverse();r--;){var d=t[r];if(typeof d!="function")throw new kt(je);if(i&&!m&&ca(d)=="wrapper")var m=new Y([],!0)}for(r=m?r:n;++r<n;){d=t[r];var y=ca(d),x=y=="wrapper"?si(d):c;m=x&&zo(x[0])&&x[1]==(Nt|Tt|wt|Rn)&&!x[4].length&&x[9]==1?m[ca(x[0])].apply(m,x[3]):d.length==1&&zo(d)?m[y]():m.thru(d)}return function(){var F=arguments,A=F[0];if(m&&F.length==1&&te(A))return m.plant(A).value();for(var R=0,L=n?t[R].apply(this,F):A;++R<n;)L=t[R].call(this,L);return L}})}function ia(e,t,n,r,i,d,m,y,x,F){function A(){for(var de=arguments.length,fe=Be(de),et=de;et--;)fe[et]=arguments[et];if(H)var ht=Qn(A),Cn=lr(fe,ht);if(r&&(fe=Ds(fe,r,i,H)),d&&(fe=qs(fe,d,m,H)),de-=Cn,H&&de<F)return Zs(e,t,ia,A.placeholder,n,fe,_t(fe,ht),y,x,F-de);var Oe=L?n:this,St=B?Oe[e]:e;return de=fe.length,y?fe=hp(fe,y):ne&&de>1&&fe.reverse(),R&&x<de&&(fe.length=x),this&&this!==Ye&&this instanceof A&&(St=Z||wr(St)),St.apply(Oe,fe)}var R=t&Nt,L=t&lt,B=t&Bt,H=t&(Tt|Xt),ne=t&Hn,Z=B?c:wr(e);return A}function Vs(e,t){return function(n,r){return Af(n,e,t(r),{})}}function sa(e,t){return function(n,r){var i;if(n===c&&r===c)return t;if(n!==c&&(i=n),r!==c){if(i===c)return r;typeof n=="string"||typeof r=="string"?(n=vt(n),r=vt(r)):(n=Ss(n),r=Ss(r)),i=e(n,r)}return i}}function Io(e){return on(function(t){return t=C(t,E(K())),ce(function(n){var r=this;return e(t,function(i){return u(i,r,n)})})})}function ua(e,t){t=t===c?" ":vt(t);var n=t.length;if(n<2)return n?Ro(t,e):t;var r=Ro(t,Ra(e/Ut(t)));return gt(t)?bn(Ue(r),0,e).join(""):r.slice(0,e)}function Xf(e,t,n,r){function i(){for(var y=-1,x=arguments.length,F=-1,A=r.length,R=Be(A+x),L=this&&this!==Ye&&this instanceof i?m:e;++F<A;)R[F]=r[F];for(;x--;)R[F++]=arguments[++y];return u(L,d?n:this,R)}var d=t&lt,m=wr(e);return i}function Hs(e){return function(t,n,r){return r&&typeof r!="number"&&it(t,n,r)&&(n=r=c),t=ln(t),n===c?(n=t,t=0):n=ln(n),r=r===c?t<n?1:-1:ln(r),qf(t,n,r,e)}}function la(e){return function(t,n){return typeof t=="string"&&typeof n=="string"||(t=Rt(t),n=Rt(n)),e(t,n)}}function Zs(e,t,n,r,i,d,m,y,x,F){var A=t&Tt,R=A?m:c,L=A?c:m,B=A?d:c,H=A?c:d;t|=A?wt:hn,t&=~(A?hn:wt),t&rt||(t&=~(lt|Bt));var ne=[e,t,i,B,R,H,L,y,x,F],Z=n.apply(c,ne);return zo(e)&&Vu(Z,ne),Z.placeholder=r,au(Z,e,t)}function Oo(e){var t=tr[e];return function(n,r){if(n=Rt(n),r=r==null?0:Xe(oe(r),292),r&&Ou(n)){var i=(be(n)+"e").split("e");return i=(be(t(i[0]+"e"+(+i[1]+r)))+"e").split("e"),+(i[0]+"e"+(+i[1]-r))}return t(n)}}function Gs(e){return function(t){var n=Qe(t);return n==Et?An(t):n==Lt?dr(t):Le(t,e(t))}}function an(e,t,n,r,i,d,m,y){var x=t&Bt;if(!x&&typeof e!="function")throw new kt(je);var F=r?r.length:0;if(F||(t&=~(wt|hn),r=i=c),m=m===c?m:We(oe(m),0),y=y===c?y:oe(y),F-=i?i.length:0,t&hn){var A=r,R=i;r=i=c}var L=x?c:si(e),B=[e,t,n,r,i,A,R,d,m,y];if(L&&fp(B,L),e=B[0],t=B[1],n=B[2],r=B[3],i=B[4],y=B[9]=B[9]===c?x?0:e.length:We(B[9]-F,0),!y&&t&(Tt|Xt)&&(t&=~(Tt|Xt)),t&&t!=lt)H=t==Tt||t==Xt?Jf(e,t,y):t!=wt&&t!=(lt|wt)||i.length?ia.apply(c,B):Xf(e,t,n,r);else var H=Kf(e,t,n);return au((L?zu:Vu)(H,B),e,t)}function Ys(e,t,n,r){return e===c||Ot(e,nr[n])&&!_e.call(r,n)?t:e}function Ks(e,t,n,r,i,d){return Ee(e)&&Ee(t)&&(d.set(t,e),na(e,t,c,Ks,d),d.delete(t)),e}function Qf(e){return Mr(e)?c:e}function Js(e,t,n,r,i,d){var m=n&zt,y=e.length,x=t.length;if(y!=x&&!(m&&x>y))return!1;var F=d.get(e),A=d.get(t);if(F&&A)return F==t&&A==e;var R=-1,L=!0,B=n&mn?new En:c;for(d.set(e,t),d.set(t,e);++R<y;){var H=e[R],ne=t[R];if(r)var Z=m?r(ne,H,R,t,e,d):r(H,ne,R,e,t,d);if(Z!==c){if(Z)continue;L=!1;break}if(B){if(!$(t,function(de,fe){if(!q(B,fe)&&(H===de||i(H,de,n,r,d)))return B.push(fe)})){L=!1;break}}else if(H!==ne&&!i(H,ne,n,r,d)){L=!1;break}}return d.delete(e),d.delete(t),L}function ep(e,t,n,r,i,d,m){switch(n){case Gn:if(e.byteLength!=t.byteLength||e.byteOffset!=t.byteOffset)return!1;e=e.buffer,t=t.buffer;case br:return!(e.byteLength!=t.byteLength||!d(new ja(e),new ja(t)));case kn:case Sn:case mr:return Ot(+e,+t);case Zr:return e.name==t.name&&e.message==t.message;case hr:case gr:return e==t+"";case Et:var y=An;case Lt:var x=r&zt;if(y||(y=Pe),e.size!=t.size&&!x)return!1;var F=m.get(e);if(F)return F==t;r|=mn,m.set(e,t);var A=Js(y(e),y(t),r,i,d,m);return m.delete(e),A;case Yr:if(Sr)return Sr.call(e)==Sr.call(t)}return!1}function tp(e,t,n,r,i,d){var m=n&zt,y=Do(e),x=y.length;if(x!=Do(t).length&&!m)return!1;for(var F=x;F--;){var A=y[F];if(!(m?A in t:_e.call(t,A)))return!1}var R=d.get(e),L=d.get(t);if(R&&L)return R==t&&L==e;var B=!0;d.set(e,t),d.set(t,e);for(var H=m;++F<x;){A=y[F];var ne=e[A],Z=t[A];if(r)var de=m?r(Z,ne,A,t,e,d):r(ne,Z,A,e,t,d);if(!(de===c?ne===Z||i(ne,Z,n,r,d):de)){B=!1;break}H||(H=A=="constructor")}if(B&&!H){var fe=e.constructor,et=t.constructor;fe!=et&&"constructor"in e&&"constructor"in t&&!(typeof fe=="function"&&fe instanceof fe&&typeof et=="function"&&et instanceof et)&&(B=!1)}return d.delete(e),d.delete(t),B}function on(e){return li(nu(e,c,lu),e+"")}function Do(e){return vs(e,Ve,ui)}function qo(e){return vs(e,pt,Wu)}function ca(e){for(var t=e.name+"",n=ar[t],r=_e.call(ar,t)?n.length:0;r--;){var i=n[r],d=i.func;if(d==null||d==e)return i.name}return t}function Qn(e){return(_e.call(o,"placeholder")?o:e).placeholder}function K(){var e=o.iteratee||Ko;return e=e===Ko?Cs:e,arguments.length?e(arguments[0],arguments[1]):e}function da(e,t){var n=e.__data__;return lp(t)?n[typeof t=="string"?"string":"hash"]:n.map}function Po(e){for(var t=Ve(e),n=t.length;n--;){var r=t[n],i=e[r];t[n]=[r,i,eu(i)]}return t}function On(e,t){var n=Pt(e,t);return _s(n)?n:c}function np(e){var t=_e.call(e,qn),n=e[qn];try{e[qn]=c;var r=!0}catch{}var i=Ta.call(e);return r&&(t?e[qn]=n:delete e[qn]),i}function rp(e,t,n){for(var r=-1,i=n.length;++r<i;){var d=n[r],m=d.size;switch(d.type){case"drop":e+=m;break;case"dropRight":t-=m;break;case"take":t=Xe(t,e+m);break;case"takeRight":e=We(e,t-m)}}return{start:e,end:t}}function ap(e){var t=e.match(Qc);return t?t[1].split(ed):[]}function Xs(e,t,n){t=yn(t,e);for(var r=-1,i=t.length,d=!1;++r<i;){var m=Zt(t[r]);if(!(d=e!=null&&n(e,m)))break;e=e[m]}return d||++r!=i?d:(i=e==null?0:e.length,!!i&&ya(i)&&sn(m,i)&&(te(e)||Pn(e)))}function op(e){var t=e.length,n=new e.constructor(t);return t&&typeof e[0]=="string"&&_e.call(e,"index")&&(n.index=e.index,n.input=e.input),n}function Qs(e){return typeof e.constructor!="function"||jr(e)?{}:or(Ma(e))}function ip(e,t,n){var r=e.constructor;switch(t){case br:return Lo(e);case kn:case Sn:return new r(+e);case Gn:return Wf(e,n);case Qa:case eo:case to:case no:case ro:case ao:case oo:case io:case so:return Is(e,n);case Et:return new r;case mr:case gr:return new r(e);case hr:return Vf(e);case Lt:return new r;case Yr:return Hf(e)}}function sp(e,t){var n=t.length;if(!n)return e;var r=n-1;return t[r]=(n>1?"& ":"")+t[r],t=t.join(n>2?", ":" "),e.replace(Xc,`{
/* [wrapped with `+t+`] */
`)}function up(e){return te(e)||Pn(e)||!!(Iu&&e&&e[Iu])}function sn(e,t){var n=typeof e;return t=t??Wt,!!t&&(n=="number"||n!="symbol"&&ld.test(e))&&e>-1&&e%1==0&&e<t}function it(e,t,n){if(!Ee(n))return!1;var r=typeof t;return!!(r=="number"?ft(n)&&sn(t,n.length):r=="string"&&t in n)&&Ot(n[t],e)}function Uo(e,t){if(te(e))return!1;var n=typeof e;return!(n!="number"&&n!="symbol"&&n!="boolean"&&e!=null&&!xt(e))||Gc.test(e)||!Zc.test(e)||t!=null&&e in Ae(t)}function lp(e){var t=typeof e;return t=="string"||t=="number"||t=="symbol"||t=="boolean"?e!=="__proto__":e===null}function zo(e){var t=ca(e),n=o[t];if(typeof n!="function"||!(t in U.prototype))return!1;if(e===n)return!0;var r=si(n);return!!r&&e===r[0]}function cp(e){return!!$u&&$u in e}function jr(e){var t=e&&e.constructor;return e===(typeof t=="function"&&t.prototype||nr)}function eu(e){return e===e&&!Ee(e)}function tu(e,t){return function(n){return n!=null&&n[e]===t&&(t!==c||e in Ae(n))}}function dp(e){var t=ha(e,function(r){return n.size===Fn&&n.clear(),r}),n=t.cache;return t}function fp(e,t){var n=e[1],r=t[1],i=n|r,d=i<(lt|Bt|Nt),m=r==Nt&&n==Tt||r==Nt&&n==Rn&&e[7].length<=t[8]||r==(Nt|Rn)&&t[7].length<=t[8]&&n==Tt;if(!d&&!m)return e;r&lt&&(e[2]=t[2],i|=n&lt?0:rt);var y=t[3];if(y){var x=e[3];e[3]=x?Ds(x,y,t[4]):y,e[4]=x?_t(e[3],yt):t[4]}return y=t[5],y&&(x=e[5],e[5]=x?qs(x,y,t[6]):y,e[6]=x?_t(e[5],yt):t[6]),y=t[7],y&&(e[7]=y),r&Nt&&(e[8]=e[8]==null?t[8]:Xe(e[8],t[8])),e[9]==null&&(e[9]=t[9]),e[0]=t[0],e[1]=i,e}function pp(e){var t=[];if(e!=null)for(var n in Ae(e))t.push(n);return t}function mp(e){return Ta.call(e)}function nu(e,t,n){return t=We(t===c?e.length-1:t,0),function(){for(var r=arguments,i=-1,d=We(r.length-t,0),m=Be(d);++i<d;)m[i]=r[t+i];i=-1;for(var y=Be(t+1);++i<t;)y[i]=r[i];return y[t]=n(m),u(e,this,y)}}function ru(e,t){return t.length<2?e:In(e,At(t,0,-1))}function hp(e,t){for(var n=e.length,r=Xe(t.length,n),i=dt(e);r--;){var d=t[r];e[r]=sn(d,n)?i[d]:c}return e}function Bo(e,t){if((t!=="constructor"||typeof e[t]!="function")&&t!="__proto__")return e[t]}function au(e,t,n){var r=t+"";return li(e,sp(r,gp(ap(r),n)))}function ou(e){var t=0,n=0;return function(){var r=wg(),i=Vr-(r-n);if(n=r,i>0){if(++t>=Wr)return arguments[0]}else t=0;return e.apply(c,arguments)}}function fa(e,t){var n=-1,r=e.length,i=r-1;for(t=t===c?r:t;++n<t;){var d=Fo(n,i),m=e[d];e[d]=e[n],e[n]=m}return e.length=t,e}function Zt(e){if(typeof e=="string"||xt(e))return e;var t=e+"";return t=="0"&&1/e==-Qt?"-0":t}function Dn(e){if(e!=null){try{return Ca.call(e)}catch{}try{return e+""}catch{}}return""}function gp(e,t){return f(ye,function(n){var r="_."+n[0];t&n[1]&&!_(e,r)&&e.push(r)}),e.sort()}function iu(e){if(e instanceof U)return e.clone();var t=new Y(e.__wrapped__,e.__chain__);return t.__actions__=dt(e.__actions__),t.__index__=e.__index__,t.__values__=e.__values__,t}function yp(e,t,n){t=(n?it(e,t,n):t===c)?1:We(oe(t),0);var r=e==null?0:e.length;if(!r||t<1)return[];for(var i=0,d=0,m=Be(Ra(r/t));i<r;)m[d++]=At(e,i,i+=t);return m}function bp(e){for(var t=-1,n=e==null?0:e.length,r=0,i=[];++t<n;){var d=e[t];d&&(i[r++]=d)}return i}function vp(){var e=arguments.length;if(!e)return[];for(var t=Be(e-1),n=arguments[0],r=e;r--;)t[r-1]=arguments[r];return w(te(n)?dt(n):[n],Ke(t,1))}function xp(e,t,n){var r=e==null?0:e.length;return r?(t=n||t===c?1:oe(t),At(e,t<0?0:t,r)):[]}function _p(e,t,n){var r=e==null?0:e.length;return r?(t=n||t===c?1:oe(t),t=r-t,At(e,0,t<0?0:t)):[]}function Cp(e,t){return e&&e.length?aa(e,K(t,3),!0,!0):[]}function Tp(e,t){return e&&e.length?aa(e,K(t,3),!0):[]}function wp(e,t,n,r){var i=e==null?0:e.length;return i?(n&&typeof n!="number"&&it(e,t,n)&&(n=0,r=i),Tf(e,t,n,r)):[]}function su(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var i=n==null?0:oe(n);return i<0&&(i=We(r+i,0)),N(e,K(t,3),i)}function uu(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var i=r-1;return n!==c&&(i=oe(n),i=n<0?We(r+i,0):Xe(i,r-1)),N(e,K(t,3),i,!0)}function lu(e){return e!=null&&e.length?Ke(e,1):[]}function jp(e){return e!=null&&e.length?Ke(e,Qt):[]}function Mp(e,t){return e!=null&&e.length?(t=t===c?1:oe(t),Ke(e,t)):[]}function Ap(e){for(var t=-1,n=e==null?0:e.length,r={};++t<n;){var i=e[t];r[i[0]]=i[1]}return r}function cu(e){return e&&e.length?e[0]:c}function Fp(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var i=n==null?0:oe(n);return i<0&&(i=We(r+i,0)),S(e,t,i)}function Rp(e){return e!=null&&e.length?At(e,0,-1):[]}function kp(e,t){return e==null?"":Cg.call(e,t)}function Ft(e){var t=e==null?0:e.length;return t?e[t-1]:c}function Sp(e,t,n){var r=e==null?0:e.length;if(!r)return-1;var i=r;return n!==c&&(i=oe(n),i=i<0?We(r+i,0):Xe(i,r-1)),t===t?Vn(e,t,i):N(e,ie,i,!0)}function $p(e,t){return e&&e.length?Ms(e,oe(t)):c}function du(e,t){return e&&e.length&&t&&t.length?Ao(e,t):e}function Np(e,t,n){return e&&e.length&&t&&t.length?Ao(e,t,K(n,2)):e}function Ep(e,t,n){return e&&e.length&&t&&t.length?Ao(e,t,c,n):e}function Lp(e,t){var n=[];if(!e||!e.length)return n;var r=-1,i=[],d=e.length;for(t=K(t,3);++r<d;){var m=e[r];t(m,r,e)&&(n.push(m),i.push(r))}return Rs(e,i),n}function Wo(e){return e==null?e:Mg.call(e)}function Ip(e,t,n){var r=e==null?0:e.length;return r?(n&&typeof n!="number"&&it(e,t,n)?(t=0,n=r):(t=t==null?0:oe(t),n=n===c?r:oe(n)),At(e,t,n)):[]}function Op(e,t){return ra(e,t)}function Dp(e,t,n){return ko(e,t,K(n,2))}function qp(e,t){var n=e==null?0:e.length;if(n){var r=ra(e,t);if(r<n&&Ot(e[r],t))return r}return-1}function Pp(e,t){return ra(e,t,!0)}function Up(e,t,n){return ko(e,t,K(n,2),!0)}function zp(e,t){if(e!=null&&e.length){var n=ra(e,t,!0)-1;if(Ot(e[n],t))return n}return-1}function Bp(e){return e&&e.length?ks(e):[]}function Wp(e,t){return e&&e.length?ks(e,K(t,2)):[]}function Vp(e){var t=e==null?0:e.length;return t?At(e,1,t):[]}function Hp(e,t,n){return e&&e.length?(t=n||t===c?1:oe(t),At(e,0,t<0?0:t)):[]}function Zp(e,t,n){var r=e==null?0:e.length;return r?(t=n||t===c?1:oe(t),t=r-t,At(e,t<0?0:t,r)):[]}function Gp(e,t){return e&&e.length?aa(e,K(t,3),!1,!0):[]}function Yp(e,t){return e&&e.length?aa(e,K(t,3)):[]}function Kp(e){return e&&e.length?gn(e):[]}function Jp(e,t){return e&&e.length?gn(e,K(t,2)):[]}function Xp(e,t){return t=typeof t=="function"?t:c,e&&e.length?gn(e,c,t):[]}function Vo(e){if(!e||!e.length)return[];var t=0;return e=h(e,function(n){if(De(n))return t=We(n.length,t),!0}),xe(t,function(n){return C(e,ae(n))})}function fu(e,t){if(!e||!e.length)return[];var n=Vo(e);return t==null?n:C(n,function(r){return u(t,c,r)})}function Qp(e,t){return Es(e||[],t||[],vr)}function em(e,t){return Es(e||[],t||[],Tr)}function pu(e){var t=o(e);return t.__chain__=!0,t}function tm(e,t){return t(e),e}function pa(e,t){return t(e)}function nm(){return pu(this)}function rm(){return new Y(this.value(),this.__chain__)}function am(){this.__values__===c&&(this.__values__=Tu(this.value()));var e=this.__index__>=this.__values__.length;return{done:e,value:e?c:this.__values__[this.__index__++]}}function om(){return this}function im(e){for(var t,n=this;n instanceof P;){var r=iu(n);r.__index__=0,r.__values__=c,t?i.__wrapped__=r:t=r;var i=r;n=n.__wrapped__}return i.__wrapped__=e,t}function sm(){var e=this.__wrapped__;if(e instanceof U){var t=e;return this.__actions__.length&&(t=new U(this)),t=t.reverse(),t.__actions__.push({func:pa,args:[Wo],thisArg:c}),new Y(t,this.__chain__)}return this.thru(Wo)}function um(){return Ns(this.__wrapped__,this.__actions__)}function lm(e,t,n){var r=te(e)?g:Cf;return n&&it(e,t,n)&&(t=c),r(e,K(t,3))}function cm(e,t){return(te(e)?h:bs)(e,K(t,3))}function dm(e,t){return Ke(ma(e,t),1)}function fm(e,t){return Ke(ma(e,t),Qt)}function pm(e,t,n){return n=n===c?1:oe(n),Ke(ma(e,t),n)}function mu(e,t){return(te(e)?f:xn)(e,K(t,3))}function hu(e,t){return(te(e)?b:Pu)(e,K(t,3))}function mm(e,t,n,r){e=ft(e)?e:er(e),n=n&&!r?oe(n):0;var i=e.length;return n<0&&(n=We(i+n,0)),ba(e)?n<=i&&e.indexOf(t,n)>-1:!!i&&S(e,t,n)>-1}function ma(e,t){return(te(e)?C:Ts)(e,K(t,3))}function hm(e,t,n,r){return e==null?[]:(te(t)||(t=t==null?[]:[t]),n=r?c:n,te(n)||(n=n==null?[]:[n]),As(e,t,n))}function gm(e,t,n){var r=te(e)?O:se,i=arguments.length<3;return r(e,K(t,4),n,i,xn)}function ym(e,t,n){var r=te(e)?I:se,i=arguments.length<3;return r(e,K(t,4),n,i,Pu)}function bm(e,t){return(te(e)?h:bs)(e,ga(K(t,3)))}function vm(e){return(te(e)?ms:Pf)(e)}function xm(e,t,n){return t=(n?it(e,t,n):t===c)?1:oe(t),(te(e)?yf:Uf)(e,t)}function _m(e){return(te(e)?bf:zf)(e)}function Cm(e){if(e==null)return 0;if(ft(e))return ba(e)?Ut(e):e.length;var t=Qe(e);return t==Et||t==Lt?e.size:jo(e).length}function Tm(e,t,n){var r=te(e)?$:Bf;return n&&it(e,t,n)&&(t=c),r(e,K(t,3))}function wm(e,t){if(typeof t!="function")throw new kt(je);return e=oe(e),function(){if(--e<1)return t.apply(this,arguments)}}function gu(e,t,n){return t=n?c:t,t=e&&t==null?e.length:t,an(e,Nt,c,c,c,c,t)}function yu(e,t){var n;if(typeof t!="function")throw new kt(je);return e=oe(e),function(){return--e>0&&(n=t.apply(this,arguments)),e<=1&&(t=c),n}}function bu(e,t,n){t=n?c:t;var r=an(e,Tt,c,c,c,c,c,t);return r.placeholder=bu.placeholder,r}function vu(e,t,n){t=n?c:t;var r=an(e,Xt,c,c,c,c,c,t);return r.placeholder=vu.placeholder,r}function xu(e,t,n){function r(Oe){var St=L,Nr=B;return L=B=c,fe=Oe,ne=e.apply(Nr,St)}function i(Oe){return fe=Oe,Z=$r(y,t),et?r(Oe):ne}function d(Oe){var St=Oe-de,Nr=Oe-fe,nl=t-St;return ht?Xe(nl,H-Nr):nl}function m(Oe){var St=Oe-de,Nr=Oe-fe;return de===c||St>=t||St<0||ht&&Nr>=H}function y(){var Oe=Na();return m(Oe)?x(Oe):(Z=$r(y,d(Oe)),c)}function x(Oe){return Z=c,Cn&&L?r(Oe):(L=B=c,ne)}function F(){Z!==c&&Bu(Z),fe=0,L=de=B=Z=c}function A(){return Z===c?ne:x(Na())}function R(){var Oe=Na(),St=m(Oe);if(L=arguments,B=this,de=Oe,St){if(Z===c)return i(de);if(ht)return Bu(Z),Z=$r(y,t),r(de)}return Z===c&&(Z=$r(y,t)),ne}var L,B,H,ne,Z,de,fe=0,et=!1,ht=!1,Cn=!0;if(typeof e!="function")throw new kt(je);return t=Rt(t)||0,Ee(n)&&(et=!!n.leading,ht="maxWait"in n,H=ht?We(Rt(n.maxWait)||0,t):H,Cn="trailing"in n?!!n.trailing:Cn),R.cancel=F,R.flush=A,R}function jm(e){return an(e,Hn)}function ha(e,t){if(typeof e!="function"||t!=null&&typeof t!="function")throw new kt(je);var n=function(){var r=arguments,i=t?t.apply(this,r):r[0],d=n.cache;if(d.has(i))return d.get(i);var m=e.apply(this,r);return n.cache=d.set(i,m)||d,m};return n.cache=new(ha.Cache||nn),n}function ga(e){if(typeof e!="function")throw new kt(je);return function(){var t=arguments;switch(t.length){case 0:return!e.call(this);case 1:return!e.call(this,t[0]);case 2:return!e.call(this,t[0],t[1]);case 3:return!e.call(this,t[0],t[1],t[2])}return!e.apply(this,t)}}function Mm(e){return yu(2,e)}function Am(e,t){if(typeof e!="function")throw new kt(je);return t=t===c?t:oe(t),ce(e,t)}function Fm(e,t){if(typeof e!="function")throw new kt(je);return t=t==null?0:We(oe(t),0),ce(function(n){var r=n[t],i=bn(n,0,t);return r&&w(i,r),u(e,this,i)})}function Rm(e,t,n){var r=!0,i=!0;if(typeof e!="function")throw new kt(je);return Ee(n)&&(r="leading"in n?!!n.leading:r,i="trailing"in n?!!n.trailing:i),xu(e,t,{leading:r,maxWait:t,trailing:i})}function km(e){return gu(e,1)}function Sm(e,t){return di(Eo(t),e)}function $m(){if(!arguments.length)return[];var e=arguments[0];return te(e)?e:[e]}function Nm(e){return Mt(e,Jt)}function Em(e,t){return t=typeof t=="function"?t:c,Mt(e,Jt,t)}function Lm(e){return Mt(e,nt|Jt)}function Im(e,t){return t=typeof t=="function"?t:c,Mt(e,nt|Jt,t)}function Om(e,t){return t==null||gs(e,t,Ve(t))}function Ot(e,t){return e===t||e!==e&&t!==t}function ft(e){return e!=null&&ya(e.length)&&!un(e)}function De(e){return Ie(e)&&ft(e)}function Dm(e){return e===!0||e===!1||Ie(e)&&ot(e)==kn}function qm(e){return Ie(e)&&e.nodeType===1&&!Mr(e)}function Pm(e){if(e==null)return!0;if(ft(e)&&(te(e)||typeof e=="string"||typeof e.splice=="function"||_n(e)||ir(e)||Pn(e)))return!e.length;var t=Qe(e);if(t==Et||t==Lt)return!e.size;if(jr(e))return!jo(e).length;for(var n in e)if(_e.call(e,n))return!1;return!0}function Um(e,t){return Cr(e,t)}function zm(e,t,n){n=typeof n=="function"?n:c;var r=n?n(e,t):c;return r===c?Cr(e,t,c,n):!!r}function Ho(e){if(!Ie(e))return!1;var t=ot(e);return t==Zr||t==Lc||typeof e.message=="string"&&typeof e.name=="string"&&!Mr(e)}function Bm(e){return typeof e=="number"&&Ou(e)}function un(e){if(!Ee(e))return!1;var t=ot(e);return t==Gr||t==Li||t==Ge||t==Oc}function _u(e){return typeof e=="number"&&e==oe(e)}function ya(e){return typeof e=="number"&&e>-1&&e%1==0&&e<=Wt}function Ee(e){var t=typeof e;return e!=null&&(t=="object"||t=="function")}function Ie(e){return e!=null&&typeof e=="object"}function Wm(e,t){return e===t||wo(e,t,Po(t))}function Vm(e,t,n){return n=typeof n=="function"?n:c,wo(e,t,Po(t),n)}function Hm(e){return Cu(e)&&e!=+e}function Zm(e){if(Lg(e))throw new ti(ut);return _s(e)}function Gm(e){return e===null}function Ym(e){return e==null}function Cu(e){return typeof e=="number"||Ie(e)&&ot(e)==mr}function Mr(e){if(!Ie(e)||ot(e)!=en)return!1;var t=Ma(e);if(t===null)return!0;var n=_e.call(t,"constructor")&&t.constructor;return typeof n=="function"&&n instanceof n&&Ca.call(n)==hg}function Km(e){return _u(e)&&e>=-Wt&&e<=Wt}function ba(e){return typeof e=="string"||!te(e)&&Ie(e)&&ot(e)==gr}function xt(e){return typeof e=="symbol"||Ie(e)&&ot(e)==Yr}function Jm(e){return e===c}function Xm(e){return Ie(e)&&Qe(e)==yr}function Qm(e){return Ie(e)&&ot(e)==qc}function Tu(e){if(!e)return[];if(ft(e))return ba(e)?Ue(e):dt(e);if(Ar&&e[Ar])return Bn(e[Ar]());var t=Qe(e);return(t==Et?An:t==Lt?Pe:er)(e)}function ln(e){return e?(e=Rt(e),e===Qt||e===-Qt?(e<0?-1:1)*Xa:e===e?e:0):e===0?e:0}function oe(e){var t=ln(e),n=t%1;return t===t?n?t-n:t:0}function wu(e){return e?Ln(oe(e),0,J):0}function Rt(e){if(typeof e=="number")return e;if(xt(e))return Zn;if(Ee(e)){var t=typeof e.valueOf=="function"?e.valueOf():e;e=Ee(t)?t+"":t}if(typeof e!="string")return e===0?e:+e;e=ge(e);var n=id.test(e);return n||ud.test(e)?Pd(e.slice(2),n?2:8):od.test(e)?Zn:+e}function ju(e){return Ht(e,pt(e))}function eh(e){return e?Ln(oe(e),-Wt,Wt):e===0?e:0}function be(e){return e==null?"":vt(e)}function th(e,t){var n=or(e);return t==null?n:hs(n,t)}function nh(e,t){return ee(e,K(t,3),Vt)}function rh(e,t){return ee(e,K(t,3),_o)}function ah(e,t){return e==null?e:ii(e,K(t,3),pt)}function oh(e,t){return e==null?e:Uu(e,K(t,3),pt)}function ih(e,t){return e&&Vt(e,K(t,3))}function sh(e,t){return e&&_o(e,K(t,3))}function uh(e){return e==null?[]:ta(e,Ve(e))}function lh(e){return e==null?[]:ta(e,pt(e))}function Zo(e,t,n){var r=e==null?c:In(e,t);return r===c?n:r}function ch(e,t){return e!=null&&Xs(e,t,wf)}function Go(e,t){return e!=null&&Xs(e,t,jf)}function Ve(e){return ft(e)?ps(e):jo(e)}function pt(e){return ft(e)?ps(e,!0):Lf(e)}function dh(e,t){var n={};return t=K(t,3),Vt(e,function(r,i,d){rn(n,t(r,i,d),r)}),n}function fh(e,t){var n={};return t=K(t,3),Vt(e,function(r,i,d){rn(n,i,t(r,i,d))}),n}function ph(e,t){return Mu(e,ga(K(t)))}function Mu(e,t){if(e==null)return{};var n=C(qo(e),function(r){return[r]});return t=K(t),Fs(e,n,function(r,i){return t(r,i[0])})}function mh(e,t,n){t=yn(t,e);var r=-1,i=t.length;for(i||(i=1,e=c);++r<i;){var d=e==null?c:e[Zt(t[r])];d===c&&(r=i,d=n),e=un(d)?d.call(e):d}return e}function hh(e,t,n){return e==null?e:Tr(e,t,n)}function gh(e,t,n,r){return r=typeof r=="function"?r:c,e==null?e:Tr(e,t,n,r)}function yh(e,t,n){var r=te(e),i=r||_n(e)||ir(e);if(t=K(t,4),n==null){var d=e&&e.constructor;n=i?r?new d:[]:Ee(e)&&un(d)?or(Ma(e)):{}}return(i?f:Vt)(e,function(m,y,x){return t(n,m,y,x)}),n}function bh(e,t){return e==null||So(e,t)}function vh(e,t,n){return e==null?e:$s(e,t,Eo(n))}function xh(e,t,n,r){return r=typeof r=="function"?r:c,e==null?e:$s(e,t,Eo(n),r)}function er(e){return e==null?[]:D(e,Ve(e))}function _h(e){return e==null?[]:D(e,pt(e))}function Ch(e,t,n){return n===c&&(n=t,t=c),n!==c&&(n=Rt(n),n=n===n?n:0),t!==c&&(t=Rt(t),t=t===t?t:0),Ln(Rt(e),t,n)}function Th(e,t,n){return t=ln(t),n===c?(n=t,t=0):n=ln(n),e=Rt(e),Mf(e,t,n)}function wh(e,t,n){if(n&&typeof n!="boolean"&&it(e,t,n)&&(t=n=c),n===c&&(typeof t=="boolean"?(n=t,t=c):typeof e=="boolean"&&(n=e,e=c)),e===c&&t===c?(e=0,t=1):(e=ln(e),t===c?(t=e,e=0):t=ln(t)),e>t){var r=e;e=t,t=r}if(n||e%1||t%1){var i=Du();return Xe(e+i*(t-e+qd("1e-"+((i+"").length-1))),t)}return Fo(e,t)}function Au(e){return pi(be(e).toLowerCase())}function Fu(e){return e=be(e),e&&e.replace(cd,Bd).replace(Rd,"")}function jh(e,t,n){e=be(e),t=vt(t);var r=e.length;n=n===c?r:Ln(oe(n),0,r);var i=n;return n-=t.length,n>=0&&e.slice(n,i)==t}function Mh(e){return e=be(e),e&&Wc.test(e)?e.replace(Di,Wd):e}function Ah(e){return e=be(e),e&&Kc.test(e)?e.replace(uo,"\\$&"):e}function Fh(e,t,n){e=be(e),t=oe(t);var r=t?Ut(e):0;if(!t||r>=t)return e;var i=(t-r)/2;return ua(ka(i),n)+e+ua(Ra(i),n)}function Rh(e,t,n){e=be(e),t=oe(t);var r=t?Ut(e):0;return t&&r<t?e+ua(t-r,n):e}function kh(e,t,n){e=be(e),t=oe(t);var r=t?Ut(e):0;return t&&r<t?ua(t-r,n)+e:e}function Sh(e,t,n){return n||t==null?t=0:t&&(t=+t),jg(be(e).replace(lo,""),t||0)}function $h(e,t,n){return t=(n?it(e,t,n):t===c)?1:oe(t),Ro(be(e),t)}function Nh(){var e=arguments,t=be(e[0]);return e.length<3?t:t.replace(e[1],e[2])}function Eh(e,t,n){return n&&typeof n!="number"&&it(e,t,n)&&(t=n=c),(n=n===c?J:n>>>0)?(e=be(e),e&&(typeof t=="string"||t!=null&&!fi(t))&&(t=vt(t),!t&&gt(e))?bn(Ue(e),0,n):e.split(t,n)):[]}function Lh(e,t,n){return e=be(e),n=n==null?0:Ln(oe(n),0,e.length),t=vt(t),e.slice(n,n+t.length)==t}function Ih(e,t,n){var r=o.templateSettings;n&&it(e,t,n)&&(t=c),e=be(e),t=Ea({},t,r,Ys);var i,d,m=Ea({},t.imports,r.imports,Ys),y=Ve(m),x=D(m,y),F=0,A=t.interpolate||Kr,R="__p += '",L=ni((t.escape||Kr).source+"|"+A.source+"|"+(A===qi?ad:Kr).source+"|"+(t.evaluate||Kr).source+"|$","g"),B="//# sourceURL="+(_e.call(t,"sourceURL")?(t.sourceURL+"").replace(/\s/g," "):"lodash.templateSources["+ ++Ed+"]")+`
`;e.replace(L,function(Z,de,fe,et,ht,Cn){return fe||(fe=et),R+=e.slice(F,Cn).replace(dd,fn),de&&(i=!0,R+=`' +
__e(`+de+`) +
'`),ht&&(d=!0,R+=`';
`+ht+`;
__p += '`),fe&&(R+=`' +
((__t = (`+fe+`)) == null ? '' : __t) +
'`),F=Cn+Z.length,Z}),R+=`';
`;var H=_e.call(t,"variable")&&t.variable;if(H){if(nd.test(H))throw new ti(Ct)}else R=`with (obj) {
`+R+`
}
`;R=(d?R.replace(Pc,""):R).replace(Uc,"$1").replace(zc,"$1;"),R="function("+(H||"obj")+`) {
`+(H?"":`obj || (obj = {});
`)+"var __t, __p = ''"+(i?", __e = _.escape":"")+(d?`, __j = Array.prototype.join;
function print() { __p += __j.call(arguments, '') }
`:`;
`)+R+`return __p
}`;var ne=tl(function(){return Su(y,B+"return "+R).apply(c,x)});if(ne.source=R,Ho(ne))throw ne;return ne}function Oh(e){return be(e).toLowerCase()}function Dh(e){return be(e).toUpperCase()}function qh(e,t,n){if(e=be(e),e&&(n||t===c))return ge(e);if(!e||!(t=vt(t)))return e;var r=Ue(e),i=Ue(t);return bn(r,Yt(r,i),dn(r,i)+1).join("")}function Ph(e,t,n){if(e=be(e),e&&(n||t===c))return e.slice(0,pn(e)+1);if(!e||!(t=vt(t)))return e;var r=Ue(e);return bn(r,0,dn(r,Ue(t))+1).join("")}function Uh(e,t,n){if(e=be(e),e&&(n||t===c))return e.replace(lo,"");if(!e||!(t=vt(t)))return e;var r=Ue(e);return bn(r,Yt(r,Ue(t))).join("")}function zh(e,t){var n=Ya,r=Br;if(Ee(t)){var i="separator"in t?t.separator:i;n="length"in t?oe(t.length):n,r="omission"in t?vt(t.omission):r}e=be(e);var d=e.length;if(gt(e)){var m=Ue(e);d=m.length}if(n>=d)return e;var y=n-Ut(r);if(y<1)return r;var x=m?bn(m,0,y).join(""):e.slice(0,y);if(i===c)return x+r;if(m&&(y+=x.length-y),fi(i)){if(e.slice(y).search(i)){var F,A=x;for(i.global||(i=ni(i.source,be(Pi.exec(i))+"g")),i.lastIndex=0;F=i.exec(A);)var R=F.index;x=x.slice(0,R===c?y:R)}}else if(e.indexOf(vt(i),y)!=y){var L=x.lastIndexOf(i);L>-1&&(x=x.slice(0,L))}return x+r}function Bh(e){return e=be(e),e&&Bc.test(e)?e.replace(Oi,Vd):e}function Ru(e,t,n){return e=be(e),t=n?c:t,t===c?cr(e)?V(e):X(e):e.match(t)||[]}function Wh(e){var t=e==null?0:e.length,n=K();return e=t?C(e,function(r){if(typeof r[1]!="function")throw new kt(je);return[n(r[0]),r[1]]}):[],ce(function(r){for(var i=-1;++i<t;){var d=e[i];if(u(d[0],this,r))return u(d[1],this,r)}})}function Vh(e){return _f(Mt(e,nt))}function Yo(e){return function(){return e}}function Hh(e,t){return e==null||e!==e?t:e}function mt(e){return e}function Ko(e){return Cs(typeof e=="function"?e:Mt(e,nt))}function Zh(e){return ws(Mt(e,nt))}function Gh(e,t){return js(e,Mt(t,nt))}function Jo(e,t,n){var r=Ve(t),i=ta(t,r);n!=null||Ee(t)&&(i.length||!r.length)||(n=t,t=e,e=this,i=ta(t,Ve(t)));var d=!(Ee(n)&&"chain"in n&&!n.chain),m=un(e);return f(i,function(y){var x=t[y];e[y]=x,m&&(e.prototype[y]=function(){var F=this.__chain__;if(d||F){var A=e(this.__wrapped__);return(A.__actions__=dt(this.__actions__)).push({func:x,args:arguments,thisArg:e}),A.__chain__=F,A}return x.apply(e,w([this.value()],arguments))})}),e}function Yh(){return Ye._===this&&(Ye._=gg),this}function Xo(){}function Kh(e){return e=oe(e),ce(function(t){return Ms(t,e)})}function ku(e){return Uo(e)?ae(Zt(e)):Df(e)}function Jh(e){return function(t){return e==null?c:In(e,t)}}function Qo(){return[]}function ei(){return!1}function Xh(){return{}}function Qh(){return""}function eg(){return!0}function tg(e,t){if(e=oe(e),e<1||e>Wt)return[];var n=J,r=Xe(e,J);t=K(t),e-=J;for(var i=xe(r,t);++n<e;)t(n);return i}function ng(e){return te(e)?C(e,Zt):xt(e)?[e]:dt(Hu(be(e)))}function rg(e){var t=++mg;return be(e)+t}function ag(e){return e&&e.length?ea(e,mt,Co):c}function og(e,t){return e&&e.length?ea(e,K(t,2),Co):c}function ig(e){return Re(e,mt)}function sg(e,t){return Re(e,K(t,2))}function ug(e){return e&&e.length?ea(e,mt,Mo):c}function lg(e,t){return e&&e.length?ea(e,K(t,2),Mo):c}function cg(e){return e&&e.length?ve(e,mt):0}function dg(e,t){return e&&e.length?ve(e,K(t,2)):0}M=M==null?Ye:Kn.defaults(Ye.Object(),M,Kn.pick(Ye,Nd));var Be=M.Array,va=M.Date,ti=M.Error,Su=M.Function,tr=M.Math,Ae=M.Object,ni=M.RegExp,fg=M.String,kt=M.TypeError,xa=Be.prototype,pg=Su.prototype,nr=Ae.prototype,_a=M["__core-js_shared__"],Ca=pg.toString,_e=nr.hasOwnProperty,mg=0,$u=function(){var e=/[^.]+$/.exec(_a&&_a.keys&&_a.keys.IE_PROTO||"");return e?"Symbol(src)_1."+e:""}(),Ta=nr.toString,hg=Ca.call(Ae),gg=Ye._,yg=ni("^"+Ca.call(_e).replace(uo,"\\$&").replace(/hasOwnProperty|(function).*?(?=\\\()| for .+?(?=\\\])/g,"$1.*?")+"$"),wa=is?M.Buffer:c,vn=M.Symbol,ja=M.Uint8Array,Nu=wa?wa.allocUnsafe:c,Ma=Wn(Ae.getPrototypeOf,Ae),Eu=Ae.create,Lu=nr.propertyIsEnumerable,Aa=xa.splice,Iu=vn?vn.isConcatSpreadable:c,Ar=vn?vn.iterator:c,qn=vn?vn.toStringTag:c,Fa=function(){try{var e=On(Ae,"defineProperty");return e({},"",{}),e}catch{}}(),bg=M.clearTimeout!==Ye.clearTimeout&&M.clearTimeout,vg=va&&va.now!==Ye.Date.now&&va.now,xg=M.setTimeout!==Ye.setTimeout&&M.setTimeout,Ra=tr.ceil,ka=tr.floor,ri=Ae.getOwnPropertySymbols,_g=wa?wa.isBuffer:c,Ou=M.isFinite,Cg=xa.join,Tg=Wn(Ae.keys,Ae),We=tr.max,Xe=tr.min,wg=va.now,jg=M.parseInt,Du=tr.random,Mg=xa.reverse,ai=On(M,"DataView"),Fr=On(M,"Map"),oi=On(M,"Promise"),rr=On(M,"Set"),Rr=On(M,"WeakMap"),kr=On(Ae,"create"),Sa=Rr&&new Rr,ar={},Ag=Dn(ai),Fg=Dn(Fr),Rg=Dn(oi),kg=Dn(rr),Sg=Dn(Rr),$a=vn?vn.prototype:c,Sr=$a?$a.valueOf:c,qu=$a?$a.toString:c,or=function(){function e(){}return function(t){if(!Ee(t))return{};if(Eu)return Eu(t);e.prototype=t;var n=new e;return e.prototype=c,n}}();o.templateSettings={escape:Vc,evaluate:Hc,interpolate:qi,variable:"",imports:{_:o}},o.prototype=P.prototype,o.prototype.constructor=o,Y.prototype=or(P.prototype),Y.prototype.constructor=Y,U.prototype=or(P.prototype),U.prototype.constructor=U,Nn.prototype.clear=Gd,Nn.prototype.delete=Yd,Nn.prototype.get=Kd,Nn.prototype.has=Jd,Nn.prototype.set=Xd,tn.prototype.clear=Qd,tn.prototype.delete=ef,tn.prototype.get=tf,tn.prototype.has=nf,tn.prototype.set=rf,nn.prototype.clear=af,nn.prototype.delete=of,nn.prototype.get=sf,nn.prototype.has=uf,nn.prototype.set=lf,En.prototype.add=En.prototype.push=cf,En.prototype.has=df,It.prototype.clear=ff,It.prototype.delete=pf,It.prototype.get=mf,It.prototype.has=hf,It.prototype.set=gf;var xn=Ps(Vt),Pu=Ps(_o,!0),ii=Us(),Uu=Us(!0),zu=Sa?function(e,t){return Sa.set(e,t),e}:mt,$g=Fa?function(e,t){return Fa(e,"toString",{configurable:!0,enumerable:!1,value:Yo(t),writable:!0})}:mt,Ng=ce,Bu=bg||function(e){return Ye.clearTimeout(e)},Eg=rr&&1/Pe(new rr([,-0]))[1]==Qt?function(e){return new rr(e)}:Xo,si=Sa?function(e){return Sa.get(e)}:Xo,ui=ri?function(e){return e==null?[]:(e=Ae(e),h(ri(e),function(t){return Lu.call(e,t)}))}:Qo,Wu=ri?function(e){for(var t=[];e;)w(t,ui(e)),e=Ma(e);return t}:Qo,Qe=ot;(ai&&Qe(new ai(new ArrayBuffer(1)))!=Gn||Fr&&Qe(new Fr)!=Et||oi&&Qe(oi.resolve())!=Ii||rr&&Qe(new rr)!=Lt||Rr&&Qe(new Rr)!=yr)&&(Qe=function(e){var t=ot(e),n=t==en?e.constructor:c,r=n?Dn(n):"";if(r)switch(r){case Ag:return Gn;case Fg:return Et;case Rg:return Ii;case kg:return Lt;case Sg:return yr}return t});var Lg=_a?un:ei,Vu=ou(zu),$r=xg||function(e,t){return Ye.setTimeout(e,t)},li=ou($g),Hu=dp(function(e){var t=[];return e.charCodeAt(0)===46&&t.push(""),e.replace(Yc,function(n,r,i,d){t.push(i?d.replace(rd,"$1"):r||n)}),t}),Ig=ce(function(e,t){return De(e)?xr(e,Ke(t,1,De,!0)):[]}),Og=ce(function(e,t){var n=Ft(t);return De(n)&&(n=c),De(e)?xr(e,Ke(t,1,De,!0),K(n,2)):[]}),Dg=ce(function(e,t){var n=Ft(t);return De(n)&&(n=c),De(e)?xr(e,Ke(t,1,De,!0),c,n):[]}),qg=ce(function(e){var t=C(e,No);return t.length&&t[0]===e[0]?To(t):[]}),Pg=ce(function(e){var t=Ft(e),n=C(e,No);return t===Ft(n)?t=c:n.pop(),n.length&&n[0]===e[0]?To(n,K(t,2)):[]}),Ug=ce(function(e){var t=Ft(e),n=C(e,No);return t=typeof t=="function"?t:c,t&&n.pop(),n.length&&n[0]===e[0]?To(n,c,t):[]}),zg=ce(du),Bg=on(function(e,t){var n=e==null?0:e.length,r=xo(e,t);return Rs(e,C(t,function(i){return sn(i,n)?+i:i}).sort(Os)),r}),Wg=ce(function(e){return gn(Ke(e,1,De,!0))}),Vg=ce(function(e){var t=Ft(e);return De(t)&&(t=c),gn(Ke(e,1,De,!0),K(t,2))}),Hg=ce(function(e){var t=Ft(e);return t=typeof t=="function"?t:c,gn(Ke(e,1,De,!0),c,t)}),Zg=ce(function(e,t){return De(e)?xr(e,t):[]}),Gg=ce(function(e){return $o(h(e,De))}),Yg=ce(function(e){var t=Ft(e);return De(t)&&(t=c),$o(h(e,De),K(t,2))}),Kg=ce(function(e){var t=Ft(e);return t=typeof t=="function"?t:c,$o(h(e,De),c,t)}),Jg=ce(Vo),Xg=ce(function(e){var t=e.length,n=t>1?e[t-1]:c;return n=typeof n=="function"?(e.pop(),n):c,fu(e,n)}),Qg=on(function(e){var t=e.length,n=t?e[0]:0,r=this.__wrapped__,i=function(d){return xo(d,e)};return!(t>1||this.__actions__.length)&&r instanceof U&&sn(n)?(r=r.slice(n,+n+(t?1:0)),r.__actions__.push({func:pa,args:[i],thisArg:c}),new Y(r,this.__chain__).thru(function(d){return t&&!d.length&&d.push(c),d})):this.thru(i)}),ey=oa(function(e,t,n){_e.call(e,n)?++e[n]:rn(e,n,1)}),ty=Bs(su),ny=Bs(uu),ry=oa(function(e,t,n){_e.call(e,n)?e[n].push(t):rn(e,n,[t])}),ay=ce(function(e,t,n){var r=-1,i=typeof t=="function",d=ft(e)?Be(e.length):[];return xn(e,function(m){d[++r]=i?u(t,m,n):_r(m,t,n)}),d}),oy=oa(function(e,t,n){rn(e,n,t)}),iy=oa(function(e,t,n){e[n?0:1].push(t)},function(){return[[],[]]}),sy=ce(function(e,t){if(e==null)return[];var n=t.length;return n>1&&it(e,t[0],t[1])?t=[]:n>2&&it(t[0],t[1],t[2])&&(t=[t[0]]),As(e,Ke(t,1),[])}),Na=vg||function(){return Ye.Date.now()},ci=ce(function(e,t,n){var r=lt;if(n.length){var i=_t(n,Qn(ci));r|=wt}return an(e,r,t,n,i)}),Zu=ce(function(e,t,n){var r=lt|Bt;if(n.length){var i=_t(n,Qn(Zu));r|=wt}return an(t,r,e,n,i)}),uy=ce(function(e,t){return ys(e,1,t)}),ly=ce(function(e,t,n){return ys(e,Rt(t)||0,n)});ha.Cache=nn;var cy=Ng(function(e,t){t=t.length==1&&te(t[0])?C(t[0],E(K())):C(Ke(t,1),E(K()));var n=t.length;return ce(function(r){for(var i=-1,d=Xe(r.length,n);++i<d;)r[i]=t[i].call(this,r[i]);return u(e,this,r)})}),di=ce(function(e,t){return an(e,wt,c,t,_t(t,Qn(di)))}),Gu=ce(function(e,t){return an(e,hn,c,t,_t(t,Qn(Gu)))}),dy=on(function(e,t){return an(e,Rn,c,c,c,t)}),fy=la(Co),py=la(function(e,t){return e>=t}),Pn=xs(function(){return arguments}())?xs:function(e){return Ie(e)&&_e.call(e,"callee")&&!Lu.call(e,"callee")},te=Be.isArray,my=ss?E(ss):Ff,_n=_g||ei,hy=us?E(us):Rf,Yu=ls?E(ls):Sf,fi=cs?E(cs):$f,Ku=ds?E(ds):Nf,ir=fs?E(fs):Ef,gy=la(Mo),yy=la(function(e,t){return e<=t}),by=Jn(function(e,t){if(jr(t)||ft(t))return Ht(t,Ve(t),e),c;for(var n in t)_e.call(t,n)&&vr(e,n,t[n])}),Ju=Jn(function(e,t){Ht(t,pt(t),e)}),Ea=Jn(function(e,t,n,r){Ht(t,pt(t),e,r)}),vy=Jn(function(e,t,n,r){Ht(t,Ve(t),e,r)}),xy=on(xo),_y=ce(function(e,t){e=Ae(e);var n=-1,r=t.length,i=r>2?t[2]:c;for(i&&it(t[0],t[1],i)&&(r=1);++n<r;)for(var d=t[n],m=pt(d),y=-1,x=m.length;++y<x;){var F=m[y],A=e[F];(A===c||Ot(A,nr[F])&&!_e.call(e,F))&&(e[F]=d[F])}return e}),Cy=ce(function(e){return e.push(c,Ks),u(Xu,c,e)}),Ty=Vs(function(e,t,n){t!=null&&typeof t.toString!="function"&&(t=Ta.call(t)),e[t]=n},Yo(mt)),wy=Vs(function(e,t,n){t!=null&&typeof t.toString!="function"&&(t=Ta.call(t)),_e.call(e,t)?e[t].push(n):e[t]=[n]},K),jy=ce(_r),My=Jn(function(e,t,n){na(e,t,n)}),Xu=Jn(function(e,t,n,r){na(e,t,n,r)}),Ay=on(function(e,t){var n={};if(e==null)return n;var r=!1;t=C(t,function(d){return d=yn(d,e),r||(r=d.length>1),d}),Ht(e,qo(e),n),r&&(n=Mt(n,nt|pr|Jt,Qf));for(var i=t.length;i--;)So(n,t[i]);return n}),Fy=on(function(e,t){return e==null?{}:Of(e,t)}),Qu=Gs(Ve),el=Gs(pt),Ry=Xn(function(e,t,n){return t=t.toLowerCase(),e+(n?Au(t):t)}),ky=Xn(function(e,t,n){return e+(n?"-":"")+t.toLowerCase()}),Sy=Xn(function(e,t,n){return e+(n?" ":"")+t.toLowerCase()}),$y=zs("toLowerCase"),Ny=Xn(function(e,t,n){return e+(n?"_":"")+t.toLowerCase()}),Ey=Xn(function(e,t,n){return e+(n?" ":"")+pi(t)}),Ly=Xn(function(e,t,n){return e+(n?" ":"")+t.toUpperCase()}),pi=zs("toUpperCase"),tl=ce(function(e,t){try{return u(e,c,t)}catch(n){return Ho(n)?n:new ti(n)}}),Iy=on(function(e,t){return f(t,function(n){n=Zt(n),rn(e,n,ci(e[n],e))}),e}),Oy=Ws(),Dy=Ws(!0),qy=ce(function(e,t){return function(n){return _r(n,e,t)}}),Py=ce(function(e,t){return function(n){return _r(e,n,t)}}),Uy=Io(C),zy=Io(g),By=Io($),Wy=Hs(),Vy=Hs(!0),Hy=sa(function(e,t){return e+t},0),Zy=Oo("ceil"),Gy=sa(function(e,t){return e/t},1),Yy=Oo("floor"),Ky=sa(function(e,t){return e*t},1),Jy=Oo("round"),Xy=sa(function(e,t){return e-t},0);return o.after=wm,o.ary=gu,o.assign=by,o.assignIn=Ju,o.assignInWith=Ea,o.assignWith=vy,o.at=xy,o.before=yu,o.bind=ci,o.bindAll=Iy,o.bindKey=Zu,o.castArray=$m,o.chain=pu,o.chunk=yp,o.compact=bp,o.concat=vp,o.cond=Wh,o.conforms=Vh,o.constant=Yo,o.countBy=ey,o.create=th,o.curry=bu,o.curryRight=vu,o.debounce=xu,o.defaults=_y,o.defaultsDeep=Cy,o.defer=uy,o.delay=ly,o.difference=Ig,o.differenceBy=Og,o.differenceWith=Dg,o.drop=xp,o.dropRight=_p,o.dropRightWhile=Cp,o.dropWhile=Tp,o.fill=wp,o.filter=cm,o.flatMap=dm,o.flatMapDeep=fm,o.flatMapDepth=pm,o.flatten=lu,o.flattenDeep=jp,o.flattenDepth=Mp,o.flip=jm,o.flow=Oy,o.flowRight=Dy,o.fromPairs=Ap,o.functions=uh,o.functionsIn=lh,o.groupBy=ry,o.initial=Rp,o.intersection=qg,o.intersectionBy=Pg,o.intersectionWith=Ug,o.invert=Ty,o.invertBy=wy,o.invokeMap=ay,o.iteratee=Ko,o.keyBy=oy,o.keys=Ve,o.keysIn=pt,o.map=ma,o.mapKeys=dh,o.mapValues=fh,o.matches=Zh,o.matchesProperty=Gh,o.memoize=ha,o.merge=My,o.mergeWith=Xu,o.method=qy,o.methodOf=Py,o.mixin=Jo,o.negate=ga,o.nthArg=Kh,o.omit=Ay,o.omitBy=ph,o.once=Mm,o.orderBy=hm,o.over=Uy,o.overArgs=cy,o.overEvery=zy,o.overSome=By,o.partial=di,o.partialRight=Gu,o.partition=iy,o.pick=Fy,o.pickBy=Mu,o.property=ku,o.propertyOf=Jh,o.pull=zg,o.pullAll=du,o.pullAllBy=Np,o.pullAllWith=Ep,o.pullAt=Bg,o.range=Wy,o.rangeRight=Vy,o.rearg=dy,o.reject=bm,o.remove=Lp,o.rest=Am,o.reverse=Wo,o.sampleSize=xm,o.set=hh,o.setWith=gh,o.shuffle=_m,o.slice=Ip,o.sortBy=sy,o.sortedUniq=Bp,o.sortedUniqBy=Wp,o.split=Eh,o.spread=Fm,o.tail=Vp,o.take=Hp,o.takeRight=Zp,o.takeRightWhile=Gp,o.takeWhile=Yp,o.tap=tm,o.throttle=Rm,o.thru=pa,o.toArray=Tu,o.toPairs=Qu,o.toPairsIn=el,o.toPath=ng,o.toPlainObject=ju,o.transform=yh,o.unary=km,o.union=Wg,o.unionBy=Vg,o.unionWith=Hg,o.uniq=Kp,o.uniqBy=Jp,o.uniqWith=Xp,o.unset=bh,o.unzip=Vo,o.unzipWith=fu,o.update=vh,o.updateWith=xh,o.values=er,o.valuesIn=_h,o.without=Zg,o.words=Ru,o.wrap=Sm,o.xor=Gg,o.xorBy=Yg,o.xorWith=Kg,o.zip=Jg,o.zipObject=Qp,o.zipObjectDeep=em,o.zipWith=Xg,o.entries=Qu,o.entriesIn=el,o.extend=Ju,o.extendWith=Ea,Jo(o,o),o.add=Hy,o.attempt=tl,o.camelCase=Ry,o.capitalize=Au,o.ceil=Zy,o.clamp=Ch,o.clone=Nm,o.cloneDeep=Lm,o.cloneDeepWith=Im,o.cloneWith=Em,o.conformsTo=Om,o.deburr=Fu,o.defaultTo=Hh,o.divide=Gy,o.endsWith=jh,o.eq=Ot,o.escape=Mh,o.escapeRegExp=Ah,o.every=lm,o.find=ty,o.findIndex=su,o.findKey=nh,o.findLast=ny,o.findLastIndex=uu,o.findLastKey=rh,o.floor=Yy,o.forEach=mu,o.forEachRight=hu,o.forIn=ah,o.forInRight=oh,o.forOwn=ih,o.forOwnRight=sh,o.get=Zo,o.gt=fy,o.gte=py,o.has=ch,o.hasIn=Go,o.head=cu,o.identity=mt,o.includes=mm,o.indexOf=Fp,o.inRange=Th,o.invoke=jy,o.isArguments=Pn,o.isArray=te,o.isArrayBuffer=my,o.isArrayLike=ft,o.isArrayLikeObject=De,o.isBoolean=Dm,o.isBuffer=_n,o.isDate=hy,o.isElement=qm,o.isEmpty=Pm,o.isEqual=Um,o.isEqualWith=zm,o.isError=Ho,o.isFinite=Bm,o.isFunction=un,o.isInteger=_u,o.isLength=ya,o.isMap=Yu,o.isMatch=Wm,o.isMatchWith=Vm,o.isNaN=Hm,o.isNative=Zm,o.isNil=Ym,o.isNull=Gm,o.isNumber=Cu,o.isObject=Ee,o.isObjectLike=Ie,o.isPlainObject=Mr,o.isRegExp=fi,o.isSafeInteger=Km,o.isSet=Ku,o.isString=ba,o.isSymbol=xt,o.isTypedArray=ir,o.isUndefined=Jm,o.isWeakMap=Xm,o.isWeakSet=Qm,o.join=kp,o.kebabCase=ky,o.last=Ft,o.lastIndexOf=Sp,o.lowerCase=Sy,o.lowerFirst=$y,o.lt=gy,o.lte=yy,o.max=ag,o.maxBy=og,o.mean=ig,o.meanBy=sg,o.min=ug,o.minBy=lg,o.stubArray=Qo,o.stubFalse=ei,o.stubObject=Xh,o.stubString=Qh,o.stubTrue=eg,o.multiply=Ky,o.nth=$p,o.noConflict=Yh,o.noop=Xo,o.now=Na,o.pad=Fh,o.padEnd=Rh,o.padStart=kh,o.parseInt=Sh,o.random=wh,o.reduce=gm,o.reduceRight=ym,o.repeat=$h,o.replace=Nh,o.result=mh,o.round=Jy,o.runInContext=v,o.sample=vm,o.size=Cm,o.snakeCase=Ny,o.some=Tm,o.sortedIndex=Op,o.sortedIndexBy=Dp,o.sortedIndexOf=qp,o.sortedLastIndex=Pp,o.sortedLastIndexBy=Up,o.sortedLastIndexOf=zp,o.startCase=Ey,o.startsWith=Lh,o.subtract=Xy,o.sum=cg,o.sumBy=dg,o.template=Ih,o.times=tg,o.toFinite=ln,o.toInteger=oe,o.toLength=wu,o.toLower=Oh,o.toNumber=Rt,o.toSafeInteger=eh,o.toString=be,o.toUpper=Dh,o.trim=qh,o.trimEnd=Ph,o.trimStart=Uh,o.truncate=zh,o.unescape=Bh,o.uniqueId=rg,o.upperCase=Ly,o.upperFirst=pi,o.each=mu,o.eachRight=hu,o.first=cu,Jo(o,function(){var e={};return Vt(o,function(t,n){_e.call(o.prototype,n)||(e[n]=t)}),e}(),{chain:!1}),o.VERSION=Q,f(["bind","bindKey","curry","curryRight","partial","partialRight"],function(e){o[e].placeholder=o}),f(["drop","take"],function(e,t){U.prototype[e]=function(n){n=n===c?1:We(oe(n),0);var r=this.__filtered__&&!t?new U(this):this.clone();return r.__filtered__?r.__takeCount__=Xe(n,r.__takeCount__):r.__views__.push({size:Xe(n,J),type:e+(r.__dir__<0?"Right":"")}),r},U.prototype[e+"Right"]=function(n){return this.reverse()[e](n).reverse()}}),f(["filter","map","takeWhile"],function(e,t){var n=t+1,r=n==Hr||n==Ja;U.prototype[e]=function(i){var d=this.clone();return d.__iteratees__.push({iteratee:K(i,3),type:n}),d.__filtered__=d.__filtered__||r,d}}),f(["head","last"],function(e,t){var n="take"+(t?"Right":"");U.prototype[e]=function(){return this[n](1).value()[0]}}),f(["initial","tail"],function(e,t){var n="drop"+(t?"":"Right");U.prototype[e]=function(){return this.__filtered__?new U(this):this[n](1)}}),U.prototype.compact=function(){return this.filter(mt)},U.prototype.find=function(e){return this.filter(e).head()},U.prototype.findLast=function(e){return this.reverse().find(e)},U.prototype.invokeMap=ce(function(e,t){return typeof e=="function"?new U(this):this.map(function(n){return _r(n,e,t)})}),U.prototype.reject=function(e){return this.filter(ga(K(e)))},U.prototype.slice=function(e,t){e=oe(e);var n=this;return n.__filtered__&&(e>0||t<0)?new U(n):(e<0?n=n.takeRight(-e):e&&(n=n.drop(e)),t!==c&&(t=oe(t),n=t<0?n.dropRight(-t):n.take(t-e)),n)},U.prototype.takeRightWhile=function(e){return this.reverse().takeWhile(e).reverse()},U.prototype.toArray=function(){return this.take(J)},Vt(U.prototype,function(e,t){var n=/^(?:filter|find|map|reject)|While$/.test(t),r=/^(?:head|last)$/.test(t),i=o[r?"take"+(t=="last"?"Right":""):t],d=r||/^find/.test(t);i&&(o.prototype[t]=function(){var m=this.__wrapped__,y=r?[1]:arguments,x=m instanceof U,F=y[0],A=x||te(m),R=function(de){var fe=i.apply(o,w([de],y));return r&&L?fe[0]:fe};A&&n&&typeof F=="function"&&F.length!=1&&(x=A=!1);var L=this.__chain__,B=!!this.__actions__.length,H=d&&!L,ne=x&&!B;if(!d&&A){m=ne?m:new U(this);var Z=e.apply(m,y);return Z.__actions__.push({func:pa,args:[R],thisArg:c}),new Y(Z,L)}return H&&ne?e.apply(this,y):(Z=this.thru(R),H?r?Z.value()[0]:Z.value():Z)})}),f(["pop","push","shift","sort","splice","unshift"],function(e){var t=xa[e],n=/^(?:push|sort|unshift)$/.test(e)?"tap":"thru",r=/^(?:pop|shift)$/.test(e);o.prototype[e]=function(){var i=arguments;if(r&&!this.__chain__){var d=this.value();return t.apply(te(d)?d:[],i)}return this[n](function(m){return t.apply(te(m)?m:[],i)})}}),Vt(U.prototype,function(e,t){var n=o[t];if(n){var r=n.name+"";_e.call(ar,r)||(ar[r]=[]),ar[r].push({name:t,func:n})}}),ar[ia(c,Bt).name]=[{name:"wrapper",func:c}],U.prototype.clone=ct,U.prototype.reverse=bo,U.prototype.value=Zd,o.prototype.at=Qg,o.prototype.chain=nm,o.prototype.commit=rm,o.prototype.next=am,o.prototype.plant=im,o.prototype.reverse=sm,o.prototype.toJSON=o.prototype.valueOf=o.prototype.value=um,o.prototype.first=o.prototype.head,Ar&&(o.prototype[Ar]=om),o},Kn=Hd();$n?(($n.exports=Kn)._=Kn,go._=Kn):Ye._=Kn}).call(Er)})(za,za.exports);var C1=za.exports,xl=C1.runInContext(),me=gb(xl,xl);const T=a=>`${He}.${a}`,Cc=pe.createContext(),Mn=()=>pe.useContext(Cc),Tc=pe.createContext(),ur=()=>pe.useContext(Tc),T1=()=>{const{components:a,componentsGroupedByCategory:s,contentTypes:u,isInDevelopmentMode:p,sortedContentTypesList:f,modifiedData:b,initialData:g}=Mn(),{toggleNotification:h}=Za(),{formatMessage:_}=he(),{trackUsage:j}=Va(),[C,w]=pe.useState(""),{onOpenModalCreateSchema:O,onOpenModalEditCategory:I}=ur(),{locale:$}=he(),{startsWith:z}=qx($,{sensitivity:"base"}),X=Px($,{sensitivity:"base"}),ee=!Object.keys(u).some(se=>u[se].isTemporary===!0)&&!Object.keys(a).some(se=>a[se].isTemporary===!0)&&Ga(b,g),N=()=>{ee?(j("willCreateContentType"),O({modalType:"contentType",kind:"collectionType",actionType:"create",forTarget:"contentType"})):ie()},S=()=>{ee?(j("willCreateSingleType"),O({modalType:"contentType",kind:"singleType",actionType:"create",forTarget:"contentType"})):ie()},le=()=>{ee?(j("willCreateComponent"),O({modalType:"component",kind:null,actionType:"create",forTarget:"component"})):ie()},ie=()=>{h({type:"info",message:_({id:T("notification.info.creating.notSaved"),defaultMessage:"Please save your work before creating a new collection type or component"})})},Re=Object.entries(s).map(([se,tt])=>({name:se,title:se,isEditable:p,onClickEdit(ve,xe){ve.stopPropagation(),ee?I(xe.name):ie()},links:tt.map(ve=>({name:ve.uid,to:`/plugins/${He}/component-categories/${se}/${ve.uid}`,title:ve.schema.displayName})).sort((ve,xe)=>X.compare(ve.title,xe.title))})).sort((se,tt)=>X.compare(se.title,tt.title)),ae=f.filter(se=>se.visible);return{menu:[{name:"models",title:{id:`${T("menu.section.models.name")}`,defaultMessage:"Collection Types"},customLink:p&&{id:`${T("button.model.create")}`,defaultMessage:"Create new collection type",onClick:N},links:ae.filter(se=>se.kind==="collectionType")},{name:"singleTypes",title:{id:`${T("menu.section.single-types.name")}`,defaultMessage:"Single Types"},customLink:p&&{id:`${T("button.single-types.create")}`,defaultMessage:"Create new single type",onClick:S},links:ae.filter(se=>se.kind==="singleType")},{name:"components",title:{id:`${T("menu.section.components.name")}`,defaultMessage:"Components"},customLink:p&&{id:`${T("button.component.create")}`,defaultMessage:"Create a new component",onClick:le},links:Re}].map(se=>{if(se.links.some(xe=>Array.isArray(xe.links))){let xe=0;return{...se,links:se.links.map(Le=>{const ge=Le.links.filter(E=>z(E.title,C));return ge.length===0?null:(xe+=ge.length,{...Le,links:ge.sort((E,D)=>X.compare(E.title,D.title))})}).filter(Boolean),linksCount:xe}}const ve=se.links.filter(xe=>z(xe.title,C)).sort((xe,Le)=>X.compare(xe.title,Le.title));return{...se,links:ve,linksCount:ve.length}}),searchValue:C,onSearchChange:w}},w1=qt(mc)`
  div {
    width: inherit;
    span:nth-child(2) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: inherit;
    }
  }
`,j1=()=>{const{menu:a,searchValue:s,onSearchChange:u}=T1(),{formatMessage:p}=he(),f=p({id:T("plugin.name"),defaultMessage:"Content-Type Builder"});return l.jsxs(Ux,{"aria-label":f,children:[l.jsx(zx,{searchable:!0,value:s,onClear:()=>u(""),onChange:b=>u(b.target.value),label:f,searchLabel:p({id:"global.search",defaultMessage:"Search"})}),l.jsx(Bx,{children:a.map(b=>l.jsxs(pe.Fragment,{children:[l.jsx(Wx,{label:p({id:b.title.id,defaultMessage:b.title.defaultMessage}),collapsable:!0,badgeLabel:b.linksCount.toString(),children:b.links.map(g=>g.links?l.jsx(Vx,{label:cn(g.title),children:g.links.map(h=>l.jsx(mc,{tag:fl,to:h.to,active:h.active,isSubSectionChild:!0,children:cn(p({id:h.name,defaultMessage:h.title}))},h.name))},g.name):l.jsx(w1,{tag:fl,to:g.to,active:g.active,width:"100%",children:cn(p({id:g.name,defaultMessage:g.title}))},g.name))}),b.customLink&&l.jsx(Ne,{paddingLeft:7,children:l.jsx(Hx,{onClick:b.customLink.onClick,startIcon:l.jsx(wn,{width:"0.8rem",height:"0.8rem"}),marginTop:2,cursor:"pointer",children:p({id:b.customLink.id,defaultMessage:b.customLink.defaultMessage})})})]},b.name))})]})},wc=a=>a.kind==="collectionType"&&(a.restrictRelationsTo===null||Array.isArray(a.restrictRelationsTo)&&a.restrictRelationsTo.length>0),Mi=(a,s)=>a.find(({name:u})=>u===s),M1=(a,s)=>!a||!s?{}:{[a]:s[a]},A1=a=>(a?.inner||[]).reduce((s,u)=>(u.path&&(s[u.path.split("[").join(".").split("]").join("")]={id:u.message,defaultMessage:u.message,values:M1(u?.type,u?.params)}),s),{}),F1=[{label:"All",children:[{label:"images (JPEG, PNG, GIF, SVG, TIFF, ICO, DVU)",value:"images"},{label:"videos (MPEG, MP4, Quicktime, WMV, AVI, FLV)",value:"videos"},{label:"audios (MP3, WAV, OGG)",value:"audios"},{label:"files (CSV, ZIP, PDF, Excel, JSON, ...)",value:"files"}]}],R1=({intlLabel:a,name:s,onChange:u,value:p=null})=>{const{formatMessage:f}=he(),b=p===null||p?.length===0?f({id:"global.none",defaultMessage:"None"}):[...p].sort().map(h=>cn(h)).join(", "),g=a.id?f({id:a.id,defaultMessage:a.defaultMessage}):s;return l.jsxs(re.Root,{name:s,children:[l.jsx(re.Label,{children:g}),l.jsx(uc,{customizeContent:()=>b,onChange:h=>{h.length>0?u({target:{name:s,value:h,type:"allowed-types-select"}}):u({target:{name:s,value:null,type:"allowed-types-select"}})},options:F1,value:p||[]})]})},_l={biginteger:Lr,blocks:Mb,boolean:jb,collectionType:il,component:wb,contentType:il,date:La,datetime:La,decimal:Lr,dynamiczone:Tb,email:Cb,enum:ol,enumeration:ol,file:mi,files:mi,float:Lr,integer:Lr,json:al,JSON:al,media:mi,number:Lr,password:_b,relation:xb,richtext:vb,singleType:bb,string:rl,text:rl,time:La,timestamp:La,uid:yb},k1=qt(Ne)`
  svg {
    height: 100%;
    width: 100%;
  }
`,Ba=({type:a,customField:s=null,...u})=>{const p=qr("AttributeIcon",b=>b.customFields.get);let f=_l[a];if(s){const g=p(s)?.icon;g&&(f=g)}return _l[a]?l.jsx(k1,{width:"3.2rem",shrink:0,...u,"aria-hidden":!0,children:l.jsx(Ne,{tag:f})}):null},jc=qt(Ne)`
  width: 100%;
  height: 100%;
  border: 1px solid ${({theme:a})=>a.colors.neutral200};
  text-align: left;
  &:hover {
    cursor: pointer;
    background: ${({theme:a})=>a.colors.primary100};
    border: 1px solid ${({theme:a})=>a.colors.primary200};
  }
`,S1=[],$1=()=>l.jsx(ue,{grow:1,justifyContent:"flex-end",children:l.jsxs(ue,{gap:1,hasRadius:!0,background:"alternative100",padding:"0.2rem 0.4rem",children:[l.jsx(X0,{width:"1rem",height:"1rem",fill:"alternative600"}),l.jsx(ze,{textColor:"alternative600",variant:"sigma",children:"New"})]})}),N1=({type:a="text"})=>{const{formatMessage:s}=he(),{onClickSelectField:u}=ur(),p=()=>{u({attributeType:a,step:a==="component"?"1":null})};return l.jsx(jc,{padding:4,tag:"button",hasRadius:!0,type:"button",onClick:p,children:l.jsxs(ue,{children:[l.jsx(Ba,{type:a}),l.jsxs(Ne,{paddingLeft:4,width:"100%",children:[l.jsxs(ue,{justifyContent:"space-between",children:[l.jsx(ze,{fontWeight:"bold",textColor:"neutral800",children:s({id:T(`attribute.${a}`),defaultMessage:a})}),S1.includes(a)&&l.jsx($1,{})]}),l.jsx(ue,{children:l.jsx(ze,{variant:"pi",textColor:"neutral600",children:s({id:T(`attribute.${a}.description`),defaultMessage:"A type for modeling data"})})})]})]})})},E1=({attributes:a})=>l.jsx($i,{tagName:"button",children:l.jsx(ue,{direction:"column",alignItems:"stretch",gap:8,children:a.map((s,u)=>l.jsx(zn.Root,{gap:3,children:s.map(p=>l.jsx(zn.Item,{col:6,direction:"column",alignItems:"stretch",children:l.jsx(N1,{type:p})},p))},u))})}),L1=({customFieldUid:a,customField:s})=>{const{type:u,intlLabel:p,intlDescription:f}=s,{formatMessage:b}=he(),{onClickSelectCustomField:g}=ur(),h=()=>{g({attributeType:u,customFieldUid:a})};return l.jsx(jc,{padding:4,tag:"button",hasRadius:!0,type:"button",onClick:h,children:l.jsxs(ue,{children:[l.jsx(Ba,{type:u,customField:a}),l.jsxs(Ne,{paddingLeft:4,children:[l.jsx(ue,{children:l.jsx(ze,{fontWeight:"bold",children:b(p)})}),l.jsx(ue,{children:l.jsx(ze,{variant:"pi",textColor:"neutral600",children:b(f)})})]})]})})},I1=qt(Ne)`
  background: ${({theme:a})=>`linear-gradient(180deg, rgba(234, 234, 239, 0) 0%, ${a.colors.neutral150} 100%)`};
  opacity: 0.33;
`,O1=()=>l.jsx(ue,{wrap:"wrap",gap:4,children:[...Array(4)].map((a,s)=>l.jsx(I1,{height:"138px",width:"375px",hasRadius:!0},`empty-card-${s}`))}),D1=()=>{const{formatMessage:a}=he();return l.jsxs(Ne,{position:"relative",children:[l.jsx(O1,{}),l.jsx(Ne,{position:"absolute",top:6,width:"100%",children:l.jsxs(ue,{alignItems:"center",justifyContent:"center",direction:"column",children:[l.jsx(Q0,{width:"160px",height:"88px"}),l.jsx(Ne,{paddingTop:6,paddingBottom:4,children:l.jsxs(Ne,{textAlign:"center",children:[l.jsx(ze,{variant:"delta",tag:"p",textColor:"neutral600",children:a({id:T("modalForm.empty.heading"),defaultMessage:"Nothing in here yet."})}),l.jsx(Ne,{paddingTop:4,children:l.jsx(ze,{variant:"delta",tag:"p",textColor:"neutral600",children:a({id:T("modalForm.empty.sub-heading"),defaultMessage:"Find what you are looking for through a wide range of extensions."})})})]})}),l.jsx(ex,{tag:nx,to:`/marketplace?${tx.stringify({categories:["Custom fields"]})}`,variant:"secondary",startIcon:l.jsx(wn,{}),children:a({id:T("modalForm.empty.button"),defaultMessage:"Add custom fields"})})]})})]})},q1=()=>{const{formatMessage:a}=he(),s=qr("CustomFieldsList",f=>f.customFields.getAll),u=Object.entries(s());if(!u.length)return l.jsx(D1,{});const p=u.sort((f,b)=>f[1].name>b[1].name?1:-1);return l.jsx($i,{tagName:"button",children:l.jsxs(ue,{direction:"column",alignItems:"stretch",gap:3,children:[l.jsx(zn.Root,{gap:3,children:p.map(([f,b])=>l.jsx(zn.Item,{col:6,direction:"column",alignItems:"stretch",children:l.jsx(L1,{customFieldUid:f,customField:b},f)},f))}),l.jsx(ac,{href:"https://docs.strapi.io/developer-docs/latest/development/custom-fields.html",isExternal:!0,children:a({id:T("modalForm.tabs.custom.howToLink"),defaultMessage:"How to add custom fields"})})]})})},P1=({attributes:a,forTarget:s,kind:u})=>{const{formatMessage:p}=he(),f=T("modalForm.tabs.default"),b=T("modalForm.tabs.custom"),g=s.includes("component")?"component":u,h=T(`modalForm.sub-header.chooseAttribute.${g}`);return l.jsx(jn.Body,{children:l.jsxs($t.Root,{variant:"simple",defaultValue:"default",children:[l.jsxs(ue,{justifyContent:"space-between",children:[l.jsx(ze,{variant:"beta",tag:"h2",children:p({id:h,defaultMessage:"Select a field"})}),l.jsxs($t.List,{children:[l.jsx($t.Trigger,{value:"default",children:p({id:f,defaultMessage:"Default"})}),l.jsx($t.Trigger,{value:"custom",children:p({id:b,defaultMessage:"Custom"})})]})]}),l.jsx(Si,{marginBottom:6}),l.jsx($t.Content,{value:"default",children:l.jsx(E1,{attributes:a})}),l.jsx($t.Content,{value:"custom",children:l.jsx(q1,{})})]})})},U1=({intlLabel:a,name:s,options:u,onChange:p,value:f=null})=>{const{formatMessage:b}=he(),g=a.id?b({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):s,h=_=>{let j="";_==="true"&&(j=!0),_==="false"&&(j=!1),p({target:{name:s,value:j,type:"select-default-boolean"}})};return l.jsxs(re.Root,{name:s,children:[l.jsx(re.Label,{children:g}),l.jsx(Ur,{onChange:h,value:(f===null?"":f).toString(),children:u.map(({metadatas:{intlLabel:_,disabled:j,hidden:C},key:w,value:O})=>l.jsx(zr,{value:O,disabled:j,hidden:C,children:_.defaultMessage},w))})]})},z1=qt(ue)`
  position: relative;
  align-items: stretch;

  label {
    border-radius: 4px;
    max-width: 50%;
    cursor: pointer;
    user-select: none;
    flex: 1;
    border-radius: 4px;
    border: 1px solid ${({theme:a})=>a.colors.neutral200};

    ${lc()}
  }

  input {
    position: absolute;
    opacity: 0;
  }

  .option {
    height: 100%;
    border-radius: 4px;
    will-change: transform, opacity;
    background: ${({theme:a})=>a.colors.neutral0};

    .checkmark {
      position: relative;
      display: block;
      will-change: transform;
      background: ${({theme:a})=>a.colors.neutral0};
      width: ${({theme:a})=>a.spaces[5]};
      height: ${({theme:a})=>a.spaces[5]};
      border: solid 1px ${({theme:a})=>a.colors.neutral300};
      border-radius: 50%;

      &:before,
      &:after {
        content: '';
        display: block;
        border-radius: 50%;
        width: ${({theme:a})=>a.spaces[3]};
        height: ${({theme:a})=>a.spaces[3]};
        position: absolute;
        top: 3px;
        left: 3px;
      }

      &:after {
        transform: scale(0);
        transition: inherit;
        will-change: transform;
      }
    }
  }

  .container input:checked ~ div {
    background: ${({theme:a})=>a.colors.primary100};
    color: ${({theme:a})=>a.colors.primary600};
    .checkmark {
      border: solid 1px ${({theme:a})=>a.colors.primary600};
      &::after {
        background: ${({theme:a})=>a.colors.primary600};
        transform: scale(1);
      }
    }
  }
`,Ei=({intlLabel:a,name:s,onChange:u,radios:p=[],value:f})=>{const{formatMessage:b}=he();return l.jsxs(ue,{direction:"column",alignItems:"stretch",gap:2,children:[l.jsx(ze,{variant:"pi",fontWeight:"bold",textColor:"neutral800",htmlFor:s,tag:"label",children:b(a)}),l.jsx(z1,{gap:4,alignItems:"stretch",children:p.map(g=>l.jsxs("label",{htmlFor:g.value.toString(),className:"container",children:[l.jsx("input",{id:g.value.toString(),name:s,className:"option-input",checked:g.value===f,value:g.value,onChange:u,type:"radio"},g.value),l.jsx(Ne,{className:"option",padding:4,children:l.jsxs(ue,{children:[l.jsx(Ne,{paddingRight:4,children:l.jsx("span",{className:"checkmark"})}),l.jsxs(ue,{direction:"column",alignItems:"stretch",gap:2,children:[l.jsx(ze,{fontWeight:"bold",children:b(g.title)}),l.jsx(ze,{variant:"pi",textColor:"neutral600",children:b(g.description)})]})]})})]},g.value))})]})},B1=({onChange:a,name:s,intlLabel:u,...p})=>{const f=b=>{const g=b.target.value!=="false";a({target:{name:s,value:g,type:"boolean-radio-group"}})};return l.jsx(Ei,{...p,name:s,onChange:f,intlLabel:u})},W1=({error:a,intlLabel:s,modifiedData:u,name:p,onChange:f,value:b=null})=>{const{formatMessage:g}=he(),h=s.id?g({id:s.id,defaultMessage:s.defaultMessage},{...s.values}):p,_=u.type==="biginteger"?"text":"number",j=!u.type,C=a?g({id:a,defaultMessage:a}):"";return l.jsxs(ue,{direction:"column",alignItems:"stretch",gap:2,children:[l.jsx(Ni,{id:p,name:p,onCheckedChange:w=>{f({target:{name:p,value:w?_==="text"?"0":0:null}})},checked:b!==null,children:h}),b!==null&&l.jsx(Ne,{paddingLeft:6,style:{maxWidth:"200px"},children:_==="text"?l.jsxs(re.Root,{error:C,name:p,children:[l.jsx(Un,{"aria-label":h,disabled:j,onChange:f,value:b===null?"":b}),l.jsx(re.Error,{})]}):l.jsxs(re.Root,{error:C,name:p,children:[l.jsx(ic,{"aria-label":h,disabled:j,onValueChange:w=>{f({target:{name:p,value:w??0,type:_}})},value:b||0}),l.jsx(re.Error,{})]})})]})},V1=({onChange:a,...s})=>{const{formatMessage:u}=he(),{toggleNotification:p}=Za(),f=b=>{p({type:"info",message:u({id:T("contentType.kind.change.warning"),defaultMessage:"You just changed the kind of a content type: API will be reset (routes, controllers, and services will be overwritten)."})}),a(b)};return l.jsx(Ei,{...s,onChange:f})},H1=({description:a,disabled:s=!1,intlLabel:u,isCreating:p,name:f,onChange:b,value:g=!1})=>{const{formatMessage:h}=he(),[_,j]=pe.useState(!1),C=u.id?h({id:u.id,defaultMessage:u.defaultMessage},{...u.values}):f,w=a?h({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"",O=()=>{b({target:{name:f,value:!1}}),j(!1)},I=$=>{if(!$&&!p){j(!0);return}b({target:{name:f,value:!!$}})};return l.jsxs(l.Fragment,{children:[l.jsxs(re.Root,{hint:w,name:f,children:[l.jsx(Ni,{checked:g,disabled:s,onCheckedChange:I,children:C}),l.jsx(re.Hint,{})]}),l.jsx(cx.Root,{open:_,onOpenChange:$=>j($),children:l.jsx(dx,{endAction:l.jsx(qe,{onClick:O,variant:"danger",width:"100%",justifyContent:"center",children:h({id:T("popUpWarning.draft-publish.button.confirm"),defaultMessage:"Yes, disable"})}),children:h({id:T("popUpWarning.draft-publish.message"),defaultMessage:"If you disable the draft & publish, your drafts will be deleted."})})})]})},Z1=({categoryName:a,deleteCategory:s,deleteComponent:u,deleteContentType:p,isAttributeModal:f,isCustomFieldModal:b,isComponentAttribute:g,isComponentToDzModal:h,isContentTypeModal:_,isCreatingComponent:j,isCreatingComponentAttribute:C,isCreatingComponentInDz:w,isCreatingComponentWhileAddingAField:O,isCreatingContentType:I,isCreatingDz:$,isComponentModal:z,isDzAttribute:X,isEditingAttribute:ee,isEditingCategory:N,isInFirstComponentStep:S,onSubmitAddComponentAttribute:le,onSubmitAddComponentToDz:ie,onSubmitCreateContentType:Re,onSubmitCreateComponent:ae,onSubmitCreateDz:ke,onSubmitEditAttribute:se,onSubmitEditCategory:tt,onSubmitEditComponent:ve,onSubmitEditContentType:xe,onSubmitEditCustomFieldAttribute:Le,onSubmitEditDz:ge,onClickFinish:E})=>{const{formatMessage:D}=he();return h?w?l.jsx(qe,{variant:"secondary",type:"submit",onClick:q=>{q.preventDefault(),ie(q,!0)},startIcon:l.jsx(wn,{}),children:D({id:T("form.button.add-first-field-to-created-component"),defaultMessage:"Add first field to the component"})}):l.jsx(qe,{variant:"default",type:"submit",onClick:q=>{q.preventDefault(),ie(q,!1)},children:D({id:"global.finish",defaultMessage:"Finish"})}):f&&X&&!$?l.jsx(qe,{variant:"default",type:"submit",onClick:q=>{q.preventDefault(),E(),ge(q,!1)},children:D({id:"global.finish",defaultMessage:"Finish"})}):f&&X&&$?l.jsx(l.Fragment,{children:l.jsx(qe,{variant:"secondary",type:"submit",onClick:q=>{q.preventDefault(),ke(q,!0)},startIcon:l.jsx(wn,{}),children:D({id:T("form.button.add-components-to-dynamiczone"),defaultMessage:"Add components to the zone"})})}):f&&g?S?l.jsx(qe,{variant:"secondary",type:"submit",onClick:q=>{q.preventDefault(),le(q,!0)},children:D(C?{id:T("form.button.configure-component"),defaultMessage:"Configure the component"}:{id:T("form.button.select-component"),defaultMessage:"Configure the component"})}):l.jsxs(ue,{gap:2,children:[l.jsx(qe,{variant:"secondary",type:"submit",onClick:q=>{q.preventDefault(),le(q,!0)},startIcon:l.jsx(wn,{}),children:D(O?{id:T("form.button.add-first-field-to-created-component"),defaultMessage:"Add first field to the component"}:{id:T("form.button.add-field"),defaultMessage:"Add another field"})}),l.jsx(qe,{variant:"default",type:"button",onClick:q=>{q.preventDefault(),E(),le(q,!1)},children:D({id:"global.finish",defaultMessage:"Finish"})})]}):f&&!g&&!X?l.jsxs(ue,{gap:2,children:[l.jsx(qe,{type:ee?"button":"submit",variant:"secondary",onClick:q=>{q.preventDefault(),se(q,!0)},startIcon:l.jsx(wn,{}),children:D({id:T("form.button.add-field"),defaultMessage:"Add another field"})}),l.jsx(qe,{type:ee?"submit":"button",variant:"default",onClick:q=>{q.preventDefault(),E(),se(q,!1)},children:D({id:"global.finish",defaultMessage:"Finish"})})]}):_?l.jsxs(ue,{gap:2,children:[!I&&l.jsxs(l.Fragment,{children:[l.jsx(qe,{type:"button",variant:"danger",onClick:q=>{q.preventDefault(),p()},children:D({id:"global.delete",defaultMessage:"Delete"})}),l.jsx(qe,{type:"submit",variant:"default",onClick:q=>{q.preventDefault(),xe(q,!1)},children:D({id:"global.finish",defaultMessage:"Finish"})})]}),I&&l.jsx(qe,{type:"submit",variant:"secondary",onClick:q=>{q.preventDefault(),Re(q,!0)},children:D({id:"global.continue",defaultMessage:"Continue"})})]}):z?l.jsxs(ue,{gap:2,children:[!j&&l.jsxs(l.Fragment,{children:[l.jsx(qe,{type:"button",variant:"danger",onClick:q=>{q.preventDefault(),u()},children:D({id:"global.delete",defaultMessage:"Delete"})}),l.jsx(qe,{type:"submit",variant:"default",onClick:q=>{q.preventDefault(),ve(q,!1)},children:D({id:"global.finish",defaultMessage:"Finish"})})]}),j&&l.jsx(qe,{type:"submit",variant:"secondary",onClick:q=>{q.preventDefault(),ae(q,!0)},children:D({id:"global.continue",defaultMessage:"Continue"})})]}):N?l.jsxs(ue,{gap:2,children:[l.jsx(qe,{type:"button",variant:"danger",onClick:q=>{q.preventDefault(),a&&s(a)},children:D({id:"global.delete",defaultMessage:"Delete"})}),l.jsx(qe,{type:"submit",variant:"default",onClick:q=>{q.preventDefault(),tt(q)},children:D({id:"global.finish",defaultMessage:"finish"})})]}):b?l.jsxs(ue,{gap:2,children:[l.jsx(qe,{type:ee?"button":"submit",variant:"secondary",onClick:q=>{q.preventDefault(),Le(q,!0)},startIcon:l.jsx(wn,{}),children:D({id:T("form.button.add-field"),defaultMessage:"Add another field"})}),l.jsx(qe,{type:ee?"submit":"button",variant:"default",onClick:q=>{q.preventDefault(),E(),Le(q,!1)},children:D({id:"global.finish",defaultMessage:"Finish"})})]}):null},G1=({actionType:a=null,attributeName:s,attributeType:u,categoryName:p,contentTypeKind:f,dynamicZoneTarget:b,forTarget:g,modalType:h=null,targetUid:_,customFieldUid:j=null,showBackLink:C=!1})=>{const{formatMessage:w}=he(),{modifiedData:O}=Mn(),{onOpenModalAddField:I}=ur();let $="component",z=[];const X=O?.[g]?.[_]||O?.[g]||null,ee=X?.schema.displayName;if(h==="contentType"&&($=f),["component","editCategory"].includes(h||"")&&($="component"),["component","contentType"].includes(h||"")){let S=T(`modalForm.component.header-${a}`);return h==="contentType"&&(S=T(`modalForm.${f}.header-create`)),a==="edit"&&(S=T("modalForm.header-edit")),l.jsx(jn.Header,{children:l.jsxs(ue,{children:[l.jsx(Ne,{children:l.jsx(Ba,{type:$})}),l.jsx(Ne,{paddingLeft:3,children:l.jsx(jn.Title,{children:w({id:S},{name:ee})})})]})})}return z=[{label:ee,info:{category:X?.category||null,name:X?.schema.displayName}}],h==="chooseAttribute"&&($=["component","components"].includes(g)?"component":X.schema.kind),h==="addComponentToDynamicZone"&&($="dynamiczone",z.push({label:b})),(h==="attribute"||h==="customField")&&($=u,z.push({label:s})),h==="editCategory"&&(z=[{label:w({id:T("modalForm.header.categories"),defaultMessage:"Categories"})},{label:p}]),l.jsx(jn.Header,{children:l.jsxs(ue,{gap:3,children:[C&&l.jsx(ac,{"aria-label":w({id:T("modalForm.header.back"),defaultMessage:"Back"}),startIcon:l.jsx(Jl,{}),onClick:()=>I({forTarget:g,targetUid:_}),href:"#back",isExternal:!1}),l.jsx(Ba,{type:$,customField:j}),l.jsx(K0,{label:z.map(({label:S})=>S).join(","),children:z.map(({label:S,info:le},ie,Re)=>{if(S=cn(S),!S)return null;const ae=`${S}.${ie}`;return le?.category&&(S=`${S} (${cn(le.category)} - ${cn(le.name)})`),l.jsx(J0,{isCurrent:ie===Re.length-1,children:S},ae)})})]})})},Y1=({modalType:a,forTarget:s,kind:u,actionType:p,step:f})=>{switch(a){case"chooseAttribute":return T(`modalForm.sub-header.chooseAttribute.${s?.includes("component")?"component":u||"collectionType"}`);case"attribute":return T(`modalForm.sub-header.attribute.${p}${f!=="null"&&f!==null&&p!=="edit"?".step":""}`);case"customField":return T(`modalForm.sub-header.attribute.${p}`);case"addComponentToDynamicZone":return T("modalForm.sub-header.addComponentToDynamicZone");default:return T("configurations")}},K1=({actionType:a,modalType:s,forTarget:u,kind:p,step:f,attributeType:b,attributeName:g,customField:h})=>{const{formatMessage:_}=he(),j=s==="customField"?h?.intlLabel:{id:T(`attribute.${b}`)};return l.jsxs(ue,{direction:"column",alignItems:"flex-start",paddingBottom:1,gap:1,children:[l.jsx(ze,{tag:"h2",variant:"beta",children:_({id:Y1({actionType:a,forTarget:u,kind:p,step:f,modalType:s}),defaultMessage:"Add new field"},{type:j?cn(_(j)):"",name:cn(g),step:f})}),l.jsx(ze,{variant:"pi",textColor:"neutral600",children:_({id:T(`attribute.${b}.description`),defaultMessage:"A type for modeling data"})})]})},Mc={alien:F0,apps:A0,archive:M0,arrowDown:j0,arrowLeft:Jl,arrowRight:w0,arrowUp:T0,attachment:C0,bell:_0,bold:x0,book:v0,briefcase:b0,brush:hi,bulletList:y0,calendar:g0,car:h0,cast:m0,chartBubble:p0,chartCircle:f0,chartPie:d0,check:c0,clock:l0,cloud:u0,code:s0,cog:i0,collapse:o0,command:a0,connector:r0,crop:n0,crown:t0,cup:e0,cursor:Qv,dashboard:Xv,database:Jv,discuss:Kv,doctor:Yv,earth:Gv,emotionHappy:Zv,emotionUnhappy:Hv,envelop:Vv,exit:Wv,expand:Bv,eye:Kl,feather:sl,file:zv,fileError:Uv,filePdf:Pv,filter:qv,folder:Dv,gate:Ov,gift:Iv,globe:Lv,grid:Ev,handHeart:Nv,hashtag:$v,headphone:Sv,heart:kv,house:Rv,information:Fv,italic:Av,key:Mv,landscape:jv,layer:wv,layout:Tv,lightbulb:Cv,link:_v,lock:xv,magic:vv,manyToMany:Yl,manyToOne:Gl,manyWays:Zl,medium:bv,message:yv,microphone:gv,monitor:hv,moon:mv,music:pv,oneToMany:Hl,oneToOne:Vl,oneWay:Wl,paint:hi,paintBrush:hi,paperPlane:fv,pencil:dv,phone:cv,picture:lv,pin:uv,pinMap:sv,plane:iv,play:ov,plus:wn,priceTag:av,puzzle:rv,question:nv,quote:tv,refresh:ev,restaurant:Qb,rocket:Xb,rotate:Jb,scissors:Kb,search:Bl,seed:Yb,server:Gb,shield:Zb,shirt:Hb,shoppingCart:Vb,slideshow:Wb,stack:Bb,star:zb,store:Ub,strikeThrough:Pb,sun:qb,television:Db,thumbDown:Ob,thumbUp:Ib,train:Lb,twitter:Eb,typhoon:Nb,underline:$b,user:Sb,volumeMute:kb,volumeUp:Rb,walk:Fb,wheelchair:Ab,write:sl},J1=qt(ue)`
  label {
    ${lc()}
    border-radius: ${({theme:a})=>a.borderRadius};
    border: 1px solid ${({theme:a})=>a.colors.neutral100};
  }
`,X1=({iconKey:a,name:s,onChange:u,isSelected:p,ariaLabel:f})=>{const b=Mc[a];return l.jsx(re.Root,{name:s,required:!1,children:l.jsxs(re.Label,{children:[l.jsxs(vx,{children:[f,l.jsx(re.Input,{type:"radio",checked:p,onChange:u,value:a,"aria-checked":p})]}),l.jsx(ue,{padding:2,cursor:"pointer",hasRadius:!0,background:p?"primary200":void 0,children:l.jsx(b,{fill:p?"primary600":"neutral300"})})]})})},Q1=({intlLabel:a,name:s,onChange:u,value:p=""})=>{const{formatMessage:f}=he(),[b,g]=pe.useState(!1),[h,_]=pe.useState(""),j=Object.keys(Mc),[C,w]=pe.useState(j),O=pe.useRef(null),I=pe.useRef(null),$=()=>{g(!b)},z=({target:{value:N}})=>{_(N),w(()=>j.filter(S=>S.toLowerCase().includes(N.toLowerCase())))},X=()=>{$(),_(""),w(j)},ee=()=>{u({target:{name:s,value:""}})};return pe.useEffect(()=>{b&&I.current?.focus()},[b]),l.jsxs(l.Fragment,{children:[l.jsxs(ue,{justifyContent:"space-between",paddingBottom:2,children:[l.jsx(ze,{variant:"pi",fontWeight:"bold",textColor:"neutral800",tag:"label",children:f(a)}),l.jsxs(ue,{gap:1,children:[b?l.jsx(gx,{ref:I,name:"searchbar",placeholder:f({id:T("ComponentIconPicker.search.placeholder"),defaultMessage:"Search for an icon"}),onBlur:()=>{h||$()},onChange:z,value:h,onClear:X,clearLabel:f({id:T("IconPicker.search.clear.label"),defaultMessage:"Clear the icon search"}),children:f({id:T("IconPicker.search.placeholder.label"),defaultMessage:"Search for an icon"})}):l.jsx(dl,{ref:O,onClick:$,withTooltip:!1,label:f({id:T("IconPicker.search.button.label"),defaultMessage:"Search icon button"}),variant:"ghost",children:l.jsx(Bl,{})}),p&&l.jsx(yx,{label:f({id:T("IconPicker.remove.tooltip"),defaultMessage:"Remove the selected icon"}),children:l.jsx(dl,{onClick:ee,withTooltip:!1,label:f({id:T("IconPicker.remove.button"),defaultMessage:"Remove the selected icon"}),variant:"ghost",children:l.jsx(bx,{})})})]})]}),l.jsx(J1,{position:"relative",padding:1,background:"neutral100",hasRadius:!0,wrap:"wrap",gap:2,maxHeight:"126px",overflow:"auto",textAlign:"center",children:C.length>0?C.map(N=>l.jsx(X1,{iconKey:N,name:s,onChange:u,isSelected:N===p,ariaLabel:f({id:T("IconPicker.icon.label"),defaultMessage:"Select {icon} icon"},{icon:N})},N)):l.jsx(Ne,{padding:4,grow:2,children:l.jsx(ze,{variant:"delta",textColor:"neutral600",textAlign:"center",children:f({id:T("IconPicker.emptyState.label"),defaultMessage:"No icon found"})})})})]})},eC=({description:a,error:s,intlLabel:u,modifiedData:p,name:f,onChange:b,value:g})=>{const{formatMessage:h}=he(),_=pe.useRef(b),j=p?.displayName||"";pe.useEffect(()=>{if(j){const I=sc(j);try{const $=Ua(I,2);_.current({target:{name:f,value:$}})}catch{_.current({target:{name:f,value:I}})}}else _.current({target:{name:f,value:""}})},[j,f]);const C=s?h({id:s,defaultMessage:s}):"",w=a?h({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"",O=h(u);return l.jsxs(re.Root,{error:C,hint:w,name:f,children:[l.jsx(re.Label,{children:O}),l.jsx(Un,{onChange:b,value:g||""}),l.jsx(re.Error,{})]})},Cl=a=>{if(a instanceof Date&&Tl(a))return a;if(typeof a=="string"||typeof a=="number"){const s=new Date(a);if(Tl(s))return s}},Tl=a=>!isNaN(a.getTime()),tC=a=>{const[s,u]=a.split(":");return`${s}:${u}`},nC=a=>a.split(":").length===2?`${a}:00.000`:a,rC=a=>{if(a)return a.split(":").length>2?tC(a):a},aC=a=>{if(a)return nC(a)},oC=({value:a})=>rC(a),wl=(a,s,u,p)=>{const f=aC(p);a({target:{name:s,value:f,type:u}})},iC=({autoComplete:a,customInputs:s,description:u,disabled:p,intlLabel:f,labelAction:b,error:g,name:h,onChange:_,options:j=[],placeholder:C,required:w,step:O,type:I,value:$,isNullable:z,attribute:X,...ee})=>{const{formatMessage:N}=he(),S=(E,D)=>{if(E&&(D==="minLength"&&D in E||D==="maxLength"&&D in E||D==="max"&&D in E||D==="min"&&D in E))return E[D]},{hint:le}=sC({description:u,fieldSchema:{minLength:S(X,"minLength"),maxLength:S(X,"maxLength"),max:S(X,"max"),min:S(X,"min")},type:X?.type||I}),[ie,Re]=pe.useState(!1),ae=s?s[I]:null,ke=$??void 0,se=ke??"";function tt(E){if(!E)return null;if(typeof E=="string")return N({id:E,defaultMessage:E});const D={...E.values};return N({id:E.id,defaultMessage:E?.defaultMessage??E.id},D)}const ve=tt(g)??void 0;if(ae)return l.jsx(ae,{...ee,attribute:X,description:u,hint:le,disabled:p,intlLabel:f,labelAction:b,error:ve||"",name:h,onChange:_,options:j,required:w,placeholder:C,type:I,value:ke});const xe=f.id?N({id:f.id,defaultMessage:f.defaultMessage},{...f.values}):h,Le=C?N({id:C.id,defaultMessage:C.defaultMessage},{...C.values}):"",ge=()=>{switch(I){case"json":return l.jsx(lx,{value:ke,disabled:p,onChange:E=>{const D=X&&"required"in X&&!X?.required&&!E.length?null:E;_({target:{name:h,value:D}},!1)},minHeight:"25.2rem",maxHeight:"50.4rem"});case"bool":return l.jsx(ux,{checked:$===null?null:$||!1,disabled:p,offLabel:N({id:"app.components.ToggleCheckbox.off-label",defaultMessage:"False"}),onLabel:N({id:"app.components.ToggleCheckbox.on-label",defaultMessage:"True"}),onChange:E=>{_({target:{name:h,value:E.target.checked}})}});case"checkbox":return l.jsx(Ni,{disabled:p,onCheckedChange:E=>{_({target:{name:h,value:E}})},checked:!!ke,children:xe});case"datetime":{const E=Cl(ke);return l.jsx(sx,{clearLabel:N({id:"clearLabel",defaultMessage:"Clear"}),disabled:p,onChange:D=>{const q=D?D.toISOString():null;_({target:{name:h,value:q,type:I}})},onClear:()=>_({target:{name:h,value:null,type:I}}),placeholder:Le,value:E})}case"date":{const E=Cl(ke);return l.jsx(ix,{clearLabel:N({id:"clearLabel",defaultMessage:"Clear"}),disabled:p,onChange:D=>{_({target:{name:h,value:D?Yx(D,{representation:"date"}):null,type:I}})},onClear:()=>_({target:{name:h,value:null,type:I}}),placeholder:Le,value:E})}case"number":return l.jsx(ic,{disabled:p,onValueChange:E=>{_({target:{name:h,value:E,type:I}})},placeholder:Le,step:O,value:ke});case"email":return l.jsx(Un,{autoComplete:a,disabled:p,onChange:E=>{_({target:{name:h,value:E.target.value,type:I}})},placeholder:Le,type:"email",value:se});case"timestamp":case"text":case"string":return l.jsx(Un,{autoComplete:a,disabled:p,onChange:E=>{_({target:{name:h,value:E.target.value,type:I}})},placeholder:Le,type:"text",value:se});case"password":return l.jsx(Un,{autoComplete:a,disabled:p,endAction:l.jsx("button",{"aria-label":N({id:"Auth.form.password.show-password",defaultMessage:"Show password"}),onClick:()=>{Re(E=>!E)},style:{border:"none",padding:0,background:"transparent"},type:"button",children:ie?l.jsx(Kl,{fill:"neutral500"}):l.jsx(ox,{fill:"neutral500"})}),onChange:E=>{_({target:{name:h,value:E.target.value,type:I}})},placeholder:Le,type:ie?"text":"password",value:se});case"select":return l.jsx(Ur,{disabled:p,onChange:E=>{_({target:{name:h,value:E,type:"select"}})},placeholder:Le,value:ke,children:j.map(({metadatas:{intlLabel:E,disabled:D,hidden:q},key:Yt,value:dn})=>l.jsx(zr,{value:dn,disabled:D,hidden:q,children:N(E)},Yt))});case"textarea":return l.jsx(oc,{disabled:p,onChange:E=>_({target:{name:h,value:E.target.value,type:I}}),placeholder:Le,value:se});case"time":{const E=oC({value:ke});return l.jsx(ax,{clearLabel:N({id:"clearLabel",defaultMessage:"Clear"}),disabled:p,onChange:D=>wl(_,h,I,D),onClear:()=>wl(_,h,I,void 0),value:E})}default:return l.jsx(Un,{disabled:!0,placeholder:"Not supported",type:"text",value:""})}};return l.jsxs(re.Root,{error:ve,name:h,hint:le,required:w,children:[I!=="checkbox"?l.jsx(re.Label,{action:b,children:xe}):null,ge(),l.jsx(re.Error,{}),l.jsx(re.Hint,{})]})},sC=({description:a,fieldSchema:s,type:u})=>{const{formatMessage:p}=he(),f=()=>a?.id?p({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"";return{hint:(()=>{const{maximum:g,minimum:h}=lC(s),_=uC({type:u,minimum:h,maximum:g}),j=typeof h=="number",C=typeof g=="number",w=C&&j,O=C||j;return!a?.id&&!O?"":p({id:"content-manager.form.Input.hint.text",defaultMessage:"{min, select, undefined {} other {min. {min}}}{divider}{max, select, undefined {} other {max. {max}}}{unit}{br}{description}"},{min:h,max:g,description:f(),unit:_?.message&&O?p(_.message,_.values):null,divider:w?p({id:"content-manager.form.Input.hint.minMaxDivider",defaultMessage:" / "}):null,br:O?l.jsx("br",{}):null})})()}},uC=({type:a,minimum:s,maximum:u})=>{if(a&&["biginteger","integer","number"].includes(a))return{};const p=Math.max(s||0,u||0);return{message:{id:"content-manager.form.Input.hint.character.unit",defaultMessage:"{maxValue, plural, one { character} other { characters}}"},values:{maxValue:p}}},lC=a=>{if(!a)return{maximum:void 0,minimum:void 0};const{minLength:s,maxLength:u,max:p,min:f}=a;let b,g;const h=Number(f),_=Number(s);Number.isNaN(h)?Number.isNaN(_)||(b=_):b=h;const j=Number(p),C=Number(u);return Number.isNaN(j)?Number.isNaN(C)||(g=C):g=j,{maximum:g,minimum:b}},Ac=pe.memo(iC,Ga),cC=({oneThatIsCreatingARelationWithAnother:a,target:s})=>{const{contentTypes:u,sortedContentTypesList:p}=Mn(),f=Ha(),b=p.filter(wc),{plugin:g=null,schema:{displayName:h}={displayName:"error"}}=u?.[s]??{},_=({uid:j,plugin:C,title:w,restrictRelationsTo:O})=>()=>{const I=C?`${C}_${w}`:w;f({type:mx,target:{value:j,oneThatIsCreatingARelationWithAnother:a,selectedContentTypeFriendlyName:I,targetContentTypeAllowedRelations:O}})};return l.jsxs(Pa.Root,{children:[l.jsx(dC,{children:`${h} ${g?`(from: ${g})`:""}`}),l.jsx(Pa.Content,{zIndex:"popover",children:b.map(({uid:j,title:C,restrictRelationsTo:w,plugin:O})=>l.jsxs(Pa.Item,{onSelect:_({uid:j,plugin:O,title:C,restrictRelationsTo:w}),children:[C," ",O&&l.jsxs(l.Fragment,{children:["(from: ",O,")"]})]},j))})]})},dC=qt(Pa.Trigger)`
  max-width: 16.8rem;
  span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }
`,jl=({disabled:a=!1,error:s,header:u,isMain:p=!1,name:f,onChange:b,oneThatIsCreatingARelationWithAnother:g="",target:h="",value:_=""})=>l.jsxs(Ne,{background:"neutral100",hasRadius:!0,borderColor:"neutral200",children:[l.jsx(ue,{paddingTop:p?4:1,paddingBottom:p?3:1,justifyContent:"center",children:p?l.jsx(ze,{variant:"pi",fontWeight:"bold",textColor:"neutral800",children:u}):l.jsx(cC,{target:h,oneThatIsCreatingARelationWithAnother:g})}),l.jsx(Si,{background:"neutral200"}),l.jsx(Ne,{padding:4,children:l.jsx(Ac,{disabled:a,error:s?.id||null,intlLabel:{id:T("form.attribute.item.defineRelation.fieldName"),defaultMessage:"Field name"},name:f,onChange:b,type:"text",value:_})})]}),fC=qt(Ne)`
  position: relative;
  width: 100%;
  &::before {
    content: '';
    position: absolute;
    top: calc(50% - 0px);
    height: 2px;
    width: 100%;
    background-color: ${({theme:a})=>a.colors.primary600};
    z-index: 0;
  }
`,pC=qt(Ne)`
  background: ${({theme:a,$isSelected:s})=>a.colors[s?"primary100":"neutral0"]};
  border: 1px solid
    ${({theme:a,$isSelected:s})=>a.colors[s?"primary700":"neutral200"]};
  border-radius: ${({theme:a})=>a.borderRadius};
  z-index: 1;
  flex: 0 0 2.4rem;
  svg {
    width: 2.4rem;
    height: 2.4rem;
    max-width: unset;
    path {
      fill: ${({theme:a,$isSelected:s})=>a.colors[s?"primary700":"neutral500"]};
    }
  }
  &:disabled {
    cursor: not-allowed;
  }
`,mC=qt(ue)`
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
`,hC={oneWay:Wl,oneToOne:Vl,oneToMany:Hl,manyToOne:Gl,manyToMany:Yl,manyWay:Zl},gC=({naturePickerType:a,oneThatIsCreatingARelationWithAnother:s,relationType:u,target:p})=>{const f=Ha(),{formatMessage:b}=he(),{contentTypes:g,modifiedData:h}=Mn(),_=["oneWay","oneToOne","oneToMany","manyToOne","manyToMany","manyWay"],j=["oneWay","manyWay"],w=(a==="contentType"?G(h,[a,"schema","kind"],""):a)==="collectionType"?_:j,O=u==="manyToOne",I=G(g,[p,"schema","displayName"],"unknown"),$=O?I:s,z=O?s:I,X=Ua($,u==="manyToMany"?2:1),ee=G(g,[p,"schema","restrictRelationsTo"],null),N=Ua(z,["manyToMany","oneToMany","manyToOne","manyWay"].includes(u)?2:1);return u?l.jsxs(ue,{style:{flex:1},children:[l.jsx(fC,{children:l.jsx(ue,{paddingLeft:9,paddingRight:9,paddingTop:1,justifyContent:"center",children:l.jsx($i,{tagName:"button",children:l.jsx(ue,{gap:3,children:w.map(S=>{const le=hC[S],ie=ee===null||ee.includes(S);return l.jsx(pC,{tag:"button",$isSelected:u===S,disabled:!ie,onClick:()=>{ie&&f({type:hx,target:{oneThatIsCreatingARelationWithAnother:s,targetContentType:p,value:S}})},padding:2,type:"button",children:l.jsx(le,{},S)},S)})})})})}),l.jsxs(mC,{justifyContent:"center",children:[l.jsxs(ze,{children:[vl(X,{length:24})," "]}),l.jsxs(ze,{textColor:"primary600",children:[b({id:T(`relation.${u}`)})," "]}),l.jsx(ze,{children:vl(N,{length:24})})]})]}):null},yC=({formErrors:a,mainBoxHeader:s,modifiedData:u,naturePickerType:p,onChange:f})=>{const b=ki(u.relation,u.targetAttribute);return l.jsxs(ue,{style:{position:"relative"},children:[l.jsx(jl,{isMain:!0,header:s,error:a?.name||null,name:"name",onChange:f,value:u?.name||""}),l.jsx(gC,{naturePickerType:p,oneThatIsCreatingARelationWithAnother:s,relationType:b,target:u.target}),l.jsx(jl,{disabled:["oneWay","manyWay"].includes(b),error:a?.targetAttribute||null,name:"targetAttribute",onChange:f,oneThatIsCreatingARelationWithAnother:s,target:u.target,value:u?.targetAttribute||""})]})},bC=({error:a=null,intlLabel:s,name:u,onChange:p,value:f=void 0,isCreating:b,dynamicZoneTarget:g})=>{const{formatMessage:h}=he(),{allComponentsCategories:_}=Mn(),[j,C]=pe.useState(_),w=a?h({id:a,defaultMessage:a}):"",O=h(s),I=z=>{p({target:{name:u,value:z,type:"select-category"}})},$=z=>{C(X=>[...X,z]),I(z)};return l.jsxs(re.Root,{error:w,name:u,children:[l.jsx(re.Label,{children:O}),l.jsx(fx,{disabled:!b&&!g,onChange:I,onCreateOption:$,value:f,creatable:!0,children:j.map(z=>l.jsx(px,{value:z,children:z},z))}),l.jsx(re.Error,{})]})},Ai=(a,s)=>s.find(u=>u.component===a),Fc=(a,s,u=0)=>{const p=Ai(a,s);if(!p||!p.childComponents||p.childComponents.length===0)return u;let f=u;return p.childComponents.forEach(b=>{const g=Fc(b.component,s,u+1);g>f&&(f=g)}),f},Rc=(a,s)=>{const u=(b,g)=>{const h=[];if(h.push(g),!b.uidsOfAllParents)return h;for(const _ of b.uidsOfAllParents){const j=Ai(_,s);j&&h.push(...u(j,g+1))}return h},p=Ai(a,s);return p?Math.max(...u(p,1)):0},vC=({error:a=null,intlLabel:s,isAddingAComponentToAnotherComponent:u,isCreating:p,isCreatingComponentWhileAddingAField:f,componentToCreate:b,name:g,onChange:h,targetUid:_,forTarget:j,value:C})=>{const{formatMessage:w}=he(),O=a?w({id:a,defaultMessage:a}):"",I=w(s),{componentsGroupedByCategory:$,componentsThatHaveOtherComponentInTheirAttributes:z,nestedComponents:X}=Mn(),ee=["component","components"].includes(j);let N=Object.entries($).reduce((S,le)=>{const[ie,Re]=le,ae=Re.map(ke=>({uid:ke.uid,label:ke.schema.displayName,categoryName:ie}));return[...S,...ae]},[]);return u&&(N=N.filter(({uid:S})=>{const le=Fc(S,z),ie=Rc(_,X);return le+ie<=rc})),ee&&(N=N.filter(S=>S.uid!==_)),f&&(N=[{uid:C,label:b?.displayName,categoryName:b?.category}]),l.jsxs(re.Root,{error:O,name:g,children:[l.jsx(re.Label,{children:I}),l.jsx(Ur,{disabled:f||!p,onChange:S=>{h({target:{name:g,value:S,type:"select-category"}})},value:C||"",children:N.map(S=>l.jsx(zr,{value:S.uid,children:`${S.categoryName} - ${S.label}`},S.uid))}),l.jsx(re.Error,{})]})},xC=({dynamicZoneTarget:a,intlLabel:s,name:u,onChange:p,value:f})=>{const{formatMessage:b}=he(),{componentsGroupedByCategory:g,modifiedData:h}=Mn(),j=Mi(h.contentType.schema.attributes,a)?.components||[],C=Object.keys(g).reduce((I,$)=>{const z=g[$].filter(({uid:X})=>!j.includes(X));return z.length>0&&(I[$]=z),I},{}),w=Object.entries(C).reduce((I,$)=>{const[z,X]=$,ee={label:z,children:X.map(({uid:N,schema:{displayName:S}})=>({label:S,value:N}))};return I.push(ee),I},[]),O=b({id:T("components.SelectComponents.displayed-value"),defaultMessage:"{number, plural, =0 {# components} one {# component} other {# components}} selected"},{number:f?.length??0});return l.jsxs(re.Root,{name:u,children:[l.jsx(re.Label,{children:b(s)}),l.jsx(uc,{id:"select1",customizeContent:()=>O,onChange:I=>{p({target:{name:u,value:I,type:"select-components"}})},options:w,value:f||[]})]})},_C=({intlLabel:a,error:s=void 0,modifiedData:u,name:p,onChange:f,options:b,value:g=""})=>{const{formatMessage:h}=he(),_=h(a),j=s?h({id:s,defaultMessage:s}):"",C=w=>{f({target:{name:p,value:w,type:"select"}}),g&&u.default!==void 0&&u.default!==null&&f({target:{name:"default",value:null}})};return l.jsxs(re.Root,{error:j,name:p,children:[l.jsx(re.Label,{children:_}),l.jsx(Ur,{onChange:C,value:g||"",children:b.map(({metadatas:{intlLabel:w,disabled:O,hidden:I},key:$,value:z})=>l.jsx(zr,{value:z,disabled:O,hidden:I,children:h({id:w.id,defaultMessage:w.defaultMessage},w.values)},$))}),l.jsx(re.Error,{})]})},kc=({intlLabel:a,error:s=void 0,modifiedData:u,name:p,onChange:f,options:b,value:g=""})=>{const{formatMessage:h}=he(),_=h(a),j=s?h({id:s,defaultMessage:s}):"",C=w=>{f({target:{name:p,value:w,type:"select"}}),g&&(w==="biginteger"&&g!=="biginteger"&&(u.default!==void 0&&u.default!==null&&f({target:{name:"default",value:null}}),u.max!==void 0&&u.max!==null&&f({target:{name:"max",value:null}}),u.min!==void 0&&u.min!==null&&f({target:{name:"min",value:null}})),typeof w=="string"&&["decimal","float","integer"].includes(w)&&g==="biginteger"&&(u.default!==void 0&&u.default!==null&&f({target:{name:"default",value:null}}),u.max!==void 0&&u.max!==null&&f({target:{name:"max",value:null}}),u.min!==void 0&&u.min!==null&&f({target:{name:"min",value:null}})))};return l.jsxs(re.Root,{error:j,name:p,children:[l.jsx(re.Label,{children:_}),l.jsx(Ur,{onChange:C,value:g||"",children:b.map(({metadatas:{intlLabel:w,disabled:O,hidden:I},key:$,value:z})=>l.jsx(zr,{value:z,disabled:O,hidden:I,children:h(w)},$))}),l.jsx(re.Error,{})]})};kc.defaultProps={error:void 0,value:""};const CC=({description:a=null,error:s=null,intlLabel:u,modifiedData:p,name:f,onChange:b,value:g=null})=>{const{formatMessage:h}=he(),_=pe.useRef(b),j=p?.displayName||"";pe.useEffect(()=>{j?_.current({target:{name:f,value:sc(j)}}):_.current({target:{name:f,value:""}})},[j,f]);const C=s?h({id:s,defaultMessage:s}):"",w=a?h({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"",O=h(u);return l.jsxs(re.Root,{error:C,hint:w,name:f,children:[l.jsx(re.Label,{children:O}),l.jsx(Un,{onChange:b,value:g||""}),l.jsx(re.Error,{}),l.jsx(re.Hint,{})]})},Ml=({form:a,formErrors:s,genericInputProps:u,modifiedData:p,onChange:f})=>{const{formatMessage:b}=he();return l.jsx(l.Fragment,{children:a.map((g,h)=>g.items.length===0?null:l.jsxs(Ne,{children:[g.sectionTitle&&l.jsx(Ne,{paddingBottom:4,children:l.jsx(ze,{variant:"delta",tag:"h3",children:b(g.sectionTitle)})}),l.jsx(zn.Root,{gap:4,children:g.items.map((_,j)=>{const C=`${h}.${j}`,w=G(p,_.name,void 0),O=Object.keys(s).find($=>$===_.name),I=O?s[O].id:G(s,[..._.name.split(".").filter($=>$!=="componentToCreate"),"id"],null);return _.type==="pushRight"?l.jsx(zn.Item,{col:_.size||6,direction:"column",alignItems:"stretch",children:l.jsx("div",{})},_.name||C):l.jsx(zn.Item,{col:_.size||6,direction:"column",alignItems:"stretch",children:l.jsx(Ac,{..._,...u,error:I,onChange:f,value:w})},_.name||C)})})]},h))})},TC=({description:a=null,disabled:s=!1,error:u="",intlLabel:p,labelAction:f,name:b,onChange:g,placeholder:h=null,value:_=""})=>{const{formatMessage:j}=he(),C=u?j({id:u,defaultMessage:u}):"",w=a?j({id:a.id,defaultMessage:a.defaultMessage},{...a.values}):"",O=j(p),I=h?j({id:h.id,defaultMessage:h.defaultMessage},{...h.values}):"",$=Array.isArray(_)?_.join(`
`):"",z=X=>{const ee=X.target.value.split(`
`);g({target:{name:b,value:ee}})};return l.jsxs(re.Root,{error:C,hint:w,name:b,children:[l.jsx(re.Label,{action:f,children:O}),l.jsx(oc,{disabled:s,onChange:z,placeholder:I,value:$}),l.jsx(re.Error,{}),l.jsx(re.Hint,{})]})},Gt={name:"name",type:"text",intlLabel:{id:"global.name",defaultMessage:"Name"},description:{id:T("modalForm.attribute.form.base.name.description"),defaultMessage:"No space is allowed for the name of the attribute"}},wC={sections:[{sectionTitle:null,items:[Gt]}]},sr={base(a=""){return[{sectionTitle:null,items:[{name:`${a}displayName`,type:"text",intlLabel:{id:T("contentType.displayName.label"),defaultMessage:"Display Name"}},{name:`${a}category`,type:"select-category",intlLabel:{id:T("modalForm.components.create-component.category.label"),defaultMessage:"Select a category or enter a name to create a new one"}}]},{sectionTitle:null,items:[{name:`${a}icon`,type:"icon-picker",size:12,intlLabel:{id:T("modalForm.components.icon.label"),defaultMessage:"Icon"}}]}]},advanced(){return[]}},W={default:{name:"default",type:"text",intlLabel:{id:T("form.attribute.settings.default"),defaultMessage:"Default value"}},max:{name:"max",type:"checkbox-with-number-field",intlLabel:{id:T("form.attribute.item.maximum"),defaultMessage:"Maximum value"}},maxLength:{name:"maxLength",type:"checkbox-with-number-field",intlLabel:{id:T("form.attribute.item.maximumLength"),defaultMessage:"Maximum length"}},min:{name:"min",type:"checkbox-with-number-field",intlLabel:{id:T("form.attribute.item.minimum"),defaultMessage:"Minimum value"}},minLength:{name:"minLength",type:"checkbox-with-number-field",intlLabel:{id:T("form.attribute.item.minimumLength"),defaultMessage:"Minimum length"}},private:{name:"private",type:"checkbox",intlLabel:{id:T("form.attribute.item.privateField"),defaultMessage:"Private field"},description:{id:T("form.attribute.item.privateField.description"),defaultMessage:"This field will not show up in the API response"}},regex:{intlLabel:{id:T("form.attribute.item.text.regex"),defaultMessage:"RegExp pattern"},name:"regex",type:"text",description:{id:T("form.attribute.item.text.regex.description"),defaultMessage:"The text of the regular expression"}},required:{name:"required",type:"checkbox",intlLabel:{id:T("form.attribute.item.requiredField"),defaultMessage:"Required field"},description:{id:T("form.attribute.item.requiredField.description"),defaultMessage:"You won't be able to create an entry if this field is empty"}},unique:{name:"unique",type:"checkbox",intlLabel:{id:T("form.attribute.item.uniqueField"),defaultMessage:"Unique field"},description:{id:T("form.attribute.item.uniqueField.description"),defaultMessage:"You won't be able to create an entry if there is an existing entry with identical content"}}},jC={blocks(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.private]}]}},boolean(){return{sections:[{sectionTitle:null,items:[{autoFocus:!0,type:"select-default-boolean",intlLabel:{id:T("form.attribute.settings.default"),defaultMessage:"Default value"},name:"default",options:[{value:"true",key:"true",metadatas:{intlLabel:{id:"true",defaultMessage:"true"}}},{value:"",key:"null",metadatas:{intlLabel:{id:"null",defaultMessage:"null"}}},{value:"false",key:"false",metadatas:{intlLabel:{id:"false",defaultMessage:"false"}}}]}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.private]}]}},component({repeatable:a},s){return s==="1"?{sections:sr.advanced()}:a?{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.private,W.max,W.min]}]}:{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.private]}]}},date({type:a}){return{sections:[{sectionTitle:null,items:[{...W.default,type:a||"date",value:null,withDefaultValue:!1,disabled:!a,autoFocus:!1}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.unique,W.private]}]}},dynamiczone(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.max,W.min]}]}},email(){return{sections:[{sectionTitle:null,items:[{...W.default,type:"email"}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.unique,W.maxLength,W.minLength,W.private]}]}},enumeration(a){return{sections:[{sectionTitle:null,items:[{name:"default",type:"select",intlLabel:{id:T("form.attribute.settings.default"),defaultMessage:"Default value"},validations:{},options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"}}},...(a.enum||[]).filter((s,u)=>a.enum.indexOf(s)===u&&s).map(s=>({key:s,value:s,metadatas:{intlLabel:{id:`${s}.no-override`,defaultMessage:s}}}))]},{intlLabel:{id:T("form.attribute.item.enumeration.graphql"),defaultMessage:"Name override for GraphQL"},name:"enumName",type:"text",validations:{},description:{id:T("form.attribute.item.enumeration.graphql.description"),defaultMessage:"Allows you to override the default generated name for GraphQL"}}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.private]}]}},json(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.private]}]}},media(){return{sections:[{sectionTitle:null,items:[{intlLabel:{id:T("form.attribute.media.allowed-types"),defaultMessage:"Select allowed types of media"},name:"allowedTypes",type:"allowed-types-select",size:7,value:"",validations:{}}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.private]}]}},number(a){const s=a.type==="decimal"||a.type==="float"?"any":1;return{sections:[{sectionTitle:null,items:[{autoFocus:!0,name:"default",type:a.type==="biginteger"?"text":"number",step:s,intlLabel:{id:T("form.attribute.settings.default"),defaultMessage:"Default value"},validations:{}}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.unique,W.max,W.min,W.private]}]}},password(){return{sections:[{sectionTitle:null,items:[W.default]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.maxLength,W.minLength,W.private]}]}},relation(){return{sections:[{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.private]}]}},richtext(){return{sections:[{sectionTitle:null,items:[W.default]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.maxLength,W.minLength,W.private]}]}},text(){return{sections:[{sectionTitle:null,items:[W.default,W.regex]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.unique,W.maxLength,W.minLength,W.private]}]}},uid(a){return{sections:[{sectionTitle:null,items:[{...W.default,disabled:!!a.targetField,type:"text"}]},{sectionTitle:{id:"global.settings",defaultMessage:"Settings"},items:[W.required,W.maxLength,W.minLength,W.private]}]}}},Fi={intlLabel:{id:"global.type",defaultMessage:"Type"},name:"createComponent",type:"boolean-radio-group",size:12,radios:[{title:{id:T("form.attribute.component.option.create"),defaultMessage:"Create a new component"},description:{id:T("form.attribute.component.option.create.description"),defaultMessage:"A component is shared across types and components, it will be available and accessible everywhere."},value:!0},{title:{id:T("form.attribute.component.option.reuse-existing"),defaultMessage:"Use an existing component"},description:{id:T("form.attribute.component.option.reuse-existing.description"),defaultMessage:"Reuse a component already created to keep your data consistent across content-types."},value:!1}]},MC={component(a,s){if(s==="1"){const u=a.createComponent===!0?sr.base("componentToCreate."):[];return{sections:[{sectionTitle:null,items:[Fi]},...u]}}return{sections:[{sectionTitle:null,items:[Gt,{name:"component",type:"select-component",intlLabel:{id:T("modalForm.attributes.select-component"),defaultMessage:"Select a component"},isMultiple:!1}]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"repeatable",type:"boolean-radio-group",size:12,radios:[{title:{id:T("form.attribute.component.option.repeatable"),defaultMessage:"Repeatable component"},description:{id:T("form.attribute.component.option.repeatable.description"),defaultMessage:"Best for multiple instances (array) of ingredients, meta tags, etc.."},value:!0},{title:{id:T("form.attribute.component.option.single"),defaultMessage:"Single component"},description:{id:T("form.attribute.component.option.single.description"),defaultMessage:"Best for grouping fields like full address, main information, etc..."},value:!1}]}]}]}},date(){return{sections:[{sectionTitle:null,items:[Gt,{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"type",type:"select-date",options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"},hidden:!0}},{key:"date",value:"date",metadatas:{intlLabel:{id:T("form.attribute.item.date.type.date"),defaultMessage:"date (ex: 01/01/{currentYear})",values:{currentYear:new Date().getFullYear()}}}},{key:"datetime",value:"datetime",metadatas:{intlLabel:{id:T("form.attribute.item.date.type.datetime"),defaultMessage:"datetime (ex: 01/01/{currentYear} 00:00 AM)",values:{currentYear:new Date().getFullYear()}}}},{key:"time",value:"time",metadatas:{intlLabel:{id:T("form.attribute.item.date.type.time"),defaultMessage:"time (ex: 00:00 AM)"}}}]}]}]}},enumeration(){return{sections:[{sectionTitle:null,items:[Gt]},{sectionTitle:null,items:[{name:"enum",type:"textarea-enum",size:6,intlLabel:{id:T("form.attribute.item.enumeration.rules"),defaultMessage:"Values (one line per value)"},placeholder:{id:T("form.attribute.item.enumeration.placeholder"),defaultMessage:`Ex:
morning
noon
evening`},validations:{required:!0}}]}]}},media(){return{sections:[{sectionTitle:null,items:[Gt]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"multiple",size:12,type:"boolean-radio-group",radios:[{title:{id:T("form.attribute.media.option.multiple"),defaultMessage:"Multiple media"},description:{id:T("form.attribute.media.option.multiple.description"),defaultMessage:"Best for sliders, carousels or multiple files download"},value:!0},{title:{id:T("form.attribute.media.option.single"),defaultMessage:"Single media"},description:{id:T("form.attribute.media.option.single.description"),defaultMessage:"Best for avatar, profile picture or cover"},value:!1}]}]}]}},number(){return{sections:[{sectionTitle:null,items:[Gt,{intlLabel:{id:T("form.attribute.item.number.type"),defaultMessage:"Number format"},name:"type",type:"select-number",options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"},hidden:!0}},{key:"integer",value:"integer",metadatas:{intlLabel:{id:T("form.attribute.item.number.type.integer"),defaultMessage:"integer (ex: 10)"}}},{key:"biginteger",value:"biginteger",metadatas:{intlLabel:{id:T("form.attribute.item.number.type.biginteger"),defaultMessage:"biginteger (ex: 123456789)"}}},{key:"decimal",value:"decimal",metadatas:{intlLabel:{id:T("form.attribute.item.number.type.decimal"),defaultMessage:"decimal (ex: 2.22)"}}},{key:"float",value:"float",metadatas:{intlLabel:{id:T("form.attribute.item.number.type.float"),defaultMessage:"decimal (ex: 3.3333333)"}}}]}]}]}},relation(){return{sections:[{sectionTitle:null,items:[{intlLabel:{id:"FIXME",defaultMessage:"FIXME"},name:"relation",size:12,type:"relation"}]}]}},string(){return{sections:[{sectionTitle:null,items:[Gt]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"type",size:12,type:"radio-group",radios:[{title:{id:T("form.attribute.text.option.short-text"),defaultMessage:"Sort text"},description:{id:T("form.attribute.text.option.short-text.description"),defaultMessage:"Best for titles, names, links (URL). It also enables exact search on the field."},value:"string"},{title:{id:T("form.attribute.text.option.long-text"),defaultMessage:"Long text"},description:{id:T("form.attribute.text.option.long-text.description"),defaultMessage:"Best for descriptions, biography. Exact search is disabled."},value:"text"}]}]}]}},text(){return{sections:[{sectionTitle:null,items:[Gt]},{sectionTitle:null,items:[{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"type",size:12,type:"radio-group",radios:[{title:{id:T("form.attribute.text.option.short-text"),defaultMessage:"Sort text"},description:{id:T("form.attribute.text.option.short-text.description"),defaultMessage:"Best for titles, names, links (URL). It also enables exact search on the field."},value:"string"},{title:{id:T("form.attribute.text.option.long-text"),defaultMessage:"Long text"},description:{id:T("form.attribute.text.option.long-text.description"),defaultMessage:"Best for descriptions, biography. Exact search is disabled."},value:"text"}]}]}]}},uid(a,s,u){const p=u.filter(({type:f})=>["string","text"].includes(f)).map(({name:f})=>({key:f,value:f,metadatas:{intlLabel:{id:`${f}.no-override`,defaultMessage:f}}}));return{sections:[{sectionTitle:null,items:[{...Gt,placeholder:{id:T("modalForm.attribute.form.base.name.placeholder"),defaultMessage:"e.g. slug, seoUrl, canonicalUrl"}},{intlLabel:{id:T("modalForm.attribute.target-field"),defaultMessage:"Attached field"},name:"targetField",type:"select",options:[{key:"__null_reset_value__",value:"",metadatas:{intlLabel:{id:"global.none",defaultMessage:"None"}}},...p]}]}]}}},Al={advanced:jC,base:MC},bi=a=>a?Y0(a,{decamelize:!1,lowercase:!1,separator:"_"}):"",Sc=/^[A-Za-z][_0-9A-Za-z]*$/,$c=a=>({name:"attributeNameAlreadyUsed",message:we.unique.id,test(s){if(!s)return!1;const u=me.snakeCase(s);return!a.some(p=>me.snakeCase(p)===u)}}),Ri=a=>({name:"forbiddenAttributeName",message:T("error.attributeName.reserved-name"),test(s){if(!s)return!1;const u=me.snakeCase(s);return!a.some(p=>me.snakeCase(p)===u)}}),k={default:()=>Te().nullable(),max:()=>Dt().integer().nullable(),min:()=>Dt().integer().when("max",(a,s)=>a?s.max(a,T("error.validation.minSupMax")):s).nullable(),maxLength:()=>Dt().integer().positive(T("error.validation.positive")).nullable(),minLength:()=>Dt().integer().min(1).when("maxLength",(a,s)=>a?s.max(a,T("error.validation.minSupMax")):s).nullable(),name(a,s){return Te().test($c(a)).test(Ri(s)).matches(Sc,we.regex.id).required(we.required.id)},required:()=>Pr(),type:()=>Te().required(we.required.id),unique:()=>Pr().nullable()},vi=(a,s)=>({name:k.name(a,s),type:k.type(),default:k.default(),unique:k.unique(),required:k.required(),maxLength:k.maxLength(),minLength:k.minLength(),regex:Te().test({name:"isValidRegExpPattern",message:T("error.validation.regex"),test(p){try{return new RegExp(p||"")!==null}catch{return!1}}}).nullable()}),xi=()=>({name:"isMinSuperiorThanMax",message:T("error.validation.minSupMax"),test(a){if(!a)return!0;const{max:s}=this.parent;return!s||Number.isNaN(gi(a))?!0:gi(s)>=gi(a)}}),Oa={date(a,s){const u={name:k.name(a,s),type:k.type()};return Fe(u)},datetime(a,s){const u={name:k.name(a,s),type:k.type()};return Fe(u)},time(a,s){const u={name:k.name(a,s),type:k.type()};return Fe(u)},default(a,s){const u={name:k.name(a,s),type:k.type()};return Fe(u)},biginteger(a,s){const u={name:k.name(a,s),type:k.type(),default:Te().nullable().matches(/^-?\d*$/),unique:k.unique(),required:k.required(),max:Te().nullable().matches(/^-?\d*$/,we.regex.id),min:Te().nullable().test(xi()).matches(/^-?\d*$/,we.regex.id)};return Fe(u)},boolean(a,s){const u={name:k.name(a,s),default:Pr().nullable(),required:k.required(),unique:k.unique()};return Fe(u)},component(a,s){const u={name:k.name(a,s),type:k.type(),required:k.required(),max:k.max(),min:k.min(),component:Te().required(we.required.id)};return Fe(u)},decimal(a,s){const u={name:k.name(a,s),type:k.type(),default:Dt(),required:k.required(),max:Dt(),min:Dt().test(xi())};return Fe(u)},dynamiczone(a,s){const u={name:k.name(a,s),type:k.type(),required:k.required(),max:k.max(),min:k.min()};return Fe(u)},email(a,s){const u={name:k.name(a,s),type:k.type(),default:Te().email().nullable(),unique:k.unique(),required:k.required(),maxLength:k.maxLength(),minLength:k.minLength()};return Fe(u)},enumeration(a,s){const u=/^[_A-Za-z][_0-9A-Za-z]*$/,p={name:Te().test($c(a)).test(Ri(s)).matches(u,we.regex.id).required(we.required.id),type:k.type(),default:k.default(),unique:k.unique(),required:k.required(),enum:cl().of(Te()).min(1,we.min.id).test({name:"areEnumValuesUnique",message:T("error.validation.enum-duplicate"),test(f){return f?!_1(f.map(bi).filter((g,h,_)=>_.indexOf(g)!==h)).length:!1}}).test({name:"doesNotHaveEmptyValues",message:T("error.validation.enum-empty-string"),test:f=>f?!f.map(bi).some(b=>b===""):!1}).test({name:"doesMatchRegex",message:T("error.validation.enum-regex"),test:f=>f?f.map(bi).every(b=>u.test(b)):!1}),enumName:Te().nullable()};return Fe(p)},float(a,s){const u={name:k.name(a,s),type:k.type(),required:k.required(),default:Dt(),max:Dt(),min:Dt().test(xi())};return Fe(u)},integer(a,s){const u={name:k.name(a,s),type:k.type(),default:Dt().integer(),unique:k.unique(),required:k.required(),max:k.max(),min:k.min()};return Fe(u)},json(a,s){const u={name:k.name(a,s),type:k.type(),required:k.required(),unique:k.unique()};return Fe(u)},media(a,s){const u={name:k.name(a,s),type:k.type(),multiple:Pr(),required:k.required(),allowedTypes:cl().of(Te().oneOf(["images","videos","files","audios"])).min(1).nullable()};return Fe(u)},password(a,s){const u={name:k.name(a,s),type:k.type(),default:k.default(),unique:k.unique(),required:k.required(),maxLength:k.maxLength(),minLength:k.minLength()};return Fe(u)},relation(a,s,u,{initialData:p,modifiedData:f}){const b={name:k.name(a,s),target:Te().required(we.required.id),relation:Te().required(),type:Te().required(),targetAttribute:G0(()=>{const g=ki(f.relation,f.targetAttribute);if(g==="oneWay"||g==="manyWay")return Te().nullable();const h=Te().test(Ri(s)),j=[...u.map(({name:C})=>C),f.name].filter(C=>C!==p.targetAttribute);return h.matches(Sc,we.regex.id).test({name:"forbiddenTargetAttributeName",message:T("error.validation.relation.targetAttribute-taken"),test(C){return C?!j.includes(C):!1}}).required(we.required.id)})};return Fe(b)},richtext(a,s){const u={name:k.name(a,s),type:k.type(),default:k.default(),unique:k.unique(),required:k.required(),maxLength:k.maxLength(),minLength:k.minLength()};return Fe(u)},blocks(a,s){const u={name:k.name(a,s),type:k.type(),default:k.default(),unique:k.unique(),required:k.required(),maxLength:k.maxLength(),minLength:k.minLength()};return Fe(u)},string(a,s){const u=vi(a,s);return Fe(u)},text(a,s){const u=vi(a,s);return Fe(u)},uid(a,s){const u=vi(a,s);return Fe(u)}},Nc=/^[A-Za-z][-_0-9A-Za-z]*$/,AC=a=>{const s={name:Te().matches(Nc,we.regex.id).test({name:"nameNotAllowed",message:we.unique.id,test(u){return u?!a.includes(u?.toLowerCase()):!1}}).required(we.required.id)};return Fe(s)},FC={base:{sections:[{sectionTitle:null,items:[{autoFocus:!0,name:"name",type:"text",intlLabel:{id:"global.name",defaultMessage:"Name"},description:{id:T("modalForm.editCategory.base.name.description"),defaultMessage:"No space is allowed for the name of the category"}}]}]}},RC=(a,s,u,p,f)=>{const b={displayName:Te().test({name:"nameAlreadyUsed",message:we.unique.id,test(g){if(!g)return!1;const h=qa(g,u),_=me.snakeCase(h),j=me.snakeCase(f);return a.every(C=>me.snakeCase(C)!==_)&&p.every(C=>me.snakeCase(C)!==j)}}).test({name:"nameNotAllowed",message:T("error.contentTypeName.reserved-name"),test(g){if(!g)return!1;const h=me.snakeCase(g);return s.every(_=>me.snakeCase(_)!==h)}}).required(we.required.id),category:Te().matches(Nc,we.regex.id).required(we.required.id),icon:Te()};return Fe(b)},Fl={name:"displayName",type:"text",intlLabel:{id:T("contentType.displayName.label"),defaultMessage:"Display name"}},_i={advanced:{default(){return{sections:[{items:[{intlLabel:{id:T("contentType.draftAndPublish.label"),defaultMessage:"Draft & publish"},description:{id:T("contentType.draftAndPublish.description"),defaultMessage:"Allows writing a draft version of an entry, before it is published"},name:"draftAndPublish",type:"toggle-draft-publish",validations:{}}]}]}}},base:{create(){return{sections:[{sectionTitle:null,items:[Fl,{description:{id:T("contentType.apiId-singular.description"),defaultMessage:"Used to generate the API routes and databases tables/collections"},intlLabel:{id:T("contentType.apiId-singular.label"),defaultMessage:"API ID (Singular)"},name:"singularName",type:"text-singular"},{type:"pushRight",size:6,intlLabel:{id:"",defaultMessage:""},name:"pushRight"},{description:{id:T("contentType.apiId-plural.description"),defaultMessage:"Pluralized API ID"},intlLabel:{id:T("contentType.apiId-plural.label"),defaultMessage:"API ID (Plural)"},name:"pluralName",type:"text-plural"}]}]}},edit(){return{sections:[{sectionTitle:null,items:[Fl,{disabled:!0,description:{id:T("contentType.apiId-singular.description"),defaultMessage:"Used to generate the API routes and databases tables/collections"},intlLabel:{id:T("contentType.apiId-singular.label"),defaultMessage:"API ID (Singular)"},name:"singularName",type:"text"},{type:"pushRight",size:6,intlLabel:{id:"",defaultMessage:""},name:"pushRight"},{disabled:!0,description:{id:T("contentType.apiId-plural.description"),defaultMessage:"Pluralized API ID"},intlLabel:{id:T("contentType.apiId-plural.label"),defaultMessage:"API ID (Plural)"},name:"pluralName",type:"text"},{intlLabel:{id:"global.type",defaultMessage:"Type"},name:"kind",type:"content-type-radio-group",size:12,radios:[{title:{id:T("form.button.collection-type.name"),defaultMessage:"Collection Type"},description:{id:T("form.button.collection-type.description"),defaultMessage:"Best for multiple instances like articles, products, comments, etc."},value:"collectionType"},{title:{id:T("form.button.single-type.name"),defaultMessage:"Single Type"},description:{id:T("form.button.single-type.description"),defaultMessage:"Best for single instance like about us, homepage, etc."},value:"singleType"}]}]}]}}}},kC=({usedContentTypeNames:a=[],reservedModels:s=[],singularNames:u=[],pluralNames:p=[],collectionNames:f=[]})=>{const b={displayName:Te().test({name:"nameAlreadyUsed",message:we.unique.id,test(g){if(!g)return!1;const h=nc(g),_=me.snakeCase(h);return!a.some(j=>me.snakeCase(j)===_)}}).test({name:"nameNotAllowed",message:T("error.contentTypeName.reserved-name"),test(g){if(!g)return!1;const h=me.snakeCase(g);return!s.some(_=>me.snakeCase(_)===h)}}).required(we.required.id),pluralName:Te().test({name:"pluralNameAlreadyUsed",message:we.unique.id,test(g){if(!g)return!1;const h=me.snakeCase(g);return!p.some(_=>me.snakeCase(_)===h)}}).test({name:"pluralNameAlreadyUsedAsSingular",message:T("error.contentType.pluralName-equals-singularName"),test(g){if(!g)return!1;const h=me.snakeCase(g);return!u.some(_=>me.snakeCase(_)===h)}}).test({name:"pluralAndSingularAreUnique",message:T("error.contentType.pluralName-used"),test(g,h){return g?me.snakeCase(h.parent.singularName)!==me.snakeCase(g):!1}}).test({name:"pluralNameNotAllowed",message:T("error.contentTypeName.reserved-name"),test(g){if(!g)return!1;const h=me.snakeCase(g);return!s.some(_=>me.snakeCase(_)===h)}}).test({name:"pluralNameNotAlreadyUsedInCollectionName",message:T("error.contentType.pluralName-equals-collectionName"),test(g){if(!g)return!1;const h=me.snakeCase(g);return!f.some(_=>me.snakeCase(_)===h)}}).required(we.required.id),singularName:Te().test({name:"singularNameAlreadyUsed",message:we.unique.id,test(g){if(!g)return!1;const h=me.snakeCase(g);return!u.some(_=>me.snakeCase(_)===h)}}).test({name:"singularNameAlreadyUsedAsPlural",message:T("error.contentType.singularName-equals-pluralName"),test(g){if(!g)return!1;const h=me.snakeCase(g);return!p.some(_=>me.snakeCase(_)===h)}}).test({name:"pluralAndSingularAreUnique",message:T("error.contentType.singularName-used"),test(g,h){return g?me.snakeCase(h.parent.pluralName)!==me.snakeCase(g):!1}}).test({name:"singularNameNotAllowed",message:T("error.contentTypeName.reserved-name"),test(g){if(!g)return!1;const h=me.snakeCase(g);return!s.some(_=>me.snakeCase(_)===h)}}).required(we.required.id),draftAndPublish:Pr(),kind:Te().oneOf(["singleType","collectionType"])};return Fe(b)},Ci={advanced:{default(){return{sections:sr.advanced()}}},base:{createComponent(){return{sections:[{sectionTitle:null,items:[Fi]},...sr.base("componentToCreate.")]}},default(){return{sections:[{sectionTitle:null,items:[Fi]},{sectionTitle:null,items:[{type:"pushRight",size:6,intlLabel:{id:"",defaultMessage:""},name:"pushRight"},{name:"components",type:"select-components",intlLabel:{id:T("modalForm.attributes.select-components"),defaultMessage:"Select the components"},isMultiple:!0}]}]}}}},Rl=(a,s)=>{a.forEach(u=>{if(!("sectionTitle"in u)){s[0].items?.push(u);return}s.push(u)})},SC=(a,s)=>`components_${me.snakeCase(s)}_${Ua(me.snakeCase(a))}`,kl=(a,s)=>a.filter(({name:u})=>u!==s.initialData.name).map(({name:u})=>u),Tn={customField:{schema({schemaAttributes:a,attributeType:s,customFieldValidator:u,reservedNames:p,schemaData:f,ctbFormsAPI:b}){const g=kl(a,f);let h;return s==="relation"?h=Oa[s](g,p.attributes,[],{initialData:{},modifiedData:{}}):h=Oa[s](g,p.attributes),b.makeCustomFieldValidator(h,u,g,p.attributes,f)},form:{base({customField:a}){const s=[{sectionTitle:null,items:[Gt]}];return a.options?.base&&Rl(a.options.base,s),{sections:s}},advanced({customField:a,data:s,step:u,extensions:p,...f}){const b=[{sectionTitle:null,items:[]}],g=p.getAdvancedForm(["attribute",a.type],{data:s,type:a.type,step:u,...f});if(a.options?.advanced&&Rl(a.options.advanced,b),g){const h={sectionTitle:{id:T("modalForm.custom-fields.advanced.settings.extended"),defaultMessage:"Extended settings"},items:g};b.push(h)}return{sections:b}}}},attribute:{schema(a,s,u,p,f,b){const g=a?.schema?.attributes??[],h=kl(g,f);try{const _=Oa[s](h,u.attributes,p,f);return b.makeValidator(["attribute",s],_,h,u.attributes,p,f)}catch(_){return console.error("Error yup build schema",_),Oa.default(h,u.attributes)}},form:{advanced({data:a,type:s,step:u,extensions:p,...f}){try{const b=Al.advanced[s](a,u).sections,g=p.getAdvancedForm(["attribute",s],{data:a,type:s,step:u,...f});return{sections:b.reduce((_,j)=>(j.sectionTitle===null?_.push(j):_.push({...j,items:[...j.items,...g]}),_),[])}}catch(b){return console.error(b),{sections:[]}}},base({data:a,type:s,step:u,attributes:p}){try{return Al.base[s](a,u,p)}catch{return wC}}}},contentType:{schema(a,s,u,p,f,b){const g=Object.values(b).map($=>$.schema.singularName),h=Object.values(b).map($=>$?.schema?.pluralName??""),_=s?a.filter($=>$!==u):a,j=s?g.filter($=>{const{schema:z}=b[u];return z.singularName!==$}):g,C=s?h.filter($=>{const{schema:z}=b[u];return z.pluralName!==$}):h,w=Object.values(b).map($=>$?.schema?.collectionName??""),O=s?w.filter($=>{const{schema:z}=b[u],X=z.collectionName;return $!==X}):w,I=kC({usedContentTypeNames:_,reservedModels:p.models,singularNames:j,pluralNames:C,collectionNames:O});return f.makeValidator(["contentType"],I,_,p.models,j,C)},form:{base({actionType:a}){return a==="create"?_i.base.create():_i.base.edit()},advanced({extensions:a}){const s=_i.advanced.default().sections.map(p=>p.items).flat(),u=a.getAdvancedForm(["contentType"]);return{sections:[{items:[...s,...u]}]}}}},component:{schema(a,s,u,p=!1,f,b,g=null){const h=p?a.filter(w=>w!==g):a,_=Object.values(f).map(w=>w?.schema?.collectionName),j=SC(b,s),C=p?_.filter(w=>w!==j):_;return RC(h,u.models,s,C,j)},form:{advanced(){return{sections:sr.advanced()}},base(){return{sections:sr.base()}}}},addComponentToDynamicZone:{form:{advanced(){return Ci.advanced.default()},base({data:a}){return a?.createComponent??!1?Ci.base.createComponent():Ci.base.default()}}},editCategory:{schema(a,s){const u=a.filter(p=>p!==s.name).map(p=>p.toLowerCase());return AC(u)},form:{advanced:()=>({sections:[]}),base(){return FC.base}}}},$C=()=>a=>a[`${He}_formModal`]||U0,NC=()=>ec($C(),a=>a),EC=(a,s)=>{const u=G(a,["contentType","schema","kind"],"");return u==="singleType"||u===s.kind?!0:G(a,["contentType","schema","attributes"],[]).filter(({relation:b,type:g,targetAttribute:h})=>{const _=ki(b,h);return g==="relation"&&!["oneWay","manyWay"].includes(_||"")}).length===0},LC=(a="",s,u)=>{const p=["text","boolean","blocks","json","number","email","date","password","media","enumeration","relation","richtext"],f=a==="contentType";if(f)return[[...p.slice(0,-1),"uid",...p.slice(-1)],["component","dynamiczone"]];if(a){const g=Rc(s,u)>=rc;if(!f&&!g)return[p,["component"]]}return[p]},Sl=a=>a.reduce((s,u)=>{const p=u.items.reduce((f,b)=>(b.name&&f.push(b.name),f),[]);return[...s,...p]},[]),IC=qt.form`
  overflow: auto;
`,OC=()=>{const{onCloseModal:a,onNavigateToChooseAttributeModal:s,onNavigateToAddCompoToDZModal:u,onNavigateToCreateComponentStep2:p,actionType:f,attributeName:b,attributeType:g,customFieldUid:h,categoryName:_,dynamicZoneTarget:j,forTarget:C,modalType:w,isOpen:O,kind:I,step:$,targetUid:z,showBackLink:X,activeTab:ee,setActiveTab:N}=ur(),S=qr("FormModal",J=>J.getPlugin),ie=qr("FormModal",J=>J.customFields.get)(h),Re=pe.useMemo(NC,[]),ae=Ha(),{toggleNotification:ke}=Za(),se=Ql(J=>Re(J),z0),tt=B0(),{trackUsage:ve}=Va(),{formatMessage:xe}=he(),ge=S(He)?.apis.forms,E=ge.components.inputs,{addAttribute:D,addCustomFieldAttribute:q,addCreatedComponentToDynamicZone:Yt,allComponentsCategories:dn,changeDynamicZoneComponents:lr,contentTypes:fn,components:Pt,createSchema:gt,deleteCategory:cr,deleteData:Bn,editCategory:An,editCustomFieldAttribute:Wn,submitData:_t,modifiedData:Pe,nestedComponents:dr,setModifiedData:fr,sortedContentTypesList:Vn,updateSchema:Ut,reservedNames:Ue}=Mn(),{componentToCreate:pn,formErrors:st,initialData:Je,isCreatingComponentWhileAddingAField:V,modifiedData:c}=se,Q=C==="contentType"||C==="component"?[C]:[C,z];pe.useEffect(()=>{if(O){const J=Vn.filter(wc);w==="editCategory"&&fr(),f==="edit"&&w==="attribute"&&C==="contentType"&&ve("willEditFieldOfContentType");const at=[...Q,"schema","attributes"],bt=Mi(G(Pe,at,[]),j)||null;if(w==="editCategory"&&f==="edit"&&ae({type:Ia,modalType:w,actionType:f,data:{name:_}}),w==="contentType"&&f==="create"&&ae({type:Ia,modalType:w,actionType:f,data:{draftAndPublish:!0},pluginOptions:{}}),w==="contentType"&&f==="edit"){const{displayName:ye,draftAndPublish:$e,kind:Ze,pluginOptions:Ge,pluralName:kn,singularName:Sn}=G(Pe,[...Q,"schema"],{displayName:null,pluginOptions:{},singularName:null,pluralName:null});ae({type:Ia,actionType:f,modalType:w,data:{displayName:ye,draftAndPublish:$e,kind:Ze,pluginOptions:Ge,pluralName:kn,singularName:Sn}})}if(w==="component"&&f==="edit"){const ye=G(Pe,Q,{});ae({type:Ia,actionType:f,modalType:w,data:{displayName:ye.schema.displayName,category:ye.category,icon:ye.schema.icon}})}if(w==="addComponentToDynamicZone"&&f==="edit"){const ye={...bt,components:[],name:j,createComponent:!1,componentToCreate:{type:"component"}};ae({type:W0,attributeToEdit:ye})}if(g){const $e={...Mi(G(Pe,at,[]),b),name:b};g==="component"&&f==="edit"&&($e.repeatable||tc($e,"repeatable",!1)),ae(w==="customField"?{type:V0,customField:ie,isEditing:f==="edit",modifiedDataToSetForEditing:$e,forTarget:C}:{type:H0,attributeType:g,nameToSetForRelation:G(J,["0","title"],"error"),targetUid:G(J,["0","uid"],"error"),isEditing:f==="edit",modifiedDataToSetForEditing:$e,step:$,forTarget:C})}}else ae({type:Ir})},[f,b,g,_,j,C,O,w]);const Se=w==="contentType",ut=w==="component",je=w==="attribute",Ct=w==="customField",Kt=g==="component"&&je,Fn=f==="create",yt=G(c,"createComponent",!1)||V,nt=$==="1",pr=w==="editCategory",Jt=w==="chooseAttribute",zt=nc(c.displayName||""),mn=G(Pe,[...Q,"schema","attributes"],null),lt=async()=>{let J;const at=yt&&$==="1"?G(c,"componentToCreate",{}):c;if(Se)J=Tn.contentType.schema(Object.keys(fn),f==="edit",G(Pe,[...Q,"uid"],null),Ue,ge,fn);else if(ut)J=Tn.component.schema(Object.keys(Pt),c.category||"",Ue,f==="edit",Pt,c.displayName||"",G(Pe,[...Q,"uid"],null));else if(Ct)J=Tn.customField.schema({schemaAttributes:G(Pe,[...Q,"schema","attributes"],[]),attributeType:ie.type,reservedNames:Ue,schemaData:{modifiedData:c,initialData:Je},ctbFormsAPI:ge,customFieldValidator:ie.options?.validator});else if(Kt&&yt&&nt)J=Tn.component.schema(Object.keys(Pt),G(c,"componentToCreate.category",""),Ue,f==="edit",Pt,c.componentToCreate.displayName||"");else if(je&&!nt){const bt=g==="relation"?"relation":c.type;let ye=[];if(bt==="relation"){const $e=G(c,["target"],null);ye=G(fn,[$e,"schema","attributes"],[]).filter(({name:Ge})=>f!=="edit"?!0:Ge!==Je.targetAttribute)}J=Tn.attribute.schema(G(Pe,Q,{}),bt,Ue,ye,{modifiedData:c,initialData:Je},ge)}else if(pr)J=Tn.editCategory.schema(dn,Je);else if(nt&&yt)J=Tn.component.schema(Object.keys(Pt),G(c,"componentToCreate.category",""),Ue,f==="edit",Pt,c.componentToCreate.displayName||"");else return;await J.validate(at,{abortEarly:!1})},Bt=pe.useCallback(({target:{name:J,value:at,type:bt,...ye}})=>{const $e=["enumName","max","min","maxLength","minLength","regex","default"];let Ze;$e.includes(J)&&at===""?Ze=null:Ze=at;const Ge=Object.assign({},st);J==="max"&&delete Ge.min,J==="maxLength"&&delete Ge.minLength,delete Ge[J],ae({type:ll,errors:Ge}),ae({type:Z0,keys:J.split("."),value:Ze,...ye})},[ae,st]),rt=async(J,at=Fn)=>{J.preventDefault();try{await lt(),hn(at);const bt=C==="components"?z:zt;if(Se)if(Fn)gt({...c,kind:I},w,zt),tt({pathname:`/plugins/${He}/content-types/${zt}`}),s({forTarget:C,targetUid:bt});else{EC(Pe,c)?(a(),await _t(c)):ke({type:"danger",message:xe({id:"notification.contentType.relations.conflict"})});return}else if(w==="component")if(Fn){const ye=qa(c.displayName,c.category),{category:$e,...Ze}=c;gt(Ze,"component",ye,$e),tt({pathname:`/plugins/${He}/component-categories/${$e}/${ye}`}),s({forTarget:C,targetUid:ye})}else{Ut(c,w,z),a();return}else if(pr){if(ml(Je.name)===ml(c.name)){a();return}An(Je.name,c);return}else if(Ct){const ye={attributeToSet:{...c,customField:h},forTarget:C,targetUid:z,initialAttribute:Je};f==="edit"?Wn(ye):q(ye),at?s({forTarget:C,targetUid:bt}):a();return}else if(je&&!yt){if(g==="dynamiczone"){D(c,C,z,f==="edit",Je),Fn?(ae({type:xx}),N("basic"),u({dynamicZoneTarget:c.name})):a();return}if(!Kt){D(c,C,z,f==="edit",Je),at?s({forTarget:C,targetUid:bt}):a();return}if(nt){p(),ae({type:_x,forTarget:C});return}D(c,C,z,f==="edit",Je,!0),at?s({forTarget:C,targetUid:z}):a()}else if(je&&yt){if(nt){ve("willCreateComponentFromAttributesModal"),ae({type:Cx,forTarget:C}),p();return}const{category:ye,type:$e,...Ze}=pn,Ge=qa(pn.displayName,ye);gt(Ze,$e,Ge,ye,yt),D(c,C,z,!1),ae({type:Ir}),at?s({forTarget:"components",targetUid:Ge}):a();return}else{if(nt)if(yt){const{category:ye,type:$e,...Ze}=c.componentToCreate,Ge=qa(c.componentToCreate.displayName,ye);gt(Ze,$e,Ge,ye,yt),Yt(j,[Ge]),s({forTarget:"components",targetUid:Ge})}else lr(j,c.components),a();else console.error("This case is not handled");return}ae({type:Ir})}catch(bt){const ye=A1(bt);ae({type:ll,errors:ye})}},Tt=()=>{window.confirm(xe({id:"window.confirm.close-modal.file",defaultMessage:"Are you sure? Your changes will be lost."}))&&(a(),ae({type:Ir}))},Xt=()=>{Ga(c,Je)?(a(),ae({type:Ir})):Tt()},wt=J=>{if(J==="advanced"){if(Se){ve("didSelectContentTypeSettings");return}C==="contentType"&&ve("didSelectContentTypeFieldSettings")}},hn=J=>{w==="attribute"&&C==="contentType"&&g!=="dynamiczone"&&J&&ve("willAddMoreFieldToContentType")},Nt=()=>!!(w==="editCategory"||w==="component"||rx(c,"createComponent")),Rn=LC(C,z,dr);if(!w)return null;const Hn=G(Tn,[w,"form"],{advanced:()=>({sections:[]}),base:()=>({sections:[]})}),Ya=C==="components"||C==="component",Br={customInputs:{"allowed-types-select":R1,"boolean-radio-group":B1,"checkbox-with-number-field":W1,"icon-picker":Q1,"content-type-radio-group":V1,"radio-group":Ei,relation:yC,"select-category":bC,"select-component":vC,"select-components":xC,"select-default-boolean":U1,"select-number":kc,"select-date":_C,"toggle-draft-publish":H1,"text-plural":eC,"text-singular":CC,"textarea-enum":TC,...E},componentToCreate:pn,dynamicZoneTarget:j,formErrors:st,isAddingAComponentToAnotherComponent:Ya,isCreatingComponentWhileAddingAField:V,mainBoxHeader:G(Pe,[...Q,"schema","displayName"],""),modifiedData:c,naturePickerType:C,isCreating:Fn,targetUid:z,forTarget:C},Wr=Hn.advanced({data:c,type:g,step:$,actionType:f,attributes:mn,extensions:ge,forTarget:C,contentTypeSchema:Pe.contentType||{},customField:ie}).sections,Vr=Hn.base({data:c,type:g,step:$,actionType:f,attributes:mn,extensions:ge,forTarget:C,contentTypeSchema:Pe.contentType||{},customField:ie}).sections,Hr=Sl(Vr),Ka=Sl(Wr),Ja=Object.keys(st).some(J=>Hr.includes(J)),Qt=Object.keys(st).some(J=>Ka.includes(J)),Wt=G(fn,[z,"schema","kind"]),Xa=()=>f==="edit"&&mn.every(({name:J})=>J!==c?.name),Zn=()=>{Xa()&&ve("didEditFieldNameOnContentType")};return l.jsx(jn.Root,{open:O,onOpenChange:Xt,children:l.jsxs(jn.Content,{children:[l.jsx(G1,{actionType:f,attributeName:b,categoryName:_,contentTypeKind:I,dynamicZoneTarget:j,modalType:w,forTarget:C,targetUid:z,attributeType:g,customFieldUid:h,showBackLink:X}),Jt&&l.jsx(P1,{attributes:Rn,forTarget:C,kind:Wt||"collectionType"}),!Jt&&l.jsxs(IC,{onSubmit:rt,children:[l.jsx(jn.Body,{children:l.jsxs($t.Root,{variant:"simple",value:ee,onValueChange:J=>{N(J),wt(J)},hasError:Ja?"basic":Qt?"advanced":void 0,children:[l.jsxs(ue,{justifyContent:"space-between",children:[l.jsx(K1,{actionType:f,forTarget:C,kind:I,step:$,modalType:w,attributeType:g,attributeName:b,customField:ie}),l.jsxs($t.List,{children:[l.jsx($t.Trigger,{value:"basic",children:xe({id:T("popUpForm.navContainer.base"),defaultMessage:"Basic settings"})}),l.jsx($t.Trigger,{value:"advanced",disabled:Nt(),children:xe({id:T("popUpForm.navContainer.advanced"),defaultMessage:"Advanced settings"})})]})]}),l.jsx(Si,{marginBottom:6}),l.jsx($t.Content,{value:"basic",children:l.jsx(ue,{direction:"column",alignItems:"stretch",gap:6,children:l.jsx(Ml,{form:Vr,formErrors:st,genericInputProps:Br,modifiedData:c,onChange:Bt})})}),l.jsx($t.Content,{value:"advanced",children:l.jsx(ue,{direction:"column",alignItems:"stretch",gap:6,children:l.jsx(Ml,{form:Wr,formErrors:st,genericInputProps:Br,modifiedData:c,onChange:Bt})})})]})}),l.jsxs(jn.Footer,{children:[l.jsx(qe,{variant:"tertiary",onClick:Xt,children:xe({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),l.jsx(Z1,{deleteCategory:cr,deleteContentType:Bn,deleteComponent:Bn,categoryName:Je.name,isAttributeModal:w==="attribute",isCustomFieldModal:w==="customField",isComponentToDzModal:w==="addComponentToDynamicZone",isComponentAttribute:g==="component",isComponentModal:w==="component",isContentTypeModal:w==="contentType",isCreatingComponent:f==="create",isCreatingDz:f==="create",isCreatingComponentAttribute:c.createComponent||!1,isCreatingComponentInDz:c.createComponent||!1,isCreatingComponentWhileAddingAField:V,isCreatingContentType:f==="create",isEditingAttribute:f==="edit",isDzAttribute:g==="dynamiczone",isEditingCategory:w==="editCategory",isInFirstComponentStep:$==="1",onSubmitAddComponentAttribute:rt,onSubmitAddComponentToDz:rt,onSubmitCreateComponent:rt,onSubmitCreateContentType:rt,onSubmitCreateDz:rt,onSubmitEditAttribute:rt,onSubmitEditCategory:rt,onSubmitEditComponent:rt,onSubmitEditContentType:rt,onSubmitEditCustomFieldAttribute:rt,onSubmitEditDz:rt,onClickFinish:Zn})]})]})]})})},DC=()=>a=>a[`${He}_dataManagerProvider`]||k0,qC=()=>ec(DC(),a=>a),PC=(a,s)=>{const u=Object.keys(a).filter(p=>{const f=G(a,p,{}),b=G(s,p,{}),g=G(f,["isTemporary"],!1),h=!Ga(f,b);return g||h});return cc(u)},UC=(a,s)=>{const u=Ec(G(a,"schema.attributes",[]),s),p=G(a,"isTemporary",!1)?{tmpUID:a.uid}:{uid:a.uid};return Object.assign({},p,{category:a.category},dc(a.schema,"attributes"),{attributes:u})},$l=(a,s=!1)=>{const u=G(a,"uid",null),p=Ec(G(a,"schema.attributes",[]),u),f=s?{category:G(a,"category","")}:{},b=Object.assign(f,dc(a.schema,"attributes"),{attributes:p});return delete b.uid,delete b.isTemporary,delete b.visible,delete b.restrictRelationsTo,b},Ec=(a,s)=>a.reduce((u,{name:p,...f})=>{const b=f,g=b.target===s,h=b.type==="relation",_=G(b,"targetAttribute",null);if(!g)if(h){const j=Object.assign({},b,{targetAttribute:Nl(_)});u[p]=Da(j)}else u[p]=Da(b);if(g){const j=b.target,C=Object.assign({},b,{target:j,targetAttribute:Nl(_)});u[p]=Da(C)}if(b.customField){const j={...b,type:"customField"};u[p]=Da(j)}return u},{}),Nl=a=>a==="-"?null:a,Da=a=>Object.keys(a).reduce((s,u)=>(a[u]!==null&&u!=="plugin"&&(s[u]=a[u]),s),{}),zC=(a,s,u)=>PC(a,s).map(b=>{const g=G(a,b,{});return UC(g,u)}),BC=a=>Jx(Object.keys(a).map(s=>({visible:a[s].schema.visible,name:s,title:a[s].schema.displayName,plugin:a[s].plugin||null,uid:s,to:`/plugins/${He}/content-types/${s}`,kind:a[s].schema.kind,restrictRelationsTo:a[s].schema.restrictRelationsTo})).filter(s=>s!==null),s=>jx(s.title)),El=a=>a.reduce((s,u)=>(s[u.uid]=u,s),{}),WC=(a,s,u,p)=>{const f=s.reduce((h,_)=>{const j=G(u,_,{});return h[_]=j,h},{});return{[p?"contentType":"component"]:a,components:f}},Ll=a=>Object.keys(a).reduce((s,u)=>{const p=a[u].schema;return s[u]={...a[u],schema:{...p,attributes:VC(p.attributes)}},s},{}),VC=a=>Object.keys(a).reduce((s,u)=>(s.push({...a[u],name:u}),s),[]),HC=a=>Object.keys(a).reduce((u,p)=>{const f=G(a,[p]),b=ZC(f);return b.childComponents.length>0&&u.push(b),u},[]),ZC=a=>{const s=G(a,["schema","attributes"],[]);return{component:a.uid,childComponents:s.filter(u=>{const{type:p}=u;return p==="component"}).map(u=>({component:u.component}))}},GC=a=>{const s=Object.keys(a).reduce((u,p)=>{const f=a?.[p]?.schema?.attributes??[],b=YC(f,p);return[...u,...b]},[]);return KC(s)},YC=(a,s)=>a.reduce((u,p)=>{const{type:f,component:b}=p;return f==="component"&&u.push({component:b,parentCompoUid:s}),u},[]),KC=a=>{const s=new Map;return a.forEach(({component:p,parentCompoUid:f})=>{s.has(p)||s.set(p,new Set),s.get(p).add(f)}),Array.from(s.entries()).map(([p,f])=>({component:p,uidsOfAllParents:Array.from(f)}))},JC=(a,s)=>{const u=Object.keys(a).map(p=>G(a,[p,...s],""));return cc(u)},Il="did-not-kill-server",XC="server is down";function Or(a,s){return new Promise(u=>{fetch(`${window.strapi.backendURL}/_health`,{method:"HEAD",mode:"no-cors",headers:{"Content-Type":"application/json","Keep-Alive":"false"}}).then(p=>{if(p.status>=400)throw new Error(XC);if(!s)throw new Error(Il);u(a)}).catch(p=>{setTimeout(()=>Or(a,p.message!==Il).then(u),100)})})}const QC=a=>Object.values(a.attributes).filter(u=>u.type==="dynamiczone").every(u=>Array.isArray(u.components)&&u.components.length>0),eT=({children:a})=>{const s=Ha(),{components:u,contentTypes:p,isLoading:f,isLoadingForDataToBeSet:b,initialData:g,modifiedData:h,reservedNames:_}=Ql(qC()),{toggleNotification:j}=Za(),{lockAppWithAutoreload:C,unlockAppWithAutoreload:w}=S0(),{setCurrentStep:O,setStepState:I}=Xl("DataManagerProvider",V=>V),z=qr("DataManagerProvider",V=>V.getPlugin)(He),X=$0("DataManagerProvider",V=>V.autoReload),{formatMessage:ee}=he(),{trackUsage:N}=Va(),S=N0("DataManagerProvider",V=>V.refetchPermissions),{pathname:le}=E0(),{onCloseModal:ie}=ur(),Re=ul(`/plugins/${He}/content-types/:uid`),ae=ul(`/plugins/${He}/component-categories/:categoryUid/:componentUid`),ke=L0(),{put:se,post:tt,del:ve}=ke,xe=pe.useRef();xe.current=ee;const Le=X,ge=Re!==null,E=ge?"contentType":"component",D=ge?G(Re,"params.uid",null):G(ae,"params.componentUid",null),q=pe.useRef(),Yt=ge?"content-types":"components";q.current=async()=>{try{const[{data:{data:V}},{data:{data:c}},{data:Q}]=await Promise.all(["components","content-types","reserved-names"].map(Kt=>ke.get(`/${He}/${Kt}`))),Se=El(V),ut=Ll(Se),je=El(c),Ct=Ll(je);s({type:I0,components:ut,contentTypes:Ct,reservedNames:Q})}catch(V){console.error({err:V}),j({type:"danger",message:ee({id:"notification.error",defaultMessage:"An error occurred"})})}},pe.useEffect(()=>(q.current(),()=>{s({type:O0})}),[]),pe.useEffect(()=>{!f&&D&&Vn()},[f,le,D]),pe.useEffect(()=>{X||j({type:"info",message:ee({id:T("notification.info.autoreaload-disable")})})},[X,j]);const dn=(V,c,Q,Se=!1,ut,je=!1)=>{s({type:Se?Ix:Ox,attributeToSet:V,forTarget:c,targetUid:Q,initialAttribute:ut,shouldAddComponentToData:je})},lr=({attributeToSet:V,forTarget:c,targetUid:Q,initialAttribute:Se})=>{s({type:Lx,attributeToSet:V,forTarget:c,targetUid:Q,initialAttribute:Se})},fn=({attributeToSet:V,forTarget:c,targetUid:Q,initialAttribute:Se})=>{s({type:Rx,attributeToSet:V,forTarget:c,targetUid:Q,initialAttribute:Se})},Pt=(V,c)=>{s({type:Ex,dynamicZoneTarget:V,componentsToAdd:c})},gt=(V,c,Q,Se,ut=!1)=>{s({type:c==="contentType"?Sx:$x,data:V,componentCategory:Se,schemaType:c,uid:Q,shouldAddComponentToData:ut})},cr=(V,c)=>{s({type:Nx,dynamicZoneTarget:V,newComponents:c})},Bn=(V,c,Q="")=>{const Se=V==="components"?Ax:Fx;V==="contentType"&&N("willDeleteFieldOfContentType"),s({type:Se,mainDataKey:V,attributeToRemoveName:c,componentUid:Q})},An=async V=>{try{const c=`/${He}/component-categories/${V}`,Q=window.confirm(ee({id:T("popUpWarning.bodyMessage.category.delete")}));ie(),Q&&(C?.(),await ve(c),await Or(!0),w?.(),await st())}catch(c){console.error({err:c}),j({type:"danger",message:ee({id:"notification.error",defaultMessage:"An error occurred"})})}finally{w?.()}},Wn=async()=>{try{const V=`/${He}/${Yt}/${D}`,c=G(h,[E,"isTemporary"],!1),Q=window.confirm(ee({id:T(`popUpWarning.bodyMessage.${ge?"contentType":"component"}.delete`)}));if(ie(),Q){if(c){s({type:kx});return}C?.(),await ve(V),await Or(!0),await w?.(),await st()}}catch(V){console.error({err:V}),j({type:"danger",message:ee({id:"notification.error",defaultMessage:"An error occurred"})})}finally{w?.()}},_t=async(V,c)=>{try{const Q=`/${He}/component-categories/${V}`;ie(),C?.(),await se(Q,c),await Or(!0),await w?.(),await st()}catch(Q){console.error({err:Q}),j({type:"danger",message:ee({id:"notification.error",defaultMessage:"An error occurred"})})}finally{w?.()}},Pe=()=>{const V=Object.assign({},u,h.components);if(!ge){const Q=G(h,"component",{});tc(V,G(Q,["uid"],""),Q)}return HC(V)},dr=()=>GC(u),fr=(V,c)=>{s({type:Mx,dzName:V,componentToRemoveIndex:c})},Vn=()=>{const c=G(ge?p:u,D??"",{schema:{attributes:[]}}),Q=D0(c.schema.attributes,u),Se=WC(c,Q,u,ge),ut=G(c,"isTemporary",!1)&&N_(G(c,"schema.attributes",[]))===0;s({type:q0,schemaToSet:Se,hasJustCreatedSchema:ut})},Ut=pe.useMemo(()=>{const V=ge?p:u;return D==="create-content-type"?!1:!Object.keys(V).includes(D||"")&&!f},[u,p,D,ge,f]),Ue=pe.useMemo(()=>{const V=Object.keys(p).filter(c=>G(p,[c,"schema","visible"],!0)).sort();return G(V,"0","create-content-type")},[p]);if(Ut)return l.jsx(P0,{to:`/plugins/${He}/content-types/${Ue}`});const pn=async V=>{try{const c=G(h,[E,"isTemporary"],!1),Q={components:zC(h.components,u,D)};if(ge){const Ct=(z?.apis?.forms).mutateContentTypeSchema({...$l(h.contentType),...V},g.contentType);if(!QC(Ct)){j({type:"danger",message:ee({id:T("notification.error.dynamiczone-min.validation"),defaultMessage:"At least one component is required in a dynamic zone to be able to save a content type"})});return}Q.contentType=Ct,N("willSaveContentType")}else Q.component=$l(h.component,!0),N("willSaveComponent");C?.();const Se=`/${He}/${Yt}`,ut=c?Se:`${Se}/${D}`;if(c?await tt(ut,Q):await se(ut,Q),c&&(g.contentType?.schema.kind==="collectionType"||g.contentType?.schema.kind==="singleType")&&(I("contentTypeBuilder.success",!0),N("didCreateGuidedTourCollectionType"),O(null)),ge){N("didSaveContentType");const je=G(Q,["contentType","schema","name"],""),Ct=G(g,["contentType","schema","name"],"");!c&&je!==Ct&&N("didEditNameOfContentType")}else N("didSaveComponent");await Or(!0),w?.(),await q.current(),s({type:wx}),await st()}catch(c){ge||N("didNotSaveComponent"),console.error({err:c.response}),j({type:"danger",message:ee({id:"notification.error",defaultMessage:"An error occurred"})})}finally{w?.()}},st=async()=>{await S()},Je=(V,c,Q)=>{s({type:Tx,data:V,schemaType:c,uid:Q})};return l.jsx(Cc.Provider,{value:{addAttribute:dn,addCustomFieldAttribute:lr,addCreatedComponentToDynamicZone:Pt,allComponentsCategories:JC(u,["category"]),changeDynamicZoneComponents:cr,components:u,componentsGroupedByCategory:Gx(u,"category"),componentsThatHaveOtherComponentInTheirAttributes:Pe(),contentTypes:p,createSchema:gt,deleteCategory:An,deleteData:Wn,editCategory:_t,editCustomFieldAttribute:fn,isInDevelopmentMode:Le,initialData:g,isInContentTypeView:ge,modifiedData:h,nestedComponents:dr(),removeAttribute:Bn,removeComponentFromDynamicZone:fr,reservedNames:_,setModifiedData:Vn,sortedContentTypesList:BC(p),submitData:pn,updateSchema:Je},children:b?l.jsx(Dr.Loading,{}):l.jsxs(l.Fragment,{children:[a,Le&&l.jsx(OC,{})]})})},tT=pe.memo(eT),Ol={actionType:null,attributeName:null,attributeType:null,categoryName:null,dynamicZoneTarget:null,forTarget:null,modalType:null,isOpen:!1,showBackLink:!1,kind:null,step:null,targetUid:null,customFieldUid:null,activeTab:"basic"},nT=({children:a})=>{const[s,u]=pe.useState(Ol),{trackUsage:p}=Va(),f=({attributeType:N,customFieldUid:S})=>{u(le=>({...le,actionType:"create",modalType:"customField",attributeType:N,customFieldUid:S,activeTab:"basic"}))},b=({attributeType:N,step:S})=>{s.forTarget==="contentType"&&p("didSelectContentTypeFieldType",{type:N}),u(le=>({...le,actionType:"create",modalType:"attribute",step:S,attributeType:N,showBackLink:!0,activeTab:"basic"}))},g=({dynamicZoneTarget:N,targetUid:S})=>{u(le=>({...le,dynamicZoneTarget:N,targetUid:S,modalType:"addComponentToDynamicZone",forTarget:"contentType",step:"1",actionType:"edit",isOpen:!0}))},h=({forTarget:N,targetUid:S})=>{u(le=>({...le,actionType:"create",forTarget:N,targetUid:S,modalType:"chooseAttribute",isOpen:!0,showBackLink:!1,activeTab:"basic"}))},_=N=>{u(S=>({...S,...N,isOpen:!0,activeTab:"basic"}))},j=N=>{u(S=>({...S,categoryName:N,actionType:"edit",modalType:"editCategory",isOpen:!0,activeTab:"basic"}))},C=({forTarget:N,targetUid:S,attributeName:le,attributeType:ie,customFieldUid:Re})=>{u(ae=>({...ae,modalType:"customField",customFieldUid:Re,actionType:"edit",forTarget:N,targetUid:S,attributeName:le,attributeType:ie,isOpen:!0,activeTab:"basic"}))},w=({forTarget:N,targetUid:S,attributeName:le,attributeType:ie,step:Re})=>{u(ae=>({...ae,modalType:"attribute",actionType:"edit",forTarget:N,targetUid:S,attributeName:le,attributeType:ie,step:Re,isOpen:!0}))},O=({modalType:N,forTarget:S,targetUid:le,kind:ie})=>{u(Re=>({...Re,modalType:N,actionType:"edit",forTarget:S,targetUid:le,kind:ie,isOpen:!0,activeTab:"basic"}))},I=()=>{u(Ol)},$=({forTarget:N,targetUid:S})=>{u(le=>({...le,forTarget:N,targetUid:S,modalType:"chooseAttribute",activeTab:"basic"}))},z=()=>{u(N=>({...N,attributeType:"component",modalType:"attribute",step:"2",activeTab:"basic"}))},X=({dynamicZoneTarget:N})=>{u(S=>({...S,dynamicZoneTarget:N,modalType:"addComponentToDynamicZone",actionType:"create",step:"1",attributeType:null,attributeName:null,activeTab:"basic"}))},ee=N=>{u(S=>({...S,activeTab:N}))};return l.jsx(Tc.Provider,{value:{...s,onClickSelectField:b,onClickSelectCustomField:f,onCloseModal:I,onNavigateToChooseAttributeModal:$,onNavigateToAddCompoToDZModal:X,onOpenModalAddComponentsToDZ:g,onNavigateToCreateComponentStep2:z,onOpenModalAddField:h,onOpenModalCreateSchema:_,onOpenModalEditCategory:j,onOpenModalEditField:w,onOpenModalEditCustomField:C,onOpenModalEditSchema:O,setFormModalNavigationState:u,setActiveTab:ee},children:a})},rT=pe.lazy(()=>pc(()=>import("./ListView-g5kB4f_y-Bsz8fZWQ.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]))),aT=()=>l.jsx(pe.Suspense,{fallback:l.jsx(Dr.Loading,{}),children:l.jsx(fc,{children:l.jsx(Ti,{path:"/:componentUid",element:l.jsx(rT,{})})})}),oT=pe.lazy(()=>pc(()=>import("./ListView-g5kB4f_y-Bsz8fZWQ.js"),__vite__mapDeps([0,1,2,3,4,5,6,7,8,9]))),iT=()=>{const{formatMessage:a}=he(),s=a({id:`${He}.plugin.name`,defaultMessage:"Content Types Builder"}),u=Xl("App",f=>f.startSection),p=pe.useRef(u);return pe.useEffect(()=>{p.current&&p.current("contentTypeBuilder")},[]),l.jsxs(Dr.Protect,{permissions:Zx.main,children:[l.jsx(Dr.Title,{children:s}),l.jsx(R0,{children:l.jsx(nT,{children:l.jsx(tT,{children:l.jsx(Dx.Root,{sideNav:l.jsx(j1,{}),children:l.jsx(pe.Suspense,{fallback:l.jsx(Dr.Loading,{}),children:l.jsxs(fc,{children:[l.jsx(Ti,{path:"content-types/:uid",element:l.jsx(oT,{})}),l.jsx(Ti,{path:"component-categories/:categoryUid/*",element:l.jsx(aT,{})})]})})})})})})]})},hT=Object.freeze(Object.defineProperty({__proto__:null,default:iT},Symbol.toStringTag,{value:"Module"}));export{Ba as A,Mc as C,ur as a,T as g,hT as i,Mn as u};
