import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  <PERSON>,
  useAPIError<PERSON><PERSON><PERSON>,
  useNotification,
  useTypedSelector
} from "./chunk-4C2ZQ5OG.js";
import {
  Table,
  Tbody,
  Td,
  Th,
  Thead,
  Tr,
  Typography,
  useGetPluginsQuery,
  useIntl,
  useNotifyAT
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/InstalledPlugins-C5Kd-OS2.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var InstalledPlugins = () => {
  var _a;
  const { formatMessage } = useIntl();
  const { notifyStatus } = useNotifyAT();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { isLoading, data, error } = useGetPluginsQuery();
  React.useEffect(() => {
    if (data) {
      notifyStatus(
        formatMessage(
          {
            id: "app.utils.notify.data-loaded",
            defaultMessage: "The {target} has loaded"
          },
          {
            target: formatMessage({
              id: "global.plugins",
              defaultMessage: "Plugins"
            })
          }
        )
      );
    }
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [data, error, formatAPIError, formatMessage, notifyStatus, toggleNotification]);
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsx)(Layouts.Root, { children: (0, import_jsx_runtime.jsxs)(Page.Main, { children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: "global.plugins",
          defaultMessage: "Plugins"
        }),
        subtitle: formatMessage({
          id: "app.components.ListPluginsPage.description",
          defaultMessage: "List of the installed plugins in the project."
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(Table, { colCount: 2, rowCount: ((_a = data == null ? void 0 : data.plugins) == null ? void 0 : _a.length) ?? 0 + 1, children: [
      (0, import_jsx_runtime.jsx)(Thead, { children: (0, import_jsx_runtime.jsxs)(Tr, { children: [
        (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({
          id: "global.name",
          defaultMessage: "Name"
        }) }) }),
        (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({
          id: "global.description",
          defaultMessage: "description"
        }) }) })
      ] }) }),
      (0, import_jsx_runtime.jsx)(Tbody, { children: data == null ? void 0 : data.plugins.map(({ name, displayName, description }) => {
        return (0, import_jsx_runtime.jsxs)(Tr, { children: [
          (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", variant: "omega", fontWeight: "bold", children: formatMessage({
            id: `global.plugins.${name}`,
            defaultMessage: displayName
          }) }) }),
          (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: formatMessage({
            id: `global.plugins.${name}.description`,
            defaultMessage: description
          }) }) })
        ] }, name);
      }) })
    ] }) })
  ] }) });
};
var ProtectedInstalledPlugins = () => {
  var _a;
  const { formatMessage } = useIntl();
  const permissions = useTypedSelector((state) => state.admin_app.permissions);
  return (0, import_jsx_runtime.jsxs)(Page.Protect, { permissions: (_a = permissions.marketplace) == null ? void 0 : _a.main, children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage({
      id: "global.plugins",
      defaultMessage: "Plugins"
    }) }),
    (0, import_jsx_runtime.jsx)(InstalledPlugins, {})
  ] });
};
export {
  InstalledPlugins,
  ProtectedInstalledPlugins
};
//# sourceMappingURL=InstalledPlugins-C5Kd-OS2-QSFJKKUW.js.map
