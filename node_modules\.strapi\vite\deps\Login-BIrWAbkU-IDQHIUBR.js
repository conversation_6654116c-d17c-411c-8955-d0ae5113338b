import {
  SSOProviders
} from "./chunk-B26CZQ3W.js";
import {
  Login
} from "./chunk-ELTZWS66.js";
import {
  useGetProvidersQuery
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Divider,
  Flex,
  Typography,
  useIntl
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/Login-BIrWAbkU.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var DividerFull = dt(Divider)`
  flex: 1;
`;
var LoginEE = (loginProps) => {
  const { formatMessage } = useIntl();
  const { isLoading, data: providers = [] } = useGetProvidersQuery(void 0, {
    skip: !window.strapi.features.isEnabled(window.strapi.features.SSO)
  });
  if (!window.strapi.features.isEnabled(window.strapi.features.SSO) || !isLoading && providers.length === 0) {
    return (0, import_jsx_runtime.jsx)(Login, { ...loginProps });
  }
  return (0, import_jsx_runtime.jsx)(Login, { ...loginProps, children: (0, import_jsx_runtime.jsx)(Box, { paddingTop: 7, children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 7, children: [
    (0, import_jsx_runtime.jsxs)(Flex, { children: [
      (0, import_jsx_runtime.jsx)(DividerFull, {}),
      (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 3, paddingRight: 3, children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({ id: "Auth.login.sso.divider" }) }) }),
      (0, import_jsx_runtime.jsx)(DividerFull, {})
    ] }),
    (0, import_jsx_runtime.jsx)(SSOProviders, { providers, displayAllProviders: false })
  ] }) }) });
};
export {
  LoginEE
};
//# sourceMappingURL=Login-BIrWAbkU-IDQHIUBR.js.map
