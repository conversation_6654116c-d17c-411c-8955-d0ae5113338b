{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/it-T1rZP3NX.mjs"], "sourcesContent": ["const Analytics = \"Analytics\";\nconst Documentation = \"Documentazione\";\nconst Email = \"Email\";\nconst Password = \"Password\";\nconst Provider = \"Provider\";\nconst ResetPasswordToken = \"Reimposta Token Password\";\nconst Role = \"Ruolo\";\nconst Username = \"Nome utente\";\nconst Users = \"Utenti\";\nconst it = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"Il tuo account è stato sospeso\",\n\t\"Auth.form.button.forgot-password\": \"Invia email\",\n\t\"Auth.form.button.go-home\": \"TORNA ALLA HOME\",\n\t\"Auth.form.button.login\": \"Accedi\",\n\t\"Auth.form.button.register\": \"Inizia adesso\",\n\t\"Auth.form.confirmPassword.label\": \"Conferma Password\",\n\t\"Auth.form.email.label\": \"Email\",\n\t\"Auth.form.email.placeholder\": \"<EMAIL>\",\n\t\"Auth.form.error.blocked\": \"Il tuo account è stato bloccato dall'amministratore.\",\n\t\"Auth.form.error.code.provide\": \"Codice fornito non corretto.\",\n\t\"Auth.form.error.confirmed\": \"L'email del tuo account non è stata confermata.\",\n\t\"Auth.form.error.email.invalid\": \"Questa email non è valida.\",\n\t\"Auth.form.error.email.provide\": \"Per favore inserisci il tuo nome utente o la tua email.\",\n\t\"Auth.form.error.email.taken\": \"Email già utilizzata.\",\n\t\"Auth.form.error.invalid\": \"Identificatore o password non valida.\",\n\t\"Auth.form.error.params.provide\": \"I parametri forniti non sono corretti.\",\n\t\"Auth.form.error.password.format\": \"La tua password non può contenere il simbolo `$` per più di tre volte.\",\n\t\"Auth.form.error.password.local\": \"Questo utente non ha mai impostato una password locale, accedi gentilmente tramite il provider usato durante la creazione dell'account\",\n\t\"Auth.form.error.password.matching\": \"La password non corrisponde.\",\n\t\"Auth.form.error.password.provide\": \"Per favore fornisci la tua password\",\n\t\"Auth.form.error.ratelimit\": \"Troppi tentativi, riprova tra un minuto.\",\n\t\"Auth.form.error.user.not-exist\": \"Questa email non esiste.\",\n\t\"Auth.form.error.username.taken\": \"Nome utente già utilizzato.\",\n\t\"Auth.form.firstname.label\": \"Nome\",\n\t\"Auth.form.firstname.placeholder\": \"Kai\",\n\t\"Auth.form.forgot-password.email.label\": \"Inserisci la tua email\",\n\t\"Auth.form.forgot-password.email.label.success\": \"Email inviata correttamente\",\n\t\"Auth.form.lastname.label\": \"Cognome\",\n\t\"Auth.form.lastname.placeholder\": \"Doe\",\n\t\"Auth.form.register.news.label\": \"Tienimi aggiornato in merito a nuove funzionalità e futuri sviluppi (così facendo accetti {terms} e {policy}).\",\n\t\"Auth.form.rememberMe.label\": \"Ricordami\",\n\t\"Auth.form.username.label\": \"Nome utente\",\n\t\"Auth.form.username.placeholder\": \"Kai Doe\",\n\t\"Auth.link.forgot-password\": \"Password dimenticata?\",\n\t\"Auth.link.ready\": \"Sei pronto per accedere?\",\n\t\"Auth.link.signin\": \"Accedi\",\n\t\"Auth.link.signin.account\": \"Hai già un account?\",\n\t\"Auth.privacy-policy-agreement.policy\": \"privacy policy\",\n\t\"Auth.privacy-policy-agreement.terms\": \"termini\",\n\t\"Content Manager\": \"Gestione Contenuti\",\n\t\"Content Type Builder\": \"Content-Types Builder\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Caricamento Files\",\n\t\"HomePage.head.title\": \"Homepage\",\n\t\"HomePage.roadmap\": \"Guarda la nostra roadmap\",\n\t\"HomePage.welcome.congrats\": \"Congratulazioni!\",\n\t\"HomePage.welcome.congrats.content\": \"Ti sei loggato come primo amministratore. Per scoprire le funzionalità di Strapi,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"ti consigliamo di creare la tua prima Collezione.\",\n\t\"Media Library\": \"Libreria media\",\n\t\"New entry\": \"Nuovo elemento\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"Ruoli e permessi\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"Alcuni ruoli non possono essere eleminati poiché sono associati agli utenti\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"Un ruolo non può essere eliminato se associato ad utenti\",\n\t\"Roles.components.List.empty.withSearch\": \"Nessun ruolo corrisponde alla ricerca ({search})...\",\n\t\"Settings.PageTitle\": \"Impostazioni - {name}\",\n\t\"Settings.application.description\": \"Vedi i dettagli del tuo progetto\",\n\t\"Settings.application.edition-title\": \"PIANO ATTUALE\",\n\t\"Settings.application.link-pricing\": \"Vedi tutti i prezzi\",\n\t\"Settings.application.link-upgrade\": \"Aggiorna il tuo progetto\",\n\t\"Settings.application.node-version\": \"VERSIONE NODE\",\n\t\"Settings.application.strapi-version\": \"VERSIONE STRAPI\",\n\t\"Settings.application.title\": \"Applicazione\",\n\t\"Settings.error\": \"Errore\",\n\t\"Settings.global\": \"Impostazioni Globali\",\n\t\"Settings.permissions\": \"Pannello di amministazione\",\n\t\"Settings.permissions.category\": \"Impostazioni permessi per la categoria {category}\",\n\t\"Settings.permissions.category.plugins\": \"Permissions settings for the {category} plugin\",\n\t\"Settings.permissions.conditions.anytime\": \"In ogni momento\",\n\t\"Settings.permissions.conditions.apply\": \"Applica\",\n\t\"Settings.permissions.conditions.can\": \"Può\",\n\t\"Settings.permissions.conditions.conditions\": \"Definisci le condizioni\",\n\t\"Settings.permissions.conditions.links\": \"Link\",\n\t\"Settings.permissions.conditions.no-actions\": \"Non ci sono azioni\",\n\t\"Settings.permissions.conditions.or\": \"Oppure\",\n\t\"Settings.permissions.conditions.when\": \"Quando\",\n\t\"Settings.permissions.users.create\": \"Crea nuovo utente\",\n\t\"Settings.permissions.users.email\": \"Email\",\n\t\"Settings.permissions.users.firstname\": \"Nome\",\n\t\"Settings.permissions.users.lastname\": \"Cognome\",\n\t\"Settings.roles.create.description\": \"Definisci permessi del ruolo\",\n\t\"Settings.roles.create.title\": \"Crea ruolo\",\n\t\"Settings.roles.created\": \"Ruolo creato\",\n\t\"Settings.roles.edit.title\": \"Modifica ruolo\",\n\t\"Settings.roles.form.button.users-with-role\": \"Utenti con questo ruolo\",\n\t\"Settings.roles.form.created\": \"Creato\",\n\t\"Settings.roles.form.description\": \"Nome e descrizione ruolo\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"Permessi per i campi\",\n\t\"Settings.roles.form.permissions.create\": \"Crea\",\n\t\"Settings.roles.form.permissions.delete\": \"Elimina\",\n\t\"Settings.roles.form.permissions.publish\": \"Pubblica\",\n\t\"Settings.roles.form.permissions.read\": \"Leggi\",\n\t\"Settings.roles.form.permissions.update\": \"Aggiorna\",\n\t\"Settings.roles.list.button.add\": \"Aggiungi nuovo ruolo\",\n\t\"Settings.roles.list.description\": \"Lista dei ruoli\",\n\t\"Settings.roles.title.singular\": \"Ruolo\",\n\t\"Settings.webhooks.create\": \"Crea un webhook\",\n\t\"Settings.webhooks.create.header\": \"Crea un nuovo header\",\n\t\"Settings.webhooks.created\": \"Webhook creato\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"Evento disponibile solo per contenuti con gestione stati Bozza/Pubblicazione abilitati\",\n\t\"Settings.webhooks.events.create\": \"Crea\",\n\t\"Settings.webhooks.events.update\": \"Aggiorna\",\n\t\"Settings.webhooks.form.events\": \"Eventi\",\n\t\"Settings.webhooks.form.headers\": \"Headers\",\n\t\"Settings.webhooks.form.url\": \"Url\",\n\t\"Settings.webhooks.key\": \"Chiave\",\n\t\"Settings.webhooks.list.button.add\": \"Aggiungi nuovo webhook\",\n\t\"Settings.webhooks.list.description\": \"Ricevi notifiche di cambiamenti in POST.\",\n\t\"Settings.webhooks.list.empty.description\": \"Aggiungi il primo alla lista\",\n\t\"Settings.webhooks.list.empty.link\": \"Leggi la documentazione\",\n\t\"Settings.webhooks.list.empty.title\": \"Non ci sono webhooks\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhooks\",\n\t\"Settings.webhooks.trigger\": \"Trigger\",\n\t\"Settings.webhooks.trigger.cancel\": \"Annulla trigger\",\n\t\"Settings.webhooks.trigger.pending\": \"In corso…\",\n\t\"Settings.webhooks.trigger.save\": \"Salva trigger\",\n\t\"Settings.webhooks.trigger.success\": \"Successo!\",\n\t\"Settings.webhooks.trigger.success.label\": \"Trigger eseguito\",\n\t\"Settings.webhooks.trigger.test\": \"Test trigger\",\n\t\"Settings.webhooks.trigger.title\": \"Salva prima di eseguire trigger\",\n\t\"Settings.webhooks.value\": \"Valore\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"Utenti & Permessi\",\n\t\"Users.components.List.empty\": \"Non ci sono utenti...\",\n\t\"Users.components.List.empty.withFilters\": \"Nessun utente trovato con i filtri applicati...\",\n\t\"Users.components.List.empty.withSearch\": \"Nessun utente corrisponde alla ricerca ({search})...\",\n\t\"app.components.BlockLink.code\": \"Esempi di codice\",\n\t\"app.components.Button.cancel\": \"Annulla\",\n\t\"app.components.Button.reset\": \"Ripristina\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"In arrivo\",\n\t\"app.components.DownloadInfo.download\": \"Download in corso...\",\n\t\"app.components.DownloadInfo.text\": \"Potrebbe volerci un minuto. Grazie della pazienza.\",\n\t\"app.components.EmptyAttributes.title\": \"Campi non ancora presenti.\",\n\t\"app.components.HomePage.button.blog\": \"LEGGI DI PIÙ SUL BLOG\",\n\t\"app.components.HomePage.community\": \"Trova la community sul web\",\n\t\"app.components.HomePage.community.content\": \"Discuti con i membri del team, i contributori e gli sviluppatori tramite i nostri canali.\",\n\t\"app.components.HomePage.create\": \"Crea il tuo primo Content-Type\",\n\t\"app.components.HomePage.welcome\": \"Benvenuto a bordo!\",\n\t\"app.components.HomePage.welcome.again\": \"Benvenuto \",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Siamo felici di averti come membro della comunità. Siamo costantemente alla ricerca di feedback, quindi sentitevi liberi di inviarci messaggi diretti su \",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"Speriamo che tu stia facendo progressi sul tuo progetto ... Sentiti libero di leggere l'ultima novità riguardo Strapi. Stiamo dando il massimo per migliorare il prodotto in base al tuo feedback.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"problemi.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" o solleva \",\n\t\"app.components.ImgPreview.hint\": \"Trascina il tuo file in quest'area o {browse} un file da caricare.\",\n\t\"app.components.ImgPreview.hint.browse\": \"cerca\",\n\t\"app.components.InputFile.newFile\": \"Aggiungi nuovo file\",\n\t\"app.components.InputFileDetails.open\": \"Apri in una nuova tab\",\n\t\"app.components.InputFileDetails.originalName\": \"Nome originale:\",\n\t\"app.components.InputFileDetails.remove\": \"Rimuovi questo file\",\n\t\"app.components.InputFileDetails.size\": \"Dimensione:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"Il download e l'installazione del plugin potrebbero richiedere qualche secondo.\",\n\t\"app.components.InstallPluginPage.Download.title\": \"Scaricando...\",\n\t\"app.components.InstallPluginPage.description\": \"Estendi la tua app senza sforzi.\",\n\t\"app.components.LeftMenuFooter.help\": \"Supporto\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Offerto da \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Collezioni\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Configurazioni\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Generale\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Nessun plugin ancora installato\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Plugins\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Entità singole\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"L'installazione del plugin potrebbe richiedere qualche secondo.\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"Disinstalla\",\n\t\"app.components.ListPluginsPage.description\": \"Lista dei plugin installati nel progetto.\",\n\t\"app.components.ListPluginsPage.head.title\": \"Lista plugin\",\n\t\"app.components.Logout.logout\": \"Disconnetti\",\n\t\"app.components.Logout.profile\": \"Profilo\",\n\t\"app.components.NotFoundPage.back\": \"Torna alla home\",\n\t\"app.components.NotFoundPage.description\": \"Non trovato\",\n\t\"app.components.Official\": \"Ufficiale\",\n\t\"app.components.Onboarding.label.completed\": \"% completato\",\n\t\"app.components.Onboarding.title\": \"Video di introduzione\",\n\t\"app.components.PluginCard.Button.label.download\": \"Download\",\n\t\"app.components.PluginCard.Button.label.install\": \"Già installato\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"La funzione autoReload necessità di essere abilitata. Per favore, avvia la app con il comando `yarn develop`.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Ho capito!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Per ragioni di sicurezza, il plugin puo essere scaricato solo in ambiente di sviluppo.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Impossibile scaricare\",\n\t\"app.components.PluginCard.compatible\": \"Compatibile con la tua app\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Compatibile con la comunità\",\n\t\"app.components.PluginCard.more-details\": \"Più dettagli\",\n\t\"app.components.Users.MagicLink.connect\": \"Invia link all'utente per connettersi.\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"Dettagli\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"Ruoli utente\",\n\t\"app.components.Users.SortPicker.button-label\": \"Ordina per\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"Email (A - Z)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"Email (Z - A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"Noma (A - Z)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"Nome (Z - A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"Cognome (A - Z)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"Cognome (Z - A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"Nome utente (A - Z)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"Nome utente (Z - A)\",\n\t\"app.components.listPlugins.button\": \"Aggiungi nuovo plugin\",\n\t\"app.components.listPlugins.title.none\": \"Nessun plugin installato\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Si è verificato un errore durante l'installazione del plugin\",\n\t\"app.containers.App.notification.error.init\": \"Si è verificato un errore durante la richiesta dell'API\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Se non ricevi questo link, contatta l'amministratore.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"La ricezione del link per reimpostare la password potrebbere richiedere qualche secondo.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email inviata\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"Attivo\",\n\t\"app.containers.Users.EditPage.header.label\": \"Modifica {name}\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"Modifica utente\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"Ruoli assegnati\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"Crea utente\",\n\t\"app.links.configure-view\": \"Configura la visualizzazione\",\n\t\"app.static.links.cheatsheet\": \"CheatSheet\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"Aggiungi filtro\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.errors.file-too-big.message\": \"Dimensioni file troppo grandi\",\n\t\"app.utils.filters\": \"Filtri\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"Pubblica\",\n\t\"app.utils.select-all\": \"Seleziona tutti\",\n\t\"app.utils.unpublish\": \"Converti in bozza\",\n\t\"component.Input.error.validation.integer\": \"Il valore deve essere un intero\",\n\t\"components.AutoReloadBlocker.description\": \"Avvia Strapi con uno dei seguenti comandi:\",\n\t\"components.AutoReloadBlocker.header\": \"Ricarica funzionalità è richiesto per questo plugin.\",\n\t\"components.ErrorBoundary.title\": \"Qualcosa è andato storto...\",\n\t\"components.Input.error.attribute.key.taken\": \"Valore già esistente\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Non può essere uguale\",\n\t\"components.Input.error.attribute.taken\": \"Nome campo già esistente\",\n\t\"components.Input.error.contain.lowercase\": \"Password deve contenere almeno una carattere minuscolo\",\n\t\"components.Input.error.contain.number\": \"Password deve contenere almeno un numero\",\n\t\"components.Input.error.contain.uppercase\": \"Password deve contenere almeno una carattere maiuscolo\",\n\t\"components.Input.error.contentTypeName.taken\": \"Nome già esistente\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"La password non corrisponde\",\n\t\"components.Input.error.validation.email\": \"Non è un'email\",\n\t\"components.Input.error.validation.json\": \"Formato JSON non corrisponde\",\n\t\"components.Input.error.validation.max\": \"Valore troppo alto {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"Valore troppo lungo {max}.\",\n\t\"components.Input.error.validation.min\": \"Valore troppo basso {min}.\",\n\t\"components.Input.error.validation.minLength\": \"Valore troppo corto {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"Non può essere superiore\",\n\t\"components.Input.error.validation.regex\": \"Questo valore non coincide con la regex.\",\n\t\"components.Input.error.validation.required\": \"Valore obbligatorio.\",\n\t\"components.Input.error.validation.unique\": \"Questo valore è già usato\",\n\t\"components.InputSelect.option.placeholder\": \"Seleziona\",\n\t\"components.ListRow.empty\": \"Non ci sono dati da mostrare.\",\n\t\"components.OverlayBlocker.description\": \"Stai utilizzando una funzionalità che necessita del riavvio del server. Per favore, attendi che il server ritorni attivo.\",\n\t\"components.OverlayBlocker.description.serverError\": \"Il server deve essere riavviato, per favore controlla i tuoi log nel terminale.\",\n\t\"components.OverlayBlocker.title\": \"Attendo il riavvio...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Il riavvio sta impiegando più del previsto\",\n\t\"components.PageFooter.select\": \"elementi per pagina\",\n\t\"components.ProductionBlocker.description\": \"Per ragioni di sicurezza dobbiamo disabilitare questo plugin in altri ambienti.\",\n\t\"components.ProductionBlocker.header\": \"Questo plugin è disponibile solo in sviluppo.\",\n\t\"components.Search.placeholder\": \"Cerca...\",\n\t\"components.Wysiwyg.collapse\": \"Chiudi\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"Titolo H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"Titolo H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"Titolo H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"Titolo H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"Titolo H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"Titolo H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Aggiungi un titolo\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"caratteri\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Espandi\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Trascina & rilascia file, incolla dagli appunti o {browse}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"selezionali\",\n\t\"components.popUpWarning.button.cancel\": \"No, annulla\",\n\t\"components.popUpWarning.button.confirm\": \"Sì, conferma\",\n\t\"components.popUpWarning.message\": \"Sei sicuro di volerlo cancellare?\",\n\t\"components.popUpWarning.title\": \"Conferma richiesta\",\n\t\"form.button.done\": \"Fatto\",\n\t\"global.prompt.unsaved\": \"Sei sicuro di voler lasciare questa pagina? Tutte le modifiche effettuate verranno perse.\",\n\t\"notification.contentType.relations.conflict\": \"Questo Tipo di Contenuto ha delle relazioni in conflitto\",\n\t\"notification.error\": \"Si è verificato un errore\",\n\t\"notification.error.layout\": \"Non è stato possibile recuperare il layout\",\n\t\"notification.form.error.fields\": \"Il form contiene degli errori\",\n\t\"notification.form.success.fields\": \"Modifiche salvate\",\n\t\"notification.link-copied\": \"Link copiato negli appunti\",\n\t\"notification.permission.not-allowed-read\": \"Non sei abilitato a visualizzare questo documento\",\n\t\"notification.success.delete\": \"L'elemento è stato eliminato\",\n\t\"notification.success.saved\": \"Salvato\",\n\t\"notification.version.update.message\": \"Una nuova versione di Strapi è disponibile!\",\n\t\"request.error.model.unknown\": \"Modello inesistente\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, it as default };\n//# sourceMappingURL=it-T1rZP3NX.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qBAAqB;AAAA,EACrB,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,+BAA+B;AAChC;", "names": []}