{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/PurchaseSingleSignOn.tsx"], "sourcesContent": ["import { Box, Main, EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { ExternalLink } from '@strapi/icons';\nimport { EmptyPermissions } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nimport { Layouts } from '../../../components/Layouts/Layout';\n\nconst PurchaseSingleSignOn = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Layouts.Root>\n      <Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: 'Settings.sso.title',\n            defaultMessage: 'Single Sign-On',\n          })}\n          subtitle={formatMessage({\n            id: 'Settings.sso.subTitle',\n            defaultMessage: 'Configure the settings for the Single Sign-On feature.',\n          })}\n        />\n        <Box paddingLeft={10} paddingRight={10}>\n          <EmptyStateLayout\n            icon={<EmptyPermissions width=\"16rem\" />}\n            content={formatMessage({\n              id: 'Settings.sso.not-available',\n              defaultMessage:\n                'SSO is only available as part of a paid plan. Upgrade to configure additional sign-in & sign-up methods for your administration panel.',\n            })}\n            action={\n              <LinkButton\n                variant=\"default\"\n                endIcon={<ExternalLink />}\n                href=\"https://strp.cc/46Fk1BA\"\n                isExternal\n                target=\"_blank\"\n              >\n                {formatMessage({\n                  id: 'global.learn-more',\n                  defaultMessage: 'Learn more',\n                })}\n              </LinkButton>\n            }\n          />\n        </Box>\n      </Main>\n    </Layouts.Root>\n  );\n};\n\nexport { PurchaseSingleSignOn };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,uBAAuB,MAAM;AAC3B,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,aACG,wBAAA,QAAQ,MAAR,EACC,cAAA,yBAAC,MACC,EAAA,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IAAA;QAEF,wBAAA,KAAA,EAAI,aAAa,IAAI,cAAc,IAClC,cAAA;MAAC;MAAA;QACC,UAAM,wBAAC,cAAiB,EAAA,OAAM,QAAQ,CAAA;QACtC,SAAS,cAAc;UACrB,IAAI;UACJ,gBACE;QAAA,CACH;QACD,YACE;UAAC;UAAA;YACC,SAAQ;YACR,aAAA,wBAAU,eAAa,CAAA,CAAA;YACvB,MAAK;YACL,YAAU;YACV,QAAO;YAEN,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QAAA;MACH;IAAA,EAGN,CAAA;EAAA,EAAA,CACF,EACF,CAAA;AAEJ;", "names": []}