{"version": 3, "sources": ["../../../@strapi/admin/admin/src/components/GuidedTour/Modal.tsx", "../../../@strapi/admin/admin/src/components/MainNav/MainNav.tsx", "../../../@strapi/admin/admin/src/components/MainNav/NavBrand.tsx", "../../../@strapi/admin/admin/src/components/MainNav/NavLink.tsx", "../../../@strapi/admin/admin/src/components/MainNav/NavUser.tsx", "../../../@strapi/admin/admin/src/components/LeftMenu.tsx", "../../../@strapi/admin/admin/src/assets/images/onboarding-preview.png", "../../../@strapi/admin/admin/src/components/Onboarding.tsx", "../../../@strapi/admin/admin/src/components/PluginsInitializer.tsx", "../../../@strapi/admin/admin/src/hooks/useMenu.ts", "../../../@strapi/admin/admin/src/layouts/AuthenticatedLayout.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport {\n  Box,\n  Button,\n  Flex,\n  <PERSON>lex<PERSON>omponent,\n  FocusTrap,\n  IconButton,\n  Portal,\n  Typography,\n  LinkButton,\n} from '@strapi/design-system';\nimport { ArrowRight, Cross } from '@strapi/icons';\nimport get from 'lodash/get';\nimport { MessageDescriptor, useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { useTracking } from '../../features/Tracking';\n\nimport { LAYOUT_DATA, STATES } from './constants';\nimport { Number, VerticalDivider } from './Ornaments';\nimport { GuidedTourContextValue, useGuidedTour } from './Provider';\n\n/* -------------------------------------------------------------------------------------------------\n * GuidedTourModal\n * -----------------------------------------------------------------------------------------------*/\n\nconst GuidedTourModal = () => {\n  const guidedTour = useGuidedTour('GuidedTourModal', (state) => state);\n\n  const {\n    currentStep,\n    guidedTourState,\n    setCurrentStep,\n    setStepState,\n    isGuidedTourVisible,\n    setSkipped,\n  } = guidedTour;\n\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n\n  if (!currentStep || !isGuidedTourVisible) {\n    return null;\n  }\n\n  const stepData = get(LAYOUT_DATA, currentStep);\n  const sectionKeys = Object.keys(guidedTourState);\n  const [sectionName, stepName] = currentStep.split('.') as [\n    keyof GuidedTourContextValue['guidedTourState'],\n    string,\n  ];\n  const sectionIndex = sectionKeys.indexOf(sectionName);\n  const stepIndex = Object.keys(guidedTourState[sectionName]).indexOf(stepName);\n  const hasSectionAfter = sectionIndex < sectionKeys.length - 1;\n  const hasStepAfter = stepIndex < Object.keys(guidedTourState[sectionName]).length - 1;\n\n  const handleCtaClick = () => {\n    setStepState(currentStep, true);\n\n    if (stepData) {\n      trackUsage(stepData.trackingEvent);\n    }\n\n    setCurrentStep(null);\n  };\n\n  const handleSkip = () => {\n    setSkipped(true);\n    setCurrentStep(null);\n    trackUsage('didSkipGuidedtour');\n  };\n\n  return (\n    <Portal>\n      <ModalWrapper onClick={handleCtaClick} padding={8} justifyContent=\"center\">\n        <FocusTrap onEscape={handleCtaClick}>\n          <Flex\n            direction=\"column\"\n            alignItems=\"stretch\"\n            background=\"neutral0\"\n            width={`66rem`}\n            shadow=\"popupShadow\"\n            hasRadius\n            padding={4}\n            gap={8}\n            role=\"dialog\"\n            aria-modal\n            onClick={(e) => e.stopPropagation()}\n          >\n            <Flex justifyContent=\"flex-end\">\n              <IconButton\n                onClick={handleCtaClick}\n                withTooltip={false}\n                label={formatMessage({\n                  id: 'app.utils.close-label',\n                  defaultMessage: 'Close',\n                })}\n              >\n                <Cross />\n              </IconButton>\n            </Flex>\n            <Box\n              paddingLeft={7}\n              paddingRight={7}\n              paddingBottom={!hasStepAfter && !hasSectionAfter ? 8 : 0}\n            >\n              <GuidedTourStepper\n                title={stepData && 'title' in stepData ? stepData.title : undefined}\n                cta={stepData && 'cta' in stepData ? stepData.cta : undefined}\n                onCtaClick={handleCtaClick}\n                sectionIndex={sectionIndex}\n                stepIndex={stepIndex}\n                hasSectionAfter={hasSectionAfter}\n              >\n                {stepData && 'content' in stepData && <GuidedTourContent {...stepData.content} />}\n              </GuidedTourStepper>\n            </Box>\n            {!(!hasStepAfter && !hasSectionAfter) && (\n              <Flex justifyContent=\"flex-end\">\n                <Button variant=\"tertiary\" onClick={handleSkip}>\n                  {formatMessage({\n                    id: 'app.components.GuidedTour.skip',\n                    defaultMessage: 'Skip the tour',\n                  })}\n                </Button>\n              </Flex>\n            )}\n          </Flex>\n        </FocusTrap>\n      </ModalWrapper>\n    </Portal>\n  );\n};\n\nconst ModalWrapper = styled<FlexComponent>(Flex)`\n  position: fixed;\n  z-index: 4;\n  inset: 0;\n  /* this is theme.colors.neutral800 with opacity */\n  background: ${({ theme }) => `${theme.colors.neutral800}1F`};\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * GuidedTourStepper\n * -----------------------------------------------------------------------------------------------*/\n\ninterface GuidedTourStepperProps {\n  title: MessageDescriptor | undefined;\n  children: React.ReactNode;\n  cta?: {\n    title: MessageDescriptor;\n    target?: string;\n  };\n  onCtaClick: () => void;\n  sectionIndex: number;\n  stepIndex: number;\n  hasSectionAfter: boolean;\n}\n\nconst GuidedTourStepper = ({\n  title,\n  children,\n  cta,\n  onCtaClick,\n  sectionIndex,\n  stepIndex,\n  hasSectionAfter,\n}: GuidedTourStepperProps) => {\n  const { formatMessage } = useIntl();\n\n  const hasSectionBefore = sectionIndex > 0;\n  const hasStepsBefore = stepIndex > 0;\n  const nextSectionIndex = sectionIndex + 1;\n\n  return (\n    <>\n      <Flex alignItems=\"stretch\">\n        <Flex marginRight={8} justifyContent=\"center\" minWidth={`3rem`}>\n          {hasSectionBefore && <VerticalDivider state={STATES.IS_DONE} minHeight={`2.4rem`} />}\n        </Flex>\n        <Typography variant=\"sigma\" textColor=\"primary600\">\n          {formatMessage({\n            id: 'app.components.GuidedTour.title',\n            defaultMessage: '3 steps to get started',\n          })}\n        </Typography>\n      </Flex>\n      <Flex>\n        <Flex marginRight={8} minWidth={`3rem`}>\n          <Number\n            state={hasStepsBefore ? STATES.IS_DONE : STATES.IS_ACTIVE}\n            paddingTop={3}\n            paddingBottom={3}\n          >\n            {sectionIndex + 1}\n          </Number>\n        </Flex>\n        {title && (\n          <Typography variant=\"alpha\" fontWeight=\"bold\" textColor=\"neutral800\" tag=\"h3\" id=\"title\">\n            {formatMessage(title)}\n          </Typography>\n        )}\n      </Flex>\n      <Flex alignItems=\"stretch\">\n        <Flex marginRight={8} direction=\"column\" justifyContent=\"center\" minWidth={`3rem`}>\n          {hasSectionAfter && (\n            <>\n              <VerticalDivider state={STATES.IS_DONE} />\n              {hasStepsBefore && (\n                <Number state={STATES.IS_ACTIVE} paddingTop={3}>\n                  {nextSectionIndex + 1}\n                </Number>\n              )}\n            </>\n          )}\n        </Flex>\n        <Box>\n          {children}\n          {cta &&\n            (cta.target ? (\n              <LinkButton\n                tag={NavLink}\n                endIcon={<ArrowRight />}\n                onClick={onCtaClick}\n                to={cta.target}\n              >\n                {formatMessage(cta.title)}\n              </LinkButton>\n            ) : (\n              <Button endIcon={<ArrowRight />} onClick={onCtaClick}>\n                {formatMessage(cta.title)}\n              </Button>\n            ))}\n        </Box>\n      </Flex>\n      {hasStepsBefore && hasSectionAfter && (\n        <Box paddingTop={3}>\n          <Flex marginRight={8} justifyContent=\"center\" width={`3rem`}>\n            <VerticalDivider state={STATES.IS_DONE} minHeight={`2.4rem`} />\n          </Flex>\n        </Box>\n      )}\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * GuidedTourContent\n * -----------------------------------------------------------------------------------------------*/\n\ninterface GuidedTourContentProps\n  extends Required<Pick<MessageDescriptor, 'defaultMessage' | 'id'>> {}\n\nconst GuidedTourContent = ({ id, defaultMessage }: GuidedTourContentProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Flex direction=\"column\" alignItems=\"stretch\" gap={4} paddingBottom={6}>\n      {formatMessage(\n        { id, defaultMessage },\n        {\n          documentationLink: DocumentationLink,\n          b: Bold,\n          p: Paragraph,\n          light: Light,\n          ul: List,\n          li: ListItem,\n        }\n      )}\n    </Flex>\n  );\n};\n\nconst DocumentationLink = (children: React.ReactNode) => (\n  <Typography\n    tag=\"a\"\n    textColor=\"primary600\"\n    target=\"_blank\"\n    rel=\"noopener noreferrer\"\n    href=\"https://docs.strapi.io/developer-docs/latest/developer-resources/database-apis-reference/rest-api.html#api-parameters\"\n  >\n    {children}\n  </Typography>\n);\n\nconst Bold = (children: React.ReactNode) => (\n  <Typography fontWeight=\"semiBold\">{children}</Typography>\n);\n\nconst Paragraph = (children: React.ReactNode) => <Typography>{children}</Typography>;\n\nconst Light = (children: React.ReactNode) => (\n  <Typography textColor=\"neutral600\">{children}</Typography>\n);\n\nconst List = (children: React.ReactNode) => (\n  <Box paddingLeft={6}>\n    <ul>{children}</ul>\n  </Box>\n);\n\nconst LiStyled = styled.li`\n  list-style: disc;\n  &::marker {\n    color: ${({ theme }) => theme.colors.neutral800};\n  }\n`;\n\nconst ListItem = (children: React.ReactNode) => <LiStyled>{children}</LiStyled>;\n\nexport { GuidedTourModal };\n", "import { Flex, FlexComponent, FlexProps } from '@strapi/design-system';\nimport { styled } from 'styled-components';\n\nconst MainNavWrapper = styled<FlexComponent<'nav'>>(Flex)`\n  border-right: 1px solid ${({ theme }) => theme.colors.neutral150};\n`;\n\nconst MainNav = (props: FlexProps<'nav'>) => (\n  <MainNavWrapper\n    alignItems=\"normal\"\n    tag=\"nav\"\n    background=\"neutral0\"\n    direction=\"column\"\n    height=\"100vh\"\n    position=\"sticky\"\n    top={0}\n    zIndex={2}\n    width={10}\n    {...props}\n  />\n);\n\nexport { MainNav };\n", "import { Box, Flex, type FlexComponent, VisuallyHidden } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useConfiguration } from '../../features/Configuration';\n\nconst BrandIconWrapper = styled<FlexComponent>(Flex)`\n  svg,\n  img {\n    border-radius: ${({ theme }) => theme.borderRadius};\n    object-fit: contain;\n    height: 2.4rem;\n    width: 2.4rem;\n  }\n`;\n\nexport const NavBrand = () => {\n  const { formatMessage } = useIntl();\n  const {\n    logos: { menu },\n  } = useConfiguration('LeftMenu');\n  return (\n    <Box padding={3}>\n      <BrandIconWrapper direction=\"column\" justifyContent=\"center\" width=\"3.2rem\" height=\"3.2rem\">\n        <img\n          src={menu.custom?.url || menu.default}\n          alt={formatMessage({\n            id: 'app.components.LeftMenu.logo.alt',\n            defaultMessage: 'Application logo',\n          })}\n          width=\"100%\"\n          height=\"100%\"\n        />\n        <VisuallyHidden>\n          <span>\n            {formatMessage({\n              id: 'app.components.LeftMenu.navbrand.title',\n              defaultMessage: 'Strapi Dashboard',\n            })}\n          </span>\n          <span>\n            {formatMessage({\n              id: 'app.components.LeftMenu.navbrand.workplace',\n              defaultMessage: 'Workplace',\n            })}\n          </span>\n        </VisuallyHidden>\n      </BrandIconWrapper>\n    </Box>\n  );\n};\n", "import * as React from 'react';\n\nimport {\n  Tooltip,\n  TooltipProps as DSTooltipProps,\n  Badge,\n  BadgeProps,\n  AccessibleIcon,\n} from '@strapi/design-system';\nimport { NavLink as RouterLink, LinkProps } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\n/* -------------------------------------------------------------------------------------------------\n * Link\n * -----------------------------------------------------------------------------------------------*/\nconst MainNavLinkWrapper = styled(RouterLink)`\n  text-decoration: none;\n  display: flex;\n  border-radius: ${({ theme }) => theme.borderRadius};\n  background: ${({ theme }) => theme.colors.neutral0};\n  color: ${({ theme }) => theme.colors.neutral500};\n  position: relative;\n  width: fit-content;\n  padding-block: 0.6rem;\n  padding-inline: 0.6rem;\n\n  &:hover,\n  &.active {\n    background: ${({ theme }) => theme.colors.neutral100};\n  }\n\n  &:hover {\n    svg path {\n      fill: ${({ theme }) => theme.colors.neutral600};\n    }\n    color: ${({ theme }) => theme.colors.neutral700};\n  }\n\n  &.active {\n    svg path {\n      fill: ${({ theme }) => theme.colors.primary600};\n    }\n\n    color: ${({ theme }) => theme.colors.primary600};\n    font-weight: 500;\n  }\n`;\n\nconst LinkImpl = ({ children, ...props }: LinkProps) => {\n  return <MainNavLinkWrapper {...props}>{children}</MainNavLinkWrapper>;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Tooltip\n * -----------------------------------------------------------------------------------------------*/\nconst TooltipImpl = ({ children, label, position = 'right' }: NavLink.TooltipProps) => {\n  return (\n    <Tooltip side={position} label={label} delayDuration={0}>\n      <span>{children}</span>\n    </Tooltip>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Icon\n * -----------------------------------------------------------------------------------------------*/\nconst IconImpl = ({ label, children }: { label: string; children: React.ReactNode }) => {\n  if (!children) {\n    return null;\n  }\n  return <AccessibleIcon label={label}>{children}</AccessibleIcon>;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Badge\n * -----------------------------------------------------------------------------------------------*/\nconst CustomBadge = styled(Badge)`\n  /* override default badge styles to change the border radius of the Base element in the Design System */\n  border-radius: ${({ theme }) => theme.spaces[10]};\n  height: 2rem;\n`;\n\nconst BadgeImpl = ({ children, label, ...props }: NavLink.NavBadgeProps) => {\n  if (!children) {\n    return null;\n  }\n  return (\n    <CustomBadge\n      position=\"absolute\"\n      top=\"-0.8rem\"\n      left=\"1.7rem\"\n      aria-label={label}\n      active={false}\n      {...props}\n    >\n      {children}\n    </CustomBadge>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * EXPORTS\n * -----------------------------------------------------------------------------------------------*/\n\nconst NavLink = {\n  Link: LinkImpl,\n  Tooltip: TooltipImpl,\n  Icon: IconImpl,\n  Badge: BadgeImpl,\n};\n\n// eslint-disable-next-line @typescript-eslint/no-namespace\nnamespace NavLink {\n  export interface NavBadgeProps extends BadgeProps {\n    children: React.ReactNode;\n    label: string;\n  }\n\n  export interface TooltipProps {\n    children: React.ReactNode;\n    label?: string;\n    position?: DSTooltipProps['side'];\n  }\n}\n\nexport { NavLink };\n", "import * as React from 'react';\n\nimport { Flex, Menu, ButtonProps, VisuallyHidden, Avatar } from '@strapi/design-system';\nimport { SignOut } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useNavigate } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { useAuth } from '../../features/Auth';\n\nexport interface NavUserProps extends ButtonProps {\n  initials: string;\n  children?: React.ReactNode;\n}\n\nconst MenuTrigger = styled(Menu.Trigger)`\n  height: 100%;\n  border-radius: 0;\n  border-width: 1px 0 0 0;\n  border-color: ${({ theme }) => theme.colors.neutral150};\n  border-style: solid;\n  padding: ${({ theme }) => theme.spaces[3]};\n  // padding 12px - 1px border width\n  padding-top: 11px;\n  // Prevent empty pixel from appearing below the main nav\n  overflow: hidden;\n`;\n\nconst MenuContent = styled(Menu.Content)`\n  left: ${({ theme }) => theme.spaces[5]};\n`;\n\nconst MenuItem = styled(Menu.Item)`\n  & > span {\n    width: 100%;\n    display: flex;\n    align-items: center;\n    gap: ${({ theme }) => theme.spaces[3]};\n    justify-content: space-between;\n  }\n`;\n\nexport const NavUser = ({ children, initials, ...props }: NavUserProps) => {\n  const { formatMessage } = useIntl();\n  const navigate = useNavigate();\n  const logout = useAuth('Logout', (state) => state.logout);\n  const handleProfile = () => {\n    navigate('/me');\n  };\n  const handleLogout = () => {\n    logout();\n    navigate('/auth/login');\n  };\n\n  return (\n    <Flex justifyContent=\"center\" {...props}>\n      <Menu.Root>\n        <MenuTrigger endIcon={null} fullWidth justifyContent=\"center\">\n          <Avatar.Item delayMs={0} fallback={initials} />\n          <VisuallyHidden tag=\"span\">{children}</VisuallyHidden>\n        </MenuTrigger>\n        <MenuContent popoverPlacement=\"top-center\" zIndex={3}>\n          <MenuItem onSelect={handleProfile}>\n            {formatMessage({\n              id: 'global.profile',\n              defaultMessage: 'Profile',\n            })}\n          </MenuItem>\n\n          <MenuItem onSelect={handleLogout} color=\"danger600\">\n            {formatMessage({\n              id: 'app.components.LeftMenu.logout',\n              defaultMessage: 'Logout',\n            })}\n            <SignOut />\n          </MenuItem>\n        </MenuContent>\n      </Menu.Root>\n    </Flex>\n  );\n};\n", "import * as React from 'react';\n\nimport { Divider, Flex, FlexComponent, useCollator } from '@strapi/design-system';\nimport { Lightning } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useLocation } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { useAuth } from '../features/Auth';\nimport { useTracking } from '../features/Tracking';\nimport { Menu, MenuItem } from '../hooks/useMenu';\nimport { getDisplayName } from '../utils/users';\n\nimport { MainNav } from './MainNav/MainNav';\nimport { NavBrand } from './MainNav/NavBrand';\nimport { NavLink } from './MainNav/NavLink';\nimport { NavUser } from './MainNav/NavUser';\n\nconst sortLinks = (links: MenuItem[]) => {\n  return links.sort((a, b) => {\n    // if no position is defined, we put the link in the position of the external plugins, before the plugins list\n    const positionA = a.position ?? 6;\n    const positionB = b.position ?? 6;\n\n    if (positionA < positionB) {\n      return -1;\n    } else {\n      return 1;\n    }\n  });\n};\n\nconst NavLinkBadgeCounter = styled(NavLink.Badge)`\n  span {\n    color: ${({ theme }) => theme.colors.neutral0};\n  }\n`;\n\nconst NavLinkBadgeLock = styled(NavLink.Badge)`\n  background-color: transparent;\n`;\n\nconst NavListWrapper = styled<FlexComponent<'ul'>>(Flex)`\n  overflow-y: auto;\n`;\n\ninterface LeftMenuProps extends Pick<Menu, 'generalSectionLinks' | 'pluginsSectionLinks'> {}\n\nconst LeftMenu = ({ generalSectionLinks, pluginsSectionLinks }: LeftMenuProps) => {\n  const user = useAuth('AuthenticatedApp', (state) => state.user);\n  const { trackUsage } = useTracking();\n  const { pathname } = useLocation();\n  const userDisplayName = getDisplayName(user);\n  const { formatMessage, locale } = useIntl();\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const initials = userDisplayName\n    .split(' ')\n    .map((name) => name.substring(0, 1))\n    .join('')\n    .substring(0, 2);\n\n  const handleClickOnLink = (destination: string) => {\n    trackUsage('willNavigate', { from: pathname, to: destination });\n  };\n\n  const listLinksAlphabeticallySorted = [...pluginsSectionLinks, ...generalSectionLinks].sort(\n    (a, b) => formatter.compare(formatMessage(a.intlLabel), formatMessage(b.intlLabel))\n  );\n  const listLinks = sortLinks(listLinksAlphabeticallySorted);\n\n  return (\n    <MainNav>\n      <NavBrand />\n\n      <Divider />\n\n      <NavListWrapper tag=\"ul\" gap={3} direction=\"column\" flex={1} paddingTop={3} paddingBottom={3}>\n        {listLinks.length > 0\n          ? listLinks.map((link) => {\n              const LinkIcon = link.icon;\n              const badgeContentLock = link?.licenseOnly ? (\n                <Lightning fill=\"warning500\" />\n              ) : undefined;\n\n              const badgeContentNumeric =\n                link.notificationsCount && link.notificationsCount > 0\n                  ? link.notificationsCount.toString()\n                  : undefined;\n\n              const labelValue = formatMessage(link.intlLabel);\n              return (\n                <Flex tag=\"li\" key={link.to}>\n                  <NavLink.Tooltip label={labelValue}>\n                    <NavLink.Link\n                      to={link.to}\n                      onClick={() => handleClickOnLink(link.to)}\n                      aria-label={labelValue}\n                    >\n                      <NavLink.Icon label={labelValue}>\n                        <LinkIcon width=\"20\" height=\"20\" fill=\"neutral500\" />\n                      </NavLink.Icon>\n                      {badgeContentLock ? (\n                        <NavLinkBadgeLock\n                          label=\"locked\"\n                          textColor=\"neutral500\"\n                          paddingLeft={0}\n                          paddingRight={0}\n                        >\n                          {badgeContentLock}\n                        </NavLinkBadgeLock>\n                      ) : badgeContentNumeric ? (\n                        <NavLinkBadgeCounter\n                          label={badgeContentNumeric}\n                          backgroundColor=\"primary600\"\n                          width=\"2.3rem\"\n                          color=\"neutral0\"\n                        >\n                          {badgeContentNumeric}\n                        </NavLinkBadgeCounter>\n                      ) : null}\n                    </NavLink.Link>\n                  </NavLink.Tooltip>\n                </Flex>\n              );\n            })\n          : null}\n      </NavListWrapper>\n      <NavUser initials={initials}>{userDisplayName}</NavUser>\n    </MainNav>\n  );\n};\n\nexport { LeftMenu };\n", "export default \"data:image/png;base64,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\"", "import * as React from 'react';\n\nimport {\n  <PERSON>,\n  <PERSON><PERSON>,\n  Divider,\n  <PERSON>lex,\n  Flex<PERSON><PERSON>ponent,\n  Popover,\n  Typography,\n  TypographyComponent,\n  VisuallyHidden,\n} from '@strapi/design-system';\nimport { Cross, Message, Play, Question, Book, PaperPlane } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport onboardingPreview from '../assets/images/onboarding-preview.png';\nimport { useAppInfo } from '../features/AppInfo';\n\nconst Onboarding = () => {\n  const [isOpen, setIsOpen] = React.useState(false);\n  const { formatMessage } = useIntl();\n  const communityEdition = useAppInfo('Onboarding', (state) => state.communityEdition);\n\n  const docLinks = [\n    ...DOCUMENTATION_LINKS,\n    {\n      label: { id: 'Settings.application.get-help', defaultMessage: 'Get help' },\n      icon: Message,\n      href: communityEdition\n        ? 'https://discord.strapi.io'\n        : 'https://support.strapi.io/support/home',\n    },\n  ];\n\n  const Icon = isOpen ? Cross : Question;\n\n  return (\n    <Popover.Root onOpenChange={setIsOpen}>\n      <Box position=\"fixed\" bottom={2} right={2}>\n        <Popover.Trigger>\n          <HelperButton\n            aria-label={formatMessage(\n              isOpen\n                ? {\n                    id: 'app.components.Onboarding.help.button-close',\n                    defaultMessage: 'Close help menu',\n                  }\n                : {\n                    id: 'app.components.Onboarding.help.button',\n                    defaultMessage: 'Open help menu',\n                  }\n            )}\n          >\n            <Icon fill=\"buttonNeutral0\" />\n          </HelperButton>\n        </Popover.Trigger>\n        <Popover.Content align=\"end\" side=\"top\" sideOffset={12}>\n          <Flex\n            justifyContent=\"space-between\"\n            paddingBottom={5}\n            paddingRight={6}\n            paddingLeft={6}\n            paddingTop={6}\n          >\n            <TypographyLineHeight fontWeight=\"bold\">\n              {formatMessage({\n                id: 'app.components.Onboarding.title',\n                defaultMessage: 'Get started videos',\n              })}\n            </TypographyLineHeight>\n            <TextLink\n              tag=\"a\"\n              href={WATCH_MORE.href}\n              target=\"_blank\"\n              rel=\"noreferrer noopener\"\n              variant=\"pi\"\n              textColor=\"primary600\"\n            >\n              {formatMessage(WATCH_MORE.label)}\n            </TextLink>\n          </Flex>\n          <Divider />\n          {VIDEO_LINKS.map(({ href, duration, label }, index) => (\n            <VideoLinkWrapper\n              tag=\"a\"\n              href={href}\n              target=\"_blank\"\n              rel=\"noreferrer noopener\"\n              key={href}\n              hasRadius\n              paddingTop={4}\n              paddingBottom={4}\n              paddingLeft={6}\n              paddingRight={11}\n            >\n              <Box paddingRight={5}>\n                <Number textColor=\"neutral200\" variant=\"alpha\">\n                  {index + 1}\n                </Number>\n              </Box>\n              <Box position=\"relative\">\n                <Preview src={onboardingPreview} alt=\"\" />\n                <IconWrapper\n                  position=\"absolute\"\n                  top=\"50%\"\n                  left=\"50%\"\n                  background=\"primary600\"\n                  borderRadius=\"50%\"\n                  justifyContent=\"center\"\n                  width={6}\n                  height={6}\n                >\n                  <Play fill=\"buttonNeutral0\" width=\"12\" height=\"12\" />\n                </IconWrapper>\n              </Box>\n              <Flex direction=\"column\" alignItems=\"start\" paddingLeft={4}>\n                <Label fontWeight=\"bold\">{formatMessage(label)}</Label>\n                <VisuallyHidden>:</VisuallyHidden>\n                <Typography textColor=\"neutral600\" variant=\"pi\">\n                  {duration}\n                </Typography>\n              </Flex>\n            </VideoLinkWrapper>\n          ))}\n          <Flex\n            direction=\"column\"\n            alignItems=\"stretch\"\n            gap={2}\n            paddingLeft={5}\n            paddingTop={2}\n            paddingBottom={5}\n          >\n            {docLinks.map(({ label, href, icon: Icon }) => (\n              <Flex gap={3} key={href}>\n                <Icon fill=\"primary600\" />\n                <TextLink\n                  tag=\"a\"\n                  href={href}\n                  target=\"_blank\"\n                  rel=\"noreferrer noopener\"\n                  variant=\"sigma\"\n                  textColor=\"primary700\"\n                >\n                  {formatMessage(label)}\n                </TextLink>\n              </Flex>\n            ))}\n          </Flex>\n        </Popover.Content>\n      </Box>\n    </Popover.Root>\n  );\n};\n\n// TODO: use new Button props derived from Box props with next DS release\nconst HelperButton = styled(Button)`\n  border-radius: 50%;\n  padding: ${({ theme }) => theme.spaces[3]};\n  /* Resetting 2rem height defined by Button component */\n  height: unset;\n  width: unset;\n\n  & > span {\n    display: flex;\n\n    svg {\n      width: 1.6rem;\n      height: 1.6rem;\n    }\n  }\n`;\n\nconst IconWrapper = styled<FlexComponent>(Flex)`\n  transform: translate(-50%, -50%);\n`;\n\nconst Number = styled<TypographyComponent>(Typography)``;\n\nconst Label = styled<TypographyComponent>(Typography)``;\n\nconst VideoLinkWrapper = styled<FlexComponent<'a'>>(Flex)`\n  text-decoration: none;\n\n  :focus-visible {\n    outline-offset: ${({ theme }) => `-${theme.spaces[1]}`};\n  }\n\n  :hover {\n    background: ${({ theme }) => theme.colors.primary100};\n\n    /* Hover style for the number displayed */\n    ${Number} {\n      color: ${({ theme }) => theme.colors.primary500};\n    }\n\n    /* Hover style for the label */\n    ${Label} {\n      color: ${({ theme }) => theme.colors.primary600};\n    }\n  }\n`;\n\nconst Preview = styled.img`\n  width: ${({ theme }) => theme.spaces[10]};\n  height: ${({ theme }) => theme.spaces[8]};\n  /* Same overlay used in ModalLayout */\n  background: ${({ theme }) => `${theme.colors.neutral800}1F`};\n  border-radius: ${({ theme }) => theme.borderRadius};\n`;\n\nconst TypographyLineHeight = styled<TypographyComponent>(Typography)`\n  /* line height of label and watch more to 1 so they can be better aligned visually */\n  line-height: 1;\n`;\n\nconst TextLink = styled<TypographyComponent<'a'>>(Typography)`\n  text-decoration: none;\n  line-height: 1;\n\n  :hover {\n    text-decoration: underline;\n  }\n`;\n\nconst VIDEO_LINKS = [\n  {\n    label: {\n      id: 'app.components.Onboarding.link.build-content',\n      defaultMessage: 'Build a content architecture',\n    },\n    href: 'https://www.youtube.com/watch?v=G9GjN0RxhkE',\n    duration: '5:48',\n  },\n  {\n    label: {\n      id: 'app.components.Onboarding.link.manage-content',\n      defaultMessage: 'Add & manage content',\n    },\n    href: 'https://www.youtube.com/watch?v=DEZw4KbybAI',\n    duration: '3:18',\n  },\n  {\n    label: { id: 'app.components.Onboarding.link.manage-media', defaultMessage: 'Manage media' },\n    href: 'https://www.youtube.com/watch?v=-61MuiMQb38',\n    duration: '3:41',\n  },\n];\n\nconst WATCH_MORE = {\n  href: 'https://www.youtube.com/playlist?list=PL7Q0DQYATmvidz6lEmwE5nIcOAYagxWqq',\n  label: {\n    id: 'app.components.Onboarding.link.more-videos',\n    defaultMessage: 'Watch more videos',\n  },\n};\n\nconst DOCUMENTATION_LINKS = [\n  {\n    label: { id: 'global.documentation', defaultMessage: 'documentation' },\n    href: 'https://docs.strapi.io',\n    icon: Book,\n  },\n  {\n    label: { id: 'app.static.links.cheatsheet', defaultMessage: 'cheatsheet' },\n    href: 'https://strapi-showcase.s3-us-west-2.amazonaws.com/CheatSheet.pdf',\n    icon: PaperPlane,\n  },\n];\n\nexport { Onboarding };\n", "import * as React from 'react';\n\nimport { produce } from 'immer';\nimport set from 'lodash/set';\n\nimport { Page } from '../components/PageHelpers';\nimport { StrapiAppContextValue, useStrapiApp } from '../features/StrapiApp';\n\n/**\n * TODO: this isn't great, and we really should focus on fixing this.\n */\nconst PluginsInitializer = ({ children }: { children: React.ReactNode }) => {\n  const appPlugins = useStrapiApp('PluginsInitializer', (state) => state.plugins);\n  const [{ plugins }, dispatch] = React.useReducer<React.Reducer<State, Action>, State>(\n    reducer,\n    initialState,\n    () => init(appPlugins)\n  );\n  const setPlugin = React.useRef((pluginId: string) => {\n    dispatch({ type: 'SET_PLUGIN_READY', pluginId });\n  });\n\n  const hasApluginNotReady = Object.keys(plugins).some(\n    (plugin) => plugins[plugin].isReady === false\n  );\n\n  /**\n   *\n   * I have spent some time trying to understand what is happening here, and wanted to\n   * leave that knowledge for my future me:\n   *\n   * `initializer` is an undocumented property of the `registerPlugin` API. At the time\n   * of writing it seems only to be used by the i18n plugin.\n   *\n   * How does it work?\n   *\n   * Every plugin that has an `initializer` component defined, receives the\n   * `setPlugin` function as a component prop. In the case of i18n the plugin fetches locales\n   * first and calls `setPlugin` with `pluginId` once they are loaded, which then triggers the\n   * reducer of the admin app defined above.\n   *\n   * Once all plugins are set to `isReady: true` the app renders.\n   *\n   * This API is used to block rendering of the admin app. We should remove that in v5 completely\n   * and make sure plugins can inject data into the global store before they are initialized, to avoid\n   * having a new prop-callback based communication channel between plugins and the core admin app.\n   *\n   */\n\n  if (hasApluginNotReady) {\n    const initializers = Object.keys(plugins).reduce((acc, current) => {\n      const InitializerComponent = plugins[current].initializer;\n\n      if (InitializerComponent) {\n        const key = plugins[current].pluginId;\n\n        acc.push(<InitializerComponent key={key} setPlugin={setPlugin.current} />);\n      }\n\n      return acc;\n    }, [] as React.ReactNode[]);\n\n    return (\n      <>\n        {initializers}\n        <Page.Loading />\n      </>\n    );\n  }\n\n  return children;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Reducer\n * -----------------------------------------------------------------------------------------------*/\n\ninterface State {\n  plugins: StrapiAppContextValue['plugins'];\n}\n\nconst initialState: State = {\n  plugins: {},\n};\n\ntype SetPluginReadyAction = {\n  type: 'SET_PLUGIN_READY';\n  pluginId: string;\n};\n\ntype Action = SetPluginReadyAction;\n\nconst reducer: React.Reducer<State, Action> = (state = initialState, action: Action): State =>\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case 'SET_PLUGIN_READY': {\n        set(draftState, ['plugins', action.pluginId, 'isReady'], true);\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n\n/* -------------------------------------------------------------------------------------------------\n * Init state\n * -----------------------------------------------------------------------------------------------*/\n\nconst init = (plugins: State['plugins']): State => {\n  return {\n    plugins,\n  };\n};\n\nexport { PluginsInitializer };\n", "import * as React from 'react';\n\nimport { Cog, ShoppingCart, House } from '@strapi/icons';\nimport cloneDeep from 'lodash/cloneDeep';\n\nimport { useTypedSelector } from '../core/store/hooks';\nimport { useAuth, AuthContextValue } from '../features/Auth';\nimport { StrapiAppContextValue, useStrapiApp } from '../features/StrapiApp';\n\n/* -------------------------------------------------------------------------------------------------\n * useMenu\n * -----------------------------------------------------------------------------------------------*/\n\nexport type MenuItem = Omit<StrapiAppContextValue['menu'][number], 'Component'>;\n\nexport interface Menu {\n  generalSectionLinks: MenuItem[];\n  pluginsSectionLinks: MenuItem[];\n  isLoading: boolean;\n}\n\nconst useMenu = (shouldUpdateStrapi: boolean) => {\n  const checkUserHasPermissions = useAuth('useMenu', (state) => state.checkUserHasPermissions);\n  const menu = useStrapiApp('useMenu', (state) => state.menu);\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n  const [menuWithUserPermissions, setMenuWithUserPermissions] = React.useState<Menu>({\n    generalSectionLinks: [\n      {\n        icon: House,\n        intlLabel: {\n          id: 'global.home',\n          defaultMessage: 'Home',\n        },\n        to: '/',\n        permissions: [],\n        position: 0,\n      },\n      {\n        icon: ShoppingCart,\n        intlLabel: {\n          id: 'global.marketplace',\n          defaultMessage: 'Marketplace',\n        },\n        to: '/marketplace',\n        permissions: permissions.marketplace?.main ?? [],\n        position: 7,\n      },\n      {\n        icon: Cog,\n        intlLabel: {\n          id: 'global.settings',\n          defaultMessage: 'Settings',\n        },\n        to: '/settings',\n        // Permissions of this link are retrieved in the init phase\n        // using the settings menu\n        permissions: [],\n        notificationsCount: 0,\n        position: 9,\n      },\n    ],\n    pluginsSectionLinks: [],\n    isLoading: true,\n  });\n  const generalSectionLinksRef = React.useRef(menuWithUserPermissions.generalSectionLinks);\n\n  React.useEffect(() => {\n    async function applyMenuPermissions() {\n      const authorizedPluginSectionLinks = await getPluginSectionLinks(\n        menu,\n        checkUserHasPermissions\n      );\n\n      const authorizedGeneralSectionLinks = await getGeneralLinks(\n        generalSectionLinksRef.current,\n        shouldUpdateStrapi,\n        checkUserHasPermissions\n      );\n\n      setMenuWithUserPermissions((state) => ({\n        ...state,\n        generalSectionLinks: authorizedGeneralSectionLinks,\n        pluginsSectionLinks: authorizedPluginSectionLinks,\n        isLoading: false,\n      }));\n    }\n\n    applyMenuPermissions();\n  }, [\n    setMenuWithUserPermissions,\n    generalSectionLinksRef,\n    menu,\n    permissions,\n    shouldUpdateStrapi,\n    checkUserHasPermissions,\n  ]);\n\n  return menuWithUserPermissions;\n};\n\n/* -------------------------------------------------------------------------------------------------\n * Utils\n * -----------------------------------------------------------------------------------------------*/\n\nconst getGeneralLinks = async (\n  generalSectionRawLinks: MenuItem[],\n  shouldUpdateStrapi: boolean = false,\n  checkUserHasPermissions: AuthContextValue['checkUserHasPermissions']\n) => {\n  const generalSectionLinksPermissions = await Promise.all(\n    generalSectionRawLinks.map(({ permissions }) => checkUserHasPermissions(permissions))\n  );\n\n  const authorizedGeneralSectionLinks = generalSectionRawLinks.filter(\n    (_, index) => generalSectionLinksPermissions[index].length > 0\n  );\n\n  const settingsLinkIndex = authorizedGeneralSectionLinks.findIndex(\n    (obj) => obj.to === '/settings'\n  );\n\n  if (settingsLinkIndex === -1) {\n    return [];\n  }\n\n  const authorizedGeneralLinksClone = cloneDeep(authorizedGeneralSectionLinks);\n\n  authorizedGeneralLinksClone[settingsLinkIndex].notificationsCount = shouldUpdateStrapi ? 1 : 0;\n\n  return authorizedGeneralLinksClone;\n};\n\nconst getPluginSectionLinks = async (\n  pluginsSectionRawLinks: MenuItem[],\n  checkUserHasPermissions: AuthContextValue['checkUserHasPermissions']\n) => {\n  const pluginSectionLinksPermissions = await Promise.all(\n    pluginsSectionRawLinks.map(({ permissions }) => checkUserHasPermissions(permissions))\n  );\n\n  const authorizedPluginSectionLinks = pluginsSectionRawLinks.filter(\n    (_, index) => pluginSectionLinksPermissions[index].length > 0\n  );\n\n  return authorizedPluginSectionLinks;\n};\n\nexport { useMenu };\n", "import * as React from 'react';\n\nimport { Box, Flex, SkipToContent } from '@strapi/design-system';\nimport { DndProvider } from 'react-dnd';\nimport { HTML5Backend } from 'react-dnd-html5-backend';\nimport { useIntl } from 'react-intl';\nimport { Outlet } from 'react-router-dom';\nimport lt from 'semver/functions/lt';\nimport valid from 'semver/functions/valid';\n\nimport packageJSON from '../../../package.json';\nimport { GuidedTourModal } from '../components/GuidedTour/Modal';\nimport { useGuidedTour } from '../components/GuidedTour/Provider';\nimport { LeftMenu } from '../components/LeftMenu';\nimport { NpsSurvey } from '../components/NpsSurvey';\nimport { Onboarding } from '../components/Onboarding';\nimport { Page } from '../components/PageHelpers';\nimport { PluginsInitializer } from '../components/PluginsInitializer';\nimport { PrivateRoute } from '../components/PrivateRoute';\nimport { AppInfoProvider } from '../features/AppInfo';\nimport { useAuth } from '../features/Auth';\nimport { useConfiguration } from '../features/Configuration';\nimport { useTracking } from '../features/Tracking';\nimport { useMenu } from '../hooks/useMenu';\nimport { useOnce } from '../hooks/useOnce';\nimport { useInformationQuery } from '../services/admin';\nimport { hashAdminUserEmail } from '../utils/users';\n\nconst strapiVersion = packageJSON.version;\n\nconst AdminLayout = () => {\n  const setGuidedTourVisibility = useGuidedTour(\n    'AdminLayout',\n    (state) => state.setGuidedTourVisibility\n  );\n  const { formatMessage } = useIntl();\n  const userInfo = useAuth('AuthenticatedApp', (state) => state.user);\n  const [userId, setUserId] = React.useState<string>();\n  const { showReleaseNotification } = useConfiguration('AuthenticatedApp');\n\n  const { data: appInfo, isLoading: isLoadingAppInfo } = useInformationQuery();\n\n  const [tagName, setTagName] = React.useState<string>(strapiVersion);\n\n  React.useEffect(() => {\n    if (showReleaseNotification) {\n      fetch('https://api.github.com/repos/strapi/strapi/releases/latest')\n        .then(async (res) => {\n          if (!res.ok) {\n            return;\n          }\n\n          const response = (await res.json()) as { tag_name: string | null | undefined };\n\n          if (!response.tag_name) {\n            throw new Error();\n          }\n\n          setTagName(response.tag_name);\n        })\n        .catch(() => {\n          /**\n           * silence is golden & we'll use the strapiVersion as a fallback\n           */\n        });\n    }\n  }, [showReleaseNotification]);\n\n  const userRoles = useAuth('AuthenticatedApp', (state) => state.user?.roles);\n\n  React.useEffect(() => {\n    if (userRoles) {\n      const isUserSuperAdmin = userRoles.find(({ code }) => code === 'strapi-super-admin');\n\n      if (isUserSuperAdmin && appInfo?.autoReload) {\n        setGuidedTourVisibility(true);\n      }\n    }\n  }, [userRoles, appInfo?.autoReload, setGuidedTourVisibility]);\n\n  React.useEffect(() => {\n    hashAdminUserEmail(userInfo).then((id) => {\n      if (id) {\n        setUserId(id);\n      }\n    });\n  }, [userInfo]);\n\n  const { trackUsage } = useTracking();\n\n  const {\n    isLoading: isLoadingMenu,\n    generalSectionLinks,\n    pluginsSectionLinks,\n  } = useMenu(checkLatestStrapiVersion(strapiVersion, tagName));\n  const { showTutorials } = useConfiguration('Admin');\n\n  /**\n   * Make sure the event is only send once after accessing the admin panel\n   * and not at runtime for example when regenerating the permissions with the ctb\n   * or with i18n\n   */\n  useOnce(() => {\n    trackUsage('didAccessAuthenticatedAdministration');\n  });\n\n  // We don't need to wait for the release query to be fetched before rendering the plugins\n  // however, we need the appInfos and the permissions\n  if (isLoadingMenu || isLoadingAppInfo) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <AppInfoProvider\n      {...appInfo}\n      userId={userId}\n      latestStrapiReleaseTag={tagName}\n      shouldUpdateStrapi={checkLatestStrapiVersion(strapiVersion, tagName)}\n    >\n      <NpsSurvey />\n      <PluginsInitializer>\n        <DndProvider backend={HTML5Backend}>\n          <Box background=\"neutral100\">\n            <SkipToContent>\n              {formatMessage({ id: 'skipToContent', defaultMessage: 'Skip to content' })}\n            </SkipToContent>\n            <Flex alignItems=\"flex-start\">\n              <LeftMenu\n                generalSectionLinks={generalSectionLinks}\n                pluginsSectionLinks={pluginsSectionLinks}\n              />\n              <Box flex={1}>\n                <Outlet />\n                <GuidedTourModal />\n                {showTutorials && <Onboarding />}\n              </Box>\n            </Flex>\n          </Box>\n        </DndProvider>\n      </PluginsInitializer>\n    </AppInfoProvider>\n  );\n};\n\nconst PrivateAdminLayout = () => {\n  return (\n    <PrivateRoute>\n      <AdminLayout />\n    </PrivateRoute>\n  );\n};\n\nconst checkLatestStrapiVersion = (\n  currentPackageVersion: string,\n  latestPublishedVersion: string = ''\n): boolean => {\n  if (!valid(currentPackageVersion) || !valid(latestPublishedVersion)) {\n    return false;\n  }\n\n  return lt(currentPackageVersion, latestPublishedVersion);\n};\n\nexport { AdminLayout, PrivateAdminLayout };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA6BA,IAAM,kBAAkB,MAAM;AAC5B,QAAM,aAAa,cAAc,mBAAmB,CAAC,UAAU,KAAK;AAE9D,QAAA;IACJ;IACA;IACA;IACA;IACA;IACA;EAAA,IACE;AAEE,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,WAAW,IAAI,YAAY;AAE/B,MAAA,CAAC,eAAe,CAAC,qBAAqB;AACjC,WAAA;EAAA;AAGH,QAAA,eAAW,WAAAA,SAAI,aAAa,WAAW;AACvC,QAAA,cAAc,OAAO,KAAK,eAAe;AAC/C,QAAM,CAAC,aAAa,QAAQ,IAAI,YAAY,MAAM,GAAG;AAI/C,QAAA,eAAe,YAAY,QAAQ,WAAW;AAC9C,QAAA,YAAY,OAAO,KAAK,gBAAgB,WAAW,CAAC,EAAE,QAAQ,QAAQ;AACtE,QAAA,kBAAkB,eAAe,YAAY,SAAS;AACtD,QAAA,eAAe,YAAY,OAAO,KAAK,gBAAgB,WAAW,CAAC,EAAE,SAAS;AAEpF,QAAM,iBAAiB,MAAM;AAC3B,iBAAa,aAAa,IAAI;AAE9B,QAAI,UAAU;AACZ,iBAAW,SAAS,aAAa;IAAA;AAGnC,mBAAe,IAAI;EAAA;AAGrB,QAAM,aAAa,MAAM;AACvB,eAAW,IAAI;AACf,mBAAe,IAAI;AACnB,eAAW,mBAAmB;EAAA;AAGhC,aACG,wBAAA,UAAA,EACC,cAAC,wBAAA,cAAA,EAAa,SAAS,gBAAgB,SAAS,GAAG,gBAAe,UAChE,cAAC,wBAAA,WAAA,EAAU,UAAU,gBACnB,cAAA;IAAC;IAAA;MACC,WAAU;MACV,YAAW;MACX,YAAW;MACX,OAAO;MACP,QAAO;MACP,WAAS;MACT,SAAS;MACT,KAAK;MACL,MAAK;MACL,cAAU;MACV,SAAS,CAAC,MAAM,EAAE,gBAAgB;MAElC,UAAA;YAAC,wBAAA,MAAA,EAAK,gBAAe,YACnB,cAAA;UAAC;UAAA;YACC,SAAS;YACT,aAAa;YACb,OAAO,cAAc;cACnB,IAAI;cACJ,gBAAgB;YAAA,CACjB;YAED,cAAA,wBAAC,eAAM,CAAA,CAAA;UAAA;QAAA,EAEX,CAAA;YACA;UAAC;UAAA;YACC,aAAa;YACb,cAAc;YACd,eAAe,CAAC,gBAAgB,CAAC,kBAAkB,IAAI;YAEvD,cAAA;cAAC;cAAA;gBACC,OAAO,YAAY,WAAW,WAAW,SAAS,QAAQ;gBAC1D,KAAK,YAAY,SAAS,WAAW,SAAS,MAAM;gBACpD,YAAY;gBACZ;gBACA;gBACA;gBAEC,UAAA,YAAY,aAAa,gBAAA,wBAAa,mBAAmB,EAAA,GAAG,SAAS,QAAS,CAAA;cAAA;YAAA;UACjF;QAAA;QAED,EAAE,CAAC,gBAAgB,CAAC,wBAClB,wBAAA,MAAA,EAAK,gBAAe,YACnB,cAAA,wBAAC,QAAO,EAAA,SAAQ,YAAW,SAAS,YACjC,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EAAA,CACH,EACF,CAAA;MAAA;IAAA;EAAA,EAEJ,CACF,EAAA,CACF,EACF,CAAA;AAEJ;AAEA,IAAM,eAAe,GAAsB,IAAI;;;;;gBAK/B,CAAC,EAAE,MAAM,MAAM,GAAG,MAAM,OAAO,UAAU,IAAI;;AAoB7D,IAAM,oBAAoB,CAAC;EACzB;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MAA8B;AACtB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,QAAM,mBAAmB,eAAe;AACxC,QAAM,iBAAiB,YAAY;AACnC,QAAM,mBAAmB,eAAe;AAExC,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAC,yBAAA,MAAA,EAAK,YAAW,WACf,UAAA;UAAA,wBAAC,MAAK,EAAA,aAAa,GAAG,gBAAe,UAAS,UAAU,QACrD,UAAoB,wBAAA,wBAAC,iBAAA,EAAgB,OAAO,OAAO,SAAS,WAAW,SAAA,CAAU,EACpF,CAAA;UAAA,wBACC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;IAAA,EACF,CAAA;QAAA,yBACC,MACC,EAAA,UAAA;UAAA,wBAAC,MAAK,EAAA,aAAa,GAAG,UAAU,QAC9B,cAAA;QAACC;QAAA;UACC,OAAO,iBAAiB,OAAO,UAAU,OAAO;UAChD,YAAY;UACZ,eAAe;UAEd,UAAe,eAAA;QAAA;MAAA,EAEpB,CAAA;MACC,aACC,wBAAC,YAAW,EAAA,SAAQ,SAAQ,YAAW,QAAO,WAAU,cAAa,KAAI,MAAK,IAAG,SAC9E,UAAA,cAAc,KAAK,EACtB,CAAA;IAAA,EAEJ,CAAA;QACA,yBAAC,MAAK,EAAA,YAAW,WACf,UAAA;UAAC,wBAAA,MAAA,EAAK,aAAa,GAAG,WAAU,UAAS,gBAAe,UAAS,UAAU,QACxE,UAAA,uBAEG,yBAAA,6BAAA,EAAA,UAAA;YAAC,wBAAA,iBAAA,EAAgB,OAAO,OAAO,QAAS,CAAA;QACvC,sBAAA,wBACEA,QAAO,EAAA,OAAO,OAAO,WAAW,YAAY,GAC1C,UAAA,mBAAmB,EACtB,CAAA;MAAA,EAAA,CAEJ,EAEJ,CAAA;UAAA,yBACC,KACE,EAAA,UAAA;QAAA;QACA,QACE,IAAI,aACH;UAAC;UAAA;YACC,KAAKC;YACL,aAAA,wBAAU,eAAW,CAAA,CAAA;YACrB,SAAS;YACT,IAAI,IAAI;YAEP,UAAA,cAAc,IAAI,KAAK;UAAA;QAAA,QAG1B,wBAAC,QAAO,EAAA,aAAU,wBAAA,eAAA,CAAA,CAAW,GAAI,SAAS,YACvC,UAAA,cAAc,IAAI,KAAK,EAC1B,CAAA;MAAA,EAEN,CAAA;IAAA,EACF,CAAA;IACC,kBAAkB,uBAChB,wBAAA,KAAA,EAAI,YAAY,GACf,cAAA,wBAAC,MAAK,EAAA,aAAa,GAAG,gBAAe,UAAS,OAAO,QACnD,cAAA,wBAAC,iBAAgB,EAAA,OAAO,OAAO,SAAS,WAAW,SAAU,CAAA,EAAA,CAC/D,EACF,CAAA;EAAA,EAEJ,CAAA;AAEJ;AASA,IAAM,oBAAoB,CAAC,EAAE,IAAI,eAAA,MAA6C;AACtE,QAAA,EAAE,cAAc,IAAI,QAAQ;AAGhC,aAAA,wBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GAAG,eAAe,GAClE,UAAA;IACC,EAAE,IAAI,eAAe;IACrB;MACE,mBAAmB;MACnB,GAAG;MACH,GAAG;MACH,OAAO;MACP,IAAI;MACJ,IAAI;IAAA;EACN,EAEJ,CAAA;AAEJ;AAEA,IAAM,oBAAoB,CAAC,iBACzB;EAAC;EAAA;IACC,KAAI;IACJ,WAAU;IACV,QAAO;IACP,KAAI;IACJ,MAAK;IAEJ;EAAA;AACH;AAGF,IAAM,OAAO,CAAC,iBAAA,wBACX,YAAW,EAAA,YAAW,YAAY,SAAS,CAAA;AAG9C,IAAM,YAAY,CAAC,iBAA8B,wBAAC,YAAA,EAAY,SAAS,CAAA;AAEvE,IAAM,QAAQ,CAAC,iBAAA,wBACZ,YAAW,EAAA,WAAU,cAAc,SAAS,CAAA;AAG/C,IAAM,OAAO,CAAC,iBACX,wBAAA,KAAA,EAAI,aAAa,GAChB,cAAA,wBAAC,MAAI,EAAA,SAAA,CAAS,EAChB,CAAA;AAGF,IAAM,WAAW,GAAO;;;aAGX,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;AAInD,IAAM,WAAW,CAAC,iBAA8B,wBAAC,UAAA,EAAU,SAAS,CAAA;ACpTpE,IAAM,iBAAiB,GAA6B,IAAI;4BAC5B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;AAGlE,IAAM,UAAU,CAAC,cACf;EAAC;EAAA;IACC,YAAW;IACX,KAAI;IACJ,YAAW;IACX,WAAU;IACV,QAAO;IACP,UAAS;IACT,KAAK;IACL,QAAQ;IACR,OAAO;IACN,GAAG;EAAA;AACN;ACbF,IAAM,mBAAmB,GAAsB,IAAI;;;qBAG9B,CAAC,EAAE,MAAM,MAAM,MAAM,YAAY;;;;;;AAO/C,IAAM,WAAW,MAAM;;AACtB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA;IACJ,OAAO,EAAE,KAAK;EAAA,IACZ,iBAAiB,UAAU;AAC/B,aACG,wBAAA,KAAA,EAAI,SAAS,GACZ,cAAC,yBAAA,kBAAA,EAAiB,WAAU,UAAS,gBAAe,UAAS,OAAM,UAAS,QAAO,UACjF,UAAA;QAAA;MAAC;MAAA;QACC,OAAK,UAAK,WAAL,mBAAa,QAAO,KAAK;QAC9B,KAAK,cAAc;UACjB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,OAAM;QACN,QAAO;MAAA;IAAA;QACT,yBACC,gBACC,EAAA,UAAA;UAAA,wBAAC,QAAA,EACE,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;UACA,wBAAC,QAAA,EACE,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;IAAA,EACF,CAAA;EAAA,EAAA,CACF,EACF,CAAA;AAEJ;ACnCA,IAAM,qBAAqB,GAAOC,OAAU;;;mBAGzB,CAAC,EAAE,MAAM,MAAM,MAAM,YAAY;gBACpC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,QAAQ;WACzC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;;;;;kBAQ/B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;;cAK1C,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;aAEvC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;;cAKrC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;aAGvC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;AAKnD,IAAM,WAAW,CAAC,EAAE,UAAU,GAAG,MAAA,MAAuB;AACtD,aAAQ,wBAAA,oBAAA,EAAoB,GAAG,OAAQ,SAAS,CAAA;AAClD;AAKA,IAAMC,eAAc,CAAC,EAAE,UAAU,OAAO,WAAW,QAAA,MAAoC;AAEnF,aAAA,wBAAC,aAAQ,EAAA,MAAM,UAAU,OAAc,eAAe,GACpD,cAAA,wBAAC,QAAM,EAAA,SAAA,CAAS,EAClB,CAAA;AAEJ;AAKA,IAAM,WAAW,CAAC,EAAE,OAAO,SAAA,MAA6D;AACtF,MAAI,CAAC,UAAU;AACN,WAAA;EAAA;AAEF,aAAA,wBAAC,gBAAe,EAAA,OAAe,SAAS,CAAA;AACjD;AAKA,IAAM,cAAc,GAAO,KAAK;;mBAEb,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,EAAE,CAAC;;;AAIlD,IAAM,YAAY,CAAC,EAAE,UAAU,OAAO,GAAG,MAAA,MAAmC;AAC1E,MAAI,CAAC,UAAU;AACN,WAAA;EAAA;AAGP,aAAA;IAAC;IAAA;MACC,UAAS;MACT,KAAI;MACJ,MAAK;MACL,cAAY;MACZ,QAAQ;MACP,GAAG;MAEH;IAAA;EAAA;AAGP;AAMA,IAAMF,WAAU;EACd,MAAM;EACN,SAASE;EACT,MAAM;EACN,OAAO;AACT;AC9FA,IAAM,cAAc,GAAO,KAAK,OAAO;;;;kBAIrB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;aAE3C,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;;;;;AAO3C,IAAM,cAAc,GAAO,KAAK,OAAO;UAC7B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;AAGxC,IAAM,WAAW,GAAO,KAAK,IAAI;;;;;WAKtB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;;;AAKlC,IAAM,UAAU,CAAC,EAAE,UAAU,UAAU,GAAG,MAAA,MAA0B;AACnE,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,WAAW,YAAY;AAC7B,QAAM,SAAS,QAAQ,UAAU,CAAC,UAAU,MAAM,MAAM;AACxD,QAAM,gBAAgB,MAAM;AAC1B,aAAS,KAAK;EAAA;AAEhB,QAAM,eAAe,MAAM;AAClB,WAAA;AACP,aAAS,aAAa;EAAA;AAItB,aAAA,wBAAC,MAAA,EAAK,gBAAe,UAAU,GAAG,OAChC,cAAA,yBAAC,KAAK,MAAL,EACC,UAAA;QAAA,yBAAC,aAAA,EAAY,SAAS,MAAM,WAAS,MAAC,gBAAe,UACnD,UAAA;UAAA,wBAAC,OAAO,MAAP,EAAY,SAAS,GAAG,UAAU,SAAU,CAAA;UAC5C,wBAAA,gBAAA,EAAe,KAAI,QAAQ,SAAS,CAAA;IAAA,EACvC,CAAA;QACC,yBAAA,aAAA,EAAY,kBAAiB,cAAa,QAAQ,GACjD,UAAA;UAAC,wBAAA,UAAA,EAAS,UAAU,eACjB,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;UAEC,yBAAA,UAAA,EAAS,UAAU,cAAc,OAAM,aACrC,UAAA;QAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB;YAAA,wBACA,cAAQ,CAAA,CAAA;MAAA,EACX,CAAA;IAAA,EACF,CAAA;EAAA,EAAA,CACF,EACF,CAAA;AAEJ;AC9DA,IAAM,YAAY,CAAC,UAAsB;AACvC,SAAO,MAAM,KAAK,CAAC,GAAG,MAAM;AAEpB,UAAA,YAAY,EAAE,YAAY;AAC1B,UAAA,YAAY,EAAE,YAAY;AAEhC,QAAI,YAAY,WAAW;AAClB,aAAA;IAAA,OACF;AACE,aAAA;IAAA;EACT,CACD;AACH;AAEA,IAAM,sBAAsB,GAAOF,SAAQ,KAAK;;aAEnC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,QAAQ;;;AAIjD,IAAM,mBAAmB,GAAOA,SAAQ,KAAK;;;AAI7C,IAAM,iBAAiB,GAA4B,IAAI;;;AAMvD,IAAM,WAAW,CAAC,EAAE,qBAAqB,oBAAA,MAAyC;AAChF,QAAM,OAAO,QAAQ,oBAAoB,CAAC,UAAU,MAAM,IAAI;AACxD,QAAA,EAAE,WAAW,IAAI,YAAY;AAC7B,QAAA,EAAE,SAAS,IAAI,YAAY;AAC3B,QAAA,kBAAkB,eAAe,IAAI;AAC3C,QAAM,EAAE,eAAe,OAAO,IAAI,QAAQ;AACpC,QAAA,YAAY,YAAY,QAAQ;IACpC,aAAa;EAAA,CACd;AAED,QAAM,WAAW,gBACd,MAAM,GAAG,EACT,IAAI,CAACG,UAASA,MAAK,UAAU,GAAG,CAAC,CAAC,EAClC,KAAK,EAAE,EACP,UAAU,GAAG,CAAC;AAEX,QAAA,oBAAoB,CAAC,gBAAwB;AACjD,eAAW,gBAAgB,EAAE,MAAM,UAAU,IAAI,YAAA,CAAa;EAAA;AAGhE,QAAM,gCAAgC,CAAC,GAAG,qBAAqB,GAAG,mBAAmB,EAAE;IACrF,CAAC,GAAG,MAAM,UAAU,QAAQ,cAAc,EAAE,SAAS,GAAG,cAAc,EAAE,SAAS,CAAC;EAAA;AAE9E,QAAA,YAAY,UAAU,6BAA6B;AAEzD,aAAA,yBACG,SACC,EAAA,UAAA;QAAA,wBAAC,UAAS,CAAA,CAAA;QAAA,wBAET,SAAQ,CAAA,CAAA;QAET,wBAAC,gBAAA,EAAe,KAAI,MAAK,KAAK,GAAG,WAAU,UAAS,MAAM,GAAG,YAAY,GAAG,eAAe,GACxF,UAAU,UAAA,SAAS,IAChB,UAAU,IAAI,CAAC,SAAS;AACtB,YAAM,WAAW,KAAK;AACtB,YAAM,oBAAmB,6BAAM,mBAAA,wBAC5B,eAAU,EAAA,MAAK,aAAA,CAAa,IAC3B;AAEE,YAAA,sBACJ,KAAK,sBAAsB,KAAK,qBAAqB,IACjD,KAAK,mBAAmB,SAAA,IACxB;AAEA,YAAA,aAAa,cAAc,KAAK,SAAS;AAE7C,iBAAA,wBAAC,MAAA,EAAK,KAAI,MACR,cAAA,wBAACH,SAAQ,SAAR,EAAgB,OAAO,YACtB,cAAA;QAACA,SAAQ;QAAR;UACC,IAAI,KAAK;UACT,SAAS,MAAM,kBAAkB,KAAK,EAAE;UACxC,cAAY;UAEZ,UAAA;gBAAA,wBAACA,SAAQ,MAAR,EAAa,OAAO,YACnB,cAAA,wBAAC,UAAS,EAAA,OAAM,MAAK,QAAO,MAAK,MAAK,aAAA,CAAa,EACrD,CAAA;YACC,uBACC;cAAC;cAAA;gBACC,OAAM;gBACN,WAAU;gBACV,aAAa;gBACb,cAAc;gBAEb,UAAA;cAAA;YAAA,IAED,0BACF;cAAC;cAAA;gBACC,OAAO;gBACP,iBAAgB;gBAChB,OAAM;gBACN,OAAM;gBAEL,UAAA;cAAA;YAAA,IAED;UAAA;QAAA;MAAA,EACN,CACF,EA9BkB,GAAA,KAAK,EA+BzB;IAAA,CAEH,IACD,KACN,CAAA;QACA,wBAAC,SAAQ,EAAA,UAAqB,UAAgB,gBAAA,CAAA;EAAA,EAChD,CAAA;AAEJ;ACrIA,IAAA,oBAAe;ACoBf,IAAM,aAAa,MAAM;AACvB,QAAM,CAAC,QAAQ,SAAS,IAAU,eAAS,KAAK;AAC1C,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,mBAAmB,WAAW,cAAc,CAAC,UAAU,MAAM,gBAAgB;AAEnF,QAAM,WAAW;IACf,GAAG;IACH;MACE,OAAO,EAAE,IAAI,iCAAiC,gBAAgB,WAAW;MACzE,MAAM;MACN,MAAM,mBACF,8BACA;IAAA;EACN;AAGI,QAAA,OAAO,SAAS,gBAAQ;AAE9B,aACG,wBAAA,QAAQ,MAAR,EAAa,cAAc,WAC1B,cAAC,yBAAA,KAAA,EAAI,UAAS,SAAQ,QAAQ,GAAG,OAAO,GACtC,UAAA;QAAC,wBAAA,QAAQ,SAAR,EACC,cAAA;MAAC;MAAA;QACC,cAAY;UACV,SACI;YACE,IAAI;YACJ,gBAAgB;UAAA,IAElB;YACE,IAAI;YACJ,gBAAgB;UAAA;QAClB;QAGN,cAAA,wBAAC,MAAK,EAAA,MAAK,iBAAiB,CAAA;MAAA;IAAA,EAEhC,CAAA;QACA,yBAAC,QAAQ,SAAR,EAAgB,OAAM,OAAM,MAAK,OAAM,YAAY,IAClD,UAAA;UAAA;QAAC;QAAA;UACC,gBAAe;UACf,eAAe;UACf,cAAc;UACd,aAAa;UACb,YAAY;UAEZ,UAAA;gBAAC,wBAAA,sBAAA,EAAqB,YAAW,QAC9B,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EACH,CAAA;gBACA;cAAC;cAAA;gBACC,KAAI;gBACJ,MAAM,WAAW;gBACjB,QAAO;gBACP,KAAI;gBACJ,SAAQ;gBACR,WAAU;gBAET,UAAA,cAAc,WAAW,KAAK;cAAA;YAAA;UACjC;QAAA;MAAA;UACF,wBACC,SAAQ,CAAA,CAAA;MACR,YAAY,IAAI,CAAC,EAAE,MAAM,UAAU,MAAA,GAAS,cAC3C;QAAC;QAAA;UACC,KAAI;UACJ;UACA,QAAO;UACP,KAAI;UAEJ,WAAS;UACT,YAAY;UACZ,eAAe;UACf,aAAa;UACb,cAAc;UAEd,UAAA;gBAAC,wBAAA,KAAA,EAAI,cAAc,GACjB,cAAC,wBAAAD,SAAA,EAAO,WAAU,cAAa,SAAQ,SACpC,UAAQ,QAAA,EAAA,CACX,EACF,CAAA;gBACA,yBAAC,KAAI,EAAA,UAAS,YACZ,UAAA;kBAAA,wBAAC,SAAQ,EAAA,KAAK,mBAAmB,KAAI,GAAG,CAAA;kBACxC;gBAAC;gBAAA;kBACC,UAAS;kBACT,KAAI;kBACJ,MAAK;kBACL,YAAW;kBACX,cAAa;kBACb,gBAAe;kBACf,OAAO;kBACP,QAAQ;kBAER,cAAA,wBAAC,eAAK,EAAA,MAAK,kBAAiB,OAAM,MAAK,QAAO,KAAK,CAAA;gBAAA;cAAA;YACrD,EACF,CAAA;gBAAA,yBACC,MAAK,EAAA,WAAU,UAAS,YAAW,SAAQ,aAAa,GACvD,UAAA;kBAAA,wBAAC,OAAM,EAAA,YAAW,QAAQ,UAAA,cAAc,KAAK,EAAE,CAAA;kBAC/C,wBAAC,gBAAA,EAAe,UAAC,IAAA,CAAA;kBAAA,wBAChB,YAAW,EAAA,WAAU,cAAa,SAAQ,MACxC,UACH,SAAA,CAAA;YAAA,EACF,CAAA;UAAA;QAAA;QAjCK;MAAA,CAmCR;UACD;QAAC;QAAA;UACC,WAAU;UACV,YAAW;UACX,KAAK;UACL,aAAa;UACb,YAAY;UACZ,eAAe;UAEd,UAAS,SAAA,IAAI,CAAC,EAAE,OAAO,MAAM,MAAMK,MAAAA,UAClC,yBAAC,MAAK,EAAA,KAAK,GACT,UAAA;gBAACA,wBAAAA,OAAA,EAAK,MAAK,aAAa,CAAA;gBACxB;cAAC;cAAA;gBACC,KAAI;gBACJ;gBACA,QAAO;gBACP,KAAI;gBACJ,SAAQ;gBACR,WAAU;gBAET,UAAA,cAAc,KAAK;cAAA;YAAA;UACtB,EAAA,GAXiB,IAYnB,CACD;QAAA;MAAA;IACH,EACF,CAAA;EAAA,EAAA,CACF,EACF,CAAA;AAEJ;AAGA,IAAM,eAAe,GAAO,MAAM;;aAErB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;;;;;;;;;;;;;AAe3C,IAAM,cAAc,GAAsB,IAAI;;;AAI9C,IAAML,UAAS,GAA4B,UAAU;AAErD,IAAM,QAAQ,GAA4B,UAAU;AAEpD,IAAM,mBAAmB,GAA2B,IAAI;;;;sBAIlC,CAAC,EAAE,MAAM,MAAM,IAAI,MAAM,OAAO,CAAC,CAAC,EAAE;;;;kBAIxC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;MAGlDA,OAAM;eACG,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;MAI/C,KAAK;eACI,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;AAKrD,IAAM,UAAU,GAAO;WACZ,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,EAAE,CAAC;YAC9B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;gBAE1B,CAAC,EAAE,MAAM,MAAM,GAAG,MAAM,OAAO,UAAU,IAAI;mBAC1C,CAAC,EAAE,MAAM,MAAM,MAAM,YAAY;;AAGpD,IAAM,uBAAuB,GAA4B,UAAU;;;;AAKnE,IAAM,WAAW,GAAiC,UAAU;;;;;;;;AAS5D,IAAM,cAAc;EAClB;IACE,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,MAAM;IACN,UAAU;EAAA;EAEZ;IACE,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,MAAM;IACN,UAAU;EAAA;EAEZ;IACE,OAAO,EAAE,IAAI,+CAA+C,gBAAgB,eAAe;IAC3F,MAAM;IACN,UAAU;EAAA;AAEd;AAEA,IAAM,aAAa;EACjB,MAAM;EACN,OAAO;IACL,IAAI;IACJ,gBAAgB;EAAA;AAEpB;AAEA,IAAM,sBAAsB;EAC1B;IACE,OAAO,EAAE,IAAI,wBAAwB,gBAAgB,gBAAgB;IACrE,MAAM;IACN,MAAM;EAAA;EAER;IACE,OAAO,EAAE,IAAI,+BAA+B,gBAAgB,aAAa;IACzE,MAAM;IACN,MAAM;EAAA;AAEV;AClQA,IAAM,qBAAqB,CAAC,EAAE,SAAA,MAA8C;AAC1E,QAAM,aAAa,aAAa,sBAAsB,CAAC,UAAU,MAAM,OAAO;AAC9E,QAAM,CAAC,EAAE,QAAA,GAAW,QAAQ,IAAU;IACpC;IACA;IACA,MAAM,KAAK,UAAU;EAAA;AAEvB,QAAM,YAAkB,aAAO,CAAC,aAAqB;AACnD,aAAS,EAAE,MAAM,oBAAoB,SAAA,CAAU;EAAA,CAChD;AAED,QAAM,qBAAqB,OAAO,KAAK,OAAO,EAAE;IAC9C,CAAC,WAAW,QAAQ,MAAM,EAAE,YAAY;EAAA;AA0B1C,MAAI,oBAAoB;AAChB,UAAA,eAAe,OAAO,KAAK,OAAO,EAAE,OAAO,CAAC,KAAK,YAAY;AAC3D,YAAA,uBAAuB,QAAQ,OAAO,EAAE;AAE9C,UAAI,sBAAsB;AAClB,cAAA,MAAM,QAAQ,OAAO,EAAE;AAE7B,YAAI,SAAM,wBAAA,sBAAA,EAA+B,WAAW,UAAU,QAAA,GAA1B,GAAmC,CAAE;MAAA;AAGpE,aAAA;IAAA,GACN,CAAA,CAAuB;AAE1B,eAEK,yBAAA,6BAAA,EAAA,UAAA;MAAA;UACD,wBAAC,KAAK,SAAL,CAAA,CAAa;IAAA,EAChB,CAAA;EAAA;AAIG,SAAA;AACT;AAUA,IAAM,eAAsB;EAC1B,SAAS,CAAA;AACX;AASA,IAAM,UAAwC,CAAC,QAAQ,cAAc,WACnE,GAAQ,OAAO,CAAC,eAAe;AAC7B,UAAQ,OAAO,MAAM;IACnB,KAAK,oBAAoB;AACvB,qBAAAM,SAAI,YAAY,CAAC,WAAW,OAAO,UAAU,SAAS,GAAG,IAAI;AAC7D;IAAA;IAEF;AACS,aAAA;EAAA;AAEb,CAAC;AAMH,IAAM,OAAO,CAAC,YAAqC;AAC1C,SAAA;IACL;EAAA;AAEJ;AC3FA,IAAM,UAAU,CAAC,uBAAgC;;AAC/C,QAAM,0BAA0B,QAAQ,WAAW,CAAC,UAAU,MAAM,uBAAuB;AAC3F,QAAM,OAAO,aAAa,WAAW,CAAC,UAAU,MAAM,IAAI;AAC1D,QAAM,cAAc,iBAAiB,CAAC,UAAU,MAAM,UAAU,WAAW;AAC3E,QAAM,CAAC,yBAAyB,0BAA0B,IAAU,eAAe;IACjF,qBAAqB;MACnB;QACE,MAAM;QACN,WAAW;UACT,IAAI;UACJ,gBAAgB;QAAA;QAElB,IAAI;QACJ,aAAa,CAAA;QACb,UAAU;MAAA;MAEZ;QACE,MAAM;QACN,WAAW;UACT,IAAI;UACJ,gBAAgB;QAAA;QAElB,IAAI;QACJ,eAAa,iBAAY,gBAAZ,mBAAyB,SAAQ,CAAA;QAC9C,UAAU;MAAA;MAEZ;QACE,MAAM;QACN,WAAW;UACT,IAAI;UACJ,gBAAgB;QAAA;QAElB,IAAI;;;QAGJ,aAAa,CAAA;QACb,oBAAoB;QACpB,UAAU;MAAA;IACZ;IAEF,qBAAqB,CAAA;IACrB,WAAW;EAAA,CACZ;AACD,QAAM,yBAA+B,aAAO,wBAAwB,mBAAmB;AAEvF,EAAM,gBAAU,MAAM;AACpB,mBAAe,uBAAuB;AACpC,YAAM,+BAA+B,MAAM;QACzC;QACA;MAAA;AAGF,YAAM,gCAAgC,MAAM;QAC1C,uBAAuB;QACvB;QACA;MAAA;AAGF,iCAA2B,CAAC,WAAW;QACrC,GAAG;QACH,qBAAqB;QACrB,qBAAqB;QACrB,WAAW;MAAA,EACX;IAAA;AAGiB,yBAAA;EAAA,GACpB;IACD;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;AAEM,SAAA;AACT;AAMA,IAAM,kBAAkB,OACtB,wBACA,qBAA8B,OAC9B,4BACG;AACG,QAAA,iCAAiC,MAAM,QAAQ;IACnD,uBAAuB,IAAI,CAAC,EAAE,YAAA,MAAkB,wBAAwB,WAAW,CAAC;EAAA;AAGtF,QAAM,gCAAgC,uBAAuB;IAC3D,CAAC,GAAG,UAAU,+BAA+B,KAAK,EAAE,SAAS;EAAA;AAG/D,QAAM,oBAAoB,8BAA8B;IACtD,CAAC,QAAQ,IAAI,OAAO;EAAA;AAGtB,MAAI,sBAAsB,IAAI;AAC5B,WAAO,CAAA;EAAC;AAGJ,QAAA,kCAA8B,iBAAAC,SAAU,6BAA6B;AAE3E,8BAA4B,iBAAiB,EAAE,qBAAqB,qBAAqB,IAAI;AAEtF,SAAA;AACT;AAEA,IAAM,wBAAwB,OAC5B,wBACA,4BACG;AACG,QAAA,gCAAgC,MAAM,QAAQ;IAClD,uBAAuB,IAAI,CAAC,EAAE,YAAA,MAAkB,wBAAwB,WAAW,CAAC;EAAA;AAGtF,QAAM,+BAA+B,uBAAuB;IAC1D,CAAC,GAAG,UAAU,8BAA8B,KAAK,EAAE,SAAS;EAAA;AAGvD,SAAA;AACT;ACrHA,IAAM,gBAAgB,YAAY;AAElC,IAAM,cAAc,MAAM;AACxB,QAAM,0BAA0B;IAC9B;IACA,CAAC,UAAU,MAAM;EAAA;AAEb,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,WAAW,QAAQ,oBAAoB,CAAC,UAAU,MAAM,IAAI;AAClE,QAAM,CAAC,QAAQ,SAAS,IAAU,eAAiB;AACnD,QAAM,EAAE,wBAAA,IAA4B,iBAAiB,kBAAkB;AAEvE,QAAM,EAAE,MAAM,SAAS,WAAW,iBAAA,IAAqB,oBAAoB;AAE3E,QAAM,CAAC,SAAS,UAAU,IAAU,eAAiB,aAAa;AAElE,EAAM,gBAAU,MAAM;AACpB,QAAI,yBAAyB;AAC3B,YAAM,4DAA4D,EAC/D,KAAK,OAAO,QAAQ;AACf,YAAA,CAAC,IAAI,IAAI;AACX;QAAA;AAGI,cAAA,WAAY,MAAM,IAAI,KAAK;AAE7B,YAAA,CAAC,SAAS,UAAU;AACtB,gBAAM,IAAI,MAAM;QAAA;AAGlB,mBAAW,SAAS,QAAQ;MAAA,CAC7B,EACA,MAAM,MAAM;MAAA,CAIZ;IAAA;EACL,GACC,CAAC,uBAAuB,CAAC;AAE5B,QAAM,YAAY,QAAQ,oBAAoB,CAAC,UAAU;;AAAA,uBAAM,SAAN,mBAAY;GAAK;AAE1E,EAAM,gBAAU,MAAM;AACpB,QAAI,WAAW;AACP,YAAA,mBAAmB,UAAU,KAAK,CAAC,EAAE,KAAK,MAAM,SAAS,oBAAoB;AAE/E,UAAA,qBAAoB,mCAAS,aAAY;AAC3C,gCAAwB,IAAI;MAAA;IAC9B;EACF,GACC,CAAC,WAAW,mCAAS,YAAY,uBAAuB,CAAC;AAE5D,EAAM,gBAAU,MAAM;AACpB,uBAAmB,QAAQ,EAAE,KAAK,CAAC,OAAO;AACxC,UAAI,IAAI;AACN,kBAAU,EAAE;MAAA;IACd,CACD;EAAA,GACA,CAAC,QAAQ,CAAC;AAEP,QAAA,EAAE,WAAW,IAAI,YAAY;AAE7B,QAAA;IACJ,WAAW;IACX;IACA;EAAA,IACE,QAAQ,yBAAyB,eAAe,OAAO,CAAC;AAC5D,QAAM,EAAE,cAAA,IAAkB,iBAAiB,OAAO;AAOlD,UAAQ,MAAM;AACZ,eAAW,sCAAsC;EAAA,CAClD;AAID,MAAI,iBAAiB,kBAAkB;AAC9B,eAAA,wBAAC,KAAK,SAAL,CAAA,CAAa;EAAA;AAIrB,aAAA;IAAC;IAAA;MACE,GAAG;MACJ;MACA,wBAAwB;MACxB,oBAAoB,yBAAyB,eAAe,OAAO;MAEnE,UAAA;YAAA,wBAAC,WAAU,CAAA,CAAA;YACX,wBAAC,oBAAA,EACC,cAAC,wBAAA,aAAA,EAAY,SAAS,cACpB,cAAA,yBAAC,KAAI,EAAA,YAAW,cACd,UAAA;cAAC,wBAAA,eAAA,EACE,UAAA,cAAc,EAAE,IAAI,iBAAiB,gBAAgB,kBAAA,CAAmB,EAC3E,CAAA;cACA,yBAAC,MAAK,EAAA,YAAW,cACf,UAAA;gBAAA;cAAC;cAAA;gBACC;gBACA;cAAA;YAAA;gBAEF,yBAAC,KAAI,EAAA,MAAM,GACT,UAAA;kBAAA,wBAAC,QAAO,CAAA,CAAA;kBAAA,wBACP,iBAAgB,CAAA,CAAA;cAChB,qBAAA,wBAAkB,YAAW,CAAA,CAAA;YAAA,EAChC,CAAA;UAAA,EACF,CAAA;QAAA,EAAA,CACF,EAAA,CACF,EACF,CAAA;MAAA;IAAA;EAAA;AAGN;AAEA,IAAM,qBAAqB,MAAM;AAC/B,aACG,wBAAA,cAAA,EACC,cAAC,wBAAA,aAAA,CAAA,CAAY,EACf,CAAA;AAEJ;AAEA,IAAM,2BAA2B,CAC/B,uBACA,yBAAiC,OACrB;AACZ,MAAI,KAAC,aAAAC,SAAM,qBAAqB,KAAK,KAAC,aAAAA,SAAM,sBAAsB,GAAG;AAC5D,WAAA;EAAA;AAGF,aAAA,UAAAC,SAAG,uBAAuB,sBAAsB;AACzD;", "names": ["get", "Number", "NavLink", "RouterLink", "TooltipImpl", "name", "Icon", "set", "cloneDeep", "valid", "lt"]}