import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/ar-7vbwrPth.mjs
var Email = "البريد الإلكتروني";
var Password = "كلمة السر";
var Provider = "مزود";
var ResetPasswordToken = "إعادة تعيين كلمة المرور";
var Role = "قاعدة";
var Username = "اسم المستخدم";
var Users = "المستخدمين";
var Analytics = "تحليلات";
var anErrorOccurred = "عذرًا! هناك خطأ ما. حاول مرة اخرى.";
var clearLabel = "حذف";
var dark = "داكن";
var Documentation = "توثيق";
var light = "فاتح";
var or = "أو";
var selectButtonTitle = "يختار";
var skipToContent = "تخطى الى المحتوى";
var submit = "يُقدِّم";
var ar = {
  "Auth.form.button.forgot-password": "إرسال للبريد",
  "Auth.form.button.login": "تسجيل دخول",
  "Auth.form.button.register": "مستعد للبدء",
  "Auth.form.email.label": "البريد الإكتروني",
  "Auth.form.email.placeholder": "<EMAIL>",
  "Auth.form.error.code.provide": "الرمز المقدم غير صحيح.",
  "Auth.form.error.email.invalid": "هذا البريد الاكتروني غير صالح.",
  "Auth.form.error.email.provide": "يرجى تقديم اسم المستخدم الخاص بك أو البريد الإلكتروني الخاص بك.",
  "Auth.form.error.email.taken": "البريد الإلكتروني مسجل بالفعل",
  "Auth.form.error.invalid": "المعرّف أو كلمة المرور غير صالحين.",
  "Auth.form.error.params.provide": "المعلومات المقدمة غير صحيحة.",
  "Auth.form.error.password.format": "لا يمكن أن تحتوي كلمة مرورك على الرمز `$` أكثر من ثلاث مرات.",
  "Auth.form.error.password.local": "لم يقم هذا المستخدم بتعيين كلمة مرور محلية مطلقًا ، الرجاء تسجيل الدخول عبر الموفر المستخدم أثناء إنشاء الحساب.",
  "Auth.form.error.password.matching": "كلمة المرور غير مطابقة.",
  "Auth.form.error.password.provide": "يرجى تقديم كلمة المرور الخاصة بك.",
  "Auth.form.error.user.not-exist": "هذا الإميل غير موجود.",
  "Auth.form.error.username.taken": "اسم المستخدم مسجل بالفعل",
  "Auth.form.forgot-password.email.label": "ادخل ايميلك",
  "Auth.form.forgot-password.email.label.success": "تم إرسال الرسالة بنجاح الى",
  "Auth.form.rememberMe.label": "تذكرني",
  "Auth.form.username.label": "اسم المستخدم",
  "Auth.form.username.placeholder": "اكتب اسمك هنا (مثل: خالد سالم)",
  "Auth.link.forgot-password": "هل نسيت كلمة السر الخاصة بك؟",
  "Auth.link.ready": "مستعد لتسجيل الدخول؟",
  "Content Manager": "مدير محتوى",
  Email,
  "Files Upload": "رفع الملفات",
  "New entry": "إدخال جديد",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  "Users & Permissions": "المستخدمين والصلاحيات",
  "app.components.BlockLink.code": "امثلة للشفرة",
  "app.components.Button.cancel": "الغاء",
  "app.components.ComingSoonPage.comingSoon": "قادم قريبًا",
  "app.components.DownloadInfo.download": "التنزيل قيد التقدم...",
  "app.components.DownloadInfo.text": "قد يستغرق هذا دقيقة. شكرا لصبرك.",
  "app.components.EmptyAttributes.title": "لا يوجد اي حقول بعد",
  "app.components.HomePage.button.blog": "اظهار المزيد على المدونة",
  "app.components.HomePage.community": "البحث عن المجتمع في الويب",
  "app.components.HomePage.community.content": "ناقش مع أعضاء الفريق والمساهمين والمطورين على قنوات مختلفة.",
  "app.components.HomePage.welcome": "مرحبًا في لوحتك!",
  "app.components.HomePage.welcome.again": "مرحبًا ",
  "app.components.HomePage.welcomeBlock.content": "نحن سعداء بوجودك كأحد أفراد المجتمع. نحن نبحث باستمرار عن ردود الفعل لا تتردد في مراسلتنا على الخاص ",
  "app.components.HomePage.welcomeBlock.content.again": "نأمل أن تحقق تقدمًا في مشروعك ... لا تتردد في قراءة عن اخر إصدار جديد من Strapi. نحن نبذل قصارى جهدنا لتحسين المنتج بناء على ملاحظاتك.",
  "app.components.HomePage.welcomeBlock.content.issues": "issues.",
  "app.components.HomePage.welcomeBlock.content.raise": " أو رفع ",
  "app.components.ImgPreview.hint": "اسحب الملف واسقطة في هذه المساحة او في {browse} لرفعة",
  "app.components.ImgPreview.hint.browse": "المتصفح",
  "app.components.InputFile.newFile": "إضافة ملف جديد",
  "app.components.InputFileDetails.open": "فتح في نافذة جديدة",
  "app.components.InputFileDetails.originalName": "الاسم الاصلي:",
  "app.components.InputFileDetails.remove": "حذف هذا الملف",
  "app.components.InputFileDetails.size": "الحجم:",
  "app.components.InstallPluginPage.description": "قم بتوسيع التطبيق الخاص بك دون عناء.",
  "app.components.LeftMenuFooter.poweredBy": "مندعوم من ",
  "app.components.LeftMenuLinkContainer.configuration": "التهيئة",
  "app.components.LeftMenuLinkContainer.general": "عام",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "لا توجد إضافات مثبته بعد",
  "app.components.LeftMenuLinkContainer.plugins": "إضافات",
  "app.components.ListPluginsPage.description": "قائمة الإضافيات المثبتة في المشروع.",
  "app.components.ListPluginsPage.head.title": "قائمة الإضافات",
  "app.components.Logout.logout": "الخروج",
  "app.components.Logout.profile": "الملف الشخصي",
  "app.components.NotFoundPage.back": "العودة للرئيسية",
  "app.components.NotFoundPage.description": "لا يوجد",
  "app.components.Official": "الرسمية",
  "app.components.PluginCard.Button.label.download": "تنزيل",
  "app.components.PluginCard.Button.label.install": "مثبت",
  "app.components.PluginCard.compatible": "متوافق مع تطبيقك",
  "app.components.PluginCard.compatibleCommunity": "متوافق مع المجتمع",
  "app.components.PluginCard.more-details": "المزيد من التفاصيل",
  "app.components.listPlugins.button": "إضافة إضافة جديدة",
  "app.components.listPlugins.title.none": "لا يوجد اي إضافات مثبته",
  "app.components.listPluginsPage.deletePlugin.error": "حدث خطأ أثناء إلغاء تثبيت الإضافة",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.defaultMessage": " ",
  "app.utils.placeholder.defaultMessage": " ",
  "components.AutoReloadBlocker.header": "مطلوب ميزة إعادة التحميل لهذه الإضافة.",
  "components.ErrorBoundary.title": "هناك خطأ ما...",
  "components.Input.error.attribute.key.taken": "هذه القيمة موجودة مسبقًا",
  "components.Input.error.attribute.sameKeyAndName": "لا تتطابق",
  "components.Input.error.attribute.taken": "اسم الحقل هذا مستخدم مسبقًا",
  "components.Input.error.contentTypeName.taken": "هذه الاسم مستخدم مسبقًا",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "كلمات السر لا تتطابق",
  "components.Input.error.validation.email": "هذا ليس بريد الإكتروني",
  "components.Input.error.validation.json": "لا يتطابق مع صيغة JSON",
  "components.Input.error.validation.max": "هذه القيمة عالية جدًا {max}.",
  "components.Input.error.validation.maxLength": "هذه القيمة طويلة جدًا {max}.",
  "components.Input.error.validation.min": "هذه القيمة قليلة جدًا {min}.",
  "components.Input.error.validation.minLength": "هذه القيمة قصيرة جدًا {min}.",
  "components.Input.error.validation.minSupMax": "لا يمكن أن تكون متفوقة",
  "components.Input.error.validation.regex": "هذه القمية لا تطابق regex.",
  "components.Input.error.validation.required": "هذه القيمة مطلوبة.",
  "components.ListRow.empty": "لا توجد بيانات ليتم عرضها.",
  "components.OverlayBlocker.description": "أنت تستخدم ميزة تحتاج إلى إعادة تشغيل الخادم. يرجى الانتظار حتى يعود الخادم.",
  "components.OverlayBlocker.title": "في انتظار إعادة التشغيل...",
  "components.PageFooter.select": "إدخالات لكل صفحة",
  "components.ProductionBlocker.description": "لأغراض السلامة ، يتعين علينا تعطيل هذه الإضافة في بيئات أخرى.",
  "components.ProductionBlocker.header": "هذه الإضافة متاحة فقط في التطوير.",
  "components.Wysiwyg.collapse": "تقليص",
  "components.Wysiwyg.selectOptions.H1": "العنوان H1",
  "components.Wysiwyg.selectOptions.H2": "العنوان H2",
  "components.Wysiwyg.selectOptions.H3": "العنوان H3",
  "components.Wysiwyg.selectOptions.H4": "العنوان H4",
  "components.Wysiwyg.selectOptions.H5": "العنوان H5",
  "components.Wysiwyg.selectOptions.H6": "العنوان H6",
  "components.Wysiwyg.selectOptions.title": "إضافة عنوان",
  "components.WysiwygBottomControls.charactersIndicators": "الأحرف",
  "components.WysiwygBottomControls.fullscreen": "توسيع",
  "components.WysiwygBottomControls.uploadFiles": "اسحب الملفات وأفلتها ، والصقها من الحافظة أو {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "حددهم",
  "components.popUpWarning.message": "هل انت متاكد من حذف هذا؟",
  "components.popUpWarning.title": "ارجو التأكيد",
  "notification.error": "حدث خطأ",
  "notification.error.layout": "تعذّر استرداد التنسيق",
  "request.error.model.unknown": "هذا النموذج غير موجود",
  "admin.pages.MarketPlacePage.filters.categories": "فئات",
  "admin.pages.MarketPlacePage.filters.collections": "المجموعات",
  "admin.pages.MarketPlacePage.head": "السوق - الإضافات",
  "admin.pages.MarketPlacePage.missingPlugin.description": "أخبرنا ما هو المكون الإضافي الذي تبحث عنه وسنعلم مطوري المكونات الإضافية في مجتمعنا في حال كانوا يبحثون عن الإلهام!",
  "admin.pages.MarketPlacePage.missingPlugin.title": "هل فقدت مكونًا إضافيًا؟",
  "admin.pages.MarketPlacePage.offline.subtitle": "يجب أن تكون متصلاً بالإنترنت للوصول إلى سوق سترابي",
  "admin.pages.MarketPlacePage.offline.title": "انت غير متصل",
  "admin.pages.MarketPlacePage.plugin.copy": "أمر نسخ التثبيت",
  "admin.pages.MarketPlacePage.plugin.copy.success": "قم بتثبيت الأمر جاهزًا ليتم لصقه في الجهاز الطرفي",
  "admin.pages.MarketPlacePage.plugin.downloads": "يتم تنزيل هذا المكون الإضافي {downloadsCount} أسبوعيًا",
  "admin.pages.MarketPlacePage.plugin.githubStars": "تم تمييز هذا المكون الإضافي بنجمة على GitHub",
  "admin.pages.MarketPlacePage.plugin.info": "يتعلم أكثر",
  "admin.pages.MarketPlacePage.plugin.info.label": "تعرف على المزيد حول {pluginName}",
  "admin.pages.MarketPlacePage.plugin.info.text": "أكثر",
  "admin.pages.MarketPlacePage.plugin.installed": "المثبتة",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "صنع بواسطة ستربي",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "Strapi تم التحقق من البرنامج المساعد من قبل ",
  "admin.pages.MarketPlacePage.plugins": "الإضافات",
  "admin.pages.MarketPlacePage.provider.downloads": "هذا الموفر لديه {downloadsCount} من التنزيلات الأسبوعية",
  "admin.pages.MarketPlacePage.provider.githubStars": "{GitHub} على {starsCount} تم تميز هذا المزود ",
  "admin.pages.MarketPlacePage.providers": "الموفرون",
  "admin.pages.MarketPlacePage.search.clear": "مسح البحث",
  "admin.pages.MarketPlacePage.search.empty": ' "{target}" لا توجد نتيجة ل',
  "admin.pages.MarketPlacePage.search.placeholder": "يبحث",
  "admin.pages.MarketPlacePage.sort.alphabetical": "ترتيب ابجدي",
  "admin.pages.MarketPlacePage.sort.alphabetical.selected": "فرز حسب الترتيب الأبجدي",
  "admin.pages.MarketPlacePage.sort.githubStars": "GitHub عدد نجوم",
  "admin.pages.MarketPlacePage.sort.githubStars.selected": "GitHub الترتيب حسب نجوم",
  "admin.pages.MarketPlacePage.sort.newest": "الأحدث",
  "admin.pages.MarketPlacePage.sort.newest.selected": "ترتيب حسب الأحدث",
  "admin.pages.MarketPlacePage.sort.npmDownloads": "عدد التنزيلات",
  "admin.pages.MarketPlacePage.sort.npmDownloads.selected": "npm فرز حسب التنزيلات ",
  "admin.pages.MarketPlacePage.submit.plugin.link": "إرسال البرنامج المساعد",
  "admin.pages.MarketPlacePage.submit.provider.link": "إرسال مزود",
  "admin.pages.MarketPlacePage.subtitle": " Strapi احصل على المزيد من",
  "admin.pages.MarketPlacePage.tab-group.label": "Strapi الإضافات ومقدمي ",
  Analytics,
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "نسخ إلى الحافظة",
  "app.component.search.label": "{target} بحث عن",
  "app.component.table.duplicate": "{target} ينسخ",
  "app.component.table.edit": "{target} يحرر",
  "app.component.table.read": "{target}يقرأ",
  "app.component.table.select.one-entry": "{target}يختار",
  "app.component.table.view": "{target} تفاصيل",
  "app.components.BlockLink.blog": "مدونة",
  "admin.pages.MarketPlacePage.filters.categoriesSelected": "{count, plural, =0 {No categories} واحد {# category} آخر{# categories}} المحدد",
  "admin.pages.MarketPlacePage.plugin.version": ' قم بتحديث إصدار الخاص بك  : "{strapiAppVersion}" ل: "{versionRange}"',
  "admin.pages.MarketPlacePage.plugin.version.null": 'تعذر التحقق من التوافق مع إصدار Strapi الخاص بك: "{strapiAppVersion}"',
  "app.components.BlockLink.blog.content": "اقرأ آخر الأخبار حول Strapi والنظام البيئي.",
  "app.components.BlockLink.cloud": "Strapi سحاب",
  "app.components.BlockLink.cloud.content": "نظام أساسي قابل للإنشاء والتعاون بشكل كامل لزيادة سرعة فريقك.",
  "app.components.BlockLink.code.content": "تعلم من خلال اختبار المشاريع الحقيقية التي طورها المجتمع.",
  "app.components.BlockLink.documentation.content": "اكتشف المفاهيم الأساسية والأدلة والتعليمات.",
  "app.components.BlockLink.tutorial": "دروس",
  "app.components.BlockLink.tutorial.content": "Strapi اتبع التعليمات خطوة بخطوة للاستخدام والتخصيص.",
  "app.components.Button.confirm": "يتأكد",
  "app.components.Button.reset": "إعادة ضبط",
  "app.components.ConfirmDialog.title": "تأكيد",
  "app.components.EmptyStateLayout.content-document": "لم يتم العثور على محتوى",
  "app.components.EmptyStateLayout.content-permissions": "ليس لديك أذونات للوصول إلى هذا المحتوى",
  "app.components.GuidedTour.apiTokens.create.content": "<p>قم بإنشاء رمز المصادقة هنا واسترجع المحتوى الذي أنشأته للتو.</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "API إنشاء رمز",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 مشاهدة المحتوى في العمل",
  "app.components.GuidedTour.apiTokens.success.cta.title": "العودة الى الصفحة الرئيسية",
  "app.components.GuidedTour.apiTokens.success.title": "✅ الخطوة 3: اكتمل",
  "app.components.GuidedTour.CM.create.content": "<p>قم بإنشاء وإدارة كل المحتوى هنا في إدارة المحتوى.</p><p>مثال: أخذ مثال موقع المدونة إلى أبعد من ذلك ، يمكن للمرء كتابة مقال وحفظه ونشره كما يحلو له.</p><p>💡 نصيحة سريعة - لا تنس النقر على ’نشر’ على المحتوى الذي تنشئه.</p>",
  "app.components.GuidedTour.CM.create.title": "⚡️ أنشئ محتوى",
  "app.components.GuidedTour.CM.success.content": "<p>رائع ، خطوة أخيرة يجب أن تبدأ بها!</p><b>🚀 مشاهدة المحتوى في العمل</b>",
  "app.components.GuidedTour.CM.success.cta.title": "API اختبر ملف",
  "app.components.GuidedTour.CM.success.title": "✅ الخطوة 2: اكتمل",
  "app.components.GuidedTour.create-content": "أنشئ محتوى",
  "app.components.GuidedTour.CTB.create.content": "<p>تساعدك أنواع المجموعات على إدارة عدة إدخالات ، والأنواع الفردية مناسبة لإدارة إدخال واحد فقط. </ p> <p> على سبيل المثال: بالنسبة إلى موقع مدونة ، ستكون المقالات من نوع المجموعة بينما تكون الصفحة الرئيسية من النوع الفردي.</p>",
  "app.components.GuidedTour.CTB.create.cta.title": "بناء نوع المجموعة",
  "app.components.GuidedTour.CTB.create.title": "🧠 قم بإنشاء أول نوع مجموعة",
  "app.components.GuidedTour.CTB.success.content": "<p>جيد!</p><b>⚡️ ما الذي تود مشاركته مع العالم؟</b>",
  "app.components.GuidedTour.CTB.success.title": "الخطوة 1:  ✅ مكتمل",
  "app.components.GuidedTour.home.apiTokens.cta.title": "API اختبار ",
  "app.components.GuidedTour.home.CM.title": "⚡️ ما الذي تود مشاركته مع العالم؟",
  "app.components.GuidedTour.home.CTB.cta.title": "Content type Builder انتقل إلى",
  "app.components.GuidedTour.home.CTB.title": "🧠 بناء هيكل المحتوى",
  "app.components.GuidedTour.skip": "تخطي الجولة",
  "app.components.GuidedTour.title": "خطوات للبدء ٣",
  "app.components.HomePage.create": "قم بإنشاء نوع المحتوى الأول الخاص بك",
  "app.components.HomePage.roadmap": "انظر خارطة الطريق لدينا",
  "app.components.InstallPluginPage.Download.description": "قد يستغرق تنزيل المكون الإضافي وتثبيته بضع ثوانٍ",
  "app.components.InstallPluginPage.Download.title": "جارى التحميل...",
  "app.components.LeftMenu.collapse": "تصغير شريط التنقل",
  "app.components.LeftMenu.expand": "قم بتوسيع شريط التنقل",
  "app.components.LeftMenu.general": "عام",
  "app.components.LeftMenu.logo.alt": "شعار التطبيق",
  "app.components.LeftMenu.logout": "تسجيل خروج",
  "app.components.LeftMenu.navbrand.title": "Strapi لوحة القيادة",
  "app.components.LeftMenu.navbrand.workplace": "مكان العمل",
  "app.components.LeftMenu.plugins": "الإضافات",
  "app.components.LeftMenuFooter.help": "يساعد",
  "app.components.LeftMenuLinkContainer.collectionTypes": "أنواع المجموعات",
  "app.components.LeftMenuLinkContainer.singleTypes": "أنواع مفردة",
  "app.components.ListPluginsPage.deletePlugin.description": "قد يستغرق الأمر بضع ثوان لإلغاء تثبيت المكون الإضافي",
  "app.components.ListPluginsPage.deletePlugin.title": "إلغاء التثبيت",
  "app.components.MarketplaceBanner": "اكتشف المكونات الإضافية التي أنشأها المجتمع ، والعديد من الأشياء الرائعة لبدء مشروعك ، في سوق Strapi.",
  "app.components.MarketplaceBanner.image.alt": "شعار صاروخ Strapi",
  "app.components.MarketplaceBanner.link": "افحصه الآن",
  "app.components.Onboarding.help.button": "زر المساعدة",
  "app.components.Onboarding.label.completed": "% مكتمل",
  "app.components.Onboarding.link.build-content": "بناء بنية المحتوى",
  "app.components.Onboarding.link.manage-content": "إضافة وإدارة المحتوى",
  "app.components.Onboarding.link.manage-media": "إدارة الوسائط",
  "app.components.Onboarding.link.more-videos": "شاهد المزيد من مقاطع الفيديو",
  "app.components.Onboarding.title": "ابدأ مقاطع الفيديو",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "يجب تمكين ميزة AutoReload. `yarn develop` يرجى بدء تطبيقك بـ .",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "أفهم!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "لأسباب أمنية ، لا يمكن تنزيل المكون الإضافي إلا في بيئة التطوير.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "التنزيل مستحيل",
  "app.components.ToggleCheckbox.off-label": "خطأ",
  "app.components.ToggleCheckbox.on-label": "حقيقي",
  "app.components.Users.MagicLink.connect": "انسخ هذا الرابط وشاركه لمنح حق الوصول لهذا المستخدم",
  "app.components.Users.MagicLink.connect.sso": "أرسل هذا الرابط إلى المستخدم ، حيث يمكن إجراء أول تسجيل دخول عبر موفر خدمة الدخول الموحد",
  "app.components.Users.ModalCreateBody.block-title.details": "بيانات المستخدم",
  "app.components.Users.ModalCreateBody.block-title.roles": "أدوار المستخدم",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "يمكن للمستخدم أن يكون له دور واحد أو عدة أدوار",
  "app.components.Users.SortPicker.button-label": "ترتيب حسب",
  "app.components.Users.SortPicker.sortby.email_asc": "البريد الإلكتروني (من الألف إلى الياء)",
  "app.components.Users.SortPicker.sortby.email_desc": "بريد إلكتروني (من ي إلى أ)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "الاسم الأول (من الألف إلى الياء)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "الاسم الأول (ي إلى أ)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "الاسم الأخير (من الألف إلى الياء)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "الاسم الأخير (ي إلى أ)",
  "app.components.Users.SortPicker.sortby.username_asc": "اسم المستخدم (من الألف إلى الياء)",
  "app.components.Users.SortPicker.sortby.username_desc": "اسم المستخدم (من ي إلى أ)",
  "app.containers.App.notification.error.init": "API حدث خطأ أثناء الطلب",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "إذا لم تستلم هذا الرابط ، فيرجى الاتصال بالمسؤول.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "قد يستغرق استلام رابط استعادة كلمة المرور بضع دقائق.",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "أرسل البريد الإلكتروني",
  "app.containers.Users.EditPage.form.active.label": "فعال",
  "app.containers.Users.EditPage.header.label": "تعديل {الاسم}",
  "app.containers.Users.EditPage.header.label-loading": "تحرير العضو",
  "app.containers.Users.EditPage.roles-bloc-title": "الأدوار المنسوبة",
  "app.containers.Users.ModalForm.footer.button-success": "قم بدعوة المستخدم",
  "app.links.configure-view": "تكوين العرض",
  "app.page.not.found": "أُووبس! يبدو أنه لا يمكننا العثور على الصفحة التي تبحث عنها ...",
  "app.static.links.cheatsheet": "ورقة الغش",
  "app.utils.add-filter": "أضف عامل تصفية",
  "app.utils.close-label": "يغلق",
  "app.utils.delete": "يمسح",
  "app.utils.duplicate": "ينسخ",
  "app.utils.edit": "يحرر",
  "app.utils.errors.file-too-big.message": "الملف كبير جدًا",
  "app.utils.filter-value": "قيمة التصفية",
  "app.utils.filters": "المرشحات",
  "app.utils.notify.data-loaded": "{target} تم تحميل",
  "app.utils.publish": "أصدر",
  "app.utils.select-all": "اختر الكل",
  "app.utils.select-field": "حدد المجال",
  "app.utils.select-filter": "حدد عامل تصفية",
  "app.utils.unpublish": "إلغاء النشر",
  "Auth.components.Oops.text": "تم تعليق حسابك.",
  "Auth.components.Oops.text.admin": "إذا كان هذا خطأ ، يرجى الاتصال بالمسؤول.",
  "Auth.components.Oops.title": "أُووبس...",
  "Auth.form.active.label": "فعال",
  "Auth.form.button.go-home": "ارجع الى الصفحة الرئيسية",
  "Auth.form.button.login.providers.error": "لا يمكننا توصيلك من خلال المزود المحدد.",
  "Auth.form.button.login.strapi": "Strapi تسجيل الدخول عبر",
  "Auth.form.button.password-recovery": "استعادة كلمة السر",
  "Auth.form.confirmPassword.label": "تأكيد كلمة المرور",
  "Auth.form.currentPassword.label": "كلمة السر الحالية",
  "Auth.form.error.blocked": "تم حظر حسابك من قبل المسؤول.",
  "Auth.form.error.confirmed": "لم يتم تأكيد البريد الإلكتروني لحسابك.",
  "Auth.form.error.ratelimit": "محاولات كثيرة ، يرجى المحاولة مرة أخرى خلال دقيقة.",
  "Auth.form.firstname.label": "الاسم الأول",
  "Auth.form.firstname.placeholder": "على سبيل المثال سمر",
  "Auth.form.lastname.label": "اسم العائلة",
  "Auth.form.lastname.placeholder": "على سبيل المثال سامي",
  "Auth.form.password.hide-password": "اخفاء كلمة المرور",
  "Auth.form.password.hint": "يجب ألا يقل عدد الأحرف عن 8 أحرف ، وحرف كبير واحد ، ورقم واحد صغير ، ورقم واحد",
  "Auth.form.password.show-password": "عرض كلمة المرور",
  "Auth.form.register.news.label": "ابقني على اطلاع بالميزات الجديدة والتحسينات القادمة (من خلال القيام بذلك فأنت تقبل {terms} و ال {policy}).",
  "Auth.form.register.subtitle": "تُستخدم بيانات الاعتماد فقط للمصادقة في Strapi. سيتم تخزين جميع البيانات المحفوظة في قاعدة البيانات الخاصة بك.",
  "Auth.form.welcome.subtitle": "Strapi قم بتسجيل الدخول إلى حسابك على",
  "Auth.form.welcome.title": "Strapi! مرحبا بك في",
  "Auth.link.signin": "تسجيل الدخول",
  "Auth.link.signin.account": "هل لديك حساب؟",
  "Auth.login.sso.divider": "أو تسجيل الدخول باستخدام",
  "Auth.login.sso.loading": "تحميل الموفرين ...",
  "Auth.login.sso.subtitle": "SSO تسجيل الدخول إلى حسابك عبر",
  "Auth.privacy-policy-agreement.policy": "سياسة الخصوصية",
  "Auth.privacy-policy-agreement.terms": "شروط",
  "Auth.reset-password.title": "إعادة تعيين كلمة المرور",
  clearLabel,
  "coming.soon": "هذا المحتوى قيد الإنشاء حاليًا وسيعود في غضون أسابيع قليلة!",
  "component.Input.error.validation.integer": "يجب أن تكون القيمة عددًا صحيحًا",
  "components.AutoReloadBlocker.description": "Strapi قم بتشغيل  باستخدام أحد الأوامر التالية:",
  "components.FilterOptions.FILTER_TYPES.$contains": "يحتوي على",
  "components.FilterOptions.FILTER_TYPES.$containsi": "يحتوي على (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "ينتهي بـ",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "ينتهي بـ (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$eq": "هو",
  "components.FilterOptions.FILTER_TYPES.$eqi": "هو (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$gt": "أكبر من",
  "components.FilterOptions.FILTER_TYPES.$gte": "أكبر من أو يساوي",
  "components.FilterOptions.FILTER_TYPES.$lt": "أقل من",
  "components.FilterOptions.FILTER_TYPES.$lte": "أقل من أو يساوي",
  "components.FilterOptions.FILTER_TYPES.$ne": "ليس",
  "components.FilterOptions.FILTER_TYPES.$nei": "ليس (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "لا يحتوي على",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "لا يحتوي على (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "هو ليس لاشيء",
  "components.FilterOptions.FILTER_TYPES.$null": "هو لاشيء",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "يبدا ب",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "يبدا ب (case insensitive)",
  "components.Input.error.contain.lowercase": "يجب أن تحتوي كلمة المرور على حرف صغير واحد على الأقل",
  "components.Input.error.contain.number": "يجب ان تحتوي كلمة المرور على الاقل رقما واحدا",
  "components.Input.error.contain.uppercase": "يجب أن تحتوي كلمة المرور على حرف كبير واحد على الأقل",
  "components.Input.error.validation.lowercase": "يجب أن تكون القيمة سلسلة أحرف صغيرة",
  "components.Input.error.validation.unique": "هذه القيمة مستخدمة بالفعل.",
  "components.InputSelect.option.placeholder": "اختر هنا",
  "components.NotAllowedInput.text": "لا أذونات لرؤية هذا المجال",
  "components.OverlayBlocker.description.serverError": "يجب إعادة تشغيل الخادم ، يرجى التحقق من سجلاتك في المحطة.",
  "components.OverlayBlocker.title.serverError": "تستغرق إعادة التشغيل وقتًا أطول من المتوقع",
  "components.pagination.go-to": "{page} انتقل إلى صفحة",
  "components.pagination.go-to-next": "انتقل إلى الصفحة التالية",
  "components.pagination.go-to-previous": "الانتقال إلى الصفحة السابقة",
  "components.pagination.remaining-links": "روابط أخرى{number} و",
  "components.popUpWarning.button.cancel": "لا ، إلغاء",
  "components.popUpWarning.button.confirm": "نعم ، قم بالتأكيد",
  "components.Search.placeholder": "بحث...",
  "components.TableHeader.sort": "{label} الفرز على",
  "components.Wysiwyg.ToggleMode.markdown-mode": "وضع Markdown",
  "components.Wysiwyg.ToggleMode.preview-mode": "وضعية المعاينة",
  "Content Type Builder": "منشئ أنواع المحتوى",
  "content-manager.api.id": "معرف API",
  "content-manager.apiError.This attribute must be unique": "{field} يجب أن يكون فريدًا",
  "content-manager.App.schemas.data-loaded": "تم تحميل المخططات بنجاح",
  "content-manager.components.DraggableCard.delete.field": "{item} حذف",
  "content-manager.components.DraggableCard.edit.field": "{item} حرر",
  "content-manager.components.DraggableCard.move.field": "{item} تحرك",
  "content-manager.components.DragHandle-label": "جر",
  "content-manager.components.DynamicTable.row-line": "{number} سطر البند",
  "content-manager.components.DynamicZone.add-component": "{componentName} أضف مكونًا إلى",
  "content-manager.components.DynamicZone.ComponentPicker-label": "اختر مكونًا واحدًا",
  "content-manager.components.DynamicZone.delete-label": "{name} حذف",
  "content-manager.components.DynamicZone.error-message": "يحتوي المكون على خطأ (أخطاء)",
  "content-manager.components.DynamicZone.missing-components": "There {number, plural, =0 {are # missing components} واحد {is # missing component} آخر {are # missing components}}",
  "content-manager.components.DynamicZone.move-down-label": "انقل المكون لأسفل",
  "content-manager.components.DynamicZone.move-up-label": "انقل المكون لأعلى",
  "content-manager.components.DynamicZone.pick-compo": "اختر مكونًا واحدًا",
  "content-manager.components.DynamicZone.required": "المكون مطلوب",
  "content-manager.components.empty-repeatable": "لا دخول حتى الان. انقر فوق الزر أدناه لإضافة واحد.",
  "content-manager.components.FieldItem.linkToComponentLayout": "قم بتعيين تخطيط المكون",
  "content-manager.components.FieldSelect.label": "أضف حقلاً",
  "content-manager.components.LeftMenu.collection-types": "أنواع المجموعات",
  "content-manager.components.LeftMenu.Search.label": "ابحث عن نوع المحتوى",
  "content-manager.components.LeftMenu.single-types": "أنواع مفردة",
  "content-manager.components.NotAllowedInput.text": "لا أذونات لرؤية هذا المجال",
  "content-manager.components.notification.info.maximum-requirement": "لقد وصلت بالفعل إلى الحد الأقصى لعدد الحقول",
  "content-manager.components.notification.info.minimum-requirement": "تمت إضافة حقل لمطابقة الحد الأدنى من المتطلبات",
  "content-manager.components.RelationInput.icon-button-aria-label": "جر",
  "content-manager.components.repeatable.reorder.error": "حدث خطأ أثناء إعادة ترتيب حقل المكون الخاص بك ، يرجى المحاولة مرة أخرى",
  "content-manager.components.RepeatableComponent.error-message": "يحتوي المكون (المكونات) على خطأ (أخطاء)",
  "content-manager.components.reset-entry": "إعادة الدخول",
  "content-manager.components.Select.draft-info-title": "الحالة: مسودة",
  "content-manager.components.Select.publish-info-title": "الحالة: منشور",
  "content-manager.components.SettingsViewWrapper.pluginHeader.description.edit-settings": "تخصيص كيف سيبدو عرض التحرير.",
  "content-manager.components.SettingsViewWrapper.pluginHeader.description.list-settings": "حدد إعدادات عرض القائمة.",
  "content-manager.components.SettingsViewWrapper.pluginHeader.title": "{name} تكوين العرض -",
  "content-manager.components.TableDelete.label": "{number, plural, one {# entry} آخر {# entries}} selected",
  "content-manager.components.uid.apply": "طبق",
  "content-manager.components.uid.available": "متاح",
  "content-manager.components.uid.regenerate": "تجديد",
  "content-manager.components.uid.suggested": "مقترح",
  "content-manager.components.uid.unavailable": "غير متوفره",
  "content-manager.containers.Edit.delete-entry": "احذف هذا الإدخال",
  "content-manager.containers.Edit.information": "معلومة",
  "content-manager.containers.Edit.information.by": "بواسطة",
  "content-manager.containers.Edit.information.created": "أُنشء",
  "content-manager.containers.Edit.information.draftVersion": "نسخة المسودة",
  "content-manager.containers.Edit.information.editing": "التحرير",
  "content-manager.containers.Edit.information.lastUpdate": "اخر تحديث",
  "content-manager.containers.Edit.information.publishedVersion": "النسخة المنشورة",
  "content-manager.containers.Edit.Link.Layout": "تكوين التخطيط",
  "content-manager.containers.Edit.Link.Model": "تحرير نوع المجموعة",
  "content-manager.containers.Edit.pluginHeader.title.new": "قم بإنشاء إدخال",
  "content-manager.containers.EditSettingsView.modal-form.edit-field": "قم بتحرير الحقل",
  "content-manager.containers.EditView.add.new-entry": "أضف إدخالاً",
  "content-manager.containers.EditView.notification.errors": "النموذج يحتوي على بعض الأخطاء",
  "content-manager.containers.List.draft": "مسودة",
  "content-manager.containers.List.published": "نشرت",
  "content-manager.containers.list.items": "{number, plural, =0 {items} one {item} other {items}}",
  "content-manager.containers.list.table-headers.publishedAt": "State",
  "content-manager.containers.ListSettingsView.modal-form.edit-label": "{fieldName} تعديل",
  "content-manager.containers.SettingPage.add.field": "أدخل حقل آخر",
  "content-manager.containers.SettingPage.add.relational-field": "أدخل حقل آخر ذي صلة",
  "content-manager.containers.SettingPage.editSettings.entry.title": "عنوان الإدخال",
  "content-manager.containers.SettingPage.editSettings.entry.title.description": "اضبط الحقل المعروض لإدخالك",
  "content-manager.containers.SettingPage.editSettings.relation-field.description": "قم بتعيين الحقل المعروض في كل من طريقتي التحرير وعرض القائمة",
  "content-manager.containers.SettingPage.layout": "تَخطِيط",
  "content-manager.containers.SettingPage.listSettings.description": "تكوين الخيارات لنوع المجموعة هذا",
  "content-manager.containers.SettingPage.pluginHeaderDescription": "تكوين الإعدادات المحددة لنوع المجموعة هذا",
  "content-manager.containers.SettingPage.relations": "حقول ذات صله",
  "content-manager.containers.SettingPage.settings": "إعدادات",
  "content-manager.containers.SettingPage.view": "رؤية",
  "content-manager.containers.SettingsPage.Block.contentType.title": "أنواع المجموعات",
  "content-manager.containers.SettingsPage.Block.generalSettings.description": "تكوين الخيارات الافتراضية لأنواع المجموعة الخاصة بك",
  "content-manager.containers.SettingsPage.pluginHeaderDescription": "قم بتكوين الإعدادات لجميع أنواع المجموعات والمجموعات الخاصة بك",
  "content-manager.containers.SettingsView.list.subtitle": "تكوين تخطيط وعرض أنواع المجموعات والمجموعات الخاصة بك",
  "content-manager.containers.SettingsView.list.title": "تكوينات العرض",
  "content-manager.containers.SettingViewModel.pluginHeader.title": "{name} مدير محتوى -",
  "content-manager.dnd.cancel-item": "{item}, dropped. Re-order cancelled.",
  "content-manager.dnd.drop-item": "{item}, dropped. Final position in list: {position}.",
  "content-manager.dnd.grab-item": "{item}, grabbed. Current position in list: {position}. Press up and down arrow to change position, Spacebar to drop, Escape to cancel.",
  "content-manager.dnd.instructions": "اضغط على مفتاح المسافة للاستيلاء وإعادة الترتيب",
  "content-manager.dnd.reorder": "{item}, انتقل. منصب جديد في القائمة: {position}.",
  "content-manager.DynamicTable.relation-loaded": "تم تحميل العلاقات",
  "content-manager.DynamicTable.relation-loading": "يتم تحميل العلاقات",
  "content-manager.DynamicTable.relation-more": "تحتوي هذه العلاقة على كيانات أكثر من المعروضة",
  "content-manager.edit-settings-view.link-to-ctb.components": "قم بتحرير المكون",
  "content-manager.edit-settings-view.link-to-ctb.content-types": "قم بتحرير نوع المحتوى",
  "content-manager.emptyAttributes.button": "انتقل إلى منشئ نوع المجموعة",
  "content-manager.emptyAttributes.description": "أضف حقلك الأول إلى نوع المجموعة الخاصة بك",
  "content-manager.form.Input.hint.character.unit": "{maxValue, plural, one { character} other { characters}}",
  "content-manager.form.Input.hint.minMaxDivider": " / ",
  "content-manager.form.Input.hint.text": "{min, select, undefined {} other {min. {min}}}{divider}{max, select, undefined {} other {max. {max}}}{unit}{br}{description}",
  "content-manager.form.Input.pageEntries.inputDescription": "ملاحظة: يمكنك تجاوز هذه القيمة في صفحة إعدادات نوع المجموعة.",
  "content-manager.form.Input.sort.order": "ترتيب الافتراضي",
  "content-manager.form.Input.wysiwyg": "WYSIWYG عرض كـ",
  "content-manager.global.displayedFields": "الحقول المعروضة",
  "content-manager.groups": "مجموعات",
  "content-manager.groups.numbered": "({number}) مجموعات",
  "content-manager.header.name": "محتوى",
  "content-manager.HeaderLayout.button.label-add-entry": "إنشاء إدخال جديد",
  "content-manager.link-to-ctb": "قم بتحرير النموذج",
  "content-manager.models": "أنواع المجموعات",
  "content-manager.models.numbered": "({number}) أنواع المجموعات",
  "content-manager.notification.info.minimumFields": "يجب أن يكون لديك حقل واحد على الأقل معروض",
  "content-manager.notification.upload.error": "حدث خطأ أثناء تحميل ملفاتك",
  "content-manager.pages.ListView.header-subtitle": "{number, plural, =0 {# entries} one {# entry} other {# entries}} found",
  "content-manager.pages.NoContentType.button": "قم بإنشاء نوع المحتوى الأول الخاص بك",
  "content-manager.pages.NoContentType.text": "ليس لديك أي محتوى حتى الآن ، نوصيك بإنشاء نوع المحتوى الأول الخاص بك.",
  "content-manager.permissions.not-allowed.create": "لا يسمح لك لإنشاء وثيقة",
  "content-manager.permissions.not-allowed.update": "لا يسمح لك أن ترى هذه الوثيقة",
  "content-manager.popover.display-relations.label": "عرض العلاقات",
  "content-manager.popUpwarning.warning.has-draft-relations.button-confirm": "نعم ، انشر",
  "content-manager.popUpwarning.warning.has-draft-relations.message": "<b>{count, plural, one { relation is } آخر { relations are } }</b> لم تنشر بعد وقد تؤدي إلى سلوك غير متوقع.",
  "content-manager.popUpWarning.warning.has-draft-relations.title": "تأكيد",
  "content-manager.popUpWarning.warning.publish-question": "هل مازلت تريد النشر؟",
  "content-manager.popUpWarning.warning.unpublish": "إذا لم تنشر هذا المحتوى ، فسيتحول تلقائيًا إلى مسودة.",
  "content-manager.popUpWarning.warning.unpublish-question": "هل أنت متأكد أنك لا تريد نشره؟",
  "content-manager.relation.add": "أضف العلاقة",
  "content-manager.relation.disconnect": "نزع",
  "content-manager.relation.isLoading": "يتم تحميل العلاقات",
  "content-manager.relation.loadMore": "تحميل المزيد",
  "content-manager.relation.notAvailable": "لا توجد علاقات متاحة",
  "content-manager.relation.publicationState.draft": "مسودة",
  "content-manager.relation.publicationState.published": "منشور",
  "content-manager.select.currently.selected": "{count} المحدد حاليا",
  "content-manager.success.record.publish": "منشور",
  "content-manager.success.record.unpublish": "غير منشورة",
  "content-manager.utils.data-loaded": "The {number, plural, =1 {entry has} other {entries have}} successfully been loaded",
  dark,
  Documentation,
  "form.button.continue": "واصل",
  "form.button.done": "منتهي",
  "global.actions": "أجراءات",
  "global.auditLogs": "سجلات التدقيق",
  "global.back": "الى الوراء",
  "global.cancel": "إلغاء",
  "global.change-password": "تغيير كلمة المرور",
  "global.content-manager": "مدير محتوى",
  "global.continue": "واصل",
  "global.delete": "مسح",
  "global.delete-target": "{target} مسح ",
  "global.description": "وصف",
  "global.details": "تفاصيل",
  "global.disabled": "إبطال",
  "global.documentation": "توثيق",
  "global.enabled": "ممكن",
  "global.finish": "نهاية",
  "global.marketplace": "المتجر",
  "global.name": "اسم",
  "global.none": "لا أحد",
  "global.password": "كلمة المرور",
  "global.plugins": "الإضافات",
  "global.plugins.content-manager": "مدير محتوى",
  "global.plugins.content-manager.description": "طريقة سريعة لرؤية وتحرير وحذف البيانات في قاعدة البيانات الخاصة بك.",
  "global.plugins.content-type-builder": "منشئ نوع المحتوى",
  "global.plugins.content-type-builder.description": "قم بنمذجة بنية البيانات الخاصة بواجهة برمجة التطبيقات (API) الخاصة بك. إنشاء مجالات وعلاقات جديدة في دقيقة واحدة فقط. يتم إنشاء الملفات وتحديثها تلقائيًا في مشروعك.",
  "global.plugins.documentation": "توثيق",
  "global.plugins.documentation.description": "قم بإنشاء مستند OpenAPI وتصور API الخاص بك باستخدام SWAGGER UI.",
  "global.plugins.email": "بريد إلكتروني",
  "global.plugins.email.description": "تكوين التطبيق الخاص بك لإرسال رسائل البريد الإلكتروني.",
  "global.plugins.graphql": "GraphQL",
  "global.plugins.graphql.description": "يضيف نقطة نهاية GraphQL بأساليب واجهة برمجة التطبيقات الافتراضية.",
  "global.plugins.i18n": "تدويل",
  "global.plugins.i18n.description": "يمكّن هذا المكون الإضافي من إنشاء المحتوى وقراءته وتحديثه بلغات مختلفة ، سواء من لوحة الإدارة أو من واجهة برمجة التطبيقات.",
  "global.plugins.sentry": "Sentry",
  "global.plugins.sentry.description": "إرسال أحداث خطأ Strapi إلى Sentry.",
  "global.plugins.upload": "مكتبة الوسائط",
  "global.plugins.upload.description": "إدارة ملفات الوسائط.",
  "global.plugins.users-permissions": "الأدوار والأذونات",
  "global.plugins.users-permissions.description": "قم بحماية API الخاص بك من خلال عملية مصادقة كاملة تعتمد على JWT. يأتي هذا المكون الإضافي أيضًا مع إستراتيجية قائمة التحكم بالوصول (ACL) التي تتيح لك إدارة الأذونات بين مجموعات المستخدمين.",
  "global.profile": "حساب تعريفي",
  "global.prompt.unsaved": "هل أنت متأكد أنك تريد مغادرة هذه الصفحة؟ ستفقد كل تعديلاتك",
  "global.reset-password": "إعادة تعيين كلمة المرور",
  "global.roles": "الأدوار",
  "global.save": "يحفظ",
  "global.search": "يبحث",
  "global.see-more": "شاهد المزيد",
  "global.select": "اختار",
  "global.select-all-entries": "حدد كل الإدخالات",
  "global.settings": "إعدادات",
  "global.type": "نوع",
  "global.users": "المستخدمون",
  "HomePage.head.title": "الصفحة الرئيسية",
  "HomePage.roadmap": "انظر خارطة الطريق لدينا",
  "HomePage.welcome.congrats": "تهاني!",
  "HomePage.welcome.congrats.content": "Strapiلقد قمت بتسجيل الدخول باعتبارك المسؤول الأول. لاكتشاف الميزات القوية التي يوفرها",
  "HomePage.welcome.congrats.content.bold": "نوصيك بإنشاء أول نوع مجموعة خاص بك.",
  light,
  "Media Library": "مكتبة الوسائط",
  "notification.contentType.relations.conflict": "نوع المحتوى له علاقات متضاربة",
  "notification.default.title": "معلومة:",
  "notification.ee.warning.at-seat-limit.title": "{LicenseLimitStatus ، حدد ، OVER_LIMIT {Over} AT_LIMIT {At}} حد المقاعد ({currentUserCount} / {allowedSeats})",
  "notification.ee.warning.over-.message": "أضف مقاعد إلى {LicenseLimitStatus ، حدد ، OVER_LIMIT {دعوة} AT_LIMIT {re-enable}} مستخدمين. إذا كنت قد فعلت ذلك بالفعل ولكن لم ينعكس في Strapi بعد ، فتأكد من إعادة تشغيل التطبيق الخاص بك.",
  "notification.error.invalid.configuration": "لديك تكوين غير صالح ، تحقق من سجل الخادم لمزيد من المعلومات.",
  "notification.error.tokennamenotunique": "تم تعيين الاسم بالفعل لرمز مميز آخر",
  "notification.form.error.fields": "النموذج يحتوي على بعض الأخطاء",
  "notification.form.success.fields": "تم حفظ التغييرات",
  "notification.link-copied": "تم نسخ الرابط في الحافظة",
  "notification.permission.not-allowed-read": "لا يسمح لك أن ترى هذه الوثيقة",
  "notification.success.apitokencreated": "تم إنشاء رمز API بنجاح",
  "notification.success.apitokenedited": "تم تحرير رمز API بنجاح",
  "notification.success.delete": "تم حذف العنصر",
  "notification.success.saved": "حفظ",
  "notification.success.title": "نجاح:",
  "notification.success.transfertokencreated": "تم إنشاء رمز النقل بنجاح",
  "notification.success.transfertokenedited": "تم تحرير رمز النقل بنجاح",
  "notification.version.update.message": "نسخة جديدة متاحة من ستربي!",
  "notification.warning.404": "404 غير موجود",
  "notification.warning.title": "تحذير:",
  or,
  "Roles & Permissions": "الأدوار والأذونات",
  "Roles.components.List.empty.withSearch": "لا يوجد دور مطابق للبحث ({search}) ...",
  "Roles.ListPage.notification.delete-all-not-allowed": "تعذر حذف بعض الأدوار لأنها مرتبطة بالمستخدمين",
  "Roles.ListPage.notification.delete-not-allowed": "لا يمكن حذف الدور إذا كان مرتبطًا بالمستخدمين",
  "Roles.RoleRow.select-all": "حدد {name} للإجراءات المجمعة",
  "Roles.RoleRow.user-count": "{number، plural، = 0 {# user} واحد {# user} آخر {# users}}",
  selectButtonTitle,
  "Settings.apiTokens.addFirstToken": "أضف رمز API الأول الخاص بك",
  "Settings.apiTokens.addNewToken": "إضافة رمز API جديد",
  "Settings.apiTokens.create": "إنشاء رمز API جديد",
  "Settings.apiTokens.createPage.BoundRoute.title": "طريق منضم إلى",
  "Settings.apiTokens.createPage.permissions.description": "يتم سرد الإجراءات المرتبطة بالمسار فقط أدناه.",
  "Settings.apiTokens.createPage.permissions.header.hint": "حدد إجراءات التطبيق أو إجراءات البرنامج المساعد وانقر على أيقونة الترس لعرض المسار المنضم",
  "Settings.apiTokens.createPage.permissions.header.title": "إعدادات متقدمة",
  "Settings.apiTokens.createPage.permissions.title": "أذونات",
  "Settings.apiTokens.createPage.title": "إنشاء رمز API",
  "Settings.apiTokens.description": "قائمة الرموز التي تم إنشاؤها لاستهلاك API",
  "Settings.apiTokens.emptyStateLayout": "ليس لديك أي محتوى حتى الآن ...",
  "Settings.apiTokens.ListView.headers.createdAt": "أنشئت في",
  "Settings.apiTokens.ListView.headers.description": "وصف",
  "Settings.apiTokens.ListView.headers.lastUsedAt": "آخر أستخدام",
  "Settings.apiTokens.ListView.headers.name": "اسم",
  "Settings.apiTokens.ListView.headers.type": "نوع الرمز",
  "Settings.apiTokens.regenerate": "تجديد",
  "Settings.apiTokens.title": "رموز API",
  "Settings.application.customization": "التخصيص",
  "Settings.application.customization.auth-logo.carousel-hint": "استبدل الشعار في صفحات المصادقة",
  "Settings.application.customization.carousel-hint": "تغيير شعار لوحة الإدارة (الحد الأقصى للبعد: {dimension} {dimension} ، الحد الأقصى لحجم الملف: {size} كيلوبايت)",
  "Settings.application.customization.carousel-slide.label": "شريحة الشعار",
  "Settings.application.customization.carousel.auth-logo.title": "شعار Auth",
  "Settings.application.customization.carousel.change-action": "تغيير الشعار",
  "Settings.application.customization.carousel.menu-logo.title": "شعار القائمة",
  "Settings.application.customization.carousel.reset-action": "إعادة تعيين الشعار",
  "Settings.application.customization.carousel.title": "شعار",
  "Settings.application.customization.menu-logo.carousel-hint": "استبدل الشعار في شريط التنقل الرئيسي",
  "Settings.application.customization.modal.cancel": "إلغاء",
  "Settings.application.customization.modal.pending": "شعار معلق",
  "Settings.application.customization.modal.pending.card-badge": "صورة",
  "Settings.application.customization.modal.pending.choose-another": "اختر شعارًا آخر",
  "Settings.application.customization.modal.pending.subtitle": "إدارة الشعار المختار قبل تحميله",
  "Settings.application.customization.modal.pending.title": "الشعار جاهز للتحميل",
  "Settings.application.customization.modal.pending.upload": "تحميل الشعار",
  "Settings.application.customization.modal.tab.label": "كيف تريد تحميل الأصول الخاصة بك؟",
  "Settings.application.customization.modal.upload": "تحميل الشعار",
  "Settings.application.customization.modal.upload.cta.browse": "تصفح ملفات",
  "Settings.application.customization.modal.upload.drag-drop": "قم بالسحب والإفلات هنا أو",
  "Settings.application.customization.modal.upload.error-format": "تم تحميل تنسيق خاطئ (التنسيقات المقبولة فقط: jpeg ، jpg ، png ، svg).",
  "Settings.application.customization.modal.upload.error-network": "خطأ في الشبكة",
  "Settings.application.customization.modal.upload.error-size": "الملف الذي تم تحميله كبير جدًا (الحد الأقصى للبعد: {dimension} x {dimension} ، الحد الأقصى لحجم الملف: {size} كيلوبايت)",
  "Settings.application.customization.modal.upload.file-validation": "أقصى بُعد: {dimension} x {dimension} ، الحد الأقصى للحجم: {size} كيلوبايت",
  "Settings.application.customization.modal.upload.from-computer": "من الكمبيوتر",
  "Settings.application.customization.modal.upload.from-url": "من URL",
  "Settings.application.customization.modal.upload.from-url.input-label": "URL",
  "Settings.application.customization.modal.upload.next": "التالي",
  "Settings.application.customization.size-details": "أقصى بُعد: {dimension} x {dimension} ، الحد الأقصى لحجم الملف: {size} كيلوبايت",
  "Settings.application.description": "المعلومات العالمية للوحة الإدارة",
  "Settings.application.edition-title": "الخطة الحالية",
  "Settings.application.ee-or-ce": "{communityEdition، select، true {Community Edition} أخرى {Enterprise Edition}}",
  "Settings.application.ee.admin-seats.add-seats": "{isHostedOnStrapiCloud، select، true {Add seat} other {Contact sales}}",
  "Settings.application.ee.admin-seats.at-limit-tooltip": "عند الحد: أضف مقاعد لدعوة المزيد من المستخدمين",
  "Settings.application.ee.admin-seats.count": "<text>{enforcementUserCount}</text>/{permittedSeats}",
  "Settings.application.get-help": "احصل على مساعدة",
  "Settings.application.link-pricing": "انظر جميع خطط التسعير",
  "Settings.application.link-upgrade": "قم بترقية لوحة الإدارة الخاصة بك",
  "Settings.application.node-version": "إصدار العقدة",
  "Settings.application.strapi-version": "نسخة ستربي",
  "Settings.application.strapiVersion": "Strapi نسخة",
  "Settings.application.title": "ملخص",
  "Settings.error": "خطأ",
  "Settings.global": "الاعدادات العامة",
  "Settings.PageTitle": "الإعدادات - {name}",
  "Settings.permissions": "لوحة الإدارة",
  "Settings.permissions.auditLogs.action": "فعل",
  "Settings.permissions.auditLogs.admin.auth.success": "دخول المشرف",
  "Settings.permissions.auditLogs.admin.logout": "خروج المسؤول",
  "Settings.permissions.auditLogs.component.create": "تكوين المكون",
  "Settings.permissions.auditLogs.component.delete": "حذف المكون",
  "Settings.permissions.auditLogs.component.update": "مكون التحديث",
  "Settings.permissions.auditLogs.content-type.create": "إنشاء نوع المحتوى",
  "Settings.permissions.auditLogs.content-type.delete": "حذف نوع المحتوى",
  "Settings.permissions.auditLogs.content-type.update": "تحديث نوع المحتوى",
  "Settings.permissions.auditLogs.date": "تاريخ",
  "Settings.permissions.auditLogs.details": "تفاصيل السجل",
  "Settings.permissions.auditLogs.entry.create": "إنشاء إدخال {model، select، undefined {} other {({model})}}",
  "Settings.permissions.auditLogs.entry.delete": "حذف الإدخال {model، select، undefined {} other {({model})}}",
  "Settings.permissions.auditLogs.entry.publish": "نشر الإدخال {model، select، undefined {} other {({model})}}",
  "Settings.permissions.auditLogs.entry.unpublish": "إلغاء نشر الإدخال {model، select، undefined {} other {({model})}}",
  "Settings.permissions.auditLogs.entry.update": "تحديث الإدخال {model، select، undefined {} other {({model})}}",
  "Settings.permissions.auditLogs.filters.combobox.aria-label": "ابحث وحدد خيارًا للتصفية",
  "Settings.permissions.auditLogs.listview.header.subtitle": "سجلات لجميع الأنشطة التي حدثت في بيئتك",
  "Settings.permissions.auditLogs.media.create": "قم بإنشاء وسائط",
  "Settings.permissions.auditLogs.media.delete": "حذف الوسائط",
  "Settings.permissions.auditLogs.media.update": "تحديث الوسائط",
  "Settings.permissions.auditLogs.payload": "الحمولة",
  "Settings.permissions.auditLogs.permission.create": "إنشاء إذن",
  "Settings.permissions.auditLogs.permission.delete": "حذف إذن",
  "Settings.permissions.auditLogs.permission.update": "إذن التحديث",
  "Settings.permissions.auditLogs.role.create": "خلق دور",
  "Settings.permissions.auditLogs.role.delete": "حذف الدور",
  "Settings.permissions.auditLogs.role.update": "تحديث الدور",
  "Settings.permissions.auditLogs.user": "مستخدم",
  "Settings.permissions.auditLogs.user.create": "إنشاء مستخدم",
  "Settings.permissions.auditLogs.user.delete": "مسح المستخدم",
  "Settings.permissions.auditLogs.user.fullname": "{firstname} {lastname}",
  "Settings.permissions.auditLogs.user.update": "تحديث المستخدم",
  "Settings.permissions.auditLogs.userId": "معرف المستخدم",
  "Settings.permissions.category": "إعدادات الأذونات لـ {category}",
  "Settings.permissions.category.plugins": "إعدادات الأذونات للمكوِّن الإضافي {category}",
  "Settings.permissions.conditions.anytime": "في أي وقت",
  "Settings.permissions.conditions.apply": "يتقدم",
  "Settings.permissions.conditions.can": "يستطيع",
  "Settings.permissions.conditions.conditions": "شروط",
  "Settings.permissions.conditions.define-conditions": "حدد الشروط",
  "Settings.permissions.conditions.links": "الروابط",
  "Settings.permissions.conditions.no-actions": "تحتاج أولاً إلى تحديد الإجراءات (إنشاء ، قراءة ، تحديث ، ...) قبل تحديد الشروط عليها.",
  "Settings.permissions.conditions.none-selected": "في أي وقت",
  "Settings.permissions.conditions.or": "أو",
  "Settings.permissions.conditions.when": "متى",
  "Settings.permissions.select-all-by-permission": "حدد كافة أذونات {label}",
  "Settings.permissions.select-by-permission": "اختار {label} إذن",
  "Settings.permissions.users.active": "نشيط",
  "Settings.permissions.users.create": "قم بدعوة مستخدم جديد",
  "Settings.permissions.users.email": "بريد إلكتروني",
  "Settings.permissions.users.firstname": "الاسم الأول",
  "Settings.permissions.users.form.sso": "تواصل مع SSO",
  "Settings.permissions.users.form.sso.description": "عند التمكين (ON) ، يمكن للمستخدمين تسجيل الدخول عبر SSO",
  "Settings.permissions.users.inactive": "غير نشط",
  "Settings.permissions.users.lastname": "اسم العائلة",
  "Settings.permissions.users.listview.header.subtitle": "جميع المستخدمين الذين لديهم حق الوصول إلى لوحة إدارة Strapi",
  "Settings.permissions.users.roles": "الأدوار",
  "Settings.permissions.users.strapi-author": "مؤلف",
  "Settings.permissions.users.strapi-editor": "محرر",
  "Settings.permissions.users.strapi-super-admin": "مشرف فائق",
  "Settings.permissions.users.tabs.label": "أذونات علامات التبويب",
  "Settings.permissions.users.user-status": "حالة المستخدم",
  "Settings.permissions.users.username": "اسم المستخدم",
  "Settings.profile.form.notify.data.loaded": "تم تحميل بيانات ملفك الشخصي",
  "Settings.profile.form.section.experience.clear.select": "امسح لغة الواجهة المحددة",
  "Settings.profile.form.section.experience.here": "هنا",
  "Settings.profile.form.section.experience.interfaceLanguage": "لغة الواجهة",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "سيعرض هذا فقط واجهتك الخاصة باللغة المختارة.",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "سيتم تطبيق تغييرات التفضيلات عليك فقط. يتوفر مزيد من المعلومات{here}.",
  "Settings.profile.form.section.experience.mode.hint": "يعرض واجهتك في الوضع المختار.",
  "Settings.profile.form.section.experience.mode.label": "وضع الواجهة",
  "Settings.profile.form.section.experience.mode.option-label": "{name} وضع",
  "Settings.profile.form.section.experience.title": "خبرة",
  "Settings.profile.form.section.head.title": "ملف تعريفي للمستخدم",
  "Settings.profile.form.section.profile.page.title": "الصفحة الشخصية",
  "Settings.roles.create.description": "تحديد الحقوق الممنوحة للدور",
  "Settings.roles.create.title": "أنشئ دورًا",
  "Settings.roles.created": "تم إنشاء الدور",
  "Settings.roles.edit.title": "تحرير دور",
  "Settings.roles.form.button.users-with-role": "{number, plural, =0 {# users} one {# user} other {# users}} with this role",
  "Settings.roles.form.created": "مكون",
  "Settings.roles.form.description": "اسم ووصف الدور",
  "Settings.roles.form.permission.property-label": "{label} أذونات",
  "Settings.roles.form.permissions.attributesPermissions": "أذونات الحقول",
  "Settings.roles.form.permissions.create": "خلق",
  "Settings.roles.form.permissions.delete": "شطب",
  "Settings.roles.form.permissions.publish": "ينشر",
  "Settings.roles.form.permissions.read": "يقرأ",
  "Settings.roles.form.permissions.update": "تحديث",
  "Settings.roles.list.button.add": "أضف دورًا جديدًا",
  "Settings.roles.list.description": "قائمة الأدوار",
  "Settings.roles.title.singular": "دور",
  "Settings.sso.description": "قم بتكوين الإعدادات لميزة الدخول الموحد.",
  "Settings.sso.form.defaultRole.description": "سيقوم بإرفاق المستخدم الجديد المصادق عليه بالدور المحدد",
  "Settings.sso.form.defaultRole.description-not-allowed": "تحتاج إلى الحصول على إذن لقراءة أدوار المسؤول",
  "Settings.sso.form.defaultRole.label": "الدور الافتراضي",
  "Settings.sso.form.registration.description": "أنشئ مستخدمًا جديدًا على تسجيل الدخول الموحّد (SSO) في حالة عدم وجود حساب",
  "Settings.sso.form.registration.label": "التسجيل التلقائي",
  "Settings.sso.title": "علامة واحدة على",
  "Settings.tokens.Button.cancel": "يلغي",
  "Settings.tokens.Button.regenerate": "تجديد",
  "Settings.tokens.copy.editMessage": "لأسباب تتعلق بالأمان ، لا يمكنك رؤية رمزك المميز إلا مرة واحدة.",
  "Settings.tokens.copy.editTitle": "لم يعد هذا الرمز المميز يمكن الوصول إليه.",
  "Settings.tokens.copy.lastWarning": "تأكد من نسخ هذا الرمز المميز ، فلن تتمكن من رؤيته مرة أخرى!",
  "Settings.tokens.duration.30-days": "30 يوما",
  "Settings.tokens.duration.7-days": "7 أيام",
  "Settings.tokens.duration.90-days": "90 يومًا",
  "Settings.tokens.duration.expiration-date": "تاريخ انتهاء الصلاحية",
  "Settings.tokens.duration.unlimited": "غير محدود",
  "Settings.tokens.form.description": "وصف",
  "Settings.tokens.form.duration": "مدة الرمز",
  "Settings.tokens.form.name": "اسم",
  "Settings.tokens.form.type": "نوع الرمز",
  "Settings.tokens.ListView.headers.createdAt": "أنشئت في",
  "Settings.tokens.ListView.headers.description": "وصف",
  "Settings.tokens.ListView.headers.lastUsedAt": "آخر أستخدام",
  "Settings.tokens.ListView.headers.name": "اسم",
  "Settings.tokens.notification.copied": "تم نسخ الرمز المميز إلى الحافظة.",
  "Settings.tokens.popUpWarning.message": "هل أنت متأكد أنك تريد إعادة إنشاء هذا الرمز المميز؟",
  "Settings.tokens.regenerate": "تجديد",
  "Settings.tokens.RegenerateDialog.title": "إعادة إنشاء الرمز المميز",
  "Settings.tokens.types.custom": "مخصص",
  "Settings.tokens.types.full-access": "الوصول الكامل",
  "Settings.tokens.types.read-only": "يقرأ فقط",
  "Settings.transferTokens.addFirstToken": "أضف أول رمز تحويل خاص بك",
  "Settings.transferTokens.addNewToken": "أضف رمز تحويل جديد",
  "Settings.transferTokens.create": "إنشاء رمز تحويل جديد",
  "Settings.transferTokens.createPage.title": "إنشاء رمز التحويل",
  "Settings.transferTokens.description": "قائمة برموز التحويل المُنشأة",
  "Settings.transferTokens.emptyStateLayout": "ليس لديك أي محتوى حتى الآن ...",
  "Settings.transferTokens.ListView.headers.type": "نوع الرمز",
  "Settings.transferTokens.title": "رموز التحويل",
  "Settings.webhooks.create": "إنشاء خطاف ويب",
  "Settings.webhooks.create.header": "إنشاء رأس جديد",
  "Settings.webhooks.created": "تم إنشاء الرد التلقائي على الويب",
  "Settings.webhooks.event.publish-tooltip": "هذا الحدث موجود فقط للمحتويات مع تمكين نظام المسودة / النشر",
  "Settings.webhooks.events.create": "أخلق",
  "Settings.webhooks.events.update": "تحديث",
  "Settings.webhooks.form.events": "الأحداث",
  "Settings.webhooks.form.headers": "الرؤوس",
  "Settings.webhooks.form.url": "URL",
  "Settings.webhooks.headers.remove": "{number} قم بإزالة صف الرأس",
  "Settings.webhooks.key": "مفتاح",
  "Settings.webhooks.list.button.add": "إنشاء خطاف ويب جديد",
  "Settings.webhooks.list.description": "احصل على إخطارات التغييرات POST",
  "Settings.webhooks.list.empty.description": "لم يتم العثور على خطافات الويب",
  "Settings.webhooks.list.empty.link": "انظر وثائقنا",
  "Settings.webhooks.list.empty.title": "لا توجد خطاطيف ويب حتى الان",
  "Settings.webhooks.list.th.actions": "أجراءات",
  "Settings.webhooks.list.th.status": "حالة",
  "Settings.webhooks.singular": "الويب هوك",
  "Settings.webhooks.title": "ويب هوك",
  "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, one {# asset} other {# assets}} selected",
  "Settings.webhooks.trigger": "مشغل",
  "Settings.webhooks.trigger.cancel": "إلغاء المشغل...",
  "Settings.webhooks.trigger.pending": "قيد الانتظار…",
  "Settings.webhooks.trigger.save": "يرجى الحفظ للتشغيل",
  "Settings.webhooks.trigger.success": "نجاح!",
  "Settings.webhooks.trigger.success.label": "نجح الزناد",
  "Settings.webhooks.trigger.test": "اختبار الزناد",
  "Settings.webhooks.trigger.title": "احفظ قبل تشغيل",
  "Settings.webhooks.value": "قيمة",
  skipToContent,
  submit,
  "Usecase.back-end": "المطور الخلفي",
  "Usecase.button.skip": "تخطي هذا السؤال",
  "Usecase.content-creator": "صانع المحتوى",
  "Usecase.front-end": "مطور الواجهة الأمامية",
  "Usecase.full-stack": "مطور كامل المكدس",
  "Usecase.input.work-type": "ما نوع العمل الذي تفعله؟",
  "Usecase.notification.success.project-created": "تم إنشاء المشروع بنجاح",
  "Usecase.other": "آخر",
  "Usecase.title": "تخبرنا أكثر قليلا عن نفسك",
  "Users.components.List.empty": "لا يوجد مستخدمون ...",
  "Users.components.List.empty.withFilters": "لا يوجد مستخدمون لديهم عوامل التصفية المطبقة ...",
  "Users.components.List.empty.withSearch": "لا يوجد مستخدمون مطابقون للبحث({search})..."
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  dark,
  ar as default,
  light,
  or,
  selectButtonTitle,
  skipToContent,
  submit
};
//# sourceMappingURL=ar-7vbwrPth-5LBFD5BD.js.map
