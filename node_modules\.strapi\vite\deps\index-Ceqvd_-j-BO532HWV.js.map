{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/admin/src/components/FormModal/Input/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/components/FormModal/index.jsx", "../../../@strapi/plugin-users-permissions/admin/src/pages/Providers/utils/forms.js", "../../../@strapi/plugin-users-permissions/admin/src/pages/Providers/index.jsx"], "sourcesContent": ["/**\n *\n * Input\n *\n */\n\nimport React from 'react';\n\nimport { TextInput, Toggle, Field } from '@strapi/design-system';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nconst Input = ({\n  description,\n  disabled,\n  intlLabel,\n  error,\n  name,\n  onChange,\n  placeholder,\n  providerToEditName,\n  type,\n  value,\n}) => {\n  const { formatMessage } = useIntl();\n  const inputValue =\n    name === 'noName'\n      ? `${window.strapi.backendURL}/api/connect/${providerToEditName}/callback`\n      : value;\n\n  const label = formatMessage(\n    { id: intlLabel.id, defaultMessage: intlLabel.defaultMessage },\n    { provider: providerToEditName, ...intlLabel.values }\n  );\n  const hint = description\n    ? formatMessage(\n        { id: description.id, defaultMessage: description.defaultMessage },\n        { provider: providerToEditName, ...description.values }\n      )\n    : '';\n\n  if (type === 'bool') {\n    return (\n      <Field.Root hint={hint} name={name}>\n        <Field.Label>{label}</Field.Label>\n        <Toggle\n          aria-label={name}\n          checked={value}\n          disabled={disabled}\n          offLabel={formatMessage({\n            id: 'app.components.ToggleCheckbox.off-label',\n            defaultMessage: 'Off',\n          })}\n          onLabel={formatMessage({\n            id: 'app.components.ToggleCheckbox.on-label',\n            defaultMessage: 'On',\n          })}\n          onChange={(e) => {\n            onChange({ target: { name, value: e.target.checked } });\n          }}\n        />\n        <Field.Hint />\n      </Field.Root>\n    );\n  }\n\n  const formattedPlaceholder = placeholder\n    ? formatMessage(\n        { id: placeholder.id, defaultMessage: placeholder.defaultMessage },\n        { ...placeholder.values }\n      )\n    : '';\n\n  const errorMessage = error ? formatMessage({ id: error, defaultMessage: error }) : '';\n\n  return (\n    <Field.Root error={errorMessage} name={name}>\n      <Field.Label>{label}</Field.Label>\n      <TextInput\n        disabled={disabled}\n        onChange={onChange}\n        placeholder={formattedPlaceholder}\n        type={type}\n        value={inputValue}\n      />\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\nInput.defaultProps = {\n  description: null,\n  disabled: false,\n  error: '',\n  placeholder: null,\n  value: '',\n};\n\nInput.propTypes = {\n  description: PropTypes.shape({\n    id: PropTypes.string.isRequired,\n    defaultMessage: PropTypes.string.isRequired,\n    values: PropTypes.object,\n  }),\n  disabled: PropTypes.bool,\n  error: PropTypes.string,\n  intlLabel: PropTypes.shape({\n    id: PropTypes.string.isRequired,\n    defaultMessage: PropTypes.string.isRequired,\n    values: PropTypes.object,\n  }).isRequired,\n  name: PropTypes.string.isRequired,\n  onChange: PropTypes.func.isRequired,\n  placeholder: PropTypes.shape({\n    id: PropTypes.string.isRequired,\n    defaultMessage: PropTypes.string.isRequired,\n    values: PropTypes.object,\n  }),\n  providerToEditName: PropTypes.string.isRequired,\n  type: PropTypes.string.isRequired,\n  value: PropTypes.oneOfType([PropTypes.bool, PropTypes.string]),\n};\n\nexport default Input;\n", "/**\n *\n * FormModal\n *\n */\n\nimport React from 'react';\n\nimport { Button, Flex, Grid, Modal, Breadcrumbs, Crumb } from '@strapi/design-system';\nimport { Form, Formik } from 'formik';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport Input from './Input';\n\nconst FormModal = ({\n  headerBreadcrumbs,\n  initialData,\n  isSubmiting,\n  layout,\n  isOpen,\n  onSubmit,\n  onToggle,\n  providerToEditName,\n}) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Modal.Root open={isOpen} onOpenChange={onToggle}>\n      <Modal.Content>\n        <Modal.Header>\n          <Breadcrumbs label={headerBreadcrumbs.join(', ')}>\n            {headerBreadcrumbs.map((crumb, index, arr) => (\n              <Crumb isCurrent={index === arr.length - 1} key={crumb}>\n                {crumb}\n              </Crumb>\n            ))}\n          </Breadcrumbs>\n        </Modal.Header>\n        <Formik\n          onSubmit={(values) => onSubmit(values)}\n          initialValues={initialData}\n          validationSchema={layout.schema}\n          validateOnChange={false}\n        >\n          {({ errors, handleChange, values }) => {\n            return (\n              <Form>\n                <Modal.Body>\n                  <Flex direction=\"column\" alignItems=\"stretch\" gap={1}>\n                    <Grid.Root gap={5}>\n                      {layout.form.map((row) => {\n                        return row.map((input) => {\n                          return (\n                            <Grid.Item\n                              key={input.name}\n                              col={input.size}\n                              xs={12}\n                              direction=\"column\"\n                              alignItems=\"stretch\"\n                            >\n                              <Input\n                                {...input}\n                                error={errors[input.name]}\n                                onChange={handleChange}\n                                value={values[input.name]}\n                                providerToEditName={providerToEditName}\n                              />\n                            </Grid.Item>\n                          );\n                        });\n                      })}\n                    </Grid.Root>\n                  </Flex>\n                </Modal.Body>\n                <Modal.Footer>\n                  <Button variant=\"tertiary\" onClick={onToggle} type=\"button\">\n                    {formatMessage({\n                      id: 'app.components.Button.cancel',\n                      defaultMessage: 'Cancel',\n                    })}\n                  </Button>\n                  <Button type=\"submit\" loading={isSubmiting}>\n                    {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n                  </Button>\n                </Modal.Footer>\n              </Form>\n            );\n          }}\n        </Formik>\n      </Modal.Content>\n    </Modal.Root>\n  );\n};\n\nFormModal.defaultProps = {\n  initialData: null,\n  providerToEditName: null,\n};\n\nFormModal.propTypes = {\n  headerBreadcrumbs: PropTypes.arrayOf(PropTypes.string).isRequired,\n  initialData: PropTypes.object,\n  layout: PropTypes.shape({\n    form: PropTypes.arrayOf(PropTypes.array),\n    schema: PropTypes.object,\n  }).isRequired,\n  isOpen: PropTypes.bool.isRequired,\n  isSubmiting: PropTypes.bool.isRequired,\n  onSubmit: PropTypes.func.isRequired,\n  onToggle: PropTypes.func.isRequired,\n  providerToEditName: PropTypes.string,\n};\n\nexport default FormModal;\n", "import { translatedErrors } from '@strapi/strapi/admin';\nimport * as yup from 'yup';\n\nimport { getTrad } from '../../../utils';\n\nconst callbackLabel = {\n  id: getTrad('PopUpForm.Providers.redirectURL.front-end.label'),\n  defaultMessage: 'The redirect URL to your front-end app',\n};\nconst callbackPlaceholder = {\n  id: 'http://www.client-app.com',\n  defaultMessage: 'http://www.client-app.com',\n};\nconst enabledDescription = {\n  id: getTrad('PopUpForm.Providers.enabled.description'),\n  defaultMessage: \"If disabled, users won't be able to use this provider.\",\n};\nconst enabledLabel = {\n  id: getTrad('PopUpForm.Providers.enabled.label'),\n  defaultMessage: 'Enable',\n};\nconst keyLabel = { id: getTrad('PopUpForm.Providers.key.label'), defaultMessage: 'Client ID' };\nconst hintLabel = {\n  id: getTrad('PopUpForm.Providers.redirectURL.label'),\n  defaultMessage: 'The redirect URL to add in your {provider} application configurations',\n};\nconst textPlaceholder = {\n  id: getTrad('PopUpForm.Providers.key.placeholder'),\n  defaultMessage: 'TEXT',\n};\n\nconst secretLabel = {\n  id: getTrad('PopUpForm.Providers.secret.label'),\n  defaultMessage: 'Client Secret',\n};\n\nconst CALLBACK_REGEX = /^$|^[a-z][a-z0-9+.-]*:\\/\\/[^\\s/$.?#](?:[^\\s]*[^\\s/$.?#])?$/i;\nconst SUBDOMAIN_REGEX = /^(([a-zA-Z0-9-]+\\.)*[a-zA-Z0-9-]+)(:\\d+)?(\\/\\S*)?$/i;\n\nconst forms = {\n  email: {\n    form: [\n      [\n        {\n          intlLabel: enabledLabel,\n          name: 'enabled',\n          type: 'bool',\n          description: enabledDescription,\n          size: 6,\n          // TODO check if still needed\n          // validations: {\n          //   required: true,\n          // },\n        },\n      ],\n    ],\n    schema: yup.object().shape({\n      enabled: yup.bool().required(translatedErrors.required.id),\n    }),\n  },\n  providers: {\n    form: [\n      [\n        {\n          intlLabel: enabledLabel,\n          name: 'enabled',\n          type: 'bool',\n          description: enabledDescription,\n          size: 6,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: keyLabel,\n          name: 'key',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: secretLabel,\n          name: 'secret',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: callbackLabel,\n          placeholder: callbackPlaceholder,\n          name: 'callback',\n          type: 'text',\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: hintLabel,\n          name: 'noName',\n          type: 'text',\n          validations: {},\n          size: 12,\n          disabled: true,\n        },\n      ],\n    ],\n    schema: yup.object().shape({\n      enabled: yup.bool().required(translatedErrors.required.id),\n      key: yup.string().when('enabled', {\n        is: true,\n        then: yup.string().required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      secret: yup.string().when('enabled', {\n        is: true,\n        then: yup.string().required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      callback: yup.string().when('enabled', {\n        is: true,\n        then: yup\n          .string()\n          .matches(CALLBACK_REGEX, translatedErrors.regex.id)\n          .required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n    }),\n  },\n  providersWithSubdomain: {\n    form: [\n      [\n        {\n          intlLabel: enabledLabel,\n          name: 'enabled',\n          type: 'bool',\n          description: enabledDescription,\n          size: 6,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: keyLabel,\n          name: 'key',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: secretLabel,\n          name: 'secret',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: {\n            id: getTrad({ id: 'PopUpForm.Providers.jwksurl.label' }),\n            defaultMessage: 'JWKS URL',\n          },\n          name: 'jwksurl',\n          type: 'text',\n          placeholder: textPlaceholder,\n          size: 12,\n          validations: {\n            required: false,\n          },\n        },\n      ],\n\n      [\n        {\n          intlLabel: {\n            id: getTrad('PopUpForm.Providers.subdomain.label'),\n            defaultMessage: 'Host URI (Subdomain)',\n          },\n          name: 'subdomain',\n          type: 'text',\n          placeholder: {\n            id: getTrad('PopUpForm.Providers.subdomain.placeholder'),\n            defaultMessage: 'my.subdomain.com',\n          },\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: callbackLabel,\n          placeholder: callbackPlaceholder,\n          name: 'callback',\n          type: 'text',\n          size: 12,\n          validations: {\n            required: true,\n          },\n        },\n      ],\n      [\n        {\n          intlLabel: hintLabel,\n          name: 'noName',\n          type: 'text',\n          validations: {},\n          size: 12,\n          disabled: true,\n        },\n      ],\n    ],\n    schema: yup.object().shape({\n      enabled: yup.bool().required(translatedErrors.required.id),\n      key: yup.string().when('enabled', {\n        is: true,\n        then: yup.string().required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      secret: yup.string().when('enabled', {\n        is: true,\n        then: yup.string().required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      subdomain: yup.string().when('enabled', {\n        is: true,\n        then: yup\n          .string()\n          .matches(SUBDOMAIN_REGEX, translatedErrors.regex.id)\n          .required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n      callback: yup.string().when('enabled', {\n        is: true,\n        then: yup\n          .string()\n          .matches(CALLBACK_REGEX, translatedErrors.regex.id)\n          .required(translatedErrors.required.id),\n        otherwise: yup.string(),\n      }),\n    }),\n  },\n};\n\nexport default forms;\n", "import * as React from 'react';\n\nimport { useTracking, Layouts } from '@strapi/admin/strapi-admin';\nimport {\n  IconButton,\n  Table,\n  Tbody,\n  Td,\n  Th,\n  Thead,\n  Tr,\n  Typography,\n  VisuallyHidden,\n  useCollator,\n} from '@strapi/design-system';\nimport { Pencil } from '@strapi/icons';\nimport {\n  Page,\n  useAPIErrorHandler,\n  useNotification,\n  useFetchClient,\n  useRBAC,\n} from '@strapi/strapi/admin';\nimport upperFirst from 'lodash/upperFirst';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQuery, useQueryClient } from 'react-query';\n\nimport FormModal from '../../components/FormModal';\nimport { PERMISSIONS } from '../../constants';\nimport { getTrad } from '../../utils';\n\nimport forms from './utils/forms';\n\nexport const ProvidersPage = () => {\n  const { formatMessage, locale } = useIntl();\n  const queryClient = useQueryClient();\n  const { trackUsage } = useTracking();\n  const [isOpen, setIsOpen] = React.useState(false);\n  const [providerToEditName, setProviderToEditName] = React.useState(null);\n  const { toggleNotification } = useNotification();\n  const { get, put } = useFetchClient();\n  const { formatAPIError } = useAPIErrorHandler();\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const {\n    isLoading: isLoadingPermissions,\n    allowedActions: { canUpdate },\n  } = useRBAC({ update: PERMISSIONS.updateProviders });\n\n  const { isLoading: isLoadingData, data } = useQuery(\n    ['users-permissions', 'get-providers'],\n    async () => {\n      const { data } = await get('/users-permissions/providers');\n\n      return data;\n    },\n    {\n      initialData: {},\n    }\n  );\n\n  const submitMutation = useMutation((body) => put('/users-permissions/providers', body), {\n    async onSuccess() {\n      await queryClient.invalidateQueries(['users-permissions', 'get-providers']);\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({ id: getTrad('notification.success.submit') }),\n      });\n\n      trackUsage('didEditAuthenticationProvider');\n\n      handleToggleModal();\n    },\n    onError(error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    },\n    refetchActive: false,\n  });\n\n  const providers = Object.entries(data)\n    .reduce((acc, [name, provider]) => {\n      const { icon, enabled, subdomain } = provider;\n\n      acc.push({\n        name,\n        icon: icon === 'envelope' ? ['fas', 'envelope'] : ['fab', icon],\n        enabled,\n        subdomain,\n      });\n\n      return acc;\n    }, [])\n    .sort((a, b) => formatter.compare(a.name, b.name));\n\n  const isLoading = isLoadingData || isLoadingPermissions;\n\n  const isProviderWithSubdomain = React.useMemo(() => {\n    if (!providerToEditName) {\n      return false;\n    }\n\n    const providerToEdit = providers.find((obj) => obj.name === providerToEditName);\n\n    return !!providerToEdit?.subdomain;\n  }, [providers, providerToEditName]);\n\n  const layoutToRender = React.useMemo(() => {\n    if (providerToEditName === 'email') {\n      return forms.email;\n    }\n\n    if (isProviderWithSubdomain) {\n      return forms.providersWithSubdomain;\n    }\n\n    return forms.providers;\n  }, [providerToEditName, isProviderWithSubdomain]);\n\n  const handleToggleModal = () => {\n    setIsOpen((prev) => !prev);\n  };\n\n  const handleClickEdit = (provider) => {\n    if (canUpdate) {\n      setProviderToEditName(provider.name);\n      handleToggleModal();\n    }\n  };\n\n  const handleSubmit = async (values) => {\n    trackUsage('willEditAuthenticationProvider');\n\n    submitMutation.mutate({ providers: { ...data, [providerToEditName]: values } });\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: formatMessage({\n              id: getTrad('HeaderNav.link.providers'),\n              defaultMessage: 'Providers',\n            }),\n          }\n        )}\n      </Page.Title>\n      <Page.Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: getTrad('HeaderNav.link.providers'),\n            defaultMessage: 'Providers',\n          })}\n        />\n        <Layouts.Content>\n          <Table colCount={3} rowCount={providers.length + 1}>\n            <Thead>\n              <Tr>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({ id: 'global.name', defaultMessage: 'Name' })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({ id: getTrad('Providers.status'), defaultMessage: 'Status' })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\">\n                    <VisuallyHidden>\n                      {formatMessage({\n                        id: 'global.settings',\n                        defaultMessage: 'Settings',\n                      })}\n                    </VisuallyHidden>\n                  </Typography>\n                </Th>\n              </Tr>\n            </Thead>\n            <Tbody>\n              {providers.map((provider) => (\n                <Tr\n                  key={provider.name}\n                  onClick={() => (canUpdate ? handleClickEdit(provider) : undefined)}\n                >\n                  <Td width=\"45%\">\n                    <Typography fontWeight=\"semiBold\" textColor=\"neutral800\">\n                      {provider.name}\n                    </Typography>\n                  </Td>\n                  <Td width=\"65%\">\n                    <Typography\n                      textColor={provider.enabled ? 'success600' : 'danger600'}\n                      data-testid={`enable-${provider.name}`}\n                    >\n                      {provider.enabled\n                        ? formatMessage({\n                            id: 'global.enabled',\n                            defaultMessage: 'Enabled',\n                          })\n                        : formatMessage({\n                            id: 'global.disabled',\n                            defaultMessage: 'Disabled',\n                          })}\n                    </Typography>\n                  </Td>\n                  <Td onClick={(e) => e.stopPropagation()}>\n                    {canUpdate && (\n                      <IconButton\n                        onClick={() => handleClickEdit(provider)}\n                        variant=\"ghost\"\n                        label=\"Edit\"\n                      >\n                        <Pencil />\n                      </IconButton>\n                    )}\n                  </Td>\n                </Tr>\n              ))}\n            </Tbody>\n          </Table>\n        </Layouts.Content>\n      </Page.Main>\n      <FormModal\n        initialData={data[providerToEditName]}\n        isOpen={isOpen}\n        isSubmiting={submitMutation.isLoading}\n        layout={layoutToRender}\n        headerBreadcrumbs={[\n          formatMessage({\n            id: getTrad('PopUpForm.header.edit.providers'),\n            defaultMessage: 'Edit Provider',\n          }),\n          upperFirst(providerToEditName),\n        ]}\n        onToggle={handleToggleModal}\n        onSubmit={handleSubmit}\n        providerToEditName={providerToEditName}\n      />\n    </Layouts.Root>\n  );\n};\n\nconst ProtectedProvidersPage = () => (\n  <Page.Protect permissions={PERMISSIONS.readProviders}>\n    <ProvidersPage />\n  </Page.Protect>\n);\n\nexport default ProtectedProvidersPage;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,QAAQ,CAAC;EACb;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MAAM;AACE,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,aACJ,SAAS,WACL,GAAG,OAAO,OAAO,UAAU,gBAAgB,kBAAkB,cAC7D;AAEN,QAAM,QAAQ;IACZ,EAAE,IAAI,UAAU,IAAI,gBAAgB,UAAU,eAAe;IAC7D,EAAE,UAAU,oBAAoB,GAAG,UAAU,OAAO;EAAA;AAEtD,QAAM,OAAO,cACT;IACE,EAAE,IAAI,YAAY,IAAI,gBAAgB,YAAY,eAAe;IACjE,EAAE,UAAU,oBAAoB,GAAG,YAAY,OAAO;EAExD,IAAA;AAEJ,MAAI,SAAS,QAAQ;AACnB,eACG,yBAAA,MAAM,MAAN,EAAW,MAAY,MACtB,UAAA;UAAC,wBAAA,MAAM,OAAN,EAAa,UAAM,MAAA,CAAA;UACpB;QAAC;QAAA;UACC,cAAY;UACZ,SAAS;UACT;UACA,UAAU,cAAc;YACtB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,UAAU,CAAC,MAAM;AACN,qBAAA,EAAE,QAAQ,EAAE,MAAM,OAAO,EAAE,OAAO,QAAQ,EAAA,CAAG;UACxD;QAAA;MACF;UACA,wBAAC,MAAM,MAAN,CAAA,CAAW;IACd,EAAA,CAAA;EAEJ;AAEA,QAAM,uBAAuB,cACzB;IACE,EAAE,IAAI,YAAY,IAAI,gBAAgB,YAAY,eAAe;IACjE,EAAE,GAAG,YAAY,OAAO;EAE1B,IAAA;AAEE,QAAA,eAAe,QAAQ,cAAc,EAAE,IAAI,OAAO,gBAAgB,MAAA,CAAO,IAAI;AAEnF,aAAA,yBACG,MAAM,MAAN,EAAW,OAAO,cAAc,MAC/B,UAAA;QAAC,wBAAA,MAAM,OAAN,EAAa,UAAM,MAAA,CAAA;QACpB;MAAC;MAAA;QACC;QACA;QACA,aAAa;QACb;QACA,OAAO;MAAA;IACT;QACA,wBAAC,MAAM,OAAN,CAAA,CAAY;EACf,EAAA,CAAA;AAEJ;AAEA,MAAM,eAAe;EACnB,aAAa;EACb,UAAU;EACV,OAAO;EACP,aAAa;EACb,OAAO;AACT;AAEA,MAAM,YAAY;EAChB,aAAa,kBAAAA,QAAU,MAAM;IAC3B,IAAI,kBAAAA,QAAU,OAAO;IACrB,gBAAgB,kBAAAA,QAAU,OAAO;IACjC,QAAQ,kBAAAA,QAAU;EAAA,CACnB;EACD,UAAU,kBAAAA,QAAU;EACpB,OAAO,kBAAAA,QAAU;EACjB,WAAW,kBAAAA,QAAU,MAAM;IACzB,IAAI,kBAAAA,QAAU,OAAO;IACrB,gBAAgB,kBAAAA,QAAU,OAAO;IACjC,QAAQ,kBAAAA,QAAU;EACnB,CAAA,EAAE;EACH,MAAM,kBAAAA,QAAU,OAAO;EACvB,UAAU,kBAAAA,QAAU,KAAK;EACzB,aAAa,kBAAAA,QAAU,MAAM;IAC3B,IAAI,kBAAAA,QAAU,OAAO;IACrB,gBAAgB,kBAAAA,QAAU,OAAO;IACjC,QAAQ,kBAAAA,QAAU;EAAA,CACnB;EACD,oBAAoB,kBAAAA,QAAU,OAAO;EACrC,MAAM,kBAAAA,QAAU,OAAO;EACvB,OAAO,kBAAAA,QAAU,UAAU,CAAC,kBAAAA,QAAU,MAAM,kBAAAA,QAAU,MAAM,CAAC;AAC/D;AC1GA,IAAM,YAAY,CAAC;EACjB;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AACF,MAAM;AACE,QAAA,EAAE,cAAA,IAAkB,QAAA;AAGxB,aAAA,wBAAC,MAAM,MAAN,EAAW,MAAM,QAAQ,cAAc,UACtC,cAAA,yBAAC,MAAM,SAAN,EACC,UAAA;QAAC,wBAAA,MAAM,QAAN,EACC,cAAC,wBAAA,aAAA,EAAY,OAAO,kBAAkB,KAAK,IAAI,GAC5C,UAAA,kBAAkB,IAAI,CAAC,OAAO,OAAO,YACpC,wBAAC,OAAM,EAAA,WAAW,UAAU,IAAI,SAAS,GACtC,UAD8C,MAAA,GAAA,KAEjD,CACD,EACH,CAAA,EAAA,CACF;QACA;MAAC;MAAA;QACC,UAAU,CAAC,WAAW,SAAS,MAAM;QACrC,eAAe;QACf,kBAAkB,OAAO;QACzB,kBAAkB;QAEjB,UAAC,CAAA,EAAE,QAAQ,cAAc,OAAA,MAAa;AACrC,qBAAA,yBACG,MACC,EAAA,UAAA;gBAAC,wBAAA,MAAM,MAAN,EACC,cAAA,wBAAC,MAAA,EAAK,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,cAAC,wBAAA,KAAK,MAAL,EAAU,KAAK,GACb,UAAO,OAAA,KAAK,IAAI,CAAC,QAAQ;AACjB,qBAAA,IAAI,IAAI,CAAC,UAAU;AAEtB,2BAAA;kBAAC,KAAK;kBAAL;oBAEC,KAAK,MAAM;oBACX,IAAI;oBACJ,WAAU;oBACV,YAAW;oBAEX,cAAA;sBAAC;sBAAA;wBACE,GAAG;wBACJ,OAAO,OAAO,MAAM,IAAI;wBACxB,UAAU;wBACV,OAAO,OAAO,MAAM,IAAI;wBACxB;sBAAA;oBACF;kBAAA;kBAZK,MAAM;gBAAA;cAab,CAEH;YAAA,CACF,EACH,CAAA,EACF,CAAA,EAAA,CACF;gBACA,yBAAC,MAAM,QAAN,EACC,UAAA;kBAAA,wBAAC,QAAA,EAAO,SAAQ,YAAW,SAAS,UAAU,MAAK,UAChD,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cACjB,CAAA,EAAA,CACH;kBACC,wBAAA,QAAA,EAAO,MAAK,UAAS,SAAS,aAC5B,UAAc,cAAA,EAAE,IAAI,eAAe,gBAAgB,OAAA,CAAQ,EAC9D,CAAA;YAAA,EAAA,CACF;UACF,EAAA,CAAA;QAEJ;MAAA;IACF;EAAA,EACF,CAAA,EACF,CAAA;AAEJ;AAEA,UAAU,eAAe;EACvB,aAAa;EACb,oBAAoB;AACtB;AAEA,UAAU,YAAY;EACpB,mBAAmB,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,MAAM,EAAE;EACvD,aAAa,kBAAAA,QAAU;EACvB,QAAQ,kBAAAA,QAAU,MAAM;IACtB,MAAM,kBAAAA,QAAU,QAAQ,kBAAAA,QAAU,KAAK;IACvC,QAAQ,kBAAAA,QAAU;EACnB,CAAA,EAAE;EACH,QAAQ,kBAAAA,QAAU,KAAK;EACvB,aAAa,kBAAAA,QAAU,KAAK;EAC5B,UAAU,kBAAAA,QAAU,KAAK;EACzB,UAAU,kBAAAA,QAAU,KAAK;EACzB,oBAAoB,kBAAAA,QAAU;AAChC;AC3GA,IAAM,gBAAgB;EACpB,IAAI,QAAQ,iDAAiD;EAC7D,gBAAgB;AAClB;AACA,IAAM,sBAAsB;EAC1B,IAAI;EACJ,gBAAgB;AAClB;AACA,IAAM,qBAAqB;EACzB,IAAI,QAAQ,yCAAyC;EACrD,gBAAgB;AAClB;AACA,IAAM,eAAe;EACnB,IAAI,QAAQ,mCAAmC;EAC/C,gBAAgB;AAClB;AACA,IAAM,WAAW,EAAE,IAAI,QAAQ,+BAA+B,GAAG,gBAAgB,YAAA;AACjF,IAAM,YAAY;EAChB,IAAI,QAAQ,uCAAuC;EACnD,gBAAgB;AAClB;AACA,IAAM,kBAAkB;EACtB,IAAI,QAAQ,qCAAqC;EACjD,gBAAgB;AAClB;AAEA,IAAM,cAAc;EAClB,IAAI,QAAQ,kCAAkC;EAC9C,gBAAgB;AAClB;AAEA,IAAM,iBAAiB;AACvB,IAAM,kBAAkB;AAExB,IAAM,QAAQ;EACZ,OAAO;IACL,MAAM;MACJ;QACE;UACE,WAAW;UACX,MAAM;UACN,MAAM;UACN,aAAa;UACb,MAAM;;;;;QAKP;MACF;IACF;IACD,QAAYC,QAAQ,EAAC,MAAM;MACzB,SAAa,OAAM,EAAC,SAAS,YAAiB,SAAS,EAAE;IAC/D,CAAK;EACF;EACD,WAAW;IACT,MAAM;MACJ;QACE;UACE,WAAW;UACX,MAAM;UACN,MAAM;UACN,aAAa;UACb,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MACD;QACE;UACE,WAAW;UACX,MAAM;UACN,MAAM;UACN,aAAa;UACb,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MACD;QACE;UACE,WAAW;UACX,MAAM;UACN,MAAM;UACN,aAAa;UACb,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MACD;QACE;UACE,WAAW;UACX,aAAa;UACb,MAAM;UACN,MAAM;UACN,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MACD;QACE;UACE,WAAW;UACX,MAAM;UACN,MAAM;UACN,aAAa,CAAE;UACf,MAAM;UACN,UAAU;QACX;MACF;IACF;IACD,QAAYA,QAAQ,EAAC,MAAM;MACzB,SAAa,OAAM,EAAC,SAAS,YAAiB,SAAS,EAAE;MACzD,KAASA,QAAA,EAAS,KAAK,WAAW;QAChC,IAAI;QACJ,MAAUA,QAAQ,EAAC,SAAS,YAAiB,SAAS,EAAE;QACxD,WAAeA,QAAQ;MAC/B,CAAO;MACD,QAAYA,QAAA,EAAS,KAAK,WAAW;QACnC,IAAI;QACJ,MAAUA,QAAQ,EAAC,SAAS,YAAiB,SAAS,EAAE;QACxD,WAAeA,QAAQ;MAC/B,CAAO;MACD,UAAcA,QAAA,EAAS,KAAK,WAAW;QACrC,IAAI;QACJ,MACGA,QAAQ,EACR,QAAQ,gBAAgB,YAAiB,MAAM,EAAE,EACjD,SAAS,YAAiB,SAAS,EAAE;QACxC,WAAeA,QAAQ;MAC/B,CAAO;IACP,CAAK;EACF;EACD,wBAAwB;IACtB,MAAM;MACJ;QACE;UACE,WAAW;UACX,MAAM;UACN,MAAM;UACN,aAAa;UACb,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MACD;QACE;UACE,WAAW;UACX,MAAM;UACN,MAAM;UACN,aAAa;UACb,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MACD;QACE;UACE,WAAW;UACX,MAAM;UACN,MAAM;UACN,aAAa;UACb,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MACD;QACE;UACE,WAAW;YACT,IAAI,QAAQ,EAAE,IAAI,oCAAmC,CAAE;YACvD,gBAAgB;UACjB;UACD,MAAM;UACN,MAAM;UACN,aAAa;UACb,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MAED;QACE;UACE,WAAW;YACT,IAAI,QAAQ,qCAAqC;YACjD,gBAAgB;UACjB;UACD,MAAM;UACN,MAAM;UACN,aAAa;YACX,IAAI,QAAQ,2CAA2C;YACvD,gBAAgB;UACjB;UACD,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MACD;QACE;UACE,WAAW;UACX,aAAa;UACb,MAAM;UACN,MAAM;UACN,MAAM;UACN,aAAa;YACX,UAAU;UACX;QACF;MACF;MACD;QACE;UACE,WAAW;UACX,MAAM;UACN,MAAM;UACN,aAAa,CAAE;UACf,MAAM;UACN,UAAU;QACX;MACF;IACF;IACD,QAAYA,QAAQ,EAAC,MAAM;MACzB,SAAa,OAAM,EAAC,SAAS,YAAiB,SAAS,EAAE;MACzD,KAASA,QAAA,EAAS,KAAK,WAAW;QAChC,IAAI;QACJ,MAAUA,QAAQ,EAAC,SAAS,YAAiB,SAAS,EAAE;QACxD,WAAeA,QAAQ;MAC/B,CAAO;MACD,QAAYA,QAAA,EAAS,KAAK,WAAW;QACnC,IAAI;QACJ,MAAUA,QAAQ,EAAC,SAAS,YAAiB,SAAS,EAAE;QACxD,WAAeA,QAAQ;MAC/B,CAAO;MACD,WAAeA,QAAA,EAAS,KAAK,WAAW;QACtC,IAAI;QACJ,MACGA,QAAQ,EACR,QAAQ,iBAAiB,YAAiB,MAAM,EAAE,EAClD,SAAS,YAAiB,SAAS,EAAE;QACxC,WAAeA,QAAQ;MAC/B,CAAO;MACD,UAAcA,QAAA,EAAS,KAAK,WAAW;QACrC,IAAI;QACJ,MACGA,QAAQ,EACR,QAAQ,gBAAgB,YAAiB,MAAM,EAAE,EACjD,SAAS,YAAiB,SAAS,EAAE;QACxC,WAAeA,QAAQ;MAC/B,CAAO;IACP,CAAK;EACF;AACH;AC3OO,IAAM,gBAAgB,MAAM;AACjC,QAAM,EAAE,eAAe,OAAO,IAAI,QAAQ;AAC1C,QAAM,cAAc,eAAA;AACd,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,CAAC,QAAQ,SAAS,IAAU,eAAS,KAAK;AAChD,QAAM,CAAC,oBAAoB,qBAAqB,IAAU,eAAS,IAAI;AACjE,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,EAAE,KAAK,IAAI,IAAI,eAAe;AAC9B,QAAA,EAAE,eAAA,IAAmB,mBAAA;AACrB,QAAA,YAAY,YAAY,QAAQ;IACpC,aAAa;EAAA,CACd;AAEK,QAAA;IACJ,WAAW;IACX,gBAAgB,EAAE,UAAU;EAAA,IAC1B,QAAQ,EAAE,QAAQ,YAAY,gBAAiB,CAAA;AAEnD,QAAM,EAAE,WAAW,eAAe,KAAS,IAAA;IACzC,CAAC,qBAAqB,eAAe;IACrC,YAAY;AACV,YAAM,EAAE,MAAAC,MAAAA,IAAS,MAAM,IAAI,8BAA8B;AAElDA,aAAAA;IACT;IACA;MACE,aAAa,CAAC;IAChB;EAAA;AAGF,QAAM,iBAAiB,YAAY,CAAC,SAAS,IAAI,gCAAgC,IAAI,GAAG;IACtF,MAAM,YAAY;AAChB,YAAM,YAAY,kBAAkB,CAAC,qBAAqB,eAAe,CAAC;AAEvD,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc,EAAE,IAAI,QAAQ,6BAA6B,EAAA,CAAG;MAAA,CACtE;AAED,iBAAW,+BAA+B;AAExB,wBAAA;IACpB;IACA,QAAQ,OAAO;AACM,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IACH;IACA,eAAe;EAAA,CAChB;AAEK,QAAA,YAAY,OAAO,QAAQ,IAAI,EAClC,OAAO,CAAC,KAAK,CAAC,MAAM,QAAQ,MAAM;AACjC,UAAM,EAAE,MAAM,SAAS,UAAA,IAAc;AAErC,QAAI,KAAK;MACP;MACA,MAAM,SAAS,aAAa,CAAC,OAAO,UAAU,IAAI,CAAC,OAAO,IAAI;MAC9D;MACA;IAAA,CACD;AAEM,WAAA;EACN,GAAA,CAAE,CAAA,EACJ,KAAK,CAAC,GAAG,MAAM,UAAU,QAAQ,EAAE,MAAM,EAAE,IAAI,CAAC;AAEnD,QAAM,YAAY,iBAAiB;AAE7B,QAAA,0BAAgC,cAAQ,MAAM;AAClD,QAAI,CAAC,oBAAoB;AAChB,aAAA;IACT;AAEA,UAAM,iBAAiB,UAAU,KAAK,CAAC,QAAQ,IAAI,SAAS,kBAAkB;AAEvE,WAAA,CAAC,EAAC,iDAAgB;EAAA,GACxB,CAAC,WAAW,kBAAkB,CAAC;AAE5B,QAAA,iBAAuB,cAAQ,MAAM;AACzC,QAAI,uBAAuB,SAAS;AAClC,aAAO,MAAM;IACf;AAEA,QAAI,yBAAyB;AAC3B,aAAO,MAAM;IACf;AAEA,WAAO,MAAM;EAAA,GACZ,CAAC,oBAAoB,uBAAuB,CAAC;AAEhD,QAAM,oBAAoB,MAAM;AACpB,cAAA,CAAC,SAAS,CAAC,IAAI;EAAA;AAGrB,QAAA,kBAAkB,CAAC,aAAa;AACpC,QAAI,WAAW;AACb,4BAAsB,SAAS,IAAI;AACjB,wBAAA;IACpB;EAAA;AAGI,QAAA,eAAe,OAAO,WAAW;AACrC,eAAW,gCAAgC;AAE5B,mBAAA,OAAO,EAAE,WAAW,EAAE,GAAG,MAAM,CAAC,kBAAkB,GAAG,OAAO,EAAA,CAAG;EAAA;AAGhF,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAGE,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM,cAAc;UAClB,IAAI,QAAQ,0BAA0B;UACtC,gBAAgB;QAAA,CACjB;MACH;IAAA,EAAA,CAEJ;QACA,yBAAC,KAAK,MAAL,EACC,UAAA;UAAA;QAAC,QAAQ;QAAR;UACC,OAAO,cAAc;YACnB,IAAI,QAAQ,0BAA0B;YACtC,gBAAgB;UAAA,CACjB;QAAA;MACH;UACA,wBAAC,QAAQ,SAAR,EACC,cAAA,yBAAC,OAAM,EAAA,UAAU,GAAG,UAAU,UAAU,SAAS,GAC/C,UAAA;YAAC,wBAAA,OAAA,EACC,cAAA,yBAAC,IACC,EAAA,UAAA;cAAA,wBAAC,IACC,EAAA,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA,EAAE,IAAI,eAAe,gBAAgB,OAAO,CAAC,EAC9D,CAAA,EAAA,CACF;cAAA,wBACC,IACC,EAAA,cAAA,wBAAC,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAA,cAAc,EAAE,IAAI,QAAQ,kBAAkB,GAAG,gBAAgB,SAAS,CAAC,EAC9E,CAAA,EAAA,CACF;cACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAClB,cAAA,wBAAC,gBAAA,EACE,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EACH,CAAA,EACF,CAAA,EAAA,CACF;QAAA,EAAA,CACF,EACF,CAAA;YACC,wBAAA,OAAA,EACE,UAAU,UAAA,IAAI,CAAC,iBACd;UAAC;UAAA;YAEC,SAAS,MAAO,YAAY,gBAAgB,QAAQ,IAAI;YAExD,UAAA;kBAAC,wBAAA,IAAA,EAAG,OAAM,OACR,cAAC,wBAAA,YAAA,EAAW,YAAW,YAAW,WAAU,cACzC,UAAS,SAAA,KACZ,CAAA,EAAA,CACF;kBACA,wBAAC,IAAG,EAAA,OAAM,OACR,cAAA;gBAAC;gBAAA;kBACC,WAAW,SAAS,UAAU,eAAe;kBAC7C,eAAa,UAAU,SAAS,IAAI;kBAEnC,UAAA,SAAS,UACN,cAAc;oBACZ,IAAI;oBACJ,gBAAgB;kBACjB,CAAA,IACD,cAAc;oBACZ,IAAI;oBACJ,gBAAgB;kBAAA,CACjB;gBAAA;cAAA,EAAA,CAET;kBACA,wBAAC,IAAA,EAAG,SAAS,CAAC,MAAM,EAAE,gBAAA,GACnB,UACC,iBAAA;gBAAC;gBAAA;kBACC,SAAS,MAAM,gBAAgB,QAAQ;kBACvC,SAAQ;kBACR,OAAM;kBAEN,cAAA,wBAAC,eAAO,CAAA,CAAA;gBAAA;cAAA,EAAA,CAGd;YAAA;UAAA;UAlCK,SAAS;QAoCjB,CAAA,EAAA,CACH;MAAA,EAAA,CACF,EACF,CAAA;IAAA,EAAA,CACF;QACA;MAAC;MAAA;QACC,aAAa,KAAK,kBAAkB;QACpC;QACA,aAAa,eAAe;QAC5B,QAAQ;QACR,mBAAmB;UACjB,cAAc;YACZ,IAAI,QAAQ,iCAAiC;YAC7C,gBAAgB;UAAA,CACjB;cACD,kBAAAC,SAAW,kBAAkB;QAC/B;QACA,UAAU;QACV,UAAU;QACV;MAAA;IACF;EACF,EAAA,CAAA;AAEJ;AAEM,IAAA,yBAAyB,UAC7B,wBAAC,KAAK,SAAL,EAAa,aAAa,YAAY,eACrC,cAAC,wBAAA,eAAA,CAAc,CAAA,EACjB,CAAA;", "names": ["PropTypes", "create", "data", "upperFirst"]}