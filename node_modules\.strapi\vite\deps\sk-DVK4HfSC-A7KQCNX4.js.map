{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/_chunks/sk-DVK4HfSC.mjs"], "sourcesContent": ["const configurations = \"nastavenia\";\nconst from = \"od\";\nconst sk = {\n  \"attribute.boolean\": \"Logická hodnota\",\n  \"attribute.boolean.description\": \"áno/nie, 1/0, pravda/lož\",\n  \"attribute.component\": \"Komponent\",\n  \"attribute.component.description\": \"<PERSON><PERSON><PERSON> pol<PERSON>, k<PERSON><PERSON> je možné opakovane používať\",\n  \"attribute.date\": \"Dátum a čas\",\n  \"attribute.date.description\": \"Dialóg pre výber dátumu a času\",\n  \"attribute.datetime\": \"Dátum a čas\",\n  \"attribute.dynamiczone\": \"Dynamická zóna\",\n  \"attribute.dynamiczone.description\": \"Umožňuje dynamicky zvoliť komponenty počas úpravy obsahu\",\n  \"attribute.email\": \"E-mailová adresa\",\n  \"attribute.email.description\": \"Políčko s automatickou validáciou formátu e-mailovej adresy\",\n  \"attribute.enumeration\": \"Vymenovanie\",\n  \"attribute.enumeration.description\": \"Zoznam preddefinovan<PERSON>ch hodnôt s výberom jednej možnosti\",\n  \"attribute.json\": \"JSON\",\n  \"attribute.json.description\": \"Dáta vo formáte JSON\",\n  \"attribute.media\": \"Súbory\",\n  \"attribute.media.description\": \"Napr. obrázky, videá, atď.\",\n  \"attribute.null\": \" \",\n  \"attribute.number\": \"Číslo\",\n  \"attribute.number.description\": \"Čísla (celé, desatinné)\",\n  \"attribute.password\": \"Heslo\",\n  \"attribute.password.description\": \"Políčko pre zadanie hesla\",\n  \"attribute.relation\": \"Relácia\",\n  \"attribute.relation.description\": \"Určuje vzťah k inému Typu obsahu\",\n  \"attribute.richtext\": \"Textový editor\",\n  \"attribute.richtext.description\": \"Textové pole s možnosťami formátovania\",\n  \"attribute.text\": \"Text\",\n  \"attribute.text.description\": \"Krátky alebo dlhší text\",\n  \"attribute.time\": \"Čas\",\n  \"attribute.timestamp\": \"Timestamp - Časový odtlačok\",\n  \"attribute.uid\": \"UID\",\n  \"attribute.uid.description\": \"Unikátny identifikátor\",\n  \"button.attributes.add.another\": \"Pridať ďalšie políčko\",\n  \"button.component.add\": \"Pridať komponent\",\n  \"button.component.create\": \"Vytvoriť nový komponent\",\n  \"button.model.create\": \"Vytvoriť nový Typ obsahu\",\n  \"button.single-types.create\": \"Vytvoriť nový jednorazový typ\",\n  \"component.repeatable\": \"(opakujúce)\",\n  \"components.componentSelect.no-component-available\": \"Už ste pridali všetky komponenty\",\n  \"components.componentSelect.no-component-available.with-search\": \"Nenašiel sa žiaden komponent splňujúci výraz\",\n  \"components.componentSelect.value-component\": \"Označené komponenty: {number} (zadajte hľadaný text)\",\n  \"components.componentSelect.value-components\": \"Označené komponenty: {number}\",\n  configurations,\n  \"contentType.collectionName.description\": \"Užitočné, ak má byť názov kolekcie (v dashboard) iný ako meno tabuľky\",\n  \"contentType.collectionName.label\": \"Meno kolekcie\",\n  \"contentType.displayName.label\": \"Názov kolekcie\",\n  \"contentType.kind.change.warning\": \"Práve ste zmenili druh typu obsahu: API bude obnovené (routes, controllers a services budú prepísané)\",\n  \"error.attributeName.reserved-name\": \"Tento názov je vyhradený a nemôže byť použitý (hrozí poškodenie iných funkcionalít systému)\",\n  \"error.contentTypeName.reserved-name\": \"Tento názov je vyhradený a nemôže byť použitý (hrozí poškodenie iných funkcionalít systému)\",\n  \"error.validation.enum-duplicate\": \"Duplicitné hodnoty nie sú povolené\",\n  \"error.validation.minSupMax\": \"Nemôže byť nadradený\",\n  \"error.validation.regex\": \"Vzor regulárneho výrazu (regex) je neplatný\",\n  \"error.validation.relation.targetAttribute-taken\": \"Tento názov už v cieľovom objekte existuje\",\n  \"form.attribute.component.option.add\": \"Pridať komponent\",\n  \"form.attribute.component.option.create\": \"Vytvoriť nový komponent\",\n  \"form.attribute.component.option.create.description\": \"Komponent je dostupný medzi všetkými typmi a komponentami.\",\n  \"form.attribute.component.option.repeatable\": \"Znovu použiteľný komponent\",\n  \"form.attribute.component.option.repeatable.description\": \"Ideálne pre viacpoložkové (polia) inštancie ako napríklad meta tagy, ingrediencie, atď..\",\n  \"form.attribute.component.option.reuse-existing\": \"Použiť existujúci komponent\",\n  \"form.attribute.component.option.reuse-existing.description\": \"Používajte už vytvorené komponenty pre uchovanie konzistentných dát medzi Typmi obsahu.\",\n  \"form.attribute.component.option.single\": \"Jednorazový komponent\",\n  \"form.attribute.component.option.single.description\": \"Vhodné pre zoskúpenie políčok, napr. celá adresa\",\n  \"form.attribute.item.customColumnName\": \"Vlastné názvy stĺpcov\",\n  \"form.attribute.item.customColumnName.description\": \"Umožňuje premenovať databázový stĺpček pre potreby API\",\n  \"form.attribute.item.defineRelation.fieldName\": \"Názov políčka\",\n  \"form.attribute.item.enumeration.graphql\": \"Názov políčka pre GraphQL\",\n  \"form.attribute.item.enumeration.graphql.description\": \"Umožňuje prepísať predvolené názvy pre GraphQL\",\n  \"form.attribute.item.enumeration.placeholder\": \"Napr.:\\nráno\\ndeň\\nvečer\",\n  \"form.attribute.item.enumeration.rules\": \"Hodnoty (jedna na riadok)\",\n  \"form.attribute.item.maximum\": \"Maximálna hodnota\",\n  \"form.attribute.item.maximumLength\": \"Maximálna dĺžka\",\n  \"form.attribute.item.minimum\": \"Minimálna hodnota\",\n  \"form.attribute.item.minimumLength\": \"Minimálna dĺžka\",\n  \"form.attribute.item.number.type\": \"Číselný formát\",\n  \"form.attribute.item.number.type.biginteger\": \"veľké číslo (napr.: 123456789)\",\n  \"form.attribute.item.number.type.decimal\": \"desatinné číslo (napr.: 2.22)\",\n  \"form.attribute.item.number.type.float\": \"desatinné číslo (napr.: 3.33333333)\",\n  \"form.attribute.item.number.type.integer\": \"celé číslo (napr.: 10)\",\n  \"form.attribute.item.privateField\": \"Skryté políčko\",\n  \"form.attribute.item.privateField.description\": \"Toto políčko sa nebude zobrazovať v API\",\n  \"form.attribute.item.requiredField\": \"Povinné políčko\",\n  \"form.attribute.item.requiredField.description\": \"Nedovolí vytvoriť záznam ak ostane toto políčko prázdne\",\n  \"form.attribute.item.text.regex\": \"Vzor regulárneho výrazu (RegExp)\",\n  \"form.attribute.item.text.regex.description\": \"Text regulárneho výrazu\",\n  \"form.attribute.item.uniqueField\": \"Unikátne políčko\",\n  \"form.attribute.item.uniqueField.description\": \"Nedovolí vytvoriť záznam ak už existuje iný záznam s rovnakou hodnotou\",\n  \"form.attribute.media.allowed-types\": \"Zvoliť povolené typy súborov\",\n  \"form.attribute.media.allowed-types.option-files\": \"Súbory\",\n  \"form.attribute.media.allowed-types.option-images\": \"Obrázky\",\n  \"form.attribute.media.allowed-types.option-videos\": \"Videá\",\n  \"form.attribute.media.option.multiple\": \"Viacero súborov\",\n  \"form.attribute.media.option.multiple.description\": \"Vhodné pre galériu, zoznam súborov na stiahnutie\",\n  \"form.attribute.media.option.single\": \"Jeden súbor\",\n  \"form.attribute.media.option.single.description\": \"Vhodné pre profilovú fotku alebo hlavný obrázok\",\n  \"form.attribute.settings.default\": \"Predvolená hodnota\",\n  \"form.attribute.text.option.long-text\": \"Dlhý text\",\n  \"form.attribute.text.option.long-text.description\": \"Vhodné pre dlhšie popisy. Presné vyhľadávanie je vypnuté.\",\n  \"form.attribute.text.option.short-text\": \"Krátky text\",\n  \"form.attribute.text.option.short-text.description\": \"Vhodné pre nadpisy, názvy, URL adresy. Presné vyhľadávanie je zapnuté.\",\n  \"form.button.add-components-to-dynamiczone\": \"Pridať komponenty do zóny\",\n  \"form.button.add-field\": \"Pridať ďalšie políčko\",\n  \"form.button.add-first-field-to-created-component\": \"Pridať prvé políčko do komponentu\",\n  \"form.button.add.field.to.collectionType\": \"Pridať ďalšie políčko do tejto kolekcie\",\n  \"form.button.add.field.to.component\": \"Pridať ďalšie políčko do komponentu\",\n  \"form.button.add.field.to.contentType\": \"Pridať ďalšie políčko do tohto typu obsahu\",\n  \"form.button.add.field.to.singleType\": \"Pridať ďalšie políčko do tohto jednorazového typu\",\n  \"form.button.cancel\": \"Zrušiť\",\n  \"form.button.collection-type.description\": \"Ideálne pre viacnásobné inštancie ako sú napríklad články, produkty, komentáre, atď.\",\n  \"form.button.configure-component\": \"Nastaviť komponent\",\n  \"form.button.configure-view\": \"Upraviť vzhľad\",\n  \"form.button.select-component\": \"Vybrať komponent\",\n  \"form.button.single-type.description\": \"Ideálne pre jednorazové inštancie ako sú napríklad domovská stránka, atď.\",\n  from,\n  \"modalForm.attribute.form.base.name.description\": \"Medzery nie sú povolené v názve políčka\",\n  \"modalForm.attribute.form.base.name.placeholder\": \"napr. slug, seoUrl, kanonickáUrl\",\n  \"modalForm.attribute.target-field\": \"Priložené políčko\",\n  \"modalForm.attributes.select-component\": \"Vyberte komponent\",\n  \"modalForm.attributes.select-components\": \"Vyberte komponenty\",\n  \"modalForm.component.header-create\": \"Vytvorte komponent\",\n  \"modalForm.components.create-component.category.label\": \"Vyberte kategóriu alebo zadajte názov pre vytvorenie novej\",\n  \"modalForm.components.icon.label\": \"Ikona\",\n  \"modalForm.editCategory.base.name.description\": \"Medzery nie sú povolené v názve kategórie\",\n  \"modalForm.header-edit\": \"Upraviť {name}\",\n  \"modalForm.header.categories\": \"Kategórie\",\n  \"modalForm.header.back\": \"späť\",\n  \"modalForm.singleType.header-create\": \"Vytvoriť jednorazový typ\",\n  \"modalForm.sub-header.addComponentToDynamicZone\": \"Pridať nový komponent do dynamickej zóny\",\n  \"modalForm.sub-header.attribute.create\": \"Pridať nové políčko {type}\",\n  \"modalForm.sub-header.attribute.create.step\": \"Pridať nový komponent ({step}/2)\",\n  \"modalForm.sub-header.attribute.edit\": \"Upraviť {name}\",\n  \"modalForm.sub-header.chooseAttribute.collectionType\": \"Vyberte typ políčka pre Typ obsahu\",\n  \"modalForm.sub-header.chooseAttribute.component\": \"Vyberte typ políčka pre komponent\",\n  \"modalForm.sub-header.chooseAttribute.singleType\": \"Vyberte typ políčka pre jednorazový typ\",\n  \"modelPage.attribute.relation-polymorphic\": \"Prepojenie (polymorfné)\",\n  \"modelPage.attribute.relationWith\": \"Prepojenie s\",\n  \"notification.info.autoreaload-disable\": \"Funkcionalita AutoReload je povinná pre použitie tohto pluginu. Spustite Váš server pomocou príkazu `strapi develop`\",\n  \"notification.info.creating.notSaved\": \"Uložte zmeny pred vytvorením nového Typu obsahu alebo komponentu\",\n  \"plugin.description.long\": \"Navrhnite štruktúru webu jednoducho. Vytvorte nové políčka a prepojenia behom pár sekúnd. Súbory sa automaticky vytvoria a upravia v rámci projektu.\",\n  \"plugin.description.short\": \"Navrhnite štruktúru webu jednoducho.\",\n  \"popUpForm.navContainer.advanced\": \"Pokročilé nastavenia\",\n  \"popUpForm.navContainer.base\": \"Základné nastavenia\",\n  \"popUpWarning.bodyMessage.cancel-modifications\": \"Ste si istý, že chcete zrušiť úpravy?\",\n  \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Ste si istý, že chcete zrušiť úpravy? Niektoré komponenty boli vytvorené alebo upravené...\",\n  \"popUpWarning.bodyMessage.category.delete\": \"Ste si istý, že chcete odstrániť túto kategóriu? Všetky komponentu budú takisto vymazané.\",\n  \"popUpWarning.bodyMessage.component.delete\": \"Ste si istý, že chcete odstrániť tento komponent?\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"Ste si istý, že chcete odstrániť tento Typ obsahu?\",\n  \"popUpWarning.draft-publish.button.confirm\": \"Áno, deaktivovať\",\n  \"popUpWarning.draft-publish.message\": \"Ak deaktivujete Draft/Publish systém, všetky Vaše návrhy (drafts) budú zmazané\",\n  \"popUpWarning.draft-publish.second-message\": \"Ste si istý, že to chcete deaktivovať?\",\n  \"prompt.unsaved\": \"Ste si istý, že chcete odísť? Všetky úpravy budú stratené.\",\n  \"relation.attributeName.placeholder\": \"Napr: autor, kategoria, tag\",\n  \"relation.manyToMany\": \"má viacero a patrí viacerým\",\n  \"relation.manyToOne\": \"má viacero\",\n  \"relation.manyWay\": \"má viacero\",\n  \"relation.oneToMany\": \"patrí viacerým\",\n  \"relation.oneToOne\": \"má jeden a patrí jednému\",\n  \"relation.oneWay\": \"má jeden\"\n};\nexport {\n  configurations,\n  sk as default,\n  from\n};\n//# sourceMappingURL=sk-DVK4HfSC.mjs.map\n"], "mappings": ";;;AAAA,IAAM,iBAAiB;AACvB,IAAM,OAAO;AACb,IAAM,KAAK;AAAA,EACT,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACrB;", "names": []}