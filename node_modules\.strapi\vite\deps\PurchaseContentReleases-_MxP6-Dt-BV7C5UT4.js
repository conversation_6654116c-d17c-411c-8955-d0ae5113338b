import {
  useIntl
} from "./chunk-DC3UNANX.js";
import "./chunk-RPX6VIML.js";
import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$H
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  EmptyStateLayout,
  LinkButton,
  Main
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$3t
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-releases/dist/_chunks/PurchaseContentReleases-_MxP6-Dt.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PurchaseContentReleases = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Layouts.Root, { children: (0, import_jsx_runtime.jsxs)(Main, { children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: "content-releases.pages.Releases.title",
          defaultMessage: "Releases"
        }),
        subtitle: formatMessage({
          id: "content-releases.pages.PurchaseRelease.subTitle",
          defaultMessage: "Manage content updates and releases."
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 10, paddingRight: 10, children: (0, import_jsx_runtime.jsx)(
      EmptyStateLayout,
      {
        icon: (0, import_jsx_runtime.jsx)(ForwardRef$H, { width: "16rem" }),
        content: formatMessage({
          id: "content-releases.pages.PurchaseRelease.not-available",
          defaultMessage: "Releases is only available as part of a paid plan. Upgrade to create and manage releases."
        }),
        action: (0, import_jsx_runtime.jsx)(
          LinkButton,
          {
            variant: "default",
            endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3t, {}),
            href: "https://strapi.io/pricing-self-hosted?utm_campaign=Growth-Experiments&utm_source=In-Product&utm_medium=Releases",
            isExternal: true,
            target: "_blank",
            children: formatMessage({
              id: "global.learn-more",
              defaultMessage: "Learn more"
            })
          }
        )
      }
    ) })
  ] }) });
};
export {
  PurchaseContentReleases
};
//# sourceMappingURL=PurchaseContentReleases-_MxP6-Dt-BV7C5UT4.js.map
