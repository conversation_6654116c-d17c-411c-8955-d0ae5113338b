import { NumberFormatDigitOptions, NumberFormatNotation, NumberFormatDigitInternalSlots } from '../types/number';
/**
 * https://tc39.es/ecma402/#sec-setnfdigitoptions
 */
export declare function SetNumberFormatDigitOptions(internalSlots: NumberFormatDigitInternalSlots, opts: NumberFormatDigitOptions, mnfdDefault: number, mxfdDefault: number, notation: NumberFormatNotation): void;
//# sourceMappingURL=SetNumberFormatDigitOptions.d.ts.map