import {
  AssetDefinition,
  AssetGridList,
  Breadcrumbs,
  BreadcrumbsDefinition,
  EditAssetDialog,
  EditFolderDialog,
  EmptyAssets,
  FilterList,
  FilterPopover,
  FolderCard,
  FolderCardBody,
  FolderCardBodyAction,
  FolderDefinition,
  FolderGridList,
  Form,
  Formik,
  SelectTree,
  SortPicker,
  TableList,
  UploadAssetDialog,
  containsAssetFilter,
  displayedFilters,
  getFolderURL,
  getTrad,
  localStorageKeys,
  normalizeAPIError,
  pluginId,
  require_prop_types,
  useAssets,
  useBulkRemove,
  useConfig,
  useFolder,
  useFolderCard,
  useFolderStructure,
  useFolders,
  useIntl,
  useMediaLibraryPermissions,
  usePersistentState,
  useSelectionState,
  viewOptions
} from "./chunk-S4GMEU6I.js";
import "./chunk-RPX6VIML.js";
import "./chunk-VOKTPSOW.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import {
  require_isEmpty
} from "./chunk-KKUAHZGP.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  ConfirmDialog,
  Layouts,
  Pagination,
  SearchInput,
  useFetchClient,
  useMutation,
  useQueryClient
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$H,
  Page,
  useNotification,
  useQueryParams,
  useTracking
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Button,
  CheckboxImpl,
  Dialog,
  Divider,
  Field,
  Flex,
  Grid,
  IconButton,
  Link as Link2,
  Loader,
  Modal,
  Popover,
  Typography,
  VisuallyHidden,
  require_lib
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  Link,
  NavLink,
  Route,
  Routes,
  useLocation,
  useNavigate
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1d,
  ForwardRef$1r,
  ForwardRef$2j,
  ForwardRef$33,
  ForwardRef$3b,
  ForwardRef$3d,
  ForwardRef$41,
  ForwardRef$4Z,
  ForwardRef$j
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/upload/dist/_chunks/index-DCAQ4hHN.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_react = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var import_isEmpty = __toESM(require_isEmpty(), 1);
var getBreadcrumbDataML = (folder, { pathname, query }) => {
  var _a, _b;
  let data = [
    {
      id: null,
      label: { id: getTrad("plugin.name"), defaultMessage: "Media Library" },
      href: folder ? getFolderURL(pathname, query || {}) : void 0
    }
  ];
  if ((folder == null ? void 0 : folder.parent) && typeof (folder == null ? void 0 : folder.parent) !== "number" && ((_a = folder == null ? void 0 : folder.parent) == null ? void 0 : _a.parent)) {
    data.push([]);
  }
  if ((folder == null ? void 0 : folder.parent) && typeof folder.parent !== "number") {
    data.push({
      id: folder.parent.id,
      label: folder.parent.name,
      href: getFolderURL(pathname, query || {}, {
        folder: (_b = folder.parent.id) == null ? void 0 : _b.toString(),
        folderPath: folder.parent.path
      })
    });
  }
  if (folder) {
    data.push({
      id: folder.id,
      label: folder.name
    });
  }
  return data;
};
var FolderCardCheckbox = (props) => {
  const { id } = useFolderCard();
  return (0, import_jsx_runtime.jsx)(Box, { position: "relative", zIndex: 2, children: (0, import_jsx_runtime.jsx)(CheckboxImpl, { "aria-labelledby": `${id}-title`, ...props }) });
};
var BulkDeleteButton = ({ selected, onSuccess }) => {
  const { formatMessage } = useIntl();
  const { remove } = useBulkRemove();
  const handleConfirmRemove = async () => {
    await remove(selected);
    onSuccess();
  };
  return (0, import_jsx_runtime.jsxs)(Dialog.Root, { children: [
    (0, import_jsx_runtime.jsx)(Dialog.Trigger, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "danger-light", size: "S", startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$j, {}), children: formatMessage({ id: "global.delete", defaultMessage: "Delete" }) }) }),
    (0, import_jsx_runtime.jsx)(ConfirmDialog, { onConfirm: handleConfirmRemove })
  ] });
};
BulkDeleteButton.propTypes = {
  selected: import_prop_types.default.arrayOf(AssetDefinition, FolderDefinition).isRequired,
  onSuccess: import_prop_types.default.func.isRequired
};
var useBulkMove = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const queryClient = useQueryClient();
  const { post } = useFetchClient();
  const bulkMoveQuery = ({ destinationFolderId, filesAndFolders }) => {
    const payload = filesAndFolders.reduce((acc, selected) => {
      const { id, type } = selected;
      const key = type === "asset" ? "fileIds" : "folderIds";
      if (!acc[key]) {
        acc[key] = [];
      }
      acc[key].push(id);
      return acc;
    }, {});
    return post("/upload/actions/bulk-move", { ...payload, destinationFolderId });
  };
  const mutation = useMutation(bulkMoveQuery, {
    onSuccess(res) {
      var _a;
      const {
        data: { data }
      } = res;
      if (((_a = data == null ? void 0 : data.files) == null ? void 0 : _a.length) > 0) {
        queryClient.refetchQueries([pluginId, "assets"], { active: true });
        queryClient.refetchQueries([pluginId, "asset-count"], { active: true });
      }
      queryClient.refetchQueries([pluginId, "folders"], { active: true });
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTrad("modal.move.success-label"),
          defaultMessage: "Elements have been moved successfully."
        })
      });
    }
  });
  const move = (destinationFolderId, filesAndFolders) => mutation.mutateAsync({ destinationFolderId, filesAndFolders });
  return { ...mutation, move };
};
var BulkMoveDialog = ({ onClose, selected = [], currentFolder }) => {
  const { formatMessage } = useIntl();
  const { data: folderStructure, isLoading } = useFolderStructure();
  const { move } = useBulkMove();
  if (!folderStructure) {
    return null;
  }
  const handleSubmit = async (values, { setErrors }) => {
    var _a;
    try {
      if (typeof values.destination !== "string") {
        const destinationValue = values.destination.value;
        await move(destinationValue, selected);
        onClose();
      }
    } catch (error) {
      const normalizedError = normalizeAPIError(error);
      if (normalizedError && "errors" in normalizedError) {
        const formikErrors = (_a = normalizedError.errors) == null ? void 0 : _a.reduce(
          (acc, error2) => {
            var _a2, _b;
            acc[((_b = (_a2 = error2.values) == null ? void 0 : _a2.path) == null ? void 0 : _b.length) || "destination"] = error2.defaultMessage;
            return acc;
          },
          {}
        );
        if (!(0, import_isEmpty.default)(formikErrors)) {
          setErrors(formikErrors);
        }
      }
    }
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Modal.Content, { children: (0, import_jsx_runtime.jsx)(Modal.Body, { children: (0, import_jsx_runtime.jsx)(Flex, { justifyContent: "center", paddingTop: 4, paddingBottom: 4, children: (0, import_jsx_runtime.jsx)(Loader, { children: formatMessage({
      id: getTrad("content.isLoading"),
      defaultMessage: "Content is loading."
    }) }) }) }) });
  }
  const initialFormData = {
    destination: {
      value: (currentFolder == null ? void 0 : currentFolder.id) || "",
      label: (currentFolder == null ? void 0 : currentFolder.name) || folderStructure[0].label
    }
  };
  return (0, import_jsx_runtime.jsx)(Modal.Content, { children: (0, import_jsx_runtime.jsx)(Formik, { validateOnChange: false, onSubmit: handleSubmit, initialValues: initialFormData, children: ({ values, errors, setFieldValue }) => (0, import_jsx_runtime.jsxs)(Form, { noValidate: true, children: [
    (0, import_jsx_runtime.jsx)(Modal.Header, { children: (0, import_jsx_runtime.jsx)(Modal.Title, { children: formatMessage({
      id: getTrad("modal.folder.move.title"),
      defaultMessage: "Move elements to"
    }) }) }),
    (0, import_jsx_runtime.jsx)(Modal.Body, { children: (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 4, children: (0, import_jsx_runtime.jsx)(Grid.Item, { xs: 12, col: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(Field.Root, { id: "folder-destination", children: [
      (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
        id: getTrad("form.input.label.folder-location"),
        defaultMessage: "Location"
      }) }),
      (0, import_jsx_runtime.jsx)(
        SelectTree,
        {
          options: folderStructure,
          onChange: (value) => {
            setFieldValue("destination", value);
          },
          defaultValue: typeof values.destination !== "string" ? values.destination : void 0,
          name: "destination",
          menuPortalTarget: document.querySelector("body"),
          inputId: "folder-destination",
          error: errors == null ? void 0 : errors.destination,
          ariaErrorMessage: "destination-error"
        }
      ),
      errors.destination && (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", tag: "p", textColor: "danger600", children: errors.destination })
    ] }) }) }) }),
    (0, import_jsx_runtime.jsxs)(Modal.Footer, { children: [
      (0, import_jsx_runtime.jsx)(Modal.Close, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "tertiary", name: "cancel", children: formatMessage({ id: "cancel", defaultMessage: "Cancel" }) }) }),
      (0, import_jsx_runtime.jsx)(Button, { type: "submit", loading: isLoading, children: formatMessage({ id: "modal.folder.move.submit", defaultMessage: "Move" }) })
    ] })
  ] }) }) });
};
var BulkMoveButton = ({ selected, onSuccess, currentFolder }) => {
  const { formatMessage } = useIntl();
  const [showConfirmDialog, setShowConfirmDialog] = (0, import_react.useState)(false);
  const handleConfirmMove = () => {
    setShowConfirmDialog(false);
    onSuccess();
  };
  return (0, import_jsx_runtime.jsxs)(Modal.Root, { open: showConfirmDialog, onOpenChange: setShowConfirmDialog, children: [
    (0, import_jsx_runtime.jsx)(Modal.Trigger, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", size: "S", startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3b, {}), children: formatMessage({ id: "global.move", defaultMessage: "Move" }) }) }),
    (0, import_jsx_runtime.jsx)(
      BulkMoveDialog,
      {
        currentFolder,
        onClose: handleConfirmMove,
        selected
      }
    )
  ] });
};
BulkMoveButton.defaultProps = {
  currentFolder: void 0,
  selected: []
};
BulkMoveButton.propTypes = {
  onSuccess: import_prop_types.default.func.isRequired,
  currentFolder: FolderDefinition,
  selected: import_prop_types.default.arrayOf(AssetDefinition, FolderDefinition)
};
var BulkActions = ({ selected, onSuccess, currentFolder }) => {
  const { formatMessage } = useIntl();
  const numberAssets = selected.reduce(function(_this, val) {
    return (val == null ? void 0 : val.type) === "folder" ? _this + val.files.count : _this + 1;
  }, 0);
  return (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, paddingBottom: 5, children: [
    (0, import_jsx_runtime.jsx)(Typography, { variant: "epsilon", textColor: "neutral600", children: formatMessage(
      {
        id: getTrad("list.assets.selected"),
        defaultMessage: "{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 asset} other {# assets}} selected"
      },
      {
        numberFolders: selected.filter(({ type }) => type === "folder").length,
        numberAssets
      }
    ) }),
    (0, import_jsx_runtime.jsx)(BulkDeleteButton, { selected, onSuccess }),
    (0, import_jsx_runtime.jsx)(BulkMoveButton, { currentFolder, selected, onSuccess })
  ] });
};
BulkActions.defaultProps = {
  currentFolder: void 0,
  selected: []
};
BulkActions.propTypes = {
  onSuccess: import_prop_types.default.func.isRequired,
  currentFolder: FolderDefinition,
  selected: import_prop_types.default.arrayOf(AssetDefinition, FolderDefinition)
};
var getContentIntlMessage = ({ isFiltering, canCreate, canRead }) => {
  if (isFiltering) {
    return {
      id: "list.assets-empty.title-withSearch",
      defaultMessage: "There are no elements with the applied filters"
    };
  }
  if (canRead) {
    if (canCreate) {
      return {
        id: "list.assets.empty-upload",
        defaultMessage: "Upload your first assets..."
      };
    }
    return {
      id: "list.assets.empty",
      defaultMessage: "Media Library is empty"
    };
  }
  return {
    id: "header.actions.no-permissions",
    defaultMessage: "No permissions to view"
  };
};
var EmptyOrNoPermissions = ({ canCreate, isFiltering, canRead, onActionClick }) => {
  const { formatMessage } = useIntl();
  const content = getContentIntlMessage({ isFiltering, canCreate, canRead });
  return (0, import_jsx_runtime.jsx)(
    EmptyAssets,
    {
      icon: !canRead ? ForwardRef$H : void 0,
      action: canCreate && !isFiltering && (0, import_jsx_runtime.jsx)(Button, { variant: "secondary", startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}), onClick: onActionClick, children: formatMessage({
        id: getTrad("header.actions.add-assets"),
        defaultMessage: "Add new assets"
      }) }),
      content: formatMessage({
        ...content,
        id: getTrad(content.id)
      })
    }
  );
};
EmptyOrNoPermissions.propTypes = {
  canCreate: import_prop_types.default.bool.isRequired,
  canRead: import_prop_types.default.bool.isRequired,
  isFiltering: import_prop_types.default.bool.isRequired,
  onActionClick: import_prop_types.default.func.isRequired
};
var Filters = () => {
  var _a;
  const [open, setOpen] = React.useState(false);
  const { formatMessage } = useIntl();
  const { trackUsage } = useTracking();
  const [{ query }, setQuery] = useQueryParams();
  const filters = ((_a = query == null ? void 0 : query.filters) == null ? void 0 : _a.$and) || [];
  const handleRemoveFilter = (nextFilters) => {
    setQuery({ filters: { $and: nextFilters }, page: 1 });
  };
  const handleSubmit = (filters2) => {
    trackUsage("didFilterMediaLibraryElements", {
      location: "content-manager",
      filter: Object.keys(filters2[filters2.length - 1])[0]
    });
    setQuery({ filters: { $and: filters2 }, page: 1 });
  };
  return (0, import_jsx_runtime.jsxs)(Popover.Root, { open, onOpenChange: setOpen, children: [
    (0, import_jsx_runtime.jsx)(Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "tertiary", startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3d, {}), size: "S", children: formatMessage({ id: "app.utils.filters", defaultMessage: "Filters" }) }) }),
    (0, import_jsx_runtime.jsx)(
      FilterPopover,
      {
        displayedFilters,
        filters,
        onSubmit: handleSubmit,
        onToggle: setOpen
      }
    ),
    (0, import_jsx_runtime.jsx)(
      FilterList,
      {
        appliedFilters: filters,
        filtersSchema: displayedFilters,
        onRemoveFilter: handleRemoveFilter
      }
    )
  ] });
};
var Header = ({
  breadcrumbs,
  canCreate,
  folder,
  onToggleEditFolderDialog,
  onToggleUploadAssetDialog
}) => {
  var _a, _b;
  const { formatMessage } = useIntl();
  const { pathname } = useLocation();
  const [{ query }] = useQueryParams();
  const backQuery = {
    ...query,
    folder: ((_a = folder == null ? void 0 : folder.parent) == null ? void 0 : _a.id) ?? void 0,
    folderPath: ((_b = folder == null ? void 0 : folder.parent) == null ? void 0 : _b.path) ?? void 0
  };
  return (0, import_jsx_runtime.jsx)(
    Layouts.Header,
    {
      title: formatMessage({
        id: getTrad("plugin.name"),
        defaultMessage: `Media Library`
      }),
      subtitle: breadcrumbs && folder && (0, import_jsx_runtime.jsx)(
        Breadcrumbs,
        {
          tag: "nav",
          label: formatMessage({
            id: getTrad("header.breadcrumbs.nav.label"),
            defaultMessage: "Folders navigation"
          }),
          breadcrumbs,
          currentFolderId: folder == null ? void 0 : folder.id
        }
      ),
      navigationAction: folder && (0, import_jsx_runtime.jsx)(
        Link2,
        {
          tag: NavLink,
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4Z, {}),
          to: `${pathname}?${(0, import_qs.stringify)(backQuery, { encode: false })}`,
          children: formatMessage({
            id: getTrad("header.actions.folder-level-up"),
            defaultMessage: "Back"
          })
        }
      ),
      primaryAction: canCreate && (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, children: [
        (0, import_jsx_runtime.jsx)(Button, { startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}), variant: "secondary", onClick: onToggleEditFolderDialog, children: formatMessage({
          id: getTrad("header.actions.add-folder"),
          defaultMessage: "Add new folder"
        }) }),
        (0, import_jsx_runtime.jsx)(Button, { startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}), onClick: onToggleUploadAssetDialog, children: formatMessage({
          id: getTrad("header.actions.add-assets"),
          defaultMessage: "Add new assets"
        }) })
      ] })
    }
  );
};
Header.defaultProps = {
  breadcrumbs: false,
  folder: null
};
Header.propTypes = {
  breadcrumbs: import_prop_types.default.oneOfType([BreadcrumbsDefinition, import_prop_types.default.bool]),
  canCreate: import_prop_types.default.bool.isRequired,
  folder: FolderDefinition,
  onToggleEditFolderDialog: import_prop_types.default.func.isRequired,
  onToggleUploadAssetDialog: import_prop_types.default.func.isRequired
};
var BoxWithHeight = dt(Box)`
  height: 3.2rem;
  display: flex;
  align-items: center;
`;
var TypographyMaxWidth = dt(Typography)`
  max-width: 100%;
`;
var ActionContainer = dt(Box)`
  svg {
    path {
      fill: ${({ theme }) => theme.colors.neutral500};
    }
  }
`;
var MediaLibrary = () => {
  var _a, _b, _c, _d, _e;
  const navigate = useNavigate();
  const {
    canRead,
    canCreate,
    canUpdate,
    canCopyLink,
    canDownload,
    canConfigureView,
    isLoading: permissionsLoading
  } = useMediaLibraryPermissions();
  const currentFolderToEditRef = (0, import_react.useRef)();
  const { formatMessage } = useIntl();
  const { pathname } = useLocation();
  const { trackUsage } = useTracking();
  const [{ query }, setQuery] = useQueryParams();
  const isFiltering = Boolean(query._q || query.filters);
  const [view, setView] = usePersistentState(localStorageKeys.view, viewOptions.GRID);
  const isGridView = view === viewOptions.GRID;
  const {
    data: assetsData,
    isLoading: assetsLoading,
    errors: assetsError
  } = useAssets({
    skipWhen: !canRead,
    query
  });
  const {
    data: foldersData,
    isLoading: foldersLoading,
    errors: foldersError
  } = useFolders({
    enabled: canRead && ((_a = assetsData == null ? void 0 : assetsData.pagination) == null ? void 0 : _a.page) === 1 && !containsAssetFilter(query),
    query
  });
  const {
    data: currentFolder,
    isLoading: isCurrentFolderLoading,
    error: currentFolderError
  } = useFolder(query == null ? void 0 : query.folder, {
    enabled: canRead && !!(query == null ? void 0 : query.folder)
  });
  if (((_b = currentFolderError == null ? void 0 : currentFolderError.response) == null ? void 0 : _b.status) === 404) {
    navigate(pathname);
  }
  const folders = (foldersData == null ? void 0 : foldersData.map((folder) => ({
    ...folder,
    type: "folder",
    folderURL: getFolderURL(pathname, query, folder.id),
    isSelectable: canUpdate
  }))) ?? [];
  const folderCount = (folders == null ? void 0 : folders.length) || 0;
  const assets = ((_c = assetsData == null ? void 0 : assetsData.results) == null ? void 0 : _c.map((asset) => ({ ...asset, type: "asset", isSelectable: canUpdate }))) || [];
  const assetCount = (assets == null ? void 0 : assets.length) ?? 0;
  const totalAssetCount = (_d = assetsData == null ? void 0 : assetsData.pagination) == null ? void 0 : _d.total;
  const isLoading = isCurrentFolderLoading || foldersLoading || permissionsLoading || assetsLoading;
  const [showUploadAssetDialog, setShowUploadAssetDialog] = (0, import_react.useState)(false);
  const [showEditFolderDialog, setShowEditFolderDialog] = (0, import_react.useState)(false);
  const [assetToEdit, setAssetToEdit] = (0, import_react.useState)(void 0);
  const [folderToEdit, setFolderToEdit] = (0, import_react.useState)(void 0);
  const [selected, { selectOne, selectAll }] = useSelectionState(["type", "id"], []);
  const indeterminateBulkSelect = (selected == null ? void 0 : selected.length) > 0 && (selected == null ? void 0 : selected.length) !== assetCount + folderCount;
  const toggleUploadAssetDialog = () => setShowUploadAssetDialog((prev) => !prev);
  const toggleEditFolderDialog = ({ created = false } = {}) => {
    if (created && (query == null ? void 0 : query.page) !== "1") {
      setQuery({
        ...query,
        page: 1
      });
    }
    setShowEditFolderDialog((prev) => !prev);
  };
  const handleBulkSelect = (checked, elements) => {
    if (checked) {
      trackUsage("didSelectAllMediaLibraryElements");
    }
    selectAll(elements);
  };
  const handleChangeSort = (value) => {
    trackUsage("didSortMediaLibraryElements", {
      location: "upload",
      sort: value
    });
    setQuery({ sort: value });
  };
  const handleEditFolder = (folder) => {
    setFolderToEdit(folder);
    setShowEditFolderDialog(true);
  };
  const handleEditFolderClose = (payload) => {
    setFolderToEdit(null);
    toggleEditFolderDialog(payload);
    if (currentFolderToEditRef.current) {
      currentFolderToEditRef.current.focus();
    }
  };
  const handleAssetDeleted = (numberOfAssets) => {
    if (numberOfAssets === assetCount && assetsData.pagination.page === assetsData.pagination.pageCount && assetsData.pagination.page > 1) {
      setQuery({
        ...query,
        page: assetsData.pagination.page - 1
      });
    }
  };
  const handleBulkActionSuccess = () => {
    selectAll();
    handleAssetDeleted(selected.length);
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  if (assetsError || foldersError) {
    return (0, import_jsx_runtime.jsx)(Page.Error, {});
  }
  return (0, import_jsx_runtime.jsxs)(Layouts.Root, { children: [
    (0, import_jsx_runtime.jsxs)(Page.Main, { children: [
      (0, import_jsx_runtime.jsx)(
        Header,
        {
          breadcrumbs: !isCurrentFolderLoading && getBreadcrumbDataML(currentFolder, { pathname, query }),
          canCreate,
          onToggleEditFolderDialog: toggleEditFolderDialog,
          onToggleUploadAssetDialog: toggleUploadAssetDialog,
          folder: currentFolder
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Layouts.Action,
        {
          startActions: (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
            canUpdate && isGridView && (assetCount > 0 || folderCount > 0) && (0, import_jsx_runtime.jsx)(
              BoxWithHeight,
              {
                paddingLeft: 2,
                paddingRight: 2,
                background: "neutral0",
                hasRadius: true,
                borderColor: "neutral200",
                children: (0, import_jsx_runtime.jsx)(
                  CheckboxImpl,
                  {
                    "aria-label": formatMessage({
                      id: getTrad("bulk.select.label"),
                      defaultMessage: "Select all folders & assets"
                    }),
                    checked: indeterminateBulkSelect ? "indeterminate" : (assetCount > 0 || folderCount > 0) && selected.length === assetCount + folderCount,
                    onCheckedChange: (e) => handleBulkSelect(e, [...assets, ...folders])
                  }
                )
              }
            ),
            canRead && isGridView && (0, import_jsx_runtime.jsx)(SortPicker, { value: query == null ? void 0 : query.sort, onChangeSort: handleChangeSort }),
            canRead && (0, import_jsx_runtime.jsx)(Filters, {})
          ] }),
          endActions: (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
            canConfigureView ? (0, import_jsx_runtime.jsx)(ActionContainer, { paddingTop: 1, paddingBottom: 1, children: (0, import_jsx_runtime.jsx)(
              IconButton,
              {
                tag: Link,
                to: {
                  pathname: `${pathname}/configuration`,
                  search: (0, import_qs.stringify)(query, { encode: false })
                },
                label: formatMessage({
                  id: "app.links.configure-view",
                  defaultMessage: "Configure the view"
                }),
                children: (0, import_jsx_runtime.jsx)(ForwardRef$41, {})
              }
            ) }) : null,
            (0, import_jsx_runtime.jsx)(ActionContainer, { paddingTop: 1, paddingBottom: 1, children: (0, import_jsx_runtime.jsx)(
              IconButton,
              {
                label: isGridView ? formatMessage({
                  id: getTrad("view-switch.list"),
                  defaultMessage: "List View"
                }) : formatMessage({
                  id: getTrad("view-switch.grid"),
                  defaultMessage: "Grid View"
                }),
                onClick: () => setView(isGridView ? viewOptions.LIST : viewOptions.GRID),
                children: isGridView ? (0, import_jsx_runtime.jsx)(ForwardRef$2j, {}) : (0, import_jsx_runtime.jsx)(ForwardRef$33, {})
              }
            ) }),
            (0, import_jsx_runtime.jsx)(
              SearchInput,
              {
                label: formatMessage({
                  id: getTrad("search.label"),
                  defaultMessage: "Search for an asset"
                }),
                trackedEvent: "didSearchMediaLibraryElements",
                trackedEventDetails: { location: "upload" }
              }
            )
          ] })
        }
      ),
      (0, import_jsx_runtime.jsxs)(Layouts.Content, { children: [
        selected.length > 0 && (0, import_jsx_runtime.jsx)(
          BulkActions,
          {
            currentFolder,
            selected,
            onSuccess: handleBulkActionSuccess
          }
        ),
        folderCount === 0 && assetCount === 0 && (0, import_jsx_runtime.jsx)(
          EmptyOrNoPermissions,
          {
            canCreate,
            canRead,
            isFiltering,
            onActionClick: toggleUploadAssetDialog
          }
        ),
        canRead && !isGridView && (assetCount > 0 || folderCount > 0) && (0, import_jsx_runtime.jsx)(
          TableList,
          {
            assetCount,
            folderCount,
            indeterminate: indeterminateBulkSelect,
            onChangeSort: handleChangeSort,
            onChangeFolder: (folderID, folderPath) => navigate(getFolderURL(pathname, query, { folder: folderID, folderPath })),
            onEditAsset: setAssetToEdit,
            onEditFolder: handleEditFolder,
            onSelectOne: selectOne,
            onSelectAll: handleBulkSelect,
            rows: [...folders, ...assets],
            selected,
            shouldDisableBulkSelect: !canUpdate,
            sortQuery: (query == null ? void 0 : query.sort) ?? ""
          }
        ),
        canRead && isGridView && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
          folderCount > 0 && (0, import_jsx_runtime.jsx)(
            FolderGridList,
            {
              title: (
                // Folders title should only appear if:
                // user is filtering and there are assets to display, to divide both type of elements
                // user is not filtering
                (isFiltering && assetCount > 0 || !isFiltering) && formatMessage(
                  {
                    id: getTrad("list.folders.title"),
                    defaultMessage: "Folders ({count})"
                  },
                  { count: folderCount }
                ) || ""
              ),
              children: folders.map((folder) => {
                const selectedFolders = selected.filter(({ type }) => type === "folder");
                const isSelected = !!selectedFolders.find(
                  (currentFolder2) => currentFolder2.id === folder.id
                );
                const url = getFolderURL(pathname, query, {
                  folder: folder == null ? void 0 : folder.id,
                  folderPath: folder == null ? void 0 : folder.path
                });
                return (0, import_jsx_runtime.jsx)(
                  Grid.Item,
                  {
                    col: 3,
                    direction: "column",
                    alignItems: "stretch",
                    children: (0, import_jsx_runtime.jsx)(
                      FolderCard,
                      {
                        ref: folderToEdit && folder.id === folderToEdit.id ? currentFolderToEditRef : void 0,
                        ariaLabel: folder.name,
                        id: `folder-${folder.id}`,
                        to: url,
                        startAction: selectOne && folder.isSelectable ? (0, import_jsx_runtime.jsx)(
                          FolderCardCheckbox,
                          {
                            "data-testid": `folder-checkbox-${folder.id}`,
                            checked: isSelected,
                            onCheckedChange: () => selectOne(folder)
                          }
                        ) : null,
                        cardActions: (0, import_jsx_runtime.jsx)(
                          IconButton,
                          {
                            "aria-label": formatMessage({
                              id: getTrad("list.folder.edit"),
                              defaultMessage: "Edit folder"
                            }),
                            onClick: () => handleEditFolder(folder),
                            children: (0, import_jsx_runtime.jsx)(ForwardRef$1r, {})
                          }
                        ),
                        children: (0, import_jsx_runtime.jsx)(FolderCardBody, { children: (0, import_jsx_runtime.jsx)(FolderCardBodyAction, { to: url, children: (0, import_jsx_runtime.jsxs)(Flex, { tag: "h2", direction: "column", alignItems: "start", maxWidth: "100%", children: [
                          (0, import_jsx_runtime.jsxs)(
                            TypographyMaxWidth,
                            {
                              fontWeight: "semiBold",
                              textColor: "neutral800",
                              ellipsis: true,
                              children: [
                                folder.name,
                                (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: ":" })
                              ]
                            }
                          ),
                          (0, import_jsx_runtime.jsx)(
                            TypographyMaxWidth,
                            {
                              tag: "span",
                              textColor: "neutral600",
                              variant: "pi",
                              ellipsis: true,
                              children: formatMessage(
                                {
                                  id: getTrad("list.folder.subtitle"),
                                  defaultMessage: "{folderCount, plural, =0 {# folder} one {# folder} other {# folders}}, {filesCount, plural, =0 {# asset} one {# asset} other {# assets}}"
                                },
                                {
                                  folderCount: folder.children.count,
                                  filesCount: folder.files.count
                                }
                              )
                            }
                          )
                        ] }) }) })
                      }
                    )
                  },
                  `folder-${folder.id}`
                );
              })
            }
          ),
          assetCount > 0 && folderCount > 0 && (0, import_jsx_runtime.jsx)(Box, { paddingTop: 6, paddingBottom: 4, children: (0, import_jsx_runtime.jsx)(Divider, {}) }),
          assetCount > 0 && (0, import_jsx_runtime.jsx)(
            AssetGridList,
            {
              assets,
              onEditAsset: setAssetToEdit,
              onSelectAsset: selectOne,
              selectedAssets: selected.filter(({ type }) => type === "asset"),
              title: (
                // Assets title should only appear if:
                // - user is not filtering
                // - user is filtering and there are folders to display, to separate them
                // - user is on page 1 since folders won't appear on any other page than the first one (no need to visually separate them)
                (!isFiltering || isFiltering && folderCount > 0) && ((_e = assetsData == null ? void 0 : assetsData.pagination) == null ? void 0 : _e.page) === 1 && formatMessage(
                  {
                    id: getTrad("list.assets.title"),
                    defaultMessage: "Assets ({count})"
                  },
                  { count: totalAssetCount }
                ) || ""
              )
            }
          )
        ] }),
        (0, import_jsx_runtime.jsxs)(Pagination.Root, { ...assetsData.pagination, children: [
          (0, import_jsx_runtime.jsx)(Pagination.PageSize, {}),
          (0, import_jsx_runtime.jsx)(Pagination.Links, {})
        ] })
      ] })
    ] }),
    showUploadAssetDialog && (0, import_jsx_runtime.jsx)(
      UploadAssetDialog,
      {
        open: showUploadAssetDialog,
        onClose: toggleUploadAssetDialog,
        trackedLocation: "upload",
        folderId: query == null ? void 0 : query.folder
      }
    ),
    showEditFolderDialog && (0, import_jsx_runtime.jsx)(
      EditFolderDialog,
      {
        open: showEditFolderDialog,
        onClose: handleEditFolderClose,
        folder: folderToEdit,
        parentFolderId: query == null ? void 0 : query.folder,
        location: "upload"
      }
    ),
    assetToEdit && (0, import_jsx_runtime.jsx)(
      EditAssetDialog,
      {
        onClose: (editedAsset) => {
          if (editedAsset === null) {
            handleAssetDeleted(1);
          }
          setAssetToEdit(void 0);
        },
        open: !!assetToEdit,
        asset: assetToEdit,
        canUpdate,
        canCopyLink,
        canDownload,
        trackedLocation: "upload"
      }
    )
  ] });
};
var ConfigureTheView = (0, import_react.lazy)(() => import("./index-C_cWV2yS-YJ74FYOM.js"));
var Upload = () => {
  const {
    config: { isLoading, isError, data: config }
  } = useConfig();
  const [{ rawQuery }, setQuery] = useQueryParams();
  const { formatMessage } = useIntl();
  const title = formatMessage({ id: getTrad("plugin.name"), defaultMessage: "Media Library" });
  (0, import_react.useEffect)(() => {
    if (isLoading || isError || rawQuery) {
      return;
    }
    setQuery({ sort: config.sort, page: 1, pageSize: config.pageSize });
  }, [isLoading, isError, config, rawQuery, setQuery]);
  if (isLoading) {
    return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
      (0, import_jsx_runtime.jsx)(Page.Title, { children: title }),
      (0, import_jsx_runtime.jsx)(Page.Loading, {})
    ] });
  }
  return (0, import_jsx_runtime.jsx)(Page.Main, { children: rawQuery ? (0, import_jsx_runtime.jsx)(import_react.Suspense, { fallback: (0, import_jsx_runtime.jsx)(Page.Loading, {}), children: (0, import_jsx_runtime.jsxs)(Routes, { children: [
    (0, import_jsx_runtime.jsx)(Route, { index: true, element: (0, import_jsx_runtime.jsx)(MediaLibrary, {}) }),
    (0, import_jsx_runtime.jsx)(Route, { path: "configuration", element: (0, import_jsx_runtime.jsx)(ConfigureTheView, { config }) })
  ] }) }) : null });
};
export {
  Upload as default
};
//# sourceMappingURL=index-DCAQ4hHN-RJTENXS3.js.map
