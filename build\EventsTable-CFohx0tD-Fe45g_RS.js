import{m as e}from"./strapi-YzJfjJ2z.js";import{a as s}from"./EditPage-BwtMkeYt-DvpG3f6N.js";import"./selectors-ZSFBgSp8-CGZgPRVm.js";import"./useWebhooks-C2yEtiSU-DlC2WY52.js";const t={"review-workflows":{"review-workflows":["review-workflows.updateEntryStage"]},releases:{releases:["releases.publish"]}},a=r=>{switch(r){case"review-workflows":return()=>[{id:"review-workflows.updateEntryStage",defaultMessage:"Stage Change"}];case"releases":return()=>[{id:"releases.publish",defaultMessage:"Publish"}]}},n=()=>e.jsxs(s.<PERSON>,{children:[e.jsx(s.Head<PERSON>,{}),e.jsx(s.Body,{}),Object.keys(t).map(r=>e.jsxs(e.Fragment,{children:[e.jsx(s.Headers,{getHeaders:a(r)}),e.jsx(s.Body,{providedEvents:t[r]})]}))]});export{n as EventsTableEE};
