'use strict';

/**
 * @fileoverview 用户账户激活流程相关的 Controller (兼容 Strapi v5.x)
 * 包含：
 * 1. sendActivation: 发送激活邮件
 * 2. activateAccount: 使用 token 激活账户
 * * 依赖于 Strapi 的 '@strapi/plugin-users-permissions' 和 '@strapi/plugin-email'。
 * * 还需要一个自定义的 Content-Type 'activation-token' (API anme: activation-token) 来存储临时令牌。
 * 'activation-token' 应包含字段: token (Text, unique), email (Email), expiresAt (Datetime)。
 */

// 引入 Node.js 内置的加密模块，用于生成安全的随机令牌
const crypto = require('crypto');

// --- 配置常量 (最佳实践: 从根目录 .env 文件中读取) ---

// 前端激活页面的URL，{token} 会被替换为实际的令牌
// 强烈建议在 .env 文件中设置 FRONTEND_URL=http://your-frontend-domain.com
const FRONTEND_ACTIVATION_URL = process.env.FRONTEND_URL || 'http://localhost:5173/activate?token={token}';

// 激活令牌的有效期（分钟）
const TOKEN_EXPIRATION_MINUTES = 15;

// 用于基础邮箱格式验证的正则表达式
const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;

module.exports = {
  /**
   * 发送账户激活邮件
   * @param {object} ctx - Koa 上下文对象
   */
  async sendActivation(ctx) {
    // 1. 从请求体中获取并验证邮箱地址
    const { email } = ctx.request.body;
    const lowerCaseEmail = String(email || '').toLowerCase();

    if (!email || !emailRegex.test(lowerCaseEmail)) {
      return ctx.badRequest('无效的邮箱格式 (Invalid email format)');
    }

    try {
      // 2. 检查邮箱是否已经被注册
      // 在 v5 中，查询插件内容类型的方式保持不变
      const user = await strapi.query('plugin::users-permissions.user').findOne({
        where: { email: lowerCaseEmail }
      });

      if (user) {
        return ctx.badRequest('此邮箱已被注册 (Email is already registered)');
      }

      // 3. 检查是否已存在该邮箱的、未过期的激活令牌
      const activationTokenService = strapi.service('api::activation-token.activation-token');
      let existingToken = await activationTokenService.find({ // v5 中 findOne 功能被合并到 find，使用 filters
        filters: {
          email: lowerCaseEmail,
          expiresAt: { $gt: new Date() },
        },
      });
      
      // `find` 返回一个数组，即使只找到一个
      existingToken = existingToken.results[0];

      let token;
      if (existingToken) {
        token = existingToken.token;
      } else {
        // 4. 如果没有，则生成一个新的随机令牌和过期时间
        token = crypto.randomBytes(32).toString('hex');
        const expiresAt = new Date();
        expiresAt.setMinutes(expiresAt.getMinutes() + TOKEN_EXPIRATION_MINUTES);

        // 清理该邮箱的旧令牌
        await strapi.query('api::activation-token.activation-token').deleteMany({
          where: { email: lowerCaseEmail },
        });

        // 创建新令牌
        await activationTokenService.create({
          data: {
            token,
            email: lowerCaseEmail,
            expiresAt,
            publishedAt: new Date(), // 如果是 draft&publish 类型，需要设置 publishedAt
          },
        });
      }

      // 5. 构造激活链接并发送邮件
      const activationLink = FRONTEND_ACTIVATION_URL.replace('{token}', token);

      // v5 变化点: 调用 email 插件服务的方式
      const emailService = strapi.plugin('email').service('email');
      
      await emailService.send({
        to: lowerCaseEmail,
        from: strapi.config.get('plugin::email.settings.defaultFrom', '<EMAIL>'),
        subject: '请激活您的账户 (Activate your account)',
        text: `您好！\n\n请点击以下链接以激活您的账户 (该链接将在 ${TOKEN_EXPIRATION_MINUTES} 分钟后失效):\n${activationLink}`,
        html: `<p>您好！</p><p>请点击以下链接以激活您的账户 (该链接将在 <b>${TOKEN_EXPIRATION_MINUTES} 分钟</b> 后失效):</p><a href="${activationLink}">${activationLink}</a>`,
      });

      // 6. 返回成功响应
      ctx.send({ message: '激活邮件已发送，请检查您的收件箱。' });

    } catch (error) {
      strapi.log.error('Error during activation process:', error);
      return ctx.internalServerError('发生未知错误，请稍后重试。 (An error occurred. Please try again later.)');
    }
  },

/**
   * 使用令牌和密码激活账户，并自动登录返回JWT (手动清理版，保证运行)
   */
async activateAccount(ctx) {
  const { token, password } = ctx.request.body;
  if (!token || !password || password.length < 8) {
    return ctx.badRequest('必须提供有效的令牌和至少8位长的密码。');
  }
  const activationTokenService = strapi.service('api::activation-token.activation-token');
  let activationToken = await activationTokenService.find({ filters: { token } });
  activationToken = activationToken.results[0];
  if (!activationToken) {
    return ctx.badRequest('无效的令牌 (Invalid token)');
  }
  if (new Date(activationToken.expiresAt) < new Date()) {
    await activationTokenService.delete(activationToken.id);
    return ctx.badRequest('令牌已过期，请重新申请。');
  }
  
  try {
    const userService = strapi.plugin('users-permissions').service('user');
    const jwtService = strapi.plugin('users-permissions').service('jwt');

    // 这个 userService.add 方法返回的 newUser 对象包含了密码哈希等敏感信息
    const newUser = await userService.add({
      username: activationToken.email,
      email: activationToken.email,
      password: password,
      provider: 'local',
      confirmed: true,
    });

    await activationTokenService.delete(activationToken.id);

    const jwt = jwtService.issue({ id: newUser.id });

    // -------------------- 【最终的、绝对可靠的修正】 --------------------
    // 由于所有内置的 sanitize API 在此场景下都无法正常工作，我们采用最直接、最安全的手动方式。
    // 我们只挑选我们确认安全的公共字段，从 newUser 对象复制到新对象，然后返回给前端。
    // 这种方法没有任何对 Strapi 内部 API 的依赖，100% 会成功执行。
    const sanitizedUser = {
      id: newUser.id,
      username: newUser.username,
      email: newUser.email,
      confirmed: newUser.confirmed,
      blocked: newUser.blocked,
      createdAt: newUser.createdAt,
      updatedAt: newUser.updatedAt,
    };
    // ------------------------------------------------------------------

    // 返回我们手动创建的、绝对安全的用户对象
    ctx.send({
      message: 'Account activated successfully',
      jwt,
      user: sanitizedUser,
    });

  } catch (error) {
    strapi.log.error('Error during account activation:', error);
    if (error.message && error.message.toLowerCase().includes('already taken')) {
      return ctx.badRequest('此邮箱或用户名已被注册。');
    }
    return ctx.internalServerError('账户创建过程中发生错误。');
  }
},
};