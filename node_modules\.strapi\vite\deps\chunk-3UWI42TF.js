import {
  buildValidParams,
  useDocument,
  useDocumentActions
} from "./chunk-D63J2BWQ.js";
import {
  Table,
  create3 as create,
  create6 as create2,
  require_omit,
  useForm,
  useTable
} from "./chunk-ELTZWS66.js";
import {
  useAP<PERSON>rror<PERSON>and<PERSON>,
  useAuth,
  useNotification,
  useQueryParams
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Button,
  CheckboxImpl,
  Dialog,
  Field,
  Flex,
  IconButton,
  Modal,
  Popover,
  SingleSelect,
  SingleSelectOption,
  Status,
  TooltipImpl,
  Typography,
  VisuallyHidden,
  __assign,
  __extends,
  __rest,
  __spreadArray,
  adminApi,
  require_get,
  require_hoist_non_react_statics_cjs,
  require_lib,
  skipToken,
  useCollator
} from "./chunk-FGNN7I5W.js";
import {
  Link,
  matchPath,
  useNavigate,
  useParams
} from "./chunk-WHKY4GB5.js";
import {
  ForwardRef$1d,
  ForwardRef$1r,
  ForwardRef$2h,
  ForwardRef$3,
  ForwardRef$3B,
  ForwardRef$3D,
  ForwardRef$3J,
  ForwardRef$3T,
  ForwardRef$3V,
  ForwardRef$4D,
  ForwardRef$4T,
  ForwardRef$4n,
  ForwardRef$j
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/useIntl.js
var React3 = __toESM(require_react());

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/injectIntl.js
var React2 = __toESM(require_react());
var import_hoist_non_react_statics = __toESM(require_hoist_non_react_statics_cjs());

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/utils.js
var React = __toESM(require_react());

// node_modules/@strapi/i18n/node_modules/@formatjs/ecma402-abstract/lib/262.js
var MINUTES_PER_HOUR = 60;
var SECONDS_PER_MINUTE = 60;
var MS_PER_SECOND = 1e3;
var MS_PER_MINUTE = MS_PER_SECOND * SECONDS_PER_MINUTE;
var MS_PER_HOUR = MS_PER_MINUTE * MINUTES_PER_HOUR;

// node_modules/@strapi/i18n/node_modules/@formatjs/ecma402-abstract/lib/IsSanctionedSimpleUnitIdentifier.js
var SANCTIONED_UNITS = [
  "angle-degree",
  "area-acre",
  "area-hectare",
  "concentr-percent",
  "digital-bit",
  "digital-byte",
  "digital-gigabit",
  "digital-gigabyte",
  "digital-kilobit",
  "digital-kilobyte",
  "digital-megabit",
  "digital-megabyte",
  "digital-petabyte",
  "digital-terabit",
  "digital-terabyte",
  "duration-day",
  "duration-hour",
  "duration-millisecond",
  "duration-minute",
  "duration-month",
  "duration-second",
  "duration-week",
  "duration-year",
  "length-centimeter",
  "length-foot",
  "length-inch",
  "length-kilometer",
  "length-meter",
  "length-mile-scandinavian",
  "length-mile",
  "length-millimeter",
  "length-yard",
  "mass-gram",
  "mass-kilogram",
  "mass-ounce",
  "mass-pound",
  "mass-stone",
  "temperature-celsius",
  "temperature-fahrenheit",
  "volume-fluid-ounce",
  "volume-gallon",
  "volume-liter",
  "volume-milliliter"
];
function removeUnitNamespace(unit) {
  return unit.slice(unit.indexOf("-") + 1);
}
var SIMPLE_UNITS = SANCTIONED_UNITS.map(removeUnitNamespace);

// node_modules/@strapi/i18n/node_modules/@formatjs/ecma402-abstract/lib/utils.js
function invariant(condition, message, Err) {
  if (Err === void 0) {
    Err = Error;
  }
  if (!condition) {
    throw new Err(message);
  }
}

// node_modules/@strapi/i18n/node_modules/@formatjs/ecma402-abstract/lib/regex.generated.js
var S_UNICODE_REGEX = /[\$\+<->\^`\|~\xA2-\xA6\xA8\xA9\xAC\xAE-\xB1\xB4\xB8\xD7\xF7\u02C2-\u02C5\u02D2-\u02DF\u02E5-\u02EB\u02ED\u02EF-\u02FF\u0375\u0384\u0385\u03F6\u0482\u058D-\u058F\u0606-\u0608\u060B\u060E\u060F\u06DE\u06E9\u06FD\u06FE\u07F6\u07FE\u07FF\u09F2\u09F3\u09FA\u09FB\u0AF1\u0B70\u0BF3-\u0BFA\u0C7F\u0D4F\u0D79\u0E3F\u0F01-\u0F03\u0F13\u0F15-\u0F17\u0F1A-\u0F1F\u0F34\u0F36\u0F38\u0FBE-\u0FC5\u0FC7-\u0FCC\u0FCE\u0FCF\u0FD5-\u0FD8\u109E\u109F\u1390-\u1399\u166D\u17DB\u1940\u19DE-\u19FF\u1B61-\u1B6A\u1B74-\u1B7C\u1FBD\u1FBF-\u1FC1\u1FCD-\u1FCF\u1FDD-\u1FDF\u1FED-\u1FEF\u1FFD\u1FFE\u2044\u2052\u207A-\u207C\u208A-\u208C\u20A0-\u20BF\u2100\u2101\u2103-\u2106\u2108\u2109\u2114\u2116-\u2118\u211E-\u2123\u2125\u2127\u2129\u212E\u213A\u213B\u2140-\u2144\u214A-\u214D\u214F\u218A\u218B\u2190-\u2307\u230C-\u2328\u232B-\u2426\u2440-\u244A\u249C-\u24E9\u2500-\u2767\u2794-\u27C4\u27C7-\u27E5\u27F0-\u2982\u2999-\u29D7\u29DC-\u29FB\u29FE-\u2B73\u2B76-\u2B95\u2B97-\u2BFF\u2CE5-\u2CEA\u2E50\u2E51\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u2FF0-\u2FFB\u3004\u3012\u3013\u3020\u3036\u3037\u303E\u303F\u309B\u309C\u3190\u3191\u3196-\u319F\u31C0-\u31E3\u3200-\u321E\u322A-\u3247\u3250\u3260-\u327F\u328A-\u32B0\u32C0-\u33FF\u4DC0-\u4DFF\uA490-\uA4C6\uA700-\uA716\uA720\uA721\uA789\uA78A\uA828-\uA82B\uA836-\uA839\uAA77-\uAA79\uAB5B\uAB6A\uAB6B\uFB29\uFBB2-\uFBC1\uFDFC\uFDFD\uFE62\uFE64-\uFE66\uFE69\uFF04\uFF0B\uFF1C-\uFF1E\uFF3E\uFF40\uFF5C\uFF5E\uFFE0-\uFFE6\uFFE8-\uFFEE\uFFFC\uFFFD]|\uD800[\uDD37-\uDD3F\uDD79-\uDD89\uDD8C-\uDD8E\uDD90-\uDD9C\uDDA0\uDDD0-\uDDFC]|\uD802[\uDC77\uDC78\uDEC8]|\uD805\uDF3F|\uD807[\uDFD5-\uDFF1]|\uD81A[\uDF3C-\uDF3F\uDF45]|\uD82F\uDC9C|\uD834[\uDC00-\uDCF5\uDD00-\uDD26\uDD29-\uDD64\uDD6A-\uDD6C\uDD83\uDD84\uDD8C-\uDDA9\uDDAE-\uDDE8\uDE00-\uDE41\uDE45\uDF00-\uDF56]|\uD835[\uDEC1\uDEDB\uDEFB\uDF15\uDF35\uDF4F\uDF6F\uDF89\uDFA9\uDFC3]|\uD836[\uDC00-\uDDFF\uDE37-\uDE3A\uDE6D-\uDE74\uDE76-\uDE83\uDE85\uDE86]|\uD838[\uDD4F\uDEFF]|\uD83B[\uDCAC\uDCB0\uDD2E\uDEF0\uDEF1]|\uD83C[\uDC00-\uDC2B\uDC30-\uDC93\uDCA0-\uDCAE\uDCB1-\uDCBF\uDCC1-\uDCCF\uDCD1-\uDCF5\uDD0D-\uDDAD\uDDE6-\uDE02\uDE10-\uDE3B\uDE40-\uDE48\uDE50\uDE51\uDE60-\uDE65\uDF00-\uDFFF]|\uD83D[\uDC00-\uDED7\uDEE0-\uDEEC\uDEF0-\uDEFC\uDF00-\uDF73\uDF80-\uDFD8\uDFE0-\uDFEB]|\uD83E[\uDC00-\uDC0B\uDC10-\uDC47\uDC50-\uDC59\uDC60-\uDC87\uDC90-\uDCAD\uDCB0\uDCB1\uDD00-\uDD78\uDD7A-\uDDCB\uDDCD-\uDE53\uDE60-\uDE6D\uDE70-\uDE74\uDE78-\uDE7A\uDE80-\uDE86\uDE90-\uDEA8\uDEB0-\uDEB6\uDEC0-\uDEC2\uDED0-\uDED6\uDF00-\uDF92\uDF94-\uDFCA]/;

// node_modules/@strapi/i18n/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/format_to_parts.js
var CARET_S_UNICODE_REGEX = new RegExp("^".concat(S_UNICODE_REGEX.source));
var S_DOLLAR_UNICODE_REGEX = new RegExp("".concat(S_UNICODE_REGEX.source, "$"));

// node_modules/@strapi/i18n/node_modules/@formatjs/ecma402-abstract/lib/data.js
var MissingLocaleDataError = (
  /** @class */
  function(_super) {
    __extends(MissingLocaleDataError2, _super);
    function MissingLocaleDataError2() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.type = "MISSING_LOCALE_DATA";
      return _this;
    }
    return MissingLocaleDataError2;
  }(Error)
);

// node_modules/@strapi/i18n/node_modules/@formatjs/ecma402-abstract/lib/types/date-time.js
var RangePatternType;
(function(RangePatternType2) {
  RangePatternType2["startRange"] = "startRange";
  RangePatternType2["shared"] = "shared";
  RangePatternType2["endRange"] = "endRange";
})(RangePatternType || (RangePatternType = {}));

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-messageformat-parser/lib/error.js
var ErrorKind;
(function(ErrorKind2) {
  ErrorKind2[ErrorKind2["EXPECT_ARGUMENT_CLOSING_BRACE"] = 1] = "EXPECT_ARGUMENT_CLOSING_BRACE";
  ErrorKind2[ErrorKind2["EMPTY_ARGUMENT"] = 2] = "EMPTY_ARGUMENT";
  ErrorKind2[ErrorKind2["MALFORMED_ARGUMENT"] = 3] = "MALFORMED_ARGUMENT";
  ErrorKind2[ErrorKind2["EXPECT_ARGUMENT_TYPE"] = 4] = "EXPECT_ARGUMENT_TYPE";
  ErrorKind2[ErrorKind2["INVALID_ARGUMENT_TYPE"] = 5] = "INVALID_ARGUMENT_TYPE";
  ErrorKind2[ErrorKind2["EXPECT_ARGUMENT_STYLE"] = 6] = "EXPECT_ARGUMENT_STYLE";
  ErrorKind2[ErrorKind2["INVALID_NUMBER_SKELETON"] = 7] = "INVALID_NUMBER_SKELETON";
  ErrorKind2[ErrorKind2["INVALID_DATE_TIME_SKELETON"] = 8] = "INVALID_DATE_TIME_SKELETON";
  ErrorKind2[ErrorKind2["EXPECT_NUMBER_SKELETON"] = 9] = "EXPECT_NUMBER_SKELETON";
  ErrorKind2[ErrorKind2["EXPECT_DATE_TIME_SKELETON"] = 10] = "EXPECT_DATE_TIME_SKELETON";
  ErrorKind2[ErrorKind2["UNCLOSED_QUOTE_IN_ARGUMENT_STYLE"] = 11] = "UNCLOSED_QUOTE_IN_ARGUMENT_STYLE";
  ErrorKind2[ErrorKind2["EXPECT_SELECT_ARGUMENT_OPTIONS"] = 12] = "EXPECT_SELECT_ARGUMENT_OPTIONS";
  ErrorKind2[ErrorKind2["EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE"] = 13] = "EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE";
  ErrorKind2[ErrorKind2["INVALID_PLURAL_ARGUMENT_OFFSET_VALUE"] = 14] = "INVALID_PLURAL_ARGUMENT_OFFSET_VALUE";
  ErrorKind2[ErrorKind2["EXPECT_SELECT_ARGUMENT_SELECTOR"] = 15] = "EXPECT_SELECT_ARGUMENT_SELECTOR";
  ErrorKind2[ErrorKind2["EXPECT_PLURAL_ARGUMENT_SELECTOR"] = 16] = "EXPECT_PLURAL_ARGUMENT_SELECTOR";
  ErrorKind2[ErrorKind2["EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT"] = 17] = "EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT";
  ErrorKind2[ErrorKind2["EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT"] = 18] = "EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT";
  ErrorKind2[ErrorKind2["INVALID_PLURAL_ARGUMENT_SELECTOR"] = 19] = "INVALID_PLURAL_ARGUMENT_SELECTOR";
  ErrorKind2[ErrorKind2["DUPLICATE_PLURAL_ARGUMENT_SELECTOR"] = 20] = "DUPLICATE_PLURAL_ARGUMENT_SELECTOR";
  ErrorKind2[ErrorKind2["DUPLICATE_SELECT_ARGUMENT_SELECTOR"] = 21] = "DUPLICATE_SELECT_ARGUMENT_SELECTOR";
  ErrorKind2[ErrorKind2["MISSING_OTHER_CLAUSE"] = 22] = "MISSING_OTHER_CLAUSE";
  ErrorKind2[ErrorKind2["INVALID_TAG"] = 23] = "INVALID_TAG";
  ErrorKind2[ErrorKind2["INVALID_TAG_NAME"] = 25] = "INVALID_TAG_NAME";
  ErrorKind2[ErrorKind2["UNMATCHED_CLOSING_TAG"] = 26] = "UNMATCHED_CLOSING_TAG";
  ErrorKind2[ErrorKind2["UNCLOSED_TAG"] = 27] = "UNCLOSED_TAG";
})(ErrorKind || (ErrorKind = {}));

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-messageformat-parser/lib/types.js
var TYPE;
(function(TYPE2) {
  TYPE2[TYPE2["literal"] = 0] = "literal";
  TYPE2[TYPE2["argument"] = 1] = "argument";
  TYPE2[TYPE2["number"] = 2] = "number";
  TYPE2[TYPE2["date"] = 3] = "date";
  TYPE2[TYPE2["time"] = 4] = "time";
  TYPE2[TYPE2["select"] = 5] = "select";
  TYPE2[TYPE2["plural"] = 6] = "plural";
  TYPE2[TYPE2["pound"] = 7] = "pound";
  TYPE2[TYPE2["tag"] = 8] = "tag";
})(TYPE || (TYPE = {}));
var SKELETON_TYPE;
(function(SKELETON_TYPE2) {
  SKELETON_TYPE2[SKELETON_TYPE2["number"] = 0] = "number";
  SKELETON_TYPE2[SKELETON_TYPE2["dateTime"] = 1] = "dateTime";
})(SKELETON_TYPE || (SKELETON_TYPE = {}));
function isLiteralElement(el) {
  return el.type === TYPE.literal;
}
function isArgumentElement(el) {
  return el.type === TYPE.argument;
}
function isNumberElement(el) {
  return el.type === TYPE.number;
}
function isDateElement(el) {
  return el.type === TYPE.date;
}
function isTimeElement(el) {
  return el.type === TYPE.time;
}
function isSelectElement(el) {
  return el.type === TYPE.select;
}
function isPluralElement(el) {
  return el.type === TYPE.plural;
}
function isPoundElement(el) {
  return el.type === TYPE.pound;
}
function isTagElement(el) {
  return el.type === TYPE.tag;
}
function isNumberSkeleton(el) {
  return !!(el && typeof el === "object" && el.type === SKELETON_TYPE.number);
}
function isDateTimeSkeleton(el) {
  return !!(el && typeof el === "object" && el.type === SKELETON_TYPE.dateTime);
}

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js
var SPACE_SEPARATOR_REGEX = /[ \xA0\u1680\u2000-\u200A\u202F\u205F\u3000]/;

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js
var DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;
function parseDateTimeSkeleton(skeleton) {
  var result = {};
  skeleton.replace(DATE_TIME_REGEX, function(match) {
    var len = match.length;
    switch (match[0]) {
      case "G":
        result.era = len === 4 ? "long" : len === 5 ? "narrow" : "short";
        break;
      case "y":
        result.year = len === 2 ? "2-digit" : "numeric";
        break;
      case "Y":
      case "u":
      case "U":
      case "r":
        throw new RangeError("`Y/u/U/r` (year) patterns are not supported, use `y` instead");
      case "q":
      case "Q":
        throw new RangeError("`q/Q` (quarter) patterns are not supported");
      case "M":
      case "L":
        result.month = ["numeric", "2-digit", "short", "long", "narrow"][len - 1];
        break;
      case "w":
      case "W":
        throw new RangeError("`w/W` (week) patterns are not supported");
      case "d":
        result.day = ["numeric", "2-digit"][len - 1];
        break;
      case "D":
      case "F":
      case "g":
        throw new RangeError("`D/F/g` (day) patterns are not supported, use `d` instead");
      case "E":
        result.weekday = len === 4 ? "long" : len === 5 ? "narrow" : "short";
        break;
      case "e":
        if (len < 4) {
          throw new RangeError("`e..eee` (weekday) patterns are not supported");
        }
        result.weekday = ["short", "long", "narrow", "short"][len - 4];
        break;
      case "c":
        if (len < 4) {
          throw new RangeError("`c..ccc` (weekday) patterns are not supported");
        }
        result.weekday = ["short", "long", "narrow", "short"][len - 4];
        break;
      case "a":
        result.hour12 = true;
        break;
      case "b":
      case "B":
        throw new RangeError("`b/B` (period) patterns are not supported, use `a` instead");
      case "h":
        result.hourCycle = "h12";
        result.hour = ["numeric", "2-digit"][len - 1];
        break;
      case "H":
        result.hourCycle = "h23";
        result.hour = ["numeric", "2-digit"][len - 1];
        break;
      case "K":
        result.hourCycle = "h11";
        result.hour = ["numeric", "2-digit"][len - 1];
        break;
      case "k":
        result.hourCycle = "h24";
        result.hour = ["numeric", "2-digit"][len - 1];
        break;
      case "j":
      case "J":
      case "C":
        throw new RangeError("`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead");
      case "m":
        result.minute = ["numeric", "2-digit"][len - 1];
        break;
      case "s":
        result.second = ["numeric", "2-digit"][len - 1];
        break;
      case "S":
      case "A":
        throw new RangeError("`S/A` (second) patterns are not supported, use `s` instead");
      case "z":
        result.timeZoneName = len < 4 ? "short" : "long";
        break;
      case "Z":
      case "O":
      case "v":
      case "V":
      case "X":
      case "x":
        throw new RangeError("`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead");
    }
    return "";
  });
  return result;
}

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js
var WHITE_SPACE_REGEX = /[\t-\r \x85\u200E\u200F\u2028\u2029]/i;

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-skeleton-parser/lib/number.js
function parseNumberSkeletonFromString(skeleton) {
  if (skeleton.length === 0) {
    throw new Error("Number skeleton cannot be empty");
  }
  var stringTokens = skeleton.split(WHITE_SPACE_REGEX).filter(function(x) {
    return x.length > 0;
  });
  var tokens = [];
  for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {
    var stringToken = stringTokens_1[_i];
    var stemAndOptions = stringToken.split("/");
    if (stemAndOptions.length === 0) {
      throw new Error("Invalid number skeleton");
    }
    var stem = stemAndOptions[0], options = stemAndOptions.slice(1);
    for (var _a2 = 0, options_1 = options; _a2 < options_1.length; _a2++) {
      var option = options_1[_a2];
      if (option.length === 0) {
        throw new Error("Invalid number skeleton");
      }
    }
    tokens.push({ stem, options });
  }
  return tokens;
}
function icuUnitToEcma(unit) {
  return unit.replace(/^(.*?)-/, "");
}
var FRACTION_PRECISION_REGEX = /^\.(?:(0+)(\*)?|(#+)|(0+)(#+))$/g;
var SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\+|#+)?[rs]?$/g;
var INTEGER_WIDTH_REGEX = /(\*)(0+)|(#+)(0+)|(0+)/g;
var CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;
function parseSignificantPrecision(str) {
  var result = {};
  if (str[str.length - 1] === "r") {
    result.roundingPriority = "morePrecision";
  } else if (str[str.length - 1] === "s") {
    result.roundingPriority = "lessPrecision";
  }
  str.replace(SIGNIFICANT_PRECISION_REGEX, function(_, g1, g2) {
    if (typeof g2 !== "string") {
      result.minimumSignificantDigits = g1.length;
      result.maximumSignificantDigits = g1.length;
    } else if (g2 === "+") {
      result.minimumSignificantDigits = g1.length;
    } else if (g1[0] === "#") {
      result.maximumSignificantDigits = g1.length;
    } else {
      result.minimumSignificantDigits = g1.length;
      result.maximumSignificantDigits = g1.length + (typeof g2 === "string" ? g2.length : 0);
    }
    return "";
  });
  return result;
}
function parseSign(str) {
  switch (str) {
    case "sign-auto":
      return {
        signDisplay: "auto"
      };
    case "sign-accounting":
    case "()":
      return {
        currencySign: "accounting"
      };
    case "sign-always":
    case "+!":
      return {
        signDisplay: "always"
      };
    case "sign-accounting-always":
    case "()!":
      return {
        signDisplay: "always",
        currencySign: "accounting"
      };
    case "sign-except-zero":
    case "+?":
      return {
        signDisplay: "exceptZero"
      };
    case "sign-accounting-except-zero":
    case "()?":
      return {
        signDisplay: "exceptZero",
        currencySign: "accounting"
      };
    case "sign-never":
    case "+_":
      return {
        signDisplay: "never"
      };
  }
}
function parseConciseScientificAndEngineeringStem(stem) {
  var result;
  if (stem[0] === "E" && stem[1] === "E") {
    result = {
      notation: "engineering"
    };
    stem = stem.slice(2);
  } else if (stem[0] === "E") {
    result = {
      notation: "scientific"
    };
    stem = stem.slice(1);
  }
  if (result) {
    var signDisplay = stem.slice(0, 2);
    if (signDisplay === "+!") {
      result.signDisplay = "always";
      stem = stem.slice(2);
    } else if (signDisplay === "+?") {
      result.signDisplay = "exceptZero";
      stem = stem.slice(2);
    }
    if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {
      throw new Error("Malformed concise eng/scientific notation");
    }
    result.minimumIntegerDigits = stem.length;
  }
  return result;
}
function parseNotationOptions(opt) {
  var result = {};
  var signOpts = parseSign(opt);
  if (signOpts) {
    return signOpts;
  }
  return result;
}
function parseNumberSkeleton(tokens) {
  var result = {};
  for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {
    var token = tokens_1[_i];
    switch (token.stem) {
      case "percent":
      case "%":
        result.style = "percent";
        continue;
      case "%x100":
        result.style = "percent";
        result.scale = 100;
        continue;
      case "currency":
        result.style = "currency";
        result.currency = token.options[0];
        continue;
      case "group-off":
      case ",_":
        result.useGrouping = false;
        continue;
      case "precision-integer":
      case ".":
        result.maximumFractionDigits = 0;
        continue;
      case "measure-unit":
      case "unit":
        result.style = "unit";
        result.unit = icuUnitToEcma(token.options[0]);
        continue;
      case "compact-short":
      case "K":
        result.notation = "compact";
        result.compactDisplay = "short";
        continue;
      case "compact-long":
      case "KK":
        result.notation = "compact";
        result.compactDisplay = "long";
        continue;
      case "scientific":
        result = __assign(__assign(__assign({}, result), { notation: "scientific" }), token.options.reduce(function(all, opt2) {
          return __assign(__assign({}, all), parseNotationOptions(opt2));
        }, {}));
        continue;
      case "engineering":
        result = __assign(__assign(__assign({}, result), { notation: "engineering" }), token.options.reduce(function(all, opt2) {
          return __assign(__assign({}, all), parseNotationOptions(opt2));
        }, {}));
        continue;
      case "notation-simple":
        result.notation = "standard";
        continue;
      case "unit-width-narrow":
        result.currencyDisplay = "narrowSymbol";
        result.unitDisplay = "narrow";
        continue;
      case "unit-width-short":
        result.currencyDisplay = "code";
        result.unitDisplay = "short";
        continue;
      case "unit-width-full-name":
        result.currencyDisplay = "name";
        result.unitDisplay = "long";
        continue;
      case "unit-width-iso-code":
        result.currencyDisplay = "symbol";
        continue;
      case "scale":
        result.scale = parseFloat(token.options[0]);
        continue;
      case "rounding-mode-floor":
        result.roundingMode = "floor";
        continue;
      case "rounding-mode-ceiling":
        result.roundingMode = "ceil";
        continue;
      case "rounding-mode-down":
        result.roundingMode = "trunc";
        continue;
      case "rounding-mode-up":
        result.roundingMode = "expand";
        continue;
      case "rounding-mode-half-even":
        result.roundingMode = "halfEven";
        continue;
      case "rounding-mode-half-down":
        result.roundingMode = "halfTrunc";
        continue;
      case "rounding-mode-half-up":
        result.roundingMode = "halfExpand";
        continue;
      case "integer-width":
        if (token.options.length > 1) {
          throw new RangeError("integer-width stems only accept a single optional option");
        }
        token.options[0].replace(INTEGER_WIDTH_REGEX, function(_, g1, g2, g3, g4, g5) {
          if (g1) {
            result.minimumIntegerDigits = g2.length;
          } else if (g3 && g4) {
            throw new Error("We currently do not support maximum integer digits");
          } else if (g5) {
            throw new Error("We currently do not support exact integer digits");
          }
          return "";
        });
        continue;
    }
    if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {
      result.minimumIntegerDigits = token.stem.length;
      continue;
    }
    if (FRACTION_PRECISION_REGEX.test(token.stem)) {
      if (token.options.length > 1) {
        throw new RangeError("Fraction-precision stems only accept a single optional option");
      }
      token.stem.replace(FRACTION_PRECISION_REGEX, function(_, g1, g2, g3, g4, g5) {
        if (g2 === "*") {
          result.minimumFractionDigits = g1.length;
        } else if (g3 && g3[0] === "#") {
          result.maximumFractionDigits = g3.length;
        } else if (g4 && g5) {
          result.minimumFractionDigits = g4.length;
          result.maximumFractionDigits = g4.length + g5.length;
        } else {
          result.minimumFractionDigits = g1.length;
          result.maximumFractionDigits = g1.length;
        }
        return "";
      });
      var opt = token.options[0];
      if (opt === "w") {
        result = __assign(__assign({}, result), { trailingZeroDisplay: "stripIfInteger" });
      } else if (opt) {
        result = __assign(__assign({}, result), parseSignificantPrecision(opt));
      }
      continue;
    }
    if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {
      result = __assign(__assign({}, result), parseSignificantPrecision(token.stem));
      continue;
    }
    var signOpts = parseSign(token.stem);
    if (signOpts) {
      result = __assign(__assign({}, result), signOpts);
    }
    var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);
    if (conciseScientificAndEngineeringOpts) {
      result = __assign(__assign({}, result), conciseScientificAndEngineeringOpts);
    }
  }
  return result;
}

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js
var timeData = {
  "001": [
    "H",
    "h"
  ],
  "AC": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "AD": [
    "H",
    "hB"
  ],
  "AE": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "AF": [
    "H",
    "hb",
    "hB",
    "h"
  ],
  "AG": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "AI": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "AL": [
    "h",
    "H",
    "hB"
  ],
  "AM": [
    "H",
    "hB"
  ],
  "AO": [
    "H",
    "hB"
  ],
  "AR": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "AS": [
    "h",
    "H"
  ],
  "AT": [
    "H",
    "hB"
  ],
  "AU": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "AW": [
    "H",
    "hB"
  ],
  "AX": [
    "H"
  ],
  "AZ": [
    "H",
    "hB",
    "h"
  ],
  "BA": [
    "H",
    "hB",
    "h"
  ],
  "BB": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "BD": [
    "h",
    "hB",
    "H"
  ],
  "BE": [
    "H",
    "hB"
  ],
  "BF": [
    "H",
    "hB"
  ],
  "BG": [
    "H",
    "hB",
    "h"
  ],
  "BH": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "BI": [
    "H",
    "h"
  ],
  "BJ": [
    "H",
    "hB"
  ],
  "BL": [
    "H",
    "hB"
  ],
  "BM": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "BN": [
    "hb",
    "hB",
    "h",
    "H"
  ],
  "BO": [
    "H",
    "hB",
    "h",
    "hb"
  ],
  "BQ": [
    "H"
  ],
  "BR": [
    "H",
    "hB"
  ],
  "BS": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "BT": [
    "h",
    "H"
  ],
  "BW": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "BY": [
    "H",
    "h"
  ],
  "BZ": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "CA": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "CC": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "CD": [
    "hB",
    "H"
  ],
  "CF": [
    "H",
    "h",
    "hB"
  ],
  "CG": [
    "H",
    "hB"
  ],
  "CH": [
    "H",
    "hB",
    "h"
  ],
  "CI": [
    "H",
    "hB"
  ],
  "CK": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "CL": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "CM": [
    "H",
    "h",
    "hB"
  ],
  "CN": [
    "H",
    "hB",
    "hb",
    "h"
  ],
  "CO": [
    "h",
    "H",
    "hB",
    "hb"
  ],
  "CP": [
    "H"
  ],
  "CR": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "CU": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "CV": [
    "H",
    "hB"
  ],
  "CW": [
    "H",
    "hB"
  ],
  "CX": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "CY": [
    "h",
    "H",
    "hb",
    "hB"
  ],
  "CZ": [
    "H"
  ],
  "DE": [
    "H",
    "hB"
  ],
  "DG": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "DJ": [
    "h",
    "H"
  ],
  "DK": [
    "H"
  ],
  "DM": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "DO": [
    "h",
    "H",
    "hB",
    "hb"
  ],
  "DZ": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "EA": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "EC": [
    "H",
    "hB",
    "h",
    "hb"
  ],
  "EE": [
    "H",
    "hB"
  ],
  "EG": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "EH": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "ER": [
    "h",
    "H"
  ],
  "ES": [
    "H",
    "hB",
    "h",
    "hb"
  ],
  "ET": [
    "hB",
    "hb",
    "h",
    "H"
  ],
  "FI": [
    "H"
  ],
  "FJ": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "FK": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "FM": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "FO": [
    "H",
    "h"
  ],
  "FR": [
    "H",
    "hB"
  ],
  "GA": [
    "H",
    "hB"
  ],
  "GB": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "GD": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "GE": [
    "H",
    "hB",
    "h"
  ],
  "GF": [
    "H",
    "hB"
  ],
  "GG": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "GH": [
    "h",
    "H"
  ],
  "GI": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "GL": [
    "H",
    "h"
  ],
  "GM": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "GN": [
    "H",
    "hB"
  ],
  "GP": [
    "H",
    "hB"
  ],
  "GQ": [
    "H",
    "hB",
    "h",
    "hb"
  ],
  "GR": [
    "h",
    "H",
    "hb",
    "hB"
  ],
  "GT": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "GU": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "GW": [
    "H",
    "hB"
  ],
  "GY": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "HK": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "HN": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "HR": [
    "H",
    "hB"
  ],
  "HU": [
    "H",
    "h"
  ],
  "IC": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "ID": [
    "H"
  ],
  "IE": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "IL": [
    "H",
    "hB"
  ],
  "IM": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "IN": [
    "h",
    "H"
  ],
  "IO": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "IQ": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "IR": [
    "hB",
    "H"
  ],
  "IS": [
    "H"
  ],
  "IT": [
    "H",
    "hB"
  ],
  "JE": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "JM": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "JO": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "JP": [
    "H",
    "K",
    "h"
  ],
  "KE": [
    "hB",
    "hb",
    "H",
    "h"
  ],
  "KG": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "KH": [
    "hB",
    "h",
    "H",
    "hb"
  ],
  "KI": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "KM": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "KN": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "KP": [
    "h",
    "H",
    "hB",
    "hb"
  ],
  "KR": [
    "h",
    "H",
    "hB",
    "hb"
  ],
  "KW": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "KY": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "KZ": [
    "H",
    "hB"
  ],
  "LA": [
    "H",
    "hb",
    "hB",
    "h"
  ],
  "LB": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "LC": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "LI": [
    "H",
    "hB",
    "h"
  ],
  "LK": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "LR": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "LS": [
    "h",
    "H"
  ],
  "LT": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "LU": [
    "H",
    "h",
    "hB"
  ],
  "LV": [
    "H",
    "hB",
    "hb",
    "h"
  ],
  "LY": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "MA": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "MC": [
    "H",
    "hB"
  ],
  "MD": [
    "H",
    "hB"
  ],
  "ME": [
    "H",
    "hB",
    "h"
  ],
  "MF": [
    "H",
    "hB"
  ],
  "MG": [
    "H",
    "h"
  ],
  "MH": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "MK": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "ML": [
    "H"
  ],
  "MM": [
    "hB",
    "hb",
    "H",
    "h"
  ],
  "MN": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "MO": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "MP": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "MQ": [
    "H",
    "hB"
  ],
  "MR": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "MS": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "MT": [
    "H",
    "h"
  ],
  "MU": [
    "H",
    "h"
  ],
  "MV": [
    "H",
    "h"
  ],
  "MW": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "MX": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "MY": [
    "hb",
    "hB",
    "h",
    "H"
  ],
  "MZ": [
    "H",
    "hB"
  ],
  "NA": [
    "h",
    "H",
    "hB",
    "hb"
  ],
  "NC": [
    "H",
    "hB"
  ],
  "NE": [
    "H"
  ],
  "NF": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "NG": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "NI": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "NL": [
    "H",
    "hB"
  ],
  "NO": [
    "H",
    "h"
  ],
  "NP": [
    "H",
    "h",
    "hB"
  ],
  "NR": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "NU": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "NZ": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "OM": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "PA": [
    "h",
    "H",
    "hB",
    "hb"
  ],
  "PE": [
    "H",
    "hB",
    "h",
    "hb"
  ],
  "PF": [
    "H",
    "h",
    "hB"
  ],
  "PG": [
    "h",
    "H"
  ],
  "PH": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "PK": [
    "h",
    "hB",
    "H"
  ],
  "PL": [
    "H",
    "h"
  ],
  "PM": [
    "H",
    "hB"
  ],
  "PN": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "PR": [
    "h",
    "H",
    "hB",
    "hb"
  ],
  "PS": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "PT": [
    "H",
    "hB"
  ],
  "PW": [
    "h",
    "H"
  ],
  "PY": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "QA": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "RE": [
    "H",
    "hB"
  ],
  "RO": [
    "H",
    "hB"
  ],
  "RS": [
    "H",
    "hB",
    "h"
  ],
  "RU": [
    "H"
  ],
  "RW": [
    "H",
    "h"
  ],
  "SA": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "SB": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "SC": [
    "H",
    "h",
    "hB"
  ],
  "SD": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "SE": [
    "H"
  ],
  "SG": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "SH": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "SI": [
    "H",
    "hB"
  ],
  "SJ": [
    "H"
  ],
  "SK": [
    "H"
  ],
  "SL": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "SM": [
    "H",
    "h",
    "hB"
  ],
  "SN": [
    "H",
    "h",
    "hB"
  ],
  "SO": [
    "h",
    "H"
  ],
  "SR": [
    "H",
    "hB"
  ],
  "SS": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "ST": [
    "H",
    "hB"
  ],
  "SV": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "SX": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "SY": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "SZ": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "TA": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "TC": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "TD": [
    "h",
    "H",
    "hB"
  ],
  "TF": [
    "H",
    "h",
    "hB"
  ],
  "TG": [
    "H",
    "hB"
  ],
  "TH": [
    "H",
    "h"
  ],
  "TJ": [
    "H",
    "h"
  ],
  "TL": [
    "H",
    "hB",
    "hb",
    "h"
  ],
  "TM": [
    "H",
    "h"
  ],
  "TN": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "TO": [
    "h",
    "H"
  ],
  "TR": [
    "H",
    "hB"
  ],
  "TT": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "TW": [
    "hB",
    "hb",
    "h",
    "H"
  ],
  "TZ": [
    "hB",
    "hb",
    "H",
    "h"
  ],
  "UA": [
    "H",
    "hB",
    "h"
  ],
  "UG": [
    "hB",
    "hb",
    "H",
    "h"
  ],
  "UM": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "US": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "UY": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "UZ": [
    "H",
    "hB",
    "h"
  ],
  "VA": [
    "H",
    "h",
    "hB"
  ],
  "VC": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "VE": [
    "h",
    "H",
    "hB",
    "hb"
  ],
  "VG": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "VI": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "VN": [
    "H",
    "h"
  ],
  "VU": [
    "h",
    "H"
  ],
  "WF": [
    "H",
    "hB"
  ],
  "WS": [
    "h",
    "H"
  ],
  "XK": [
    "H",
    "hB",
    "h"
  ],
  "YE": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "YT": [
    "H",
    "hB"
  ],
  "ZA": [
    "H",
    "h",
    "hb",
    "hB"
  ],
  "ZM": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "ZW": [
    "H",
    "h"
  ],
  "af-ZA": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "ar-001": [
    "h",
    "hB",
    "hb",
    "H"
  ],
  "ca-ES": [
    "H",
    "h",
    "hB"
  ],
  "en-001": [
    "h",
    "hb",
    "H",
    "hB"
  ],
  "es-BO": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "es-BR": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "es-EC": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "es-ES": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "es-GQ": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "es-PE": [
    "H",
    "h",
    "hB",
    "hb"
  ],
  "fr-CA": [
    "H",
    "h",
    "hB"
  ],
  "gl-ES": [
    "H",
    "h",
    "hB"
  ],
  "gu-IN": [
    "hB",
    "hb",
    "h",
    "H"
  ],
  "hi-IN": [
    "hB",
    "h",
    "H"
  ],
  "it-CH": [
    "H",
    "h",
    "hB"
  ],
  "it-IT": [
    "H",
    "h",
    "hB"
  ],
  "kn-IN": [
    "hB",
    "h",
    "H"
  ],
  "ml-IN": [
    "hB",
    "h",
    "H"
  ],
  "mr-IN": [
    "hB",
    "hb",
    "h",
    "H"
  ],
  "pa-IN": [
    "hB",
    "hb",
    "h",
    "H"
  ],
  "ta-IN": [
    "hB",
    "h",
    "hb",
    "H"
  ],
  "te-IN": [
    "hB",
    "h",
    "H"
  ],
  "zu-ZA": [
    "H",
    "hB",
    "hb",
    "h"
  ]
};

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js
function getBestPattern(skeleton, locale) {
  var skeletonCopy = "";
  for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {
    var patternChar = skeleton.charAt(patternPos);
    if (patternChar === "j") {
      var extraLength = 0;
      while (patternPos + 1 < skeleton.length && skeleton.charAt(patternPos + 1) === patternChar) {
        extraLength++;
        patternPos++;
      }
      var hourLen = 1 + (extraLength & 1);
      var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);
      var dayPeriodChar = "a";
      var hourChar = getDefaultHourSymbolFromLocale(locale);
      if (hourChar == "H" || hourChar == "k") {
        dayPeriodLen = 0;
      }
      while (dayPeriodLen-- > 0) {
        skeletonCopy += dayPeriodChar;
      }
      while (hourLen-- > 0) {
        skeletonCopy = hourChar + skeletonCopy;
      }
    } else if (patternChar === "J") {
      skeletonCopy += "H";
    } else {
      skeletonCopy += patternChar;
    }
  }
  return skeletonCopy;
}
function getDefaultHourSymbolFromLocale(locale) {
  var hourCycle = locale.hourCycle;
  if (hourCycle === void 0 && // @ts-ignore hourCycle(s) is not identified yet
  locale.hourCycles && // @ts-ignore
  locale.hourCycles.length) {
    hourCycle = locale.hourCycles[0];
  }
  if (hourCycle) {
    switch (hourCycle) {
      case "h24":
        return "k";
      case "h23":
        return "H";
      case "h12":
        return "h";
      case "h11":
        return "K";
      default:
        throw new Error("Invalid hourCycle");
    }
  }
  var languageTag = locale.language;
  var regionTag;
  if (languageTag !== "root") {
    regionTag = locale.maximize().region;
  }
  var hourCycles = timeData[regionTag || ""] || timeData[languageTag || ""] || timeData["".concat(languageTag, "-001")] || timeData["001"];
  return hourCycles[0];
}

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js
var _a;
var SPACE_SEPARATOR_START_REGEX = new RegExp("^".concat(SPACE_SEPARATOR_REGEX.source, "*"));
var SPACE_SEPARATOR_END_REGEX = new RegExp("".concat(SPACE_SEPARATOR_REGEX.source, "*$"));
function createLocation(start, end) {
  return { start, end };
}
var hasNativeStartsWith = !!String.prototype.startsWith && "_a".startsWith("a", 1);
var hasNativeFromCodePoint = !!String.fromCodePoint;
var hasNativeFromEntries = !!Object.fromEntries;
var hasNativeCodePointAt = !!String.prototype.codePointAt;
var hasTrimStart = !!String.prototype.trimStart;
var hasTrimEnd = !!String.prototype.trimEnd;
var hasNativeIsSafeInteger = !!Number.isSafeInteger;
var isSafeInteger = hasNativeIsSafeInteger ? Number.isSafeInteger : function(n) {
  return typeof n === "number" && isFinite(n) && Math.floor(n) === n && Math.abs(n) <= 9007199254740991;
};
var REGEX_SUPPORTS_U_AND_Y = true;
try {
  re = RE("([^\\p{White_Space}\\p{Pattern_Syntax}]*)", "yu");
  REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec("a")) === null || _a === void 0 ? void 0 : _a[0]) === "a";
} catch (_) {
  REGEX_SUPPORTS_U_AND_Y = false;
}
var re;
var startsWith = hasNativeStartsWith ? (
  // Native
  function startsWith2(s, search, position) {
    return s.startsWith(search, position);
  }
) : (
  // For IE11
  function startsWith3(s, search, position) {
    return s.slice(position, position + search.length) === search;
  }
);
var fromCodePoint = hasNativeFromCodePoint ? String.fromCodePoint : (
  // IE11
  function fromCodePoint2() {
    var codePoints = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      codePoints[_i] = arguments[_i];
    }
    var elements = "";
    var length = codePoints.length;
    var i = 0;
    var code;
    while (length > i) {
      code = codePoints[i++];
      if (code > 1114111)
        throw RangeError(code + " is not a valid code point");
      elements += code < 65536 ? String.fromCharCode(code) : String.fromCharCode(((code -= 65536) >> 10) + 55296, code % 1024 + 56320);
    }
    return elements;
  }
);
var fromEntries = (
  // native
  hasNativeFromEntries ? Object.fromEntries : (
    // Ponyfill
    function fromEntries2(entries) {
      var obj = {};
      for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {
        var _a2 = entries_1[_i], k = _a2[0], v = _a2[1];
        obj[k] = v;
      }
      return obj;
    }
  )
);
var codePointAt = hasNativeCodePointAt ? (
  // Native
  function codePointAt2(s, index2) {
    return s.codePointAt(index2);
  }
) : (
  // IE 11
  function codePointAt3(s, index2) {
    var size = s.length;
    if (index2 < 0 || index2 >= size) {
      return void 0;
    }
    var first = s.charCodeAt(index2);
    var second;
    return first < 55296 || first > 56319 || index2 + 1 === size || (second = s.charCodeAt(index2 + 1)) < 56320 || second > 57343 ? first : (first - 55296 << 10) + (second - 56320) + 65536;
  }
);
var trimStart = hasTrimStart ? (
  // Native
  function trimStart2(s) {
    return s.trimStart();
  }
) : (
  // Ponyfill
  function trimStart3(s) {
    return s.replace(SPACE_SEPARATOR_START_REGEX, "");
  }
);
var trimEnd = hasTrimEnd ? (
  // Native
  function trimEnd2(s) {
    return s.trimEnd();
  }
) : (
  // Ponyfill
  function trimEnd3(s) {
    return s.replace(SPACE_SEPARATOR_END_REGEX, "");
  }
);
function RE(s, flag) {
  return new RegExp(s, flag);
}
var matchIdentifierAtIndex;
if (REGEX_SUPPORTS_U_AND_Y) {
  IDENTIFIER_PREFIX_RE_1 = RE("([^\\p{White_Space}\\p{Pattern_Syntax}]*)", "yu");
  matchIdentifierAtIndex = function matchIdentifierAtIndex2(s, index2) {
    var _a2;
    IDENTIFIER_PREFIX_RE_1.lastIndex = index2;
    var match = IDENTIFIER_PREFIX_RE_1.exec(s);
    return (_a2 = match[1]) !== null && _a2 !== void 0 ? _a2 : "";
  };
} else {
  matchIdentifierAtIndex = function matchIdentifierAtIndex2(s, index2) {
    var match = [];
    while (true) {
      var c = codePointAt(s, index2);
      if (c === void 0 || _isWhiteSpace(c) || _isPatternSyntax(c)) {
        break;
      }
      match.push(c);
      index2 += c >= 65536 ? 2 : 1;
    }
    return fromCodePoint.apply(void 0, match);
  };
}
var IDENTIFIER_PREFIX_RE_1;
var Parser = (
  /** @class */
  function() {
    function Parser2(message, options) {
      if (options === void 0) {
        options = {};
      }
      this.message = message;
      this.position = { offset: 0, line: 1, column: 1 };
      this.ignoreTag = !!options.ignoreTag;
      this.locale = options.locale;
      this.requiresOtherClause = !!options.requiresOtherClause;
      this.shouldParseSkeletons = !!options.shouldParseSkeletons;
    }
    Parser2.prototype.parse = function() {
      if (this.offset() !== 0) {
        throw Error("parser can only be used once");
      }
      return this.parseMessage(0, "", false);
    };
    Parser2.prototype.parseMessage = function(nestingLevel, parentArgType, expectingCloseTag) {
      var elements = [];
      while (!this.isEOF()) {
        var char = this.char();
        if (char === 123) {
          var result = this.parseArgument(nestingLevel, expectingCloseTag);
          if (result.err) {
            return result;
          }
          elements.push(result.val);
        } else if (char === 125 && nestingLevel > 0) {
          break;
        } else if (char === 35 && (parentArgType === "plural" || parentArgType === "selectordinal")) {
          var position = this.clonePosition();
          this.bump();
          elements.push({
            type: TYPE.pound,
            location: createLocation(position, this.clonePosition())
          });
        } else if (char === 60 && !this.ignoreTag && this.peek() === 47) {
          if (expectingCloseTag) {
            break;
          } else {
            return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));
          }
        } else if (char === 60 && !this.ignoreTag && _isAlpha(this.peek() || 0)) {
          var result = this.parseTag(nestingLevel, parentArgType);
          if (result.err) {
            return result;
          }
          elements.push(result.val);
        } else {
          var result = this.parseLiteral(nestingLevel, parentArgType);
          if (result.err) {
            return result;
          }
          elements.push(result.val);
        }
      }
      return { val: elements, err: null };
    };
    Parser2.prototype.parseTag = function(nestingLevel, parentArgType) {
      var startPosition = this.clonePosition();
      this.bump();
      var tagName = this.parseTagName();
      this.bumpSpace();
      if (this.bumpIf("/>")) {
        return {
          val: {
            type: TYPE.literal,
            value: "<".concat(tagName, "/>"),
            location: createLocation(startPosition, this.clonePosition())
          },
          err: null
        };
      } else if (this.bumpIf(">")) {
        var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);
        if (childrenResult.err) {
          return childrenResult;
        }
        var children = childrenResult.val;
        var endTagStartPosition = this.clonePosition();
        if (this.bumpIf("</")) {
          if (this.isEOF() || !_isAlpha(this.char())) {
            return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));
          }
          var closingTagNameStartPosition = this.clonePosition();
          var closingTagName = this.parseTagName();
          if (tagName !== closingTagName) {
            return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));
          }
          this.bumpSpace();
          if (!this.bumpIf(">")) {
            return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));
          }
          return {
            val: {
              type: TYPE.tag,
              value: tagName,
              children,
              location: createLocation(startPosition, this.clonePosition())
            },
            err: null
          };
        } else {
          return this.error(ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));
        }
      } else {
        return this.error(ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));
      }
    };
    Parser2.prototype.parseTagName = function() {
      var startOffset = this.offset();
      this.bump();
      while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {
        this.bump();
      }
      return this.message.slice(startOffset, this.offset());
    };
    Parser2.prototype.parseLiteral = function(nestingLevel, parentArgType) {
      var start = this.clonePosition();
      var value = "";
      while (true) {
        var parseQuoteResult = this.tryParseQuote(parentArgType);
        if (parseQuoteResult) {
          value += parseQuoteResult;
          continue;
        }
        var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);
        if (parseUnquotedResult) {
          value += parseUnquotedResult;
          continue;
        }
        var parseLeftAngleResult = this.tryParseLeftAngleBracket();
        if (parseLeftAngleResult) {
          value += parseLeftAngleResult;
          continue;
        }
        break;
      }
      var location = createLocation(start, this.clonePosition());
      return {
        val: { type: TYPE.literal, value, location },
        err: null
      };
    };
    Parser2.prototype.tryParseLeftAngleBracket = function() {
      if (!this.isEOF() && this.char() === 60 && (this.ignoreTag || // If at the opening tag or closing tag position, bail.
      !_isAlphaOrSlash(this.peek() || 0))) {
        this.bump();
        return "<";
      }
      return null;
    };
    Parser2.prototype.tryParseQuote = function(parentArgType) {
      if (this.isEOF() || this.char() !== 39) {
        return null;
      }
      switch (this.peek()) {
        case 39:
          this.bump();
          this.bump();
          return "'";
        case 123:
        case 60:
        case 62:
        case 125:
          break;
        case 35:
          if (parentArgType === "plural" || parentArgType === "selectordinal") {
            break;
          }
          return null;
        default:
          return null;
      }
      this.bump();
      var codePoints = [this.char()];
      this.bump();
      while (!this.isEOF()) {
        var ch = this.char();
        if (ch === 39) {
          if (this.peek() === 39) {
            codePoints.push(39);
            this.bump();
          } else {
            this.bump();
            break;
          }
        } else {
          codePoints.push(ch);
        }
        this.bump();
      }
      return fromCodePoint.apply(void 0, codePoints);
    };
    Parser2.prototype.tryParseUnquoted = function(nestingLevel, parentArgType) {
      if (this.isEOF()) {
        return null;
      }
      var ch = this.char();
      if (ch === 60 || ch === 123 || ch === 35 && (parentArgType === "plural" || parentArgType === "selectordinal") || ch === 125 && nestingLevel > 0) {
        return null;
      } else {
        this.bump();
        return fromCodePoint(ch);
      }
    };
    Parser2.prototype.parseArgument = function(nestingLevel, expectingCloseTag) {
      var openingBracePosition = this.clonePosition();
      this.bump();
      this.bumpSpace();
      if (this.isEOF()) {
        return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));
      }
      if (this.char() === 125) {
        this.bump();
        return this.error(ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));
      }
      var value = this.parseIdentifierIfPossible().value;
      if (!value) {
        return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));
      }
      this.bumpSpace();
      if (this.isEOF()) {
        return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));
      }
      switch (this.char()) {
        case 125: {
          this.bump();
          return {
            val: {
              type: TYPE.argument,
              // value does not include the opening and closing braces.
              value,
              location: createLocation(openingBracePosition, this.clonePosition())
            },
            err: null
          };
        }
        case 44: {
          this.bump();
          this.bumpSpace();
          if (this.isEOF()) {
            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));
          }
          return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);
        }
        default:
          return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));
      }
    };
    Parser2.prototype.parseIdentifierIfPossible = function() {
      var startingPosition = this.clonePosition();
      var startOffset = this.offset();
      var value = matchIdentifierAtIndex(this.message, startOffset);
      var endOffset = startOffset + value.length;
      this.bumpTo(endOffset);
      var endPosition = this.clonePosition();
      var location = createLocation(startingPosition, endPosition);
      return { value, location };
    };
    Parser2.prototype.parseArgumentOptions = function(nestingLevel, expectingCloseTag, value, openingBracePosition) {
      var _a2;
      var typeStartPosition = this.clonePosition();
      var argType = this.parseIdentifierIfPossible().value;
      var typeEndPosition = this.clonePosition();
      switch (argType) {
        case "":
          return this.error(ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));
        case "number":
        case "date":
        case "time": {
          this.bumpSpace();
          var styleAndLocation = null;
          if (this.bumpIf(",")) {
            this.bumpSpace();
            var styleStartPosition = this.clonePosition();
            var result = this.parseSimpleArgStyleIfPossible();
            if (result.err) {
              return result;
            }
            var style = trimEnd(result.val);
            if (style.length === 0) {
              return this.error(ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));
            }
            var styleLocation = createLocation(styleStartPosition, this.clonePosition());
            styleAndLocation = { style, styleLocation };
          }
          var argCloseResult = this.tryParseArgumentClose(openingBracePosition);
          if (argCloseResult.err) {
            return argCloseResult;
          }
          var location_1 = createLocation(openingBracePosition, this.clonePosition());
          if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, "::", 0)) {
            var skeleton = trimStart(styleAndLocation.style.slice(2));
            if (argType === "number") {
              var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);
              if (result.err) {
                return result;
              }
              return {
                val: { type: TYPE.number, value, location: location_1, style: result.val },
                err: null
              };
            } else {
              if (skeleton.length === 0) {
                return this.error(ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);
              }
              var dateTimePattern = skeleton;
              if (this.locale) {
                dateTimePattern = getBestPattern(skeleton, this.locale);
              }
              var style = {
                type: SKELETON_TYPE.dateTime,
                pattern: dateTimePattern,
                location: styleAndLocation.styleLocation,
                parsedOptions: this.shouldParseSkeletons ? parseDateTimeSkeleton(dateTimePattern) : {}
              };
              var type = argType === "date" ? TYPE.date : TYPE.time;
              return {
                val: { type, value, location: location_1, style },
                err: null
              };
            }
          }
          return {
            val: {
              type: argType === "number" ? TYPE.number : argType === "date" ? TYPE.date : TYPE.time,
              value,
              location: location_1,
              style: (_a2 = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a2 !== void 0 ? _a2 : null
            },
            err: null
          };
        }
        case "plural":
        case "selectordinal":
        case "select": {
          var typeEndPosition_1 = this.clonePosition();
          this.bumpSpace();
          if (!this.bumpIf(",")) {
            return this.error(ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, __assign({}, typeEndPosition_1)));
          }
          this.bumpSpace();
          var identifierAndLocation = this.parseIdentifierIfPossible();
          var pluralOffset = 0;
          if (argType !== "select" && identifierAndLocation.value === "offset") {
            if (!this.bumpIf(":")) {
              return this.error(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));
            }
            this.bumpSpace();
            var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);
            if (result.err) {
              return result;
            }
            this.bumpSpace();
            identifierAndLocation = this.parseIdentifierIfPossible();
            pluralOffset = result.val;
          }
          var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);
          if (optionsResult.err) {
            return optionsResult;
          }
          var argCloseResult = this.tryParseArgumentClose(openingBracePosition);
          if (argCloseResult.err) {
            return argCloseResult;
          }
          var location_2 = createLocation(openingBracePosition, this.clonePosition());
          if (argType === "select") {
            return {
              val: {
                type: TYPE.select,
                value,
                options: fromEntries(optionsResult.val),
                location: location_2
              },
              err: null
            };
          } else {
            return {
              val: {
                type: TYPE.plural,
                value,
                options: fromEntries(optionsResult.val),
                offset: pluralOffset,
                pluralType: argType === "plural" ? "cardinal" : "ordinal",
                location: location_2
              },
              err: null
            };
          }
        }
        default:
          return this.error(ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));
      }
    };
    Parser2.prototype.tryParseArgumentClose = function(openingBracePosition) {
      if (this.isEOF() || this.char() !== 125) {
        return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));
      }
      this.bump();
      return { val: true, err: null };
    };
    Parser2.prototype.parseSimpleArgStyleIfPossible = function() {
      var nestedBraces = 0;
      var startPosition = this.clonePosition();
      while (!this.isEOF()) {
        var ch = this.char();
        switch (ch) {
          case 39: {
            this.bump();
            var apostrophePosition = this.clonePosition();
            if (!this.bumpUntil("'")) {
              return this.error(ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));
            }
            this.bump();
            break;
          }
          case 123: {
            nestedBraces += 1;
            this.bump();
            break;
          }
          case 125: {
            if (nestedBraces > 0) {
              nestedBraces -= 1;
            } else {
              return {
                val: this.message.slice(startPosition.offset, this.offset()),
                err: null
              };
            }
            break;
          }
          default:
            this.bump();
            break;
        }
      }
      return {
        val: this.message.slice(startPosition.offset, this.offset()),
        err: null
      };
    };
    Parser2.prototype.parseNumberSkeletonFromString = function(skeleton, location) {
      var tokens = [];
      try {
        tokens = parseNumberSkeletonFromString(skeleton);
      } catch (e) {
        return this.error(ErrorKind.INVALID_NUMBER_SKELETON, location);
      }
      return {
        val: {
          type: SKELETON_TYPE.number,
          tokens,
          location,
          parsedOptions: this.shouldParseSkeletons ? parseNumberSkeleton(tokens) : {}
        },
        err: null
      };
    };
    Parser2.prototype.tryParsePluralOrSelectOptions = function(nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {
      var _a2;
      var hasOtherClause = false;
      var options = [];
      var parsedSelectors = /* @__PURE__ */ new Set();
      var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;
      while (true) {
        if (selector.length === 0) {
          var startPosition = this.clonePosition();
          if (parentArgType !== "select" && this.bumpIf("=")) {
            var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);
            if (result.err) {
              return result;
            }
            selectorLocation = createLocation(startPosition, this.clonePosition());
            selector = this.message.slice(startPosition.offset, this.offset());
          } else {
            break;
          }
        }
        if (parsedSelectors.has(selector)) {
          return this.error(parentArgType === "select" ? ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR : ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);
        }
        if (selector === "other") {
          hasOtherClause = true;
        }
        this.bumpSpace();
        var openingBracePosition = this.clonePosition();
        if (!this.bumpIf("{")) {
          return this.error(parentArgType === "select" ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));
        }
        var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);
        if (fragmentResult.err) {
          return fragmentResult;
        }
        var argCloseResult = this.tryParseArgumentClose(openingBracePosition);
        if (argCloseResult.err) {
          return argCloseResult;
        }
        options.push([
          selector,
          {
            value: fragmentResult.val,
            location: createLocation(openingBracePosition, this.clonePosition())
          }
        ]);
        parsedSelectors.add(selector);
        this.bumpSpace();
        _a2 = this.parseIdentifierIfPossible(), selector = _a2.value, selectorLocation = _a2.location;
      }
      if (options.length === 0) {
        return this.error(parentArgType === "select" ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));
      }
      if (this.requiresOtherClause && !hasOtherClause) {
        return this.error(ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));
      }
      return { val: options, err: null };
    };
    Parser2.prototype.tryParseDecimalInteger = function(expectNumberError, invalidNumberError) {
      var sign = 1;
      var startingPosition = this.clonePosition();
      if (this.bumpIf("+")) {
      } else if (this.bumpIf("-")) {
        sign = -1;
      }
      var hasDigits = false;
      var decimal = 0;
      while (!this.isEOF()) {
        var ch = this.char();
        if (ch >= 48 && ch <= 57) {
          hasDigits = true;
          decimal = decimal * 10 + (ch - 48);
          this.bump();
        } else {
          break;
        }
      }
      var location = createLocation(startingPosition, this.clonePosition());
      if (!hasDigits) {
        return this.error(expectNumberError, location);
      }
      decimal *= sign;
      if (!isSafeInteger(decimal)) {
        return this.error(invalidNumberError, location);
      }
      return { val: decimal, err: null };
    };
    Parser2.prototype.offset = function() {
      return this.position.offset;
    };
    Parser2.prototype.isEOF = function() {
      return this.offset() === this.message.length;
    };
    Parser2.prototype.clonePosition = function() {
      return {
        offset: this.position.offset,
        line: this.position.line,
        column: this.position.column
      };
    };
    Parser2.prototype.char = function() {
      var offset = this.position.offset;
      if (offset >= this.message.length) {
        throw Error("out of bound");
      }
      var code = codePointAt(this.message, offset);
      if (code === void 0) {
        throw Error("Offset ".concat(offset, " is at invalid UTF-16 code unit boundary"));
      }
      return code;
    };
    Parser2.prototype.error = function(kind, location) {
      return {
        val: null,
        err: {
          kind,
          message: this.message,
          location
        }
      };
    };
    Parser2.prototype.bump = function() {
      if (this.isEOF()) {
        return;
      }
      var code = this.char();
      if (code === 10) {
        this.position.line += 1;
        this.position.column = 1;
        this.position.offset += 1;
      } else {
        this.position.column += 1;
        this.position.offset += code < 65536 ? 1 : 2;
      }
    };
    Parser2.prototype.bumpIf = function(prefix) {
      if (startsWith(this.message, prefix, this.offset())) {
        for (var i = 0; i < prefix.length; i++) {
          this.bump();
        }
        return true;
      }
      return false;
    };
    Parser2.prototype.bumpUntil = function(pattern) {
      var currentOffset = this.offset();
      var index2 = this.message.indexOf(pattern, currentOffset);
      if (index2 >= 0) {
        this.bumpTo(index2);
        return true;
      } else {
        this.bumpTo(this.message.length);
        return false;
      }
    };
    Parser2.prototype.bumpTo = function(targetOffset) {
      if (this.offset() > targetOffset) {
        throw Error("targetOffset ".concat(targetOffset, " must be greater than or equal to the current offset ").concat(this.offset()));
      }
      targetOffset = Math.min(targetOffset, this.message.length);
      while (true) {
        var offset = this.offset();
        if (offset === targetOffset) {
          break;
        }
        if (offset > targetOffset) {
          throw Error("targetOffset ".concat(targetOffset, " is at invalid UTF-16 code unit boundary"));
        }
        this.bump();
        if (this.isEOF()) {
          break;
        }
      }
    };
    Parser2.prototype.bumpSpace = function() {
      while (!this.isEOF() && _isWhiteSpace(this.char())) {
        this.bump();
      }
    };
    Parser2.prototype.peek = function() {
      if (this.isEOF()) {
        return null;
      }
      var code = this.char();
      var offset = this.offset();
      var nextCode = this.message.charCodeAt(offset + (code >= 65536 ? 2 : 1));
      return nextCode !== null && nextCode !== void 0 ? nextCode : null;
    };
    return Parser2;
  }()
);
function _isAlpha(codepoint) {
  return codepoint >= 97 && codepoint <= 122 || codepoint >= 65 && codepoint <= 90;
}
function _isAlphaOrSlash(codepoint) {
  return _isAlpha(codepoint) || codepoint === 47;
}
function _isPotentialElementNameChar(c) {
  return c === 45 || c === 46 || c >= 48 && c <= 57 || c === 95 || c >= 97 && c <= 122 || c >= 65 && c <= 90 || c == 183 || c >= 192 && c <= 214 || c >= 216 && c <= 246 || c >= 248 && c <= 893 || c >= 895 && c <= 8191 || c >= 8204 && c <= 8205 || c >= 8255 && c <= 8256 || c >= 8304 && c <= 8591 || c >= 11264 && c <= 12271 || c >= 12289 && c <= 55295 || c >= 63744 && c <= 64975 || c >= 65008 && c <= 65533 || c >= 65536 && c <= 983039;
}
function _isWhiteSpace(c) {
  return c >= 9 && c <= 13 || c === 32 || c === 133 || c >= 8206 && c <= 8207 || c === 8232 || c === 8233;
}
function _isPatternSyntax(c) {
  return c >= 33 && c <= 35 || c === 36 || c >= 37 && c <= 39 || c === 40 || c === 41 || c === 42 || c === 43 || c === 44 || c === 45 || c >= 46 && c <= 47 || c >= 58 && c <= 59 || c >= 60 && c <= 62 || c >= 63 && c <= 64 || c === 91 || c === 92 || c === 93 || c === 94 || c === 96 || c === 123 || c === 124 || c === 125 || c === 126 || c === 161 || c >= 162 && c <= 165 || c === 166 || c === 167 || c === 169 || c === 171 || c === 172 || c === 174 || c === 176 || c === 177 || c === 182 || c === 187 || c === 191 || c === 215 || c === 247 || c >= 8208 && c <= 8213 || c >= 8214 && c <= 8215 || c === 8216 || c === 8217 || c === 8218 || c >= 8219 && c <= 8220 || c === 8221 || c === 8222 || c === 8223 || c >= 8224 && c <= 8231 || c >= 8240 && c <= 8248 || c === 8249 || c === 8250 || c >= 8251 && c <= 8254 || c >= 8257 && c <= 8259 || c === 8260 || c === 8261 || c === 8262 || c >= 8263 && c <= 8273 || c === 8274 || c === 8275 || c >= 8277 && c <= 8286 || c >= 8592 && c <= 8596 || c >= 8597 && c <= 8601 || c >= 8602 && c <= 8603 || c >= 8604 && c <= 8607 || c === 8608 || c >= 8609 && c <= 8610 || c === 8611 || c >= 8612 && c <= 8613 || c === 8614 || c >= 8615 && c <= 8621 || c === 8622 || c >= 8623 && c <= 8653 || c >= 8654 && c <= 8655 || c >= 8656 && c <= 8657 || c === 8658 || c === 8659 || c === 8660 || c >= 8661 && c <= 8691 || c >= 8692 && c <= 8959 || c >= 8960 && c <= 8967 || c === 8968 || c === 8969 || c === 8970 || c === 8971 || c >= 8972 && c <= 8991 || c >= 8992 && c <= 8993 || c >= 8994 && c <= 9e3 || c === 9001 || c === 9002 || c >= 9003 && c <= 9083 || c === 9084 || c >= 9085 && c <= 9114 || c >= 9115 && c <= 9139 || c >= 9140 && c <= 9179 || c >= 9180 && c <= 9185 || c >= 9186 && c <= 9254 || c >= 9255 && c <= 9279 || c >= 9280 && c <= 9290 || c >= 9291 && c <= 9311 || c >= 9472 && c <= 9654 || c === 9655 || c >= 9656 && c <= 9664 || c === 9665 || c >= 9666 && c <= 9719 || c >= 9720 && c <= 9727 || c >= 9728 && c <= 9838 || c === 9839 || c >= 9840 && c <= 10087 || c === 10088 || c === 10089 || c === 10090 || c === 10091 || c === 10092 || c === 10093 || c === 10094 || c === 10095 || c === 10096 || c === 10097 || c === 10098 || c === 10099 || c === 10100 || c === 10101 || c >= 10132 && c <= 10175 || c >= 10176 && c <= 10180 || c === 10181 || c === 10182 || c >= 10183 && c <= 10213 || c === 10214 || c === 10215 || c === 10216 || c === 10217 || c === 10218 || c === 10219 || c === 10220 || c === 10221 || c === 10222 || c === 10223 || c >= 10224 && c <= 10239 || c >= 10240 && c <= 10495 || c >= 10496 && c <= 10626 || c === 10627 || c === 10628 || c === 10629 || c === 10630 || c === 10631 || c === 10632 || c === 10633 || c === 10634 || c === 10635 || c === 10636 || c === 10637 || c === 10638 || c === 10639 || c === 10640 || c === 10641 || c === 10642 || c === 10643 || c === 10644 || c === 10645 || c === 10646 || c === 10647 || c === 10648 || c >= 10649 && c <= 10711 || c === 10712 || c === 10713 || c === 10714 || c === 10715 || c >= 10716 && c <= 10747 || c === 10748 || c === 10749 || c >= 10750 && c <= 11007 || c >= 11008 && c <= 11055 || c >= 11056 && c <= 11076 || c >= 11077 && c <= 11078 || c >= 11079 && c <= 11084 || c >= 11085 && c <= 11123 || c >= 11124 && c <= 11125 || c >= 11126 && c <= 11157 || c === 11158 || c >= 11159 && c <= 11263 || c >= 11776 && c <= 11777 || c === 11778 || c === 11779 || c === 11780 || c === 11781 || c >= 11782 && c <= 11784 || c === 11785 || c === 11786 || c === 11787 || c === 11788 || c === 11789 || c >= 11790 && c <= 11798 || c === 11799 || c >= 11800 && c <= 11801 || c === 11802 || c === 11803 || c === 11804 || c === 11805 || c >= 11806 && c <= 11807 || c === 11808 || c === 11809 || c === 11810 || c === 11811 || c === 11812 || c === 11813 || c === 11814 || c === 11815 || c === 11816 || c === 11817 || c >= 11818 && c <= 11822 || c === 11823 || c >= 11824 && c <= 11833 || c >= 11834 && c <= 11835 || c >= 11836 && c <= 11839 || c === 11840 || c === 11841 || c === 11842 || c >= 11843 && c <= 11855 || c >= 11856 && c <= 11857 || c === 11858 || c >= 11859 && c <= 11903 || c >= 12289 && c <= 12291 || c === 12296 || c === 12297 || c === 12298 || c === 12299 || c === 12300 || c === 12301 || c === 12302 || c === 12303 || c === 12304 || c === 12305 || c >= 12306 && c <= 12307 || c === 12308 || c === 12309 || c === 12310 || c === 12311 || c === 12312 || c === 12313 || c === 12314 || c === 12315 || c === 12316 || c === 12317 || c >= 12318 && c <= 12319 || c === 12320 || c === 12336 || c === 64830 || c === 64831 || c >= 65093 && c <= 65094;
}

// node_modules/@strapi/i18n/node_modules/@formatjs/icu-messageformat-parser/lib/index.js
function pruneLocation(els) {
  els.forEach(function(el) {
    delete el.location;
    if (isSelectElement(el) || isPluralElement(el)) {
      for (var k in el.options) {
        delete el.options[k].location;
        pruneLocation(el.options[k].value);
      }
    } else if (isNumberElement(el) && isNumberSkeleton(el.style)) {
      delete el.style.location;
    } else if ((isDateElement(el) || isTimeElement(el)) && isDateTimeSkeleton(el.style)) {
      delete el.style.location;
    } else if (isTagElement(el)) {
      pruneLocation(el.children);
    }
  });
}
function parse(message, opts) {
  if (opts === void 0) {
    opts = {};
  }
  opts = __assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);
  var result = new Parser(message, opts).parse();
  if (result.err) {
    var error = SyntaxError(ErrorKind[result.err.kind]);
    error.location = result.err.location;
    error.originalMessage = result.err.message;
    throw error;
  }
  if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {
    pruneLocation(result.val);
  }
  return result.val;
}

// node_modules/@strapi/i18n/node_modules/@formatjs/fast-memoize/lib/index.js
function memoize(fn, options) {
  var cache = options && options.cache ? options.cache : cacheDefault;
  var serializer = options && options.serializer ? options.serializer : serializerDefault;
  var strategy = options && options.strategy ? options.strategy : strategyDefault;
  return strategy(fn, {
    cache,
    serializer
  });
}
function isPrimitive(value) {
  return value == null || typeof value === "number" || typeof value === "boolean";
}
function monadic(fn, cache, serializer, arg) {
  var cacheKey = isPrimitive(arg) ? arg : serializer(arg);
  var computedValue = cache.get(cacheKey);
  if (typeof computedValue === "undefined") {
    computedValue = fn.call(this, arg);
    cache.set(cacheKey, computedValue);
  }
  return computedValue;
}
function variadic(fn, cache, serializer) {
  var args = Array.prototype.slice.call(arguments, 3);
  var cacheKey = serializer(args);
  var computedValue = cache.get(cacheKey);
  if (typeof computedValue === "undefined") {
    computedValue = fn.apply(this, args);
    cache.set(cacheKey, computedValue);
  }
  return computedValue;
}
function assemble(fn, context, strategy, cache, serialize) {
  return strategy.bind(context, fn, cache, serialize);
}
function strategyDefault(fn, options) {
  var strategy = fn.length === 1 ? monadic : variadic;
  return assemble(fn, this, strategy, options.cache.create(), options.serializer);
}
function strategyVariadic(fn, options) {
  return assemble(fn, this, variadic, options.cache.create(), options.serializer);
}
function strategyMonadic(fn, options) {
  return assemble(fn, this, monadic, options.cache.create(), options.serializer);
}
var serializerDefault = function() {
  return JSON.stringify(arguments);
};
function ObjectWithoutPrototypeCache() {
  this.cache = /* @__PURE__ */ Object.create(null);
}
ObjectWithoutPrototypeCache.prototype.get = function(key) {
  return this.cache[key];
};
ObjectWithoutPrototypeCache.prototype.set = function(key, value) {
  this.cache[key] = value;
};
var cacheDefault = {
  create: function create3() {
    return new ObjectWithoutPrototypeCache();
  }
};
var strategies = {
  variadic: strategyVariadic,
  monadic: strategyMonadic
};

// node_modules/@strapi/i18n/node_modules/intl-messageformat/lib/src/error.js
var ErrorCode;
(function(ErrorCode2) {
  ErrorCode2["MISSING_VALUE"] = "MISSING_VALUE";
  ErrorCode2["INVALID_VALUE"] = "INVALID_VALUE";
  ErrorCode2["MISSING_INTL_API"] = "MISSING_INTL_API";
})(ErrorCode || (ErrorCode = {}));
var FormatError = (
  /** @class */
  function(_super) {
    __extends(FormatError2, _super);
    function FormatError2(msg, code, originalMessage) {
      var _this = _super.call(this, msg) || this;
      _this.code = code;
      _this.originalMessage = originalMessage;
      return _this;
    }
    FormatError2.prototype.toString = function() {
      return "[formatjs Error: ".concat(this.code, "] ").concat(this.message);
    };
    return FormatError2;
  }(Error)
);
var InvalidValueError = (
  /** @class */
  function(_super) {
    __extends(InvalidValueError2, _super);
    function InvalidValueError2(variableId, value, options, originalMessage) {
      return _super.call(this, 'Invalid values for "'.concat(variableId, '": "').concat(value, '". Options are "').concat(Object.keys(options).join('", "'), '"'), ErrorCode.INVALID_VALUE, originalMessage) || this;
    }
    return InvalidValueError2;
  }(FormatError)
);
var InvalidValueTypeError = (
  /** @class */
  function(_super) {
    __extends(InvalidValueTypeError2, _super);
    function InvalidValueTypeError2(value, type, originalMessage) {
      return _super.call(this, 'Value for "'.concat(value, '" must be of type ').concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;
    }
    return InvalidValueTypeError2;
  }(FormatError)
);
var MissingValueError = (
  /** @class */
  function(_super) {
    __extends(MissingValueError2, _super);
    function MissingValueError2(variableId, originalMessage) {
      return _super.call(this, 'The intl string context variable "'.concat(variableId, '" was not provided to the string "').concat(originalMessage, '"'), ErrorCode.MISSING_VALUE, originalMessage) || this;
    }
    return MissingValueError2;
  }(FormatError)
);

// node_modules/@strapi/i18n/node_modules/intl-messageformat/lib/src/formatters.js
var PART_TYPE;
(function(PART_TYPE2) {
  PART_TYPE2[PART_TYPE2["literal"] = 0] = "literal";
  PART_TYPE2[PART_TYPE2["object"] = 1] = "object";
})(PART_TYPE || (PART_TYPE = {}));
function mergeLiteral(parts) {
  if (parts.length < 2) {
    return parts;
  }
  return parts.reduce(function(all, part) {
    var lastPart = all[all.length - 1];
    if (!lastPart || lastPart.type !== PART_TYPE.literal || part.type !== PART_TYPE.literal) {
      all.push(part);
    } else {
      lastPart.value += part.value;
    }
    return all;
  }, []);
}
function isFormatXMLElementFn(el) {
  return typeof el === "function";
}
function formatToParts2(els, locales, formatters, formats, values, currentPluralValue, originalMessage) {
  if (els.length === 1 && isLiteralElement(els[0])) {
    return [
      {
        type: PART_TYPE.literal,
        value: els[0].value
      }
    ];
  }
  var result = [];
  for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {
    var el = els_1[_i];
    if (isLiteralElement(el)) {
      result.push({
        type: PART_TYPE.literal,
        value: el.value
      });
      continue;
    }
    if (isPoundElement(el)) {
      if (typeof currentPluralValue === "number") {
        result.push({
          type: PART_TYPE.literal,
          value: formatters.getNumberFormat(locales).format(currentPluralValue)
        });
      }
      continue;
    }
    var varName = el.value;
    if (!(values && varName in values)) {
      throw new MissingValueError(varName, originalMessage);
    }
    var value = values[varName];
    if (isArgumentElement(el)) {
      if (!value || typeof value === "string" || typeof value === "number") {
        value = typeof value === "string" || typeof value === "number" ? String(value) : "";
      }
      result.push({
        type: typeof value === "string" ? PART_TYPE.literal : PART_TYPE.object,
        value
      });
      continue;
    }
    if (isDateElement(el)) {
      var style = typeof el.style === "string" ? formats.date[el.style] : isDateTimeSkeleton(el.style) ? el.style.parsedOptions : void 0;
      result.push({
        type: PART_TYPE.literal,
        value: formatters.getDateTimeFormat(locales, style).format(value)
      });
      continue;
    }
    if (isTimeElement(el)) {
      var style = typeof el.style === "string" ? formats.time[el.style] : isDateTimeSkeleton(el.style) ? el.style.parsedOptions : formats.time.medium;
      result.push({
        type: PART_TYPE.literal,
        value: formatters.getDateTimeFormat(locales, style).format(value)
      });
      continue;
    }
    if (isNumberElement(el)) {
      var style = typeof el.style === "string" ? formats.number[el.style] : isNumberSkeleton(el.style) ? el.style.parsedOptions : void 0;
      if (style && style.scale) {
        value = value * (style.scale || 1);
      }
      result.push({
        type: PART_TYPE.literal,
        value: formatters.getNumberFormat(locales, style).format(value)
      });
      continue;
    }
    if (isTagElement(el)) {
      var children = el.children, value_1 = el.value;
      var formatFn = values[value_1];
      if (!isFormatXMLElementFn(formatFn)) {
        throw new InvalidValueTypeError(value_1, "function", originalMessage);
      }
      var parts = formatToParts2(children, locales, formatters, formats, values, currentPluralValue);
      var chunks = formatFn(parts.map(function(p) {
        return p.value;
      }));
      if (!Array.isArray(chunks)) {
        chunks = [chunks];
      }
      result.push.apply(result, chunks.map(function(c) {
        return {
          type: typeof c === "string" ? PART_TYPE.literal : PART_TYPE.object,
          value: c
        };
      }));
    }
    if (isSelectElement(el)) {
      var opt = el.options[value] || el.options.other;
      if (!opt) {
        throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);
      }
      result.push.apply(result, formatToParts2(opt.value, locales, formatters, formats, values));
      continue;
    }
    if (isPluralElement(el)) {
      var opt = el.options["=".concat(value)];
      if (!opt) {
        if (!Intl.PluralRules) {
          throw new FormatError('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n', ErrorCode.MISSING_INTL_API, originalMessage);
        }
        var rule = formatters.getPluralRules(locales, { type: el.pluralType }).select(value - (el.offset || 0));
        opt = el.options[rule] || el.options.other;
      }
      if (!opt) {
        throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);
      }
      result.push.apply(result, formatToParts2(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));
      continue;
    }
  }
  return mergeLiteral(result);
}

// node_modules/@strapi/i18n/node_modules/intl-messageformat/lib/src/core.js
function mergeConfig(c1, c2) {
  if (!c2) {
    return c1;
  }
  return __assign(__assign(__assign({}, c1 || {}), c2 || {}), Object.keys(c1).reduce(function(all, k) {
    all[k] = __assign(__assign({}, c1[k]), c2[k] || {});
    return all;
  }, {}));
}
function mergeConfigs(defaultConfig, configs) {
  if (!configs) {
    return defaultConfig;
  }
  return Object.keys(defaultConfig).reduce(function(all, k) {
    all[k] = mergeConfig(defaultConfig[k], configs[k]);
    return all;
  }, __assign({}, defaultConfig));
}
function createFastMemoizeCache(store) {
  return {
    create: function() {
      return {
        get: function(key) {
          return store[key];
        },
        set: function(key, value) {
          store[key] = value;
        }
      };
    }
  };
}
function createDefaultFormatters(cache) {
  if (cache === void 0) {
    cache = {
      number: {},
      dateTime: {},
      pluralRules: {}
    };
  }
  return {
    getNumberFormat: memoize(function() {
      var _a2;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return new ((_a2 = Intl.NumberFormat).bind.apply(_a2, __spreadArray([void 0], args, false)))();
    }, {
      cache: createFastMemoizeCache(cache.number),
      strategy: strategies.variadic
    }),
    getDateTimeFormat: memoize(function() {
      var _a2;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return new ((_a2 = Intl.DateTimeFormat).bind.apply(_a2, __spreadArray([void 0], args, false)))();
    }, {
      cache: createFastMemoizeCache(cache.dateTime),
      strategy: strategies.variadic
    }),
    getPluralRules: memoize(function() {
      var _a2;
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return new ((_a2 = Intl.PluralRules).bind.apply(_a2, __spreadArray([void 0], args, false)))();
    }, {
      cache: createFastMemoizeCache(cache.pluralRules),
      strategy: strategies.variadic
    })
  };
}
var IntlMessageFormat = (
  /** @class */
  function() {
    function IntlMessageFormat2(message, locales, overrideFormats, opts) {
      var _this = this;
      if (locales === void 0) {
        locales = IntlMessageFormat2.defaultLocale;
      }
      this.formatterCache = {
        number: {},
        dateTime: {},
        pluralRules: {}
      };
      this.format = function(values) {
        var parts = _this.formatToParts(values);
        if (parts.length === 1) {
          return parts[0].value;
        }
        var result = parts.reduce(function(all, part) {
          if (!all.length || part.type !== PART_TYPE.literal || typeof all[all.length - 1] !== "string") {
            all.push(part.value);
          } else {
            all[all.length - 1] += part.value;
          }
          return all;
        }, []);
        if (result.length <= 1) {
          return result[0] || "";
        }
        return result;
      };
      this.formatToParts = function(values) {
        return formatToParts2(_this.ast, _this.locales, _this.formatters, _this.formats, values, void 0, _this.message);
      };
      this.resolvedOptions = function() {
        var _a3;
        return {
          locale: ((_a3 = _this.resolvedLocale) === null || _a3 === void 0 ? void 0 : _a3.toString()) || Intl.NumberFormat.supportedLocalesOf(_this.locales)[0]
        };
      };
      this.getAst = function() {
        return _this.ast;
      };
      this.locales = locales;
      this.resolvedLocale = IntlMessageFormat2.resolveLocale(locales);
      if (typeof message === "string") {
        this.message = message;
        if (!IntlMessageFormat2.__parse) {
          throw new TypeError("IntlMessageFormat.__parse must be set to process `message` of type `string`");
        }
        var _a2 = opts || {}, formatters = _a2.formatters, parseOpts = __rest(_a2, ["formatters"]);
        this.ast = IntlMessageFormat2.__parse(message, __assign(__assign({}, parseOpts), { locale: this.resolvedLocale }));
      } else {
        this.ast = message;
      }
      if (!Array.isArray(this.ast)) {
        throw new TypeError("A message must be provided as a String or AST.");
      }
      this.formats = mergeConfigs(IntlMessageFormat2.formats, overrideFormats);
      this.formatters = opts && opts.formatters || createDefaultFormatters(this.formatterCache);
    }
    Object.defineProperty(IntlMessageFormat2, "defaultLocale", {
      get: function() {
        if (!IntlMessageFormat2.memoizedDefaultLocale) {
          IntlMessageFormat2.memoizedDefaultLocale = new Intl.NumberFormat().resolvedOptions().locale;
        }
        return IntlMessageFormat2.memoizedDefaultLocale;
      },
      enumerable: false,
      configurable: true
    });
    IntlMessageFormat2.memoizedDefaultLocale = null;
    IntlMessageFormat2.resolveLocale = function(locales) {
      if (typeof Intl.Locale === "undefined") {
        return;
      }
      var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);
      if (supportedLocales.length > 0) {
        return new Intl.Locale(supportedLocales[0]);
      }
      return new Intl.Locale(typeof locales === "string" ? locales : locales[0]);
    };
    IntlMessageFormat2.__parse = parse;
    IntlMessageFormat2.formats = {
      number: {
        integer: {
          maximumFractionDigits: 0
        },
        currency: {
          style: "currency"
        },
        percent: {
          style: "percent"
        }
      },
      date: {
        short: {
          month: "numeric",
          day: "numeric",
          year: "2-digit"
        },
        medium: {
          month: "short",
          day: "numeric",
          year: "numeric"
        },
        long: {
          month: "long",
          day: "numeric",
          year: "numeric"
        },
        full: {
          weekday: "long",
          month: "long",
          day: "numeric",
          year: "numeric"
        }
      },
      time: {
        short: {
          hour: "numeric",
          minute: "numeric"
        },
        medium: {
          hour: "numeric",
          minute: "numeric",
          second: "numeric"
        },
        long: {
          hour: "numeric",
          minute: "numeric",
          second: "numeric",
          timeZoneName: "short"
        },
        full: {
          hour: "numeric",
          minute: "numeric",
          second: "numeric",
          timeZoneName: "short"
        }
      }
    };
    return IntlMessageFormat2;
  }()
);

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/error.js
var IntlErrorCode;
(function(IntlErrorCode2) {
  IntlErrorCode2["FORMAT_ERROR"] = "FORMAT_ERROR";
  IntlErrorCode2["UNSUPPORTED_FORMATTER"] = "UNSUPPORTED_FORMATTER";
  IntlErrorCode2["INVALID_CONFIG"] = "INVALID_CONFIG";
  IntlErrorCode2["MISSING_DATA"] = "MISSING_DATA";
  IntlErrorCode2["MISSING_TRANSLATION"] = "MISSING_TRANSLATION";
})(IntlErrorCode || (IntlErrorCode = {}));
var IntlError = (
  /** @class */
  function(_super) {
    __extends(IntlError2, _super);
    function IntlError2(code, message, exception) {
      var _this = this;
      var err = exception ? exception instanceof Error ? exception : new Error(String(exception)) : void 0;
      _this = _super.call(this, "[@formatjs/intl Error ".concat(code, "] ").concat(message, "\n").concat(err ? "\n".concat(err.message, "\n").concat(err.stack) : "")) || this;
      _this.code = code;
      if (typeof Error.captureStackTrace === "function") {
        Error.captureStackTrace(_this, IntlError2);
      }
      return _this;
    }
    return IntlError2;
  }(Error)
);
var UnsupportedFormatterError = (
  /** @class */
  function(_super) {
    __extends(UnsupportedFormatterError2, _super);
    function UnsupportedFormatterError2(message, exception) {
      return _super.call(this, IntlErrorCode.UNSUPPORTED_FORMATTER, message, exception) || this;
    }
    return UnsupportedFormatterError2;
  }(IntlError)
);
var InvalidConfigError = (
  /** @class */
  function(_super) {
    __extends(InvalidConfigError2, _super);
    function InvalidConfigError2(message, exception) {
      return _super.call(this, IntlErrorCode.INVALID_CONFIG, message, exception) || this;
    }
    return InvalidConfigError2;
  }(IntlError)
);
var MissingDataError = (
  /** @class */
  function(_super) {
    __extends(MissingDataError2, _super);
    function MissingDataError2(message, exception) {
      return _super.call(this, IntlErrorCode.MISSING_DATA, message, exception) || this;
    }
    return MissingDataError2;
  }(IntlError)
);
var IntlFormatError = (
  /** @class */
  function(_super) {
    __extends(IntlFormatError2, _super);
    function IntlFormatError2(message, locale, exception) {
      var _this = _super.call(this, IntlErrorCode.FORMAT_ERROR, "".concat(message, "\nLocale: ").concat(locale, "\n"), exception) || this;
      _this.locale = locale;
      return _this;
    }
    return IntlFormatError2;
  }(IntlError)
);
var MessageFormatError = (
  /** @class */
  function(_super) {
    __extends(MessageFormatError2, _super);
    function MessageFormatError2(message, locale, descriptor, exception) {
      var _this = _super.call(this, "".concat(message, "\nMessageID: ").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.id, "\nDefault Message: ").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.defaultMessage, "\nDescription: ").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.description, "\n"), locale, exception) || this;
      _this.descriptor = descriptor;
      _this.locale = locale;
      return _this;
    }
    return MessageFormatError2;
  }(IntlFormatError)
);
var MissingTranslationError = (
  /** @class */
  function(_super) {
    __extends(MissingTranslationError2, _super);
    function MissingTranslationError2(descriptor, locale) {
      var _this = _super.call(this, IntlErrorCode.MISSING_TRANSLATION, 'Missing message: "'.concat(descriptor.id, '" for locale "').concat(locale, '", using ').concat(descriptor.defaultMessage ? "default message (".concat(typeof descriptor.defaultMessage === "string" ? descriptor.defaultMessage : descriptor.defaultMessage.map(function(e) {
        var _a2;
        return (_a2 = e.value) !== null && _a2 !== void 0 ? _a2 : JSON.stringify(e);
      }).join(), ")") : "id", " as fallback.")) || this;
      _this.descriptor = descriptor;
      return _this;
    }
    return MissingTranslationError2;
  }(IntlError)
);

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/utils.js
function filterProps(props, allowlist, defaults) {
  if (defaults === void 0) {
    defaults = {};
  }
  return allowlist.reduce(function(filtered, name) {
    if (name in props) {
      filtered[name] = props[name];
    } else if (name in defaults) {
      filtered[name] = defaults[name];
    }
    return filtered;
  }, {});
}
var defaultErrorHandler = function(error) {
  if (true) {
    console.error(error);
  }
};
var defaultWarnHandler = function(warning) {
  if (true) {
    console.warn(warning);
  }
};
var DEFAULT_INTL_CONFIG = {
  formats: {},
  messages: {},
  timeZone: void 0,
  defaultLocale: "en",
  defaultFormats: {},
  fallbackOnEmptyString: true,
  onError: defaultErrorHandler,
  onWarn: defaultWarnHandler
};
function createIntlCache() {
  return {
    dateTime: {},
    number: {},
    message: {},
    relativeTime: {},
    pluralRules: {},
    list: {},
    displayNames: {}
  };
}
function createFastMemoizeCache2(store) {
  return {
    create: function() {
      return {
        get: function(key) {
          return store[key];
        },
        set: function(key, value) {
          store[key] = value;
        }
      };
    }
  };
}
function createFormatters(cache) {
  if (cache === void 0) {
    cache = createIntlCache();
  }
  var RelativeTimeFormat = Intl.RelativeTimeFormat;
  var ListFormat = Intl.ListFormat;
  var DisplayNames = Intl.DisplayNames;
  var getDateTimeFormat = memoize(function() {
    var _a2;
    var args = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }
    return new ((_a2 = Intl.DateTimeFormat).bind.apply(_a2, __spreadArray([void 0], args, false)))();
  }, {
    cache: createFastMemoizeCache2(cache.dateTime),
    strategy: strategies.variadic
  });
  var getNumberFormat = memoize(function() {
    var _a2;
    var args = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }
    return new ((_a2 = Intl.NumberFormat).bind.apply(_a2, __spreadArray([void 0], args, false)))();
  }, {
    cache: createFastMemoizeCache2(cache.number),
    strategy: strategies.variadic
  });
  var getPluralRules = memoize(function() {
    var _a2;
    var args = [];
    for (var _i = 0; _i < arguments.length; _i++) {
      args[_i] = arguments[_i];
    }
    return new ((_a2 = Intl.PluralRules).bind.apply(_a2, __spreadArray([void 0], args, false)))();
  }, {
    cache: createFastMemoizeCache2(cache.pluralRules),
    strategy: strategies.variadic
  });
  return {
    getDateTimeFormat,
    getNumberFormat,
    getMessageFormat: memoize(function(message, locales, overrideFormats, opts) {
      return new IntlMessageFormat(message, locales, overrideFormats, __assign({ formatters: {
        getNumberFormat,
        getDateTimeFormat,
        getPluralRules
      } }, opts || {}));
    }, {
      cache: createFastMemoizeCache2(cache.message),
      strategy: strategies.variadic
    }),
    getRelativeTimeFormat: memoize(function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return new (RelativeTimeFormat.bind.apply(RelativeTimeFormat, __spreadArray([void 0], args, false)))();
    }, {
      cache: createFastMemoizeCache2(cache.relativeTime),
      strategy: strategies.variadic
    }),
    getPluralRules,
    getListFormat: memoize(function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return new (ListFormat.bind.apply(ListFormat, __spreadArray([void 0], args, false)))();
    }, {
      cache: createFastMemoizeCache2(cache.list),
      strategy: strategies.variadic
    }),
    getDisplayNames: memoize(function() {
      var args = [];
      for (var _i = 0; _i < arguments.length; _i++) {
        args[_i] = arguments[_i];
      }
      return new (DisplayNames.bind.apply(DisplayNames, __spreadArray([void 0], args, false)))();
    }, {
      cache: createFastMemoizeCache2(cache.displayNames),
      strategy: strategies.variadic
    })
  };
}
function getNamedFormat(formats, type, name, onError) {
  var formatType = formats && formats[type];
  var format;
  if (formatType) {
    format = formatType[name];
  }
  if (format) {
    return format;
  }
  onError(new UnsupportedFormatterError("No ".concat(type, " format named: ").concat(name)));
}

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/message.js
function setTimeZoneInOptions(opts, timeZone) {
  return Object.keys(opts).reduce(function(all, k) {
    all[k] = __assign({ timeZone }, opts[k]);
    return all;
  }, {});
}
function deepMergeOptions(opts1, opts2) {
  var keys = Object.keys(__assign(__assign({}, opts1), opts2));
  return keys.reduce(function(all, k) {
    all[k] = __assign(__assign({}, opts1[k] || {}), opts2[k] || {});
    return all;
  }, {});
}
function deepMergeFormatsAndSetTimeZone(f1, timeZone) {
  if (!timeZone) {
    return f1;
  }
  var mfFormats = IntlMessageFormat.formats;
  return __assign(__assign(__assign({}, mfFormats), f1), { date: deepMergeOptions(setTimeZoneInOptions(mfFormats.date, timeZone), setTimeZoneInOptions(f1.date || {}, timeZone)), time: deepMergeOptions(setTimeZoneInOptions(mfFormats.time, timeZone), setTimeZoneInOptions(f1.time || {}, timeZone)) });
}
var formatMessage = function(_a2, state, messageDescriptor, values, opts) {
  var locale = _a2.locale, formats = _a2.formats, messages = _a2.messages, defaultLocale = _a2.defaultLocale, defaultFormats = _a2.defaultFormats, fallbackOnEmptyString = _a2.fallbackOnEmptyString, onError = _a2.onError, timeZone = _a2.timeZone, defaultRichTextElements = _a2.defaultRichTextElements;
  if (messageDescriptor === void 0) {
    messageDescriptor = { id: "" };
  }
  var msgId = messageDescriptor.id, defaultMessage = messageDescriptor.defaultMessage;
  invariant(!!msgId, "[@formatjs/intl] An `id` must be provided to format a message. You can either:\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\nto autofix this issue");
  var id = String(msgId);
  var message = (
    // In case messages is Object.create(null)
    // e.g import('foo.json') from webpack)
    // See https://github.com/formatjs/formatjs/issues/1914
    messages && Object.prototype.hasOwnProperty.call(messages, id) && messages[id]
  );
  if (Array.isArray(message) && message.length === 1 && message[0].type === TYPE.literal) {
    return message[0].value;
  }
  if (!values && message && typeof message === "string" && !defaultRichTextElements) {
    return message.replace(/'\{(.*?)\}'/gi, "{$1}");
  }
  values = __assign(__assign({}, defaultRichTextElements), values || {});
  formats = deepMergeFormatsAndSetTimeZone(formats, timeZone);
  defaultFormats = deepMergeFormatsAndSetTimeZone(defaultFormats, timeZone);
  if (!message) {
    if (fallbackOnEmptyString === false && message === "") {
      return message;
    }
    if (!defaultMessage || locale && locale.toLowerCase() !== defaultLocale.toLowerCase()) {
      onError(new MissingTranslationError(messageDescriptor, locale));
    }
    if (defaultMessage) {
      try {
        var formatter = state.getMessageFormat(defaultMessage, defaultLocale, defaultFormats, opts);
        return formatter.format(values);
      } catch (e) {
        onError(new MessageFormatError('Error formatting default message for: "'.concat(id, '", rendering default message verbatim'), locale, messageDescriptor, e));
        return typeof defaultMessage === "string" ? defaultMessage : id;
      }
    }
    return id;
  }
  try {
    var formatter = state.getMessageFormat(message, locale, formats, __assign({ formatters: state }, opts || {}));
    return formatter.format(values);
  } catch (e) {
    onError(new MessageFormatError('Error formatting message: "'.concat(id, '", using ').concat(defaultMessage ? "default message" : "id", " as fallback."), locale, messageDescriptor, e));
  }
  if (defaultMessage) {
    try {
      var formatter = state.getMessageFormat(defaultMessage, defaultLocale, defaultFormats, opts);
      return formatter.format(values);
    } catch (e) {
      onError(new MessageFormatError('Error formatting the default message for: "'.concat(id, '", rendering message verbatim'), locale, messageDescriptor, e));
    }
  }
  if (typeof message === "string") {
    return message;
  }
  if (typeof defaultMessage === "string") {
    return defaultMessage;
  }
  return id;
};

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/dateTime.js
var DATE_TIME_FORMAT_OPTIONS = [
  "formatMatcher",
  "timeZone",
  "hour12",
  "weekday",
  "era",
  "year",
  "month",
  "day",
  "hour",
  "minute",
  "second",
  "timeZoneName",
  "hourCycle",
  "dateStyle",
  "timeStyle",
  "calendar",
  // 'dayPeriod',
  "numberingSystem",
  "fractionalSecondDigits"
];
function getFormatter(_a2, type, getDateTimeFormat, options) {
  var locale = _a2.locale, formats = _a2.formats, onError = _a2.onError, timeZone = _a2.timeZone;
  if (options === void 0) {
    options = {};
  }
  var format = options.format;
  var defaults = __assign(__assign({}, timeZone && { timeZone }), format && getNamedFormat(formats, type, format, onError));
  var filteredOptions = filterProps(options, DATE_TIME_FORMAT_OPTIONS, defaults);
  if (type === "time" && !filteredOptions.hour && !filteredOptions.minute && !filteredOptions.second && !filteredOptions.timeStyle && !filteredOptions.dateStyle) {
    filteredOptions = __assign(__assign({}, filteredOptions), { hour: "numeric", minute: "numeric" });
  }
  return getDateTimeFormat(locale, filteredOptions);
}
function formatDate(config, getDateTimeFormat) {
  var _a2 = [];
  for (var _i = 2; _i < arguments.length; _i++) {
    _a2[_i - 2] = arguments[_i];
  }
  var value = _a2[0], _b = _a2[1], options = _b === void 0 ? {} : _b;
  var date = typeof value === "string" ? new Date(value || 0) : value;
  try {
    return getFormatter(config, "date", getDateTimeFormat, options).format(date);
  } catch (e) {
    config.onError(new IntlFormatError("Error formatting date.", config.locale, e));
  }
  return String(date);
}
function formatTime(config, getDateTimeFormat) {
  var _a2 = [];
  for (var _i = 2; _i < arguments.length; _i++) {
    _a2[_i - 2] = arguments[_i];
  }
  var value = _a2[0], _b = _a2[1], options = _b === void 0 ? {} : _b;
  var date = typeof value === "string" ? new Date(value || 0) : value;
  try {
    return getFormatter(config, "time", getDateTimeFormat, options).format(date);
  } catch (e) {
    config.onError(new IntlFormatError("Error formatting time.", config.locale, e));
  }
  return String(date);
}
function formatDateTimeRange(config, getDateTimeFormat) {
  var _a2 = [];
  for (var _i = 2; _i < arguments.length; _i++) {
    _a2[_i - 2] = arguments[_i];
  }
  var from = _a2[0], to = _a2[1], _b = _a2[2], options = _b === void 0 ? {} : _b;
  var timeZone = config.timeZone, locale = config.locale, onError = config.onError;
  var filteredOptions = filterProps(options, DATE_TIME_FORMAT_OPTIONS, timeZone ? { timeZone } : {});
  try {
    return getDateTimeFormat(locale, filteredOptions).formatRange(from, to);
  } catch (e) {
    onError(new IntlFormatError("Error formatting date time range.", config.locale, e));
  }
  return String(from);
}
function formatDateToParts(config, getDateTimeFormat) {
  var _a2 = [];
  for (var _i = 2; _i < arguments.length; _i++) {
    _a2[_i - 2] = arguments[_i];
  }
  var value = _a2[0], _b = _a2[1], options = _b === void 0 ? {} : _b;
  var date = typeof value === "string" ? new Date(value || 0) : value;
  try {
    return getFormatter(config, "date", getDateTimeFormat, options).formatToParts(date);
  } catch (e) {
    config.onError(new IntlFormatError("Error formatting date.", config.locale, e));
  }
  return [];
}
function formatTimeToParts(config, getDateTimeFormat) {
  var _a2 = [];
  for (var _i = 2; _i < arguments.length; _i++) {
    _a2[_i - 2] = arguments[_i];
  }
  var value = _a2[0], _b = _a2[1], options = _b === void 0 ? {} : _b;
  var date = typeof value === "string" ? new Date(value || 0) : value;
  try {
    return getFormatter(config, "time", getDateTimeFormat, options).formatToParts(date);
  } catch (e) {
    config.onError(new IntlFormatError("Error formatting time.", config.locale, e));
  }
  return [];
}

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/displayName.js
var DISPLAY_NAMES_OPTONS = [
  "style",
  "type",
  "fallback",
  "languageDisplay"
];
function formatDisplayName(_a2, getDisplayNames, value, options) {
  var locale = _a2.locale, onError = _a2.onError;
  var DisplayNames = Intl.DisplayNames;
  if (!DisplayNames) {
    onError(new FormatError('Intl.DisplayNames is not available in this environment.\nTry polyfilling it using "@formatjs/intl-displaynames"\n', ErrorCode.MISSING_INTL_API));
  }
  var filteredOptions = filterProps(options, DISPLAY_NAMES_OPTONS);
  try {
    return getDisplayNames(locale, filteredOptions).of(value);
  } catch (e) {
    onError(new IntlFormatError("Error formatting display name.", locale, e));
  }
}

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/list.js
var LIST_FORMAT_OPTIONS = [
  "type",
  "style"
];
var now = Date.now();
function generateToken(i) {
  return "".concat(now, "_").concat(i, "_").concat(now);
}
function formatList(opts, getListFormat, values, options) {
  if (options === void 0) {
    options = {};
  }
  var results = formatListToParts(opts, getListFormat, values, options).reduce(function(all, el) {
    var val = el.value;
    if (typeof val !== "string") {
      all.push(val);
    } else if (typeof all[all.length - 1] === "string") {
      all[all.length - 1] += val;
    } else {
      all.push(val);
    }
    return all;
  }, []);
  return results.length === 1 ? results[0] : results.length === 0 ? "" : results;
}
function formatListToParts(_a2, getListFormat, values, options) {
  var locale = _a2.locale, onError = _a2.onError;
  if (options === void 0) {
    options = {};
  }
  var ListFormat = Intl.ListFormat;
  if (!ListFormat) {
    onError(new FormatError('Intl.ListFormat is not available in this environment.\nTry polyfilling it using "@formatjs/intl-listformat"\n', ErrorCode.MISSING_INTL_API));
  }
  var filteredOptions = filterProps(options, LIST_FORMAT_OPTIONS);
  try {
    var richValues_1 = {};
    var serializedValues = values.map(function(v, i) {
      if (typeof v === "object") {
        var id = generateToken(i);
        richValues_1[id] = v;
        return id;
      }
      return String(v);
    });
    return getListFormat(locale, filteredOptions).formatToParts(serializedValues).map(function(part) {
      return part.type === "literal" ? part : __assign(__assign({}, part), { value: richValues_1[part.value] || part.value });
    });
  } catch (e) {
    onError(new IntlFormatError("Error formatting list.", locale, e));
  }
  return values;
}

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/plural.js
var PLURAL_FORMAT_OPTIONS = ["type"];
function formatPlural(_a2, getPluralRules, value, options) {
  var locale = _a2.locale, onError = _a2.onError;
  if (options === void 0) {
    options = {};
  }
  if (!Intl.PluralRules) {
    onError(new FormatError('Intl.PluralRules is not available in this environment.\nTry polyfilling it using "@formatjs/intl-pluralrules"\n', ErrorCode.MISSING_INTL_API));
  }
  var filteredOptions = filterProps(options, PLURAL_FORMAT_OPTIONS);
  try {
    return getPluralRules(locale, filteredOptions).select(value);
  } catch (e) {
    onError(new IntlFormatError("Error formatting plural.", locale, e));
  }
  return "other";
}

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/relativeTime.js
var RELATIVE_TIME_FORMAT_OPTIONS = ["numeric", "style"];
function getFormatter2(_a2, getRelativeTimeFormat, options) {
  var locale = _a2.locale, formats = _a2.formats, onError = _a2.onError;
  if (options === void 0) {
    options = {};
  }
  var format = options.format;
  var defaults = !!format && getNamedFormat(formats, "relative", format, onError) || {};
  var filteredOptions = filterProps(options, RELATIVE_TIME_FORMAT_OPTIONS, defaults);
  return getRelativeTimeFormat(locale, filteredOptions);
}
function formatRelativeTime(config, getRelativeTimeFormat, value, unit, options) {
  if (options === void 0) {
    options = {};
  }
  if (!unit) {
    unit = "second";
  }
  var RelativeTimeFormat = Intl.RelativeTimeFormat;
  if (!RelativeTimeFormat) {
    config.onError(new FormatError('Intl.RelativeTimeFormat is not available in this environment.\nTry polyfilling it using "@formatjs/intl-relativetimeformat"\n', ErrorCode.MISSING_INTL_API));
  }
  try {
    return getFormatter2(config, getRelativeTimeFormat, options).format(value, unit);
  } catch (e) {
    config.onError(new IntlFormatError("Error formatting relative time.", config.locale, e));
  }
  return String(value);
}

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/number.js
var NUMBER_FORMAT_OPTIONS = [
  "style",
  "currency",
  "unit",
  "unitDisplay",
  "useGrouping",
  "minimumIntegerDigits",
  "minimumFractionDigits",
  "maximumFractionDigits",
  "minimumSignificantDigits",
  "maximumSignificantDigits",
  // ES2020 NumberFormat
  "compactDisplay",
  "currencyDisplay",
  "currencySign",
  "notation",
  "signDisplay",
  "unit",
  "unitDisplay",
  "numberingSystem",
  // ES2023 NumberFormat
  "trailingZeroDisplay",
  "roundingPriority",
  "roundingIncrement",
  "roundingMode"
];
function getFormatter3(_a2, getNumberFormat, options) {
  var locale = _a2.locale, formats = _a2.formats, onError = _a2.onError;
  if (options === void 0) {
    options = {};
  }
  var format = options.format;
  var defaults = format && getNamedFormat(formats, "number", format, onError) || {};
  var filteredOptions = filterProps(options, NUMBER_FORMAT_OPTIONS, defaults);
  return getNumberFormat(locale, filteredOptions);
}
function formatNumber(config, getNumberFormat, value, options) {
  if (options === void 0) {
    options = {};
  }
  try {
    return getFormatter3(config, getNumberFormat, options).format(value);
  } catch (e) {
    config.onError(new IntlFormatError("Error formatting number.", config.locale, e));
  }
  return String(value);
}
function formatNumberToParts(config, getNumberFormat, value, options) {
  if (options === void 0) {
    options = {};
  }
  try {
    return getFormatter3(config, getNumberFormat, options).formatToParts(value);
  } catch (e) {
    config.onError(new IntlFormatError("Error formatting number.", config.locale, e));
  }
  return [];
}

// node_modules/@strapi/i18n/node_modules/@formatjs/intl/lib/src/create-intl.js
function messagesContainString(messages) {
  var firstMessage = messages ? messages[Object.keys(messages)[0]] : void 0;
  return typeof firstMessage === "string";
}
function verifyConfigMessages(config) {
  if (config.onWarn && config.defaultRichTextElements && messagesContainString(config.messages || {})) {
    config.onWarn('[@formatjs/intl] "defaultRichTextElements" was specified but "message" was not pre-compiled. \nPlease consider using "@formatjs/cli" to pre-compile your messages for performance.\nFor more details see https://formatjs.io/docs/getting-started/message-distribution');
  }
}
function createIntl(config, cache) {
  var formatters = createFormatters(cache);
  var resolvedConfig = __assign(__assign({}, DEFAULT_INTL_CONFIG), config);
  var locale = resolvedConfig.locale, defaultLocale = resolvedConfig.defaultLocale, onError = resolvedConfig.onError;
  if (!locale) {
    if (onError) {
      onError(new InvalidConfigError('"locale" was not configured, using "'.concat(defaultLocale, '" as fallback. See https://formatjs.io/docs/react-intl/api#intlshape for more details')));
    }
    resolvedConfig.locale = resolvedConfig.defaultLocale || "en";
  } else if (!Intl.NumberFormat.supportedLocalesOf(locale).length && onError) {
    onError(new MissingDataError('Missing locale data for locale: "'.concat(locale, '" in Intl.NumberFormat. Using default locale: "').concat(defaultLocale, '" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details')));
  } else if (!Intl.DateTimeFormat.supportedLocalesOf(locale).length && onError) {
    onError(new MissingDataError('Missing locale data for locale: "'.concat(locale, '" in Intl.DateTimeFormat. Using default locale: "').concat(defaultLocale, '" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details')));
  }
  verifyConfigMessages(resolvedConfig);
  return __assign(__assign({}, resolvedConfig), {
    formatters,
    formatNumber: formatNumber.bind(null, resolvedConfig, formatters.getNumberFormat),
    formatNumberToParts: formatNumberToParts.bind(null, resolvedConfig, formatters.getNumberFormat),
    formatRelativeTime: formatRelativeTime.bind(null, resolvedConfig, formatters.getRelativeTimeFormat),
    formatDate: formatDate.bind(null, resolvedConfig, formatters.getDateTimeFormat),
    formatDateToParts: formatDateToParts.bind(null, resolvedConfig, formatters.getDateTimeFormat),
    formatTime: formatTime.bind(null, resolvedConfig, formatters.getDateTimeFormat),
    formatDateTimeRange: formatDateTimeRange.bind(null, resolvedConfig, formatters.getDateTimeFormat),
    formatTimeToParts: formatTimeToParts.bind(null, resolvedConfig, formatters.getDateTimeFormat),
    formatPlural: formatPlural.bind(null, resolvedConfig, formatters.getPluralRules),
    // @ts-expect-error TODO: will get to this later
    formatMessage: formatMessage.bind(null, resolvedConfig, formatters),
    // @ts-expect-error TODO: will get to this later
    $t: formatMessage.bind(null, resolvedConfig, formatters),
    formatList: formatList.bind(null, resolvedConfig, formatters.getListFormat),
    formatListToParts: formatListToParts.bind(null, resolvedConfig, formatters.getListFormat),
    formatDisplayName: formatDisplayName.bind(null, resolvedConfig, formatters.getDisplayNames)
  });
}

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/utils.js
function invariantIntlContext(intl) {
  invariant(intl, "[React Intl] Could not find required `intl` object. <IntlProvider> needs to exist in the component ancestry.");
}
var DEFAULT_INTL_CONFIG2 = __assign(__assign({}, DEFAULT_INTL_CONFIG), { textComponent: React.Fragment });
function assignUniqueKeysToParts(formatXMLElementFn) {
  return function(parts) {
    return formatXMLElementFn(React.Children.toArray(parts));
  };
}
function shallowEqual(objA, objB) {
  if (objA === objB) {
    return true;
  }
  if (!objA || !objB) {
    return false;
  }
  var aKeys = Object.keys(objA);
  var bKeys = Object.keys(objB);
  var len = aKeys.length;
  if (bKeys.length !== len) {
    return false;
  }
  for (var i = 0; i < len; i++) {
    var key = aKeys[i];
    if (objA[key] !== objB[key] || !Object.prototype.hasOwnProperty.call(objB, key)) {
      return false;
    }
  }
  return true;
}

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/injectIntl.js
var IntlContext = typeof window !== "undefined" && !window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__ ? window.__REACT_INTL_CONTEXT__ || (window.__REACT_INTL_CONTEXT__ = React2.createContext(null)) : React2.createContext(null);
var IntlConsumer = IntlContext.Consumer;
var IntlProvider = IntlContext.Provider;
var Provider = IntlProvider;
var Context = IntlContext;

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/useIntl.js
function useIntl() {
  var intl = React3.useContext(Context);
  invariantIntlContext(intl);
  return intl;
}

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/createFormattedComponent.js
var React4 = __toESM(require_react());
var DisplayName;
(function(DisplayName2) {
  DisplayName2["formatDate"] = "FormattedDate";
  DisplayName2["formatTime"] = "FormattedTime";
  DisplayName2["formatNumber"] = "FormattedNumber";
  DisplayName2["formatList"] = "FormattedList";
  DisplayName2["formatDisplayName"] = "FormattedDisplayName";
})(DisplayName || (DisplayName = {}));
var DisplayNameParts;
(function(DisplayNameParts2) {
  DisplayNameParts2["formatDate"] = "FormattedDateParts";
  DisplayNameParts2["formatTime"] = "FormattedTimeParts";
  DisplayNameParts2["formatNumber"] = "FormattedNumberParts";
  DisplayNameParts2["formatList"] = "FormattedListParts";
})(DisplayNameParts || (DisplayNameParts = {}));
var FormattedNumberParts = function(props) {
  var intl = useIntl();
  var value = props.value, children = props.children, formatProps = __rest(props, ["value", "children"]);
  return children(intl.formatNumberToParts(value, formatProps));
};
FormattedNumberParts.displayName = "FormattedNumberParts";
FormattedNumberParts.displayName = "FormattedNumberParts";
function createFormattedDateTimePartsComponent(name) {
  var ComponentParts = function(props) {
    var intl = useIntl();
    var value = props.value, children = props.children, formatProps = __rest(props, ["value", "children"]);
    var date = typeof value === "string" ? new Date(value || 0) : value;
    var formattedParts = name === "formatDate" ? intl.formatDateToParts(date, formatProps) : intl.formatTimeToParts(date, formatProps);
    return children(formattedParts);
  };
  ComponentParts.displayName = DisplayNameParts[name];
  return ComponentParts;
}
function createFormattedComponent(name) {
  var Component = function(props) {
    var intl = useIntl();
    var value = props.value, children = props.children, formatProps = __rest(
      props,
      ["value", "children"]
    );
    var formattedValue = intl[name](value, formatProps);
    if (typeof children === "function") {
      return children(formattedValue);
    }
    var Text = intl.textComponent || React4.Fragment;
    return React4.createElement(Text, null, formattedValue);
  };
  Component.displayName = DisplayName[name];
  return Component;
}

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/provider.js
var React6 = __toESM(require_react());

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/createIntl.js
var React5 = __toESM(require_react());
function assignUniqueKeysToFormatXMLElementFnArgument(values) {
  if (!values) {
    return values;
  }
  return Object.keys(values).reduce(function(acc, k) {
    var v = values[k];
    acc[k] = isFormatXMLElementFn(v) ? assignUniqueKeysToParts(v) : v;
    return acc;
  }, {});
}
var formatMessage2 = function(config, formatters, descriptor, rawValues) {
  var rest = [];
  for (var _i = 4; _i < arguments.length; _i++) {
    rest[_i - 4] = arguments[_i];
  }
  var values = assignUniqueKeysToFormatXMLElementFnArgument(rawValues);
  var chunks = formatMessage.apply(void 0, __spreadArray([
    config,
    formatters,
    descriptor,
    values
  ], rest, false));
  if (Array.isArray(chunks)) {
    return React5.Children.toArray(chunks);
  }
  return chunks;
};
var createIntl2 = function(_a2, cache) {
  var rawDefaultRichTextElements = _a2.defaultRichTextElements, config = __rest(_a2, ["defaultRichTextElements"]);
  var defaultRichTextElements = assignUniqueKeysToFormatXMLElementFnArgument(rawDefaultRichTextElements);
  var coreIntl = createIntl(__assign(__assign(__assign({}, DEFAULT_INTL_CONFIG2), config), { defaultRichTextElements }), cache);
  var resolvedConfig = {
    locale: coreIntl.locale,
    timeZone: coreIntl.timeZone,
    fallbackOnEmptyString: coreIntl.fallbackOnEmptyString,
    formats: coreIntl.formats,
    defaultLocale: coreIntl.defaultLocale,
    defaultFormats: coreIntl.defaultFormats,
    messages: coreIntl.messages,
    onError: coreIntl.onError,
    defaultRichTextElements
  };
  return __assign(__assign({}, coreIntl), {
    formatMessage: formatMessage2.bind(
      null,
      resolvedConfig,
      // @ts-expect-error fix this
      coreIntl.formatters
    ),
    // @ts-expect-error fix this
    $t: formatMessage2.bind(null, resolvedConfig, coreIntl.formatters)
  });
};

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/provider.js
function processIntlConfig(config) {
  return {
    locale: config.locale,
    timeZone: config.timeZone,
    fallbackOnEmptyString: config.fallbackOnEmptyString,
    formats: config.formats,
    textComponent: config.textComponent,
    messages: config.messages,
    defaultLocale: config.defaultLocale,
    defaultFormats: config.defaultFormats,
    onError: config.onError,
    onWarn: config.onWarn,
    wrapRichTextChunksInFragment: config.wrapRichTextChunksInFragment,
    defaultRichTextElements: config.defaultRichTextElements
  };
}
var IntlProvider2 = (
  /** @class */
  function(_super) {
    __extends(IntlProvider3, _super);
    function IntlProvider3() {
      var _this = _super !== null && _super.apply(this, arguments) || this;
      _this.cache = createIntlCache();
      _this.state = {
        cache: _this.cache,
        intl: createIntl2(processIntlConfig(_this.props), _this.cache),
        prevConfig: processIntlConfig(_this.props)
      };
      return _this;
    }
    IntlProvider3.getDerivedStateFromProps = function(props, _a2) {
      var prevConfig = _a2.prevConfig, cache = _a2.cache;
      var config = processIntlConfig(props);
      if (!shallowEqual(prevConfig, config)) {
        return {
          intl: createIntl2(config, cache),
          prevConfig: config
        };
      }
      return null;
    };
    IntlProvider3.prototype.render = function() {
      invariantIntlContext(this.state.intl);
      return React6.createElement(Provider, { value: this.state.intl }, this.props.children);
    };
    IntlProvider3.displayName = "IntlProvider";
    IntlProvider3.defaultProps = DEFAULT_INTL_CONFIG2;
    return IntlProvider3;
  }(React6.PureComponent)
);

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/relative.js
var React7 = __toESM(require_react());
var MINUTE = 60;
var HOUR = 60 * 60;
var DAY = 60 * 60 * 24;
function selectUnit(seconds) {
  var absValue = Math.abs(seconds);
  if (absValue < MINUTE) {
    return "second";
  }
  if (absValue < HOUR) {
    return "minute";
  }
  if (absValue < DAY) {
    return "hour";
  }
  return "day";
}
function getDurationInSeconds(unit) {
  switch (unit) {
    case "second":
      return 1;
    case "minute":
      return MINUTE;
    case "hour":
      return HOUR;
    default:
      return DAY;
  }
}
function valueToSeconds(value, unit) {
  if (!value) {
    return 0;
  }
  switch (unit) {
    case "second":
      return value;
    case "minute":
      return value * MINUTE;
    default:
      return value * HOUR;
  }
}
var INCREMENTABLE_UNITS = [
  "second",
  "minute",
  "hour"
];
function canIncrement(unit) {
  if (unit === void 0) {
    unit = "second";
  }
  return INCREMENTABLE_UNITS.indexOf(unit) > -1;
}
var SimpleFormattedRelativeTime = function(props) {
  var _a2 = useIntl(), formatRelativeTime2 = _a2.formatRelativeTime, Text = _a2.textComponent;
  var children = props.children, value = props.value, unit = props.unit, otherProps = __rest(props, ["children", "value", "unit"]);
  var formattedRelativeTime = formatRelativeTime2(value || 0, unit, otherProps);
  if (typeof children === "function") {
    return children(formattedRelativeTime);
  }
  if (Text) {
    return React7.createElement(Text, null, formattedRelativeTime);
  }
  return React7.createElement(React7.Fragment, null, formattedRelativeTime);
};
var FormattedRelativeTime = function(_a2) {
  var _b = _a2.value, value = _b === void 0 ? 0 : _b, _c = _a2.unit, unit = _c === void 0 ? "second" : _c, updateIntervalInSeconds = _a2.updateIntervalInSeconds, otherProps = __rest(_a2, ["value", "unit", "updateIntervalInSeconds"]);
  invariant(!updateIntervalInSeconds || !!(updateIntervalInSeconds && canIncrement(unit)), "Cannot schedule update with unit longer than hour");
  var _d = React7.useState(), prevUnit = _d[0], setPrevUnit = _d[1];
  var _e = React7.useState(0), prevValue = _e[0], setPrevValue = _e[1];
  var _f = React7.useState(0), currentValueInSeconds = _f[0], setCurrentValueInSeconds = _f[1];
  var updateTimer;
  if (unit !== prevUnit || value !== prevValue) {
    setPrevValue(value || 0);
    setPrevUnit(unit);
    setCurrentValueInSeconds(canIncrement(unit) ? valueToSeconds(value, unit) : 0);
  }
  React7.useEffect(function() {
    function clearUpdateTimer() {
      clearTimeout(updateTimer);
    }
    clearUpdateTimer();
    if (!updateIntervalInSeconds || !canIncrement(unit)) {
      return clearUpdateTimer;
    }
    var nextValueInSeconds = currentValueInSeconds - updateIntervalInSeconds;
    var nextUnit = selectUnit(nextValueInSeconds);
    if (nextUnit === "day") {
      return clearUpdateTimer;
    }
    var unitDuration2 = getDurationInSeconds(nextUnit);
    var remainder = nextValueInSeconds % unitDuration2;
    var prevInterestingValueInSeconds = nextValueInSeconds - remainder;
    var nextInterestingValueInSeconds = prevInterestingValueInSeconds >= currentValueInSeconds ? prevInterestingValueInSeconds - unitDuration2 : prevInterestingValueInSeconds;
    var delayInSeconds = Math.abs(nextInterestingValueInSeconds - currentValueInSeconds);
    if (currentValueInSeconds !== nextInterestingValueInSeconds) {
      updateTimer = setTimeout(function() {
        return setCurrentValueInSeconds(nextInterestingValueInSeconds);
      }, delayInSeconds * 1e3);
    }
    return clearUpdateTimer;
  }, [currentValueInSeconds, updateIntervalInSeconds, unit]);
  var currentValue = value || 0;
  var currentUnit = unit;
  if (canIncrement(unit) && typeof currentValueInSeconds === "number" && updateIntervalInSeconds) {
    currentUnit = selectUnit(currentValueInSeconds);
    var unitDuration = getDurationInSeconds(currentUnit);
    currentValue = Math.round(currentValueInSeconds / unitDuration);
  }
  return React7.createElement(SimpleFormattedRelativeTime, __assign({ value: currentValue, unit: currentUnit }, otherProps));
};
FormattedRelativeTime.displayName = "FormattedRelativeTime";

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/plural.js
var React8 = __toESM(require_react());
var FormattedPlural = function(props) {
  var _a2 = useIntl(), formatPlural2 = _a2.formatPlural, Text = _a2.textComponent;
  var value = props.value, other = props.other, children = props.children;
  var pluralCategory = formatPlural2(value, props);
  var formattedPlural = props[pluralCategory] || other;
  if (typeof children === "function") {
    return children(formattedPlural);
  }
  if (Text) {
    return React8.createElement(Text, null, formattedPlural);
  }
  return formattedPlural;
};
FormattedPlural.displayName = "FormattedPlural";

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/message.js
var React9 = __toESM(require_react());
function areEqual(prevProps, nextProps) {
  var values = prevProps.values, otherProps = __rest(prevProps, ["values"]);
  var nextValues = nextProps.values, nextOtherProps = __rest(nextProps, ["values"]);
  return shallowEqual(nextValues, values) && shallowEqual(otherProps, nextOtherProps);
}
function FormattedMessage(props) {
  var intl = useIntl();
  var formatMessage3 = intl.formatMessage, _a2 = intl.textComponent, Text = _a2 === void 0 ? React9.Fragment : _a2;
  var id = props.id, description = props.description, defaultMessage = props.defaultMessage, values = props.values, children = props.children, _b = props.tagName, Component = _b === void 0 ? Text : _b, ignoreTag = props.ignoreTag;
  var descriptor = { id, description, defaultMessage };
  var nodes = formatMessage3(descriptor, values, {
    ignoreTag
  });
  if (typeof children === "function") {
    return children(Array.isArray(nodes) ? nodes : [nodes]);
  }
  if (Component) {
    return React9.createElement(Component, null, React9.Children.toArray(nodes));
  }
  return React9.createElement(React9.Fragment, null, nodes);
}
FormattedMessage.displayName = "FormattedMessage";
var MemoizedFormattedMessage = React9.memo(FormattedMessage, areEqual);
MemoizedFormattedMessage.displayName = "MemoizedFormattedMessage";

// node_modules/@strapi/i18n/node_modules/react-intl/lib/src/components/dateTimeRange.js
var React10 = __toESM(require_react());
var FormattedDateTimeRange = function(props) {
  var intl = useIntl();
  var from = props.from, to = props.to, children = props.children, formatProps = __rest(props, ["from", "to", "children"]);
  var formattedValue = intl.formatDateTimeRange(from, to, formatProps);
  if (typeof children === "function") {
    return children(formattedValue);
  }
  var Text = intl.textComponent || React10.Fragment;
  return React10.createElement(Text, null, formattedValue);
};
FormattedDateTimeRange.displayName = "FormattedDateTimeRange";

// node_modules/@strapi/i18n/node_modules/react-intl/lib/index.js
var FormattedDate = createFormattedComponent("formatDate");
var FormattedTime = createFormattedComponent("formatTime");
var FormattedNumber = createFormattedComponent("formatNumber");
var FormattedList = createFormattedComponent("formatList");
var FormattedDisplayName = createFormattedComponent("formatDisplayName");
var FormattedDateParts = createFormattedDateTimePartsComponent("formatDate");
var FormattedTimeParts = createFormattedDateTimePartsComponent("formatTime");

// node_modules/@strapi/i18n/dist/_chunks/index-CNR8i3KM.mjs
var import_get = __toESM(require_get(), 1);
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React11 = __toESM(require_react(), 1);
var qs = __toESM(require_lib(), 1);
var import_qs = __toESM(require_lib(), 1);
var import_omit = __toESM(require_omit(), 1);
var __variableDynamicImportRuntimeHelper = (glob, path) => {
  const v = glob[path];
  if (v) {
    return typeof v === "function" ? v() : Promise.resolve(v);
  }
  return new Promise((_, reject) => {
    (typeof queueMicrotask === "function" ? queueMicrotask : setTimeout)(reject.bind(null, new Error("Unknown variable dynamic import: " + path)));
  });
};
var pluginId = "i18n";
var getTranslation = (id) => `${pluginId}.${id}`;
var TextAlignTypography = dt(Typography)`
  text-align: center;
`;
var CheckboxConfirmation = ({
  description,
  isCreating = false,
  intlLabel,
  name,
  onChange,
  value
}) => {
  const { formatMessage: formatMessage3 } = useIntl();
  const [isOpen, setIsOpen] = React11.useState(false);
  const handleChange = (value2) => {
    if (isCreating || value2) {
      return onChange({ target: { name, value: value2, type: "checkbox" } });
    }
    if (!value2) {
      return setIsOpen(true);
    }
    return null;
  };
  const handleConfirm = () => {
    onChange({ target: { name, value: false, type: "checkbox" } });
  };
  const label = intlLabel.id ? formatMessage3(
    { id: intlLabel.id, defaultMessage: intlLabel.defaultMessage },
    { ...intlLabel.values }
  ) : name;
  const hint = description ? formatMessage3(
    { id: description.id, defaultMessage: description.defaultMessage },
    { ...description.values }
  ) : "";
  return (0, import_jsx_runtime.jsxs)(Dialog.Root, { open: isOpen, onOpenChange: setIsOpen, children: [
    (0, import_jsx_runtime.jsxs)(Field.Root, { hint, name, children: [
      (0, import_jsx_runtime.jsx)(CheckboxImpl, { onCheckedChange: handleChange, checked: value, children: label }),
      (0, import_jsx_runtime.jsx)(Field.Hint, {})
    ] }),
    (0, import_jsx_runtime.jsxs)(Dialog.Content, { children: [
      (0, import_jsx_runtime.jsx)(Dialog.Header, { children: formatMessage3({
        id: getTranslation("CheckboxConfirmation.Modal.title"),
        defaultMessage: "Disable localization"
      }) }),
      (0, import_jsx_runtime.jsx)(Dialog.Body, { icon: (0, import_jsx_runtime.jsx)(ForwardRef$3, {}), children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 2, children: [
        (0, import_jsx_runtime.jsx)(Flex, { justifyContent: "center", children: (0, import_jsx_runtime.jsx)(TextAlignTypography, { children: formatMessage3({
          id: getTranslation("CheckboxConfirmation.Modal.content"),
          defaultMessage: "Disabling localization will engender the deletion of all your content but the one associated to your default locale (if existing)."
        }) }) }),
        (0, import_jsx_runtime.jsx)(Flex, { justifyContent: "center", children: (0, import_jsx_runtime.jsx)(Typography, { fontWeight: "semiBold", children: formatMessage3({
          id: getTranslation("CheckboxConfirmation.Modal.body"),
          defaultMessage: "Do you want to disable it?"
        }) }) })
      ] }) }),
      (0, import_jsx_runtime.jsxs)(Dialog.Footer, { children: [
        (0, import_jsx_runtime.jsx)(Dialog.Cancel, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "tertiary", children: formatMessage3({
          id: "components.popUpWarning.button.cancel",
          defaultMessage: "No, cancel"
        }) }) }),
        (0, import_jsx_runtime.jsx)(Dialog.Action, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "danger-light", onClick: handleConfirm, children: formatMessage3({
          id: getTranslation("CheckboxConfirmation.Modal.button-confirm"),
          defaultMessage: "Yes, disable"
        }) }) })
      ] })
    ] })
  ] });
};
var LOCALIZED_FIELDS = [
  "biginteger",
  "boolean",
  "component",
  "date",
  "datetime",
  "decimal",
  "dynamiczone",
  "email",
  "enumeration",
  "float",
  "integer",
  "json",
  "media",
  "number",
  "password",
  "richtext",
  "blocks",
  "string",
  "text",
  "time"
];
var doesPluginOptionsHaveI18nLocalized = (opts) => typeof opts === "object" && opts !== null && "i18n" in opts && typeof opts.i18n === "object" && opts.i18n !== null && "localized" in opts.i18n && typeof opts.i18n.localized === "boolean";
var capitalize = (str) => str.charAt(0).toUpperCase() + str.slice(1);
var useI18n = () => {
  const params = useParams();
  const userPermissions = useAuth("useI18n", (state) => state.permissions);
  const actions = React11.useMemo(() => {
    const permissions = userPermissions.filter((permission) => permission.subject === params.slug);
    return permissions.reduce(
      (acc, permission) => {
        var _a2;
        const [actionShorthand] = permission.action.split(".").slice(-1);
        return {
          ...acc,
          [`can${capitalize(actionShorthand)}`]: ((_a2 = permission.properties) == null ? void 0 : _a2.locales) ?? []
        };
      },
      { canCreate: [], canRead: [], canUpdate: [], canDelete: [], canPublish: [] }
    );
  }, [params.slug, userPermissions]);
  const { schema } = useDocument(
    {
      // We can non-null assert these because below we skip the query if they are not present
      collectionType: params.collectionType,
      model: params.slug
    },
    {
      skip: true
    }
  );
  if (doesPluginOptionsHaveI18nLocalized(schema == null ? void 0 : schema.pluginOptions)) {
    return {
      hasI18n: schema.pluginOptions.i18n.localized,
      ...actions
    };
  }
  return {
    hasI18n: false,
    ...actions
  };
};
var i18nApi = adminApi.enhanceEndpoints({
  addTagTypes: ["Locale"]
});
var localesApi = i18nApi.injectEndpoints({
  endpoints: (builder) => ({
    createLocale: builder.mutation({
      query: (data2) => ({
        url: "/i18n/locales",
        method: "POST",
        data: data2
      }),
      invalidatesTags: [{ type: "Locale", id: "LIST" }]
    }),
    deleteLocale: builder.mutation({
      query: (id) => ({
        url: `/i18n/locales/${id}`,
        method: "DELETE"
      }),
      invalidatesTags: (result, error, id) => [{ type: "Locale", id }]
    }),
    getLocales: builder.query({
      query: () => "/i18n/locales",
      providesTags: (res) => [
        { type: "Locale", id: "LIST" },
        ...Array.isArray(res) ? res.map((locale) => ({
          type: "Locale",
          id: locale.id
        })) : []
      ]
    }),
    getDefaultLocales: builder.query({
      query: () => "/i18n/iso-locales"
    }),
    updateLocale: builder.mutation({
      query: ({ id, ...data2 }) => ({
        url: `/i18n/locales/${id}`,
        method: "PUT",
        data: data2
      }),
      invalidatesTags: (result, error, { id }) => [{ type: "Locale", id }]
    })
  })
});
var {
  useCreateLocaleMutation,
  useDeleteLocaleMutation,
  useGetLocalesQuery,
  useGetDefaultLocalesQuery,
  useUpdateLocaleMutation
} = localesApi;
var relationsApi = i18nApi.injectEndpoints({
  overrideExisting: true,
  endpoints: (builder) => ({
    getManyDraftRelationCount: builder.query({
      query: ({ model, ...params }) => ({
        url: `/content-manager/collection-types/${model}/actions/countManyEntriesDraftRelations`,
        method: "GET",
        config: {
          params
        }
      }),
      transformResponse: (response) => response.data
    })
  })
});
var { useGetManyDraftRelationCountQuery } = relationsApi;
var cleanData = (data2, schema, components) => {
  const cleanedData = removeFields(data2, [
    "createdAt",
    "createdBy",
    "updatedAt",
    "updatedBy",
    "id",
    "documentId",
    "publishedAt",
    "strapi_stage",
    "strapi_assignee",
    "locale",
    "status"
  ]);
  const cleanedDataWithoutPasswordAndRelation = recursiveRemoveFieldTypes(
    cleanedData,
    schema,
    components,
    ["relation", "password"]
  );
  return cleanedDataWithoutPasswordAndRelation;
};
var removeFields = (data2, fields) => {
  return Object.keys(data2).reduce((acc, current) => {
    if (fields.includes(current)) {
      return acc;
    }
    acc[current] = data2[current];
    return acc;
  }, {});
};
var recursiveRemoveFieldTypes = (data2, schema, components, fields) => {
  return Object.keys(data2).reduce((acc, current) => {
    const attribute = schema.attributes[current] ?? { type: void 0 };
    if (fields.includes(attribute.type)) {
      return acc;
    }
    if (attribute.type === "dynamiczone") {
      acc[current] = data2[current].map((componentValue, index2) => {
        const { id: _, ...rest } = recursiveRemoveFieldTypes(
          componentValue,
          components[componentValue.__component],
          components,
          fields
        );
        return {
          ...rest,
          __temp_key__: index2 + 1
        };
      });
    } else if (attribute.type === "component") {
      const { repeatable, component } = attribute;
      if (repeatable) {
        acc[current] = (data2[current] ?? []).map((compoData, index2) => {
          const { id: _, ...rest } = recursiveRemoveFieldTypes(
            compoData,
            components[component],
            components,
            fields
          );
          return {
            ...rest,
            __temp_key__: index2 + 1
          };
        });
      } else {
        const { id: _, ...rest } = recursiveRemoveFieldTypes(
          data2[current] ?? {},
          components[component],
          components,
          fields
        );
        acc[current] = rest;
      }
    } else {
      acc[current] = data2[current];
    }
    return acc;
  }, {});
};
var isErrorMessageDescriptor = (object) => {
  return typeof object === "object" && object !== null && "id" in object && "defaultMessage" in object;
};
var EntryValidationText = ({
  status = "draft",
  validationErrors,
  action
}) => {
  const { formatMessage: formatMessage3 } = useIntl();
  const getErrorStr = (key, value) => {
    if (typeof value === "string") {
      return `${key}: ${value}`;
    } else if (isErrorMessageDescriptor(value)) {
      return `${key}: ${formatMessage3(value)}`;
    } else if (Array.isArray(value)) {
      return value.map((v) => getErrorStr(key, v)).join(" ");
    } else if (typeof value === "object" && !Array.isArray(value)) {
      return Object.entries(value).map(([k, v]) => getErrorStr(k, v)).join(" ");
    } else {
      return "";
    }
  };
  if (validationErrors) {
    const validationErrorsMessages = Object.entries(validationErrors).map(([key, value]) => {
      return getErrorStr(key, value);
    }).join(" ");
    return (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, children: [
      (0, import_jsx_runtime.jsx)(ForwardRef$3T, { fill: "danger600" }),
      (0, import_jsx_runtime.jsx)(TooltipImpl, { label: validationErrorsMessages, children: (0, import_jsx_runtime.jsx)(
        Typography,
        {
          maxWidth: "30rem",
          textColor: "danger600",
          variant: "omega",
          fontWeight: "semiBold",
          ellipsis: true,
          children: validationErrorsMessages
        }
      ) })
    ] });
  }
  const getStatusMessage = () => {
    if (action === "bulk-publish") {
      if (status === "published") {
        return {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" }),
          text: formatMessage3({
            id: "content-manager.bulk-publish.already-published",
            defaultMessage: "Already Published"
          }),
          textColor: "success600",
          fontWeight: "bold"
        };
      } else if (status === "modified") {
        return {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$4T, { fill: "alternative600" }),
          text: formatMessage3({
            id: "app.utils.ready-to-publish-changes",
            defaultMessage: "Ready to publish changes"
          })
        };
      } else {
        return {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" }),
          text: formatMessage3({
            id: "app.utils.ready-to-publish",
            defaultMessage: "Ready to publish"
          })
        };
      }
    } else {
      if (status === "draft") {
        return {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" }),
          text: formatMessage3({
            id: "content-manager.bulk-unpublish.already-unpublished",
            defaultMessage: "Already Unpublished"
          }),
          textColor: "success600",
          fontWeight: "bold"
        };
      } else {
        return {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" }),
          text: formatMessage3({
            id: "app.utils.ready-to-unpublish-changes",
            defaultMessage: "Ready to unpublish"
          }),
          textColor: "success600",
          fontWeight: "bold"
        };
      }
    }
  };
  const { icon, text, textColor = "success600", fontWeight = "normal" } = getStatusMessage();
  return (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, children: [
    icon,
    (0, import_jsx_runtime.jsx)(Typography, { textColor, fontWeight, children: text })
  ] });
};
var BoldChunk = (chunks) => (0, import_jsx_runtime.jsx)(Typography, { fontWeight: "bold", children: chunks });
var BulkLocaleActionModal = ({
  headers,
  rows,
  localesMetadata,
  validationErrors = {},
  action
}) => {
  const { formatMessage: formatMessage3 } = useIntl();
  const selectedRows = useTable(
    "BulkLocaleActionModal",
    (state) => state.selectedRows
  );
  const getFormattedCountMessage = () => {
    const currentStatusByLocale = rows.reduce((acc, { locale, status }) => {
      acc[locale] = status;
      return acc;
    }, {});
    const localesWithErrors = Object.keys(validationErrors);
    const publishedCount = selectedRows.filter(
      ({ locale }) => currentStatusByLocale[locale] === "published"
    ).length;
    const draftCount = selectedRows.filter(
      ({ locale }) => (currentStatusByLocale[locale] === "draft" || currentStatusByLocale[locale] === "modified") && !localesWithErrors.includes(locale)
    ).length;
    const withErrorsCount = localesWithErrors.length;
    const messageId = action === "bulk-publish" ? "content-manager.containers.list.selectedEntriesModal.selectedCount.publish" : "content-manager.containers.list.selectedEntriesModal.selectedCount.unpublish";
    const defaultMessage = action === "bulk-publish" ? "<b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} already published. <b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} ready to publish. <b>{withErrorsCount}</b> {withErrorsCount, plural, =0 {entries} one {entry} other {entries}} waiting for action." : "<b>{draftCount}</b> {draftCount, plural, =0 {entries} one {entry} other {entries}} already unpublished. <b>{publishedCount}</b> {publishedCount, plural, =0 {entries} one {entry} other {entries}} ready to unpublish.";
    return formatMessage3(
      {
        id: messageId,
        defaultMessage
      },
      {
        withErrorsCount,
        draftCount,
        publishedCount,
        b: BoldChunk
      }
    );
  };
  return (0, import_jsx_runtime.jsxs)(Modal.Body, { children: [
    (0, import_jsx_runtime.jsx)(Typography, { children: getFormattedCountMessage() }),
    (0, import_jsx_runtime.jsx)(Box, { marginTop: 5, children: (0, import_jsx_runtime.jsxs)(Table.Content, { children: [
      (0, import_jsx_runtime.jsxs)(Table.Head, { children: [
        (0, import_jsx_runtime.jsx)(Table.HeaderCheckboxCell, {}),
        headers.map((head) => (0, import_jsx_runtime.jsx)(Table.HeaderCell, { ...head }, head.name))
      ] }),
      (0, import_jsx_runtime.jsx)(Table.Body, { children: rows.map(({ locale, status }, index2) => {
        var _a2;
        const error = (validationErrors == null ? void 0 : validationErrors[locale]) ?? null;
        const statusVariant = status === "draft" ? "primary" : status === "published" ? "success" : "alternative";
        return (0, import_jsx_runtime.jsxs)(Table.Row, { children: [
          (0, import_jsx_runtime.jsx)(Table.CheckboxCell, { id: locale, "aria-label": `Select ${locale}` }),
          (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: Array.isArray(localesMetadata) ? (_a2 = localesMetadata.find((localeEntry) => localeEntry.code === locale)) == null ? void 0 : _a2.name : locale }) }),
          (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(Box, { display: "flex", children: (0, import_jsx_runtime.jsx)(
            Status,
            {
              display: "flex",
              paddingLeft: "6px",
              paddingRight: "6px",
              paddingTop: "2px",
              paddingBottom: "2px",
              size: "S",
              variant: statusVariant,
              children: (0, import_jsx_runtime.jsx)(Typography, { tag: "span", variant: "pi", fontWeight: "bold", children: capitalize(status) })
            }
          ) }) }),
          (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(EntryValidationText, { validationErrors: error, status, action }) }),
          (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(
            IconButton,
            {
              tag: Link,
              to: {
                search: (0, import_qs.stringify)({ plugins: { i18n: { locale } } })
              },
              label: formatMessage3(
                {
                  id: getTranslation("Settings.list.actions.edit"),
                  defaultMessage: "Edit {name} locale"
                },
                {
                  name: locale
                }
              ),
              variant: "ghost",
              children: (0, import_jsx_runtime.jsx)(ForwardRef$1r, {})
            }
          ) })
        ] }, index2);
      }) })
    ] }) })
  ] });
};
var statusVariants = {
  draft: "secondary",
  published: "success",
  modified: "alternative"
};
var LocaleOption = ({
  isDraftAndPublishEnabled,
  locale,
  status,
  entryExists
}) => {
  const { formatMessage: formatMessage3 } = useIntl();
  if (!entryExists) {
    return formatMessage3(
      {
        id: getTranslation("CMEditViewLocalePicker.locale.create"),
        defaultMessage: "Create <bold>{locale}</bold> locale"
      },
      {
        bold: (locale2) => (0, import_jsx_runtime.jsx)("b", { children: locale2 }),
        locale: locale.name
      }
    );
  }
  return (0, import_jsx_runtime.jsxs)(Flex, { width: "100%", gap: 1, justifyContent: "space-between", children: [
    (0, import_jsx_runtime.jsx)(Typography, { children: locale.name }),
    isDraftAndPublishEnabled ? (0, import_jsx_runtime.jsx)(
      Status,
      {
        display: "flex",
        paddingLeft: "6px",
        paddingRight: "6px",
        paddingTop: "2px",
        paddingBottom: "2px",
        size: "S",
        variant: statusVariants[status],
        children: (0, import_jsx_runtime.jsx)(Typography, { tag: "span", variant: "pi", fontWeight: "bold", children: capitalize(status) })
      }
    ) : null
  ] });
};
var LocalePickerAction = ({
  document,
  meta,
  model,
  collectionType,
  documentId
}) => {
  var _a2, _b;
  const { formatMessage: formatMessage3 } = useIntl();
  const [{ query }, setQuery] = useQueryParams();
  const { hasI18n, canCreate, canRead } = useI18n();
  const { data: locales = [] } = useGetLocalesQuery();
  const currentDesiredLocale = (_b = (_a2 = query.plugins) == null ? void 0 : _a2.i18n) == null ? void 0 : _b.locale;
  const { schema } = useDocument({
    model,
    collectionType,
    documentId,
    params: { locale: currentDesiredLocale }
  });
  const handleSelect = React11.useCallback(
    (value) => {
      setQuery({
        plugins: {
          ...query.plugins,
          i18n: {
            locale: value
          }
        }
      });
    },
    [query.plugins, setQuery]
  );
  React11.useEffect(() => {
    if (!Array.isArray(locales) || !hasI18n) {
      return;
    }
    const doesLocaleExist = locales.find((loc) => loc.code === currentDesiredLocale);
    const defaultLocale = locales.find((locale) => locale.isDefault);
    if (!doesLocaleExist && (defaultLocale == null ? void 0 : defaultLocale.code)) {
      handleSelect(defaultLocale.code);
    }
  }, [handleSelect, hasI18n, locales, currentDesiredLocale]);
  const currentLocale = Array.isArray(locales) ? locales.find((locale) => locale.code === currentDesiredLocale) : void 0;
  const allCurrentLocales = [
    { status: getDocumentStatus(document, meta), locale: currentLocale == null ? void 0 : currentLocale.code },
    ...(meta == null ? void 0 : meta.availableLocales) ?? []
  ];
  if (!hasI18n || !Array.isArray(locales) || locales.length === 0) {
    return null;
  }
  return {
    label: formatMessage3({
      id: getTranslation("Settings.locales.modal.locales.label"),
      defaultMessage: "Locales"
    }),
    options: locales.map((locale) => {
      var _a3;
      const entryWithLocaleExists = allCurrentLocales.some((doc) => doc.locale === locale.code);
      const currentLocaleDoc = allCurrentLocales.find(
        (doc) => "locale" in doc ? doc.locale === locale.code : false
      );
      const permissionsToCheck = currentLocaleDoc ? canRead : canCreate;
      return {
        disabled: !permissionsToCheck.includes(locale.code),
        value: locale.code,
        label: (0, import_jsx_runtime.jsx)(
          LocaleOption,
          {
            isDraftAndPublishEnabled: !!((_a3 = schema == null ? void 0 : schema.options) == null ? void 0 : _a3.draftAndPublish),
            locale,
            status: currentLocaleDoc == null ? void 0 : currentLocaleDoc.status,
            entryExists: entryWithLocaleExists
          }
        ),
        startIcon: !entryWithLocaleExists ? (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}) : null
      };
    }),
    customizeContent: () => currentLocale == null ? void 0 : currentLocale.name,
    onSelect: handleSelect,
    value: currentLocale
  };
};
var getDocumentStatus = (document, meta) => {
  const docStatus = document == null ? void 0 : document.status;
  const statuses = (meta == null ? void 0 : meta.availableStatus) ?? [];
  if (!docStatus) {
    return "draft";
  }
  if (docStatus === "draft" && statuses.find((doc) => doc.publishedAt !== null)) {
    return "published";
  }
  return docStatus;
};
var FillFromAnotherLocaleAction = ({
  documentId,
  meta,
  model,
  collectionType
}) => {
  var _a2, _b;
  const { formatMessage: formatMessage3 } = useIntl();
  const [{ query }] = useQueryParams();
  const { hasI18n } = useI18n();
  const currentDesiredLocale = (_b = (_a2 = query.plugins) == null ? void 0 : _a2.i18n) == null ? void 0 : _b.locale;
  const [localeSelected, setLocaleSelected] = React11.useState(null);
  const setValues = useForm("FillFromAnotherLocale", (state) => state.setValues);
  const { getDocument } = useDocumentActions();
  const { schema, components } = useDocument({
    model,
    documentId,
    collectionType,
    params: { locale: currentDesiredLocale }
  });
  const { data: locales = [] } = useGetLocalesQuery();
  const availableLocales = Array.isArray(locales) ? locales.filter((locale) => meta == null ? void 0 : meta.availableLocales.some((l) => l.locale === locale.code)) : [];
  const fillFromLocale = (onClose) => async () => {
    const response = await getDocument({
      collectionType,
      model,
      documentId,
      params: { locale: localeSelected }
    });
    if (!response || !schema) {
      return;
    }
    const { data: data2 } = response;
    const cleanedData = cleanData(data2, schema, components);
    setValues(cleanedData);
    onClose();
  };
  if (!hasI18n) {
    return null;
  }
  return {
    type: "icon",
    icon: (0, import_jsx_runtime.jsx)(ForwardRef$3J, {}),
    disabled: availableLocales.length === 0,
    label: formatMessage3({
      id: getTranslation("CMEditViewCopyLocale.copy-text"),
      defaultMessage: "Fill in from another locale"
    }),
    dialog: {
      type: "dialog",
      title: formatMessage3({
        id: getTranslation("CMEditViewCopyLocale.dialog.title"),
        defaultMessage: "Confirmation"
      }),
      content: ({ onClose }) => (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
        (0, import_jsx_runtime.jsx)(Dialog.Body, { children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", gap: 3, children: [
          (0, import_jsx_runtime.jsx)(ForwardRef$3, { width: "24px", height: "24px", fill: "danger600" }),
          (0, import_jsx_runtime.jsx)(Typography, { textAlign: "center", children: formatMessage3({
            id: getTranslation("CMEditViewCopyLocale.dialog.body"),
            defaultMessage: "Your current content will be erased and filled by the content of the selected locale:"
          }) }),
          (0, import_jsx_runtime.jsxs)(Field.Root, { width: "100%", children: [
            (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage3({
              id: getTranslation("CMEditViewCopyLocale.dialog.field.label"),
              defaultMessage: "Locale"
            }) }),
            (0, import_jsx_runtime.jsx)(
              SingleSelect,
              {
                value: localeSelected,
                placeholder: formatMessage3({
                  id: getTranslation("CMEditViewCopyLocale.dialog.field.placeholder"),
                  defaultMessage: "Select one locale..."
                }),
                onChange: (value) => setLocaleSelected(value),
                children: availableLocales.map((locale) => (0, import_jsx_runtime.jsx)(SingleSelectOption, { value: locale.code, children: locale.name }, locale.code))
              }
            )
          ] })
        ] }) }),
        (0, import_jsx_runtime.jsx)(Dialog.Footer, { children: (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, width: "100%", children: [
          (0, import_jsx_runtime.jsx)(Button, { flex: "auto", variant: "tertiary", onClick: onClose, children: formatMessage3({
            id: getTranslation("CMEditViewCopyLocale.cancel-text"),
            defaultMessage: "No, cancel"
          }) }),
          (0, import_jsx_runtime.jsx)(Button, { flex: "auto", variant: "success", onClick: fillFromLocale(onClose), children: formatMessage3({
            id: getTranslation("CMEditViewCopyLocale.submit-text"),
            defaultMessage: "Yes, fill in"
          }) })
        ] }) })
      ] })
    }
  };
};
var DeleteLocaleAction = ({
  document,
  documentId,
  model,
  collectionType
}) => {
  var _a2, _b;
  const { formatMessage: formatMessage3 } = useIntl();
  const navigate = useNavigate();
  const { toggleNotification } = useNotification();
  const { delete: deleteAction } = useDocumentActions();
  const { hasI18n, canDelete } = useI18n();
  const [{ query }] = useQueryParams();
  const { data: locales = [] } = useGetLocalesQuery();
  const currentDesiredLocale = (_b = (_a2 = query.plugins) == null ? void 0 : _a2.i18n) == null ? void 0 : _b.locale;
  const locale = !("error" in locales) && locales.find((loc) => loc.code === currentDesiredLocale);
  if (!hasI18n) {
    return null;
  }
  return {
    disabled: (document == null ? void 0 : document.locale) && !canDelete.includes(document.locale) || !document || !document.id,
    position: ["header", "table-row"],
    label: formatMessage3(
      {
        id: getTranslation("actions.delete.label"),
        defaultMessage: "Delete entry ({locale})"
      },
      { locale: locale && locale.name }
    ),
    icon: (0, import_jsx_runtime.jsx)(StyledTrash, {}),
    variant: "danger",
    dialog: {
      type: "dialog",
      title: formatMessage3({
        id: getTranslation("actions.delete.dialog.title"),
        defaultMessage: "Confirmation"
      }),
      content: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", gap: 2, children: [
        (0, import_jsx_runtime.jsx)(ForwardRef$3, { width: "24px", height: "24px", fill: "danger600" }),
        (0, import_jsx_runtime.jsx)(Typography, { tag: "p", variant: "omega", textAlign: "center", children: formatMessage3({
          id: getTranslation("actions.delete.dialog.body"),
          defaultMessage: "Are you sure?"
        }) })
      ] }),
      onConfirm: async () => {
        const unableToDelete = (
          // We are unable to delete a collection type without a document ID
          // & unable to delete generally if there is no document locale
          collectionType !== "single-types" && !documentId || !(document == null ? void 0 : document.locale)
        );
        if (unableToDelete) {
          console.error(
            "You're trying to delete a document without an id or locale, this is likely a bug with Strapi. Please open an issue."
          );
          toggleNotification({
            message: formatMessage3({
              id: getTranslation("actions.delete.error"),
              defaultMessage: "An error occurred while trying to delete the document locale."
            }),
            type: "danger"
          });
          return;
        }
        const res = await deleteAction({
          documentId,
          model,
          collectionType,
          params: { locale: document.locale }
        });
        if (!("error" in res)) {
          navigate({ pathname: `../${collectionType}/${model}` }, { replace: true });
        }
      }
    }
  };
};
var BulkLocaleAction = ({
  document: baseDocument,
  documentId,
  model,
  collectionType,
  action
}) => {
  var _a2;
  const baseLocale = (baseDocument == null ? void 0 : baseDocument.locale) ?? null;
  const [{ query }] = useQueryParams();
  const params = React11.useMemo(() => buildValidParams(query), [query]);
  const isOnPublishedTab = query.status === "published";
  const { formatMessage: formatMessage3 } = useIntl();
  const { hasI18n, canPublish } = useI18n();
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [selectedRows, setSelectedRows] = React11.useState([]);
  const [isDraftRelationConfirmationOpen, setIsDraftRelationConfirmationOpen] = React11.useState(false);
  const { publishMany: publishManyAction, unpublishMany: unpublishManyAction } = useDocumentActions();
  const {
    document,
    meta: documentMeta,
    schema,
    validate
  } = useDocument(
    {
      model,
      collectionType,
      documentId,
      params: {
        locale: baseLocale
      }
    },
    {
      skip: !hasI18n || !baseLocale
    }
  );
  const { data: localesMetadata = [] } = useGetLocalesQuery(hasI18n ? void 0 : skipToken);
  const headers = [
    {
      label: formatMessage3({
        id: "global.name",
        defaultMessage: "Name"
      }),
      name: "name"
    },
    {
      label: formatMessage3({
        id: getTranslation("CMEditViewBulkLocale.status"),
        defaultMessage: "Status"
      }),
      name: "status"
    },
    {
      label: formatMessage3({
        id: getTranslation("CMEditViewBulkLocale.publication-status"),
        defaultMessage: "Publication Status"
      }),
      name: "publication-status"
    }
  ];
  const [rows, validationErrors] = React11.useMemo(() => {
    if (!document || !(documentMeta == null ? void 0 : documentMeta.availableLocales)) {
      return [[], {}];
    }
    const rowsFromMeta = documentMeta == null ? void 0 : documentMeta.availableLocales.map((doc) => {
      const { locale, status } = doc;
      return { locale, status };
    });
    rowsFromMeta.unshift({
      locale: document.locale,
      status: document.status
    });
    const allDocuments = [document, ...(documentMeta == null ? void 0 : documentMeta.availableLocales) ?? []];
    const errors = allDocuments.reduce((errs, document2) => {
      if (!document2) {
        return errs;
      }
      const validation = validate(document2);
      if (validation !== null) {
        errs[document2.locale] = validation;
      }
      return errs;
    }, {});
    return [rowsFromMeta, errors];
  }, [document, documentMeta == null ? void 0 : documentMeta.availableLocales, validate]);
  const isBulkPublish = action === "bulk-publish";
  const localesForAction = selectedRows.reduce((acc, selectedRow) => {
    const isValidLocale = (
      // Validation errors are irrelevant if we are trying to unpublish
      !isBulkPublish || !Object.keys(validationErrors).includes(selectedRow.locale)
    );
    const shouldAddLocale = isBulkPublish ? selectedRow.status !== "published" && isValidLocale : selectedRow.status !== "draft" && isValidLocale;
    if (shouldAddLocale) {
      acc.push(selectedRow.locale);
    }
    return acc;
  }, []);
  const enableDraftRelationsCount = false;
  const {
    data: draftRelationsCount = 0,
    isLoading: isDraftRelationsLoading,
    error: isDraftRelationsError
  } = useGetManyDraftRelationCountQuery(
    {
      model,
      documentIds: [documentId],
      locale: localesForAction
    },
    {
      skip: !enableDraftRelationsCount
    }
  );
  React11.useEffect(() => {
    if (isDraftRelationsError) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(isDraftRelationsError)
      });
    }
  }, [isDraftRelationsError, toggleNotification, formatAPIError]);
  if (!((_a2 = schema == null ? void 0 : schema.options) == null ? void 0 : _a2.draftAndPublish)) {
    return null;
  }
  if (!hasI18n) {
    return null;
  }
  if (!documentId) {
    return null;
  }
  const publish = async () => {
    await publishManyAction({
      model,
      documentIds: [documentId],
      params: {
        ...params,
        locale: localesForAction
      }
    });
    setSelectedRows([]);
  };
  const unpublish = async () => {
    await unpublishManyAction({
      model,
      documentIds: [documentId],
      params: {
        ...params,
        locale: localesForAction
      }
    });
    setSelectedRows([]);
  };
  const handleAction = async () => {
    if (draftRelationsCount > 0) {
      setIsDraftRelationConfirmationOpen(true);
    } else if (isBulkPublish) {
      await publish();
    } else {
      await unpublish();
    }
  };
  if (isDraftRelationConfirmationOpen) {
    return {
      label: formatMessage3({
        id: "app.components.ConfirmDialog.title",
        defaultMessage: "Confirmation"
      }),
      variant: "danger",
      dialog: {
        onCancel: () => {
          setIsDraftRelationConfirmationOpen(false);
        },
        onConfirm: async () => {
          await publish();
          setIsDraftRelationConfirmationOpen(false);
        },
        type: "dialog",
        title: formatMessage3({
          id: getTranslation("actions.publish.dialog.title"),
          defaultMessage: "Confirmation"
        }),
        content: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "center", gap: 2, children: [
          (0, import_jsx_runtime.jsx)(ForwardRef$3, { width: "2.4rem", height: "2.4rem", fill: "danger600" }),
          (0, import_jsx_runtime.jsx)(Typography, { textAlign: "center", children: formatMessage3({
            id: getTranslation("CMEditViewBulkLocale.draft-relation-warning"),
            defaultMessage: "Some locales are related to draft entries. Publishing them could leave broken links in your app."
          }) }),
          (0, import_jsx_runtime.jsx)(Typography, { textAlign: "center", children: formatMessage3({
            id: getTranslation("CMEditViewBulkLocale.continue-confirmation"),
            defaultMessage: "Are you sure you want to continue?"
          }) })
        ] })
      }
    };
  }
  const hasPermission = selectedRows.map(({ locale }) => locale).every((locale) => canPublish.includes(locale));
  return {
    label: formatMessage3({
      id: getTranslation(`CMEditViewBulkLocale.${isBulkPublish ? "publish" : "unpublish"}-title`),
      defaultMessage: `${isBulkPublish ? "Publish" : "Unpublish"} Multiple Locales`
    }),
    variant: isBulkPublish ? "secondary" : "danger",
    icon: isBulkPublish ? (0, import_jsx_runtime.jsx)(ForwardRef$2h, {}) : (0, import_jsx_runtime.jsx)(ForwardRef$3V, {}),
    disabled: isOnPublishedTab || canPublish.length === 0,
    position: ["panel"],
    dialog: {
      type: "modal",
      title: formatMessage3({
        id: getTranslation(`CMEditViewBulkLocale.${isBulkPublish ? "publish" : "unpublish"}-title`),
        defaultMessage: `${isBulkPublish ? "Publish" : "Unpublish"} Multiple Locales`
      }),
      content: () => {
        return (0, import_jsx_runtime.jsx)(
          Table.Root,
          {
            headers,
            rows: rows.map((row) => ({
              ...row,
              id: row.locale
            })),
            selectedRows,
            onSelectedRowsChange: (tableSelectedRows) => setSelectedRows(tableSelectedRows),
            children: (0, import_jsx_runtime.jsx)(
              BulkLocaleActionModal,
              {
                validationErrors,
                headers,
                rows,
                localesMetadata,
                action: action ?? "bulk-publish"
              }
            )
          }
        );
      },
      footer: () => (0, import_jsx_runtime.jsx)(Modal.Footer, { justifyContent: "flex-end", children: (0, import_jsx_runtime.jsx)(
        Button,
        {
          loading: isDraftRelationsLoading,
          disabled: !hasPermission || localesForAction.length === 0,
          variant: "default",
          onClick: handleAction,
          children: formatMessage3({
            id: isBulkPublish ? "app.utils.publish" : "app.utils.unpublish",
            defaultMessage: isBulkPublish ? "Publish" : "Unpublish"
          })
        }
      ) })
    }
  };
};
var BulkLocalePublishAction = (props) => {
  return BulkLocaleAction({ action: "bulk-publish", ...props });
};
var BulkLocaleUnpublishAction = (props) => {
  return BulkLocaleAction({ action: "bulk-unpublish", ...props });
};
var StyledTrash = dt(ForwardRef$j)`
  path {
    fill: currentColor;
  }
`;
var Emphasis = (chunks) => {
  return (0, import_jsx_runtime.jsx)(Typography, { fontWeight: "semiBold", textColor: "danger500", children: chunks });
};
var DeleteModalAdditionalInfo = () => {
  const { hasI18n } = useI18n();
  const { formatMessage: formatMessage3 } = useIntl();
  if (!hasI18n) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(Typography, { textColor: "danger500", children: formatMessage3(
    {
      id: getTranslation("Settings.list.actions.deleteAdditionalInfos"),
      defaultMessage: "This will delete the active locale versions <em>(from Internationalization)</em>"
    },
    {
      em: Emphasis
    }
  ) });
};
var PublishModalAdditionalInfo = () => {
  const { hasI18n } = useI18n();
  const { formatMessage: formatMessage3 } = useIntl();
  if (!hasI18n) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(Typography, { textColor: "danger500", children: formatMessage3(
    {
      id: getTranslation("Settings.list.actions.publishAdditionalInfos"),
      defaultMessage: "This will publish the active locale versions <em>(from Internationalization)</em>"
    },
    {
      em: Emphasis
    }
  ) });
};
var UnpublishModalAdditionalInfo = () => {
  const { hasI18n } = useI18n();
  const { formatMessage: formatMessage3 } = useIntl();
  if (!hasI18n) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(Typography, { textColor: "danger500", children: formatMessage3(
    {
      id: getTranslation("Settings.list.actions.unpublishAdditionalInfos"),
      defaultMessage: "This will unpublish the active locale versions <em>(from Internationalization)</em>"
    },
    {
      em: Emphasis
    }
  ) });
};
var LocalePicker = () => {
  var _a2, _b, _c, _d, _e;
  const { formatMessage: formatMessage3 } = useIntl();
  const [{ query }, setQuery] = useQueryParams();
  const { hasI18n, canRead, canCreate } = useI18n();
  const { data: locales = [] } = useGetLocalesQuery(void 0, {
    skip: !hasI18n
  });
  const handleChange = React11.useCallback(
    (code, replace = false) => {
      setQuery(
        {
          page: 1,
          plugins: { ...query.plugins, i18n: { locale: code } }
        },
        "push",
        replace
      );
    },
    [query.plugins, setQuery]
  );
  React11.useEffect(() => {
    var _a3, _b2;
    if (!Array.isArray(locales) || !hasI18n) {
      return;
    }
    const currentDesiredLocale = (_b2 = (_a3 = query.plugins) == null ? void 0 : _a3.i18n) == null ? void 0 : _b2.locale;
    const doesLocaleExist = locales.find((loc) => loc.code === currentDesiredLocale);
    const defaultLocale = locales.find((locale) => locale.isDefault);
    if (!doesLocaleExist && (defaultLocale == null ? void 0 : defaultLocale.code)) {
      handleChange(defaultLocale.code, true);
    }
  }, [hasI18n, handleChange, locales, (_b = (_a2 = query.plugins) == null ? void 0 : _a2.i18n) == null ? void 0 : _b.locale]);
  if (!hasI18n || !Array.isArray(locales) || locales.length === 0) {
    return null;
  }
  const displayedLocales = locales.filter((locale) => {
    return canCreate.includes(locale.code) || canRead.includes(locale.code);
  });
  return (0, import_jsx_runtime.jsx)(
    SingleSelect,
    {
      size: "S",
      "aria-label": formatMessage3({
        id: getTranslation("actions.select-locale"),
        defaultMessage: "Select locale"
      }),
      value: ((_d = (_c = query.plugins) == null ? void 0 : _c.i18n) == null ? void 0 : _d.locale) || ((_e = locales.find((locale) => locale.isDefault)) == null ? void 0 : _e.code),
      onChange: handleChange,
      children: displayedLocales.map((locale) => (0, import_jsx_runtime.jsx)(SingleSelectOption, { value: locale.code, children: locale.name }, locale.id))
    }
  );
};
var PERMISSIONS = {
  accessMain: [{ action: "plugin::i18n.locale.read", subject: null }],
  create: [{ action: "plugin::i18n.locale.create", subject: null }],
  delete: [{ action: "plugin::i18n.locale.delete", subject: null }],
  update: [{ action: "plugin::i18n.locale.update", subject: null }],
  read: [{ action: "plugin::i18n.locale.read", subject: null }]
};
var mutateEditViewHook = ({ layout }) => {
  if (!("i18n" in layout.options) || typeof layout.options.i18n === "object" && layout.options.i18n !== null && "localized" in layout.options.i18n && !layout.options.i18n.localized) {
    return { layout };
  }
  const components = Object.entries(layout.components).reduce(
    (acc, [key, componentLayout]) => {
      return {
        ...acc,
        [key]: {
          ...componentLayout,
          layout: componentLayout.layout.map((row) => row.map(addLabelActionToField))
        }
      };
    },
    {}
  );
  return {
    layout: {
      ...layout,
      components,
      layout: layout.layout.map((panel) => panel.map((row) => row.map(addLabelActionToField)))
    }
  };
};
var addLabelActionToField = (field) => {
  const isFieldLocalized = doesFieldHaveI18nPluginOpt(field.attribute.pluginOptions) ? field.attribute.pluginOptions.i18n.localized : true;
  const labelActionProps = {
    title: {
      id: isFieldLocalized ? getTranslation("Field.localized") : getTranslation("Field.not-localized"),
      defaultMessage: isFieldLocalized ? "This value is unique for the selected locale" : "This value is the same across all locales"
    },
    icon: isFieldLocalized ? (0, import_jsx_runtime.jsx)(ForwardRef$3D, {}) : (0, import_jsx_runtime.jsx)(ForwardRef$3B, {})
  };
  return {
    ...field,
    labelAction: (0, import_jsx_runtime.jsx)(LabelAction, { ...labelActionProps })
  };
};
var doesFieldHaveI18nPluginOpt = (pluginOpts) => {
  if (!pluginOpts) {
    return false;
  }
  return "i18n" in pluginOpts && typeof pluginOpts.i18n === "object" && pluginOpts.i18n !== null && "localized" in pluginOpts.i18n;
};
var LabelAction = ({ title, icon }) => {
  const { formatMessage: formatMessage3 } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Span, { tag: "span", children: [
    (0, import_jsx_runtime.jsx)(VisuallyHidden, { tag: "span", children: formatMessage3(title) }),
    React11.cloneElement(icon, {
      "aria-hidden": true,
      focusable: false
      // See: https://allyjs.io/tutorials/focusing-in-svg.html#making-svg-elements-focusable
    })
  ] });
};
var Span = dt(Flex)`
  svg {
    width: 12px;
    height: 12px;

    fill: ${({ theme }) => theme.colors.neutral500};

    path {
      fill: ${({ theme }) => theme.colors.neutral500};
    }
  }
`;
var LocaleListCell = ({
  documentId,
  locale: currentLocale,
  collectionType,
  model
}) => {
  const { meta, isLoading } = useDocument({
    documentId,
    collectionType,
    model,
    params: {
      locale: currentLocale
    }
  });
  const { locale: language } = useIntl();
  const { data: locales = [] } = useGetLocalesQuery();
  const formatter = useCollator(language, {
    sensitivity: "base"
  });
  if (!Array.isArray(locales) || isLoading) {
    return null;
  }
  const availableLocales = (meta == null ? void 0 : meta.availableLocales.map((doc) => doc.locale)) ?? [];
  const localesForDocument = locales.reduce((acc, locale) => {
    const createdLocale = [currentLocale, ...availableLocales].find((loc) => {
      return loc === locale.code;
    });
    if (createdLocale) {
      acc.push(locale);
    }
    return acc;
  }, []).map((locale) => {
    if (locale.isDefault) {
      return `${locale.name} (default)`;
    }
    return locale.name;
  }).toSorted((a, b) => formatter.compare(a, b));
  return (0, import_jsx_runtime.jsxs)(Popover.Root, { children: [
    (0, import_jsx_runtime.jsx)(Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "ghost", type: "button", onClick: (e) => e.stopPropagation(), children: (0, import_jsx_runtime.jsxs)(Flex, { minWidth: "100%", alignItems: "center", justifyContent: "center", fontWeight: "regular", children: [
      (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", ellipsis: true, marginRight: 2, children: localesForDocument.join(", ") }),
      (0, import_jsx_runtime.jsx)(Flex, { children: (0, import_jsx_runtime.jsx)(ForwardRef$4D, { width: "1.2rem", height: "1.2rem" }) })
    ] }) }) }),
    (0, import_jsx_runtime.jsx)(Popover.Content, { sideOffset: 16, children: (0, import_jsx_runtime.jsx)("ul", { children: localesForDocument.map((name) => (0, import_jsx_runtime.jsx)(Box, { padding: 3, tag: "li", children: (0, import_jsx_runtime.jsx)(Typography, { children: name }) }, name)) }) })
  ] });
};
var addColumnToTableHook = ({ displayedHeaders, layout }) => {
  const { options } = layout;
  const isFieldLocalized = doesPluginOptionsHaveI18nLocalized(options) ? options.i18n.localized : false;
  if (!isFieldLocalized) {
    return { displayedHeaders, layout };
  }
  return {
    displayedHeaders: [
      ...displayedHeaders,
      {
        attribute: { type: "string" },
        label: {
          id: getTranslation("list-view.table.header.label"),
          defaultMessage: "Available in"
        },
        searchable: false,
        sortable: false,
        name: "locales",
        // @ts-expect-error – ID is seen as number | string; this will change when we move the type over.
        cellFormatter: (props, _header, meta) => (0, import_jsx_runtime.jsx)(LocaleListCell, { ...props, ...meta })
      }
    ],
    layout
  };
};
var addLocaleToReleasesHook = ({ displayedHeaders = [] }) => {
  return {
    displayedHeaders: [
      ...displayedHeaders,
      {
        label: {
          id: "content-releases.page.ReleaseDetails.table.header.label.locale",
          defaultMessage: "locale"
        },
        name: "locale"
      }
    ],
    hasI18nEnabled: true
  };
};
var extendCTBAttributeInitialDataMiddleware = () => {
  return ({ getState }) => (next) => (action) => {
    const enhanceAction = () => {
      try {
        const store = getState();
        const hasi18nEnabled = (0, import_get.default)(
          store,
          [
            "content-type-builder_dataManagerProvider",
            "modifiedData",
            "contentType",
            "schema",
            "pluginOptions",
            "i18n",
            "localized"
          ],
          false
        );
        if (hasi18nEnabled) {
          const pluginOptions = action.options ? { ...action.options.pluginOptions, i18n: { localized: true } } : { i18n: { localized: true } };
          return next({
            ...action,
            options: {
              pluginOptions
            }
          });
        }
        return next(action);
      } catch (err) {
        return next(action);
      }
    };
    if (action.type === "ContentTypeBuilder/FormModal/SET_ATTRIBUTE_DATA_SCHEMA" && action.forTarget === "contentType" && !["relation", "component"].includes(action.attributeType) && !action.isEditing) {
      return enhanceAction();
    }
    if (action.type === "ContentTypeBuilder/FormModal/SET_CUSTOM_FIELD_DATA_SCHEMA" && action.forTarget === "contentType" && !action.isEditing) {
      return enhanceAction();
    }
    if ((action.type === "ContentTypeBuilder/FormModal/RESET_PROPS_AND_SET_FORM_FOR_ADDING_AN_EXISTING_COMPO" || action.type === "ContentTypeBuilder/FormModal/RESET_PROPS_AND_SAVE_CURRENT_DATA") && action.forTarget === "contentType") {
      return enhanceAction();
    }
    return next(action);
  };
};
var extendCTBInitialDataMiddleware = () => {
  return () => (next) => (action) => {
    var _a2, _b;
    if (action.type === "ContentTypeBuilder/FormModal/SET_DATA_TO_EDIT" && action.modalType === "contentType") {
      const i18n = { localized: false };
      const pluginOptions = action.data.pluginOptions ? { ...action.data.pluginOptions, i18n } : { i18n };
      const data2 = { ...action.data, pluginOptions };
      if (action.actionType === "create") {
        return next({ ...action, data: data2 });
      }
      if (!((_b = (_a2 = action.data.pluginOptions) == null ? void 0 : _a2.i18n) == null ? void 0 : _b.localized)) {
        return next({ ...action, data: data2 });
      }
    }
    return next(action);
  };
};
var localeMiddleware = (ctx) => (next) => (permissions) => {
  const match = matchPath("/content-manager/:collectionType/:model?/:id", ctx.pathname);
  if (!match) {
    return next(permissions);
  }
  const search = qs.parse(ctx.search);
  if (typeof search !== "object") {
    return next(permissions);
  }
  if (!("plugins" in search && typeof search.plugins === "object")) {
    return next(permissions);
  }
  if (!("i18n" in search.plugins && typeof search.plugins.i18n === "object" && !Array.isArray(search.plugins.i18n))) {
    return next(permissions);
  }
  const { locale } = search.plugins.i18n;
  if (typeof locale !== "string") {
    return next(permissions);
  }
  const revisedPermissions = permissions.filter(
    (permission) => {
      var _a2;
      return !((_a2 = permission.properties) == null ? void 0 : _a2.locales) || permission.properties.locales.includes(locale);
    }
  );
  return next(revisedPermissions);
};
var prefixPluginTranslations = (trad, pluginId2) => {
  return Object.keys(trad).reduce((acc, current) => {
    acc[`${pluginId2}.${current}`] = trad[current];
    return acc;
  }, {});
};
var mutateCTBContentTypeSchema = (nextSchema, prevSchema) => {
  var _a2, _b;
  if (!doesPluginOptionsHaveI18nLocalized(nextSchema.pluginOptions)) {
    return nextSchema;
  }
  const isNextSchemaLocalized = nextSchema.pluginOptions.i18n.localized;
  const isPrevSchemaLocalized = doesPluginOptionsHaveI18nLocalized(
    (_a2 = prevSchema == null ? void 0 : prevSchema.schema) == null ? void 0 : _a2.pluginOptions
  ) ? (_b = prevSchema == null ? void 0 : prevSchema.schema) == null ? void 0 : _b.pluginOptions.i18n.localized : false;
  if (isNextSchemaLocalized && isPrevSchemaLocalized) {
    return nextSchema;
  }
  if (isNextSchemaLocalized) {
    const attributes = addLocalisationToFields(nextSchema.attributes);
    return { ...nextSchema, attributes };
  }
  if (!isNextSchemaLocalized) {
    const pluginOptions = (0, import_omit.default)(nextSchema.pluginOptions, "i18n");
    const attributes = disableAttributesLocalisation(nextSchema.attributes);
    return { ...nextSchema, pluginOptions, attributes };
  }
  return nextSchema;
};
var addLocalisationToFields = (attributes) => Object.keys(attributes).reduce((acc, current) => {
  const currentAttribute = attributes[current];
  if (LOCALIZED_FIELDS.includes(currentAttribute.type)) {
    const i18n = { localized: true };
    const pluginOptions = currentAttribute.pluginOptions ? { ...currentAttribute.pluginOptions, i18n } : { i18n };
    acc[current] = { ...currentAttribute, pluginOptions };
    return acc;
  }
  acc[current] = currentAttribute;
  return acc;
}, {});
var disableAttributesLocalisation = (attributes) => Object.keys(attributes).reduce((acc, current) => {
  acc[current] = (0, import_omit.default)(attributes[current], "pluginOptions.i18n");
  return acc;
}, {});
var index = {
  register(app) {
    app.addMiddlewares([extendCTBAttributeInitialDataMiddleware, extendCTBInitialDataMiddleware]);
    app.addMiddlewares([() => i18nApi.middleware]);
    app.addReducers({
      [i18nApi.reducerPath]: i18nApi.reducer
    });
    app.addRBACMiddleware([localeMiddleware]);
    app.registerPlugin({
      id: pluginId,
      name: pluginId
    });
  },
  bootstrap(app) {
    app.registerHook("Admin/CM/pages/ListView/inject-column-in-table", addColumnToTableHook);
    app.registerHook("Admin/CM/pages/EditView/mutate-edit-view-layout", mutateEditViewHook);
    app.registerHook(
      "ContentReleases/pages/ReleaseDetails/add-locale-in-releases",
      addLocaleToReleasesHook
    );
    app.addSettingsLink("global", {
      intlLabel: {
        id: getTranslation("plugin.name"),
        defaultMessage: "Internationalization"
      },
      id: "internationalization",
      to: "internationalization",
      Component: () => import("./SettingsPage-D67CaQAB-DN67WMKT.js").then((mod) => ({ default: mod.ProtectedSettingsPage })),
      permissions: PERMISSIONS.accessMain
    });
    const contentManager = app.getPlugin("content-manager");
    contentManager.apis.addDocumentHeaderAction([LocalePickerAction, FillFromAnotherLocaleAction]);
    contentManager.apis.addDocumentAction((actions) => {
      const indexOfDeleteAction = actions.findIndex((action) => action.type === "delete");
      actions.splice(indexOfDeleteAction, 0, DeleteLocaleAction);
      return actions;
    });
    contentManager.apis.addDocumentAction((actions) => {
      actions.splice(2, 0, BulkLocalePublishAction);
      actions.splice(5, 0, BulkLocaleUnpublishAction);
      return actions;
    });
    contentManager.injectComponent("listView", "actions", {
      name: "i18n-locale-filter",
      Component: LocalePicker
    });
    contentManager.injectComponent("listView", "publishModalAdditionalInfos", {
      name: "i18n-publish-bullets-in-modal",
      Component: PublishModalAdditionalInfo
    });
    contentManager.injectComponent("listView", "unpublishModalAdditionalInfos", {
      name: "i18n-unpublish-bullets-in-modal",
      Component: UnpublishModalAdditionalInfo
    });
    contentManager.injectComponent("listView", "deleteModalAdditionalInfos", {
      name: "i18n-delete-bullets-in-modal",
      Component: DeleteModalAdditionalInfo
    });
    const ctbPlugin = app.getPlugin("content-type-builder");
    if (ctbPlugin) {
      const ctbFormsAPI = ctbPlugin.apis.forms;
      ctbFormsAPI.addContentTypeSchemaMutation(mutateCTBContentTypeSchema);
      ctbFormsAPI.components.add({ id: "checkboxConfirmation", component: CheckboxConfirmation });
      ctbFormsAPI.extendContentType({
        validator: () => ({
          i18n: create2().shape({
            localized: create()
          })
        }),
        form: {
          advanced() {
            return [
              {
                name: "pluginOptions.i18n.localized",
                description: {
                  id: getTranslation("plugin.schema.i18n.localized.description-content-type"),
                  defaultMessage: "Allows translating an entry into different languages"
                },
                type: "checkboxConfirmation",
                intlLabel: {
                  id: getTranslation("plugin.schema.i18n.localized.label-content-type"),
                  defaultMessage: "Localization"
                }
              }
            ];
          }
        }
      });
      ctbFormsAPI.extendFields(LOCALIZED_FIELDS, {
        validator: (args) => ({
          i18n: create2().shape({
            localized: create().test({
              name: "ensure-unique-localization",
              message: getTranslation("plugin.schema.i18n.ensure-unique-localization"),
              test(value) {
                if (value === void 0 || value) {
                  return true;
                }
                const unique = (0, import_get.default)(args, ["3", "modifiedData", "unique"], null);
                if (unique && !value) {
                  return false;
                }
                return true;
              }
            })
          })
        }),
        form: {
          advanced({ contentTypeSchema, forTarget, type, step }) {
            if (forTarget !== "contentType") {
              return [];
            }
            const hasI18nEnabled = (0, import_get.default)(
              contentTypeSchema,
              ["schema", "pluginOptions", "i18n", "localized"],
              false
            );
            if (!hasI18nEnabled) {
              return [];
            }
            if (type === "component" && step === "1") {
              return [];
            }
            return [
              {
                name: "pluginOptions.i18n.localized",
                description: {
                  id: getTranslation("plugin.schema.i18n.localized.description-field"),
                  defaultMessage: "The field can have different values in each locale"
                },
                type: "checkbox",
                intlLabel: {
                  id: getTranslation("plugin.schema.i18n.localized.label-field"),
                  defaultMessage: "Enable localization for this field"
                }
              }
            ];
          }
        }
      });
    }
  },
  async registerTrads({ locales }) {
    const importedTrads = await Promise.all(
      locales.map((locale) => {
        return __variableDynamicImportRuntimeHelper(Object.assign({ "./translations/de.json": () => import("./de-9eCAqqrB-DIKNJRVS.js"), "./translations/dk.json": () => import("./dk-2qBjxt-P-4UCWN4LW.js"), "./translations/en.json": () => import("./en-DlXfy6Gy-SZG3UESB.js"), "./translations/es.json": () => import("./es-DlmMVaBG-LME6H2BI.js"), "./translations/fr.json": () => import("./fr-3S6ke71d-G6MKIIOM.js"), "./translations/ko.json": () => import("./ko-qTjQ8IMw-TJAF4GJ5.js"), "./translations/pl.json": () => import("./pl-B67TSHqT-Y27R7J4E.js"), "./translations/ru.json": () => import("./ru-hagMa57T-SAU2A6I4.js"), "./translations/tr.json": () => import("./tr-Dw_jmkG--U64YYHME.js"), "./translations/zh-Hans.json": () => import("./zh-Hans-Dyc-aR-h-BRU2ZDHC.js"), "./translations/zh.json": () => import("./zh-57YM4amO-DSRSNHHU.js") }), `./translations/${locale}.json`).then(({ default: data2 }) => {
          return {
            data: prefixPluginTranslations(data2, pluginId),
            locale
          };
        }).catch(() => {
          return {
            data: {},
            locale
          };
        });
      })
    );
    return Promise.resolve(importedTrads);
  }
};

export {
  useIntl,
  getTranslation,
  useCreateLocaleMutation,
  useDeleteLocaleMutation,
  useGetLocalesQuery,
  useGetDefaultLocalesQuery,
  useUpdateLocaleMutation,
  PERMISSIONS,
  index
};
//# sourceMappingURL=chunk-3UWI42TF.js.map
