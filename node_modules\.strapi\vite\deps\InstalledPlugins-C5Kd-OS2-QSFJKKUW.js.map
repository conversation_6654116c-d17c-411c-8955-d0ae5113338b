{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/InstalledPlugins.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { Table, Tbody, Td, Th, Thead, Tr, Typography, useNotifyAT } from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\n\nimport { Layouts } from '../../../components/Layouts/Layout';\nimport { Page } from '../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../core/store/hooks';\nimport { useNotification } from '../../../features/Notifications';\nimport { useAPIErrorHandler } from '../../../hooks/useAPIErrorHandler';\nimport { useGetPluginsQuery } from '../../../services/admin';\n\nconst InstalledPlugins = () => {\n  const { formatMessage } = useIntl();\n  const { notifyStatus } = useNotifyAT();\n  const { toggleNotification } = useNotification();\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  const { isLoading, data, error } = useGetPluginsQuery();\n\n  React.useEffect(() => {\n    if (data) {\n      notifyStatus(\n        formatMessage(\n          {\n            id: 'app.utils.notify.data-loaded',\n            defaultMessage: 'The {target} has loaded',\n          },\n          {\n            target: formatMessage({\n              id: 'global.plugins',\n              defaultMessage: 'Plugins',\n            }),\n          }\n        )\n      );\n    }\n\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [data, error, formatAPIError, formatMessage, notifyStatus, toggleNotification]);\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: 'global.plugins',\n            defaultMessage: 'Plugins',\n          })}\n          subtitle={formatMessage({\n            id: 'app.components.ListPluginsPage.description',\n            defaultMessage: 'List of the installed plugins in the project.',\n          })}\n        />\n        <Layouts.Content>\n          <Table colCount={2} rowCount={data?.plugins?.length ?? 0 + 1}>\n            <Thead>\n              <Tr>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.name',\n                      defaultMessage: 'Name',\n                    })}\n                  </Typography>\n                </Th>\n                <Th>\n                  <Typography variant=\"sigma\" textColor=\"neutral600\">\n                    {formatMessage({\n                      id: 'global.description',\n                      defaultMessage: 'description',\n                    })}\n                  </Typography>\n                </Th>\n              </Tr>\n            </Thead>\n            <Tbody>\n              {data?.plugins.map(({ name, displayName, description }) => {\n                return (\n                  <Tr key={name}>\n                    <Td>\n                      <Typography textColor=\"neutral800\" variant=\"omega\" fontWeight=\"bold\">\n                        {formatMessage({\n                          id: `global.plugins.${name}`,\n                          defaultMessage: displayName,\n                        })}\n                      </Typography>\n                    </Td>\n                    <Td>\n                      <Typography textColor=\"neutral800\">\n                        {formatMessage({\n                          id: `global.plugins.${name}.description`,\n                          defaultMessage: description,\n                        })}\n                      </Typography>\n                    </Td>\n                  </Tr>\n                );\n              })}\n            </Tbody>\n          </Table>\n        </Layouts.Content>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\nconst ProtectedInstalledPlugins = () => {\n  const { formatMessage } = useIntl();\n  const permissions = useTypedSelector((state) => state.admin_app.permissions);\n\n  return (\n    <Page.Protect permissions={permissions.marketplace?.main}>\n      <Page.Title>\n        {formatMessage({\n          id: 'global.plugins',\n          defaultMessage: 'Plugins',\n        })}\n      </Page.Title>\n      <InstalledPlugins />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedInstalledPlugins, InstalledPlugins };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYA,IAAM,mBAAmB,MAAM;;AACvB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,aAAa,IAAI,YAAY;AAC/B,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AAEvE,QAAM,EAAE,WAAW,MAAM,MAAA,IAAU,mBAAmB;AAEtD,EAAM,gBAAU,MAAM;AACpB,QAAI,MAAM;AACR;QACE;UACE;YACE,IAAI;YACJ,gBAAgB;UAAA;UAElB;YACE,QAAQ,cAAc;cACpB,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QACH;MACF;IACF;AAGF,QAAI,OAAO;AACU,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IAAA;EACH,GACC,CAAC,MAAM,OAAO,gBAAgB,eAAe,cAAc,kBAAkB,CAAC;AAEjF,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAA,CAAa;EAAA;AAGvB,aAAA,wBACG,QAAQ,MAAR,EACC,cAAC,yBAAA,KAAK,MAAL,EACC,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IAAA;QAEF,wBAAA,QAAQ,SAAR,EACC,cAAC,yBAAA,OAAA,EAAM,UAAU,GAAG,YAAU,kCAAM,YAAN,mBAAe,WAAU,IAAI,GACzD,UAAA;UAAC,wBAAA,OAAA,EACC,cAAA,yBAAC,IACC,EAAA,UAAA;YAAA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EAAA,CACH,EACF,CAAA;YACA,wBAAC,IAAA,EACC,cAAC,wBAAA,YAAA,EAAW,SAAQ,SAAQ,WAAU,cACnC,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EAAA,CACH,EACF,CAAA;MAAA,EAAA,CACF,EACF,CAAA;UACA,wBAAC,OACE,EAAA,UAAA,6BAAM,QAAQ,IAAI,CAAC,EAAE,MAAM,aAAa,YAAA,MAAkB;AACzD,mBAAA,yBACG,IACC,EAAA,UAAA;cAAC,wBAAA,IAAA,EACC,cAAA,wBAAC,YAAW,EAAA,WAAU,cAAa,SAAQ,SAAQ,YAAW,QAC3D,UAAc,cAAA;YACb,IAAI,kBAAkB,IAAI;YAC1B,gBAAgB;UAAA,CACjB,EAAA,CACH,EACF,CAAA;cAAA,wBACC,IACC,EAAA,cAAA,wBAAC,YAAW,EAAA,WAAU,cACnB,UAAc,cAAA;YACb,IAAI,kBAAkB,IAAI;YAC1B,gBAAgB;UAAA,CACjB,EAAA,CACH,EACF,CAAA;QAAA,EAAA,GAhBO,IAiBT;MAAA,GAGN,CAAA;IAAA,EAAA,CACF,EACF,CAAA;EAAA,EAAA,CACF,EACF,CAAA;AAEJ;AAEA,IAAM,4BAA4B,MAAM;;AAChC,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,cAAc,iBAAiB,CAAC,UAAU,MAAM,UAAU,WAAW;AAE3E,aAAA,yBACG,KAAK,SAAL,EAAa,cAAa,iBAAY,gBAAZ,mBAAyB,MAClD,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAc,cAAA;MACb,IAAI;MACJ,gBAAgB;IAAA,CACjB,EACH,CAAA;QAAA,wBACC,kBAAiB,CAAA,CAAA;EAAA,EACpB,CAAA;AAEJ;", "names": []}