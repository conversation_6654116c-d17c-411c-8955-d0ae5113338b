import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/ru-VWy-IB7K.mjs
var ru = {
  "BoundRoute.title": "Связать путь с",
  "EditForm.inputSelect.description.role": "При регистрации пользователи будут иметь выбранную роль.",
  "EditForm.inputSelect.label.role": "Роль по умолчанию для новых пользователей",
  "EditForm.inputToggle.description.email": "Запретить пользователю создавать несколько учётных записей, используя один и тот же адрес электронной почты, у разных поставщиков аутентификации.",
  "EditForm.inputToggle.description.email-confirmation": "Если включено (ON), при регистрации пользователи будут получать письмо для подтверждения адреса электронной почты.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "Укажите URL-адрес для перенаправления пользователей после подтверждения адреса электронной почты.",
  "EditForm.inputToggle.description.email-reset-password": "URL-адрес страницы для сброса пароля учётной записи пользователя",
  "EditForm.inputToggle.description.sign-up": "Если выключено (OFF), процесс регистрации пользователей запрещен. Никто не может зарегистрироваться, независимо от провайдера.",
  "EditForm.inputToggle.label.email": "Одна учётная запись на один адрес электронной почты",
  "EditForm.inputToggle.label.email-confirmation": "Включить подтверждение по электронной почте",
  "EditForm.inputToggle.label.email-confirmation-redirection": "URL-адрес для перенаправления",
  "EditForm.inputToggle.label.email-reset-password": "Страница сброса пароля",
  "EditForm.inputToggle.label.sign-up": "Включить регистрации",
  "EditForm.inputToggle.placeholder.email-confirmation-redirection": "например: https://yourfrontend.com/email-confirmation-redirection",
  "EditForm.inputToggle.placeholder.email-reset-password": "например: https://yourfrontend.com/reset-password",
  "EditPage.form.roles": "Сведения о роли",
  "Email.template.data.loaded": "Шаблоны автоматических писем для электронной почты были загружены",
  "Email.template.email_confirmation": "Адреса электронной почты с письмом о подтверждении",
  "Email.template.form.edit.label": "Редактировать шаблон",
  "Email.template.table.action.label": "действие",
  "Email.template.table.icon.label": "иконка",
  "Email.template.table.name.label": "название",
  "Form.advancedSettings.data.loaded": "Данные расширенных настроек были загружены",
  "HeaderNav.link.advancedSettings": "Расширенные настройки",
  "HeaderNav.link.emailTemplates": "Шаблоны писем",
  "HeaderNav.link.providers": "Провайдеры",
  "Plugin.permissions.plugins.description": "Определите все разрешенные действия для плагина {name}.",
  "Plugins.header.description": "Ниже перечислены только действия, связанные с путём.",
  "Plugins.header.title": "Разрешения",
  "Policies.header.hint": "Выберите действия приложения или плагина и нажмите на значок шестерёнки, чтобы отобразить связанный путь",
  "Policies.header.title": "Расширенные настройки",
  "PopUpForm.Email.email_templates.inputDescription": "Если вы не уверены, как использовать переменные — {link}",
  "PopUpForm.Email.link.documentation": "ознакомьтесь с нашей документацией.",
  "PopUpForm.Email.options.from.email.label": "Электронная почта отправителя",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Имя отправителя",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "Сообщение",
  "PopUpForm.Email.options.object.label": "Тема",
  "PopUpForm.Email.options.object.placeholder": "Пожалуйста, подтвердите свой адрес электронной почты для %APP_NAME%",
  "PopUpForm.Email.options.response_email.label": "Электронная почта для ответов",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Если этот параметр отключен, пользователи не смогут использовать этого поставщика.",
  "PopUpForm.Providers.enabled.label": "Включено",
  "PopUpForm.Providers.key.label": "ID клиента",
  "PopUpForm.Providers.key.placeholder": "TEXT",
  "PopUpForm.Providers.redirectURL.front-end.label": "URL-адрес перенаправления на ваше приложение",
  "PopUpForm.Providers.redirectURL.label": "URL-адрес перенаправления, который нужно добавить в {provider} конфигурации приложения",
  "PopUpForm.Providers.secret.label": "Client Secret",
  "PopUpForm.Providers.secret.placeholder": "TEXT",
  "PopUpForm.Providers.subdomain.label": "Хост URI (Поддомен)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "Редактировать шаблон письма",
  "PopUpForm.header.edit.providers": "Редактировать провайдера",
  "Providers.data.loaded": "Провайдеры были загружены",
  "Providers.status": "Статус",
  "Roles.empty": "У вас пока нет никаких ролей.",
  "Roles.empty.search": "Ни одна роль не соответствует поисковому запросу.",
  "Settings.roles.deleted": "Роль удалена",
  "Settings.roles.edited": "Роль отредактирована",
  "Settings.section-label": "Плагин «Пользователи и Разрешения»",
  "components.Input.error.validation.email": "Неверный адрес электронной почты",
  "components.Input.error.validation.json": "Это не соответствует формату JSON",
  "components.Input.error.validation.max": "Значение слишком велико.",
  "components.Input.error.validation.maxLength": "Значение слишком длинное.",
  "components.Input.error.validation.min": "Значение слишком мало.",
  "components.Input.error.validation.minLength": "Значение слишком короткое.",
  "components.Input.error.validation.minSupMax": "Не может быть выше",
  "components.Input.error.validation.regex": "Значение не соответствует регулярному выражению.",
  "components.Input.error.validation.required": "Это значение является обязательным.",
  "components.Input.error.validation.unique": "Это значение уже используется.",
  "notification.success.submit": "Настройки были обновлены",
  "page.title": "Настройки — Роли",
  "plugin.description.long": "Защитите свой API с помощью полноценного процесса аутентификации, основанного на JWT. Этот плагин также имеет настройки стратегии ACL, которые позволяют вам управлять разрешениями между группами пользователей.",
  "plugin.description.short": "Защитите свой API с помощью полноценного процесса аутентификации, основанного на JWT.",
  "plugin.name": "Пользователи и Разрешения",
  "popUpWarning.button.cancel": "Отменить",
  "popUpWarning.button.confirm": "Подтвердить",
  "popUpWarning.title": "Пожалуйста подтвердите",
  "popUpWarning.warning.cancel": "Вы уверены, что хотите отменить свои изменения?"
};
export {
  ru as default
};
//# sourceMappingURL=ru-VWy-IB7K-FQXZINQQ.js.map
