import {
  RELEASE_SCHEMA,
  utcToZonedTime,
  zonedTimeToUtc
} from "./chunk-FHDIK6Y5.js";
import {
  Form,
  Formik,
  PERMISSIONS,
  ReleaseActionMenu,
  ReleaseActionOptions,
  getTimezoneOffset,
  getTimezones,
  pluginId,
  releaseApi,
  useCreateReleaseMutation,
  useDeleteReleaseMutation,
  useFormikContext,
  useGetReleaseActionsQuery,
  useGetReleaseQuery,
  useGetReleaseSettingsQuery,
  useGetReleasesQuery,
  usePublishReleaseMutation,
  useUpdateReleaseActionMutation,
  useUpdateReleaseMutation
} from "./chunk-6NLSGXWT.js";
import {
  useIntl
} from "./chunk-DC3UNANX.js";
import "./chunk-C7H2BX76.js";
import "./chunk-EGRHWZRV.js";
import {
  useDocument
} from "./chunk-D63J2BWQ.js";
import "./chunk-RPX6VIML.js";
import {
  useLicenseLimits
} from "./chunk-N562NCQ4.js";
import {
  format,
  intervalToDuration,
  isPast
} from "./chunk-7LS5AJR5.js";
import {
  formatISO
} from "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  BackButton,
  ConfirmDialog,
  Layouts,
  Pagination,
  Table
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler,
  useNotification,
  useQueryParams,
  useRBAC,
  useStrapiApp,
  useTracking
} from "./chunk-4C2ZQ5OG.js";
import {
  Alert,
  Badge,
  Box,
  Button,
  CheckboxImpl,
  Combobox,
  DatePicker$1,
  Dialog,
  Divider,
  EmptyStateLayout,
  Field,
  Flex,
  Grid,
  Link as Link2,
  LinkButton,
  Main,
  MenuItem,
  Modal,
  Option,
  Popover,
  SimpleMenu,
  SingleSelect,
  SingleSelectOption,
  Tabs,
  Td,
  TextInput,
  TimePicker$1,
  Tr,
  Typography,
  isFetchError,
  require_lib,
  useDispatch
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  Link,
  NavLink,
  Navigate,
  Route,
  Routes,
  useLocation,
  useNavigate,
  useParams
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1R,
  ForwardRef$1d,
  ForwardRef$1r,
  ForwardRef$3T,
  ForwardRef$4D,
  ForwardRef$4T,
  ForwardRef$4n,
  ForwardRef$j
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-releases/dist/_chunks/App-BX6_LcmS.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
var StyledPopoverFlex = dt(Flex)`
  width: 100%;
  max-width: 256px;

  & > * {
    border-bottom: 1px solid ${({ theme }) => theme.colors.neutral150};
  }

  & > *:last-child {
    border-bottom: none;
  }
`;
var EntryStatusTrigger = ({
  action,
  status,
  hasErrors,
  requiredStage,
  entryStage
}) => {
  const { formatMessage } = useIntl();
  if (action === "publish") {
    if (hasErrors || requiredStage && requiredStage.id !== (entryStage == null ? void 0 : entryStage.id)) {
      return (0, import_jsx_runtime.jsx)(Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
        Button,
        {
          variant: "ghost",
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3T, { fill: "danger600" }),
          endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4D, {}),
          children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "danger600", variant: "omega", fontWeight: "bold", children: formatMessage({
            id: "content-releases.pages.ReleaseDetails.entry-validation.not-ready",
            defaultMessage: "Not ready to publish"
          }) })
        }
      ) });
    }
    if (status === "draft") {
      return (0, import_jsx_runtime.jsx)(Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
        Button,
        {
          variant: "ghost",
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" }),
          endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4D, {}),
          children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "success600", variant: "omega", fontWeight: "bold", children: formatMessage({
            id: "content-releases.pages.ReleaseDetails.entry-validation.ready-to-publish",
            defaultMessage: "Ready to publish"
          }) })
        }
      ) });
    }
    if (status === "modified") {
      return (0, import_jsx_runtime.jsx)(Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
        Button,
        {
          variant: "ghost",
          startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4T, { fill: "alternative600" }),
          endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4D, {}),
          children: (0, import_jsx_runtime.jsx)(Typography, { variant: "omega", fontWeight: "bold", textColor: "alternative600", children: formatMessage({
            id: "content-releases.pages.ReleaseDetails.entry-validation.modified",
            defaultMessage: "Ready to publish changes"
          }) })
        }
      ) });
    }
    return (0, import_jsx_runtime.jsx)(Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
      Button,
      {
        variant: "ghost",
        startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" }),
        endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4D, {}),
        children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "success600", variant: "omega", fontWeight: "bold", children: formatMessage({
          id: "content-releases.pages.ReleaseDetails.entry-validation.already-published",
          defaultMessage: "Already published"
        }) })
      }
    ) });
  }
  if (status === "published") {
    return (0, import_jsx_runtime.jsx)(Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(
      Button,
      {
        variant: "ghost",
        startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" }),
        endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4D, {}),
        children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "success600", variant: "omega", fontWeight: "bold", children: formatMessage({
          id: "content-releases.pages.ReleaseDetails.entry-validation.ready-to-unpublish",
          defaultMessage: "Ready to unpublish"
        }) })
      }
    ) });
  }
  return (0, import_jsx_runtime.jsx)(Popover.Trigger, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "ghost", startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" }), endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4D, {}), children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "success600", variant: "omega", fontWeight: "bold", children: formatMessage({
    id: "content-releases.pages.ReleaseDetails.entry-validation.already-unpublished",
    defaultMessage: "Already unpublished"
  }) }) }) });
};
var FieldsValidation = ({
  hasErrors,
  errors,
  kind,
  contentTypeUid,
  documentId,
  locale
}) => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", gap: 1, width: "100%", padding: 5, children: [
    (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, width: "100%", children: [
      (0, import_jsx_runtime.jsx)(Typography, { fontWeight: "bold", children: formatMessage({
        id: "content-releases.pages.ReleaseDetails.entry-validation.fields",
        defaultMessage: "Fields"
      }) }),
      hasErrors ? (0, import_jsx_runtime.jsx)(ForwardRef$3T, { fill: "danger600" }) : (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" })
    ] }),
    (0, import_jsx_runtime.jsx)(Typography, { width: "100%", textColor: "neutral600", children: hasErrors ? formatMessage(
      {
        id: "content-releases.pages.ReleaseDetails.entry-validation.fields.error",
        defaultMessage: "{errors} errors on fields."
      },
      { errors: errors ? Object.keys(errors).length : 0 }
    ) : formatMessage({
      id: "content-releases.pages.ReleaseDetails.entry-validation.fields.success",
      defaultMessage: "All fields are filled correctly."
    }) }),
    hasErrors && (0, import_jsx_runtime.jsx)(
      LinkButton,
      {
        tag: Link,
        to: {
          pathname: `/content-manager/${kind === "collectionType" ? "collection-types" : "single-types"}/${contentTypeUid}/${documentId}`,
          search: locale ? (0, import_qs.stringify)({
            plugins: {
              i18n: {
                locale
              }
            }
          }) : ""
        },
        variant: "secondary",
        fullWidth: true,
        state: { forceValidation: true },
        children: formatMessage({
          id: "content-releases.pages.ReleaseDetails.entry-validation.fields.see-errors",
          defaultMessage: "See errors"
        })
      }
    )
  ] });
};
var getReviewStageIcon = ({
  contentTypeHasReviewWorkflow,
  requiredStage,
  entryStage
}) => {
  if (!contentTypeHasReviewWorkflow) {
    return (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "neutral200" });
  }
  if (requiredStage && requiredStage.id !== (entryStage == null ? void 0 : entryStage.id)) {
    return (0, import_jsx_runtime.jsx)(ForwardRef$3T, { fill: "danger600" });
  }
  return (0, import_jsx_runtime.jsx)(ForwardRef$4n, { fill: "success600" });
};
var getReviewStageMessage = ({
  contentTypeHasReviewWorkflow,
  requiredStage,
  entryStage,
  formatMessage
}) => {
  if (!contentTypeHasReviewWorkflow) {
    return formatMessage({
      id: "content-releases.pages.ReleaseDetails.entry-validation.review-stage.not-enabled",
      defaultMessage: "This entry is not associated to any workflow."
    });
  }
  if (requiredStage && requiredStage.id !== (entryStage == null ? void 0 : entryStage.id)) {
    return formatMessage(
      {
        id: "content-releases.pages.ReleaseDetails.entry-validation.review-stage.not-ready",
        defaultMessage: "This entry is not at the required stage for publishing. ({stageName})"
      },
      {
        stageName: (requiredStage == null ? void 0 : requiredStage.name) ?? ""
      }
    );
  }
  if (requiredStage && requiredStage.id === (entryStage == null ? void 0 : entryStage.id)) {
    return formatMessage(
      {
        id: "content-releases.pages.ReleaseDetails.entry-validation.review-stage.ready",
        defaultMessage: "This entry is at the required stage for publishing. ({stageName})"
      },
      {
        stageName: (requiredStage == null ? void 0 : requiredStage.name) ?? ""
      }
    );
  }
  return formatMessage({
    id: "content-releases.pages.ReleaseDetails.entry-validation.review-stage.stage-not-required",
    defaultMessage: "No required stage for publication"
  });
};
var ReviewStageValidation = ({
  contentTypeHasReviewWorkflow,
  requiredStage,
  entryStage
}) => {
  const { formatMessage } = useIntl();
  const Icon = getReviewStageIcon({
    contentTypeHasReviewWorkflow,
    requiredStage,
    entryStage
  });
  return (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", gap: 1, width: "100%", padding: 5, children: [
    (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, width: "100%", children: [
      (0, import_jsx_runtime.jsx)(Typography, { fontWeight: "bold", children: formatMessage({
        id: "content-releases.pages.ReleaseDetails.entry-validation.review-stage",
        defaultMessage: "Review stage"
      }) }),
      Icon
    ] }),
    (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral600", children: getReviewStageMessage({
      contentTypeHasReviewWorkflow,
      requiredStage,
      entryStage,
      formatMessage
    }) })
  ] });
};
var EntryValidationPopover = ({
  schema,
  entry,
  status,
  action
}) => {
  const { validate, isLoading } = useDocument(
    {
      collectionType: (schema == null ? void 0 : schema.kind) ?? "",
      model: (schema == null ? void 0 : schema.uid) ?? ""
    },
    {
      // useDocument makes a request to get more data about the entry, but we only want to have the validation function so we skip the request
      skip: true
    }
  );
  const errors = isLoading ? null : validate(entry);
  const hasErrors = errors ? Object.keys(errors).length > 0 : false;
  const contentTypeHasReviewWorkflow = (schema == null ? void 0 : schema.hasReviewWorkflow) ?? false;
  const requiredStage = schema == null ? void 0 : schema.stageRequiredToPublish;
  const entryStage = entry.strapi_stage;
  if (isLoading) {
    return null;
  }
  return (0, import_jsx_runtime.jsxs)(Popover.Root, { children: [
    (0, import_jsx_runtime.jsx)(
      EntryStatusTrigger,
      {
        action,
        status,
        hasErrors,
        requiredStage,
        entryStage
      }
    ),
    (0, import_jsx_runtime.jsx)(Popover.Content, { children: (0, import_jsx_runtime.jsxs)(StyledPopoverFlex, { direction: "column", children: [
      (0, import_jsx_runtime.jsx)(
        FieldsValidation,
        {
          hasErrors,
          errors,
          contentTypeUid: schema == null ? void 0 : schema.uid,
          kind: schema == null ? void 0 : schema.kind,
          documentId: entry.documentId,
          locale: entry.locale
        }
      ),
      (0, import_jsx_runtime.jsx)(
        ReviewStageValidation,
        {
          contentTypeHasReviewWorkflow,
          requiredStage,
          entryStage
        }
      )
    ] }) })
  ] });
};
var intervals = ["years", "months", "days", "hours", "minutes", "seconds"];
var RelativeTime$1 = React.forwardRef(
  ({ timestamp, customIntervals = [], ...restProps }, forwardedRef) => {
    const { formatRelativeTime, formatDate, formatTime } = useIntl();
    const interval = intervalToDuration({
      start: timestamp,
      end: Date.now()
      // see https://github.com/date-fns/date-fns/issues/2891 – No idea why it's all partial it returns it every time.
    });
    const unit = intervals.find((intervalUnit) => {
      return interval[intervalUnit] > 0 && Object.keys(interval).includes(intervalUnit);
    });
    const relativeTime = isPast(timestamp) ? -interval[unit] : interval[unit];
    const customInterval = customIntervals.find(
      (custom) => interval[custom.unit] < custom.threshold
    );
    const displayText = customInterval ? customInterval.text : formatRelativeTime(relativeTime, unit, { numeric: "auto" });
    return (0, import_jsx_runtime.jsx)(
      "time",
      {
        ref: forwardedRef,
        dateTime: timestamp.toISOString(),
        role: "time",
        title: `${formatDate(timestamp)} ${formatTime(timestamp)}`,
        ...restProps,
        children: displayText
      }
    );
  }
);
var ReleaseModal = ({
  handleClose,
  open,
  handleSubmit,
  initialValues,
  isLoading = false
}) => {
  const { formatMessage } = useIntl();
  const { pathname } = useLocation();
  const isCreatingRelease = pathname === `/plugins/${pluginId}`;
  const { timezoneList, systemTimezone = { value: "UTC+00:00-Africa/Abidjan " } } = getTimezones(
    initialValues.scheduledAt ? new Date(initialValues.scheduledAt) : /* @__PURE__ */ new Date()
  );
  const getScheduledTimestamp = (values) => {
    const { date, time, timezone } = values;
    if (!date || !time || !timezone)
      return null;
    const timezoneWithoutOffset = timezone.split("&")[1];
    return zonedTimeToUtc(`${date} ${time}`, timezoneWithoutOffset);
  };
  const getTimezoneWithOffset = () => {
    const currentTimezone = timezoneList.find(
      (timezone) => timezone.value.split("&")[1] === initialValues.timezone
    );
    return (currentTimezone == null ? void 0 : currentTimezone.value) || systemTimezone.value;
  };
  return (0, import_jsx_runtime.jsx)(Modal.Root, { open, onOpenChange: handleClose, children: (0, import_jsx_runtime.jsxs)(Modal.Content, { children: [
    (0, import_jsx_runtime.jsx)(Modal.Header, { children: (0, import_jsx_runtime.jsx)(Modal.Title, { children: formatMessage(
      {
        id: "content-releases.modal.title",
        defaultMessage: "{isCreatingRelease, select, true {New release} other {Edit release}}"
      },
      { isCreatingRelease }
    ) }) }),
    (0, import_jsx_runtime.jsx)(
      Formik,
      {
        onSubmit: (values) => {
          handleSubmit({
            ...values,
            timezone: values.timezone ? values.timezone.split("&")[1] : null,
            scheduledAt: values.isScheduled ? getScheduledTimestamp(values) : null
          });
        },
        initialValues: {
          ...initialValues,
          timezone: initialValues.timezone ? getTimezoneWithOffset() : systemTimezone.value
        },
        validationSchema: RELEASE_SCHEMA,
        validateOnChange: false,
        children: ({ values, errors, handleChange, setFieldValue }) => {
          return (0, import_jsx_runtime.jsxs)(Form, { children: [
            (0, import_jsx_runtime.jsx)(Modal.Body, { children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 6, children: [
              (0, import_jsx_runtime.jsxs)(
                Field.Root,
                {
                  name: "name",
                  error: errors.name && formatMessage({ id: errors.name, defaultMessage: errors.name }),
                  required: true,
                  children: [
                    (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                      id: "content-releases.modal.form.input.label.release-name",
                      defaultMessage: "Name"
                    }) }),
                    (0, import_jsx_runtime.jsx)(TextInput, { value: values.name, onChange: handleChange }),
                    (0, import_jsx_runtime.jsx)(Field.Error, {})
                  ]
                }
              ),
              (0, import_jsx_runtime.jsx)(Box, { width: "max-content", children: (0, import_jsx_runtime.jsx)(
                CheckboxImpl,
                {
                  name: "isScheduled",
                  checked: values.isScheduled,
                  onCheckedChange: (checked) => {
                    setFieldValue("isScheduled", checked);
                    if (!checked) {
                      setFieldValue("date", null);
                      setFieldValue("time", "");
                      setFieldValue("timezone", null);
                    } else {
                      setFieldValue("date", initialValues.date);
                      setFieldValue("time", initialValues.time);
                      setFieldValue(
                        "timezone",
                        initialValues.timezone ?? (systemTimezone == null ? void 0 : systemTimezone.value)
                      );
                    }
                  },
                  children: (0, import_jsx_runtime.jsx)(
                    Typography,
                    {
                      textColor: values.isScheduled ? "primary600" : "neutral800",
                      fontWeight: values.isScheduled ? "semiBold" : "regular",
                      children: formatMessage({
                        id: "modal.form.input.label.schedule-release",
                        defaultMessage: "Schedule release"
                      })
                    }
                  )
                }
              ) }),
              values.isScheduled && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
                (0, import_jsx_runtime.jsxs)(Flex, { gap: 4, alignItems: "start", children: [
                  (0, import_jsx_runtime.jsx)(Box, { width: "100%", children: (0, import_jsx_runtime.jsxs)(
                    Field.Root,
                    {
                      name: "date",
                      error: errors.date && formatMessage({ id: errors.date, defaultMessage: errors.date }),
                      required: true,
                      children: [
                        (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                          id: "content-releases.modal.form.input.label.date",
                          defaultMessage: "Date"
                        }) }),
                        (0, import_jsx_runtime.jsx)(
                          DatePicker$1,
                          {
                            onChange: (date) => {
                              const isoFormatDate = date ? formatISO(date, { representation: "date" }) : null;
                              setFieldValue("date", isoFormatDate);
                            },
                            clearLabel: formatMessage({
                              id: "content-releases.modal.form.input.clearLabel",
                              defaultMessage: "Clear"
                            }),
                            onClear: () => {
                              setFieldValue("date", null);
                            },
                            value: values.date ? new Date(values.date) : /* @__PURE__ */ new Date(),
                            minDate: utcToZonedTime(/* @__PURE__ */ new Date(), values.timezone.split("&")[1])
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(Field.Error, {})
                      ]
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Box, { width: "100%", children: (0, import_jsx_runtime.jsxs)(
                    Field.Root,
                    {
                      name: "time",
                      error: errors.time && formatMessage({ id: errors.time, defaultMessage: errors.time }),
                      required: true,
                      children: [
                        (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                          id: "content-releases.modal.form.input.label.time",
                          defaultMessage: "Time"
                        }) }),
                        (0, import_jsx_runtime.jsx)(
                          TimePicker$1,
                          {
                            onChange: (time) => {
                              setFieldValue("time", time);
                            },
                            clearLabel: formatMessage({
                              id: "content-releases.modal.form.input.clearLabel",
                              defaultMessage: "Clear"
                            }),
                            onClear: () => {
                              setFieldValue("time", "");
                            },
                            value: values.time || void 0
                          }
                        ),
                        (0, import_jsx_runtime.jsx)(Field.Error, {})
                      ]
                    }
                  ) })
                ] }),
                (0, import_jsx_runtime.jsx)(TimezoneComponent, { timezoneOptions: timezoneList })
              ] })
            ] }) }),
            (0, import_jsx_runtime.jsxs)(Modal.Footer, { children: [
              (0, import_jsx_runtime.jsx)(Modal.Close, { children: (0, import_jsx_runtime.jsx)(Button, { variant: "tertiary", name: "cancel", children: formatMessage({ id: "cancel", defaultMessage: "Cancel" }) }) }),
              (0, import_jsx_runtime.jsx)(Button, { name: "submit", loading: isLoading, type: "submit", children: formatMessage(
                {
                  id: "content-releases.modal.form.button.submit",
                  defaultMessage: "{isCreatingRelease, select, true {Continue} other {Save}}"
                },
                { isCreatingRelease }
              ) })
            ] })
          ] });
        }
      }
    )
  ] }) });
};
var TimezoneComponent = ({ timezoneOptions }) => {
  const { values, errors, setFieldValue } = useFormikContext();
  const { formatMessage } = useIntl();
  const [timezoneList, setTimezoneList] = React.useState(timezoneOptions);
  React.useEffect(() => {
    if (values.date) {
      const { timezoneList: timezoneList2 } = getTimezones(new Date(values.date));
      setTimezoneList(timezoneList2);
      const updatedTimezone = values.timezone && timezoneList2.find((tz) => tz.value.split("&")[1] === values.timezone.split("&")[1]);
      if (updatedTimezone) {
        setFieldValue("timezone", updatedTimezone.value);
      }
    }
  }, [setFieldValue, values.date, values.timezone]);
  return (0, import_jsx_runtime.jsxs)(
    Field.Root,
    {
      name: "timezone",
      error: errors.timezone && formatMessage({ id: errors.timezone, defaultMessage: errors.timezone }),
      required: true,
      children: [
        (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
          id: "content-releases.modal.form.input.label.timezone",
          defaultMessage: "Timezone"
        }) }),
        (0, import_jsx_runtime.jsx)(
          Combobox,
          {
            autocomplete: { type: "list", filter: "contains" },
            value: values.timezone || void 0,
            textValue: values.timezone ? values.timezone.replace(/&/, " ") : void 0,
            onChange: (timezone) => {
              setFieldValue("timezone", timezone);
            },
            onTextValueChange: (timezone) => {
              setFieldValue("timezone", timezone);
            },
            onClear: () => {
              setFieldValue("timezone", "");
            },
            children: timezoneList.map((timezone) => (0, import_jsx_runtime.jsx)(Option, { value: timezone.value, children: timezone.value.replace(/&/, " ") }, timezone.value))
          }
        ),
        (0, import_jsx_runtime.jsx)(Field.Error, {})
      ]
    }
  );
};
var useTypedDispatch = useDispatch;
var isBaseQueryError = (error) => {
  return typeof error !== "undefined" && error.name !== void 0;
};
var LinkCard = dt(Link2)`
  display: block;
`;
var RelativeTime = dt(RelativeTime$1)`
  display: inline-block;
  &::first-letter {
    text-transform: uppercase;
  }
`;
var getBadgeProps = (status) => {
  let color;
  switch (status) {
    case "ready":
      color = "success";
      break;
    case "blocked":
      color = "warning";
      break;
    case "failed":
      color = "danger";
      break;
    case "done":
      color = "primary";
      break;
    case "empty":
    default:
      color = "neutral";
  }
  return {
    textColor: `${color}600`,
    backgroundColor: `${color}100`,
    borderColor: `${color}200`
  };
};
var ReleasesGrid = ({ sectionTitle, releases = [], isError = false }) => {
  const { formatMessage } = useIntl();
  if (isError) {
    return (0, import_jsx_runtime.jsx)(Page.Error, {});
  }
  if ((releases == null ? void 0 : releases.length) === 0) {
    return (0, import_jsx_runtime.jsx)(
      EmptyStateLayout,
      {
        content: formatMessage(
          {
            id: "content-releases.page.Releases.tab.emptyEntries",
            defaultMessage: "No releases"
          },
          {
            target: sectionTitle
          }
        ),
        icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: "16rem" })
      }
    );
  }
  return (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 4, children: releases.map(({ id, name, scheduledAt, status }) => (0, import_jsx_runtime.jsx)(Grid.Item, { col: 3, s: 6, xs: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(LinkCard, { tag: NavLink, to: `${id}`, isExternal: false, children: (0, import_jsx_runtime.jsxs)(
    Flex,
    {
      direction: "column",
      justifyContent: "space-between",
      padding: 4,
      hasRadius: true,
      background: "neutral0",
      shadow: "tableShadow",
      height: "100%",
      width: "100%",
      alignItems: "start",
      gap: 4,
      children: [
        (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "start", gap: 1, children: [
          (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", tag: "h3", variant: "delta", fontWeight: "bold", children: name }),
          (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", textColor: "neutral600", children: scheduledAt ? (0, import_jsx_runtime.jsx)(RelativeTime, { timestamp: new Date(scheduledAt) }) : formatMessage({
            id: "content-releases.pages.Releases.not-scheduled",
            defaultMessage: "Not scheduled"
          }) })
        ] }),
        (0, import_jsx_runtime.jsx)(Badge, { ...getBadgeProps(status), children: status })
      ]
    }
  ) }) }, id)) });
};
var StyledAlert = dt(Alert)`
  button {
    display: none;
  }
  p + div {
    margin-left: auto;
  }
`;
var INITIAL_FORM_VALUES = {
  name: "",
  date: format(/* @__PURE__ */ new Date(), "yyyy-MM-dd"),
  time: "",
  isScheduled: true,
  scheduledAt: null,
  timezone: null
};
var ReleasesPage = () => {
  var _a, _b, _c, _d, _e, _f, _g, _h, _i, _j, _k, _l;
  const location = useLocation();
  const [releaseModalShown, setReleaseModalShown] = React.useState(false);
  const { toggleNotification } = useNotification();
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const { formatAPIError } = useAPIErrorHandler();
  const [{ query }, setQuery] = useQueryParams();
  const response = useGetReleasesQuery(query);
  const { data, isLoading: isLoadingSettings } = useGetReleaseSettingsQuery();
  const [createRelease, { isLoading: isSubmittingForm }] = useCreateReleaseMutation();
  const { getFeature } = useLicenseLimits();
  const { maximumReleases = 3 } = getFeature("cms-content-releases");
  const { trackUsage } = useTracking();
  const {
    allowedActions: { canCreate }
  } = useRBAC(PERMISSIONS);
  const { isLoading: isLoadingReleases, isSuccess, isError } = response;
  const activeTab = ((_b = (_a = response == null ? void 0 : response.currentData) == null ? void 0 : _a.meta) == null ? void 0 : _b.activeTab) || "pending";
  React.useEffect(() => {
    var _a2;
    if ((_a2 = location == null ? void 0 : location.state) == null ? void 0 : _a2.errors) {
      toggleNotification({
        type: "danger",
        title: formatMessage({
          id: "content-releases.pages.Releases.notification.error.title",
          defaultMessage: "Your request could not be processed."
        }),
        message: formatMessage({
          id: "content-releases.pages.Releases.notification.error.message",
          defaultMessage: "Please try again or open another release."
        })
      });
      navigate("", { replace: true, state: null });
    }
  }, [formatMessage, (_c = location == null ? void 0 : location.state) == null ? void 0 : _c.errors, navigate, toggleNotification]);
  const toggleAddReleaseModal = () => {
    setReleaseModalShown((prev) => !prev);
  };
  if (isLoadingReleases || isLoadingSettings) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  const totalPendingReleases = isSuccess && ((_e = (_d = response.currentData) == null ? void 0 : _d.meta) == null ? void 0 : _e.pendingReleasesCount) || 0;
  const hasReachedMaximumPendingReleases = totalPendingReleases >= maximumReleases;
  const handleTabChange = (tabValue) => {
    var _a2, _b2, _c2;
    setQuery({
      ...query,
      page: 1,
      pageSize: ((_c2 = (_b2 = (_a2 = response == null ? void 0 : response.currentData) == null ? void 0 : _a2.meta) == null ? void 0 : _b2.pagination) == null ? void 0 : _c2.pageSize) || 16,
      filters: {
        releasedAt: {
          $notNull: tabValue !== "pending"
        }
      }
    });
  };
  const handleAddRelease = async ({ name, scheduledAt, timezone }) => {
    const response2 = await createRelease({
      name,
      scheduledAt,
      timezone
    });
    if ("data" in response2) {
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "content-releases.modal.release-created-notification-success",
          defaultMessage: "Release created."
        })
      });
      trackUsage("didCreateRelease");
      navigate(response2.data.data.id.toString());
    } else if (isFetchError(response2.error)) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(response2.error)
      });
    } else {
      toggleNotification({
        type: "danger",
        message: formatMessage({ id: "notification.error", defaultMessage: "An error occurred" })
      });
    }
  };
  return (0, import_jsx_runtime.jsxs)(Main, { "aria-busy": isLoadingReleases || isLoadingSettings, children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: "content-releases.pages.Releases.title",
          defaultMessage: "Releases"
        }),
        subtitle: formatMessage({
          id: "content-releases.pages.Releases.header-subtitle",
          defaultMessage: "Create and manage content updates"
        }),
        primaryAction: canCreate ? (0, import_jsx_runtime.jsx)(
          Button,
          {
            startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
            onClick: toggleAddReleaseModal,
            disabled: hasReachedMaximumPendingReleases,
            children: formatMessage({
              id: "content-releases.header.actions.add-release",
              defaultMessage: "New release"
            })
          }
        ) : null
      }
    ),
    (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
      hasReachedMaximumPendingReleases && (0, import_jsx_runtime.jsx)(
        StyledAlert,
        {
          marginBottom: 6,
          action: (0, import_jsx_runtime.jsx)(Link2, { href: "https://strapi.io/pricing-cloud", isExternal: true, children: formatMessage({
            id: "content-releases.pages.Releases.max-limit-reached.action",
            defaultMessage: "Explore plans"
          }) }),
          title: formatMessage(
            {
              id: "content-releases.pages.Releases.max-limit-reached.title",
              defaultMessage: "You have reached the {number} pending {number, plural, one {release} other {releases}} limit."
            },
            { number: maximumReleases }
          ),
          onClose: () => {
          },
          closeLabel: "",
          children: formatMessage({
            id: "content-releases.pages.Releases.max-limit-reached.message",
            defaultMessage: "Upgrade to manage an unlimited number of releases."
          })
        }
      ),
      (0, import_jsx_runtime.jsxs)(Tabs.Root, { variant: "simple", onValueChange: handleTabChange, value: activeTab, children: [
        (0, import_jsx_runtime.jsxs)(Box, { paddingBottom: 8, children: [
          (0, import_jsx_runtime.jsxs)(
            Tabs.List,
            {
              "aria-label": formatMessage({
                id: "content-releases.pages.Releases.tab-group.label",
                defaultMessage: "Releases list"
              }),
              children: [
                (0, import_jsx_runtime.jsx)(Tabs.Trigger, { value: "pending", children: formatMessage(
                  {
                    id: "content-releases.pages.Releases.tab.pending",
                    defaultMessage: "Pending ({count})"
                  },
                  {
                    count: totalPendingReleases
                  }
                ) }),
                (0, import_jsx_runtime.jsx)(Tabs.Trigger, { value: "done", children: formatMessage({
                  id: "content-releases.pages.Releases.tab.done",
                  defaultMessage: "Done"
                }) })
              ]
            }
          ),
          (0, import_jsx_runtime.jsx)(Divider, {})
        ] }),
        (0, import_jsx_runtime.jsx)(Tabs.Content, { value: "pending", children: (0, import_jsx_runtime.jsx)(
          ReleasesGrid,
          {
            sectionTitle: "pending",
            releases: (_f = response == null ? void 0 : response.currentData) == null ? void 0 : _f.data,
            isError
          }
        ) }),
        (0, import_jsx_runtime.jsx)(Tabs.Content, { value: "done", children: (0, import_jsx_runtime.jsx)(
          ReleasesGrid,
          {
            sectionTitle: "done",
            releases: (_g = response == null ? void 0 : response.currentData) == null ? void 0 : _g.data,
            isError
          }
        ) })
      ] }),
      (0, import_jsx_runtime.jsxs)(
        Pagination.Root,
        {
          ...(_i = (_h = response == null ? void 0 : response.currentData) == null ? void 0 : _h.meta) == null ? void 0 : _i.pagination,
          defaultPageSize: (_l = (_k = (_j = response == null ? void 0 : response.currentData) == null ? void 0 : _j.meta) == null ? void 0 : _k.pagination) == null ? void 0 : _l.pageSize,
          children: [
            (0, import_jsx_runtime.jsx)(Pagination.PageSize, { options: ["8", "16", "32", "64"] }),
            (0, import_jsx_runtime.jsx)(Pagination.Links, {})
          ]
        }
      )
    ] }) }),
    (0, import_jsx_runtime.jsx)(
      ReleaseModal,
      {
        open: releaseModalShown,
        handleClose: toggleAddReleaseModal,
        handleSubmit: handleAddRelease,
        isLoading: isSubmittingForm,
        initialValues: {
          ...INITIAL_FORM_VALUES,
          timezone: (data == null ? void 0 : data.data.defaultTimezone) ? data.data.defaultTimezone.split("&")[1] : null
        }
      }
    )
  ] });
};
var ReleaseInfoWrapper = dt(Flex)`
  align-self: stretch;
  border-bottom-right-radius: ${({ theme }) => theme.borderRadius};
  border-bottom-left-radius: ${({ theme }) => theme.borderRadius};
  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};
`;
var StyledMenuItem = dt(MenuItem)`
  svg path {
    fill: ${({ theme, disabled }) => disabled && theme.colors.neutral500};
  }
  span {
    color: ${({ theme, disabled }) => disabled && theme.colors.neutral500};
  }

  &:hover {
    background: ${({ theme, $variant = "neutral" }) => theme.colors[`${$variant}100`]};
  }
`;
var PencilIcon = dt(ForwardRef$1r)`
  width: ${({ theme }) => theme.spaces[4]};
  height: ${({ theme }) => theme.spaces[4]};
  path {
    fill: ${({ theme }) => theme.colors.neutral600};
  }
`;
var TrashIcon = dt(ForwardRef$j)`
  width: ${({ theme }) => theme.spaces[4]};
  height: ${({ theme }) => theme.spaces[4]};
  path {
    fill: ${({ theme }) => theme.colors.danger600};
  }
`;
var ReleaseDetailsLayout = ({
  toggleEditReleaseModal,
  toggleWarningSubmit,
  children
}) => {
  const { formatMessage, formatDate, formatTime } = useIntl();
  const { releaseId } = useParams();
  const {
    data,
    isLoading: isLoadingDetails,
    error
  } = useGetReleaseQuery(
    { id: releaseId },
    {
      skip: !releaseId
    }
  );
  const [publishRelease, { isLoading: isPublishing }] = usePublishReleaseMutation();
  const { toggleNotification } = useNotification();
  const { formatAPIError } = useAPIErrorHandler();
  const { allowedActions } = useRBAC(PERMISSIONS);
  const { canUpdate, canDelete, canPublish } = allowedActions;
  const dispatch = useTypedDispatch();
  const { trackUsage } = useTracking();
  const release = data == null ? void 0 : data.data;
  const handlePublishRelease = (id) => async () => {
    const response = await publishRelease({ id });
    if ("data" in response) {
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "content-releases.pages.ReleaseDetails.publish-notification-success",
          defaultMessage: "Release was published successfully."
        })
      });
      const { totalEntries: totalEntries2, totalPublishedEntries, totalUnpublishedEntries } = response.data.meta;
      trackUsage("didPublishRelease", {
        totalEntries: totalEntries2,
        totalPublishedEntries,
        totalUnpublishedEntries
      });
    } else if (isFetchError(response.error)) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(response.error)
      });
    } else {
      toggleNotification({
        type: "danger",
        message: formatMessage({ id: "notification.error", defaultMessage: "An error occurred" })
      });
    }
  };
  const handleRefresh = () => {
    dispatch(
      releaseApi.util.invalidateTags([
        { type: "ReleaseAction", id: "LIST" },
        { type: "Release", id: releaseId }
      ])
    );
  };
  const getCreatedByUser = () => {
    if (!(release == null ? void 0 : release.createdBy)) {
      return null;
    }
    if (release.createdBy.username) {
      return release.createdBy.username;
    }
    if (release.createdBy.firstname) {
      return `${release.createdBy.firstname} ${release.createdBy.lastname || ""}`.trim();
    }
    return release.createdBy.email;
  };
  if (isLoadingDetails) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  if (isBaseQueryError(error) && "code" in error || !release) {
    return (0, import_jsx_runtime.jsx)(
      Navigate,
      {
        to: "..",
        state: {
          errors: [
            {
              // @ts-expect-error – TODO: fix this weird error flow
              code: error == null ? void 0 : error.code
            }
          ]
        }
      }
    );
  }
  const totalEntries = release.actions.meta.count || 0;
  const hasCreatedByUser = Boolean(getCreatedByUser());
  const isScheduled = release.scheduledAt && release.timezone;
  const numberOfEntriesText = formatMessage(
    {
      id: "content-releases.pages.Details.header-subtitle",
      defaultMessage: "{number, plural, =0 {No entries} one {# entry} other {# entries}}"
    },
    { number: totalEntries }
  );
  const scheduledText = isScheduled ? formatMessage(
    {
      id: "content-releases.pages.ReleaseDetails.header-subtitle.scheduled",
      defaultMessage: "Scheduled for {date} at {time} ({offset})"
    },
    {
      date: formatDate(new Date(release.scheduledAt), {
        weekday: "long",
        day: "numeric",
        month: "long",
        year: "numeric",
        timeZone: release.timezone
      }),
      time: formatTime(new Date(release.scheduledAt), {
        timeZone: release.timezone,
        hourCycle: "h23"
      }),
      offset: getTimezoneOffset(release.timezone, new Date(release.scheduledAt))
    }
  ) : "";
  return (0, import_jsx_runtime.jsxs)(Main, { "aria-busy": isLoadingDetails, children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: release.name,
        subtitle: (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, lineHeight: 6, children: [
          (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral600", variant: "epsilon", children: numberOfEntriesText + (isScheduled ? ` - ${scheduledText}` : "") }),
          (0, import_jsx_runtime.jsx)(Badge, { ...getBadgeProps(release.status), children: release.status })
        ] }),
        navigationAction: (0, import_jsx_runtime.jsx)(BackButton, {}),
        primaryAction: !release.releasedAt && (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, children: [
          (0, import_jsx_runtime.jsxs)(
            SimpleMenuButton,
            {
              label: (0, import_jsx_runtime.jsx)(ForwardRef$1R, {}),
              variant: "tertiary",
              endIcon: null,
              paddingLeft: "7px",
              paddingRight: "7px",
              "aria-label": formatMessage({
                id: "content-releases.header.actions.open-release-actions",
                defaultMessage: "Release edit and delete menu"
              }),
              popoverPlacement: "bottom-end",
              children: [
                (0, import_jsx_runtime.jsx)(StyledMenuItem, { disabled: !canUpdate, onSelect: toggleEditReleaseModal, children: (0, import_jsx_runtime.jsxs)(Flex, { alignItems: "center", gap: 2, hasRadius: true, width: "100%", children: [
                  (0, import_jsx_runtime.jsx)(PencilIcon, {}),
                  (0, import_jsx_runtime.jsx)(Typography, { ellipsis: true, children: formatMessage({
                    id: "content-releases.header.actions.edit",
                    defaultMessage: "Edit"
                  }) })
                ] }) }),
                (0, import_jsx_runtime.jsx)(
                  StyledMenuItem,
                  {
                    disabled: !canDelete,
                    onSelect: toggleWarningSubmit,
                    $variant: "danger",
                    children: (0, import_jsx_runtime.jsxs)(Flex, { alignItems: "center", gap: 2, hasRadius: true, width: "100%", children: [
                      (0, import_jsx_runtime.jsx)(TrashIcon, {}),
                      (0, import_jsx_runtime.jsx)(Typography, { ellipsis: true, textColor: "danger600", children: formatMessage({
                        id: "content-releases.header.actions.delete",
                        defaultMessage: "Delete"
                      }) })
                    ] })
                  }
                ),
                (0, import_jsx_runtime.jsxs)(
                  ReleaseInfoWrapper,
                  {
                    direction: "column",
                    justifyContent: "center",
                    alignItems: "flex-start",
                    gap: 1,
                    padding: 4,
                    children: [
                      (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", fontWeight: "bold", children: formatMessage({
                        id: "content-releases.header.actions.created",
                        defaultMessage: "Created"
                      }) }),
                      (0, import_jsx_runtime.jsxs)(Typography, { variant: "pi", color: "neutral300", children: [
                        (0, import_jsx_runtime.jsx)(RelativeTime$1, { timestamp: new Date(release.createdAt) }),
                        formatMessage(
                          {
                            id: "content-releases.header.actions.created.description",
                            defaultMessage: "{hasCreatedByUser, select, true { by {createdBy}} other { by deleted user}}"
                          },
                          { createdBy: getCreatedByUser(), hasCreatedByUser }
                        )
                      ] })
                    ]
                  }
                )
              ]
            }
          ),
          (0, import_jsx_runtime.jsx)(Button, { size: "S", variant: "tertiary", onClick: handleRefresh, children: formatMessage({
            id: "content-releases.header.actions.refresh",
            defaultMessage: "Refresh"
          }) }),
          canPublish ? (0, import_jsx_runtime.jsx)(
            Button,
            {
              size: "S",
              variant: "default",
              onClick: handlePublishRelease(release.id.toString()),
              loading: isPublishing,
              disabled: release.actions.meta.count === 0,
              children: formatMessage({
                id: "content-releases.header.actions.publish",
                defaultMessage: "Publish"
              })
            }
          ) : null
        ] })
      }
    ),
    children
  ] });
};
var SimpleMenuButton = dt(SimpleMenu)`
  & > span {
    display: flex;
  }
`;
var GROUP_BY_OPTIONS = ["contentType", "locale", "action"];
var GROUP_BY_OPTIONS_NO_LOCALE = ["contentType", "action"];
var getGroupByOptionLabel = (value) => {
  if (value === "locale") {
    return {
      id: "content-releases.pages.ReleaseDetails.groupBy.option.locales",
      defaultMessage: "Locales"
    };
  }
  if (value === "action") {
    return {
      id: "content-releases.pages.ReleaseDetails.groupBy.option.actions",
      defaultMessage: "Actions"
    };
  }
  return {
    id: "content-releases.pages.ReleaseDetails.groupBy.option.content-type",
    defaultMessage: "Content-Types"
  };
};
var ReleaseDetailsBody = ({ releaseId }) => {
  var _a;
  const { formatMessage } = useIntl();
  const [{ query }, setQuery] = useQueryParams();
  const { toggleNotification } = useNotification();
  const { formatAPIError } = useAPIErrorHandler();
  const {
    data: releaseData,
    isLoading: isReleaseLoading,
    error: releaseError
  } = useGetReleaseQuery({ id: releaseId });
  const {
    allowedActions: { canUpdate }
  } = useRBAC(PERMISSIONS);
  const runHookWaterfall = useStrapiApp("ReleaseDetailsPage", (state) => state.runHookWaterfall);
  const { displayedHeaders, hasI18nEnabled } = runHookWaterfall("ContentReleases/pages/ReleaseDetails/add-locale-in-releases", {
    displayedHeaders: [
      {
        label: {
          id: "content-releases.page.ReleaseDetails.table.header.label.name",
          defaultMessage: "name"
        },
        name: "name"
      }
    ],
    hasI18nEnabled: false
  });
  const release = releaseData == null ? void 0 : releaseData.data;
  const selectedGroupBy = (query == null ? void 0 : query.groupBy) || "contentType";
  const {
    isLoading,
    isFetching,
    isError,
    data,
    error: releaseActionsError
  } = useGetReleaseActionsQuery({
    ...query,
    releaseId
  });
  const [updateReleaseAction] = useUpdateReleaseActionMutation();
  const handleChangeType = async (e, actionId, actionPath) => {
    const response = await updateReleaseAction({
      params: {
        releaseId,
        actionId
      },
      body: {
        type: e.target.value
      },
      query,
      // We are passing the query params to make optimistic updates
      actionPath
      // We are passing the action path to found the position in the cache of the action for optimistic updates
    });
    if ("error" in response) {
      if (isFetchError(response.error)) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(response.error)
        });
      } else {
        toggleNotification({
          type: "danger",
          message: formatMessage({ id: "notification.error", defaultMessage: "An error occurred" })
        });
      }
    }
  };
  if (isLoading || isReleaseLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  const releaseActions = data == null ? void 0 : data.data;
  const releaseMeta = data == null ? void 0 : data.meta;
  const contentTypes = (releaseMeta == null ? void 0 : releaseMeta.contentTypes) || {};
  (releaseMeta == null ? void 0 : releaseMeta.components) || {};
  if (isBaseQueryError(releaseError) || !release) {
    const errorsArray = [];
    if (releaseError && "code" in releaseError) {
      errorsArray.push({
        code: releaseError.code
      });
    }
    if (releaseActionsError && "code" in releaseActionsError) {
      errorsArray.push({
        code: releaseActionsError.code
      });
    }
    return (0, import_jsx_runtime.jsx)(
      Navigate,
      {
        to: "..",
        state: {
          errors: errorsArray
        }
      }
    );
  }
  if (isError || !releaseActions) {
    return (0, import_jsx_runtime.jsx)(Page.Error, {});
  }
  if (Object.keys(releaseActions).length === 0) {
    return (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsx)(
      EmptyStateLayout,
      {
        action: (0, import_jsx_runtime.jsx)(
          LinkButton,
          {
            tag: Link,
            to: {
              pathname: "/content-manager"
            },
            style: { textDecoration: "none" },
            variant: "secondary",
            children: formatMessage({
              id: "content-releases.page.Details.button.openContentManager",
              defaultMessage: "Open the Content Manager"
            })
          }
        ),
        icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: "16rem" }),
        content: formatMessage({
          id: "content-releases.pages.Details.tab.emptyEntries",
          defaultMessage: "This release is empty. Open the Content Manager, select an entry and add it to the release."
        })
      }
    ) });
  }
  const groupByLabel = formatMessage({
    id: "content-releases.pages.ReleaseDetails.groupBy.aria-label",
    defaultMessage: "Group by"
  });
  const headers = [
    ...displayedHeaders,
    {
      label: {
        id: "content-releases.page.ReleaseDetails.table.header.label.content-type",
        defaultMessage: "content-type"
      },
      name: "content-type"
    },
    {
      label: {
        id: "content-releases.page.ReleaseDetails.table.header.label.action",
        defaultMessage: "action"
      },
      name: "action"
    },
    ...!release.releasedAt ? [
      {
        label: {
          id: "content-releases.page.ReleaseDetails.table.header.label.status",
          defaultMessage: "status"
        },
        name: "status"
      }
    ] : []
  ];
  const options = hasI18nEnabled ? GROUP_BY_OPTIONS : GROUP_BY_OPTIONS_NO_LOCALE;
  return (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(Flex, { gap: 8, direction: "column", alignItems: "stretch", children: [
    (0, import_jsx_runtime.jsx)(Flex, { children: (0, import_jsx_runtime.jsx)(
      SingleSelect,
      {
        placeholder: groupByLabel,
        "aria-label": groupByLabel,
        customizeContent: (value) => formatMessage(
          {
            id: `content-releases.pages.ReleaseDetails.groupBy.label`,
            defaultMessage: `Group by {groupBy}`
          },
          {
            groupBy: value
          }
        ),
        value: formatMessage(getGroupByOptionLabel(selectedGroupBy)),
        onChange: (value) => setQuery({ groupBy: value }),
        children: options.map((option) => (0, import_jsx_runtime.jsx)(SingleSelectOption, { value: option, children: formatMessage(getGroupByOptionLabel(option)) }, option))
      }
    ) }),
    Object.keys(releaseActions).map((key) => (0, import_jsx_runtime.jsxs)(Flex, { gap: 4, direction: "column", alignItems: "stretch", children: [
      (0, import_jsx_runtime.jsx)(Flex, { role: "separator", "aria-label": key, children: (0, import_jsx_runtime.jsx)(Badge, { children: key }) }),
      (0, import_jsx_runtime.jsx)(
        Table.Root,
        {
          rows: releaseActions[key].map((item) => ({
            ...item,
            id: Number(item.entry.id)
          })),
          headers,
          isLoading: isLoading || isFetching,
          children: (0, import_jsx_runtime.jsxs)(Table.Content, { children: [
            (0, import_jsx_runtime.jsx)(Table.Head, { children: headers.map(({ label, name }) => (0, import_jsx_runtime.jsx)(Table.HeaderCell, { label: formatMessage(label), name }, name)) }),
            (0, import_jsx_runtime.jsx)(Table.Loading, {}),
            (0, import_jsx_runtime.jsx)(Table.Body, { children: releaseActions[key].map(
              ({ id, contentType, locale, type, entry, status }, actionIndex) => (0, import_jsx_runtime.jsxs)(Tr, { children: [
                (0, import_jsx_runtime.jsx)(Td, { width: "25%", maxWidth: "200px", children: (0, import_jsx_runtime.jsx)(Typography, { ellipsis: true, children: `${contentType.mainFieldValue || entry.id}` }) }),
                hasI18nEnabled && (0, import_jsx_runtime.jsx)(Td, { width: "10%", children: (0, import_jsx_runtime.jsx)(Typography, { children: `${(locale == null ? void 0 : locale.name) ? locale.name : "-"}` }) }),
                (0, import_jsx_runtime.jsx)(Td, { width: "10%", children: (0, import_jsx_runtime.jsx)(Typography, { children: contentType.displayName || "" }) }),
                (0, import_jsx_runtime.jsx)(Td, { width: "20%", children: release.releasedAt ? (0, import_jsx_runtime.jsx)(Typography, { children: formatMessage(
                  {
                    id: "content-releases.page.ReleaseDetails.table.action-published",
                    defaultMessage: "This entry was <b>{isPublish, select, true {published} other {unpublished}}</b>."
                  },
                  {
                    isPublish: type === "publish",
                    b: (children) => (0, import_jsx_runtime.jsx)(Typography, { fontWeight: "bold", children })
                  }
                ) }) : (0, import_jsx_runtime.jsx)(
                  ReleaseActionOptions,
                  {
                    selected: type,
                    handleChange: (e) => handleChangeType(e, id, [key, actionIndex]),
                    name: `release-action-${id}-type`,
                    disabled: !canUpdate
                  }
                ) }),
                !release.releasedAt && (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
                  (0, import_jsx_runtime.jsx)(Td, { width: "20%", minWidth: "200px", children: (0, import_jsx_runtime.jsx)(
                    EntryValidationPopover,
                    {
                      action: type,
                      schema: contentTypes == null ? void 0 : contentTypes[contentType.uid],
                      entry,
                      status
                    }
                  ) }),
                  (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsx)(Flex, { justifyContent: "flex-end", children: (0, import_jsx_runtime.jsxs)(ReleaseActionMenu.Root, { children: [
                    (0, import_jsx_runtime.jsx)(
                      ReleaseActionMenu.ReleaseActionEntryLinkItem,
                      {
                        contentTypeUid: contentType.uid,
                        documentId: entry.documentId,
                        locale: locale == null ? void 0 : locale.code
                      }
                    ),
                    (0, import_jsx_runtime.jsx)(
                      ReleaseActionMenu.DeleteReleaseActionItem,
                      {
                        releaseId: release.id,
                        actionId: id
                      }
                    )
                  ] }) }) })
                ] })
              ] }, id)
            ) })
          ] })
        }
      )
    ] }, `releases-group-${key}`)),
    (0, import_jsx_runtime.jsxs)(
      Pagination.Root,
      {
        ...releaseMeta == null ? void 0 : releaseMeta.pagination,
        defaultPageSize: (_a = releaseMeta == null ? void 0 : releaseMeta.pagination) == null ? void 0 : _a.pageSize,
        children: [
          (0, import_jsx_runtime.jsx)(Pagination.PageSize, {}),
          (0, import_jsx_runtime.jsx)(Pagination.Links, {})
        ]
      }
    )
  ] }) });
};
var ReleaseDetailsPage = () => {
  const { formatMessage } = useIntl();
  const { releaseId } = useParams();
  const { toggleNotification } = useNotification();
  const { formatAPIError } = useAPIErrorHandler();
  const navigate = useNavigate();
  const [releaseModalShown, setReleaseModalShown] = React.useState(false);
  const [showWarningSubmit, setWarningSubmit] = React.useState(false);
  const {
    isLoading: isLoadingDetails,
    data,
    isSuccess: isSuccessDetails
  } = useGetReleaseQuery(
    { id: releaseId },
    {
      skip: !releaseId
    }
  );
  const { data: dataTimezone, isLoading: isLoadingTimezone } = useGetReleaseSettingsQuery();
  const [updateRelease, { isLoading: isSubmittingForm }] = useUpdateReleaseMutation();
  const [deleteRelease] = useDeleteReleaseMutation();
  const toggleEditReleaseModal = () => {
    setReleaseModalShown((prev) => !prev);
  };
  const getTimezoneValue = () => {
    if (releaseData == null ? void 0 : releaseData.timezone) {
      return releaseData.timezone;
    } else {
      if (dataTimezone == null ? void 0 : dataTimezone.data.defaultTimezone) {
        return dataTimezone.data.defaultTimezone;
      }
      return null;
    }
  };
  const toggleWarningSubmit = () => setWarningSubmit((prevState) => !prevState);
  if (isLoadingDetails || isLoadingTimezone) {
    return (0, import_jsx_runtime.jsx)(
      ReleaseDetailsLayout,
      {
        toggleEditReleaseModal,
        toggleWarningSubmit,
        children: (0, import_jsx_runtime.jsx)(Page.Loading, {})
      }
    );
  }
  if (!releaseId) {
    return (0, import_jsx_runtime.jsx)(Navigate, { to: ".." });
  }
  const releaseData = isSuccessDetails && (data == null ? void 0 : data.data) || null;
  const title = (releaseData == null ? void 0 : releaseData.name) || "";
  const timezone = getTimezoneValue();
  const scheduledAt = (releaseData == null ? void 0 : releaseData.scheduledAt) && timezone ? utcToZonedTime(releaseData.scheduledAt, timezone) : null;
  const date = scheduledAt ? format(scheduledAt, "yyyy-MM-dd") : void 0;
  const time = scheduledAt ? format(scheduledAt, "HH:mm") : "";
  const handleEditRelease = async (values) => {
    const response = await updateRelease({
      id: releaseId,
      name: values.name,
      scheduledAt: values.scheduledAt,
      timezone: values.timezone
    });
    if ("data" in response) {
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: "content-releases.modal.release-updated-notification-success",
          defaultMessage: "Release updated."
        })
      });
      toggleEditReleaseModal();
    } else if (isFetchError(response.error)) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(response.error)
      });
    } else {
      toggleNotification({
        type: "danger",
        message: formatMessage({ id: "notification.error", defaultMessage: "An error occurred" })
      });
    }
  };
  const handleDeleteRelease = async () => {
    const response = await deleteRelease({
      id: releaseId
    });
    if ("data" in response) {
      navigate("..");
    } else if (isFetchError(response.error)) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(response.error)
      });
    } else {
      toggleNotification({
        type: "danger",
        message: formatMessage({ id: "notification.error", defaultMessage: "An error occurred" })
      });
    }
  };
  return (0, import_jsx_runtime.jsxs)(
    ReleaseDetailsLayout,
    {
      toggleEditReleaseModal,
      toggleWarningSubmit,
      children: [
        (0, import_jsx_runtime.jsx)(ReleaseDetailsBody, { releaseId }),
        (0, import_jsx_runtime.jsx)(
          ReleaseModal,
          {
            open: releaseModalShown,
            handleClose: toggleEditReleaseModal,
            handleSubmit: handleEditRelease,
            isLoading: isLoadingDetails || isSubmittingForm,
            initialValues: {
              name: title || "",
              scheduledAt,
              date,
              time,
              isScheduled: Boolean(scheduledAt),
              timezone
            }
          }
        ),
        (0, import_jsx_runtime.jsx)(Dialog.Root, { open: showWarningSubmit, onOpenChange: toggleWarningSubmit, children: (0, import_jsx_runtime.jsx)(ConfirmDialog, { onConfirm: handleDeleteRelease, children: formatMessage({
          id: "content-releases.dialog.confirmation-message",
          defaultMessage: "Are you sure you want to delete this release?"
        }) }) })
      ]
    }
  );
};
var App = () => {
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.main, children: (0, import_jsx_runtime.jsxs)(Routes, { children: [
    (0, import_jsx_runtime.jsx)(Route, { index: true, element: (0, import_jsx_runtime.jsx)(ReleasesPage, {}) }),
    (0, import_jsx_runtime.jsx)(Route, { path: ":releaseId", element: (0, import_jsx_runtime.jsx)(ReleaseDetailsPage, {}) })
  ] }) });
};
export {
  App
};
//# sourceMappingURL=App-BX6_LcmS-7VD7VAA7.js.map
