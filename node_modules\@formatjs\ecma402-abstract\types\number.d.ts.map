{"version": 3, "file": "number.d.ts", "sourceRoot": "", "sources": ["number.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,cAAc,EAAC,MAAM,gBAAgB,CAAA;AAC7C,OAAO,EAAC,UAAU,EAAC,MAAM,QAAQ,CAAA;AAEjC,MAAM,MAAM,oBAAoB,GAC5B,UAAU,GACV,YAAY,GACZ,aAAa,GACb,SAAS,CAAA;AAEb,MAAM,MAAM,oBAAoB,GAAG,MAAM,GAAG,eAAe,GAAG,eAAe,CAAA;AAE7E,MAAM,MAAM,wBAAwB,GAChC,eAAe,GACf,eAAe,GACf,mBAAmB,GACnB,gBAAgB,CAAA;AAEpB,MAAM,MAAM,gBAAgB,GACxB,MAAM,GACN,OAAO,GACP,QAAQ,GACR,OAAO,GACP,UAAU,GACV,WAAW,GACX,YAAY,GACZ,WAAW,GACX,UAAU,CAAA;AAEd,MAAM,MAAM,wBAAwB,GAChC,UAAU,GACV,MAAM,GACN,eAAe,GACf,WAAW,GACX,WAAW,CAAA;AAEf,MAAM,MAAM,eAAe,GAAG,MAAM,GAAG,MAAM,GAAG,QAAQ,GAAG,OAAO,CAAA;AAElE,MAAM,WAAW,wBAAwB;IACvC,oBAAoB,CAAC,EAAE,MAAM,CAAA;IAC7B,wBAAwB,CAAC,EAAE,MAAM,CAAA;IACjC,wBAAwB,CAAC,EAAE,MAAM,CAAA;IACjC,qBAAqB,CAAC,EAAE,MAAM,CAAA;IAC9B,qBAAqB,CAAC,EAAE,MAAM,CAAA;IAC9B,gBAAgB,CAAC,EAAE,oBAAoB,CAAA;CACxC;AAED,MAAM,WAAW,8BAA8B;IAC7C,oBAAoB,EAAE,MAAM,CAAA;IAC5B,wBAAwB,CAAC,EAAE,MAAM,CAAA;IACjC,wBAAwB,CAAC,EAAE,MAAM,CAAA;IACjC,YAAY,EAAE,wBAAwB,CAAA;IAEtC,qBAAqB,CAAC,EAAE,MAAM,CAAA;IAC9B,qBAAqB,CAAC,EAAE,MAAM,CAAA;IAC9B,QAAQ,CAAC,EAAE,oBAAoB,CAAA;IAC/B,iBAAiB,CAAC,EAAE,MAAM,CAAA;IAC1B,mBAAmB,CAAC,EAAE,mBAAmB,CAAA;CAC1C;AAGD,MAAM,MAAM,mBAAmB,GAAG,UAAU,CAAC,8BAA8B,CAAC,CAAA;AAE5E,MAAM,WAAW,8BAA8B;IAC7C,KAAK,EAAE,aAAa,CAAA;IACpB,UAAU,EAAE,MAAM,CAAC,MAAM,EAAE,YAAY,CAAC,CAAA;IACxC,OAAO,EAAE,aAAa,CAAA;IAEtB,EAAE,EAAE,MAAM,EAAE,CAAA;CACb;AAED,MAAM,WAAW,aAAa;IAC5B,MAAM,EAAE,MAAM,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAA;IAChC,QAAQ,EAAE,MAAM,CAAC,MAAM,EAAE,gBAAgB,CAAC,CAAA;CAC3C;AAED,MAAM,WAAW,QAAQ;IAEvB,IAAI,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAA;IAC/B,KAAK,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAA;IAChC,MAAM,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAA;IAEjC,OAAO,EAAE,MAAM,CAAC,QAAQ,GAAG,OAAO,GAAG,MAAM,EAAE,MAAM,GAAG,SAAS,CAAC,CAAA;CACjE;AAID,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,MAAM,CAAA;IACZ,KAAK,EAAE,MAAM,CAAA;IACb,MAAM,EAAE,MAAM,CAAA;CACf;AAED,MAAM,WAAW,YAAY;IAC3B,WAAW,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAA;IACtC,MAAM,EAAE,MAAM,CAAA;IACd,MAAM,EAAE,MAAM,CAAA;CACf;AAED,MAAM,MAAM,gBAAgB,GACxB,MAAM,GACN,OAAO,GACP,QAAQ,GACR,SAAS,GACT,UAAU,GACV,WAAW,GACX,YAAY,GACZ,aAAa,GACb,cAAc,GACd,eAAe,GACf,gBAAgB,GAChB,iBAAiB,CAAA;AACrB,MAAM,MAAM,eAAe,GAAG,MAAM,CAAA;AAEpC;;;;;;GAMG;AACH,MAAM,WAAW,mBAAmB;IAClC,mBAAmB,EAAE,MAAM,CAAA;IAC3B,kBAAkB,EAAE,MAAM,CAAA;CAC3B;AAED,MAAM,WAAW,eAAe;IAC9B,eAAe,EAAE,mBAAmB,CAAA;IACpC,QAAQ,EAAE,MAAM,CAAA;IAChB,UAAU,EAAE,MAAM,CAAA;IAClB,KAAK,CAAC,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAA;IAG3D,WAAW,EAAE,MAAM,CAAA;CACpB;AAED,MAAM,WAAW,WAAW;IAC1B,OAAO,EAAE,MAAM,CAAA;IACf,KAAK,EAAE,MAAM,CAAA;IACb,IAAI,EAAE,MAAM,CAAA;IACZ,WAAW,EAAE,MAAM,CAAA;IACnB,QAAQ,EAAE,MAAM,CAAA;IAChB,SAAS,EAAE,MAAM,CAAA;IACjB,WAAW,EAAE,MAAM,CAAA;IACnB,sBAAsB,EAAE,MAAM,CAAA;IAC9B,QAAQ,EAAE,MAAM,CAAA;IAChB,QAAQ,EAAE,MAAM,CAAA;IAChB,GAAG,EAAE,MAAM,CAAA;IACX,aAAa,EAAE,MAAM,CAAA;IACrB,iBAAiB,EAAE,MAAM,CAAA;IACzB,SAAS,EAAE,MAAM,CAAA;CAClB;AAED,MAAM,WAAW,aAAa;IAC5B,EAAE,EAAE,MAAM,EAAE,CAAA;IAEZ,OAAO,EAAE,MAAM,CAAC,eAAe,EAAE,WAAW,CAAC,CAAA;IAE7C,OAAO,EAAE,MAAM,CACb,eAAe,EACf;QAEE,QAAQ,EAAE,MAAM,CAAA;QAEhB,IAAI,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAA;QACzD,KAAK,EAAE,MAAM,CAAC,gBAAgB,EAAE,iBAAiB,CAAC,MAAM,CAAC,CAAC,CAAA;KAC3D,CACF,CAAA;IACD,OAAO,EAAE,MAAM,CAAC,eAAe,EAAE,MAAM,CAAC,CAAA;IACxC,QAAQ,EAAE,MAAM,CAAC,eAAe,EAAE,eAAe,CAAC,CAAA;CACnD;AAED,MAAM,MAAM,iBAAiB,CAAC,CAAC,IAAI,IAAI,CACrC,OAAO,CAAC,MAAM,CAAC,cAAc,EAAE,CAAC,CAAC,CAAC,EAClC,OAAO,CACR,GAAG;IACF,KAAK,EAAE,CAAC,CAAA;CACT,CAAA;AAED,MAAM,WAAW,qBAAqB;IACpC,eAAe,EAAE,MAAM,CAAA;IACvB,aAAa,EAAE,MAAM,CAAA;IACrB,kBAAkB,EAAE,MAAM,CAAA;CAC3B;AAED,MAAM,MAAM,gCAAgC,GAAG,QAAQ,GAAG,UAAU,CAAA;AACpE,MAAM,MAAM,wBAAwB,GAChC,SAAS,GACT,SAAS,GACT,UAAU,GACV,MAAM,CAAA;AACV,MAAM,MAAM,iCAAiC,GAAG,OAAO,GAAG,MAAM,CAAA;AAChE,MAAM,MAAM,kCAAkC,GAC1C,QAAQ,GACR,MAAM,GACN,MAAM,GACN,cAAc,CAAA;AAClB,MAAM,MAAM,+BAA+B,GAAG,UAAU,GAAG,YAAY,CAAA;AACvE,MAAM,MAAM,2BAA2B,GAAG,oBAAoB,CAAA;AAC9D,MAAM,MAAM,8BAA8B,GACtC,MAAM,GACN,QAAQ,GACR,OAAO,GACP,YAAY,GACZ,UAAU,CAAA;AACd,MAAM,MAAM,8BAA8B,GAAG,MAAM,GAAG,OAAO,GAAG,QAAQ,CAAA;AACxE,MAAM,MAAM,mBAAmB,GAAG,MAAM,GAAG,gBAAgB,CAAA;AAE3D,MAAM,WAAW,oBAAqB,SAAQ,8BAA8B;IAC1E,MAAM,EAAE,MAAM,CAAA;IACd,UAAU,EAAE,MAAM,CAAA;IAClB,KAAK,EAAE,wBAAwB,CAAA;IAC/B,QAAQ,CAAC,EAAE,MAAM,CAAA;IACjB,eAAe,EAAE,kCAAkC,CAAA;IACnD,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,WAAW,EAAE,8BAA8B,CAAA;IAC3C,YAAY,EAAE,+BAA+B,CAAA;IAC7C,QAAQ,EAAE,2BAA2B,CAAA;IACrC,cAAc,EAAE,iCAAiC,CAAA;IACjD,WAAW,EAAE,8BAA8B,CAAA;IAC3C,WAAW,CAAC,EAAE,eAAe,CAAA;IAC7B,EAAE,EAAE,IAAI,CAAC,WAAW,CAAA;IACpB,WAAW,CAAC,EAAE,IAAI,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAA;IACzC,eAAe,EAAE,MAAM,CAAA;IAEvB,cAAc,EAAE,8BAA8B,CAAA;IAC9C,YAAY,CAAC,EAAE,gBAAgB,CAAA;CAChC;AAED,MAAM,MAAM,mBAAmB,GAAG,IAAI,CACpC,IAAI,CAAC,mBAAmB,EACxB,aAAa,GAAG,aAAa,CAC9B,GACC,wBAAwB,GAAG;IACzB,aAAa,CAAC,EAAE,gCAAgC,CAAA;IAChD,KAAK,CAAC,EAAE,wBAAwB,CAAA;IAChC,cAAc,CAAC,EAAE,iCAAiC,CAAA;IAClD,eAAe,CAAC,EAAE,kCAAkC,CAAA;IACpD,YAAY,CAAC,EAAE,+BAA+B,CAAA;IAC9C,QAAQ,CAAC,EAAE,2BAA2B,CAAA;IACtC,WAAW,CAAC,EAAE,8BAA8B,CAAA;IAC5C,IAAI,CAAC,EAAE,MAAM,CAAA;IACb,WAAW,CAAC,EAAE,8BAA8B,CAAA;IAC5C,eAAe,CAAC,EAAE,MAAM,CAAA;IACxB,mBAAmB,CAAC,EAAE,mBAAmB,CAAA;IACzC,gBAAgB,CAAC,EAAE,oBAAoB,CAAA;IACvC,iBAAiB,CAAC,EAAE,MAAM,CAAA;IAC1B,YAAY,CAAC,EAAE,gBAAgB,CAAA;IAC/B,WAAW,CAAC,EAAE,eAAe,CAAA;CAC9B,CAAA;AAEH,MAAM,MAAM,2BAA2B,GAAG,IAAI,CAAC,2BAA2B,GACxE,IAAI,CACF,oBAAoB,EAClB,cAAc,GACd,MAAM,GACN,aAAa,GACb,UAAU,GACV,gBAAgB,GAChB,aAAa,CAChB,CAAA;AAEH,MAAM,MAAM,qBAAqB,GAC7B,IAAI,CAAC,qBAAqB,GAC1B,mBAAmB,GACnB,mBAAmB,GACnB,iBAAiB,GACjB,SAAS,GACT,MAAM,GACN,SAAS,GACT,mBAAmB,CAAA;AAEvB,MAAM,WAAW,gBAAgB;IAC/B,IAAI,EAAE,qBAAqB,CAAA;IAC3B,KAAK,EAAE,MAAM,CAAA;IACb,MAAM,CAAC,EAAE,MAAM,CAAA;CAChB;AAED,MAAM,WAAW,kBAAmB,SAAQ,gBAAgB;IAC1D,MAAM,EAAE,MAAM,CAAA;CACf"}