import{ba as T,cG as I,a as N,c as v,cN as A,cP as D,fj as k,r as w,f2 as G,m as s,n as c}from"./strapi-YzJfjJ2z.js";import{C as O,T as R}from"./Form-CxQ2pPjq-BLI6Xln2.js";import{u as U}from"./hooks-E5u1mcgM-Ce79Lxm5.js";import{s as H}from"./objects-D6yBsdmx-8Q5QgcI9.js";import"./useDragAndDrop-DdHgKsqq-DDLMDz6i.js";import"./ComponentIcon-u4bIXTFY-BYfvWXgU.js";import"./FieldTypeIcon-CMlNO8PE-C0u_4SP8.js";import"./getEmptyImage-CjqolaH3.js";const Q=()=>{const{trackUsage:o}=T(),{formatMessage:n}=I(),{toggleNotification:a}=N(),{_unstableFormatAPIError:u}=v(),{isLoading:F,schema:y,model:L}=A(),{isLoading:S,error:h,list:E,edit:g}=D(),{fieldSizes:x,error:d,isLoading:_,isFetching:j}=k(void 0,{selectFromResult:e=>{const l=Object.entries(e.data?.fieldSizes??{}).reduce((r,[t,{default:i}])=>(r[t]=i,r),{});return{isFetching:e.isFetching,isLoading:e.isLoading,error:e.error,fieldSizes:l}}});w.useEffect(()=>{d&&a({type:"danger",message:u(d)})},[d,u,a]);const C=F||S||_||j,[M]=G(),P=async e=>{try{o("willSaveContentTypeLayout");const l=Object.entries(E.metadatas).reduce((t,[i,{mainField:m,...f}])=>{const z=g.metadatas[i],{__temp_key__:V,size:$,name:q,...b}=e.layout.flatMap(p=>p.children).find(p=>p.name===i)??{};return t[i]={edit:{...z,...b},list:f},t},{}),r=await M({layouts:{edit:e.layout.map(t=>t.children.reduce((i,{name:m,size:f})=>m!==R?[...i,{name:m,size:f}]:i,[])),list:E.layout.map(t=>t.name)},settings:H(e.settings,"displayName",void 0),metadatas:l,uid:L});"data"in r?(o("didEditEditSettings"),a({type:"success",message:n({id:"notification.success.saved",defaultMessage:"Saved"})})):a({type:"danger",message:u(r.error)})}catch{a({type:"danger",message:n({id:"notification.error",defaultMessage:"An error occurred"})})}};return C?s.jsx(c.Loading,{}):d||h||!y?s.jsx(c.Error,{}):s.jsxs(s.Fragment,{children:[s.jsx(c.Title,{children:`Configure ${g.settings.displayName} Edit View`}),s.jsx(O,{onSubmit:P,attributes:y.attributes,fieldSizes:x,layout:g})]})},te=()=>{const o=U(n=>n.admin_app.permissions.contentManager?.collectionTypesConfigurations);return s.jsx(c.Protect,{permissions:o,children:s.jsx(Q,{})})};export{Q as EditConfigurationPage,te as ProtectedEditConfigurationPage};
