import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-type-builder/dist/_chunks/ar-BYDB75EB.mjs
var from = "من";
var ar = {
  "attribute.boolean": "منطقي",
  "attribute.date": "تاريخ",
  "attribute.email": "بريد الإكتروني",
  "attribute.enumeration": "تعداد",
  "attribute.json": "JSON",
  "attribute.media": "وسائط",
  "attribute.password": "كلمة سر",
  "attribute.relation": "علاقة",
  "attribute.text": "نص",
  "form.attribute.item.customColumnName": "أسماء الأعمدة المخصصة",
  "form.attribute.item.customColumnName.description": "يفيد ذلك في إعادة تسمية أسماء أعمدة قاعدة البيانات بتنسيق أكثر شمولاً لاستجابات واجهة برمجة التطبيقات ( API )",
  "form.attribute.item.defineRelation.fieldName": "اسم الحقل",
  "form.attribute.item.enumeration.graphql": "تجاوز الاسم لـ GraphQL",
  "form.attribute.item.enumeration.graphql.description": "يسمح لك بتجاوز الاسم الذي تم إنشاؤه افتراضيًا لـ GraphQL",
  "form.attribute.item.enumeration.rules": "Values (one line per value)",
  "form.attribute.item.maximum": "اقصى قيمة",
  "form.attribute.item.maximumLength": "أقصى طول",
  "form.attribute.item.minimum": "أدنى قيمة",
  "form.attribute.item.minimumLength": "أدنى طول",
  "form.attribute.item.number.type": "تنسيق الرقم",
  "form.attribute.item.number.type.decimal": "عدد عشري (مثال: 2.22)",
  "form.attribute.item.number.type.float": "عدد عائم (مثال: 3.33333333)",
  "form.attribute.item.number.type.integer": "عدد صحيح (مثال: 10)",
  "form.attribute.item.requiredField": "الحقل مطلوب",
  "form.attribute.item.requiredField.description": "لن تتمكن من إنشاء إدخال إذا كان هذا الحقل فارغًا",
  "form.attribute.item.uniqueField": "حقل فريد",
  "form.attribute.item.uniqueField.description": "لن تتمكن من إنشاء إدخال إذا كان هناك إدخال حالي بمحتوى متطابق",
  "form.attribute.settings.default": "القيمة الأفتراضية",
  "form.button.cancel": "الغاء",
  from,
  "modelPage.attribute.relationWith": "علاقة مع",
  "plugin.description.long": "قم بتجميع هيكل البيانات الخاص بـ API الخاص بك. إنشاء حقول وعلاقات جديدة في دقيقة واحدة فقط. يتم إنشاء الملفات وتحديثها تلقائيًا في مشروعك.",
  "plugin.description.short": "قم بتجميع هيكل البيانات الخاص بـ API الخاص بك.",
  "popUpForm.navContainer.advanced": "إعدادات متقدمة",
  "popUpForm.navContainer.base": "إعدادات القاعدة",
  "popUpWarning.bodyMessage.contentType.delete": "هل أنت متأكد من أنك تريد حذف نوع المحتوى هذا؟",
  "relation.attributeName.placeholder": "مثال: المؤلف, الفئة, الوسم",
  "relation.manyToMany": "يملك وينتم للكثير",
  "relation.manyToOne": "يملك الكثير",
  "relation.oneToMany": "ينتمي للكثير",
  "relation.oneToOne": "يتشارك بواحد",
  "relation.oneWay": "يمتلك واحد",
  "modalForm.header.back": "خلف"
};
export {
  ar as default,
  from
};
//# sourceMappingURL=ar-BYDB75EB-TKFQDAA7.js.map
