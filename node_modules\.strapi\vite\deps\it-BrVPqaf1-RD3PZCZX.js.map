{"version": 3, "sources": ["../../../@strapi/content-manager/dist/_chunks/it-BrVPqaf1.mjs"], "sourcesContent": ["const groups = \"Gruppi\";\nconst models = \"Tipi Collezione\";\nconst pageNotFound = \"Pagina non trovata\";\nconst it = {\n  \"EditRelations.title\": \"Dati relazionali\",\n  \"api.id\": \"API ID\",\n  \"components.AddFilterCTA.add\": \"Filtri\",\n  \"components.AddFilterCTA.hide\": \"Filtri\",\n  \"components.DraggableAttr.edit\": \"Clicca per modificare\",\n  \"components.DynamicZone.pick-compo\": \"Scegli un componente\",\n  \"components.DynamicZone.required\": \"Componente richiesto\",\n  \"components.EmptyAttributesBlock.button\": \"Vai alla pagina delle impostazioni\",\n  \"components.EmptyAttributesBlock.description\": \"Puoi cambiare le tue impostazioni\",\n  \"components.FieldItem.linkToComponentLayout\": \"Modifica impaginazione componente\",\n  \"components.FilterOptions.button.apply\": \"Applica\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Applica\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Cancella tutto\",\n  \"components.FiltersPickWrapper.PluginHeader.description\": \"Imposta le condizioni da applicare per filtrare gli elementi\",\n  \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filtri\",\n  \"components.FiltersPickWrapper.hide\": \"Nascondi\",\n  \"components.LimitSelect.itemsPerPage\": \"Elementi per pagina\",\n  \"components.NotAllowedInput.text\": \"Non hai il permesso di vedere questo campo\",\n  \"components.Search.placeholder\": \"Ricerca elementi...\",\n  \"components.Select.draft-info-title\": \"Stato: Bozza\",\n  \"components.Select.publish-info-title\": \"Stato: Pubblicato\",\n  \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Personalizza l'aspetto della schermata di modifica.\",\n  \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Definisci le impostazioni delle liste di elementi.\",\n  \"components.SettingsViewWrapper.pluginHeader.title\": \"Configura la vista - {name}\",\n  \"components.TableDelete.delete\": \"Elimina tutti\",\n  \"components.TableDelete.deleteSelected\": \"Elimina selezionato\",\n  \"components.TableEmpty.withFilters\": \"Nessun {contentType} con questi filtri...\",\n  \"components.TableEmpty.withSearch\": \"Nessun {contentType} corrispondente alla ricerca ({search})...\",\n  \"components.TableEmpty.withoutFilter\": \"Non ci sono {contentType}...\",\n  \"components.empty-repeatable\": \"Ancora nessun elemento. Clicca il pulsante sottostante per aggiungerne uno.\",\n  \"components.notification.info.maximum-requirement\": \"Hai già raggiunto il massimo numero di campi\",\n  \"components.notification.info.minimum-requirement\": \"È stato aggiunto un campo per soddisfare il requisito minimo\",\n  \"components.repeatable.reorder.error\": \"Si è verificato un errore durante il riordinamento del campo del componente\",\n  \"components.reset-entry\": \"Azzera elemento\",\n  \"components.uid.apply\": \"applica\",\n  \"components.uid.available\": \"disponibile\",\n  \"components.uid.regenerate\": \"rigenera\",\n  \"components.uid.suggested\": \"suggerito\",\n  \"components.uid.unavailable\": \"non disponibile\",\n  \"containers.Edit.Link.Layout\": \"Configura impaginazione\",\n  \"containers.Edit.Link.Model\": \"Modifica la Collezione\",\n  \"containers.Edit.addAnItem\": \"Aggiungi un elemento...\",\n  \"containers.Edit.clickToJump\": \"Clicca per andare all'elemento\",\n  \"containers.Edit.delete\": \"Elimina\",\n  \"containers.Edit.delete-entry\": \"Elimina questo elemento\",\n  \"containers.Edit.editing\": \"Modifica in corso...\",\n  \"containers.Edit.information\": \"Informazioni\",\n  \"containers.Edit.information.by\": \"Da\",\n  \"containers.Edit.information.draftVersion\": \"versione bozza\",\n  \"containers.Edit.information.editing\": \"Modifica\",\n  \"containers.Edit.information.lastUpdate\": \"Aggiornato\",\n  \"containers.Edit.information.publishedVersion\": \"versione pubblicata\",\n  \"containers.Edit.pluginHeader.title.new\": \"Crea un elemento\",\n  \"containers.Edit.reset\": \"Azzera\",\n  \"containers.Edit.returnList\": \"Torna alla lista\",\n  \"containers.Edit.seeDetails\": \"Dettagli\",\n  \"containers.Edit.submit\": \"Salva\",\n  \"containers.EditSettingsView.modal-form.edit-field\": \"Modifica campo\",\n  \"containers.EditView.notification.errors\": \"Il form contiene degli errori\",\n  \"containers.Home.introduction\": \"Per modificare le voci, visitare il link nel menu di sinistra. Questo plugin non ha un modo per modificare le impostazioni ed è ancora in fase di sviluppo attivo.\",\n  \"containers.Home.pluginHeaderDescription\": \"Gestisci i tuoi dati attraverso un'interfaccia bella e potente.\",\n  \"containers.Home.pluginHeaderTitle\": \"Gestore Contenuti\",\n  \"containers.List.draft\": \"Bozza\",\n  \"containers.List.errorFetchRecords\": \"Errore\",\n  \"containers.List.published\": \"Pubblicato\",\n  \"containers.list.displayedFields\": \"Campi visualizzati\",\n  \"containers.list.table-headers.publishedAt\": \"Stato\",\n  \"containers.ListSettingsView.modal-form.edit-label\": \"Modifica etichetta\",\n  \"containers.SettingPage.add.field\": \"Inserisci nuovo campo\",\n  \"containers.SettingPage.attributes\": \"Attributi\",\n  \"containers.SettingPage.attributes.description\": \"Definisci l'ordine degli attributi\",\n  \"containers.SettingPage.editSettings.description\": \"Sposta i campi per costruire il layout\",\n  \"containers.SettingPage.editSettings.entry.title\": \"Titolo elemento\",\n  \"containers.SettingPage.editSettings.entry.title.description\": \"Scegli quale campo mostrare dell'elemento\",\n  \"containers.SettingPage.editSettings.relation-field.description\": \"Scegli il campo da mostrare nelle liste e durante la modifica\",\n  \"containers.SettingPage.editSettings.title\": \"Modifica (impostazioni)\",\n  \"containers.SettingPage.layout\": \"Layout\",\n  \"containers.SettingPage.listSettings.description\": \"Scegli le opzioni per questa Collezione\",\n  \"containers.SettingPage.listSettings.title\": \"Lista (impostazioni)\",\n  \"containers.SettingPage.pluginHeaderDescription\": \"Configura le impostazioni specifiche per questa Collezione\",\n  \"containers.SettingPage.settings\": \"Impostazioni\",\n  \"containers.SettingPage.view\": \"Vista\",\n  \"containers.SettingViewModel.pluginHeader.title\": \"Gestore Contenuti - {name}\",\n  \"containers.SettingsPage.Block.contentType.description\": \"Configura le impostazioni specifiche\",\n  \"containers.SettingsPage.Block.contentType.title\": \"Tipi Collezione\",\n  \"containers.SettingsPage.Block.generalSettings.description\": \"Configura le impostazioni di default per le tue Collezioni\",\n  \"containers.SettingsPage.Block.generalSettings.title\": \"Generali\",\n  \"containers.SettingsPage.pluginHeaderDescription\": \"Configura le impostazioni per tutti i Tipi Collezione e i Gruppi\",\n  \"containers.SettingsView.list.subtitle\": \"Configura il layout e la vista per i tuoi tipi Collezione e i gruppi\",\n  \"containers.SettingsView.list.title\": \"Configurazioni vista\",\n  \"emptyAttributes.button\": \"Vai al costruttore di collezioni\",\n  \"emptyAttributes.description\": \"Aggiungi il primo campo al tuo Tipo Collezione\",\n  \"emptyAttributes.title\": \"Nessun campo presente\",\n  \"error.attribute.key.taken\": \"Questo valore esiste già\",\n  \"error.attribute.sameKeyAndName\": \"Non possono essere uguali\",\n  \"error.attribute.taken\": \"Esiste già un campo con questo nome\",\n  \"error.contentTypeName.taken\": \"Questo nome esiste già\",\n  \"error.model.fetch\": \"Si è verificato un errore durante il caricamento dei modelli di configurazione.\",\n  \"error.record.create\": \"Si è verificato un errore durante la creazione dell'elemento.\",\n  \"error.record.delete\": \"Si è verificato un errore durante la cancellazione dell'elemento.\",\n  \"error.record.fetch\": \"Si è verificato un errore durante il caricamento dell'elemento.\",\n  \"error.record.update\": \"Si è verificato un errore durante l'aggiornamento dell'elemento.\",\n  \"error.records.count\": \"Si è verificato un errore durante il conteggio degli elementi.\",\n  \"error.records.fetch\": \"Si è verificato un errore durante il caricamento degli elementi.\",\n  \"error.schema.generation\": \"Si è verificato un errore durante la generazione dello schema.\",\n  \"error.validation.json\": \"Non è un JSON\",\n  \"error.validation.max\": \"Il valore è troppo alto.\",\n  \"error.validation.maxLength\": \"Il valore è troppo lungo.\",\n  \"error.validation.min\": \"Il valore è troppo basso.\",\n  \"error.validation.minLength\": \"Il valore è troppo breve.\",\n  \"error.validation.minSupMax\": \"Non può essere superiore\",\n  \"error.validation.regex\": \"Il valore non corrisponde alla RegEx.\",\n  \"error.validation.required\": \"Questo valore è richiesto.\",\n  \"form.Input.bulkActions\": \"Abilita azioni in blocco\",\n  \"form.Input.defaultSort\": \"Attributo di ordinamento di default\",\n  \"form.Input.description\": \"Descrizione\",\n  \"form.Input.description.placeholder\": \"Mostra nome nel profilo\",\n  \"form.Input.editable\": \"Campo modificabile\",\n  \"form.Input.filters\": \"Abilita filtri\",\n  \"form.Input.label\": \"Etichetta\",\n  \"form.Input.label.inputDescription\": \"Questo valore sovrascrive l'etichetta mostrata nell'intestazione della tabella\",\n  \"form.Input.pageEntries\": \"Righe per pagina\",\n  \"form.Input.pageEntries.inputDescription\": \"Nota: Puoi sovrascrivere questo valore nella pagina delle impostazioni del Tipo Collezione.\",\n  \"form.Input.placeholder\": \"Segnaposto\",\n  \"form.Input.placeholder.placeholder\": \"Il mio fantastico valore\",\n  \"form.Input.search\": \"Abilita ricerca\",\n  \"form.Input.search.field\": \"Abilita ricerca su questo campo\",\n  \"form.Input.sort.field\": \"Abilita ordinamento su questo campo\",\n  \"form.Input.wysiwyg\": \"Mostra come WYSIWYG\",\n  \"global.displayedFields\": \"Campi visualizzati\",\n  groups,\n  \"groups.numbered\": \"Gruppi ({number})\",\n  models,\n  \"models.numbered\": \"Tipi Collezione ({number})\",\n  \"notification.error.displayedFields\": \"Devi avere almeno un campo visualizzato\",\n  \"notification.error.relationship.fetch\": \"Si è verificato un errore durante il caricamento della relazione.\",\n  \"notification.info.SettingPage.disableSort\": \"Devi avere almeno un attributo con ordinamento abilitato\",\n  \"notification.info.minimumFields\": \"Devi avere almeno un campo visualizzato\",\n  \"notification.upload.error\": \"Si è verificato un errore durante il caricamento dei file\",\n  pageNotFound,\n  \"permissions.not-allowed.create\": \"Non sei autorizzato a creare documenti\",\n  \"permissions.not-allowed.update\": \"Non sei autorizzato a vedere questo documento\",\n  \"plugin.description.long\": \"Permette di vedere, modificare e cancellare i dati presenti nel database in modo veloce.\",\n  \"plugin.description.short\": \"Permette di vedere, modificare e cancellare i dati presenti nel database in modo veloce.\",\n  \"success.record.delete\": \"Eliminato\",\n  \"success.record.publish\": \"Pubblicato\",\n  \"success.record.save\": \"Salvato\",\n  \"success.record.unpublish\": \"Non pubblicato\",\n  \"popUpWarning.warning.publish-question\": \"Vuoi ancora pubblicarlo?\",\n  \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Sì, pubblica\"\n};\nexport {\n  it as default,\n  groups,\n  models,\n  pageNotFound\n};\n//# sourceMappingURL=it-BrVPqaf1.mjs.map\n"], "mappings": ";;;AAAA,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,KAAK;AAAA,EACT,uBAAuB;AAAA,EACvB,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,2DAA2D;AAC7D;", "names": []}