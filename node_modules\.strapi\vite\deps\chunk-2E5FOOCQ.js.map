{"version": 3, "sources": ["../../../@strapi/admin/admin/src/utils/users.ts"], "sourcesContent": ["import type { User } from '../features/Auth';\n\n/* -------------------------------------------------------------------------------------------------\n * getDisplayName\n * -----------------------------------------------------------------------------------------------*/\n\n/**\n * Retrieves the display name of an admin panel user\n */\nconst getDisplayName = ({ firstname, lastname, username, email }: Partial<User> = {}): string => {\n  if (username) {\n    return username;\n  }\n\n  // firstname is not required if the user is created with a username\n  if (firstname) {\n    return `${firstname} ${lastname ?? ''}`.trim();\n  }\n\n  return email ?? '';\n};\n\n/* -------------------------------------------------------------------------------------------------\n * hashAdminUserEmail\n * -----------------------------------------------------------------------------------------------*/\n\nconst hashAdminUserEmail = async (payload?: User) => {\n  if (!payload || !payload.email) {\n    return null;\n  }\n\n  try {\n    return await digestMessage(payload.email);\n  } catch (error) {\n    return null;\n  }\n};\n\nconst bufferToHex = (buffer: ArrayBuffer) => {\n  return [...new Uint8Array(buffer)].map((b) => b.toString(16).padStart(2, '0')).join('');\n};\nconst digestMessage = async (message: string) => {\n  const msgUint8 = new TextEncoder().encode(message);\n  const hashBuffer = await crypto.subtle.digest('SHA-256', msgUint8);\n\n  return bufferToHex(hashBuffer);\n};\n\nexport { getDisplayName, hashAdminUserEmail };\n"], "mappings": ";AASM,IAAA,iBAAiB,CAAC,EAAE,WAAW,UAAU,UAAU,MAAA,IAAyB,CAAA,MAAe;AAC/F,MAAI,UAAU;AACL,WAAA;EAAA;AAIT,MAAI,WAAW;AACb,WAAO,GAAG,SAAS,IAAI,YAAY,EAAE,GAAG,KAAK;EAAA;AAG/C,SAAO,SAAS;AAClB;AAMM,IAAA,qBAAqB,OAAO,YAAmB;AACnD,MAAI,CAAC,WAAW,CAAC,QAAQ,OAAO;AACvB,WAAA;EAAA;AAGL,MAAA;AACK,WAAA,MAAM,cAAc,QAAQ,KAAK;EAAA,SACjC,OAAO;AACP,WAAA;EAAA;AAEX;AAEA,IAAM,cAAc,CAAC,WAAwB;AACpC,SAAA,CAAC,GAAG,IAAI,WAAW,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,EAAE,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG,CAAC,EAAE,KAAK,EAAE;AACxF;AACA,IAAM,gBAAgB,OAAO,YAAoB;AAC/C,QAAM,WAAW,IAAI,YAAY,EAAE,OAAO,OAAO;AACjD,QAAM,aAAa,MAAM,OAAO,OAAO,OAAO,WAAW,QAAQ;AAEjE,SAAO,YAAY,UAAU;AAC/B;", "names": []}