{"name": "@formatjs/ecma402-abstract", "version": "1.14.3", "description": "A collection of implementation for ECMAScript abstract operations", "keywords": ["intl", "i18n", "relative", "javascript", "es", "abstract", "ecma402", "ecma262", "format"], "dependencies": {"@formatjs/intl-localematcher": "0.2.32", "tslib": "^2.4.0"}, "author": "<PERSON> <<EMAIL>", "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "repository": {"type": "git", "url": "**************:formatjs/formatjs.git"}, "sideEffects": false, "main": "index.js", "module": "lib/index.js", "types": "index.d.ts", "homepage": "https://github.com/formatjs/formatjs", "license": "MIT", "gitHead": "a7842673d8ad205171ad7c8cb8bb2f318b427c0c"}