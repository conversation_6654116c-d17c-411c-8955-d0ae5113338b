const e={"BoundRoute.title":"Wywoływanie","EditForm.inputSelect.description.role":"Połączy nowego uwierzytelnionego użytkownika z wybraną rolą.","EditForm.inputSelect.label.role":"Domyślna rola dla uwierzytelnionych użytkowników","EditForm.inputToggle.description.email":"Nie zezwalaj użytkownikowi na tworzenie wielu kont za pomocą tego samego adresu e-mail u różnych dostawców uwierzytelniania.","EditForm.inputToggle.description.email-confirmation":"<PERSON><PERSON> (ON), nowo zarejestrowani uzytkownicy otrzymają wiadomość potwierdzającą.","EditForm.inputToggle.description.email-confirmation-redirection":"Po potwierdzeniu adresu email, wybierz gdzie zostaniesz przekierowany.","EditForm.inputToggle.description.email-reset-password":"Adres URL strony resetowania hasła aplikacji","EditForm.inputToggle.description.sign-up":"<PERSON> w<PERSON>u (OFF) proces rejestracji jest zabroniony. Nikt nie może już dołączyć bez względu na używanego dostawcę.","EditForm.inputToggle.label.email":"Jedno konto na adres email","EditForm.inputToggle.label.email-confirmation":"Zezwól na potwierdzenie adresu email","EditForm.inputToggle.label.email-confirmation-redirection":"Url przekierowania","EditForm.inputToggle.label.email-reset-password":"Strona resetowania hasła","EditForm.inputToggle.label.sign-up":"Włącz możliwość rejestracji","EditForm.inputToggle.placeholder.email-confirmation-redirection":"ex: https://yourfrontend.com/confirmation-redirection","EditForm.inputToggle.placeholder.email-reset-password":"ex: https://yourfrontend.com/reset-password","EditPage.form.roles":"Szczegóły roli","Email.template.data.loaded":"Szablon email został załadowany","Email.template.email_confirmation":"Potwierdzenie adresu email","Email.template.form.edit.label":"Edytuj szablon","Email.template.table.action.label":"akcja","Email.template.table.icon.label":"ikonka","Email.template.table.name.label":"nazwa","Form.advancedSettings.data.loaded":"Ustawienia zaawansowane zostały załadowane","HeaderNav.link.advancedSettings":"Zaawansowane","HeaderNav.link.emailTemplates":"Szablony e-mail","HeaderNav.link.providers":"Dostawcy","Plugin.permissions.plugins.description":"Określ dozwolone działania dla pluginu {name}.","Plugins.header.description":"Jedynie akcje związane z wywoływaniami są wymienione poniżej.","Plugins.header.title":"Uprawnienia","Policies.header.hint":"Wybierz działania aplikacji lub działania pluginu i kliknij ikonę koła zębatego, aby wyświetlić wywoływania","Policies.header.title":"Zaawansowane","PopUpForm.Email.email_templates.inputDescription":"Nie wiesz jak skonfigurować zmienne? {link}","PopUpForm.Email.link.documentation":"sprawdź dokumentację.","PopUpForm.Email.options.from.email.label":"Email nadawcy","PopUpForm.Email.options.from.email.placeholder":"<EMAIL>","PopUpForm.Email.options.from.name.label":"Nazwa nadawcy","PopUpForm.Email.options.from.name.placeholder":"Jan Nowak","PopUpForm.Email.options.message.label":"Wiadomość","PopUpForm.Email.options.object.label":"Temat","PopUpForm.Email.options.object.placeholder":"Proszę potwierdź adres email dla %APP_NAME%","PopUpForm.Email.options.response_email.label":"Email zwrotny","PopUpForm.Email.options.response_email.placeholder":"<EMAIL>","PopUpForm.Providers.enabled.description":"W przypadku wyłączenia, użytkownicy nie będą mogli skorzystać z tego dostawcy.","PopUpForm.Providers.enabled.label":"Włączony","PopUpForm.Providers.key.label":"ID klienta","PopUpForm.Providers.key.placeholder":"TEKST","PopUpForm.Providers.redirectURL.front-end.label":"Adres przekierowania do własnej aplikacji","PopUpForm.Providers.redirectURL.label":"Adres przekierowania do dodania w twoich ustawieniach aplikacji: {provider}","PopUpForm.Providers.secret.label":"Klucz sekretny klienta","PopUpForm.Providers.secret.placeholder":"TEKST","PopUpForm.Providers.subdomain.label":"Host URI (Subdomain)","PopUpForm.Providers.subdomain.placeholder":"my.subdomain.com","PopUpForm.header.edit.email-templates":"Zmień szablony e-mail","PopUpForm.header.edit.providers":"Edytuj dostawcę","Providers.data.loaded":"Dostawcy zostali załadowani","Providers.status":"Status","Roles.empty":"Nie masz jeszcze żadnych ról.","Roles.empty.search":"Żadne role nie pasują do wyszukiwania.","Settings.roles.deleted":"Rola usunięta","Settings.roles.edited":"Rola edytowana","Settings.section-label":"Użytkownicy i Uprawnienia","components.Input.error.validation.email":"Ten email jest niepoprawny","components.Input.error.validation.json":"Nie pasuje do formatu JSON","components.Input.error.validation.max":"Wartość zbyt duża.","components.Input.error.validation.maxLength":"Wartość zbyt długa.","components.Input.error.validation.min":"Wartość zbyt mała.","components.Input.error.validation.minLength":"Wartość zbyt krótka.","components.Input.error.validation.minSupMax":"Nie może być wyższy","components.Input.error.validation.regex":"Wartość nie pasuje do regexa.","components.Input.error.validation.required":"Wartość wymagana.","components.Input.error.validation.unique":"Wartość już używana.","notification.success.submit":"Ustawienia zostały zaktualizowane","page.title":"Ustawienia - Role","plugin.description.long":"Chroń API za pomocą procesu pełnego uwierzytelniania opartego na JWT. Ten plugin zawiera również strategię ACL, która pozwala zarządzać uprawnieniami między grupami użytkowników.","plugin.description.short":"Chroń API za pomocą procesu pełnego uwierzytelniania opartego na JWT","plugin.name":"Role i Uprawnienia","popUpWarning.button.cancel":"Anuluj","popUpWarning.button.confirm":"Potwierdź","popUpWarning.title":"Proszę potwierdź","popUpWarning.warning.cancel":"Czy jesteś pewny, że chcesz anulować zmiany?"};export{e as default};
