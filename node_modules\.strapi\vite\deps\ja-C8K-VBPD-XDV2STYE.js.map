{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/_chunks/ja-C8K-VBPD.mjs"], "sourcesContent": ["const ja = {\n  \"BoundRoute.title\": \"Bound route to\",\n  \"EditForm.inputSelect.description.role\": \"新しい認証されたユーザーが選択された役割にアタッチされます。\",\n  \"EditForm.inputSelect.label.role\": \"認証されたユーザーのデフォルトの役割\",\n  \"EditForm.inputToggle.description.email\": \"ユーザーが異なる認証プロバイダで同じ電子メールアドレスを使用して複数のアカウントを作成できないようにします。\",\n  \"EditForm.inputToggle.description.email-confirmation\": \"有効（ON）にすると、新しい登録ユーザーに確認メールが送信されます。\",\n  \"EditForm.inputToggle.description.email-confirmation-redirection\": \"あなたのEメールを確認したら、リダイレクト先を選択してください。\",\n  \"EditForm.inputToggle.description.sign-up\": \"あなたの電子メールを確認した後、リダイレクト先を選択しました。\",\n  \"EditForm.inputToggle.label.email\": \"メールアドレスごとに1つのアカウント\",\n  \"EditForm.inputToggle.label.email-confirmation\": \"Eメールの確認を有効にする\",\n  \"EditForm.inputToggle.label.email-confirmation-redirection\": \"リダイレクトURL\",\n  \"EditForm.inputToggle.label.sign-up\": \"申し込みを有効にする\",\n  \"HeaderNav.link.advancedSettings\": \"高度な設定\",\n  \"HeaderNav.link.emailTemplates\": \"メールテンプレート\",\n  \"HeaderNav.link.providers\": \"プロバイダー\",\n  \"Plugin.permissions.plugins.description\": \"{name} 個のプラグインに対して許可されたすべてのアクションを定義する\",\n  \"Plugins.header.description\": \"ルートにバインドされたアクションのみが以下にリストされています\",\n  \"Plugins.header.title\": \"権限\",\n  \"Policies.header.hint\": \"アプリケーションのアクションまたはプラグインのアクションを選択し、コグアイコンをクリックしてバインドされたルートを表示します\",\n  \"Policies.header.title\": \"高度な設定\",\n  \"PopUpForm.Email.email_templates.inputDescription\": \"変数の使用方法がわからない場合は、{link}\",\n  \"PopUpForm.Email.options.from.email.label\": \"送信者Eメール\",\n  \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Email.options.from.name.label\": \"送信者名\",\n  \"PopUpForm.Email.options.from.name.placeholder\": \"Kai Doe\",\n  \"PopUpForm.Email.options.message.label\": \"メッセージ\",\n  \"PopUpForm.Email.options.object.label\": \"件名\",\n  \"PopUpForm.Email.options.response_email.label\": \"応答メール\",\n  \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Providers.enabled.description\": \"無効にすると、ユーザーはこのプロバイダを使用できなくなります。\",\n  \"PopUpForm.Providers.enabled.label\": \"有効にする\",\n  \"PopUpForm.Providers.key.label\": \"クライアントID\",\n  \"PopUpForm.Providers.key.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.redirectURL.front-end.label\": \"フロントエンドアプリへのリダイレクトURL\",\n  \"PopUpForm.Providers.secret.label\": \"クライアントの秘密\",\n  \"PopUpForm.Providers.secret.placeholder\": \"TEXT\",\n  \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n  \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n  \"PopUpForm.header.edit.email-templates\": \"メールテンプレートの編集\",\n  \"notification.success.submit\": \"設定が更新されました\",\n  \"plugin.description.long\": \"JWTに基づいた完全な認証プロセスでAPIを保護します。このプラグインには、ユーザーのグループ間で権限を管理できるACL戦略もあります。\",\n  \"plugin.description.short\": \"JWTに基づく完全な認証プロセスでAPIを保護する\",\n  \"plugin.name\": \"ロールと権限\"\n};\nexport {\n  ja as default\n};\n//# sourceMappingURL=ja-C8K-VBPD.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,sCAAsC;AAAA,EACtC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AACjB;", "names": []}