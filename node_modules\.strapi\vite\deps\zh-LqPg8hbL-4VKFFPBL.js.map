{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/zh-LqPg8hbL.mjs"], "sourcesContent": ["const Analytics = \"分析器\";\nconst Documentation = \"文件\";\nconst Email = \"電子郵件\";\nconst Password = \"密碼\";\nconst Provider = \"提供者\";\nconst ResetPasswordToken = \"密碼重設代碼\";\nconst Role = \"身份\";\nconst light = \"明亮\";\nconst dark = \"黑暗\";\nconst Username = \"使用者名稱\";\nconst Users = \"使用者\";\nconst anErrorOccurred = \"糟糕！出了點問題。請再試一次。\";\nconst clearLabel = \"清除\";\nconst or = \"或\";\nconst skipToContent = \"跳過到內容\";\nconst submit = \"送出\";\nconst zh = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"您的帳號已經被停用\",\n\t\"Auth.components.Oops.text.admin\": \"如果是個誤會，請聯繫您的管理員協助處理。\",\n\t\"Auth.components.Oops.title\": \"唉呀...\",\n\t\"Auth.form.active.label\": \"啟用\",\n\t\"Auth.form.button.forgot-password\": \"傳送電子郵件\",\n\t\"Auth.form.button.go-home\": \"回到首頁\",\n\t\"Auth.form.button.login\": \"登入\",\n\t\"Auth.form.button.login.providers.error\": \"無法透過所選的驗證方式連接您的帳號。\",\n\t\"Auth.form.button.login.strapi\": \"透過 Strapi 登入\",\n\t\"Auth.form.button.password-recovery\": \"找回密碼\",\n\t\"Auth.form.button.register\": \"準備開始\",\n\t\"Auth.form.confirmPassword.label\": \"再確認一次密碼\",\n\t\"Auth.form.currentPassword.label\": \"目前密碼\",\n\t\"Auth.form.email.label\": \"電子郵件\",\n\t\"Auth.form.email.placeholder\": \"例如 <EMAIL>\",\n\t\"Auth.form.error.blocked\": \"您的帳號已經被系統管理員停用。\",\n\t\"Auth.form.error.code.provide\": \"提供的代碼不正確。\",\n\t\"Auth.form.error.confirmed\": \"您的電子郵件地址尚未經過認證。\",\n\t\"Auth.form.error.email.invalid\": \"電子郵件地址無效。\",\n\t\"Auth.form.error.email.provide\": \"請輸入使用者名稱或電子郵件地址。\",\n\t\"Auth.form.error.email.taken\": \"此電子郵件地址已被使用。\",\n\t\"Auth.form.error.invalid\": \"使用者名稱或密碼不正確。\",\n\t\"Auth.form.error.params.provide\": \"參數不正確。\",\n\t\"Auth.form.error.password.format\": \"您的密碼不能包含超過三個 `$` 符號\",\n\t\"Auth.form.error.password.local\": \"這個使用者還沒有設定本站密碼，請使用建立帳號時使用的驗證方式登入。\",\n\t\"Auth.form.error.password.matching\": \"密碼不符。\",\n\t\"Auth.form.error.password.provide\": \"請輸入您的密碼。\",\n\t\"Auth.form.error.ratelimit\": \"嘗試次數過多，請稍後再試。\",\n\t\"Auth.form.error.user.not-exist\": \"這個電子郵件地址不存在。\",\n\t\"Auth.form.error.username.taken\": \"使用者名稱已被使用。\",\n\t\"Auth.form.firstname.label\": \"名字\",\n\t\"Auth.form.firstname.placeholder\": \"例如 Kai\",\n\t\"Auth.form.forgot-password.email.label\": \"輸入您的電子郵件地址\",\n\t\"Auth.form.forgot-password.email.label.success\": \"郵件已成功寄至 \",\n\t\"Auth.form.lastname.label\": \"姓氏\",\n\t\"Auth.form.lastname.placeholder\": \"例如 Doe\",\n\t\"Auth.form.password.hide-password\": \"隱藏密碼\",\n\t\"Auth.form.password.hint\": \"密碼至少要有 8 個字元，1 個大寫字母，1 個小寫字母和 1 個數字\",\n\t\"Auth.form.password.show-password\": \"顯示密碼\",\n\t\"Auth.form.register.news.label\": \"有新功能和改進時通知我 (打勾表示您接受 {terms} 和 {policy})。\",\n\t\"Auth.form.register.subtitle\": \"認證僅用於 Strapi 的驗證，所有儲存的資料都放在您的資料庫中。\",\n\t\"Auth.form.rememberMe.label\": \"記得我\",\n\t\"Auth.form.username.label\": \"使用者名稱\",\n\t\"Auth.form.username.placeholder\": \"例如 Kai Doe\",\n\t\"Auth.form.welcome.subtitle\": \"登入您的 Strapi 帳號\",\n\t\"Auth.form.welcome.title\": \"歡迎使用 Strapi！\",\n\t\"Auth.link.forgot-password\": \"忘記密碼？\",\n\t\"Auth.link.ready\": \"準備好登入了嗎？\",\n\t\"Auth.link.signin\": \"登入\",\n\t\"Auth.link.signin.account\": \"已經有帳號了嗎？\",\n\t\"Auth.login.sso.divider\": \"或登入透過\",\n\t\"Auth.login.sso.loading\": \"正在載入驗證方式...\",\n\t\"Auth.login.sso.subtitle\": \"透過 SSO 登入您的帳號\",\n\t\"Auth.privacy-policy-agreement.policy\": \"隱私權政策\",\n\t\"Auth.privacy-policy-agreement.terms\": \"服務條款\",\n\t\"Auth.reset-password.title\": \"重設密碼\",\n\t\"Content Manager\": \"內容管理員\",\n\t\"Content Type Builder\": \"內容型別建立員\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"檔案上傳\",\n\t\"HomePage.head.title\": \"首頁\",\n\t\"HomePage.roadmap\": \"查看我們的藍圖\",\n\t\"HomePage.welcome.congrats\": \"恭喜！\",\n\t\"HomePage.welcome.congrats.content\": \"您以首位管理員的身分登入，若要探索 Strapi 的強大功能，\",\n\t\"HomePage.welcome.congrats.content.bold\": \"建議您從建立第一個內容型別開始。\",\n\t\"Media Library\": \"媒體庫\",\n\t\"New entry\": \"新增項目\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"角色與權限\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"無法刪除一些角色，因為角色與使用者還有關聯。\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"與使用者有關聯的角色無法刪除\",\n\t\"Roles.RoleRow.select-all\": \"選擇 {name} 進行大量操作\",\n\t\"Roles.RoleRow.user-count\": \"{number, plural, =0 {#  user} one {#  user} other {# users}}\",\n\t\"Roles.components.List.empty.withSearch\": \"找不到您搜尋的角色 ({search})...\",\n\t\"Settings.PageTitle\": \"設定 - {name}\",\n\t\"Settings.apiTokens.addFirstToken\": \"新增您的第一個 API 權杖\",\n\t\"Settings.apiTokens.addNewToken\": \"新增 API 權杖\",\n\t\"Settings.tokens.copy.editMessage\": \"基於安全考量，您只能查看您的權杖一次。\",\n\t\"Settings.tokens.copy.editTitle\": \"這個權杖已無法存取。\",\n\t\"Settings.tokens.copy.lastWarning\": \"請務必複製這個權杖，錯過就不會再顯示了！\",\n\t\"Settings.apiTokens.create\": \"新增\",\n\t\"Settings.apiTokens.description\": \"已產生可使用 API 的權杖列表\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"您還沒有任何內容...\",\n\t\"Settings.apiTokens.ListView.headers.name\": \"名稱\",\n\t\"Settings.apiTokens.ListView.headers.description\": \"說明\",\n\t\"Settings.apiTokens.ListView.headers.type\": \"權杖類型\",\n\t\"Settings.apiTokens.ListView.headers.createdAt\": \"建立時間\",\n\t\"Settings.apiTokens.ListView.headers.lastUsedAt\": \"最後使用時間\",\n\t\"Settings.tokens.notification.copied\": \"權杖已複製到剪貼簿。\",\n\t\"Settings.apiTokens.title\": \"API 權杖\",\n\t\"Settings.tokens.types.full-access\": \"完全控制\",\n\t\"Settings.tokens.types.read-only\": \"唯讀\",\n\t\"Settings.tokens.duration.7-days\": \"7 天\",\n\t\"Settings.tokens.duration.30-days\": \"30 天\",\n\t\"Settings.tokens.duration.90-days\": \"90 天\",\n\t\"Settings.tokens.duration.unlimited\": \"無限制\",\n\t\"Settings.tokens.form.duration\": \"權杖有效期限\",\n\t\"Settings.tokens.form.type\": \"權杖類型\",\n\t\"Settings.tokens.duration.expiration-date\": \"到期日\",\n\t\"Settings.apiTokens.createPage.permissions.title\": \"權限\",\n\t\"Settings.apiTokens.createPage.permissions.description\": \"下方僅列出與路徑繫結的操作。\",\n\t\"Settings.tokens.RegenerateDialog.title\": \"重新產生權杖\",\n\t\"Settings.tokens.popUpWarning.message\": \"您確定要重新產生此權杖嗎？\",\n\t\"Settings.tokens.Button.cancel\": \"取消\",\n\t\"Settings.tokens.Button.regenerate\": \"重新產生\",\n\t\"Settings.application.description\": \"管理後台的全域資訊\",\n\t\"Settings.application.edition-title\": \"目前方案\",\n\t\"Settings.application.get-help\": \"取得協助\",\n\t\"Settings.application.link-pricing\": \"查看所有方案\",\n\t\"Settings.application.link-upgrade\": \"升級您的管理後台\",\n\t\"Settings.application.node-version\": \"node 版本\",\n\t\"Settings.application.strapi-version\": \"strapi 版本\",\n\t\"Settings.application.strapiVersion\": \"strapi 版本\",\n\t\"Settings.application.title\": \"總覽\",\n\t\"Settings.application.customization\": \"自訂\",\n\t\"Settings.application.customization.carousel.title\": \"標誌\",\n\t\"Settings.application.customization.carousel.change-action\": \"更改標誌\",\n\t\"Settings.application.customization.carousel.reset-action\": \"重設標誌\",\n\t\"Settings.application.customization.carousel-slide.label\": \"標誌投影片\",\n\t\"Settings.application.customization.carousel-hint\": \"更改管理面板標誌 (最大解析度：{dimension}x{dimension}，最大檔案大小：{size}KB)\",\n\t\"Settings.application.customization.modal.cancel\": \"取消\",\n\t\"Settings.application.customization.modal.upload\": \"上傳標誌\",\n\t\"Settings.application.customization.modal.tab.label\": \"您要如何上傳檔案？\",\n\t\"Settings.application.customization.modal.upload.from-computer\": \"從電腦\",\n\t\"Settings.application.customization.modal.upload.file-validation\": \"最大解析度：{dimension}x{dimension}，最大檔案大小：{size}KB\",\n\t\"Settings.application.customization.modal.upload.error-format\": \"上傳格式錯誤 (接受的格式：jpeg、jpg、png、svg)。\",\n\t\"Settings.application.customization.modal.upload.error-size\": \"上傳的檔案過大 (最大解析度：{dimension}x{dimension}，最大檔案大小：{size}KB)\",\n\t\"Settings.application.customization.modal.upload.error-network\": \"網路錯誤\",\n\t\"Settings.application.customization.modal.upload.cta.browse\": \"瀏覽檔案\",\n\t\"Settings.application.customization.modal.upload.drag-drop\": \"拖曳至此或\",\n\t\"Settings.application.customization.modal.upload.from-url\": \"從網址\",\n\t\"Settings.application.customization.modal.upload.from-url.input-label\": \"網址\",\n\t\"Settings.application.customization.modal.upload.next\": \"下一步\",\n\t\"Settings.application.customization.modal.pending\": \"擱置中的標誌\",\n\t\"Settings.application.customization.modal.pending.choose-another\": \"選擇其他標誌\",\n\t\"Settings.application.customization.modal.pending.title\": \"標誌已準備上傳\",\n\t\"Settings.application.customization.modal.pending.subtitle\": \"在上傳前管理選擇的標誌\",\n\t\"Settings.application.customization.modal.pending.upload\": \"上傳標誌\",\n\t\"Settings.application.customization.modal.pending.card-badge\": \"圖片\",\n\t\"Settings.error\": \"錯誤\",\n\t\"Settings.global\": \"全域設定\",\n\t\"Settings.permissions\": \"管理員後台\",\n\t\"Settings.permissions.category\": \"{category} 的權限設定\",\n\t\"Settings.permissions.category.plugins\": \"{category} 擴充功能的權限設定\",\n\t\"Settings.permissions.conditions.anytime\": \"隨時\",\n\t\"Settings.permissions.conditions.apply\": \"套用\",\n\t\"Settings.permissions.conditions.can\": \"可以\",\n\t\"Settings.permissions.conditions.conditions\": \"設定條件\",\n\t\"Settings.permissions.conditions.links\": \"連結\",\n\t\"Settings.permissions.conditions.no-actions\": \"在設定條件之前，您要先選則動作（新增、讀取、更新...）\",\n\t\"Settings.permissions.conditions.none-selected\": \"隨時\",\n\t\"Settings.permissions.conditions.or\": \"或\",\n\t\"Settings.permissions.conditions.when\": \"當\",\n\t\"Settings.permissions.select-all-by-permission\": \"選擇所有 {label} 權限\",\n\t\"Settings.permissions.select-by-permission\": \"選擇 {label} 權限\",\n\t\"Settings.permissions.users.create\": \"新增使用者\",\n\t\"Settings.permissions.users.email\": \"電子郵件\",\n\t\"Settings.permissions.users.firstname\": \"名字\",\n\t\"Settings.permissions.users.lastname\": \"姓氏\",\n\t\"Settings.permissions.users.user-status\": \"使用者狀態\",\n\t\"Settings.permissions.users.roles\": \"角色\",\n\t\"Settings.permissions.users.username\": \"使用者名稱\",\n\t\"Settings.permissions.users.active\": \"使用中\",\n\t\"Settings.permissions.users.inactive\": \"閒置中\",\n\t\"Settings.permissions.users.form.sso\": \"透過 SSO 登入\",\n\t\"Settings.permissions.users.form.sso.description\": \"當啟動這個選項時 (ON)，使用者可以透過 SSO 登入\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"找到 {number, plural, =0 {# users} one {# user } other {# users}}\",\n\t\"Settings.permissions.users.tabs.label\": \"分頁權限\",\n\t\"Settings.permissions.users.strapi-super-admin\": \"超級管理員\",\n\t\"Settings.permissions.users.strapi-editor\": \"編輯者\",\n\t\"Settings.permissions.users.strapi-author\": \"作者\",\n\t\"Settings.profile.form.notify.data.loaded\": \"您的個人檔案資料已經載入\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"清除已選的介面語言\",\n\t\"Settings.profile.form.section.experience.here\": \"此文檔\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"介面語言\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"將會用所選擇的語言顯示您的介面\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"只有您的介面會變為所選擇的語言。如果要為您的團隊提供其他語言，請參考{here}。\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"介面模式\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"在選擇的模式中顯示您的介面。\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{name} 模式\",\n\tlight: light,\n\tdark: dark,\n\t\"Settings.profile.form.section.experience.title\": \"體驗\",\n\t\"Settings.profile.form.section.head.title\": \"使用者個人檔案\",\n\t\"Settings.profile.form.section.profile.page.title\": \"個人檔案頁面\",\n\t\"Settings.roles.create.description\": \"定義賦予角色的權限\",\n\t\"Settings.roles.create.title\": \"新增角色\",\n\t\"Settings.roles.created\": \"角色已新增\",\n\t\"Settings.roles.edit.title\": \"編輯角色\",\n\t\"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# 使用者} one {# 使用者} other {# 使用者}} 是這個角色\",\n\t\"Settings.roles.form.created\": \"已新增\",\n\t\"Settings.roles.form.description\": \"角色的名字與描述\",\n\t\"Settings.roles.form.permission.property-label\": \"{label} 權限\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"欄位權限\",\n\t\"Settings.roles.form.permissions.create\": \"新增\",\n\t\"Settings.roles.form.permissions.delete\": \"刪除\",\n\t\"Settings.roles.form.permissions.publish\": \"發布\",\n\t\"Settings.roles.form.permissions.read\": \"讀取\",\n\t\"Settings.roles.form.permissions.update\": \"更新\",\n\t\"Settings.roles.list.button.add\": \"加入新的角色\",\n\t\"Settings.roles.list.description\": \"角色列表\",\n\t\"Settings.roles.title.singular\": \"角色\",\n\t\"Settings.sso.description\": \"調整 Single Sign-On 功能的設定\",\n\t\"Settings.sso.form.defaultRole.description\": \"它將新驗證的使用者附加到所選角色\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"您需要有讀取管理員角色的權限\",\n\t\"Settings.sso.form.defaultRole.label\": \"預設角色\",\n\t\"Settings.sso.form.registration.description\": \"SSO 登入帳號不存在時就新增使用者\",\n\t\"Settings.sso.form.registration.label\": \"自動註冊\",\n\t\"Settings.sso.title\": \"Single Sign-On\",\n\t\"Settings.webhooks.create\": \"新增 webhook\",\n\t\"Settings.webhooks.create.header\": \"新增一個 Header\",\n\t\"Settings.webhooks.created\": \"Webhook 已新增\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"此事件僅適用於啟用了草稿/發布系統的內容\",\n\t\"Settings.webhooks.events.create\": \"新增\",\n\t\"Settings.webhooks.events.update\": \"更新\",\n\t\"Settings.webhooks.form.events\": \"事件\",\n\t\"Settings.webhooks.form.headers\": \"Headers\",\n\t\"Settings.webhooks.form.url\": \"網址\",\n\t\"Settings.webhooks.headers.remove\": \"移除 header 第 {number} 行\",\n\t\"Settings.webhooks.key\": \"密鑰\",\n\t\"Settings.webhooks.list.button.add\": \"新增新的 webhook\",\n\t\"Settings.webhooks.list.description\": \"獲得 POST 更新通知\",\n\t\"Settings.webhooks.list.empty.description\": \"新增您第一個 webhook\",\n\t\"Settings.webhooks.list.empty.link\": \"參考我們的文件\",\n\t\"Settings.webhooks.list.empty.title\": \"這裡還沒有 webhook\",\n\t\"Settings.webhooks.list.th.actions\": \"動作\",\n\t\"Settings.webhooks.list.th.status\": \"狀態\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhooks\",\n\t\"Settings.webhooks.to.delete\": \"已選擇 {webhooksToDeleteLength, plural, one {# asset} other {# assets}}\",\n\t\"Settings.webhooks.trigger\": \"觸發\",\n\t\"Settings.webhooks.trigger.cancel\": \"取消觸發\",\n\t\"Settings.webhooks.trigger.pending\": \"等待…\",\n\t\"Settings.webhooks.trigger.save\": \"請儲存到觸發\",\n\t\"Settings.webhooks.trigger.success\": \"成功！\",\n\t\"Settings.webhooks.trigger.success.label\": \"觸發成功\",\n\t\"Settings.webhooks.trigger.test\": \"測試觸發\",\n\t\"Settings.webhooks.trigger.title\": \"觸發前先儲存\",\n\t\"Settings.webhooks.value\": \"數值\",\n\t\"Usecase.back-end\": \"後端開發者\",\n\t\"Usecase.button.skip\": \"跳過此問題\",\n\t\"Usecase.content-creator\": \"內容創作者\",\n\t\"Usecase.front-end\": \"後端開發者\",\n\t\"Usecase.full-stack\": \"全棧開發者\",\n\t\"Usecase.input.work-type\": \"您做甚麼類型的工作？\",\n\t\"Usecase.notification.success.project-created\": \"專案已成功建立\",\n\t\"Usecase.other\": \"其他\",\n\t\"Usecase.title\": \"再多向我們介紹您自己\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"使用者與權限\",\n\t\"Users.components.List.empty\": \"還沒有使用者...\",\n\t\"Users.components.List.empty.withFilters\": \"沒有符合篩選條件的使用者...\",\n\t\"Users.components.List.empty.withSearch\": \"沒有符合搜尋條件 ({search}) 的使用者...\",\n\t\"admin.pages.MarketPlacePage.head\": \"市集 - 擴充功能\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"您已離線\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"您必須連接至網際網路以存取 Strapi 市集。\",\n\t\"admin.pages.MarketPlacePage.plugins\": \"外掛程式\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"複製安裝命令\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"安裝命令已可貼至您的終端機\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"了解詳情\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"了解關於 {pluginName} 的詳細資訊\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"更多\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"已安裝\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Strapi 製作\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"經 Strapi 驗證的外掛程式\",\n\t\"admin.pages.MarketPlacePage.plugin.version\": \"更新您的 Strapi 版本：\\\"{strapiAppVersion}\\\" 至：\\\"{versionRange}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.version.null\": \"無法驗證與您的 Strapi 版本的相容性：\\\"{strapiAppVersion}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.githubStars\": \"此外掛程式在 GitHub 上獲得了 {starsCount} 顆星星\",\n\t\"admin.pages.MarketPlacePage.plugin.downloads\": \"此外掛程式的每周下載量為 {downloadsCount}\",\n\t\"admin.pages.MarketPlacePage.providers\": \"供應者\",\n\t\"admin.pages.MarketPlacePage.provider.githubStars\": \"此供應者在 GitHub 上獲得了 {starsCount} 顆星星\",\n\t\"admin.pages.MarketPlacePage.provider.downloads\": \"此供應者的每周下載量為 {downloadsCount}\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"清除搜尋\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \"沒有 \\\"{target}\\\" 的搜尋結果\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"搜尋\",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"申請您的擴充功能\",\n\t\"admin.pages.MarketPlacePage.submit.provider.link\": \"申請供應者\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \"充分利用 Strapi\",\n\t\"admin.pages.MarketPlacePage.tab-group.label\": \"Strapi 的外掛程式和供應者\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"找不到外掛程式？\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"告訴我們您在尋找什麼外掛程式，我們會轉告我們的社群外掛程式開發者，這將成為他們的靈感來源！\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical\": \"按字母排序\",\n\t\"admin.pages.MarketPlacePage.sort.newest\": \"最新\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"按字母排序\",\n\t\"admin.pages.MarketPlacePage.sort.newest.selected\": \"依最新時間排序\",\n\t\"admin.pages.MarketPlacePage.filters.collections\": \"集合\",\n\t\"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"{count, plural, =0 {No collections} one {# collection} other {# collections}} selected\",\n\t\"admin.pages.MarketPlacePage.filters.categories\": \"類別\",\n\t\"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count, plural, =0 {No categories} one {# category} other {# categories}} selected\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"複製到剪貼簿\",\n\t\"app.component.search.label\": \"搜尋 {target}\",\n\t\"app.component.table.duplicate\": \"建立副本 {target}\",\n\t\"app.component.table.edit\": \"編輯 {target}\",\n\t\"app.component.table.select.one-entry\": \"選擇 {target}\",\n\t\"app.components.BlockLink.blog\": \"部落格\",\n\t\"app.components.BlockLink.blog.content\": \"閱讀關於 Strapi 和相關的最新資訊\",\n\t\"app.components.BlockLink.code\": \"範例\",\n\t\"app.components.BlockLink.code.content\": \"透過測試社群開發的真實專案而學習。\",\n\t\"app.components.BlockLink.documentation.content\": \"探索基礎觀念、指南和分解步驟說明。\",\n\t\"app.components.BlockLink.tutorial\": \"引導\",\n\t\"app.components.BlockLink.tutorial.content\": \"跟著手把手的教學引導使用和客製 Strapi\",\n\t\"app.components.Button.cancel\": \"取消\",\n\t\"app.components.Button.confirm\": \"確認\",\n\t\"app.components.Button.reset\": \"重設\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"即將推出\",\n\t\"app.components.ConfirmDialog.title\": \"確認\",\n\t\"app.components.DownloadInfo.download\": \"下載中...\",\n\t\"app.components.DownloadInfo.text\": \"可能需要一分鐘。感謝您的耐心。\",\n\t\"app.components.EmptyAttributes.title\": \"這裡還沒有任何欄位\",\n\t\"app.components.EmptyStateLayout.content-document\": \"找不到資料\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"您沒有權限存取這個內容\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>在內容管理員中建立和管理所有內容。</p><p>範例：再以部落格作為例子，使用者可以撰寫、儲存、和發布文章。</p><p>💡 小提示 - 別忘了發佈您剛建立的內容。</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"⚡️ 建立內容\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>太棒了，只差最後一步！</p><b>🚀  See content in action</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"測試 API\",\n\t\"app.components.GuidedTour.CM.success.title\": \"第 2 步：完成 ✅\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>集合型別能夠幫助您管理多個項目，單一型別只適合管理一個項目。</p> <p>範例：對於部落格來說，文章會是集合型別，而首頁會是單一型別。</p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"建立集合型別\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 建立首個集合型別\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>做得好！</p><b>⚡️ 您想要和世界分享些什麼？</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"第 1 步：完成 ✅\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>在此產生驗證權杖，取得您剛建立的內容。</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"產生 API 權杖\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 See content in action\",\n\t\"app.components.GuidedTour.apiTokens.success.content\": \"<p>See content in action by making an HTTP request:</p><ul><li><p>向此網址傳送 HTTP 請求：<light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>並附帶此標頭：<light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>For more ways to interact with content, see the <documentationLink>documentation</documentationLink>.</p>\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"返回首頁\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"第 3 步：完成 ✅\",\n\t\"app.components.GuidedTour.create-content\": \"建立內容\",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️ 您想要和世界分享些什麼？\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"前往內容建立員\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 建立內容結構\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"測試 API\",\n\t\"app.components.GuidedTour.skip\": \"跳過導覽\",\n\t\"app.components.GuidedTour.title\": \"3 steps to get started\",\n\t\"app.components.HomePage.button.blog\": \"到部落格上閱讀更多\",\n\t\"app.components.HomePage.community\": \"探索開發社群\",\n\t\"app.components.HomePage.community.content\": \"在不同的社群中與其他成員、貢獻者以及開發者討論。\",\n\t\"app.components.HomePage.create\": \"建立您第一個內容型別\",\n\t\"app.components.HomePage.roadmap\": \"看我們的 roadmap\",\n\t\"app.components.HomePage.welcome\": \"歡迎加入！\",\n\t\"app.components.HomePage.welcome.again\": \"歡迎回來! \",\n\t\"app.components.HomePage.welcomeBlock.content\": \"恭喜！您登入成為第一個管理員，探索 Strapi 的強大功能，建議您從建立第一個內容型別開始。\",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"我們希望您在專案上有所進展！請隨時閱讀有關 Strapi 的最新消息。我們將根據您的回饋盡最大努力改進產品。\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"問題。\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \"或是回報\",\n\t\"app.components.ImgPreview.hint\": \"將您要上傳的檔案拖曳到此區域，或是瀏覽檔案\",\n\t\"app.components.ImgPreview.hint.browse\": \"瀏覽\",\n\t\"app.components.InputFile.newFile\": \"增加新檔案\",\n\t\"app.components.InputFileDetails.open\": \"在新分頁中開啟\",\n\t\"app.components.InputFileDetails.originalName\": \"原始名稱：\",\n\t\"app.components.InputFileDetails.remove\": \"移除檔案\",\n\t\"app.components.InputFileDetails.size\": \"大小：\",\n\t\"app.components.InstallPluginPage.Download.description\": \"下載和安裝擴充功能可能需要幾秒鐘的時間。\",\n\t\"app.components.InstallPluginPage.Download.title\": \"下載中...\",\n\t\"app.components.InstallPluginPage.description\": \"輕鬆擴充您的應用程式\",\n\t\"app.components.LeftMenu.collapse\": \"收起導航列\",\n\t\"app.components.LeftMenu.expand\": \"打開導航列\",\n\t\"app.components.LeftMenu.general\": \"一般\",\n\t\"app.components.LeftMenu.logout\": \"登出\",\n\t\"app.components.LeftMenu.logo.alt\": \"應用程式標誌\",\n\t\"app.components.LeftMenu.plugins\": \"外掛程式\",\n\t\"app.components.LeftMenu.navbrand.title\": \"Strapi 控制台\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"工作區\",\n\t\"app.components.LeftMenuFooter.help\": \"說明\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Powered by \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"集合型別\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"設定\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"一般\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"目前沒有安裝任何擴充功能\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"擴充功能\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"單一型別\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"解除安裝擴充功能可能需要幾秒鐘的時間。\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"解除安裝\",\n\t\"app.components.ListPluginsPage.description\": \"這個專案安裝的擴充功能列表\",\n\t\"app.components.ListPluginsPage.head.title\": \"擴充功能列表\",\n\t\"app.components.Logout.logout\": \"登出\",\n\t\"app.components.Logout.profile\": \"個人檔案\",\n\t\"app.components.MarketplaceBanner\": \"在 Strapi Awesome 上發現社群建構的擴充套件，以及更多可驅動您的專案的精彩內容。\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"strapi 火箭 logo\",\n\t\"app.components.MarketplaceBanner.link\": \"現在看看\",\n\t\"app.components.NotFoundPage.back\": \"回到主頁\",\n\t\"app.components.NotFoundPage.description\": \"找不到此頁面\",\n\t\"app.components.Official\": \"官方\",\n\t\"app.components.Onboarding.help.button\": \"幫助按鈕\",\n\t\"app.components.Onboarding.label.completed\": \"% 完成\",\n\t\"app.components.Onboarding.title\": \"入門影片\",\n\t\"app.components.PluginCard.Button.label.download\": \"下載\",\n\t\"app.components.PluginCard.Button.label.install\": \"已安裝\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"要啟用自動重新整理功能。請使用 yarn develop 啟動您的 App。\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"我了解！\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"基於安全考量，擴充套件只能在開發環境中下載。\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"不能下載\",\n\t\"app.components.PluginCard.compatible\": \"與您的專案相容\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"相容社群\",\n\t\"app.components.PluginCard.more-details\": \"顯示更多\",\n\t\"app.components.ToggleCheckbox.off-label\": \"關\",\n\t\"app.components.ToggleCheckbox.on-label\": \"開\",\n\t\"app.components.Users.MagicLink.connect\": \"將此連結傳送給使用者讓他可以開通帳號。\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"將此連結傳送給使用者讓他可以透過 SSO 登入。\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"使用者詳細資料\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"使用者角色\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"使用者可以有一個或多個角色\",\n\t\"app.components.Users.SortPicker.button-label\": \"排序按照\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"電子郵件地址 (A 到 Z)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"電子郵件地址 (Z 到 A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"名字 (A 到 Z)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"名字 (Z 到 A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"姓氏 (A 到 Z)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"姓氏 (Z 到 A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"使用者名稱 (A 到 Z)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"使用者名稱 (Z 到 A)\",\n\t\"app.components.listPlugins.button\": \"安裝新的擴充功能\",\n\t\"app.components.listPlugins.title.none\": \"目前沒有安裝任何擴充功能\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"解除安裝擴充功能時發生錯誤\",\n\t\"app.containers.App.notification.error.init\": \"向 API 請求時發生錯誤\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"如果您沒有收到這個連結，請詢問您的管理員。\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"可能需要幾分鐘才能收到您的恢復密碼連結。\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"送出電子郵件\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"有效\",\n\t\"app.containers.Users.EditPage.header.label\": \"編輯 {name}\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"編輯使用者\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"歸屬的角色\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"新增使用者\",\n\t\"app.links.configure-view\": \"設定檢視\",\n\t\"app.page.not.found\": \"哎呀! 似乎找不到您要瀏覽的網頁...\",\n\t\"app.static.links.cheatsheet\": \"備忘錄\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"新增篩選器\",\n\t\"app.utils.close-label\": \"關閉\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.duplicate\": \"建立副本\",\n\t\"app.utils.edit\": \"編輯\",\n\t\"app.utils.delete\": \"刪除\",\n\t\"app.utils.errors.file-too-big.message\": \"檔案容量太大了\",\n\t\"app.utils.filter-value\": \"篩選器的值\",\n\t\"app.utils.filters\": \"篩選條件\",\n\t\"app.utils.notify.data-loaded\": \"{target} 已經讀取\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"發布\",\n\t\"app.utils.select-all\": \"全選\",\n\t\"app.utils.select-field\": \"選擇欄位\",\n\t\"app.utils.select-filter\": \"選擇篩選\",\n\t\"app.utils.unpublish\": \"取消發布\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"此內容目前正在建設中，將在幾週後恢復！\",\n\t\"component.Input.error.validation.integer\": \"這個值必須是數值\",\n\t\"components.AutoReloadBlocker.description\": \"使用以下指令之一執行 Strapi：\",\n\t\"components.AutoReloadBlocker.header\": \"這個擴充功能需要自動重新整理功能才能載入\",\n\t\"components.ErrorBoundary.title\": \"有錯誤發生...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"包含\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"包含 (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"結尾是\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"結尾是 (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"是\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"是 (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"大於\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"大於或等於\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"小於\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"小於或等於\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"不是\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"不是 (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"不包含\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"不包含 (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"不是 null\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"是 null\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"起首是\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"起首是 (case insensitive)\",\n\t\"components.Input.error.attribute.key.taken\": \"這個值已經存在了\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"不能等於\",\n\t\"components.Input.error.attribute.taken\": \"這個欄位名稱已經存在了\",\n\t\"components.Input.error.contain.lowercase\": \"密碼必須至少包含一個小寫字母\",\n\t\"components.Input.error.contain.number\": \"密碼必須至少包含一個數字\",\n\t\"components.Input.error.contain.uppercase\": \"密碼必須至少包含一個大寫字母\",\n\t\"components.Input.error.contentTypeName.taken\": \"這個名稱已經存在了\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"密碼不符\",\n\t\"components.Input.error.validation.email\": \"請輸入有效的電子郵件地址\",\n\t\"components.Input.error.validation.json\": \"非法的 JSON 格式\",\n\t\"components.Input.error.validation.lowercase\": \"必須是小寫字母\",\n\t\"components.Input.error.validation.max\": \"數值太大 {max}\",\n\t\"components.Input.error.validation.maxLength\": \"超過長度 {max}\",\n\t\"components.Input.error.validation.min\": \"數值太小 {min}\",\n\t\"components.Input.error.validation.minLength\": \"長度不夠 {min}\",\n\t\"components.Input.error.validation.minSupMax\": \"不能接受這個值\",\n\t\"components.Input.error.validation.regex\": \"此值無法通過正規表達式\",\n\t\"components.Input.error.validation.required\": \"必填欄位\",\n\t\"components.Input.error.validation.unique\": \"這個值已經有了\",\n\t\"components.InputSelect.option.placeholder\": \"這裡選擇\",\n\t\"components.ListRow.empty\": \"沒有資料可以顯示\",\n\t\"components.NotAllowedInput.text\": \"沒有權限顯示這個欄位\",\n\t\"components.OverlayBlocker.description\": \"您正在使用的功能需要重新啟動，請等待重新啟動完成。\",\n\t\"components.OverlayBlocker.description.serverError\": \"伺服器應該已經重新啟動，請在終端機中查看 日誌。\",\n\t\"components.OverlayBlocker.title\": \"等待重新啟動中...\",\n\t\"components.OverlayBlocker.title.serverError\": \"重新啟動花費的時間比預期的久。\",\n\t\"components.PageFooter.select\": \"項目/頁\",\n\t\"components.ProductionBlocker.description\": \"基於安全考量，我們需要在其他環境關閉這個擴充功能\",\n\t\"components.ProductionBlocker.header\": \"這個擴充功能只能在開發環境中使用\",\n\t\"components.Search.placeholder\": \"搜尋...\",\n\t\"components.TableHeader.sort\": \"以 {label} 排序\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown 模式\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"預覽模式\",\n\t\"components.Wysiwyg.collapse\": \"折疊\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"標題1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"標題2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"標題3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"標題4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"標題5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"標題6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"新增標題\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"字元\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"展開\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"拖曳檔案、從剪貼簿貼上或 {browse}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"選擇檔案\",\n\t\"components.pagination.go-to\": \"前往 {page} 頁面\",\n\t\"components.pagination.go-to-next\": \"前往下一頁\",\n\t\"components.pagination.go-to-previous\": \"前往上一頁\",\n\t\"components.pagination.remaining-links\": \"及 {number} 個其他連結\",\n\t\"components.popUpWarning.button.cancel\": \"不, 取消\",\n\t\"components.popUpWarning.button.confirm\": \"是, 確認\",\n\t\"components.popUpWarning.message\": \"您確定要刪除此項目嗎？\",\n\t\"components.popUpWarning.title\": \"請確認\",\n\t\"form.button.continue\": \"繼續\",\n\t\"form.button.done\": \"完成\",\n\t\"global.search\": \"搜尋\",\n\t\"global.actions\": \"操作\",\n\t\"global.back\": \"返回\",\n\t\"global.cancel\": \"取消\",\n\t\"global.change-password\": \"更改密碼\",\n\t\"global.content-manager\": \"內容管理者\",\n\t\"global.continue\": \"繼續\",\n\t\"global.delete\": \"刪除\",\n\t\"global.delete-target\": \"刪除 {target}\",\n\t\"global.description\": \"說明\",\n\t\"global.details\": \"詳細資訊\",\n\t\"global.disabled\": \"已停用\",\n\t\"global.documentation\": \"說明文件\",\n\t\"global.enabled\": \"已啟用\",\n\t\"global.finish\": \"完成\",\n\t\"global.marketplace\": \"市集\",\n\t\"global.name\": \"名稱\",\n\t\"global.move\": \"移動\",\n\t\"global.none\": \"無\",\n\t\"global.password\": \"密碼\",\n\t\"global.plugins\": \"外掛程式\",\n\t\"global.plugins.content-manager\": \"內容管理員\",\n\t\"global.plugins.content-manager.description\": \"快速查看、編輯、並刪除資料庫中的資料。\",\n\t\"global.plugins.content-type-builder\": \"內容型別建立員\",\n\t\"global.plugins.content-type-builder.description\": \"為您的 API 的資料結構建立模型。在幾分鐘內建立新的欄位和關聯。檔案將自動在您的專案中自動建立並更新。\",\n\t\"global.plugins.email\": \"電子郵件\",\n\t\"global.plugins.email.description\": \"設定您的應用程式以傳送電子郵件。\",\n\t\"global.plugins.upload\": \"媒體庫\",\n\t\"global.plugins.upload.description\": \"媒體檔案管理。\",\n\t\"global.plugins.graphql\": \"GraphQL\",\n\t\"global.plugins.graphql.description\": \"新增使用預設 API 方法的 GraphQL 端點。\",\n\t\"global.plugins.documentation\": \"說明文件\",\n\t\"global.plugins.documentation.description\": \"建立 OpenAPI 文件，並透過 SWAGGER UI 來可視化您的 API。\",\n\t\"global.plugins.i18n\": \"國際化\",\n\t\"global.plugins.i18n.description\": \"此外掛程式允許您從管理面板和 API 建立、讀取、更新不同語言的內容。\",\n\t\"global.plugins.sentry\": \"Sentry\",\n\t\"global.plugins.sentry.description\": \"將 Strapi 錯誤事件傳送至 Sentry。\",\n\t\"global.plugins.users-permissions\": \"角色與權限\",\n\t\"global.plugins.users-permissions.description\": \"使用基於 JWT 的完整驗證流程來保護您的 API。此外掛程式還附帶 ACT 策略，讓您能夠管理使用者群組間的權限。\",\n\t\"global.profile\": \"個人檔案\",\n\t\"global.prompt.unsaved\": \"您確定要離開網頁嗎？您的所有未存檔的編輯都會消失\",\n\t\"global.reset-password\": \"重設密碼\",\n\t\"global.roles\": \"角色\",\n\t\"global.save\": \"儲存\",\n\t\"global.see-more\": \"查看更多\",\n\t\"global.select\": \"選取\",\n\t\"global.select-all-entries\": \"選取所有實體\",\n\t\"global.settings\": \"設定\",\n\t\"global.type\": \"類型\",\n\t\"global.users\": \"使用者\",\n\t\"notification.contentType.relations.conflict\": \"內容型別有關聯衝突\",\n\t\"notification.default.title\": \"訊息：\",\n\t\"notification.error\": \"發生錯誤\",\n\t\"notification.error.layout\": \"無法取得佈局\",\n\t\"notification.form.error.fields\": \"表單包含一些錯誤\",\n\t\"notification.form.success.fields\": \"修改已儲存\",\n\t\"notification.link-copied\": \"連結已複製到剪貼簿\",\n\t\"notification.permission.not-allowed-read\": \"您沒有查看此檔案的權限\",\n\t\"notification.success.delete\": \"該項已被刪除\",\n\t\"notification.success.saved\": \"已儲存\",\n\t\"notification.success.title\": \"成功:\",\n\t\"notification.success.apitokencreated\": \"成功建立 API 權杖\",\n\t\"notification.success.apitokenedited\": \"成功編輯 API 權杖\",\n\t\"notification.error.tokennamenotunique\": \"名稱已經指派給其他權杖\",\n\t\"notification.version.update.message\": \"有新版本的 Strapi 可用!\",\n\t\"notification.warning.title\": \"警告:\",\n\t\"notification.warning.404\": \"404 - 找不到\",\n\tor: or,\n\t\"request.error.model.unknown\": \"不存在的資料\",\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, zh as default, light, or, skipToContent, submit };\n//# sourceMappingURL=zh-LqPg8hbL.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,kDAAkD;AAAA,EAClD,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,gDAAgD;AAAA,EAChD,yCAAyC;AAAA,EACzC,oDAAoD;AAAA,EACpD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,2CAA2C;AAAA,EAC3C,0DAA0D;AAAA,EAC1D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACD;", "names": []}