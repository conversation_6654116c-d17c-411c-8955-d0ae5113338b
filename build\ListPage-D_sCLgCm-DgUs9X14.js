const __vite__fileDeps=["MagicLinkEE-Dv-aox3R-DVXhGqr3.js","strapi-YzJfjJ2z.js","strapi-COJtagOC.css","SelectRoles-C1P9kRCc-DM4OhDyY.js","useAdminRoles-Bd2N7J7A-CIPQp8aL.js","CreateActionEE-1i1Hn9yC-CMTHaYjc.js","isNil-D4cgUxqE.js","ListPage-feNYkeWT-CzACEPrH.js","useLicenseLimitNotification-J4qdwz19-Ce8ibyli.js","users-8N93LH7R-MOwOr-tf.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{bj as M,bk as E,c as J,r as g,ax as K,e as re,al as ie,a as W,au as H,a5 as ne,az as le,bl as oe,m as e,A as T,bm as de,s as A,w as v,bn as ce,bo as ue,n as P,L as q,b1 as me,aF as I,aG as n,I as $,F as ge,_ as pe,b3 as he,aH as B,bp as xe,M as b,B as be,C as fe,O as je,J as R,G as f,Q as G,i as Me,aN as Ee,k as j,l as N,aO as Ae,b5 as Se,b6 as Ce}from"./strapi-YzJfjJ2z.js";import{g as Q}from"./users-8N93LH7R-MOwOr-tf.js";import{M as _e,S as ye}from"./SelectRoles-C1P9kRCc-DM4OhDyY.js";import"./useAdminRoles-Bd2N7J7A-CIPQp8aL.js";const Le=g.forwardRef((a,l)=>{const{formatMessage:p}=H();return e.jsx(T,{ref:l,startIcon:e.jsx(de,{}),size:"S",...a,children:p({id:"Settings.permissions.users.create",defaultMessage:"Invite new user"})})}),Ie=({onToggle:a})=>{const[l,p]=g.useState("create"),[k,F]=g.useState(""),{formatMessage:t}=H(),{toggleNotification:m}=W(),{_unstableFormatAPIError:w,_unstableFormatValidationErrors:S}=J(),c=M(Re,async()=>(await E(()=>import("./ModalForm-B9SUkQ1l-CamjQBT3.js"),[])).ROLE_LAYOUT,{combine(o,d){return[...o,...d]},defaultValue:[]}),D=M(Y,async()=>(await E(()=>import("./ModalForm-B9SUkQ1l-CamjQBT3.js"),[])).FORM_INITIAL_VALUES,{combine(o,d){return{...o,...d}},defaultValue:Y}),C=M(_e,async()=>(await E(()=>import("./MagicLinkEE-Dv-aox3R-DVXhGqr3.js"),__vite__mapDeps([0,1,2,3,4]))).MagicLinkEE),[h]=xe(),_=t({id:"Settings.permissions.users.create",defaultMessage:"Invite new user"}),O=async(o,{setErrors:d})=>{const r=await h({...o,roles:o.roles??[]});"data"in r?(r.data.registrationToken&&F(r.data.registrationToken),U()):(m({type:"danger",message:w(r.error)}),Ae(r.error)&&r.error.name==="ValidationError"&&d(S(r.error)))},U=()=>{L?p(L):a()},{buttonSubmitLabel:y,isDisabled:x,next:L}=ve[l];return C?e.jsx(b.Root,{defaultOpen:!0,onOpenChange:a,children:e.jsxs(b.Content,{children:[e.jsx(b.Header,{children:e.jsx(be,{label:_,children:e.jsx(fe,{isCurrent:!0,children:_})})}),e.jsx(je,{method:l==="create"?"POST":"PUT",initialValues:D??{},onSubmit:O,validationSchema:Pe,children:({isSubmitting:o})=>e.jsxs(e.Fragment,{children:[e.jsx(b.Body,{children:e.jsxs(v,{direction:"column",alignItems:"stretch",gap:6,children:[l!=="create"&&e.jsx(C,{registrationToken:k}),e.jsxs(R,{children:[e.jsx(A,{variant:"beta",tag:"h2",children:t({id:"app.components.Users.ModalCreateBody.block-title.details",defaultMessage:"User details"})}),e.jsx(R,{paddingTop:4,children:e.jsx(v,{direction:"column",alignItems:"stretch",gap:1,children:e.jsx(f.Root,{gap:5,children:Te.map(d=>d.map(({size:r,...i})=>e.jsx(f.Item,{col:r,direction:"column",alignItems:"stretch",children:e.jsx(G,{...i,disabled:x,label:t(i.label),placeholder:t(i.placeholder)})},i.name)))})})})]}),e.jsxs(R,{children:[e.jsx(A,{variant:"beta",tag:"h2",children:t({id:"global.roles",defaultMessage:"User's role"})}),e.jsx(R,{paddingTop:4,children:e.jsxs(f.Root,{gap:5,children:[e.jsx(f.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(ye,{disabled:x})}),c.map(d=>d.map(({size:r,...i})=>e.jsx(f.Item,{col:r,direction:"column",alignItems:"stretch",children:e.jsx(G,{...i,disabled:x,label:t(i.label),placeholder:i.placeholder?t(i.placeholder):void 0,hint:i.hint?t(i.hint):void 0})},i.name)))]})})]})]})}),e.jsxs(b.Footer,{children:[e.jsx(T,{variant:"tertiary",onClick:a,type:"button",children:t({id:"app.components.Button.cancel",defaultMessage:"Cancel"})}),l==="create"?e.jsx(T,{type:"submit",loading:o,children:t(y)}):e.jsx(T,{type:"button",loading:o,onClick:a,children:t(y)})]})]})})]})}):null},Y={firstname:"",lastname:"",email:"",roles:[]},Re=[],Te=[[{label:{id:"Auth.form.firstname.label",defaultMessage:"First name"},name:"firstname",placeholder:{id:"Auth.form.firstname.placeholder",defaultMessage:"e.g. Kai"},type:"string",size:6,required:!0},{label:{id:"Auth.form.lastname.label",defaultMessage:"Last name"},name:"lastname",placeholder:{id:"Auth.form.lastname.placeholder",defaultMessage:"e.g. Doe"},type:"string",size:6}],[{label:{id:"Auth.form.email.label",defaultMessage:"Email"},name:"email",placeholder:{id:"Auth.form.email.placeholder",defaultMessage:"e.g. <EMAIL>"},type:"email",size:6,required:!0}]],Pe=Me().shape({firstname:N().trim().required({id:j.required.id,defaultMessage:"This field is required"}).nullable(),lastname:N(),email:N().email(j.email).required({id:j.required.id,defaultMessage:"This field is required"}).nullable(),roles:Ee().min(1,{id:j.required.id,defaultMessage:"This field is required"}).required({id:j.required.id,defaultMessage:"This field is required"})}),ve={create:{buttonSubmitLabel:{id:"app.containers.Users.ModalForm.footer.button-success",defaultMessage:"Invite user"},isDisabled:!1,next:"magic-link"},"magic-link":{buttonSubmitLabel:{id:"global.finish",defaultMessage:"Finish"},isDisabled:!0,next:null}},ke=()=>{const{_unstableFormatAPIError:a}=J(),[l,p]=g.useState(!1),k=K(s=>s.admin_app.permissions),{allowedActions:{canCreate:F,canDelete:t,canRead:m}}=re(k.settings?.users),w=ie(),{toggleNotification:S}=W(),{formatMessage:c}=H(),{search:D}=ne(),[C,h]=g.useState(!1),[_,O]=g.useState([]),{data:U,isError:y,isLoading:x}=le(oe.parse(D,{ignoreQueryPrefix:!0})),{pagination:L,users:o=[]}=U??{},d=M(Le,async()=>(await E(()=>import("./CreateActionEE-1i1Hn9yC-CMTHaYjc.js"),__vite__mapDeps([5,1,2,6]))).CreateActionEE),r=Fe.map(s=>({...s,label:c(s.label)})),i=c({id:"global.users",defaultMessage:"Users"}),z=()=>{p(s=>!s)},[X]=ue(),Z=async s=>{try{const u=await X({ids:s});"error"in u&&S({type:"danger",message:a(u.error)})}catch{S({type:"danger",message:c({id:"global.error",defaultMessage:"An error occurred"})})}},ee=s=>()=>{m&&w(s.toString())},se=s=>async()=>{O([s]),h(!0)},ae=async()=>{await Z(_),h(!1)};return d?y?e.jsx(P.Error,{}):e.jsxs(P.Main,{"aria-busy":x,children:[e.jsx(P.Title,{children:c({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Users"})}),e.jsx(q.Header,{primaryAction:F&&e.jsx(d,{onClick:z}),title:i,subtitle:c({id:"Settings.permissions.users.listview.header.subtitle",defaultMessage:"All the users who have access to the Strapi admin panel"})}),e.jsx(q.Action,{startActions:e.jsxs(e.Fragment,{children:[e.jsx(me,{label:c({id:"app.component.search.label",defaultMessage:"Search for {target}"},{target:i})}),e.jsxs(I.Root,{options:we,children:[e.jsx(I.Trigger,{}),e.jsx(I.Popover,{}),e.jsx(I.List,{})]})]})}),e.jsxs(q.Content,{children:[e.jsxs(n.Root,{rows:o,headers:r,children:[e.jsx(n.ActionBar,{}),e.jsxs(n.Content,{children:[e.jsxs(n.Head,{children:[t?e.jsx(n.HeaderCheckboxCell,{}):null,r.map(s=>e.jsx(n.HeaderCell,{...s},s.name))]}),e.jsx(n.Empty,{}),e.jsx(n.Loading,{}),e.jsx(n.Body,{children:o.map(s=>e.jsxs(n.Row,{onClick:ee(s.id),cursor:m?"pointer":"default",children:[t?e.jsx(n.CheckboxCell,{id:s.id}):null,r.map(({cellFormatter:u,name:V,...te})=>e.jsx(n.Cell,{children:typeof u=="function"?u(s,{name:V,...te}):e.jsx(A,{textColor:"neutral800",children:s[V]||"-"})},V)),m||t?e.jsx(n.Cell,{onClick:u=>u.stopPropagation(),children:e.jsxs(v,{justifyContent:"end",children:[m?e.jsx($,{tag:pe,to:s.id.toString(),label:c({id:"app.component.table.edit",defaultMessage:"Edit {target}"},{target:Q(s)}),variant:"ghost",children:e.jsx(ge,{})}):null,t?e.jsx($,{onClick:se(s.id),label:c({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:Q(s)}),variant:"ghost",children:e.jsx(he,{})}):null]})}):null]},s.id))})]})]}),e.jsxs(B.Root,{...L,children:[e.jsx(B.PageSize,{}),e.jsx(B.Links,{})]})]}),l&&e.jsx(Ie,{onToggle:z}),e.jsx(Se.Root,{open:C,onOpenChange:h,children:e.jsx(Ce,{onConfirm:ae})})]}):null},Fe=[{name:"firstname",label:{id:"Settings.permissions.users.firstname",defaultMessage:"Firstname"},sortable:!0},{name:"lastname",label:{id:"Settings.permissions.users.lastname",defaultMessage:"Lastname"},sortable:!0},{name:"email",label:{id:"Settings.permissions.users.email",defaultMessage:"Email"},sortable:!0},{name:"roles",label:{id:"Settings.permissions.users.roles",defaultMessage:"Roles"},sortable:!1,cellFormatter({roles:a}){return e.jsx(A,{textColor:"neutral800",children:a.map(l=>l.name).join(`,
`)})}},{name:"username",label:{id:"Settings.permissions.users.username",defaultMessage:"Username"},sortable:!0},{name:"isActive",label:{id:"Settings.permissions.users.user-status",defaultMessage:"User status"},sortable:!1,cellFormatter({isActive:a}){return e.jsx(v,{children:e.jsx(ce,{size:"S",borderWidth:0,background:"transparent",color:"neutral800",variant:a?"success":"danger",children:e.jsx(A,{children:a?"Active":"Inactive"})})})}}],we=[{name:"firstname",label:"Firstname",type:"string"},{name:"lastname",label:"Lastname",type:"string"},{name:"email",label:"Email",type:"email"},{name:"username",label:"Username",type:"string"},{name:"isActive",label:"Active user",type:"boolean"}],De=()=>{const a=M(ke,async()=>(await E(()=>import("./ListPage-feNYkeWT-CzACEPrH.js"),__vite__mapDeps([7,1,2,8,6,9,3,4]))).UserListPageEE);return a?e.jsx(a,{}):null},Be=()=>{const a=K(l=>l.admin_app.permissions.settings?.users.read);return e.jsx(P.Protect,{permissions:a,children:e.jsx(De,{})})};export{De as ListPage,ke as ListPageCE,Be as ProtectedListPage};
