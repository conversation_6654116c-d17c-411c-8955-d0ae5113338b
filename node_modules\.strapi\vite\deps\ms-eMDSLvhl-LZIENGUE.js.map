{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/ms-eMDSLvhl.mjs"], "sourcesContent": ["const Analytics = \"Analisis\";\nconst Documentation = \"Dokumen\";\nconst Email = \"Email\";\nconst Password = \"Kata Laluan\";\nconst Provider = \"Penyedia\";\nconst ResetPasswordToken = \"Token penetapan semula kata laluan\";\nconst Role = \"Peranan\";\nconst Username = \"Nama Pengg<PERSON>a\";\nconst Users = \"Para Pengguna\";\nconst ms = {\n\tAnalytics: Analytics,\n\t\"Auth.form.button.forgot-password\": \"Hantar e-mel\",\n\t\"Auth.form.button.login\": \"Log masuk\",\n\t\"Auth.form.button.register\": \"Sedia untuk mulakan\",\n\t\"Auth.form.error.blocked\": \"A<PERSON>un anda telah disekat oleh pengelola.\",\n\t\"Auth.form.error.code.provide\": \"Kod yang salah terlah diberikan.\",\n\t\"Auth.form.error.confirmed\": \"E-mel akaun anda tidak disahkan lagi.\",\n\t\"Auth.form.error.email.invalid\": \"E-mel ini tidak sah.\",\n\t\"Auth.form.error.email.provide\": \"Sila berikan nama pengguna atau e-mel anda.\",\n\t\"Auth.form.error.email.taken\": \"E-mel sudah diambil.\",\n\t\"Auth.form.error.invalid\": \"Username/Email atau kata laluan tidak tepat.\",\n\t\"Auth.form.error.params.provide\": \"Parameter yang tidak tepat telah diberikan\",\n\t\"Auth.form.error.password.format\": \"Kata laluan anda tidak boleh mengandungi simbol `$` lebih dari tiga kali.\",\n\t\"Auth.form.error.password.local\": \"Pengguna ini tidak pernah menetapkan kata laluan local, sila log masuk melalui provider yang digunakan sewaktu pembuatan akaun.\",\n\t\"Auth.form.error.password.matching\": \"Kata laluan tidak sepadan.\",\n\t\"Auth.form.error.password.provide\": \"Sila isikan kata laluan anda.\",\n\t\"Auth.form.error.ratelimit\": \"Terlalu banyak percubaan, sila cuba sebentar lagi.\",\n\t\"Auth.form.error.user.not-exist\": \"E-mel ini tidak wujud.\",\n\t\"Auth.form.error.username.taken\": \"Nama pengguna sudah diambil.\",\n\t\"Auth.form.forgot-password.email.label\": \"Masukkan emel anda\",\n\t\"Auth.form.forgot-password.email.label.success\": \"E-mel berjaya dihantar ke\",\n\t\"Auth.form.register.news.label\": \"Ikuti perkembangan terkini mengenai ciri baru dan penambahbaikan yang akan datang (dengan melakukan ini, anda menerima {terms} dan {policy}).\",\n\t\"Auth.link.forgot-password\": \"Lupa kata laluan anda ?\",\n\t\"Auth.link.ready\": \"Sedia untuk log masuk?\",\n\t\"Auth.privacy-policy-agreement.policy\": \"dasar privasi\",\n\t\"Auth.privacy-policy-agreement.terms\": \"syarat\",\n\t\"Content Manager\": \"Pengurus Kandungan\",\n\t\"Content Type Builder\": \"Pembina Jenis Kandungan\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Fail Muat Naik\",\n\t\"HomePage.head.title\": \"Halaman Utama\",\n\t\"HomePage.roadmap\": \"Lihat jadual kerja kami\",\n\t\"HomePage.welcome.congrats\": \"Tahniah!\",\n\t\"HomePage.welcome.congrats.content\": \"Anda telah masuk sebagai pengelola yang pertama. Untuk meneroka ciri yang bagus disediakan oleh Strapi,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"kami mengesyorkan anda untuk buat Jenis Koleksi yang pertama.\",\n\t\"New entry\": \"Entri baru\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"Peranan & Keizinan\",\n\t\"Settings.error\": \"Ralat\",\n\t\"Settings.global\": \"Tetapan global\",\n\t\"Settings.webhooks.create\": \"Buat satu webhook\",\n\t\"Settings.webhooks.create.header\": \"Buat header baru\",\n\t\"Settings.webhooks.created\": \"Webhook telah dibuat\",\n\t\"Settings.webhooks.events.create\": \"Cipta\",\n\t\"Settings.webhooks.form.events\": \"Sewaktu\",\n\t\"Settings.webhooks.form.headers\": \"Tajuk\",\n\t\"Settings.webhooks.form.url\": \"Url\",\n\t\"Settings.webhooks.key\": \"Kata Kunci\",\n\t\"Settings.webhooks.list.button.add\": \"Tambah webhook baru\",\n\t\"Settings.webhooks.list.description\": \"Dapatkan pemberitahuan perubahan untuk POST.\",\n\t\"Settings.webhooks.list.empty.description\": \"Tambah satu dalam senarai.\",\n\t\"Settings.webhooks.list.empty.link\": \"Lihat dokumen kami\",\n\t\"Settings.webhooks.list.empty.title\": \"Belum ada webhook\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhooks\",\n\t\"Settings.webhooks.trigger\": \"Cetus\",\n\t\"Settings.webhooks.trigger.cancel\": \"Batalkan pencetusan\",\n\t\"Settings.webhooks.trigger.pending\": \"Belum selesai…\",\n\t\"Settings.webhooks.trigger.save\": \"Sila simpan untuk cetuskan\",\n\t\"Settings.webhooks.trigger.success\": \"Berjaya!\",\n\t\"Settings.webhooks.trigger.success.label\": \"Cetusan berjaya\",\n\t\"Settings.webhooks.trigger.test\": \"Uji Cetusan\",\n\t\"Settings.webhooks.trigger.title\": \"Simpan sebelum cetus\",\n\t\"Settings.webhooks.value\": \"Kandungan\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"Para Pengguna & Keizinan\",\n\t\"app.components.BlockLink.code\": \"Contoh Kod\",\n\t\"app.components.Button.cancel\": \"Batal\",\n\t\"app.components.Button.reset\": \"Set Semula\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"Akan Datang\",\n\t\"app.components.DownloadInfo.download\": \"Muat turun sedang dijalankan...\",\n\t\"app.components.DownloadInfo.text\": \"Ini akan mengambil masa, terima kasih atas kesabaran anda.\",\n\t\"app.components.EmptyAttributes.title\": \"Tiada ruang disini\",\n\t\"app.components.HomePage.button.blog\": \"LIHAT LEBIH LAGI DI BLOG\",\n\t\"app.components.HomePage.community\": \"Cari komuniti di web\",\n\t\"app.components.HomePage.community.content\": \"Bincang dengan ahli kumpulan, penyumbang dan pembangun di saluran berbeza.\",\n\t\"app.components.HomePage.create\": \"Cipta Jenis Kandungan anda\",\n\t\"app.components.HomePage.welcome\": \"Selamat datang!\",\n\t\"app.components.HomePage.welcome.again\": \"Selamat datang \",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Kami mengalu-alukan kedatangan anda di komuniti. Kami sentiasa mencari penambahbaikan, jadi jangan segan silu untuk mesej kami di \",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"Kami harap anda membuat progress pada projek anda... Luangkan masa untuk membaca berita baru kami tentang strapi. Kami memberikan yang terbaik untuk menambah baik produk ini berdasarkan maklum balas anda.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"isu-isu.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" atau berikan \",\n\t\"app.components.ImgPreview.hint\": \"Tarik & Lepas fail anda kedalam kawasan ini atau {browse} fail untuk muat naik\",\n\t\"app.components.ImgPreview.hint.browse\": \"pilih fail\",\n\t\"app.components.InputFile.newFile\": \"Tambah fail baru\",\n\t\"app.components.InputFileDetails.open\": \"Buka di tab baru\",\n\t\"app.components.InputFileDetails.originalName\": \"Nama asal:\",\n\t\"app.components.InputFileDetails.remove\": \"Buang fail ini\",\n\t\"app.components.InputFileDetails.size\": \"Saiz:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"Ia mungkin mengambil beberapa saat untuk memuat turun dan memasang plugin.\",\n\t\"app.components.InstallPluginPage.Download.title\": \"Memuat Turun...\",\n\t\"app.components.InstallPluginPage.description\": \"Kembangkan aplikasi anda dengan mudah.\",\n\t\"app.components.LeftMenuFooter.help\": \"Bantuan\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Dikuasakan oleh \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Jenis Koleksi\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Konfigurasi\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Umum\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Belum ada plugin yang dipasang\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Plugin-plugin\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Jenis Tunggal\",\n\t\"app.components.ListPluginsPage.description\": \"Senarai plugin yang dipasang didalam projek ini.\",\n\t\"app.components.ListPluginsPage.head.title\": \"Senarai plugin\",\n\t\"app.components.Logout.logout\": \"Log Keluar\",\n\t\"app.components.Logout.profile\": \"Profil\",\n\t\"app.components.NotFoundPage.back\": \"Kembali ke laman utama\",\n\t\"app.components.NotFoundPage.description\": \"Tidak dijumpai\",\n\t\"app.components.Official\": \"Rasmi\",\n\t\"app.components.Onboarding.label.completed\": \"% siap\",\n\t\"app.components.Onboarding.title\": \"Video-video untuk bermula\",\n\t\"app.components.PluginCard.Button.label.download\": \"Muat turun\",\n\t\"app.components.PluginCard.Button.label.install\": \"Sudah dipasang\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Ciri AutoReload perlu diaktifkan. Sila mulakkan aplikasi anda dengan `yarn develop`.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Saya Faham!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Untuk tujuan keselamatan, plugin hanya boleh di muat turun dalam environment 'development'.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Memuat turun adalah mustahil\",\n\t\"app.components.PluginCard.compatible\": \"Serasi dengan applikasi anda\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Serasi dengan komuniti\",\n\t\"app.components.PluginCard.more-details\": \"Butiran selanjutnya\",\n\t\"app.components.listPlugins.button\": \"Tambah Plugin Baru\",\n\t\"app.components.listPlugins.title.none\": \"Tiada plugin dipasang\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Satu ralat muncul ketika membuang plugin tersebut\",\n\t\"app.containers.App.notification.error.init\": \"Ralat berlaku semasa permintaan API\",\n\t\"app.links.configure-view\": \"Susun paparan\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.filters\": \"Tapisan\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"component.Input.error.validation.integer\": \"Nilainya haruslah dalam integer\",\n\t\"components.AutoReloadBlocker.description\": \"Jalankan Strapi dengan salah satu arahan berikut:\",\n\t\"components.AutoReloadBlocker.header\": \"ciri Reload diperlukan untuk plugin ini.\",\n\t\"components.ErrorBoundary.title\": \"Ada sesuatu yang tidak kena...\",\n\t\"components.Input.error.attribute.key.taken\": \"Nilai ini sudah wujud\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Tak boleh sama\",\n\t\"components.Input.error.attribute.taken\": \"Nama kotak ini sudah wujud\",\n\t\"components.Input.error.contentTypeName.taken\": \"Nama ini sudah wujud\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"Kata laluan tidak sepadan\",\n\t\"components.Input.error.validation.email\": \"Ini bukan email\",\n\t\"components.Input.error.validation.json\": \"Ini tak sepadan dengan format JSON\",\n\t\"components.Input.error.validation.max\": \"Nilai isinya terlalu tinggi {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"Panjang isinya terlalu panjang {max}.\",\n\t\"components.Input.error.validation.min\": \"Nilai isinya terlalu rendah {min}.\",\n\t\"components.Input.error.validation.minLength\": \"Panjang isinya terlalu pendek {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"Tidak boleh lebih tinggi\",\n\t\"components.Input.error.validation.regex\": \"Nilai isinya tidak sepadan dengan regex.\",\n\t\"components.Input.error.validation.required\": \"Nilai ini adalah wajib.\",\n\t\"components.Input.error.validation.unique\": \"Nilai ini telah digunakan.\",\n\t\"components.InputSelect.option.placeholder\": \"Pilih disini\",\n\t\"components.ListRow.empty\": \"Tiada data untuk pamirkan.\",\n\t\"components.OverlayBlocker.description\": \"Anda menggunakan ciri yang memerlukan pelayan untuk dimulakan semula. Sila tunggu sehinggal pelayan habis.\",\n\t\"components.OverlayBlocker.description.serverError\": \"Pelayan sepatutnya telah dimulakan semula, sila periksa log anda di terminal.\",\n\t\"components.OverlayBlocker.title\": \"Menunggu untuk dimulakan semula...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Permulaan semula mengambil masa lebih lama daripada yang dijangkakan\",\n\t\"components.PageFooter.select\": \"entri dipaparkan setiap halaman\",\n\t\"components.ProductionBlocker.description\": \"Untuk tujuan keslamatan kami perlu menyahkan plugin didalam environment lain.\",\n\t\"components.ProductionBlocker.header\": \"Plugin in hanya tersedia dalam pembangunan(development).\",\n\t\"components.Search.placeholder\": \"Cari...\",\n\t\"components.Wysiwyg.collapse\": \"Tutup\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"Tajuk H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"Tajuk H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"Tajuk H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"Tajuk H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"Tajuk H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"Tajuk H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Tambah tajuk\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"aksara\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Besarkan\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Tarik & Lepas fail, tampal dari clipboard atau {browse}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"pilih\",\n\t\"components.popUpWarning.message\": \"Anda yakin untuk memadam?\",\n\t\"components.popUpWarning.title\": \"Sila sahkan\",\n\t\"form.button.done\": \"Siap\",\n\t\"global.prompt.unsaved\": \"Adakah anda pasti untuk meninggalkan halaman ini? Segala perubahan anda akan hilang\",\n\t\"notification.contentType.relations.conflict\": \"Jenis kandungan ada hubungan(relations) yang saling bertentangan\",\n\t\"notification.error\": \"satu ralat muncul\",\n\t\"notification.error.layout\": \"Tidak dapat kesan susunan atur\",\n\t\"notification.form.error.fields\": \"Borang mengandungi beberapa kesalahan\",\n\t\"notification.form.success.fields\": \"Perubahan disimpan\",\n\t\"notification.success.delete\": \"Item telah dipadamkan\",\n\t\"request.error.model.unknown\": \"model ini tidak wujud\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, ms as default };\n//# sourceMappingURL=ms-eMDSLvhl.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,KAAK;AAAA,EACV;AAAA,EACA,oCAAoC;AAAA,EACpC,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,wCAAwC;AAAA,EACxC,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,+BAA+B;AAAA,EAC/B,+BAA+B;AAChC;", "names": []}