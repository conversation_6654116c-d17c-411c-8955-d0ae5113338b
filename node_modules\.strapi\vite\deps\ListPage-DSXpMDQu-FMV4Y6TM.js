import {
  getDisplayName
} from "./chunk-2E5FOOCQ.js";
import {
  parseISO
} from "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Filters,
  Layouts,
  Pagination,
  Table,
  useAdminUsers,
  useField
} from "./chunk-ELTZWS66.js";
import {
  Page,
  useAPIErrorHandler,
  useNotification,
  useQueryParams,
  useRBAC,
  useTypedSelector
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Breadcrumbs,
  Combobox,
  Crumb,
  Field,
  Flex,
  Grid,
  IconButton,
  JSONInput,
  Loader,
  Modal,
  Option,
  Typography,
  adminApi,
  useIntl
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$3r
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/ListPage-DSXpMDQu.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var auditLogsService = adminApi.injectEndpoints({
  endpoints: (builder) => ({
    getAuditLogs: builder.query({
      query: (params) => ({
        url: `/admin/audit-logs`,
        config: {
          params
        }
      })
    }),
    getAuditLog: builder.query({
      query: (id) => `/admin/audit-logs/${id}`
    })
  }),
  overrideExisting: false
});
var { useGetAuditLogsQuery, useGetAuditLogQuery } = auditLogsService;
var useFormatTimeStamp = () => {
  const { formatDate } = useIntl();
  const formatTimeStamp = (value) => {
    const date = parseISO(value);
    const formattedDate = formatDate(date, {
      dateStyle: "long"
    });
    const formattedTime = formatDate(date, {
      timeStyle: "medium",
      hourCycle: "h24"
    });
    return `${formattedDate}, ${formattedTime}`;
  };
  return formatTimeStamp;
};
var actionTypes = {
  "entry.create": "Create entry{model, select, undefined {} other { ({model})}}",
  "entry.update": "Update entry{model, select, undefined {} other { ({model})}}",
  "entry.delete": "Delete entry{model, select, undefined {} other { ({model})}}",
  "entry.publish": "Publish entry{model, select, undefined {} other { ({model})}}",
  "entry.unpublish": "Unpublish entry{model, select, undefined {} other { ({model})}}",
  "media.create": "Create media",
  "media.update": "Update media",
  "media.delete": "Delete media",
  "media-folder.create": "Create media folder",
  "media-folder.update": "Update media folder",
  "media-folder.delete": "Delete media folder",
  "user.create": "Create user",
  "user.update": "Update user",
  "user.delete": "Delete user",
  "admin.auth.success": "Admin login",
  "admin.logout": "Admin logout",
  "content-type.create": "Create content type",
  "content-type.update": "Update content type",
  "content-type.delete": "Delete content type",
  "component.create": "Create component",
  "component.update": "Update component",
  "component.delete": "Delete component",
  "role.create": "Create role",
  "role.update": "Update role",
  "role.delete": "Delete role",
  "permission.create": "Create permission",
  "permission.update": "Update permission",
  "permission.delete": "Delete permission"
};
var getDefaultMessage = (value) => {
  return actionTypes[value] || value;
};
var Modal2 = ({ handleClose, logId }) => {
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const { data, error, isLoading } = useGetAuditLogQuery(logId);
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
      handleClose();
    }
  }, [error, formatAPIError, handleClose, toggleNotification]);
  const formatTimeStamp = useFormatTimeStamp();
  const formattedDate = data && "date" in data ? formatTimeStamp(data.date) : "";
  return (0, import_jsx_runtime.jsx)(Modal.Root, { defaultOpen: true, onOpenChange: handleClose, children: (0, import_jsx_runtime.jsxs)(Modal.Content, { children: [
    (0, import_jsx_runtime.jsx)(Modal.Header, { children: (0, import_jsx_runtime.jsx)(Breadcrumbs, { label: formattedDate, id: "title", children: (0, import_jsx_runtime.jsx)(Crumb, { isCurrent: true, children: formattedDate }) }) }),
    (0, import_jsx_runtime.jsx)(Modal.Body, { children: (0, import_jsx_runtime.jsx)(ActionBody, { isLoading, data, formattedDate }) })
  ] }) });
};
var ActionBody = ({ isLoading, data, formattedDate }) => {
  const { formatMessage } = useIntl();
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Flex, { padding: 7, justifyContent: "center", alignItems: "center", children: (0, import_jsx_runtime.jsx)(Loader, { children: "Loading content..." }) });
  }
  const { action, user, payload } = data;
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(Box, { marginBottom: 3, children: (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", id: "title", children: formatMessage({
      id: "Settings.permissions.auditLogs.details",
      defaultMessage: "Log Details"
    }) }) }),
    (0, import_jsx_runtime.jsxs)(
      Grid.Root,
      {
        gap: 4,
        gridCols: 2,
        paddingTop: 4,
        paddingBottom: 4,
        paddingLeft: 6,
        paddingRight: 6,
        marginBottom: 4,
        background: "neutral100",
        hasRadius: true,
        children: [
          (0, import_jsx_runtime.jsx)(
            ActionItem,
            {
              actionLabel: formatMessage({
                id: "Settings.permissions.auditLogs.action",
                defaultMessage: "Action"
              }),
              actionName: formatMessage(
                {
                  id: `Settings.permissions.auditLogs.${action}`,
                  defaultMessage: getDefaultMessage(action)
                },
                // @ts-expect-error - any
                { model: payload == null ? void 0 : payload.model }
              )
            }
          ),
          (0, import_jsx_runtime.jsx)(
            ActionItem,
            {
              actionLabel: formatMessage({
                id: "Settings.permissions.auditLogs.date",
                defaultMessage: "Date"
              }),
              actionName: formattedDate
            }
          ),
          (0, import_jsx_runtime.jsx)(
            ActionItem,
            {
              actionLabel: formatMessage({
                id: "Settings.permissions.auditLogs.user",
                defaultMessage: "User"
              }),
              actionName: (user == null ? void 0 : user.displayName) || "-"
            }
          ),
          (0, import_jsx_runtime.jsx)(
            ActionItem,
            {
              actionLabel: formatMessage({
                id: "Settings.permissions.auditLogs.userId",
                defaultMessage: "User ID"
              }),
              actionName: (user == null ? void 0 : user.id.toString()) || "-"
            }
          )
        ]
      }
    ),
    (0, import_jsx_runtime.jsxs)(Field.Root, { children: [
      (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
        id: "Settings.permissions.auditLogs.payload",
        defaultMessage: "Payload"
      }) }),
      (0, import_jsx_runtime.jsx)(Payload, { value: JSON.stringify(payload, null, 2), disabled: true })
    ] })
  ] });
};
var Payload = dt(JSONInput)`
  max-width: 100%;
  overflow: scroll;
`;
var ActionItem = ({ actionLabel, actionName }) => {
  return (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "baseline", gap: 1, children: [
    (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral600", variant: "sigma", children: actionLabel }),
    (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral600", children: actionName })
  ] });
};
var useAuditLogsData = ({
  canReadAuditLogs,
  canReadUsers
}) => {
  const { toggleNotification } = useNotification();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  const [{ query }] = useQueryParams();
  const {
    data,
    error,
    isError: isUsersError,
    isLoading: isLoadingUsers
  } = useAdminUsers(
    {},
    {
      skip: !canReadUsers,
      refetchOnMountOrArgChange: true
    }
  );
  React.useEffect(() => {
    if (error) {
      toggleNotification({ type: "danger", message: formatAPIError(error) });
    }
  }, [error, toggleNotification, formatAPIError]);
  const {
    data: auditLogs,
    isLoading: isLoadingAuditLogs,
    isError: isAuditLogsError,
    error: auditLogsError
  } = useGetAuditLogsQuery(query, {
    refetchOnMountOrArgChange: true,
    skip: !canReadAuditLogs
  });
  React.useEffect(() => {
    if (auditLogsError) {
      toggleNotification({ type: "danger", message: formatAPIError(auditLogsError) });
    }
  }, [auditLogsError, toggleNotification, formatAPIError]);
  return {
    auditLogs,
    users: (data == null ? void 0 : data.users) ?? [],
    isLoading: isLoadingUsers || isLoadingAuditLogs,
    hasError: isAuditLogsError || isUsersError
  };
};
var ComboboxFilter = (props) => {
  var _a;
  const { formatMessage } = useIntl();
  const field = useField(props.name);
  const ariaLabel = formatMessage({
    id: "Settings.permissions.auditLogs.filter.aria-label",
    defaultMessage: "Search and select an option to filter"
  });
  const handleChange = (value) => {
    field.onChange(props.name, value);
  };
  return (0, import_jsx_runtime.jsx)(Combobox, { "aria-label": ariaLabel, value: field.value, onChange: handleChange, children: (_a = props.options) == null ? void 0 : _a.map((opt) => {
    const value = typeof opt === "string" ? opt : opt.value;
    const label = typeof opt === "string" ? opt : opt.label;
    return (0, import_jsx_runtime.jsx)(Option, { value, children: label }, value);
  }) });
};
var getDisplayedFilters = ({
  formatMessage,
  users,
  canReadUsers
}) => {
  const operators = [
    {
      label: formatMessage({
        id: "components.FilterOptions.FILTER_TYPES.$eq",
        defaultMessage: "is"
      }),
      value: "$eq"
    },
    {
      label: formatMessage({
        id: "components.FilterOptions.FILTER_TYPES.$ne",
        defaultMessage: "is not"
      }),
      value: "$ne"
    }
  ];
  const filters = [
    {
      input: ComboboxFilter,
      label: formatMessage({
        id: "Settings.permissions.auditLogs.action",
        defaultMessage: "Action"
      }),
      name: "action",
      operators,
      options: Object.keys(actionTypes).map((action) => ({
        label: formatMessage(
          {
            id: `Settings.permissions.auditLogs.${action}`,
            defaultMessage: getDefaultMessage(action)
          },
          { model: void 0 }
        ),
        value: action
      })),
      type: "enumeration"
    },
    {
      label: formatMessage({
        id: "Settings.permissions.auditLogs.date",
        defaultMessage: "Date"
      }),
      name: "date",
      type: "datetime"
    }
  ];
  if (canReadUsers && users) {
    return [
      ...filters,
      {
        input: ComboboxFilter,
        label: formatMessage({
          id: "Settings.permissions.auditLogs.user",
          defaultMessage: "User"
        }),
        mainField: { name: "id", type: "integer" },
        name: "user",
        operators,
        options: users.map((user) => ({
          label: getDisplayName(user),
          value: user.id.toString()
        })),
        type: "relation"
      }
    ];
  }
  return filters;
};
var ListPage = () => {
  const { formatMessage } = useIntl();
  const permissions = useTypedSelector((state) => state.admin_app.permissions.settings);
  const {
    allowedActions: { canRead: canReadAuditLogs, canReadUsers },
    isLoading: isLoadingRBAC
  } = useRBAC({
    ...permissions == null ? void 0 : permissions.auditLogs,
    readUsers: (permissions == null ? void 0 : permissions.users.read) || []
  });
  const [{ query }, setQuery] = useQueryParams();
  const {
    auditLogs,
    users,
    isLoading: isLoadingData,
    hasError
  } = useAuditLogsData({
    canReadAuditLogs,
    canReadUsers
  });
  const formatTimeStamp = useFormatTimeStamp();
  const displayedFilters = getDisplayedFilters({ formatMessage, users, canReadUsers });
  const headers = [
    {
      name: "action",
      label: formatMessage({
        id: "Settings.permissions.auditLogs.action",
        defaultMessage: "Action"
      }),
      sortable: true
    },
    {
      name: "date",
      label: formatMessage({
        id: "Settings.permissions.auditLogs.date",
        defaultMessage: "Date"
      }),
      sortable: true
    },
    {
      name: "user",
      label: formatMessage({
        id: "Settings.permissions.auditLogs.user",
        defaultMessage: "User"
      }),
      sortable: false,
      // In this case, the passed parameter cannot and shouldn't be something else than User
      cellFormatter: ({ user }) => user ? user.displayName : ""
    }
  ];
  if (hasError) {
    return (0, import_jsx_runtime.jsx)(Page.Error, {});
  }
  const isLoading = isLoadingData || isLoadingRBAC;
  const { results = [] } = auditLogs ?? {};
  return (0, import_jsx_runtime.jsxs)(Page.Main, { "aria-busy": isLoading, children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      {
        name: formatMessage({
          id: "global.auditLogs",
          defaultMessage: "Audit Logs"
        })
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: "global.auditLogs",
          defaultMessage: "Audit Logs"
        }),
        subtitle: formatMessage({
          id: "Settings.permissions.auditLogs.listview.header.subtitle",
          defaultMessage: "Logs of all the activities that happened in your environment"
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(
      Layouts.Action,
      {
        startActions: (0, import_jsx_runtime.jsxs)(Filters.Root, { options: displayedFilters, children: [
          (0, import_jsx_runtime.jsx)(Filters.Trigger, {}),
          (0, import_jsx_runtime.jsx)(Filters.Popover, {}),
          (0, import_jsx_runtime.jsx)(Filters.List, {})
        ] })
      }
    ),
    (0, import_jsx_runtime.jsxs)(Layouts.Content, { children: [
      (0, import_jsx_runtime.jsx)(Table.Root, { rows: results, headers, isLoading, children: (0, import_jsx_runtime.jsxs)(Table.Content, { children: [
        (0, import_jsx_runtime.jsx)(Table.Head, { children: headers.map((header) => (0, import_jsx_runtime.jsx)(Table.HeaderCell, { ...header }, header.name)) }),
        (0, import_jsx_runtime.jsx)(Table.Empty, {}),
        (0, import_jsx_runtime.jsx)(Table.Loading, {}),
        (0, import_jsx_runtime.jsx)(Table.Body, { children: results.map((log) => (0, import_jsx_runtime.jsxs)(Table.Row, { onClick: () => setQuery({ id: log.id }), children: [
          headers.map((header) => {
            var _a;
            const { name, cellFormatter } = header;
            switch (name) {
              case "action":
                return (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: formatMessage(
                  {
                    id: `Settings.permissions.auditLogs.${log.action}`,
                    // @ts-expect-error – getDefaultMessage probably doesn't benefit from being so strongly typed unless we just add string at the end.
                    defaultMessage: getDefaultMessage(log.action)
                  },
                  { model: ((_a = log.payload) == null ? void 0 : _a.model) ?? "" }
                ) }) }, name);
              case "date":
                return (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: formatTimeStamp(log.date) }) }, name);
              case "user":
                return (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: cellFormatter ? cellFormatter(log, header) : "-" }) }, name);
              default:
                return (0, import_jsx_runtime.jsx)(Table.Cell, { children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral800", children: log[name] || "-" }) }, name);
            }
          }),
          (0, import_jsx_runtime.jsx)(Table.Cell, { onClick: (e) => e.stopPropagation(), children: (0, import_jsx_runtime.jsx)(Flex, { justifyContent: "end", children: (0, import_jsx_runtime.jsx)(
            IconButton,
            {
              onClick: () => setQuery({ id: log.id }),
              withTooltip: false,
              label: formatMessage(
                { id: "app.component.table.view", defaultMessage: "{target} details" },
                { target: `${log.action} action` }
              ),
              variant: "ghost",
              children: (0, import_jsx_runtime.jsx)(ForwardRef$3r, {})
            }
          ) }) })
        ] }, log.id)) })
      ] }) }),
      (0, import_jsx_runtime.jsxs)(Pagination.Root, { ...auditLogs == null ? void 0 : auditLogs.pagination, children: [
        (0, import_jsx_runtime.jsx)(Pagination.PageSize, {}),
        (0, import_jsx_runtime.jsx)(Pagination.Links, {})
      ] })
    ] }),
    (query == null ? void 0 : query.id) && (0, import_jsx_runtime.jsx)(Modal2, { handleClose: () => setQuery({ id: "" }, "remove"), logId: query.id.toString() })
  ] });
};
var ProtectedListPage = () => {
  const permissions = useTypedSelector(
    (state) => {
      var _a, _b;
      return (_b = (_a = state.admin_app.permissions.settings) == null ? void 0 : _a.auditLogs) == null ? void 0 : _b.main;
    }
  );
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions, children: (0, import_jsx_runtime.jsx)(ListPage, {}) });
};
export {
  ListPage,
  ProtectedListPage
};
//# sourceMappingURL=ListPage-DSXpMDQu-FMV4Y6TM.js.map
