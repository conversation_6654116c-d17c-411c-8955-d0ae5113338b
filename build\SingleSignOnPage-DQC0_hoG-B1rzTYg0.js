import{ax as j,m as s,n as g,au as v,a as A,c as O,aJ as k,aK as C,e as E,L as p,O as F,A as T,N as q,w as z,s as H,G as R,Q as N,aC as V,x as u,aL as _,aM as $,i as B,aN as G,ar as b,k as f,j as Q,aO as U}from"./strapi-YzJfjJ2z.js";import{u as D}from"./useAdminRoles-Bd2N7J7A-CIPQp8aL.js";const J=B().shape({autoRegister:Q().required(f.required),defaultRole:b().when("autoRegister",(e,t)=>e?t.required(f.required):t.nullable()),ssoLockedRoles:G().nullable().of(b().when("ssoLockedRoles",(e,t)=>e?t.required(f.required):t.nullable()))}),K=()=>{const{formatMessage:e}=v(),t=j(l=>l.admin_app.permissions),{toggleNotification:o}=A(),{_unstableFormatAPIError:h,_unstableFormatValidationErrors:m}=O(),{isLoading:i,data:n}=k(),[d,{isLoading:M}]=C(),{isLoading:L,allowedActions:{canUpdate:y,canRead:w}}=E({...t.settings?.sso,readRoles:t.settings?.roles.read??[]}),{roles:S,isLoading:I}=D(void 0,{skip:!w}),P=async(l,c)=>{try{const a=await d(l);if("error"in a){U(a.error)&&a.error.name==="ValidationError"?c.setErrors(m(a.error)):o({type:"danger",message:h(a.error)});return}o({type:"success",message:e({id:"notification.success.saved"})})}catch{o({type:"danger",message:e({id:"notification.error",defaultMessage:"An error occurred, please try again."})})}},x=I||L||i;return s.jsxs(p.Root,{children:[s.jsx(g.Title,{children:e({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"SSO"})}),s.jsx(g.Main,{"aria-busy":M||x,tabIndex:-1,children:s.jsx(F,{method:"PUT",onSubmit:P,validationSchema:J,disabled:!y,initialValues:n||{autoRegister:!1,defaultRole:null,ssoLockedRoles:null},children:({modified:l,isSubmitting:c})=>s.jsxs(s.Fragment,{children:[s.jsx(p.Header,{primaryAction:s.jsx(T,{disabled:!l,loading:c,startIcon:s.jsx(q,{}),type:"submit",children:e({id:"global.save",defaultMessage:"Save"})}),title:e({id:"Settings.sso.title",defaultMessage:"Single Sign-On"}),subtitle:e({id:"Settings.sso.description",defaultMessage:"Configure the settings for the Single Sign-On feature."})}),s.jsx(p.Content,{children:c||x?s.jsx(g.Loading,{}):s.jsxs(z,{direction:"column",alignItems:"stretch",gap:4,background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:[s.jsx(H,{variant:"delta",tag:"h2",children:e({id:"global.settings",defaultMessage:"Settings"})}),s.jsx(R.Root,{gap:4,children:[{hint:e({id:"Settings.sso.form.registration.description",defaultMessage:"Create new user on SSO login if no account exists"}),label:e({id:"Settings.sso.form.registration.label",defaultMessage:"Auto-registration"}),name:"autoRegister",size:6,type:"boolean"},{hint:e({id:"Settings.sso.form.defaultRole.description",defaultMessage:"It will attach the new authenticated user to the selected role"}),label:e({id:"Settings.sso.form.defaultRole.label",defaultMessage:"Default role"}),name:"defaultRole",options:S.map(({id:a,name:r})=>({label:r,value:a.toString()})),placeholder:e({id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"}),size:6,type:"enumeration"},{hint:e({id:"Settings.sso.form.localAuthenticationLock.description",defaultMessage:"Select the roles for which you want to disable the local authentication"}),label:e({id:"Settings.sso.form.localAuthenticationLock.label",defaultMessage:"Local authentication lock-out"}),name:"ssoLockedRoles",options:S.map(({id:a,name:r})=>({label:r,value:a.toString()})),placeholder:e({id:"components.InputSelect.option.placeholder",defaultMessage:"Choose here"}),size:6,type:"multi"}].map(({size:a,...r})=>s.jsx(R.Item,{col:a,direction:"column",alignItems:"stretch",children:s.jsx(W,{...r})},r.name))})]})})]})})})]})},W=e=>{switch(e.type){case"multi":return s.jsx(X,{...e});default:return s.jsx(N,{...e})}},X=({hint:e,label:t,name:o,options:h,...m})=>{const i=V(o);return s.jsxs(u.Root,{name:o,hint:e,error:i.error,children:[s.jsx(u.Label,{children:t}),s.jsx(_,{onChange:n=>i.onChange("ssoLockedRoles",n),onClear:()=>i.onChange("ssoLockedRoles",[]),value:i.value??[],withTags:!0,...m,children:h.map(({label:n,value:d})=>s.jsx($,{value:d,children:n},d))}),s.jsx(u.Hint,{}),s.jsx(u.Error,{})]})},ee=()=>{const e=j(t=>t.admin_app.permissions.settings?.sso?.main);return s.jsx(g.Protect,{permissions:e,children:s.jsx(K,{})})};export{ee as ProtectedSSO,K as SingleSignOnPage};
