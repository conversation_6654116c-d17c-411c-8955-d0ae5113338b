const __vite__fileDeps=["MagicLinkEE-Dv-aox3R-DVXhGqr3.js","strapi-YzJfjJ2z.js","strapi-COJtagOC.css","SelectRoles-C1P9kRCc-DM4OhDyY.js","useAdminRoles-Bd2N7J7A-CIPQp8aL.js"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{au as C,at as B,al as z,a as O,bj as D,bk as N,c as q,ax as T,e as F,bv as V,az as H,r as $,m as e,n as c,bw as E,O as G,L as P,bh as K,A as Q,N as Y,J as p,w as h,s as y,G as l,Q as J,i as W,aN as X,k as v,j as Z,aO as ee}from"./strapi-YzJfjJ2z.js";import{s as se}from"./selectors-ZSFBgSp8-CGZgPRVm.js";import{g as ae}from"./users-8N93LH7R-MOwOr-tf.js";import{M as te,S as re}from"./SelectRoles-C1P9kRCc-DM4OhDyY.js";import{C as ie}from"./validation-DZQHQxC3-UHkI79p7.js";import"./useAdminRoles-Bd2N7J7A-CIPQp8aL.js";const oe=W().shape({...ie,isActive:Z(),roles:X().min(1,{id:v.required.id,defaultMessage:"This field is required"}).required({id:v.required.id,defaultMessage:"This field is required"})}),L=["email","firstname","lastname","username","isActive","roles"],ne=()=>{const{formatMessage:s}=C(),x=B("/settings/users/:id")?.params?.id??"",M=z(),{toggleNotification:r}=O(),b=D(te,async()=>(await N(()=>import("./MagicLinkEE-Dv-aox3R-DVXhGqr3.js"),__vite__mapDeps([0,1,2,3,4]))).MagicLinkEE),{_unstableFormatAPIError:m,_unstableFormatValidationErrors:I}=q(),A=T(se),{isLoading:_,allowedActions:{canUpdate:u}}=F({read:A.settings?.users.read??[],update:A.settings?.users.update??[]}),[k]=V(),{data:R,error:i,isLoading:U}=H({id:x},{refetchOnMountOrArgChange:!0}),[o]=R?.users??[];if($.useEffect(()=>{i&&(i.name==="UnauthorizedError"?(r({type:"info",message:s({id:"notification.permission.not-allowed-read",defaultMessage:"You are not allowed to see this document"})}),M("/")):r({type:"danger",message:m(i)}))},[i,m,s,M,r]),U||!b||_)return e.jsx(c.Loading,{});const j={...E(o,L),roles:o.roles.map(({id:a})=>a),password:"",confirmPassword:""},S=async(a,n)=>{const{confirmPassword:w,...g}=a,t=await k({id:x,...g});"error"in t&&ee(t.error)?(t.error.name==="ValidationError"&&n.setErrors(I(t.error)),r({type:"danger",message:m(t.error)})):(r({type:"success",message:s({id:"notification.success.saved",defaultMessage:"Saved"})}),n.setValues({...E(a,L),password:"",confirmPassword:""}))};return e.jsxs(c.Main,{children:[e.jsx(c.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Users"})}),e.jsx(G,{method:"PUT",onSubmit:S,initialValues:j,validationSchema:oe,children:({isSubmitting:a,modified:n})=>e.jsxs(e.Fragment,{children:[e.jsx(P.Header,{primaryAction:e.jsx(Q,{disabled:a||!u||!n,startIcon:e.jsx(Y,{}),loading:a,type:"submit",children:s({id:"global.save",defaultMessage:"Save"})}),title:s({id:"app.containers.Users.EditPage.header.label",defaultMessage:"Edit {name}"},{name:ae(j)}),navigationAction:e.jsx(K,{})}),e.jsxs(P.Content,{children:[o?.registrationToken&&e.jsx(p,{paddingBottom:6,children:e.jsx(b,{registrationToken:o.registrationToken})}),e.jsxs(h,{direction:"column",alignItems:"stretch",gap:7,children:[e.jsx(p,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(h,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(y,{variant:"delta",tag:"h2",children:s({id:"app.components.Users.ModalCreateBody.block-title.details",defaultMessage:"Details"})}),e.jsx(l.Root,{gap:5,children:de.map(w=>w.map(({size:g,label:t,...d})=>e.jsx(l.Item,{col:g,direction:"column",alignItems:"stretch",children:e.jsx(J,{...d,disabled:!u,label:s(t),placeholder:"placeholder"in d?s(d.placeholder):void 0})},d.name)))})]})}),e.jsx(p,{background:"neutral0",hasRadius:!0,shadow:"filterShadow",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,children:e.jsxs(h,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(y,{variant:"delta",tag:"h2",children:s({id:"global.roles",defaultMessage:"User's role"})}),e.jsx(l.Root,{gap:5,children:e.jsx(l.Item,{col:6,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(re,{disabled:!u})})})]})})]})]})]})})]})},de=[[{label:{id:"Auth.form.firstname.label",defaultMessage:"First name"},name:"firstname",placeholder:{id:"Auth.form.firstname.placeholder",defaultMessage:"e.g. Kai"},type:"string",size:6,required:!0},{label:{id:"Auth.form.lastname.label",defaultMessage:"Last name"},name:"lastname",placeholder:{id:"Auth.form.lastname.placeholder",defaultMessage:"e.g. Doe"},type:"string",size:6}],[{label:{id:"Auth.form.email.label",defaultMessage:"Email"},name:"email",placeholder:{id:"Auth.form.email.placeholder",defaultMessage:"e.g. <EMAIL>"},type:"email",size:6,required:!0},{label:{id:"Auth.form.username.label",defaultMessage:"Username"},name:"username",placeholder:{id:"Auth.form.username.placeholder",defaultMessage:"e.g. Kai_Doe"},type:"string",size:6}],[{autoComplete:"new-password",label:{id:"global.password",defaultMessage:"Password"},name:"password",type:"password",size:6},{autoComplete:"new-password",label:{id:"Auth.form.confirmPassword.label",defaultMessage:"Password confirmation"},name:"confirmPassword",type:"password",size:6}],[{label:{id:"Auth.form.active.label",defaultMessage:"Active"},name:"isActive",type:"boolean",size:6}]],fe=()=>{const s=T(f=>f.admin_app.permissions.settings?.users.read);return e.jsx(c.Protect,{permissions:s,children:e.jsx(ne,{})})};export{ne as EditPage,fe as ProtectedEditPage};
