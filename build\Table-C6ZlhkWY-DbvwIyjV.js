import{ay as R,au as j,al as k,ba as S,d as M,m as e,aG as a,s as g,r as B,bP as E,bQ as I,w as A,S as L,bK as P,_ as $,F,b5 as v,J as U,I as N,b3 as H,b6 as O}from"./strapi-YzJfjJ2z.js";const W=["years","months","days","hours","minutes","seconds"],y=B.forwardRef(({timestamp:s,customIntervals:n=[],...l},i)=>{const{formatRelativeTime:d,formatDate:r,formatTime:c}=j(),u=E({start:s,end:Date.now()}),x=W.find(o=>u[o]>0&&Object.keys(u).includes(o)),h=I(s)?-u[x]:u[x],m=n.find(o=>u[o.unit]<o.threshold),p=m?m.text:d(h,x,{numeric:"auto"});return e.jsx("time",{ref:i,dateTime:s.toISOString(),role:"time",title:`${r(s)} ${c(s)}`,...l,children:p})}),K=({permissions:s,headers:n=[],isLoading:l=!1,tokens:i=[],onConfirmDelete:d,tokenType:r})=>{const[{query:c}]=R(),{formatMessage:u,locale:x}=j(),[,h]=c&&c.sort?c.sort.split(":"):[void 0,"ASC"],m=k(),{trackUsage:p}=S(),o=M(x),f=[...i].sort((t,b)=>h==="DESC"?o.compare(b.name,t.name):o.compare(t.name,b.name)),{canDelete:C,canUpdate:T,canRead:D}=s,w=t=>()=>{D&&(p("willEditTokenFromList",{tokenType:r}),m(t.toString()))};return e.jsx(a.Root,{headers:n,rows:f,isLoading:l,children:e.jsxs(a.Content,{children:[e.jsx(a.Head,{children:n.map(t=>e.jsx(a.HeaderCell,{...t},t.name))}),e.jsx(a.Empty,{}),e.jsx(a.Loading,{}),e.jsx(a.Body,{children:f.map(t=>e.jsxs(a.Row,{onClick:w(t.id),children:[e.jsx(a.Cell,{maxWidth:"25rem",children:e.jsx(g,{textColor:"neutral800",fontWeight:"bold",ellipsis:!0,children:t.name})}),e.jsx(a.Cell,{maxWidth:"25rem",children:e.jsx(g,{textColor:"neutral800",ellipsis:!0,children:t.description})}),e.jsx(a.Cell,{children:e.jsx(g,{textColor:"neutral800",children:e.jsx(y,{timestamp:new Date(t.createdAt)})})}),e.jsx(a.Cell,{children:t.lastUsedAt&&e.jsx(g,{textColor:"neutral800",children:e.jsx(y,{timestamp:new Date(t.lastUsedAt),customIntervals:[{unit:"hours",threshold:1,text:u({id:"Settings.apiTokens.lastHour",defaultMessage:"last hour"})}]})})}),T||D||C?e.jsx(a.Cell,{children:e.jsxs(A,{justifyContent:"end",children:[T&&e.jsx(z,{tokenName:t.name,tokenId:t.id}),C&&e.jsx(q,{tokenName:t.name,onClickDelete:()=>d?.(t.id),tokenType:r})]})}):null]},t.id))})]})})},G={edit:{id:"app.component.table.edit",defaultMessage:"Edit {target}"},read:{id:"app.component.table.read",defaultMessage:"Read {target}"}},Q=({tokenName:s,tokenId:n,buttonType:l="edit",children:i})=>{const{formatMessage:d}=j();return e.jsx(_,{tag:$,to:n.toString(),onClick:r=>r.stopPropagation(),title:d(G[l],{target:s}),variant:"ghost",size:"S",children:i})},_=L(P)`
  padding: 0.7rem;

  & > span {
    display: flex;
  }
`,q=({tokenName:s,onClickDelete:n,tokenType:l})=>{const{formatMessage:i}=j(),{trackUsage:d}=S(),r=()=>{d("willDeleteToken",{tokenType:l}),n()};return e.jsx(v.Root,{children:e.jsxs(U,{paddingLeft:1,onClick:c=>c.stopPropagation(),children:[e.jsx(v.Trigger,{children:e.jsx(N,{label:i({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:`${s}`}),name:"delete",variant:"ghost",children:e.jsx(H,{})})}),e.jsx(O,{onConfirm:r})]})})},z=({tokenName:s,tokenId:n})=>e.jsx(Q,{tokenName:s,tokenId:n,children:e.jsx(F,{})});export{K as T};
