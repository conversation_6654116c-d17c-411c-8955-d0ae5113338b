{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/_chunks/tr-DsUerr-c.mjs"], "sourcesContent": ["const configurations = \"Kurulumlar\";\nconst from = \"kimden\";\nconst tr = {\n  \"ComponentIconPicker.search.placeholder\": \"Bir ikon ara\",\n  \"attribute.boolean\": \"Mantıksal\",\n  \"attribute.component\": \"Bileşen\",\n  \"attribute.component.description\": \"Tekrarlayabileceğin alanlar grubu\",\n  \"attribute.customField\": \"Özel alan\",\n  \"attribute.date\": \"Tarih\",\n  \"attribute.dynamiczone\": \"Dinamik bölge\",\n  \"attribute.dynamiczone.description\": \"İçeriği düzenlerken bileşenleri dinamik olarak seç\",\n  \"attribute.email\": \"E-posta\",\n  \"attribute.enumeration\": \"Enumeration\",\n  \"attribute.json\": \"JSON\",\n  \"attribute.media\": \"Medya\",\n  \"attribute.null\": \" \",\n  \"attribute.number\": \"Sayı\",\n  \"attribute.number.description\": \"Sayılar (tamsayı, kayan, ondalıklı)\",\n  \"attribute.password\": \"<PERSON>rola\",\n  \"attribute.relation\": \"<PERSON><PERSON><PERSON><PERSON>\",\n  \"attribute.richtext\": \"Zengin metin\",\n  \"attribute.richtext.description\": \"Biçimlendirme seçenekleri olan bir zengin metin düzenleyici\",\n  \"attribute.text\": \"Yazı\",\n  \"attribute.time\": \"Zaman\",\n  \"attribute.timestamp\": \"Zaman damgası\",\n  \"attribute.uid\": \"UID\",\n  \"attribute.uid.description\": \"Benzersiz kimlik numarası\",\n  \"button.attributes.add.another\": \"Başka bir alan ekle\",\n  \"button.component.add\": \"Bir bileşen ekle\",\n  \"button.component.create\": \"Yeni bileşen oluştur\",\n  \"button.model.create\": \"Yeni koleksiyon tipi oluştur\",\n  \"button.single-types.create\": \"Yeni tekil tip oluştur\",\n  \"component.repeatable\": \"(tekrarlayabilen)\",\n  \"components.SelectComponents.displayed-value\": \"{number} bileşen seçildi\",\n  \"components.componentSelect.no-component-available\": \"Tüm bileşenleri ekledin\",\n  \"components.componentSelect.no-component-available.with-search\": \"Aramanızla eşleşen bileşen bulunamadı\",\n  \"components.componentSelect.value-component\": \"{number} bileşen seçildi (aramak istediğin bileşeni yaz)\",\n  \"components.componentSelect.value-components\": \"{number} bileşen seçildi\",\n  configurations,\n  \"contentType.apiId-plural.description\": \"Çoğul API Kimliği\",\n  \"contentType.apiId-plural.label\": \"API Kimliği (Çoğul)\",\n  \"contentType.apiId-singular.description\": \"UID API yolları ve veritabanı tablolarını oluşturmak için kullanılır\",\n  \"contentType.apiId-singular.label\": \"API Kimliği (Tekil)\",\n  \"contentType.collectionName.description\": \"İçerik tipin ve tablo adın farklı olduğu durumlarda kullanışlıdır\",\n  \"contentType.collectionName.label\": \"Koleksiyon adı\",\n  \"contentType.displayName.label\": \"Görüntülenme adı\",\n  \"contentType.kind.change.warning\": \"İçerik tipinin çeşidini değiştirdiniz. API sıfırlanacak (routes, controllers, services).\",\n  \"error.attributeName.reserved-name\": \"Bu isim, diğer özellikleri bozabileceğinden, içerik tipi için kullanılamaz\",\n  \"error.contentType.pluralName-used\": \"Bu değer tekil olanla aynı olamaz\",\n  \"error.contentType.singularName-used\": \"Bu değer çoğul olanla aynı olamaz\",\n  \"error.contentTypeName.reserved-name\": \"Bu isim, diğer özellikleri bozabileceğinden, projede kullanılamaz\",\n  \"error.validation.enum-duplicate\": \"Mükerrer değerlere izin verilmez (yalnızca alfanümerik karakterler hesaba katılmıştır).\",\n  \"error.validation.enum-empty-string\": \"Kelime boş olamaz\",\n  \"error.validation.enum-regex\": \"En az bir değer geçersiz. Değerlerin içinde sayılardan önce en az bir alfabetik karakter olmalıdır.\",\n  \"error.validation.minSupMax\": \"Üst olamaz\",\n  \"error.validation.positive\": \"Pozitif sayı olmalıdır\",\n  \"error.validation.regex\": \"Regex ifadesi geçersiz\",\n  \"error.validation.relation.targetAttribute-taken\": \"Bu isim hedefte yer alıyor\",\n  \"form.attribute.component.option.add\": \"Bir bileşen ekle\",\n  \"form.attribute.component.option.create\": \"Yeni bir bileşen oluştur\",\n  \"form.attribute.component.option.create.description\": \"Bileşen tipler ve bileşenler arasında paylaşılır ve her yerden erişilebilir.\",\n  \"form.attribute.component.option.repeatable\": \"Tekrarlanabilir bileşen\",\n  \"form.attribute.component.option.repeatable.description\": \"Çok kayıtlı (diziler) içerikler, meta etiketleri, v.b. için ideal\",\n  \"form.attribute.component.option.reuse-existing\": \"Mevcut bir bileşeni kullan\",\n  \"form.attribute.component.option.reuse-existing.description\": \"Verilerini içerik tipleri arasında tutarlı kılmak için daha önceden oluşturulmuş bir bileşeni kullan.\",\n  \"form.attribute.component.option.single\": \"Tekil bileşen\",\n  \"form.attribute.component.option.single.description\": \"Gruplama alanları (adres, temel bilgiler, v.b. için ideal\",\n  \"form.attribute.item.customColumnName\": \"Özel kolon isimleri\",\n  \"form.attribute.item.customColumnName.description\": \"Bu veritabanı sütun isimleri servis yanıtları için daha kapsamlı bir biçimde yeniden adlandırmak için kullanışlıdır\",\n  \"form.attribute.item.date.type.date\": \"tarih (ör: 01/01/{currentYear})\",\n  \"form.attribute.item.date.type.datetime\": \"tarih saat (ör: 01/01/{currentYear} 00:00)\",\n  \"form.attribute.item.date.type.time\": \"saat (ör: 00:00)\",\n  \"form.attribute.item.defineRelation.fieldName\": \"Alan adı\",\n  \"form.attribute.item.enumeration.graphql\": \"GraphQL için isim geçersiz kıl\",\n  \"form.attribute.item.enumeration.graphql.description\": \"GraphQL için varsayılan oluşturulan adı geçersiz kılmanıza izin verir.\",\n  \"form.attribute.item.enumeration.placeholder\": \"Örn:\\nsabah\\nöğlen\\nakşam\",\n  \"form.attribute.item.enumeration.rules\": \"Values (one line per value)\",\n  \"form.attribute.item.maximum\": \"En yüksek değer\",\n  \"form.attribute.item.maximumLength\": \"En yüksek uzunluk\",\n  \"form.attribute.item.minimum\": \"En düşük değer\",\n  \"form.attribute.item.minimumLength\": \"En düşük uzunluk\",\n  \"form.attribute.item.number.type\": \"Sayı biçimi\",\n  \"form.attribute.item.number.type.decimal\": \"ondalık (ex: 2.22)\",\n  \"form.attribute.item.number.type.float\": \"float (ex: 3.33333333)\",\n  \"form.attribute.item.number.type.integer\": \"tamsayı (ex: 10)\",\n  \"form.attribute.item.privateField\": \"Gizli alan\",\n  \"form.attribute.item.privateField.description\": \"Bu alan API yanıtında görünmez\",\n  \"form.attribute.item.requiredField\": \"Zorunlu alan\",\n  \"form.attribute.item.requiredField.description\": \"Bu alan boşsa kayıt oluşturamazsınız\",\n  \"form.attribute.item.text.regex\": \"RegExp ifadesi\",\n  \"form.attribute.item.text.regex.description\": \"RegExp ifadesi\",\n  \"form.attribute.item.uniqueField\": \"Benzersiz alan\",\n  \"form.attribute.item.uniqueField.description\": \"Aynı içeriğe sahip bir kayıt varsa kayıt oluşturamazsınız.\",\n  \"form.attribute.settings.default\": \"Varsayılan değer\",\n  \"form.button.add-components-to-dynamiczone\": \"Bölgeye bileşenleri ekle\",\n  \"form.button.add-field\": \"Başka bir alan ekle\",\n  \"form.button.add-first-field-to-created-component\": \"İlk alanı bileşene ekle\",\n  \"form.button.add.field.to.collectionType\": \"Bu koleksiyon tipine başka bir alan ekle\",\n  \"form.button.add.field.to.component\": \"Bu bileşene başka bir alan ekle\",\n  \"form.button.add.field.to.contentType\": \"Bu içerik tipine başka bir alan ekle\",\n  \"form.button.add.field.to.singleType\": \"Bu tekil tipe başka bir alan ekle\",\n  \"form.button.cancel\": \"İptal\",\n  \"form.button.collection-type.description\": \"Çoklu kayıtlar (makaleler, ürünler, yorumlar, v.b.) için ideal\",\n  \"form.button.collection-type.name\": \"Koleksiyon Tipi\",\n  \"form.button.configure-component\": \"Bileşeni ayarla\",\n  \"form.button.configure-view\": \"Görünümü ayarla\",\n  \"form.button.select-component\": \"Bir bileşen seç\",\n  \"form.button.single-type.description\": \"Tekil kayıtlar (hakkımızda, ana sayfa, v.b.) için ideal\",\n  \"form.button.single-type.name\": \"Tekil Tip\",\n  from,\n  \"listView.headerLayout.description\": \"İçeriğinin veri mimarisini kur\",\n  \"menu.section.components.name\": \"Bileşenler\",\n  \"menu.section.models.name\": \"Koleksiyon Tipleri\",\n  \"menu.section.single-types.name\": \"Tekil Tipler\",\n  \"modalForm.attribute.form.base.name.description\": \"Nitelik adında boşluk olamaz\",\n  \"modalForm.attribute.form.base.name.placeholder\": \"ör. slug, seoUrl, canonicalUrl\",\n  \"modalForm.attribute.target-field\": \"İliştirilmiş alan\",\n  \"modalForm.attributes.select-component\": \"Bir bileşen seç\",\n  \"modalForm.attributes.select-components\": \"Bileşenleri seç\",\n  \"modalForm.collectionType.header-create\": \"Bir koleksiyon tipi oluştur\",\n  \"modalForm.component.header-create\": \"Bir bileşen oluştur\",\n  \"modalForm.components.create-component.category.label\": \"Kategori seç ya da yeni bir tane oluşturmak için isim gir\",\n  \"modalForm.components.icon.label\": \"İkon\",\n  \"modalForm.custom-fields.advanced.settings.extended\": \"Gelişmiş ayarlar\",\n  \"modalForm.editCategory.base.name.description\": \"Kategori adında boşluk olamaz\",\n  \"modalForm.empty.button\": \"Özel alanlar ekle\",\n  \"modalForm.empty.heading\": \"Burada henüz bir şey yok.\",\n  \"modalForm.empty.sub-heading\": \"Geniş yelpazedeki uzantılar ile aradığını bul.\",\n  \"modalForm.header-edit\": \"Düzenle: {name}\",\n  \"modalForm.header.categories\": \"Kategoriler\",\n  \"modalForm.header.back\": \"Geri\",\n  \"modalForm.singleType.header-create\": \"Bir tekil tip oluştur\",\n  \"modalForm.sub-header.addComponentToDynamicZone\": \"Dinamik bölgeye yeni bileşen ekle\",\n  \"modalForm.sub-header.attribute.create\": \"Yeni {type} alanı ekle\",\n  \"modalForm.sub-header.attribute.create.step\": \"Yeni bileşen ekle ({step}/2)\",\n  \"modalForm.sub-header.attribute.edit\": \"Düzenle: {name}\",\n  \"modalForm.sub-header.chooseAttribute.collectionType\": \"Koleksiyon tipin için bir alan seç\",\n  \"modalForm.sub-header.chooseAttribute.component\": \"Bileşenin için bir alan seç\",\n  \"modalForm.sub-header.chooseAttribute.singleType\": \"Tekil tipin için bir alan seç\",\n  \"modalForm.tabs.custom\": \"Özel\",\n  \"modalForm.tabs.custom.howToLink\": \"Nasıl özel alan eklenir\",\n  \"modalForm.tabs.default\": \"Varsayılan\",\n  \"modalForm.tabs.label\": \"Varsayılan ve Özel tip sekmeleri\",\n  \"modelPage.attribute.relationWith\": \"İlişkili\",\n  \"notification.error.dynamiczone-min.validation\": \"Bir içerik tipini kaydetmek için dinamik bölgede en az bir bileşen olması gereklidir\",\n  \"notification.info.autoreaload-disable\": \"Bu eklentinin kullanılabilmesi için otomatik yeniden yükleme (autoReload) özelliği gereklidir. Sunucunu `strapi develop` ile başlat\",\n  \"notification.info.creating.notSaved\": \"Yeni bir koleksiyon tipi ya da bileşen oluşturmadan önce yaptıklarını kaydet\",\n  \"plugin.description.long\": \"APInin veri yapısını modelle. Sadece bir iki dakikada yeni alanlar ve ilişkiler oluştur. Projendeki dosyalar otomatik olarak oluşturulur ve güncellenir.\",\n  \"plugin.description.short\": \"APInin veri yapısını modelle.\",\n  \"plugin.name\": \"İçerik Tipi Kurucusu\",\n  \"popUpForm.navContainer.advanced\": \"Gelişmiş Ayarlar\",\n  \"popUpForm.navContainer.base\": \"Temel ayarlar\",\n  \"popUpWarning.bodyMessage.cancel-modifications\": \"Değişikliklerini iptal etmek istediğinden emin misin?\",\n  \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Değişikliklerini iptal etmek istediğinden emin misin? Bazı bileşenler oluşturuldu ya da değiştirildi.\",\n  \"popUpWarning.bodyMessage.category.delete\": \"Bu kategoriyi silmek istediğinden emin misin? Tüm bileşenler de silinecek.\",\n  \"popUpWarning.bodyMessage.component.delete\": \"Bu bileşeni silmek istediğinden emin misin?\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"Bu İçerik Türünü silmek istediğinizden emin misiniz?\",\n  \"popUpWarning.draft-publish.button.confirm\": \"Evet, devredışı bırak\",\n  \"popUpWarning.draft-publish.message\": \"Taslak/Yayınla sistemini devredışı bırakırsan taslakların silinecek.\",\n  \"popUpWarning.draft-publish.second-message\": \"Devredışı bırakmak istediğinden emin misin?\",\n  \"prompt.unsaved\": \"Çıkmak istediğinden emin misin? Tüm değişikliklerin kaybolacak.\",\n  \"relation.attributeName.placeholder\": \"Örnek: yazar, katagori, etiket\",\n  \"relation.manyToMany\": \"birçok kişiye ait ve ait\",\n  \"relation.manyToOne\": \"Birçok var\",\n  \"relation.manyWay\": \"çok yönlü\",\n  \"relation.oneToMany\": \"Birçoğuna ait\",\n  \"relation.oneToOne\": \"biri var\",\n  \"relation.oneWay\": \"tek yönlü\",\n  \"table.button.no-fields\": \"Yeni alan ekle\",\n  \"table.content.create-first-content-type\": \"İlk Koleksiyon-Tipini oluştur\",\n  \"table.content.no-fields.collection-type\": \"Bu Koleksiyon-Tipine ile alanını ekle\",\n  \"table.content.no-fields.component\": \"Bu bileşene ilk alanını ekle\"\n};\nexport {\n  configurations,\n  tr as default,\n  from\n};\n//# sourceMappingURL=tr-DsUerr-c.mjs.map\n"], "mappings": ";;;AAAA,IAAM,iBAAiB;AACvB,IAAM,OAAO;AACb,IAAM,KAAK;AAAA,EACT,0CAA0C;AAAA,EAC1C,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,yBAAyB;AAAA,EACzB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,wCAAwC;AAAA,EACxC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,sCAAsC;AAAA,EACtC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,0CAA0C;AAAA,EAC1C,sCAAsC;AAAA,EACtC,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,mCAAmC;AAAA,EACnC,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC,gCAAgC;AAAA,EAChC;AAAA,EACA,qCAAqC;AAAA,EACrC,gCAAgC;AAAA,EAChC,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,yBAAyB;AAAA,EACzB,mCAAmC;AAAA,EACnC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AAAA,EACnB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,qCAAqC;AACvC;", "names": []}