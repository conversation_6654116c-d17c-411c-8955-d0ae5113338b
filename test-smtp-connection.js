/**
 * SMTP 连接测试脚本
 * 用于测试服务器是否能连接到 163 邮箱 SMTP 服务器
 */

const nodemailer = require('nodemailer');

// 从环境变量读取配置
const config = {
  host: process.env.SMTP_HOST || 'smtp.163.com',
  port: parseInt(process.env.SMTP_PORT) || 465,
  secure: true, // 使用 SSL
  auth: {
    user: process.env.SMTP_USERNAME || '<EMAIL>',
    pass: process.env.SMTP_PASSWORD || 'AJjSX5ptg3BVPJhp',
  },
  connectionTimeout: 60000,
  greetingTimeout: 30000,
  socketTimeout: 60000,
  tls: {
    rejectUnauthorized: false
  }
};

console.log('🔧 测试 SMTP 连接配置:');
console.log(`主机: ${config.host}`);
console.log(`端口: ${config.port}`);
console.log(`用户: ${config.auth.user}`);
console.log(`SSL: ${config.secure}`);
console.log('=' * 50);

async function testSMTPConnection() {
  try {
    console.log('📡 创建 SMTP 传输器...');
    const transporter = nodemailer.createTransporter(config);

    console.log('🔍 验证 SMTP 连接...');
    const isValid = await transporter.verify();
    
    if (isValid) {
      console.log('✅ SMTP 连接成功！');
      
      // 尝试发送测试邮件
      console.log('📧 发送测试邮件...');
      const info = await transporter.sendMail({
        from: config.auth.user,
        to: config.auth.user, // 发送给自己
        subject: 'SMTP 连接测试',
        text: '这是一封测试邮件，用于验证 SMTP 连接是否正常。',
        html: '<p>这是一封测试邮件，用于验证 SMTP 连接是否正常。</p>'
      });
      
      console.log('✅ 测试邮件发送成功！');
      console.log('邮件ID:', info.messageId);
      
    } else {
      console.log('❌ SMTP 连接验证失败');
    }
    
  } catch (error) {
    console.log('❌ SMTP 连接测试失败:');
    console.error('错误详情:', error.message);
    
    // 提供具体的错误分析
    if (error.code === 'ECONNREFUSED') {
      console.log('🔍 分析: 连接被拒绝，可能是:');
      console.log('  - 防火墙阻止了连接');
      console.log('  - SMTP 服务器地址或端口错误');
      console.log('  - 网络连接问题');
    } else if (error.code === 'ENOTFOUND') {
      console.log('🔍 分析: 无法解析主机名，可能是:');
      console.log('  - DNS 解析问题');
      console.log('  - SMTP 服务器地址错误');
    } else if (error.responseCode === 535) {
      console.log('🔍 分析: 认证失败，可能是:');
      console.log('  - 用户名或密码错误');
      console.log('  - 需要开启客户端授权密码');
    }
  }
}

// 运行测试
testSMTPConnection();
