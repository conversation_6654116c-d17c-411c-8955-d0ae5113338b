{"version": 3, "sources": ["../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/components/createFormattedComponent.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/components/useIntl.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/components/injectIntl.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/utils.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/ecma402-abstract/lib/262.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/ecma402-abstract/lib/IsSanctionedSimpleUnitIdentifier.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/ecma402-abstract/lib/utils.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/ecma402-abstract/lib/regex.generated.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/ecma402-abstract/lib/NumberFormat/format_to_parts.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/ecma402-abstract/lib/data.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/ecma402-abstract/lib/types/date-time.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-messageformat-parser/lib/error.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-messageformat-parser/lib/types.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-messageformat-parser/lib/regex.generated.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-skeleton-parser/lib/date-time.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-skeleton-parser/lib/regex.generated.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-skeleton-parser/lib/number.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-messageformat-parser/lib/time-data.generated.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-messageformat-parser/lib/date-time-pattern-generator.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-messageformat-parser/lib/parser.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/icu-messageformat-parser/lib/index.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/fast-memoize/lib/index.js", "../../../@strapi/plugin-cloud/node_modules/intl-messageformat/lib/src/error.js", "../../../@strapi/plugin-cloud/node_modules/intl-messageformat/lib/src/formatters.js", "../../../@strapi/plugin-cloud/node_modules/intl-messageformat/lib/src/core.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/error.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/utils.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/message.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/dateTime.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/displayName.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/list.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/plural.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/relativeTime.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/number.js", "../../../@strapi/plugin-cloud/node_modules/@formatjs/intl/lib/src/create-intl.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/components/provider.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/components/createIntl.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/components/relative.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/components/plural.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/components/message.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/src/components/dateTimeRange.js", "../../../@strapi/plugin-cloud/node_modules/react-intl/lib/index.js", "../../../@strapi/plugin-cloud/admin/src/utils/getTrad.ts", "../../../@strapi/plugin-cloud/admin/src/components/Icons/UploadIcon.tsx", "../../../@strapi/plugin-cloud/admin/src/components/Cloud.tsx", "../../../@strapi/plugin-cloud/admin/src/components/Icons/LinkIcon.tsx", "../../../@strapi/plugin-cloud/admin/src/components/Github.tsx", "../../../@strapi/plugin-cloud/admin/src/pages/assets/corner-ornament.svg", "../../../@strapi/plugin-cloud/admin/src/pages/assets/left-side-cloud.png", "../../../@strapi/plugin-cloud/admin/src/pages/assets/right-side-cloud.png", "../../../@strapi/plugin-cloud/admin/src/pages/HomePage.tsx", "../../../@strapi/plugin-cloud/admin/src/pages/App.tsx"], "sourcesContent": ["import { __rest } from \"tslib\";\nimport * as React from 'react';\nimport useIntl from './useIntl';\nvar DisplayName;\n(function (DisplayName) {\n    DisplayName[\"formatDate\"] = \"FormattedDate\";\n    DisplayName[\"formatTime\"] = \"FormattedTime\";\n    DisplayName[\"formatNumber\"] = \"FormattedNumber\";\n    DisplayName[\"formatList\"] = \"FormattedList\";\n    // Note that this DisplayName is the locale display name, not to be confused with\n    // the name of the enum, which is for React component display name in dev tools.\n    DisplayName[\"formatDisplayName\"] = \"FormattedDisplayName\";\n})(DisplayName || (DisplayName = {}));\nvar DisplayNameParts;\n(function (DisplayNameParts) {\n    DisplayNameParts[\"formatDate\"] = \"FormattedDateParts\";\n    DisplayNameParts[\"formatTime\"] = \"FormattedTimeParts\";\n    DisplayNameParts[\"formatNumber\"] = \"FormattedNumberParts\";\n    DisplayNameParts[\"formatList\"] = \"FormattedListParts\";\n})(DisplayNameParts || (DisplayNameParts = {}));\nexport var FormattedNumberParts = function (props) {\n    var intl = useIntl();\n    var value = props.value, children = props.children, formatProps = __rest(props, [\"value\", \"children\"]);\n    return children(intl.formatNumberToParts(value, formatProps));\n};\nFormattedNumberParts.displayName = 'FormattedNumberParts';\nexport var FormattedListParts = function (props) {\n    var intl = useIntl();\n    var value = props.value, children = props.children, formatProps = __rest(props, [\"value\", \"children\"]);\n    return children(intl.formatListToParts(value, formatProps));\n};\nFormattedNumberParts.displayName = 'FormattedNumberParts';\nexport function createFormattedDateTimePartsComponent(name) {\n    var ComponentParts = function (props) {\n        var intl = useIntl();\n        var value = props.value, children = props.children, formatProps = __rest(props, [\"value\", \"children\"]);\n        var date = typeof value === 'string' ? new Date(value || 0) : value;\n        var formattedParts = name === 'formatDate'\n            ? intl.formatDateToParts(date, formatProps)\n            : intl.formatTimeToParts(date, formatProps);\n        return children(formattedParts);\n    };\n    ComponentParts.displayName = DisplayNameParts[name];\n    return ComponentParts;\n}\nexport function createFormattedComponent(name) {\n    var Component = function (props) {\n        var intl = useIntl();\n        var value = props.value, children = props.children, formatProps = __rest(props\n        // TODO: fix TS type definition for localeMatcher upstream\n        , [\"value\", \"children\"]);\n        // TODO: fix TS type definition for localeMatcher upstream\n        var formattedValue = intl[name](value, formatProps);\n        if (typeof children === 'function') {\n            return children(formattedValue);\n        }\n        var Text = intl.textComponent || React.Fragment;\n        return React.createElement(Text, null, formattedValue);\n    };\n    Component.displayName = DisplayName[name];\n    return Component;\n}\n", "import * as React from 'react';\nimport { Context } from './injectIntl';\nimport { invariantIntlContext } from '../utils';\nexport default function useIntl() {\n    var intl = React.useContext(Context);\n    invariantIntlContext(intl);\n    return intl;\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport hoistNonReactStatics from 'hoist-non-react-statics';\nimport { invariantIntlContext } from '../utils';\nfunction getDisplayName(Component) {\n    return Component.displayName || Component.name || 'Component';\n}\n// This is primarily dealing with packaging systems where multiple copies of react-intl\n// might exist\nvar IntlContext = typeof window !== 'undefined' && !window.__REACT_INTL_BYPASS_GLOBAL_CONTEXT__\n    ? window.__REACT_INTL_CONTEXT__ ||\n        (window.__REACT_INTL_CONTEXT__ = React.createContext(null))\n    : React.createContext(null);\nvar IntlConsumer = IntlContext.Consumer, IntlProvider = IntlContext.Provider;\nexport var Provider = IntlProvider;\nexport var Context = IntlContext;\nexport default function injectIntl(WrappedComponent, options) {\n    var _a = options || {}, _b = _a.intlPropName, intlPropName = _b === void 0 ? 'intl' : _b, _c = _a.forwardRef, forwardRef = _c === void 0 ? false : _c, _d = _a.enforceContext, enforceContext = _d === void 0 ? true : _d;\n    var WithIntl = function (props) { return (React.createElement(IntlConsumer, null, function (intl) {\n        var _a;\n        if (enforceContext) {\n            invariantIntlContext(intl);\n        }\n        var intlProp = (_a = {}, _a[intlPropName] = intl, _a);\n        return (React.createElement(WrappedComponent, __assign({}, props, intlProp, { ref: forwardRef ? props.forwardedRef : null })));\n    })); };\n    WithIntl.displayName = \"injectIntl(\".concat(getDisplayName(WrappedComponent), \")\");\n    WithIntl.WrappedComponent = WrappedComponent;\n    if (forwardRef) {\n        return hoistNonReactStatics(React.forwardRef(function (props, ref) { return (React.createElement(WithIntl, __assign({}, props, { forwardedRef: ref }))); }), WrappedComponent);\n    }\n    return hoistNonReactStatics(WithIntl, WrappedComponent);\n}\n", "import { __assign } from \"tslib\";\nimport * as React from 'react';\nimport { invariant } from '@formatjs/ecma402-abstract';\nimport { DEFAULT_INTL_CONFIG as CORE_DEFAULT_INTL_CONFIG } from '@formatjs/intl';\nexport function invariantIntlContext(intl) {\n    invariant(intl, '[React Intl] Could not find required `intl` object. ' +\n        '<IntlProvider> needs to exist in the component ancestry.');\n}\nexport var DEFAULT_INTL_CONFIG = __assign(__assign({}, CORE_DEFAULT_INTL_CONFIG), { textComponent: React.Fragment });\n/**\n * Takes a `formatXMLElementFn`, and composes it in function, which passes\n * argument `parts` through, assigning unique key to each part, to prevent\n * \"Each child in a list should have a unique \"key\"\" React error.\n * @param formatXMLElementFn\n */\nexport function assignUniqueKeysToParts(formatXMLElementFn) {\n    return function (parts) {\n        // eslint-disable-next-line prefer-rest-params\n        return formatXMLElementFn(React.Children.toArray(parts));\n    };\n}\nexport function shallowEqual(objA, objB) {\n    if (objA === objB) {\n        return true;\n    }\n    if (!objA || !objB) {\n        return false;\n    }\n    var aKeys = Object.keys(objA);\n    var bKeys = Object.keys(objB);\n    var len = aKeys.length;\n    if (bKeys.length !== len) {\n        return false;\n    }\n    for (var i = 0; i < len; i++) {\n        var key = aKeys[i];\n        if (objA[key] !== objB[key] ||\n            !Object.prototype.hasOwnProperty.call(objB, key)) {\n            return false;\n        }\n    }\n    return true;\n}\n", "/**\n * https://tc39.es/ecma262/#sec-tostring\n */\nexport function ToString(o) {\n    // Only symbol is irregular...\n    if (typeof o === 'symbol') {\n        throw TypeError('Cannot convert a Symbol value to a string');\n    }\n    return String(o);\n}\n/**\n * https://tc39.es/ecma262/#sec-tonumber\n * @param val\n */\nexport function ToNumber(val) {\n    if (val === undefined) {\n        return NaN;\n    }\n    if (val === null) {\n        return +0;\n    }\n    if (typeof val === 'boolean') {\n        return val ? 1 : +0;\n    }\n    if (typeof val === 'number') {\n        return val;\n    }\n    if (typeof val === 'symbol' || typeof val === 'bigint') {\n        throw new TypeError('Cannot convert symbol/bigint to number');\n    }\n    return Number(val);\n}\n/**\n * https://tc39.es/ecma262/#sec-tointeger\n * @param n\n */\nfunction ToInteger(n) {\n    var number = ToNumber(n);\n    if (isNaN(number) || SameValue(number, -0)) {\n        return 0;\n    }\n    if (isFinite(number)) {\n        return number;\n    }\n    var integer = Math.floor(Math.abs(number));\n    if (number < 0) {\n        integer = -integer;\n    }\n    if (SameValue(integer, -0)) {\n        return 0;\n    }\n    return integer;\n}\n/**\n * https://tc39.es/ecma262/#sec-timeclip\n * @param time\n */\nexport function TimeClip(time) {\n    if (!isFinite(time)) {\n        return NaN;\n    }\n    if (Math.abs(time) > 8.64 * 1e15) {\n        return NaN;\n    }\n    return ToInteger(time);\n}\n/**\n * https://tc39.es/ecma262/#sec-toobject\n * @param arg\n */\nexport function ToObject(arg) {\n    if (arg == null) {\n        throw new TypeError('undefined/null cannot be converted to object');\n    }\n    return Object(arg);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-samevalue\n * @param x\n * @param y\n */\nexport function SameValue(x, y) {\n    if (Object.is) {\n        return Object.is(x, y);\n    }\n    // SameValue algorithm\n    if (x === y) {\n        // Steps 1-5, 7-10\n        // Steps 6.b-6.e: +0 != -0\n        return x !== 0 || 1 / x === 1 / y;\n    }\n    // Step 6.a: NaN == NaN\n    return x !== x && y !== y;\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-arraycreate\n * @param len\n */\nexport function ArrayCreate(len) {\n    return new Array(len);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-hasownproperty\n * @param o\n * @param prop\n */\nexport function HasOwnProperty(o, prop) {\n    return Object.prototype.hasOwnProperty.call(o, prop);\n}\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#sec-type\n * @param x\n */\nexport function Type(x) {\n    if (x === null) {\n        return 'Null';\n    }\n    if (typeof x === 'undefined') {\n        return 'Undefined';\n    }\n    if (typeof x === 'function' || typeof x === 'object') {\n        return 'Object';\n    }\n    if (typeof x === 'number') {\n        return 'Number';\n    }\n    if (typeof x === 'boolean') {\n        return 'Boolean';\n    }\n    if (typeof x === 'string') {\n        return 'String';\n    }\n    if (typeof x === 'symbol') {\n        return 'Symbol';\n    }\n    if (typeof x === 'bigint') {\n        return 'BigInt';\n    }\n}\nvar MS_PER_DAY = 86400000;\n/**\n * https://www.ecma-international.org/ecma-262/11.0/index.html#eqn-modulo\n * @param x\n * @param y\n * @return k of the same sign as y\n */\nfunction mod(x, y) {\n    return x - Math.floor(x / y) * y;\n}\n/**\n * https://tc39.es/ecma262/#eqn-Day\n * @param t\n */\nexport function Day(t) {\n    return Math.floor(t / MS_PER_DAY);\n}\n/**\n * https://tc39.es/ecma262/#sec-week-day\n * @param t\n */\nexport function WeekDay(t) {\n    return mod(Day(t) + 4, 7);\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param y\n */\nexport function DayFromYear(y) {\n    return Date.UTC(y, 0) / MS_PER_DAY;\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param y\n */\nexport function TimeFromYear(y) {\n    return Date.UTC(y, 0);\n}\n/**\n * https://tc39.es/ecma262/#sec-year-number\n * @param t\n */\nexport function YearFromTime(t) {\n    return new Date(t).getUTCFullYear();\n}\nexport function DaysInYear(y) {\n    if (y % 4 !== 0) {\n        return 365;\n    }\n    if (y % 100 !== 0) {\n        return 366;\n    }\n    if (y % 400 !== 0) {\n        return 365;\n    }\n    return 366;\n}\nexport function DayWithinYear(t) {\n    return Day(t) - DayFromYear(YearFromTime(t));\n}\nexport function InLeapYear(t) {\n    return DaysInYear(YearFromTime(t)) === 365 ? 0 : 1;\n}\n/**\n * https://tc39.es/ecma262/#sec-month-number\n * @param t\n */\nexport function MonthFromTime(t) {\n    var dwy = DayWithinYear(t);\n    var leap = InLeapYear(t);\n    if (dwy >= 0 && dwy < 31) {\n        return 0;\n    }\n    if (dwy < 59 + leap) {\n        return 1;\n    }\n    if (dwy < 90 + leap) {\n        return 2;\n    }\n    if (dwy < 120 + leap) {\n        return 3;\n    }\n    if (dwy < 151 + leap) {\n        return 4;\n    }\n    if (dwy < 181 + leap) {\n        return 5;\n    }\n    if (dwy < 212 + leap) {\n        return 6;\n    }\n    if (dwy < 243 + leap) {\n        return 7;\n    }\n    if (dwy < 273 + leap) {\n        return 8;\n    }\n    if (dwy < 304 + leap) {\n        return 9;\n    }\n    if (dwy < 334 + leap) {\n        return 10;\n    }\n    if (dwy < 365 + leap) {\n        return 11;\n    }\n    throw new Error('Invalid time');\n}\nexport function DateFromTime(t) {\n    var dwy = DayWithinYear(t);\n    var mft = MonthFromTime(t);\n    var leap = InLeapYear(t);\n    if (mft === 0) {\n        return dwy + 1;\n    }\n    if (mft === 1) {\n        return dwy - 30;\n    }\n    if (mft === 2) {\n        return dwy - 58 - leap;\n    }\n    if (mft === 3) {\n        return dwy - 89 - leap;\n    }\n    if (mft === 4) {\n        return dwy - 119 - leap;\n    }\n    if (mft === 5) {\n        return dwy - 150 - leap;\n    }\n    if (mft === 6) {\n        return dwy - 180 - leap;\n    }\n    if (mft === 7) {\n        return dwy - 211 - leap;\n    }\n    if (mft === 8) {\n        return dwy - 242 - leap;\n    }\n    if (mft === 9) {\n        return dwy - 272 - leap;\n    }\n    if (mft === 10) {\n        return dwy - 303 - leap;\n    }\n    if (mft === 11) {\n        return dwy - 333 - leap;\n    }\n    throw new Error('Invalid time');\n}\nvar HOURS_PER_DAY = 24;\nvar MINUTES_PER_HOUR = 60;\nvar SECONDS_PER_MINUTE = 60;\nvar MS_PER_SECOND = 1e3;\nvar MS_PER_MINUTE = MS_PER_SECOND * SECONDS_PER_MINUTE;\nvar MS_PER_HOUR = MS_PER_MINUTE * MINUTES_PER_HOUR;\nexport function HourFromTime(t) {\n    return mod(Math.floor(t / MS_PER_HOUR), HOURS_PER_DAY);\n}\nexport function MinFromTime(t) {\n    return mod(Math.floor(t / MS_PER_MINUTE), MINUTES_PER_HOUR);\n}\nexport function SecFromTime(t) {\n    return mod(Math.floor(t / MS_PER_SECOND), SECONDS_PER_MINUTE);\n}\nfunction IsCallable(fn) {\n    return typeof fn === 'function';\n}\n/**\n * The abstract operation OrdinaryHasInstance implements\n * the default algorithm for determining if an object O\n * inherits from the instance object inheritance path\n * provided by constructor C.\n * @param C class\n * @param O object\n * @param internalSlots internalSlots\n */\nexport function OrdinaryHasInstance(C, O, internalSlots) {\n    if (!IsCallable(C)) {\n        return false;\n    }\n    if (internalSlots === null || internalSlots === void 0 ? void 0 : internalSlots.boundTargetFunction) {\n        var BC = internalSlots === null || internalSlots === void 0 ? void 0 : internalSlots.boundTargetFunction;\n        return O instanceof BC;\n    }\n    if (typeof O !== 'object') {\n        return false;\n    }\n    var P = C.prototype;\n    if (typeof P !== 'object') {\n        throw new TypeError('OrdinaryHasInstance called on an object with an invalid prototype property.');\n    }\n    return Object.prototype.isPrototypeOf.call(P, O);\n}\nexport function msFromTime(t) {\n    return mod(t, MS_PER_SECOND);\n}\n", "/**\n * https://tc39.es/ecma402/#table-sanctioned-simple-unit-identifiers\n */\nexport var SANCTIONED_UNITS = [\n    'angle-degree',\n    'area-acre',\n    'area-hectare',\n    'concentr-percent',\n    'digital-bit',\n    'digital-byte',\n    'digital-gigabit',\n    'digital-gigabyte',\n    'digital-kilobit',\n    'digital-kilobyte',\n    'digital-megabit',\n    'digital-megabyte',\n    'digital-petabyte',\n    'digital-terabit',\n    'digital-terabyte',\n    'duration-day',\n    'duration-hour',\n    'duration-millisecond',\n    'duration-minute',\n    'duration-month',\n    'duration-second',\n    'duration-week',\n    'duration-year',\n    'length-centimeter',\n    'length-foot',\n    'length-inch',\n    'length-kilometer',\n    'length-meter',\n    'length-mile-scandinavian',\n    'length-mile',\n    'length-millimeter',\n    'length-yard',\n    'mass-gram',\n    'mass-kilogram',\n    'mass-ounce',\n    'mass-pound',\n    'mass-stone',\n    'temperature-celsius',\n    'temperature-fahrenheit',\n    'volume-fluid-ounce',\n    'volume-gallon',\n    'volume-liter',\n    'volume-milliliter',\n];\n// In CLDR, the unit name always follows the form `namespace-unit` pattern.\n// For example: `digital-bit` instead of `bit`. This function removes the namespace prefix.\nexport function removeUnitNamespace(unit) {\n    return unit.slice(unit.indexOf('-') + 1);\n}\n/**\n * https://tc39.es/ecma402/#table-sanctioned-simple-unit-identifiers\n */\nexport var SIMPLE_UNITS = SANCTIONED_UNITS.map(removeUnitNamespace);\n/**\n * https://tc39.es/ecma402/#sec-issanctionedsimpleunitidentifier\n */\nexport function IsSanctionedSimpleUnitIdentifier(unitIdentifier) {\n    return SIMPLE_UNITS.indexOf(unitIdentifier) > -1;\n}\n", "/**\n * Cannot do Math.log(x) / Math.log(10) bc if IEEE floating point issue\n * @param x number\n */\nexport function getMagnitude(x) {\n    // Cannot count string length via Number.toString because it may use scientific notation\n    // for very small or very large numbers.\n    return Math.floor(Math.log(x) * Math.LOG10E);\n}\nexport function repeat(s, times) {\n    if (typeof s.repeat === 'function') {\n        return s.repeat(times);\n    }\n    var arr = new Array(times);\n    for (var i = 0; i < arr.length; i++) {\n        arr[i] = s;\n    }\n    return arr.join('');\n}\nexport function setInternalSlot(map, pl, field, value) {\n    if (!map.get(pl)) {\n        map.set(pl, Object.create(null));\n    }\n    var slots = map.get(pl);\n    slots[field] = value;\n}\nexport function setMultiInternalSlots(map, pl, props) {\n    for (var _i = 0, _a = Object.keys(props); _i < _a.length; _i++) {\n        var k = _a[_i];\n        setInternalSlot(map, pl, k, props[k]);\n    }\n}\nexport function getInternalSlot(map, pl, field) {\n    return getMultiInternalSlots(map, pl, field)[field];\n}\nexport function getMultiInternalSlots(map, pl) {\n    var fields = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        fields[_i - 2] = arguments[_i];\n    }\n    var slots = map.get(pl);\n    if (!slots) {\n        throw new TypeError(\"\".concat(pl, \" InternalSlot has not been initialized\"));\n    }\n    return fields.reduce(function (all, f) {\n        all[f] = slots[f];\n        return all;\n    }, Object.create(null));\n}\nexport function isLiteralPart(patternPart) {\n    return patternPart.type === 'literal';\n}\n/*\n  17 ECMAScript Standard Built-in Objects:\n    Every built-in Function object, including constructors, that is not\n    identified as an anonymous function has a name property whose value\n    is a String.\n\n    Unless otherwise specified, the name property of a built-in Function\n    object, if it exists, has the attributes { [[Writable]]: false,\n    [[Enumerable]]: false, [[Configurable]]: true }.\n*/\nexport function defineProperty(target, name, _a) {\n    var value = _a.value;\n    Object.defineProperty(target, name, {\n        configurable: true,\n        enumerable: false,\n        writable: true,\n        value: value,\n    });\n}\nexport var UNICODE_EXTENSION_SEQUENCE_REGEX = /-u(?:-[0-9a-z]{2,8})+/gi;\nexport function invariant(condition, message, Err) {\n    if (Err === void 0) { Err = Error; }\n    if (!condition) {\n        throw new Err(message);\n    }\n}\n", "// @generated from regex-gen.ts\nexport var S_UNICODE_REGEX = /[\\$\\+<->\\^`\\|~\\xA2-\\xA6\\xA8\\xA9\\xAC\\xAE-\\xB1\\xB4\\xB8\\xD7\\xF7\\u02C2-\\u02C5\\u02D2-\\u02DF\\u02E5-\\u02EB\\u02ED\\u02EF-\\u02FF\\u0375\\u0384\\u0385\\u03F6\\u0482\\u058D-\\u058F\\u0606-\\u0608\\u060B\\u060E\\u060F\\u06DE\\u06E9\\u06FD\\u06FE\\u07F6\\u07FE\\u07FF\\u09F2\\u09F3\\u09FA\\u09FB\\u0AF1\\u0B70\\u0BF3-\\u0BFA\\u0C7F\\u0D4F\\u0D79\\u0E3F\\u0F01-\\u0F03\\u0F13\\u0F15-\\u0F17\\u0F1A-\\u0F1F\\u0F34\\u0F36\\u0F38\\u0FBE-\\u0FC5\\u0FC7-\\u0FCC\\u0FCE\\u0FCF\\u0FD5-\\u0FD8\\u109E\\u109F\\u1390-\\u1399\\u166D\\u17DB\\u1940\\u19DE-\\u19FF\\u1B61-\\u1B6A\\u1B74-\\u1B7C\\u1FBD\\u1FBF-\\u1FC1\\u1FCD-\\u1FCF\\u1FDD-\\u1FDF\\u1FED-\\u1FEF\\u1FFD\\u1FFE\\u2044\\u2052\\u207A-\\u207C\\u208A-\\u208C\\u20A0-\\u20BF\\u2100\\u2101\\u2103-\\u2106\\u2108\\u2109\\u2114\\u2116-\\u2118\\u211E-\\u2123\\u2125\\u2127\\u2129\\u212E\\u213A\\u213B\\u2140-\\u2144\\u214A-\\u214D\\u214F\\u218A\\u218B\\u2190-\\u2307\\u230C-\\u2328\\u232B-\\u2426\\u2440-\\u244A\\u249C-\\u24E9\\u2500-\\u2767\\u2794-\\u27C4\\u27C7-\\u27E5\\u27F0-\\u2982\\u2999-\\u29D7\\u29DC-\\u29FB\\u29FE-\\u2B73\\u2B76-\\u2B95\\u2B97-\\u2BFF\\u2CE5-\\u2CEA\\u2E50\\u2E51\\u2E80-\\u2E99\\u2E9B-\\u2EF3\\u2F00-\\u2FD5\\u2FF0-\\u2FFB\\u3004\\u3012\\u3013\\u3020\\u3036\\u3037\\u303E\\u303F\\u309B\\u309C\\u3190\\u3191\\u3196-\\u319F\\u31C0-\\u31E3\\u3200-\\u321E\\u322A-\\u3247\\u3250\\u3260-\\u327F\\u328A-\\u32B0\\u32C0-\\u33FF\\u4DC0-\\u4DFF\\uA490-\\uA4C6\\uA700-\\uA716\\uA720\\uA721\\uA789\\uA78A\\uA828-\\uA82B\\uA836-\\uA839\\uAA77-\\uAA79\\uAB5B\\uAB6A\\uAB6B\\uFB29\\uFBB2-\\uFBC1\\uFDFC\\uFDFD\\uFE62\\uFE64-\\uFE66\\uFE69\\uFF04\\uFF0B\\uFF1C-\\uFF1E\\uFF3E\\uFF40\\uFF5C\\uFF5E\\uFFE0-\\uFFE6\\uFFE8-\\uFFEE\\uFFFC\\uFFFD]|\\uD800[\\uDD37-\\uDD3F\\uDD79-\\uDD89\\uDD8C-\\uDD8E\\uDD90-\\uDD9C\\uDDA0\\uDDD0-\\uDDFC]|\\uD802[\\uDC77\\uDC78\\uDEC8]|\\uD805\\uDF3F|\\uD807[\\uDFD5-\\uDFF1]|\\uD81A[\\uDF3C-\\uDF3F\\uDF45]|\\uD82F\\uDC9C|\\uD834[\\uDC00-\\uDCF5\\uDD00-\\uDD26\\uDD29-\\uDD64\\uDD6A-\\uDD6C\\uDD83\\uDD84\\uDD8C-\\uDDA9\\uDDAE-\\uDDE8\\uDE00-\\uDE41\\uDE45\\uDF00-\\uDF56]|\\uD835[\\uDEC1\\uDEDB\\uDEFB\\uDF15\\uDF35\\uDF4F\\uDF6F\\uDF89\\uDFA9\\uDFC3]|\\uD836[\\uDC00-\\uDDFF\\uDE37-\\uDE3A\\uDE6D-\\uDE74\\uDE76-\\uDE83\\uDE85\\uDE86]|\\uD838[\\uDD4F\\uDEFF]|\\uD83B[\\uDCAC\\uDCB0\\uDD2E\\uDEF0\\uDEF1]|\\uD83C[\\uDC00-\\uDC2B\\uDC30-\\uDC93\\uDCA0-\\uDCAE\\uDCB1-\\uDCBF\\uDCC1-\\uDCCF\\uDCD1-\\uDCF5\\uDD0D-\\uDDAD\\uDDE6-\\uDE02\\uDE10-\\uDE3B\\uDE40-\\uDE48\\uDE50\\uDE51\\uDE60-\\uDE65\\uDF00-\\uDFFF]|\\uD83D[\\uDC00-\\uDED7\\uDEE0-\\uDEEC\\uDEF0-\\uDEFC\\uDF00-\\uDF73\\uDF80-\\uDFD8\\uDFE0-\\uDFEB]|\\uD83E[\\uDC00-\\uDC0B\\uDC10-\\uDC47\\uDC50-\\uDC59\\uDC60-\\uDC87\\uDC90-\\uDCAD\\uDCB0\\uDCB1\\uDD00-\\uDD78\\uDD7A-\\uDDCB\\uDDCD-\\uDE53\\uDE60-\\uDE6D\\uDE70-\\uDE74\\uDE78-\\uDE7A\\uDE80-\\uDE86\\uDE90-\\uDEA8\\uDEB0-\\uDEB6\\uDEC0-\\uDEC2\\uDED0-\\uDED6\\uDF00-\\uDF92\\uDF94-\\uDFCA]/;\n", "import { ToRawFixed } from './ToRawFixed';\nimport { digitMapping } from './digit-mapping.generated';\nimport { S_UNICODE_REGEX } from '../regex.generated';\n// This is from: unicode-12.1.0/General_Category/Symbol/regex.js\n// IE11 does not support unicode flag, otherwise this is just /\\p{S}/u.\n// /^\\p{S}/u\nvar CARET_S_UNICODE_REGEX = new RegExp(\"^\".concat(S_UNICODE_REGEX.source));\n// /\\p{S}$/u\nvar S_DOLLAR_UNICODE_REGEX = new RegExp(\"\".concat(S_UNICODE_REGEX.source, \"$\"));\nvar CLDR_NUMBER_PATTERN = /[#0](?:[\\.,][#0]+)*/g;\nexport default function formatToParts(numberResult, data, pl, options) {\n    var sign = numberResult.sign, exponent = numberResult.exponent, magnitude = numberResult.magnitude;\n    var notation = options.notation, style = options.style, numberingSystem = options.numberingSystem;\n    var defaultNumberingSystem = data.numbers.nu[0];\n    // #region Part 1: partition and interpolate the CLDR number pattern.\n    // ----------------------------------------------------------\n    var compactNumberPattern = null;\n    if (notation === 'compact' && magnitude) {\n        compactNumberPattern = getCompactDisplayPattern(numberResult, pl, data, style, options.compactDisplay, options.currencyDisplay, numberingSystem);\n    }\n    // This is used multiple times\n    var nonNameCurrencyPart;\n    if (style === 'currency' && options.currencyDisplay !== 'name') {\n        var byCurrencyDisplay = data.currencies[options.currency];\n        if (byCurrencyDisplay) {\n            switch (options.currencyDisplay) {\n                case 'code':\n                    nonNameCurrencyPart = options.currency;\n                    break;\n                case 'symbol':\n                    nonNameCurrencyPart = byCurrencyDisplay.symbol;\n                    break;\n                default:\n                    nonNameCurrencyPart = byCurrencyDisplay.narrow;\n                    break;\n            }\n        }\n        else {\n            // Fallback for unknown currency\n            nonNameCurrencyPart = options.currency;\n        }\n    }\n    var numberPattern;\n    if (!compactNumberPattern) {\n        // Note: if the style is unit, or is currency and the currency display is name,\n        // its unit parts will be interpolated in part 2. So here we can fallback to decimal.\n        if (style === 'decimal' ||\n            style === 'unit' ||\n            (style === 'currency' && options.currencyDisplay === 'name')) {\n            // Shortcut for decimal\n            var decimalData = data.numbers.decimal[numberingSystem] ||\n                data.numbers.decimal[defaultNumberingSystem];\n            numberPattern = getPatternForSign(decimalData.standard, sign);\n        }\n        else if (style === 'currency') {\n            var currencyData = data.numbers.currency[numberingSystem] ||\n                data.numbers.currency[defaultNumberingSystem];\n            // We replace number pattern part with `0` for easier postprocessing.\n            numberPattern = getPatternForSign(currencyData[options.currencySign], sign);\n        }\n        else {\n            // percent\n            var percentPattern = data.numbers.percent[numberingSystem] ||\n                data.numbers.percent[defaultNumberingSystem];\n            numberPattern = getPatternForSign(percentPattern, sign);\n        }\n    }\n    else {\n        numberPattern = compactNumberPattern;\n    }\n    // Extract the decimal number pattern string. It looks like \"#,##0,00\", which will later be\n    // used to infer decimal group sizes.\n    var decimalNumberPattern = CLDR_NUMBER_PATTERN.exec(numberPattern)[0];\n    // Now we start to substitute patterns\n    // 1. replace strings like `0` and `#,##0.00` with `{0}`\n    // 2. unquote characters (invariant: the quoted characters does not contain the special tokens)\n    numberPattern = numberPattern\n        .replace(CLDR_NUMBER_PATTERN, '{0}')\n        .replace(/'(.)'/g, '$1');\n    // Handle currency spacing (both compact and non-compact).\n    if (style === 'currency' && options.currencyDisplay !== 'name') {\n        var currencyData = data.numbers.currency[numberingSystem] ||\n            data.numbers.currency[defaultNumberingSystem];\n        // See `currencySpacing` substitution rule in TR-35.\n        // Here we always assume the currencyMatch is \"[:^S:]\" and surroundingMatch is \"[:digit:]\".\n        //\n        // Example 1: for pattern \"#,##0.00¤\" with symbol \"US$\", we replace \"¤\" with the symbol,\n        // but insert an extra non-break space before the symbol, because \"[:^S:]\" matches \"U\" in\n        // \"US$\" and \"[:digit:]\" matches the latn numbering system digits.\n        //\n        // Example 2: for pattern \"¤#,##0.00\" with symbol \"US$\", there is no spacing between symbol\n        // and number, because `$` does not match \"[:^S:]\".\n        //\n        // Implementation note: here we do the best effort to infer the insertion.\n        // We also assume that `beforeInsertBetween` and `afterInsertBetween` will never be `;`.\n        var afterCurrency = currencyData.currencySpacing.afterInsertBetween;\n        if (afterCurrency && !S_DOLLAR_UNICODE_REGEX.test(nonNameCurrencyPart)) {\n            numberPattern = numberPattern.replace('¤{0}', \"\\u00A4\".concat(afterCurrency, \"{0}\"));\n        }\n        var beforeCurrency = currencyData.currencySpacing.beforeInsertBetween;\n        if (beforeCurrency && !CARET_S_UNICODE_REGEX.test(nonNameCurrencyPart)) {\n            numberPattern = numberPattern.replace('{0}¤', \"{0}\".concat(beforeCurrency, \"\\u00A4\"));\n        }\n    }\n    // The following tokens are special: `{0}`, `¤`, `%`, `-`, `+`, `{c:...}.\n    var numberPatternParts = numberPattern.split(/({c:[^}]+}|\\{0\\}|[¤%\\-\\+])/g);\n    var numberParts = [];\n    var symbols = data.numbers.symbols[numberingSystem] ||\n        data.numbers.symbols[defaultNumberingSystem];\n    for (var _i = 0, numberPatternParts_1 = numberPatternParts; _i < numberPatternParts_1.length; _i++) {\n        var part = numberPatternParts_1[_i];\n        if (!part) {\n            continue;\n        }\n        switch (part) {\n            case '{0}': {\n                // We only need to handle scientific and engineering notation here.\n                numberParts.push.apply(numberParts, paritionNumberIntoParts(symbols, numberResult, notation, exponent, numberingSystem, \n                // If compact number pattern exists, do not insert group separators.\n                !compactNumberPattern && Boolean(options.useGrouping), decimalNumberPattern));\n                break;\n            }\n            case '-':\n                numberParts.push({ type: 'minusSign', value: symbols.minusSign });\n                break;\n            case '+':\n                numberParts.push({ type: 'plusSign', value: symbols.plusSign });\n                break;\n            case '%':\n                numberParts.push({ type: 'percentSign', value: symbols.percentSign });\n                break;\n            case '¤':\n                // Computed above when handling currency spacing.\n                numberParts.push({ type: 'currency', value: nonNameCurrencyPart });\n                break;\n            default:\n                if (/^\\{c:/.test(part)) {\n                    numberParts.push({\n                        type: 'compact',\n                        value: part.substring(3, part.length - 1),\n                    });\n                }\n                else {\n                    // literal\n                    numberParts.push({ type: 'literal', value: part });\n                }\n                break;\n        }\n    }\n    // #endregion\n    // #region Part 2: interpolate unit pattern if necessary.\n    // ----------------------------------------------\n    switch (style) {\n        case 'currency': {\n            // `currencyDisplay: 'name'` has similar pattern handling as units.\n            if (options.currencyDisplay === 'name') {\n                var unitPattern = (data.numbers.currency[numberingSystem] ||\n                    data.numbers.currency[defaultNumberingSystem]).unitPattern;\n                // Select plural\n                var unitName = void 0;\n                var currencyNameData = data.currencies[options.currency];\n                if (currencyNameData) {\n                    unitName = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), currencyNameData.displayName);\n                }\n                else {\n                    // Fallback for unknown currency\n                    unitName = options.currency;\n                }\n                // Do {0} and {1} substitution\n                var unitPatternParts = unitPattern.split(/(\\{[01]\\})/g);\n                var result = [];\n                for (var _a = 0, unitPatternParts_1 = unitPatternParts; _a < unitPatternParts_1.length; _a++) {\n                    var part = unitPatternParts_1[_a];\n                    switch (part) {\n                        case '{0}':\n                            result.push.apply(result, numberParts);\n                            break;\n                        case '{1}':\n                            result.push({ type: 'currency', value: unitName });\n                            break;\n                        default:\n                            if (part) {\n                                result.push({ type: 'literal', value: part });\n                            }\n                            break;\n                    }\n                }\n                return result;\n            }\n            else {\n                return numberParts;\n            }\n        }\n        case 'unit': {\n            var unit = options.unit, unitDisplay = options.unitDisplay;\n            var unitData = data.units.simple[unit];\n            var unitPattern = void 0;\n            if (unitData) {\n                // Simple unit pattern\n                unitPattern = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), data.units.simple[unit][unitDisplay]);\n            }\n            else {\n                // See: http://unicode.org/reports/tr35/tr35-general.html#perUnitPatterns\n                // If cannot find unit in the simple pattern, it must be \"per\" compound pattern.\n                // Implementation note: we are not following TR-35 here because we need to format to parts!\n                var _b = unit.split('-per-'), numeratorUnit = _b[0], denominatorUnit = _b[1];\n                unitData = data.units.simple[numeratorUnit];\n                var numeratorUnitPattern = selectPlural(pl, numberResult.roundedNumber * Math.pow(10, exponent), data.units.simple[numeratorUnit][unitDisplay]);\n                var perUnitPattern = data.units.simple[denominatorUnit].perUnit[unitDisplay];\n                if (perUnitPattern) {\n                    // perUnitPattern exists, combine it with numeratorUnitPattern\n                    unitPattern = perUnitPattern.replace('{0}', numeratorUnitPattern);\n                }\n                else {\n                    // get compoundUnit pattern (e.g. \"{0} per {1}\"), repalce {0} with numerator pattern and {1} with\n                    // the denominator pattern in singular form.\n                    var perPattern = data.units.compound.per[unitDisplay];\n                    var denominatorPattern = selectPlural(pl, 1, data.units.simple[denominatorUnit][unitDisplay]);\n                    unitPattern = unitPattern = perPattern\n                        .replace('{0}', numeratorUnitPattern)\n                        .replace('{1}', denominatorPattern.replace('{0}', ''));\n                }\n            }\n            var result = [];\n            // We need spacing around \"{0}\" because they are not treated as \"unit\" parts, but \"literal\".\n            for (var _c = 0, _d = unitPattern.split(/(\\s*\\{0\\}\\s*)/); _c < _d.length; _c++) {\n                var part = _d[_c];\n                var interpolateMatch = /^(\\s*)\\{0\\}(\\s*)$/.exec(part);\n                if (interpolateMatch) {\n                    // Space before \"{0}\"\n                    if (interpolateMatch[1]) {\n                        result.push({ type: 'literal', value: interpolateMatch[1] });\n                    }\n                    // \"{0}\" itself\n                    result.push.apply(result, numberParts);\n                    // Space after \"{0}\"\n                    if (interpolateMatch[2]) {\n                        result.push({ type: 'literal', value: interpolateMatch[2] });\n                    }\n                }\n                else if (part) {\n                    result.push({ type: 'unit', value: part });\n                }\n            }\n            return result;\n        }\n        default:\n            return numberParts;\n    }\n    // #endregion\n}\n// A subset of https://tc39.es/ecma402/#sec-partitionnotationsubpattern\n// Plus the exponent parts handling.\nfunction paritionNumberIntoParts(symbols, numberResult, notation, exponent, numberingSystem, useGrouping, \n/**\n * This is the decimal number pattern without signs or symbols.\n * It is used to infer the group size when `useGrouping` is true.\n *\n * A typical value looks like \"#,##0.00\" (primary group size is 3).\n * Some locales like Hindi has secondary group size of 2 (e.g. \"#,##,##0.00\").\n */\ndecimalNumberPattern) {\n    var result = [];\n    // eslint-disable-next-line prefer-const\n    var n = numberResult.formattedString, x = numberResult.roundedNumber;\n    if (isNaN(x)) {\n        return [{ type: 'nan', value: n }];\n    }\n    else if (!isFinite(x)) {\n        return [{ type: 'infinity', value: n }];\n    }\n    var digitReplacementTable = digitMapping[numberingSystem];\n    if (digitReplacementTable) {\n        n = n.replace(/\\d/g, function (digit) { return digitReplacementTable[+digit] || digit; });\n    }\n    // TODO: Else use an implementation dependent algorithm to map n to the appropriate\n    // representation of n in the given numbering system.\n    var decimalSepIndex = n.indexOf('.');\n    var integer;\n    var fraction;\n    if (decimalSepIndex > 0) {\n        integer = n.slice(0, decimalSepIndex);\n        fraction = n.slice(decimalSepIndex + 1);\n    }\n    else {\n        integer = n;\n    }\n    // #region Grouping integer digits\n    // The weird compact and x >= 10000 check is to ensure consistency with Node.js and Chrome.\n    // Note that `de` does not have compact form for thousands, but Node.js does not insert grouping separator\n    // unless the rounded number is greater than 10000:\n    //   NumberFormat('de', {notation: 'compact', compactDisplay: 'short'}).format(1234) //=> \"1234\"\n    //   NumberFormat('de').format(1234) //=> \"1.234\"\n    if (useGrouping && (notation !== 'compact' || x >= 10000)) {\n        var groupSepSymbol = symbols.group;\n        var groups = [];\n        // > There may be two different grouping sizes: The primary grouping size used for the least\n        // > significant integer group, and the secondary grouping size used for more significant groups.\n        // > If a pattern contains multiple grouping separators, the interval between the last one and the\n        // > end of the integer defines the primary grouping size, and the interval between the last two\n        // > defines the secondary grouping size. All others are ignored.\n        var integerNumberPattern = decimalNumberPattern.split('.')[0];\n        var patternGroups = integerNumberPattern.split(',');\n        var primaryGroupingSize = 3;\n        var secondaryGroupingSize = 3;\n        if (patternGroups.length > 1) {\n            primaryGroupingSize = patternGroups[patternGroups.length - 1].length;\n        }\n        if (patternGroups.length > 2) {\n            secondaryGroupingSize = patternGroups[patternGroups.length - 2].length;\n        }\n        var i = integer.length - primaryGroupingSize;\n        if (i > 0) {\n            // Slice the least significant integer group\n            groups.push(integer.slice(i, i + primaryGroupingSize));\n            // Then iteratively push the more signicant groups\n            // TODO: handle surrogate pairs in some numbering system digits\n            for (i -= secondaryGroupingSize; i > 0; i -= secondaryGroupingSize) {\n                groups.push(integer.slice(i, i + secondaryGroupingSize));\n            }\n            groups.push(integer.slice(0, i + secondaryGroupingSize));\n        }\n        else {\n            groups.push(integer);\n        }\n        while (groups.length > 0) {\n            var integerGroup = groups.pop();\n            result.push({ type: 'integer', value: integerGroup });\n            if (groups.length > 0) {\n                result.push({ type: 'group', value: groupSepSymbol });\n            }\n        }\n    }\n    else {\n        result.push({ type: 'integer', value: integer });\n    }\n    // #endregion\n    if (fraction !== undefined) {\n        result.push({ type: 'decimal', value: symbols.decimal }, { type: 'fraction', value: fraction });\n    }\n    if ((notation === 'scientific' || notation === 'engineering') &&\n        isFinite(x)) {\n        result.push({ type: 'exponentSeparator', value: symbols.exponential });\n        if (exponent < 0) {\n            result.push({ type: 'exponentMinusSign', value: symbols.minusSign });\n            exponent = -exponent;\n        }\n        var exponentResult = ToRawFixed(exponent, 0, 0);\n        result.push({\n            type: 'exponentInteger',\n            value: exponentResult.formattedString,\n        });\n    }\n    return result;\n}\nfunction getPatternForSign(pattern, sign) {\n    if (pattern.indexOf(';') < 0) {\n        pattern = \"\".concat(pattern, \";-\").concat(pattern);\n    }\n    var _a = pattern.split(';'), zeroPattern = _a[0], negativePattern = _a[1];\n    switch (sign) {\n        case 0:\n            return zeroPattern;\n        case -1:\n            return negativePattern;\n        default:\n            return negativePattern.indexOf('-') >= 0\n                ? negativePattern.replace(/-/g, '+')\n                : \"+\".concat(zeroPattern);\n    }\n}\n// Find the CLDR pattern for compact notation based on the magnitude of data and style.\n//\n// Example return value: \"¤ {c:laki}000;¤{c:laki} -0\" (`sw` locale):\n// - Notice the `{c:...}` token that wraps the compact literal.\n// - The consecutive zeros are normalized to single zero to match CLDR_NUMBER_PATTERN.\n//\n// Returning null means the compact display pattern cannot be found.\nfunction getCompactDisplayPattern(numberResult, pl, data, style, compactDisplay, currencyDisplay, numberingSystem) {\n    var _a;\n    var roundedNumber = numberResult.roundedNumber, sign = numberResult.sign, magnitude = numberResult.magnitude;\n    var magnitudeKey = String(Math.pow(10, magnitude));\n    var defaultNumberingSystem = data.numbers.nu[0];\n    var pattern;\n    if (style === 'currency' && currencyDisplay !== 'name') {\n        var byNumberingSystem = data.numbers.currency;\n        var currencyData = byNumberingSystem[numberingSystem] ||\n            byNumberingSystem[defaultNumberingSystem];\n        // NOTE: compact notation ignores currencySign!\n        var compactPluralRules = (_a = currencyData.short) === null || _a === void 0 ? void 0 : _a[magnitudeKey];\n        if (!compactPluralRules) {\n            return null;\n        }\n        pattern = selectPlural(pl, roundedNumber, compactPluralRules);\n    }\n    else {\n        var byNumberingSystem = data.numbers.decimal;\n        var byCompactDisplay = byNumberingSystem[numberingSystem] ||\n            byNumberingSystem[defaultNumberingSystem];\n        var compactPlaralRule = byCompactDisplay[compactDisplay][magnitudeKey];\n        if (!compactPlaralRule) {\n            return null;\n        }\n        pattern = selectPlural(pl, roundedNumber, compactPlaralRule);\n    }\n    // See https://unicode.org/reports/tr35/tr35-numbers.html#Compact_Number_Formats\n    // > If the value is precisely “0”, either explicit or defaulted, then the normal number format\n    // > pattern for that sort of object is supplied.\n    if (pattern === '0') {\n        return null;\n    }\n    pattern = getPatternForSign(pattern, sign)\n        // Extract compact literal from the pattern\n        .replace(/([^\\s;\\-\\+\\d¤]+)/g, '{c:$1}')\n        // We replace one or more zeros with a single zero so it matches `CLDR_NUMBER_PATTERN`.\n        .replace(/0+/, '0');\n    return pattern;\n}\nfunction selectPlural(pl, x, rules) {\n    return rules[pl.select(x)] || rules.other;\n}\n", "import { __extends } from \"tslib\";\nvar MissingLocaleDataError = /** @class */ (function (_super) {\n    __extends(MissingLocaleDataError, _super);\n    function MissingLocaleDataError() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = 'MISSING_LOCALE_DATA';\n        return _this;\n    }\n    return MissingLocaleDataError;\n}(Error));\nexport function isMissingLocaleDataError(e) {\n    return e.type === 'MISSING_LOCALE_DATA';\n}\n", "export var RangePatternType;\n(function (RangePatternType) {\n    RangePatternType[\"startRange\"] = \"startRange\";\n    RangePatternType[\"shared\"] = \"shared\";\n    RangePatternType[\"endRange\"] = \"endRange\";\n})(RangePatternType || (RangePatternType = {}));\n", "export var ErrorKind;\n(function (ErrorKind) {\n    /** Argument is unclosed (e.g. `{0`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_CLOSING_BRACE\"] = 1] = \"EXPECT_ARGUMENT_CLOSING_BRACE\";\n    /** Argument is empty (e.g. `{}`). */\n    ErrorKind[ErrorKind[\"EMPTY_ARGUMENT\"] = 2] = \"EMPTY_ARGUMENT\";\n    /** Argument is malformed (e.g. `{foo!}``) */\n    ErrorKind[ErrorKind[\"MALFORMED_ARGUMENT\"] = 3] = \"MALFORMED_ARGUMENT\";\n    /** Expect an argument type (e.g. `{foo,}`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_TYPE\"] = 4] = \"EXPECT_ARGUMENT_TYPE\";\n    /** Unsupported argument type (e.g. `{foo,foo}`) */\n    ErrorKind[ErrorKind[\"INVALID_ARGUMENT_TYPE\"] = 5] = \"INVALID_ARGUMENT_TYPE\";\n    /** Expect an argument style (e.g. `{foo, number, }`) */\n    ErrorKind[ErrorKind[\"EXPECT_ARGUMENT_STYLE\"] = 6] = \"EXPECT_ARGUMENT_STYLE\";\n    /** The number skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_NUMBER_SKELETON\"] = 7] = \"INVALID_NUMBER_SKELETON\";\n    /** The date time skeleton is invalid. */\n    ErrorKind[ErrorKind[\"INVALID_DATE_TIME_SKELETON\"] = 8] = \"INVALID_DATE_TIME_SKELETON\";\n    /** Exepct a number skeleton following the `::` (e.g. `{foo, number, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_NUMBER_SKELETON\"] = 9] = \"EXPECT_NUMBER_SKELETON\";\n    /** Exepct a date time skeleton following the `::` (e.g. `{foo, date, ::}`) */\n    ErrorKind[ErrorKind[\"EXPECT_DATE_TIME_SKELETON\"] = 10] = \"EXPECT_DATE_TIME_SKELETON\";\n    /** Unmatched apostrophes in the argument style (e.g. `{foo, number, 'test`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\"] = 11] = \"UNCLOSED_QUOTE_IN_ARGUMENT_STYLE\";\n    /** Missing select argument options (e.g. `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_OPTIONS\"] = 12] = \"EXPECT_SELECT_ARGUMENT_OPTIONS\";\n    /** Expecting an offset value in `plural` or `selectordinal` argument (e.g `{foo, plural, offset}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 13] = \"EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Offset value in `plural` or `selectordinal` is invalid (e.g. `{foo, plural, offset: x}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\"] = 14] = \"INVALID_PLURAL_ARGUMENT_OFFSET_VALUE\";\n    /** Expecting a selector in `select` argument (e.g `{foo, select}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR\"] = 15] = \"EXPECT_SELECT_ARGUMENT_SELECTOR\";\n    /** Expecting a selector in `plural` or `selectordinal` argument (e.g `{foo, plural}`) */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR\"] = 16] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR\";\n    /** Expecting a message fragment after the `select` selector (e.g. `{foo, select, apple}`) */\n    ErrorKind[ErrorKind[\"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\"] = 17] = \"EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\";\n    /**\n     * Expecting a message fragment after the `plural` or `selectordinal` selector\n     * (e.g. `{foo, plural, one}`)\n     */\n    ErrorKind[ErrorKind[\"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\"] = 18] = \"EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT\";\n    /** Selector in `plural` or `selectordinal` is malformed (e.g. `{foo, plural, =x {#}}`) */\n    ErrorKind[ErrorKind[\"INVALID_PLURAL_ARGUMENT_SELECTOR\"] = 19] = \"INVALID_PLURAL_ARGUMENT_SELECTOR\";\n    /**\n     * Duplicate selectors in `plural` or `selectordinal` argument.\n     * (e.g. {foo, plural, one {#} one {#}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\"] = 20] = \"DUPLICATE_PLURAL_ARGUMENT_SELECTOR\";\n    /** Duplicate selectors in `select` argument.\n     * (e.g. {foo, select, apple {apple} apple {apple}})\n     */\n    ErrorKind[ErrorKind[\"DUPLICATE_SELECT_ARGUMENT_SELECTOR\"] = 21] = \"DUPLICATE_SELECT_ARGUMENT_SELECTOR\";\n    /** Plural or select argument option must have `other` clause. */\n    ErrorKind[ErrorKind[\"MISSING_OTHER_CLAUSE\"] = 22] = \"MISSING_OTHER_CLAUSE\";\n    /** The tag is malformed. (e.g. `<bold!>foo</bold!>) */\n    ErrorKind[ErrorKind[\"INVALID_TAG\"] = 23] = \"INVALID_TAG\";\n    /** The tag name is invalid. (e.g. `<123>foo</123>`) */\n    ErrorKind[ErrorKind[\"INVALID_TAG_NAME\"] = 25] = \"INVALID_TAG_NAME\";\n    /** The closing tag does not match the opening tag. (e.g. `<bold>foo</italic>`) */\n    ErrorKind[ErrorKind[\"UNMATCHED_CLOSING_TAG\"] = 26] = \"UNMATCHED_CLOSING_TAG\";\n    /** The opening tag has unmatched closing tag. (e.g. `<bold>foo`) */\n    ErrorKind[ErrorKind[\"UNCLOSED_TAG\"] = 27] = \"UNCLOSED_TAG\";\n})(ErrorKind || (ErrorKind = {}));\n", "export var TYPE;\n(function (TYPE) {\n    /**\n     * Raw text\n     */\n    TYPE[TYPE[\"literal\"] = 0] = \"literal\";\n    /**\n     * Variable w/o any format, e.g `var` in `this is a {var}`\n     */\n    TYPE[TYPE[\"argument\"] = 1] = \"argument\";\n    /**\n     * Variable w/ number format\n     */\n    TYPE[TYPE[\"number\"] = 2] = \"number\";\n    /**\n     * Variable w/ date format\n     */\n    TYPE[TYPE[\"date\"] = 3] = \"date\";\n    /**\n     * Variable w/ time format\n     */\n    TYPE[TYPE[\"time\"] = 4] = \"time\";\n    /**\n     * Variable w/ select format\n     */\n    TYPE[TYPE[\"select\"] = 5] = \"select\";\n    /**\n     * Variable w/ plural format\n     */\n    TYPE[TYPE[\"plural\"] = 6] = \"plural\";\n    /**\n     * Only possible within plural argument.\n     * This is the `#` symbol that will be substituted with the count.\n     */\n    TYPE[TYPE[\"pound\"] = 7] = \"pound\";\n    /**\n     * XML-like tag\n     */\n    TYPE[TYPE[\"tag\"] = 8] = \"tag\";\n})(TYPE || (TYPE = {}));\nexport var SKELETON_TYPE;\n(function (SKELETON_TYPE) {\n    SKELETON_TYPE[SKELETON_TYPE[\"number\"] = 0] = \"number\";\n    SKELETON_TYPE[SKELETON_TYPE[\"dateTime\"] = 1] = \"dateTime\";\n})(SKELETON_TYPE || (SKELETON_TYPE = {}));\n/**\n * Type Guards\n */\nexport function isLiteralElement(el) {\n    return el.type === TYPE.literal;\n}\nexport function isArgumentElement(el) {\n    return el.type === TYPE.argument;\n}\nexport function isNumberElement(el) {\n    return el.type === TYPE.number;\n}\nexport function isDateElement(el) {\n    return el.type === TYPE.date;\n}\nexport function isTimeElement(el) {\n    return el.type === TYPE.time;\n}\nexport function isSelectElement(el) {\n    return el.type === TYPE.select;\n}\nexport function isPluralElement(el) {\n    return el.type === TYPE.plural;\n}\nexport function isPoundElement(el) {\n    return el.type === TYPE.pound;\n}\nexport function isTagElement(el) {\n    return el.type === TYPE.tag;\n}\nexport function isNumberSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.number);\n}\nexport function isDateTimeSkeleton(el) {\n    return !!(el && typeof el === 'object' && el.type === SKELETON_TYPE.dateTime);\n}\nexport function createLiteralElement(value) {\n    return {\n        type: TYPE.literal,\n        value: value,\n    };\n}\nexport function createNumberElement(value, style) {\n    return {\n        type: TYPE.number,\n        value: value,\n        style: style,\n    };\n}\n", "// @generated from regex-gen.ts\nexport var SPACE_SEPARATOR_REGEX = /[ \\xA0\\u1680\\u2000-\\u200A\\u202F\\u205F\\u3000]/;\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/;\n", "/**\n * https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * Credit: https://github.com/caridy/intl-datetimeformat-pattern/blob/master/index.js\n * with some tweaks\n */\nvar DATE_TIME_REGEX = /(?:[Eec]{1,6}|G{1,5}|[Qq]{1,5}|(?:[yYur]+|U{1,5})|[ML]{1,5}|d{1,2}|D{1,3}|F{1}|[abB]{1,5}|[hkHK]{1,2}|w{1,2}|W{1}|m{1,2}|s{1,2}|[zZOvVxX]{1,4})(?=([^']*'[^']*')*[^']*$)/g;\n/**\n * Parse Date time skeleton into Intl.DateTimeFormatOptions\n * Ref: https://unicode.org/reports/tr35/tr35-dates.html#Date_Field_Symbol_Table\n * @public\n * @param skeleton skeleton string\n */\nexport function parseDateTimeSkeleton(skeleton) {\n    var result = {};\n    skeleton.replace(DATE_TIME_REGEX, function (match) {\n        var len = match.length;\n        switch (match[0]) {\n            // Era\n            case 'G':\n                result.era = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            // Year\n            case 'y':\n                result.year = len === 2 ? '2-digit' : 'numeric';\n                break;\n            case 'Y':\n            case 'u':\n            case 'U':\n            case 'r':\n                throw new RangeError('`Y/u/U/r` (year) patterns are not supported, use `y` instead');\n            // Quarter\n            case 'q':\n            case 'Q':\n                throw new RangeError('`q/Q` (quarter) patterns are not supported');\n            // Month\n            case 'M':\n            case 'L':\n                result.month = ['numeric', '2-digit', 'short', 'long', 'narrow'][len - 1];\n                break;\n            // Week\n            case 'w':\n            case 'W':\n                throw new RangeError('`w/W` (week) patterns are not supported');\n            case 'd':\n                result.day = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'D':\n            case 'F':\n            case 'g':\n                throw new RangeError('`D/F/g` (day) patterns are not supported, use `d` instead');\n            // Weekday\n            case 'E':\n                result.weekday = len === 4 ? 'long' : len === 5 ? 'narrow' : 'short';\n                break;\n            case 'e':\n                if (len < 4) {\n                    throw new RangeError('`e..eee` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            case 'c':\n                if (len < 4) {\n                    throw new RangeError('`c..ccc` (weekday) patterns are not supported');\n                }\n                result.weekday = ['short', 'long', 'narrow', 'short'][len - 4];\n                break;\n            // Period\n            case 'a': // AM, PM\n                result.hour12 = true;\n                break;\n            case 'b': // am, pm, noon, midnight\n            case 'B': // flexible day periods\n                throw new RangeError('`b/B` (period) patterns are not supported, use `a` instead');\n            // Hour\n            case 'h':\n                result.hourCycle = 'h12';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'H':\n                result.hourCycle = 'h23';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'K':\n                result.hourCycle = 'h11';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'k':\n                result.hourCycle = 'h24';\n                result.hour = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'j':\n            case 'J':\n            case 'C':\n                throw new RangeError('`j/J/C` (hour) patterns are not supported, use `h/H/K/k` instead');\n            // Minute\n            case 'm':\n                result.minute = ['numeric', '2-digit'][len - 1];\n                break;\n            // Second\n            case 's':\n                result.second = ['numeric', '2-digit'][len - 1];\n                break;\n            case 'S':\n            case 'A':\n                throw new RangeError('`S/A` (second) patterns are not supported, use `s` instead');\n            // Zone\n            case 'z': // 1..3, 4: specific non-location format\n                result.timeZoneName = len < 4 ? 'short' : 'long';\n                break;\n            case 'Z': // 1..3, 4, 5: The ISO8601 varios formats\n            case 'O': // 1, 4: milliseconds in day short, long\n            case 'v': // 1, 4: generic non-location format\n            case 'V': // 1, 2, 3, 4: time zone ID or city\n            case 'X': // 1, 2, 3, 4: The ISO8601 varios formats\n            case 'x': // 1, 2, 3, 4: The ISO8601 varios formats\n                throw new RangeError('`Z/O/v/V/X/x` (timeZone) patterns are not supported, use `z` instead');\n        }\n        return '';\n    });\n    return result;\n}\n", "// @generated from regex-gen.ts\nexport var WHITE_SPACE_REGEX = /[\\t-\\r \\x85\\u200E\\u200F\\u2028\\u2029]/i;\n", "import { __assign } from \"tslib\";\nimport { WHITE_SPACE_REGEX } from './regex.generated';\nexport function parseNumberSkeletonFromString(skeleton) {\n    if (skeleton.length === 0) {\n        throw new Error('Number skeleton cannot be empty');\n    }\n    // Parse the skeleton\n    var stringTokens = skeleton\n        .split(WHITE_SPACE_REGEX)\n        .filter(function (x) { return x.length > 0; });\n    var tokens = [];\n    for (var _i = 0, stringTokens_1 = stringTokens; _i < stringTokens_1.length; _i++) {\n        var stringToken = stringTokens_1[_i];\n        var stemAndOptions = stringToken.split('/');\n        if (stemAndOptions.length === 0) {\n            throw new Error('Invalid number skeleton');\n        }\n        var stem = stemAndOptions[0], options = stemAndOptions.slice(1);\n        for (var _a = 0, options_1 = options; _a < options_1.length; _a++) {\n            var option = options_1[_a];\n            if (option.length === 0) {\n                throw new Error('Invalid number skeleton');\n            }\n        }\n        tokens.push({ stem: stem, options: options });\n    }\n    return tokens;\n}\nfunction icuUnitToEcma(unit) {\n    return unit.replace(/^(.*?)-/, '');\n}\nvar FRACTION_PRECISION_REGEX = /^\\.(?:(0+)(\\*)?|(#+)|(0+)(#+))$/g;\nvar SIGNIFICANT_PRECISION_REGEX = /^(@+)?(\\+|#+)?[rs]?$/g;\nvar INTEGER_WIDTH_REGEX = /(\\*)(0+)|(#+)(0+)|(0+)/g;\nvar CONCISE_INTEGER_WIDTH_REGEX = /^(0+)$/;\nfunction parseSignificantPrecision(str) {\n    var result = {};\n    if (str[str.length - 1] === 'r') {\n        result.roundingPriority = 'morePrecision';\n    }\n    else if (str[str.length - 1] === 's') {\n        result.roundingPriority = 'lessPrecision';\n    }\n    str.replace(SIGNIFICANT_PRECISION_REGEX, function (_, g1, g2) {\n        // @@@ case\n        if (typeof g2 !== 'string') {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits = g1.length;\n        }\n        // @@@+ case\n        else if (g2 === '+') {\n            result.minimumSignificantDigits = g1.length;\n        }\n        // .### case\n        else if (g1[0] === '#') {\n            result.maximumSignificantDigits = g1.length;\n        }\n        // .@@## or .@@@ case\n        else {\n            result.minimumSignificantDigits = g1.length;\n            result.maximumSignificantDigits =\n                g1.length + (typeof g2 === 'string' ? g2.length : 0);\n        }\n        return '';\n    });\n    return result;\n}\nfunction parseSign(str) {\n    switch (str) {\n        case 'sign-auto':\n            return {\n                signDisplay: 'auto',\n            };\n        case 'sign-accounting':\n        case '()':\n            return {\n                currencySign: 'accounting',\n            };\n        case 'sign-always':\n        case '+!':\n            return {\n                signDisplay: 'always',\n            };\n        case 'sign-accounting-always':\n        case '()!':\n            return {\n                signDisplay: 'always',\n                currencySign: 'accounting',\n            };\n        case 'sign-except-zero':\n        case '+?':\n            return {\n                signDisplay: 'exceptZero',\n            };\n        case 'sign-accounting-except-zero':\n        case '()?':\n            return {\n                signDisplay: 'exceptZero',\n                currencySign: 'accounting',\n            };\n        case 'sign-never':\n        case '+_':\n            return {\n                signDisplay: 'never',\n            };\n    }\n}\nfunction parseConciseScientificAndEngineeringStem(stem) {\n    // Engineering\n    var result;\n    if (stem[0] === 'E' && stem[1] === 'E') {\n        result = {\n            notation: 'engineering',\n        };\n        stem = stem.slice(2);\n    }\n    else if (stem[0] === 'E') {\n        result = {\n            notation: 'scientific',\n        };\n        stem = stem.slice(1);\n    }\n    if (result) {\n        var signDisplay = stem.slice(0, 2);\n        if (signDisplay === '+!') {\n            result.signDisplay = 'always';\n            stem = stem.slice(2);\n        }\n        else if (signDisplay === '+?') {\n            result.signDisplay = 'exceptZero';\n            stem = stem.slice(2);\n        }\n        if (!CONCISE_INTEGER_WIDTH_REGEX.test(stem)) {\n            throw new Error('Malformed concise eng/scientific notation');\n        }\n        result.minimumIntegerDigits = stem.length;\n    }\n    return result;\n}\nfunction parseNotationOptions(opt) {\n    var result = {};\n    var signOpts = parseSign(opt);\n    if (signOpts) {\n        return signOpts;\n    }\n    return result;\n}\n/**\n * https://github.com/unicode-org/icu/blob/master/docs/userguide/format_parse/numbers/skeletons.md#skeleton-stems-and-options\n */\nexport function parseNumberSkeleton(tokens) {\n    var result = {};\n    for (var _i = 0, tokens_1 = tokens; _i < tokens_1.length; _i++) {\n        var token = tokens_1[_i];\n        switch (token.stem) {\n            case 'percent':\n            case '%':\n                result.style = 'percent';\n                continue;\n            case '%x100':\n                result.style = 'percent';\n                result.scale = 100;\n                continue;\n            case 'currency':\n                result.style = 'currency';\n                result.currency = token.options[0];\n                continue;\n            case 'group-off':\n            case ',_':\n                result.useGrouping = false;\n                continue;\n            case 'precision-integer':\n            case '.':\n                result.maximumFractionDigits = 0;\n                continue;\n            case 'measure-unit':\n            case 'unit':\n                result.style = 'unit';\n                result.unit = icuUnitToEcma(token.options[0]);\n                continue;\n            case 'compact-short':\n            case 'K':\n                result.notation = 'compact';\n                result.compactDisplay = 'short';\n                continue;\n            case 'compact-long':\n            case 'KK':\n                result.notation = 'compact';\n                result.compactDisplay = 'long';\n                continue;\n            case 'scientific':\n                result = __assign(__assign(__assign({}, result), { notation: 'scientific' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'engineering':\n                result = __assign(__assign(__assign({}, result), { notation: 'engineering' }), token.options.reduce(function (all, opt) { return (__assign(__assign({}, all), parseNotationOptions(opt))); }, {}));\n                continue;\n            case 'notation-simple':\n                result.notation = 'standard';\n                continue;\n            // https://github.com/unicode-org/icu/blob/master/icu4c/source/i18n/unicode/unumberformatter.h\n            case 'unit-width-narrow':\n                result.currencyDisplay = 'narrowSymbol';\n                result.unitDisplay = 'narrow';\n                continue;\n            case 'unit-width-short':\n                result.currencyDisplay = 'code';\n                result.unitDisplay = 'short';\n                continue;\n            case 'unit-width-full-name':\n                result.currencyDisplay = 'name';\n                result.unitDisplay = 'long';\n                continue;\n            case 'unit-width-iso-code':\n                result.currencyDisplay = 'symbol';\n                continue;\n            case 'scale':\n                result.scale = parseFloat(token.options[0]);\n                continue;\n            case 'rounding-mode-floor':\n                result.roundingMode = 'floor';\n                continue;\n            case 'rounding-mode-ceiling':\n                result.roundingMode = 'ceil';\n                continue;\n            case 'rounding-mode-down':\n                result.roundingMode = 'trunc';\n                continue;\n            case 'rounding-mode-up':\n                result.roundingMode = 'expand';\n                continue;\n            case 'rounding-mode-half-even':\n                result.roundingMode = 'halfEven';\n                continue;\n            case 'rounding-mode-half-down':\n                result.roundingMode = 'halfTrunc';\n                continue;\n            case 'rounding-mode-half-up':\n                result.roundingMode = 'halfExpand';\n                continue;\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n            case 'integer-width':\n                if (token.options.length > 1) {\n                    throw new RangeError('integer-width stems only accept a single optional option');\n                }\n                token.options[0].replace(INTEGER_WIDTH_REGEX, function (_, g1, g2, g3, g4, g5) {\n                    if (g1) {\n                        result.minimumIntegerDigits = g2.length;\n                    }\n                    else if (g3 && g4) {\n                        throw new Error('We currently do not support maximum integer digits');\n                    }\n                    else if (g5) {\n                        throw new Error('We currently do not support exact integer digits');\n                    }\n                    return '';\n                });\n                continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#integer-width\n        if (CONCISE_INTEGER_WIDTH_REGEX.test(token.stem)) {\n            result.minimumIntegerDigits = token.stem.length;\n            continue;\n        }\n        if (FRACTION_PRECISION_REGEX.test(token.stem)) {\n            // Precision\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#fraction-precision\n            // precision-integer case\n            if (token.options.length > 1) {\n                throw new RangeError('Fraction-precision stems only accept a single optional option');\n            }\n            token.stem.replace(FRACTION_PRECISION_REGEX, function (_, g1, g2, g3, g4, g5) {\n                // .000* case (before ICU67 it was .000+)\n                if (g2 === '*') {\n                    result.minimumFractionDigits = g1.length;\n                }\n                // .### case\n                else if (g3 && g3[0] === '#') {\n                    result.maximumFractionDigits = g3.length;\n                }\n                // .00## case\n                else if (g4 && g5) {\n                    result.minimumFractionDigits = g4.length;\n                    result.maximumFractionDigits = g4.length + g5.length;\n                }\n                else {\n                    result.minimumFractionDigits = g1.length;\n                    result.maximumFractionDigits = g1.length;\n                }\n                return '';\n            });\n            var opt = token.options[0];\n            // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#trailing-zero-display\n            if (opt === 'w') {\n                result = __assign(__assign({}, result), { trailingZeroDisplay: 'stripIfInteger' });\n            }\n            else if (opt) {\n                result = __assign(__assign({}, result), parseSignificantPrecision(opt));\n            }\n            continue;\n        }\n        // https://unicode-org.github.io/icu/userguide/format_parse/numbers/skeletons.html#significant-digits-precision\n        if (SIGNIFICANT_PRECISION_REGEX.test(token.stem)) {\n            result = __assign(__assign({}, result), parseSignificantPrecision(token.stem));\n            continue;\n        }\n        var signOpts = parseSign(token.stem);\n        if (signOpts) {\n            result = __assign(__assign({}, result), signOpts);\n        }\n        var conciseScientificAndEngineeringOpts = parseConciseScientificAndEngineeringStem(token.stem);\n        if (conciseScientificAndEngineeringOpts) {\n            result = __assign(__assign({}, result), conciseScientificAndEngineeringOpts);\n        }\n    }\n    return result;\n}\n", "// @generated from time-data-gen.ts\n// prettier-ignore  \nexport var timeData = {\n    \"001\": [\n        \"H\",\n        \"h\"\n    ],\n    \"AC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"AF\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"AG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"AL\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"AS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"AT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"AW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"AX\": [\n        \"H\"\n    ],\n    \"AZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BD\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"BE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BG\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"BH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"BI\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BJ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BN\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"BO\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"BQ\": [\n        \"H\"\n    ],\n    \"BR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"BS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"BT\": [\n        \"h\",\n        \"H\"\n    ],\n    \"BW\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"BY\": [\n        \"H\",\n        \"h\"\n    ],\n    \"BZ\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CA\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"CC\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CD\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"CF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CH\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"CI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CL\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"CN\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"CO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CP\": [\n        \"H\"\n    ],\n    \"CR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CU\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"CV\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"CX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CY\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"CZ\": [\n        \"H\"\n    ],\n    \"DE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"DG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"DJ\": [\n        \"h\",\n        \"H\"\n    ],\n    \"DK\": [\n        \"H\"\n    ],\n    \"DM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"DO\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"DZ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"EC\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"EE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"EG\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"EH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ER\": [\n        \"h\",\n        \"H\"\n    ],\n    \"ES\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"ET\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"FI\": [\n        \"H\"\n    ],\n    \"FJ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"FM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"FO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"FR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GA\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GB\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GD\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GE\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"GF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GH\": [\n        \"h\",\n        \"H\"\n    ],\n    \"GI\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"GM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GN\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GP\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GQ\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"GR\": [\n        \"h\",\n        \"H\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"GT\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"GU\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"GW\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"GY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"HK\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"HN\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"HR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"HU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"IC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ID\": [\n        \"H\"\n    ],\n    \"IE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"IM\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IN\": [\n        \"h\",\n        \"H\"\n    ],\n    \"IO\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"IQ\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"IR\": [\n        \"hB\",\n        \"H\"\n    ],\n    \"IS\": [\n        \"H\"\n    ],\n    \"IT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"JE\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"JM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"JO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"JP\": [\n        \"H\",\n        \"K\",\n        \"h\"\n    ],\n    \"KE\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"KG\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KH\": [\n        \"hB\",\n        \"h\",\n        \"H\",\n        \"hb\"\n    ],\n    \"KI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KM\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KN\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KP\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"KW\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"KY\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"KZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"LA\": [\n        \"H\",\n        \"hb\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LB\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"LC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LI\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"LK\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"LR\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"LS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"LT\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"LU\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"LV\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"LY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MD\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ME\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"MF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MG\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MH\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MK\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ML\": [\n        \"H\"\n    ],\n    \"MM\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"MN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MO\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MP\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MQ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"MR\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"MS\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"MT\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MU\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MV\": [\n        \"H\",\n        \"h\"\n    ],\n    \"MW\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"MX\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"MY\": [\n        \"hb\",\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"MZ\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NC\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NE\": [\n        \"H\"\n    ],\n    \"NF\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NG\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NI\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"NL\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"NO\": [\n        \"H\",\n        \"h\"\n    ],\n    \"NP\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"NR\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NU\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"NZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"OM\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PA\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PE\": [\n        \"H\",\n        \"hB\",\n        \"h\",\n        \"hb\"\n    ],\n    \"PF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"PG\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PH\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PK\": [\n        \"h\",\n        \"hB\",\n        \"H\"\n    ],\n    \"PL\": [\n        \"H\",\n        \"h\"\n    ],\n    \"PM\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PN\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"PR\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"PS\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"PT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"PW\": [\n        \"h\",\n        \"H\"\n    ],\n    \"PY\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"QA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"RE\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RO\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"RS\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"RU\": [\n        \"H\"\n    ],\n    \"RW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"SA\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SB\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SC\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SD\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SE\": [\n        \"H\"\n    ],\n    \"SG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SH\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SI\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SJ\": [\n        \"H\"\n    ],\n    \"SK\": [\n        \"H\"\n    ],\n    \"SL\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"SM\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SN\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"SO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"SR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SS\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ST\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"SV\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"SX\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"SY\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"SZ\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"TC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TD\": [\n        \"h\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TF\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"TG\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TH\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TJ\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TL\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ],\n    \"TM\": [\n        \"H\",\n        \"h\"\n    ],\n    \"TN\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"TO\": [\n        \"h\",\n        \"H\"\n    ],\n    \"TR\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"TT\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"TW\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"TZ\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UA\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"UG\": [\n        \"hB\",\n        \"hb\",\n        \"H\",\n        \"h\"\n    ],\n    \"UM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"US\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"UY\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"UZ\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"VA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"VC\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VE\": [\n        \"h\",\n        \"H\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"VG\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VI\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"VN\": [\n        \"H\",\n        \"h\"\n    ],\n    \"VU\": [\n        \"h\",\n        \"H\"\n    ],\n    \"WF\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"WS\": [\n        \"h\",\n        \"H\"\n    ],\n    \"XK\": [\n        \"H\",\n        \"hB\",\n        \"h\"\n    ],\n    \"YE\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"YT\": [\n        \"H\",\n        \"hB\"\n    ],\n    \"ZA\": [\n        \"H\",\n        \"h\",\n        \"hb\",\n        \"hB\"\n    ],\n    \"ZM\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"ZW\": [\n        \"H\",\n        \"h\"\n    ],\n    \"af-ZA\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"ar-001\": [\n        \"h\",\n        \"hB\",\n        \"hb\",\n        \"H\"\n    ],\n    \"ca-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"en-001\": [\n        \"h\",\n        \"hb\",\n        \"H\",\n        \"hB\"\n    ],\n    \"es-BO\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-BR\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-EC\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-GQ\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"es-PE\": [\n        \"H\",\n        \"h\",\n        \"hB\",\n        \"hb\"\n    ],\n    \"fr-CA\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gl-ES\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"gu-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"hi-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"it-CH\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"it-IT\": [\n        \"H\",\n        \"h\",\n        \"hB\"\n    ],\n    \"kn-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"ml-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"mr-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"pa-IN\": [\n        \"hB\",\n        \"hb\",\n        \"h\",\n        \"H\"\n    ],\n    \"ta-IN\": [\n        \"hB\",\n        \"h\",\n        \"hb\",\n        \"H\"\n    ],\n    \"te-IN\": [\n        \"hB\",\n        \"h\",\n        \"H\"\n    ],\n    \"zu-ZA\": [\n        \"H\",\n        \"hB\",\n        \"hb\",\n        \"h\"\n    ]\n};\n", "import { timeData } from './time-data.generated';\n/**\n * Returns the best matching date time pattern if a date time skeleton\n * pattern is provided with a locale. Follows the Unicode specification:\n * https://www.unicode.org/reports/tr35/tr35-dates.html#table-mapping-requested-time-skeletons-to-patterns\n * @param skeleton date time skeleton pattern that possibly includes j, J or C\n * @param locale\n */\nexport function getBestPattern(skeleton, locale) {\n    var skeletonCopy = '';\n    for (var patternPos = 0; patternPos < skeleton.length; patternPos++) {\n        var patternChar = skeleton.charAt(patternPos);\n        if (patternChar === 'j') {\n            var extraLength = 0;\n            while (patternPos + 1 < skeleton.length &&\n                skeleton.charAt(patternPos + 1) === patternChar) {\n                extraLength++;\n                patternPos++;\n            }\n            var hourLen = 1 + (extraLength & 1);\n            var dayPeriodLen = extraLength < 2 ? 1 : 3 + (extraLength >> 1);\n            var dayPeriodChar = 'a';\n            var hourChar = getDefaultHourSymbolFromLocale(locale);\n            if (hourChar == 'H' || hourChar == 'k') {\n                dayPeriodLen = 0;\n            }\n            while (dayPeriodLen-- > 0) {\n                skeletonCopy += dayPeriodChar;\n            }\n            while (hourLen-- > 0) {\n                skeletonCopy = hourChar + skeletonCopy;\n            }\n        }\n        else if (patternChar === 'J') {\n            skeletonCopy += 'H';\n        }\n        else {\n            skeletonCopy += patternChar;\n        }\n    }\n    return skeletonCopy;\n}\n/**\n * Maps the [hour cycle type](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Intl/Locale/hourCycle)\n * of the given `locale` to the corresponding time pattern.\n * @param locale\n */\nfunction getDefaultHourSymbolFromLocale(locale) {\n    var hourCycle = locale.hourCycle;\n    if (hourCycle === undefined &&\n        // @ts-ignore hourCycle(s) is not identified yet\n        locale.hourCycles &&\n        // @ts-ignore\n        locale.hourCycles.length) {\n        // @ts-ignore\n        hourCycle = locale.hourCycles[0];\n    }\n    if (hourCycle) {\n        switch (hourCycle) {\n            case 'h24':\n                return 'k';\n            case 'h23':\n                return 'H';\n            case 'h12':\n                return 'h';\n            case 'h11':\n                return 'K';\n            default:\n                throw new Error('Invalid hourCycle');\n        }\n    }\n    // TODO: Once hourCycle is fully supported remove the following with data generation\n    var languageTag = locale.language;\n    var regionTag;\n    if (languageTag !== 'root') {\n        regionTag = locale.maximize().region;\n    }\n    var hourCycles = timeData[regionTag || ''] ||\n        timeData[languageTag || ''] ||\n        timeData[\"\".concat(languageTag, \"-001\")] ||\n        timeData['001'];\n    return hourCycles[0];\n}\n", "var _a;\nimport { __assign } from \"tslib\";\nimport { <PERSON>rrorKind } from './error';\nimport { SKELETON_TYPE, TYPE, } from './types';\nimport { SPACE_SEPARATOR_REGEX } from './regex.generated';\nimport { parseNumberSkeleton, parseNumberSkeletonFromString, parseDateTimeSkeleton, } from '@formatjs/icu-skeleton-parser';\nimport { getBestPattern } from './date-time-pattern-generator';\nvar SPACE_SEPARATOR_START_REGEX = new RegExp(\"^\".concat(SPACE_SEPARATOR_REGEX.source, \"*\"));\nvar SPACE_SEPARATOR_END_REGEX = new RegExp(\"\".concat(SPACE_SEPARATOR_REGEX.source, \"*$\"));\nfunction createLocation(start, end) {\n    return { start: start, end: end };\n}\n// #region Ponyfills\n// Consolidate these variables up top for easier toggling during debugging\nvar hasNativeStartsWith = !!String.prototype.startsWith && '_a'.startsWith('a', 1);\nvar hasNativeFromCodePoint = !!String.fromCodePoint;\nvar hasNativeFromEntries = !!Object.fromEntries;\nvar hasNativeCodePointAt = !!String.prototype.codePointAt;\nvar hasTrimStart = !!String.prototype.trimStart;\nvar hasTrimEnd = !!String.prototype.trimEnd;\nvar hasNativeIsSafeInteger = !!Number.isSafeInteger;\nvar isSafeInteger = hasNativeIsSafeInteger\n    ? Number.isSafeInteger\n    : function (n) {\n        return (typeof n === 'number' &&\n            isFinite(n) &&\n            Math.floor(n) === n &&\n            Math.abs(n) <= 0x1fffffffffffff);\n    };\n// IE11 does not support y and u.\nvar REGEX_SUPPORTS_U_AND_Y = true;\ntry {\n    var re = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    /**\n     * legacy Edge or Xbox One browser\n     * Unicode flag support: supported\n     * Pattern_Syntax support: not supported\n     * See https://github.com/formatjs/formatjs/issues/2822\n     */\n    REGEX_SUPPORTS_U_AND_Y = ((_a = re.exec('a')) === null || _a === void 0 ? void 0 : _a[0]) === 'a';\n}\ncatch (_) {\n    REGEX_SUPPORTS_U_AND_Y = false;\n}\nvar startsWith = hasNativeStartsWith\n    ? // Native\n        function startsWith(s, search, position) {\n            return s.startsWith(search, position);\n        }\n    : // For IE11\n        function startsWith(s, search, position) {\n            return s.slice(position, position + search.length) === search;\n        };\nvar fromCodePoint = hasNativeFromCodePoint\n    ? String.fromCodePoint\n    : // IE11\n        function fromCodePoint() {\n            var codePoints = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                codePoints[_i] = arguments[_i];\n            }\n            var elements = '';\n            var length = codePoints.length;\n            var i = 0;\n            var code;\n            while (length > i) {\n                code = codePoints[i++];\n                if (code > 0x10ffff)\n                    throw RangeError(code + ' is not a valid code point');\n                elements +=\n                    code < 0x10000\n                        ? String.fromCharCode(code)\n                        : String.fromCharCode(((code -= 0x10000) >> 10) + 0xd800, (code % 0x400) + 0xdc00);\n            }\n            return elements;\n        };\nvar fromEntries = \n// native\nhasNativeFromEntries\n    ? Object.fromEntries\n    : // Ponyfill\n        function fromEntries(entries) {\n            var obj = {};\n            for (var _i = 0, entries_1 = entries; _i < entries_1.length; _i++) {\n                var _a = entries_1[_i], k = _a[0], v = _a[1];\n                obj[k] = v;\n            }\n            return obj;\n        };\nvar codePointAt = hasNativeCodePointAt\n    ? // Native\n        function codePointAt(s, index) {\n            return s.codePointAt(index);\n        }\n    : // IE 11\n        function codePointAt(s, index) {\n            var size = s.length;\n            if (index < 0 || index >= size) {\n                return undefined;\n            }\n            var first = s.charCodeAt(index);\n            var second;\n            return first < 0xd800 ||\n                first > 0xdbff ||\n                index + 1 === size ||\n                (second = s.charCodeAt(index + 1)) < 0xdc00 ||\n                second > 0xdfff\n                ? first\n                : ((first - 0xd800) << 10) + (second - 0xdc00) + 0x10000;\n        };\nvar trimStart = hasTrimStart\n    ? // Native\n        function trimStart(s) {\n            return s.trimStart();\n        }\n    : // Ponyfill\n        function trimStart(s) {\n            return s.replace(SPACE_SEPARATOR_START_REGEX, '');\n        };\nvar trimEnd = hasTrimEnd\n    ? // Native\n        function trimEnd(s) {\n            return s.trimEnd();\n        }\n    : // Ponyfill\n        function trimEnd(s) {\n            return s.replace(SPACE_SEPARATOR_END_REGEX, '');\n        };\n// Prevent minifier to translate new RegExp to literal form that might cause syntax error on IE11.\nfunction RE(s, flag) {\n    return new RegExp(s, flag);\n}\n// #endregion\nvar matchIdentifierAtIndex;\nif (REGEX_SUPPORTS_U_AND_Y) {\n    // Native\n    var IDENTIFIER_PREFIX_RE_1 = RE('([^\\\\p{White_Space}\\\\p{Pattern_Syntax}]*)', 'yu');\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var _a;\n        IDENTIFIER_PREFIX_RE_1.lastIndex = index;\n        var match = IDENTIFIER_PREFIX_RE_1.exec(s);\n        return (_a = match[1]) !== null && _a !== void 0 ? _a : '';\n    };\n}\nelse {\n    // IE11\n    matchIdentifierAtIndex = function matchIdentifierAtIndex(s, index) {\n        var match = [];\n        while (true) {\n            var c = codePointAt(s, index);\n            if (c === undefined || _isWhiteSpace(c) || _isPatternSyntax(c)) {\n                break;\n            }\n            match.push(c);\n            index += c >= 0x10000 ? 2 : 1;\n        }\n        return fromCodePoint.apply(void 0, match);\n    };\n}\nvar Parser = /** @class */ (function () {\n    function Parser(message, options) {\n        if (options === void 0) { options = {}; }\n        this.message = message;\n        this.position = { offset: 0, line: 1, column: 1 };\n        this.ignoreTag = !!options.ignoreTag;\n        this.locale = options.locale;\n        this.requiresOtherClause = !!options.requiresOtherClause;\n        this.shouldParseSkeletons = !!options.shouldParseSkeletons;\n    }\n    Parser.prototype.parse = function () {\n        if (this.offset() !== 0) {\n            throw Error('parser can only be used once');\n        }\n        return this.parseMessage(0, '', false);\n    };\n    Parser.prototype.parseMessage = function (nestingLevel, parentArgType, expectingCloseTag) {\n        var elements = [];\n        while (!this.isEOF()) {\n            var char = this.char();\n            if (char === 123 /* `{` */) {\n                var result = this.parseArgument(nestingLevel, expectingCloseTag);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else if (char === 125 /* `}` */ && nestingLevel > 0) {\n                break;\n            }\n            else if (char === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) {\n                var position = this.clonePosition();\n                this.bump();\n                elements.push({\n                    type: TYPE.pound,\n                    location: createLocation(position, this.clonePosition()),\n                });\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                this.peek() === 47 // char code for '/'\n            ) {\n                if (expectingCloseTag) {\n                    break;\n                }\n                else {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(this.clonePosition(), this.clonePosition()));\n                }\n            }\n            else if (char === 60 /* `<` */ &&\n                !this.ignoreTag &&\n                _isAlpha(this.peek() || 0)) {\n                var result = this.parseTag(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n            else {\n                var result = this.parseLiteral(nestingLevel, parentArgType);\n                if (result.err) {\n                    return result;\n                }\n                elements.push(result.val);\n            }\n        }\n        return { val: elements, err: null };\n    };\n    /**\n     * A tag name must start with an ASCII lower/upper case letter. The grammar is based on the\n     * [custom element name][] except that a dash is NOT always mandatory and uppercase letters\n     * are accepted:\n     *\n     * ```\n     * tag ::= \"<\" tagName (whitespace)* \"/>\" | \"<\" tagName (whitespace)* \">\" message \"</\" tagName (whitespace)* \">\"\n     * tagName ::= [a-z] (PENChar)*\n     * PENChar ::=\n     *     \"-\" | \".\" | [0-9] | \"_\" | [a-z] | [A-Z] | #xB7 | [#xC0-#xD6] | [#xD8-#xF6] | [#xF8-#x37D] |\n     *     [#x37F-#x1FFF] | [#x200C-#x200D] | [#x203F-#x2040] | [#x2070-#x218F] | [#x2C00-#x2FEF] |\n     *     [#x3001-#xD7FF] | [#xF900-#xFDCF] | [#xFDF0-#xFFFD] | [#x10000-#xEFFFF]\n     * ```\n     *\n     * [custom element name]: https://html.spec.whatwg.org/multipage/custom-elements.html#valid-custom-element-name\n     * NOTE: We're a bit more lax here since HTML technically does not allow uppercase HTML element but we do\n     * since other tag-based engines like React allow it\n     */\n    Parser.prototype.parseTag = function (nestingLevel, parentArgType) {\n        var startPosition = this.clonePosition();\n        this.bump(); // `<`\n        var tagName = this.parseTagName();\n        this.bumpSpace();\n        if (this.bumpIf('/>')) {\n            // Self closing tag\n            return {\n                val: {\n                    type: TYPE.literal,\n                    value: \"<\".concat(tagName, \"/>\"),\n                    location: createLocation(startPosition, this.clonePosition()),\n                },\n                err: null,\n            };\n        }\n        else if (this.bumpIf('>')) {\n            var childrenResult = this.parseMessage(nestingLevel + 1, parentArgType, true);\n            if (childrenResult.err) {\n                return childrenResult;\n            }\n            var children = childrenResult.val;\n            // Expecting a close tag\n            var endTagStartPosition = this.clonePosition();\n            if (this.bumpIf('</')) {\n                if (this.isEOF() || !_isAlpha(this.char())) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                var closingTagNameStartPosition = this.clonePosition();\n                var closingTagName = this.parseTagName();\n                if (tagName !== closingTagName) {\n                    return this.error(ErrorKind.UNMATCHED_CLOSING_TAG, createLocation(closingTagNameStartPosition, this.clonePosition()));\n                }\n                this.bumpSpace();\n                if (!this.bumpIf('>')) {\n                    return this.error(ErrorKind.INVALID_TAG, createLocation(endTagStartPosition, this.clonePosition()));\n                }\n                return {\n                    val: {\n                        type: TYPE.tag,\n                        value: tagName,\n                        children: children,\n                        location: createLocation(startPosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            else {\n                return this.error(ErrorKind.UNCLOSED_TAG, createLocation(startPosition, this.clonePosition()));\n            }\n        }\n        else {\n            return this.error(ErrorKind.INVALID_TAG, createLocation(startPosition, this.clonePosition()));\n        }\n    };\n    /**\n     * This method assumes that the caller has peeked ahead for the first tag character.\n     */\n    Parser.prototype.parseTagName = function () {\n        var startOffset = this.offset();\n        this.bump(); // the first tag name character\n        while (!this.isEOF() && _isPotentialElementNameChar(this.char())) {\n            this.bump();\n        }\n        return this.message.slice(startOffset, this.offset());\n    };\n    Parser.prototype.parseLiteral = function (nestingLevel, parentArgType) {\n        var start = this.clonePosition();\n        var value = '';\n        while (true) {\n            var parseQuoteResult = this.tryParseQuote(parentArgType);\n            if (parseQuoteResult) {\n                value += parseQuoteResult;\n                continue;\n            }\n            var parseUnquotedResult = this.tryParseUnquoted(nestingLevel, parentArgType);\n            if (parseUnquotedResult) {\n                value += parseUnquotedResult;\n                continue;\n            }\n            var parseLeftAngleResult = this.tryParseLeftAngleBracket();\n            if (parseLeftAngleResult) {\n                value += parseLeftAngleResult;\n                continue;\n            }\n            break;\n        }\n        var location = createLocation(start, this.clonePosition());\n        return {\n            val: { type: TYPE.literal, value: value, location: location },\n            err: null,\n        };\n    };\n    Parser.prototype.tryParseLeftAngleBracket = function () {\n        if (!this.isEOF() &&\n            this.char() === 60 /* `<` */ &&\n            (this.ignoreTag ||\n                // If at the opening tag or closing tag position, bail.\n                !_isAlphaOrSlash(this.peek() || 0))) {\n            this.bump(); // `<`\n            return '<';\n        }\n        return null;\n    };\n    /**\n     * Starting with ICU 4.8, an ASCII apostrophe only starts quoted text if it immediately precedes\n     * a character that requires quoting (that is, \"only where needed\"), and works the same in\n     * nested messages as on the top level of the pattern. The new behavior is otherwise compatible.\n     */\n    Parser.prototype.tryParseQuote = function (parentArgType) {\n        if (this.isEOF() || this.char() !== 39 /* `'` */) {\n            return null;\n        }\n        // Parse escaped char following the apostrophe, or early return if there is no escaped char.\n        // Check if is valid escaped character\n        switch (this.peek()) {\n            case 39 /* `'` */:\n                // double quote, should return as a single quote.\n                this.bump();\n                this.bump();\n                return \"'\";\n            // '{', '<', '>', '}'\n            case 123:\n            case 60:\n            case 62:\n            case 125:\n                break;\n            case 35: // '#'\n                if (parentArgType === 'plural' || parentArgType === 'selectordinal') {\n                    break;\n                }\n                return null;\n            default:\n                return null;\n        }\n        this.bump(); // apostrophe\n        var codePoints = [this.char()]; // escaped char\n        this.bump();\n        // read chars until the optional closing apostrophe is found\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch === 39 /* `'` */) {\n                if (this.peek() === 39 /* `'` */) {\n                    codePoints.push(39);\n                    // Bump one more time because we need to skip 2 characters.\n                    this.bump();\n                }\n                else {\n                    // Optional closing apostrophe.\n                    this.bump();\n                    break;\n                }\n            }\n            else {\n                codePoints.push(ch);\n            }\n            this.bump();\n        }\n        return fromCodePoint.apply(void 0, codePoints);\n    };\n    Parser.prototype.tryParseUnquoted = function (nestingLevel, parentArgType) {\n        if (this.isEOF()) {\n            return null;\n        }\n        var ch = this.char();\n        if (ch === 60 /* `<` */ ||\n            ch === 123 /* `{` */ ||\n            (ch === 35 /* `#` */ &&\n                (parentArgType === 'plural' || parentArgType === 'selectordinal')) ||\n            (ch === 125 /* `}` */ && nestingLevel > 0)) {\n            return null;\n        }\n        else {\n            this.bump();\n            return fromCodePoint(ch);\n        }\n    };\n    Parser.prototype.parseArgument = function (nestingLevel, expectingCloseTag) {\n        var openingBracePosition = this.clonePosition();\n        this.bump(); // `{`\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        if (this.char() === 125 /* `}` */) {\n            this.bump();\n            return this.error(ErrorKind.EMPTY_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        // argument name\n        var value = this.parseIdentifierIfPossible().value;\n        if (!value) {\n            return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bumpSpace();\n        if (this.isEOF()) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        switch (this.char()) {\n            // Simple argument: `{name}`\n            case 125 /* `}` */: {\n                this.bump(); // `}`\n                return {\n                    val: {\n                        type: TYPE.argument,\n                        // value does not include the opening and closing braces.\n                        value: value,\n                        location: createLocation(openingBracePosition, this.clonePosition()),\n                    },\n                    err: null,\n                };\n            }\n            // Argument with options: `{name, format, ...}`\n            case 44 /* `,` */: {\n                this.bump(); // `,`\n                this.bumpSpace();\n                if (this.isEOF()) {\n                    return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n                }\n                return this.parseArgumentOptions(nestingLevel, expectingCloseTag, value, openingBracePosition);\n            }\n            default:\n                return this.error(ErrorKind.MALFORMED_ARGUMENT, createLocation(openingBracePosition, this.clonePosition()));\n        }\n    };\n    /**\n     * Advance the parser until the end of the identifier, if it is currently on\n     * an identifier character. Return an empty string otherwise.\n     */\n    Parser.prototype.parseIdentifierIfPossible = function () {\n        var startingPosition = this.clonePosition();\n        var startOffset = this.offset();\n        var value = matchIdentifierAtIndex(this.message, startOffset);\n        var endOffset = startOffset + value.length;\n        this.bumpTo(endOffset);\n        var endPosition = this.clonePosition();\n        var location = createLocation(startingPosition, endPosition);\n        return { value: value, location: location };\n    };\n    Parser.prototype.parseArgumentOptions = function (nestingLevel, expectingCloseTag, value, openingBracePosition) {\n        var _a;\n        // Parse this range:\n        // {name, type, style}\n        //        ^---^\n        var typeStartPosition = this.clonePosition();\n        var argType = this.parseIdentifierIfPossible().value;\n        var typeEndPosition = this.clonePosition();\n        switch (argType) {\n            case '':\n                // Expecting a style string number, date, time, plural, selectordinal, or select.\n                return this.error(ErrorKind.EXPECT_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n            case 'number':\n            case 'date':\n            case 'time': {\n                // Parse this range:\n                // {name, number, style}\n                //              ^-------^\n                this.bumpSpace();\n                var styleAndLocation = null;\n                if (this.bumpIf(',')) {\n                    this.bumpSpace();\n                    var styleStartPosition = this.clonePosition();\n                    var result = this.parseSimpleArgStyleIfPossible();\n                    if (result.err) {\n                        return result;\n                    }\n                    var style = trimEnd(result.val);\n                    if (style.length === 0) {\n                        return this.error(ErrorKind.EXPECT_ARGUMENT_STYLE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    var styleLocation = createLocation(styleStartPosition, this.clonePosition());\n                    styleAndLocation = { style: style, styleLocation: styleLocation };\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_1 = createLocation(openingBracePosition, this.clonePosition());\n                // Extract style or skeleton\n                if (styleAndLocation && startsWith(styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style, '::', 0)) {\n                    // Skeleton starts with `::`.\n                    var skeleton = trimStart(styleAndLocation.style.slice(2));\n                    if (argType === 'number') {\n                        var result = this.parseNumberSkeletonFromString(skeleton, styleAndLocation.styleLocation);\n                        if (result.err) {\n                            return result;\n                        }\n                        return {\n                            val: { type: TYPE.number, value: value, location: location_1, style: result.val },\n                            err: null,\n                        };\n                    }\n                    else {\n                        if (skeleton.length === 0) {\n                            return this.error(ErrorKind.EXPECT_DATE_TIME_SKELETON, location_1);\n                        }\n                        var dateTimePattern = skeleton;\n                        // Get \"best match\" pattern only if locale is passed, if not, let it\n                        // pass as-is where `parseDateTimeSkeleton()` will throw an error\n                        // for unsupported patterns.\n                        if (this.locale) {\n                            dateTimePattern = getBestPattern(skeleton, this.locale);\n                        }\n                        var style = {\n                            type: SKELETON_TYPE.dateTime,\n                            pattern: dateTimePattern,\n                            location: styleAndLocation.styleLocation,\n                            parsedOptions: this.shouldParseSkeletons\n                                ? parseDateTimeSkeleton(dateTimePattern)\n                                : {},\n                        };\n                        var type = argType === 'date' ? TYPE.date : TYPE.time;\n                        return {\n                            val: { type: type, value: value, location: location_1, style: style },\n                            err: null,\n                        };\n                    }\n                }\n                // Regular style or no style.\n                return {\n                    val: {\n                        type: argType === 'number'\n                            ? TYPE.number\n                            : argType === 'date'\n                                ? TYPE.date\n                                : TYPE.time,\n                        value: value,\n                        location: location_1,\n                        style: (_a = styleAndLocation === null || styleAndLocation === void 0 ? void 0 : styleAndLocation.style) !== null && _a !== void 0 ? _a : null,\n                    },\n                    err: null,\n                };\n            }\n            case 'plural':\n            case 'selectordinal':\n            case 'select': {\n                // Parse this range:\n                // {name, plural, options}\n                //              ^---------^\n                var typeEndPosition_1 = this.clonePosition();\n                this.bumpSpace();\n                if (!this.bumpIf(',')) {\n                    return this.error(ErrorKind.EXPECT_SELECT_ARGUMENT_OPTIONS, createLocation(typeEndPosition_1, __assign({}, typeEndPosition_1)));\n                }\n                this.bumpSpace();\n                // Parse offset:\n                // {name, plural, offset:1, options}\n                //                ^-----^\n                //\n                // or the first option:\n                //\n                // {name, plural, one {...} other {...}}\n                //                ^--^\n                var identifierAndLocation = this.parseIdentifierIfPossible();\n                var pluralOffset = 0;\n                if (argType !== 'select' && identifierAndLocation.value === 'offset') {\n                    if (!this.bumpIf(':')) {\n                        return this.error(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, createLocation(this.clonePosition(), this.clonePosition()));\n                    }\n                    this.bumpSpace();\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_OFFSET_VALUE, ErrorKind.INVALID_PLURAL_ARGUMENT_OFFSET_VALUE);\n                    if (result.err) {\n                        return result;\n                    }\n                    // Parse another identifier for option parsing\n                    this.bumpSpace();\n                    identifierAndLocation = this.parseIdentifierIfPossible();\n                    pluralOffset = result.val;\n                }\n                var optionsResult = this.tryParsePluralOrSelectOptions(nestingLevel, argType, expectingCloseTag, identifierAndLocation);\n                if (optionsResult.err) {\n                    return optionsResult;\n                }\n                var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n                if (argCloseResult.err) {\n                    return argCloseResult;\n                }\n                var location_2 = createLocation(openingBracePosition, this.clonePosition());\n                if (argType === 'select') {\n                    return {\n                        val: {\n                            type: TYPE.select,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n                else {\n                    return {\n                        val: {\n                            type: TYPE.plural,\n                            value: value,\n                            options: fromEntries(optionsResult.val),\n                            offset: pluralOffset,\n                            pluralType: argType === 'plural' ? 'cardinal' : 'ordinal',\n                            location: location_2,\n                        },\n                        err: null,\n                    };\n                }\n            }\n            default:\n                return this.error(ErrorKind.INVALID_ARGUMENT_TYPE, createLocation(typeStartPosition, typeEndPosition));\n        }\n    };\n    Parser.prototype.tryParseArgumentClose = function (openingBracePosition) {\n        // Parse: {value, number, ::currency/GBP }\n        //\n        if (this.isEOF() || this.char() !== 125 /* `}` */) {\n            return this.error(ErrorKind.EXPECT_ARGUMENT_CLOSING_BRACE, createLocation(openingBracePosition, this.clonePosition()));\n        }\n        this.bump(); // `}`\n        return { val: true, err: null };\n    };\n    /**\n     * See: https://github.com/unicode-org/icu/blob/af7ed1f6d2298013dc303628438ec4abe1f16479/icu4c/source/common/messagepattern.cpp#L659\n     */\n    Parser.prototype.parseSimpleArgStyleIfPossible = function () {\n        var nestedBraces = 0;\n        var startPosition = this.clonePosition();\n        while (!this.isEOF()) {\n            var ch = this.char();\n            switch (ch) {\n                case 39 /* `'` */: {\n                    // Treat apostrophe as quoting but include it in the style part.\n                    // Find the end of the quoted literal text.\n                    this.bump();\n                    var apostrophePosition = this.clonePosition();\n                    if (!this.bumpUntil(\"'\")) {\n                        return this.error(ErrorKind.UNCLOSED_QUOTE_IN_ARGUMENT_STYLE, createLocation(apostrophePosition, this.clonePosition()));\n                    }\n                    this.bump();\n                    break;\n                }\n                case 123 /* `{` */: {\n                    nestedBraces += 1;\n                    this.bump();\n                    break;\n                }\n                case 125 /* `}` */: {\n                    if (nestedBraces > 0) {\n                        nestedBraces -= 1;\n                    }\n                    else {\n                        return {\n                            val: this.message.slice(startPosition.offset, this.offset()),\n                            err: null,\n                        };\n                    }\n                    break;\n                }\n                default:\n                    this.bump();\n                    break;\n            }\n        }\n        return {\n            val: this.message.slice(startPosition.offset, this.offset()),\n            err: null,\n        };\n    };\n    Parser.prototype.parseNumberSkeletonFromString = function (skeleton, location) {\n        var tokens = [];\n        try {\n            tokens = parseNumberSkeletonFromString(skeleton);\n        }\n        catch (e) {\n            return this.error(ErrorKind.INVALID_NUMBER_SKELETON, location);\n        }\n        return {\n            val: {\n                type: SKELETON_TYPE.number,\n                tokens: tokens,\n                location: location,\n                parsedOptions: this.shouldParseSkeletons\n                    ? parseNumberSkeleton(tokens)\n                    : {},\n            },\n            err: null,\n        };\n    };\n    /**\n     * @param nesting_level The current nesting level of messages.\n     *     This can be positive when parsing message fragment in select or plural argument options.\n     * @param parent_arg_type The parent argument's type.\n     * @param parsed_first_identifier If provided, this is the first identifier-like selector of\n     *     the argument. It is a by-product of a previous parsing attempt.\n     * @param expecting_close_tag If true, this message is directly or indirectly nested inside\n     *     between a pair of opening and closing tags. The nested message will not parse beyond\n     *     the closing tag boundary.\n     */\n    Parser.prototype.tryParsePluralOrSelectOptions = function (nestingLevel, parentArgType, expectCloseTag, parsedFirstIdentifier) {\n        var _a;\n        var hasOtherClause = false;\n        var options = [];\n        var parsedSelectors = new Set();\n        var selector = parsedFirstIdentifier.value, selectorLocation = parsedFirstIdentifier.location;\n        // Parse:\n        // one {one apple}\n        // ^--^\n        while (true) {\n            if (selector.length === 0) {\n                var startPosition = this.clonePosition();\n                if (parentArgType !== 'select' && this.bumpIf('=')) {\n                    // Try parse `={number}` selector\n                    var result = this.tryParseDecimalInteger(ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, ErrorKind.INVALID_PLURAL_ARGUMENT_SELECTOR);\n                    if (result.err) {\n                        return result;\n                    }\n                    selectorLocation = createLocation(startPosition, this.clonePosition());\n                    selector = this.message.slice(startPosition.offset, this.offset());\n                }\n                else {\n                    break;\n                }\n            }\n            // Duplicate selector clauses\n            if (parsedSelectors.has(selector)) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.DUPLICATE_SELECT_ARGUMENT_SELECTOR\n                    : ErrorKind.DUPLICATE_PLURAL_ARGUMENT_SELECTOR, selectorLocation);\n            }\n            if (selector === 'other') {\n                hasOtherClause = true;\n            }\n            // Parse:\n            // one {one apple}\n            //     ^----------^\n            this.bumpSpace();\n            var openingBracePosition = this.clonePosition();\n            if (!this.bumpIf('{')) {\n                return this.error(parentArgType === 'select'\n                    ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR_FRAGMENT\n                    : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR_FRAGMENT, createLocation(this.clonePosition(), this.clonePosition()));\n            }\n            var fragmentResult = this.parseMessage(nestingLevel + 1, parentArgType, expectCloseTag);\n            if (fragmentResult.err) {\n                return fragmentResult;\n            }\n            var argCloseResult = this.tryParseArgumentClose(openingBracePosition);\n            if (argCloseResult.err) {\n                return argCloseResult;\n            }\n            options.push([\n                selector,\n                {\n                    value: fragmentResult.val,\n                    location: createLocation(openingBracePosition, this.clonePosition()),\n                },\n            ]);\n            // Keep track of the existing selectors\n            parsedSelectors.add(selector);\n            // Prep next selector clause.\n            this.bumpSpace();\n            (_a = this.parseIdentifierIfPossible(), selector = _a.value, selectorLocation = _a.location);\n        }\n        if (options.length === 0) {\n            return this.error(parentArgType === 'select'\n                ? ErrorKind.EXPECT_SELECT_ARGUMENT_SELECTOR\n                : ErrorKind.EXPECT_PLURAL_ARGUMENT_SELECTOR, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        if (this.requiresOtherClause && !hasOtherClause) {\n            return this.error(ErrorKind.MISSING_OTHER_CLAUSE, createLocation(this.clonePosition(), this.clonePosition()));\n        }\n        return { val: options, err: null };\n    };\n    Parser.prototype.tryParseDecimalInteger = function (expectNumberError, invalidNumberError) {\n        var sign = 1;\n        var startingPosition = this.clonePosition();\n        if (this.bumpIf('+')) {\n        }\n        else if (this.bumpIf('-')) {\n            sign = -1;\n        }\n        var hasDigits = false;\n        var decimal = 0;\n        while (!this.isEOF()) {\n            var ch = this.char();\n            if (ch >= 48 /* `0` */ && ch <= 57 /* `9` */) {\n                hasDigits = true;\n                decimal = decimal * 10 + (ch - 48);\n                this.bump();\n            }\n            else {\n                break;\n            }\n        }\n        var location = createLocation(startingPosition, this.clonePosition());\n        if (!hasDigits) {\n            return this.error(expectNumberError, location);\n        }\n        decimal *= sign;\n        if (!isSafeInteger(decimal)) {\n            return this.error(invalidNumberError, location);\n        }\n        return { val: decimal, err: null };\n    };\n    Parser.prototype.offset = function () {\n        return this.position.offset;\n    };\n    Parser.prototype.isEOF = function () {\n        return this.offset() === this.message.length;\n    };\n    Parser.prototype.clonePosition = function () {\n        // This is much faster than `Object.assign` or spread.\n        return {\n            offset: this.position.offset,\n            line: this.position.line,\n            column: this.position.column,\n        };\n    };\n    /**\n     * Return the code point at the current position of the parser.\n     * Throws if the index is out of bound.\n     */\n    Parser.prototype.char = function () {\n        var offset = this.position.offset;\n        if (offset >= this.message.length) {\n            throw Error('out of bound');\n        }\n        var code = codePointAt(this.message, offset);\n        if (code === undefined) {\n            throw Error(\"Offset \".concat(offset, \" is at invalid UTF-16 code unit boundary\"));\n        }\n        return code;\n    };\n    Parser.prototype.error = function (kind, location) {\n        return {\n            val: null,\n            err: {\n                kind: kind,\n                message: this.message,\n                location: location,\n            },\n        };\n    };\n    /** Bump the parser to the next UTF-16 code unit. */\n    Parser.prototype.bump = function () {\n        if (this.isEOF()) {\n            return;\n        }\n        var code = this.char();\n        if (code === 10 /* '\\n' */) {\n            this.position.line += 1;\n            this.position.column = 1;\n            this.position.offset += 1;\n        }\n        else {\n            this.position.column += 1;\n            // 0 ~ 0x10000 -> unicode BMP, otherwise skip the surrogate pair.\n            this.position.offset += code < 0x10000 ? 1 : 2;\n        }\n    };\n    /**\n     * If the substring starting at the current position of the parser has\n     * the given prefix, then bump the parser to the character immediately\n     * following the prefix and return true. Otherwise, don't bump the parser\n     * and return false.\n     */\n    Parser.prototype.bumpIf = function (prefix) {\n        if (startsWith(this.message, prefix, this.offset())) {\n            for (var i = 0; i < prefix.length; i++) {\n                this.bump();\n            }\n            return true;\n        }\n        return false;\n    };\n    /**\n     * Bump the parser until the pattern character is found and return `true`.\n     * Otherwise bump to the end of the file and return `false`.\n     */\n    Parser.prototype.bumpUntil = function (pattern) {\n        var currentOffset = this.offset();\n        var index = this.message.indexOf(pattern, currentOffset);\n        if (index >= 0) {\n            this.bumpTo(index);\n            return true;\n        }\n        else {\n            this.bumpTo(this.message.length);\n            return false;\n        }\n    };\n    /**\n     * Bump the parser to the target offset.\n     * If target offset is beyond the end of the input, bump the parser to the end of the input.\n     */\n    Parser.prototype.bumpTo = function (targetOffset) {\n        if (this.offset() > targetOffset) {\n            throw Error(\"targetOffset \".concat(targetOffset, \" must be greater than or equal to the current offset \").concat(this.offset()));\n        }\n        targetOffset = Math.min(targetOffset, this.message.length);\n        while (true) {\n            var offset = this.offset();\n            if (offset === targetOffset) {\n                break;\n            }\n            if (offset > targetOffset) {\n                throw Error(\"targetOffset \".concat(targetOffset, \" is at invalid UTF-16 code unit boundary\"));\n            }\n            this.bump();\n            if (this.isEOF()) {\n                break;\n            }\n        }\n    };\n    /** advance the parser through all whitespace to the next non-whitespace code unit. */\n    Parser.prototype.bumpSpace = function () {\n        while (!this.isEOF() && _isWhiteSpace(this.char())) {\n            this.bump();\n        }\n    };\n    /**\n     * Peek at the *next* Unicode codepoint in the input without advancing the parser.\n     * If the input has been exhausted, then this returns null.\n     */\n    Parser.prototype.peek = function () {\n        if (this.isEOF()) {\n            return null;\n        }\n        var code = this.char();\n        var offset = this.offset();\n        var nextCode = this.message.charCodeAt(offset + (code >= 0x10000 ? 2 : 1));\n        return nextCode !== null && nextCode !== void 0 ? nextCode : null;\n    };\n    return Parser;\n}());\nexport { Parser };\n/**\n * This check if codepoint is alphabet (lower & uppercase)\n * @param codepoint\n * @returns\n */\nfunction _isAlpha(codepoint) {\n    return ((codepoint >= 97 && codepoint <= 122) ||\n        (codepoint >= 65 && codepoint <= 90));\n}\nfunction _isAlphaOrSlash(codepoint) {\n    return _isAlpha(codepoint) || codepoint === 47; /* '/' */\n}\n/** See `parseTag` function docs. */\nfunction _isPotentialElementNameChar(c) {\n    return (c === 45 /* '-' */ ||\n        c === 46 /* '.' */ ||\n        (c >= 48 && c <= 57) /* 0..9 */ ||\n        c === 95 /* '_' */ ||\n        (c >= 97 && c <= 122) /** a..z */ ||\n        (c >= 65 && c <= 90) /* A..Z */ ||\n        c == 0xb7 ||\n        (c >= 0xc0 && c <= 0xd6) ||\n        (c >= 0xd8 && c <= 0xf6) ||\n        (c >= 0xf8 && c <= 0x37d) ||\n        (c >= 0x37f && c <= 0x1fff) ||\n        (c >= 0x200c && c <= 0x200d) ||\n        (c >= 0x203f && c <= 0x2040) ||\n        (c >= 0x2070 && c <= 0x218f) ||\n        (c >= 0x2c00 && c <= 0x2fef) ||\n        (c >= 0x3001 && c <= 0xd7ff) ||\n        (c >= 0xf900 && c <= 0xfdcf) ||\n        (c >= 0xfdf0 && c <= 0xfffd) ||\n        (c >= 0x10000 && c <= 0xeffff));\n}\n/**\n * Code point equivalent of regex `\\p{White_Space}`.\n * From: https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isWhiteSpace(c) {\n    return ((c >= 0x0009 && c <= 0x000d) ||\n        c === 0x0020 ||\n        c === 0x0085 ||\n        (c >= 0x200e && c <= 0x200f) ||\n        c === 0x2028 ||\n        c === 0x2029);\n}\n/**\n * Code point equivalent of regex `\\p{Pattern_Syntax}`.\n * See https://www.unicode.org/Public/UCD/latest/ucd/PropList.txt\n */\nfunction _isPatternSyntax(c) {\n    return ((c >= 0x0021 && c <= 0x0023) ||\n        c === 0x0024 ||\n        (c >= 0x0025 && c <= 0x0027) ||\n        c === 0x0028 ||\n        c === 0x0029 ||\n        c === 0x002a ||\n        c === 0x002b ||\n        c === 0x002c ||\n        c === 0x002d ||\n        (c >= 0x002e && c <= 0x002f) ||\n        (c >= 0x003a && c <= 0x003b) ||\n        (c >= 0x003c && c <= 0x003e) ||\n        (c >= 0x003f && c <= 0x0040) ||\n        c === 0x005b ||\n        c === 0x005c ||\n        c === 0x005d ||\n        c === 0x005e ||\n        c === 0x0060 ||\n        c === 0x007b ||\n        c === 0x007c ||\n        c === 0x007d ||\n        c === 0x007e ||\n        c === 0x00a1 ||\n        (c >= 0x00a2 && c <= 0x00a5) ||\n        c === 0x00a6 ||\n        c === 0x00a7 ||\n        c === 0x00a9 ||\n        c === 0x00ab ||\n        c === 0x00ac ||\n        c === 0x00ae ||\n        c === 0x00b0 ||\n        c === 0x00b1 ||\n        c === 0x00b6 ||\n        c === 0x00bb ||\n        c === 0x00bf ||\n        c === 0x00d7 ||\n        c === 0x00f7 ||\n        (c >= 0x2010 && c <= 0x2015) ||\n        (c >= 0x2016 && c <= 0x2017) ||\n        c === 0x2018 ||\n        c === 0x2019 ||\n        c === 0x201a ||\n        (c >= 0x201b && c <= 0x201c) ||\n        c === 0x201d ||\n        c === 0x201e ||\n        c === 0x201f ||\n        (c >= 0x2020 && c <= 0x2027) ||\n        (c >= 0x2030 && c <= 0x2038) ||\n        c === 0x2039 ||\n        c === 0x203a ||\n        (c >= 0x203b && c <= 0x203e) ||\n        (c >= 0x2041 && c <= 0x2043) ||\n        c === 0x2044 ||\n        c === 0x2045 ||\n        c === 0x2046 ||\n        (c >= 0x2047 && c <= 0x2051) ||\n        c === 0x2052 ||\n        c === 0x2053 ||\n        (c >= 0x2055 && c <= 0x205e) ||\n        (c >= 0x2190 && c <= 0x2194) ||\n        (c >= 0x2195 && c <= 0x2199) ||\n        (c >= 0x219a && c <= 0x219b) ||\n        (c >= 0x219c && c <= 0x219f) ||\n        c === 0x21a0 ||\n        (c >= 0x21a1 && c <= 0x21a2) ||\n        c === 0x21a3 ||\n        (c >= 0x21a4 && c <= 0x21a5) ||\n        c === 0x21a6 ||\n        (c >= 0x21a7 && c <= 0x21ad) ||\n        c === 0x21ae ||\n        (c >= 0x21af && c <= 0x21cd) ||\n        (c >= 0x21ce && c <= 0x21cf) ||\n        (c >= 0x21d0 && c <= 0x21d1) ||\n        c === 0x21d2 ||\n        c === 0x21d3 ||\n        c === 0x21d4 ||\n        (c >= 0x21d5 && c <= 0x21f3) ||\n        (c >= 0x21f4 && c <= 0x22ff) ||\n        (c >= 0x2300 && c <= 0x2307) ||\n        c === 0x2308 ||\n        c === 0x2309 ||\n        c === 0x230a ||\n        c === 0x230b ||\n        (c >= 0x230c && c <= 0x231f) ||\n        (c >= 0x2320 && c <= 0x2321) ||\n        (c >= 0x2322 && c <= 0x2328) ||\n        c === 0x2329 ||\n        c === 0x232a ||\n        (c >= 0x232b && c <= 0x237b) ||\n        c === 0x237c ||\n        (c >= 0x237d && c <= 0x239a) ||\n        (c >= 0x239b && c <= 0x23b3) ||\n        (c >= 0x23b4 && c <= 0x23db) ||\n        (c >= 0x23dc && c <= 0x23e1) ||\n        (c >= 0x23e2 && c <= 0x2426) ||\n        (c >= 0x2427 && c <= 0x243f) ||\n        (c >= 0x2440 && c <= 0x244a) ||\n        (c >= 0x244b && c <= 0x245f) ||\n        (c >= 0x2500 && c <= 0x25b6) ||\n        c === 0x25b7 ||\n        (c >= 0x25b8 && c <= 0x25c0) ||\n        c === 0x25c1 ||\n        (c >= 0x25c2 && c <= 0x25f7) ||\n        (c >= 0x25f8 && c <= 0x25ff) ||\n        (c >= 0x2600 && c <= 0x266e) ||\n        c === 0x266f ||\n        (c >= 0x2670 && c <= 0x2767) ||\n        c === 0x2768 ||\n        c === 0x2769 ||\n        c === 0x276a ||\n        c === 0x276b ||\n        c === 0x276c ||\n        c === 0x276d ||\n        c === 0x276e ||\n        c === 0x276f ||\n        c === 0x2770 ||\n        c === 0x2771 ||\n        c === 0x2772 ||\n        c === 0x2773 ||\n        c === 0x2774 ||\n        c === 0x2775 ||\n        (c >= 0x2794 && c <= 0x27bf) ||\n        (c >= 0x27c0 && c <= 0x27c4) ||\n        c === 0x27c5 ||\n        c === 0x27c6 ||\n        (c >= 0x27c7 && c <= 0x27e5) ||\n        c === 0x27e6 ||\n        c === 0x27e7 ||\n        c === 0x27e8 ||\n        c === 0x27e9 ||\n        c === 0x27ea ||\n        c === 0x27eb ||\n        c === 0x27ec ||\n        c === 0x27ed ||\n        c === 0x27ee ||\n        c === 0x27ef ||\n        (c >= 0x27f0 && c <= 0x27ff) ||\n        (c >= 0x2800 && c <= 0x28ff) ||\n        (c >= 0x2900 && c <= 0x2982) ||\n        c === 0x2983 ||\n        c === 0x2984 ||\n        c === 0x2985 ||\n        c === 0x2986 ||\n        c === 0x2987 ||\n        c === 0x2988 ||\n        c === 0x2989 ||\n        c === 0x298a ||\n        c === 0x298b ||\n        c === 0x298c ||\n        c === 0x298d ||\n        c === 0x298e ||\n        c === 0x298f ||\n        c === 0x2990 ||\n        c === 0x2991 ||\n        c === 0x2992 ||\n        c === 0x2993 ||\n        c === 0x2994 ||\n        c === 0x2995 ||\n        c === 0x2996 ||\n        c === 0x2997 ||\n        c === 0x2998 ||\n        (c >= 0x2999 && c <= 0x29d7) ||\n        c === 0x29d8 ||\n        c === 0x29d9 ||\n        c === 0x29da ||\n        c === 0x29db ||\n        (c >= 0x29dc && c <= 0x29fb) ||\n        c === 0x29fc ||\n        c === 0x29fd ||\n        (c >= 0x29fe && c <= 0x2aff) ||\n        (c >= 0x2b00 && c <= 0x2b2f) ||\n        (c >= 0x2b30 && c <= 0x2b44) ||\n        (c >= 0x2b45 && c <= 0x2b46) ||\n        (c >= 0x2b47 && c <= 0x2b4c) ||\n        (c >= 0x2b4d && c <= 0x2b73) ||\n        (c >= 0x2b74 && c <= 0x2b75) ||\n        (c >= 0x2b76 && c <= 0x2b95) ||\n        c === 0x2b96 ||\n        (c >= 0x2b97 && c <= 0x2bff) ||\n        (c >= 0x2e00 && c <= 0x2e01) ||\n        c === 0x2e02 ||\n        c === 0x2e03 ||\n        c === 0x2e04 ||\n        c === 0x2e05 ||\n        (c >= 0x2e06 && c <= 0x2e08) ||\n        c === 0x2e09 ||\n        c === 0x2e0a ||\n        c === 0x2e0b ||\n        c === 0x2e0c ||\n        c === 0x2e0d ||\n        (c >= 0x2e0e && c <= 0x2e16) ||\n        c === 0x2e17 ||\n        (c >= 0x2e18 && c <= 0x2e19) ||\n        c === 0x2e1a ||\n        c === 0x2e1b ||\n        c === 0x2e1c ||\n        c === 0x2e1d ||\n        (c >= 0x2e1e && c <= 0x2e1f) ||\n        c === 0x2e20 ||\n        c === 0x2e21 ||\n        c === 0x2e22 ||\n        c === 0x2e23 ||\n        c === 0x2e24 ||\n        c === 0x2e25 ||\n        c === 0x2e26 ||\n        c === 0x2e27 ||\n        c === 0x2e28 ||\n        c === 0x2e29 ||\n        (c >= 0x2e2a && c <= 0x2e2e) ||\n        c === 0x2e2f ||\n        (c >= 0x2e30 && c <= 0x2e39) ||\n        (c >= 0x2e3a && c <= 0x2e3b) ||\n        (c >= 0x2e3c && c <= 0x2e3f) ||\n        c === 0x2e40 ||\n        c === 0x2e41 ||\n        c === 0x2e42 ||\n        (c >= 0x2e43 && c <= 0x2e4f) ||\n        (c >= 0x2e50 && c <= 0x2e51) ||\n        c === 0x2e52 ||\n        (c >= 0x2e53 && c <= 0x2e7f) ||\n        (c >= 0x3001 && c <= 0x3003) ||\n        c === 0x3008 ||\n        c === 0x3009 ||\n        c === 0x300a ||\n        c === 0x300b ||\n        c === 0x300c ||\n        c === 0x300d ||\n        c === 0x300e ||\n        c === 0x300f ||\n        c === 0x3010 ||\n        c === 0x3011 ||\n        (c >= 0x3012 && c <= 0x3013) ||\n        c === 0x3014 ||\n        c === 0x3015 ||\n        c === 0x3016 ||\n        c === 0x3017 ||\n        c === 0x3018 ||\n        c === 0x3019 ||\n        c === 0x301a ||\n        c === 0x301b ||\n        c === 0x301c ||\n        c === 0x301d ||\n        (c >= 0x301e && c <= 0x301f) ||\n        c === 0x3020 ||\n        c === 0x3030 ||\n        c === 0xfd3e ||\n        c === 0xfd3f ||\n        (c >= 0xfe45 && c <= 0xfe46));\n}\n", "import { __assign } from \"tslib\";\nimport { ErrorKind } from './error';\nimport { Parser } from './parser';\nimport { isDateElement, isDateTimeSkeleton, isNumberElement, isNumberSkeleton, isPluralElement, isSelectElement, isTagElement, isTimeElement, } from './types';\nfunction pruneLocation(els) {\n    els.forEach(function (el) {\n        delete el.location;\n        if (isSelectElement(el) || isPluralElement(el)) {\n            for (var k in el.options) {\n                delete el.options[k].location;\n                pruneLocation(el.options[k].value);\n            }\n        }\n        else if (isNumberElement(el) && isNumberSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if ((isDateElement(el) || isTimeElement(el)) &&\n            isDateTimeSkeleton(el.style)) {\n            delete el.style.location;\n        }\n        else if (isTagElement(el)) {\n            pruneLocation(el.children);\n        }\n    });\n}\nexport function parse(message, opts) {\n    if (opts === void 0) { opts = {}; }\n    opts = __assign({ shouldParseSkeletons: true, requiresOtherClause: true }, opts);\n    var result = new Parser(message, opts).parse();\n    if (result.err) {\n        var error = SyntaxError(ErrorKind[result.err.kind]);\n        // @ts-expect-error Assign to error object\n        error.location = result.err.location;\n        // @ts-expect-error Assign to error object\n        error.originalMessage = result.err.message;\n        throw error;\n    }\n    if (!(opts === null || opts === void 0 ? void 0 : opts.captureLocation)) {\n        pruneLocation(result.val);\n    }\n    return result.val;\n}\nexport * from './types';\n// only for testing\nexport var _Parser = Parser;\n", "//\n// Main\n//\nexport function memoize(fn, options) {\n    var cache = options && options.cache ? options.cache : cacheDefault;\n    var serializer = options && options.serializer ? options.serializer : serializerDefault;\n    var strategy = options && options.strategy ? options.strategy : strategyDefault;\n    return strategy(fn, {\n        cache: cache,\n        serializer: serializer,\n    });\n}\n//\n// Strategy\n//\nfunction isPrimitive(value) {\n    return (value == null || typeof value === 'number' || typeof value === 'boolean'); // || typeof value === \"string\" 'unsafe' primitive for our needs\n}\nfunction monadic(fn, cache, serializer, arg) {\n    var cacheKey = isPrimitive(arg) ? arg : serializer(arg);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.call(this, arg);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction variadic(fn, cache, serializer) {\n    var args = Array.prototype.slice.call(arguments, 3);\n    var cacheKey = serializer(args);\n    var computedValue = cache.get(cacheKey);\n    if (typeof computedValue === 'undefined') {\n        computedValue = fn.apply(this, args);\n        cache.set(cacheKey, computedValue);\n    }\n    return computedValue;\n}\nfunction assemble(fn, context, strategy, cache, serialize) {\n    return strategy.bind(context, fn, cache, serialize);\n}\nfunction strategyDefault(fn, options) {\n    var strategy = fn.length === 1 ? monadic : variadic;\n    return assemble(fn, this, strategy, options.cache.create(), options.serializer);\n}\nfunction strategyVariadic(fn, options) {\n    return assemble(fn, this, variadic, options.cache.create(), options.serializer);\n}\nfunction strategyMonadic(fn, options) {\n    return assemble(fn, this, monadic, options.cache.create(), options.serializer);\n}\n//\n// Serializer\n//\nvar serializerDefault = function () {\n    return JSON.stringify(arguments);\n};\n//\n// Cache\n//\nfunction ObjectWithoutPrototypeCache() {\n    this.cache = Object.create(null);\n}\nObjectWithoutPrototypeCache.prototype.get = function (key) {\n    return this.cache[key];\n};\nObjectWithoutPrototypeCache.prototype.set = function (key, value) {\n    this.cache[key] = value;\n};\nvar cacheDefault = {\n    create: function create() {\n        // @ts-ignore\n        return new ObjectWithoutPrototypeCache();\n    },\n};\nexport var strategies = {\n    variadic: strategyVariadic,\n    monadic: strategyMonadic,\n};\n", "import { __extends } from \"tslib\";\nexport var ErrorCode;\n(function (ErrorCode) {\n    // When we have a placeholder but no value to format\n    ErrorCode[\"MISSING_VALUE\"] = \"MISSING_VALUE\";\n    // When value supplied is invalid\n    ErrorCode[\"INVALID_VALUE\"] = \"INVALID_VALUE\";\n    // When we need specific Intl API but it's not available\n    ErrorCode[\"MISSING_INTL_API\"] = \"MISSING_INTL_API\";\n})(ErrorCode || (ErrorCode = {}));\nvar FormatError = /** @class */ (function (_super) {\n    __extends(FormatError, _super);\n    function FormatError(msg, code, originalMessage) {\n        var _this = _super.call(this, msg) || this;\n        _this.code = code;\n        _this.originalMessage = originalMessage;\n        return _this;\n    }\n    FormatError.prototype.toString = function () {\n        return \"[formatjs Error: \".concat(this.code, \"] \").concat(this.message);\n    };\n    return FormatError;\n}(Error));\nexport { FormatError };\nvar InvalidValueError = /** @class */ (function (_super) {\n    __extends(InvalidValueError, _super);\n    function InvalidValueError(variableId, value, options, originalMessage) {\n        return _super.call(this, \"Invalid values for \\\"\".concat(variableId, \"\\\": \\\"\").concat(value, \"\\\". Options are \\\"\").concat(Object.keys(options).join('\", \"'), \"\\\"\"), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueError;\n}(FormatError));\nexport { InvalidValueError };\nvar InvalidValueTypeError = /** @class */ (function (_super) {\n    __extends(InvalidValueTypeError, _super);\n    function InvalidValueTypeError(value, type, originalMessage) {\n        return _super.call(this, \"Value for \\\"\".concat(value, \"\\\" must be of type \").concat(type), ErrorCode.INVALID_VALUE, originalMessage) || this;\n    }\n    return InvalidValueTypeError;\n}(FormatError));\nexport { InvalidValueTypeError };\nvar MissingValueError = /** @class */ (function (_super) {\n    __extends(MissingValueError, _super);\n    function MissingValueError(variableId, originalMessage) {\n        return _super.call(this, \"The intl string context variable \\\"\".concat(variableId, \"\\\" was not provided to the string \\\"\").concat(originalMessage, \"\\\"\"), ErrorCode.MISSING_VALUE, originalMessage) || this;\n    }\n    return MissingValueError;\n}(FormatError));\nexport { MissingValueError };\n", "import { isArgumentElement, isDateElement, isDateTimeSkeleton, isLiteralElement, isNumberElement, isNumberSkeleton, isPluralElement, isPoundElement, isSelectElement, isTimeElement, isTagElement, } from '@formatjs/icu-messageformat-parser';\nimport { MissingValueError, InvalidValueError, ErrorCode, FormatError, InvalidValueTypeError, } from './error';\nexport var PART_TYPE;\n(function (PART_TYPE) {\n    PART_TYPE[PART_TYPE[\"literal\"] = 0] = \"literal\";\n    PART_TYPE[PART_TYPE[\"object\"] = 1] = \"object\";\n})(PART_TYPE || (PART_TYPE = {}));\nfunction mergeLiteral(parts) {\n    if (parts.length < 2) {\n        return parts;\n    }\n    return parts.reduce(function (all, part) {\n        var lastPart = all[all.length - 1];\n        if (!lastPart ||\n            lastPart.type !== PART_TYPE.literal ||\n            part.type !== PART_TYPE.literal) {\n            all.push(part);\n        }\n        else {\n            lastPart.value += part.value;\n        }\n        return all;\n    }, []);\n}\nexport function isFormatXMLElementFn(el) {\n    return typeof el === 'function';\n}\n// TODO(skeleton): add skeleton support\nexport function formatToParts(els, locales, formatters, formats, values, currentPluralValue, \n// For debugging\noriginalMessage) {\n    // Hot path for straight simple msg translations\n    if (els.length === 1 && isLiteralElement(els[0])) {\n        return [\n            {\n                type: PART_TYPE.literal,\n                value: els[0].value,\n            },\n        ];\n    }\n    var result = [];\n    for (var _i = 0, els_1 = els; _i < els_1.length; _i++) {\n        var el = els_1[_i];\n        // Exit early for string parts.\n        if (isLiteralElement(el)) {\n            result.push({\n                type: PART_TYPE.literal,\n                value: el.value,\n            });\n            continue;\n        }\n        // TODO: should this part be literal type?\n        // Replace `#` in plural rules with the actual numeric value.\n        if (isPoundElement(el)) {\n            if (typeof currentPluralValue === 'number') {\n                result.push({\n                    type: PART_TYPE.literal,\n                    value: formatters.getNumberFormat(locales).format(currentPluralValue),\n                });\n            }\n            continue;\n        }\n        var varName = el.value;\n        // Enforce that all required values are provided by the caller.\n        if (!(values && varName in values)) {\n            throw new MissingValueError(varName, originalMessage);\n        }\n        var value = values[varName];\n        if (isArgumentElement(el)) {\n            if (!value || typeof value === 'string' || typeof value === 'number') {\n                value =\n                    typeof value === 'string' || typeof value === 'number'\n                        ? String(value)\n                        : '';\n            }\n            result.push({\n                type: typeof value === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                value: value,\n            });\n            continue;\n        }\n        // Recursively format plural and select parts' option — which can be a\n        // nested pattern structure. The choosing of the option to use is\n        // abstracted-by and delegated-to the part helper object.\n        if (isDateElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.date[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTimeElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.time[el.style]\n                : isDateTimeSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : formats.time.medium;\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getDateTimeFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isNumberElement(el)) {\n            var style = typeof el.style === 'string'\n                ? formats.number[el.style]\n                : isNumberSkeleton(el.style)\n                    ? el.style.parsedOptions\n                    : undefined;\n            if (style && style.scale) {\n                value =\n                    value *\n                        (style.scale || 1);\n            }\n            result.push({\n                type: PART_TYPE.literal,\n                value: formatters\n                    .getNumberFormat(locales, style)\n                    .format(value),\n            });\n            continue;\n        }\n        if (isTagElement(el)) {\n            var children = el.children, value_1 = el.value;\n            var formatFn = values[value_1];\n            if (!isFormatXMLElementFn(formatFn)) {\n                throw new InvalidValueTypeError(value_1, 'function', originalMessage);\n            }\n            var parts = formatToParts(children, locales, formatters, formats, values, currentPluralValue);\n            var chunks = formatFn(parts.map(function (p) { return p.value; }));\n            if (!Array.isArray(chunks)) {\n                chunks = [chunks];\n            }\n            result.push.apply(result, chunks.map(function (c) {\n                return {\n                    type: typeof c === 'string' ? PART_TYPE.literal : PART_TYPE.object,\n                    value: c,\n                };\n            }));\n        }\n        if (isSelectElement(el)) {\n            var opt = el.options[value] || el.options.other;\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values));\n            continue;\n        }\n        if (isPluralElement(el)) {\n            var opt = el.options[\"=\".concat(value)];\n            if (!opt) {\n                if (!Intl.PluralRules) {\n                    throw new FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", ErrorCode.MISSING_INTL_API, originalMessage);\n                }\n                var rule = formatters\n                    .getPluralRules(locales, { type: el.pluralType })\n                    .select(value - (el.offset || 0));\n                opt = el.options[rule] || el.options.other;\n            }\n            if (!opt) {\n                throw new InvalidValueError(el.value, value, Object.keys(el.options), originalMessage);\n            }\n            result.push.apply(result, formatToParts(opt.value, locales, formatters, formats, values, value - (el.offset || 0)));\n            continue;\n        }\n    }\n    return mergeLiteral(result);\n}\n", "/*\nCopyright (c) 2014, Yahoo! Inc. All rights reserved.\nCopyrights licensed under the New BSD License.\nSee the accompanying LICENSE file for terms.\n*/\nimport { __assign, __rest, __spreadArray } from \"tslib\";\nimport { parse, } from '@formatjs/icu-messageformat-parser';\nimport { memoize, strategies } from '@formatjs/fast-memoize';\nimport { formatToParts, PART_TYPE, } from './formatters';\n// -- MessageFormat --------------------------------------------------------\nfunction mergeConfig(c1, c2) {\n    if (!c2) {\n        return c1;\n    }\n    return __assign(__assign(__assign({}, (c1 || {})), (c2 || {})), Object.keys(c1).reduce(function (all, k) {\n        all[k] = __assign(__assign({}, c1[k]), (c2[k] || {}));\n        return all;\n    }, {}));\n}\nfunction mergeConfigs(defaultConfig, configs) {\n    if (!configs) {\n        return defaultConfig;\n    }\n    return Object.keys(defaultConfig).reduce(function (all, k) {\n        all[k] = mergeConfig(defaultConfig[k], configs[k]);\n        return all;\n    }, __assign({}, defaultConfig));\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\nfunction createDefaultFormatters(cache) {\n    if (cache === void 0) { cache = {\n        number: {},\n        dateTime: {},\n        pluralRules: {},\n    }; }\n    return {\n        getNumberFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.NumberFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.number),\n            strategy: strategies.variadic,\n        }),\n        getDateTimeFormat: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.DateTimeFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.dateTime),\n            strategy: strategies.variadic,\n        }),\n        getPluralRules: memoize(function () {\n            var _a;\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new ((_a = Intl.PluralRules).bind.apply(_a, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.pluralRules),\n            strategy: strategies.variadic,\n        }),\n    };\n}\nvar IntlMessageFormat = /** @class */ (function () {\n    function IntlMessageFormat(message, locales, overrideFormats, opts) {\n        var _this = this;\n        if (locales === void 0) { locales = IntlMessageFormat.defaultLocale; }\n        this.formatterCache = {\n            number: {},\n            dateTime: {},\n            pluralRules: {},\n        };\n        this.format = function (values) {\n            var parts = _this.formatToParts(values);\n            // Hot path for straight simple msg translations\n            if (parts.length === 1) {\n                return parts[0].value;\n            }\n            var result = parts.reduce(function (all, part) {\n                if (!all.length ||\n                    part.type !== PART_TYPE.literal ||\n                    typeof all[all.length - 1] !== 'string') {\n                    all.push(part.value);\n                }\n                else {\n                    all[all.length - 1] += part.value;\n                }\n                return all;\n            }, []);\n            if (result.length <= 1) {\n                return result[0] || '';\n            }\n            return result;\n        };\n        this.formatToParts = function (values) {\n            return formatToParts(_this.ast, _this.locales, _this.formatters, _this.formats, values, undefined, _this.message);\n        };\n        this.resolvedOptions = function () {\n            var _a;\n            return ({\n                locale: ((_a = _this.resolvedLocale) === null || _a === void 0 ? void 0 : _a.toString()) ||\n                    Intl.NumberFormat.supportedLocalesOf(_this.locales)[0],\n            });\n        };\n        this.getAst = function () { return _this.ast; };\n        // Defined first because it's used to build the format pattern.\n        this.locales = locales;\n        this.resolvedLocale = IntlMessageFormat.resolveLocale(locales);\n        if (typeof message === 'string') {\n            this.message = message;\n            if (!IntlMessageFormat.__parse) {\n                throw new TypeError('IntlMessageFormat.__parse must be set to process `message` of type `string`');\n            }\n            var _a = opts || {}, formatters = _a.formatters, parseOpts = __rest(_a, [\"formatters\"]);\n            // Parse string messages into an AST.\n            this.ast = IntlMessageFormat.__parse(message, __assign(__assign({}, parseOpts), { locale: this.resolvedLocale }));\n        }\n        else {\n            this.ast = message;\n        }\n        if (!Array.isArray(this.ast)) {\n            throw new TypeError('A message must be provided as a String or AST.');\n        }\n        // Creates a new object with the specified `formats` merged with the default\n        // formats.\n        this.formats = mergeConfigs(IntlMessageFormat.formats, overrideFormats);\n        this.formatters =\n            (opts && opts.formatters) || createDefaultFormatters(this.formatterCache);\n    }\n    Object.defineProperty(IntlMessageFormat, \"defaultLocale\", {\n        get: function () {\n            if (!IntlMessageFormat.memoizedDefaultLocale) {\n                IntlMessageFormat.memoizedDefaultLocale =\n                    new Intl.NumberFormat().resolvedOptions().locale;\n            }\n            return IntlMessageFormat.memoizedDefaultLocale;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    IntlMessageFormat.memoizedDefaultLocale = null;\n    IntlMessageFormat.resolveLocale = function (locales) {\n        if (typeof Intl.Locale === 'undefined') {\n            return;\n        }\n        var supportedLocales = Intl.NumberFormat.supportedLocalesOf(locales);\n        if (supportedLocales.length > 0) {\n            return new Intl.Locale(supportedLocales[0]);\n        }\n        return new Intl.Locale(typeof locales === 'string' ? locales : locales[0]);\n    };\n    IntlMessageFormat.__parse = parse;\n    // Default format options used as the prototype of the `formats` provided to the\n    // constructor. These are used when constructing the internal Intl.NumberFormat\n    // and Intl.DateTimeFormat instances.\n    IntlMessageFormat.formats = {\n        number: {\n            integer: {\n                maximumFractionDigits: 0,\n            },\n            currency: {\n                style: 'currency',\n            },\n            percent: {\n                style: 'percent',\n            },\n        },\n        date: {\n            short: {\n                month: 'numeric',\n                day: 'numeric',\n                year: '2-digit',\n            },\n            medium: {\n                month: 'short',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            long: {\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n            full: {\n                weekday: 'long',\n                month: 'long',\n                day: 'numeric',\n                year: 'numeric',\n            },\n        },\n        time: {\n            short: {\n                hour: 'numeric',\n                minute: 'numeric',\n            },\n            medium: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n            },\n            long: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n            full: {\n                hour: 'numeric',\n                minute: 'numeric',\n                second: 'numeric',\n                timeZoneName: 'short',\n            },\n        },\n    };\n    return IntlMessageFormat;\n}());\nexport { IntlMessageFormat };\n", "import { __extends } from \"tslib\";\nexport var IntlErrorCode;\n(function (IntlErrorCode) {\n    IntlErrorCode[\"FORMAT_ERROR\"] = \"FORMAT_ERROR\";\n    IntlErrorCode[\"UNSUPPORTED_FORMATTER\"] = \"UNSUPPORTED_FORMATTER\";\n    IntlErrorCode[\"INVALID_CONFIG\"] = \"INVALID_CONFIG\";\n    IntlErrorCode[\"MISSING_DATA\"] = \"MISSING_DATA\";\n    IntlErrorCode[\"MISSING_TRANSLATION\"] = \"MISSING_TRANSLATION\";\n})(IntlErrorCode || (IntlErrorCode = {}));\nvar IntlError = /** @class */ (function (_super) {\n    __extends(IntlError, _super);\n    function IntlError(code, message, exception) {\n        var _this = this;\n        var err = exception\n            ? exception instanceof Error\n                ? exception\n                : new Error(String(exception))\n            : undefined;\n        _this = _super.call(this, \"[@formatjs/intl Error \".concat(code, \"] \").concat(message, \"\\n\").concat(err ? \"\\n\".concat(err.message, \"\\n\").concat(err.stack) : '')) || this;\n        _this.code = code;\n        // @ts-ignore just so we don't need to declare dep on @types/node\n        if (typeof Error.captureStackTrace === 'function') {\n            // @ts-ignore just so we don't need to declare dep on @types/node\n            Error.captureStackTrace(_this, IntlError);\n        }\n        return _this;\n    }\n    return IntlError;\n}(Error));\nexport { IntlError };\nvar UnsupportedFormatterError = /** @class */ (function (_super) {\n    __extends(UnsupportedFormatterError, _super);\n    function UnsupportedFormatterError(message, exception) {\n        return _super.call(this, IntlErrorCode.UNSUPPORTED_FORMATTER, message, exception) || this;\n    }\n    return UnsupportedFormatterError;\n}(IntlError));\nexport { UnsupportedFormatterError };\nvar InvalidConfigError = /** @class */ (function (_super) {\n    __extends(InvalidConfigError, _super);\n    function InvalidConfigError(message, exception) {\n        return _super.call(this, IntlErrorCode.INVALID_CONFIG, message, exception) || this;\n    }\n    return InvalidConfigError;\n}(IntlError));\nexport { InvalidConfigError };\nvar MissingDataError = /** @class */ (function (_super) {\n    __extends(MissingDataError, _super);\n    function MissingDataError(message, exception) {\n        return _super.call(this, IntlErrorCode.MISSING_DATA, message, exception) || this;\n    }\n    return MissingDataError;\n}(IntlError));\nexport { MissingDataError };\nvar IntlFormatError = /** @class */ (function (_super) {\n    __extends(IntlFormatError, _super);\n    function IntlFormatError(message, locale, exception) {\n        var _this = _super.call(this, IntlErrorCode.FORMAT_ERROR, \"\".concat(message, \"\\nLocale: \").concat(locale, \"\\n\"), exception) || this;\n        _this.locale = locale;\n        return _this;\n    }\n    return IntlFormatError;\n}(IntlError));\nexport { IntlFormatError };\nvar MessageFormatError = /** @class */ (function (_super) {\n    __extends(MessageFormatError, _super);\n    function MessageFormatError(message, locale, descriptor, exception) {\n        var _this = _super.call(this, \"\".concat(message, \"\\nMessageID: \").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.id, \"\\nDefault Message: \").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.defaultMessage, \"\\nDescription: \").concat(descriptor === null || descriptor === void 0 ? void 0 : descriptor.description, \"\\n\"), locale, exception) || this;\n        _this.descriptor = descriptor;\n        _this.locale = locale;\n        return _this;\n    }\n    return MessageFormatError;\n}(IntlFormatError));\nexport { MessageFormatError };\nvar MissingTranslationError = /** @class */ (function (_super) {\n    __extends(MissingTranslationError, _super);\n    function MissingTranslationError(descriptor, locale) {\n        var _this = _super.call(this, IntlErrorCode.MISSING_TRANSLATION, \"Missing message: \\\"\".concat(descriptor.id, \"\\\" for locale \\\"\").concat(locale, \"\\\", using \").concat(descriptor.defaultMessage\n            ? \"default message (\".concat(typeof descriptor.defaultMessage === 'string'\n                ? descriptor.defaultMessage\n                : descriptor.defaultMessage\n                    .map(function (e) { var _a; return (_a = e.value) !== null && _a !== void 0 ? _a : JSON.stringify(e); })\n                    .join(), \")\")\n            : 'id', \" as fallback.\")) || this;\n        _this.descriptor = descriptor;\n        return _this;\n    }\n    return MissingTranslationError;\n}(IntlError));\nexport { MissingTranslationError };\n", "import { __assign, __spreadArray } from \"tslib\";\nimport { IntlMessageFormat } from 'intl-messageformat';\nimport { memoize, strategies } from '@formatjs/fast-memoize';\nimport { UnsupportedFormatterError } from './error';\nexport function filterProps(props, allowlist, defaults) {\n    if (defaults === void 0) { defaults = {}; }\n    return allowlist.reduce(function (filtered, name) {\n        if (name in props) {\n            filtered[name] = props[name];\n        }\n        else if (name in defaults) {\n            filtered[name] = defaults[name];\n        }\n        return filtered;\n    }, {});\n}\nvar defaultErrorHandler = function (error) {\n    // @ts-ignore just so we don't need to declare dep on @types/node\n    if (process.env.NODE_ENV !== 'production') {\n        console.error(error);\n    }\n};\nvar defaultWarnHandler = function (warning) {\n    // @ts-ignore just so we don't need to declare dep on @types/node\n    if (process.env.NODE_ENV !== 'production') {\n        console.warn(warning);\n    }\n};\nexport var DEFAULT_INTL_CONFIG = {\n    formats: {},\n    messages: {},\n    timeZone: undefined,\n    defaultLocale: 'en',\n    defaultFormats: {},\n    fallbackOnEmptyString: true,\n    onError: defaultErrorHandler,\n    onWarn: defaultWarnHandler,\n};\nexport function createIntlCache() {\n    return {\n        dateTime: {},\n        number: {},\n        message: {},\n        relativeTime: {},\n        pluralRules: {},\n        list: {},\n        displayNames: {},\n    };\n}\nfunction createFastMemoizeCache(store) {\n    return {\n        create: function () {\n            return {\n                get: function (key) {\n                    return store[key];\n                },\n                set: function (key, value) {\n                    store[key] = value;\n                },\n            };\n        },\n    };\n}\n/**\n * Create intl formatters and populate cache\n * @param cache explicit cache to prevent leaking memory\n */\nexport function createFormatters(cache) {\n    if (cache === void 0) { cache = createIntlCache(); }\n    var RelativeTimeFormat = Intl.RelativeTimeFormat;\n    var ListFormat = Intl.ListFormat;\n    var DisplayNames = Intl.DisplayNames;\n    var getDateTimeFormat = memoize(function () {\n        var _a;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return new ((_a = Intl.DateTimeFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n    }, {\n        cache: createFastMemoizeCache(cache.dateTime),\n        strategy: strategies.variadic,\n    });\n    var getNumberFormat = memoize(function () {\n        var _a;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return new ((_a = Intl.NumberFormat).bind.apply(_a, __spreadArray([void 0], args, false)))();\n    }, {\n        cache: createFastMemoizeCache(cache.number),\n        strategy: strategies.variadic,\n    });\n    var getPluralRules = memoize(function () {\n        var _a;\n        var args = [];\n        for (var _i = 0; _i < arguments.length; _i++) {\n            args[_i] = arguments[_i];\n        }\n        return new ((_a = Intl.PluralRules).bind.apply(_a, __spreadArray([void 0], args, false)))();\n    }, {\n        cache: createFastMemoizeCache(cache.pluralRules),\n        strategy: strategies.variadic,\n    });\n    return {\n        getDateTimeFormat: getDateTimeFormat,\n        getNumberFormat: getNumberFormat,\n        getMessageFormat: memoize(function (message, locales, overrideFormats, opts) {\n            return new IntlMessageFormat(message, locales, overrideFormats, __assign({ formatters: {\n                    getNumberFormat: getNumberFormat,\n                    getDateTimeFormat: getDateTimeFormat,\n                    getPluralRules: getPluralRules,\n                } }, (opts || {})));\n        }, {\n            cache: createFastMemoizeCache(cache.message),\n            strategy: strategies.variadic,\n        }),\n        getRelativeTimeFormat: memoize(function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new (RelativeTimeFormat.bind.apply(RelativeTimeFormat, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.relativeTime),\n            strategy: strategies.variadic,\n        }),\n        getPluralRules: getPluralRules,\n        getListFormat: memoize(function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new (ListFormat.bind.apply(ListFormat, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.list),\n            strategy: strategies.variadic,\n        }),\n        getDisplayNames: memoize(function () {\n            var args = [];\n            for (var _i = 0; _i < arguments.length; _i++) {\n                args[_i] = arguments[_i];\n            }\n            return new (DisplayNames.bind.apply(DisplayNames, __spreadArray([void 0], args, false)))();\n        }, {\n            cache: createFastMemoizeCache(cache.displayNames),\n            strategy: strategies.variadic,\n        }),\n    };\n}\nexport function getNamedFormat(formats, type, name, onError) {\n    var formatType = formats && formats[type];\n    var format;\n    if (formatType) {\n        format = formatType[name];\n    }\n    if (format) {\n        return format;\n    }\n    onError(new UnsupportedFormatterError(\"No \".concat(type, \" format named: \").concat(name)));\n}\n", "import { __assign } from \"tslib\";\nimport { invariant } from '@formatjs/ecma402-abstract';\nimport { IntlMessageFormat, } from 'intl-messageformat';\nimport { MissingTranslationError, MessageFormatError } from './error';\nimport { TYPE } from '@formatjs/icu-messageformat-parser';\nfunction setTimeZoneInOptions(opts, timeZone) {\n    return Object.keys(opts).reduce(function (all, k) {\n        all[k] = __assign({ timeZone: timeZone }, opts[k]);\n        return all;\n    }, {});\n}\nfunction deepMergeOptions(opts1, opts2) {\n    var keys = Object.keys(__assign(__assign({}, opts1), opts2));\n    return keys.reduce(function (all, k) {\n        all[k] = __assign(__assign({}, (opts1[k] || {})), (opts2[k] || {}));\n        return all;\n    }, {});\n}\nfunction deepMergeFormatsAndSetTimeZone(f1, timeZone) {\n    if (!timeZone) {\n        return f1;\n    }\n    var mfFormats = IntlMessageFormat.formats;\n    return __assign(__assign(__assign({}, mfFormats), f1), { date: deepMergeOptions(setTimeZoneInOptions(mfFormats.date, timeZone), setTimeZoneInOptions(f1.date || {}, timeZone)), time: deepMergeOptions(setTimeZoneInOptions(mfFormats.time, timeZone), setTimeZoneInOptions(f1.time || {}, timeZone)) });\n}\nexport var formatMessage = function (_a, state, messageDescriptor, values, opts) {\n    var locale = _a.locale, formats = _a.formats, messages = _a.messages, defaultLocale = _a.defaultLocale, defaultFormats = _a.defaultFormats, fallbackOnEmptyString = _a.fallbackOnEmptyString, onError = _a.onError, timeZone = _a.timeZone, defaultRichTextElements = _a.defaultRichTextElements;\n    if (messageDescriptor === void 0) { messageDescriptor = { id: '' }; }\n    var msgId = messageDescriptor.id, defaultMessage = messageDescriptor.defaultMessage;\n    // `id` is a required field of a Message Descriptor.\n    invariant(!!msgId, \"[@formatjs/intl] An `id` must be provided to format a message. You can either:\\n1. Configure your build toolchain with [babel-plugin-formatjs](https://formatjs.io/docs/tooling/babel-plugin)\\nor [@formatjs/ts-transformer](https://formatjs.io/docs/tooling/ts-transformer) OR\\n2. Configure your `eslint` config to include [eslint-plugin-formatjs](https://formatjs.io/docs/tooling/linter#enforce-id)\\nto autofix this issue\");\n    var id = String(msgId);\n    var message = \n    // In case messages is Object.create(null)\n    // e.g import('foo.json') from webpack)\n    // See https://github.com/formatjs/formatjs/issues/1914\n    messages &&\n        Object.prototype.hasOwnProperty.call(messages, id) &&\n        messages[id];\n    // IMPORTANT: Hot path if `message` is AST with a single literal node\n    if (Array.isArray(message) &&\n        message.length === 1 &&\n        message[0].type === TYPE.literal) {\n        return message[0].value;\n    }\n    // IMPORTANT: Hot path straight lookup for performance\n    if (!values &&\n        message &&\n        typeof message === 'string' &&\n        !defaultRichTextElements) {\n        return message.replace(/'\\{(.*?)\\}'/gi, \"{$1}\");\n    }\n    values = __assign(__assign({}, defaultRichTextElements), (values || {}));\n    formats = deepMergeFormatsAndSetTimeZone(formats, timeZone);\n    defaultFormats = deepMergeFormatsAndSetTimeZone(defaultFormats, timeZone);\n    if (!message) {\n        if (fallbackOnEmptyString === false && message === '') {\n            return message;\n        }\n        if (!defaultMessage ||\n            (locale && locale.toLowerCase() !== defaultLocale.toLowerCase())) {\n            // This prevents warnings from littering the console in development\n            // when no `messages` are passed into the <IntlProvider> for the\n            // default locale.\n            onError(new MissingTranslationError(messageDescriptor, locale));\n        }\n        if (defaultMessage) {\n            try {\n                var formatter = state.getMessageFormat(defaultMessage, defaultLocale, defaultFormats, opts);\n                return formatter.format(values);\n            }\n            catch (e) {\n                onError(new MessageFormatError(\"Error formatting default message for: \\\"\".concat(id, \"\\\", rendering default message verbatim\"), locale, messageDescriptor, e));\n                return typeof defaultMessage === 'string' ? defaultMessage : id;\n            }\n        }\n        return id;\n    }\n    // We have the translated message\n    try {\n        var formatter = state.getMessageFormat(message, locale, formats, __assign({ formatters: state }, (opts || {})));\n        return formatter.format(values);\n    }\n    catch (e) {\n        onError(new MessageFormatError(\"Error formatting message: \\\"\".concat(id, \"\\\", using \").concat(defaultMessage ? 'default message' : 'id', \" as fallback.\"), locale, messageDescriptor, e));\n    }\n    if (defaultMessage) {\n        try {\n            var formatter = state.getMessageFormat(defaultMessage, defaultLocale, defaultFormats, opts);\n            return formatter.format(values);\n        }\n        catch (e) {\n            onError(new MessageFormatError(\"Error formatting the default message for: \\\"\".concat(id, \"\\\", rendering message verbatim\"), locale, messageDescriptor, e));\n        }\n    }\n    if (typeof message === 'string') {\n        return message;\n    }\n    if (typeof defaultMessage === 'string') {\n        return defaultMessage;\n    }\n    return id;\n};\n", "import { __assign } from \"tslib\";\nimport { filterProps, getNamedFormat } from './utils';\nimport { IntlFormatError } from './error';\nvar DATE_TIME_FORMAT_OPTIONS = [\n    'formatMatcher',\n    'timeZone',\n    'hour12',\n    'weekday',\n    'era',\n    'year',\n    'month',\n    'day',\n    'hour',\n    'minute',\n    'second',\n    'timeZoneName',\n    'hourCycle',\n    'dateStyle',\n    'timeStyle',\n    'calendar',\n    // 'dayPeriod',\n    'numberingSystem',\n    'fractionalSecondDigits',\n];\nexport function getFormatter(_a, type, getDateTimeFormat, options) {\n    var locale = _a.locale, formats = _a.formats, onError = _a.onError, timeZone = _a.timeZone;\n    if (options === void 0) { options = {}; }\n    var format = options.format;\n    var defaults = __assign(__assign({}, (timeZone && { timeZone: timeZone })), (format && getNamedFormat(formats, type, format, onError)));\n    var filteredOptions = filterProps(options, DATE_TIME_FORMAT_OPTIONS, defaults);\n    if (type === 'time' &&\n        !filteredOptions.hour &&\n        !filteredOptions.minute &&\n        !filteredOptions.second &&\n        !filteredOptions.timeStyle &&\n        !filteredOptions.dateStyle) {\n        // Add default formatting options if hour, minute, or second isn't defined.\n        filteredOptions = __assign(__assign({}, filteredOptions), { hour: 'numeric', minute: 'numeric' });\n    }\n    return getDateTimeFormat(locale, filteredOptions);\n}\nexport function formatDate(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var value = _a[0], _b = _a[1], options = _b === void 0 ? {} : _b;\n    var date = typeof value === 'string' ? new Date(value || 0) : value;\n    try {\n        return getFormatter(config, 'date', getDateTimeFormat, options).format(date);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting date.', config.locale, e));\n    }\n    return String(date);\n}\nexport function formatTime(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var value = _a[0], _b = _a[1], options = _b === void 0 ? {} : _b;\n    var date = typeof value === 'string' ? new Date(value || 0) : value;\n    try {\n        return getFormatter(config, 'time', getDateTimeFormat, options).format(date);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting time.', config.locale, e));\n    }\n    return String(date);\n}\nexport function formatDateTimeRange(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var from = _a[0], to = _a[1], _b = _a[2], options = _b === void 0 ? {} : _b;\n    var timeZone = config.timeZone, locale = config.locale, onError = config.onError;\n    var filteredOptions = filterProps(options, DATE_TIME_FORMAT_OPTIONS, timeZone ? { timeZone: timeZone } : {});\n    try {\n        return getDateTimeFormat(locale, filteredOptions).formatRange(from, to);\n    }\n    catch (e) {\n        onError(new IntlFormatError('Error formatting date time range.', config.locale, e));\n    }\n    return String(from);\n}\nexport function formatDateToParts(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var value = _a[0], _b = _a[1], options = _b === void 0 ? {} : _b;\n    var date = typeof value === 'string' ? new Date(value || 0) : value;\n    try {\n        return getFormatter(config, 'date', getDateTimeFormat, options).formatToParts(date); // TODO: remove this when https://github.com/microsoft/TypeScript/pull/50402 is merged\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting date.', config.locale, e));\n    }\n    return [];\n}\nexport function formatTimeToParts(config, getDateTimeFormat) {\n    var _a = [];\n    for (var _i = 2; _i < arguments.length; _i++) {\n        _a[_i - 2] = arguments[_i];\n    }\n    var value = _a[0], _b = _a[1], options = _b === void 0 ? {} : _b;\n    var date = typeof value === 'string' ? new Date(value || 0) : value;\n    try {\n        return getFormatter(config, 'time', getDateTimeFormat, options).formatToParts(date); // TODO: remove this when https://github.com/microsoft/TypeScript/pull/50402 is merged\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting time.', config.locale, e));\n    }\n    return [];\n}\n", "import { filterProps } from './utils';\nimport { FormatError, ErrorCode } from 'intl-messageformat';\nimport { IntlFormatError } from './error';\nvar DISPLAY_NAMES_OPTONS = [\n    'style',\n    'type',\n    'fallback',\n    'languageDisplay',\n];\nexport function formatDisplayName(_a, getDisplayNames, value, options) {\n    var locale = _a.locale, onError = _a.onError;\n    var DisplayNames = Intl.DisplayNames;\n    if (!DisplayNames) {\n        onError(new FormatError(\"Intl.DisplayNames is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-displaynames\\\"\\n\", ErrorCode.MISSING_INTL_API));\n    }\n    var filteredOptions = filterProps(options, DISPLAY_NAMES_OPTONS);\n    try {\n        return getDisplayNames(locale, filteredOptions).of(value);\n    }\n    catch (e) {\n        onError(new IntlFormatError('Error formatting display name.', locale, e));\n    }\n}\n", "import { __assign } from \"tslib\";\nimport { filterProps } from './utils';\nimport { FormatError, ErrorCode } from 'intl-messageformat';\nimport { IntlFormatError } from './error';\nvar LIST_FORMAT_OPTIONS = [\n    'type',\n    'style',\n];\nvar now = Date.now();\nfunction generateToken(i) {\n    return \"\".concat(now, \"_\").concat(i, \"_\").concat(now);\n}\nexport function formatList(opts, getListFormat, values, options) {\n    if (options === void 0) { options = {}; }\n    var results = formatListToParts(opts, getListFormat, values, options).reduce(function (all, el) {\n        var val = el.value;\n        if (typeof val !== 'string') {\n            all.push(val);\n        }\n        else if (typeof all[all.length - 1] === 'string') {\n            all[all.length - 1] += val;\n        }\n        else {\n            all.push(val);\n        }\n        return all;\n    }, []);\n    return results.length === 1 ? results[0] : results.length === 0 ? '' : results;\n}\nexport function formatListToParts(_a, getListFormat, values, options) {\n    var locale = _a.locale, onError = _a.onError;\n    if (options === void 0) { options = {}; }\n    var ListFormat = Intl.ListFormat;\n    if (!ListFormat) {\n        onError(new FormatError(\"Intl.ListFormat is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-listformat\\\"\\n\", ErrorCode.MISSING_INTL_API));\n    }\n    var filteredOptions = filterProps(options, LIST_FORMAT_OPTIONS);\n    try {\n        var richValues_1 = {};\n        var serializedValues = values.map(function (v, i) {\n            if (typeof v === 'object') {\n                var id = generateToken(i);\n                richValues_1[id] = v;\n                return id;\n            }\n            return String(v);\n        });\n        return getListFormat(locale, filteredOptions)\n            .formatToParts(serializedValues)\n            .map(function (part) {\n            return part.type === 'literal'\n                ? part\n                : __assign(__assign({}, part), { value: richValues_1[part.value] || part.value });\n        });\n    }\n    catch (e) {\n        onError(new IntlFormatError('Error formatting list.', locale, e));\n    }\n    // @ts-ignore\n    return values;\n}\n", "import { filterProps } from './utils';\nimport { IntlFormatError } from './error';\nimport { ErrorCode, FormatError } from 'intl-messageformat';\nvar PLURAL_FORMAT_OPTIONS = ['type'];\nexport function formatPlural(_a, getPluralRules, value, options) {\n    var locale = _a.locale, onError = _a.onError;\n    if (options === void 0) { options = {}; }\n    if (!Intl.PluralRules) {\n        onError(new FormatError(\"Intl.PluralRules is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-pluralrules\\\"\\n\", ErrorCode.MISSING_INTL_API));\n    }\n    var filteredOptions = filterProps(options, PLURAL_FORMAT_OPTIONS);\n    try {\n        return getPluralRules(locale, filteredOptions).select(value);\n    }\n    catch (e) {\n        onError(new IntlFormatError('Error formatting plural.', locale, e));\n    }\n    return 'other';\n}\n", "import { getNamedFormat, filterProps } from './utils';\nimport { FormatError, ErrorCode } from 'intl-messageformat';\nimport { IntlFormatError } from './error';\nvar RELATIVE_TIME_FORMAT_OPTIONS = ['numeric', 'style'];\nfunction getFormatter(_a, getRelativeTimeFormat, options) {\n    var locale = _a.locale, formats = _a.formats, onError = _a.onError;\n    if (options === void 0) { options = {}; }\n    var format = options.format;\n    var defaults = (!!format && getNamedFormat(formats, 'relative', format, onError)) || {};\n    var filteredOptions = filterProps(options, RELATIVE_TIME_FORMAT_OPTIONS, defaults);\n    return getRelativeTimeFormat(locale, filteredOptions);\n}\nexport function formatRelativeTime(config, getRelativeTimeFormat, value, unit, options) {\n    if (options === void 0) { options = {}; }\n    if (!unit) {\n        unit = 'second';\n    }\n    var RelativeTimeFormat = Intl.RelativeTimeFormat;\n    if (!RelativeTimeFormat) {\n        config.onError(new FormatError(\"Intl.RelativeTimeFormat is not available in this environment.\\nTry polyfilling it using \\\"@formatjs/intl-relativetimeformat\\\"\\n\", ErrorCode.MISSING_INTL_API));\n    }\n    try {\n        return getFormatter(config, getRelativeTimeFormat, options).format(value, unit);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting relative time.', config.locale, e));\n    }\n    return String(value);\n}\n", "import { IntlFormatError } from './error';\nimport { filterProps, getNamedFormat } from './utils';\nvar NUMBER_FORMAT_OPTIONS = [\n    'style',\n    'currency',\n    'unit',\n    'unitDisplay',\n    'useGrouping',\n    'minimumIntegerDigits',\n    'minimumFractionDigits',\n    'maximumFractionDigits',\n    'minimumSignificantDigits',\n    'maximumSignificantDigits',\n    // ES2020 NumberFormat\n    'compactDisplay',\n    'currencyDisplay',\n    'currencySign',\n    'notation',\n    'signDisplay',\n    'unit',\n    'unitDisplay',\n    'numberingSystem',\n    // ES2023 NumberFormat\n    'trailingZeroDisplay',\n    'roundingPriority',\n    'roundingIncrement',\n    'roundingMode',\n];\nexport function getFormatter(_a, getNumberFormat, options) {\n    var locale = _a.locale, formats = _a.formats, onError = _a.onError;\n    if (options === void 0) { options = {}; }\n    var format = options.format;\n    var defaults = ((format &&\n        getNamedFormat(formats, 'number', format, onError)) ||\n        {});\n    var filteredOptions = filterProps(options, NUMBER_FORMAT_OPTIONS, defaults);\n    return getNumberFormat(locale, filteredOptions);\n}\nexport function formatNumber(config, getNumberFormat, value, options) {\n    if (options === void 0) { options = {}; }\n    try {\n        return getFormatter(config, getNumberFormat, options).format(value);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting number.', config.locale, e));\n    }\n    return String(value);\n}\nexport function formatNumberToParts(config, getNumberFormat, value, options) {\n    if (options === void 0) { options = {}; }\n    try {\n        return getFormatter(config, getNumberFormat, options).formatToParts(value);\n    }\n    catch (e) {\n        config.onError(new IntlFormatError('Error formatting number.', config.locale, e));\n    }\n    return [];\n}\n", "import { __assign } from \"tslib\";\nimport { createFormatters, DEFAULT_INTL_CONFIG } from './utils';\nimport { InvalidConfigError, MissingDataError } from './error';\nimport { formatNumber, formatNumberToParts } from './number';\nimport { formatRelativeTime } from './relativeTime';\nimport { formatDate, formatDateToParts, formatTime, formatTimeToParts, formatDateTimeRange, } from './dateTime';\nimport { formatPlural } from './plural';\nimport { formatMessage } from './message';\nimport { formatList, formatListToParts } from './list';\nimport { formatDisplayName } from './displayName';\nfunction messagesContainString(messages) {\n    var firstMessage = messages ? messages[Object.keys(messages)[0]] : undefined;\n    return typeof firstMessage === 'string';\n}\nfunction verifyConfigMessages(config) {\n    if (config.onWarn &&\n        config.defaultRichTextElements &&\n        messagesContainString(config.messages || {})) {\n        config.onWarn(\"[@formatjs/intl] \\\"defaultRichTextElements\\\" was specified but \\\"message\\\" was not pre-compiled. \\nPlease consider using \\\"@formatjs/cli\\\" to pre-compile your messages for performance.\\nFor more details see https://formatjs.io/docs/getting-started/message-distribution\");\n    }\n}\n/**\n * Create intl object\n * @param config intl config\n * @param cache cache for formatter instances to prevent memory leak\n */\nexport function createIntl(config, cache) {\n    var formatters = createFormatters(cache);\n    var resolvedConfig = __assign(__assign({}, DEFAULT_INTL_CONFIG), config);\n    var locale = resolvedConfig.locale, defaultLocale = resolvedConfig.defaultLocale, onError = resolvedConfig.onError;\n    if (!locale) {\n        if (onError) {\n            onError(new InvalidConfigError(\"\\\"locale\\\" was not configured, using \\\"\".concat(defaultLocale, \"\\\" as fallback. See https://formatjs.io/docs/react-intl/api#intlshape for more details\")));\n        }\n        // Since there's no registered locale data for `locale`, this will\n        // fallback to the `defaultLocale` to make sure things can render.\n        // The `messages` are overridden to the `defaultProps` empty object\n        // to maintain referential equality across re-renders. It's assumed\n        // each <FormattedMessage> contains a `defaultMessage` prop.\n        resolvedConfig.locale = resolvedConfig.defaultLocale || 'en';\n    }\n    else if (!Intl.NumberFormat.supportedLocalesOf(locale).length && onError) {\n        onError(new MissingDataError(\"Missing locale data for locale: \\\"\".concat(locale, \"\\\" in Intl.NumberFormat. Using default locale: \\\"\").concat(defaultLocale, \"\\\" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details\")));\n    }\n    else if (!Intl.DateTimeFormat.supportedLocalesOf(locale).length &&\n        onError) {\n        onError(new MissingDataError(\"Missing locale data for locale: \\\"\".concat(locale, \"\\\" in Intl.DateTimeFormat. Using default locale: \\\"\").concat(defaultLocale, \"\\\" as fallback. See https://formatjs.io/docs/react-intl#runtime-requirements for more details\")));\n    }\n    verifyConfigMessages(resolvedConfig);\n    return __assign(__assign({}, resolvedConfig), { formatters: formatters, formatNumber: formatNumber.bind(null, resolvedConfig, formatters.getNumberFormat), formatNumberToParts: formatNumberToParts.bind(null, resolvedConfig, formatters.getNumberFormat), formatRelativeTime: formatRelativeTime.bind(null, resolvedConfig, formatters.getRelativeTimeFormat), formatDate: formatDate.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatDateToParts: formatDateToParts.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatTime: formatTime.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatDateTimeRange: formatDateTimeRange.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatTimeToParts: formatTimeToParts.bind(null, resolvedConfig, formatters.getDateTimeFormat), formatPlural: formatPlural.bind(null, resolvedConfig, formatters.getPluralRules), \n        // @ts-expect-error TODO: will get to this later\n        formatMessage: formatMessage.bind(null, resolvedConfig, formatters), \n        // @ts-expect-error TODO: will get to this later\n        $t: formatMessage.bind(null, resolvedConfig, formatters), formatList: formatList.bind(null, resolvedConfig, formatters.getListFormat), formatListToParts: formatListToParts.bind(null, resolvedConfig, formatters.getListFormat), formatDisplayName: formatDisplayName.bind(null, resolvedConfig, formatters.getDisplayNames) });\n}\n", "/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport { __extends } from \"tslib\";\nimport { createIntlCache } from '@formatjs/intl';\nimport * as React from 'react';\nimport { DEFAULT_INTL_CONFIG, invariantIntlContext, shallowEqual } from '../utils';\nimport { Provider } from './injectIntl';\nimport { createIntl } from './createIntl';\nfunction processIntlConfig(config) {\n    return {\n        locale: config.locale,\n        timeZone: config.timeZone,\n        fallbackOnEmptyString: config.fallbackOnEmptyString,\n        formats: config.formats,\n        textComponent: config.textComponent,\n        messages: config.messages,\n        defaultLocale: config.defaultLocale,\n        defaultFormats: config.defaultFormats,\n        onError: config.onError,\n        onWarn: config.onWarn,\n        wrapRichTextChunksInFragment: config.wrapRichTextChunksInFragment,\n        defaultRichTextElements: config.defaultRichTextElements,\n    };\n}\nvar IntlProvider = /** @class */ (function (_super) {\n    __extends(IntlProvider, _super);\n    function IntlProvider() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.cache = createIntlCache();\n        _this.state = {\n            cache: _this.cache,\n            intl: createIntl(processIntlConfig(_this.props), _this.cache),\n            prevConfig: processIntlConfig(_this.props),\n        };\n        return _this;\n    }\n    IntlProvider.getDerivedStateFromProps = function (props, _a) {\n        var prevConfig = _a.prevConfig, cache = _a.cache;\n        var config = processIntlConfig(props);\n        if (!shallowEqual(prevConfig, config)) {\n            return {\n                intl: createIntl(config, cache),\n                prevConfig: config,\n            };\n        }\n        return null;\n    };\n    IntlProvider.prototype.render = function () {\n        invariantIntlContext(this.state.intl);\n        return React.createElement(Provider, { value: this.state.intl }, this.props.children);\n    };\n    IntlProvider.displayName = 'IntlProvider';\n    IntlProvider.defaultProps = DEFAULT_INTL_CONFIG;\n    return IntlProvider;\n}(React.PureComponent));\nexport default IntlProvider;\n", "/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport { __assign, __rest, __spreadArray } from \"tslib\";\nimport { createIntl as coreCreateIntl, formatMessage as coreFormatMessage, } from '@formatjs/intl';\nimport * as React from 'react';\nimport { DEFAULT_INTL_CONFIG, assignUniqueKeysToParts } from '../utils';\nimport { isFormatXMLElementFn, } from 'intl-messageformat';\nfunction assignUniqueKeysToFormatXMLElementFnArgument(values) {\n    if (!values) {\n        return values;\n    }\n    return Object.keys(values).reduce(function (acc, k) {\n        var v = values[k];\n        acc[k] = isFormatXMLElementFn(v)\n            ? assignUniqueKeysToParts(v)\n            : v;\n        return acc;\n    }, {});\n}\nvar formatMessage = function (config, formatters, descriptor, rawValues) {\n    var rest = [];\n    for (var _i = 4; _i < arguments.length; _i++) {\n        rest[_i - 4] = arguments[_i];\n    }\n    var values = assignUniqueKeysToFormatXMLElementFnArgument(rawValues);\n    var chunks = coreFormatMessage.apply(void 0, __spreadArray([config,\n        formatters,\n        descriptor,\n        values], rest, false));\n    if (Array.isArray(chunks)) {\n        return React.Children.toArray(chunks);\n    }\n    return chunks;\n};\n/**\n * Create intl object\n * @param config intl config\n * @param cache cache for formatter instances to prevent memory leak\n */\nexport var createIntl = function (_a, cache) {\n    var rawDefaultRichTextElements = _a.defaultRichTextElements, config = __rest(_a, [\"defaultRichTextElements\"]);\n    var defaultRichTextElements = assignUniqueKeysToFormatXMLElementFnArgument(rawDefaultRichTextElements);\n    var coreIntl = coreCreateIntl(__assign(__assign(__assign({}, DEFAULT_INTL_CONFIG), config), { defaultRichTextElements: defaultRichTextElements }), cache);\n    var resolvedConfig = {\n        locale: coreIntl.locale,\n        timeZone: coreIntl.timeZone,\n        fallbackOnEmptyString: coreIntl.fallbackOnEmptyString,\n        formats: coreIntl.formats,\n        defaultLocale: coreIntl.defaultLocale,\n        defaultFormats: coreIntl.defaultFormats,\n        messages: coreIntl.messages,\n        onError: coreIntl.onError,\n        defaultRichTextElements: defaultRichTextElements,\n    };\n    return __assign(__assign({}, coreIntl), { formatMessage: formatMessage.bind(null, resolvedConfig, \n        // @ts-expect-error fix this\n        coreIntl.formatters), \n        // @ts-expect-error fix this\n        $t: formatMessage.bind(null, resolvedConfig, coreIntl.formatters) });\n};\n", "import { __assign, __rest } from \"tslib\";\n/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport * as React from 'react';\nimport { invariant, } from '@formatjs/ecma402-abstract';\nimport useIntl from './useIntl';\nvar MINUTE = 60;\nvar HOUR = 60 * 60;\nvar DAY = 60 * 60 * 24;\nfunction selectUnit(seconds) {\n    var absValue = Math.abs(seconds);\n    if (absValue < MINUTE) {\n        return 'second';\n    }\n    if (absValue < HOUR) {\n        return 'minute';\n    }\n    if (absValue < DAY) {\n        return 'hour';\n    }\n    return 'day';\n}\nfunction getDurationInSeconds(unit) {\n    switch (unit) {\n        case 'second':\n            return 1;\n        case 'minute':\n            return MINUTE;\n        case 'hour':\n            return HOUR;\n        default:\n            return DAY;\n    }\n}\nfunction valueToSeconds(value, unit) {\n    if (!value) {\n        return 0;\n    }\n    switch (unit) {\n        case 'second':\n            return value;\n        case 'minute':\n            return value * MINUTE;\n        default:\n            return value * HOUR;\n    }\n}\nvar INCREMENTABLE_UNITS = [\n    'second',\n    'minute',\n    'hour',\n];\nfunction canIncrement(unit) {\n    if (unit === void 0) { unit = 'second'; }\n    return INCREMENTABLE_UNITS.indexOf(unit) > -1;\n}\nvar SimpleFormattedRelativeTime = function (props) {\n    var _a = useIntl(), formatRelativeTime = _a.formatRelativeTime, Text = _a.textComponent;\n    var children = props.children, value = props.value, unit = props.unit, otherProps = __rest(props, [\"children\", \"value\", \"unit\"]);\n    var formattedRelativeTime = formatRelativeTime(value || 0, unit, otherProps);\n    if (typeof children === 'function') {\n        return children(formattedRelativeTime);\n    }\n    if (Text) {\n        return React.createElement(Text, null, formattedRelativeTime);\n    }\n    return React.createElement(React.Fragment, null, formattedRelativeTime);\n};\nvar FormattedRelativeTime = function (_a) {\n    var _b = _a.value, value = _b === void 0 ? 0 : _b, _c = _a.unit, unit = _c === void 0 ? 'second' : _c, updateIntervalInSeconds = _a.updateIntervalInSeconds, otherProps = __rest(_a, [\"value\", \"unit\", \"updateIntervalInSeconds\"]);\n    invariant(!updateIntervalInSeconds ||\n        !!(updateIntervalInSeconds && canIncrement(unit)), 'Cannot schedule update with unit longer than hour');\n    var _d = React.useState(), prevUnit = _d[0], setPrevUnit = _d[1];\n    var _e = React.useState(0), prevValue = _e[0], setPrevValue = _e[1];\n    var _f = React.useState(0), currentValueInSeconds = _f[0], setCurrentValueInSeconds = _f[1];\n    var updateTimer;\n    if (unit !== prevUnit || value !== prevValue) {\n        setPrevValue(value || 0);\n        setPrevUnit(unit);\n        setCurrentValueInSeconds(canIncrement(unit) ? valueToSeconds(value, unit) : 0);\n    }\n    React.useEffect(function () {\n        function clearUpdateTimer() {\n            clearTimeout(updateTimer);\n        }\n        clearUpdateTimer();\n        // If there's no interval and we cannot increment this unit, do nothing\n        if (!updateIntervalInSeconds || !canIncrement(unit)) {\n            return clearUpdateTimer;\n        }\n        // Figure out the next interesting time\n        var nextValueInSeconds = currentValueInSeconds - updateIntervalInSeconds;\n        var nextUnit = selectUnit(nextValueInSeconds);\n        // We've reached the max auto incrementable unit, don't schedule another update\n        if (nextUnit === 'day') {\n            return clearUpdateTimer;\n        }\n        var unitDuration = getDurationInSeconds(nextUnit);\n        var remainder = nextValueInSeconds % unitDuration;\n        var prevInterestingValueInSeconds = nextValueInSeconds - remainder;\n        var nextInterestingValueInSeconds = prevInterestingValueInSeconds >= currentValueInSeconds\n            ? prevInterestingValueInSeconds - unitDuration\n            : prevInterestingValueInSeconds;\n        var delayInSeconds = Math.abs(nextInterestingValueInSeconds - currentValueInSeconds);\n        if (currentValueInSeconds !== nextInterestingValueInSeconds) {\n            updateTimer = setTimeout(function () { return setCurrentValueInSeconds(nextInterestingValueInSeconds); }, delayInSeconds * 1e3);\n        }\n        return clearUpdateTimer;\n    }, [currentValueInSeconds, updateIntervalInSeconds, unit]);\n    var currentValue = value || 0;\n    var currentUnit = unit;\n    if (canIncrement(unit) &&\n        typeof currentValueInSeconds === 'number' &&\n        updateIntervalInSeconds) {\n        currentUnit = selectUnit(currentValueInSeconds);\n        var unitDuration = getDurationInSeconds(currentUnit);\n        currentValue = Math.round(currentValueInSeconds / unitDuration);\n    }\n    return (React.createElement(SimpleFormattedRelativeTime, __assign({ value: currentValue, unit: currentUnit }, otherProps)));\n};\nFormattedRelativeTime.displayName = 'FormattedRelativeTime';\nexport default FormattedRelativeTime;\n", "/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport * as React from 'react';\nimport useIntl from './useIntl';\nvar FormattedPlural = function (props) {\n    var _a = useIntl(), formatPlural = _a.formatPlural, Text = _a.textComponent;\n    var value = props.value, other = props.other, children = props.children;\n    var pluralCategory = formatPlural(value, props);\n    var formattedPlural = props[pluralCategory] || other;\n    if (typeof children === 'function') {\n        return children(formattedPlural);\n    }\n    if (Text) {\n        return React.createElement(Text, null, formattedPlural);\n    }\n    // Work around @types/react where React.FC cannot return string\n    return formattedPlural;\n};\nFormattedPlural.displayName = 'FormattedPlural';\nexport default FormattedPlural;\n", "/*\n * Copyright 2015, Yahoo Inc.\n * Copyrights licensed under the New BSD License.\n * See the accompanying LICENSE file for terms.\n */\nimport { __rest } from \"tslib\";\nimport * as React from 'react';\nimport useIntl from './useIntl';\nimport { shallowEqual } from '../utils';\nfunction areEqual(prevProps, nextProps) {\n    var values = prevProps.values, otherProps = __rest(prevProps, [\"values\"]);\n    var nextValues = nextProps.values, nextOtherProps = __rest(nextProps, [\"values\"]);\n    return (shallowEqual(nextValues, values) &&\n        shallowEqual(otherProps, nextOtherProps));\n}\nfunction FormattedMessage(props) {\n    var intl = useIntl();\n    var formatMessage = intl.formatMessage, _a = intl.textComponent, Text = _a === void 0 ? React.Fragment : _a;\n    var id = props.id, description = props.description, defaultMessage = props.defaultMessage, values = props.values, children = props.children, _b = props.tagName, Component = _b === void 0 ? Text : _b, ignoreTag = props.ignoreTag;\n    var descriptor = { id: id, description: description, defaultMessage: defaultMessage };\n    var nodes = formatMessage(descriptor, values, {\n        ignoreTag: ignoreTag,\n    });\n    if (typeof children === 'function') {\n        return children(Array.isArray(nodes) ? nodes : [nodes]);\n    }\n    if (Component) {\n        return React.createElement(Component, null, React.Children.toArray(nodes));\n    }\n    return React.createElement(React.Fragment, null, nodes);\n}\nFormattedMessage.displayName = 'FormattedMessage';\nvar MemoizedFormattedMessage = React.memo(FormattedMessage, areEqual);\nMemoizedFormattedMessage.displayName = 'MemoizedFormattedMessage';\nexport default MemoizedFormattedMessage;\n", "import { __rest } from \"tslib\";\nimport * as React from 'react';\nimport useIntl from './useIntl';\nvar FormattedDateTimeRange = function (props) {\n    var intl = useIntl();\n    var from = props.from, to = props.to, children = props.children, formatProps = __rest(props, [\"from\", \"to\", \"children\"]);\n    var formattedValue = intl.formatDateTimeRange(from, to, formatProps);\n    if (typeof children === 'function') {\n        return children(formattedValue);\n    }\n    var Text = intl.textComponent || React.Fragment;\n    return React.createElement(Text, null, formattedValue);\n};\nFormattedDateTimeRange.displayName = 'FormattedDateTimeRange';\nexport default FormattedDateTimeRange;\n", "import { createFormattedComponent, createFormattedDateTimePartsComponent, } from './src/components/createFormattedComponent';\nimport injectIntl, { Provider as RawIntlProvider, Context as IntlContext, } from './src/components/injectIntl';\nimport useIntl from './src/components/useIntl';\nimport IntlProvider from './src/components/provider';\nimport { createIntl } from './src/components/createIntl';\nimport FormattedRelativeTime from './src/components/relative';\nimport FormattedPlural from './src/components/plural';\nimport FormattedMessage from './src/components/message';\nimport FormattedDateTimeRange from './src/components/dateTimeRange';\nexport { FormattedDateTimeRange, FormattedMessage, FormattedPlural, FormattedRelativeTime, IntlContext, IntlProvider, RawIntlProvider, createIntl, injectIntl, useIntl, };\nexport { createIntlCache, UnsupportedFormatterError, InvalidConfigError, MissingDataError, MessageFormatError, MissingTranslationError, IntlErrorCode as ReactIntlErrorCode, IntlError as ReactIntlError, } from '@formatjs/intl';\nexport function defineMessages(msgs) {\n    return msgs;\n}\nexport function defineMessage(msg) {\n    return msg;\n}\n// IMPORTANT: Explicit here to prevent api-extractor from outputing `import('./src/types').CustomFormatConfig`\nexport var FormattedDate = createFormattedComponent('formatDate');\nexport var FormattedTime = createFormattedComponent('formatTime');\nexport var FormattedNumber = createFormattedComponent('formatNumber');\nexport var FormattedList = createFormattedComponent('formatList');\nexport var FormattedDisplayName = createFormattedComponent('formatDisplayName');\nexport var FormattedDateParts = createFormattedDateTimePartsComponent('formatDate');\nexport var FormattedTimeParts = createFormattedDateTimePartsComponent('formatTime');\nexport { FormattedNumberParts, FormattedListParts, } from './src/components/createFormattedComponent';\n", "import { pluginId } from '../pluginId';\n\nconst getTrad = (id: string) => `${pluginId}.${id}`;\n\nexport { getTrad };\n", "export const UploadIcon = () => (\n  <svg width=\"142\" height=\"74\" viewBox=\"0 0 142 74\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <g opacity=\"0.88\">\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M6.50177 8.21597C3.2423 8.21597 0.599976 10.8653 0.599976 14.1334C0.599976 17.4016 3.2423 20.0509 6.50177 20.0509H40.2263C36.9669 20.0509 34.3245 22.7002 34.3245 25.9684C34.3245 29.2365 36.9669 31.8858 40.2263 31.8858H21.6778C18.4183 31.8858 15.776 34.5352 15.776 37.8033C15.776 41.0714 18.4183 43.7207 21.6778 43.7207H30.2556C34.3654 43.7207 37.697 46.3701 37.697 49.6382C37.697 51.3602 36.6436 52.9534 34.5369 54.4177C33.3426 55.2478 31.828 55.4312 30.4754 55.9657C28.2859 56.8308 26.7365 58.9705 26.7365 61.4731C26.7365 64.7413 29.3788 67.3906 32.6383 67.3906H102.617C105.876 67.3906 108.519 64.7413 108.519 61.4731C108.519 58.205 105.876 55.5557 102.617 55.5557H135.498C138.758 55.5557 141.4 52.9063 141.4 49.6382C141.4 46.3701 138.758 43.7207 135.498 43.7207H101.774C98.5142 43.7207 95.8718 41.0714 95.8718 37.8033C95.8718 34.5352 98.5142 31.8858 101.774 31.8858H122.851C126.111 31.8858 128.753 29.2365 128.753 25.9684C128.753 22.7002 126.111 20.0509 122.851 20.0509H89.1269C92.3864 20.0509 95.0287 17.4016 95.0287 14.1334C95.0287 10.8653 92.3864 8.21597 89.1269 8.21597H6.50177ZM6.50177 31.8858C3.2423 31.8858 0.599976 34.5352 0.599976 37.8033C0.599976 41.0714 3.2423 43.7207 6.50177 43.7207C9.76124 43.7207 12.4036 41.0714 12.4036 37.8033C12.4036 34.5352 9.76124 31.8858 6.50177 31.8858Z\"\n        fill=\"#DBDBFA\"\n      />\n      <path d=\"M73.6731 73V39.8706\" stroke=\"white\" strokeWidth=\"2\" strokeLinecap=\"round\" />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M50.6061 9.64246C44.2424 9.64246 39.0829 14.802 39.0829 21.1657C39.0829 21.3861 39.103 21.6007 39.116 21.8197C39.1016 22.0818 39.0829 22.3469 39.0829 22.6061C33.3371 25.3602 29 31.9069 29 38.6739C29 48.0582 36.6774 55.7355 46.0616 55.7355H53.487L56.9785 11.5668C55.1535 10.3526 52.9626 9.64246 50.6061 9.64246Z\"\n        fill=\"#F1F1FE\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M99.4633 24.0465C99.2977 11.5164 89.1025 1 76.5334 1L90.8165 55.7355H96.5364C105.274 55.7355 112.423 48.5868 112.423 39.8507C112.423 32.1143 106.814 25.4308 99.4633 24.0465Z\"\n        fill=\"#DEDDFE\"\n      />\n      <path\n        fillRule=\"evenodd\"\n        clipRule=\"evenodd\"\n        d=\"M95.2638 24.0465C95.0982 11.5164 84.9029 1 72.3339 1C64.3267 1 63.3991 3.40404 59.2493 9.64245C57.4228 8.42819 57.2845 9.64245 54.928 9.64245C48.5629 9.64245 43.4048 14.802 43.4048 21.1657C43.4048 21.3861 43.4249 21.6007 43.4379 21.8197C43.4235 22.0818 43.4048 22.3469 43.4048 22.6061C37.659 25.3602 33.3219 31.9069 33.3219 38.6739C33.3219 48.0582 40.9993 55.7355 50.3835 55.7355H57.8089H86.617H92.3369C101.074 55.7355 108.223 48.5868 108.223 39.8507C108.223 32.1143 102.614 25.4308 95.2638 24.0465Z\"\n        fill=\"white\"\n      />\n      <path\n        d=\"M99.6256 24.0466C99.6256 24.0466 95.9741 23.5064 92.4192 24.0466\"\n        stroke=\"#8A88FE\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n      <path\n        d=\"M39.1237 21.1657C39.1237 14.802 44.2832 9.64246 50.6469 9.64246C57.0107 9.64246 62.1702 14.802 62.1702 21.1657\"\n        stroke=\"#8A88FE\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n      <path\n        d=\"M90.9784 55.7355H96.6997C105.436 55.7355 112.584 48.5868 112.584 39.8507C112.584 32.1143 106.976 25.4308 99.6251 24.0465C99.4609 11.5164 89.2657 1 76.6953 1C68.688 1 61.1705 5.32843 57.0193 11.5668C55.1943 10.3526 53.0034 9.64245 50.6469 9.64245C44.2832 9.64245 39.1236 14.802 39.1236 21.1657C39.1236 21.3861 39.1438 21.6007 39.1568 21.8197C39.1424 22.0818 39.1236 22.3469 39.1236 22.6061C33.3778 25.3602 29.0408 31.9069 29.0408 38.6739C29.0408 48.0582 36.7182 55.7355 46.1024 55.7355H53.5277\"\n        stroke=\"#8A88FE\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n      <path\n        d=\"M72.2531 71.58V38.4506\"\n        stroke=\"#8A88FE\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n      <path\n        d=\"M72.2531 38.4506L83.7763 49.9739\"\n        stroke=\"#8A88FE\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n      <path\n        d=\"M60.7297 49.9739L72.253 38.4506\"\n        stroke=\"#8A88FE\"\n        strokeWidth=\"2\"\n        strokeLinecap=\"round\"\n        strokeLinejoin=\"round\"\n      />\n    </g>\n  </svg>\n);\n", "/*\n *\n * Cloud component\n *\n */\n\nimport { Box, Flex, Typography, LinkButton } from '@strapi/design-system';\nimport { ExternalLink } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { getTrad } from '../utils/getTrad';\n\nimport { UploadIcon } from './Icons/UploadIcon';\n\nconst EmptyStateIconWrapper = styled(Box)`\n  svg {\n    height: 8.8rem;\n  }\n`;\n\nconst CloudBox = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box padding={4} hasRadius>\n      <Flex alignItems=\"center\" direction=\"column\" padding={11} hasRadius>\n        <EmptyStateIconWrapper paddingBottom={6} aria-hidden>\n          <UploadIcon />\n        </EmptyStateIconWrapper>\n        <Box paddingBottom={4}>\n          <Typography variant=\"beta\" tag=\"p\" textAlign=\"center\" textColor=\"neutral1000\">\n            {formatMessage({\n              id: getTrad('Homepage.cloudBox.title'),\n              defaultMessage: 'Deploy to Strapi Cloud',\n            })}\n          </Typography>\n        </Box>\n        <Typography variant=\"epsilon\" tag=\"p\" textAlign=\"center\" textColor=\"neutral600\">\n          {formatMessage({\n            id: getTrad('Homepage.cloudBox.subTitle'),\n            defaultMessage:\n              'Enjoy a Strapi-optimized stack including database, email provider, and CDN.',\n          })}\n        </Typography>\n        <Box marginTop={4}>\n          <LinkButton\n            variant=\"default\"\n            endIcon={<ExternalLink />}\n            href=\"https://cloud.strapi.io/login?utm_campaign=Strapi%20Cloud%20Plugin&utm_source=In-Product&utm_medium=CTA\"\n            isExternal\n            target=\"_blank\"\n          >\n            {formatMessage({\n              id: getTrad('Homepage.cloudBox.buttonText'),\n              defaultMessage: 'Deploy to Strapi Cloud',\n            })}\n          </LinkButton>\n        </Box>\n      </Flex>\n    </Box>\n  );\n};\n\nexport { CloudBox };\n", "export const LinkIcon = () => (\n  <svg width=\"160\" height=\"88\" viewBox=\"0 0 160 88\" fill=\"none\" xmlns=\"http://www.w3.org/2000/svg\">\n    <g clipPath=\"url(#clip0_7_368)\">\n      <g opacity=\"0.84\">\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M139.583 14.4069C142.655 14.4069 145.145 16.8967 145.145 19.968C145.145 23.0394 142.655 25.5292 139.583 25.5292H107.806C110.877 25.5292 113.367 28.019 113.367 31.0903C113.367 34.1616 110.877 36.6514 107.806 36.6514H125.283C128.355 36.6514 130.845 39.1412 130.845 42.2125C130.845 45.2838 128.355 47.7736 125.283 47.7736H117.201C113.328 47.7736 110.189 50.2634 110.189 53.3347C110.189 54.5054 110.708 55.6126 111.747 56.6565C113.567 58.4852 116.602 58.5506 118.564 60.2255C119.759 61.2455 120.517 62.7627 120.517 64.4569C120.517 67.5283 118.027 70.0181 114.956 70.0181H49.0167C45.9454 70.0181 43.4556 67.5283 43.4556 64.4569C43.4556 61.3856 45.9454 58.8958 49.0167 58.8958H18.0334C14.9621 58.8958 12.4723 56.406 12.4723 53.3347C12.4723 50.2634 14.9621 47.7736 18.0334 47.7736H49.8112C52.8825 47.7736 55.3723 45.2838 55.3723 42.2125C55.3723 39.1412 52.8825 36.6514 49.8112 36.6514H29.9501C26.8788 36.6514 24.389 34.1616 24.389 31.0903C24.389 28.019 26.8788 25.5292 29.9501 25.5292H61.7279C58.6565 25.5292 56.1667 23.0394 56.1667 19.968C56.1667 16.8967 58.6565 14.4069 61.7279 14.4069H139.583ZM139.583 36.6514C142.655 36.6514 145.145 39.1412 145.145 42.2125C145.145 45.2838 142.655 47.7736 139.583 47.7736C136.512 47.7736 134.022 45.2838 134.022 42.2125C134.022 39.1412 136.512 36.6514 139.583 36.6514Z\"\n          fill=\"#D9D8FF\"\n          fillOpacity=\"0.8\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M81.0546 42.3195C75.781 37.0459 75.781 28.4165 81.0546 23.1428L92.2419 11.9555C97.5155 6.68192 106.145 6.68192 111.419 11.9555C116.692 17.2291 116.692 25.8586 111.419 31.1334L100.231 42.3195C94.9576 47.5931 86.3282 47.5931 81.0546 42.3195Z\"\n          fill=\"white\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M78.1614 42.3195C72.8878 37.0459 72.8878 28.4165 78.1614 23.1428L89.3487 11.9555C94.6223 6.68192 103.252 6.68192 108.525 11.9555C113.799 17.2291 113.799 25.8586 108.525 31.1334L97.3381 42.3195C92.0644 47.5931 83.435 47.5931 78.1614 42.3195Z\"\n          fill=\"#D9D8FF\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M78.2876 42.3195C73.014 37.0459 73.014 28.4165 78.2876 23.1428L89.4749 11.9555C94.7485 6.68192 99.9778 6.68192 105.251 11.9555C110.526 17.2291 110.526 25.8586 105.251 31.1334L94.0653 42.3195C88.7917 47.5931 83.5612 47.5931 78.2876 42.3195Z\"\n          fill=\"#F0F0FF\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M108.965 11.9555C111.829 14.8193 113.124 18.6721 112.878 22.4399C112.671 20.0398 111.375 17.6386 108.965 15.2287C103.691 9.95511 95.062 9.95511 89.7884 15.2287L78.601 26.416C76.1912 28.8259 74.895 31.2272 74.6878 33.6272C74.4423 29.8594 75.7373 26.0066 78.601 23.1428L89.7884 11.9555C95.062 6.68192 103.691 6.68192 108.965 11.9555Z\"\n          fill=\"white\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M78.1192 45.6809C72.8456 40.4073 64.2162 40.4073 58.9426 45.6809L47.7553 56.8682C42.4816 62.1418 42.4816 70.7713 47.7553 76.0449C53.0289 81.3185 61.6583 81.3185 66.9319 76.0449L78.1192 64.8588C83.3928 59.584 83.3928 50.9545 78.1192 45.6809Z\"\n          fill=\"white\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M78.1192 42.7884C72.8456 37.5148 64.2162 37.5148 58.9426 42.7884L56.5685 45.1613C57.0692 45.8384 56.8595 46.9829 55.9518 47.2555C56.0887 47.5823 56.0887 47.9708 55.863 48.3704C54.9923 49.912 53.8108 51.1848 52.4172 52.2639C51.9127 52.6537 51.2763 52.7634 50.7448 52.407C50.5364 52.7264 50.1997 52.9632 49.7002 52.9632C49.4079 52.9632 49.1834 52.867 48.991 52.7388L47.7553 53.9758C42.4816 59.2494 42.4816 67.8788 47.7553 73.1524C53.0289 78.426 61.6583 78.426 66.9319 73.1524L78.1192 61.9663C83.3928 56.6915 83.3928 48.0621 78.1192 42.7884Z\"\n          fill=\"#D9D8FF\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M78.1192 42.915C72.8456 37.6414 64.2162 37.6414 58.9426 42.915L56.4106 45.4457C56.8595 46.2326 56.5105 47.4906 55.3278 47.4906C55.2945 47.4906 55.2735 47.4745 55.2427 47.4733C55.4634 48.0752 55.2155 48.7115 54.6901 49.0815C53.6566 49.8067 53.0535 51.0585 52.1853 51.9551C51.7931 52.3609 51.3294 52.4226 50.9224 52.2894C50.7177 52.6298 50.3748 52.8863 49.8543 52.8863C49.5768 52.8863 49.361 52.7999 49.1736 52.6828L47.7553 54.1011C43.398 58.4584 43.398 65.5227 47.7553 69.8788C53.0289 75.1524 61.6583 75.1524 66.9319 69.8788L78.1192 58.6927C82.4765 54.3354 82.4765 47.271 78.1192 42.915Z\"\n          fill=\"#F0F0FF\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M47.7553 73.5916C50.619 76.4553 54.4718 77.7503 58.2396 77.5048C55.8396 77.2976 53.4383 76.0014 51.0285 73.5916C45.7548 68.3179 45.7548 59.6885 51.0285 54.4137L62.2158 43.2276C64.6256 40.8177 67.0269 39.5215 69.4269 39.3143C65.6579 39.0689 61.8063 40.3638 58.9426 43.2276L47.7553 54.4137C42.4816 59.6885 42.4816 68.3179 47.7553 73.5916Z\"\n          fill=\"white\"\n        />\n        <path\n          d=\"M103.041 36.617L97.3392 42.3198C92.0656 47.5922 83.4349 47.5922 78.1613 42.3198C72.8877 37.0462 72.8877 28.4155 78.1613 23.1419L89.3486 11.9546C94.6222 6.68223 103.252 6.68223 108.525 11.9546C113.056 16.4858 113.694 23.4934 110.441 28.7226\"\n          stroke=\"#7B79FF\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M56.3043 45L58.9843 42.32C64.2579 37.0464 72.8874 37.0464 78.161 42.32C83.4346 47.5936 83.4346 56.2243 78.161 61.4979L66.9749 72.684C61.7013 77.9576 53.0706 77.9576 47.797 72.684C42.5234 67.4104 42.5234 58.7809 47.797 53.5061L48.9045 52.3998\"\n          stroke=\"#7B79FF\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          d=\"M89.6695 30.8114L66.6536 53.8274\"\n          stroke=\"#7B79FF\"\n          strokeWidth=\"2\"\n          strokeLinecap=\"round\"\n          strokeLinejoin=\"round\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M53.7086 48.6992C53.7086 49.3799 53.156 49.9325 52.4752 49.9325C51.7945 49.9325 51.2419 49.3799 51.2419 48.6992C51.2419 48.0184 51.7945 47.4659 52.4752 47.4659C53.156 47.4659 53.7086 48.0184 53.7086 48.6992Z\"\n          fill=\"#7B79FF\"\n        />\n        <path\n          fillRule=\"evenodd\"\n          clipRule=\"evenodd\"\n          d=\"M107.974 32.6662C107.974 33.3469 107.421 33.8995 106.741 33.8995C106.06 33.8995 105.507 33.3469 105.507 32.6662C105.507 31.9854 106.06 31.4328 106.741 31.4328C107.421 31.4328 107.974 31.9854 107.974 32.6662Z\"\n          fill=\"#7B79FF\"\n        />\n      </g>\n    </g>\n    <defs>\n      <clipPath id=\"clip0_7_368\">\n        <rect width=\"158.4\" height=\"88\" fill=\"white\" transform=\"translate(0.800049)\" />\n      </clipPath>\n    </defs>\n  </svg>\n);\n", "/*\n *\n * Github component\n *\n */\n\nimport { Box, Flex, Typography, LinkButton } from '@strapi/design-system';\nimport { GitHub } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { getTrad } from '../utils/getTrad';\n\nimport { LinkIcon } from './Icons/LinkIcon';\n\nconst EmptyStateIconWrapper = styled(Box)`\n  svg {\n    height: 8.8rem;\n  }\n`;\n\nconst CustomGithubButton = styled(LinkButton)`\n  background-color: #000000;\n  color: #ffffff;\n  border: none;\n\n  & svg > path {\n    fill: ${({ theme }) => theme.colors.neutral0};\n  }\n\n  &:hover {\n    background-color: #32324d !important;\n    border: none !important;\n  }\n`;\n\nconst GithubBox = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box padding={4}>\n      <Flex alignItems=\"center\" direction=\"column\" padding={11}>\n        <EmptyStateIconWrapper paddingBottom={6} aria-hidden>\n          <LinkIcon />\n        </EmptyStateIconWrapper>\n        <Box paddingBottom={4}>\n          <Typography variant=\"beta\" tag=\"p\" textAlign=\"center\" textColor=\"neutral1000\">\n            {formatMessage({\n              id: getTrad('Homepage.githubBox.title.not-versioned'),\n              defaultMessage: 'Push your project on GitHub',\n            })}\n          </Typography>\n        </Box>\n        <Typography variant=\"epsilon\" tag=\"p\" textAlign=\"center\" textColor=\"neutral600\">\n          {formatMessage({\n            id: getTrad('Homepage.githubBox.subTitle.not-versioned'),\n            defaultMessage:\n              'Your project has to be versioned on GitHub before deploying on Strapi Cloud.',\n          })}\n        </Typography>\n        <Box marginTop={4}>\n          <CustomGithubButton\n            isExternal\n            startIcon={<GitHub />}\n            href=\"https://github.com/new\"\n            target=\"_blank\"\n          >\n            {formatMessage({\n              id: getTrad('Homepage.githubBox.buttonText'),\n              defaultMessage: 'Upload to GitHub',\n            })}\n          </CustomGithubButton>\n        </Box>\n      </Flex>\n    </Box>\n  );\n};\n\nexport { GithubBox };\n", "export default \"data:image/svg+xml,%3csvg%20width='148'%20height='148'%20fill='none'%20xmlns='http://www.w3.org/2000/svg'%3e%3cg%20opacity='.8'%20fill-rule='evenodd'%20clip-rule='evenodd'%3e%3cpath%20opacity='.15'%20d='M110.81%2037H73.97V74.1h36.84V37Z'%20fill='url(%23a)'/%3e%3cpath%20opacity='.07'%20d='M36.84%200H0v37.08h36.84V0Z'%20fill='url(%23b)'/%3e%3cpath%20opacity='.07'%20d='M73.92%2073.95H37.08v37.08h36.84V73.95Z'%20fill='url(%23c)'/%3e%3cpath%20opacity='.07'%20d='M147.99%20110.92h-37.3V148H148v-37.08Z'%20fill='url(%23d)'/%3e%3cpath%20opacity='.15'%20d='M73.83%2037H36.84L73.83%200v37Z'%20fill='url(%23e)'/%3e%3cpath%20opacity='.15'%20d='M110.6%20111.02v-37h36.98l-36.99%2037Z'%20fill='url(%23f)'/%3e%3cpath%20opacity='.4'%20d='M73.83%200v37h36.98v37.01h37V3a3%203%200%200%200-3-3H73.82Z'%20fill='url(%23g)'/%3e%3c/g%3e%3cdefs%3e%3clinearGradient%20id='a'%20x1='91.31'%20y1='83.31'%20x2='118.24'%20y2='56.59'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%237A92FF'/%3e%3cstop%20offset='1'%20stop-color='%233253EA'/%3e%3c/linearGradient%3e%3clinearGradient%20id='b'%20x1='40.99'%20y1='13.88'%20x2='.01'%20y2='11.64'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23A8B8FF'/%3e%3cstop%20offset='1'%20stop-color='%233253EA'/%3e%3c/linearGradient%3e%3clinearGradient%20id='c'%20x1='54.41'%20y1='120.25'%20x2='81.35'%20y2='93.52'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23A8B8FF'/%3e%3cstop%20offset='1'%20stop-color='%233253EA'/%3e%3c/linearGradient%3e%3clinearGradient%20id='d'%20x1='128.24'%20y1='157.22'%20x2='155.17'%20y2='130.17'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%23A8B8FF'/%3e%3cstop%20offset='1'%20stop-color='%233253EA'/%3e%3c/linearGradient%3e%3clinearGradient%20id='e'%20x1='54.24'%20y1='46.21'%20x2='81.12'%20y2='19.38'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%237A92FF'/%3e%3cstop%20offset='1'%20stop-color='%233253EA'/%3e%3c/linearGradient%3e%3clinearGradient%20id='f'%20x1='126.28'%20y1='74.05'%20x2='124.94'%20y2='111.07'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%237A92FF'/%3e%3cstop%20offset='1'%20stop-color='%233253EA'/%3e%3c/linearGradient%3e%3clinearGradient%20id='g'%20x1='73.37'%20y1='36.87'%20x2='132.87'%20y2='66.74'%20gradientUnits='userSpaceOnUse'%3e%3cstop%20stop-color='%237A92FF'/%3e%3cstop%20offset='1'%20stop-color='%233858EA'/%3e%3c/linearGradient%3e%3c/defs%3e%3c/svg%3e\"", "export default \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAMsAAAEUCAYAAACF5MJ9AAAACXBIWXMAAAsTAAALEwEAmpwYAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAGE0SURBVHgB7b1tsCZJdR548r23v2Z6ZnoYDQLEaJqFxQIUATKwaOVwzEhar6XYCEuxXrBWjpXlnbUc/rFihTf00z3IIW8oYiO0klZ/sRDSDy9ha0H2PwfiK8IRxhjGYpBkI9ENg/kamNvd89Xd977pyqo8Wc85eU5Wve+9t7unp0533arKPPl9PjOz8iVaYIEFFlhggQUWWGCBBRZYYIEFFlhggQUWWGCBBRZYYIEFFlhggQUWWGCBBRZYYIFjghgjvgZ1T/EB8QA/0HS+TZwFFpgDtxsRFYYIIZR7CspxfCf1nHDFO6ZnxuqeIy2wwJawomOCpAkSkWaNwEyA92BpEwzLjMJxghmoZvTAadL1+OOPB07PYVwnIx9RLyf/BV7mcKwEwYQJRK+iR8I1cApzgIaInJnDVJqhZlYzch0408BlscZatNICR65ZmAEyrYX3v//9PSEmSW9oksIoWQvhVdKBhmANU7RFfu9pusNdcR3At4mq3CirO/Bfzgs1XmAmXRhlgQQba5ZMQIVotX8Bwt9NnyAx0YULFyLj5vBzN27cOH/ixIm3rtd0frWih1NYF/e6Du++DuVcejey3etyuNzhfDm/X+zSX4rx4IkbN+Le6dO7n+/i9nI5uoK95gD/yGqvUjSDWdhq5wJ3HhyGWQoh0WguMQEFy3TCbPb29u6/7777ElO8rWOKR7qwt3VJztOxQXwiMVNX3ie68hLzfMJgdOYIQiaGOCEMoP0LLCABpGrIJk9xjNk55vD0jjjp/sILL7yuC/8/uuuP1uv4THfFW3g909XjD7rr57vrPEnHPmA7wfG3LoRF1bxcAW19ZBSYzTLvwCQp7P7uupAZJN6+1/rj+/v7fzczdPFd0H9RbS3vKq74Wwu8/EAQB16KSRA3MchPHyWDHBzUF4cfNeOkeu/vx58npUUnNIzuowXuMMBFPX5H29yaijWnZ9lB7yT0e0NYvZdsR9yFONPyT+4C4ur3qbQbQpooeP/ODn28e75EQ9+I6WwG9R7ZtwM/boGXOAT9HOvV7hDrFfUCI5NQxyQ0m0lSsk0I3az8zWOci12+H+wmBn6na/8lZ0atz9YJWyYB7gAoNraa1cLZoLKuQYpxaANNwkR8WAY5DFhlc9hM5rnY4X2wu/9Gd+1ZmsUSLLmvxEwhLVPPLznoF/GUuRVhsa83JWDBrl8fSQyUmKQb87/oGOUCzWQU/XyzwSqbw9Kdrwac7+IvdNe/Pzigv4MRmvBxShkXPbNWWjTNSxSqqVCczdJXt2j4o50T/LkpZ9xzyqeubdMd9mpNIjSuL6dpZ8fxL32LU+tE1Z64BV4iULaHpG0l/AxQXrrwbnX94P/Z2dn9WPf6NiuzGZK5h5b1cavoyCt3hqb5cqdlLwy4sZitmUHQhC2QHX/NVAvc5iCmOaPcnAgL2vHR7vqdLvRhK5OXk6BsMPrFa9de/LEzZ85c5ACYLBHvY15i9iws5tntDas4bjgstjVsXKSDg4PHu6g/utWMon0K7/1m1cOA86dOnf6Lrr8ugAOP/knU/cvhROOuZl4IXUy12w/ErmMepLxDOC0sfiw78BUw0XiSdpuJHmQAfWk8791ipuNgJC+/1F+dWfZP0943z9RSM4rF/M3ahXdBC62/wK2HakDSoL34Ip0/fZo+Zm1sPCqi2yafbaadvTRHOWvr5JWmmX+0u1+y1qhA+4jpesVIy0bN2wiSZhGDsb+//+ipU/TvNaPMkc5TBDhXynv5bMNgLcf9qLSOZ5Yl8/X552+8bcQbJgCy44+TJ4UpkGHgk4AFbgPAXcFpkN6bZ7s22qoyprfDNiXIm0kfzJjb1BOZ2kl7/syZEx+7fv36z+NaSxgXrziAYENmyXLMe2GY2wHKgCXH1PJPPBNmavyOe3xbq/GHzfOwJpqVvsv3l3Z2wm+M7/ZHaDKNPLBj2Wd2a2GVnPlu/eRxzSiDpLQpr0WQhzXV5sImjDK3TG9yYFNwBMyvJ4E0xBdGQe3B72LlH7b/L4xyiyExyvt3dlb/SEcMg7TZ4ByF035Y7XCc0KLVuRMJ3fsvdUTfa5gkqLqrSqXXZ3Q0LXBLIKRtGzpwE+K9WYR9uzBRa6p8DrMkWK/p7+7uhg+i5uYzCYY04yImaBdej+EkC9PcZBDMMg5OCaEp7fJy9T03tYg0/v7+jb988uTJJ9C8gueCnbQPHuzBeEOei1l2M6EsSo4r+RjtD0bLnscxvBPHcxstp/F3d0987IUXXngYCT6pDT7OiSGZaVqbwGzaAjcRQv6MdgwAzWIRRYtB5jj3R6mJbgfT7JAa5uLVq1feft9996VjmliNG5q++jYGP6tYzLGbBCs94zW+xyNllFb6beFmTE/PYYZDrCGdv/fee/85FzfEa8ElV/750EJcl4nLARk3BcLBwdpyS6uQo1i/2DTuZsJR1GOKsbAMuaC57mYkd36FX4cwc0YMtQkzjT4zYYFjgonjWy0/ZjM4ytm047TS9QzgYfOYipflrS7cuHHjkc4/6V/5yFpYkOStMgHuwsnH/WQLHA8oZpELyujEH5aAtk2vt5TcDDhMOdum3dnZ/cBjjz12LjFMvsRuZdzej9/yM/MwzgLHB44Zlgb96Dv+qEydWylA55qUc+lWmWO/0Zlj7yNlboEW0eF6O4zQRgscLYBmwXn845FQR0Hkx8Eocx35qfJxX9mceta7F1bv7aaTH2WtwnfUJjSaZTE7+8V/WUyx44VKswwDHm6aQ34rtM0U/lG1cROrCHAvfu1rT739oYce2uMAvS/M2iZjTCnrsAUOCepLSb6P08caWkyk34962vWo8tjEET8MoM/ngaGJzr/mNa/9RYkzbudPYOwnK58s60XNBY4OQLMEd/PkzdAmt9oXOW6YswkTcPauXXvxL//ar/3apfSSZ8kQygzZ+KFlWdzHxcxqd/MC20M+CmmeNjmqyRaLKTZllOOe+LmZE0ujRi9B506ePHkhPfDsGKOOacSqPu6AiXBfGOUIIezvtxYlR01zVAuLL7UZsaMsaw4DIs6VK5dff+7cuYv8DoyjZ8lIz4xxGvxkeYHDgWHfjswBky+m2TQ182MRhzI3toKb6Xxvo/G8vDfdEnT27L0fSHdmkHSPYCuzn6K2xOgP92OsP1leYAswmCUSqX1hlvM+x3n14i3G4/vNMn+suh3FbmmvX1zB4giUBKtVeGRvb+88YyZmYVuLV/uHvMsP2BZNM+QTGSfqSYIFNgdHs8g+nbuhclvQNvuUM3xc4G1HacFsDeU8T3kUd999z3thVd/MljVLt+7Cx/HqWrGJUPAX2BwMnwXXuYT5e8vhZs+YHWd5G/gve926y+tx3YXqgYlEzR+Z6ncrd8y2pgW2BscMY7i5jDJFQDdbIB5neXP9l+7/ude85jXp929wuhJNsQT4e5b5NhaQ119irH/WcIENoGiWcatEux9vtnS/U8DU1UGph+j4h5122VmF76HaaOuZIn+/v6ayfimKsaTfMoJbwMTUsQpdGMUFyy6yDNnKLwvjDWWVGIHu5fq1F3/8zJkzn6CBQXANxWQANUOmp5OXY5W2AGGGTXXgcW1ivN0hqrsZH+W7ZpBI0H+G0Jkyfnd3T/5Uic/nuuaNlvHd7353mZXRM2I5rJhoC6NsD9Up+vB2UwjZZMCbPJZTMoClvuVRF5wADBHls/wdMKPwfLn93cWtdlY/99RTT53jXODAivDhD384jvUIq/wrCDKLcVOlrsXCOTNh9gr+7AxvM1OtZQ61hEG0DBxrolDj5TBmHppYVwn6YaYpRnI6uEdNGuYtb3lL5EVMfdCF0jbLyv6GkH+A1Yqq+/Fm7SJu5t8Ii1AHpG+8czybQkWwRyHkpxlFF6zColY9c0CzgIpjUyybXQyBZ8WShuGD+voIGLBgnMiPX1kuMA0NzdLQKkgwxwhTek0IdeU4s1QXmi5skOlRtm8iP1MIgYaC972dnX5WrEDHOARmWF97YyVfHEKO74uGmQ+Nbx9aNgodCcSZCNbKeiWEsyVetpuA9kAImMi6oFyZkLaHbformu/nvvnNb55PDMKhHaPg6n7ZEqMP76NsluHessw0C6PMBGcF/2hgE4He42MCUgJ5TgYEU7BVBrc3eCYuasr0eHCwft/Jkzu/STJYtLLxHUs1JMt3+/PB3Ug5Pk/7CVGnZpOIpM8QyR69ogggQTXSwSlYXVFXZi7cxPmhuEEc+l85BH9WXX9e3D/jt/nKT+EeZkstoi+z+C9t2JWv3Sp+mjLO1MJiy/J1OVI4zmM2tUYAzWDONJHBHB5VHYcMtJjMqOMm2XmUN4civfSr1eoRfIfdxylJ/7OHTz75ZOA4Zhx1aB/6K7TAPHAd/PHT1BwCfVrov0URCC1iewmZSgiWiYnNsExPnQZDg5I8Ir0K/9znPvvgO97xDj4fud/3xQuUkKlV9KBOqh8dWxz9ORBu3FhHMYM0BLOutrkE30VudMsJ36ritrJz27zi7Jggn3BqL/d7UKjpdu3a9b95112nPoI5Pfroo6G71jyNrGbAPEsaZ8gWZpmAFZtMg8jJoWnWJN01o/A9kt/9RwxeMV5RYeJ9bl7aUoyN5/GKE12QO1vYqbpgEFDGNGD6u7u7+zAp0fTxj39c5MgHiJPB8/BRGK+1LObYDOg1iwqiw8njw8FUyZvUrGUe8bOVxs5Fz84ZRI2LOpr4prYMFF8RWihvBW+93v/QyZMn/9f0lr5RgaNem7xqnGxJypeZzOPlDHI2jGcbj1DKbNrzljSnxvOcvFr10ZrBzSVLYH6uGMP7JtlcJFKzGv2Ul543hDiBnL67P3Fvt9bSxzKj5LUXNKuEAozjETCikMw4iLuAA5JZChH0LxxImwASXwIt2TchdMuOaLlMGKbr4ULUnppXoUDVAV+WmVqYi/MGptJmmE5DzLCoUoLwJYdi49u6xciSKK/iI1IAM6x/T6A+S+Y5Y7EpU2+JWWCEhhlW5FjTbNEQaZqgqYHT0ga+raENriAlMhK5vltgxbEAqWdDVB30s8aKFVOWsIA5KLwgWGpvd3f1YOfQJ1+lD1AM04cl5kjTyLgrOYXpH3o1q7lABeF6ZhbZbeN6ix40TZZkvAtQdBOM6GnAUifmp7QvMdd3cMPn1mUaIkktxu8skIqBBCk4Xvfh5z9fpo8FMjBNbb8Jl8v86b0KPy4/klRgheYOg2QQySh4J+ddgBrkqK4m4EYwnZkIB3yPUXApPMJ7CQ9GOGvYaBQZRFyEf2ZTSp+GnDX/torVr6hLUZONGA899NB96Z60S773iMgouGcsv5cMx8/Iic8fw13JZfAXRhmhN8Pq3jCIUlk582FzCbwVYN2E7+XUI7enJ2I+qM6oJ8dbPFCZhzGzSxyfKY6SOxqfR2oGCcU3SpIspLPD+gLG58GE3Nu78o4HHzz3BDYuOf3MLGkbP5pfWCr/XHgpL6cfF6L7n7NYwVaahWEIzDAVTIcGPWPUZwtSX7wTUtyEOZSRK9tv2jxC07JUyfMfzJxjSZGe1uvhZKH+zkwx2XXRuonoqGLknMKqNwf292/8429d/s4H/+vv//6vUG1y9ZBX9qszx+DT4sDPzsdhVZ4vZ5DM0tMCEpyjSqyp0ylfYQ4UU2N+Wou4NSOZOFS30kiqEnRrHEljdMwR87sJDq9bpKc1i9Q2ZDIPVCcR9aVIB5+4/uL+Ry9e/PrH3/WuNxU/Rn3rUldjZJqWpLG66WUJI7PM6QprFqnlRGtmKhrBYUIDPEIvySOR9hP0RtDYZH6POUfiXRf/IlbF+/W2EQJoyGgmUgwUTTYR4TGOqbq6fuTG/v5Hv/ytr33kXW960x58+9IzDn96nN7VVHKcWMlfmOX69XWcptkJBMEcxZ4y0moZrrJhux2c5zBRdqyIvfIkyBeW5KRjDRJz22gWVOLXUtLkiOlM8E2zDKodkelQU3E+Me51fz+yv15/8MH77/kklsL3/AtiY+nODNmyb2yAQbNUtFNEdh1Waes2ceop6OY6g4PfJnwL5mquWLVrnbojylNOg5XXTAaqiiPJfH3JlllWtIyMjBJreCsM4oZdPFgf/MoD95/9XfI1RDTeg/P8soSVoJdyWYxCVDFK4D/IHDJJKKSmCR8JIZSi+ZkMfF9LaQgk5DdPA1fbTUJ5Xa8P6ODggMpXt3lWKpSL5EUzrmC8hzGkz5fGckiXkfEZT4eVncmIF4IoK9f9/M5q5wN7l1/40re+c+XnL136xvnUbOX0k9GJBIdalCF6uX4kNphh02iViYRgxSEP+iZZMErCPL0xUXFbrs6XtQ6atkQDygd4atUS61oZWFPNIxAm8laYPYI86PeuyWgjDDXN+ncfuP/ux7EWeNAFMo9+59rD2kzIeDNo6aULhVnE2GnCMxlEM0MgnwLmhMscTWZoQWuiAZ9zMcN0b0c8QZqF1hO/VpbqFOhmW6QUQJxE2Rv44DIOxwgTLOepmMbwcy52Fuf777//zO82vtuvQH0rQ3PS3AkQrl0/iPXsUY6k0TSaFoUWjkctioCnQK/LNFGlRBTFUpotWhM67VkkmjyqXxHNgjn8U+o5J7bSKHwbI4pcjyqMaqaR7xGt0otXLl/+8YcfftXFMWn7GxeYAACFdGdrltVgIkAHQ2TdcsTC2KDuiBZVci3phUiUd8TzFirzFbOfFZT3XFbVKc1wHVBGIvYb2Pr2fBFsFlZf+AjgQ4TKsYFnkj1Wrqp57KOAv0SGHyPinLAqnc6jfz9/37n7/vw7zzz3gUvf6P2Z4DEKzpLlnc1sgkHv3JnQaZZxI6UvFmaYQD1amBfX8C2GbSdQZiB3K0pJQ/VmT9ZqKXRNa2lOhiFe5ymHu1FiozvmUkv03qCaBiJFUB2ocYSZFkDDMnoczTV8t7TMwf6N933P99z7/6upZPZTyGkKmhZ3pIYp37PUrQu1ArEkf48abH9BxRUzwCojX/hJgEIuGkInRe0o8k7apJvlYpWQrIT+oii0BktY4lknmlrd8WEulUgNNmqSamsrIjJuVh2jWGctEqTmo1GT6Bk3amiZ3RMn/sV3nnn21zVn8L4x3ZRuVq0/VYarY8ys3REQrl07iKXnUS4os6DAlJ8RLUk/qu6WlqiKpWnbHv0tGZVX3Jl+AgjAQlOhKt1q9sYahhWngeYJAjO/Mh5KD0WdhdI2fTpYd4mjRtpMy8SLV1688uMPv+pVX5blD4iOraapaNL/ealAxyxrFs4eyvioGUVJ/QHbX3jEPG1dPfYz7/SdklEyn1iqSRQrwvcHLGatMuY5CwIUyxNJQCrMMBubZtBoZLohTrVWj0GUWWCYZhrh8ItpZsEwexTXv3T//Wc/CLuSoX6j1WD1LzAWvdSh+Cw9CJmQfQqlIXytoFfeawybbOaGzyG7KMVuULoiQD7Bzy3MCHGDsQ8dHEcXmllhFpsyjcCIBNrEYJIcjpa2ZKZ+ivlX6mrUP2uhpqHN6r8UYVU+QMJOoixdKHcgjb5CVCF8kXEfITrPGgdz0FTWYhQe1FhQg2YUzIlNG9O2Zo8GL0hX7PwcGyRmPXNVX6Mqt8uRbbBqSGLWLUDdhvgcjuam9lEoGL5MKIRfzZ6t6MLT33n216u6gMbg7/7BPBO6l17isGJnVreuhonBJS1IGHQY40UVp/PUDOYLpoHu11BCKIw+Zs0swMSQqQDLjkrqq+ZGqzZT8tKIr5gujDUfCwtVWslsyKCSaQIKi4C5ajxvmhkmBpipKJ1XtvOL333muc994QtfvT8JJj5ZBu5jt6Dmu0O2x4QXr/nbXdisqonYa/s2cTaDDe7RGK7tZWE7e6vwITrtCNnKjFL6apgxxLOoIGyWNmpTbgitE6mRiyUxvvNzFCbilO+Czr7l+F978fqPvepV5y6WKqtF/YZ5Ri9VaDILoDXiNmECL4uI4tWIdpzH7KOMzAFkH0asUbYOz73eCRaLGCb2xODOHvoNGabUKFoJoniNM5mmxTDCZzH8GIthOu33o50fcym/U2shU8XNoLnbD1a0MWiTSIm60rMGccf6I6qYGcWbmy8dHaH0SGXFHu2Zcf0kQhVXY10CLmOMkjpW/lLEClALZo963C699ndKmyF9AOuNY9HMZOFR1mj4PQRYY+L4IcNxLQfXYEaTrHs+32nuP/rGN/bOi/bY/RUNs2wciaLZbm8emmCW4IQFHx8GSTPG6DyGElfWXkoaKncRnvMb3zhPfpJmFgf1DFQWI4n09zGzBmgLholTiFvShenf4BMMTWGa8szhEgkXL7UfQ0HmwybsUJdw/uSpE3/whS984X62wfJWZOzXmIOHlzh+SAZ7ygLi3K4wwSz1iGoG0HEILLnkjBsSdBB0iIzCOGM6fh81QCgawqhPoIYFFWeEbAdxLL4NoU4zF7S4QhFSmAL5AbUMahFQRWIyIBjPQrMAw6xWb/2+177+n6fi80+K9z+QBIQfQDiWgzK4XshUt71mqQlUC9JgMsGAF6s0yBiSQWK5jB0TqsxIZJpGMZePjAJ5lPrNIFYDorqL2pQGttMGTOMhRj890XzmQYuS5/pwZ11QphmFOm6cHawZJgRLO0mGyXk9+vR3n/2dIZ8hZ+6vzEDEcWCKs1rJLSC63TVLeOHFg3wUji+JmeA5vhYALNNGkhlmmzy8jBVoQzs1GnWU+ckQiRZpmomQ2IOHscWYhlZErLOfU1edj1yPBUFW/uB7FHExs1nMz/l/HxAjCIwcr53+YWKgLFyqooYbLFTmGpNewLytYcWMkkD6C6gVhrZIkwhBmlZjXlFeZfQ9DdOA0GAUZXJZuUYo3iy2oTiwFS6Sigt2sItvBW1EPRE0Bo2ahuuC/aO1DAWSjn/u0IIXtHlWr8NkTXXh6aev/FQKhR9VYnPclAHoqOAE0O24NtNrlvnyNoHWIoBybLJBM0qtTTRYNSalGTfV+uNoOypMyEw/j1kFbZrGSmBwN2qZSsNQ1jBFkwx/5mgYmNHau3L5ytsffrjffGlZpV5zCmHdrh+TreYNRSSa+hmPQzYrGs9272ozjszMysBCXmJWO0B8o1ma6YZ7JOGGkdOAI4LDZDekhT6D16ItACNE1CTU1DCoVWCm89w9997zL0gNW9IU6KuQ7D3G4TzodmOUBKvZQ3GIqs9KKuxtvKMYDIXYeeklBKPXMROvLLyIamKPdbDIQsyIOGU0im6iRmp22mR/gpQZedpmmP5RmWR9PwuG0WYYSUefCMyydF+99envXP11gpbA9DCaY5VuZvMrjj7AbQOdGTZnBd8HGJfybrVQOJ9ZIwg/AxIGL/dAle0tjELOE1sUJK4HlZXnNaRKF6YQmsHNYgxzLNpRfvpIRuPBLEOeVyZZtrUqp59nPNlEYzNMO/z7N/Z/7MEH7/mEWTA0HTUKFeuwoN425lhznSU6zwhaPAizRSUKYP6gGaRlyBAmyaJofiV1Qy4nOGVOaQiFJl9mDFF0rjng2iMawXjdmHoCplEShx+zehA6KFA9rUxBmGjWGky6dk/sfuCrX/3qOa6ymtAJyuzCGqVfLluFAW6b9RfBLFENwVwmsN4rEymM0l8iyzKYIQYmAPMA70GWIwnBgMP09Qybh+uNV5xIv4HlJiPjTHwjTTAYholdhAb8EA6FVeEWwTAlbaDCMPn9/F1nH/hHmdr5p/uC2gxrMUPkHcy306zYSnZfu16a8FEiFnWc44l8kwE1BK6NjOnzrD9SH5ZlZTxFRDOIzKDH0q4psDRL1JEikMyKRC9ASww/C3IrSOTOKlqPxYpT08qhPNOk/9Kph/c+/fSzj2JNglrex+1PPQJMBuTdAAFreKsYqDLDcBbJIgCJTASCpIQ1EwZJfGy4RpFPKIzk9kpU5RnRFn1iMZ501/GCkc00PslGLBQzx7ApaA7C5gxDgvQcDQNO/zA2wCRqhkykD6CTMtPs7O5c4Hzzty9VldXqfQzjz4/LiFv4s32rQXLG1liMWiC/Fs1AxCu3zRFrSuZoSGBgFFgdxiTqoQ3BqUdo1MdTAKbkj2aMTKMZpQFxKvIwjIMIgV+lKaVfQA0IHhNaJUimQ3NstQqPPPPMCz+XnnmxErbB4Cl9vIhZMclY6nzZcNQQnk+LkofVap6o1tJUqJFGdsEvgl+iFT4DKi0W7fJaGYQphNmhbQTdbSUwTqeZBNOEjePfCCERdecgTXiGjAVZEbjVTFmZHbv4taeefvsP/uBDz1g1wA/EiAgttbILACp5S5hmNSUVCwTnuZXcMpW2YBQrq6DeyzVRTqXFyMCf0oRRadRIk+3aSOLPxdtWu2gI8gGUQjG3gkZW/ov0YUiYYzmL869+9Sveiz8Ci9WFRU2xnZ/jcCr5Vk2PhedfmLPdhaSWOKaqWowipKquTyuvqQzCRBoHZyKLHNHWLph9VT2nXm4/EDXr2WwCRI7T71GEjc9RxoLm0JpmDFPahcLes1e+/fqHHnpoTzcJtu+LMmHtxRTRN9OHWRnl2zBTM2wKRSiHOqzJm6o+mtBn+QnRuXQZjeQVsKPVKE6L1rmgqyciDgPCFJ3wX8L4PrQjgGYJxY8hpV2GoHjurrte8V4OjNa3GsTMFZkZxmoq/JutYIpmwfO+JiXZBOi01jsPUIkLDjJW1ooLjUrQtNSuylA+gd5pUOHTVGZ+uqje7Rc77SaRk802rQbQJEIAxUq7WFpk3HgpN1t2imDv/nOnH2jUptTE8l9KybdgVmw11M46WLt+7iHY4dG4a+1QCe4o86yeSWbmSlXr8nDrxwqnZBGbisLNp1UFL90mo+7ibivdMO2U/yJQgkYnsVgppGAOCvHcN59+5qdJAvDf+KhX8NUWmP4Oe8gO0/pZsBqIedAsUwygXyz84EVq8Jo3RfRenNNVXhYuA6vsRJvIL8MWBpuwgBLqlvSxcL3AOYJBl4caFaIFzWspjyYYjCk/8jQyTiWf3D31i5jFu9/9bj5YXMyIlRJDRVVZiRVGuSk+/6BZoiIJ3emsRomqxTmP2FClV8zWujQo6o3BrqImDreORjktni3P0a9iC6LuMCxgDlTifCILi9Fog7pnpKC0gl+dkTuKz0Jh5BTAGRknPPLtZ64+ksLSukv6yfEUnddg+m0xcYShWuOaDK/g90yUcY9dq/SFP5d8FiFGAjXFq47zYEOqCg6vlqKCjAjVQ53eqmLJZhvbHpGigR/8RKEZPzubqSrZKGEmnsJnYc2fKKNQGomY84zZhPVmxAjWXCId7B/85m/91v/9S/gjsAa4VGSsvRw7rEgqvHyn2X7ELA3RyIsJF/2XosIJiDvaxeso77kKi424GRB0JZvUF2vtynenHiaKg1/qVOXSztcFoV2c/EOoA7NEsLWLTLqzu/tzrEmw5Bw2DLkyrZQ/o6s2UxRtD6tSVgjU3BjWGqhNtEyUQYPBaeNawboaZQVZ5Vnypg3qNieOy5tFdSrhBumGukebCpT0iBlX9k1DVbeDReRQjyArRrKPdb11pWvfpS/g3De+8fSjnb9S8NKCZWIWOFJpxI71iZdgouHQHxvTjOssaJs4xXmd67k8LR4LVlyU6TAvTfhzaLSYCB6BxjrjqCvgQYB2z6xYBHOmfJosCiVVteAwQq4AYbqgSiEZP5upq0rkx+x30BimdyMjAmsXazWfcvjJU2f+Tuev8H6xgCYZToMZjBJwwZKZCnycY2GYgVkqdWcje9ZY4AfAq4ewvjT+ulH2JhoiioqRLwYRT6WPWLFoFIOMmPHmaY2sLQKcqiP/AKbHtbFRRlBp2xpmTr+W74eaJap3S7uQ1C6r1c7fSHc4hb8Pf/LJJ3vG4enh97znPdXueGtlHz5ZPhbI6yyqIuWPBFezwF0P7ywN0ApoSmqjQAbLNyJqmySNuFY7RkdYZ+jlFyWjcOoqzegQIQuAuCdl8sMdfzmzPQrzqQu0SwnaQLsIwurpujPF9h7hrBLTdGZZyNom8nb+NFvGDKWqXDQKxwMTHTmEZ58/EN8jIqtqE+jI2DY6mQZxq+nAeK942kszg6GCE12qZwiQSQim3CHZAWOI46CIdEFnQ1Yab1Sp3ecesNlJcUxetGs2L+PIzoOm1TuPYwlf5wzWMfzGL/xv/8s/fMtb3hLxW5fsu5DRME0JbKal9Kv8heXxMUt+tOrRTpzvc1W5kO6t4qIhhMjB59pvSMgmQ26QcJNkdt10B7DOSNzlNTRIPIyb2Lw5WfwUPtSiMAxqxyh+jmJYD8PpY5N5KP2a9BP3n7v77Zx/1ihjeR2ktRQIi1xGYxs/bvU/MsZRtuAGJBCnzRPM1jSZQgMfGCViuqguDo66etF85mIjjXsWnCy9qok0U3jk4jUEU1l5tTpr1BWVeijpo8AYriOiGWQQJTjQjw8CIYwOi0iRglZv/epXv3ofv+MWfvBbCKaUi7mlTDPegEkcf9R7x8apY1EqSeIk41mrlTnVCnARFekiysxXhGdRVCCqt+ZEdct73cIoh5Fg+vgwrDdbEwqtpsSZ3R/VS9SBVUAo9Rw7wIOa6OpKT7Cr7nf12mymtWAutyibjxjEvNPPip05+6guIf3haWTQKiW3xBQXLlxgLTOQxlZ28nyQZhhusdVCqyEIK2titBbGbKwwo5iqghCpd0aTmT6SbZIMqYIVbi2c0QSEDXABaRxtDK87ryzqueC1py6T8QOFdn/PNMmkViMQBsYvhlE+DrYyw+B8sYP933zg/rPvI6uBBt9mJpLyKJq/PDZTtM0DWGfJRINTO46wwCQxGiaWqmKAsJHQoyhGm01K0QgNIYvTcjAIbTTGt2y+OnRaupqPTgBVgkL2by0+kKhkpbA9sVJWsSpzzFv28CHA0EaGrstNDMUikEMwvuyuVg8bGAGOQxLVBUY5VDM2hcIsVSdHEpZAYQyI060bWzAaSppEQ1VMNBhAEs30AkaoCMVbo6g9FRob1CpmYlhinIEaa/wYLQTQLkSjFOY4wXG14JBZBqMSYbJ+xquChikWVHyoU6ApRmH1CCkWxJV9dvDFzso6yyJcwJeZpfTnQnj2uQOxEM3b9QPYzzjLX8/417qbGWnEG9/kM1HNPhb7Fa/OyCPo4mX6MnBRjH5R2THagwtZl9IiuVaqC441ZbVSHgNs9RX0t2lxjPj1uGC+RJoh3cqRXX+eRi68G8cIfo/l2ftIbJwl+9M/ufg973rXm/YSk6R1FgY9O5bDisOPDn0OOLYNlj2zcK7IME1yCES4rR+tNxtGxkIGjBWOJhJRJFHw6hS5UrXd2qDmvrQY5Xb0AEwxwTBmGVG2Inq07dXJXISWTCQZxu6vEs3xpslHtC3DwJ5G+DtGlDWXOD4zgxX/Be7PXn3x7d/3feeegExLRxhrLn289d2+riYdIez2parx4Q180Su//NhsxoZlTTvNOGz8HjG/opT9gR96JyjCZMoNAo+wdtHegDcQ8vibl/2PJWUxKWY4pSDODEbySwbHfEEp5LeM6roFRxjkipQ3M9MxUAi+0nc1p0eLYVTeDdGpsgPTImKntZOdOEE/1N16Zkmr+JxbXqwUzKJW68uiJPTdjBI3h3D1uYPRAg4tDTFnqDVYJpMxwji5EJw4rEXRbOROFwIrU+PUEEJLLWqpHRtEPl+BCYQ5vRjc2TyZoUn7Aj/IfhgyV30q8erKUNUJqGUrzZIjLLOLUMPEKDTOjes3/vEDD9zzK48++mifxcc//vGoGlNqk7fExLrYWDEQHSGsVBs3BN2eKMMj4gR1h0KLYxjGMGsQaOx0dfI64Rd1YnGKxTpeJYzL4AEcP6/OxcyGoxyVWA0Eal++ZPcKtz5K7TIKAdXPqoQqH4Zg43oQjbRBj3/ZK5ZfhxmxxCSFUWDrCnI2AaOImhlCZhsJ74LQLA20fJ/QDj3qzHAimrFlwXTcLEUbgSmVHqHmuovKx9pqYroRJdJ8bOLOHsHgyfyxjgFwx2hDGxMwzYwKBPdFBgvtErEccv2WMQz8ljV95L77Tv9PSbM8+OCDkZ183FCpTLHS2rHZ4yDBUbBItIeCGczS0AQqzFkYyii+OSTubENnp2CcmVPM0Bxww7QIvkmopxrqWTcu2YFjZBhnlsfK0mEYQ7BRbJh6Q5rQ4hCdRDPLyCml7oJZhBUw3Ndx/cS5e+96R2diJUZhcwtnxlw6xelk3AaTGA0+IjsiZhnpcKyWMQKtAZsC4WPo8YtGsUTKJJAxtYSMBl5LQiK+NdVKFcxhlrqmKhzoF21/r1uDFkKGv1Gy3YARawFh4czoBGSW8ZG83cf6vTDLOl46d9+ZN3CuegqZgJPZ4YdDxkVtNByV/xKuPnvAxr2HQttCGWRVzWhKO1VqIMOHkulaGqZaRukDbSlrksVMwepFNFvXIGpTnwU2eQZGCS2tkYMD5ig6ioc7EJkMI8MnGSbKBzTFRuaJxQ0tPiWBhlknzRIv3XfvmTdkzVKy53WWdKWPwnJcTRl5Gtkx3Q/NKAlWBLSMxDlwvFSZY2R9F1Ijcl6haGO8qBqo+nksT8tm1lA8Drofoqi/jLIZZXiKBENsONlU8sV7jUDTECWq1f8COcbSn8NMk8MolOUBWX2nc41OrMzXq9JYoI/nC5YgMUKvQR9++9vfHrIJViAxCGaVnX6B42kW/JoyxjkD0wbTZxGCi+Q8vFzd1xogS2kjzDOT/HibGHScr10MLcThjalZe0cCM2iosqhaFUTSAQeqUpmFivAabFC/NdsdyauXnyu55lhs4A7aexRSQy2y8MxvxMKX1KIkjUL23ntOn6CaIKhz+oMzlUw6zJsUyu+H8l9WRQIXTTJ2AMta7gruflFrycsQpuvDAgKlnh4C/Yy4ZOJF8Ufv/Bq1RDTLoDF+bKCpRcviJVQ5qpr2FyY1EKCYulkwFjXE6i26yCrMMIUdTBOvrNE0oB4pb1ynIc2IJQbJCQuj4H4xr1jNKGqG7FA/V2FvpCSvTvwcqSZ6DVpGIuXw3WJyzDeoOzOCRThDnuUHQ+G54I1irzIv+47l9xAU6/HO2VAxin4eK8Rl0lTnmhDd0HyJbtVjovLhttImZWkck4uq15YtYCjtYft+fs8Lkqu81qJL6X0ZZhi1/SXksIBrbH2EsiLCtjNUKe2VZ+ess4wFx6iJPkGrmzycqXeIQYKOjSnUMOK3V+0zsRlT4CJdsUdl/czSQ/PVjJwctTBPJs8Z/tKKYBl00TZ+qzYZ5lhlcpPQ0IXSUThFEjNhLKSyGRY6pumRDYbB96g+DEM8Uc2kTdJPhT9+yO/zZzKL2ZVbQDt95RtsCGzHC7t1yDgjOLNeUdbPxJE1JfzJayJqGIq6DDL9GhfmMhZNM03R06GNIyocfAyGsfti2Q5W3jPCODummIX6BckUtHfvPadeSbXS1r3H4RHXU0jp2gZszSy789DmaJM5jNRmlKkFOG2YcZjQAJkakP6lk4eSMyr7YMjdYRkoWeZnNa8QpSXLNpE5kWZqDoumLDzZXUZx5W/gx6AxpIYpJYfacMZaSWqWDBnX68vpMc2Iffazn9VJemT8OvLChQs4rsw0xN+9gLm1SW83YTUXcRhzhzLM94n8lJ8lTLwQ5Hw8kaHOqUitMU9AhkQ4+yIrMafepmanWTZ8CyXWufM9emma+bLEJnLNWRrixD4xh7/CjPGM+sXJC7nEOgq2fwxhT6fgTZWJgdI9TyP3vonle8DZyQHCys9ZaH9mUxBmmMeCIw23JT/HMa7ezqLLiUIj5GchjlDAzNVkvLrfYmxtl1t54ao+SECSpspW34Ko8kWrVDeQwouQQS2uYFtQsHun4Mw074IbEMQrzoCOBIWCLTNzDuh5C/2WGD/R+Sz/Xc4yooZJC5Xp7ixI2oNXqlDRKpDfZr8etovt0nQ7FzRTCA2BdygshiwAcPS1mG0OpWacMayf6qw8WQOnUH9wcNjgwDJlJSM19lEBZlC56Hh+CKpLECkqmVGjyH10chzZxFQqbWKcK0osAWPvaKbGIuaSUYy9GRY6JqGOSUQc7xXjEtAce1wdh0QDE/SfIqfTXwxhHVrCvgXh8tUDpudcEmuQqN6BKcYIO1cUj3VkZigZZlu5REKHmxZwo3HB0kxR5Dlu3BzwrBUZUQNFfOObo05AC1jdEXT1yM6GyGgKNXoBCgxNRIPYW/EiAKyFzOFSnMDJLqBZcGaMNcuNGwe/9Yr77/qHUMqaW5EXJYlGY8/szmSG8fFICRLD5HDcUKknkkwqtWCX2yx95Cg6ZhT0mcix99XK28BMFqNwwjDyUqVB9N2DmujNZ6FdeCcC42A+WEdqm02sBPNMgaXf8EXMlnmi1ubjugljU6hSEFalrTIaxXswpV3ISx/nU2Kg9UV8z4uSafo4wBTygJrvyuFnZgiZOQjjUcNsu20/XGafxeqxqOtoEeVUVxNNU4kKEzNbRMChG8PI137/GIJyKD4SuXMqRbKzpDEyIKIwo9lOUsHTk+JjAsFro4kXJuJQ1iSIAYyycbhwMoY5p2gZkprlyrNX/9rf+B/++ifYBEtrLcgksMGySeBZgzBe0SRHcabYriFYvWqAPxKo0gIVHUaJ4/KxwYC5Q4tPQfV34v67ZAoeJ28qWlTBqgozjEo+/mit0jCKkCKYYZC47oGNh07lEdsMI03IeSLOzCMaiqUPDLUGzhDqoAoOrl/fS4ySmOTq1atu1Xj3sT5InCGbXmL2WG+oBM1CtAHTFJ9lAk3lCZRVonS8TlZT5LiZBI9fmnaYqcixOr1uuyCihpQPZDePcQLajdy8AHUo+MGlxGYZNEG8gSpRYOGHMM0I04uSNXO5uCgUCBITapAcCJol5ohBs9DePWdPvpKTGyv4PbbzjYsJeGqlsditGWcWbMEsm8kjj/j1zI3e1Vzjkjtj5yo0eJ4yQ4IVoNNPUGrQT0FGusPiMIwoJpjVquo7bwGT5g1jmEYr8uEQzLI+OPjkvfee+Wsyde/Y9w/WPjGSRGkBM0Q0ly82ZJQEK7M8t8drIo7ULk+YK0aaasMjd3SMBZcZhaOLTQzhZjW8Zhi4YkGT1D0a5czpZkVAcQaepb91hEklpd/svDSqbq6HGBsIRSatSQwGjm6rAI46iPGJbso4ra30uaTpY84eZ8HAYUdbQY9yf/pL3lRZNIuxoTKFbyT5QbM44nJEzViW6UMlztQizqIkPyvkhng1Eqi4cU9YdFrQhpYGCpMBGDXa8NaMVqX8gp+t1jAWswT3xQ2arY1afVKyGeT4UN8oRxenjok1S9Y26f78c1ff/cpXPvBRUs1S5hiaYRYJTVEKZeaBGeTNFiXD3tX9aJtJhllR4tp+xWhi6dzwPjKcqBDV/ou7tSRAHnnQoyyioqwjIQyaZ8p4R60GP0FVjhm9BSM3q8omYov5pySNYJacKWiakVl49X58/sqlr77xzW9+/aWsWXhREkdSj6qIu1kbKjvNst+L8lpL6L7TDEMkdHTQwwvv0cxCFqTvGucowKZdWb1MOIMIGuNDnU0jAIPbDKN7zGMErV388uoAlvxT1vUUPzQZJmuKkUdyrbMpF0PNLAMDxUtn7z71Rp2T894/Jw2TZsPS8+P1T+kV537GSv1GlLXqGSXCZ8NRZoMNK5sbYTvLYDUqRoljBuUUFotCQTtUcgOvQ0DUVTLyFNWLwLdxjNdVc8sgmYY9tBiNUTfyiUZBVf7RzsTrqiLoqYEQJzLhujljEq0HT0hCvx7srz/p1KRAdvQjfimZf9HY5ATYUGlWM9PwxpQV9q7sj4mmdC2L22q1DuOzaaR9D5JEd1gm0FkEo5oN682V4qTyLHmpMlvaJZAd2ecXJGOa6bWmbeVv9DFtEu+hT2gvoXkL8ccSx+EsdIRmiaNl8lz2V9785jfTF7/4RUsOpSzW5ENUuFYcNT4InE2Jq5IBM0F0qtHj5Be+G4wyaNaIorVcLck0F3TPBCOuegl+PlZ4NMrS1Y6bZIhEhF0cyNS6U35XbFRcD6Hu98j5t6pL0/HBQC59JOo6qkfRh9kc+9f/+tOfSq8do/TBaSaMr1RMdvJLD6nv8GMOM3qRCPegiQ2+W8IubtfuQUi4IBorwJLcTWryQYsETSsE8ZZ7EwycqgDrNZIrQUuUroSgFjuRSFtEMWpjrzAI0uhB4pVqbDr+IOtiqLIVeDGX22iqCtGvjQ4Ow/rKz/7sT/XfsSTNkkLhw68e0lGunEmeDQt560sJVwuVwo/hHcj5x1xxQ2UMG2497mfDhKizmGAbsEwJgBbNETW7WcQLeqTNqy/MMSMz06wBvGoWSXG9MKmULRV0Y5yiyKlvoEY/BXGz824wSyk7tMdBwkBK3CfigBDOL47Txs8/9/zfe/DBcx8iaCabY7xVP93vueeetE8sGkcilYKZQWBD5VBabX6JDci0AUifZYNuKSWTLe1buFN5tMraBiy+ZVtaEINDsCXRiqpKtgjVjg/mm8g2TMRDhFu+atu2TMWR05QR5a10MAnzb/yiM9KX/tOf/6Wf/dm/eTH7Kn0RavoYN1T2OB1TrGFPmCVPhiJj/UNHinE2Jifzp73H1XYMA+kQxmdO2WIQauBE4+5dU+DJCl1GeW5ohPJoqS7Ai05BhU6qhkSzbsEK9OItCHY9jFeyKtDs4zjR/0Z9CdofjQwO9vc/9La3velSxyjFks6mGPGGyqRVcH9YmhXjT4sTZP+lpMca4TdYDKxN+MikTSE8kzVLaxOjppeS2Mgw0rRmEdKdJpA3yTg/N747803oFLfy0wSV3iLiSQ3FWQSlOqJB68EWRKLcBgcF98UwucIMpmxpoGxWDaaXCCSxL4zG+Ke//a3//uGHv++TZMgwhmyKVbyemOTb3/42mmTi18Hwx45aay0bb6R8RphhEFFpG6HdRfiYZkICtUBz0Fx7bQrXwnHSILHrNZYivkLN9ITxNJ135hgTR+TZYAYim2HM7ptgFh3YZJgo6zWuEChmifp90DB55f7S3Xef/IE3vOENfezJkydjMsU6zRKs6WP9my3GtheP7EbWjeZ5EBuR624ovcCp8Vxju+TJmmUoZmvwkYtUM0U0UWXrRSfOyLvKDzVDBGJDc6XR9X1yyCNY8aocMy5XIIZQVc9ONK8+InsMNnBCoyy3WCciBrnZFdGHh3GXccrg6vNXfzW9felLX6KOYXjaOJw5c6ZHy9PGPXSapc8av8PPq/e9fMoaxKwhL1ompz9/57LedAYMoXfwh9Lm51ExgZL4SG8eIbTEgciz9W5VTOPOkB2Vhgh+kauMpJos0kd8qR9VnUM7fkTx43XhFjMblQ1uRnZXYmSJz2YWfu7DEdF4Xq/Xl//ki3/6rne+860XiUzNUI1Yp0n6RUk9RazwPbuDSMuOcUPlDOoYYTXk4oxGtO+eJkAiQ9yo8yNHilKdp3g3m67iESfaWesiA1w0p4icNzJFVMVjARO0PgnVYq4nKFTbq7oosjTHgNPmq1V3xRveS6GH9LQ+WH80MUrSKNmhr1rFzj0/J62C5xwzLi5Q6qNcccsLb8WHj8CC+lpyFuwK+2Dgt7HB+rM7S3vozjckWHn3qgbdNdUCtIGDCvfSaXOL4N0SQwRNd/Oz6kSj9pHk4qflfp4cOUt9WX2swqt8Qx1q41BTyphfguSMvLanlz/7s/+YTDBKJljCTkyTfZaCnj4r1schJWCGYX8FfncSFyIlWcTYokavhSYoBz8Ak0Sq9L8n0TzQA6ikW0VEioC9Vs4pamYVXCj18eLMFydoG3PMaKyWYyPALGbNjWQdFog1ag1h31fBbtMgoMaeHElnHGD8vOLGjf0P3Xfvmb9XVXD6Wbznxcn14/YPslZp0KnPGqg/ZukQs2HuSMiiLYrEeLckEkwSnKQWYVMDF991cV7cXGgxjBsVWgzRSJcTBtU4uXBqfhpL2EpxrplSt62+bfm9zDBEtSAamHEsql6MjNyOvS8++fkffuc733lRZR/zqn3/rA7Zi+qO4eYQi5MqDGY5zGzYqiTBbfdssKoso66+vhwQ6cgmlmCEB/J7C6tAM+NaEtTMI1J7uHTmwY6iiaRMdGzexMIo+fA/qIjcHJjjhDzOP00YtUiRFFavbvtDqcePoEq1IgNNA4muX3vx/+0Y5RKist9y/fr1kg1/+JV8lfyjRn11+Rwxrj7+fAT+EKtmFFk1saHSk8UuDJqlTxaAKfwvISvJpEynugQyp2mt/ByUjcHKw83XU3PQ5eJ0GC+OqFLOvulCpgYahmHMxDpRZsxqqIxgiUDz/R6y6jeagla31OsrmXGFyiqahIDBL/2lN/5Xb3rqqafWiUGSv3L+/Plw8eJFvfW+OPZ5P5iI44f8o6wh+y+e1omwidLcI0Ybklt45rIyw5S5pIImWdFMA0R0S0EReqXInTjr3DDEk5bPkFkwpoR1fwgXImCxIysI1AlmKAJpgziPYao4KBtX6aspY4qgiYaIK5ev/MKrXvXAhwpCbiozTqddegc/7QsDzUIOs1gj14P+QlKPxVD3Epdl0wbfs1RsyZIhDxQKDauGniUmnuM0o0wSwTagKzSnEI2XiVh/JYoft2Ex/fGgoGIi2DelD6MslulqFDB4Gs4RQpwVZMdFAzc4KQDxxo0b/xIZJTEGp+wYJRqMUnLmo5DyM3dfH4dHsybCh3PCrMP1SH0ZGWH6eDbsyoaiVPHNsIId6MhGs2UiRY1g2Qmtd85Hm1Qluv415hqCMEOCmFseC8OTNIdQUVAlcbA/sb2WRurzDNsJj5InZC7bzIis7oJMF2Q0NZiZtWJc0+X/+Gf/6f/EqM4/6R8Skzz//POrvL0lvvDCC30yXL1PmkWdHdYXl8wv+K0WdOgNRak+kghhrOKGEL57eT/KlBYlHhPo6lrEPsUUXtbIGFXc5Oa6saxS7MAEQTHZWPVsOqnfhQk6I31+FQTrFQG3mVPmmLaf1OjKB65nlLiwxlYzbv0blPp8txTy3AvP/fKDrzj3W51/Qp1/kvwU2t3dTdtcDLFh2gElzDjruPJZYL0l5veAp1Ie9pC9zCz6uKE2w2zFltuAxzAcDTZpiwEm826i5TJyH6166hkSR5WJTYgkHfUhgBzUKtBrUaupwUw41LOqY4tBQ61jueU1sHk6PB8cxE/ec/bkX0dGSfe0eTIvSBZfhbPNU8jlpyZSBGiWcmCFduoVk3Bak2qYSTY9MyzB7lCCla8/Gob1MQ0ebpETDfxgS4Y+SjNKVGmMQqWpxL8URpUm0WlkuCUQjboT18OOt0d0Ip4mNGf+I9eWo8yP7egQvaInw0VYNhGHx3jpT//kT36BGSQB33nzZNYuva+STLC0iTJ/GRnSTBhny19I5qIi7g9jJnFW72WXK4d+U0bp03z3st6iP5f6uRZEhk7eOBsza282AzK3DumrIIwSz95RPb/fipZw2udplzooTGuQ0IhTWRSmQJ9kqo75RU/tF7M+GEdkUUQLLcfBjxh1ab779DN/67WvfeVHjaK1qaUde4Jt+hUunxdmMImmQpN2aJOBNgCYRYhCUsa5lLymSWQPTuMIGtt0QmnvQB8vHGyAQFtMUesGTkMwmQF/MIlqqtTt0uYaWQVBatYWJJmyYphmPcd2BiJTXeh1peHgkhEBeTJTZol98doLv/qKc/f8aqdVImsTsju4f06m1ytf+cq12g/GzBHn7DQ2PiEuC/lREcM2GoVhVaw7j8JyM4vzyao21ojW9GplOkUqhM6mkLhoJLqca1Wd3mqO48HhIouZnMKmyZY7HxzrK3pRJMc3XxtydSiMI3+6nAfF9NmiwQ2N17KmFEb1NE5/Q/tKc8YM9g/2/zAxSnrOjNJnkhYgk0lmlZrML4NR+nT5G5Ye1BFI/BPeQwvHGS4O09PHYhp5W1iR7pwhZ7HeQmRJKMkc/foCXEgbBQcN8TEaSB6jRwcaGWLECCS4d6y8yNm7xHpJE2wNl4eJ/EgajoZr02YJYHqsyxifhSlESK9jW2JVXszaGuQR5gnvA8B2mVxo4ccA/g5JXo/r9Vc+8gf/6u/ze2aOmP2WpGV67OSvIKSPuxp7wVjD9J8RM8OwCZZX58WJ+Yn29Df2oZ4e3AqUGQbkGMF2RYhE1sHd7d9gkWYDDrppB8gqQlhw8IKTdlPQlsL2qcR2l+xM4BJG0MgMud9NsxYK8uICWgFWHoCOJp5pEuqGVd1cVvAvff7Jz//Ej7zjHZdSaDbBtNQilRsOPm6g7PHhNyXJSY95zB20TfEFrDCfiM0agsQKdC2Fs4pjJ5BYS6B2IBkXjfSmXR0qPMxJghXWAk+41GogwuUmGS2WconcirPplJQ1AhN5JCB6xlUyo9SLlLYoom+GANWkW9LKvCyc0RxfX/r85wqjJE3SY6R7uvg7e0jVp0yOfLrz0UdpBgwWJPsfXU1X8ltSvrw6r06fDHprfowxWtZClBJ6KwCfZRyPCM+BLNEgfQpmhFx/UQCYvi5EHG0bQ903hSnt5Zc6mJX5vTAN/DR5IHdyzGIwwTAmGVFhUsQRZpomcrPsWJdRVYYmIXrphkbvffmrF//Wj/zIoFEYkr+SFh/BwS/ZaTMsr9z3v0qMU8a84zg7+OUQcL0I2WcqTa6A2/HRh6ZZLfYhfGdPr+BXKCTFic0UNeg0kugIRbLA3xKEUqdm9dDE0eaOF2eaRSqNxQijU15riFqhjiGmsqVGs0J7RKRLao+4Ns1EN1ZDH/ee+spf/MQb3/jG//DqV786fP3rX9cSLeb1FG28rckZeHX0EeGpLupEFw1VGEwdl5kxOiSRraZz0MyhRKCDH0JQacgwZ0CtbQGVZC7PoYmLW4nQL9NSWM15iLwi5htkeCQIy3+iIiWejpXOZ6Mf4gRGU4MEbffVKOUPY8D4Kd7qnPnLX/7zgVFSbGKU1772tX0czHpZPosZjtvyYfNk5HPB1Lf2JFoSo+BpNUPWl2dNIW8DvWaZQIFnLb4ZIzhmWGzk6dkPwJ6OhCeI1xJ8jqZoaQlMV6oKdauqr5oyx6E24wzE0ED0qj/IqLqzAvzp2RS0SzArUe//GvouDj7KYHqFjjn6b1KyQ4+DWp7Z9OI9YR1zrHnVPoV7s2H614mt71d4Zgxnw/Q6nrEOsxUMJ/s080HpL8R3eY+mp6hEIUomw55GZ1Y4yXG8W+aOxkUG0oD+k3aQyxVVetWcEsZ5GmRrDgxL99iomAizbAuahEGuykawks+9PuJYece6YGaU9Tp+5TOf/8xPvuc9P32JsZNfkhglaZbuKg3JDFLe83b8Pg1ub1EnThZ862e883lhPT7vBUtngiVI08i4poJrfOEoOIVmaRaBTmOb8L5xsSVdPUUdVZjGqwG1CytlrCGXGbUnvk3VZ4CnRUL1QKXSoZGBp2E8nc/t5+yn6hiq3opVuv2DG5/68D/7/Z957LHH9jqmiE899VQffl6u1PegZsB6SCe4pM+HeU8YSQlc8NPesI6Bem2lTs0vOMZWl+Ehmt/Xb0ukFaw2wo5RzdJEeJ5MSgPBjun6kKIVeNYiiFX1SgB678AoJRzKjKi6PAlPtlY4EsEU9Ko7jRXlODHnPCJp7UeRXEdAP+tmBvfdYJR8v3b9xd++9+zpn0BGYR8F1lNQi4R85/d+dzEzCh8Azj9/VwqmwW/hfPJvs/SgGEQ0j2knnzrJ4fh8JFBpliJ8N6IPVnuQRyBDQ7SY3I5TdNNCna7lETl6ObOmlJjqw74Jhg8iRjiw1De0SO6DoPLgOKQWzevjomhQBcYxKJldB/Hys1cv//KrXvXA73HaxCzp7k0Npz/8uTDfMY4UIeuf71Zd0L+zr6I3UOLXklVFYN8LaJxDDX6lWYp9G6kp1fWsljVzhAdtRENaYl68YBmFNqDafmZKIF2GgUe6/kcmZIgmFjAsJgd9SswGfNU5kNg9X2mRqBRRzginqs20JU2AU2RIJuoZ5eCPP/vvPvPf/tAP/eDv5SxiN0VMnVbpsbJG6Z+Tr5JmwQw/hfJnxBFPn+wYhDcC9fvC+IvIPOuFSrTPi882RmZRWqZomLGdZb0l5s2Thx78VXF0o2QQTWvaeW5ZJsVxVo68KEOVZ00A2HnH5vucPIb6K+52GiQJXIY3ajn+RcaGeN6zVUJYSGjsGA2BIcWvYBpMKzgxZJwxMBAySiyO/LVr13777N0nf/hnfuZ/vNRNC5ec8hRx8Vc4Zffe55LNL/64q++i5Kew2ZWmiPOPE4nOTH5KuieHHo48Kjj5E+L4uH2QXsFl5jiqRUgNK6YTvEpJlplgMNHwMF6j5BtXunkgsCf0M85WiXBVBy89GXgaP1QVT3QSXU2BBG7lp7EEONpR4/Q5BFKIcQzPKiay1s/PpeuhjMKmFb/kza4EjBY5nPd4HXzlP3/9P//km3/g9b+cYjKjiAodHBzwzBeH9eHZJKsUYGIc/nHVBMwovLaSZsPw2CPjZ/D49yMrbcJMwav7uOM4alPmCGCFRFyYRRHt8GJnECqKJzAHYpUnx4VVGPGMMoOVX1B1NJ413hgWZuU158K2D1eUfUGqrap/C7EqXL5iqU8ARpBaGgVT4PSxKr5UIhhsz3WK63j5+eef+yd333XyTX/lh9/xqcQkyeSSJQ2QtEvSLMlnSaZXAr4nMwy2s2BNBcMk7YIr9WqzpHUAuO7ZQaHmDu+mj/vZM5wJO6LZYgG7saoCjVLqSPlyGopEwIa2KmE42dwE+Tw0CuMIwomktPX6uWWCWozCBUYVhHEF0nCvxornc98Fjjj3HZiO86urFczH4XVIsH/j+u995t9+5v/623/73ZdS+De/+c2+iqBVQmIc2M7SVwyde95+D858gWSCqcXHkD8Vtpx6ZPlSXtIsuGel74pxoTEGvadli+/r58AuV6vQXYs24Rn5CltohQ0RQwHMEBM7Q00mqYjdwMHytSwNlvjV6S0ZliP6/o8yDCV+kIWOjAIdUuYnAol0+LmyPu5orHfN7n08BI+y1Qgr+aUDJQ4+9a1vfvOf/NW/+t98MjHG937v91Yyhv0TZY71kLRJYpLzw8mSumP7Z2YU2HpfqtA59qJn+LBvUozDphczAi9GBvjJiDzbVeofxr1WR2yGBapMJtf8gIvfV9mu0CZQZdZQhHL4IzEnHcXKbEl3/iEhme9I4HxfhTpsSDq6t7rNGpffw2qsU85kuPKuOj7sIgQZLa5cT7JwmGnA6VDVINR+IUZpegUizaBU9flY1vpgv2OSr/3Evfec/Mk3vOGhTzEjrFYrzY3ake/jz+ct+AmSyZW1SgnLs1/91vtkevGuYq5CniqO+vfus68SYDq4+Cl6ETILW2QUUj0QGe8oYTWWEOyBpnosEOJWzFursKDuZqFMdEVkE6HE7l+RORSh6PzMDZXAhFzTgAE6HrQ9ahYhCKCemhm00232vTMgU0zDAiHB9f3r//LrX/vaTyYmed3rvv9TnSbpI9k3gVmvMVEGXoBMAMcaRV58ZD8Ft9/zz9vl0ybLpY5k7cviGbDkqySm4DWVCjH2J08GFra5/5hpIqBxHB0l7DazKyMxSLRcA/Es5rYJv5r0fYmRckIVKw58IJbdEbCGtDGbQP3fIHMlUQPmJEtkx6ouJWUY30rJQHy5+WN72MmPsi+QkbH8IOpQx48FYKszCuapkqLQSIuKL1x/4bf/2R/8f7/9vz/22OXMID0YvklviiUNk8ISg7BmyVPD1aDmM8D6ODzeKOGpLx95D5hJblmrxG9/+9vgtQ2ATMObIjuHnqqzHbhrjtHRDs9cOUBqP2IrD8DtKidOh9V07ac9qvKJZJ9s0T8eOsghs1mWJtSvIo+s7dbdzNaNG9d+7/Le5T983ete82nOutMgkX2TbHIho0T8JoUZJd+1L1J6AVboSw+xGWb96jA/sHOfT2/RvSrS6VMm+4eG5jC26B8ZhL3ELL3jamQepDTjMIsC5tLsJoBFWmEWIZp2RCMfK02t7xSV0IZ843QOa0Wv33rLKhiBNDJKzyBEl66/+OK/evq73/rDR/7KD3+q0xqlVGSSFMiaI72zdmFcY8GR47AV0WkdniTZ362PuRIAoxDmmX9KouTp7Acr5bDfojdPHtdsWPbPg1gzQOc5+7eVQ62vVbDDD3OtIF+2yVdG/ErhenHkxFvlhUa5QeVlXmTc1UXYv3iVdBkL6ziEXLpx/drv7+3t/f0/fuLfvvneu3be8j2vuPuXf+ANr2NGIS6CfZHEGOlibcKMguspyCiw6NhD56cEzJeBt7Hkb+qFWODvVJhJ0sZI9llgtZ6PZUU/RWxtyU4If1wf0OzCPV/4dSQdA+werK9/Oq6TxAnDxOU6pgXDmE71jev1UJUyDbDqWLlD7qaIYhwWBhJCPOjwdlZdXXv8FJXfScmidejL6uLWKc1qyHPEGdaWhrd0PEDKL5kM/Js3K5LPJV8jzIZeBK77smP54bP+vauvce/TVLj8vhZlcjoOX9HO969W9PAwkqoSOWxggqHJqLXWFC93nb13sH/jj7v6Xrrx4vWvXLmy98ef/vS/eeKxx/7ny5CbpYApa5SyRpLvpO7RWk85D9vus18SeC0ll8N7vazuLS3Nq/S8zb4yuXi1Hr5bKfG8xaUPHJ35sdGsPvJ6C6y7HBuEjtvPdo6Vi9DFl+cOL3bvvc3bSSZsuK5lTM5kxunz4DK6cE4rysjxff76GdKYBk2KX2fC5rSMh2Xjs9VOHZfC0hg8/fTTEeuY47C8qh1cfqrbP/39Dz987t57zp29+577drqwU6fO3Hfi9Jn7ON2NG9cvX7v+4uXnnr96+crVZ/b+w5NPXn7fP/gHezk9gbawgBnBopSo7sFIy75K5I+3lJ9SZr8grx6PnXo+gpVPbDGOX506XbLQUtIk6YOufCaYiM/TxEW7aPPrZsDd3YCIK4Xl6yzH43u6p7DEaBAmLswnP59VeZcr5aNwzvJzo15nrThdBr5Dfc06p/CM48bpeCP/uxv9cXaiDN3PblswTLe/Y4C70tW96+sMhJ+BO1+n8eqY4VR3P5Xu/Jyvkx1jnEz3fJ3oGOZEuvPVmVi7+XmXrxSWw3fgSqp51THGynom23q1wo4fgFDNAfcInCYGkBQzkGJKUoOdw+6iCYJPVxpwDrMIz6tzJhR+5+e7vDpMta3FpLmss2QQ9VzmtuKg7botdxlMckY9t67CJJ0m4edT+TkxhGCUzDx8ZyZhBmFm4WuHmaTTMIVR0nO6FHMIRkmaPTvy5dLvNMCxM00qIHV2U93nOGdOZ4hnsPLI5hSbZuXdKDfmWRpddimjZZJonGS3p7s2UXRbjbb3beV6sv2vcZ0+K2YR1yfPQAWrjlYbdF2wT1JdksmZxkMtKBbIs1potgpTJznvaSNkeja2qvRlqg+3sF964J/j1qfgMx7u/eLjV9ML+y+k/Bty5wyJnfvi0B+3I98CLWkrCQ7ap+A0NIGQeEqae1LSlKiGdLfyuots7XGXlR/VGsYq26wzpL3L015eGdtcqDWhXqwlWuZW0SQdY5Q7P9OgNfQzmmCn4F40inFZppe4WHtQ1iRZi3BY/0zS9EpMkWaHQg6ztEpZiOT3mwE4fSQkXwKWhp1UKD/BnOIBB6WDmIrMOAEXvnT+CXgKE6UprBMQrAWUOmaNUZxW1iApTM3u9Hg5jzIJgJoGtCaWHVRbS92zxtBrFFhfXQ8LTImo+4a1cWpH0k7Qt/1UHM5mpTj+5DcBf3OSnXUxLZwc9hSW93lVznieJi5hsJ2lIsw849Xjpq8e8UdTKc+AJcdefwKcw4jqRciiQbrnnu5w4y2fmB/Gh5uqXe4G6XS3uqMEtaRXS7rdrZ8nJGGxxflu4bM2UuFao+hnsx1KWos8OT8jTD/f7eGxZtR1tcpgHKN/qj7Oz00fBLRI0Rrsi2Q/Q/soxZknpVG0M5/ek0OPF2UHnqRmQUe+0ibGxRrEXH5Kmob9FfBbbh60iJcUkepni5A9nAYxlHCMy4R8hshlsp7oElFwfKstrXjFNFN9UOFmwuwJuOFgV+3BOilT0exTXRbcZznuJBlFM4Y2u4rpxQyRmQZnvopDT4b5RcqhZ1MLmGbSmSdnNgzMsZtnhlmfjpJhJiAeP6OJlUwAXuzKz2UhDHF3dnbYyRT3FI9l8IQC58XpoH79ezIvUly68gZAYVagE5zMMFixHjsh75UiGnfhWpDrV3DyvXemc7lB9WcxZ5IpldY0Uhqua6qPWlGPemU9t7u0hw+MUH0o6omf/CaTK128hpIWGNO74cALsytdyYlPV9pmn3YR54+4yu+p5G/nRf341Hs+lzgfNiHy55+6KwXn3cR8lFEyudgc04B7v45rW0sLhMlAtaSt5ubnaBSSUlVfZ1Bz0AzpT7bG0mWcmcJlyeytQ+g2c51AorekPb6fQa1HtdYx+8iqF+dD4KijmcXPyZxiUwunf0lplxx2KmmKrC0qB95ZRylOPZteZEwTU54qBq1iTg2zAz+lTbLGIboF08UCJmZTBDHgoJE0A1wzgQltBoMJptS2vrOGYJkkGvcM1esNFrO5zODU+4zVLsRBpnH694zTvyWfTPSmL4JMQoohrDBS/gmp9RN1sdl1Ui04Vr5JZopqHQXey2xXuuNF4KNYTIN+ipoduyWAA1eITkkva5C1E9kKv8so54wiqoqAtESdUxbWG/NAjWK1TTOiVx4p6a7ymd03DSddM3Llg7AG8RiClI+Cfop1pXjQMicVk7A2sVbqC6NoJiGpUXaA6JFB+ks77RbTgHa5pcBOoDvQLN1aA60loMI7rcMdghN1sPJUdTTjlSliEqtmKq8crG8D9zTkV5XdaEez360yjPTC1HLMsF5zpDC9hUWZW4VZcJaL1HoKGesozCzWajwwibtST9Rcodfm162BTNAuUZIzuJqYLaJn4tH2NjWIjhxipRlEnepj1OO0V79WWZjWqr8WIB7D6zyRofgyhMZpq7+pZhLNEDj9q82t/kIGwTvPdDGjkMEYOEVs+SmUtYjewmIwkGVSVdoFZrpuLZMwAMFrQjutpbeSkE1piQSGZRDZphMw7WmP8GaYYqcbDNGU4pohUJsQkWnaGfla7dPxpw3tprVBYXwjznxmrYHTweyf8D4u8h34SUYhYBaI23HMr55Z1BSx8EHUOonwURw/5bYAk4DJIWqqpVtFCMqmrgipZSa1ykWmaJVBDaI1zLdSFy3hsa5kS/pC2FivFlN7RE/ZlNJ+iF5ApLapVRYRkRnA96g0CoE/QnKmSzMGapFq1kvNfO2AA2+ZYpWp5Wkauh3MLwBB+IqYKofRIB5vECtpToq4UHJiufrZI9pGXc7oemkCc6S/p0XPNPrBdLSZyJUj7vYpawHHaReagiRjlWlgGme3islF9V4uPcOl93iV6eB0ZbwdvbVeaxTUJHqfFyntohiFwfNXbh+w5uP1gFmDCereGlxLInq42uQrxMxhisBNSWswhCeRTxFoAXIYYKreXhlkCA5v9mrCzDql+tKdycJ3XjexzCwC550MTYIaBX0SzSjeNykkmUIzS/FD8KJ5Tv1twTSr9Plo+nQUNs/183N5s10P1i875dXfoH7lqfzu+f7+fmDcfC84WEbezJdWpte8wY+GVfHImwA53enTp9cqj4h1hG3nfd04PtchqnARB/WM0HbcNl7ioc0B0kasP39ZmO7wjH2U2lfawzicN44Hr7Tj+Vx8igocP9Tnk88Ujni2MHwCjOFB/fBpzD8kVM3P8nfzfAaxOrxbb4TkfsF4gQPHrpIH6mDvWz9nnKEpqabCswQ7zTg4PWlcRTvhFGYrf0yn3k8b4aUOSsJWZUyUV+Vn4bND7X1RuEl5hsbWlzCtrClfvHCNBDWJmhLmq/VFY7UhksAM074JqangqTUUY2Vem2a3FZy0dpqSP6BmnJdWM4OyrVsMaBIbEr9OM4MBThp5mszk4Pbh3A96NRy3jqh66roJUwnztvpB5Xmy9azeT7QuayoYVuNxtgt3Cu/ojZB6dZ5o/CZFmVqEs2Gef3IzN0duBNa0IhF5g4REaUo0nGnBlWGdxpKMCkdMeRoS9ZQON+plSWV3T5RiHrM/FGGb9bfy0enUgqBV72pFXT8bvobntJuzW4aGQe0hfBX1WbD10ZZ28AUTeP6J4+zfngzjDAQO+glypJnHNAYjYD4nvDJxmwXiNNYJTniOLOIZktaTxnPXJFrtLIyCplCjvlVb5pTnMQqGp4s1hlpEPAGzWWJLfb5W+lt58p35atsKGWZY47olXz1uBXpOngyCozaBnrDucwkMiXPKDue6OnUWdYJZHySuyfq08p1qk5cX11dN155s1Q3XQIwZrJPaD6FaY5ibHgkYBOJ3WswB2kOcxqJ9FW1yUa1FmOzIWaG/vZmFZhAnzSCixhYKk5iJpAnVIBiX0DHOIETxjtrFqq9mWr1wZ5mRigmsep5o9a+uq2YI/QwMdMJJYzGH0CwQb668e9PCxhqKucfLYhKiaktL/6Kc+9vXVwGYZAaSg+VJ92pgeXCt3auIYxEsTWgoqokR8c3ySBJXaUuD2CvGVfmfnCi/WRcyfAnji0QRR2BiGb5K5XeQsY2eQHtYXzSSs72ejJV4Aibx1lCIxM5hPdt12zMIghhER7pVDEM1AZbwqUUvY5BbElQwl5NeEKZTTjWdCgSn21dJcc2EXpsaM1EnHQIvDIF1UofUFVwIL2YXjRrjhJrNElpEb0mx7sggfBILOR9uoSM/tTEyBYAZdltst98GqkHHQbUIHwesQfjVxen47jEVSSI5iem98rheSGzWFvOJuvdxmlH01pBWmxsMMRdvV8XtOpfnvAsGwi30mlnSe067mnPhNhZmjnynOd+kAN5LFqoBbBGUZiI9y4LTkWowBTF48UjwJInihCqjKockY1RERYooLSnNaw76+FFkVGMriG77CSs9KaY1mNmsD9aLlGkFBL+DWsXY4KhnuZARmrNcFpPQaIIRnvFlnfWV6cycIn6pAQ7SbuOy4s2D1ZgQ9EB7klDN959olH1CE4Qut/FumiiKaC0CN/OeqK8r6TUj6j5plNPar4XPgoFo/CBL7AimmhFc38Tax+V8F98Ke+mDR+zes0eQuD0CcTSB6nfjIyLzXTObUUYhmok8dzUe10lpPC+dm5exyDeLCVQ9d+b2hfctScNZLxdog6kZLnTkLa1hmlt0JzFJhpXxg5hl45w6XRB/PLNscEthCS+dRkj1Brr+jNsUz3npnx3gPNOxOrxRDyAAXvkhnFQW1jufo1tw04/mkAF8TA/XJdeZ8u+G9L9SlerBv5xr5YF9ws/dPZ2LzL/Aa23+i6qfezyuDzkbBVV84Lqnsow2lny4j/GXf/PxQyW/YOxi5OOJ1K9t9ccUYf7MBNZxRVH+bPsdxzCVlAMJWWkBvZ9IzdvvggngSmd1yIErebks7eMY9WxJalNzosTXmkXXids0oT12HrXXMlxfgxprHd6ULoY38tF+CGoG7X+0tEkxtxxfRJhayrl/yTvzFXjz70T1pjqqTRiBZ5g/2jTaadjoAl/Z3bu6PG16kWTEncYUqj5kwTSpaJoBXTzdp94z+BIV8av+cs0ptbo+11G3fBPNAC5z6IXHeCuOUr1FsLPN5RAr31f69HQiakrTBo5gJgevIjp1WvuOjiclwa34qToTMLbuE2CEXTKkvtYQXJ+pfjcI3mIQsTNYz2ap7+Mnj03F7+LZ12nsA7ujoSIM7UCqQVxZ8/VWPiSJAaWfxl1ZOFPmCpZlSFgzP5KE1GQqqhfrKmYkX9pXBM5Eqs78FeV47dPt0tvicfbK2DIvzCoyzC0aTSki21l3p4MNZrtjYWPpNsNOrvKyGGjKxMALzRJOO8VI5BA+GYxvaRTPNGoxP7RpZdzdMIfART0bH1oJU8v4Bl6YV60TViY+9SXjnVTcHQ3WQFeMMINoLEmpCVFIc8yf8cBEsJhxNcXMug5IeDpvx5QzNY1VhsdoZPgLqr/MeKrNJm9nb+Woq0VB01knXwtUTIEOfcNEu/Oc+AnwiGJlmAuVWWHFkxpkZWpVkhWlptqT5GmHiiEVkQiibawnCDPMMtPIYf6GKSU0ARlE3zihsdISJB3uiknIYQpO09AOuE1+1gZIWIikO21KeC5Yv5uhJaBnyojBco7DqXDRVND2toen8q3yw3Ick8ZkFC/eaYdgCN0O605g+jzufwMiyjWc8JWFQ45DTgaxt+LAT+nvYKZV08EMxg7iOx8M+9YcTIugcRW4cYWpPBuE5knQQiiaAOcyYpw4f1cTKs4ekcGo1p2f5wgD3W6dRr27v7c4pRmIzEMjyoZI8plrAYtAPak2k4g9gtAzMeW5xZBenqo+XvpWfsioui7BMAvFsaJOnap6pGdPQ2ji1jNXUI7HENivrZ2+6FtU+7cUM5FeN3m5mlwWuINOhoT1JKH1bJkehsmnCVfUQ9v+Vn1bddTvVn5GPSvTqNVeUgKkZW5NEHZhDAuv5Wgb8QieCWbFLczhgSNZMSx4xEVKOpEkMksyelLTJDp8RyJX94rgWAOoGSJdTlVnrZla5h8ZWsKLw7oqZ1nUHdtjMAmRQ+SoCZRWaGkdYvz4Evy891aBu+UBOppIdTg4gYJItIlANjGQyj8YxGbGGc+a8M36eG0km9nNNJrwtPloMYBmDk3Mcy+HCUiHoVOuyiDAE6aZGt9Fs3ig7FlqqX+PGOBOhrQntPU9u5ocAmmVSeR+bOQSrQozyyXaSJpPtmeTmSurvmosyNPqVhutOi3MsD3g3HlFTIaKJ5ogDm/AMtOQN6gtYlbPQdV9kmCwjROMKzTqhFYgJ70ZbtWf7y2GQpNSt1WNFSntTUbfLnAI8AYS44hmEoMeSD3IWmO1tIZTF4txppiPyCAgAiZOgVNEPveakPDavCVVx6oP1GxiyHX18iejX8jogwW2AHewp0yuBGAnk8ZRU63C4dZxVBM3TWkArn/0f/bZqrdbJrSZIJ68NQ2rz4x6zdakRKawqBgs1pYA6TrD+wJHCcAclX/RkmZTWoJ8iTqLQMhmokriYrhKR5YWUu9WWvTlTI1IBqM4phJ5bVV5E0lGs8LJqC9BGlrgeME0d2LtWyCudfXgmFFN6R/VdovomEMRJhEsjcAMTzYTmnk7jIh9YdbFaGuz7qqPCNpBjboscJuBJmrNODjFWKSuxrHSq7x0nmSBkqgchqaHNkM4T4tpiWpG52fSuLGeIscyiwAhHzRzezhVH2K7FniJARLQDCJx0xv5VUSoklaS1UprlOcxWtiknliPaPhoc8FgaJG3Vf425SywwAILLLDAAgsssMACCyywwAILLLDAAgsssMACCyywwAILLLDAAgsssMACCxjwXwAjz3DG4uUkTgAAAABJRU5ErkJggg==\"", "export default \"data:image/png;base64,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\"", "/*\n *\n * HomePage\n *\n */\n\nimport { Box, Flex, Typography, Link } from '@strapi/design-system';\nimport { Layouts } from '@strapi/strapi/admin';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { CloudBox } from '../components/Cloud';\nimport { GithubBox } from '../components/Github';\nimport { getTrad } from '../utils/getTrad';\n\nimport cornerOrnamentPath from './assets/corner-ornament.svg';\nimport leftSideCloudPath from './assets/left-side-cloud.png';\nimport rightSideCloudPath from './assets/right-side-cloud.png';\n\nconst LogoContainer = styled(Box)`\n  position: absolute;\n  top: 0;\n  right: 0;\n\n  img {\n    width: 15rem;\n  }\n`;\n\nconst RightSideCloudContainer = styled(Box)`\n  position: absolute;\n  top: 400px;\n  right: 0;\n\n  img {\n    width: 15rem;\n  }\n`;\n\nconst LeftSideCloudContainer = styled(Box)`\n  position: absolute;\n  top: 150px;\n  left: 56px;\n\n  img {\n    width: 15rem;\n  }\n`;\n\nconst HomePage = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box paddingLeft={10} paddingRight={10}>\n      <RightSideCloudContainer>\n        <img alt=\"right-side-cloud\" aria-hidden src={rightSideCloudPath} />\n      </RightSideCloudContainer>\n      <LeftSideCloudContainer>\n        <img alt=\"left-side-cloud\" aria-hidden src={leftSideCloudPath} />\n      </LeftSideCloudContainer>\n      <LogoContainer>\n        <img alt=\"strapi-corner-ornament\" aria-hidden src={cornerOrnamentPath} />\n      </LogoContainer>\n\n      <Box paddingLeft={10} paddingRight={10} paddingBottom={8} paddingTop={10}>\n        <Flex justifyContent=\"space-between\" alignItems=\"center\" direction=\"column\">\n          <Flex minWidth={0}>\n            <Typography tag=\"h1\" variant=\"alpha\">\n              {formatMessage({\n                id: getTrad('Homepage.title'),\n                defaultMessage: 'Fully-managed Cloud Hosting for your Strapi Project',\n              })}\n            </Typography>\n          </Flex>\n        </Flex>\n        <Flex alignItems=\"center\" direction=\"column\">\n          <Typography variant=\"epsilon\" textColor=\"neutral600\" tag=\"p\">\n            {formatMessage({\n              id: getTrad('Homepage.subTitle'),\n              defaultMessage:\n                'Follow this 2 steps process to get Everything You Need to Run Strapi in Production.',\n            })}\n          </Typography>\n        </Flex>\n      </Box>\n      <Box padding={10}>\n        <Layouts.Grid size=\"M\">\n          <GithubBox />\n          <CloudBox />\n        </Layouts.Grid>\n        <Box padding={6} borderRadius={8} hasRadius background=\"neutral0\" borderColor=\"neutral200\">\n          <Box paddingBottom={2}>\n            <Typography variant=\"delta\" fontWeight=\"bold\" textColor=\"neutral1000\" tag=\"p\">\n              {formatMessage({\n                id: getTrad('Homepage.textBox.label.versioned'),\n                defaultMessage: 'Try Strapi Cloud for Free!',\n              })}\n            </Typography>\n          </Box>\n\n          <Typography variant=\"epsilon\" textColor=\"neutral1000\" tag=\"p\">\n            {formatMessage({\n              id: getTrad('Homepage.textBox.text.versioned'),\n              defaultMessage:\n                'Strapi Cloud offers a 14 days free trial for you to experiment with your project on the cloud including all features.',\n            })}{' '}\n            <Link href=\"https://strapi.io/cloud?utm_campaign=Strapi%20Cloud%20Plugin&utm_source=In-Product&utm_medium=CTA\">\n              Learn more\n            </Link>\n          </Typography>\n        </Box>\n      </Box>\n    </Box>\n  );\n};\n\nexport { HomePage };\n", "/**\n *\n * This component is the skeleton around the actual pages, and should only\n * contain code that should be seen on all pages. (e.g. navigation bar)\n *\n */\n\nimport { Page } from '@strapi/strapi/admin';\nimport { Routes, Route } from 'react-router-dom';\n\nimport { HomePage } from './HomePage';\n\nconst App = () => {\n  return (\n    <div>\n      <Routes>\n        <Route index element={<HomePage />} />\n        <Route path=\"*\" element={<Page.Error />} />\n      </Routes>\n    </div>\n  );\n};\n\nexport { App };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AACA,IAAAA,SAAuB;;;ACDvB,IAAAC,SAAuB;;;ACCvB,IAAAC,SAAuB;AACvB,qCAAiC;;;ACDjC,YAAuB;;;ACiSvB,IAAI,mBAAmB;AACvB,IAAI,qBAAqB;AACzB,IAAI,gBAAgB;AACpB,IAAI,gBAAgB,gBAAgB;AACpC,IAAI,cAAc,gBAAgB;;;ACnS3B,IAAI,mBAAmB;AAAA,EAC1B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AAGO,SAAS,oBAAoB,MAAM;AACtC,SAAO,KAAK,MAAM,KAAK,QAAQ,GAAG,IAAI,CAAC;AAC3C;AAIO,IAAI,eAAe,iBAAiB,IAAI,mBAAmB;;;ACgB3D,SAAS,UAAU,WAAW,SAAS,KAAK;AAC/C,MAAI,QAAQ,QAAQ;AAAE,UAAM;AAAA,EAAO;AACnC,MAAI,CAAC,WAAW;AACZ,UAAM,IAAI,IAAI,OAAO;AAAA,EACzB;AACJ;;;AC5EO,IAAI,kBAAkB;;;ACK7B,IAAI,wBAAwB,IAAI,OAAO,IAAI,OAAO,gBAAgB,MAAM,CAAC;AAEzE,IAAI,yBAAyB,IAAI,OAAO,GAAG,OAAO,gBAAgB,QAAQ,GAAG,CAAC;;;ACP9E,IAAI;AAAA;AAAA,EAAwC,SAAU,QAAQ;AAC1D,cAAUC,yBAAwB,MAAM;AACxC,aAASA,0BAAyB;AAC9B,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,OAAO;AACb,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;;;ACTA,IAAI;AAAA,CACV,SAAUC,mBAAkB;AACzB,EAAAA,kBAAiB,YAAY,IAAI;AACjC,EAAAA,kBAAiB,QAAQ,IAAI;AAC7B,EAAAA,kBAAiB,UAAU,IAAI;AACnC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;;;ACLvC,IAAI;AAAA,CACV,SAAUC,YAAW;AAElB,EAAAA,WAAUA,WAAU,+BAA+B,IAAI,CAAC,IAAI;AAE5D,EAAAA,WAAUA,WAAU,gBAAgB,IAAI,CAAC,IAAI;AAE7C,EAAAA,WAAUA,WAAU,oBAAoB,IAAI,CAAC,IAAI;AAEjD,EAAAA,WAAUA,WAAU,sBAAsB,IAAI,CAAC,IAAI;AAEnD,EAAAA,WAAUA,WAAU,uBAAuB,IAAI,CAAC,IAAI;AAEpD,EAAAA,WAAUA,WAAU,uBAAuB,IAAI,CAAC,IAAI;AAEpD,EAAAA,WAAUA,WAAU,yBAAyB,IAAI,CAAC,IAAI;AAEtD,EAAAA,WAAUA,WAAU,4BAA4B,IAAI,CAAC,IAAI;AAEzD,EAAAA,WAAUA,WAAU,wBAAwB,IAAI,CAAC,IAAI;AAErD,EAAAA,WAAUA,WAAU,2BAA2B,IAAI,EAAE,IAAI;AAEzD,EAAAA,WAAUA,WAAU,kCAAkC,IAAI,EAAE,IAAI;AAEhE,EAAAA,WAAUA,WAAU,gCAAgC,IAAI,EAAE,IAAI;AAE9D,EAAAA,WAAUA,WAAU,qCAAqC,IAAI,EAAE,IAAI;AAEnE,EAAAA,WAAUA,WAAU,sCAAsC,IAAI,EAAE,IAAI;AAEpE,EAAAA,WAAUA,WAAU,iCAAiC,IAAI,EAAE,IAAI;AAE/D,EAAAA,WAAUA,WAAU,iCAAiC,IAAI,EAAE,IAAI;AAE/D,EAAAA,WAAUA,WAAU,0CAA0C,IAAI,EAAE,IAAI;AAKxE,EAAAA,WAAUA,WAAU,0CAA0C,IAAI,EAAE,IAAI;AAExE,EAAAA,WAAUA,WAAU,kCAAkC,IAAI,EAAE,IAAI;AAKhE,EAAAA,WAAUA,WAAU,oCAAoC,IAAI,EAAE,IAAI;AAIlE,EAAAA,WAAUA,WAAU,oCAAoC,IAAI,EAAE,IAAI;AAElE,EAAAA,WAAUA,WAAU,sBAAsB,IAAI,EAAE,IAAI;AAEpD,EAAAA,WAAUA,WAAU,aAAa,IAAI,EAAE,IAAI;AAE3C,EAAAA,WAAUA,WAAU,kBAAkB,IAAI,EAAE,IAAI;AAEhD,EAAAA,WAAUA,WAAU,uBAAuB,IAAI,EAAE,IAAI;AAErD,EAAAA,WAAUA,WAAU,cAAc,IAAI,EAAE,IAAI;AAChD,GAAG,cAAc,YAAY,CAAC,EAAE;;;AC9DzB,IAAI;AAAA,CACV,SAAUC,OAAM;AAIb,EAAAA,MAAKA,MAAK,SAAS,IAAI,CAAC,IAAI;AAI5B,EAAAA,MAAKA,MAAK,UAAU,IAAI,CAAC,IAAI;AAI7B,EAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAI3B,EAAAA,MAAKA,MAAK,MAAM,IAAI,CAAC,IAAI;AAIzB,EAAAA,MAAKA,MAAK,MAAM,IAAI,CAAC,IAAI;AAIzB,EAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAI3B,EAAAA,MAAKA,MAAK,QAAQ,IAAI,CAAC,IAAI;AAK3B,EAAAA,MAAKA,MAAK,OAAO,IAAI,CAAC,IAAI;AAI1B,EAAAA,MAAKA,MAAK,KAAK,IAAI,CAAC,IAAI;AAC5B,GAAG,SAAS,OAAO,CAAC,EAAE;AACf,IAAI;AAAA,CACV,SAAUC,gBAAe;AACtB,EAAAA,eAAcA,eAAc,QAAQ,IAAI,CAAC,IAAI;AAC7C,EAAAA,eAAcA,eAAc,UAAU,IAAI,CAAC,IAAI;AACnD,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AAIjC,SAAS,iBAAiB,IAAI;AACjC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,kBAAkB,IAAI;AAClC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,gBAAgB,IAAI;AAChC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,cAAc,IAAI;AAC9B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,cAAc,IAAI;AAC9B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,gBAAgB,IAAI;AAChC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,gBAAgB,IAAI;AAChC,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,eAAe,IAAI;AAC/B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,aAAa,IAAI;AAC7B,SAAO,GAAG,SAAS,KAAK;AAC5B;AACO,SAAS,iBAAiB,IAAI;AACjC,SAAO,CAAC,EAAE,MAAM,OAAO,OAAO,YAAY,GAAG,SAAS,cAAc;AACxE;AACO,SAAS,mBAAmB,IAAI;AACnC,SAAO,CAAC,EAAE,MAAM,OAAO,OAAO,YAAY,GAAG,SAAS,cAAc;AACxE;;;AC/EO,IAAI,wBAAwB;;;ACInC,IAAI,kBAAkB;AAOf,SAAS,sBAAsB,UAAU;AAC5C,MAAI,SAAS,CAAC;AACd,WAAS,QAAQ,iBAAiB,SAAU,OAAO;AAC/C,QAAI,MAAM,MAAM;AAChB,YAAQ,MAAM,CAAC,GAAG;AAAA,MAEd,KAAK;AACD,eAAO,MAAM,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;AACzD;AAAA,MAEJ,KAAK;AACD,eAAO,OAAO,QAAQ,IAAI,YAAY;AACtC;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,8DAA8D;AAAA,MAEvF,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,4CAA4C;AAAA,MAErE,KAAK;AAAA,MACL,KAAK;AACD,eAAO,QAAQ,CAAC,WAAW,WAAW,SAAS,QAAQ,QAAQ,EAAE,MAAM,CAAC;AACxE;AAAA,MAEJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,yCAAyC;AAAA,MAClE,KAAK;AACD,eAAO,MAAM,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC3C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,2DAA2D;AAAA,MAEpF,KAAK;AACD,eAAO,UAAU,QAAQ,IAAI,SAAS,QAAQ,IAAI,WAAW;AAC7D;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,GAAG;AACT,gBAAM,IAAI,WAAW,+CAA+C;AAAA,QACxE;AACA,eAAO,UAAU,CAAC,SAAS,QAAQ,UAAU,OAAO,EAAE,MAAM,CAAC;AAC7D;AAAA,MACJ,KAAK;AACD,YAAI,MAAM,GAAG;AACT,gBAAM,IAAI,WAAW,+CAA+C;AAAA,QACxE;AACA,eAAO,UAAU,CAAC,SAAS,QAAQ,UAAU,OAAO,EAAE,MAAM,CAAC;AAC7D;AAAA,MAEJ,KAAK;AACD,eAAO,SAAS;AAChB;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,4DAA4D;AAAA,MAErF,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AACD,eAAO,YAAY;AACnB,eAAO,OAAO,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC5C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,kEAAkE;AAAA,MAE3F,KAAK;AACD,eAAO,SAAS,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC9C;AAAA,MAEJ,KAAK;AACD,eAAO,SAAS,CAAC,WAAW,SAAS,EAAE,MAAM,CAAC;AAC9C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,4DAA4D;AAAA,MAErF,KAAK;AACD,eAAO,eAAe,MAAM,IAAI,UAAU;AAC1C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AACD,cAAM,IAAI,WAAW,sEAAsE;AAAA,IACnG;AACA,WAAO;AAAA,EACX,CAAC;AACD,SAAO;AACX;;;ACvHO,IAAI,oBAAoB;;;ACCxB,SAAS,8BAA8B,UAAU;AACpD,MAAI,SAAS,WAAW,GAAG;AACvB,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACrD;AAEA,MAAI,eAAe,SACd,MAAM,iBAAiB,EACvB,OAAO,SAAU,GAAG;AAAE,WAAO,EAAE,SAAS;AAAA,EAAG,CAAC;AACjD,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,iBAAiB,cAAc,KAAK,eAAe,QAAQ,MAAM;AAC9E,QAAI,cAAc,eAAe,EAAE;AACnC,QAAI,iBAAiB,YAAY,MAAM,GAAG;AAC1C,QAAI,eAAe,WAAW,GAAG;AAC7B,YAAM,IAAI,MAAM,yBAAyB;AAAA,IAC7C;AACA,QAAI,OAAO,eAAe,CAAC,GAAG,UAAU,eAAe,MAAM,CAAC;AAC9D,aAASC,MAAK,GAAG,YAAY,SAASA,MAAK,UAAU,QAAQA,OAAM;AAC/D,UAAI,SAAS,UAAUA,GAAE;AACzB,UAAI,OAAO,WAAW,GAAG;AACrB,cAAM,IAAI,MAAM,yBAAyB;AAAA,MAC7C;AAAA,IACJ;AACA,WAAO,KAAK,EAAE,MAAY,QAAiB,CAAC;AAAA,EAChD;AACA,SAAO;AACX;AACA,SAAS,cAAc,MAAM;AACzB,SAAO,KAAK,QAAQ,WAAW,EAAE;AACrC;AACA,IAAI,2BAA2B;AAC/B,IAAI,8BAA8B;AAClC,IAAI,sBAAsB;AAC1B,IAAI,8BAA8B;AAClC,SAAS,0BAA0B,KAAK;AACpC,MAAI,SAAS,CAAC;AACd,MAAI,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAC7B,WAAO,mBAAmB;AAAA,EAC9B,WACS,IAAI,IAAI,SAAS,CAAC,MAAM,KAAK;AAClC,WAAO,mBAAmB;AAAA,EAC9B;AACA,MAAI,QAAQ,6BAA6B,SAAU,GAAG,IAAI,IAAI;AAE1D,QAAI,OAAO,OAAO,UAAU;AACxB,aAAO,2BAA2B,GAAG;AACrC,aAAO,2BAA2B,GAAG;AAAA,IACzC,WAES,OAAO,KAAK;AACjB,aAAO,2BAA2B,GAAG;AAAA,IACzC,WAES,GAAG,CAAC,MAAM,KAAK;AACpB,aAAO,2BAA2B,GAAG;AAAA,IACzC,OAEK;AACD,aAAO,2BAA2B,GAAG;AACrC,aAAO,2BACH,GAAG,UAAU,OAAO,OAAO,WAAW,GAAG,SAAS;AAAA,IAC1D;AACA,WAAO;AAAA,EACX,CAAC;AACD,SAAO;AACX;AACA,SAAS,UAAU,KAAK;AACpB,UAAQ,KAAK;AAAA,IACT,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,cAAc;AAAA,MAClB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,QACb,cAAc;AAAA,MAClB;AAAA,IACJ,KAAK;AAAA,IACL,KAAK;AACD,aAAO;AAAA,QACH,aAAa;AAAA,MACjB;AAAA,EACR;AACJ;AACA,SAAS,yCAAyC,MAAM;AAEpD,MAAI;AACJ,MAAI,KAAK,CAAC,MAAM,OAAO,KAAK,CAAC,MAAM,KAAK;AACpC,aAAS;AAAA,MACL,UAAU;AAAA,IACd;AACA,WAAO,KAAK,MAAM,CAAC;AAAA,EACvB,WACS,KAAK,CAAC,MAAM,KAAK;AACtB,aAAS;AAAA,MACL,UAAU;AAAA,IACd;AACA,WAAO,KAAK,MAAM,CAAC;AAAA,EACvB;AACA,MAAI,QAAQ;AACR,QAAI,cAAc,KAAK,MAAM,GAAG,CAAC;AACjC,QAAI,gBAAgB,MAAM;AACtB,aAAO,cAAc;AACrB,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB,WACS,gBAAgB,MAAM;AAC3B,aAAO,cAAc;AACrB,aAAO,KAAK,MAAM,CAAC;AAAA,IACvB;AACA,QAAI,CAAC,4BAA4B,KAAK,IAAI,GAAG;AACzC,YAAM,IAAI,MAAM,2CAA2C;AAAA,IAC/D;AACA,WAAO,uBAAuB,KAAK;AAAA,EACvC;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,KAAK;AAC/B,MAAI,SAAS,CAAC;AACd,MAAI,WAAW,UAAU,GAAG;AAC5B,MAAI,UAAU;AACV,WAAO;AAAA,EACX;AACA,SAAO;AACX;AAIO,SAAS,oBAAoB,QAAQ;AACxC,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,WAAW,QAAQ,KAAK,SAAS,QAAQ,MAAM;AAC5D,QAAI,QAAQ,SAAS,EAAE;AACvB,YAAQ,MAAM,MAAM;AAAA,MAChB,KAAK;AAAA,MACL,KAAK;AACD,eAAO,QAAQ;AACf;AAAA,MACJ,KAAK;AACD,eAAO,QAAQ;AACf,eAAO,QAAQ;AACf;AAAA,MACJ,KAAK;AACD,eAAO,QAAQ;AACf,eAAO,WAAW,MAAM,QAAQ,CAAC;AACjC;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,wBAAwB;AAC/B;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,QAAQ;AACf,eAAO,OAAO,cAAc,MAAM,QAAQ,CAAC,CAAC;AAC5C;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,WAAW;AAClB,eAAO,iBAAiB;AACxB;AAAA,MACJ,KAAK;AAAA,MACL,KAAK;AACD,eAAO,WAAW;AAClB,eAAO,iBAAiB;AACxB;AAAA,MACJ,KAAK;AACD,iBAAS,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,aAAa,CAAC,GAAG,MAAM,QAAQ,OAAO,SAAU,KAAKC,MAAK;AAAE,iBAAQ,SAAS,SAAS,CAAC,GAAG,GAAG,GAAG,qBAAqBA,IAAG,CAAC;AAAA,QAAI,GAAG,CAAC,CAAC,CAAC;AAChM;AAAA,MACJ,KAAK;AACD,iBAAS,SAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,UAAU,cAAc,CAAC,GAAG,MAAM,QAAQ,OAAO,SAAU,KAAKA,MAAK;AAAE,iBAAQ,SAAS,SAAS,CAAC,GAAG,GAAG,GAAG,qBAAqBA,IAAG,CAAC;AAAA,QAAI,GAAG,CAAC,CAAC,CAAC;AACjM;AAAA,MACJ,KAAK;AACD,eAAO,WAAW;AAClB;AAAA,MAEJ,KAAK;AACD,eAAO,kBAAkB;AACzB,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AACD,eAAO,kBAAkB;AACzB,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AACD,eAAO,kBAAkB;AACzB,eAAO,cAAc;AACrB;AAAA,MACJ,KAAK;AACD,eAAO,kBAAkB;AACzB;AAAA,MACJ,KAAK;AACD,eAAO,QAAQ,WAAW,MAAM,QAAQ,CAAC,CAAC;AAC1C;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MACJ,KAAK;AACD,eAAO,eAAe;AACtB;AAAA,MAEJ,KAAK;AACD,YAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,gBAAM,IAAI,WAAW,0DAA0D;AAAA,QACnF;AACA,cAAM,QAAQ,CAAC,EAAE,QAAQ,qBAAqB,SAAU,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI;AAC3E,cAAI,IAAI;AACJ,mBAAO,uBAAuB,GAAG;AAAA,UACrC,WACS,MAAM,IAAI;AACf,kBAAM,IAAI,MAAM,oDAAoD;AAAA,UACxE,WACS,IAAI;AACT,kBAAM,IAAI,MAAM,kDAAkD;AAAA,UACtE;AACA,iBAAO;AAAA,QACX,CAAC;AACD;AAAA,IACR;AAEA,QAAI,4BAA4B,KAAK,MAAM,IAAI,GAAG;AAC9C,aAAO,uBAAuB,MAAM,KAAK;AACzC;AAAA,IACJ;AACA,QAAI,yBAAyB,KAAK,MAAM,IAAI,GAAG;AAI3C,UAAI,MAAM,QAAQ,SAAS,GAAG;AAC1B,cAAM,IAAI,WAAW,+DAA+D;AAAA,MACxF;AACA,YAAM,KAAK,QAAQ,0BAA0B,SAAU,GAAG,IAAI,IAAI,IAAI,IAAI,IAAI;AAE1E,YAAI,OAAO,KAAK;AACZ,iBAAO,wBAAwB,GAAG;AAAA,QACtC,WAES,MAAM,GAAG,CAAC,MAAM,KAAK;AAC1B,iBAAO,wBAAwB,GAAG;AAAA,QACtC,WAES,MAAM,IAAI;AACf,iBAAO,wBAAwB,GAAG;AAClC,iBAAO,wBAAwB,GAAG,SAAS,GAAG;AAAA,QAClD,OACK;AACD,iBAAO,wBAAwB,GAAG;AAClC,iBAAO,wBAAwB,GAAG;AAAA,QACtC;AACA,eAAO;AAAA,MACX,CAAC;AACD,UAAI,MAAM,MAAM,QAAQ,CAAC;AAEzB,UAAI,QAAQ,KAAK;AACb,iBAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,EAAE,qBAAqB,iBAAiB,CAAC;AAAA,MACrF,WACS,KAAK;AACV,iBAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,0BAA0B,GAAG,CAAC;AAAA,MAC1E;AACA;AAAA,IACJ;AAEA,QAAI,4BAA4B,KAAK,MAAM,IAAI,GAAG;AAC9C,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,0BAA0B,MAAM,IAAI,CAAC;AAC7E;AAAA,IACJ;AACA,QAAI,WAAW,UAAU,MAAM,IAAI;AACnC,QAAI,UAAU;AACV,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,QAAQ;AAAA,IACpD;AACA,QAAI,sCAAsC,yCAAyC,MAAM,IAAI;AAC7F,QAAI,qCAAqC;AACrC,eAAS,SAAS,SAAS,CAAC,GAAG,MAAM,GAAG,mCAAmC;AAAA,IAC/E;AAAA,EACJ;AACA,SAAO;AACX;;;ACzTO,IAAI,WAAW;AAAA,EAClB,OAAO;AAAA,IACH;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,MAAM;AAAA,IACF;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,UAAU;AAAA,IACN;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AAAA,EACA,SAAS;AAAA,IACL;AAAA,IACA;AAAA,IACA;AAAA,IACA;AAAA,EACJ;AACJ;;;AC13CO,SAAS,eAAe,UAAU,QAAQ;AAC7C,MAAI,eAAe;AACnB,WAAS,aAAa,GAAG,aAAa,SAAS,QAAQ,cAAc;AACjE,QAAI,cAAc,SAAS,OAAO,UAAU;AAC5C,QAAI,gBAAgB,KAAK;AACrB,UAAI,cAAc;AAClB,aAAO,aAAa,IAAI,SAAS,UAC7B,SAAS,OAAO,aAAa,CAAC,MAAM,aAAa;AACjD;AACA;AAAA,MACJ;AACA,UAAI,UAAU,KAAK,cAAc;AACjC,UAAI,eAAe,cAAc,IAAI,IAAI,KAAK,eAAe;AAC7D,UAAI,gBAAgB;AACpB,UAAI,WAAW,+BAA+B,MAAM;AACpD,UAAI,YAAY,OAAO,YAAY,KAAK;AACpC,uBAAe;AAAA,MACnB;AACA,aAAO,iBAAiB,GAAG;AACvB,wBAAgB;AAAA,MACpB;AACA,aAAO,YAAY,GAAG;AAClB,uBAAe,WAAW;AAAA,MAC9B;AAAA,IACJ,WACS,gBAAgB,KAAK;AAC1B,sBAAgB;AAAA,IACpB,OACK;AACD,sBAAgB;AAAA,IACpB;AAAA,EACJ;AACA,SAAO;AACX;AAMA,SAAS,+BAA+B,QAAQ;AAC5C,MAAI,YAAY,OAAO;AACvB,MAAI,cAAc;AAAA,EAEd,OAAO;AAAA,EAEP,OAAO,WAAW,QAAQ;AAE1B,gBAAY,OAAO,WAAW,CAAC;AAAA,EACnC;AACA,MAAI,WAAW;AACX,YAAQ,WAAW;AAAA,MACf,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX,KAAK;AACD,eAAO;AAAA,MACX;AACI,cAAM,IAAI,MAAM,mBAAmB;AAAA,IAC3C;AAAA,EACJ;AAEA,MAAI,cAAc,OAAO;AACzB,MAAI;AACJ,MAAI,gBAAgB,QAAQ;AACxB,gBAAY,OAAO,SAAS,EAAE;AAAA,EAClC;AACA,MAAI,aAAa,SAAS,aAAa,EAAE,KACrC,SAAS,eAAe,EAAE,KAC1B,SAAS,GAAG,OAAO,aAAa,MAAM,CAAC,KACvC,SAAS,KAAK;AAClB,SAAO,WAAW,CAAC;AACvB;;;AClFA,IAAI;AAOJ,IAAI,8BAA8B,IAAI,OAAO,IAAI,OAAO,sBAAsB,QAAQ,GAAG,CAAC;AAC1F,IAAI,4BAA4B,IAAI,OAAO,GAAG,OAAO,sBAAsB,QAAQ,IAAI,CAAC;AACxF,SAAS,eAAe,OAAO,KAAK;AAChC,SAAO,EAAE,OAAc,IAAS;AACpC;AAGA,IAAI,sBAAsB,CAAC,CAAC,OAAO,UAAU,cAAc,KAAK,WAAW,KAAK,CAAC;AACjF,IAAI,yBAAyB,CAAC,CAAC,OAAO;AACtC,IAAI,uBAAuB,CAAC,CAAC,OAAO;AACpC,IAAI,uBAAuB,CAAC,CAAC,OAAO,UAAU;AAC9C,IAAI,eAAe,CAAC,CAAC,OAAO,UAAU;AACtC,IAAI,aAAa,CAAC,CAAC,OAAO,UAAU;AACpC,IAAI,yBAAyB,CAAC,CAAC,OAAO;AACtC,IAAI,gBAAgB,yBACd,OAAO,gBACP,SAAU,GAAG;AACX,SAAQ,OAAO,MAAM,YACjB,SAAS,CAAC,KACV,KAAK,MAAM,CAAC,MAAM,KAClB,KAAK,IAAI,CAAC,KAAK;AACvB;AAEJ,IAAI,yBAAyB;AAC7B,IAAI;AACI,OAAK,GAAG,6CAA6C,IAAI;AAO7D,6BAA2B,KAAK,GAAG,KAAK,GAAG,OAAO,QAAQ,OAAO,SAAS,SAAS,GAAG,CAAC,OAAO;AAClG,SACO,GAAG;AACN,2BAAyB;AAC7B;AAXQ;AAYR,IAAI,aAAa;AAAA;AAAA,EAET,SAASC,YAAW,GAAG,QAAQ,UAAU;AACrC,WAAO,EAAE,WAAW,QAAQ,QAAQ;AAAA,EACxC;AAAA;AAAA;AAAA,EAEA,SAASA,YAAW,GAAG,QAAQ,UAAU;AACrC,WAAO,EAAE,MAAM,UAAU,WAAW,OAAO,MAAM,MAAM;AAAA,EAC3D;AAAA;AACR,IAAI,gBAAgB,yBACd,OAAO;AAAA;AAAA,EAEL,SAASC,iBAAgB;AACrB,QAAI,aAAa,CAAC;AAClB,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,iBAAW,EAAE,IAAI,UAAU,EAAE;AAAA,IACjC;AACA,QAAI,WAAW;AACf,QAAI,SAAS,WAAW;AACxB,QAAI,IAAI;AACR,QAAI;AACJ,WAAO,SAAS,GAAG;AACf,aAAO,WAAW,GAAG;AACrB,UAAI,OAAO;AACP,cAAM,WAAW,OAAO,4BAA4B;AACxD,kBACI,OAAO,QACD,OAAO,aAAa,IAAI,IACxB,OAAO,eAAe,QAAQ,UAAY,MAAM,OAAS,OAAO,OAAS,KAAM;AAAA,IAC7F;AACA,WAAO;AAAA,EACX;AAAA;AACR,IAAI;AAAA;AAAA,EAEJ,uBACM,OAAO;AAAA;AAAA,IAEL,SAASC,aAAY,SAAS;AAC1B,UAAI,MAAM,CAAC;AACX,eAAS,KAAK,GAAG,YAAY,SAAS,KAAK,UAAU,QAAQ,MAAM;AAC/D,YAAIC,MAAK,UAAU,EAAE,GAAG,IAAIA,IAAG,CAAC,GAAG,IAAIA,IAAG,CAAC;AAC3C,YAAI,CAAC,IAAI;AAAA,MACb;AACA,aAAO;AAAA,IACX;AAAA;AAAA;AACR,IAAI,cAAc;AAAA;AAAA,EAEV,SAASC,aAAY,GAAG,OAAO;AAC3B,WAAO,EAAE,YAAY,KAAK;AAAA,EAC9B;AAAA;AAAA;AAAA,EAEA,SAASA,aAAY,GAAG,OAAO;AAC3B,QAAI,OAAO,EAAE;AACb,QAAI,QAAQ,KAAK,SAAS,MAAM;AAC5B,aAAO;AAAA,IACX;AACA,QAAI,QAAQ,EAAE,WAAW,KAAK;AAC9B,QAAI;AACJ,WAAO,QAAQ,SACX,QAAQ,SACR,QAAQ,MAAM,SACb,SAAS,EAAE,WAAW,QAAQ,CAAC,KAAK,SACrC,SAAS,QACP,SACE,QAAQ,SAAW,OAAO,SAAS,SAAU;AAAA,EACzD;AAAA;AACR,IAAI,YAAY;AAAA;AAAA,EAER,SAASC,WAAU,GAAG;AAClB,WAAO,EAAE,UAAU;AAAA,EACvB;AAAA;AAAA;AAAA,EAEA,SAASA,WAAU,GAAG;AAClB,WAAO,EAAE,QAAQ,6BAA6B,EAAE;AAAA,EACpD;AAAA;AACR,IAAI,UAAU;AAAA;AAAA,EAEN,SAASC,SAAQ,GAAG;AAChB,WAAO,EAAE,QAAQ;AAAA,EACrB;AAAA;AAAA;AAAA,EAEA,SAASA,SAAQ,GAAG;AAChB,WAAO,EAAE,QAAQ,2BAA2B,EAAE;AAAA,EAClD;AAAA;AAER,SAAS,GAAG,GAAG,MAAM;AACjB,SAAO,IAAI,OAAO,GAAG,IAAI;AAC7B;AAEA,IAAI;AACJ,IAAI,wBAAwB;AAEpB,2BAAyB,GAAG,6CAA6C,IAAI;AACjF,2BAAyB,SAASC,wBAAuB,GAAG,OAAO;AAC/D,QAAIJ;AACJ,2BAAuB,YAAY;AACnC,QAAI,QAAQ,uBAAuB,KAAK,CAAC;AACzC,YAAQA,MAAK,MAAM,CAAC,OAAO,QAAQA,QAAO,SAASA,MAAK;AAAA,EAC5D;AACJ,OACK;AAED,2BAAyB,SAASI,wBAAuB,GAAG,OAAO;AAC/D,QAAI,QAAQ,CAAC;AACb,WAAO,MAAM;AACT,UAAI,IAAI,YAAY,GAAG,KAAK;AAC5B,UAAI,MAAM,UAAa,cAAc,CAAC,KAAK,iBAAiB,CAAC,GAAG;AAC5D;AAAA,MACJ;AACA,YAAM,KAAK,CAAC;AACZ,eAAS,KAAK,QAAU,IAAI;AAAA,IAChC;AACA,WAAO,cAAc,MAAM,QAAQ,KAAK;AAAA,EAC5C;AACJ;AAtBQ;AAuBR,IAAI;AAAA;AAAA,EAAwB,WAAY;AACpC,aAASC,QAAO,SAAS,SAAS;AAC9B,UAAI,YAAY,QAAQ;AAAE,kBAAU,CAAC;AAAA,MAAG;AACxC,WAAK,UAAU;AACf,WAAK,WAAW,EAAE,QAAQ,GAAG,MAAM,GAAG,QAAQ,EAAE;AAChD,WAAK,YAAY,CAAC,CAAC,QAAQ;AAC3B,WAAK,SAAS,QAAQ;AACtB,WAAK,sBAAsB,CAAC,CAAC,QAAQ;AACrC,WAAK,uBAAuB,CAAC,CAAC,QAAQ;AAAA,IAC1C;AACA,IAAAA,QAAO,UAAU,QAAQ,WAAY;AACjC,UAAI,KAAK,OAAO,MAAM,GAAG;AACrB,cAAM,MAAM,8BAA8B;AAAA,MAC9C;AACA,aAAO,KAAK,aAAa,GAAG,IAAI,KAAK;AAAA,IACzC;AACA,IAAAA,QAAO,UAAU,eAAe,SAAU,cAAc,eAAe,mBAAmB;AACtF,UAAI,WAAW,CAAC;AAChB,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,OAAO,KAAK,KAAK;AACrB,YAAI,SAAS,KAAe;AACxB,cAAI,SAAS,KAAK,cAAc,cAAc,iBAAiB;AAC/D,cAAI,OAAO,KAAK;AACZ,mBAAO;AAAA,UACX;AACA,mBAAS,KAAK,OAAO,GAAG;AAAA,QAC5B,WACS,SAAS,OAAiB,eAAe,GAAG;AACjD;AAAA,QACJ,WACS,SAAS,OACb,kBAAkB,YAAY,kBAAkB,kBAAkB;AACnE,cAAI,WAAW,KAAK,cAAc;AAClC,eAAK,KAAK;AACV,mBAAS,KAAK;AAAA,YACV,MAAM,KAAK;AAAA,YACX,UAAU,eAAe,UAAU,KAAK,cAAc,CAAC;AAAA,UAC3D,CAAC;AAAA,QACL,WACS,SAAS,MACd,CAAC,KAAK,aACN,KAAK,KAAK,MAAM,IAClB;AACE,cAAI,mBAAmB;AACnB;AAAA,UACJ,OACK;AACD,mBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,UACjH;AAAA,QACJ,WACS,SAAS,MACd,CAAC,KAAK,aACN,SAAS,KAAK,KAAK,KAAK,CAAC,GAAG;AAC5B,cAAI,SAAS,KAAK,SAAS,cAAc,aAAa;AACtD,cAAI,OAAO,KAAK;AACZ,mBAAO;AAAA,UACX;AACA,mBAAS,KAAK,OAAO,GAAG;AAAA,QAC5B,OACK;AACD,cAAI,SAAS,KAAK,aAAa,cAAc,aAAa;AAC1D,cAAI,OAAO,KAAK;AACZ,mBAAO;AAAA,UACX;AACA,mBAAS,KAAK,OAAO,GAAG;AAAA,QAC5B;AAAA,MACJ;AACA,aAAO,EAAE,KAAK,UAAU,KAAK,KAAK;AAAA,IACtC;AAmBA,IAAAA,QAAO,UAAU,WAAW,SAAU,cAAc,eAAe;AAC/D,UAAI,gBAAgB,KAAK,cAAc;AACvC,WAAK,KAAK;AACV,UAAI,UAAU,KAAK,aAAa;AAChC,WAAK,UAAU;AACf,UAAI,KAAK,OAAO,IAAI,GAAG;AAEnB,eAAO;AAAA,UACH,KAAK;AAAA,YACD,MAAM,KAAK;AAAA,YACX,OAAO,IAAI,OAAO,SAAS,IAAI;AAAA,YAC/B,UAAU,eAAe,eAAe,KAAK,cAAc,CAAC;AAAA,UAChE;AAAA,UACA,KAAK;AAAA,QACT;AAAA,MACJ,WACS,KAAK,OAAO,GAAG,GAAG;AACvB,YAAI,iBAAiB,KAAK,aAAa,eAAe,GAAG,eAAe,IAAI;AAC5E,YAAI,eAAe,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,YAAI,WAAW,eAAe;AAE9B,YAAI,sBAAsB,KAAK,cAAc;AAC7C,YAAI,KAAK,OAAO,IAAI,GAAG;AACnB,cAAI,KAAK,MAAM,KAAK,CAAC,SAAS,KAAK,KAAK,CAAC,GAAG;AACxC,mBAAO,KAAK,MAAM,UAAU,aAAa,eAAe,qBAAqB,KAAK,cAAc,CAAC,CAAC;AAAA,UACtG;AACA,cAAI,8BAA8B,KAAK,cAAc;AACrD,cAAI,iBAAiB,KAAK,aAAa;AACvC,cAAI,YAAY,gBAAgB;AAC5B,mBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,6BAA6B,KAAK,cAAc,CAAC,CAAC;AAAA,UACxH;AACA,eAAK,UAAU;AACf,cAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,mBAAO,KAAK,MAAM,UAAU,aAAa,eAAe,qBAAqB,KAAK,cAAc,CAAC,CAAC;AAAA,UACtG;AACA,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,KAAK;AAAA,cACX,OAAO;AAAA,cACP;AAAA,cACA,UAAU,eAAe,eAAe,KAAK,cAAc,CAAC;AAAA,YAChE;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ,OACK;AACD,iBAAO,KAAK,MAAM,UAAU,cAAc,eAAe,eAAe,KAAK,cAAc,CAAC,CAAC;AAAA,QACjG;AAAA,MACJ,OACK;AACD,eAAO,KAAK,MAAM,UAAU,aAAa,eAAe,eAAe,KAAK,cAAc,CAAC,CAAC;AAAA,MAChG;AAAA,IACJ;AAIA,IAAAA,QAAO,UAAU,eAAe,WAAY;AACxC,UAAI,cAAc,KAAK,OAAO;AAC9B,WAAK,KAAK;AACV,aAAO,CAAC,KAAK,MAAM,KAAK,4BAA4B,KAAK,KAAK,CAAC,GAAG;AAC9D,aAAK,KAAK;AAAA,MACd;AACA,aAAO,KAAK,QAAQ,MAAM,aAAa,KAAK,OAAO,CAAC;AAAA,IACxD;AACA,IAAAA,QAAO,UAAU,eAAe,SAAU,cAAc,eAAe;AACnE,UAAI,QAAQ,KAAK,cAAc;AAC/B,UAAI,QAAQ;AACZ,aAAO,MAAM;AACT,YAAI,mBAAmB,KAAK,cAAc,aAAa;AACvD,YAAI,kBAAkB;AAClB,mBAAS;AACT;AAAA,QACJ;AACA,YAAI,sBAAsB,KAAK,iBAAiB,cAAc,aAAa;AAC3E,YAAI,qBAAqB;AACrB,mBAAS;AACT;AAAA,QACJ;AACA,YAAI,uBAAuB,KAAK,yBAAyB;AACzD,YAAI,sBAAsB;AACtB,mBAAS;AACT;AAAA,QACJ;AACA;AAAA,MACJ;AACA,UAAI,WAAW,eAAe,OAAO,KAAK,cAAc,CAAC;AACzD,aAAO;AAAA,QACH,KAAK,EAAE,MAAM,KAAK,SAAS,OAAc,SAAmB;AAAA,QAC5D,KAAK;AAAA,MACT;AAAA,IACJ;AACA,IAAAA,QAAO,UAAU,2BAA2B,WAAY;AACpD,UAAI,CAAC,KAAK,MAAM,KACZ,KAAK,KAAK,MAAM,OACf,KAAK;AAAA,MAEF,CAAC,gBAAgB,KAAK,KAAK,KAAK,CAAC,IAAI;AACzC,aAAK,KAAK;AACV,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAMA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,eAAe;AACtD,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,IAAc;AAC9C,eAAO;AAAA,MACX;AAGA,cAAQ,KAAK,KAAK,GAAG;AAAA,QACjB,KAAK;AAED,eAAK,KAAK;AACV,eAAK,KAAK;AACV,iBAAO;AAAA,QAEX,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACD;AAAA,QACJ,KAAK;AACD,cAAI,kBAAkB,YAAY,kBAAkB,iBAAiB;AACjE;AAAA,UACJ;AACA,iBAAO;AAAA,QACX;AACI,iBAAO;AAAA,MACf;AACA,WAAK,KAAK;AACV,UAAI,aAAa,CAAC,KAAK,KAAK,CAAC;AAC7B,WAAK,KAAK;AAEV,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,KAAK,KAAK;AACnB,YAAI,OAAO,IAAc;AACrB,cAAI,KAAK,KAAK,MAAM,IAAc;AAC9B,uBAAW,KAAK,EAAE;AAElB,iBAAK,KAAK;AAAA,UACd,OACK;AAED,iBAAK,KAAK;AACV;AAAA,UACJ;AAAA,QACJ,OACK;AACD,qBAAW,KAAK,EAAE;AAAA,QACtB;AACA,aAAK,KAAK;AAAA,MACd;AACA,aAAO,cAAc,MAAM,QAAQ,UAAU;AAAA,IACjD;AACA,IAAAA,QAAO,UAAU,mBAAmB,SAAU,cAAc,eAAe;AACvE,UAAI,KAAK,MAAM,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,KAAK,KAAK,KAAK;AACnB,UAAI,OAAO,MACP,OAAO,OACN,OAAO,OACH,kBAAkB,YAAY,kBAAkB,oBACpD,OAAO,OAAiB,eAAe,GAAI;AAC5C,eAAO;AAAA,MACX,OACK;AACD,aAAK,KAAK;AACV,eAAO,cAAc,EAAE;AAAA,MAC3B;AAAA,IACJ;AACA,IAAAA,QAAO,UAAU,gBAAgB,SAAU,cAAc,mBAAmB;AACxE,UAAI,uBAAuB,KAAK,cAAc;AAC9C,WAAK,KAAK;AACV,WAAK,UAAU;AACf,UAAI,KAAK,MAAM,GAAG;AACd,eAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MACzH;AACA,UAAI,KAAK,KAAK,MAAM,KAAe;AAC/B,aAAK,KAAK;AACV,eAAO,KAAK,MAAM,UAAU,gBAAgB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MAC1G;AAEA,UAAI,QAAQ,KAAK,0BAA0B,EAAE;AAC7C,UAAI,CAAC,OAAO;AACR,eAAO,KAAK,MAAM,UAAU,oBAAoB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MAC9G;AACA,WAAK,UAAU;AACf,UAAI,KAAK,MAAM,GAAG;AACd,eAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MACzH;AACA,cAAQ,KAAK,KAAK,GAAG;AAAA,QAEjB,KAAK,KAAe;AAChB,eAAK,KAAK;AACV,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,KAAK;AAAA;AAAA,cAEX;AAAA,cACA,UAAU,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAAA,YACvE;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ;AAAA,QAEA,KAAK,IAAc;AACf,eAAK,KAAK;AACV,eAAK,UAAU;AACf,cAAI,KAAK,MAAM,GAAG;AACd,mBAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,UACzH;AACA,iBAAO,KAAK,qBAAqB,cAAc,mBAAmB,OAAO,oBAAoB;AAAA,QACjG;AAAA,QACA;AACI,iBAAO,KAAK,MAAM,UAAU,oBAAoB,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MAClH;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,4BAA4B,WAAY;AACrD,UAAI,mBAAmB,KAAK,cAAc;AAC1C,UAAI,cAAc,KAAK,OAAO;AAC9B,UAAI,QAAQ,uBAAuB,KAAK,SAAS,WAAW;AAC5D,UAAI,YAAY,cAAc,MAAM;AACpC,WAAK,OAAO,SAAS;AACrB,UAAI,cAAc,KAAK,cAAc;AACrC,UAAI,WAAW,eAAe,kBAAkB,WAAW;AAC3D,aAAO,EAAE,OAAc,SAAmB;AAAA,IAC9C;AACA,IAAAA,QAAO,UAAU,uBAAuB,SAAU,cAAc,mBAAmB,OAAO,sBAAsB;AAC5G,UAAIL;AAIJ,UAAI,oBAAoB,KAAK,cAAc;AAC3C,UAAI,UAAU,KAAK,0BAA0B,EAAE;AAC/C,UAAI,kBAAkB,KAAK,cAAc;AACzC,cAAQ,SAAS;AAAA,QACb,KAAK;AAED,iBAAO,KAAK,MAAM,UAAU,sBAAsB,eAAe,mBAAmB,eAAe,CAAC;AAAA,QACxG,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,QAAQ;AAIT,eAAK,UAAU;AACf,cAAI,mBAAmB;AACvB,cAAI,KAAK,OAAO,GAAG,GAAG;AAClB,iBAAK,UAAU;AACf,gBAAI,qBAAqB,KAAK,cAAc;AAC5C,gBAAI,SAAS,KAAK,8BAA8B;AAChD,gBAAI,OAAO,KAAK;AACZ,qBAAO;AAAA,YACX;AACA,gBAAI,QAAQ,QAAQ,OAAO,GAAG;AAC9B,gBAAI,MAAM,WAAW,GAAG;AACpB,qBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,YACjH;AACA,gBAAI,gBAAgB,eAAe,oBAAoB,KAAK,cAAc,CAAC;AAC3E,+BAAmB,EAAE,OAAc,cAA6B;AAAA,UACpE;AACA,cAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,cAAI,eAAe,KAAK;AACpB,mBAAO;AAAA,UACX;AACA,cAAI,aAAa,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAE1E,cAAI,oBAAoB,WAAW,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,OAAO,MAAM,CAAC,GAAG;AAErI,gBAAI,WAAW,UAAU,iBAAiB,MAAM,MAAM,CAAC,CAAC;AACxD,gBAAI,YAAY,UAAU;AACtB,kBAAI,SAAS,KAAK,8BAA8B,UAAU,iBAAiB,aAAa;AACxF,kBAAI,OAAO,KAAK;AACZ,uBAAO;AAAA,cACX;AACA,qBAAO;AAAA,gBACH,KAAK,EAAE,MAAM,KAAK,QAAQ,OAAc,UAAU,YAAY,OAAO,OAAO,IAAI;AAAA,gBAChF,KAAK;AAAA,cACT;AAAA,YACJ,OACK;AACD,kBAAI,SAAS,WAAW,GAAG;AACvB,uBAAO,KAAK,MAAM,UAAU,2BAA2B,UAAU;AAAA,cACrE;AACA,kBAAI,kBAAkB;AAItB,kBAAI,KAAK,QAAQ;AACb,kCAAkB,eAAe,UAAU,KAAK,MAAM;AAAA,cAC1D;AACA,kBAAI,QAAQ;AAAA,gBACR,MAAM,cAAc;AAAA,gBACpB,SAAS;AAAA,gBACT,UAAU,iBAAiB;AAAA,gBAC3B,eAAe,KAAK,uBACd,sBAAsB,eAAe,IACrC,CAAC;AAAA,cACX;AACA,kBAAI,OAAO,YAAY,SAAS,KAAK,OAAO,KAAK;AACjD,qBAAO;AAAA,gBACH,KAAK,EAAE,MAAY,OAAc,UAAU,YAAY,MAAa;AAAA,gBACpE,KAAK;AAAA,cACT;AAAA,YACJ;AAAA,UACJ;AAEA,iBAAO;AAAA,YACH,KAAK;AAAA,cACD,MAAM,YAAY,WACZ,KAAK,SACL,YAAY,SACR,KAAK,OACL,KAAK;AAAA,cACf;AAAA,cACA,UAAU;AAAA,cACV,QAAQA,MAAK,qBAAqB,QAAQ,qBAAqB,SAAS,SAAS,iBAAiB,WAAW,QAAQA,QAAO,SAASA,MAAK;AAAA,YAC9I;AAAA,YACA,KAAK;AAAA,UACT;AAAA,QACJ;AAAA,QACA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,UAAU;AAIX,cAAI,oBAAoB,KAAK,cAAc;AAC3C,eAAK,UAAU;AACf,cAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,mBAAO,KAAK,MAAM,UAAU,gCAAgC,eAAe,mBAAmB,SAAS,CAAC,GAAG,iBAAiB,CAAC,CAAC;AAAA,UAClI;AACA,eAAK,UAAU;AASf,cAAI,wBAAwB,KAAK,0BAA0B;AAC3D,cAAI,eAAe;AACnB,cAAI,YAAY,YAAY,sBAAsB,UAAU,UAAU;AAClE,gBAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,qBAAO,KAAK,MAAM,UAAU,qCAAqC,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,YAC/H;AACA,iBAAK,UAAU;AACf,gBAAI,SAAS,KAAK,uBAAuB,UAAU,qCAAqC,UAAU,oCAAoC;AACtI,gBAAI,OAAO,KAAK;AACZ,qBAAO;AAAA,YACX;AAEA,iBAAK,UAAU;AACf,oCAAwB,KAAK,0BAA0B;AACvD,2BAAe,OAAO;AAAA,UAC1B;AACA,cAAI,gBAAgB,KAAK,8BAA8B,cAAc,SAAS,mBAAmB,qBAAqB;AACtH,cAAI,cAAc,KAAK;AACnB,mBAAO;AAAA,UACX;AACA,cAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,cAAI,eAAe,KAAK;AACpB,mBAAO;AAAA,UACX;AACA,cAAI,aAAa,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAC1E,cAAI,YAAY,UAAU;AACtB,mBAAO;AAAA,cACH,KAAK;AAAA,gBACD,MAAM,KAAK;AAAA,gBACX;AAAA,gBACA,SAAS,YAAY,cAAc,GAAG;AAAA,gBACtC,UAAU;AAAA,cACd;AAAA,cACA,KAAK;AAAA,YACT;AAAA,UACJ,OACK;AACD,mBAAO;AAAA,cACH,KAAK;AAAA,gBACD,MAAM,KAAK;AAAA,gBACX;AAAA,gBACA,SAAS,YAAY,cAAc,GAAG;AAAA,gBACtC,QAAQ;AAAA,gBACR,YAAY,YAAY,WAAW,aAAa;AAAA,gBAChD,UAAU;AAAA,cACd;AAAA,cACA,KAAK;AAAA,YACT;AAAA,UACJ;AAAA,QACJ;AAAA,QACA;AACI,iBAAO,KAAK,MAAM,UAAU,uBAAuB,eAAe,mBAAmB,eAAe,CAAC;AAAA,MAC7G;AAAA,IACJ;AACA,IAAAK,QAAO,UAAU,wBAAwB,SAAU,sBAAsB;AAGrE,UAAI,KAAK,MAAM,KAAK,KAAK,KAAK,MAAM,KAAe;AAC/C,eAAO,KAAK,MAAM,UAAU,+BAA+B,eAAe,sBAAsB,KAAK,cAAc,CAAC,CAAC;AAAA,MACzH;AACA,WAAK,KAAK;AACV,aAAO,EAAE,KAAK,MAAM,KAAK,KAAK;AAAA,IAClC;AAIA,IAAAA,QAAO,UAAU,gCAAgC,WAAY;AACzD,UAAI,eAAe;AACnB,UAAI,gBAAgB,KAAK,cAAc;AACvC,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,KAAK,KAAK;AACnB,gBAAQ,IAAI;AAAA,UACR,KAAK,IAAc;AAGf,iBAAK,KAAK;AACV,gBAAI,qBAAqB,KAAK,cAAc;AAC5C,gBAAI,CAAC,KAAK,UAAU,GAAG,GAAG;AACtB,qBAAO,KAAK,MAAM,UAAU,kCAAkC,eAAe,oBAAoB,KAAK,cAAc,CAAC,CAAC;AAAA,YAC1H;AACA,iBAAK,KAAK;AACV;AAAA,UACJ;AAAA,UACA,KAAK,KAAe;AAChB,4BAAgB;AAChB,iBAAK,KAAK;AACV;AAAA,UACJ;AAAA,UACA,KAAK,KAAe;AAChB,gBAAI,eAAe,GAAG;AAClB,8BAAgB;AAAA,YACpB,OACK;AACD,qBAAO;AAAA,gBACH,KAAK,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,gBAC3D,KAAK;AAAA,cACT;AAAA,YACJ;AACA;AAAA,UACJ;AAAA,UACA;AACI,iBAAK,KAAK;AACV;AAAA,QACR;AAAA,MACJ;AACA,aAAO;AAAA,QACH,KAAK,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,QAC3D,KAAK;AAAA,MACT;AAAA,IACJ;AACA,IAAAA,QAAO,UAAU,gCAAgC,SAAU,UAAU,UAAU;AAC3E,UAAI,SAAS,CAAC;AACd,UAAI;AACA,iBAAS,8BAA8B,QAAQ;AAAA,MACnD,SACO,GAAG;AACN,eAAO,KAAK,MAAM,UAAU,yBAAyB,QAAQ;AAAA,MACjE;AACA,aAAO;AAAA,QACH,KAAK;AAAA,UACD,MAAM,cAAc;AAAA,UACpB;AAAA,UACA;AAAA,UACA,eAAe,KAAK,uBACd,oBAAoB,MAAM,IAC1B,CAAC;AAAA,QACX;AAAA,QACA,KAAK;AAAA,MACT;AAAA,IACJ;AAWA,IAAAA,QAAO,UAAU,gCAAgC,SAAU,cAAc,eAAe,gBAAgB,uBAAuB;AAC3H,UAAIL;AACJ,UAAI,iBAAiB;AACrB,UAAI,UAAU,CAAC;AACf,UAAI,kBAAkB,oBAAI,IAAI;AAC9B,UAAI,WAAW,sBAAsB,OAAO,mBAAmB,sBAAsB;AAIrF,aAAO,MAAM;AACT,YAAI,SAAS,WAAW,GAAG;AACvB,cAAI,gBAAgB,KAAK,cAAc;AACvC,cAAI,kBAAkB,YAAY,KAAK,OAAO,GAAG,GAAG;AAEhD,gBAAI,SAAS,KAAK,uBAAuB,UAAU,iCAAiC,UAAU,gCAAgC;AAC9H,gBAAI,OAAO,KAAK;AACZ,qBAAO;AAAA,YACX;AACA,+BAAmB,eAAe,eAAe,KAAK,cAAc,CAAC;AACrE,uBAAW,KAAK,QAAQ,MAAM,cAAc,QAAQ,KAAK,OAAO,CAAC;AAAA,UACrE,OACK;AACD;AAAA,UACJ;AAAA,QACJ;AAEA,YAAI,gBAAgB,IAAI,QAAQ,GAAG;AAC/B,iBAAO,KAAK,MAAM,kBAAkB,WAC9B,UAAU,qCACV,UAAU,oCAAoC,gBAAgB;AAAA,QACxE;AACA,YAAI,aAAa,SAAS;AACtB,2BAAiB;AAAA,QACrB;AAIA,aAAK,UAAU;AACf,YAAI,uBAAuB,KAAK,cAAc;AAC9C,YAAI,CAAC,KAAK,OAAO,GAAG,GAAG;AACnB,iBAAO,KAAK,MAAM,kBAAkB,WAC9B,UAAU,2CACV,UAAU,0CAA0C,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,QACxH;AACA,YAAI,iBAAiB,KAAK,aAAa,eAAe,GAAG,eAAe,cAAc;AACtF,YAAI,eAAe,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,YAAI,iBAAiB,KAAK,sBAAsB,oBAAoB;AACpE,YAAI,eAAe,KAAK;AACpB,iBAAO;AAAA,QACX;AACA,gBAAQ,KAAK;AAAA,UACT;AAAA,UACA;AAAA,YACI,OAAO,eAAe;AAAA,YACtB,UAAU,eAAe,sBAAsB,KAAK,cAAc,CAAC;AAAA,UACvE;AAAA,QACJ,CAAC;AAED,wBAAgB,IAAI,QAAQ;AAE5B,aAAK,UAAU;AACf,QAACA,MAAK,KAAK,0BAA0B,GAAG,WAAWA,IAAG,OAAO,mBAAmBA,IAAG;AAAA,MACvF;AACA,UAAI,QAAQ,WAAW,GAAG;AACtB,eAAO,KAAK,MAAM,kBAAkB,WAC9B,UAAU,kCACV,UAAU,iCAAiC,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,MAC/G;AACA,UAAI,KAAK,uBAAuB,CAAC,gBAAgB;AAC7C,eAAO,KAAK,MAAM,UAAU,sBAAsB,eAAe,KAAK,cAAc,GAAG,KAAK,cAAc,CAAC,CAAC;AAAA,MAChH;AACA,aAAO,EAAE,KAAK,SAAS,KAAK,KAAK;AAAA,IACrC;AACA,IAAAK,QAAO,UAAU,yBAAyB,SAAU,mBAAmB,oBAAoB;AACvF,UAAI,OAAO;AACX,UAAI,mBAAmB,KAAK,cAAc;AAC1C,UAAI,KAAK,OAAO,GAAG,GAAG;AAAA,MACtB,WACS,KAAK,OAAO,GAAG,GAAG;AACvB,eAAO;AAAA,MACX;AACA,UAAI,YAAY;AAChB,UAAI,UAAU;AACd,aAAO,CAAC,KAAK,MAAM,GAAG;AAClB,YAAI,KAAK,KAAK,KAAK;AACnB,YAAI,MAAM,MAAgB,MAAM,IAAc;AAC1C,sBAAY;AACZ,oBAAU,UAAU,MAAM,KAAK;AAC/B,eAAK,KAAK;AAAA,QACd,OACK;AACD;AAAA,QACJ;AAAA,MACJ;AACA,UAAI,WAAW,eAAe,kBAAkB,KAAK,cAAc,CAAC;AACpE,UAAI,CAAC,WAAW;AACZ,eAAO,KAAK,MAAM,mBAAmB,QAAQ;AAAA,MACjD;AACA,iBAAW;AACX,UAAI,CAAC,cAAc,OAAO,GAAG;AACzB,eAAO,KAAK,MAAM,oBAAoB,QAAQ;AAAA,MAClD;AACA,aAAO,EAAE,KAAK,SAAS,KAAK,KAAK;AAAA,IACrC;AACA,IAAAA,QAAO,UAAU,SAAS,WAAY;AAClC,aAAO,KAAK,SAAS;AAAA,IACzB;AACA,IAAAA,QAAO,UAAU,QAAQ,WAAY;AACjC,aAAO,KAAK,OAAO,MAAM,KAAK,QAAQ;AAAA,IAC1C;AACA,IAAAA,QAAO,UAAU,gBAAgB,WAAY;AAEzC,aAAO;AAAA,QACH,QAAQ,KAAK,SAAS;AAAA,QACtB,MAAM,KAAK,SAAS;AAAA,QACpB,QAAQ,KAAK,SAAS;AAAA,MAC1B;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,UAAI,SAAS,KAAK,SAAS;AAC3B,UAAI,UAAU,KAAK,QAAQ,QAAQ;AAC/B,cAAM,MAAM,cAAc;AAAA,MAC9B;AACA,UAAI,OAAO,YAAY,KAAK,SAAS,MAAM;AAC3C,UAAI,SAAS,QAAW;AACpB,cAAM,MAAM,UAAU,OAAO,QAAQ,0CAA0C,CAAC;AAAA,MACpF;AACA,aAAO;AAAA,IACX;AACA,IAAAA,QAAO,UAAU,QAAQ,SAAU,MAAM,UAAU;AAC/C,aAAO;AAAA,QACH,KAAK;AAAA,QACL,KAAK;AAAA,UACD;AAAA,UACA,SAAS,KAAK;AAAA,UACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,IAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,UAAI,KAAK,MAAM,GAAG;AACd;AAAA,MACJ;AACA,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,SAAS,IAAe;AACxB,aAAK,SAAS,QAAQ;AACtB,aAAK,SAAS,SAAS;AACvB,aAAK,SAAS,UAAU;AAAA,MAC5B,OACK;AACD,aAAK,SAAS,UAAU;AAExB,aAAK,SAAS,UAAU,OAAO,QAAU,IAAI;AAAA,MACjD;AAAA,IACJ;AAOA,IAAAA,QAAO,UAAU,SAAS,SAAU,QAAQ;AACxC,UAAI,WAAW,KAAK,SAAS,QAAQ,KAAK,OAAO,CAAC,GAAG;AACjD,iBAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpC,eAAK,KAAK;AAAA,QACd;AACA,eAAO;AAAA,MACX;AACA,aAAO;AAAA,IACX;AAKA,IAAAA,QAAO,UAAU,YAAY,SAAU,SAAS;AAC5C,UAAI,gBAAgB,KAAK,OAAO;AAChC,UAAI,QAAQ,KAAK,QAAQ,QAAQ,SAAS,aAAa;AACvD,UAAI,SAAS,GAAG;AACZ,aAAK,OAAO,KAAK;AACjB,eAAO;AAAA,MACX,OACK;AACD,aAAK,OAAO,KAAK,QAAQ,MAAM;AAC/B,eAAO;AAAA,MACX;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,SAAS,SAAU,cAAc;AAC9C,UAAI,KAAK,OAAO,IAAI,cAAc;AAC9B,cAAM,MAAM,gBAAgB,OAAO,cAAc,uDAAuD,EAAE,OAAO,KAAK,OAAO,CAAC,CAAC;AAAA,MACnI;AACA,qBAAe,KAAK,IAAI,cAAc,KAAK,QAAQ,MAAM;AACzD,aAAO,MAAM;AACT,YAAI,SAAS,KAAK,OAAO;AACzB,YAAI,WAAW,cAAc;AACzB;AAAA,QACJ;AACA,YAAI,SAAS,cAAc;AACvB,gBAAM,MAAM,gBAAgB,OAAO,cAAc,0CAA0C,CAAC;AAAA,QAChG;AACA,aAAK,KAAK;AACV,YAAI,KAAK,MAAM,GAAG;AACd;AAAA,QACJ;AAAA,MACJ;AAAA,IACJ;AAEA,IAAAA,QAAO,UAAU,YAAY,WAAY;AACrC,aAAO,CAAC,KAAK,MAAM,KAAK,cAAc,KAAK,KAAK,CAAC,GAAG;AAChD,aAAK,KAAK;AAAA,MACd;AAAA,IACJ;AAKA,IAAAA,QAAO,UAAU,OAAO,WAAY;AAChC,UAAI,KAAK,MAAM,GAAG;AACd,eAAO;AAAA,MACX;AACA,UAAI,OAAO,KAAK,KAAK;AACrB,UAAI,SAAS,KAAK,OAAO;AACzB,UAAI,WAAW,KAAK,QAAQ,WAAW,UAAU,QAAQ,QAAU,IAAI,EAAE;AACzE,aAAO,aAAa,QAAQ,aAAa,SAAS,WAAW;AAAA,IACjE;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;AAOF,SAAS,SAAS,WAAW;AACzB,SAAS,aAAa,MAAM,aAAa,OACpC,aAAa,MAAM,aAAa;AACzC;AACA,SAAS,gBAAgB,WAAW;AAChC,SAAO,SAAS,SAAS,KAAK,cAAc;AAChD;AAEA,SAAS,4BAA4B,GAAG;AACpC,SAAQ,MAAM,MACV,MAAM,MACL,KAAK,MAAM,KAAK,MACjB,MAAM,MACL,KAAK,MAAM,KAAK,OAChB,KAAK,MAAM,KAAK,MACjB,KAAK,OACJ,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAQ,KAAK,OAClB,KAAK,OAAS,KAAK,QACnB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAW,KAAK;AAC9B;AAKA,SAAS,cAAc,GAAG;AACtB,SAAS,KAAK,KAAU,KAAK,MACzB,MAAM,MACN,MAAM,OACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM;AACd;AAKA,SAAS,iBAAiB,GAAG;AACzB,SAAS,KAAK,MAAU,KAAK,MACzB,MAAM,MACL,KAAK,MAAU,KAAK,MACrB,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACL,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACpB,KAAK,MAAU,KAAK,MACrB,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,MACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACL,KAAK,OAAU,KAAK,OACrB,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACN,MAAM,OACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACN,MAAM,QACN,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,OACrB,MAAM,QACN,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACpB,KAAK,QAAU,KAAK,QACrB,MAAM,QACL,KAAK,QAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACL,KAAK,SAAU,KAAK,SACpB,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK,SACrB,MAAM,SACN,MAAM,SACN,MAAM,SACN,MAAM,SACL,KAAK,SAAU,KAAK;AAC7B;;;ACvvCA,SAAS,cAAc,KAAK;AACxB,MAAI,QAAQ,SAAU,IAAI;AACtB,WAAO,GAAG;AACV,QAAI,gBAAgB,EAAE,KAAK,gBAAgB,EAAE,GAAG;AAC5C,eAAS,KAAK,GAAG,SAAS;AACtB,eAAO,GAAG,QAAQ,CAAC,EAAE;AACrB,sBAAc,GAAG,QAAQ,CAAC,EAAE,KAAK;AAAA,MACrC;AAAA,IACJ,WACS,gBAAgB,EAAE,KAAK,iBAAiB,GAAG,KAAK,GAAG;AACxD,aAAO,GAAG,MAAM;AAAA,IACpB,YACU,cAAc,EAAE,KAAK,cAAc,EAAE,MAC3C,mBAAmB,GAAG,KAAK,GAAG;AAC9B,aAAO,GAAG,MAAM;AAAA,IACpB,WACS,aAAa,EAAE,GAAG;AACvB,oBAAc,GAAG,QAAQ;AAAA,IAC7B;AAAA,EACJ,CAAC;AACL;AACO,SAAS,MAAM,SAAS,MAAM;AACjC,MAAI,SAAS,QAAQ;AAAE,WAAO,CAAC;AAAA,EAAG;AAClC,SAAO,SAAS,EAAE,sBAAsB,MAAM,qBAAqB,KAAK,GAAG,IAAI;AAC/E,MAAI,SAAS,IAAI,OAAO,SAAS,IAAI,EAAE,MAAM;AAC7C,MAAI,OAAO,KAAK;AACZ,QAAI,QAAQ,YAAY,UAAU,OAAO,IAAI,IAAI,CAAC;AAElD,UAAM,WAAW,OAAO,IAAI;AAE5B,UAAM,kBAAkB,OAAO,IAAI;AACnC,UAAM;AAAA,EACV;AACA,MAAI,EAAE,SAAS,QAAQ,SAAS,SAAS,SAAS,KAAK,kBAAkB;AACrE,kBAAc,OAAO,GAAG;AAAA,EAC5B;AACA,SAAO,OAAO;AAClB;;;ACtCO,SAAS,QAAQ,IAAI,SAAS;AACjC,MAAI,QAAQ,WAAW,QAAQ,QAAQ,QAAQ,QAAQ;AACvD,MAAI,aAAa,WAAW,QAAQ,aAAa,QAAQ,aAAa;AACtE,MAAI,WAAW,WAAW,QAAQ,WAAW,QAAQ,WAAW;AAChE,SAAO,SAAS,IAAI;AAAA,IAChB;AAAA,IACA;AAAA,EACJ,CAAC;AACL;AAIA,SAAS,YAAY,OAAO;AACxB,SAAQ,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,UAAU;AAC3E;AACA,SAAS,QAAQ,IAAI,OAAO,YAAY,KAAK;AACzC,MAAI,WAAW,YAAY,GAAG,IAAI,MAAM,WAAW,GAAG;AACtD,MAAI,gBAAgB,MAAM,IAAI,QAAQ;AACtC,MAAI,OAAO,kBAAkB,aAAa;AACtC,oBAAgB,GAAG,KAAK,MAAM,GAAG;AACjC,UAAM,IAAI,UAAU,aAAa;AAAA,EACrC;AACA,SAAO;AACX;AACA,SAAS,SAAS,IAAI,OAAO,YAAY;AACrC,MAAI,OAAO,MAAM,UAAU,MAAM,KAAK,WAAW,CAAC;AAClD,MAAI,WAAW,WAAW,IAAI;AAC9B,MAAI,gBAAgB,MAAM,IAAI,QAAQ;AACtC,MAAI,OAAO,kBAAkB,aAAa;AACtC,oBAAgB,GAAG,MAAM,MAAM,IAAI;AACnC,UAAM,IAAI,UAAU,aAAa;AAAA,EACrC;AACA,SAAO;AACX;AACA,SAAS,SAAS,IAAI,SAAS,UAAU,OAAO,WAAW;AACvD,SAAO,SAAS,KAAK,SAAS,IAAI,OAAO,SAAS;AACtD;AACA,SAAS,gBAAgB,IAAI,SAAS;AAClC,MAAI,WAAW,GAAG,WAAW,IAAI,UAAU;AAC3C,SAAO,SAAS,IAAI,MAAM,UAAU,QAAQ,MAAM,OAAO,GAAG,QAAQ,UAAU;AAClF;AACA,SAAS,iBAAiB,IAAI,SAAS;AACnC,SAAO,SAAS,IAAI,MAAM,UAAU,QAAQ,MAAM,OAAO,GAAG,QAAQ,UAAU;AAClF;AACA,SAAS,gBAAgB,IAAI,SAAS;AAClC,SAAO,SAAS,IAAI,MAAM,SAAS,QAAQ,MAAM,OAAO,GAAG,QAAQ,UAAU;AACjF;AAIA,IAAI,oBAAoB,WAAY;AAChC,SAAO,KAAK,UAAU,SAAS;AACnC;AAIA,SAAS,8BAA8B;AACnC,OAAK,QAAQ,uBAAO,OAAO,IAAI;AACnC;AACA,4BAA4B,UAAU,MAAM,SAAU,KAAK;AACvD,SAAO,KAAK,MAAM,GAAG;AACzB;AACA,4BAA4B,UAAU,MAAM,SAAU,KAAK,OAAO;AAC9D,OAAK,MAAM,GAAG,IAAI;AACtB;AACA,IAAI,eAAe;AAAA,EACf,QAAQ,SAAS,SAAS;AAEtB,WAAO,IAAI,4BAA4B;AAAA,EAC3C;AACJ;AACO,IAAI,aAAa;AAAA,EACpB,UAAU;AAAA,EACV,SAAS;AACb;;;AC5EO,IAAI;AAAA,CACV,SAAUC,YAAW;AAElB,EAAAA,WAAU,eAAe,IAAI;AAE7B,EAAAA,WAAU,eAAe,IAAI;AAE7B,EAAAA,WAAU,kBAAkB,IAAI;AACpC,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,IAAI;AAAA;AAAA,EAA6B,SAAU,QAAQ;AAC/C,cAAUC,cAAa,MAAM;AAC7B,aAASA,aAAY,KAAK,MAAM,iBAAiB;AAC7C,UAAI,QAAQ,OAAO,KAAK,MAAM,GAAG,KAAK;AACtC,YAAM,OAAO;AACb,YAAM,kBAAkB;AACxB,aAAO;AAAA,IACX;AACA,IAAAA,aAAY,UAAU,WAAW,WAAY;AACzC,aAAO,oBAAoB,OAAO,KAAK,MAAM,IAAI,EAAE,OAAO,KAAK,OAAO;AAAA,IAC1E;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;AAEP,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,YAAY,OAAO,SAAS,iBAAiB;AACpE,aAAO,OAAO,KAAK,MAAM,uBAAwB,OAAO,YAAY,MAAQ,EAAE,OAAO,OAAO,kBAAoB,EAAE,OAAO,OAAO,KAAK,OAAO,EAAE,KAAK,MAAM,GAAG,GAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,IACpN;AACA,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;AAEb,IAAI;AAAA;AAAA,EAAuC,SAAU,QAAQ;AACzD,cAAUC,wBAAuB,MAAM;AACvC,aAASA,uBAAsB,OAAO,MAAM,iBAAiB;AACzD,aAAO,OAAO,KAAK,MAAM,cAAe,OAAO,OAAO,oBAAqB,EAAE,OAAO,IAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,IAC5I;AACA,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;AAEb,IAAI;AAAA;AAAA,EAAmC,SAAU,QAAQ;AACrD,cAAUC,oBAAmB,MAAM;AACnC,aAASA,mBAAkB,YAAY,iBAAiB;AACpD,aAAO,OAAO,KAAK,MAAM,qCAAsC,OAAO,YAAY,oCAAsC,EAAE,OAAO,iBAAiB,GAAI,GAAG,UAAU,eAAe,eAAe,KAAK;AAAA,IAC1M;AACA,WAAOA;AAAA,EACX,EAAE,WAAW;AAAA;;;AC5CN,IAAI;AAAA,CACV,SAAUC,YAAW;AAClB,EAAAA,WAAUA,WAAU,SAAS,IAAI,CAAC,IAAI;AACtC,EAAAA,WAAUA,WAAU,QAAQ,IAAI,CAAC,IAAI;AACzC,GAAG,cAAc,YAAY,CAAC,EAAE;AAChC,SAAS,aAAa,OAAO;AACzB,MAAI,MAAM,SAAS,GAAG;AAClB,WAAO;AAAA,EACX;AACA,SAAO,MAAM,OAAO,SAAU,KAAK,MAAM;AACrC,QAAI,WAAW,IAAI,IAAI,SAAS,CAAC;AACjC,QAAI,CAAC,YACD,SAAS,SAAS,UAAU,WAC5B,KAAK,SAAS,UAAU,SAAS;AACjC,UAAI,KAAK,IAAI;AAAA,IACjB,OACK;AACD,eAAS,SAAS,KAAK;AAAA,IAC3B;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACO,SAAS,qBAAqB,IAAI;AACrC,SAAO,OAAO,OAAO;AACzB;AAEO,SAASC,eAAc,KAAK,SAAS,YAAY,SAAS,QAAQ,oBAEzE,iBAAiB;AAEb,MAAI,IAAI,WAAW,KAAK,iBAAiB,IAAI,CAAC,CAAC,GAAG;AAC9C,WAAO;AAAA,MACH;AAAA,QACI,MAAM,UAAU;AAAA,QAChB,OAAO,IAAI,CAAC,EAAE;AAAA,MAClB;AAAA,IACJ;AAAA,EACJ;AACA,MAAI,SAAS,CAAC;AACd,WAAS,KAAK,GAAG,QAAQ,KAAK,KAAK,MAAM,QAAQ,MAAM;AACnD,QAAI,KAAK,MAAM,EAAE;AAEjB,QAAI,iBAAiB,EAAE,GAAG;AACtB,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,GAAG;AAAA,MACd,CAAC;AACD;AAAA,IACJ;AAGA,QAAI,eAAe,EAAE,GAAG;AACpB,UAAI,OAAO,uBAAuB,UAAU;AACxC,eAAO,KAAK;AAAA,UACR,MAAM,UAAU;AAAA,UAChB,OAAO,WAAW,gBAAgB,OAAO,EAAE,OAAO,kBAAkB;AAAA,QACxE,CAAC;AAAA,MACL;AACA;AAAA,IACJ;AACA,QAAI,UAAU,GAAG;AAEjB,QAAI,EAAE,UAAU,WAAW,SAAS;AAChC,YAAM,IAAI,kBAAkB,SAAS,eAAe;AAAA,IACxD;AACA,QAAI,QAAQ,OAAO,OAAO;AAC1B,QAAI,kBAAkB,EAAE,GAAG;AACvB,UAAI,CAAC,SAAS,OAAO,UAAU,YAAY,OAAO,UAAU,UAAU;AAClE,gBACI,OAAO,UAAU,YAAY,OAAO,UAAU,WACxC,OAAO,KAAK,IACZ;AAAA,MACd;AACA,aAAO,KAAK;AAAA,QACR,MAAM,OAAO,UAAU,WAAW,UAAU,UAAU,UAAU;AAAA,QAChE;AAAA,MACJ,CAAC;AACD;AAAA,IACJ;AAIA,QAAI,cAAc,EAAE,GAAG;AACnB,UAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,KAAK,GAAG,KAAK,IACrB,mBAAmB,GAAG,KAAK,IACvB,GAAG,MAAM,gBACT;AACV,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,WACF,kBAAkB,SAAS,KAAK,EAChC,OAAO,KAAK;AAAA,MACrB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,cAAc,EAAE,GAAG;AACnB,UAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,KAAK,GAAG,KAAK,IACrB,mBAAmB,GAAG,KAAK,IACvB,GAAG,MAAM,gBACT,QAAQ,KAAK;AACvB,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,WACF,kBAAkB,SAAS,KAAK,EAChC,OAAO,KAAK;AAAA,MACrB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,gBAAgB,EAAE,GAAG;AACrB,UAAI,QAAQ,OAAO,GAAG,UAAU,WAC1B,QAAQ,OAAO,GAAG,KAAK,IACvB,iBAAiB,GAAG,KAAK,IACrB,GAAG,MAAM,gBACT;AACV,UAAI,SAAS,MAAM,OAAO;AACtB,gBACI,SACK,MAAM,SAAS;AAAA,MAC5B;AACA,aAAO,KAAK;AAAA,QACR,MAAM,UAAU;AAAA,QAChB,OAAO,WACF,gBAAgB,SAAS,KAAK,EAC9B,OAAO,KAAK;AAAA,MACrB,CAAC;AACD;AAAA,IACJ;AACA,QAAI,aAAa,EAAE,GAAG;AAClB,UAAI,WAAW,GAAG,UAAU,UAAU,GAAG;AACzC,UAAI,WAAW,OAAO,OAAO;AAC7B,UAAI,CAAC,qBAAqB,QAAQ,GAAG;AACjC,cAAM,IAAI,sBAAsB,SAAS,YAAY,eAAe;AAAA,MACxE;AACA,UAAI,QAAQA,eAAc,UAAU,SAAS,YAAY,SAAS,QAAQ,kBAAkB;AAC5F,UAAI,SAAS,SAAS,MAAM,IAAI,SAAU,GAAG;AAAE,eAAO,EAAE;AAAA,MAAO,CAAC,CAAC;AACjE,UAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AACxB,iBAAS,CAAC,MAAM;AAAA,MACpB;AACA,aAAO,KAAK,MAAM,QAAQ,OAAO,IAAI,SAAU,GAAG;AAC9C,eAAO;AAAA,UACH,MAAM,OAAO,MAAM,WAAW,UAAU,UAAU,UAAU;AAAA,UAC5D,OAAO;AAAA,QACX;AAAA,MACJ,CAAC,CAAC;AAAA,IACN;AACA,QAAI,gBAAgB,EAAE,GAAG;AACrB,UAAI,MAAM,GAAG,QAAQ,KAAK,KAAK,GAAG,QAAQ;AAC1C,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,kBAAkB,GAAG,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,GAAG,eAAe;AAAA,MACzF;AACA,aAAO,KAAK,MAAM,QAAQA,eAAc,IAAI,OAAO,SAAS,YAAY,SAAS,MAAM,CAAC;AACxF;AAAA,IACJ;AACA,QAAI,gBAAgB,EAAE,GAAG;AACrB,UAAI,MAAM,GAAG,QAAQ,IAAI,OAAO,KAAK,CAAC;AACtC,UAAI,CAAC,KAAK;AACN,YAAI,CAAC,KAAK,aAAa;AACnB,gBAAM,IAAI,YAAY,mHAAqH,UAAU,kBAAkB,eAAe;AAAA,QAC1L;AACA,YAAI,OAAO,WACN,eAAe,SAAS,EAAE,MAAM,GAAG,WAAW,CAAC,EAC/C,OAAO,SAAS,GAAG,UAAU,EAAE;AACpC,cAAM,GAAG,QAAQ,IAAI,KAAK,GAAG,QAAQ;AAAA,MACzC;AACA,UAAI,CAAC,KAAK;AACN,cAAM,IAAI,kBAAkB,GAAG,OAAO,OAAO,OAAO,KAAK,GAAG,OAAO,GAAG,eAAe;AAAA,MACzF;AACA,aAAO,KAAK,MAAM,QAAQA,eAAc,IAAI,OAAO,SAAS,YAAY,SAAS,QAAQ,SAAS,GAAG,UAAU,EAAE,CAAC;AAClH;AAAA,IACJ;AAAA,EACJ;AACA,SAAO,aAAa,MAAM;AAC9B;;;ACtKA,SAAS,YAAY,IAAI,IAAI;AACzB,MAAI,CAAC,IAAI;AACL,WAAO;AAAA,EACX;AACA,SAAO,SAAS,SAAS,SAAS,CAAC,GAAI,MAAM,CAAC,CAAE,GAAI,MAAM,CAAC,CAAE,GAAG,OAAO,KAAK,EAAE,EAAE,OAAO,SAAU,KAAK,GAAG;AACrG,QAAI,CAAC,IAAI,SAAS,SAAS,CAAC,GAAG,GAAG,CAAC,CAAC,GAAI,GAAG,CAAC,KAAK,CAAC,CAAE;AACpD,WAAO;AAAA,EACX,GAAG,CAAC,CAAC,CAAC;AACV;AACA,SAAS,aAAa,eAAe,SAAS;AAC1C,MAAI,CAAC,SAAS;AACV,WAAO;AAAA,EACX;AACA,SAAO,OAAO,KAAK,aAAa,EAAE,OAAO,SAAU,KAAK,GAAG;AACvD,QAAI,CAAC,IAAI,YAAY,cAAc,CAAC,GAAG,QAAQ,CAAC,CAAC;AACjD,WAAO;AAAA,EACX,GAAG,SAAS,CAAC,GAAG,aAAa,CAAC;AAClC;AACA,SAAS,uBAAuB,OAAO;AACnC,SAAO;AAAA,IACH,QAAQ,WAAY;AAChB,aAAO;AAAA,QACH,KAAK,SAAU,KAAK;AAChB,iBAAO,MAAM,GAAG;AAAA,QACpB;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AACvB,gBAAM,GAAG,IAAI;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AACA,SAAS,wBAAwB,OAAO;AACpC,MAAI,UAAU,QAAQ;AAAE,YAAQ;AAAA,MAC5B,QAAQ,CAAC;AAAA,MACT,UAAU,CAAC;AAAA,MACX,aAAa,CAAC;AAAA,IAClB;AAAA,EAAG;AACH,SAAO;AAAA,IACH,iBAAiB,QAAQ,WAAY;AACjC,UAAIC;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,MAAMA,MAAK,KAAK,cAAc,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IAC/F,GAAG;AAAA,MACC,OAAO,uBAAuB,MAAM,MAAM;AAAA,MAC1C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,mBAAmB,QAAQ,WAAY;AACnC,UAAIA;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,MAAMA,MAAK,KAAK,gBAAgB,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IACjG,GAAG;AAAA,MACC,OAAO,uBAAuB,MAAM,QAAQ;AAAA,MAC5C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,gBAAgB,QAAQ,WAAY;AAChC,UAAIA;AACJ,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,MAAMA,MAAK,KAAK,aAAa,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IAC9F,GAAG;AAAA,MACC,OAAO,uBAAuB,MAAM,WAAW;AAAA,MAC/C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,EACL;AACJ;AACA,IAAI;AAAA;AAAA,EAAmC,WAAY;AAC/C,aAASC,mBAAkB,SAAS,SAAS,iBAAiB,MAAM;AAChE,UAAI,QAAQ;AACZ,UAAI,YAAY,QAAQ;AAAE,kBAAUA,mBAAkB;AAAA,MAAe;AACrE,WAAK,iBAAiB;AAAA,QAClB,QAAQ,CAAC;AAAA,QACT,UAAU,CAAC;AAAA,QACX,aAAa,CAAC;AAAA,MAClB;AACA,WAAK,SAAS,SAAU,QAAQ;AAC5B,YAAI,QAAQ,MAAM,cAAc,MAAM;AAEtC,YAAI,MAAM,WAAW,GAAG;AACpB,iBAAO,MAAM,CAAC,EAAE;AAAA,QACpB;AACA,YAAI,SAAS,MAAM,OAAO,SAAU,KAAK,MAAM;AAC3C,cAAI,CAAC,IAAI,UACL,KAAK,SAAS,UAAU,WACxB,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,UAAU;AACzC,gBAAI,KAAK,KAAK,KAAK;AAAA,UACvB,OACK;AACD,gBAAI,IAAI,SAAS,CAAC,KAAK,KAAK;AAAA,UAChC;AACA,iBAAO;AAAA,QACX,GAAG,CAAC,CAAC;AACL,YAAI,OAAO,UAAU,GAAG;AACpB,iBAAO,OAAO,CAAC,KAAK;AAAA,QACxB;AACA,eAAO;AAAA,MACX;AACA,WAAK,gBAAgB,SAAU,QAAQ;AACnC,eAAOC,eAAc,MAAM,KAAK,MAAM,SAAS,MAAM,YAAY,MAAM,SAAS,QAAQ,QAAW,MAAM,OAAO;AAAA,MACpH;AACA,WAAK,kBAAkB,WAAY;AAC/B,YAAIF;AACJ,eAAQ;AAAA,UACJ,UAAUA,MAAK,MAAM,oBAAoB,QAAQA,QAAO,SAAS,SAASA,IAAG,SAAS,MAClF,KAAK,aAAa,mBAAmB,MAAM,OAAO,EAAE,CAAC;AAAA,QAC7D;AAAA,MACJ;AACA,WAAK,SAAS,WAAY;AAAE,eAAO,MAAM;AAAA,MAAK;AAE9C,WAAK,UAAU;AACf,WAAK,iBAAiBC,mBAAkB,cAAc,OAAO;AAC7D,UAAI,OAAO,YAAY,UAAU;AAC7B,aAAK,UAAU;AACf,YAAI,CAACA,mBAAkB,SAAS;AAC5B,gBAAM,IAAI,UAAU,6EAA6E;AAAA,QACrG;AACA,YAAID,MAAK,QAAQ,CAAC,GAAG,aAAaA,IAAG,YAAY,YAAY,OAAOA,KAAI,CAAC,YAAY,CAAC;AAEtF,aAAK,MAAMC,mBAAkB,QAAQ,SAAS,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,QAAQ,KAAK,eAAe,CAAC,CAAC;AAAA,MACpH,OACK;AACD,aAAK,MAAM;AAAA,MACf;AACA,UAAI,CAAC,MAAM,QAAQ,KAAK,GAAG,GAAG;AAC1B,cAAM,IAAI,UAAU,gDAAgD;AAAA,MACxE;AAGA,WAAK,UAAU,aAAaA,mBAAkB,SAAS,eAAe;AACtE,WAAK,aACA,QAAQ,KAAK,cAAe,wBAAwB,KAAK,cAAc;AAAA,IAChF;AACA,WAAO,eAAeA,oBAAmB,iBAAiB;AAAA,MACtD,KAAK,WAAY;AACb,YAAI,CAACA,mBAAkB,uBAAuB;AAC1C,UAAAA,mBAAkB,wBACd,IAAI,KAAK,aAAa,EAAE,gBAAgB,EAAE;AAAA,QAClD;AACA,eAAOA,mBAAkB;AAAA,MAC7B;AAAA,MACA,YAAY;AAAA,MACZ,cAAc;AAAA,IAClB,CAAC;AACD,IAAAA,mBAAkB,wBAAwB;AAC1C,IAAAA,mBAAkB,gBAAgB,SAAU,SAAS;AACjD,UAAI,OAAO,KAAK,WAAW,aAAa;AACpC;AAAA,MACJ;AACA,UAAI,mBAAmB,KAAK,aAAa,mBAAmB,OAAO;AACnE,UAAI,iBAAiB,SAAS,GAAG;AAC7B,eAAO,IAAI,KAAK,OAAO,iBAAiB,CAAC,CAAC;AAAA,MAC9C;AACA,aAAO,IAAI,KAAK,OAAO,OAAO,YAAY,WAAW,UAAU,QAAQ,CAAC,CAAC;AAAA,IAC7E;AACA,IAAAA,mBAAkB,UAAU;AAI5B,IAAAA,mBAAkB,UAAU;AAAA,MACxB,QAAQ;AAAA,QACJ,SAAS;AAAA,UACL,uBAAuB;AAAA,QAC3B;AAAA,QACA,UAAU;AAAA,UACN,OAAO;AAAA,QACX;AAAA,QACA,SAAS;AAAA,UACL,OAAO;AAAA,QACX;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO;AAAA,UACH,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,QACA,QAAQ;AAAA,UACJ,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,QACA,MAAM;AAAA,UACF,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,QACA,MAAM;AAAA,UACF,SAAS;AAAA,UACT,OAAO;AAAA,UACP,KAAK;AAAA,UACL,MAAM;AAAA,QACV;AAAA,MACJ;AAAA,MACA,MAAM;AAAA,QACF,OAAO;AAAA,UACH,MAAM;AAAA,UACN,QAAQ;AAAA,QACZ;AAAA,QACA,QAAQ;AAAA,UACJ,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,QACZ;AAAA,QACA,MAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,cAAc;AAAA,QAClB;AAAA,QACA,MAAM;AAAA,UACF,MAAM;AAAA,UACN,QAAQ;AAAA,UACR,QAAQ;AAAA,UACR,cAAc;AAAA,QAClB;AAAA,MACJ;AAAA,IACJ;AACA,WAAOA;AAAA,EACX,EAAE;AAAA;;;AC3OK,IAAI;AAAA,CACV,SAAUE,gBAAe;AACtB,EAAAA,eAAc,cAAc,IAAI;AAChC,EAAAA,eAAc,uBAAuB,IAAI;AACzC,EAAAA,eAAc,gBAAgB,IAAI;AAClC,EAAAA,eAAc,cAAc,IAAI;AAChC,EAAAA,eAAc,qBAAqB,IAAI;AAC3C,GAAG,kBAAkB,gBAAgB,CAAC,EAAE;AACxC,IAAI;AAAA;AAAA,EAA2B,SAAU,QAAQ;AAC7C,cAAUC,YAAW,MAAM;AAC3B,aAASA,WAAU,MAAM,SAAS,WAAW;AACzC,UAAI,QAAQ;AACZ,UAAI,MAAM,YACJ,qBAAqB,QACjB,YACA,IAAI,MAAM,OAAO,SAAS,CAAC,IAC/B;AACN,cAAQ,OAAO,KAAK,MAAM,yBAAyB,OAAO,MAAM,IAAI,EAAE,OAAO,SAAS,IAAI,EAAE,OAAO,MAAM,KAAK,OAAO,IAAI,SAAS,IAAI,EAAE,OAAO,IAAI,KAAK,IAAI,EAAE,CAAC,KAAK;AACpK,YAAM,OAAO;AAEb,UAAI,OAAO,MAAM,sBAAsB,YAAY;AAE/C,cAAM,kBAAkB,OAAOA,UAAS;AAAA,MAC5C;AACA,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,KAAK;AAAA;AAEP,IAAI;AAAA;AAAA,EAA2C,SAAU,QAAQ;AAC7D,cAAUC,4BAA2B,MAAM;AAC3C,aAASA,2BAA0B,SAAS,WAAW;AACnD,aAAO,OAAO,KAAK,MAAM,cAAc,uBAAuB,SAAS,SAAS,KAAK;AAAA,IACzF;AACA,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;AAEX,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUC,qBAAoB,MAAM;AACpC,aAASA,oBAAmB,SAAS,WAAW;AAC5C,aAAO,OAAO,KAAK,MAAM,cAAc,gBAAgB,SAAS,SAAS,KAAK;AAAA,IAClF;AACA,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;AAEX,IAAI;AAAA;AAAA,EAAkC,SAAU,QAAQ;AACpD,cAAUC,mBAAkB,MAAM;AAClC,aAASA,kBAAiB,SAAS,WAAW;AAC1C,aAAO,OAAO,KAAK,MAAM,cAAc,cAAc,SAAS,SAAS,KAAK;AAAA,IAChF;AACA,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;AAEX,IAAI;AAAA;AAAA,EAAiC,SAAU,QAAQ;AACnD,cAAUC,kBAAiB,MAAM;AACjC,aAASA,iBAAgB,SAAS,QAAQ,WAAW;AACjD,UAAI,QAAQ,OAAO,KAAK,MAAM,cAAc,cAAc,GAAG,OAAO,SAAS,YAAY,EAAE,OAAO,QAAQ,IAAI,GAAG,SAAS,KAAK;AAC/H,YAAM,SAAS;AACf,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,SAAS;AAAA;AAEX,IAAI;AAAA;AAAA,EAAoC,SAAU,QAAQ;AACtD,cAAUC,qBAAoB,MAAM;AACpC,aAASA,oBAAmB,SAAS,QAAQ,YAAY,WAAW;AAChE,UAAI,QAAQ,OAAO,KAAK,MAAM,GAAG,OAAO,SAAS,eAAe,EAAE,OAAO,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,IAAI,qBAAqB,EAAE,OAAO,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,gBAAgB,iBAAiB,EAAE,OAAO,eAAe,QAAQ,eAAe,SAAS,SAAS,WAAW,aAAa,IAAI,GAAG,QAAQ,SAAS,KAAK;AACxY,YAAM,aAAa;AACnB,YAAM,SAAS;AACf,aAAO;AAAA,IACX;AACA,WAAOA;AAAA,EACX,EAAE,eAAe;AAAA;AAEjB,IAAI;AAAA;AAAA,EAAyC,SAAU,QAAQ;AAC3D,cAAUC,0BAAyB,MAAM;AACzC,aAASA,yBAAwB,YAAY,QAAQ;AACjD,UAAI,QAAQ,OAAO,KAAK,MAAM,cAAc,qBAAqB,qBAAsB,OAAO,WAAW,IAAI,gBAAkB,EAAE,OAAO,QAAQ,WAAY,EAAE,OAAO,WAAW,iBAC1K,oBAAoB,OAAO,OAAO,WAAW,mBAAmB,WAC5D,WAAW,iBACX,WAAW,eACR,IAAI,SAAU,GAAG;AAAE,YAAIC;AAAI,gBAAQA,MAAK,EAAE,WAAW,QAAQA,QAAO,SAASA,MAAK,KAAK,UAAU,CAAC;AAAA,MAAG,CAAC,EACtG,KAAK,GAAG,GAAG,IAClB,MAAM,eAAe,CAAC,KAAK;AACjC,YAAM,aAAa;AACnB,aAAO;AAAA,IACX;AACA,WAAOD;AAAA,EACX,EAAE,SAAS;AAAA;;;ACrFJ,SAAS,YAAY,OAAO,WAAW,UAAU;AACpD,MAAI,aAAa,QAAQ;AAAE,eAAW,CAAC;AAAA,EAAG;AAC1C,SAAO,UAAU,OAAO,SAAU,UAAU,MAAM;AAC9C,QAAI,QAAQ,OAAO;AACf,eAAS,IAAI,IAAI,MAAM,IAAI;AAAA,IAC/B,WACS,QAAQ,UAAU;AACvB,eAAS,IAAI,IAAI,SAAS,IAAI;AAAA,IAClC;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,IAAI,sBAAsB,SAAU,OAAO;AAEvC,MAAI,MAAuC;AACvC,YAAQ,MAAM,KAAK;AAAA,EACvB;AACJ;AACA,IAAI,qBAAqB,SAAU,SAAS;AAExC,MAAI,MAAuC;AACvC,YAAQ,KAAK,OAAO;AAAA,EACxB;AACJ;AACO,IAAI,sBAAsB;AAAA,EAC7B,SAAS,CAAC;AAAA,EACV,UAAU,CAAC;AAAA,EACX,UAAU;AAAA,EACV,eAAe;AAAA,EACf,gBAAgB,CAAC;AAAA,EACjB,uBAAuB;AAAA,EACvB,SAAS;AAAA,EACT,QAAQ;AACZ;AACO,SAAS,kBAAkB;AAC9B,SAAO;AAAA,IACH,UAAU,CAAC;AAAA,IACX,QAAQ,CAAC;AAAA,IACT,SAAS,CAAC;AAAA,IACV,cAAc,CAAC;AAAA,IACf,aAAa,CAAC;AAAA,IACd,MAAM,CAAC;AAAA,IACP,cAAc,CAAC;AAAA,EACnB;AACJ;AACA,SAASE,wBAAuB,OAAO;AACnC,SAAO;AAAA,IACH,QAAQ,WAAY;AAChB,aAAO;AAAA,QACH,KAAK,SAAU,KAAK;AAChB,iBAAO,MAAM,GAAG;AAAA,QACpB;AAAA,QACA,KAAK,SAAU,KAAK,OAAO;AACvB,gBAAM,GAAG,IAAI;AAAA,QACjB;AAAA,MACJ;AAAA,IACJ;AAAA,EACJ;AACJ;AAKO,SAAS,iBAAiB,OAAO;AACpC,MAAI,UAAU,QAAQ;AAAE,YAAQ,gBAAgB;AAAA,EAAG;AACnD,MAAI,qBAAqB,KAAK;AAC9B,MAAI,aAAa,KAAK;AACtB,MAAI,eAAe,KAAK;AACxB,MAAI,oBAAoB,QAAQ,WAAY;AACxC,QAAIC;AACJ,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,MAAMA,MAAK,KAAK,gBAAgB,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,EACjG,GAAG;AAAA,IACC,OAAOD,wBAAuB,MAAM,QAAQ;AAAA,IAC5C,UAAU,WAAW;AAAA,EACzB,CAAC;AACD,MAAI,kBAAkB,QAAQ,WAAY;AACtC,QAAIC;AACJ,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,MAAMA,MAAK,KAAK,cAAc,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,EAC/F,GAAG;AAAA,IACC,OAAOD,wBAAuB,MAAM,MAAM;AAAA,IAC1C,UAAU,WAAW;AAAA,EACzB,CAAC;AACD,MAAI,iBAAiB,QAAQ,WAAY;AACrC,QAAIC;AACJ,QAAI,OAAO,CAAC;AACZ,aAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,WAAK,EAAE,IAAI,UAAU,EAAE;AAAA,IAC3B;AACA,WAAO,MAAMA,MAAK,KAAK,aAAa,KAAK,MAAMA,KAAI,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,EAC9F,GAAG;AAAA,IACC,OAAOD,wBAAuB,MAAM,WAAW;AAAA,IAC/C,UAAU,WAAW;AAAA,EACzB,CAAC;AACD,SAAO;AAAA,IACH;AAAA,IACA;AAAA,IACA,kBAAkB,QAAQ,SAAU,SAAS,SAAS,iBAAiB,MAAM;AACzE,aAAO,IAAI,kBAAkB,SAAS,SAAS,iBAAiB,SAAS,EAAE,YAAY;AAAA,QAC/E;AAAA,QACA;AAAA,QACA;AAAA,MACJ,EAAE,GAAI,QAAQ,CAAC,CAAE,CAAC;AAAA,IAC1B,GAAG;AAAA,MACC,OAAOA,wBAAuB,MAAM,OAAO;AAAA,MAC3C,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,uBAAuB,QAAQ,WAAY;AACvC,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,KAAK,mBAAmB,KAAK,MAAM,oBAAoB,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IACzG,GAAG;AAAA,MACC,OAAOA,wBAAuB,MAAM,YAAY;AAAA,MAChD,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD;AAAA,IACA,eAAe,QAAQ,WAAY;AAC/B,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,KAAK,WAAW,KAAK,MAAM,YAAY,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IACzF,GAAG;AAAA,MACC,OAAOA,wBAAuB,MAAM,IAAI;AAAA,MACxC,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,IACD,iBAAiB,QAAQ,WAAY;AACjC,UAAI,OAAO,CAAC;AACZ,eAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,aAAK,EAAE,IAAI,UAAU,EAAE;AAAA,MAC3B;AACA,aAAO,KAAK,aAAa,KAAK,MAAM,cAAc,cAAc,CAAC,MAAM,GAAG,MAAM,KAAK,CAAC,GAAG;AAAA,IAC7F,GAAG;AAAA,MACC,OAAOA,wBAAuB,MAAM,YAAY;AAAA,MAChD,UAAU,WAAW;AAAA,IACzB,CAAC;AAAA,EACL;AACJ;AACO,SAAS,eAAe,SAAS,MAAM,MAAM,SAAS;AACzD,MAAI,aAAa,WAAW,QAAQ,IAAI;AACxC,MAAI;AACJ,MAAI,YAAY;AACZ,aAAS,WAAW,IAAI;AAAA,EAC5B;AACA,MAAI,QAAQ;AACR,WAAO;AAAA,EACX;AACA,UAAQ,IAAI,0BAA0B,MAAM,OAAO,MAAM,iBAAiB,EAAE,OAAO,IAAI,CAAC,CAAC;AAC7F;;;AC5JA,SAAS,qBAAqB,MAAM,UAAU;AAC1C,SAAO,OAAO,KAAK,IAAI,EAAE,OAAO,SAAU,KAAK,GAAG;AAC9C,QAAI,CAAC,IAAI,SAAS,EAAE,SAAmB,GAAG,KAAK,CAAC,CAAC;AACjD,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,SAAS,iBAAiB,OAAO,OAAO;AACpC,MAAI,OAAO,OAAO,KAAK,SAAS,SAAS,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;AAC3D,SAAO,KAAK,OAAO,SAAU,KAAK,GAAG;AACjC,QAAI,CAAC,IAAI,SAAS,SAAS,CAAC,GAAI,MAAM,CAAC,KAAK,CAAC,CAAE,GAAI,MAAM,CAAC,KAAK,CAAC,CAAE;AAClE,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,SAAS,+BAA+B,IAAI,UAAU;AAClD,MAAI,CAAC,UAAU;AACX,WAAO;AAAA,EACX;AACA,MAAI,YAAY,kBAAkB;AAClC,SAAO,SAAS,SAAS,SAAS,CAAC,GAAG,SAAS,GAAG,EAAE,GAAG,EAAE,MAAM,iBAAiB,qBAAqB,UAAU,MAAM,QAAQ,GAAG,qBAAqB,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,GAAG,MAAM,iBAAiB,qBAAqB,UAAU,MAAM,QAAQ,GAAG,qBAAqB,GAAG,QAAQ,CAAC,GAAG,QAAQ,CAAC,EAAE,CAAC;AAC3S;AACO,IAAI,gBAAgB,SAAUE,KAAI,OAAO,mBAAmB,QAAQ,MAAM;AAC7E,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG,SAAS,WAAWA,IAAG,UAAU,gBAAgBA,IAAG,eAAe,iBAAiBA,IAAG,gBAAgB,wBAAwBA,IAAG,uBAAuB,UAAUA,IAAG,SAAS,WAAWA,IAAG,UAAU,0BAA0BA,IAAG;AACzQ,MAAI,sBAAsB,QAAQ;AAAE,wBAAoB,EAAE,IAAI,GAAG;AAAA,EAAG;AACpE,MAAI,QAAQ,kBAAkB,IAAI,iBAAiB,kBAAkB;AAErE,YAAU,CAAC,CAAC,OAAO,oaAAoa;AACvb,MAAI,KAAK,OAAO,KAAK;AACrB,MAAI;AAAA;AAAA;AAAA;AAAA,IAIJ,YACI,OAAO,UAAU,eAAe,KAAK,UAAU,EAAE,KACjD,SAAS,EAAE;AAAA;AAEf,MAAI,MAAM,QAAQ,OAAO,KACrB,QAAQ,WAAW,KACnB,QAAQ,CAAC,EAAE,SAAS,KAAK,SAAS;AAClC,WAAO,QAAQ,CAAC,EAAE;AAAA,EACtB;AAEA,MAAI,CAAC,UACD,WACA,OAAO,YAAY,YACnB,CAAC,yBAAyB;AAC1B,WAAO,QAAQ,QAAQ,iBAAiB,MAAM;AAAA,EAClD;AACA,WAAS,SAAS,SAAS,CAAC,GAAG,uBAAuB,GAAI,UAAU,CAAC,CAAE;AACvE,YAAU,+BAA+B,SAAS,QAAQ;AAC1D,mBAAiB,+BAA+B,gBAAgB,QAAQ;AACxE,MAAI,CAAC,SAAS;AACV,QAAI,0BAA0B,SAAS,YAAY,IAAI;AACnD,aAAO;AAAA,IACX;AACA,QAAI,CAAC,kBACA,UAAU,OAAO,YAAY,MAAM,cAAc,YAAY,GAAI;AAIlE,cAAQ,IAAI,wBAAwB,mBAAmB,MAAM,CAAC;AAAA,IAClE;AACA,QAAI,gBAAgB;AAChB,UAAI;AACA,YAAI,YAAY,MAAM,iBAAiB,gBAAgB,eAAe,gBAAgB,IAAI;AAC1F,eAAO,UAAU,OAAO,MAAM;AAAA,MAClC,SACO,GAAG;AACN,gBAAQ,IAAI,mBAAmB,0CAA2C,OAAO,IAAI,uCAAwC,GAAG,QAAQ,mBAAmB,CAAC,CAAC;AAC7J,eAAO,OAAO,mBAAmB,WAAW,iBAAiB;AAAA,MACjE;AAAA,IACJ;AACA,WAAO;AAAA,EACX;AAEA,MAAI;AACA,QAAI,YAAY,MAAM,iBAAiB,SAAS,QAAQ,SAAS,SAAS,EAAE,YAAY,MAAM,GAAI,QAAQ,CAAC,CAAE,CAAC;AAC9G,WAAO,UAAU,OAAO,MAAM;AAAA,EAClC,SACO,GAAG;AACN,YAAQ,IAAI,mBAAmB,8BAA+B,OAAO,IAAI,WAAY,EAAE,OAAO,iBAAiB,oBAAoB,MAAM,eAAe,GAAG,QAAQ,mBAAmB,CAAC,CAAC;AAAA,EAC5L;AACA,MAAI,gBAAgB;AAChB,QAAI;AACA,UAAI,YAAY,MAAM,iBAAiB,gBAAgB,eAAe,gBAAgB,IAAI;AAC1F,aAAO,UAAU,OAAO,MAAM;AAAA,IAClC,SACO,GAAG;AACN,cAAQ,IAAI,mBAAmB,8CAA+C,OAAO,IAAI,+BAAgC,GAAG,QAAQ,mBAAmB,CAAC,CAAC;AAAA,IAC7J;AAAA,EACJ;AACA,MAAI,OAAO,YAAY,UAAU;AAC7B,WAAO;AAAA,EACX;AACA,MAAI,OAAO,mBAAmB,UAAU;AACpC,WAAO;AAAA,EACX;AACA,SAAO;AACX;;;ACnGA,IAAI,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AACJ;AACO,SAAS,aAAaC,KAAI,MAAM,mBAAmB,SAAS;AAC/D,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG,SAAS,UAAUA,IAAG,SAAS,WAAWA,IAAG;AAClF,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAW,SAAS,SAAS,CAAC,GAAI,YAAY,EAAE,SAAmB,CAAE,GAAI,UAAU,eAAe,SAAS,MAAM,QAAQ,OAAO,CAAE;AACtI,MAAI,kBAAkB,YAAY,SAAS,0BAA0B,QAAQ;AAC7E,MAAI,SAAS,UACT,CAAC,gBAAgB,QACjB,CAAC,gBAAgB,UACjB,CAAC,gBAAgB,UACjB,CAAC,gBAAgB,aACjB,CAAC,gBAAgB,WAAW;AAE5B,sBAAkB,SAAS,SAAS,CAAC,GAAG,eAAe,GAAG,EAAE,MAAM,WAAW,QAAQ,UAAU,CAAC;AAAA,EACpG;AACA,SAAO,kBAAkB,QAAQ,eAAe;AACpD;AACO,SAAS,WAAW,QAAQ,mBAAmB;AAClD,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,QAAQA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AAC9D,MAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,MAAI;AACA,WAAO,aAAa,QAAQ,QAAQ,mBAAmB,OAAO,EAAE,OAAO,IAAI;AAAA,EAC/E,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,0BAA0B,OAAO,QAAQ,CAAC,CAAC;AAAA,EAClF;AACA,SAAO,OAAO,IAAI;AACtB;AACO,SAAS,WAAW,QAAQ,mBAAmB;AAClD,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,QAAQA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AAC9D,MAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,MAAI;AACA,WAAO,aAAa,QAAQ,QAAQ,mBAAmB,OAAO,EAAE,OAAO,IAAI;AAAA,EAC/E,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,0BAA0B,OAAO,QAAQ,CAAC,CAAC;AAAA,EAClF;AACA,SAAO,OAAO,IAAI;AACtB;AACO,SAAS,oBAAoB,QAAQ,mBAAmB;AAC3D,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,OAAOA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AACzE,MAAI,WAAW,OAAO,UAAU,SAAS,OAAO,QAAQ,UAAU,OAAO;AACzE,MAAI,kBAAkB,YAAY,SAAS,0BAA0B,WAAW,EAAE,SAAmB,IAAI,CAAC,CAAC;AAC3G,MAAI;AACA,WAAO,kBAAkB,QAAQ,eAAe,EAAE,YAAY,MAAM,EAAE;AAAA,EAC1E,SACO,GAAG;AACN,YAAQ,IAAI,gBAAgB,qCAAqC,OAAO,QAAQ,CAAC,CAAC;AAAA,EACtF;AACA,SAAO,OAAO,IAAI;AACtB;AACO,SAAS,kBAAkB,QAAQ,mBAAmB;AACzD,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,QAAQA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AAC9D,MAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,MAAI;AACA,WAAO,aAAa,QAAQ,QAAQ,mBAAmB,OAAO,EAAE,cAAc,IAAI;AAAA,EACtF,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,0BAA0B,OAAO,QAAQ,CAAC,CAAC;AAAA,EAClF;AACA,SAAO,CAAC;AACZ;AACO,SAAS,kBAAkB,QAAQ,mBAAmB;AACzD,MAAIA,MAAK,CAAC;AACV,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,IAAAA,IAAG,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC7B;AACA,MAAI,QAAQA,IAAG,CAAC,GAAG,KAAKA,IAAG,CAAC,GAAG,UAAU,OAAO,SAAS,CAAC,IAAI;AAC9D,MAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,MAAI;AACA,WAAO,aAAa,QAAQ,QAAQ,mBAAmB,OAAO,EAAE,cAAc,IAAI;AAAA,EACtF,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,0BAA0B,OAAO,QAAQ,CAAC,CAAC;AAAA,EAClF;AACA,SAAO,CAAC;AACZ;;;ACjHA,IAAI,uBAAuB;AAAA,EACvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,SAAS,kBAAkBC,KAAI,iBAAiB,OAAO,SAAS;AACnE,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG;AACrC,MAAI,eAAe,KAAK;AACxB,MAAI,CAAC,cAAc;AACf,YAAQ,IAAI,YAAY,qHAAuH,UAAU,gBAAgB,CAAC;AAAA,EAC9K;AACA,MAAI,kBAAkB,YAAY,SAAS,oBAAoB;AAC/D,MAAI;AACA,WAAO,gBAAgB,QAAQ,eAAe,EAAE,GAAG,KAAK;AAAA,EAC5D,SACO,GAAG;AACN,YAAQ,IAAI,gBAAgB,kCAAkC,QAAQ,CAAC,CAAC;AAAA,EAC5E;AACJ;;;AClBA,IAAI,sBAAsB;AAAA,EACtB;AAAA,EACA;AACJ;AACA,IAAI,MAAM,KAAK,IAAI;AACnB,SAAS,cAAc,GAAG;AACtB,SAAO,GAAG,OAAO,KAAK,GAAG,EAAE,OAAO,GAAG,GAAG,EAAE,OAAO,GAAG;AACxD;AACO,SAAS,WAAW,MAAM,eAAe,QAAQ,SAAS;AAC7D,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,UAAU,kBAAkB,MAAM,eAAe,QAAQ,OAAO,EAAE,OAAO,SAAU,KAAK,IAAI;AAC5F,QAAI,MAAM,GAAG;AACb,QAAI,OAAO,QAAQ,UAAU;AACzB,UAAI,KAAK,GAAG;AAAA,IAChB,WACS,OAAO,IAAI,IAAI,SAAS,CAAC,MAAM,UAAU;AAC9C,UAAI,IAAI,SAAS,CAAC,KAAK;AAAA,IAC3B,OACK;AACD,UAAI,KAAK,GAAG;AAAA,IAChB;AACA,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACL,SAAO,QAAQ,WAAW,IAAI,QAAQ,CAAC,IAAI,QAAQ,WAAW,IAAI,KAAK;AAC3E;AACO,SAAS,kBAAkBC,KAAI,eAAe,QAAQ,SAAS;AAClE,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG;AACrC,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,aAAa,KAAK;AACtB,MAAI,CAAC,YAAY;AACb,YAAQ,IAAI,YAAY,iHAAmH,UAAU,gBAAgB,CAAC;AAAA,EAC1K;AACA,MAAI,kBAAkB,YAAY,SAAS,mBAAmB;AAC9D,MAAI;AACA,QAAI,eAAe,CAAC;AACpB,QAAI,mBAAmB,OAAO,IAAI,SAAU,GAAG,GAAG;AAC9C,UAAI,OAAO,MAAM,UAAU;AACvB,YAAI,KAAK,cAAc,CAAC;AACxB,qBAAa,EAAE,IAAI;AACnB,eAAO;AAAA,MACX;AACA,aAAO,OAAO,CAAC;AAAA,IACnB,CAAC;AACD,WAAO,cAAc,QAAQ,eAAe,EACvC,cAAc,gBAAgB,EAC9B,IAAI,SAAU,MAAM;AACrB,aAAO,KAAK,SAAS,YACf,OACA,SAAS,SAAS,CAAC,GAAG,IAAI,GAAG,EAAE,OAAO,aAAa,KAAK,KAAK,KAAK,KAAK,MAAM,CAAC;AAAA,IACxF,CAAC;AAAA,EACL,SACO,GAAG;AACN,YAAQ,IAAI,gBAAgB,0BAA0B,QAAQ,CAAC,CAAC;AAAA,EACpE;AAEA,SAAO;AACX;;;ACzDA,IAAI,wBAAwB,CAAC,MAAM;AAC5B,SAAS,aAAaC,KAAI,gBAAgB,OAAO,SAAS;AAC7D,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG;AACrC,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,CAAC,KAAK,aAAa;AACnB,YAAQ,IAAI,YAAY,mHAAqH,UAAU,gBAAgB,CAAC;AAAA,EAC5K;AACA,MAAI,kBAAkB,YAAY,SAAS,qBAAqB;AAChE,MAAI;AACA,WAAO,eAAe,QAAQ,eAAe,EAAE,OAAO,KAAK;AAAA,EAC/D,SACO,GAAG;AACN,YAAQ,IAAI,gBAAgB,4BAA4B,QAAQ,CAAC,CAAC;AAAA,EACtE;AACA,SAAO;AACX;;;ACfA,IAAI,+BAA+B,CAAC,WAAW,OAAO;AACtD,SAASC,cAAaC,KAAI,uBAAuB,SAAS;AACtD,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG,SAAS,UAAUA,IAAG;AAC3D,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAY,CAAC,CAAC,UAAU,eAAe,SAAS,YAAY,QAAQ,OAAO,KAAM,CAAC;AACtF,MAAI,kBAAkB,YAAY,SAAS,8BAA8B,QAAQ;AACjF,SAAO,sBAAsB,QAAQ,eAAe;AACxD;AACO,SAAS,mBAAmB,QAAQ,uBAAuB,OAAO,MAAM,SAAS;AACpF,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,CAAC,MAAM;AACP,WAAO;AAAA,EACX;AACA,MAAI,qBAAqB,KAAK;AAC9B,MAAI,CAAC,oBAAoB;AACrB,WAAO,QAAQ,IAAI,YAAY,iIAAmI,UAAU,gBAAgB,CAAC;AAAA,EACjM;AACA,MAAI;AACA,WAAOD,cAAa,QAAQ,uBAAuB,OAAO,EAAE,OAAO,OAAO,IAAI;AAAA,EAClF,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,mCAAmC,OAAO,QAAQ,CAAC,CAAC;AAAA,EAC3F;AACA,SAAO,OAAO,KAAK;AACvB;;;AC1BA,IAAI,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA,EAEA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACJ;AACO,SAASE,cAAaC,KAAI,iBAAiB,SAAS;AACvD,MAAI,SAASA,IAAG,QAAQ,UAAUA,IAAG,SAAS,UAAUA,IAAG;AAC3D,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI,SAAS,QAAQ;AACrB,MAAI,WAAa,UACb,eAAe,SAAS,UAAU,QAAQ,OAAO,KACjD,CAAC;AACL,MAAI,kBAAkB,YAAY,SAAS,uBAAuB,QAAQ;AAC1E,SAAO,gBAAgB,QAAQ,eAAe;AAClD;AACO,SAAS,aAAa,QAAQ,iBAAiB,OAAO,SAAS;AAClE,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI;AACA,WAAOD,cAAa,QAAQ,iBAAiB,OAAO,EAAE,OAAO,KAAK;AAAA,EACtE,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,4BAA4B,OAAO,QAAQ,CAAC,CAAC;AAAA,EACpF;AACA,SAAO,OAAO,KAAK;AACvB;AACO,SAAS,oBAAoB,QAAQ,iBAAiB,OAAO,SAAS;AACzE,MAAI,YAAY,QAAQ;AAAE,cAAU,CAAC;AAAA,EAAG;AACxC,MAAI;AACA,WAAOA,cAAa,QAAQ,iBAAiB,OAAO,EAAE,cAAc,KAAK;AAAA,EAC7E,SACO,GAAG;AACN,WAAO,QAAQ,IAAI,gBAAgB,4BAA4B,OAAO,QAAQ,CAAC,CAAC;AAAA,EACpF;AACA,SAAO,CAAC;AACZ;;;AC/CA,SAAS,sBAAsB,UAAU;AACrC,MAAI,eAAe,WAAW,SAAS,OAAO,KAAK,QAAQ,EAAE,CAAC,CAAC,IAAI;AACnE,SAAO,OAAO,iBAAiB;AACnC;AACA,SAAS,qBAAqB,QAAQ;AAClC,MAAI,OAAO,UACP,OAAO,2BACP,sBAAsB,OAAO,YAAY,CAAC,CAAC,GAAG;AAC9C,WAAO,OAAO,wQAA8Q;AAAA,EAChS;AACJ;AAMO,SAAS,WAAW,QAAQ,OAAO;AACtC,MAAI,aAAa,iBAAiB,KAAK;AACvC,MAAI,iBAAiB,SAAS,SAAS,CAAC,GAAG,mBAAmB,GAAG,MAAM;AACvE,MAAI,SAAS,eAAe,QAAQ,gBAAgB,eAAe,eAAe,UAAU,eAAe;AAC3G,MAAI,CAAC,QAAQ;AACT,QAAI,SAAS;AACT,cAAQ,IAAI,mBAAmB,uCAA0C,OAAO,eAAe,uFAAwF,CAAC,CAAC;AAAA,IAC7L;AAMA,mBAAe,SAAS,eAAe,iBAAiB;AAAA,EAC5D,WACS,CAAC,KAAK,aAAa,mBAAmB,MAAM,EAAE,UAAU,SAAS;AACtE,YAAQ,IAAI,iBAAiB,oCAAqC,OAAO,QAAQ,iDAAmD,EAAE,OAAO,eAAe,8FAA+F,CAAC,CAAC;AAAA,EACjQ,WACS,CAAC,KAAK,eAAe,mBAAmB,MAAM,EAAE,UACrD,SAAS;AACT,YAAQ,IAAI,iBAAiB,oCAAqC,OAAO,QAAQ,mDAAqD,EAAE,OAAO,eAAe,8FAA+F,CAAC,CAAC;AAAA,EACnQ;AACA,uBAAqB,cAAc;AACnC,SAAO,SAAS,SAAS,CAAC,GAAG,cAAc,GAAG;AAAA,IAAE;AAAA,IAAwB,cAAc,aAAa,KAAK,MAAM,gBAAgB,WAAW,eAAe;AAAA,IAAG,qBAAqB,oBAAoB,KAAK,MAAM,gBAAgB,WAAW,eAAe;AAAA,IAAG,oBAAoB,mBAAmB,KAAK,MAAM,gBAAgB,WAAW,qBAAqB;AAAA,IAAG,YAAY,WAAW,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,mBAAmB,kBAAkB,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,YAAY,WAAW,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,qBAAqB,oBAAoB,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,mBAAmB,kBAAkB,KAAK,MAAM,gBAAgB,WAAW,iBAAiB;AAAA,IAAG,cAAc,aAAa,KAAK,MAAM,gBAAgB,WAAW,cAAc;AAAA;AAAA,IAE/2B,eAAe,cAAc,KAAK,MAAM,gBAAgB,UAAU;AAAA;AAAA,IAElE,IAAI,cAAc,KAAK,MAAM,gBAAgB,UAAU;AAAA,IAAG,YAAY,WAAW,KAAK,MAAM,gBAAgB,WAAW,aAAa;AAAA,IAAG,mBAAmB,kBAAkB,KAAK,MAAM,gBAAgB,WAAW,aAAa;AAAA,IAAG,mBAAmB,kBAAkB,KAAK,MAAM,gBAAgB,WAAW,eAAe;AAAA,EAAE,CAAC;AACvU;;;A/BlDO,SAAS,qBAAqB,MAAM;AACvC,YAAU,MAAM,8GAC8C;AAClE;AACO,IAAIE,uBAAsB,SAAS,SAAS,CAAC,GAAG,mBAAwB,GAAG,EAAE,eAAqB,eAAS,CAAC;AAO5G,SAAS,wBAAwB,oBAAoB;AACxD,SAAO,SAAU,OAAO;AAEpB,WAAO,mBAAyB,eAAS,QAAQ,KAAK,CAAC;AAAA,EAC3D;AACJ;AACO,SAAS,aAAa,MAAM,MAAM;AACrC,MAAI,SAAS,MAAM;AACf,WAAO;AAAA,EACX;AACA,MAAI,CAAC,QAAQ,CAAC,MAAM;AAChB,WAAO;AAAA,EACX;AACA,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,QAAQ,OAAO,KAAK,IAAI;AAC5B,MAAI,MAAM,MAAM;AAChB,MAAI,MAAM,WAAW,KAAK;AACtB,WAAO;AAAA,EACX;AACA,WAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC1B,QAAI,MAAM,MAAM,CAAC;AACjB,QAAI,KAAK,GAAG,MAAM,KAAK,GAAG,KACtB,CAAC,OAAO,UAAU,eAAe,KAAK,MAAM,GAAG,GAAG;AAClD,aAAO;AAAA,IACX;AAAA,EACJ;AACA,SAAO;AACX;;;ADjCA,IAAI,cAAc,OAAO,WAAW,eAAe,CAAC,OAAO,uCACrD,OAAO,2BACJ,OAAO,yBAA+B,qBAAc,IAAI,KACrD,qBAAc,IAAI;AAC9B,IAAI,eAAe,YAAY;AAA/B,IAAyC,eAAe,YAAY;AAC7D,IAAI,WAAW;AACf,IAAI,UAAU;;;ADZN,SAAR,UAA2B;AAC9B,MAAI,OAAa,kBAAW,OAAO;AACnC,uBAAqB,IAAI;AACzB,SAAO;AACX;;;ADJA,IAAI;AAAA,CACH,SAAUC,cAAa;AACpB,EAAAA,aAAY,YAAY,IAAI;AAC5B,EAAAA,aAAY,YAAY,IAAI;AAC5B,EAAAA,aAAY,cAAc,IAAI;AAC9B,EAAAA,aAAY,YAAY,IAAI;AAG5B,EAAAA,aAAY,mBAAmB,IAAI;AACvC,GAAG,gBAAgB,cAAc,CAAC,EAAE;AACpC,IAAI;AAAA,CACH,SAAUC,mBAAkB;AACzB,EAAAA,kBAAiB,YAAY,IAAI;AACjC,EAAAA,kBAAiB,YAAY,IAAI;AACjC,EAAAA,kBAAiB,cAAc,IAAI;AACnC,EAAAA,kBAAiB,YAAY,IAAI;AACrC,GAAG,qBAAqB,mBAAmB,CAAC,EAAE;AACvC,IAAI,uBAAuB,SAAU,OAAO;AAC/C,MAAI,OAAO,QAAQ;AACnB,MAAI,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,cAAc,OAAO,OAAO,CAAC,SAAS,UAAU,CAAC;AACrG,SAAO,SAAS,KAAK,oBAAoB,OAAO,WAAW,CAAC;AAChE;AACA,qBAAqB,cAAc;AAMnC,qBAAqB,cAAc;AAC5B,SAAS,sCAAsC,MAAM;AACxD,MAAI,iBAAiB,SAAU,OAAO;AAClC,QAAI,OAAO,QAAQ;AACnB,QAAI,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,cAAc,OAAO,OAAO,CAAC,SAAS,UAAU,CAAC;AACrG,QAAI,OAAO,OAAO,UAAU,WAAW,IAAI,KAAK,SAAS,CAAC,IAAI;AAC9D,QAAI,iBAAiB,SAAS,eACxB,KAAK,kBAAkB,MAAM,WAAW,IACxC,KAAK,kBAAkB,MAAM,WAAW;AAC9C,WAAO,SAAS,cAAc;AAAA,EAClC;AACA,iBAAe,cAAc,iBAAiB,IAAI;AAClD,SAAO;AACX;AACO,SAAS,yBAAyB,MAAM;AAC3C,MAAI,YAAY,SAAU,OAAO;AAC7B,QAAI,OAAO,QAAQ;AACnB,QAAI,QAAQ,MAAM,OAAO,WAAW,MAAM,UAAU,cAAc;AAAA,MAAO;AAAA,MAEvE,CAAC,SAAS,UAAU;AAAA,IAAC;AAEvB,QAAI,iBAAiB,KAAK,IAAI,EAAE,OAAO,WAAW;AAClD,QAAI,OAAO,aAAa,YAAY;AAChC,aAAO,SAAS,cAAc;AAAA,IAClC;AACA,QAAI,OAAO,KAAK,iBAAuB;AACvC,WAAa,qBAAc,MAAM,MAAM,cAAc;AAAA,EACzD;AACA,YAAU,cAAc,YAAY,IAAI;AACxC,SAAO;AACX;;;AmCtDA,IAAAC,SAAuB;;;ACAvB,IAAAC,SAAuB;AAGvB,SAAS,6CAA6C,QAAQ;AAC1D,MAAI,CAAC,QAAQ;AACT,WAAO;AAAA,EACX;AACA,SAAO,OAAO,KAAK,MAAM,EAAE,OAAO,SAAU,KAAK,GAAG;AAChD,QAAI,IAAI,OAAO,CAAC;AAChB,QAAI,CAAC,IAAI,qBAAqB,CAAC,IACzB,wBAAwB,CAAC,IACzB;AACN,WAAO;AAAA,EACX,GAAG,CAAC,CAAC;AACT;AACA,IAAIC,iBAAgB,SAAU,QAAQ,YAAY,YAAY,WAAW;AACrE,MAAI,OAAO,CAAC;AACZ,WAAS,KAAK,GAAG,KAAK,UAAU,QAAQ,MAAM;AAC1C,SAAK,KAAK,CAAC,IAAI,UAAU,EAAE;AAAA,EAC/B;AACA,MAAI,SAAS,6CAA6C,SAAS;AACnE,MAAI,SAAS,cAAkB,MAAM,QAAQ,cAAc;AAAA,IAAC;AAAA,IACxD;AAAA,IACA;AAAA,IACA;AAAA,EAAM,GAAG,MAAM,KAAK,CAAC;AACzB,MAAI,MAAM,QAAQ,MAAM,GAAG;AACvB,WAAa,gBAAS,QAAQ,MAAM;AAAA,EACxC;AACA,SAAO;AACX;AAMO,IAAIC,cAAa,SAAUC,KAAI,OAAO;AACzC,MAAI,6BAA6BA,IAAG,yBAAyB,SAAS,OAAOA,KAAI,CAAC,yBAAyB,CAAC;AAC5G,MAAI,0BAA0B,6CAA6C,0BAA0B;AACrG,MAAI,WAAW,WAAe,SAAS,SAAS,SAAS,CAAC,GAAGC,oBAAmB,GAAG,MAAM,GAAG,EAAE,wBAAiD,CAAC,GAAG,KAAK;AACxJ,MAAI,iBAAiB;AAAA,IACjB,QAAQ,SAAS;AAAA,IACjB,UAAU,SAAS;AAAA,IACnB,uBAAuB,SAAS;AAAA,IAChC,SAAS,SAAS;AAAA,IAClB,eAAe,SAAS;AAAA,IACxB,gBAAgB,SAAS;AAAA,IACzB,UAAU,SAAS;AAAA,IACnB,SAAS,SAAS;AAAA,IAClB;AAAA,EACJ;AACA,SAAO,SAAS,SAAS,CAAC,GAAG,QAAQ,GAAG;AAAA,IAAE,eAAeH,eAAc;AAAA,MAAK;AAAA,MAAM;AAAA;AAAA,MAE9E,SAAS;AAAA,IAAU;AAAA;AAAA,IAEnB,IAAIA,eAAc,KAAK,MAAM,gBAAgB,SAAS,UAAU;AAAA,EAAE,CAAC;AAC3E;;;ADnDA,SAAS,kBAAkB,QAAQ;AAC/B,SAAO;AAAA,IACH,QAAQ,OAAO;AAAA,IACf,UAAU,OAAO;AAAA,IACjB,uBAAuB,OAAO;AAAA,IAC9B,SAAS,OAAO;AAAA,IAChB,eAAe,OAAO;AAAA,IACtB,UAAU,OAAO;AAAA,IACjB,eAAe,OAAO;AAAA,IACtB,gBAAgB,OAAO;AAAA,IACvB,SAAS,OAAO;AAAA,IAChB,QAAQ,OAAO;AAAA,IACf,8BAA8B,OAAO;AAAA,IACrC,yBAAyB,OAAO;AAAA,EACpC;AACJ;AACA,IAAII;AAAA;AAAA,EAA8B,SAAU,QAAQ;AAChD,cAAUA,eAAc,MAAM;AAC9B,aAASA,gBAAe;AACpB,UAAI,QAAQ,WAAW,QAAQ,OAAO,MAAM,MAAM,SAAS,KAAK;AAChE,YAAM,QAAQ,gBAAgB;AAC9B,YAAM,QAAQ;AAAA,QACV,OAAO,MAAM;AAAA,QACb,MAAMC,YAAW,kBAAkB,MAAM,KAAK,GAAG,MAAM,KAAK;AAAA,QAC5D,YAAY,kBAAkB,MAAM,KAAK;AAAA,MAC7C;AACA,aAAO;AAAA,IACX;AACA,IAAAD,cAAa,2BAA2B,SAAU,OAAOE,KAAI;AACzD,UAAI,aAAaA,IAAG,YAAY,QAAQA,IAAG;AAC3C,UAAI,SAAS,kBAAkB,KAAK;AACpC,UAAI,CAAC,aAAa,YAAY,MAAM,GAAG;AACnC,eAAO;AAAA,UACH,MAAMD,YAAW,QAAQ,KAAK;AAAA,UAC9B,YAAY;AAAA,QAChB;AAAA,MACJ;AACA,aAAO;AAAA,IACX;AACA,IAAAD,cAAa,UAAU,SAAS,WAAY;AACxC,2BAAqB,KAAK,MAAM,IAAI;AACpC,aAAa,qBAAc,UAAU,EAAE,OAAO,KAAK,MAAM,KAAK,GAAG,KAAK,MAAM,QAAQ;AAAA,IACxF;AACA,IAAAA,cAAa,cAAc;AAC3B,IAAAA,cAAa,eAAeG;AAC5B,WAAOH;AAAA,EACX,EAAQ,oBAAa;AAAA;;;AEnDrB,IAAAI,SAAuB;AAGvB,IAAI,SAAS;AACb,IAAI,OAAO,KAAK;AAChB,IAAI,MAAM,KAAK,KAAK;AACpB,SAAS,WAAW,SAAS;AACzB,MAAI,WAAW,KAAK,IAAI,OAAO;AAC/B,MAAI,WAAW,QAAQ;AACnB,WAAO;AAAA,EACX;AACA,MAAI,WAAW,MAAM;AACjB,WAAO;AAAA,EACX;AACA,MAAI,WAAW,KAAK;AAChB,WAAO;AAAA,EACX;AACA,SAAO;AACX;AACA,SAAS,qBAAqB,MAAM;AAChC,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO;AAAA,IACX;AACI,aAAO;AAAA,EACf;AACJ;AACA,SAAS,eAAe,OAAO,MAAM;AACjC,MAAI,CAAC,OAAO;AACR,WAAO;AAAA,EACX;AACA,UAAQ,MAAM;AAAA,IACV,KAAK;AACD,aAAO;AAAA,IACX,KAAK;AACD,aAAO,QAAQ;AAAA,IACnB;AACI,aAAO,QAAQ;AAAA,EACvB;AACJ;AACA,IAAI,sBAAsB;AAAA,EACtB;AAAA,EACA;AAAA,EACA;AACJ;AACA,SAAS,aAAa,MAAM;AACxB,MAAI,SAAS,QAAQ;AAAE,WAAO;AAAA,EAAU;AACxC,SAAO,oBAAoB,QAAQ,IAAI,IAAI;AAC/C;AACA,IAAI,8BAA8B,SAAU,OAAO;AAC/C,MAAIC,MAAK,QAAQ,GAAGC,sBAAqBD,IAAG,oBAAoB,OAAOA,IAAG;AAC1E,MAAI,WAAW,MAAM,UAAU,QAAQ,MAAM,OAAO,OAAO,MAAM,MAAM,aAAa,OAAO,OAAO,CAAC,YAAY,SAAS,MAAM,CAAC;AAC/H,MAAI,wBAAwBC,oBAAmB,SAAS,GAAG,MAAM,UAAU;AAC3E,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,qBAAqB;AAAA,EACzC;AACA,MAAI,MAAM;AACN,WAAa,qBAAc,MAAM,MAAM,qBAAqB;AAAA,EAChE;AACA,SAAa,qBAAoB,iBAAU,MAAM,qBAAqB;AAC1E;AACA,IAAI,wBAAwB,SAAUD,KAAI;AACtC,MAAI,KAAKA,IAAG,OAAO,QAAQ,OAAO,SAAS,IAAI,IAAI,KAAKA,IAAG,MAAM,OAAO,OAAO,SAAS,WAAW,IAAI,0BAA0BA,IAAG,yBAAyB,aAAa,OAAOA,KAAI,CAAC,SAAS,QAAQ,yBAAyB,CAAC;AACjO,YAAU,CAAC,2BACP,CAAC,EAAE,2BAA2B,aAAa,IAAI,IAAI,mDAAmD;AAC1G,MAAI,KAAW,gBAAS,GAAG,WAAW,GAAG,CAAC,GAAG,cAAc,GAAG,CAAC;AAC/D,MAAI,KAAW,gBAAS,CAAC,GAAG,YAAY,GAAG,CAAC,GAAG,eAAe,GAAG,CAAC;AAClE,MAAI,KAAW,gBAAS,CAAC,GAAG,wBAAwB,GAAG,CAAC,GAAG,2BAA2B,GAAG,CAAC;AAC1F,MAAI;AACJ,MAAI,SAAS,YAAY,UAAU,WAAW;AAC1C,iBAAa,SAAS,CAAC;AACvB,gBAAY,IAAI;AAChB,6BAAyB,aAAa,IAAI,IAAI,eAAe,OAAO,IAAI,IAAI,CAAC;AAAA,EACjF;AACA,EAAM,iBAAU,WAAY;AACxB,aAAS,mBAAmB;AACxB,mBAAa,WAAW;AAAA,IAC5B;AACA,qBAAiB;AAEjB,QAAI,CAAC,2BAA2B,CAAC,aAAa,IAAI,GAAG;AACjD,aAAO;AAAA,IACX;AAEA,QAAI,qBAAqB,wBAAwB;AACjD,QAAI,WAAW,WAAW,kBAAkB;AAE5C,QAAI,aAAa,OAAO;AACpB,aAAO;AAAA,IACX;AACA,QAAIE,gBAAe,qBAAqB,QAAQ;AAChD,QAAI,YAAY,qBAAqBA;AACrC,QAAI,gCAAgC,qBAAqB;AACzD,QAAI,gCAAgC,iCAAiC,wBAC/D,gCAAgCA,gBAChC;AACN,QAAI,iBAAiB,KAAK,IAAI,gCAAgC,qBAAqB;AACnF,QAAI,0BAA0B,+BAA+B;AACzD,oBAAc,WAAW,WAAY;AAAE,eAAO,yBAAyB,6BAA6B;AAAA,MAAG,GAAG,iBAAiB,GAAG;AAAA,IAClI;AACA,WAAO;AAAA,EACX,GAAG,CAAC,uBAAuB,yBAAyB,IAAI,CAAC;AACzD,MAAI,eAAe,SAAS;AAC5B,MAAI,cAAc;AAClB,MAAI,aAAa,IAAI,KACjB,OAAO,0BAA0B,YACjC,yBAAyB;AACzB,kBAAc,WAAW,qBAAqB;AAC9C,QAAI,eAAe,qBAAqB,WAAW;AACnD,mBAAe,KAAK,MAAM,wBAAwB,YAAY;AAAA,EAClE;AACA,SAAc,qBAAc,6BAA6B,SAAS,EAAE,OAAO,cAAc,MAAM,YAAY,GAAG,UAAU,CAAC;AAC7H;AACA,sBAAsB,cAAc;;;ACtHpC,IAAAC,SAAuB;AAEvB,IAAI,kBAAkB,SAAU,OAAO;AACnC,MAAIC,MAAK,QAAQ,GAAGC,gBAAeD,IAAG,cAAc,OAAOA,IAAG;AAC9D,MAAI,QAAQ,MAAM,OAAO,QAAQ,MAAM,OAAO,WAAW,MAAM;AAC/D,MAAI,iBAAiBC,cAAa,OAAO,KAAK;AAC9C,MAAI,kBAAkB,MAAM,cAAc,KAAK;AAC/C,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,eAAe;AAAA,EACnC;AACA,MAAI,MAAM;AACN,WAAa,qBAAc,MAAM,MAAM,eAAe;AAAA,EAC1D;AAEA,SAAO;AACX;AACA,gBAAgB,cAAc;;;ACf9B,IAAAC,SAAuB;AAGvB,SAAS,SAAS,WAAW,WAAW;AACpC,MAAI,SAAS,UAAU,QAAQ,aAAa,OAAO,WAAW,CAAC,QAAQ,CAAC;AACxE,MAAI,aAAa,UAAU,QAAQ,iBAAiB,OAAO,WAAW,CAAC,QAAQ,CAAC;AAChF,SAAQ,aAAa,YAAY,MAAM,KACnC,aAAa,YAAY,cAAc;AAC/C;AACA,SAAS,iBAAiB,OAAO;AAC7B,MAAI,OAAO,QAAQ;AACnB,MAAIC,iBAAgB,KAAK,eAAeC,MAAK,KAAK,eAAe,OAAOA,QAAO,SAAe,kBAAWA;AACzG,MAAI,KAAK,MAAM,IAAI,cAAc,MAAM,aAAa,iBAAiB,MAAM,gBAAgB,SAAS,MAAM,QAAQ,WAAW,MAAM,UAAU,KAAK,MAAM,SAAS,YAAY,OAAO,SAAS,OAAO,IAAI,YAAY,MAAM;AAC1N,MAAI,aAAa,EAAE,IAAQ,aAA0B,eAA+B;AACpF,MAAI,QAAQD,eAAc,YAAY,QAAQ;AAAA,IAC1C;AAAA,EACJ,CAAC;AACD,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,MAAM,QAAQ,KAAK,IAAI,QAAQ,CAAC,KAAK,CAAC;AAAA,EAC1D;AACA,MAAI,WAAW;AACX,WAAa,qBAAc,WAAW,MAAY,gBAAS,QAAQ,KAAK,CAAC;AAAA,EAC7E;AACA,SAAa,qBAAoB,iBAAU,MAAM,KAAK;AAC1D;AACA,iBAAiB,cAAc;AAC/B,IAAI,2BAAiC,YAAK,kBAAkB,QAAQ;AACpE,yBAAyB,cAAc;;;AChCvC,IAAAE,UAAuB;AAEvB,IAAI,yBAAyB,SAAU,OAAO;AAC1C,MAAI,OAAO,QAAQ;AACnB,MAAI,OAAO,MAAM,MAAM,KAAK,MAAM,IAAI,WAAW,MAAM,UAAU,cAAc,OAAO,OAAO,CAAC,QAAQ,MAAM,UAAU,CAAC;AACvH,MAAI,iBAAiB,KAAK,oBAAoB,MAAM,IAAI,WAAW;AACnE,MAAI,OAAO,aAAa,YAAY;AAChC,WAAO,SAAS,cAAc;AAAA,EAClC;AACA,MAAI,OAAO,KAAK,iBAAuB;AACvC,SAAa,sBAAc,MAAM,MAAM,cAAc;AACzD;AACA,uBAAuB,cAAc;;;ACK9B,IAAI,gBAAgB,yBAAyB,YAAY;AACzD,IAAI,gBAAgB,yBAAyB,YAAY;AACzD,IAAI,kBAAkB,yBAAyB,cAAc;AAC7D,IAAI,gBAAgB,yBAAyB,YAAY;AACzD,IAAI,uBAAuB,yBAAyB,mBAAmB;AACvE,IAAI,qBAAqB,sCAAsC,YAAY;AAC3E,IAAI,qBAAqB,sCAAsC,YAAY;;;ACtBlF,IAAM,UAAU,CAAC,OAAe,GAAG,QAAQ,IAAI,EAAE;ACF1C,IAAM,aAAa,UACxB,wBAAC,OAAI,EAAA,OAAM,OAAM,QAAO,MAAK,SAAQ,cAAa,MAAK,QAAO,OAAM,8BAClE,cAAC,yBAAA,KAAA,EAAE,SAAQ,QACT,UAAA;MAAA;IAAC;IAAA;MACC,UAAS;MACT,UAAS;MACT,GAAE;MACF,MAAK;IAAA;EACP;MACA,wBAAC,QAAA,EAAK,GAAE,uBAAsB,QAAO,SAAQ,aAAY,KAAI,eAAc,QAAQ,CAAA;MACnF;IAAC;IAAA;MACC,UAAS;MACT,UAAS;MACT,GAAE;MACF,MAAK;IAAA;EACP;MACA;IAAC;IAAA;MACC,UAAS;MACT,UAAS;MACT,GAAE;MACF,MAAK;IAAA;EACP;MACA;IAAC;IAAA;MACC,UAAS;MACT,UAAS;MACT,GAAE;MACF,MAAK;IAAA;EACP;MACA;IAAC;IAAA;MACC,GAAE;MACF,QAAO;MACP,aAAY;MACZ,eAAc;MACd,gBAAe;IAAA;EACjB;MACA;IAAC;IAAA;MACC,GAAE;MACF,QAAO;MACP,aAAY;MACZ,eAAc;MACd,gBAAe;IAAA;EACjB;MACA;IAAC;IAAA;MACC,GAAE;MACF,QAAO;MACP,aAAY;MACZ,eAAc;MACd,gBAAe;IAAA;EACjB;MACA;IAAC;IAAA;MACC,GAAE;MACF,QAAO;MACP,aAAY;MACZ,eAAc;MACd,gBAAe;IAAA;EACjB;MACA;IAAC;IAAA;MACC,GAAE;MACF,QAAO;MACP,aAAY;MACZ,eAAc;MACd,gBAAe;IAAA;EACjB;MACA;IAAC;IAAA;MACC,GAAE;MACF,QAAO;MACP,aAAY;MACZ,eAAc;MACd,gBAAe;IAAA;EACjB;AAAA,EAAA,CACF,EACF,CAAA;ACxDF,IAAMC,0BAAwB,GAAO,GAAG;;;;;AAMxC,IAAM,WAAW,MAAM;AACf,QAAA,EAAE,eAAAC,eAAA,IAAkB,QAAA;AAE1B,aACG,wBAAA,KAAA,EAAI,SAAS,GAAG,WAAS,MACxB,cAAA,yBAAC,MAAK,EAAA,YAAW,UAAS,WAAU,UAAS,SAAS,IAAI,WAAS,MACjE,UAAA;QAAA,wBAACD,yBAAAA,EAAsB,eAAe,GAAG,eAAW,MAClD,cAAA,wBAAC,YAAA,CAAA,CAAW,EACd,CAAA;QACC,wBAAA,KAAA,EAAI,eAAe,GAClB,cAAA,wBAAC,YAAW,EAAA,SAAQ,QAAO,KAAI,KAAI,WAAU,UAAS,WAAU,eAC7D,UAAcC,eAAA;MACb,IAAI,QAAQ,yBAAyB;MACrC,gBAAgB;IAAA,CACjB,EAAA,CACH,EACF,CAAA;QACA,wBAAC,YAAW,EAAA,SAAQ,WAAU,KAAI,KAAI,WAAU,UAAS,WAAU,cAChE,UAAcA,eAAA;MACb,IAAI,QAAQ,4BAA4B;MACxC,gBACE;IACH,CAAA,EAAA,CACH;QACA,wBAAC,KAAI,EAAA,WAAW,GACd,cAAA;MAAC;MAAA;QACC,SAAQ;QACR,aAAA,wBAAU,eAAa,CAAA,CAAA;QACvB,MAAK;QACL,YAAU;QACV,QAAO;QAEN,UAAcA,eAAA;UACb,IAAI,QAAQ,8BAA8B;UAC1C,gBAAgB;QAAA,CACjB;MAAA;IAAA,EAAA,CAEL;EAAA,EACF,CAAA,EACF,CAAA;AAEJ;AC9DO,IAAM,WAAW,UACrB,yBAAA,OAAA,EAAI,OAAM,OAAM,QAAO,MAAK,SAAQ,cAAa,MAAK,QAAO,OAAM,8BAClE,UAAA;MAAA,wBAAC,KAAA,EAAE,UAAS,qBACV,cAAC,yBAAA,KAAA,EAAE,SAAQ,QACT,UAAA;QAAA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;QACL,aAAY;MAAA;IACd;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;QACA;MAAC;MAAA;QACC,GAAE;QACF,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;MAAA;IACjB;QACA;MAAC;MAAA;QACC,GAAE;QACF,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;MAAA;IACjB;QACA;MAAC;MAAA;QACC,GAAE;QACF,QAAO;QACP,aAAY;QACZ,eAAc;QACd,gBAAe;MAAA;IACjB;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;QACA;MAAC;MAAA;QACC,UAAS;QACT,UAAS;QACT,GAAE;QACF,MAAK;MAAA;IACP;EAAA,EAAA,CACF,EACF,CAAA;MAAA,wBACC,QACC,EAAA,cAAA,wBAAC,YAAS,EAAA,IAAG,eACX,cAAC,wBAAA,QAAA,EAAK,OAAM,SAAQ,QAAO,MAAK,MAAK,SAAQ,WAAU,sBAAA,CAAsB,EAC/E,CAAA,EAAA,CACF;AAAA,EAAA,CACF;ACpFF,IAAM,wBAAwB,GAAO,GAAG;;;;;AAMxC,IAAM,qBAAqB,GAAO,UAAU;;;;;;YAMhC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,QAAQ;;;;;;;;AAShD,IAAM,YAAY,MAAM;AAChB,QAAA,EAAE,eAAAA,eAAA,IAAkB,QAAA;AAGxB,aAAA,wBAAC,KAAI,EAAA,SAAS,GACZ,cAAA,yBAAC,MAAK,EAAA,YAAW,UAAS,WAAU,UAAS,SAAS,IACpD,UAAA;QAAA,wBAAC,uBAAA,EAAsB,eAAe,GAAG,eAAW,MAClD,cAAA,wBAAC,UAAA,CAAA,CAAS,EACZ,CAAA;QACC,wBAAA,KAAA,EAAI,eAAe,GAClB,cAAA,wBAAC,YAAW,EAAA,SAAQ,QAAO,KAAI,KAAI,WAAU,UAAS,WAAU,eAC7D,UAAcA,eAAA;MACb,IAAI,QAAQ,wCAAwC;MACpD,gBAAgB;IAAA,CACjB,EAAA,CACH,EACF,CAAA;QACA,wBAAC,YAAW,EAAA,SAAQ,WAAU,KAAI,KAAI,WAAU,UAAS,WAAU,cAChE,UAAcA,eAAA;MACb,IAAI,QAAQ,2CAA2C;MACvD,gBACE;IACH,CAAA,EAAA,CACH;QACA,wBAAC,KAAI,EAAA,WAAW,GACd,cAAA;MAAC;MAAA;QACC,YAAU;QACV,eAAA,wBAAY,cAAO,CAAA,CAAA;QACnB,MAAK;QACL,QAAO;QAEN,UAAcA,eAAA;UACb,IAAI,QAAQ,+BAA+B;UAC3C,gBAAgB;QAAA,CACjB;MAAA;IAAA,EAAA,CAEL;EAAA,EACF,CAAA,EACF,CAAA;AAEJ;AC5EA,IAAe,qBAAA;ACAf,IAAe,oBAAA;ACAf,IAAe,qBAAA;ACmBf,IAAM,gBAAgB,GAAO,GAAG;;;;;;;;;AAUhC,IAAM,0BAA0B,GAAO,GAAG;;;;;;;;;AAU1C,IAAM,yBAAyB,GAAO,GAAG;;;;;;;;;AAUzC,IAAM,WAAW,MAAM;AACf,QAAA,EAAE,eAAAA,eAAA,IAAkB,QAAA;AAE1B,aACG,yBAAA,KAAA,EAAI,aAAa,IAAI,cAAc,IAClC,UAAA;QAAC,wBAAA,yBAAA,EACC,cAAA,wBAAC,OAAI,EAAA,KAAI,oBAAmB,eAAW,MAAC,KAAK,mBAAA,CAAoB,EACnE,CAAA;QACA,wBAAC,wBACC,EAAA,cAAA,wBAAC,OAAI,EAAA,KAAI,mBAAkB,eAAW,MAAC,KAAK,kBAAA,CAAmB,EACjE,CAAA;QACA,wBAAC,eACC,EAAA,cAAA,wBAAC,OAAI,EAAA,KAAI,0BAAyB,eAAW,MAAC,KAAK,mBAAA,CAAoB,EACzE,CAAA;QAEA,yBAAC,KAAA,EAAI,aAAa,IAAI,cAAc,IAAI,eAAe,GAAG,YAAY,IACpE,UAAA;UAAA,wBAAC,MAAA,EAAK,gBAAe,iBAAgB,YAAW,UAAS,WAAU,UACjE,cAAA,wBAAC,MAAK,EAAA,UAAU,GACd,cAAC,wBAAA,YAAA,EAAW,KAAI,MAAK,SAAQ,SAC1B,UAAcA,eAAA;QACb,IAAI,QAAQ,gBAAgB;QAC5B,gBAAgB;MAAA,CACjB,EACH,CAAA,EACF,CAAA,EAAA,CACF;UACC,wBAAA,MAAA,EAAK,YAAW,UAAS,WAAU,UAClC,cAAA,wBAAC,YAAW,EAAA,SAAQ,WAAU,WAAU,cAAa,KAAI,KACtD,UAAcA,eAAA;QACb,IAAI,QAAQ,mBAAmB;QAC/B,gBACE;MAAA,CACH,EAAA,CACH,EACF,CAAA;IAAA,EAAA,CACF;QACA,yBAAC,KAAI,EAAA,SAAS,IACZ,UAAA;UAAA,yBAAC,QAAQ,MAAR,EAAa,MAAK,KACjB,UAAA;YAAA,wBAAC,WAAU,CAAA,CAAA;YAAA,wBACV,UAAS,CAAA,CAAA;MAAA,EAAA,CACZ;UACA,yBAAC,KAAI,EAAA,SAAS,GAAG,cAAc,GAAG,WAAS,MAAC,YAAW,YAAW,aAAY,cAC5E,UAAA;YAAA,wBAAC,KAAI,EAAA,eAAe,GAClB,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,YAAW,QAAO,WAAU,eAAc,KAAI,KACvE,UAAcA,eAAA;UACb,IAAI,QAAQ,kCAAkC;UAC9C,gBAAgB;QAAA,CACjB,EAAA,CACH,EACF,CAAA;YAAA,yBAEC,YAAW,EAAA,SAAQ,WAAU,WAAU,eAAc,KAAI,KACvD,UAAA;UAAcA,eAAA;YACb,IAAI,QAAQ,iCAAiC;YAC7C,gBACE;UAAA,CACH;UAAG;cACH,wBAAA,MAAA,EAAK,MAAK,qGAAoG,UAE/G,aAAA,CAAA;QAAA,EAAA,CACF;MAAA,EAAA,CACF;IAAA,EAAA,CACF;EACF,EAAA,CAAA;AAEJ;ACtGA,IAAM,MAAM,MAAM;AAEd,aAAA,wBAAC,OACC,EAAA,cAAA,yBAAC,QACC,EAAA,UAAA;QAAA,wBAAC,OAAA,EAAM,OAAK,MAAC,aAAS,wBAAC,UAAA,CAAS,CAAA,EAAA,CAAI;QACpC,wBAAC,OAAA,EAAM,MAAK,KAAI,aAAU,wBAAA,KAAK,OAAL,CAAW,CAAA,EAAA,CAAI;EAAA,EAC3C,CAAA,EACF,CAAA;AAEJ;", "names": ["React", "React", "React", "MissingLocaleDataError", "RangePatternType", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TYPE", "SKELETON_TYPE", "_a", "opt", "startsWith", "fromCodePoint", "fromEntries", "_a", "codePointAt", "trimStart", "trimEnd", "matchIdentifierAtIndex", "<PERSON><PERSON><PERSON>", "ErrorCode", "FormatError", "InvalidValueError", "InvalidValueTypeError", "Missing<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "PART_TYPE", "formatToParts", "_a", "IntlMessageFormat", "formatToParts", "IntlErrorCode", "IntlError", "UnsupportedFormatterError", "InvalidConfigError", "MissingDataError", "IntlFormatError", "MessageFormatError", "MissingTranslationError", "_a", "createFastMemoizeCache", "_a", "_a", "_a", "_a", "_a", "_a", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_a", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_a", "DEFAULT_INTL_CONFIG", "DisplayName", "DisplayNameParts", "React", "React", "formatMessage", "createIntl", "_a", "DEFAULT_INTL_CONFIG", "IntlProvider", "createIntl", "_a", "DEFAULT_INTL_CONFIG", "React", "_a", "formatRelativeTime", "unitDuration", "React", "_a", "formatPlural", "React", "formatMessage", "_a", "React", "EmptyStateIconWrapper", "formatMessage"]}