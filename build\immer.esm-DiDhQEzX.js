function g(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];{var o=be[e],c=o?typeof o=="function"?o.apply(null,n):o:"unknown error nr: "+e;throw Error("[Immer] "+c)}}function S(e){return!!e&&!!e[y]}function k(e){var t;return!!e&&(function(n){if(!n||typeof n!="object")return!1;var r=Object.getPrototypeOf(n);if(r===null)return!0;var o=Object.hasOwnProperty.call(r,"constructor")&&r.constructor;return o===Object||typeof o=="function"&&Function.toString.call(o)===me}(e)||Array.isArray(e)||!!e[z]||!!(!((t=e.constructor)===null||t===void 0)&&t[z])||W(e)||$(e))}function Oe(e){return S(e)||g(23,e),e[y].t}function x(e,t,n){n===void 0&&(n=!1),R(e)===0?(n?Object.keys:N)(e).forEach(function(r){n&&typeof r=="symbol"||t(r,e[r],e)}):e.forEach(function(r,o){return t(o,r,e)})}function R(e){var t=e[y];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:W(e)?2:$(e)?3:0}function _(e,t){return R(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function T(e,t){return R(e)===2?e.get(t):e[t]}function fe(e,t,n){var r=R(e);r===2?e.set(t,n):r===3?e.add(n):e[t]=n}function se(e,t){return e===t?e!==0||1/e==1/t:e!=e&&t!=t}function W(e){return ye&&e instanceof Map}function $(e){return ge&&e instanceof Set}function D(e){return e.o||e.t}function Y(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=pe(e);delete t[y];for(var n=N(t),r=0;r<n.length;r++){var o=n[r],c=t[o];c.writable===!1&&(c.writable=!0,c.configurable=!0),(c.get||c.set)&&(t[o]={configurable:!0,writable:!0,enumerable:c.enumerable,value:e[o]})}return Object.create(Object.getPrototypeOf(e),t)}function Z(e,t){return t===void 0&&(t=!1),V(e)||S(e)||!k(e)||(R(e)>1&&(e.set=e.add=e.clear=e.delete=he),Object.freeze(e),t&&x(e,function(n,r){return Z(r,!0)},!0)),e}function he(){g(2)}function V(e){return e==null||typeof e!="object"||Object.isFrozen(e)}function A(e){var t=X[e];return t||g(18,e),t}function le(e,t){X[e]||(X[e]=t)}function H(){return E||g(0),E}function J(e,t){t&&(A("Patches"),e.u=[],e.s=[],e.v=t)}function C(e){L(e),e.p.forEach(ve),e.p=null}function L(e){e===E&&(E=e.l)}function ne(e){return E={p:[],l:E,h:e,m:!0,_:0}}function ve(e){var t=e[y];t.i===0||t.i===1?t.j():t.g=!0}function G(e,t){t._=t.p.length;var n=t.p[0],r=e!==void 0&&e!==n;return t.h.O||A("ES5").S(t,e,r),r?(n[y].P&&(C(t),g(4)),k(e)&&(e=K(t,e),t.l||U(t,e)),t.u&&A("Patches").M(n[y].t,e,t.u,t.s)):e=K(t,n,[]),C(t),t.u&&t.v(t.u,t.s),e!==te?e:void 0}function K(e,t,n){if(V(t))return t;var r=t[y];if(!r)return x(t,function(i,u){return oe(e,r,t,i,u,n)},!0),t;if(r.A!==e)return t;if(!r.P)return U(e,r.t,!0),r.t;if(!r.I){r.I=!0,r.A._--;var o=r.i===4||r.i===5?r.o=Y(r.k):r.o,c=o,a=!1;r.i===3&&(c=new Set(o),o.clear(),a=!0),x(c,function(i,u){return oe(e,r,o,i,u,n,a)}),U(e,o,!1),n&&e.u&&A("Patches").N(r,n,e.u,e.s)}return r.o}function oe(e,t,n,r,o,c,a){if(o===n&&g(5),S(o)){var i=K(e,o,c&&t&&t.i!==3&&!_(t.R,r)?c.concat(r):void 0);if(fe(n,r,i),!S(i))return;e.m=!1}else a&&n.add(o);if(k(o)&&!V(o)){if(!e.h.D&&e._<1)return;K(e,o),t&&t.A.l||U(e,o)}}function U(e,t,n){n===void 0&&(n=!1),!e.l&&e.h.D&&e.m&&Z(t,n)}function q(e,t){var n=e[y];return(n?D(n):e)[t]}function ae(e,t){if(t in e)for(var n=Object.getPrototypeOf(e);n;){var r=Object.getOwnPropertyDescriptor(n,t);if(r)return r;n=Object.getPrototypeOf(n)}}function I(e){e.P||(e.P=!0,e.l&&I(e.l))}function B(e){e.o||(e.o=Y(e.t))}function Q(e,t,n){var r=W(t)?A("MapSet").F(t,n):$(t)?A("MapSet").T(t,n):e.O?function(o,c){var a=Array.isArray(o),i={i:a?1:0,A:c?c.A:H(),P:!1,I:!1,R:{},l:c,t:o,k:null,o:null,j:null,C:!1},u=i,f=F;a&&(u=[i],f=M);var p=Proxy.revocable(u,f),l=p.revoke,v=p.proxy;return i.k=v,i.j=l,v}(t,n):A("ES5").J(t,n);return(n?n.A:H()).p.push(r),r}function de(e){return S(e)||g(22,e),function t(n){if(!k(n))return n;var r,o=n[y],c=R(n);if(o){if(!o.P&&(o.i<4||!A("ES5").K(o)))return o.t;o.I=!0,r=ue(n,c),o.I=!1}else r=ue(n,c);return x(r,function(a,i){o&&T(o.t,a)===i||fe(r,a,t(i))}),c===3?new Set(r):r}(e)}function ue(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return Y(e)}function we(){function e(a,i){var u=c[a];return u?u.enumerable=i:c[a]=u={configurable:!0,enumerable:i,get:function(){var f=this[y];return o(f),F.get(f,a)},set:function(f){var p=this[y];o(p),F.set(p,a,f)}},u}function t(a){for(var i=a.length-1;i>=0;i--){var u=a[i][y];if(!u.P)switch(u.i){case 5:r(u)&&I(u);break;case 4:n(u)&&I(u)}}}function n(a){for(var i=a.t,u=a.k,f=N(u),p=f.length-1;p>=0;p--){var l=f[p];if(l!==y){var v=i[l];if(v===void 0&&!_(i,l))return!0;var s=u[l],h=s&&s[y];if(h?h.t!==v:!se(s,v))return!0}}var d=!!i[y];return f.length!==N(i).length+(d?0:1)}function r(a){var i=a.k;if(i.length!==a.t.length)return!0;var u=Object.getOwnPropertyDescriptor(i,i.length-1);if(u&&!u.get)return!0;for(var f=0;f<i.length;f++)if(!i.hasOwnProperty(f))return!0;return!1}function o(a){a.g&&g(3,JSON.stringify(D(a)))}var c={};le("ES5",{J:function(a,i){var u=Array.isArray(a),f=function(l,v){if(l){for(var s=Array(v.length),h=0;h<v.length;h++)Object.defineProperty(s,""+h,e(h,!0));return s}var d=pe(v);delete d[y];for(var m=N(d),b=0;b<m.length;b++){var P=m[b];d[P]=e(P,l||!!d[P].enumerable)}return Object.create(Object.getPrototypeOf(v),d)}(u,a),p={i:u?5:4,A:i?i.A:H(),P:!1,I:!1,R:{},l:i,t:a,k:f,o:null,g:!1,C:!1};return Object.defineProperty(f,y,{value:p,writable:!0}),f},S:function(a,i,u){u?S(i)&&i[y].A===a&&t(a.p):(a.u&&function f(p){if(p&&typeof p=="object"){var l=p[y];if(l){var v=l.t,s=l.k,h=l.R,d=l.i;if(d===4)x(s,function(j){j!==y&&(v[j]!==void 0||_(v,j)?h[j]||f(s[j]):(h[j]=!0,I(l)))}),x(v,function(j){s[j]!==void 0||_(s,j)||(h[j]=!1,I(l))});else if(d===5){if(r(l)&&(I(l),h.length=!0),s.length<v.length)for(var m=s.length;m<v.length;m++)h[m]=!1;else for(var b=v.length;b<s.length;b++)h[b]=!0;for(var P=Math.min(s.length,v.length),O=0;O<P;O++)s.hasOwnProperty(O)||(h[O]=!0),h[O]===void 0&&f(s[O])}}}}(a.p[0]),t(a.p))},K:function(a){return a.i===4?n(a):r(a)}})}function je(){function e(r){if(!k(r))return r;if(Array.isArray(r))return r.map(e);if(W(r))return new Map(Array.from(r.entries()).map(function(a){return[a[0],e(a[1])]}));if($(r))return new Set(Array.from(r).map(e));var o=Object.create(Object.getPrototypeOf(r));for(var c in r)o[c]=e(r[c]);return _(r,z)&&(o[z]=r[z]),o}function t(r){return S(r)?e(r):r}var n="add";le("Patches",{$:function(r,o){return o.forEach(function(c){for(var a=c.path,i=c.op,u=r,f=0;f<a.length-1;f++){var p=R(u),l=a[f];typeof l!="string"&&typeof l!="number"&&(l=""+l),p!==0&&p!==1||l!=="__proto__"&&l!=="constructor"||g(24),typeof u=="function"&&l==="prototype"&&g(24),typeof(u=T(u,l))!="object"&&g(15,a.join("/"))}var v=R(u),s=e(c.value),h=a[a.length-1];switch(i){case"replace":switch(v){case 2:return u.set(h,s);case 3:g(16);default:return u[h]=s}case n:switch(v){case 1:return h==="-"?u.push(s):u.splice(h,0,s);case 2:return u.set(h,s);case 3:return u.add(s);default:return u[h]=s}case"remove":switch(v){case 1:return u.splice(h,1);case 2:return u.delete(h);case 3:return u.delete(c.value);default:return delete u[h]}default:g(17,i)}}),r},N:function(r,o,c,a){switch(r.i){case 0:case 4:case 2:return function(i,u,f,p){var l=i.t,v=i.o;x(i.R,function(s,h){var d=T(l,s),m=T(v,s),b=h?_(l,s)?"replace":n:"remove";if(d!==m||b!=="replace"){var P=u.concat(s);f.push(b==="remove"?{op:b,path:P}:{op:b,path:P,value:m}),p.push(b===n?{op:"remove",path:P}:b==="remove"?{op:n,path:P,value:t(d)}:{op:"replace",path:P,value:t(d)})}})}(r,o,c,a);case 5:case 1:return function(i,u,f,p){var l=i.t,v=i.R,s=i.o;if(s.length<l.length){var h=[s,l];l=h[0],s=h[1];var d=[p,f];f=d[0],p=d[1]}for(var m=0;m<l.length;m++)if(v[m]&&s[m]!==l[m]){var b=u.concat([m]);f.push({op:"replace",path:b,value:t(s[m])}),p.push({op:"replace",path:b,value:t(l[m])})}for(var P=l.length;P<s.length;P++){var O=u.concat([P]);f.push({op:n,path:O,value:t(s[P])})}l.length<s.length&&p.push({op:"replace",path:u.concat(["length"]),value:l.length})}(r,o,c,a);case 3:return function(i,u,f,p){var l=i.t,v=i.o,s=0;l.forEach(function(h){if(!v.has(h)){var d=u.concat([s]);f.push({op:"remove",path:d,value:h}),p.unshift({op:n,path:d,value:h})}s++}),s=0,v.forEach(function(h){if(!l.has(h)){var d=u.concat([s]);f.push({op:n,path:d,value:h}),p.unshift({op:"remove",path:d,value:h})}s++})}(r,o,c,a)}},M:function(r,o,c,a){c.push({op:"replace",path:[],value:o===te?void 0:o}),a.push({op:"replace",path:[],value:r})}})}var ie,E,ee=typeof Symbol<"u"&&typeof Symbol("x")=="symbol",ye=typeof Map<"u",ge=typeof Set<"u",ce=typeof Proxy<"u"&&Proxy.revocable!==void 0&&typeof Reflect<"u",te=ee?Symbol.for("immer-nothing"):((ie={})["immer-nothing"]=!0,ie),z=ee?Symbol.for("immer-draftable"):"__$immer_draftable",y=ee?Symbol.for("immer-state"):"__$immer_state",be={0:"Illegal state",1:"Immer drafts cannot have computed properties",2:"This object has been frozen and should not be mutated",3:function(e){return"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? "+e},4:"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.",5:"Immer forbids circular references",6:"The first or second argument to `produce` must be a function",7:"The third argument to `produce` must be a function or undefined",8:"First argument to `createDraft` must be a plain object, an array, or an immerable object",9:"First argument to `finishDraft` must be a draft returned by `createDraft`",10:"The given draft is already finalized",11:"Object.defineProperty() cannot be used on an Immer draft",12:"Object.setPrototypeOf() cannot be used on an Immer draft",13:"Immer only supports deleting array indices",14:"Immer only supports setting array indices and the 'length' property",15:function(e){return"Cannot apply patch, path doesn't resolve: "+e},16:'Sets cannot have "replace" patches.',17:function(e){return"Unsupported patch operation: "+e},18:function(e){return"The plugin for '"+e+"' has not been loaded into Immer. To enable the plugin, import and call `enable"+e+"()` when initializing your application."},20:"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available",21:function(e){return"produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '"+e+"'"},22:function(e){return"'current' expects a draft, got: "+e},23:function(e){return"'original' expects a draft, got: "+e},24:"Patching reserved attributes like __proto__, prototype and constructor is not allowed"},me=""+Object.prototype.constructor,N=typeof Reflect<"u"&&Reflect.ownKeys?Reflect.ownKeys:Object.getOwnPropertySymbols!==void 0?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,pe=Object.getOwnPropertyDescriptors||function(e){var t={};return N(e).forEach(function(n){t[n]=Object.getOwnPropertyDescriptor(e,n)}),t},X={},F={get:function(e,t){if(t===y)return e;var n=D(e);if(!_(n,t))return function(o,c,a){var i,u=ae(c,a);return u?"value"in u?u.value:(i=u.get)===null||i===void 0?void 0:i.call(o.k):void 0}(e,n,t);var r=n[t];return e.I||!k(r)?r:r===q(e.t,t)?(B(e),e.o[t]=Q(e.A.h,r,e)):r},has:function(e,t){return t in D(e)},ownKeys:function(e){return Reflect.ownKeys(D(e))},set:function(e,t,n){var r=ae(D(e),t);if(r?.set)return r.set.call(e.k,n),!0;if(!e.P){var o=q(D(e),t),c=o?.[y];if(c&&c.t===n)return e.o[t]=n,e.R[t]=!1,!0;if(se(n,o)&&(n!==void 0||_(e.t,t)))return!0;B(e),I(e)}return e.o[t]===n&&(n!==void 0||t in e.o)||Number.isNaN(n)&&Number.isNaN(e.o[t])||(e.o[t]=n,e.R[t]=!0),!0},deleteProperty:function(e,t){return q(e.t,t)!==void 0||t in e.t?(e.R[t]=!1,B(e),I(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var n=D(e),r=Reflect.getOwnPropertyDescriptor(n,t);return r&&{writable:!0,configurable:e.i!==1||t!=="length",enumerable:r.enumerable,value:n[t]}},defineProperty:function(){g(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){g(12)}},M={};x(F,function(e,t){M[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),M.deleteProperty=function(e,t){return isNaN(parseInt(t))&&g(13),M.set.call(this,e,t,void 0)},M.set=function(e,t,n){return t!=="length"&&isNaN(parseInt(t))&&g(14),F.set.call(this,e[0],t,n,e[0])};var Pe=function(){function e(n){var r=this;this.O=ce,this.D=!0,this.produce=function(o,c,a){if(typeof o=="function"&&typeof c!="function"){var i=c;c=o;var u=r;return function(d){var m=this;d===void 0&&(d=i);for(var b=arguments.length,P=Array(b>1?b-1:0),O=1;O<b;O++)P[O-1]=arguments[O];return u.produce(d,function(j){var re;return(re=c).call.apply(re,[m,j].concat(P))})}}var f;if(typeof c!="function"&&g(6),a!==void 0&&typeof a!="function"&&g(7),k(o)){var p=ne(r),l=Q(r,o,void 0),v=!0;try{f=c(l),v=!1}finally{v?C(p):L(p)}return typeof Promise<"u"&&f instanceof Promise?f.then(function(d){return J(p,a),G(d,p)},function(d){throw C(p),d}):(J(p,a),G(f,p))}if(!o||typeof o!="object"){if((f=c(o))===void 0&&(f=o),f===te&&(f=void 0),r.D&&Z(f,!0),a){var s=[],h=[];A("Patches").M(o,f,s,h),a(s,h)}return f}g(21,o)},this.produceWithPatches=function(o,c){if(typeof o=="function")return function(f){for(var p=arguments.length,l=Array(p>1?p-1:0),v=1;v<p;v++)l[v-1]=arguments[v];return r.produceWithPatches(f,function(s){return o.apply(void 0,[s].concat(l))})};var a,i,u=r.produce(o,c,function(f,p){a=f,i=p});return typeof Promise<"u"&&u instanceof Promise?u.then(function(f){return[f,a,i]}):[u,a,i]},typeof n?.useProxies=="boolean"&&this.setUseProxies(n.useProxies),typeof n?.autoFreeze=="boolean"&&this.setAutoFreeze(n.autoFreeze)}var t=e.prototype;return t.createDraft=function(n){k(n)||g(8),S(n)&&(n=de(n));var r=ne(this),o=Q(this,n,void 0);return o[y].C=!0,L(r),o},t.finishDraft=function(n,r){var o=n&&n[y];o&&o.C||g(9),o.I&&g(10);var c=o.A;return J(c,r),G(void 0,c)},t.setAutoFreeze=function(n){this.D=n},t.setUseProxies=function(n){n&&!ce&&g(20),this.O=n},t.applyPatches=function(n,r){var o;for(o=r.length-1;o>=0;o--){var c=r[o];if(c.path.length===0&&c.op==="replace"){n=c.value;break}}o>-1&&(r=r.slice(o+1));var a=A("Patches").$;return S(n)?a(n,r):this.produce(n,function(i){return a(i,r)})},e}(),w=new Pe,Ae=w.produce,Se=w.produceWithPatches.bind(w);w.setAutoFreeze.bind(w);w.setUseProxies.bind(w);var De=w.applyPatches.bind(w);w.createDraft.bind(w);w.finishDraft.bind(w);export{we as F,je as T,Se as c,Oe as e,Ae as f,De as p,S as r,k as t};
