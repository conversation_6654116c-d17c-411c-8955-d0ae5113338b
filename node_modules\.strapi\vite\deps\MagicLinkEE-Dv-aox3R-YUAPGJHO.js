import {
  MagicLinkWrapper
} from "./chunk-RGUY4FUD.js";
import "./chunk-FUOVX7ZC.js";
import {
  getBasename
} from "./chunk-ELTZWS66.js";
import "./chunk-4C2ZQ5OG.js";
import {
  useIntl
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/MagicLinkEE-Dv-aox3R.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var MagicLinkEE = ({ registrationToken }) => {
  const { formatMessage } = useIntl();
  if (registrationToken) {
    return (0, import_jsx_runtime.jsx)(
      MagicLinkWrapper,
      {
        target: `${window.location.origin}${getBasename()}/auth/register?registrationToken=${registrationToken}`,
        children: formatMessage({
          id: "app.components.Users.MagicLink.connect",
          defaultMessage: "Copy and share this link to give access to this user"
        })
      }
    );
  }
  return (0, import_jsx_runtime.jsx)(MagicLinkWrapper, { target: `${window.location.origin}${getBasename()}/auth/login`, children: formatMessage({
    id: "app.components.Users.MagicLink.connect.sso",
    defaultMessage: "Send this link to the user, the first login can be made via a SSO provider."
  }) });
};
export {
  MagicLinkEE
};
//# sourceMappingURL=MagicLinkEE-Dv-aox3R-YUAPGJHO.js.map
