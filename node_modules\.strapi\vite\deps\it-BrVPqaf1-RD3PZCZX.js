import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/it-BrVPqaf1.mjs
var groups = "Grup<PERSON>";
var models = "Tipi Collezione";
var pageNotFound = "Pagina non trovata";
var it = {
  "EditRelations.title": "Dati relazionali",
  "api.id": "API ID",
  "components.AddFilterCTA.add": "Filtri",
  "components.AddFilterCTA.hide": "Filtri",
  "components.DraggableAttr.edit": "Clicca per modificare",
  "components.DynamicZone.pick-compo": "Scegli un componente",
  "components.DynamicZone.required": "Componente richiesto",
  "components.EmptyAttributesBlock.button": "Vai alla pagina delle impostazioni",
  "components.EmptyAttributesBlock.description": "Puoi cambiare le tue impostazioni",
  "components.FieldItem.linkToComponentLayout": "Modifica impaginazione componente",
  "components.FilterOptions.button.apply": "Applica",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Applica",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Cancella tutto",
  "components.FiltersPickWrapper.PluginHeader.description": "Imposta le condizioni da applicare per filtrare gli elementi",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filtri",
  "components.FiltersPickWrapper.hide": "Nascondi",
  "components.LimitSelect.itemsPerPage": "Elementi per pagina",
  "components.NotAllowedInput.text": "Non hai il permesso di vedere questo campo",
  "components.Search.placeholder": "Ricerca elementi...",
  "components.Select.draft-info-title": "Stato: Bozza",
  "components.Select.publish-info-title": "Stato: Pubblicato",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Personalizza l'aspetto della schermata di modifica.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Definisci le impostazioni delle liste di elementi.",
  "components.SettingsViewWrapper.pluginHeader.title": "Configura la vista - {name}",
  "components.TableDelete.delete": "Elimina tutti",
  "components.TableDelete.deleteSelected": "Elimina selezionato",
  "components.TableEmpty.withFilters": "Nessun {contentType} con questi filtri...",
  "components.TableEmpty.withSearch": "Nessun {contentType} corrispondente alla ricerca ({search})...",
  "components.TableEmpty.withoutFilter": "Non ci sono {contentType}...",
  "components.empty-repeatable": "Ancora nessun elemento. Clicca il pulsante sottostante per aggiungerne uno.",
  "components.notification.info.maximum-requirement": "Hai già raggiunto il massimo numero di campi",
  "components.notification.info.minimum-requirement": "È stato aggiunto un campo per soddisfare il requisito minimo",
  "components.repeatable.reorder.error": "Si è verificato un errore durante il riordinamento del campo del componente",
  "components.reset-entry": "Azzera elemento",
  "components.uid.apply": "applica",
  "components.uid.available": "disponibile",
  "components.uid.regenerate": "rigenera",
  "components.uid.suggested": "suggerito",
  "components.uid.unavailable": "non disponibile",
  "containers.Edit.Link.Layout": "Configura impaginazione",
  "containers.Edit.Link.Model": "Modifica la Collezione",
  "containers.Edit.addAnItem": "Aggiungi un elemento...",
  "containers.Edit.clickToJump": "Clicca per andare all'elemento",
  "containers.Edit.delete": "Elimina",
  "containers.Edit.delete-entry": "Elimina questo elemento",
  "containers.Edit.editing": "Modifica in corso...",
  "containers.Edit.information": "Informazioni",
  "containers.Edit.information.by": "Da",
  "containers.Edit.information.draftVersion": "versione bozza",
  "containers.Edit.information.editing": "Modifica",
  "containers.Edit.information.lastUpdate": "Aggiornato",
  "containers.Edit.information.publishedVersion": "versione pubblicata",
  "containers.Edit.pluginHeader.title.new": "Crea un elemento",
  "containers.Edit.reset": "Azzera",
  "containers.Edit.returnList": "Torna alla lista",
  "containers.Edit.seeDetails": "Dettagli",
  "containers.Edit.submit": "Salva",
  "containers.EditSettingsView.modal-form.edit-field": "Modifica campo",
  "containers.EditView.notification.errors": "Il form contiene degli errori",
  "containers.Home.introduction": "Per modificare le voci, visitare il link nel menu di sinistra. Questo plugin non ha un modo per modificare le impostazioni ed è ancora in fase di sviluppo attivo.",
  "containers.Home.pluginHeaderDescription": "Gestisci i tuoi dati attraverso un'interfaccia bella e potente.",
  "containers.Home.pluginHeaderTitle": "Gestore Contenuti",
  "containers.List.draft": "Bozza",
  "containers.List.errorFetchRecords": "Errore",
  "containers.List.published": "Pubblicato",
  "containers.list.displayedFields": "Campi visualizzati",
  "containers.list.table-headers.publishedAt": "Stato",
  "containers.ListSettingsView.modal-form.edit-label": "Modifica etichetta",
  "containers.SettingPage.add.field": "Inserisci nuovo campo",
  "containers.SettingPage.attributes": "Attributi",
  "containers.SettingPage.attributes.description": "Definisci l'ordine degli attributi",
  "containers.SettingPage.editSettings.description": "Sposta i campi per costruire il layout",
  "containers.SettingPage.editSettings.entry.title": "Titolo elemento",
  "containers.SettingPage.editSettings.entry.title.description": "Scegli quale campo mostrare dell'elemento",
  "containers.SettingPage.editSettings.relation-field.description": "Scegli il campo da mostrare nelle liste e durante la modifica",
  "containers.SettingPage.editSettings.title": "Modifica (impostazioni)",
  "containers.SettingPage.layout": "Layout",
  "containers.SettingPage.listSettings.description": "Scegli le opzioni per questa Collezione",
  "containers.SettingPage.listSettings.title": "Lista (impostazioni)",
  "containers.SettingPage.pluginHeaderDescription": "Configura le impostazioni specifiche per questa Collezione",
  "containers.SettingPage.settings": "Impostazioni",
  "containers.SettingPage.view": "Vista",
  "containers.SettingViewModel.pluginHeader.title": "Gestore Contenuti - {name}",
  "containers.SettingsPage.Block.contentType.description": "Configura le impostazioni specifiche",
  "containers.SettingsPage.Block.contentType.title": "Tipi Collezione",
  "containers.SettingsPage.Block.generalSettings.description": "Configura le impostazioni di default per le tue Collezioni",
  "containers.SettingsPage.Block.generalSettings.title": "Generali",
  "containers.SettingsPage.pluginHeaderDescription": "Configura le impostazioni per tutti i Tipi Collezione e i Gruppi",
  "containers.SettingsView.list.subtitle": "Configura il layout e la vista per i tuoi tipi Collezione e i gruppi",
  "containers.SettingsView.list.title": "Configurazioni vista",
  "emptyAttributes.button": "Vai al costruttore di collezioni",
  "emptyAttributes.description": "Aggiungi il primo campo al tuo Tipo Collezione",
  "emptyAttributes.title": "Nessun campo presente",
  "error.attribute.key.taken": "Questo valore esiste già",
  "error.attribute.sameKeyAndName": "Non possono essere uguali",
  "error.attribute.taken": "Esiste già un campo con questo nome",
  "error.contentTypeName.taken": "Questo nome esiste già",
  "error.model.fetch": "Si è verificato un errore durante il caricamento dei modelli di configurazione.",
  "error.record.create": "Si è verificato un errore durante la creazione dell'elemento.",
  "error.record.delete": "Si è verificato un errore durante la cancellazione dell'elemento.",
  "error.record.fetch": "Si è verificato un errore durante il caricamento dell'elemento.",
  "error.record.update": "Si è verificato un errore durante l'aggiornamento dell'elemento.",
  "error.records.count": "Si è verificato un errore durante il conteggio degli elementi.",
  "error.records.fetch": "Si è verificato un errore durante il caricamento degli elementi.",
  "error.schema.generation": "Si è verificato un errore durante la generazione dello schema.",
  "error.validation.json": "Non è un JSON",
  "error.validation.max": "Il valore è troppo alto.",
  "error.validation.maxLength": "Il valore è troppo lungo.",
  "error.validation.min": "Il valore è troppo basso.",
  "error.validation.minLength": "Il valore è troppo breve.",
  "error.validation.minSupMax": "Non può essere superiore",
  "error.validation.regex": "Il valore non corrisponde alla RegEx.",
  "error.validation.required": "Questo valore è richiesto.",
  "form.Input.bulkActions": "Abilita azioni in blocco",
  "form.Input.defaultSort": "Attributo di ordinamento di default",
  "form.Input.description": "Descrizione",
  "form.Input.description.placeholder": "Mostra nome nel profilo",
  "form.Input.editable": "Campo modificabile",
  "form.Input.filters": "Abilita filtri",
  "form.Input.label": "Etichetta",
  "form.Input.label.inputDescription": "Questo valore sovrascrive l'etichetta mostrata nell'intestazione della tabella",
  "form.Input.pageEntries": "Righe per pagina",
  "form.Input.pageEntries.inputDescription": "Nota: Puoi sovrascrivere questo valore nella pagina delle impostazioni del Tipo Collezione.",
  "form.Input.placeholder": "Segnaposto",
  "form.Input.placeholder.placeholder": "Il mio fantastico valore",
  "form.Input.search": "Abilita ricerca",
  "form.Input.search.field": "Abilita ricerca su questo campo",
  "form.Input.sort.field": "Abilita ordinamento su questo campo",
  "form.Input.wysiwyg": "Mostra come WYSIWYG",
  "global.displayedFields": "Campi visualizzati",
  groups,
  "groups.numbered": "Gruppi ({number})",
  models,
  "models.numbered": "Tipi Collezione ({number})",
  "notification.error.displayedFields": "Devi avere almeno un campo visualizzato",
  "notification.error.relationship.fetch": "Si è verificato un errore durante il caricamento della relazione.",
  "notification.info.SettingPage.disableSort": "Devi avere almeno un attributo con ordinamento abilitato",
  "notification.info.minimumFields": "Devi avere almeno un campo visualizzato",
  "notification.upload.error": "Si è verificato un errore durante il caricamento dei file",
  pageNotFound,
  "permissions.not-allowed.create": "Non sei autorizzato a creare documenti",
  "permissions.not-allowed.update": "Non sei autorizzato a vedere questo documento",
  "plugin.description.long": "Permette di vedere, modificare e cancellare i dati presenti nel database in modo veloce.",
  "plugin.description.short": "Permette di vedere, modificare e cancellare i dati presenti nel database in modo veloce.",
  "success.record.delete": "Eliminato",
  "success.record.publish": "Pubblicato",
  "success.record.save": "Salvato",
  "success.record.unpublish": "Non pubblicato",
  "popUpWarning.warning.publish-question": "Vuoi ancora pubblicarlo?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Sì, pubblica"
};
export {
  it as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=it-BrVPqaf1-RD3PZCZX.js.map
