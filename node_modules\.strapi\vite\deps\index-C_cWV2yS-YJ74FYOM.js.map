{"version": 3, "sources": ["../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/components/Settings.jsx", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/state/actionTypes.js", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/state/actions.js", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/state/init.js", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/state/reducer.js", "../../../@strapi/upload/admin/src/pages/App/ConfigureTheView/index.jsx"], "sourcesContent": ["import React from 'react';\n\nimport { Box, Grid, SingleSelectOption, SingleSelect, Field } from '@strapi/design-system';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport { pageSizes, sortOptions } from '../../../../constants';\nimport { getTrad } from '../../../../utils';\n\nconst Settings = ({ sort = '', pageSize = 10, onChange }) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Box\n      background=\"neutral0\"\n      hasRadius\n      shadow=\"tableShadow\"\n      paddingTop={6}\n      paddingBottom={6}\n      paddingLeft={7}\n      paddingRight={7}\n    >\n      <Grid.Root gap={4}>\n        <Grid.Item s={12} col={6} direction=\"column\" alignItems=\"stretch\">\n          <Field.Root\n            hint={formatMessage({\n              id: getTrad('config.entries.note'),\n              defaultMessage: 'Number of assets displayed by default in the Media Library',\n            })}\n            name=\"pageSize\"\n          >\n            <Field.Label>\n              {formatMessage({\n                id: getTrad('config.entries.title'),\n                defaultMessage: 'Entries per page',\n              })}\n            </Field.Label>\n            <SingleSelect\n              onChange={(value) => onChange({ target: { name: 'pageSize', value } })}\n              value={pageSize}\n            >\n              {pageSizes.map((pageSize) => (\n                <SingleSelectOption key={pageSize} value={pageSize}>\n                  {pageSize}\n                </SingleSelectOption>\n              ))}\n            </SingleSelect>\n            <Field.Hint />\n          </Field.Root>\n        </Grid.Item>\n        <Grid.Item s={12} col={6} direction=\"column\" alignItems=\"stretch\">\n          <Field.Root\n            hint={formatMessage({\n              id: getTrad('config.note'),\n              defaultMessage: 'Note: You can override this value in the media library.',\n            })}\n            name=\"sort\"\n          >\n            <Field.Label>\n              {formatMessage({\n                id: getTrad('config.sort.title'),\n                defaultMessage: 'Default sort order',\n              })}\n            </Field.Label>\n            <SingleSelect\n              onChange={(value) => onChange({ target: { name: 'sort', value } })}\n              value={sort}\n              test-sort={sort}\n              data-testid=\"sort-select\"\n            >\n              {sortOptions.map((filter) => (\n                <SingleSelectOption\n                  data-testid={`sort-option-${filter.value}`}\n                  key={filter.key}\n                  value={filter.value}\n                >\n                  {formatMessage({ id: getTrad(filter.key), defaultMessage: `${filter.value}` })}\n                </SingleSelectOption>\n              ))}\n            </SingleSelect>\n            <Field.Hint />\n          </Field.Root>\n        </Grid.Item>\n      </Grid.Root>\n    </Box>\n  );\n};\n\nSettings.propTypes = {\n  sort: PropTypes.string.isRequired,\n  pageSize: PropTypes.number.isRequired,\n  onChange: PropTypes.func.isRequired,\n};\n\nexport { Settings };\n", "import pluginId from '../../../../pluginId';\n\nexport const ON_CHANGE = `${pluginId}/ON_CHANGE`;\nexport const SET_LOADED = `${pluginId}/SET_LOADED`;\n", "import { ON_CHANGE, SET_LOADED } from './actionTypes';\n\nexport const onChange = ({ name, value }) => ({\n  type: ON_CHANGE,\n  keys: name,\n  value,\n});\n\nexport const setLoaded = () => ({\n  type: SET_LOADED,\n});\n", "const initialState = {\n  initialData: {},\n  modifiedData: {},\n};\n\nconst init = (configData) => {\n  return {\n    ...initialState,\n    initialData: configData,\n    modifiedData: configData,\n  };\n};\nexport { init, initialState };\n", "import { produce } from 'immer'; // current\nimport get from 'lodash/get';\nimport set from 'lodash/set';\n\nimport { ON_CHANGE, SET_LOADED } from './actionTypes';\nimport { init, initialState } from './init';\n\nconst reducer = (\n  state = initialState,\n  action = {\n    type: '',\n  }\n) =>\n  // eslint-disable-next-line consistent-return\n  produce(state, (draftState) => {\n    switch (action.type) {\n      case ON_CHANGE: {\n        set(draftState, ['modifiedData', ...action.keys.split('.')], action.value);\n        break;\n      }\n      case SET_LOADED: {\n        // This action re-initialises the state using the current modifiedData.\n        const reInitialise = init(get(draftState, ['modifiedData'], {}));\n        draftState.initialData = reInitialise.initialData;\n        draftState.modifiedData = reInitialise.modifiedData;\n        break;\n      }\n      default:\n        return draftState;\n    }\n  });\n\nexport default reducer;\n", "import React, { useReducer, useState } from 'react';\n\nimport {\n  ConfirmDialog,\n  useTracking,\n  useNotification,\n  Page,\n  Layouts,\n} from '@strapi/admin/strapi-admin';\nimport { But<PERSON>, Dialog, Link } from '@strapi/design-system';\nimport { ArrowLeft, Check } from '@strapi/icons';\nimport isEqual from 'lodash/isEqual';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\nimport { NavLink } from 'react-router-dom';\n\nimport { useConfig } from '../../../hooks/useConfig';\nimport pluginID from '../../../pluginId';\nimport { getTrad } from '../../../utils';\n\nimport { Settings } from './components/Settings';\nimport { onChange, setLoaded } from './state/actions';\nimport { init, initialState } from './state/init';\nimport reducer from './state/reducer';\n\nconst ConfigureTheView = ({ config }) => {\n  const { trackUsage } = useTracking();\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const { mutateConfig } = useConfig();\n  const { isLoading: isSubmittingForm } = mutateConfig;\n\n  const [showWarningSubmit, setWarningSubmit] = useState(false);\n  const toggleWarningSubmit = () => setWarningSubmit((prevState) => !prevState);\n\n  const [reducerState, dispatch] = useReducer(reducer, initialState, () => init(config));\n  const { initialData, modifiedData } = reducerState;\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    toggleWarningSubmit();\n  };\n\n  const handleConfirm = async () => {\n    trackUsage('willEditMediaLibraryConfig');\n    await mutateConfig.mutateAsync(modifiedData);\n    setWarningSubmit(false);\n    dispatch(setLoaded());\n    toggleNotification({\n      type: 'success',\n      message: formatMessage({\n        id: 'notification.form.success.fields',\n        defaultMessage: 'Changes saved',\n      }),\n    });\n  };\n\n  const handleChange = ({ target: { name, value } }) => {\n    dispatch(onChange({ name, value }));\n  };\n\n  return (\n    <Layouts.Root>\n      <Page.Main aria-busy={isSubmittingForm}>\n        <form onSubmit={handleSubmit}>\n          <Layouts.Header\n            navigationAction={\n              <Link\n                tag={NavLink}\n                startIcon={<ArrowLeft />}\n                to={`/plugins/${pluginID}`}\n                id=\"go-back\"\n              >\n                {formatMessage({ id: getTrad('config.back'), defaultMessage: 'Back' })}\n              </Link>\n            }\n            primaryAction={\n              <Button\n                size=\"S\"\n                startIcon={<Check />}\n                disabled={isEqual(modifiedData, initialData)}\n                type=\"submit\"\n              >\n                {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n              </Button>\n            }\n            subtitle={formatMessage({\n              id: getTrad('config.subtitle'),\n              defaultMessage: 'Define the view settings of the media library.',\n            })}\n            title={formatMessage({\n              id: getTrad('config.title'),\n              defaultMessage: 'Configure the view - Media Library',\n            })}\n          />\n\n          <Layouts.Content>\n            <Settings\n              data-testid=\"settings\"\n              pageSize={modifiedData.pageSize || ''}\n              sort={modifiedData.sort || ''}\n              onChange={handleChange}\n            />\n          </Layouts.Content>\n\n          <Dialog.Root open={showWarningSubmit} onOpenChange={toggleWarningSubmit}>\n            <ConfirmDialog onConfirm={handleConfirm} variant=\"default\">\n              {formatMessage({\n                id: getTrad('config.popUpWarning.warning.updateAllSettings'),\n                defaultMessage: 'This will modify all your settings',\n              })}\n            </ConfirmDialog>\n          </Dialog.Root>\n        </form>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\nConfigureTheView.propTypes = {\n  config: PropTypes.shape({\n    pageSize: PropTypes.number,\n    sort: PropTypes.string,\n  }).isRequired,\n};\n\nexport default ConfigureTheView;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASA,IAAM,WAAW,CAAC,EAAE,OAAO,IAAI,WAAW,IAAI,UAAAA,UAAAA,MAAe;AACrD,QAAA,EAAE,cAAA,IAAkB,QAAA;AAGxB,aAAA;IAAC;IAAA;MACC,YAAW;MACX,WAAS;MACT,QAAO;MACP,YAAY;MACZ,eAAe;MACf,aAAa;MACb,cAAc;MAEd,cAAC,yBAAA,KAAK,MAAL,EAAU,KAAK,GACd,UAAA;YAAC,wBAAA,KAAK,MAAL,EAAU,GAAG,IAAI,KAAK,GAAG,WAAU,UAAS,YAAW,WACtD,cAAA;UAAC,MAAM;UAAN;YACC,MAAM,cAAc;cAClB,IAAI,QAAQ,qBAAqB;cACjC,gBAAgB;YAAA,CACjB;YACD,MAAK;YAEL,UAAA;kBAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;gBACb,IAAI,QAAQ,sBAAsB;gBAClC,gBAAgB;cACjB,CAAA,EAAA,CACH;kBACA;gBAAC;gBAAA;kBACC,UAAU,CAAC,UAAUA,UAAS,EAAE,QAAQ,EAAE,MAAM,YAAY,MAAM,EAAA,CAAG;kBACrE,OAAO;kBAEN,UAAA,UAAU,IAAI,CAACC,kBACd,wBAAC,oBAAkC,EAAA,OAAOA,WACvC,UAAAA,UADsBA,GAAAA,SAEzB,CACD;gBAAA;cACH;kBACA,wBAAC,MAAM,MAAN,CAAA,CAAW;YAAA;UAAA;QAAA,EAAA,CAEhB;YACA,wBAAC,KAAK,MAAL,EAAU,GAAG,IAAI,KAAK,GAAG,WAAU,UAAS,YAAW,WACtD,cAAA;UAAC,MAAM;UAAN;YACC,MAAM,cAAc;cAClB,IAAI,QAAQ,aAAa;cACzB,gBAAgB;YAAA,CACjB;YACD,MAAK;YAEL,UAAA;kBAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;gBACb,IAAI,QAAQ,mBAAmB;gBAC/B,gBAAgB;cACjB,CAAA,EAAA,CACH;kBACA;gBAAC;gBAAA;kBACC,UAAU,CAAC,UAAUD,UAAS,EAAE,QAAQ,EAAE,MAAM,QAAQ,MAAM,EAAA,CAAG;kBACjE,OAAO;kBACP,aAAW;kBACX,eAAY;kBAEX,UAAA,YAAY,IAAI,CAAC,eAChB;oBAAC;oBAAA;sBACC,eAAa,eAAe,OAAO,KAAK;sBAExC,OAAO,OAAO;sBAEb,UAAc,cAAA,EAAE,IAAI,QAAQ,OAAO,GAAG,GAAG,gBAAgB,GAAG,OAAO,KAAK,GAAA,CAAI;oBAAA;oBAHxE,OAAO;kBAAA,CAKf;gBAAA;cACH;kBACA,wBAAC,MAAM,MAAN,CAAA,CAAW;YAAA;UAAA;QAAA,EAAA,CAEhB;MAAA,EAAA,CACF;IAAA;EAAA;AAGN;AAEA,SAAS,YAAY;EACnB,MAAM,kBAAAE,QAAU,OAAO;EACvB,UAAU,kBAAAA,QAAU,OAAO;EAC3B,UAAU,kBAAAA,QAAU,KAAK;AAC3B;AC1FO,IAAM,YAAY,GAAG,QAAQ;AAC7B,IAAM,aAAa,GAAG,QAAQ;ACD9B,IAAM,WAAW,CAAC,EAAE,MAAM,MAAA,OAAa;EAC5C,MAAM;EACN,MAAM;EACN;AACF;AAEO,IAAM,YAAY,OAAO;EAC9B,MAAM;AACR;ACVA,IAAM,eAAe;EACnB,aAAa,CAAE;EACf,cAAc,CAAE;AAClB;AAEA,IAAM,OAAO,CAAC,eAAe;AAC3B,SAAO;IACL,GAAG;IACH,aAAa;IACb,cAAc;EAClB;AACA;ACJA,IAAM,UAAU,CACd,QAAQ,cACR,SAAS;EACP,MAAM;AACP;;EAGD,GAAQ,OAAO,CAAC,eAAe;AAC7B,YAAQ,OAAO,MAAI;MACjB,KAAK,WAAW;AACd,uBAAAC,SAAI,YAAY,CAAC,gBAAgB,GAAG,OAAO,KAAK,MAAM,GAAG,CAAC,GAAG,OAAO,KAAK;AACzE;MACD;MACD,KAAK,YAAY;AAEf,cAAM,eAAe,SAAK,WAAAC,SAAI,YAAY,CAAC,cAAc,GAAG,CAAE,CAAA,CAAC;AAC/D,mBAAW,cAAc,aAAa;AACtC,mBAAW,eAAe,aAAa;AACvC;MACD;MACD;AACE,eAAO;IACV;EACL,CAAG;;ACLH,IAAM,mBAAmB,CAAC,EAAE,OAAA,MAAa;AACjC,QAAA,EAAE,WAAA,IAAe,YAAA;AACjB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA,EAAE,aAAA,IAAiB,UAAA;AACnB,QAAA,EAAE,WAAW,iBAAqB,IAAA;AAExC,QAAM,CAAC,mBAAmB,gBAAgB,QAAI,uBAAS,KAAK;AAC5D,QAAM,sBAAsB,MAAM,iBAAiB,CAAC,cAAc,CAAC,SAAS;AAEtE,QAAA,CAAC,cAAc,QAAQ,QAAI,yBAAW,SAAS,cAAc,MAAM,KAAK,MAAM,CAAC;AAC/E,QAAA,EAAE,aAAa,aAAiB,IAAA;AAEhC,QAAA,eAAe,CAAC,MAAM;AAC1B,MAAE,eAAe;AACG,wBAAA;EAAA;AAGtB,QAAM,gBAAgB,YAAY;AAChC,eAAW,4BAA4B;AACjC,UAAA,aAAa,YAAY,YAAY;AAC3C,qBAAiB,KAAK;AACtB,aAAS,UAAA,CAAW;AACD,uBAAA;MACjB,MAAM;MACN,SAAS,cAAc;QACrB,IAAI;QACJ,gBAAgB;MAAA,CACjB;IAAA,CACF;EAAA;AAGG,QAAA,eAAe,CAAC,EAAE,QAAQ,EAAE,MAAM,MAAA,EAAA,MAAc;AACpD,aAAS,SAAS,EAAE,MAAM,MAAA,CAAO,CAAC;EAAA;AAGpC,aACG,wBAAA,QAAQ,MAAR,EACC,cAAC,wBAAA,KAAK,MAAL,EAAU,aAAW,kBACpB,cAAC,yBAAA,QAAA,EAAK,UAAU,cACd,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,sBACE;UAAC;UAAA;YACC,KAAK;YACL,eAAA,wBAAY,eAAU,CAAA,CAAA;YACtB,IAAI,YAAYC,QAAQ;YACxB,IAAG;YAEF,UAAA,cAAc,EAAE,IAAI,QAAQ,aAAa,GAAG,gBAAgB,OAAA,CAAQ;UAAA;QACvE;QAEF,mBACE;UAAC;UAAA;YACC,MAAK;YACL,eAAA,wBAAY,eAAM,CAAA,CAAA;YAClB,cAAU,eAAAC,SAAQ,cAAc,WAAW;YAC3C,MAAK;YAEJ,UAAA,cAAc,EAAE,IAAI,eAAe,gBAAgB,OAAA,CAAQ;UAAA;QAC9D;QAEF,UAAU,cAAc;UACtB,IAAI,QAAQ,iBAAiB;UAC7B,gBAAgB;QAAA,CACjB;QACD,OAAO,cAAc;UACnB,IAAI,QAAQ,cAAc;UAC1B,gBAAgB;QAAA,CACjB;MAAA;IACH;QAEA,wBAAC,QAAQ,SAAR,EACC,cAAA;MAAC;MAAA;QACC,eAAY;QACZ,UAAU,aAAa,YAAY;QACnC,MAAM,aAAa,QAAQ;QAC3B,UAAU;MAAA;IAAA,EAAA,CAEd;QAEC,wBAAA,OAAO,MAAP,EAAY,MAAM,mBAAmB,cAAc,qBAClD,cAAA,wBAAC,eAAc,EAAA,WAAW,eAAe,SAAQ,WAC9C,UAAc,cAAA;MACb,IAAI,QAAQ,+CAA+C;MAC3D,gBAAgB;IAAA,CACjB,EAAA,CACH,EACF,CAAA;EAAA,EAAA,CACF,EAAA,CACF,EACF,CAAA;AAEJ;AAEA,iBAAiB,YAAY;EAC3B,QAAQ,kBAAAJ,QAAU,MAAM;IACtB,UAAU,kBAAAA,QAAU;IACpB,MAAM,kBAAAA,QAAU;EACjB,CAAA,EAAE;AACL;", "names": ["onChange", "pageSize", "PropTypes", "set", "get", "pluginID", "isEqual"]}