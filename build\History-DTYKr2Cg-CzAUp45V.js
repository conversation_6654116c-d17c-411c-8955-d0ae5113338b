import{cB as pe,m as e,cC as be,cD as xe,b9 as W,e as ee,cE as te,n as A,J as b,cF as je,r as R,cG as w,cH as ve,cI as Ce,cJ as Ie,ay as z,cK as Me,cL as Te,cM as Q,aV as G,Y as _,_ as Z,bl as N,w as h,aX as we,al as Fe,a as Le,b5 as Y,L as ne,A as J,Z as Ae,s as p,b6 as Re,ai as Ve,cN as se,W as ke,O as q,G as B,bF as De,cO as P,c9 as ae,cP as Be,x as O,S as ie,a0 as Pe,Q as H,aC as re,aT as Se,cQ as ze,aS as Ne,aU as oe,cR as Ee,cS as He}from"./strapi-YzJfjJ2z.js";import{u as de}from"./hooks-E5u1mcgM-Ce79Lxm5.js";import{r as $e,p as qe,u as Oe,a as Ue,b as We,N as Qe,M as _e,c as Ze,D as Ge,d as Ye,e as Je}from"./Field-B2C1tq9_-CuRzLj73.js";import{g as Ke}from"./relations-sRERvWmr-drnKn_J5.js";import"./Relations-7ItTFWp7-CaFM264H.js";import"./useDragAndDrop-DdHgKsqq-DDLMDz6i.js";import"./getEmptyImage-CjqolaH3.js";import"./ComponentIcon-u4bIXTFY-BYfvWXgU.js";import"./objects-D6yBsdmx-8Q5QgcI9.js";import"./useDebounce-DmuSJIF3-BaTG1oIL.js";const S=ie(Pe).attrs({closeLabel:"Close",onClose:()=>{},shadow:"none"})`
  button {
    display: none;
  }
`,Xe=ie(_)`
  display: block;

  & > span {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    display: block;
  }
`,et=n=>{const{formatMessage:s}=w(),a=re(n.name);let i;if(a&&(i=Array.isArray(a.value)?{results:a.value,meta:{missingCount:0}}:a.value),!i||i.results.length===0&&i.meta.missingCount===0)return e.jsxs(e.Fragment,{children:[e.jsx(O.Label,{action:n.labelAction,children:n.label}),e.jsx(b,{marginTop:1,children:e.jsx(S,{variant:"default",children:s({id:"content-manager.history.content.no-relations",defaultMessage:"No relations."})})})]});const{results:t,meta:r}=i;return e.jsxs(b,{children:[e.jsx(O.Label,{children:n.label}),t.length>0&&e.jsx(h,{direction:"column",gap:2,marginTop:1,alignItems:"stretch",children:t.map(d=>{const{targetModel:u}=n.attribute,y=`../${Q}/${u}/${d.documentId}`,f=Ke(d,n.mainField),v=u==="admin::user";return e.jsxs(h,{paddingTop:2,paddingBottom:2,paddingLeft:4,paddingRight:4,hasRadius:!0,borderColor:"neutral200",background:"neutral150",justifyContent:"space-between",children:[e.jsx(b,{minWidth:0,paddingTop:1,paddingBottom:1,paddingRight:4,children:e.jsx(Se,{label:f,children:v?e.jsx(p,{children:f}):e.jsx(Xe,{tag:Z,to:y,children:f})})}),e.jsx(ze,{status:d.status})]},d.documentId??d.id)})}),r.missingCount>0&&e.jsx(S,{marginTop:1,variant:"warning",title:s({id:"content-manager.history.content.missing-relations.title",defaultMessage:"{number, plural, =1 {Missing relation} other {{number} missing relations}}"},{number:r.missingCount}),children:s({id:"content-manager.history.content.missing-relations.message",defaultMessage:"{number, plural, =1 {It has} other {They have}} been deleted and can't be restored."},{number:r.missingCount})})]})},tt=n=>{const{value:s}=re(n.name),a=s?s.results:[],i=s?s.meta:{missingCount:0},{formatMessage:t}=w(),d=ae("CustomMediaInput",u=>u.fields).media;return e.jsxs(h,{direction:"column",gap:2,alignItems:"stretch",children:[e.jsx(q,{method:"PUT",disabled:!0,initialValues:{[n.name]:a},children:e.jsx(d,{...n,disabled:!0,multiple:a.length>1})}),i.missingCount>0&&e.jsx(S,{variant:"warning",closeLabel:"Close",onClose:()=>{},title:t({id:"content-manager.history.content.missing-assets.title",defaultMessage:"{number, plural, =1 {Missing asset} other {{number} missing assets}}"},{number:i.missingCount}),children:t({id:"content-manager.history.content.missing-assets.message",defaultMessage:"{number, plural, =1 {It has} other {They have}} been deleted in the Media Library and can't be restored."},{number:i.missingCount})})]})},nt=n=>{if(!R.isValidElement(n))return n;const s=n.props.title.id;return s==="i18n.Field.localized"?R.cloneElement(n,{...n.props,title:{id:"history.content.localized",defaultMessage:"This value is specific to this locale. If you restore this version, the content will not be replaced for other locales."}}):s==="i18n.Field.not-localized"?R.cloneElement(n,{...n.props,title:{id:"history.content.not-localized",defaultMessage:"This value is common to all locales. If you restore this version and save the changes, the content will be replaced for all locales."}}):n},U=({visible:n,hint:s,shouldIgnoreRBAC:a=!1,labelAction:i,...t})=>{const r=nt(i),{formatMessage:d}=w(),u=C("VersionContent",o=>o.selectedVersion),y=C("VersionContent",o=>o.configuration),f=de(o=>o["content-manager"].app.fieldSizes),{id:v,components:T}=se(),l=De("InputRenderer",o=>o.disabled),c=Oe("isInDynamicZone",o=>o.isInDynamicZone),I=P("InputRenderer",o=>o.canCreateFields),F=P("InputRenderer",o=>o.canReadFields),M=P("InputRenderer",o=>o.canUpdateFields),L=P("InputRenderer",o=>o.canUserAction),g=v?M:I,V=v?F:I,m=L(t.name,V,t.type),k=L(t.name,g,t.type),D=ae("InputRenderer",o=>o.fields),{lazyComponentStore:ue}=Ue($(t.attribute)?[t.attribute.customField]:void 0),x=We(s,t.attribute),{edit:{components:me}}=Be();if(!n)return null;if(!a&&!m&&!c)return e.jsx(Qe,{hint:x,...t});const j=!k&&!c||t.disabled||l,ge=u.meta.unknownAttributes.added;if(Object.keys(ge).includes(t.name))return e.jsxs(h,{direction:"column",alignItems:"flex-start",gap:1,children:[e.jsx(O.Label,{children:t.label}),e.jsx(S,{width:"100%",closeLabel:"Close",onClose:()=>{},variant:"warning",title:d({id:"content-manager.history.content.new-field.title",defaultMessage:"New field"}),children:d({id:"content-manager.history.content.new-field.message",defaultMessage:"This field didn't exist when this version was saved. If you restore this version, it will be empty."})})]});if($(t.attribute)){const o=ue[t.attribute.customField];return o?e.jsx(o,{...t,hint:x,labelAction:r,disabled:j}):e.jsx(H,{...t,hint:x,labelAction:r,type:t.attribute.customField,disabled:j})}if(t.type==="media")return e.jsx(tt,{...t,labelAction:r,disabled:j});const he=Object.keys(D);if(!$(t.attribute)&&he.includes(t.type)){const o=D[t.type];return e.jsx(o,{...t,hint:x,labelAction:r,disabled:j})}switch(t.type){case"blocks":return e.jsx(Je,{...t,hint:x,type:t.type,disabled:j});case"component":const{layout:o}=me[t.attribute.component],[ye]=ce({layout:[o],metadatas:y.components[t.attribute.component].metadatas,fieldSizes:f,schemaAttributes:T[t.attribute.component].attributes});return e.jsx(Ye,{...t,layout:[...o,...ye||[]],hint:x,labelAction:r,disabled:j,children:E=>e.jsx(U,{...E,shouldIgnoreRBAC:!0})});case"dynamiczone":return e.jsx(Ge,{...t,hint:x,labelAction:r,disabled:j});case"relation":return e.jsx(et,{...t,hint:x,labelAction:r,disabled:j});case"richtext":return e.jsx(Ze,{...t,hint:x,type:t.type,labelAction:r,disabled:j});case"uid":return e.jsx(_e,{...t,hint:x,type:t.type,labelAction:r,disabled:j});case"enumeration":return e.jsx(H,{...t,hint:x,labelAction:r,options:t.attribute.enum.map(E=>({value:E})),type:t.customField?"custom-field":t.type,disabled:j});default:const{unique:ht,mainField:yt,...fe}=t;return e.jsx(H,{...fe,hint:x,labelAction:r,type:t.customField?"custom-field":t.type,disabled:j})}},$=n=>"customField"in n&&typeof n.customField=="string",le=n=>n.reduce((s,a)=>a.type==="dynamiczone"?(s.push([a]),s):(s[s.length-1]||s.push([]),s[s.length-1].push(a),s),[]).map(s=>[s]);function ce({layout:n,metadatas:s,schemaAttributes:a,fieldSizes:i}){const t=n.flatMap(d=>d.flatMap(u=>u.flatMap(y=>y.name))),r=Object.entries(s).reduce((d,[u,y])=>{if(!t.includes(u)&&y.edit.visible===!0){const f=a[u];d.push({attribute:f,type:f.type,visible:!0,disabled:!0,label:y.edit.label||u,name:u,size:i[f.type].default??12})}return d},[]);return le(r)}const K=({panel:n})=>{if(n.some(s=>s.some(a=>a.type==="dynamiczone"))){const[s]=n,[a]=s;return e.jsx(B.Root,{gap:4,children:e.jsx(B.Item,{col:12,s:12,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(U,{...a})})},a.name)}return e.jsx(b,{hasRadius:!0,background:"neutral0",shadow:"tableShadow",paddingLeft:6,paddingRight:6,paddingTop:6,paddingBottom:6,borderColor:"neutral150",children:e.jsx(h,{direction:"column",alignItems:"stretch",gap:6,children:n.map((s,a)=>e.jsx(B.Root,{gap:4,children:s.map(({size:i,...t})=>e.jsx(B.Item,{col:i,s:12,xs:12,direction:"column",alignItems:"stretch",children:e.jsx(U,{...t})},t.name))},a))})})},st=()=>{const{formatMessage:n}=w(),{fieldSizes:s}=de(l=>l["content-manager"].app),a=C("VersionContent",l=>l.selectedVersion),i=C("VersionContent",l=>l.layout),t=C("VersionContent",l=>l.configuration),r=C("VersionContent",l=>l.schema),d=a.meta.unknownAttributes.removed,u=Object.entries(d).map(([l,c])=>({attribute:c,shouldIgnoreRBAC:!0,type:c.type,visible:!0,disabled:!0,label:l,name:l,size:s[c.type].default??12})),y=le(u),f=ce({metadatas:t.contentType.metadatas,layout:i,schemaAttributes:r.attributes,fieldSizes:s}),{components:v}=se(),T=R.useMemo(()=>((c,I={})=>F=>{const M={attributes:c};return ke($e(M),qe(M,I))(F)})(a.schema,v)(a.data),[v,a.data,a.schema]);return e.jsxs(ne.Content,{children:[e.jsx(b,{paddingBottom:8,children:e.jsx(q,{disabled:!0,method:"PUT",initialValues:T,children:e.jsx(h,{direction:"column",alignItems:"stretch",gap:6,position:"relative",children:[...i,...f].map((l,c)=>e.jsx(K,{panel:l},c))})})}),u.length>0&&e.jsxs(e.Fragment,{children:[e.jsx(Ne,{}),e.jsxs(b,{paddingTop:8,children:[e.jsxs(h,{direction:"column",alignItems:"flex-start",paddingBottom:6,gap:1,children:[e.jsx(p,{variant:"delta",children:n({id:"content-manager.history.content.unknown-fields.title",defaultMessage:"Unknown fields"})}),e.jsx(p,{variant:"pi",children:n({id:"content-manager.history.content.unknown-fields.message",defaultMessage:"These fields have been deleted or renamed in the Content-Type Builder. <b>These fields will not be restored.</b>"},{b:l=>e.jsx(p,{variant:"pi",fontWeight:"bold",children:l})})})]}),e.jsx(q,{disabled:!0,method:"PUT",initialValues:a.data,children:e.jsx(h,{direction:"column",alignItems:"stretch",gap:6,position:"relative",children:y.map((l,c)=>e.jsx(K,{panel:l},c))})})]})]})]})},at=Te.injectEndpoints({endpoints:n=>({getHistoryVersions:n.query({query(s){return{url:"/content-manager/history-versions",method:"GET",config:{params:s}}},providesTags:["HistoryVersion"]}),restoreVersion:n.mutation({query({params:s,body:a}){return{url:`/content-manager/history-versions/${s.versionId}/restore`,method:"PUT",data:a}},invalidatesTags:(s,a,{documentId:i,collectionType:t,params:r})=>["HistoryVersion",{type:"Document",id:t===Q?`${r.contentType}_${i}`:r.contentType}]})})}),{useGetHistoryVersionsQuery:it,useRestoreVersionMutation:rt}=at,ot=({headerId:n})=>{const[s,a]=R.useState(!1),i=Fe(),{formatMessage:t,formatDate:r}=w(),{toggleNotification:d}=Le(),[{query:u}]=z(),{collectionType:y,slug:f}=W(),[v,{isLoading:T}]=rt(),{allowedActions:l}=ee(te.map(m=>({action:m,subject:f}))),c=C("VersionHeader",m=>m.selectedVersion),I=C("VersionHeader",m=>m.mainField),F=C("VersionHeader",m=>m.schema),M=C("VersionHeader",m=>m.page===1&&m.versions.data[0].id===m.selectedVersion.id),L=c.data[I],g=()=>({pathname:"..",search:N.stringify({plugins:u.plugins},{encode:!1})}),V=async()=>{try{const m=await v({documentId:c.relatedDocumentId,collectionType:y,params:{versionId:c.id,contentType:c.contentType},body:{contentType:c.contentType}});"data"in m&&(i(g(),{relative:"path"}),d({type:"success",title:t({id:"content-manager.restore.success.title",defaultMessage:"Version restored."}),message:t({id:"content-manager.restore.success.message",defaultMessage:"The content of the restored version is not published yet."})})),"error"in m&&d({type:"danger",message:t({id:"content-manager.history.restore.error.message",defaultMessage:"Could not restore version."})})}catch{d({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred"})})}};return e.jsxs(Y.Root,{open:s,onOpenChange:a,children:[e.jsx(ne.BaseHeader,{id:n,title:r(new Date(c.createdAt),{year:"numeric",month:"numeric",day:"numeric",hour:"numeric",minute:"numeric"}),subtitle:e.jsx(p,{variant:"epsilon",textColor:"neutral600",children:t({id:"content-manager.history.version.subtitle",defaultMessage:"{hasLocale, select, true {{subtitle}, in {locale}} other {{subtitle}}}"},{hasLocale:!!c.locale,subtitle:`${L||""} (${F.info.singularName})`.trim(),locale:c.locale?.name})}),navigationAction:e.jsx(_,{startIcon:e.jsx(Ae,{}),tag:Z,to:g(),relative:"path",isExternal:!1,children:t({id:"global.back",defaultMessage:"Back"})}),sticky:!1,primaryAction:e.jsx(Y.Trigger,{children:e.jsx(J,{disabled:!l.canUpdate||M,onClick:()=>{a(!0)},children:t({id:"content-manager.history.restore.confirm.button",defaultMessage:"Restore"})})})}),e.jsx(Re,{onConfirm:V,endAction:e.jsx(J,{variant:"secondary",onClick:V,loading:T,children:t({id:"content-manager.history.restore.confirm.button",defaultMessage:"Restore"})}),children:e.jsxs(h,{direction:"column",alignItems:"center",justifyContent:"center",gap:2,textAlign:"center",children:[e.jsx(h,{justifyContent:"center",children:e.jsx(Ve,{width:"24px",height:"24px",fill:"danger600"})}),e.jsx(p,{children:t({id:"content-manager.history.restore.confirm.title",defaultMessage:"Are you sure you want to restore this version?"})}),e.jsx(p,{children:t({id:"content-manager.history.restore.confirm.message",defaultMessage:"{isDraft, select, true {The restored content will override your draft.} other {The restored content won't be published, it will override the draft and be saved as pending changes. You'll be able to publish the changes at anytime.}}"},{isDraft:c.status==="draft"})})]})})]})},dt=n=>e.jsx(p,{textColor:"primary600",variant:"pi",children:n}),lt=({version:n,isCurrent:s})=>{const{formatDate:a,formatMessage:i}=w(),[{query:t}]=z(),r=(()=>{switch(n.status){case"draft":return{background:"secondary100",border:"secondary200",text:"secondary700",message:{id:"content-manager.containers.List.draft",defaultMessage:"Draft"}};case"modified":return{background:"alternative100",border:"alternative200",text:"alternative700",message:{id:"content-manager.containers.List.modified",defaultMessage:"Modified"}};case"published":default:return{background:"success100",border:"success200",text:"success700",message:{id:"content-manager.containers.List.published",defaultMessage:"Published"}}}})(),d=t.id===n.id.toString(),u=n.createdBy&&Ee(n.createdBy);return e.jsxs(h,{direction:"column",alignItems:"flex-start",gap:3,hasRadius:!0,borderWidth:"1px",borderStyle:"solid",borderColor:d?"primary600":"neutral200",color:"neutral800",padding:5,tag:oe,to:`?${N.stringify({...t,id:n.id})}`,style:{textDecoration:"none"},children:[e.jsxs(h,{direction:"column",gap:1,alignItems:"flex-start",children:[e.jsx(p,{tag:"h3",fontWeight:"semiBold",children:a(n.createdAt,{day:"numeric",month:"numeric",year:"numeric",hour:"2-digit",minute:"2-digit"})}),e.jsx(p,{tag:"p",variant:"pi",textColor:"neutral600",children:i({id:"content-manager.history.sidebar.versionDescription",defaultMessage:"{distanceToNow}{isAnonymous, select, true {} other { by {author}}}{isCurrent, select, true { <b>(current)</b>} other {}}"},{distanceToNow:e.jsx(He,{timestamp:new Date(n.createdAt)}),author:u,isAnonymous:!n.createdBy,isCurrent:s,b:dt})})]}),n.status&&e.jsx(b,{background:r.background,borderStyle:"solid",borderWidth:"1px",borderColor:r.border,hasRadius:!0,paddingLeft:"6px",paddingRight:"6px",paddingTop:"2px",paddingBottom:"2px",children:e.jsx(p,{variant:"pi",fontWeight:"bold",textColor:r.text,children:i(r.message)})})]})},X=({page:n,children:s})=>{const[{query:a}]=z(),{id:i,...t}=a;return e.jsx(oe,{to:{search:N.stringify({...t,page:n})},style:{textDecoration:"none"},children:e.jsx(p,{variant:"omega",textColor:"primary600",children:s})})},ct=()=>{const{formatMessage:n}=w(),{versions:s,page:a}=C("VersionsList",i=>({versions:i.versions,page:i.page}));return e.jsxs(h,{shrink:0,direction:"column",alignItems:"stretch",width:"320px",height:"100vh",background:"neutral0",borderColor:"neutral200",borderWidth:"0 0 0 1px",borderStyle:"solid",tag:"aside",children:[e.jsxs(h,{direction:"row",justifyContent:"space-between",padding:4,borderColor:"neutral200",borderWidth:"0 0 1px",borderStyle:"solid",tag:"header",children:[e.jsx(p,{tag:"h2",variant:"omega",fontWeight:"semiBold",children:n({id:"content-manager.history.sidebar.title",defaultMessage:"Versions"})}),e.jsx(b,{background:"neutral150",hasRadius:!0,padding:1,children:e.jsx(p,{variant:"sigma",textColor:"neutral600",children:s.meta.pagination.total})})]}),e.jsxs(b,{flex:1,overflow:"auto",children:[s.meta.pagination.page>1&&e.jsx(b,{paddingTop:4,textAlign:"center",children:e.jsx(X,{page:a-1,children:n({id:"content-manager.history.sidebar.show-newer",defaultMessage:"Show newer versions"})})}),e.jsx(h,{direction:"column",gap:3,padding:4,tag:"ul",alignItems:"stretch",children:s.data.map((i,t)=>e.jsx("li",{"aria-label":n({id:"content-manager.history.sidebar.title.version-card.aria-label",defaultMessage:"Version card"}),children:e.jsx(lt,{version:i,isCurrent:a===1&&t===0})},i.id))}),s.meta.pagination.page<s.meta.pagination.pageCount&&e.jsx(b,{paddingBottom:4,textAlign:"center",children:e.jsx(X,{page:a+1,children:n({id:"content-manager.history.sidebar.show-older",defaultMessage:"Show older versions"})})})]})]})},[ut,C]=pe("HistoryPage"),mt=()=>{const n=R.useId(),{formatMessage:s}=w(),{slug:a,id:i,collectionType:t}=W(),{isLoading:r,schema:d}=ve({collectionType:t,model:a}),{isLoading:u,edit:{layout:y,settings:{displayName:f,mainField:v}}}=Ce(a),{data:T,isLoading:l}=Ie(a),[{query:c}]=z(),{id:I,...F}=c,M=Me(F),L=M.page?Number(M.page):1,g=it({contentType:a,...i?{documentId:i}:{},...M},{refetchOnMountOrArgChange:!0}),V=R.useRef(g.requestId),m=g.requestId===V.current;if(!a||t===Q&&!i)return e.jsx(G,{to:"/content-manager"});if(r||u||g.isFetching||m||l)return e.jsx(A.Loading,{});if(!g.isError&&!g.data?.data?.length)return e.jsx(e.Fragment,{children:e.jsx(A.NoData,{action:e.jsx(_,{tag:Z,to:`/content-manager/${t}/${a}${i?`/${i}`:""}`,children:s({id:"global.back",defaultMessage:"Back"})})})});if(g.data?.data?.length&&!I)return e.jsx(G,{to:{search:N.stringify({...c,id:g.data.data[0].id})},replace:!0});const k=g.data?.data?.find(D=>D.id.toString()===I);return g.isError||!y||!d||!k||!T||g.data.error?e.jsx(A.Error,{}):e.jsxs(e.Fragment,{children:[e.jsx(A.Title,{children:s({id:"content-manager.history.page-title",defaultMessage:"{contentType} history"},{contentType:f})}),e.jsx(ut,{contentType:a,id:i,schema:d,layout:y,configuration:T,selectedVersion:k,versions:g.data,page:L,mainField:v,children:e.jsxs(h,{direction:"row",alignItems:"flex-start",children:[e.jsxs(we,{grow:1,height:"100vh",background:"neutral100",paddingBottom:6,overflow:"auto",labelledBy:n,children:[e.jsx(ot,{headerId:n}),e.jsx(st,{})]}),e.jsx(ct,{})]})})]})},gt=()=>{const{slug:n}=W(),{permissions:s=[],isLoading:a,error:i}=ee(te.map(t=>({action:t,subject:n})));return a?e.jsx(A.Loading,{}):i||!n?e.jsx(b,{height:"100vh",width:"100vw",position:"fixed",top:0,left:0,zIndex:2,background:"neutral0",children:e.jsx(A.Error,{})}):e.jsx(b,{height:"100vh",width:"100vw",position:"fixed",top:0,left:0,zIndex:2,background:"neutral0",children:e.jsx(A.Protect,{permissions:s,children:({permissions:t})=>e.jsx(je,{permissions:t,children:e.jsx(mt,{})})})})},wt=()=>e.jsx(be,{children:e.jsx(xe,{children:e.jsx(gt,{})})});export{ut as HistoryProvider,wt as ProtectedHistoryPage,C as useHistoryContext};
