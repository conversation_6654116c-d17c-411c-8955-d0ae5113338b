{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/services/admin.ts", "../../../@strapi/review-workflows/admin/src/routes/settings/hooks/useKeyboardDragAndDrop.ts", "../../../@strapi/review-workflows/admin/src/routes/settings/hooks/useDragAndDrop.ts", "../../../@strapi/review-workflows/admin/src/routes/settings/components/AddStage.tsx", "../../../@strapi/review-workflows/admin/src/routes/settings/components/Stages.tsx", "../../../@strapi/review-workflows/admin/src/routes/settings/components/WorkflowAttributes.tsx", "../../../@strapi/review-workflows/admin/src/routes/settings/id.tsx"], "sourcesContent": ["import { SanitizedAdminUser } from '@strapi/admin/strapi-admin';\n\nimport { reviewWorkflowsApi } from './api';\n\ntype Roles = SanitizedAdminUser['roles'];\ntype RolesResponse = { data: Roles };\n\nconst adminApi = reviewWorkflowsApi.injectEndpoints({\n  endpoints(builder) {\n    return {\n      getAdminRoles: builder.query<Roles, void>({\n        query: () => ({\n          url: `/admin/roles`,\n          method: 'GET',\n        }),\n        transformResponse: (res: RolesResponse) => {\n          return res.data;\n        },\n      }),\n    };\n  },\n});\n\nconst { useGetAdminRolesQuery } = adminApi;\n\nexport { useGetAdminRolesQuery };\nexport type { SanitizedAdminUser, Roles };\n", "import * as React from 'react';\n\nexport type UseKeyboardDragAndDropCallbacks<TIndex extends number | Array<number> = number> = {\n  onCancel?: (index: TIndex) => void;\n  onDropItem?: (currentIndex: TIndex, newIndex?: TIndex) => void;\n  onGrabItem?: (index: TIndex) => void;\n  onMoveItem?: (newIndex: TIndex, currentIndex: TIndex) => void;\n};\n\n/**\n * Utility hook designed to implement keyboard accessibile drag and drop by\n * returning an onKeyDown handler to be passed to the drag icon button.\n *\n * @internal - You should use `useDragAndDrop` instead.\n */\nexport const useKeyboardDragAndDrop = <TIndex extends number | Array<number> = number>(\n  active: boolean,\n  index: TIndex,\n  { onCancel, onDropItem, onGrabItem, onMoveItem }: UseKeyboardDragAndDropCallbacks<TIndex>\n) => {\n  const [isSelected, setIsSelected] = React.useState(false);\n\n  const handleMove = (movement: 'UP' | 'DOWN') => {\n    if (!isSelected) {\n      return;\n    }\n    if (typeof index === 'number' && onMoveItem) {\n      if (movement === 'UP') {\n        onMoveItem((index - 1) as TIndex, index);\n      } else if (movement === 'DOWN') {\n        onMoveItem((index + 1) as TIndex, index);\n      }\n    }\n  };\n\n  const handleDragClick = () => {\n    if (isSelected) {\n      if (onDropItem) {\n        onDropItem(index);\n      }\n      setIsSelected(false);\n    } else {\n      if (onGrabItem) {\n        onGrabItem(index);\n      }\n      setIsSelected(true);\n    }\n  };\n\n  const handleCancel = () => {\n    if (isSelected) {\n      setIsSelected(false);\n\n      if (onCancel) {\n        onCancel(index);\n      }\n    }\n  };\n\n  const handleKeyDown = <E extends Element>(e: React.KeyboardEvent<E>) => {\n    if (!active) {\n      return;\n    }\n\n    if (e.key === 'Tab' && !isSelected) {\n      return;\n    }\n\n    e.preventDefault();\n\n    switch (e.key) {\n      case ' ':\n      case 'Enter':\n        handleDragClick();\n        break;\n\n      case 'Escape':\n        handleCancel();\n        break;\n\n      case 'ArrowDown':\n      case 'ArrowRight':\n        handleMove('DOWN');\n        break;\n\n      case 'ArrowUp':\n      case 'ArrowLeft':\n        handleMove('UP');\n        break;\n\n      default:\n    }\n  };\n\n  return handleKeyDown;\n};\n", "import * as React from 'react';\n\nimport {\n  useDrag,\n  useDrop,\n  type HandlerManager,\n  type ConnectDragSource,\n  type ConnectDropTarget,\n  type ConnectDragPreview,\n  type DragSourceMonitor,\n} from 'react-dnd';\n\nimport {\n  useKeyboardDragAndDrop,\n  type UseKeyboardDragAndDropCallbacks,\n} from './useKeyboardDragAndDrop';\n\nimport type { Data } from '@strapi/types';\n\nconst DIRECTIONS = {\n  UPWARD: 'upward',\n  DOWNWARD: 'downward',\n} as const;\n\nconst DROP_SENSITIVITY = {\n  REGULAR: 'regular',\n  IMMEDIATE: 'immediate',\n} as const;\n\ninterface UseDragAndDropOptions<\n  TIndex extends number | Array<number> = number,\n  TItem extends { index: TIndex } = { index: TIndex },\n> extends UseKeyboardDragAndDropCallbacks<TIndex> {\n  type?: string;\n  index: TIndex;\n  item?: TItem;\n  onStart?: () => void;\n  onEnd?: () => void;\n  dropSensitivity?: (typeof DROP_SENSITIVITY)[keyof typeof DROP_SENSITIVITY];\n}\n\ntype Identifier = ReturnType<HandlerManager['getHandlerId']>;\n\ntype UseDragAndDropReturn<E extends Element = HTMLElement> = [\n  props: {\n    handlerId: Identifier;\n    isDragging: boolean;\n    handleKeyDown: <E extends Element>(event: React.KeyboardEvent<E>) => void;\n    isOverDropTarget: boolean;\n    direction: (typeof DIRECTIONS)[keyof typeof DIRECTIONS] | null;\n  },\n  objectRef: React.RefObject<E>,\n  dropRef: ConnectDropTarget,\n  dragRef: ConnectDragSource,\n  dragPreviewRef: ConnectDragPreview,\n];\n\ntype DropCollectedProps = {\n  handlerId: Identifier;\n  isOver: boolean;\n};\n\n/**\n * A utility hook abstracting the general drag and drop hooks from react-dnd.\n * Centralising the same behaviours and by default offering keyboard support.\n */\nconst useDragAndDrop = <\n  TIndex extends number | Array<number>,\n  TItem extends { index: TIndex; id?: Data.ID; [key: string]: unknown } = {\n    index: TIndex;\n    [key: string]: unknown;\n  },\n  E extends Element = HTMLElement,\n>(\n  active: boolean,\n  {\n    type = 'STRAPI_DND',\n    index,\n    item,\n    onStart,\n    onEnd,\n    onGrabItem,\n    onDropItem,\n    onCancel,\n    onMoveItem,\n    dropSensitivity = DROP_SENSITIVITY.REGULAR,\n  }: UseDragAndDropOptions<TIndex, TItem>\n): UseDragAndDropReturn<E> => {\n  const objectRef = React.useRef<E>(null);\n\n  const [{ handlerId, isOver }, dropRef] = useDrop<TItem, void, DropCollectedProps>({\n    accept: type,\n    collect(monitor) {\n      return {\n        handlerId: monitor.getHandlerId(),\n        isOver: monitor.isOver({ shallow: true }),\n      };\n    },\n    drop(item) {\n      const draggedIndex = item.index;\n      const newIndex = index;\n\n      if (isOver && onDropItem) {\n        onDropItem(draggedIndex, newIndex);\n      }\n    },\n    hover(item, monitor) {\n      if (!objectRef.current || !onMoveItem) {\n        return;\n      }\n\n      const dragIndex = item.index;\n      const newIndex = index;\n\n      const hoverBoundingRect = objectRef.current?.getBoundingClientRect();\n      const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2;\n      const clientOffset = monitor.getClientOffset();\n      if (!clientOffset) return;\n\n      const hoverClientY = clientOffset && clientOffset.y - hoverBoundingRect.top;\n      if (typeof dragIndex === 'number' && typeof newIndex === 'number') {\n        if (dragIndex === newIndex) {\n          // Don't replace items with themselves\n          return;\n        }\n\n        if (dropSensitivity === DROP_SENSITIVITY.REGULAR) {\n          // Dragging downwards\n          if (dragIndex < newIndex && hoverClientY < hoverMiddleY) {\n            return;\n          }\n\n          // Dragging upwards\n          if (dragIndex > newIndex && hoverClientY > hoverMiddleY) {\n            return;\n          }\n        }\n\n        // Time to actually perform the action\n        onMoveItem(newIndex, dragIndex);\n        item.index = newIndex;\n      } else {\n        // Using numbers as indices doesn't work for nested list items with path like [1, 1, 0]\n        if (Array.isArray(dragIndex) && Array.isArray(newIndex)) {\n          // Indices comparison to find item position in nested list\n          const minLength = Math.min(dragIndex.length, newIndex.length);\n          let areEqual = true;\n          let isLessThan = false;\n          let isGreaterThan = false;\n\n          for (let i = 0; i < minLength; i++) {\n            if (dragIndex[i] < newIndex[i]) {\n              isLessThan = true;\n              areEqual = false;\n              break;\n            } else if (dragIndex[i] > newIndex[i]) {\n              isGreaterThan = true;\n              areEqual = false;\n              break;\n            }\n          }\n\n          // Don't replace items with themselves\n          if (areEqual && dragIndex.length === newIndex.length) {\n            return;\n          }\n\n          if (dropSensitivity === DROP_SENSITIVITY.REGULAR) {\n            // Dragging downwards\n            if (isLessThan && !isGreaterThan && hoverClientY < hoverMiddleY) {\n              return;\n            }\n\n            // Dragging upwards\n            if (isGreaterThan && !isLessThan && hoverClientY > hoverMiddleY) {\n              return;\n            }\n          }\n        }\n\n        onMoveItem(newIndex, dragIndex);\n        item.index = newIndex;\n      }\n    },\n  });\n\n  const getDragDirection = (monitor: DragSourceMonitor<TItem, void>) => {\n    if (\n      monitor &&\n      monitor.isDragging() &&\n      !monitor.didDrop() &&\n      monitor.getInitialClientOffset() &&\n      monitor.getClientOffset()\n    ) {\n      const deltaY = monitor.getInitialClientOffset()!.y - monitor.getClientOffset()!.y;\n\n      if (deltaY > 0) return DIRECTIONS.UPWARD;\n\n      if (deltaY < 0) return DIRECTIONS.DOWNWARD;\n\n      return null;\n    }\n\n    return null;\n  };\n\n  const [{ isDragging, direction }, dragRef, dragPreviewRef] = useDrag({\n    type,\n    item() {\n      if (onStart) {\n        onStart();\n      }\n\n      /**\n       * This will be attached and it helps define the preview sizes\n       * when a component is flexy e.g. Relations\n       */\n      const { width } = objectRef.current?.getBoundingClientRect() ?? {};\n\n      return { index, width, ...item };\n    },\n    end() {\n      if (onEnd) {\n        onEnd();\n      }\n    },\n    canDrag: active,\n    /**\n     * This is useful when the item is in a virtualized list.\n     * However, if we don't have an ID then we want the libraries\n     * defaults to take care of this.\n     */\n    isDragging: item?.id\n      ? (monitor) => {\n          return item.id === monitor.getItem().id;\n        }\n      : undefined,\n    collect: (monitor) => ({\n      isDragging: monitor.isDragging(),\n      initialOffset: monitor.getInitialClientOffset(),\n      currentOffset: monitor.getClientOffset(),\n      direction: getDragDirection(monitor),\n    }),\n  });\n\n  const handleKeyDown = useKeyboardDragAndDrop(active, index, {\n    onGrabItem,\n    onDropItem,\n    onCancel,\n    onMoveItem,\n  });\n\n  return [\n    { handlerId, isDragging, handleKeyDown, isOverDropTarget: isOver, direction },\n    objectRef,\n    dropRef,\n    dragRef,\n    dragPreviewRef,\n  ];\n};\n\nexport {\n  useDragAndDrop,\n  UseDragAndDropReturn,\n  UseDragAndDropOptions,\n  DIRECTIONS,\n  DROP_SENSITIVITY,\n};\n", "import { Box, BoxComponent, ButtonProps, Flex, Typography } from '@strapi/design-system';\nimport { PlusCircle } from '@strapi/icons';\nimport { styled } from 'styled-components';\n\nexport const AddStage = ({ children, ...props }: ButtonProps) => {\n  return (\n    <StyledButton\n      tag=\"button\"\n      background=\"neutral0\"\n      borderColor=\"neutral150\"\n      paddingBottom={3}\n      paddingLeft={4}\n      paddingRight={4}\n      paddingTop={3}\n      shadow=\"filterShadow\"\n      {...props}\n    >\n      <Typography variant=\"pi\" fontWeight=\"bold\">\n        <Flex tag=\"span\" gap={2}>\n          <PlusCircle width=\"2.4rem\" height=\"2.4rem\" aria-hidden />\n          {children}\n        </Flex>\n      </Typography>\n    </StyledButton>\n  );\n};\n\nconst StyledButton = styled<BoxComponent<'button'>>(Box)`\n  border-radius: 26px;\n  color: ${({ theme }) => theme.colors.neutral500};\n\n  &:hover {\n    color: ${({ theme }) => theme.colors.primary600};\n  }\n\n  &:active {\n    color: ${({ theme }) => theme.colors.primary600};\n  }\n`;\n", "import * as React from 'react';\n\nimport {\n  useField,\n  useForm,\n  useTracking,\n  ConfirmDialog,\n  useNotification,\n  InputRenderer as AdminInputRenderer,\n  InputProps,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Box,\n  Flex,\n  MultiSelectOption,\n  Accordion,\n  Grid,\n  IconButton,\n  MultiSelect,\n  MultiSelectGroup,\n  SingleSelect,\n  SingleSelectOption,\n  TextInput,\n  VisuallyHidden,\n  useComposedRefs,\n  Menu,\n  MenuItem,\n  Field,\n  Dialog,\n} from '@strapi/design-system';\nimport { Duplicate, Drag, More, EyeStriked } from '@strapi/icons';\nimport { getEmptyImage } from 'react-dnd-html5-backend';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { Stage as IStage, StagePermission } from '../../../../../shared/contracts/review-workflows';\nimport { useGetAdminRolesQuery } from '../../../services/admin';\nimport { AVAILABLE_COLORS, getStageColorByHex } from '../../../utils/colors';\nimport { DRAG_DROP_TYPES } from '../constants';\nimport { useDragAndDrop } from '../hooks/useDragAndDrop';\n\nimport { AddStage } from './AddStage';\n\ninterface WorkflowStage extends Pick<IStage, 'id' | 'name' | 'permissions' | 'color'> {\n  __temp_key__: string;\n}\n\n/* -------------------------------------------------------------------------------------------------\n * Stages\n * -----------------------------------------------------------------------------------------------*/\ninterface StagesProps {\n  canDelete?: boolean;\n  canUpdate?: boolean;\n  isCreating?: boolean;\n}\n\nconst Stages = ({ canDelete = true, canUpdate = true, isCreating }: StagesProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const addFieldRow = useForm('Stages', (state) => state.addFieldRow);\n  const { value: stages = [] } = useField<WorkflowStage[]>('stages');\n\n  return (\n    <Flex direction=\"column\" gap={6} width=\"100%\">\n      <Box position=\"relative\" width=\"100%\">\n        <Background\n          background=\"neutral200\"\n          height=\"100%\"\n          left=\"50%\"\n          position=\"absolute\"\n          top=\"0\"\n          width={2}\n        />\n\n        <Flex direction=\"column\" alignItems=\"stretch\" gap={6} position=\"relative\" tag=\"ol\">\n          {stages.map((stage, index) => {\n            return (\n              <Box key={stage.__temp_key__} tag=\"li\">\n                <Stage\n                  index={index}\n                  canDelete={stages.length > 1 && canDelete}\n                  canReorder={stages.length > 1}\n                  canUpdate={canUpdate}\n                  stagesCount={stages.length}\n                  defaultOpen={!stage.id}\n                  {...stage}\n                />\n              </Box>\n            );\n          })}\n        </Flex>\n      </Box>\n\n      {canUpdate && (\n        <AddStage\n          type=\"button\"\n          onClick={() => {\n            addFieldRow('stages', { name: '' });\n            trackUsage('willCreateStage');\n          }}\n        >\n          {formatMessage({\n            id: 'Settings.review-workflows.stage.add',\n            defaultMessage: 'Add new stage',\n          })}\n        </AddStage>\n      )}\n    </Flex>\n  );\n};\n\nconst Background = styled(Box)`\n  transform: translateX(-50%);\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * Stage\n * -----------------------------------------------------------------------------------------------*/\ninterface StageProps extends WorkflowStage {\n  canDelete?: boolean;\n  canReorder?: boolean;\n  canUpdate?: boolean;\n  index: number;\n  stagesCount: number;\n  defaultOpen?: boolean;\n}\n\nconst Stage = ({\n  index,\n  canDelete = false,\n  canReorder = false,\n  canUpdate = false,\n  stagesCount,\n  name,\n  permissions,\n  color,\n  defaultOpen,\n}: StageProps) => {\n  const [liveText, setLiveText] = React.useState<string>();\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const stageErrors = useForm('Stages', (state) => state.errors.stages as object[]);\n  const error = stageErrors?.[index];\n  const addFieldRow = useForm('Stage', (state) => state.addFieldRow);\n  const moveFieldRow = useForm('Stage', (state) => state.moveFieldRow);\n  const removeFieldRow = useForm('Stage', (state) => state.removeFieldRow);\n\n  const getItemPos = (index: number) => `${index + 1} of ${stagesCount}`;\n\n  const handleGrabStage = (index: number) => {\n    setLiveText(\n      formatMessage(\n        {\n          id: 'dnd.grab-item',\n          defaultMessage: `{item}, grabbed. Current position in list: {position}. Press up and down arrow to change position, Spacebar to drop, Escape to cancel.`,\n        },\n        {\n          item: name,\n          position: getItemPos(index),\n        }\n      )\n    );\n  };\n\n  const handleDropStage = (index: number) => {\n    setLiveText(\n      formatMessage(\n        {\n          id: 'dnd.drop-item',\n          defaultMessage: `{item}, dropped. Final position in list: {position}.`,\n        },\n        {\n          item: name,\n          position: getItemPos(index),\n        }\n      )\n    );\n  };\n\n  const handleCancelDragStage = () => {\n    setLiveText(\n      formatMessage(\n        {\n          id: 'dnd.cancel-item',\n          defaultMessage: '{item}, dropped. Re-order cancelled.',\n        },\n        {\n          item: name,\n        }\n      )\n    );\n  };\n\n  const handleMoveStage = (newIndex: number, oldIndex: number) => {\n    setLiveText(\n      formatMessage(\n        {\n          id: 'dnd.reorder',\n          defaultMessage: '{item}, moved. New position in list: {position}.',\n        },\n        {\n          item: name,\n          position: getItemPos(newIndex),\n        }\n      )\n    );\n\n    moveFieldRow('stages', oldIndex, newIndex);\n  };\n\n  const [{ handlerId, isDragging, handleKeyDown }, stageRef, dropRef, dragRef, dragPreviewRef] =\n    useDragAndDrop(canReorder, {\n      index,\n      item: {\n        index,\n        name,\n      },\n      onGrabItem: handleGrabStage,\n      onDropItem: handleDropStage,\n      onMoveItem: handleMoveStage,\n      onCancel: handleCancelDragStage,\n      type: DRAG_DROP_TYPES.STAGE,\n    });\n\n  // @ts-expect-error – the stageRef is incorrectly typed.\n  const composedRef = useComposedRefs<HTMLDivElement>(stageRef, dropRef);\n\n  React.useEffect(() => {\n    dragPreviewRef(getEmptyImage(), { captureDraggingState: false });\n  }, [dragPreviewRef, index]);\n\n  const handleCloneClick = () => {\n    addFieldRow('stages', { name, color, permissions });\n  };\n\n  const id = React.useId();\n\n  return (\n    <Box ref={composedRef} shadow=\"tableShadow\">\n      {liveText && <VisuallyHidden aria-live=\"assertive\">{liveText}</VisuallyHidden>}\n\n      {isDragging ? (\n        <Box\n          background=\"primary100\"\n          borderStyle=\"dashed\"\n          borderColor=\"primary600\"\n          borderWidth=\"1px\"\n          display=\"block\"\n          hasRadius\n          padding={6}\n        />\n      ) : (\n        <AccordionRoot\n          onValueChange={(value) => {\n            if (value) {\n              trackUsage('willEditStage');\n            }\n          }}\n          defaultValue={defaultOpen ? id : undefined}\n          $error={Object.values(error ?? {}).length > 0}\n        >\n          <Accordion.Item value={id}>\n            <Accordion.Header>\n              <Accordion.Trigger>{name}</Accordion.Trigger>\n              <Accordion.Actions>\n                {canDelete || canUpdate ? (\n                  <>\n                    <Menu.Root>\n                      <ContextMenuTrigger size=\"S\" endIcon={null} paddingLeft={2} paddingRight={2}>\n                        <More aria-hidden focusable={false} />\n                        <VisuallyHidden tag=\"span\">\n                          {formatMessage({\n                            id: '[tbdb].components.DynamicZone.more-actions',\n                            defaultMessage: 'More actions',\n                          })}\n                        </VisuallyHidden>\n                      </ContextMenuTrigger>\n                      {/* z-index needs to be as big as the one defined for the wrapper in Stages, otherwise the menu\n                       * disappears behind the accordion\n                       */}\n                      <Menu.Content popoverPlacement=\"bottom-end\" zIndex={2}>\n                        <Menu.SubRoot>\n                          {canUpdate && (\n                            <MenuItem onClick={handleCloneClick}>\n                              {formatMessage({\n                                id: 'Settings.review-workflows.stage.delete',\n                                defaultMessage: 'Duplicate stage',\n                              })}\n                            </MenuItem>\n                          )}\n\n                          {canDelete && (\n                            <DeleteMenuItem onClick={() => removeFieldRow('stages', index)}>\n                              {formatMessage({\n                                id: 'Settings.review-workflows.stage.delete',\n                                defaultMessage: 'Delete',\n                              })}\n                            </DeleteMenuItem>\n                          )}\n                        </Menu.SubRoot>\n                      </Menu.Content>\n                    </Menu.Root>\n\n                    {canUpdate && (\n                      <IconButton\n                        background=\"transparent\"\n                        hasRadius\n                        variant=\"ghost\"\n                        data-handler-id={handlerId}\n                        ref={dragRef}\n                        label={formatMessage({\n                          id: 'Settings.review-workflows.stage.drag',\n                          defaultMessage: 'Drag',\n                        })}\n                        onClick={(e) => e.stopPropagation()}\n                        onKeyDown={handleKeyDown}\n                      >\n                        <Drag />\n                      </IconButton>\n                    )}\n                  </>\n                ) : null}\n              </Accordion.Actions>\n            </Accordion.Header>\n            <Accordion.Content>\n              <Grid.Root gap={4} padding={6}>\n                {[\n                  {\n                    disabled: !canUpdate,\n                    label: formatMessage({\n                      id: 'Settings.review-workflows.stage.name.label',\n                      defaultMessage: 'Stage name',\n                    }),\n                    name: `stages.${index}.name`,\n                    required: true,\n                    size: 6,\n                    type: 'string' as const,\n                  },\n                  {\n                    disabled: !canUpdate,\n                    label: formatMessage({\n                      id: 'content-manager.reviewWorkflows.stage.color',\n                      defaultMessage: 'Color',\n                    }),\n                    name: `stages.${index}.color`,\n                    required: true,\n                    size: 6,\n                    type: 'color' as const,\n                  },\n                  {\n                    disabled: !canUpdate,\n                    label: formatMessage({\n                      id: 'Settings.review-workflows.stage.permissions.label',\n                      defaultMessage: 'Roles that can change this stage',\n                    }),\n                    name: `stages.${index}.permissions`,\n                    placeholder: formatMessage({\n                      id: 'Settings.review-workflows.stage.permissions.placeholder',\n                      defaultMessage: 'Select a role',\n                    }),\n                    required: true,\n                    size: 6,\n                    type: 'permissions' as const,\n                  },\n                ].map(({ size, ...field }) => (\n                  <Grid.Item key={field.name} col={size} direction=\"column\" alignItems=\"stretch\">\n                    <InputRenderer {...field} />\n                  </Grid.Item>\n                ))}\n              </Grid.Root>\n            </Accordion.Content>\n          </Accordion.Item>\n        </AccordionRoot>\n      )}\n    </Box>\n  );\n};\n\nconst AccordionRoot = styled(Accordion.Root)<{ $error?: boolean }>`\n  border: 1px solid\n    ${({ theme, $error }) => ($error ? theme.colors.danger600 : theme.colors.neutral200)};\n`;\n\nconst DeleteMenuItem = styled(MenuItem)`\n  color: ${({ theme }) => theme.colors.danger600};\n`;\n\n// Removing the font-size from the child-span aligns the\n// more icon vertically\nconst ContextMenuTrigger = styled(Menu.Trigger)`\n  :hover,\n  :focus {\n    background-color: ${({ theme }) => theme.colors.neutral100};\n  }\n\n  > span {\n    font-size: 0;\n  }\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * InputRenderer\n * -----------------------------------------------------------------------------------------------*/\n\ntype InputRendererProps = InputProps | ColorSelectorProps | PermissionsFieldProps;\n\nconst InputRenderer = (props: InputRendererProps) => {\n  switch (props.type) {\n    case 'color':\n      return <ColorSelector {...props} />;\n    case 'permissions':\n      return <PermissionsField {...props} />;\n    default:\n      return <AdminInputRenderer {...props} />;\n  }\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ColorSelector\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ColorSelectorProps\n  extends Omit<Extract<InputProps, { type: 'enumeration' }>, 'type' | 'options'> {\n  type: 'color';\n}\n\nconst ColorSelector = ({ disabled, label, name, required }: ColorSelectorProps) => {\n  const { formatMessage } = useIntl();\n  const { value, error, onChange } = useField<string>(name);\n\n  const colorOptions = AVAILABLE_COLORS.map(({ hex, name }) => ({\n    value: hex,\n    label: formatMessage(\n      {\n        id: 'Settings.review-workflows.stage.color.name',\n        defaultMessage: '{name}',\n      },\n      { name }\n    ),\n    color: hex,\n  }));\n\n  const { themeColorName } = getStageColorByHex(value) ?? {};\n\n  return (\n    <Field.Root error={error} name={name} required={required}>\n      <Field.Label>{label}</Field.Label>\n      <SingleSelect\n        disabled={disabled}\n        onChange={(v) => {\n          onChange(name, v.toString());\n        }}\n        value={value?.toUpperCase()}\n        startIcon={\n          <Flex\n            tag=\"span\"\n            height={2}\n            background={value}\n            borderColor={themeColorName === 'neutral0' ? 'neutral150' : 'transparent'}\n            hasRadius\n            shrink={0}\n            width={2}\n          />\n        }\n      >\n        {colorOptions.map(({ value, label, color }) => {\n          const { themeColorName } = getStageColorByHex(color) || {};\n\n          return (\n            <SingleSelectOption\n              value={value}\n              key={value}\n              startIcon={\n                <Flex\n                  tag=\"span\"\n                  height={2}\n                  background={color}\n                  borderColor={themeColorName === 'neutral0' ? 'neutral150' : 'transparent'}\n                  hasRadius\n                  shrink={0}\n                  width={2}\n                />\n              }\n            >\n              {label}\n            </SingleSelectOption>\n          );\n        })}\n      </SingleSelect>\n      <Field.Error />\n    </Field.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * PermissionsField\n * -----------------------------------------------------------------------------------------------*/\ninterface PermissionsFieldProps\n  extends Omit<Extract<InputProps, { type: 'enumeration' }>, 'type' | 'options'> {\n  type: 'permissions';\n}\n\nconst PermissionsField = ({ disabled, name, placeholder, required }: PermissionsFieldProps) => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const [isApplyAllConfirmationOpen, setIsApplyAllConfirmationOpen] = React.useState(false);\n  const { value = [], error, onChange } = useField<StagePermission[]>(name);\n  const allStages = useForm<WorkflowStage[]>('PermissionsField', (state) => state.values.stages);\n  const onFormValueChange = useForm('PermissionsField', (state) => state.onChange);\n  const rolesErrorCount = React.useRef(0);\n\n  const { data: roles = [], isLoading, error: getRolesError } = useGetAdminRolesQuery();\n\n  // Super admins always have permissions to do everything and therefore\n  // there is no point for this role to show up in the role combobox\n  const filteredRoles = roles?.filter((role) => role.code !== 'strapi-super-admin') ?? [];\n\n  React.useEffect(() => {\n    if (\n      !isLoading &&\n      getRolesError &&\n      'status' in getRolesError &&\n      getRolesError.status == 403 &&\n      rolesErrorCount.current === 0\n    ) {\n      rolesErrorCount.current = 1;\n\n      toggleNotification({\n        blockTransition: true,\n        type: 'danger',\n        message: formatMessage({\n          id: 'review-workflows.stage.permissions.noPermissions.description',\n          defaultMessage: 'You don’t have the permission to see roles. Contact your administrator.',\n        }),\n      });\n    }\n  }, [formatMessage, isLoading, roles, toggleNotification, getRolesError]);\n\n  if (!isLoading && filteredRoles.length === 0) {\n    return (\n      <Field.Root\n        name={name}\n        hint={formatMessage({\n          id: 'Settings.review-workflows.stage.permissions.noPermissions.description',\n          defaultMessage: 'You don’t have the permission to see roles',\n        })}\n        required={required}\n      >\n        <Field.Label>\n          {formatMessage({\n            id: 'Settings.review-workflows.stage.permissions.label',\n            defaultMessage: 'Roles that can change this stage',\n          })}\n        </Field.Label>\n        <TextInput\n          disabled\n          placeholder={formatMessage({\n            id: 'components.NotAllowedInput.text',\n            defaultMessage: 'No permissions to see this field',\n          })}\n          startAction={<EyeStriked fill=\"neutral600\" />}\n          type=\"text\"\n          value=\"\"\n        />\n        <Field.Hint />\n      </Field.Root>\n    );\n  }\n\n  return (\n    <>\n      <Flex alignItems=\"flex-end\" gap={3}>\n        <PermissionWrapper grow={1}>\n          <Field.Root error={error} name={name} required>\n            <Field.Label>\n              {formatMessage({\n                id: 'Settings.review-workflows.stage.permissions.label',\n                defaultMessage: 'Roles that can change this stage',\n              })}\n            </Field.Label>\n            <MultiSelect\n              disabled={disabled}\n              onChange={(values) => {\n                // Because the select components expects strings for values, but\n                // the yup schema validates we are sending full permission objects to the API,\n                // we must coerce the string value back to an object\n                const permissions = values.map((value) => ({\n                  role: parseInt(value, 10),\n                  action: 'admin::review-workflows.stage.transition',\n                }));\n\n                onChange(name, permissions);\n              }}\n              placeholder={placeholder}\n              // The Select component expects strings for values\n              value={value.map((permission) => `${permission.role}`)}\n              withTags\n            >\n              <MultiSelectGroup\n                label={formatMessage({\n                  id: 'Settings.review-workflows.stage.permissions.allRoles.label',\n                  defaultMessage: 'All roles',\n                })}\n                values={filteredRoles.map((r) => `${r.id}`)}\n              >\n                {filteredRoles.map((role) => {\n                  return (\n                    <NestedOption key={role.id} value={`${role.id}`}>\n                      {role.name}\n                    </NestedOption>\n                  );\n                })}\n              </MultiSelectGroup>\n            </MultiSelect>\n            <Field.Error />\n          </Field.Root>\n        </PermissionWrapper>\n        <Dialog.Root open={isApplyAllConfirmationOpen} onOpenChange={setIsApplyAllConfirmationOpen}>\n          <Dialog.Trigger>\n            <IconButton\n              disabled={disabled}\n              label={formatMessage({\n                id: 'Settings.review-workflows.stage.permissions.apply.label',\n                defaultMessage: 'Apply to all stages',\n              })}\n              size=\"L\"\n            >\n              <Duplicate />\n            </IconButton>\n          </Dialog.Trigger>\n          <ConfirmDialog\n            onConfirm={() => {\n              onFormValueChange(\n                'stages',\n                allStages.map((stage) => ({\n                  ...stage,\n                  permissions: value,\n                }))\n              );\n\n              setIsApplyAllConfirmationOpen(false);\n              toggleNotification({\n                type: 'success',\n                message: formatMessage({\n                  id: 'Settings.review-workflows.page.edit.confirm.stages.permissions.copy.success',\n                  defaultMessage: 'Applied roles to all other stages of the workflow',\n                }),\n              });\n            }}\n            variant=\"default\"\n          >\n            {formatMessage({\n              id: 'Settings.review-workflows.page.edit.confirm.stages.permissions.copy',\n              defaultMessage:\n                'Roles that can change that stage will be applied to all the other stages.',\n            })}\n          </ConfirmDialog>\n        </Dialog.Root>\n      </Flex>\n    </>\n  );\n};\n\nconst NestedOption = styled(MultiSelectOption)`\n  padding-left: ${({ theme }) => theme.spaces[7]};\n`;\n\n// Grow the size of the permission Select\nconst PermissionWrapper = styled(Flex)`\n  > * {\n    flex-grow: 1;\n  }\n`;\n\nexport { Stages };\nexport type { StagesProps, WorkflowStage };\n", "import { Input<PERSON><PERSON>er, useField, useForm } from '@strapi/admin/strapi-admin';\nimport {\n  Field,\n  Grid,\n  MultiSelect,\n  MultiSelectGroup,\n  MultiSelectOption,\n  Typography,\n  useCollator,\n  SingleSelect,\n  SingleSelectOption,\n} from '@strapi/design-system';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { useGetContentTypesQuery } from '../../../services/content-manager';\nimport { useReviewWorkflows } from '../hooks/useReviewWorkflows';\n\nimport type { WorkflowStage } from './Stages';\n\n/* -------------------------------------------------------------------------------------------------\n * WorkflowAttributes\n * -----------------------------------------------------------------------------------------------*/\ninterface WorkflowAttributesProps {\n  canUpdate?: boolean;\n}\n\nconst WorkflowAttributes = ({ canUpdate = true }: WorkflowAttributesProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Grid.Root background=\"neutral0\" hasRadius gap={4} padding={6} shadow=\"tableShadow\">\n      <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n        <InputRenderer\n          disabled={!canUpdate}\n          label={formatMessage({\n            id: 'Settings.review-workflows.workflow.name.label',\n            defaultMessage: 'Workflow Name',\n          })}\n          name=\"name\"\n          required\n          type=\"string\"\n        />\n      </Grid.Item>\n      <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n        <ContentTypesSelector disabled={!canUpdate} />\n      </Grid.Item>\n      <Grid.Item col={6} direction=\"column\" alignItems=\"stretch\">\n        <StageSelector disabled={!canUpdate} />\n      </Grid.Item>\n    </Grid.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ContentTypesSelector\n * -----------------------------------------------------------------------------------------------*/\ninterface ContentTypesSelectorProps {\n  disabled?: boolean;\n}\n\nconst ContentTypesSelector = ({ disabled }: ContentTypesSelectorProps) => {\n  const { formatMessage, locale } = useIntl();\n  const { data: contentTypes, isLoading } = useGetContentTypesQuery();\n  const { workflows } = useReviewWorkflows();\n  const currentWorkflow = useForm('ContentTypesSelector', (state) => state.values);\n\n  const { error, value, onChange } = useField('contentTypes');\n\n  const formatter = useCollator(locale, {\n    sensitivity: 'base',\n  });\n\n  const isDisabled =\n    disabled ||\n    isLoading ||\n    !contentTypes ||\n    (contentTypes.collectionType.length === 0 && contentTypes.singleType.length === 0);\n\n  const collectionTypes = (contentTypes?.collectionType ?? [])\n    .toSorted((a, b) => formatter.compare(a.info.displayName, b.info.displayName))\n    .map((contentType) => ({\n      label: contentType.info.displayName,\n      value: contentType.uid,\n    }));\n\n  const singleTypes = (contentTypes?.singleType ?? []).map((contentType) => ({\n    label: contentType.info.displayName,\n    value: contentType.uid,\n  }));\n\n  return (\n    <Field.Root error={error} name={'contentTypes'}>\n      <Field.Label>\n        {formatMessage({\n          id: 'Settings.review-workflows.workflow.contentTypes.label',\n          defaultMessage: 'Associated to',\n        })}\n      </Field.Label>\n      <MultiSelect\n        customizeContent={(value) =>\n          formatMessage(\n            {\n              id: 'Settings.review-workflows.workflow.contentTypes.displayValue',\n              defaultMessage:\n                '{count} {count, plural, one {content type} other {content types}} selected',\n            },\n            { count: value?.length }\n          )\n        }\n        disabled={isDisabled}\n        onChange={(values) => {\n          onChange('contentTypes', values);\n        }}\n        value={value}\n        placeholder={formatMessage({\n          id: 'Settings.review-workflows.workflow.contentTypes.placeholder',\n          defaultMessage: 'Select',\n        })}\n      >\n        {[\n          ...(collectionTypes.length > 0\n            ? [\n                {\n                  label: formatMessage({\n                    id: 'Settings.review-workflows.workflow.contentTypes.collectionTypes.label',\n                    defaultMessage: 'Collection Types',\n                  }),\n                  children: collectionTypes,\n                },\n              ]\n            : []),\n\n          ...(singleTypes.length > 0\n            ? [\n                {\n                  label: formatMessage({\n                    id: 'Settings.review-workflows.workflow.contentTypes.singleTypes.label',\n                    defaultMessage: 'Single Types',\n                  }),\n                  children: singleTypes,\n                },\n              ]\n            : []),\n        ].map((opt) => {\n          return (\n            <MultiSelectGroup\n              key={opt.label}\n              label={opt.label}\n              values={opt.children.map((child) => child.value.toString())}\n            >\n              {opt.children.map((child) => {\n                const { name: assignedWorkflowName } =\n                  workflows?.find(\n                    (workflow) =>\n                      ((currentWorkflow && workflow.id !== currentWorkflow.id) ||\n                        !currentWorkflow) &&\n                      workflow.contentTypes.includes(child.value)\n                  ) ?? {};\n\n                return (\n                  <NestedOption key={child.value} value={child.value}>\n                    <Typography>\n                      {\n                        // @ts-expect-error - formatMessage options doesn't expect to be a React component but that's what we need actually for the <i> and <em> components\n                        formatMessage(\n                          {\n                            id: 'Settings.review-workflows.workflow.contentTypes.assigned.notice',\n                            defaultMessage:\n                              '{label} {name, select, undefined {} other {<i>(assigned to <em>{name}</em> workflow)</i>}}',\n                          },\n                          {\n                            label: child.label,\n                            name: assignedWorkflowName,\n                            em: (...children) => (\n                              <Typography tag=\"em\" fontWeight=\"bold\">\n                                {children}\n                              </Typography>\n                            ),\n                            i: (...children) => (\n                              <ContentTypeTakeNotice>{children}</ContentTypeTakeNotice>\n                            ),\n                          }\n                        )\n                      }\n                    </Typography>\n                  </NestedOption>\n                );\n              })}\n            </MultiSelectGroup>\n          );\n        })}\n      </MultiSelect>\n    </Field.Root>\n  );\n};\n\nconst NestedOption = styled(MultiSelectOption)`\n  padding-left: ${({ theme }) => theme.spaces[7]};\n`;\n\nconst ContentTypeTakeNotice = styled(Typography)`\n  font-style: italic;\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * StageSelector\n * -----------------------------------------------------------------------------------------------*/\ninterface StageSelectorProps {\n  disabled?: boolean;\n}\n\nconst StageSelector = ({ disabled }: StageSelectorProps) => {\n  const { value: stages = [] } = useField<WorkflowStage[]>('stages');\n  const { formatMessage } = useIntl();\n\n  const { error, value, onChange } = useField('stageRequiredToPublish');\n\n  // stages with empty names are not valid, so we avoid them from being used to avoid errors\n  const validStages = stages.filter((stage) => stage.name);\n\n  return (\n    <Field.Root\n      error={error}\n      name=\"stageRequiredToPublish\"\n      hint={formatMessage({\n        id: 'settings.review-workflows.workflow.stageRequiredToPublish.hint',\n        defaultMessage:\n          'Prevents entries from being published if they are not at the required stage.',\n      })}\n    >\n      <Field.Label>\n        {formatMessage({\n          id: 'settings.review-workflows.workflow.stageRequiredToPublish.label',\n          defaultMessage: 'Required stage for publishing',\n        })}\n      </Field.Label>\n      <SingleSelect\n        disabled={disabled}\n        onChange={(value) => {\n          onChange('stageRequiredToPublish', value);\n        }}\n        value={value}\n      >\n        <SingleSelectOption value={''}>\n          {formatMessage({\n            id: 'settings.review-workflows.workflow.stageRequiredToPublish.any',\n            defaultMessage: 'Any stage',\n          })}\n        </SingleSelectOption>\n        {validStages.map((stage, i) => (\n          <SingleSelectOption\n            key={`requiredToPublishStage-${stage.id || stage.__temp_key__}`}\n            value={stage.id?.toString() || stage.__temp_key__}\n          >\n            {stage.name}\n          </SingleSelectOption>\n        ))}\n      </SingleSelect>\n      <Field.Hint />\n    </Field.Root>\n  );\n};\n\nexport { WorkflowAttributes };\nexport type { WorkflowAttributesProps };\n", "import * as React from 'react';\n\nimport {\n  Con<PERSON><PERSON><PERSON><PERSON><PERSON>,\n  BackButton,\n  useNotification,\n  useAPIErrorHand<PERSON>,\n  useRBAC,\n  Form,\n  Page,\n  FormProps,\n  FormHelpers,\n} from '@strapi/admin/strapi-admin';\nimport { useLicenseLimits } from '@strapi/admin/strapi-admin/ee';\nimport { <PERSON>ton, <PERSON>alog, Flex, Typography } from '@strapi/design-system';\nimport { Check } from '@strapi/icons';\nimport { generateNKeysBetween } from 'fractional-indexing';\nimport { useIntl } from 'react-intl';\nimport { useNavigate, useParams } from 'react-router-dom';\nimport * as yup from 'yup';\n\nimport { LimitsModal } from '../../components/LimitsModal';\nimport {\n  CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME,\n  CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME,\n} from '../../constants';\nimport { useTypedSelector } from '../../modules/hooks';\nimport { isBaseQueryError } from '../../utils/api';\n\nimport * as Layout from './components/Layout';\nimport { Stages, WorkflowStage } from './components/Stages';\nimport { WorkflowAttributes } from './components/WorkflowAttributes';\nimport { useReviewWorkflows } from './hooks/useReviewWorkflows';\n\nimport type { Stage, Workflow } from '../../../../shared/contracts/review-workflows';\n\n/* -------------------------------------------------------------------------------------------------\n * EditPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst WORKFLOW_SCHEMA = yup.object({\n  contentTypes: yup.array().of(yup.string()),\n  name: yup\n    .string()\n    .max(255, {\n      id: 'review-workflows.validation.name.max-length',\n      defaultMessage: 'Name can not be longer than 255 characters',\n    })\n    .required()\n    .nullable(),\n  stages: yup\n    .array()\n    .of(\n      yup.object().shape({\n        name: yup\n          .string()\n          .nullable()\n          .required({\n            id: 'review-workflows.validation.stage.name',\n            defaultMessage: 'Name is required',\n          })\n          .max(255, {\n            id: 'review-workflows.validation.stage.max-length',\n            defaultMessage: 'Name can not be longer than 255 characters',\n          })\n          .test(\n            'unique-name',\n            {\n              id: 'review-workflows.validation.stage.duplicate',\n              defaultMessage: 'Stage name must be unique',\n            },\n            (stageName, context) => {\n              // @ts-expect-error it does exist.\n              const { stages } = context.from[1].value;\n\n              return stages.filter((stage: Stage) => stage.name === stageName).length === 1;\n            }\n          ),\n        color: yup\n          .string()\n          .nullable()\n          .required({\n            id: 'review-workflows.validation.stage.color',\n            defaultMessage: 'Color is required',\n          })\n          .matches(/^#(?:[0-9a-fA-F]{3}){1,2}$/i),\n\n        permissions: yup\n          .array(\n            yup.object({\n              role: yup\n                .number()\n                .strict()\n                .typeError({\n                  id: 'review-workflows.validation.stage.permissions.role.number',\n                  defaultMessage: 'Role must be of type number',\n                })\n                .required(),\n              action: yup.string().required({\n                id: 'review-workflows.validation.stage.permissions.action.required',\n                defaultMessage: 'Action is a required argument',\n              }),\n            })\n          )\n          .strict(),\n      })\n    )\n    .min(1),\n  stageRequiredToPublish: yup.string().nullable(),\n});\n\nconst EditPage = () => {\n  const { id = '' } = useParams<{ id: string }>();\n  const isCreatingWorkflow = id === 'create';\n  const { formatMessage } = useIntl();\n  const { _unstableFormatValidationErrors: formatValidationErrors } = useAPIErrorHandler();\n  const navigate = useNavigate();\n  const { toggleNotification } = useNotification();\n  const {\n    isLoading: isLoadingWorkflow,\n    meta,\n    workflows,\n    error,\n    update,\n    create,\n  } = useReviewWorkflows();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions['settings']?.['review-workflows']\n  );\n  const {\n    allowedActions: { canDelete, canUpdate, canCreate },\n  } = useRBAC(permissions);\n\n  const [savePrompts, setSavePrompts] = React.useState<{\n    hasDeletedServerStages?: boolean;\n    hasReassignedContentTypes?: boolean;\n  }>({});\n  const { getFeature, isLoading: isLicenseLoading } = useLicenseLimits();\n  const [showLimitModal, setShowLimitModal] = React.useState<'workflow' | 'stage' | null>(null);\n\n  const currentWorkflow = workflows?.find((workflow) => workflow.id === parseInt(id, 10));\n  const contentTypesFromOtherWorkflows = workflows\n    ?.filter((workflow) => workflow.id !== parseInt(id, 10))\n    .flatMap((workflow) => workflow.contentTypes);\n\n  const limits = getFeature<string>('review-workflows');\n  const numberOfWorkflows = limits?.[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME];\n  const stagesPerWorkflow = limits?.[CHARGEBEE_STAGES_PER_WORKFLOW_ENTITLEMENT_NAME];\n\n  interface FormValues {\n    name: string;\n    stages: WorkflowStage[];\n    contentTypes: string[];\n    stageRequiredToPublish: string | null;\n  }\n\n  const submitForm = async (data: FormValues, helpers: Pick<FormHelpers, 'setErrors'>) => {\n    try {\n      const { stageRequiredToPublish, ...rest } = data;\n      const stageRequiredToPublishName =\n        stageRequiredToPublish === ''\n          ? null\n          : rest.stages.find(\n              (stage) =>\n                stage.id === Number(stageRequiredToPublish) ||\n                stage.__temp_key__ === stageRequiredToPublish\n            )?.name;\n\n      if (!isCreatingWorkflow) {\n        const res = await update(id, {\n          ...rest,\n          // compare permissions of stages and only submit them if at least one has\n          // changed; this enables partial updates e.g. for users who don't have\n          // permissions to see roles\n          stages: rest.stages.map((stage) => {\n            let hasUpdatedPermissions = true;\n            const serverStage = currentWorkflow?.stages?.find(\n              (serverStage) => serverStage.id === stage?.id\n            );\n            if (serverStage) {\n              hasUpdatedPermissions =\n                serverStage.permissions?.length !== stage.permissions?.length ||\n                !serverStage.permissions?.every(\n                  (serverPermission) =>\n                    !!stage.permissions?.find(\n                      (permission) => permission.role === serverPermission.role\n                    )\n                );\n            }\n            return {\n              ...stage,\n              permissions: hasUpdatedPermissions ? stage.permissions : undefined,\n            } satisfies Stage;\n          }),\n          stageRequiredToPublishName,\n        });\n\n        if ('error' in res && isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        }\n      } else {\n        const res = await create({\n          ...rest,\n          stageRequiredToPublishName,\n        });\n\n        if ('error' in res && isBaseQueryError(res.error) && res.error.name === 'ValidationError') {\n          helpers.setErrors(formatValidationErrors(res.error));\n        } else if ('data' in res) {\n          navigate(`../${res.data.id}`, { replace: true });\n        }\n      }\n    } catch (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'An error occurred',\n        }),\n      });\n    }\n    setSavePrompts({});\n  };\n\n  const handleConfirmDeleteDialog =\n    (data: FormValues, helpers: Pick<FormHelpers, 'setErrors'>) => async () => {\n      await submitForm(data, helpers);\n    };\n\n  const handleConfirmClose = () => {\n    setSavePrompts({});\n  };\n\n  const handleSubmit: FormProps<FormValues>['onSubmit'] = async (data, helpers) => {\n    const isContentTypeReassignment = data.contentTypes.some((contentType) =>\n      contentTypesFromOtherWorkflows?.includes(contentType)\n    );\n    const hasDeletedServerStages =\n      !isCreatingWorkflow &&\n      !currentWorkflow?.stages.every((stage) =>\n        data.stages.some((newStage) => newStage.id === stage.id)\n      );\n\n    if (meta && numberOfWorkflows && meta?.workflowCount > parseInt(numberOfWorkflows, 10)) {\n      /**\n       * If the current license has a limit, check if the total count of workflows\n       * exceeds that limit and display the limits modal instead of sending the\n       * update, because it would throw an API error.\n       */\n      setShowLimitModal('workflow');\n\n      /**\n       * If the current license has a limit, check if the total count of stages\n       * exceeds that limit and display the limits modal instead of sending the\n       * update, because it would throw an API error.\n       */\n    } else if (\n      data.stages &&\n      stagesPerWorkflow &&\n      data.stages.length > parseInt(stagesPerWorkflow, 10)\n    ) {\n      setShowLimitModal('stage');\n    } else if (hasDeletedServerStages || isContentTypeReassignment) {\n      if (hasDeletedServerStages) {\n        setSavePrompts((prev) => ({ ...prev, hasDeletedServerStages: true }));\n      }\n\n      if (isContentTypeReassignment) {\n        setSavePrompts((prev) => ({ ...prev, hasReassignedContentTypes: true }));\n      }\n    } else {\n      await submitForm(data, helpers);\n    }\n  };\n\n  /**\n   * If the current license has a limit:\n   * check if the total count of workflows or stages exceeds that limit and display\n   * the limits modal on page load. It can be closed by the user, but the\n   * API will throw an error in case they try to create a new workflow or update the\n   * stages.\n   *\n   * If the current license does not have a limit (e.g. offline license):\n   * do nothing (for now). In case they are trying to create the 201st workflow/ stage\n   * the API will throw an error.\n   *\n   */\n  React.useEffect(() => {\n    if (!isLoadingWorkflow && !isLicenseLoading) {\n      if (meta && numberOfWorkflows && meta?.workflowCount > parseInt(numberOfWorkflows, 10)) {\n        setShowLimitModal('workflow');\n      } else if (\n        currentWorkflow &&\n        currentWorkflow.stages &&\n        stagesPerWorkflow &&\n        currentWorkflow.stages.length > parseInt(stagesPerWorkflow, 10)\n      ) {\n        setShowLimitModal('stage');\n      }\n    }\n  }, [\n    currentWorkflow,\n    isLicenseLoading,\n    isLoadingWorkflow,\n    limits,\n    meta,\n    numberOfWorkflows,\n    stagesPerWorkflow,\n  ]);\n\n  const initialValues: FormValues = React.useMemo(() => {\n    if (isCreatingWorkflow || !currentWorkflow) {\n      return {\n        name: '',\n        stages: [],\n        contentTypes: [],\n        stageRequiredToPublish: '',\n      };\n    } else {\n      return {\n        name: currentWorkflow.name,\n        stages: addTmpKeysToStages(currentWorkflow.stages),\n        contentTypes: currentWorkflow.contentTypes,\n        stageRequiredToPublish: currentWorkflow.stageRequiredToPublish?.id.toString() ?? '',\n      };\n    }\n  }, [currentWorkflow, isCreatingWorkflow]);\n\n  if (isLoadingWorkflow) {\n    return <Page.Loading />;\n  }\n\n  if (error) {\n    return <Page.Error />;\n  }\n\n  return (\n    <>\n      <Layout.DragLayerRendered />\n\n      <Form\n        method={isCreatingWorkflow ? 'POST' : 'PUT'}\n        initialValues={initialValues}\n        validationSchema={WORKFLOW_SCHEMA}\n        onSubmit={handleSubmit}\n      >\n        {({ modified, isSubmitting, values, setErrors }) => (\n          <>\n            <Layout.Header\n              navigationAction={<BackButton />}\n              primaryAction={\n                canUpdate || canCreate ? (\n                  <Button\n                    startIcon={<Check />}\n                    type=\"submit\"\n                    disabled={!modified || isSubmitting || values.stages.length === 0}\n                    // if the confirm dialog is open the loading state is on\n                    // the confirm button already\n                    loading={!Boolean(Object.keys(savePrompts).length > 0) && isSubmitting}\n                  >\n                    {formatMessage({\n                      id: 'global.save',\n                      defaultMessage: 'Save',\n                    })}\n                  </Button>\n                ) : null\n              }\n              subtitle={formatMessage(\n                {\n                  id: 'review-workflows.page.subtitle',\n                  defaultMessage: '{count, plural, one {# stage} other {# stages}}',\n                },\n                { count: currentWorkflow?.stages?.length ?? 0 }\n              )}\n              title={\n                currentWorkflow?.name ||\n                formatMessage({\n                  id: 'Settings.review-workflows.create.page.title',\n                  defaultMessage: 'Create Review Workflow',\n                })\n              }\n            />\n            <Layout.Root>\n              <Flex alignItems=\"stretch\" direction=\"column\" gap={7}>\n                <WorkflowAttributes canUpdate={canUpdate || canCreate} />\n                <Stages\n                  canDelete={canDelete}\n                  canUpdate={canUpdate || canCreate}\n                  isCreating={isCreatingWorkflow}\n                />\n              </Flex>\n            </Layout.Root>\n            <Dialog.Root\n              open={Object.keys(savePrompts).length > 0}\n              onOpenChange={handleConfirmClose}\n            >\n              <ConfirmDialog onConfirm={handleConfirmDeleteDialog(values, { setErrors })}>\n                <Flex direction=\"column\" gap={5}>\n                  {savePrompts.hasDeletedServerStages && (\n                    <Typography textAlign=\"center\" variant=\"omega\">\n                      {formatMessage({\n                        id: 'review-workflows.page.delete.confirm.stages.body',\n                        defaultMessage:\n                          'All entries assigned to deleted stages will be moved to the previous stage.',\n                      })}\n                    </Typography>\n                  )}\n\n                  {savePrompts.hasReassignedContentTypes && (\n                    <Typography textAlign=\"center\" variant=\"omega\">\n                      {formatMessage(\n                        {\n                          id: 'review-workflows.page.delete.confirm.contentType.body',\n                          defaultMessage:\n                            '{count} {count, plural, one {content-type} other {content-types}} {count, plural, one {is} other {are}} already mapped to {count, plural, one {another workflow} other {other workflows}}. If you save changes, {count, plural, one {this} other {these}} {count, plural, one {content-type} other {{count} content-types}} will no more be mapped to the {count, plural, one {another workflow} other {other workflows}} and all corresponding information will be removed.',\n                        },\n                        {\n                          count:\n                            contentTypesFromOtherWorkflows?.filter((contentType) =>\n                              values.contentTypes.includes(contentType)\n                            ).length ?? 0,\n                        }\n                      )}\n                    </Typography>\n                  )}\n\n                  <Typography textAlign=\"center\" variant=\"omega\">\n                    {formatMessage({\n                      id: 'review-workflows.page.delete.confirm.confirm',\n                      defaultMessage: 'Are you sure you want to save?',\n                    })}\n                  </Typography>\n                </Flex>\n              </ConfirmDialog>\n            </Dialog.Root>\n          </>\n        )}\n      </Form>\n\n      <LimitsModal.Root\n        open={showLimitModal === 'workflow'}\n        onOpenChange={() => setShowLimitModal(null)}\n      >\n        <LimitsModal.Title>\n          {formatMessage({\n            id: 'review-workflows.edit.page.workflows.limit.title',\n            defaultMessage: 'You’ve reached the limit of workflows in your plan',\n          })}\n        </LimitsModal.Title>\n\n        <LimitsModal.Body>\n          {formatMessage({\n            id: 'review-workflows.edit.page.workflows.limit.body',\n            defaultMessage: 'Delete a workflow or contact Sales to enable more workflows.',\n          })}\n        </LimitsModal.Body>\n      </LimitsModal.Root>\n\n      <LimitsModal.Root\n        open={showLimitModal === 'stage'}\n        onOpenChange={() => setShowLimitModal(null)}\n      >\n        <LimitsModal.Title>\n          {formatMessage({\n            id: 'review-workflows.edit.page.stages.limit.title',\n            defaultMessage: 'You have reached the limit of stages for this workflow in your plan',\n          })}\n        </LimitsModal.Title>\n\n        <LimitsModal.Body>\n          {formatMessage({\n            id: 'review-workflows.edit.page.stages.limit.body',\n            defaultMessage: 'Try deleting some stages or contact Sales to enable more stages.',\n          })}\n        </LimitsModal.Body>\n      </LimitsModal.Root>\n    </>\n  );\n};\n\nconst addTmpKeysToStages = (data: Workflow['stages']) => {\n  const keys = generateNKeysBetween(undefined, undefined, data.length);\n\n  return data.map((datum, index) => ({\n    ...datum,\n    __temp_key__: keys[index],\n  }));\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ProtectedEditPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ProtectedEditPage = () => {\n  const permissions = useTypedSelector((state) => {\n    const {\n      create = [],\n      update = [],\n      read = [],\n    } = state.admin_app.permissions.settings?.['review-workflows'] ?? {};\n\n    return [...create, ...update, ...read];\n  });\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <EditPage />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedEditPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOA,IAAM,WAAW,mBAAmB,gBAAgB;EAClD,UAAU,SAAS;AACV,WAAA;MACL,eAAe,QAAQ,MAAmB;QACxC,OAAO,OAAO;UACZ,KAAK;UACL,QAAQ;QAAA;QAEV,mBAAmB,CAAC,QAAuB;AACzC,iBAAO,IAAI;QACb;MAAA,CACD;IAAA;EAEL;AACF,CAAC;AAED,IAAM,EAAE,sBAA0B,IAAA;ACRrB,IAAA,yBAAyB,CACpC,QACA,OACA,EAAE,UAAU,YAAY,YAAY,WAAA,MACjC;AACH,QAAM,CAAC,YAAY,aAAa,IAAU,eAAS,KAAK;AAElD,QAAA,aAAa,CAAC,aAA4B;AAC9C,QAAI,CAAC,YAAY;AACf;IACF;AACI,QAAA,OAAO,UAAU,YAAY,YAAY;AAC3C,UAAI,aAAa,MAAM;AACT,mBAAA,QAAQ,GAAc,KAAK;MAAA,WAC9B,aAAa,QAAQ;AAClB,mBAAA,QAAQ,GAAc,KAAK;MACzC;IACF;EAAA;AAGF,QAAM,kBAAkB,MAAM;AAC5B,QAAI,YAAY;AACd,UAAI,YAAY;AACd,mBAAW,KAAK;MAClB;AACA,oBAAc,KAAK;IAAA,OACd;AACL,UAAI,YAAY;AACd,mBAAW,KAAK;MAClB;AACA,oBAAc,IAAI;IACpB;EAAA;AAGF,QAAM,eAAe,MAAM;AACzB,QAAI,YAAY;AACd,oBAAc,KAAK;AAEnB,UAAI,UAAU;AACZ,iBAAS,KAAK;MAChB;IACF;EAAA;AAGI,QAAA,gBAAgB,CAAoB,MAA8B;AACtE,QAAI,CAAC,QAAQ;AACX;IACF;AAEA,QAAI,EAAE,QAAQ,SAAS,CAAC,YAAY;AAClC;IACF;AAEA,MAAE,eAAe;AAEjB,YAAQ,EAAE,KAAK;MACb,KAAK;MACL,KAAK;AACa,wBAAA;AAChB;MAEF,KAAK;AACU,qBAAA;AACb;MAEF,KAAK;MACL,KAAK;AACH,mBAAW,MAAM;AACjB;MAEF,KAAK;MACL,KAAK;AACH,mBAAW,IAAI;AACf;IAGJ;EAAA;AAGK,SAAA;AACT;AC5EA,IAAM,aAAa;EACjB,QAAQ;EACR,UAAU;AACZ;AAEA,IAAM,mBAAmB;EACvB,SAAS;EACT,WAAW;AACb;AAuCA,IAAM,iBAAiB,CAQrB,QACA;EACE,OAAO;EACP;EACA;EACA;EACA;EACA;EACA;EACA;EACA;EACA,kBAAkB,iBAAiB;AACrC,MAC4B;AACtB,QAAA,YAAkB,aAAU,IAAI;AAEtC,QAAM,CAAC,EAAE,WAAW,OAAU,GAAA,OAAO,IAAI,QAAyC;IAChF,QAAQ;IACR,QAAQ,SAAS;AACR,aAAA;QACL,WAAW,QAAQ,aAAa;QAChC,QAAQ,QAAQ,OAAO,EAAE,SAAS,KAAA,CAAM;MAAA;IAE5C;IACA,KAAKA,OAAM;AACT,YAAM,eAAeA,MAAK;AAC1B,YAAM,WAAW;AAEjB,UAAI,UAAU,YAAY;AACxB,mBAAW,cAAc,QAAQ;MACnC;IACF;IACA,MAAMA,OAAM,SAAS;;AACnB,UAAI,CAAC,UAAU,WAAW,CAAC,YAAY;AACrC;MACF;AAEA,YAAM,YAAYA,MAAK;AACvB,YAAM,WAAW;AAEX,YAAA,qBAAoB,eAAU,YAAV,mBAAmB;AAC7C,YAAM,gBAAgB,kBAAkB,SAAS,kBAAkB,OAAO;AACpE,YAAA,eAAe,QAAQ,gBAAA;AAC7B,UAAI,CAAC;AAAc;AAEnB,YAAM,eAAe,gBAAgB,aAAa,IAAI,kBAAkB;AACxE,UAAI,OAAO,cAAc,YAAY,OAAO,aAAa,UAAU;AACjE,YAAI,cAAc,UAAU;AAE1B;QACF;AAEI,YAAA,oBAAoB,iBAAiB,SAAS;AAE5C,cAAA,YAAY,YAAY,eAAe,cAAc;AACvD;UACF;AAGI,cAAA,YAAY,YAAY,eAAe,cAAc;AACvD;UACF;QACF;AAGA,mBAAW,UAAU,SAAS;AAC9BA,cAAK,QAAQ;MAAA,OACR;AAEL,YAAI,MAAM,QAAQ,SAAS,KAAK,MAAM,QAAQ,QAAQ,GAAG;AAEvD,gBAAM,YAAY,KAAK,IAAI,UAAU,QAAQ,SAAS,MAAM;AAC5D,cAAI,WAAW;AACf,cAAI,aAAa;AACjB,cAAI,gBAAgB;AAEpB,mBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,gBAAI,UAAU,CAAC,IAAI,SAAS,CAAC,GAAG;AACjB,2BAAA;AACF,yBAAA;AACX;YAAA,WACS,UAAU,CAAC,IAAI,SAAS,CAAC,GAAG;AACrB,8BAAA;AACL,yBAAA;AACX;YACF;UACF;AAGA,cAAI,YAAY,UAAU,WAAW,SAAS,QAAQ;AACpD;UACF;AAEI,cAAA,oBAAoB,iBAAiB,SAAS;AAEhD,gBAAI,cAAc,CAAC,iBAAiB,eAAe,cAAc;AAC/D;YACF;AAGA,gBAAI,iBAAiB,CAAC,cAAc,eAAe,cAAc;AAC/D;YACF;UACF;QACF;AAEA,mBAAW,UAAU,SAAS;AAC9BA,cAAK,QAAQ;MACf;IACF;EAAA,CACD;AAEK,QAAA,mBAAmB,CAAC,YAA4C;AACpE,QACE,WACA,QAAQ,WAAW,KACnB,CAAC,QAAQ,QAAQ,KACjB,QAAQ,uBAAA,KACR,QAAQ,gBAAA,GACR;AACA,YAAM,SAAS,QAAQ,uBAAA,EAA0B,IAAI,QAAQ,gBAAmB,EAAA;AAEhF,UAAI,SAAS;AAAG,eAAO,WAAW;AAElC,UAAI,SAAS;AAAG,eAAO,WAAW;AAE3B,aAAA;IACT;AAEO,WAAA;EAAA;AAGH,QAAA,CAAC,EAAE,YAAY,UAAA,GAAa,SAAS,cAAc,IAAI,QAAQ;IACnE;IACA,OAAO;;AACL,UAAI,SAAS;AACH,gBAAA;MACV;AAMA,YAAM,EAAE,MAAM,MAAI,eAAU,YAAV,mBAAmB,4BAA2B,CAAA;AAEhE,aAAO,EAAE,OAAO,OAAO,GAAG,KAAK;IACjC;IACA,MAAM;AACJ,UAAI,OAAO;AACH,cAAA;MACR;IACF;IACA,SAAS;;;;;;IAMT,aAAY,6BAAM,MACd,CAAC,YAAY;AACX,aAAO,KAAK,OAAO,QAAQ,QAAA,EAAU;IAEvC,IAAA;IACJ,SAAS,CAAC,aAAa;MACrB,YAAY,QAAQ,WAAW;MAC/B,eAAe,QAAQ,uBAAuB;MAC9C,eAAe,QAAQ,gBAAgB;MACvC,WAAW,iBAAiB,OAAO;IAAA;EACrC,CACD;AAEK,QAAA,gBAAgB,uBAAuB,QAAQ,OAAO;IAC1D;IACA;IACA;IACA;EAAA,CACD;AAEM,SAAA;IACL,EAAE,WAAW,YAAY,eAAe,kBAAkB,QAAQ,UAAU;IAC5E;IACA;IACA;IACA;EAAA;AAEJ;AC/PO,IAAM,WAAW,CAAC,EAAE,UAAU,GAAG,MAAA,MAAyB;AAE7D,aAAA;IAAC;IAAA;MACC,KAAI;MACJ,YAAW;MACX,aAAY;MACZ,eAAe;MACf,aAAa;MACb,cAAc;MACd,YAAY;MACZ,QAAO;MACN,GAAG;MAEJ,cAAA,wBAAC,YAAW,EAAA,SAAQ,MAAK,YAAW,QAClC,cAAA,yBAAC,MAAK,EAAA,KAAI,QAAO,KAAK,GACpB,UAAA;YAAA,wBAAC,eAAA,EAAW,OAAM,UAAS,QAAO,UAAS,eAAW,KAAA,CAAC;QACtD;MAAA,EAAA,CACH,EACF,CAAA;IAAA;EAAA;AAGN;AAEA,IAAM,eAAe,GAA+B,GAAG;;WAE5C,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;aAGpC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;aAItC,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;ACoBnD,IAAM,SAAS,CAAC,EAAE,YAAY,MAAM,YAAY,MAAM,WAAA,MAA8B;AAC5E,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,cAAc,QAAQ,UAAU,CAAC,UAAU,MAAM,WAAW;AAClE,QAAM,EAAE,OAAO,SAAS,CAAG,EAAA,IAAI,SAA0B,QAAQ;AAEjE,aAAA,yBACG,MAAK,EAAA,WAAU,UAAS,KAAK,GAAG,OAAM,QACrC,UAAA;QAAA,yBAAC,KAAI,EAAA,UAAS,YAAW,OAAM,QAC7B,UAAA;UAAA;QAAC;QAAA;UACC,YAAW;UACX,QAAO;UACP,MAAK;UACL,UAAS;UACT,KAAI;UACJ,OAAO;QAAA;MACT;UAAA,wBAEC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GAAG,UAAS,YAAW,KAAI,MAC3E,UAAA,OAAO,IAAI,CAAC,OAAO,UAAU;AAE1B,mBAAA,wBAAC,KAA6B,EAAA,KAAI,MAChC,cAAA;UAAC;UAAA;YACC;YACA,WAAW,OAAO,SAAS,KAAK;YAChC,YAAY,OAAO,SAAS;YAC5B;YACA,aAAa,OAAO;YACpB,aAAa,CAAC,MAAM;YACnB,GAAG;UAAA;QAAA,EAAA,GARE,MAAM,YAUhB;MAEH,CAAA,EAAA,CACH;IAAA,EAAA,CACF;IAEC,iBACC;MAAC;MAAA;QACC,MAAK;QACL,SAAS,MAAM;AACb,sBAAY,UAAU,EAAE,MAAM,GAAI,CAAA;AAClC,qBAAW,iBAAiB;QAC9B;QAEC,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH;EAEJ,EAAA,CAAA;AAEJ;AAEA,IAAM,aAAa,GAAO,GAAG;;;AAgB7B,IAAM,QAAQ,CAAC;EACb;EACA,YAAY;EACZ,aAAa;EACb,YAAY;EACZ;EACA;EACA;EACA;EACA;AACF,MAAkB;AAChB,QAAM,CAAC,UAAU,WAAW,IAAU,eAAiB;AACjD,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,cAAc,QAAQ,UAAU,CAAC,UAAU,MAAM,OAAO,MAAkB;AAC1E,QAAA,QAAQ,2CAAc;AAC5B,QAAM,cAAc,QAAQ,SAAS,CAAC,UAAU,MAAM,WAAW;AACjE,QAAM,eAAe,QAAQ,SAAS,CAAC,UAAU,MAAM,YAAY;AACnE,QAAM,iBAAiB,QAAQ,SAAS,CAAC,UAAU,MAAM,cAAc;AAEvE,QAAM,aAAa,CAACC,WAAkB,GAAGA,SAAQ,CAAC,OAAO,WAAW;AAE9D,QAAA,kBAAkB,CAACA,WAAkB;AACzC;MACE;QACE;UACE,IAAI;UACJ,gBAAgB;QAClB;QACA;UACE,MAAM;UACN,UAAU,WAAWA,MAAK;QAC5B;MACF;IAAA;EACF;AAGI,QAAA,kBAAkB,CAACA,WAAkB;AACzC;MACE;QACE;UACE,IAAI;UACJ,gBAAgB;QAClB;QACA;UACE,MAAM;UACN,UAAU,WAAWA,MAAK;QAC5B;MACF;IAAA;EACF;AAGF,QAAM,wBAAwB,MAAM;AAClC;MACE;QACE;UACE,IAAI;UACJ,gBAAgB;QAClB;QACA;UACE,MAAM;QACR;MACF;IAAA;EACF;AAGI,QAAA,kBAAkB,CAAC,UAAkB,aAAqB;AAC9D;MACE;QACE;UACE,IAAI;UACJ,gBAAgB;QAClB;QACA;UACE,MAAM;UACN,UAAU,WAAW,QAAQ;QAC/B;MACF;IAAA;AAGW,iBAAA,UAAU,UAAU,QAAQ;EAAA;AAG3C,QAAM,CAAC,EAAE,WAAW,YAAY,cAAc,GAAG,UAAU,SAAS,SAAS,cAAc,IACzF,eAAe,YAAY;IACzB;IACA,MAAM;MACJ;MACA;IACF;IACA,YAAY;IACZ,YAAY;IACZ,YAAY;IACZ,UAAU;IACV,MAAM,gBAAgB;EAAA,CACvB;AAGG,QAAA,cAAc,gBAAgC,UAAU,OAAO;AAErE,EAAM,gBAAU,MAAM;AACpB,mBAAe,cAAc,GAAG,EAAE,sBAAsB,MAAO,CAAA;EAAA,GAC9D,CAAC,gBAAgB,KAAK,CAAC;AAE1B,QAAM,mBAAmB,MAAM;AAC7B,gBAAY,UAAU,EAAE,MAAM,OAAO,YAAa,CAAA;EAAA;AAG9C,QAAA,KAAW,YAAA;AAEjB,aACG,yBAAA,KAAA,EAAI,KAAK,aAAa,QAAO,eAC3B,UAAA;IAAA,gBAAa,wBAAA,gBAAA,EAAe,aAAU,aAAa,UAAS,SAAA,CAAA;IAE5D,iBACC;MAAC;MAAA;QACC,YAAW;QACX,aAAY;QACZ,aAAY;QACZ,aAAY;QACZ,SAAQ;QACR,WAAS;QACT,SAAS;MAAA;IAAA,QAGX;MAAC;MAAA;QACC,eAAe,CAAC,UAAU;AACxB,cAAI,OAAO;AACT,uBAAW,eAAe;UAC5B;QACF;QACA,cAAc,cAAc,KAAK;QACjC,QAAQ,OAAO,OAAO,SAAS,CAAE,CAAA,EAAE,SAAS;QAE5C,cAAC,yBAAA,UAAU,MAAV,EAAe,OAAO,IACrB,UAAA;cAAC,yBAAA,UAAU,QAAV,EACC,UAAA;gBAAC,wBAAA,UAAU,SAAV,EAAmB,UAAK,KAAA,CAAA;gBAAA,wBACxB,UAAU,SAAV,EACE,UAAA,aAAa,gBAEV,yBAAA,6BAAA,EAAA,UAAA;kBAAC,yBAAA,KAAK,MAAL,EACC,UAAA;oBAAC,yBAAA,oBAAA,EAAmB,MAAK,KAAI,SAAS,MAAM,aAAa,GAAG,cAAc,GACxE,UAAA;sBAAA,wBAAC,eAAK,EAAA,eAAW,MAAC,WAAW,MAAA,CAAO;sBACnC,wBAAA,gBAAA,EAAe,KAAI,QACjB,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBACjB,CAAA,EAAA,CACH;gBAAA,EAAA,CACF;oBAIA,wBAAC,KAAK,SAAL,EAAa,kBAAiB,cAAa,QAAQ,GAClD,cAAA,yBAAC,KAAK,SAAL,EACE,UAAA;kBAAA,iBACE,wBAAA,UAAA,EAAS,SAAS,kBAChB,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBACjB,CAAA,EAAA,CACH;kBAGD,iBAAA,wBACE,gBAAe,EAAA,SAAS,MAAM,eAAe,UAAU,KAAK,GAC1D,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBACjB,CAAA,EAAA,CACH;gBAAA,EAAA,CAEJ,EACF,CAAA;cAAA,EAAA,CACF;cAEC,iBACC;gBAAC;gBAAA;kBACC,YAAW;kBACX,WAAS;kBACT,SAAQ;kBACR,mBAAiB;kBACjB,KAAK;kBACL,OAAO,cAAc;oBACnB,IAAI;oBACJ,gBAAgB;kBAAA,CACjB;kBACD,SAAS,CAAC,MAAM,EAAE,gBAAgB;kBAClC,WAAW;kBAEX,cAAA,wBAAC,eAAK,CAAA,CAAA;gBAAA;cACR;YAAA,EAEJ,CAAA,IACE,KAAA,CACN;UAAA,EAAA,CACF;cACA,wBAAC,UAAU,SAAV,EACC,cAAA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,SAAS,GACzB,UAAA;YACC;cACE,UAAU,CAAC;cACX,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,MAAM,UAAU,KAAK;cACrB,UAAU;cACV,MAAM;cACN,MAAM;YACR;YACA;cACE,UAAU,CAAC;cACX,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,MAAM,UAAU,KAAK;cACrB,UAAU;cACV,MAAM;cACN,MAAM;YACR;YACA;cACE,UAAU,CAAC;cACX,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,MAAM,UAAU,KAAK;cACrB,aAAa,cAAc;gBACzB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,UAAU;cACV,MAAM;cACN,MAAM;YACR;UACF,EAAE,IAAI,CAAC,EAAE,MAAM,GAAG,MAChB,UAAA,wBAAC,KAAK,MAAL,EAA2B,KAAK,MAAM,WAAU,UAAS,YAAW,WACnE,cAAA,wBAAC,eAAe,EAAA,GAAG,MAAA,CAAO,EAAA,GADZ,MAAM,IAEtB,CACD,EAAA,CACH,EACF,CAAA;QAAA,EAAA,CACF;MAAA;IACF;EAEJ,EAAA,CAAA;AAEJ;AAEA,IAAM,gBAAgB,GAAO,UAAU,IAAI;;MAErC,CAAC,EAAE,OAAO,OAAO,MAAO,SAAS,MAAM,OAAO,YAAY,MAAM,OAAO,UAAW;;AAGxF,IAAM,iBAAiB,GAAO,QAAQ;WAC3B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,SAAS;;AAKhD,IAAM,qBAAqB,GAAO,KAAK,OAAO;;;wBAGtB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;;;;AAc9D,IAAM,gBAAgB,CAAC,UAA8B;AACnD,UAAQ,MAAM,MAAM;IAClB,KAAK;AACI,iBAAA,wBAAC,eAAe,EAAA,GAAG,MAAO,CAAA;IACnC,KAAK;AACI,iBAAA,wBAAC,kBAAkB,EAAA,GAAG,MAAO,CAAA;IACtC;AACS,iBAAA,wBAACC,uBAAoB,EAAA,GAAG,MAAO,CAAA;EAC1C;AACF;AAWA,IAAM,gBAAgB,CAAC,EAAE,UAAU,OAAO,MAAM,SAAA,MAAmC;AAC3E,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,OAAO,OAAO,SAAS,IAAI,SAAiB,IAAI;AAElD,QAAA,eAAe,iBAAiB,IAAI,CAAC,EAAE,KAAK,MAAAC,MAAAA,OAAY;IAC5D,OAAO;IACP,OAAO;MACL;QACE,IAAI;QACJ,gBAAgB;MAClB;MACA,EAAE,MAAAA,MAAK;IACT;IACA,OAAO;EACP,EAAA;AAEF,QAAM,EAAE,eAAe,IAAI,mBAAmB,KAAK,KAAK,CAAA;AAExD,aAAA,yBACG,MAAM,MAAN,EAAW,OAAc,MAAY,UACpC,UAAA;QAAC,wBAAA,MAAM,OAAN,EAAa,UAAM,MAAA,CAAA;QACpB;MAAC;MAAA;QACC;QACA,UAAU,CAAC,MAAM;AACN,mBAAA,MAAM,EAAE,SAAU,CAAA;QAC7B;QACA,OAAO,+BAAO;QACd,eACE;UAAC;UAAA;YACC,KAAI;YACJ,QAAQ;YACR,YAAY;YACZ,aAAa,mBAAmB,aAAa,eAAe;YAC5D,WAAS;YACT,QAAQ;YACR,OAAO;UAAA;QACT;QAGD,UAAA,aAAa,IAAI,CAAC,EAAE,OAAAC,QAAO,OAAAC,QAAO,MAAA,MAAY;AAC7C,gBAAM,EAAE,gBAAAC,gBAAAA,IAAmB,mBAAmB,KAAK,KAAK,CAAA;AAGtD,qBAAA;YAAC;YAAA;cACC,OAAOF;cAEP,eACE;gBAAC;gBAAA;kBACC,KAAI;kBACJ,QAAQ;kBACR,YAAY;kBACZ,aAAaE,oBAAmB,aAAa,eAAe;kBAC5D,WAAS;kBACT,QAAQ;kBACR,OAAO;gBAAA;cACT;cAGD,UAAAD;YAAA;YAbID;UAAA;QAcP,CAEH;MAAA;IACH;QACA,wBAAC,MAAM,OAAN,CAAA,CAAY;EACf,EAAA,CAAA;AAEJ;AAUA,IAAM,mBAAmB,CAAC,EAAE,UAAU,MAAM,aAAa,SAAA,MAAsC;AACvF,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,CAAC,4BAA4B,6BAA6B,IAAU,eAAS,KAAK;AAClF,QAAA,EAAE,QAAQ,CAAA,GAAI,OAAO,SAAA,IAAa,SAA4B,IAAI;AACxE,QAAM,YAAY,QAAyB,oBAAoB,CAAC,UAAU,MAAM,OAAO,MAAM;AAC7F,QAAM,oBAAoB,QAAQ,oBAAoB,CAAC,UAAU,MAAM,QAAQ;AACzE,QAAA,kBAAwB,aAAO,CAAC;AAEhC,QAAA,EAAE,MAAM,QAAQ,CAAA,GAAI,WAAW,OAAO,cAAA,IAAkB,sBAAA;AAIxD,QAAA,iBAAgB,+BAAO,OAAO,CAAC,SAAS,KAAK,SAAS,0BAAyB,CAAA;AAErF,EAAM,gBAAU,MAAM;AAElB,QAAA,CAAC,aACD,iBACA,YAAY,iBACZ,cAAc,UAAU,OACxB,gBAAgB,YAAY,GAC5B;AACA,sBAAgB,UAAU;AAEP,yBAAA;QACjB,iBAAiB;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IACH;EAAA,GACC,CAAC,eAAe,WAAW,OAAO,oBAAoB,aAAa,CAAC;AAEvE,MAAI,CAAC,aAAa,cAAc,WAAW,GAAG;AAE1C,eAAA;MAAC,MAAM;MAAN;QACC;QACA,MAAM,cAAc;UAClB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD;QAEA,UAAA;cAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UACjB,CAAA,EAAA,CACH;cACA;YAAC;YAAA;cACC,UAAQ;cACR,aAAa,cAAc;gBACzB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,iBAAa,wBAAC,eAAW,EAAA,MAAK,aAAa,CAAA;cAC3C,MAAK;cACL,OAAM;YAAA;UACR;cACA,wBAAC,MAAM,MAAN,CAAA,CAAW;QAAA;MAAA;IAAA;EAGlB;AAEA,aAAA,wBAAA,6BAAA,EAEI,cAAC,yBAAA,MAAA,EAAK,YAAW,YAAW,KAAK,GAC/B,UAAA;QAAC,wBAAA,mBAAA,EAAkB,MAAM,GACvB,cAAC,yBAAA,MAAM,MAAN,EAAW,OAAc,MAAY,UAAQ,MAC5C,UAAA;UAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MACjB,CAAA,EAAA,CACH;UACA;QAAC;QAAA;UACC;UACA,UAAU,CAAC,WAAW;AAIpB,kBAAM,cAAc,OAAO,IAAI,CAACA,YAAW;cACzC,MAAM,SAASA,QAAO,EAAE;cACxB,QAAQ;YACR,EAAA;AAEF,qBAAS,MAAM,WAAW;UAC5B;UACA;UAEA,OAAO,MAAM,IAAI,CAAC,eAAe,GAAG,WAAW,IAAI,EAAE;UACrD,UAAQ;UAER,cAAA;YAAC;YAAA;cACC,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,QAAQ,cAAc,IAAI,CAAC,MAAM,GAAG,EAAE,EAAE,EAAE;cAEzC,UAAA,cAAc,IAAI,CAAC,SAAS;AAEzB,2BAAA,wBAACG,gBAA2B,EAAA,OAAO,GAAG,KAAK,EAAE,IAC1C,UAAA,KAAK,KADW,GAAA,KAAK,EAExB;cAAA,CAEH;YAAA;UACH;QAAA;MACF;UACA,wBAAC,MAAM,OAAN,CAAA,CAAY;IAAA,EAAA,CACf,EACF,CAAA;QAAA,yBACC,OAAO,MAAP,EAAY,MAAM,4BAA4B,cAAc,+BAC3D,UAAA;UAAC,wBAAA,OAAO,SAAP,EACC,cAAA;QAAC;QAAA;UACC;UACA,OAAO,cAAc;YACnB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,MAAK;UAEL,cAAA,wBAAC,eAAU,CAAA,CAAA;QAAA;MAAA,EAAA,CAEf;UACA;QAAC;QAAA;UACC,WAAW,MAAM;AACf;cACE;cACA,UAAU,IAAI,CAAC,WAAW;gBACxB,GAAG;gBACH,aAAa;cAAA,EACb;YAAA;AAGJ,0CAA8B,KAAK;AAChB,+BAAA;cACjB,MAAM;cACN,SAAS,cAAc;gBACrB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA,CACF;UACH;UACA,SAAQ;UAEP,UAAc,cAAA;YACb,IAAI;YACJ,gBACE;UAAA,CACH;QAAA;MACH;IAAA,EAAA,CACF;EAAA,EACF,CAAA,EACF,CAAA;AAEJ;AAEA,IAAMA,iBAAe,GAAO,iBAAiB;kBAC3B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;AAIhD,IAAM,oBAAoB,GAAO,IAAI;;;;;ACjoBrC,IAAM,qBAAqB,CAAC,EAAE,YAAY,KAAA,MAAoC;AACtE,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,aACG,yBAAA,KAAK,MAAL,EAAU,YAAW,YAAW,WAAS,MAAC,KAAK,GAAG,SAAS,GAAG,QAAO,eACpE,UAAA;QAAC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,WAAU,UAAS,YAAW,WAC/C,cAAA;MAACC;MAAA;QACC,UAAU,CAAC;QACX,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,MAAK;QACL,UAAQ;QACR,MAAK;MAAA;IAAA,EAAA,CAET;QACC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,WAAU,UAAS,YAAW,WAC/C,cAAC,wBAAA,sBAAA,EAAqB,UAAU,CAAC,UAAW,CAAA,EAAA,CAC9C;QACC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,WAAU,UAAS,YAAW,WAC/C,cAAC,wBAAA,eAAA,EAAc,UAAU,CAAC,UAAW,CAAA,EAAA,CACvC;EACF,EAAA,CAAA;AAEJ;AASA,IAAM,uBAAuB,CAAC,EAAE,SAAA,MAA0C;AACxE,QAAM,EAAE,eAAe,OAAO,IAAI,QAAQ;AAC1C,QAAM,EAAE,MAAM,cAAc,UAAA,IAAc,wBAAwB;AAC5D,QAAA,EAAE,UAAA,IAAc,mBAAA;AACtB,QAAM,kBAAkB,QAAQ,wBAAwB,CAAC,UAAU,MAAM,MAAM;AAE/E,QAAM,EAAE,OAAO,OAAO,SAAS,IAAI,SAAS,cAAc;AAEpD,QAAA,YAAY,YAAY,QAAQ;IACpC,aAAa;EAAA,CACd;AAEK,QAAA,aACJ,YACA,aACA,CAAC,gBACA,aAAa,eAAe,WAAW,KAAK,aAAa,WAAW,WAAW;AAE5E,QAAA,oBAAmB,6CAAc,mBAAkB,CAAA,GACtD,SAAS,CAAC,GAAG,MAAM,UAAU,QAAQ,EAAE,KAAK,aAAa,EAAE,KAAK,WAAW,CAAC,EAC5E,IAAI,CAAC,iBAAiB;IACrB,OAAO,YAAY,KAAK;IACxB,OAAO,YAAY;EACnB,EAAA;AAEJ,QAAM,gBAAe,6CAAc,eAAc,CAAA,GAAI,IAAI,CAAC,iBAAiB;IACzE,OAAO,YAAY,KAAK;IACxB,OAAO,YAAY;EACnB,EAAA;AAEF,aAAA,yBACG,MAAM,MAAN,EAAW,OAAc,MAAM,gBAC9B,UAAA;QAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;MACb,IAAI;MACJ,gBAAgB;IACjB,CAAA,EAAA,CACH;QACA;MAAC;MAAA;QACC,kBAAkB,CAACJ,WACjB;UACE;YACE,IAAI;YACJ,gBACE;UACJ;UACA,EAAE,OAAOA,iCAAO,OAAO;QACzB;QAEF,UAAU;QACV,UAAU,CAAC,WAAW;AACpB,mBAAS,gBAAgB,MAAM;QACjC;QACA;QACA,aAAa,cAAc;UACzB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QAEA,UAAA;UACC,GAAI,gBAAgB,SAAS,IACzB;YACE;cACE,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,UAAU;YACZ;UAAA,IAEF,CAAC;UAEL,GAAI,YAAY,SAAS,IACrB;YACE;cACE,OAAO,cAAc;gBACnB,IAAI;gBACJ,gBAAgB;cAAA,CACjB;cACD,UAAU;YACZ;UAAA,IAEF,CAAC;QAAA,EACL,IAAI,CAAC,QAAQ;AAEX,qBAAA;YAAC;YAAA;cAEC,OAAO,IAAI;cACX,QAAQ,IAAI,SAAS,IAAI,CAAC,UAAU,MAAM,MAAM,SAAA,CAAU;cAEzD,UAAI,IAAA,SAAS,IAAI,CAAC,UAAU;AAC3B,sBAAM,EAAE,MAAM,qBAAqB,KACjC,uCAAW;kBACT,CAAC,cACG,mBAAmB,SAAS,OAAO,gBAAgB,MACnD,CAAC,oBACH,SAAS,aAAa,SAAS,MAAM,KAAK;sBACzC,CAAA;AAEP,2BACG,wBAAA,cAAA,EAA+B,OAAO,MAAM,OAC3C,cAAC,wBAAA,YAAA;;kBAGG,UAAA;oBACE;sBACE,IAAI;sBACJ,gBACE;oBACJ;oBACA;sBACE,OAAO,MAAM;sBACb,MAAM;sBACN,IAAI,IAAI,iBACN,wBAAC,YAAA,EAAW,KAAI,MAAK,YAAW,QAC7B,SACH,CAAA;sBAEF,GAAG,IAAI,iBACL,wBAAC,uBAAA,EAAuB,SAAA,CAAS;oBAErC;kBACF;gBAAA,CAEJ,EAAA,GAxBiB,MAAM,KAyBzB;cAAA,CAEH;YAAA;YAzCI,IAAI;UAAA;QA0CX,CAEH;MAAA;IACH;EACF,EAAA,CAAA;AAEJ;AAEA,IAAM,eAAe,GAAO,iBAAiB;kBAC3B,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,CAAC,CAAC;;AAGhD,IAAM,wBAAwB,GAAO,UAAU;;;AAW/C,IAAM,gBAAgB,CAAC,EAAE,SAAA,MAAmC;AAC1D,QAAM,EAAE,OAAO,SAAS,CAAG,EAAA,IAAI,SAA0B,QAAQ;AAC3D,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,QAAM,EAAE,OAAO,OAAO,SAAS,IAAI,SAAS,wBAAwB;AAGpE,QAAM,cAAc,OAAO,OAAO,CAAC,UAAU,MAAM,IAAI;AAGrD,aAAA;IAAC,MAAM;IAAN;MACC;MACA,MAAK;MACL,MAAM,cAAc;QAClB,IAAI;QACJ,gBACE;MAAA,CACH;MAED,UAAA;YAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QACjB,CAAA,EAAA,CACH;YACA;UAAC;UAAA;YACC;YACA,UAAU,CAACA,WAAU;AACnB,uBAAS,0BAA0BA,MAAK;YAC1C;YACA;YAEA,UAAA;kBAAC,wBAAA,oBAAA,EAAmB,OAAO,IACxB,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cACjB,CAAA,EAAA,CACH;cACC,YAAY,IAAI,CAAC,OAAO,MAAA;;AACvB;kBAAC;kBAAA;oBAEC,SAAO,WAAM,OAAN,mBAAU,eAAc,MAAM;oBAEpC,UAAM,MAAA;kBAAA;kBAHF,0BAA0B,MAAM,MAAM,MAAM,YAAY;gBAAA;eAKhE;YAAA;UAAA;QACH;YACA,wBAAC,MAAM,MAAN,CAAA,CAAW;MAAA;IAAA;EAAA;AAGlB;AC9NA,IAAM,kBAAsBK,QAAO;EACjC,cAAkBA,QAAA,EAAQ,GAAO,OAAA,CAAQ;EACzC,MACG,OAAA,EACA,IAAI,KAAK;IACR,IAAI;IACJ,gBAAgB;EAAA,CACjB,EACA,SAAS,EACT,SAAS;EACZ,QACGA,QAAA,EACA;IACKA,QAAO,EAAE,MAAM;MACjB,MACG,OACA,EAAA,SAAA,EACA,SAAS;QACR,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACA,IAAI,KAAK;QACR,IAAI;QACJ,gBAAgB;MACjB,CAAA,EACA;QACC;QACA;UACE,IAAI;UACJ,gBAAgB;QAClB;QACA,CAAC,WAAW,YAAY;AAEtB,gBAAM,EAAE,OAAO,IAAI,QAAQ,KAAK,CAAC,EAAE;AAE5B,iBAAA,OAAO,OAAO,CAAC,UAAiB,MAAM,SAAS,SAAS,EAAE,WAAW;QAC9E;MACF;MACF,OACG,OACA,EAAA,SAAA,EACA,SAAS;QACR,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACA,QAAQ,6BAA6B;MAExC,aACGA;QACKA,QAAO;UACT,MACGA,QACA,EAAA,OAAA,EACA,UAAU;YACT,IAAI;YACJ,gBAAgB;UACjB,CAAA,EACA,SAAS;UACZ,QAAY,OAAO,EAAE,SAAS;YAC5B,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA,CACF;MAAA,EAEF,OAAO;IAAA,CACX;EAAA,EAEF,IAAI,CAAC;EACR,wBAA4B,OAAO,EAAE,SAAS;AAChD,CAAC;AAED,IAAM,WAAW,MAAM;AACrB,QAAM,EAAE,KAAK,GAAG,IAAI,UAA0B;AAC9C,QAAM,qBAAqB,OAAO;AAC5B,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,iCAAiC,uBAAuB,IAAI,mBAAmB;AACvF,QAAM,WAAW,YAAA;AACX,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AACzB,QAAA;IACJ,WAAW;IACX;IACA;IACA;IACA;IACA,QAAAA;EAAA,IACE,mBAAmB;AACvB,QAAM,cAAc;IAClB,CAAC,UAAA;;AAAU,yBAAM,UAAU,YAAY,UAAU,MAAtC,mBAA0C;;EAAkB;AAEnE,QAAA;IACJ,gBAAgB,EAAE,WAAW,WAAW,UAAU;EAAA,IAChD,QAAQ,WAAW;AAEvB,QAAM,CAAC,aAAa,cAAc,IAAU,eAGzC,CAAA,CAAE;AACL,QAAM,EAAE,YAAY,WAAW,iBAAA,IAAqB,iBAAiB;AACrE,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,eAAsC,IAAI;AAEtF,QAAA,kBAAkB,uCAAW,KAAK,CAAC,aAAa,SAAS,OAAO,SAAS,IAAI,EAAE;AACrF,QAAM,iCAAiC,uCACnC,OAAO,CAAC,aAAa,SAAS,OAAO,SAAS,IAAI,EAAE,GACrD,QAAQ,CAAC,aAAa,SAAS;AAE5B,QAAA,SAAS,WAAmB,kBAAkB;AAC9C,QAAA,oBAAoB,iCAAS;AAC7B,QAAA,oBAAoB,iCAAS;AAS7B,QAAA,aAAa,OAAO,MAAkB,YAA4C;;AAClF,QAAA;AACF,YAAM,EAAE,wBAAwB,GAAG,KAAA,IAAS;AAC5C,YAAM,6BACJ,2BAA2B,KACvB,QACA,UAAK,OAAO;QACV,CAAC,UACC,MAAM,OAAO,OAAO,sBAAsB,KAC1C,MAAM,iBAAiB;MACxB,MAJH,mBAIG;AAET,UAAI,CAAC,oBAAoB;AACjB,cAAA,MAAM,MAAM,OAAO,IAAI;UAC3B,GAAG;;;;UAIH,QAAQ,KAAK,OAAO,IAAI,CAAC,UAAU;;AACjC,gBAAI,wBAAwB;AACtB,kBAAA,eAAcC,MAAA,mDAAiB,WAAjB,gBAAAA,IAAyB;cAC3C,CAACC,iBAAgBA,aAAY,QAAO,+BAAO;;AAE7C,gBAAI,aAAa;AAEb,wCAAA,iBAAY,gBAAZ,mBAAyB,cAAW,WAAM,gBAAN,mBAAmB,WACvD,GAAC,iBAAY,gBAAZ,mBAAyB;gBACxB,CAAC,qBAAA;;AACC,0BAAC,GAACD,MAAA,MAAM,gBAAN,gBAAAA,IAAmB;oBACnB,CAAC,eAAe,WAAW,SAAS,iBAAiB;;;;YAG/D;AACO,mBAAA;cACL,GAAG;cACH,aAAa,wBAAwB,MAAM,cAAc;YAAA;UAC3D,CACD;UACD;QAAA,CACD;AAEG,YAAA,WAAW,OAAO,iBAAiB,IAAI,KAAK,KAAK,IAAI,MAAM,SAAS,mBAAmB;AACzF,kBAAQ,UAAU,uBAAuB,IAAI,KAAK,CAAC;QACrD;MAAA,OACK;AACC,cAAA,MAAM,MAAMD,QAAO;UACvB,GAAG;UACH;QAAA,CACD;AAEG,YAAA,WAAW,OAAO,iBAAiB,IAAI,KAAK,KAAK,IAAI,MAAM,SAAS,mBAAmB;AACzF,kBAAQ,UAAU,uBAAuB,IAAI,KAAK,CAAC;QAAA,WAC1C,UAAU,KAAK;AACf,mBAAA,MAAM,IAAI,KAAK,EAAE,IAAI,EAAE,SAAS,KAAA,CAAM;QACjD;MACF;IAAA,SACOG,QAAO;AACK,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IACH;AACA,mBAAe,CAAE,CAAA;EAAA;AAGnB,QAAM,4BACJ,CAAC,MAAkB,YAA4C,YAAY;AACnE,UAAA,WAAW,MAAM,OAAO;EAAA;AAGlC,QAAM,qBAAqB,MAAM;AAC/B,mBAAe,CAAE,CAAA;EAAA;AAGb,QAAA,eAAkD,OAAO,MAAM,YAAY;AACzE,UAAA,4BAA4B,KAAK,aAAa;MAAK,CAAC,gBACxD,iFAAgC,SAAS;IAAW;AAEtD,UAAM,yBACJ,CAAC,sBACD,EAAC,mDAAiB,OAAO;MAAM,CAAC,UAC9B,KAAK,OAAO,KAAK,CAAC,aAAa,SAAS,OAAO,MAAM,EAAE;;AAG3D,QAAI,QAAQ,sBAAqB,6BAAM,iBAAgB,SAAS,mBAAmB,EAAE,GAAG;AAMtF,wBAAkB,UAAU;IAO9B,WACE,KAAK,UACL,qBACA,KAAK,OAAO,SAAS,SAAS,mBAAmB,EAAE,GACnD;AACA,wBAAkB,OAAO;IAAA,WAChB,0BAA0B,2BAA2B;AAC9D,UAAI,wBAAwB;AAC1B,uBAAe,CAAC,UAAU,EAAE,GAAG,MAAM,wBAAwB,KAAO,EAAA;MACtE;AAEA,UAAI,2BAA2B;AAC7B,uBAAe,CAAC,UAAU,EAAE,GAAG,MAAM,2BAA2B,KAAO,EAAA;MACzE;IAAA,OACK;AACC,YAAA,WAAW,MAAM,OAAO;IAChC;EAAA;AAeF,EAAM,gBAAU,MAAM;AAChB,QAAA,CAAC,qBAAqB,CAAC,kBAAkB;AAC3C,UAAI,QAAQ,sBAAqB,6BAAM,iBAAgB,SAAS,mBAAmB,EAAE,GAAG;AACtF,0BAAkB,UAAU;MAC9B,WACE,mBACA,gBAAgB,UAChB,qBACA,gBAAgB,OAAO,SAAS,SAAS,mBAAmB,EAAE,GAC9D;AACA,0BAAkB,OAAO;MAC3B;IACF;EAAA,GACC;IACD;IACA;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;AAEK,QAAA,gBAAkC,cAAQ,MAAM;;AAChD,QAAA,sBAAsB,CAAC,iBAAiB;AACnC,aAAA;QACL,MAAM;QACN,QAAQ,CAAC;QACT,cAAc,CAAC;QACf,wBAAwB;MAAA;IAC1B,OACK;AACE,aAAA;QACL,MAAM,gBAAgB;QACtB,QAAQ,mBAAmB,gBAAgB,MAAM;QACjD,cAAc,gBAAgB;QAC9B,0BAAwB,qBAAgB,2BAAhB,mBAAwC,GAAG,eAAc;MAAA;IAErF;EAAA,GACC,CAAC,iBAAiB,kBAAkB,CAAC;AAExC,MAAI,mBAAmB;AACd,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEA,MAAI,OAAO;AACF,eAAA,wBAAC,KAAK,OAAL,CAAW,CAAA;EACrB;AAEA,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAC,wBAAAC,mBAAA,CAAA,CAAyB;QAE1B;MAAC;MAAA;QACC,QAAQ,qBAAqB,SAAS;QACtC;QACA,kBAAkB;QAClB,UAAU;QAET,UAAA,CAAC,EAAE,UAAU,cAAc,QAAQ,UAAA,MAAA;;AAEhC,8CAAA,6BAAA,EAAA,UAAA;gBAAA;cAACC;cAAA;gBACC,sBAAA,wBAAmB,YAAW,CAAA,CAAA;gBAC9B,eACE,aAAa,gBACX;kBAAC;kBAAA;oBACC,eAAA,wBAAY,eAAM,CAAA,CAAA;oBAClB,MAAK;oBACL,UAAU,CAAC,YAAY,gBAAgB,OAAO,OAAO,WAAW;oBAGhE,SAAS,CAAC,QAAQ,OAAO,KAAK,WAAW,EAAE,SAAS,CAAC,KAAK;oBAEzD,UAAc,cAAA;sBACb,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;kBAAA;gBAAA,IAED;gBAEN,UAAU;kBACR;oBACE,IAAI;oBACJ,gBAAgB;kBAClB;kBACA,EAAE,SAAO,wDAAiB,WAAjB,mBAAyB,WAAU,EAAE;gBAChD;gBACA,QACE,mDAAiB,SACjB,cAAc;kBACZ,IAAI;kBACJ,gBAAgB;gBAAA,CACjB;cAAA;YAEL;gBACA,wBAACC,MAAA,EACC,cAAA,yBAAC,MAAK,EAAA,YAAW,WAAU,WAAU,UAAS,KAAK,GACjD,UAAA;kBAAC,wBAAA,oBAAA,EAAmB,WAAW,aAAa,UAAW,CAAA;kBACvD;gBAAC;gBAAA;kBACC;kBACA,WAAW,aAAa;kBACxB,YAAY;gBAAA;cACd;YAAA,EAAA,CACF,EACF,CAAA;gBACA;cAAC,OAAO;cAAP;gBACC,MAAM,OAAO,KAAK,WAAW,EAAE,SAAS;gBACxC,cAAc;gBAEd,cAAC,wBAAA,eAAA,EAAc,WAAW,0BAA0B,QAAQ,EAAE,UAAW,CAAA,GACvE,cAAC,yBAAA,MAAA,EAAK,WAAU,UAAS,KAAK,GAC3B,UAAA;kBAAA,YAAY,8BACV,wBAAA,YAAA,EAAW,WAAU,UAAS,SAAQ,SACpC,UAAc,cAAA;oBACb,IAAI;oBACJ,gBACE;kBACH,CAAA,EAAA,CACH;kBAGD,YAAY,iCACX,wBAAC,YAAA,EAAW,WAAU,UAAS,SAAQ,SACpC,UAAA;oBACC;sBACE,IAAI;sBACJ,gBACE;oBACJ;oBACA;sBACE,QACE,iFAAgC;wBAAO,CAAC,gBACtC,OAAO,aAAa,SAAS,WAAW;wBACxC,WAAU;oBAChB;kBAAA,EAAA,CAEJ;sBAAA,wBAGD,YAAW,EAAA,WAAU,UAAS,SAAQ,SACpC,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBACjB,CAAA,EAAA,CACH;gBAAA,EAAA,CACF,EACF,CAAA;cAAA;YACF;UAAA,EAAA,CACF;;MAAA;IAEJ;QAEA;MAAC,YAAY;MAAZ;QACC,MAAM,mBAAmB;QACzB,cAAc,MAAM,kBAAkB,IAAI;QAE1C,UAAA;cAAC,wBAAA,YAAY,OAAZ,EACE,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UACjB,CAAA,EAAA,CACH;cAEC,wBAAA,YAAY,MAAZ,EACE,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UACjB,CAAA,EAAA,CACH;QAAA;MAAA;IACF;QAEA;MAAC,YAAY;MAAZ;QACC,MAAM,mBAAmB;QACzB,cAAc,MAAM,kBAAkB,IAAI;QAE1C,UAAA;cAAC,wBAAA,YAAY,OAAZ,EACE,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UACjB,CAAA,EAAA,CACH;cAEC,wBAAA,YAAY,MAAZ,EACE,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UACjB,CAAA,EAAA,CACH;QAAA;MAAA;IACF;EACF,EAAA,CAAA;AAEJ;AAEA,IAAM,qBAAqB,CAAC,SAA6B;AACvD,QAAM,OAAO,qBAAqB,QAAW,QAAW,KAAK,MAAM;AAEnE,SAAO,KAAK,IAAI,CAAC,OAAO,WAAW;IACjC,GAAG;IACH,cAAc,KAAK,KAAK;EACxB,EAAA;AACJ;AAMA,IAAM,oBAAoB,MAAM;AACxB,QAAA,cAAc,iBAAiB,CAAC,UAAU;;AACxC,UAAA;MACJ,QAAAN,UAAS,CAAC;MACV,SAAS,CAAC;MACV,OAAO,CAAC;IAAA,MACN,WAAM,UAAU,YAAY,aAA5B,mBAAuC,wBAAuB,CAAA;AAElE,WAAO,CAAC,GAAGA,SAAQ,GAAG,QAAQ,GAAG,IAAI;EAAA,CACtC;AAED,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,UAAA,CAAA,CAAS,EACZ,CAAA;AAEJ;", "names": ["item", "index", "AdminInput<PERSON><PERSON><PERSON>", "name", "value", "label", "themeColorName", "NestedOption", "InputR<PERSON><PERSON>", "create", "_a", "serverStage", "error", "Layout.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Layout.Header", "Layout.Root"]}