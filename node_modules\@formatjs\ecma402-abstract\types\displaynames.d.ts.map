{"version": 3, "file": "displaynames.d.ts", "sourceRoot": "", "sources": ["displaynames.ts"], "names": [], "mappings": "AAAA,OAAO,EAAC,UAAU,EAAC,MAAM,QAAQ,CAAA;AAEjC,KAAK,WAAW,GAAG,MAAM,CAAA;AACzB,KAAK,UAAU,GAAG,MAAM,CAAA;AACxB,KAAK,UAAU,GAAG,MAAM,CAAA;AACxB,KAAK,YAAY,GAAG,MAAM,CAAA;AAC1B,KAAK,YAAY,GAAG,MAAM,CAAA;AAC1B,KAAK,iBAAiB,GAAG,MAAM,CAAA;AAE/B,MAAM,WAAW,gBAAgB;IAC/B;;;OAGG;IACH,KAAK,EAAE;QACL;;WAEG;QACH,QAAQ,EAAE;YACR,OAAO,EAAE;gBACP,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;gBACnC,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;gBAClC,IAAI,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;aAClC,CAAA;YACD,QAAQ,EAAE;gBACR,MAAM,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;gBACnC,KAAK,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;gBAClC,IAAI,EAAE,MAAM,CAAC,WAAW,EAAE,MAAM,CAAC,CAAA;aAClC,CAAA;SACF,CAAA;QACD,MAAM,EAAE;YACN,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;YAClC,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;YACjC,IAAI,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;SACjC,CAAA;QACD,MAAM,EAAE;YACN,MAAM,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;YAClC,KAAK,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;YACjC,IAAI,EAAE,MAAM,CAAC,UAAU,EAAE,MAAM,CAAC,CAAA;SACjC,CAAA;QACD,QAAQ,EAAE;YACR,MAAM,EAAE,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;YACpC,KAAK,EAAE,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;YACnC,IAAI,EAAE,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;SACnC,CAAA;QACD,QAAQ,EAAE;YACR,MAAM,EAAE,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;YACpC,KAAK,EAAE,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;YACnC,IAAI,EAAE,MAAM,CAAC,YAAY,EAAE,MAAM,CAAC,CAAA;SACnC,CAAA;QACD,aAAa,EAAE;YACb,MAAM,EAAE,MAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAA;YACzC,KAAK,EAAE,MAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAA;YACxC,IAAI,EAAE,MAAM,CAAC,iBAAiB,EAAE,MAAM,CAAC,CAAA;SACxC,CAAA;KACF,CAAA;IACD;;;;OAIG;IACH,QAAQ,EAAE;QACR,MAAM,EAAE,MAAM,CAAA;KACf,CAAA;CACF;AAED,MAAM,MAAM,sBAAsB,GAAG,UAAU,CAAC,gBAAgB,CAAC,CAAA"}