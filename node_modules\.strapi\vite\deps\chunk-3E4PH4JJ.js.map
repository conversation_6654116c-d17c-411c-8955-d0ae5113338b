{"version": 3, "sources": ["../../../date-fns/esm/toDate/index.js", "../../../date-fns/esm/_lib/addLeadingZeros/index.js", "../../../date-fns/esm/formatISO/index.js"], "sourcesContent": ["import _typeof from \"@babel/runtime/helpers/esm/typeof\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name toDate\n * @category Common Helpers\n * @summary Convert the given argument to an instance of Date.\n *\n * @description\n * Convert the given argument to an instance of Date.\n *\n * If the argument is an instance of Date, the function returns its clone.\n *\n * If the argument is a number, it is treated as a timestamp.\n *\n * If the argument is none of the above, the function returns Invalid Date.\n *\n * **Note**: *all* Date arguments passed to any *date-fns* function is processed by `toDate`.\n *\n * @param {Date|Number} argument - the value to convert\n * @returns {Date} the parsed date in the local time zone\n * @throws {TypeError} 1 argument required\n *\n * @example\n * // Clone the date:\n * const result = toDate(new Date(2014, 1, 11, 11, 30, 30))\n * //=> Tue Feb 11 2014 11:30:30\n *\n * @example\n * // Convert the timestamp to date:\n * const result = toDate(1392098430000)\n * //=> Tue Feb 11 2014 11:30:30\n */\nexport default function toDate(argument) {\n  requiredArgs(1, arguments);\n  var argStr = Object.prototype.toString.call(argument);\n\n  // Clone the date\n  if (argument instanceof Date || _typeof(argument) === 'object' && argStr === '[object Date]') {\n    // Prevent the date to lose the milliseconds when passed to new Date() in IE10\n    return new Date(argument.getTime());\n  } else if (typeof argument === 'number' || argStr === '[object Number]') {\n    return new Date(argument);\n  } else {\n    if ((typeof argument === 'string' || argStr === '[object String]') && typeof console !== 'undefined') {\n      // eslint-disable-next-line no-console\n      console.warn(\"Starting with v2.0.0-beta.1 date-fns doesn't accept strings as date arguments. Please use `parseISO` to parse strings. See: https://github.com/date-fns/date-fns/blob/master/docs/upgradeGuide.md#string-arguments\");\n      // eslint-disable-next-line no-console\n      console.warn(new Error().stack);\n    }\n    return new Date(NaN);\n  }\n}", "export default function addLeadingZeros(number, targetLength) {\n  var sign = number < 0 ? '-' : '';\n  var output = Math.abs(number).toString();\n  while (output.length < targetLength) {\n    output = '0' + output;\n  }\n  return sign + output;\n}", "import toDate from \"../toDate/index.js\";\nimport addLeadingZeros from \"../_lib/addLeadingZeros/index.js\";\nimport requiredArgs from \"../_lib/requiredArgs/index.js\";\n/**\n * @name formatISO\n * @category Common Helpers\n * @summary Format the date according to the ISO 8601 standard (https://support.sas.com/documentation/cdl/en/lrdict/64316/HTML/default/viewer.htm#a003169814.htm).\n *\n * @description\n * Return the formatted date string in ISO 8601 format. Options may be passed to control the parts and notations of the date.\n *\n * @param {Date|Number} date - the original date\n * @param {Object} [options] - an object with options.\n * @param {'extended'|'basic'} [options.format='extended'] - if 'basic', hide delimiters between date and time values.\n * @param {'complete'|'date'|'time'} [options.representation='complete'] - format date, time with local time zone, or both.\n * @returns {String} the formatted date string (in local time zone)\n * @throws {TypeError} 1 argument required\n * @throws {RangeError} `date` must not be Invalid Date\n * @throws {RangeError} `options.format` must be 'extended' or 'basic'\n * @throws {RangeError} `options.representation` must be 'date', 'time' or 'complete'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52))\n * //=> '2019-09-18T19:00:52Z'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601, short format (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { format: 'basic' })\n * //=> '20190918T190052'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, date only:\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'date' })\n * //=> '2019-09-18'\n *\n * @example\n * // Represent 18 September 2019 in ISO 8601 format, time only (local time zone is UTC):\n * const result = formatISO(new Date(2019, 8, 18, 19, 0, 52), { representation: 'time' })\n * //=> '19:00:52Z'\n */\nexport default function formatISO(date, options) {\n  var _options$format, _options$representati;\n  requiredArgs(1, arguments);\n  var originalDate = toDate(date);\n  if (isNaN(originalDate.getTime())) {\n    throw new RangeError('Invalid time value');\n  }\n  var format = String((_options$format = options === null || options === void 0 ? void 0 : options.format) !== null && _options$format !== void 0 ? _options$format : 'extended');\n  var representation = String((_options$representati = options === null || options === void 0 ? void 0 : options.representation) !== null && _options$representati !== void 0 ? _options$representati : 'complete');\n  if (format !== 'extended' && format !== 'basic') {\n    throw new RangeError(\"format must be 'extended' or 'basic'\");\n  }\n  if (representation !== 'date' && representation !== 'time' && representation !== 'complete') {\n    throw new RangeError(\"representation must be 'date', 'time', or 'complete'\");\n  }\n  var result = '';\n  var tzOffset = '';\n  var dateDelimiter = format === 'extended' ? '-' : '';\n  var timeDelimiter = format === 'extended' ? ':' : '';\n\n  // Representation is either 'date' or 'complete'\n  if (representation !== 'time') {\n    var day = addLeadingZeros(originalDate.getDate(), 2);\n    var month = addLeadingZeros(originalDate.getMonth() + 1, 2);\n    var year = addLeadingZeros(originalDate.getFullYear(), 4);\n\n    // yyyyMMdd or yyyy-MM-dd.\n    result = \"\".concat(year).concat(dateDelimiter).concat(month).concat(dateDelimiter).concat(day);\n  }\n\n  // Representation is either 'time' or 'complete'\n  if (representation !== 'date') {\n    // Add the timezone.\n    var offset = originalDate.getTimezoneOffset();\n    if (offset !== 0) {\n      var absoluteOffset = Math.abs(offset);\n      var hourOffset = addLeadingZeros(Math.floor(absoluteOffset / 60), 2);\n      var minuteOffset = addLeadingZeros(absoluteOffset % 60, 2);\n      // If less than 0, the sign is +, because it is ahead of time.\n      var sign = offset < 0 ? '+' : '-';\n      tzOffset = \"\".concat(sign).concat(hourOffset, \":\").concat(minuteOffset);\n    } else {\n      tzOffset = 'Z';\n    }\n    var hour = addLeadingZeros(originalDate.getHours(), 2);\n    var minute = addLeadingZeros(originalDate.getMinutes(), 2);\n    var second = addLeadingZeros(originalDate.getSeconds(), 2);\n\n    // If there's also date, separate it with time with 'T'\n    var separator = result === '' ? '' : 'T';\n\n    // Creates a time string consisting of hour, minute, and second, separated by delimiters, if defined.\n    var time = [hour, minute, second].join(timeDelimiter);\n\n    // HHmmss or HH:mm:ss.\n    result = \"\".concat(result).concat(separator).concat(time).concat(tzOffset);\n  }\n  return result;\n}"], "mappings": ";;;;;;;;AAgCe,SAAR,OAAwB,UAAU;AACvC,eAAa,GAAG,SAAS;AACzB,MAAI,SAAS,OAAO,UAAU,SAAS,KAAK,QAAQ;AAGpD,MAAI,oBAAoB,QAAQ,QAAQ,QAAQ,MAAM,YAAY,WAAW,iBAAiB;AAE5F,WAAO,IAAI,KAAK,SAAS,QAAQ,CAAC;AAAA,EACpC,WAAW,OAAO,aAAa,YAAY,WAAW,mBAAmB;AACvE,WAAO,IAAI,KAAK,QAAQ;AAAA,EAC1B,OAAO;AACL,SAAK,OAAO,aAAa,YAAY,WAAW,sBAAsB,OAAO,YAAY,aAAa;AAEpG,cAAQ,KAAK,oNAAoN;AAEjO,cAAQ,KAAK,IAAI,MAAM,EAAE,KAAK;AAAA,IAChC;AACA,WAAO,oBAAI,KAAK,GAAG;AAAA,EACrB;AACF;;;ACnDe,SAAR,gBAAiC,QAAQ,cAAc;AAC5D,MAAI,OAAO,SAAS,IAAI,MAAM;AAC9B,MAAI,SAAS,KAAK,IAAI,MAAM,EAAE,SAAS;AACvC,SAAO,OAAO,SAAS,cAAc;AACnC,aAAS,MAAM;AAAA,EACjB;AACA,SAAO,OAAO;AAChB;;;ACkCe,SAAR,UAA2B,MAAM,SAAS;AAC/C,MAAI,iBAAiB;AACrB,eAAa,GAAG,SAAS;AACzB,MAAI,eAAe,OAAO,IAAI;AAC9B,MAAI,MAAM,aAAa,QAAQ,CAAC,GAAG;AACjC,UAAM,IAAI,WAAW,oBAAoB;AAAA,EAC3C;AACA,MAAI,SAAS,QAAQ,kBAAkB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,YAAY,QAAQ,oBAAoB,SAAS,kBAAkB,UAAU;AAC9K,MAAI,iBAAiB,QAAQ,wBAAwB,YAAY,QAAQ,YAAY,SAAS,SAAS,QAAQ,oBAAoB,QAAQ,0BAA0B,SAAS,wBAAwB,UAAU;AAChN,MAAI,WAAW,cAAc,WAAW,SAAS;AAC/C,UAAM,IAAI,WAAW,sCAAsC;AAAA,EAC7D;AACA,MAAI,mBAAmB,UAAU,mBAAmB,UAAU,mBAAmB,YAAY;AAC3F,UAAM,IAAI,WAAW,sDAAsD;AAAA,EAC7E;AACA,MAAI,SAAS;AACb,MAAI,WAAW;AACf,MAAI,gBAAgB,WAAW,aAAa,MAAM;AAClD,MAAI,gBAAgB,WAAW,aAAa,MAAM;AAGlD,MAAI,mBAAmB,QAAQ;AAC7B,QAAI,MAAM,gBAAgB,aAAa,QAAQ,GAAG,CAAC;AACnD,QAAI,QAAQ,gBAAgB,aAAa,SAAS,IAAI,GAAG,CAAC;AAC1D,QAAI,OAAO,gBAAgB,aAAa,YAAY,GAAG,CAAC;AAGxD,aAAS,GAAG,OAAO,IAAI,EAAE,OAAO,aAAa,EAAE,OAAO,KAAK,EAAE,OAAO,aAAa,EAAE,OAAO,GAAG;AAAA,EAC/F;AAGA,MAAI,mBAAmB,QAAQ;AAE7B,QAAI,SAAS,aAAa,kBAAkB;AAC5C,QAAI,WAAW,GAAG;AAChB,UAAI,iBAAiB,KAAK,IAAI,MAAM;AACpC,UAAI,aAAa,gBAAgB,KAAK,MAAM,iBAAiB,EAAE,GAAG,CAAC;AACnE,UAAI,eAAe,gBAAgB,iBAAiB,IAAI,CAAC;AAEzD,UAAI,OAAO,SAAS,IAAI,MAAM;AAC9B,iBAAW,GAAG,OAAO,IAAI,EAAE,OAAO,YAAY,GAAG,EAAE,OAAO,YAAY;AAAA,IACxE,OAAO;AACL,iBAAW;AAAA,IACb;AACA,QAAI,OAAO,gBAAgB,aAAa,SAAS,GAAG,CAAC;AACrD,QAAI,SAAS,gBAAgB,aAAa,WAAW,GAAG,CAAC;AACzD,QAAI,SAAS,gBAAgB,aAAa,WAAW,GAAG,CAAC;AAGzD,QAAI,YAAY,WAAW,KAAK,KAAK;AAGrC,QAAI,OAAO,CAAC,MAAM,QAAQ,MAAM,EAAE,KAAK,aAAa;AAGpD,aAAS,GAAG,OAAO,MAAM,EAAE,OAAO,SAAS,EAAE,OAAO,IAAI,EAAE,OAAO,QAAQ;AAAA,EAC3E;AACA,SAAO;AACT;", "names": []}