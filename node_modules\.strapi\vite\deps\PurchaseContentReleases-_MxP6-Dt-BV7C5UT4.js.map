{"version": 3, "sources": ["../../../@strapi/content-releases/admin/src/pages/PurchaseContentReleases.tsx"], "sourcesContent": ["import { Layouts } from '@strapi/admin/strapi-admin';\nimport { Box, Main, EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { ExternalLink } from '@strapi/icons';\nimport { EmptyPermissions } from '@strapi/icons/symbols';\nimport { useIntl } from 'react-intl';\n\nconst PurchaseContentReleases = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Layouts.Root>\n      <Main>\n        <Layouts.Header\n          title={formatMessage({\n            id: 'content-releases.pages.Releases.title',\n            defaultMessage: 'Releases',\n          })}\n          subtitle={formatMessage({\n            id: 'content-releases.pages.PurchaseRelease.subTitle',\n            defaultMessage: 'Manage content updates and releases.',\n          })}\n        />\n        <Box paddingLeft={10} paddingRight={10}>\n          <EmptyStateLayout\n            icon={<EmptyPermissions width=\"16rem\" />}\n            content={formatMessage({\n              id: 'content-releases.pages.PurchaseRelease.not-available',\n              defaultMessage:\n                'Releases is only available as part of a paid plan. Upgrade to create and manage releases.',\n            })}\n            action={\n              <LinkButton\n                variant=\"default\"\n                endIcon={<ExternalLink />}\n                href=\"https://strapi.io/pricing-self-hosted?utm_campaign=Growth-Experiments&utm_source=In-Product&utm_medium=Releases\"\n                isExternal\n                target=\"_blank\"\n              >\n                {formatMessage({\n                  id: 'global.learn-more',\n                  defaultMessage: 'Learn more',\n                })}\n              </LinkButton>\n            }\n          />\n        </Box>\n      </Main>\n    </Layouts.Root>\n  );\n};\n\nexport { PurchaseContentReleases };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMA,IAAM,0BAA0B,MAAM;AAC9B,QAAA,EAAE,cAAA,IAAkB,QAAA;AAE1B,aACG,wBAAA,QAAQ,MAAR,EACC,cAAA,yBAAC,MACC,EAAA,UAAA;QAAA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH;QACC,wBAAA,KAAA,EAAI,aAAa,IAAI,cAAc,IAClC,cAAA;MAAC;MAAA;QACC,UAAM,wBAAC,cAAiB,EAAA,OAAM,QAAQ,CAAA;QACtC,SAAS,cAAc;UACrB,IAAI;UACJ,gBACE;QAAA,CACH;QACD,YACE;UAAC;UAAA;YACC,SAAQ;YACR,aAAA,wBAAU,eAAa,CAAA,CAAA;YACvB,MAAK;YACL,YAAU;YACV,QAAO;YAEN,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QACH;MAAA;IAAA,EAAA,CAGN;EAAA,EACF,CAAA,EACF,CAAA;AAEJ;", "names": []}