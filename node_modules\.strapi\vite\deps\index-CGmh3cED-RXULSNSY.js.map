{"version": 3, "sources": ["../../../@strapi/review-workflows/admin/src/routes/settings/index.tsx"], "sourcesContent": ["/* eslint-disable check-file/no-index */\n/* eslint-disable check-file/filename-naming-convention */\nimport * as React from 'react';\n\nimport { Page, useTracking, ConfirmDialog, useRBAC, Table } from '@strapi/admin/strapi-admin';\nimport { useLicenseLimits } from '@strapi/admin/strapi-admin/ee';\nimport { Flex, IconButton, TF<PERSON>er, Typography, LinkButton, Dialog } from '@strapi/design-system';\nimport { Pencil, Plus, Trash } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { NavLink, Link, useNavigate } from 'react-router-dom';\n\nimport { LimitsModal } from '../../components/LimitsModal';\nimport { CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME } from '../../constants';\nimport { useTypedSelector } from '../../modules/hooks';\nimport { ContentType, useGetContentTypesQuery } from '../../services/content-manager';\n\nimport * as Layout from './components/Layout';\nimport { useReviewWorkflows } from './hooks/useReviewWorkflows';\n\nexport const ReviewWorkflowsListView = () => {\n  const { formatMessage } = useIntl();\n  const navigate = useNavigate();\n  const { trackUsage } = useTracking();\n  const [workflowToDelete, setWorkflowToDelete] = React.useState<string | null>(null);\n  const [showLimitModal, setShowLimitModal] = React.useState<boolean>(false);\n  const { data, isLoading: isLoadingModels } = useGetContentTypesQuery();\n  const { meta, workflows, isLoading, delete: deleteAction } = useReviewWorkflows();\n  const { getFeature, isLoading: isLicenseLoading } = useLicenseLimits();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['review-workflows']\n  );\n  const {\n    allowedActions: { canCreate, canRead, canUpdate, canDelete },\n  } = useRBAC(permissions);\n\n  const limits = getFeature('review-workflows');\n  const numberOfWorkflows = limits?.[CHARGEBEE_WORKFLOW_ENTITLEMENT_NAME] as string;\n\n  const handleDeleteWorkflow = (workflowId: string) => {\n    setWorkflowToDelete(workflowId);\n  };\n\n  const toggleConfirmDeleteDialog = () => {\n    setWorkflowToDelete(null);\n  };\n\n  const handleConfirmDeleteDialog = async () => {\n    if (!workflowToDelete) return;\n\n    await deleteAction(workflowToDelete);\n\n    setWorkflowToDelete(null);\n  };\n\n  const handleCreateClick: React.MouseEventHandler<HTMLAnchorElement> &\n    ((event: React.MouseEvent<HTMLButtonElement, MouseEvent>) => void) = (event) => {\n    event.preventDefault();\n    /**\n     * If the current license has a workflow limit:\n     * check if the total count of workflows exceeds that limit. If so,\n     * prevent the navigation and show the limits overlay.\n     *\n     * If the current license does not have a limit (e.g. offline license):\n     * allow the user to navigate to the create-view. In case they exceed the\n     * current hard-limit of 200 they will see an error thrown by the API.\n     */\n\n    if (numberOfWorkflows && meta && meta?.workflowCount >= parseInt(numberOfWorkflows, 10)) {\n      event.preventDefault();\n      setShowLimitModal(true);\n    } else {\n      navigate('create');\n      trackUsage('willCreateWorkflow');\n    }\n  };\n\n  /**\n   * If the current license has a limit:\n   * check if the total count of workflows or stages exceeds that limit and display\n   * the limits modal on page load. It can be closed by the user, but the\n   * API will throw an error in case they try to create a new workflow or update the\n   * stages.\n   *\n   * If the current license does not have a limit (e.g. offline license):\n   * do nothing (for now). In case they are trying to create the 201st workflow/ stage\n   * the API will throw an error.\n   *\n   */\n  React.useEffect(() => {\n    if (!isLoading && !isLicenseLoading) {\n      if (numberOfWorkflows && meta && meta?.workflowCount > parseInt(numberOfWorkflows, 10)) {\n        setShowLimitModal(true);\n      }\n    }\n  }, [isLicenseLoading, isLoading, meta, meta?.workflowCount, numberOfWorkflows]);\n\n  const headers = [\n    {\n      label: formatMessage({\n        id: 'Settings.review-workflows.list.page.list.column.name.title',\n        defaultMessage: 'Name',\n      }),\n      name: 'name',\n    },\n    {\n      label: formatMessage({\n        id: 'Settings.review-workflows.list.page.list.column.stages.title',\n        defaultMessage: 'Stages',\n      }),\n      name: 'stages',\n    },\n    {\n      label: formatMessage({\n        id: 'Settings.review-workflows.list.page.list.column.contentTypes.title',\n        defaultMessage: 'Content Types',\n      }),\n      name: 'content-types',\n    },\n  ];\n\n  if (isLoading || isLoadingModels) {\n    return <Page.Loading />;\n  }\n\n  const contentTypes = Object.values(data ?? {}).reduce<ContentType[]>((acc, curr) => {\n    acc.push(...curr);\n    return acc;\n  }, []);\n\n  return (\n    <>\n      <Layout.Header\n        primaryAction={\n          canCreate ? (\n            <LinkButton\n              startIcon={<Plus />}\n              size=\"S\"\n              tag={NavLink}\n              to=\"create\"\n              onClick={handleCreateClick}\n            >\n              {formatMessage({\n                id: 'Settings.review-workflows.list.page.create',\n                defaultMessage: 'Create new workflow',\n              })}\n            </LinkButton>\n          ) : null\n        }\n        subtitle={formatMessage({\n          id: 'Settings.review-workflows.list.page.subtitle',\n          defaultMessage: 'Manage your content review process',\n        })}\n        title={formatMessage({\n          id: 'Settings.review-workflows.list.page.title',\n          defaultMessage: 'Review Workflows',\n        })}\n      />\n\n      <Layout.Root>\n        <Table.Root\n          isLoading={isLoading}\n          rows={workflows}\n          footer={\n            canCreate ? (\n              <TFooter icon={<Plus />} onClick={handleCreateClick}>\n                {formatMessage({\n                  id: 'Settings.review-workflows.list.page.create',\n                  defaultMessage: 'Create new workflow',\n                })}\n              </TFooter>\n            ) : null\n          }\n          headers={headers}\n        >\n          <Table.Content>\n            <Table.Head>\n              {headers.map((head) => (\n                <Table.HeaderCell key={head.name} {...head} />\n              ))}\n            </Table.Head>\n\n            <Table.Body>\n              {workflows.map((workflow) => (\n                <Table.Row\n                  onClick={() => {\n                    navigate(`${workflow.id}`);\n                  }}\n                  key={workflow.id}\n                >\n                  <Table.Cell width=\"25rem\">\n                    <Typography textColor=\"neutral800\" fontWeight=\"bold\" ellipsis>\n                      {workflow.name}\n                    </Typography>\n                  </Table.Cell>\n                  <Table.Cell>\n                    <Typography textColor=\"neutral800\">{workflow.stages.length}</Typography>\n                  </Table.Cell>\n                  <Table.Cell>\n                    <Typography textColor=\"neutral800\">\n                      {workflow.contentTypes\n                        .map((uid: string) => {\n                          const contentType = contentTypes.find(\n                            (contentType) => contentType.uid === uid\n                          );\n\n                          return contentType?.info.displayName ?? '';\n                        })\n                        .join(', ')}\n                    </Typography>\n                  </Table.Cell>\n                  <Table.Cell>\n                    <Flex alignItems=\"center\" justifyContent=\"end\">\n                      {canRead || canUpdate ? (\n                        <IconButton\n                          tag={Link}\n                          to={workflow.id.toString()}\n                          label={formatMessage(\n                            {\n                              id: 'Settings.review-workflows.list.page.list.column.actions.edit.label',\n                              defaultMessage: 'Edit {name}',\n                            },\n                            { name: workflow.name }\n                          )}\n                          variant=\"ghost\"\n                        >\n                          <Pencil />\n                        </IconButton>\n                      ) : null}\n                      {workflows.length > 1 && canDelete ? (\n                        <IconButton\n                          withTooltip={false}\n                          label={formatMessage(\n                            {\n                              id: 'Settings.review-workflows.list.page.list.column.actions.delete.label',\n                              defaultMessage: 'Delete {name}',\n                            },\n                            { name: 'Default workflow' }\n                          )}\n                          variant=\"ghost\"\n                          onClick={(e) => {\n                            e.stopPropagation();\n                            handleDeleteWorkflow(String(workflow.id));\n                          }}\n                        >\n                          <Trash />\n                        </IconButton>\n                      ) : null}\n                    </Flex>\n                  </Table.Cell>\n                </Table.Row>\n              ))}\n            </Table.Body>\n          </Table.Content>\n        </Table.Root>\n\n        <Dialog.Root open={!!workflowToDelete} onOpenChange={toggleConfirmDeleteDialog}>\n          <ConfirmDialog onConfirm={handleConfirmDeleteDialog}>\n            {formatMessage({\n              id: 'Settings.review-workflows.list.page.delete.confirm.body',\n              defaultMessage:\n                'If you remove this worfklow, all stage-related information will be removed for this content-type. Are you sure you want to remove it?',\n            })}\n          </ConfirmDialog>\n        </Dialog.Root>\n\n        <LimitsModal.Root open={showLimitModal} onOpenChange={() => setShowLimitModal(false)}>\n          <LimitsModal.Title>\n            {formatMessage({\n              id: 'Settings.review-workflows.list.page.workflows.limit.title',\n              defaultMessage: 'You’ve reached the limit of workflows in your plan',\n            })}\n          </LimitsModal.Title>\n\n          <LimitsModal.Body>\n            {formatMessage({\n              id: 'Settings.review-workflows.list.page.workflows.limit.body',\n              defaultMessage: 'Delete a workflow or contact Sales to enable more workflows.',\n            })}\n          </LimitsModal.Body>\n        </LimitsModal.Root>\n      </Layout.Root>\n    </>\n  );\n};\n\nconst ProtectedListPage = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['review-workflows']?.main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ReviewWorkflowsListView />\n    </Page.Protect>\n  );\n};\n\nexport { ProtectedListPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBO,IAAM,0BAA0B,MAAM;AACrC,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,WAAW,YAAA;AACX,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,CAAC,kBAAkB,mBAAmB,IAAU,eAAwB,IAAI;AAClF,QAAM,CAAC,gBAAgB,iBAAiB,IAAU,eAAkB,KAAK;AACzE,QAAM,EAAE,MAAM,WAAW,gBAAA,IAAoB,wBAAwB;AACrE,QAAM,EAAE,MAAM,WAAW,WAAW,QAAQ,aAAA,IAAiB,mBAAA;AAC7D,QAAM,EAAE,YAAY,WAAW,iBAAA,IAAqB,iBAAiB;AACrE,QAAM,cAAc;IAClB,CAAC,UAAA;;AAAU,yBAAM,UAAU,YAAY,aAA5B,mBAAuC;;EAAkB;AAEhE,QAAA;IACJ,gBAAgB,EAAE,WAAW,SAAS,WAAW,UAAU;EAAA,IACzD,QAAQ,WAAW;AAEjB,QAAA,SAAS,WAAW,kBAAkB;AACtC,QAAA,oBAAoB,iCAAS;AAE7B,QAAA,uBAAuB,CAAC,eAAuB;AACnD,wBAAoB,UAAU;EAAA;AAGhC,QAAM,4BAA4B,MAAM;AACtC,wBAAoB,IAAI;EAAA;AAG1B,QAAM,4BAA4B,YAAY;AAC5C,QAAI,CAAC;AAAkB;AAEvB,UAAM,aAAa,gBAAgB;AAEnC,wBAAoB,IAAI;EAAA;AAGpB,QAAA,oBACiE,CAAC,UAAU;AAChF,UAAM,eAAe;AAWrB,QAAI,qBAAqB,SAAQ,6BAAM,kBAAiB,SAAS,mBAAmB,EAAE,GAAG;AACvF,YAAM,eAAe;AACrB,wBAAkB,IAAI;IAAA,OACjB;AACL,eAAS,QAAQ;AACjB,iBAAW,oBAAoB;IACjC;EAAA;AAeF,EAAM,gBAAU,MAAM;AAChB,QAAA,CAAC,aAAa,CAAC,kBAAkB;AACnC,UAAI,qBAAqB,SAAQ,6BAAM,iBAAgB,SAAS,mBAAmB,EAAE,GAAG;AACtF,0BAAkB,IAAI;MACxB;IACF;EAAA,GACC,CAAC,kBAAkB,WAAW,MAAM,6BAAM,eAAe,iBAAiB,CAAC;AAE9E,QAAM,UAAU;IACd;MACE,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,MAAM;IACR;IACA;MACE,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,MAAM;IACR;IACA;MACE,OAAO,cAAc;QACnB,IAAI;QACJ,gBAAgB;MAAA,CACjB;MACD,MAAM;IACR;EAAA;AAGF,MAAI,aAAa,iBAAiB;AACzB,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEM,QAAA,eAAe,OAAO,OAAO,QAAQ,CAAE,CAAA,EAAE,OAAsB,CAAC,KAAK,SAAS;AAC9E,QAAA,KAAK,GAAG,IAAI;AACT,WAAA;EACT,GAAG,CAAE,CAAA;AAEL,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAA;MAACA;MAAA;QACC,eACE,gBACE;UAAC;UAAA;YACC,eAAA,wBAAY,eAAK,CAAA,CAAA;YACjB,MAAK;YACL,KAAK;YACL,IAAG;YACH,SAAS;YAER,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QAAA,IAED;QAEN,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,OAAO,cAAc;UACnB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH;QAEA,yBAACC,MAAA,EACC,UAAA;UAAA;QAAC,MAAM;QAAN;UACC;UACA,MAAM;UACN,QACE,gBACE,wBAAC,SAAQ,EAAA,UAAA,wBAAO,eAAK,CAAA,CAAA,GAAI,SAAS,mBAC/B,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EAAA,CACH,IACE;UAEN;UAEA,cAAA,yBAAC,MAAM,SAAN,EACC,UAAA;gBAAA,wBAAC,MAAM,MAAN,EACE,UAAQ,QAAA,IAAI,CAAC,aACZ,wBAAC,MAAM,YAAN,EAAkC,GAAG,KAAA,GAAf,KAAK,IAAgB,CAC7C,EAAA,CACH;gBAAA,wBAEC,MAAM,MAAN,EACE,UAAU,UAAA,IAAI,CAAC,iBACd;cAAC,MAAM;cAAN;gBACC,SAAS,MAAM;AACJ,2BAAA,GAAG,SAAS,EAAE,EAAE;gBAC3B;gBAGA,UAAA;sBAAA,wBAAC,MAAM,MAAN,EAAW,OAAM,SAChB,cAAC,wBAAA,YAAA,EAAW,WAAU,cAAa,YAAW,QAAO,UAAQ,MAC1D,UAAA,SAAS,KACZ,CAAA,EAAA,CACF;sBACA,wBAAC,MAAM,MAAN,EACC,cAAA,wBAAC,YAAW,EAAA,WAAU,cAAc,UAAA,SAAS,OAAO,OAAA,CAAO,EAC7D,CAAA;sBACC,wBAAA,MAAM,MAAN,EACC,cAAC,wBAAA,YAAA,EAAW,WAAU,cACnB,UAAS,SAAA,aACP,IAAI,CAAC,QAAgB;AACpB,0BAAM,cAAc,aAAa;sBAC/B,CAACC,iBAAgBA,aAAY,QAAQ;oBAAA;AAGhC,4BAAA,2CAAa,KAAK,gBAAe;kBACzC,CAAA,EACA,KAAK,IAAI,EAAA,CACd,EACF,CAAA;sBACA,wBAAC,MAAM,MAAN,EACC,cAAA,yBAAC,MAAK,EAAA,YAAW,UAAS,gBAAe,OACtC,UAAA;oBAAA,WAAW,gBACV;sBAAC;sBAAA;wBACC,KAAK;wBACL,IAAI,SAAS,GAAG,SAAS;wBACzB,OAAO;0BACL;4BACE,IAAI;4BACJ,gBAAgB;0BAClB;0BACA,EAAE,MAAM,SAAS,KAAK;wBACxB;wBACA,SAAQ;wBAER,cAAA,wBAAC,eAAO,CAAA,CAAA;sBAAA;oBAAA,IAER;oBACH,UAAU,SAAS,KAAK,gBACvB;sBAAC;sBAAA;wBACC,aAAa;wBACb,OAAO;0BACL;4BACE,IAAI;4BACJ,gBAAgB;0BAClB;0BACA,EAAE,MAAM,mBAAmB;wBAC7B;wBACA,SAAQ;wBACR,SAAS,CAAC,MAAM;AACd,4BAAE,gBAAgB;AACG,+CAAA,OAAO,SAAS,EAAE,CAAC;wBAC1C;wBAEA,cAAA,wBAAC,cAAM,CAAA,CAAA;sBAAA;oBAAA,IAEP;kBAAA,EAAA,CACN,EACF,CAAA;gBAAA;cAAA;cA7DK,SAAS;YA+DjB,CAAA,EAAA,CACH;UAAA,EAAA,CACF;QAAA;MACF;UAEC,wBAAA,OAAO,MAAP,EAAY,MAAM,CAAC,CAAC,kBAAkB,cAAc,2BACnD,cAAA,wBAAC,eAAc,EAAA,WAAW,2BACvB,UAAc,cAAA;QACb,IAAI;QACJ,gBACE;MAAA,CACH,EAAA,CACH,EACF,CAAA;UAEA,yBAAC,YAAY,MAAZ,EAAiB,MAAM,gBAAgB,cAAc,MAAM,kBAAkB,KAAK,GACjF,UAAA;YAAC,wBAAA,YAAY,OAAZ,EACE,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QACjB,CAAA,EAAA,CACH;YAEC,wBAAA,YAAY,MAAZ,EACE,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QACjB,CAAA,EAAA,CACH;MAAA,EAAA,CACF;IAAA,EAAA,CACF;EACF,EAAA,CAAA;AAEJ;AAEA,IAAM,oBAAoB,MAAM;AAC9B,QAAM,cAAc;IAClB,CAAC,UAAA;;AAAU,+BAAM,UAAU,YAAY,aAA5B,mBAAuC,wBAAvC,mBAA4D;;EAAA;AAGzE,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,yBAAA,CAAA,CAAwB,EAC3B,CAAA;AAEJ;", "names": ["Layout.Header", "Layout.Root", "contentType"]}