{"name": "@formatjs/fast-memoize", "version": "2.0.1", "description": "fork of fast-memoize and support esm", "main": "index.js", "module": "lib/index.js", "repository": {"type": "git", "url": "git+ssh://**************/formatjs/formatjs.git"}, "keywords": ["intl", "fast-memoize", "memoize", "i18n"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/formatjs/formatjs/issues"}, "homepage": "https://github.com/formatjs/formatjs#readme", "dependencies": {"tslib": "^2.4.0"}}