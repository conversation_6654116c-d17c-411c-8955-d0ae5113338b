import {
  useLicenseLimits
} from "./chunk-N562NCQ4.js";
import {
  selectAdminPermissions
} from "./chunk-4SE2T7NO.js";
import {
  useRBAC
} from "./chunk-4C2ZQ5OG.js";
import {
  Flex,
  Grid,
  Link,
  TooltipImpl,
  Typography,
  useIntl,
  useSelector
} from "./chunk-FGNN7I5W.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$3,
  ForwardRef$3t
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/AdminSeatInfo-BtXH5sC-.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var BILLING_STRAPI_CLOUD_URL = "https://cloud.strapi.io/profile/billing";
var BILLING_SELF_HOSTED_URL = "https://strapi.io/billing/request-seats";
var AdminSeatInfoEE = () => {
  const { formatMessage } = useIntl();
  const { settings } = useSelector(selectAdminPermissions);
  const {
    isLoading: isRBACLoading,
    allowedActions: { canRead, canCreate, canUpdate, canDelete }
  } = useRBAC((settings == null ? void 0 : settings.users) ?? {});
  const {
    license,
    isError,
    isLoading: isLicenseLoading
  } = useLicenseLimits({
    // TODO: this creates a waterfall which we should avoid to render earlier, but for that
    // we will have to move away from data-fetching hooks to query functions.
    // Short-term we could at least implement a loader, for the user to have visual feedback
    // in case the requests take a while
    enabled: !isRBACLoading && canRead && canCreate && canUpdate && canDelete
  });
  const isLoading = isRBACLoading || isLicenseLoading;
  if (isError || isLoading || !license) {
    return null;
  }
  const { licenseLimitStatus, enforcementUserCount, permittedSeats, isHostedOnStrapiCloud } = license;
  if (!permittedSeats) {
    return null;
  }
  return (0, import_jsx_runtime.jsxs)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: [
    (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({
      id: "Settings.application.admin-seats",
      defaultMessage: "Admin seats"
    }) }),
    (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, children: [
      (0, import_jsx_runtime.jsx)(Flex, { children: (0, import_jsx_runtime.jsx)(Typography, { tag: "p", children: formatMessage(
        {
          id: "Settings.application.ee.admin-seats.count",
          defaultMessage: "<text>{enforcementUserCount}</text>/{permittedSeats}"
        },
        {
          permittedSeats,
          enforcementUserCount,
          text: (chunks) => (0, import_jsx_runtime.jsx)(
            Typography,
            {
              fontWeight: "semiBold",
              textColor: enforcementUserCount > permittedSeats ? "danger500" : void 0,
              children: chunks
            }
          )
        }
      ) }) }),
      licenseLimitStatus === "OVER_LIMIT" && (0, import_jsx_runtime.jsx)(
        TooltipImpl,
        {
          label: formatMessage({
            id: "Settings.application.ee.admin-seats.at-limit-tooltip",
            defaultMessage: "At limit: add seats to invite more users"
          }),
          children: (0, import_jsx_runtime.jsx)(ForwardRef$3, { width: "1.4rem", height: "1.4rem", fill: "danger500" })
        }
      )
    ] }),
    (0, import_jsx_runtime.jsx)(
      Link,
      {
        href: isHostedOnStrapiCloud ? BILLING_STRAPI_CLOUD_URL : BILLING_SELF_HOSTED_URL,
        isExternal: true,
        endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3t, {}),
        children: formatMessage(
          {
            id: "Settings.application.ee.admin-seats.add-seats",
            defaultMessage: "{isHostedOnStrapiCloud, select, true {Add seats} other {Contact sales}}"
          },
          { isHostedOnStrapiCloud }
        )
      }
    )
  ] });
};
export {
  AdminSeatInfoEE
};
//# sourceMappingURL=AdminSeatInfo-BtXH5sC--E6R2VU5T.js.map
