const __vite__fileDeps=["strapi-YzJfjJ2z.js","strapi-COJtagOC.css"],__vite__mapDeps=i=>i.map(i=>__vite__fileDeps[i]);
import{at as I,au as M,r as g,bV as w,b$ as T,c9 as D,ap as A,ca as O,bj as C,bk as U,m as a,n as N,aV as R,L as V,cb as $,ba as B,a5 as H,cc as K,cd as G,ce as F,cf as W,S as P,cg as Y,ch as q,_ as z}from"./strapi-YzJfjJ2z.js";import{s as J}from"./sortBy-vMk_GNbv.js";import{s as Q}from"./selectors-ZSFBgSp8-CGZgPRVm.js";import"./_baseMap-LzQFtWYw.js";import"./_baseEach-BkkNIx9z.js";const X=n=>n.map(i=>{const r=i.links.map(o=>({...o,isDisplayed:!1}));return{...i,links:r}}),Z=()=>{const[{isLoading:n,menu:i},r]=g.useState({isLoading:!0,menu:[]}),o=w("useSettingsMenu",s=>s.checkUserHasPermissions),b=T("useSettingsMenu",s=>s.shouldUpdateStrapi),m=D("useSettingsMenu",s=>s.settings),c=A(Q),h=g.useMemo(()=>O(),[]),{admin:e,global:t}=C(h,async()=>(await U(()=>import("./strapi-YzJfjJ2z.js").then(s=>s.jO),__vite__mapDeps([0,1])).then(s=>s.K)).SETTINGS_LINKS_EE(),{combine(s,l){return{admin:[...l.admin,...s.admin],global:[...s.global,...l.global]}},defaultValue:{admin:[],global:[]}}),f=g.useCallback(s=>{if(!s.id)throw new Error("The settings menu item must have an id attribute.");return{...s,permissions:c.settings?.[s.id]?.main??[]}},[c.settings]);return g.useEffect(()=>{const s=async()=>{const _=await(S=>Promise.all(S.reduce((d,L,k)=>{const x=L.links.map(async(v,p)=>({hasPermission:(await o(v.permissions)).length>0,sectionIndex:k,linkIndex:p}));return[...d,...x]},[])))(j);r(S=>({...S,isLoading:!1,menu:j.map((d,L)=>({...d,links:d.links.map((k,x)=>{const v=_.find(p=>p.sectionIndex===L&&p.linkIndex===x);return{...k,isDisplayed:!!v?.hasPermission}})}))}))},{global:l,...E}=m,j=X([{...l,links:J([...l.links,...t.map(f)],u=>u.id).map(u=>({...u,hasNotification:u.id==="000-application-infos"&&b}))},{id:"permissions",intlLabel:{id:"Settings.permissions",defaultMessage:"Administration Panel"},links:e.map(f)},...Object.values(E)]);s()},[e,t,m,b,f,o]),{isLoading:n,menu:i.map(s=>({...s,links:s.links.filter(l=>l.isDisplayed)}))}},y=P(q)`
  right: 15px;
  position: absolute;
  bottom: 50%;
  transform: translateY(50%);

  path {
    fill: ${({theme:n})=>n.colors.warning500};
  }
`,ss=P(Y)`
  &.active ${y} {
    right: 13px;
  }
`,ts=({menu:n})=>{const{formatMessage:i}=M(),{trackUsage:r}=B(),{pathname:o}=H(),m=n.filter(e=>!e.links.every(t=>t.isDisplayed===!1)).map(e=>({...e,title:e.intlLabel,links:e.links.map(t=>({...t,title:t.intlLabel,name:t.id}))})),c=i({id:"global.settings",defaultMessage:"Settings"}),h=e=>()=>{r("willNavigate",{from:o,to:e})};return a.jsxs(K,{"aria-label":c,children:[a.jsx(G,{label:c}),a.jsx(F,{children:m.map(e=>a.jsx(W,{label:i(e.intlLabel),children:e.links.map(t=>a.jsxs(ss,{tag:z,withBullet:t.hasNotification,to:t.to,onClick:h(t.to),position:"relative",children:[i(t.intlLabel),t?.licenseOnly&&a.jsx(y,{width:"1.5rem",height:"1.5rem"})]},t.id))},e.id))})]})},rs=()=>{const n=I("/settings/:settingId/*"),{formatMessage:i}=M(),{isLoading:r,menu:o}=Z();return r?a.jsx(N.Loading,{}):n?.params.settingId?a.jsxs(V.Root,{sideNav:a.jsx(ts,{menu:o}),children:[a.jsx(N.Title,{children:i({id:"global.settings",defaultMessage:"Settings"})}),a.jsx($,{})]}):a.jsx(R,{to:"application-infos"})};export{rs as Layout};
