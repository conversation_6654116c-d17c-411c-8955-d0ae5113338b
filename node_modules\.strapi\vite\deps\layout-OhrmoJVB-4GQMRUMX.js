import {
  Disconnect<PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>per,
  <PERSON><PERSON>lli<PERSON>
} from "./chunk-I66PR7OD.js";
import "./chunk-3O7FGDSC.js";
import {
  CardDragPreview
} from "./chunk-PVIQSKRY.js";
import {
  ItemTypes
} from "./chunk-Y4KMKP2D.js";
import {
  useTypedDispatch,
  useTypedSelector
} from "./chunk-QLLK5WRM.js";
import "./chunk-3TBNZ2ES.js";
import {
  COLLECTION_TYPES,
  DocumentStatus,
  HOOKS,
  SINGLE_TYPES,
  getTranslation,
  setInitialData,
  useContentTypeSchema,
  useGetAllContentTypeSettingsQuery,
  useGetInitialDataQuery,
  useIntl
} from "./chunk-D63J2BWQ.js";
import "./chunk-RPX6VIML.js";
import {
  useDragLayer
} from "./chunk-VOKTPSOW.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  Page,
  useAPIErrorHandler,
  useAuth,
  useGuidedTour,
  useNotification,
  useQueryParams,
  useStrapiApp
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Flex,
  IconButton,
  SubNav,
  SubNavHeader,
  SubNavLink,
  SubNavSection,
  SubNavSections,
  Typography,
  require_lib,
  useCollator,
  useFilter,
  useNotifyAT
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  NavLink,
  Navigate,
  Outlet,
  useLocation,
  useMatch
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$3H,
  ForwardRef$3V,
  ForwardRef$4D,
  ForwardRef$j
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/layout-OhrmoJVB.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_react = __toESM(require_react(), 1);
var import_qs = __toESM(require_lib(), 1);
function getStyle(initialOffset, currentOffset, mouseOffset) {
  if (!initialOffset || !currentOffset || !mouseOffset) {
    return { display: "none" };
  }
  const { x, y } = mouseOffset;
  return {
    transform: `translate(${x}px, ${y}px)`
  };
}
var DragLayer = ({ renderItem }) => {
  const { itemType, isDragging, item, initialOffset, currentOffset, mouseOffset } = useDragLayer(
    (monitor) => ({
      item: monitor.getItem(),
      itemType: monitor.getItemType(),
      initialOffset: monitor.getInitialSourceClientOffset(),
      currentOffset: monitor.getSourceClientOffset(),
      isDragging: monitor.isDragging(),
      mouseOffset: monitor.getClientOffset()
    })
  );
  if (!isDragging) {
    return null;
  }
  return (0, import_jsx_runtime.jsx)(
    Box,
    {
      height: "100%",
      left: 0,
      position: "fixed",
      pointerEvents: "none",
      top: 0,
      zIndex: 100,
      width: "100%",
      children: (0, import_jsx_runtime.jsx)(Box, { style: getStyle(initialOffset, currentOffset, mouseOffset), children: renderItem({ type: itemType, item }) })
    }
  );
};
var ComponentDragPreview = ({ displayedValue }) => {
  return (0, import_jsx_runtime.jsxs)(
    Flex,
    {
      background: "neutral0",
      borderColor: "neutral200",
      justifyContent: "space-between",
      gap: 3,
      padding: 3,
      width: "30rem",
      children: [
        (0, import_jsx_runtime.jsx)(ToggleButton, { type: "button", children: (0, import_jsx_runtime.jsxs)(Flex, { gap: 6, children: [
          (0, import_jsx_runtime.jsx)(
            DropdownIconWrapper,
            {
              alignItems: "center",
              justifyContent: "center",
              background: "neutral200",
              height: "3.2rem",
              width: "3.2rem",
              children: (0, import_jsx_runtime.jsx)(ForwardRef$4D, {})
            }
          ),
          (0, import_jsx_runtime.jsx)(Flex, { maxWidth: "15rem", children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "neutral700", ellipsis: true, children: displayedValue }) })
        ] }) }),
        (0, import_jsx_runtime.jsxs)(Flex, { gap: 2, children: [
          (0, import_jsx_runtime.jsx)(IconButton, { withTooltip: false, label: "", variant: "ghost", children: (0, import_jsx_runtime.jsx)(ForwardRef$j, {}) }),
          (0, import_jsx_runtime.jsx)(IconButton, { withTooltip: false, label: "", variant: "ghost", children: (0, import_jsx_runtime.jsx)(ForwardRef$3H, {}) })
        ] })
      ]
    }
  );
};
var DropdownIconWrapper = dt(Flex)`
  border-radius: 50%;

  svg {
    height: 0.6rem;
    width: 1.1rem;
    > path {
      fill: ${({ theme }) => theme.colors.neutral600};
    }
  }
`;
var ToggleButton = dt.button`
  border: none;
  background: transparent;
  display: block;
  width: 100%;
  text-align: unset;
  padding: 0;
`;
var RelationDragPreview = ({ status, displayedValue, width }) => {
  return (0, import_jsx_runtime.jsx)(Box, { style: { width }, children: (0, import_jsx_runtime.jsxs)(
    Flex,
    {
      paddingTop: 2,
      paddingBottom: 2,
      paddingLeft: 2,
      paddingRight: 4,
      hasRadius: true,
      borderWidth: 1,
      background: "neutral0",
      borderColor: "neutral200",
      justifyContent: "space-between",
      gap: 4,
      children: [
        (0, import_jsx_runtime.jsxs)(FlexWrapper, { gap: 1, children: [
          (0, import_jsx_runtime.jsx)(IconButton, { withTooltip: false, label: "", variant: "ghost", children: (0, import_jsx_runtime.jsx)(ForwardRef$3H, {}) }),
          (0, import_jsx_runtime.jsxs)(Flex, { width: "100%", minWidth: 0, justifyContent: "space-between", children: [
            (0, import_jsx_runtime.jsx)(Box, { minWidth: 0, paddingTop: 1, paddingBottom: 1, paddingRight: 4, children: (0, import_jsx_runtime.jsx)(LinkEllipsis, { href: "", children: (0, import_jsx_runtime.jsx)(Typography, { textColor: "primary600", ellipsis: true, children: displayedValue }) }) }),
            status ? (0, import_jsx_runtime.jsx)(DocumentStatus, { status }) : null
          ] })
        ] }),
        (0, import_jsx_runtime.jsx)(DisconnectButton, { type: "button", children: (0, import_jsx_runtime.jsx)(ForwardRef$3V, { width: "12px" }) })
      ]
    }
  ) });
};
var SubNavLinkCustom = dt(SubNavLink)`
  div {
    width: inherit;
    span:nth-child(2) {
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      width: inherit;
    }
  }
`;
var LeftMenu = () => {
  const [search, setSearch] = React.useState("");
  const [{ query }] = useQueryParams();
  const { formatMessage, locale } = useIntl();
  const collectionTypeLinks = useTypedSelector(
    (state) => state["content-manager"].app.collectionTypeLinks
  );
  const singleTypeLinks = useTypedSelector((state) => state["content-manager"].app.singleTypeLinks);
  const { schemas } = useContentTypeSchema();
  const { startsWith } = useFilter(locale, {
    sensitivity: "base"
  });
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const menu = React.useMemo(
    () => [
      {
        id: "collectionTypes",
        title: formatMessage({
          id: getTranslation("components.LeftMenu.collection-types"),
          defaultMessage: "Collection Types"
        }),
        searchable: true,
        links: collectionTypeLinks
      },
      {
        id: "singleTypes",
        title: formatMessage({
          id: getTranslation("components.LeftMenu.single-types"),
          defaultMessage: "Single Types"
        }),
        searchable: true,
        links: singleTypeLinks
      }
    ].map((section) => ({
      ...section,
      links: section.links.filter((link) => startsWith(link.title, search)).sort((a, b) => formatter.compare(a.title, b.title)).map((link) => {
        return {
          ...link,
          title: formatMessage({ id: link.title, defaultMessage: link.title })
        };
      })
    })),
    [collectionTypeLinks, search, singleTypeLinks, startsWith, formatMessage, formatter]
  );
  const handleClear = () => {
    setSearch("");
  };
  const handleChangeSearch = ({ target: { value } }) => {
    setSearch(value);
  };
  const label = formatMessage({
    id: getTranslation("header.name"),
    defaultMessage: "Content Manager"
  });
  const getPluginsParamsForLink = (link) => {
    var _a, _b;
    const schema = schemas.find((schema2) => schema2.uid === link.uid);
    const isI18nEnabled = Boolean((_b = (_a = schema == null ? void 0 : schema.pluginOptions) == null ? void 0 : _a.i18n) == null ? void 0 : _b.localized);
    if (query.plugins && "i18n" in query.plugins) {
      const { i18n, ...restPlugins } = query.plugins;
      if (!isI18nEnabled) {
        return restPlugins;
      }
      return { i18n, ...restPlugins };
    }
    return query.plugins;
  };
  return (0, import_jsx_runtime.jsxs)(SubNav, { "aria-label": label, children: [
    (0, import_jsx_runtime.jsx)(
      SubNavHeader,
      {
        label,
        searchable: true,
        value: search,
        onChange: handleChangeSearch,
        onClear: handleClear,
        searchLabel: formatMessage({
          id: "content-manager.components.LeftMenu.Search.label",
          defaultMessage: "Search for a content type"
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(SubNavSections, { children: menu.map((section) => {
      return (0, import_jsx_runtime.jsx)(
        SubNavSection,
        {
          label: section.title,
          badgeLabel: section.links.length.toString(),
          children: section.links.map((link) => {
            return (0, import_jsx_runtime.jsx)(
              SubNavLinkCustom,
              {
                tag: NavLink,
                to: {
                  pathname: link.to,
                  search: (0, import_qs.stringify)({
                    ...(0, import_qs.parse)(link.search ?? ""),
                    plugins: getPluginsParamsForLink(link)
                  })
                },
                width: "100%",
                children: link.title
              },
              link.uid
            );
          })
        },
        section.id
      );
    }) })
  ] });
};
var { MUTATE_COLLECTION_TYPES_LINKS, MUTATE_SINGLE_TYPES_LINKS } = HOOKS;
var useContentManagerInitData = () => {
  const { toggleNotification } = useNotification();
  const dispatch = useTypedDispatch();
  const runHookWaterfall = useStrapiApp(
    "useContentManagerInitData",
    (state2) => state2.runHookWaterfall
  );
  const { notifyStatus } = useNotifyAT();
  const { formatMessage } = useIntl();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler(getTranslation);
  const checkUserHasPermissions = useAuth(
    "useContentManagerInitData",
    (state2) => state2.checkUserHasPermissions
  );
  const state = useTypedSelector((state2) => state2["content-manager"].app);
  const initialDataQuery = useGetInitialDataQuery(void 0, {
    /**
     * TODO: remove this when the CTB has been refactored to use redux-toolkit-query
     * and it can invalidate the cache on mutation
     */
    refetchOnMountOrArgChange: true
  });
  (0, import_react.useEffect)(() => {
    if (initialDataQuery.data) {
      notifyStatus(
        formatMessage({
          id: getTranslation("App.schemas.data-loaded"),
          defaultMessage: "The schemas have been successfully loaded."
        })
      );
    }
  }, [formatMessage, initialDataQuery.data, notifyStatus]);
  (0, import_react.useEffect)(() => {
    if (initialDataQuery.error) {
      toggleNotification({ type: "danger", message: formatAPIError(initialDataQuery.error) });
    }
  }, [formatAPIError, initialDataQuery.error, toggleNotification]);
  const contentTypeSettingsQuery = useGetAllContentTypeSettingsQuery();
  (0, import_react.useEffect)(() => {
    if (contentTypeSettingsQuery.error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(contentTypeSettingsQuery.error)
      });
    }
  }, [formatAPIError, contentTypeSettingsQuery.error, toggleNotification]);
  const formatData = async (components, contentTypes, fieldSizes, contentTypeConfigurations) => {
    const { collectionType: collectionTypeLinks, singleType: singleTypeLinks } = contentTypes.reduce(
      (acc, model) => {
        acc[model.kind].push(model);
        return acc;
      },
      {
        collectionType: [],
        singleType: []
      }
    );
    const collectionTypeSectionLinks = generateLinks(
      collectionTypeLinks,
      "collectionTypes",
      contentTypeConfigurations
    );
    const singleTypeSectionLinks = generateLinks(singleTypeLinks, "singleTypes");
    const collectionTypeLinksPermissions = await Promise.all(
      collectionTypeSectionLinks.map(({ permissions }) => checkUserHasPermissions(permissions))
    );
    const authorizedCollectionTypeLinks = collectionTypeSectionLinks.filter(
      (_, index) => collectionTypeLinksPermissions[index].length > 0
    );
    const singleTypeLinksPermissions = await Promise.all(
      singleTypeSectionLinks.map(({ permissions }) => checkUserHasPermissions(permissions))
    );
    const authorizedSingleTypeLinks = singleTypeSectionLinks.filter(
      (_, index) => singleTypeLinksPermissions[index].length > 0
    );
    const { ctLinks } = runHookWaterfall(MUTATE_COLLECTION_TYPES_LINKS, {
      ctLinks: authorizedCollectionTypeLinks,
      models: contentTypes
    });
    const { stLinks } = runHookWaterfall(MUTATE_SINGLE_TYPES_LINKS, {
      stLinks: authorizedSingleTypeLinks,
      models: contentTypes
    });
    dispatch(
      setInitialData({
        authorizedCollectionTypeLinks: ctLinks,
        authorizedSingleTypeLinks: stLinks,
        components,
        contentTypeSchemas: contentTypes,
        fieldSizes
      })
    );
  };
  (0, import_react.useEffect)(() => {
    if (initialDataQuery.data && contentTypeSettingsQuery.data) {
      formatData(
        initialDataQuery.data.components,
        initialDataQuery.data.contentTypes,
        initialDataQuery.data.fieldSizes,
        contentTypeSettingsQuery.data
      );
    }
  }, [initialDataQuery.data, contentTypeSettingsQuery.data]);
  return { ...state };
};
var generateLinks = (links, type, configurations = []) => {
  return links.filter((link) => link.isDisplayed).map((link) => {
    const collectionTypesPermissions = [
      { action: "plugin::content-manager.explorer.create", subject: link.uid },
      { action: "plugin::content-manager.explorer.read", subject: link.uid }
    ];
    const singleTypesPermissions = [
      { action: "plugin::content-manager.explorer.read", subject: link.uid }
    ];
    const permissions = type === "collectionTypes" ? collectionTypesPermissions : singleTypesPermissions;
    const currentContentTypeConfig = configurations.find(({ uid }) => uid === link.uid);
    let search = null;
    if (currentContentTypeConfig) {
      const searchParams = {
        page: 1,
        pageSize: currentContentTypeConfig.settings.pageSize,
        sort: `${currentContentTypeConfig.settings.defaultSortBy}:${currentContentTypeConfig.settings.defaultSortOrder}`
      };
      search = (0, import_qs.stringify)(searchParams, { encode: false });
    }
    return {
      permissions,
      search,
      kind: link.kind,
      title: link.info.displayName,
      to: `/content-manager/${link.kind === "collectionType" ? COLLECTION_TYPES : SINGLE_TYPES}/${link.uid}`,
      uid: link.uid,
      // Used for the list item key in the helper plugin
      name: link.uid,
      isDisplayed: link.isDisplayed
    };
  });
};
var Layout = () => {
  const contentTypeMatch = useMatch("/content-manager/:kind/:uid/*");
  const { isLoading, collectionTypeLinks, models, singleTypeLinks } = useContentManagerInitData();
  const authorisedModels = [...collectionTypeLinks, ...singleTypeLinks].sort(
    (a, b) => a.title.localeCompare(b.title)
  );
  const { pathname } = useLocation();
  const { formatMessage } = useIntl();
  const startSection = useGuidedTour("Layout", (state) => state.startSection);
  const startSectionRef = React.useRef(startSection);
  React.useEffect(() => {
    if (startSectionRef.current) {
      startSectionRef.current("contentManager");
    }
  }, []);
  if (isLoading) {
    return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
      (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage({
        id: getTranslation("plugin.name"),
        defaultMessage: "Content Manager"
      }) }),
      (0, import_jsx_runtime.jsx)(Page.Loading, {})
    ] });
  }
  const supportedModelsToDisplay = models.filter(({ isDisplayed }) => isDisplayed);
  if (authorisedModels.length === 0 && supportedModelsToDisplay.length > 0 && pathname !== "/content-manager/403") {
    return (0, import_jsx_runtime.jsx)(Navigate, { to: "/403" });
  }
  if (supportedModelsToDisplay.length === 0 && pathname !== "/no-content-types") {
    return (0, import_jsx_runtime.jsx)(Navigate, { to: "/no-content-types" });
  }
  if (!contentTypeMatch && authorisedModels.length > 0) {
    return (0, import_jsx_runtime.jsx)(
      Navigate,
      {
        to: {
          pathname: authorisedModels[0].to,
          search: authorisedModels[0].search ?? ""
        },
        replace: true
      }
    );
  }
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage({
      id: getTranslation("plugin.name"),
      defaultMessage: "Content Manager"
    }) }),
    (0, import_jsx_runtime.jsxs)(Layouts.Root, { sideNav: (0, import_jsx_runtime.jsx)(LeftMenu, {}), children: [
      (0, import_jsx_runtime.jsx)(DragLayer, { renderItem: renderDraglayerItem }),
      (0, import_jsx_runtime.jsx)(Outlet, {})
    ] })
  ] });
};
function renderDraglayerItem({ type, item }) {
  if (!type || type && typeof type !== "string") {
    return null;
  }
  const [actualType] = type.split("_");
  switch (actualType) {
    case ItemTypes.EDIT_FIELD:
    case ItemTypes.FIELD:
      return (0, import_jsx_runtime.jsx)(CardDragPreview, { label: item.label });
    case ItemTypes.COMPONENT:
    case ItemTypes.DYNAMIC_ZONE:
      return (0, import_jsx_runtime.jsx)(ComponentDragPreview, { displayedValue: item.displayedValue });
    case ItemTypes.RELATION:
      return (0, import_jsx_runtime.jsx)(RelationDragPreview, { ...item });
    default:
      return null;
  }
}
export {
  Layout
};
//# sourceMappingURL=layout-OhrmoJVB-4GQMRUMX.js.map
