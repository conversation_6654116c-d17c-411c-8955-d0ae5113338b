function s(e){for(var t=arguments.length,r=Array(t>1?t-1:0),n=1;n<t;n++)r[n-1]=arguments[n];{var o=ve[e],u=o?typeof o=="function"?o.apply(null,r):o:"unknown error nr: "+e;throw Error("[Immer] "+u)}}function P(e){return!!e&&!!e[p]}function m(e){var t;return!!e&&(function(r){if(!r||typeof r!="object")return!1;var n=Object.getPrototypeOf(r);if(n===null)return!0;var o=Object.hasOwnProperty.call(n,"constructor")&&n.constructor;return o===Object||typeof o=="function"&&Function.toString.call(o)===he}(e)||Array.isArray(e)||!!e[ne]||!!(!((t=e.constructor)===null||t===void 0)&&t[ne])||U(e)||W(e))}function A(e,t,r){r===void 0&&(r=!1),j(e)===0?(r?Object.keys:H)(e).forEach(function(n){r&&typeof n=="symbol"||t(n,e[n],e)}):e.forEach(function(n,o){return t(o,n,e)})}function j(e){var t=e[p];return t?t.i>3?t.i-4:t.i:Array.isArray(e)?1:U(e)?2:W(e)?3:0}function F(e,t){return j(e)===2?e.has(t):Object.prototype.hasOwnProperty.call(e,t)}function fe(e,t){return j(e)===2?e.get(t):e[t]}function oe(e,t,r){var n=j(e);n===2?e.set(t,r):n===3?e.add(r):e[t]=r}function ce(e,t){return e===t?e!==0||1/e==1/t:e!=e&&t!=t}function U(e){return de&&e instanceof Map}function W(e){return ye&&e instanceof Set}function b(e){return e.o||e.t}function $(e){if(Array.isArray(e))return Array.prototype.slice.call(e);var t=be(e);delete t[p];for(var r=H(t),n=0;n<r.length;n++){var o=r[n],u=t[o];u.writable===!1&&(u.writable=!0,u.configurable=!0),(u.get||u.set)&&(t[o]={configurable:!0,writable:!0,enumerable:u.enumerable,value:e[o]})}return Object.create(Object.getPrototypeOf(e),t)}function G(e,t){return t===void 0&&(t=!1),q(e)||P(e)||!m(e)||(j(e)>1&&(e.set=e.add=e.clear=e.delete=se),Object.freeze(e),t&&A(e,function(r,n){return G(n,!0)},!0)),e}function se(){s(2)}function q(e){return e==null||typeof e!="object"||Object.isFrozen(e)}function v(e){var t=me[e];return t||s(18,e),t}function X(){return O||s(0),O}function R(e,t){t&&(v("Patches"),e.u=[],e.s=[],e.v=t)}function I(e){M(e),e.p.forEach(le),e.p=null}function M(e){e===O&&(O=e.l)}function Y(e){return O={p:[],l:O,h:e,m:!0,_:0}}function le(e){var t=e[p];t.i===0||t.i===1?t.j():t.g=!0}function k(e,t){t._=t.p.length;var r=t.p[0],n=e!==void 0&&e!==r;return t.h.O||v("ES5").S(t,e,n),n?(r[p].P&&(I(t),s(4)),m(e)&&(e=x(t,e),t.l||_(t,e)),t.u&&v("Patches").M(r[p].t,e,t.u,t.s)):e=x(t,r,[]),I(t),t.u&&t.v(t.u,t.s),e!==ue?e:void 0}function x(e,t,r){if(q(t))return t;var n=t[p];if(!n)return A(t,function(a,c){return Z(e,n,t,a,c,r)},!0),t;if(n.A!==e)return t;if(!n.P)return _(e,n.t,!0),n.t;if(!n.I){n.I=!0,n.A._--;var o=n.i===4||n.i===5?n.o=$(n.k):n.o,u=o,i=!1;n.i===3&&(u=new Set(o),o.clear(),i=!0),A(u,function(a,c){return Z(e,n,o,a,c,r,i)}),_(e,o,!1),r&&e.u&&v("Patches").N(n,r,e.u,e.s)}return n.o}function Z(e,t,r,n,o,u,i){if(o===r&&s(5),P(o)){var a=x(e,o,u&&t&&t.i!==3&&!F(t.R,n)?u.concat(n):void 0);if(oe(r,n,a),!P(a))return;e.m=!1}else i&&r.add(o);if(m(o)&&!q(o)){if(!e.h.D&&e._<1)return;x(e,o),t&&t.A.l||_(e,o)}}function _(e,t,r){r===void 0&&(r=!1),!e.l&&e.h.D&&e.m&&G(t,r)}function z(e,t){var r=e[p];return(r?b(r):e)[t]}function V(e,t){if(t in e)for(var r=Object.getPrototypeOf(e);r;){var n=Object.getOwnPropertyDescriptor(r,t);if(n)return n;r=Object.getPrototypeOf(r)}}function T(e){e.P||(e.P=!0,e.l&&T(e.l))}function E(e){e.o||(e.o=$(e.t))}function C(e,t,r){var n=U(t)?v("MapSet").F(t,r):W(t)?v("MapSet").T(t,r):e.O?function(o,u){var i=Array.isArray(o),a={i:i?1:0,A:u?u.A:X(),P:!1,I:!1,R:{},l:u,t:o,k:null,o:null,j:null,C:!1},c=a,f=K;i&&(c=[a],f=w);var l=Proxy.revocable(c,f),g=l.revoke,y=l.proxy;return a.k=y,a.j=g,y}(t,r):v("ES5").J(t,r);return(r?r.A:X()).p.push(n),n}function pe(e){return P(e)||s(22,e),function t(r){if(!m(r))return r;var n,o=r[p],u=j(r);if(o){if(!o.P&&(o.i<4||!v("ES5").K(o)))return o.t;o.I=!0,n=ee(r,u),o.I=!1}else n=ee(r,u);return A(n,function(i,a){o&&fe(o.t,i)===a||oe(n,i,t(a))}),u===3?new Set(n):n}(e)}function ee(e,t){switch(t){case 2:return new Map(e);case 3:return Array.from(e)}return $(e)}var te,O,B=typeof Symbol<"u"&&typeof Symbol("x")=="symbol",de=typeof Map<"u",ye=typeof Set<"u",re=typeof Proxy<"u"&&Proxy.revocable!==void 0&&typeof Reflect<"u",ue=B?Symbol.for("immer-nothing"):((te={})["immer-nothing"]=!0,te),ne=B?Symbol.for("immer-draftable"):"__$immer_draftable",p=B?Symbol.for("immer-state"):"__$immer_state",ve={0:"Illegal state",1:"Immer drafts cannot have computed properties",2:"This object has been frozen and should not be mutated",3:function(e){return"Cannot use a proxy that has been revoked. Did you pass an object from inside an immer function to an async process? "+e},4:"An immer producer returned a new value *and* modified its draft. Either return a new value *or* modify the draft.",5:"Immer forbids circular references",6:"The first or second argument to `produce` must be a function",7:"The third argument to `produce` must be a function or undefined",8:"First argument to `createDraft` must be a plain object, an array, or an immerable object",9:"First argument to `finishDraft` must be a draft returned by `createDraft`",10:"The given draft is already finalized",11:"Object.defineProperty() cannot be used on an Immer draft",12:"Object.setPrototypeOf() cannot be used on an Immer draft",13:"Immer only supports deleting array indices",14:"Immer only supports setting array indices and the 'length' property",15:function(e){return"Cannot apply patch, path doesn't resolve: "+e},16:'Sets cannot have "replace" patches.',17:function(e){return"Unsupported patch operation: "+e},18:function(e){return"The plugin for '"+e+"' has not been loaded into Immer. To enable the plugin, import and call `enable"+e+"()` when initializing your application."},20:"Cannot use proxies if Proxy, Proxy.revocable or Reflect are not available",21:function(e){return"produce can only be called on things that are draftable: plain objects, arrays, Map, Set or classes that are marked with '[immerable]: true'. Got '"+e+"'"},22:function(e){return"'current' expects a draft, got: "+e},23:function(e){return"'original' expects a draft, got: "+e},24:"Patching reserved attributes like __proto__, prototype and constructor is not allowed"},he=""+Object.prototype.constructor,H=typeof Reflect<"u"&&Reflect.ownKeys?Reflect.ownKeys:Object.getOwnPropertySymbols!==void 0?function(e){return Object.getOwnPropertyNames(e).concat(Object.getOwnPropertySymbols(e))}:Object.getOwnPropertyNames,be=Object.getOwnPropertyDescriptors||function(e){var t={};return H(e).forEach(function(r){t[r]=Object.getOwnPropertyDescriptor(e,r)}),t},me={},K={get:function(e,t){if(t===p)return e;var r=b(e);if(!F(r,t))return function(o,u,i){var a,c=V(u,i);return c?"value"in c?c.value:(a=c.get)===null||a===void 0?void 0:a.call(o.k):void 0}(e,r,t);var n=r[t];return e.I||!m(n)?n:n===z(e.t,t)?(E(e),e.o[t]=C(e.A.h,n,e)):n},has:function(e,t){return t in b(e)},ownKeys:function(e){return Reflect.ownKeys(b(e))},set:function(e,t,r){var n=V(b(e),t);if(n?.set)return n.set.call(e.k,r),!0;if(!e.P){var o=z(b(e),t),u=o?.[p];if(u&&u.t===r)return e.o[t]=r,e.R[t]=!1,!0;if(ce(r,o)&&(r!==void 0||F(e.t,t)))return!0;E(e),T(e)}return e.o[t]===r&&(r!==void 0||t in e.o)||Number.isNaN(r)&&Number.isNaN(e.o[t])||(e.o[t]=r,e.R[t]=!0),!0},deleteProperty:function(e,t){return z(e.t,t)!==void 0||t in e.t?(e.R[t]=!1,E(e),T(e)):delete e.R[t],e.o&&delete e.o[t],!0},getOwnPropertyDescriptor:function(e,t){var r=b(e),n=Reflect.getOwnPropertyDescriptor(r,t);return n&&{writable:!0,configurable:e.i!==1||t!=="length",enumerable:n.enumerable,value:r[t]}},defineProperty:function(){s(11)},getPrototypeOf:function(e){return Object.getPrototypeOf(e.t)},setPrototypeOf:function(){s(12)}},w={};A(K,function(e,t){w[e]=function(){return arguments[0]=arguments[0][0],t.apply(this,arguments)}}),w.deleteProperty=function(e,t){return isNaN(parseInt(t))&&s(13),w.set.call(this,e,t,void 0)},w.set=function(e,t,r){return t!=="length"&&isNaN(parseInt(t))&&s(14),K.set.call(this,e[0],t,r,e[0])};var ge=function(){function e(r){var n=this;this.O=re,this.D=!0,this.produce=function(o,u,i){if(typeof o=="function"&&typeof u!="function"){var a=u;u=o;var c=n;return function(h){var ie=this;h===void 0&&(h=a);for(var N=arguments.length,L=Array(N>1?N-1:0),D=1;D<N;D++)L[D-1]=arguments[D];return c.produce(h,function(ae){var Q;return(Q=u).call.apply(Q,[ie,ae].concat(L))})}}var f;if(typeof u!="function"&&s(6),i!==void 0&&typeof i!="function"&&s(7),m(o)){var l=Y(n),g=C(n,o,void 0),y=!0;try{f=u(g),y=!1}finally{y?I(l):M(l)}return typeof Promise<"u"&&f instanceof Promise?f.then(function(h){return R(l,i),k(h,l)},function(h){throw I(l),h}):(R(l,i),k(f,l))}if(!o||typeof o!="object"){if((f=u(o))===void 0&&(f=o),f===ue&&(f=void 0),n.D&&G(f,!0),i){var S=[],J=[];v("Patches").M(o,f,S,J),i(S,J)}return f}s(21,o)},this.produceWithPatches=function(o,u){if(typeof o=="function")return function(f){for(var l=arguments.length,g=Array(l>1?l-1:0),y=1;y<l;y++)g[y-1]=arguments[y];return n.produceWithPatches(f,function(S){return o.apply(void 0,[S].concat(g))})};var i,a,c=n.produce(o,u,function(f,l){i=f,a=l});return typeof Promise<"u"&&c instanceof Promise?c.then(function(f){return[f,i,a]}):[c,i,a]},typeof r?.useProxies=="boolean"&&this.setUseProxies(r.useProxies),typeof r?.autoFreeze=="boolean"&&this.setAutoFreeze(r.autoFreeze)}var t=e.prototype;return t.createDraft=function(r){m(r)||s(8),P(r)&&(r=pe(r));var n=Y(this),o=C(this,r,void 0);return o[p].C=!0,M(n),o},t.finishDraft=function(r,n){var o=r&&r[p];o&&o.C||s(9),o.I&&s(10);var u=o.A;return R(u,n),k(void 0,u)},t.setAutoFreeze=function(r){this.D=r},t.setUseProxies=function(r){r&&!re&&s(20),this.O=r},t.applyPatches=function(r,n){var o;for(o=n.length-1;o>=0;o--){var u=n[o];if(u.path.length===0&&u.op==="replace"){r=u.value;break}}o>-1&&(n=n.slice(o+1));var i=v("Patches").$;return P(r)?i(r,n):this.produce(r,function(a){return i(a,n)})},e}(),d=new ge,Pe=d.produce;d.produceWithPatches.bind(d);d.setAutoFreeze.bind(d);d.setUseProxies.bind(d);d.applyPatches.bind(d);d.createDraft.bind(d);d.finishDraft.bind(d);export{Pe as f};
