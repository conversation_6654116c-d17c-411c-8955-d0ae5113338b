{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/hi-DW2CutdA.mjs"], "sourcesContent": ["const Analytics = \"वैश्लेषिकी\";\nconst Documentation = \"प्रलेखन\";\nconst Email = \"ईमेल\";\nconst Password = \"पासवर्ड\";\nconst Provider = \"प्रदाता\";\nconst ResetPasswordToken = \"पासवर्ड रीसेट करें टोकन\";\nconst Role = \"भूमिका\";\nconst Username = \"उपयोगकर्ता नाम\";\nconst Users = \"उपयोगकर्ता \";\nconst anErrorOccurred = \"ओह! कुछ गलत हो गया। कृपया पुन: प्रयास करें।\";\nconst clearLabel = \"साफ़\";\nconst or = \"या\";\nconst skipToContent = \"इसे छोड़कर सामग्री पर बढ़ने के लिए\";\nconst submit = \"प्रस्तुत करना\";\nconst hi = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"आपके खाते को निलंबित कर दिया गया है.\",\n\t\"Auth.components.Oops.text.admin\": \"अगर यह गलती है, तो कृपया अपने व्यवस्थापक से संपर्क करें.\",\n\t\"Auth.components.Oops.title\": \"उफ़...\",\n\t\"Auth.form.button.forgot-password\": \"ईमेल भेजें\",\n\t\"Auth.form.button.go-home\": \"वापीस घर जाओ\",\n\t\"Auth.form.button.login\": \"लॉग इन करें\",\n\t\"Auth.form.button.login.providers.error\": \"हम आपको चयनित प्रदाता के माध्यम से कनेक्ट नहीं कर सकते हैं।\",\n\t\"Auth.form.button.login.strapi\": \"Strapi . के माध्यम से लॉग इन करें\",\n\t\"Auth.form.button.password-recovery\": \"पासवर्ड की दोबारा प्राप्ति\",\n\t\"Auth.form.button.register\": \"चलो शुरू करते हैं\",\n\t\"Auth.form.confirmPassword.label\": \"पुष्टिकरण पासवर्ड\",\n\t\"Auth.form.currentPassword.label\": \"वर्तमान पासवर्ड\",\n\t\"Auth.form.email.label\": \"ईमेल\",\n\t\"Auth.form.email.placeholder\": \"e.g. <EMAIL>\",\n\t\"Auth.form.error.blocked\": \"आपका खाता व्यवस्थापक द्वारा अवरुद्ध कर दिया गया है\",\n\t\"Auth.form.error.code.provide\": \"गलत कोड प्रदान किया गया।\",\n\t\"Auth.form.error.confirmed\": \"आपके खाते के ईमेल की पुष्टि नहीं हुई है।\",\n\t\"Auth.form.error.email.invalid\": \"ईमेल अवैध है।\",\n\t\"Auth.form.error.email.provide\": \"कृपया अपना उपयोगकर्ता नाम या अपना ईमेल प्रदान करें।\",\n\t\"Auth.form.error.email.taken\": \" ईमेल पहले से ही लिया जा चुका है।\",\n\t\"Auth.form.error.invalid\": \"पहचानकर्ता या पासवर्ड अमान्य।\",\n\t\"Auth.form.error.params.provide\": \"गलत पैरामीटर प्रदान किए गए।\",\n\t\"Auth.form.error.password.format\": \"आपके पासवर्ड में `$` का प्रतीक तीन बार से अधिक नहीं हो सकता।\",\n\t\"Auth.form.error.password.local\": \"इस उपयोगकर्ता ने कभी भी स्थानीय पासवर्ड सेट नहीं किया है, कृपया खाता निर्माण के दौरान उपयोग किए गए प्रदाता के माध्यम से लॉगिन करें।\",\n\t\"Auth.form.error.password.matching\": \"पासवर्ड मेल नहीं खाते।\",\n\t\"Auth.form.error.password.provide\": \" कृपया अपना पासवर्ड प्रदान करें। \",\n\t\"Auth.form.error.ratelimit\": \"बहुत अधिक प्रयास, कृपया एक मिनट में पुन: प्रयास करें।\",\n\t\"Auth.form.error.user.not-exist\": \"यह ईमेल मौजूद नहीं है।\",\n\t\"Auth.form.error.username.taken\": \"उपयोगकर्ता का नाम पहले से लिया है।\",\n\t\"Auth.form.firstname.label\": \"संतोष\",\n\t\"Auth.form.firstname.placeholder\": \"जैसे काई\",\n\t\"Auth.form.forgot-password.email.label\": \"अपना ईमेल दर्ज करें\",\n\t\"Auth.form.forgot-password.email.label.success\": \"ईमेल सफलतापूर्वक भेजा गया\",\n\t\"Auth.form.lastname.label\": \"उपनाम\",\n\t\"Auth.form.lastname.placeholder\": \"जैसे हरिणी\",\n\t\"Auth.form.password.hide-password\": \"पासवर्ड छिपाएं\",\n\t\"Auth.form.password.hint\": \"कम से कम 8 अक्षर, 1 अपरकेस, 1 लोअरकेस और 1 नंबर होना चाहिए\",\n\t\"Auth.form.password.show-password\": \"पासवर्ड दिखाए\",\n\t\"Auth.form.register.news.label\": \"मुझे नई सुविधाओं और आगामी सुधारों के बारे में अपडेट रखें (ऐसा करने से आप {शर्तें} और {नीति} स्वीकार करते हैं)।\",\n\t\"Auth.form.register.subtitle\": \"क्रेडेंशियल का उपयोग केवल Strapi में प्रमाणित करने के लिए किया जाता है। सभी सहेजे गए डेटा आपके डेटाबेस में संग्रहीत किए जाएंगे।\",\n\t\"Auth.form.rememberMe.label\": \"पहचाना की नहीं\",\n\t\"Auth.form.username.label\": \"उपयोगकर्ता नाम\",\n\t\"Auth.form.username.placeholder\": \"जैसे काई_डो\",\n\t\"Auth.form.welcome.subtitle\": \"अपने Strapi खाते में लॉग इन करें\",\n\t\"Auth.form.welcome.title\": \"स्ट्रैपी में आपका स्वागत है!\",\n\t\"Auth.link.forgot-password\": \"पासवर्ड भूल गए हैं?\",\n\t\"Auth.link.ready\": \"साइन इन करने के लिए तैयार हैं?\",\n\t\"Auth.link.signin\": \"साइन इन करें\",\n\t\"Auth.link.signin.account\": \"क्या आपके पास पहले से एक खाता मौजूद है?\",\n\t\"Auth.login.sso.divider\": \"या लॉगिन करें\",\n\t\"Auth.login.sso.loading\": \"प्रदाता लोड हो रहे हैं...\",\n\t\"Auth.login.sso.subtitle\": \"SSO के माध्यम से अपने खाते में लॉगिन करें\",\n\t\"Auth.privacy-policy-agreement.policy\": \"गोपनीयता नीति\",\n\t\"Auth.privacy-policy-agreement.terms\": \"शर्तें\",\n\t\"Auth.reset-password.title\": \"पासवर्ड रीसेट\",\n\t\"Content Manager\": \"सामग्री प्रबंधक\",\n\t\"Content Type Builder\": \"सामग्री-प्रकार बिल्डर\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"फ़ाइलें अपलोड\",\n\t\"HomePage.head.title\": \"होमपेज\",\n\t\"HomePage.roadmap\": \"हमारा रोडमैप देखें\",\n\t\"HomePage.welcome.congrats\": \"बधाई!\",\n\t\"HomePage.welcome.congrats.content\": \"आप पहले व्यवस्थापक के रूप में लॉग इन हैं। Strapi द्वारा प्रदान की गई शक्तिशाली विशेषताओं की खोज करने के लिए,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"हम आपको अपना पहला संग्रह-प्रकार बनाने की सलाह देते हैं।\",\n\t\"Media Library\": \"मीडिया लाइब्रेरी\",\n\t\"New entry\": \"नविन प्रवेश\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"भूमिकाएं और अनुमतियां\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"कुछ भूमिकाओं को हटाया नहीं जा सका क्योंकि वे उपयोगकर्ताओं से संबद्ध हैं\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"उपयोगकर्ताओं के साथ संबद्ध होने पर भूमिका को हटाया नहीं जा सकता\",\n\t\"Roles.RoleRow.select-all\": \"बल्क कार्रवाइयों के लिए {नाम} चुनें\",\n\t\"Roles.RoleRow.user-count\": \"{संख्या, बहुवचन, =0 {# उपयोगकर्ता} एक {# उपयोगकर्ता} अन्य {# उपयोगकर्ता}}\",\n\t\"Roles.components.List.empty.withSearch\": \"खोज से संबंधित कोई भूमिका नहीं है ({खोज})...\",\n\t\"Settings.PageTitle\": \"सेटिंग्स - {नाम}\",\n\t\"Settings.apiTokens.addFirstToken\": \"अपना पहला एपीआई टोकन जोड़ें\",\n\t\"Settings.apiTokens.addNewToken\": \"नया एपीआई टोकन जोड़ें\",\n\t\"Settings.tokens.copy.editMessage\": \"सुरक्षा कारणों से, आप अपना टोकन केवल एक बार देख सकते हैं.\",\n\t\"Settings.tokens.copy.editTitle\": \"यह टोकन अब उपलब्ध नहीं है।\",\n\t\"Settings.tokens.copy.lastWarning\": \"इस टोकन को कॉपी करना सुनिश्चित करें, आप इसे दोबारा नहीं देख पाएंगे!\",\n\t\"Settings.apiTokens.create\": \"नया एपीआई टोकन बनाएं\",\n\t\"Settings.apiTokens.description\": \"एपीआई का उपभोग करने के लिए उत्पन्न टोकन की सूची\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"आपके पास अभी तक कोई सामग्री नहीं है...\",\n\t\"Settings.tokens.notification.copied\": \"टोकन को क्लिपबोर्ड पर कॉपी किया गया\",\n\t\"Settings.apiTokens.title\": \"एपीआई टोकन\",\n\t\"Settings.tokens.types.full-access\": \"पूर्ण पहुँच\",\n\t\"Settings.tokens.types.read-only\": \"केवल पढ़ने के लिए\",\n\t\"Settings.application.description\": \"व्यवस्थापन पैनल की वैश्विक जानकारी\",\n\t\"Settings.application.edition-title\": \"वर्तमान योजना\",\n\t\"Settings.application.get-help\": \"मदद लें\",\n\t\"Settings.application.link-pricing\": \"सभी मूल्य निर्धारण योजनाएं देखें\",\n\t\"Settings.application.link-upgrade\": \"अपना व्यवस्थापक पैनल अपग्रेड करें\",\n\t\"Settings.application.node-version\": \"नोड संस्करण\",\n\t\"Settings.application.strapi-version\": \"स्ट्रैपी संस्करण\",\n\t\"Settings.application.strapiVersion\": \"स्ट्रैपी संस्करण\",\n\t\"Settings.application.title\": \"अवलोकन\",\n\t\"Settings.error\": \"गलती\",\n\t\"Settings.global\": \"वैश्विक सेटिंग्स\",\n\t\"Settings.permissions\": \"प्रशासन पैनल\",\n\t\"Settings.permissions.category\": \"{श्रेणी} के लिए अनुमतियां सेटिंग\",\n\t\"Settings.permissions.category.plugins\": \"{श्रेणी} प्लगइन के लिए अनुमतियाँ सेटिंग\",\n\t\"Settings.permissions.conditions.anytime\": \"किसी भी समय\",\n\t\"Settings.permissions.conditions.apply\": \"आवेदन करना\",\n\t\"Settings.permissions.conditions.can\": \"कर सकना\",\n\t\"Settings.permissions.conditions.conditions\": \"शर्तों को परिभाषित करें\",\n\t\"Settings.permissions.conditions.links\": \"लिंक\",\n\t\"Settings.permissions.conditions.no-actions\": \"उन पर शर्तों को परिभाषित करने से पहले आपको पहले क्रियाओं का चयन करना होगा (बनाएं, पढ़ें, अपडेट करें, ...)\",\n\t\"Settings.permissions.conditions.none-selected\": \"किसी भी समय\",\n\t\"Settings.permissions.conditions.or\": \"या\",\n\t\"Settings.permissions.conditions.when\": \"कब\",\n\t\"Settings.permissions.select-all-by-permission\": \"सभी {लेबल} अनुमतियों का चयन करें\",\n\t\"Settings.permissions.select-by-permission\": \"{लेबल} अनुमति चुनें\",\n\t\"Settings.permissions.users.create\": \"नए उपयोगकर्ता को आमंत्रित करें\",\n\t\"Settings.permissions.users.email\": \"ईमेल\",\n\t\"Settings.permissions.users.firstname\": \"प्रथम नाम\",\n\t\"Settings.permissions.users.lastname\": \"उपनाम\",\n\t\"Settings.permissions.users.form.sso\": \"एसएसओ से जुड़ें\",\n\t\"Settings.permissions.users.form.sso.description\": \"सक्षम (चालू) होने पर, उपयोगकर्ता एसएसओ के माध्यम से लॉगिन कर सकते हैं\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"वे सभी उपयोगकर्ता जिनके पास Strapi व्यवस्थापक पैनल तक पहुंच है\",\n\t\"Settings.permissions.users.tabs.label\": \"टैब अनुमतियां\",\n\t\"Settings.profile.form.notify.data.loaded\": \"आपका प्रोफ़ाइल डेटा लोड कर दिया गया है\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"चयनित इंटरफ़ेस भाषा साफ़ करें\",\n\t\"Settings.profile.form.section.experience.here\": \"यहां\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"अंतरफलक भाषा\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"यह केवल आपके स्वयं के इंटरफ़ेस को चुनी हुई भाषा में प्रदर्शित करेगा।\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"वरीयता परिवर्तन केवल आप पर लागू होंगे। यहां अधिक जानकारी उपलब्ध है}।\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"इंटरफ़ेस मोड\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"आपके इंटरफ़ेस को चुने हुए मोड में प्रदर्शित करता है।\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{नाम} मोड\",\n\t\"Settings.profile.form.section.experience.title\": \"अनुभव\",\n\t\"Settings.profile.form.section.head.title\": \"उपयोगकर्ता रूपरेखा\",\n\t\"Settings.profile.form.section.profile.page.title\": \"प्रोफ़ाइल पृष्ठ\",\n\t\"Settings.roles.create.description\": \"भूमिका को दिए गए अधिकारों को परिभाषित करें\",\n\t\"Settings.roles.create.title\": \"एक भूमिका बनाएँ\",\n\t\"Settings.roles.created\": \"भूमिका सृजित\",\n\t\"Settings.roles.edit.title\": \"भूमिका संपादित करें\",\n\t\"Settings.roles.form.button.users-with-role\": \"{संख्या, बहुवचन, =0 {# उपयोगकर्ता} एक {# उपयोगकर्ता} अन्य {# उपयोगकर्ता}} इस भूमिका के साथ\",\n\t\"Settings.roles.form.created\": \"बनाया था\",\n\t\"Settings.roles.form.description\": \"भूमिका का नाम और विवरण\",\n\t\"Settings.roles.form.permission.property-label\": \"{लेबल} अनुमतियां\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"फ़ील्ड अनुमतियाँ\",\n\t\"Settings.roles.form.permissions.create\": \"सृजन करना\",\n\t\"Settings.roles.form.permissions.delete\": \"मिटाना\",\n\t\"Settings.roles.form.permissions.publish\": \"प्रकाशित करना\",\n\t\"Settings.roles.form.permissions.read\": \"पढ़ना\",\n\t\"Settings.roles.form.permissions.update\": \"अद्यतन\",\n\t\"Settings.roles.list.button.add\": \"नई भूमिका जोड़ें\",\n\t\"Settings.roles.list.description\": \"भूमिकाओं की सूची\",\n\t\"Settings.roles.title.singular\": \"भूमिका\",\n\t\"Settings.sso.description\": \"एकल साइन-ऑन सुविधा के लिए सेटिंग्स कॉन्फ़िगर करें।\",\n\t\"Settings.sso.form.defaultRole.description\": \"यह नए प्रमाणित उपयोगकर्ता को चयनित भूमिका से जोड़ देगा\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"आपको व्यवस्थापक भूमिकाओं को पढ़ने की अनुमति की आवश्यकता है\",\n\t\"Settings.sso.form.defaultRole.label\": \"डिफ़ॉल्ट भूमिका\",\n\t\"Settings.sso.form.registration.description\": \"यदि कोई खाता मौजूद नहीं है तो SSO लॉगिन पर नया उपयोगकर्ता बनाएं\",\n\t\"Settings.sso.form.registration.label\": \"स्वतः पंजीकरण\",\n\t\"Settings.sso.title\": \"एक बार दर्ज करना\",\n\t\"Settings.webhooks.create\": \"एक वेबहुक बनाएं\",\n\t\"Settings.webhooks.create.header\": \"नया हेडर बनाएं\",\n\t\"Settings.webhooks.created\": \"वेबहुक बनाया गया\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"यह घटना केवल उन सामग्रियों के लिए मौजूद है जिनमें ड्राफ्ट/प्रकाशन प्रणाली सक्षम है\",\n\t\"Settings.webhooks.events.create\": \"सृजन करना\",\n\t\"Settings.webhooks.events.update\": \"अद्यतन\",\n\t\"Settings.webhooks.form.events\": \"आयोजन\",\n\t\"Settings.webhooks.form.headers\": \"हेडर\",\n\t\"Settings.webhooks.form.url\": \"यूआरएल\",\n\t\"Settings.webhooks.headers.remove\": \"शीर्षलेख पंक्ति निकालें {संख्या}\",\n\t\"Settings.webhooks.key\": \"चाभी\",\n\t\"Settings.webhooks.list.button.add\": \"नया वेबहुक बनाएं\",\n\t\"Settings.webhooks.list.description\": \"POST परिवर्तन सूचनाएं प्राप्त करें\",\n\t\"Settings.webhooks.list.empty.description\": \"कोई वेबहुक नहीं मिला\",\n\t\"Settings.webhooks.list.empty.link\": \"हमारे दस्तावेज़ देखें\",\n\t\"Settings.webhooks.list.empty.title\": \"अभी तक कोई वेबहुक नहीं है\",\n\t\"Settings.webhooks.list.th.actions\": \"कार्रवाई\",\n\t\"Settings.webhooks.list.th.status\": \"दर्जा\",\n\t\"Settings.webhooks.singular\": \" वेबहुक\",\n\t\"Settings.webhooks.title\": \" वेबहुक\",\n\t\"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, बहुवचन, एक {# संपत्ति} अन्य {# संपत्ति}} चयनित\",\n\t\"Settings.webhooks.trigger\": \"चालू कर देना\",\n\t\"Settings.webhooks.trigger.cancel\": \"ट्रिगर रद्द करें\",\n\t\"Settings.webhooks.trigger.pending\": \"लंबित…\",\n\t\"Settings.webhooks.trigger.save\": \"कृपया ट्रिगर करने के लिए सहेजें\",\n\t\"Settings.webhooks.trigger.success\": \"सफलता!\",\n\t\"Settings.webhooks.trigger.success.label\": \"ट्रिगर सफल हुआ\",\n\t\"Settings.webhooks.trigger.test\": \"टेस्ट-ट्रिगर\",\n\t\"Settings.webhooks.trigger.title\": \"ट्रिगर से पहले सहेजें\",\n\t\"Settings.webhooks.value\": \"मूल्य\",\n\t\"Usecase.back-end\": \"बैक-एंड डेवलपर\",\n\t\"Usecase.button.skip\": \"इस प्रश्न को छोड़ें\",\n\t\"Usecase.content-creator\": \"सामग्री निर्माता\",\n\t\"Usecase.front-end\": \"फ़्रंट एंड डेवलपर\",\n\t\"Usecase.full-stack\": \"पूरी स्टैक बनानेवाला\",\n\t\"Usecase.input.work-type\": \"आप किस तरह का काम करते है?\",\n\t\"Usecase.notification.success.project-created\": \"परियोजना सफलतापूर्वक बनाई गई है\",\n\t\"Usecase.other\": \"अन्य\",\n\t\"Usecase.title\": \"अपने बारे में कुछ और बताएं\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"उपयोगकर्ता और अनुमतियां\",\n\t\"Users.components.List.empty\": \"कोई उपयोगकर्ता नहीं है ...\",\n\t\"Users.components.List.empty.withFilters\": \"लागू फ़िल्टर वाले कोई उपयोगकर्ता नहीं हैं...\",\n\t\"Users.components.List.empty.withSearch\": \"खोज से संबंधित कोई उपयोगकर्ता नहीं है ({search})...\",\n\t\"admin.pages.MarketPlacePage.head\": \"बाज़ार - प्लगइन्स\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"आप संपर्क में नहीं हैं\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"Strapi Market तक पहुंचने के लिए आपको इंटरनेट से कनेक्ट होने की आवश्यकता है।\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"कॉपी इंस्टॉल कमांड\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"अपने टर्मिनल में चिपकाने के लिए तैयार कमांड स्थापित करें\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"और अधिक जानें\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"{प्लगइननाम} के बारे में अधिक जानें\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"और अधिक जानें\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"स्थापित\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Strapi . द्वारा निर्मित\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Strapi द्वारा सत्यापित प्लगइन\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"प्लगइन खोज साफ़ करें\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \"\\\"{target}\\\" के लिए कोई परिणाम नहीं\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"एक प्लगइन खोजें\",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"अपना प्लगइन सबमिट करें\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \"Strapi . से अधिक लाभ उठाएं\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"एक प्लगइन गुम है?\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"हमें बताएं कि आप किस प्लगइन की तलाश कर रहे हैं और अगर वे प्रेरणा की तलाश में हैं तो हम अपने समुदाय प्लगइन डेवलपर्स को बताएंगे!\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"क्लिपबोर्ड पर कॉपी करें\",\n\t\"app.component.search.label\": \"{लक्ष्य} खोजें\",\n\t\"app.component.table.duplicate\": \"डुप्लीकेट {लक्ष्य}\",\n\t\"app.component.table.edit\": \"संपादित करें {लक्ष्य}\",\n\t\"app.component.table.select.one-entry\": \"{लक्ष्य} चुनें\",\n\t\"app.components.BlockLink.blog\": \"ब्लॉग\",\n\t\"app.components.BlockLink.blog.content\": \"Strapi और पारिस्थितिकी तंत्र के बारे में नवीनतम समाचार पढ़ें।\",\n\t\"app.components.BlockLink.code\": \"कोड उदाहरण\",\n\t\"app.components.BlockLink.code.content\": \"वास्तविक परियोजनाओं का परीक्षण करके जानें समुदाय का विकास हुआ।\",\n\t\"app.components.BlockLink.documentation.content\": \"आवश्यक अवधारणाओं, गाइडों और निर्देशों की खोज करें।\",\n\t\"app.components.BlockLink.tutorial\": \"ट्यूटोरियल\",\n\t\"app.components.BlockLink.tutorial.content\": \"Strapi का उपयोग और कस्टमाइज़ करने के लिए चरण-दर-चरण निर्देशों का पालन करें।\",\n\t\"app.components.Button.cancel\": \"रद्द करना\",\n\t\"app.components.Button.confirm\": \"पुष्टि करें\",\n\t\"app.components.Button.reset\": \"रीसेट\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"जल्द आ रहा है\",\n\t\"app.components.ConfirmDialog.title\": \"पुष्टीकरण\",\n\t\"app.components.DownloadInfo.download\": \"डाउनलोड जारी है...\",\n\t\"app.components.DownloadInfo.text\": \"इसमें एक मिनट लग सकता है। आपके धैर्य के लिए धन्यवाद।\",\n\t\"app.components.EmptyAttributes.title\": \"अभी तक कोई फ़ील्ड नहीं है\",\n\t\"app.components.EmptyStateLayout.content-document\": \"कोई सामग्री नहीं मिली\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"आपके पास उस सामग्री तक पहुंचने की अनुमति नहीं है\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>कंटेंट मैनेजर में यहां सभी सामग्री बनाएं और प्रबंधित करें।</p><p>पूर्व: ब्लॉग वेबसाइट के उदाहरण को आगे बढ़ाते हुए, कोई एक लेख लिख सकता है, उसे सहेज सकता है और अपनी पसंद के अनुसार प्रकाशित कर सकता है।</p>< p>💡 त्वरित युक्ति - आपके द्वारा बनाई गई सामग्री पर प्रकाशित करें हिट करना न भूलें।</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"️ सामग्री बनाएं\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>बहुत बढ़िया, एक आखिरी कदम!</p><b>🚀 सामग्री को क्रियान्वित देखें</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"एपीआई का परीक्षण करें\",\n\t\"app.components.GuidedTour.CM.success.title\": \"चरण 2: पूर्ण\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>संग्रह प्रकार आपको कई प्रविष्टियों को प्रबंधित करने में मदद करते हैं, एकल प्रकार केवल एक प्रविष्टि को प्रबंधित करने के लिए उपयुक्त होते हैं।</p> <p>उदा: एक ब्लॉग वेबसाइट के लिए, लेख एक संग्रह प्रकार होगा जबकि एक मुखपृष्ठ एकल प्रकार होगा। </p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"एक संग्रह प्रकार बनाएँ\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 पहला संग्रह प्रकार बनाएं\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>अच्छा चल रहा है!</p><b>⚡️ आप दुनिया के साथ क्या साझा करना चाहेंगे?</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"चरण 1: पूर्ण✅\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>यहां एक प्रमाणीकरण टोकन जेनरेट करें और आपके द्वारा अभी-अभी बनाई गई सामग्री को पुनः प्राप्त करें।</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"एक एपीआई टोकन उत्पन्न करें\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 कार्रवाई में सामग्री देखें\",\n\t\"app.components.GuidedTour.apiTokens.success.content\": \"<p>HTTP अनुरोध करके सामग्री को क्रियान्वित देखें:</p><ul><li><p>इस URL पर: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT '>'</light></p></li><p>शीर्षक के साथ: <light>प्राधिकरण: वाहक '<'Your_API_TOKEN'>'</light></p></li> </ul><p>सामग्री के साथ इंटरैक्ट करने के अधिक तरीकों के लिए, <दस्तावेज़ीकरणलिंक>दस्तावेज़ीकरण</दस्तावेज़ीकरणलिंक> देखें।</p>\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"होमपेज पर वापस जाएं\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"चरण 3: पूर्ण✅\",\n\t\"app.components.GuidedTour.create-content\": \"सामग्री बनाएं\",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️ आप दुनिया के साथ क्या साझा करना चाहेंगे?\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"सामग्री प्रकार बिल्डर पर जाएं\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 सामग्री संरचना बनाएँ\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"एपीआई का परीक्षण करें\",\n\t\"app.components.GuidedTour.skip\": \"टूर छोड़ें\",\n\t\"app.components.GuidedTour.title\": \"आरंभ करने के लिए 3 कदम\",\n\t\"app.components.HomePage.button.blog\": \"ब्लॉग पर और देखें\",\n\t\"app.components.HomePage.community\": \"समुदाय में शामिल हों\",\n\t\"app.components.HomePage.community.content\": \"विभिन्न चैनलों पर टीम के सदस्यों, योगदानकर्ताओं और डेवलपर्स के साथ चर्चा करें।\",\n\t\"app.components.HomePage.create\": \"अपना पहला सामग्री प्रकार बनाएं\",\n\t\"app.components.HomePage.roadmap\": \"हमारा रोडमैप देखें\",\n\t\"app.components.HomePage.welcome\": \"बोर्ड पर आपका स्वागत है 👋\",\n\t\"app.components.HomePage.welcome.again\": \"स्वागत है\",\n\t\"app.components.HomePage.welcomeBlock.content\": \"बधाई! आप पहले व्यवस्थापक के रूप में लॉग इन हैं। Strapi द्वारा प्रदान की गई शक्तिशाली सुविधाओं को खोजने के लिए, हम आपको अपना पहला सामग्री प्रकार बनाने की सलाह देते हैं!\",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"हमें उम्मीद है कि आप अपनी परियोजना पर प्रगति कर रहे हैं! Strapi के बारे में नवीनतम समाचार पढ़ने के लिए स्वतंत्र महसूस करें। हम आपकी प्रतिक्रिया के आधार पर उत्पाद को बेहतर बनाने के लिए अपना सर्वश्रेष्ठ दे रहे हैं।\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"मुद्दे।\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" या बढ़ाओ\",\n\t\"app.components.ImgPreview.hint\": \"अपनी फ़ाइल को इस क्षेत्र में खींचें और छोड़ें या किसी फ़ाइल को अपलोड करने के लिए {ब्राउज़ करें}\",\n\t\"app.components.ImgPreview.hint.browse\": \"ब्राउज़\",\n\t\"app.components.InputFile.newFile\": \"नई फ़ाइल जोड़ें\",\n\t\"app.components.InputFileDetails.open\": \"एक नए टैब में खोलें\",\n\t\"app.components.InputFileDetails.originalName\": \"मूल नाम:\",\n\t\"app.components.InputFileDetails.remove\": \"इस फ़ाइल को हटाएं\",\n\t\"app.components.InputFileDetails.size\": \"आकार:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"प्लगइन को डाउनलोड और इंस्टॉल करने में कुछ सेकंड लग सकते हैं।\",\n\t\"app.components.InstallPluginPage.Download.title\": \"डाउनलोड हो रहा है...\",\n\t\"app.components.InstallPluginPage.description\": \"अपने ऐप को आसानी से बढ़ाएं।\",\n\t\"app.components.LeftMenu.collapse\": \"नेवबार को संक्षिप्त करें\",\n\t\"app.components.LeftMenu.expand\": \"नवबार का विस्तार करें\",\n\t\"app.components.LeftMenu.logout\": \"लॉग आउट\",\n\t\"app.components.LeftMenu.navbrand.title\": \"स्ट्रैपी डैशबोर्ड\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"कार्यस्थल\",\n\t\"app.components.LeftMenuFooter.help\": \"मदद करना\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"द्वारा संचालित \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"संग्रह प्रकार\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"विन्यास\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"सामान्य\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"अभी तक कोई प्लग इन इंस्टॉल नहीं है\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"प्लग-इन\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"एकल प्रकार\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"प्लगइन को अनइंस्टॉल करने में कुछ सेकंड लग सकते हैं।\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"की स्थापना रद्द\",\n\t\"app.components.ListPluginsPage.description\": \"परियोजना में स्थापित प्लगइन्स की सूची।\",\n\t\"app.components.ListPluginsPage.head.title\": \"सूची प्लगइन्स\",\n\t\"app.components.Logout.logout\": \"लॉग आउट\",\n\t\"app.components.Logout.profile\": \"प्रोफ़ाइल\",\n\t\"app.components.MarketplaceBanner\": \"Strapi Awesome पर समुदाय द्वारा बनाए गए प्लग इन और अपने प्रोजेक्ट को किकस्टार्ट करने के लिए कई और शानदार चीज़ें खोजें।\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"एक स्ट्रैपी रॉकेट लोगो\",\n\t\"app.components.MarketplaceBanner.link\": \"अब इसे जांचें\",\n\t\"app.components.NotFoundPage.back\": \"मुखपृष्ठ पर वापस\",\n\t\"app.components.NotFoundPage.description\": \"पता नहीं चला\",\n\t\"app.components.Official\": \"अधिकारी\",\n\t\"app.components.Onboarding.help.button\": \"सहायता बटन\",\n\t\"app.components.Onboarding.label.completed\": \"% पूरा किया हुआ\",\n\t\"app.components.Onboarding.title\": \"वीडियो शुरू करें\",\n\t\"app.components.PluginCard.Button.label.download\": \"डाउनलोड\",\n\t\"app.components.PluginCard.Button.label.install\": \"पहले से ही इनस्टाल्ड है\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"AutoReload सुविधा को सक्षम करने की आवश्यकता है। कृपया अपना ऐप `यार्न डिवेलप` के साथ शुरू करें।\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"मै समझता हुँ!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"सुरक्षा कारणों से, एक प्लगइन केवल विकास के माहौल में ही डाउनलोड किया जा सकता है।\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"डाउनलोड करना असंभव है\",\n\t\"app.components.PluginCard.compatible\": \"आपके ऐप के साथ संगत\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"समुदाय के साथ संगत\",\n\t\"app.components.PluginCard.more-details\": \"अधिक जानकारी\",\n\t\"app.components.ToggleCheckbox.off-label\": \"असत्य\",\n\t\"app.components.ToggleCheckbox.on-label\": \"सत्य\",\n\t\"app.components.Users.MagicLink.connect\": \"इस उपयोगकर्ता को एक्सेस देने के लिए इस लिंक को कॉपी और शेयर करें\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"उपयोगकर्ता को यह लिंक भेजें, पहला लॉगिन एसएसओ प्रदाता के माध्यम से किया जा सकता है\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"उपयोगकर्ता की जानकारी\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"उपयोगकर्ता की भूमिकाएँ\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"एक उपयोगकर्ता की एक या कई भूमिकाएँ हो सकती हैं\",\n\t\"app.components.Users.SortPicker.button-label\": \"इसके अनुसार क्रमबद्ध करें\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"ईमेल (A से  Z)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"ईमेल (Z से A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"प्रथम नाम  (A से  Z)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"प्रथम नाम (Z से A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"उपनाम(A से  Z)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"उपनाम (Z से A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"उपयोगकर्ता नाम(A से  Z)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"उपयोगकर्ता नाम (Z से A))\",\n\t\"app.components.listPlugins.button\": \"नया प्लगइन जोड़ें\",\n\t\"app.components.listPlugins.title.none\": \"कोई प्लग इन इंस्टॉल नहीं है\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"प्लगइन की स्थापना रद्द करते समय एक त्रुटि हुई\",\n\t\"app.containers.App.notification.error.init\": \"एपीआई का अनुरोध करते समय एक त्रुटि हुई\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"यदि आपको यह लिंक नहीं मिलता है, तो कृपया अपने व्यवस्थापक से संपर्क करें।\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"आपका पासवर्ड पुनर्प्राप्ति लिंक प्राप्त करने में कुछ मिनट लग सकते हैं।\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"ईमेल भेजा\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"सक्रिय\",\n\t\"app.containers.Users.EditPage.header.label\": \"नाम संपादित करें\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"यूजर को संपादित करो\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"जिम्मेदार भूमिकाएं\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"उपयोगकर्ता को आमंत्रित करें\",\n\t\"app.links.configure-view\": \"दृश्य कॉन्फ़िगर करें\",\n\t\"app.page.not.found\": \"उफ़! आप जिस पेज की तलाश कर रहे हैं, वह हमें नहीं मिल रहा है...\",\n\t\"app.static.links.cheatsheet\": \"प्रवंचक पत्रक\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"फ़िल्टर जोड़ें\",\n\t\"app.utils.close-label\": \"बंद करना\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.duplicate\": \"डुप्लिकेट\",\n\t\"app.utils.edit\": \"संपादन करना\",\n\t\"app.utils.errors.file-too-big.message\": \"फ़ाइल बहुत बड़ी है\",\n\t\"app.utils.filter-value\": \"फ़िल्टर मान\",\n\t\"app.utils.filters\": \"फ़िल्टर\",\n\t\"app.utils.notify.data-loaded\": \"{target} लोड हो गया है\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"प्रकाशित करना\",\n\t\"app.utils.select-all\": \"सभी का चयन करे\",\n\t\"app.utils.select-field\": \"फ़ील्ड चुनें\",\n\t\"app.utils.select-filter\": \"फ़िल्टर चुनें\",\n\t\"app.utils.unpublish\": \"अप्रकाशित\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"यह सामग्री वर्तमान में निर्माणाधीन है और कुछ हफ्तों में वापस आ जाएगी!\",\n\t\"component.Input.error.validation.integer\": \"मान एक पूर्णांक होना चाहिए\",\n\t\"components.AutoReloadBlocker.description\": \"निम्न आदेशों में से एक के साथ Strapi चलाएँ:\",\n\t\"components.AutoReloadBlocker.header\": \"इस प्लगइन के लिए रीलोड फीचर की आवश्यकता है।\",\n\t\"components.ErrorBoundary.title\": \"कुछ गलत हो गया...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"शामिल है\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"शामिल है (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"इसी के साथ समाप्त होता है\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"इसी के साथ समाप्त होता है (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"है\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"है (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"से बड़ा है\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"से अधिक या बराबर है\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"से कम है\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"से कम या उसके बराबर है\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"नहीं है\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"नहीं है (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"शामिल नहीं है\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"शामिल नहीं है (case insensitive)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"निरर्थक नहीं है\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"निरर्थक है\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"इसके साथ आरंभ होता है\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"इसके साथ आरंभ होता है (case insensitive)\",\n\t\"components.Input.error.attribute.key.taken\": \"यह मान पहले से मौजूद है\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"बराबर नहीं हो सकता\",\n\t\"components.Input.error.attribute.taken\": \"यह फ़ील्ड नाम पहले से मौजूद है\",\n\t\"components.Input.error.contain.lowercase\": \"पासवर्ड में कम से कम एक लोअरकेस वर्ण होना चाहिए\",\n\t\"components.Input.error.contain.number\": \"सांकेतिक शब्द में कम से कम एक संख्या शामिल होना चाहिए\",\n\t\"components.Input.error.contain.uppercase\": \"पासवर्ड में कम से कम एक बड़ा अक्षर होना चाहिए\",\n\t\"components.Input.error.contentTypeName.taken\": \"यह नाम पहले से ही मौजूद है\",\n\t\"components.Input.error.custom-error\": \"{त्रुटि संदेश} \",\n\t\"components.Input.error.password.noMatch\": \"पासवर्ड मेल नहीं खाते\",\n\t\"components.Input.error.validation.email\": \"यह एक अमान्य ईमेल है\",\n\t\"components.Input.error.validation.json\": \"यह JSON प्रारूप से मेल नहीं खाता\",\n\t\"components.Input.error.validation.lowercase\": \"मान एक लोअरकेस स्ट्रिंग होना चाहिए\",\n\t\"components.Input.error.validation.max\": \"मान बहुत ज़्यादा है {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"मान बहुत लंबा है {max}.\",\n\t\"components.Input.error.validation.min\": \"मान बहुत कम है {min}.\",\n\t\"components.Input.error.validation.minLength\": \"मान बहुत छोटा है {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"श्रेष्ठ नहीं हो सकता\",\n\t\"components.Input.error.validation.regex\": \"मान रेगेक्स से मेल नहीं खाता।\",\n\t\"components.Input.error.validation.required\": \"यह मान आवश्यक है।\",\n\t\"components.Input.error.validation.unique\": \"यह मान पहले ही उपयोग किया जा चुका है।\",\n\t\"components.InputSelect.option.placeholder\": \"यहां चुनें\",\n\t\"components.ListRow.empty\": \"दिखाने के लिए कोई डेटा नहीं है।\",\n\t\"components.NotAllowedInput.text\": \"इस फ़ील्ड को देखने की अनुमति नहीं है\",\n\t\"components.OverlayBlocker.description\": \"आप एक ऐसी सुविधा का उपयोग कर रहे हैं जिसके लिए सर्वर को पुनरारंभ करने की आवश्यकता है। कृपया सर्वर चालू होने तक प्रतीक्षा करें.\",\n\t\"components.OverlayBlocker.description.serverError\": \"सर्वर को पुनरारंभ होना चाहिए था, कृपया टर्मिनल में अपने लॉग जांचें।\",\n\t\"components.OverlayBlocker.title\": \"पुनः आरंभ की प्रतीक्षा कर रहा है...\",\n\t\"components.OverlayBlocker.title.serverError\": \"पुनरारंभ अपेक्षा से अधिक समय ले रहा है\",\n\t\"components.PageFooter.select\": \"प्रति पृष्ठ प्रविष्टियाँ\",\n\t\"components.ProductionBlocker.description\": \"सुरक्षा उद्देश्यों के लिए हमें इस प्लगइन को अन्य वातावरणों में अक्षम करना होगा।\",\n\t\"components.ProductionBlocker.header\": \"यह प्लगइन केवल विकास में उपलब्ध है।\",\n\t\"components.Search.placeholder\": \"खोज...\",\n\t\"components.TableHeader.sort\": \"{लेबल} पर छाँटें\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"मार्कडाउन मोड\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"पूर्वावलोकन मोड\",\n\t\"components.Wysiwyg.collapse\": \"गिर जाना\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"शीर्षक H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"शीर्षक H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"शीर्षक H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"शीर्षक H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"शीर्षक H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"शीर्षक H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"एक शीर्षक जोड़ें\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"पात्र\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"बढ़ाना\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"फ़ाइलें खींचें और छोड़ें, क्लिपबोर्ड से चिपकाएं या {ब्राउज़ करें}।\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"उन्हें चुनें\",\n\t\"components.pagination.go-to\": \"पेज {पेज} पर जाएं\",\n\t\"components.pagination.go-to-next\": \"अगले पेज पर जाएं\",\n\t\"components.pagination.go-to-previous\": \"पिछले पृष्ठ पर जाएं\",\n\t\"components.pagination.remaining-links\": \"और {नंबर} अन्य लिंक\",\n\t\"components.popUpWarning.button.cancel\": \"नहीं, रद्द करें\",\n\t\"components.popUpWarning.button.confirm\": \"हाँ, पुष्टि करें\",\n\t\"components.popUpWarning.message\": \"क्या आप वाकई इसे हटाना चाहते हैं?\",\n\t\"components.popUpWarning.title\": \"कृपया पुष्टि करें\",\n\t\"form.button.continue\": \"जारी रखना\",\n\t\"form.button.done\": \"पूर्ण\",\n\t\"global.actions\": \"कार्रवाई\",\n\t\"global.back\": \"पीछे\",\n\t\"global.change-password\": \"पासवर्ड बदलें\",\n\t\"global.content-manager\": \"सामग्री प्रबंधक\",\n\t\"global.continue\": \"जारी रखना\",\n\t\"global.delete\": \"मिटाना\",\n\t\"global.delete-target\": \"{target} मिटाएं\",\n\t\"global.description\": \"विवरण\",\n\t\"global.details\": \"विवरण\",\n\t\"global.disabled\": \"अक्षम\",\n\t\"global.documentation\": \"प्रलेखन\",\n\t\"global.enabled\": \"सक्षम\",\n\t\"global.finish\": \"खत्म करना\",\n\t\"global.marketplace\": \"बाजार\",\n\t\"global.name\": \"नाम\",\n\t\"global.none\": \"कोई भी नहीं\",\n\t\"global.password\": \"पासवर्ड\",\n\t\"global.plugins\": \"प्लग-इन\",\n\t\"global.profile\": \"प्रोफ़ाइल\",\n\t\"global.prompt.unsaved\": \"क्या आप वाकई इस पेज को छोड़ना चाहते हैं? आपके सभी संशोधन नष्ट हो जाएंगे\",\n\t\"global.reset-password\": \"पासवर्ड रीसेट\",\n\t\"global.roles\": \"भूमिकाएँ\",\n\t\"global.save\": \"बचाना\",\n\t\"global.see-more\": \"और देखें\",\n\t\"global.select\": \"चुनना\",\n\t\"global.select-all-entries\": \"सभी प्रविष्टियों का चयन करें\",\n\t\"global.settings\": \"समायोजन\",\n\t\"global.type\": \"टाइप\",\n\t\"global.users\": \"उपयोगकर्ताओं\",\n\t\"notification.contentType.relations.conflict\": \"सामग्री प्रकार में परस्पर विरोधी संबंध हैं\",\n\t\"notification.default.title\": \"जानकारी:\",\n\t\"notification.error\": \"एक त्रुटि पाई गई\",\n\t\"notification.error.layout\": \"लेआउट पुनर्प्राप्त नहीं किया जा सका\",\n\t\"notification.form.error.fields\": \"प्रपत्र में कुछ त्रुटियां हैं\",\n\t\"notification.form.success.fields\": \"बदलाव सुरक्षित किया गया\",\n\t\"notification.link-copied\": \"लिंक को क्लिपबोर्ड में कॉपी किया गया\",\n\t\"notification.permission.not-allowed-read\": \"आपको यह दस्तावेज़ देखने की अनुमति नहीं है\",\n\t\"notification.success.delete\": \"आइटम हटा दिया गया है\",\n\t\"notification.success.saved\": \"बचाया\",\n\t\"notification.success.title\": \"सफलता:\",\n\t\"notification.version.update.message\": \"Strapi का एक नया संस्करण उपलब्ध है!\",\n\t\"notification.warning.title\": \"चेतावनी:\",\n\tor: or,\n\t\"request.error.model.unknown\": \"यह मॉडल मौजूद नहीं है\",\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, hi as default, or, skipToContent, submit };\n//# sourceMappingURL=hi-DW2CutdA.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,wCAAwC;AAAA,EACxC,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACD;", "names": []}