{"name": "@formatjs/icu-messageformat-parser", "version": "2.3.1", "main": "index.js", "module": "lib/index.js", "types": "index.d.ts", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/formatjs/formatjs.git", "directory": "packages/icu-messageformat-parser"}, "dependencies": {"@formatjs/ecma402-abstract": "1.14.3", "@formatjs/icu-skeleton-parser": "1.3.18", "tslib": "^2.4.0"}}