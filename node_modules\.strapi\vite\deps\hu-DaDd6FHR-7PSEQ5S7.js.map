{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/hu-DaDd6FHR.mjs"], "sourcesContent": ["const Analytics = \"Analitika\";\nconst Documentation = \"Do<PERSON>mentáció\";\nconst Email = \"Email\";\nconst Password = \"Je<PERSON><PERSON><PERSON>\";\nconst Provider = \"Szolgáltató\";\nconst ResetPasswordToken = \"Token visszaállítása\";\nconst Role = \"Szerepkör\";\nconst light = \"Világos\";\nconst dark = \"Sötét\";\nconst Username = \"Felhasználónév\";\nconst Users = \"Felhasználók\";\nconst anErrorOccurred = \"Hoppá! Valami elromlott. Kérlek próbáld újra.\";\nconst clearLabel = \"Kiür<PERSON><PERSON>\";\nconst or = \"Vagy\";\nconst skipToContent = \"Kihagyás\";\nconst submit = \"Küldés\";\nconst hu = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"A fiókodat felfüggesztettük\",\n\t\"Auth.components.Oops.text.admin\": \"Amennyiben ez hiba, kérj<PERSON>k vegye fel a kapcsolatot az adminisztrátorokkal!\",\n\t\"Auth.components.Oops.title\": \"Oops...\",\n\t\"Auth.form.active.label\": \"Aktív\",\n\t\"Auth.form.button.forgot-password\": \"Email küldése\",\n\t\"Auth.form.button.go-home\": \"Vissza a kezdőlapra\",\n\t\"Auth.form.button.login\": \"Bejelentkezés\",\n\t\"Auth.form.button.login.providers.error\": \"Nem sikerült kapcsolódni a szolgáltatón keresztül\",\n\t\"Auth.form.button.login.strapi\": \"Bejelentkezés Strapi-val\",\n\t\"Auth.form.button.password-recovery\": \"Jelszó visszaállítása\",\n\t\"Auth.form.button.register\": \"Kezdjük\",\n\t\"Auth.form.confirmPassword.label\": \"Jelszó megerősítése\",\n\t\"Auth.form.currentPassword.label\": \"Jelenlegi jelszó\",\n\t\"Auth.form.email.label\": \"Email\",\n\t\"Auth.form.email.placeholder\": \"e.g. <EMAIL>\",\n\t\"Auth.form.error.blocked\": \"A fiókodat az adminisztrátor blokkolta\",\n\t\"Auth.form.error.code.provide\": \"Hibás a megadott kód\",\n\t\"Auth.form.error.confirmed\": \"Az email cím nincs megerősítve\",\n\t\"Auth.form.error.email.invalid\": \"Hibás email.\",\n\t\"Auth.form.error.email.provide\": \"Kérjük adja meg felhasználónevét és jelszavát.\",\n\t\"Auth.form.error.email.taken\": \"Ez az email cím már foglalt.\",\n\t\"Auth.form.error.invalid\": \"Felhasználónév vagy jelszó hibás.\",\n\t\"Auth.form.error.params.provide\": \"Hibás a megadott adat.\",\n\t\"Auth.form.error.password.format\": \"A jelszó nem tartalmazhatja a `$` szimbólumot többször, mint három.\",\n\t\"Auth.form.error.password.local\": \"Ez a felhasználó nem állított be jelszót, kérjük jelentkezzen be szolgáltatón keresztül.\",\n\t\"Auth.form.error.password.matching\": \"A jelszavak nem egeyznek.\",\n\t\"Auth.form.error.password.provide\": \"Kérjük adja meg a jelszavát.\",\n\t\"Auth.form.error.ratelimit\": \"Túl sok próbálkozás, kérjük próbálkozzon újra egy perc múlva.\",\n\t\"Auth.form.error.user.not-exist\": \"Ez az email nem létezik.\",\n\t\"Auth.form.error.username.taken\": \"A felhasználónév foglalt.\",\n\t\"Auth.form.firstname.label\": \"Keresztnév\",\n\t\"Auth.form.firstname.placeholder\": \"pl. Elek\",\n\t\"Auth.form.forgot-password.email.label\": \"Adja meg az email címét\",\n\t\"Auth.form.forgot-password.email.label.success\": \"Az email-t sikeresen kiküldtük\",\n\t\"Auth.form.lastname.label\": \"Vezetéknév\",\n\t\"Auth.form.lastname.placeholder\": \"pl. Teszt\",\n\t\"Auth.form.password.hide-password\": \"Jelszó elrejtése\",\n\t\"Auth.form.password.hint\": \"A jelszónak legalább 8 karaktert, 1 nagybetűt, 1 kisbetűt és 1 számot kell tartalmaznia.\",\n\t\"Auth.form.password.show-password\": \"Jelszó megjelenítése\",\n\t\"Auth.form.register.news.label\": \"Értesítést kérek az új funkciókról és javításokról (ezzel elfogadja a {terms} és a {policy}).\",\n\t\"Auth.form.register.subtitle\": \"Ez csak az admin oldalra való bejelentkezésre ad lehetőséget. Minden elmentett adat a saját adatbázisaba kerül mentésre.\",\n\t\"Auth.form.rememberMe.label\": \"Emlékezz rám\",\n\t\"Auth.form.username.label\": \"Felhasználónév\",\n\t\"Auth.form.username.placeholder\": \"e.g. Kai_Doe\",\n\t\"Auth.form.welcome.subtitle\": \"Bejelentkezés a Strapi fiókjába\",\n\t\"Auth.form.welcome.title\": \"Üdvözöljük!\",\n\t\"Auth.link.forgot-password\": \"Elfelejtette a jelszavát?\",\n\t\"Auth.link.ready\": \"Készen áll a bejelentkezésre?\",\n\t\"Auth.link.signin\": \"Bejelentkezés\",\n\t\"Auth.link.signin.account\": \"Már van felhasználói fiókja?\",\n\t\"Auth.login.sso.divider\": \"Vagy bejelentkezés ezzel:\",\n\t\"Auth.login.sso.loading\": \"Szolgáltatók betöltése...\",\n\t\"Auth.login.sso.subtitle\": \"Bejelentkezés a fiókjába SSO-val\",\n\t\"Auth.privacy-policy-agreement.policy\": \"adatvédelmi nyilatkozat\",\n\t\"Auth.privacy-policy-agreement.terms\": \"felhasználási feltételek\",\n\t\"Auth.reset-password.title\": \"Jelszó visszaállítása\",\n\t\"Content Manager\": \"Tartalom Menedzser\",\n\t\"Content Type Builder\": \"Tartalomtípus építő\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Fájl feltöltés\",\n\t\"HomePage.head.title\": \"Kezdőlap\",\n\t\"HomePage.roadmap\": \"Nézze meg a terveinket\",\n\t\"HomePage.welcome.congrats\": \"Gratulálunk!\",\n\t\"HomePage.welcome.congrats.content\": \"Első adminisztrátorként jelentkezett be. Ahhoz, hogy felfedezhesse a Strapi funkcióit,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"azt ajánljuk, hogy hozza létre az első tartalomtípust.\",\n\t\"Media Library\": \"Média Könyvtár\",\n\t\"New entry\": \"Új elem\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"Szerepkörök & Engedélyek\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"Egyes szerepkörök nem törölhetők, mivel felhasználókhoz vannak társítva\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"A felhasználókhoz társított szerepkör nem törölhető\",\n\t\"Roles.RoleRow.select-all\": \"{name} kiválasztása tömeges műveletekhez\",\n\t\"Roles.RoleRow.user-count\": \"{number, plural, =0 {# felhasználó} one {# felhasználó} other {# felhasználók}}\",\n\t\"Roles.components.List.empty.withSearch\": \"Nincs a keresésnek megfelelő szerepkör ({search})...\",\n\t\"Settings.PageTitle\": \"Beállítások - {name}\",\n\t\"Settings.apiTokens.addFirstToken\": \"Első API Token hozzáadása\",\n\t\"Settings.apiTokens.addNewToken\": \"Új API Token hozzáadása\",\n\t\"Settings.tokens.copy.editMessage\": \"Biztonsági okokból csak egyszer láthatja a tokent.\",\n\t\"Settings.tokens.copy.editTitle\": \"Ez a token már nem elérhető.\",\n\t\"Settings.tokens.copy.lastWarning\": \"Másolja le a tokent, mert később már nem lesz látható!\",\n\t\"Settings.apiTokens.create\": \"Új hozzáadása\",\n\t\"Settings.apiTokens.description\": \"Az API felhasználásához generált tokenek listája\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"Még nincs tartalom hozzáadva...\",\n\t\"Settings.apiTokens.ListView.headers.name\": \"Név\",\n\t\"Settings.apiTokens.ListView.headers.description\": \"Leírás\",\n\t\"Settings.apiTokens.ListView.headers.type\": \"Token típusa\",\n\t\"Settings.apiTokens.ListView.headers.createdAt\": \"Létrehozva\",\n\t\"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Utoljára használva\",\n\t\"Settings.tokens.notification.copied\": \"Token a vágólapra másolva.\",\n\t\"Settings.apiTokens.title\": \"API Token-ek\",\n\t\"Settings.tokens.types.full-access\": \"Teljes hozzáférés\",\n\t\"Settings.tokens.types.read-only\": \"Csak olvasható\",\n\t\"Settings.tokens.duration.7-days\": \"7 nap\",\n\t\"Settings.tokens.duration.30-days\": \"30 nap\",\n\t\"Settings.tokens.duration.90-days\": \"90 nap\",\n\t\"Settings.tokens.duration.unlimited\": \"Korlátlan\",\n\t\"Settings.tokens.form.duration\": \"Token időtartama\",\n\t\"Settings.tokens.form.type\": \"Token típusa\",\n\t\"Settings.tokens.duration.expiration-date\": \"Lejárati dátum\",\n\t\"Settings.apiTokens.createPage.permissions.title\": \"Engedélyek\",\n\t\"Settings.apiTokens.createPage.permissions.description\": \"Csak az útvonalakhoz kötött műveletek szerepelnek az alábbiakban.\",\n\t\"Settings.tokens.RegenerateDialog.title\": \"Token újragenerálása\",\n\t\"Settings.tokens.popUpWarning.message\": \"Biztosan újragenerálod ezt a token-t?\",\n\t\"Settings.tokens.Button.cancel\": \"Mégse\",\n\t\"Settings.tokens.Button.regenerate\": \"Újragenerálás\",\n\t\"Settings.application.description\": \"Az adminisztrációs panel globális információi\",\n\t\"Settings.application.edition-title\": \"Aktuális csomag\",\n\t\"Settings.application.get-help\": \"Kérje segítségünket\",\n\t\"Settings.application.link-pricing\": \"Tekintse meg az összes csomagot\",\n\t\"Settings.application.link-upgrade\": \"Frissítse az adminisztrációs panelt\",\n\t\"Settings.application.node-version\": \"node verzió\",\n\t\"Settings.application.strapi-version\": \"strapi verzió\",\n\t\"Settings.application.strapiVersion\": \"strapi verzió\",\n\t\"Settings.application.title\": \"Áttekintés\",\n\t\"Settings.application.customization\": \"Testreszabás\",\n\t\"Settings.application.customization.carousel.title\": \"Logó\",\n\t\"Settings.application.customization.carousel.change-action\": \"Logó módosítása\",\n\t\"Settings.application.customization.carousel.reset-action\": \"Logó visszaállítása\",\n\t\"Settings.application.customization.carousel-slide.label\": \"Logó diasor\",\n\t\"Settings.application.customization.carousel-hint\": \"Változtasd meg az admin panel logóját (Max méret: {dimension}x{dimension}, Max fájlméret: {size}KB)\",\n\t\"Settings.application.customization.modal.cancel\": \"Mégse\",\n\t\"Settings.application.customization.modal.upload\": \"Logó feltöltése\",\n\t\"Settings.application.customization.modal.tab.label\": \"Hogyan szeretnéd feltölteni az állományaidat?\",\n\t\"Settings.application.customization.modal.upload.from-computer\": \"Számítógépről\",\n\t\"Settings.application.customization.modal.upload.file-validation\": \"Max méret: {dimension}x{dimension}, Max méret: {size}KB\",\n\t\"Settings.application.customization.modal.upload.error-format\": \"Rossz formátumot töltöttél fel (csak a következő formátumokat fogadja el: jpeg, jpg, png, svg).\",\n\t\"Settings.application.customization.modal.upload.error-size\": \"A feltöltött fájl túl nagy (max méret: {dimension}x{dimension}, max fájlméret: {size}KB)\",\n\t\"Settings.application.customization.modal.upload.error-network\": \"Hálózati hiba\",\n\t\"Settings.application.customization.modal.upload.cta.browse\": \"Fájlok tallózása\",\n\t\"Settings.application.customization.modal.upload.drag-drop\": \"Húzz és ejtsd ide vagy\",\n\t\"Settings.application.customization.modal.upload.from-url\": \"URL-ről\",\n\t\"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n\t\"Settings.application.customization.modal.upload.next\": \"Következő\",\n\t\"Settings.application.customization.modal.pending\": \"Függőben lévő logó\",\n\t\"Settings.application.customization.modal.pending.choose-another\": \"Válassz másik logót\",\n\t\"Settings.application.customization.modal.pending.title\": \"Logó készen áll a feltöltésre\",\n\t\"Settings.application.customization.modal.pending.subtitle\": \"Kezeljed a kiválasztott logót a feltöltés előtt\",\n\t\"Settings.application.customization.modal.pending.upload\": \"Logó feltöltése\",\n\t\"Settings.application.customization.modal.pending.card-badge\": \"kép\",\n\t\"Settings.error\": \"Hiba\",\n\t\"Settings.global\": \"Globális Beállítások\",\n\t\"Settings.permissions\": \"Adminisztrációs panel\",\n\t\"Settings.permissions.category\": \"{category} engedélyeinek beállításai\",\n\t\"Settings.permissions.category.plugins\": \"{category} plugin engedélyeinek beállításai\",\n\t\"Settings.permissions.conditions.anytime\": \"Bármikor\",\n\t\"Settings.permissions.conditions.apply\": \"Alkalmaz\",\n\t\"Settings.permissions.conditions.can\": \"Tudja\",\n\t\"Settings.permissions.conditions.conditions\": \"Határozza meg a feltételeket\",\n\t\"Settings.permissions.conditions.links\": \"Linkek\",\n\t\"Settings.permissions.conditions.no-actions\": \"Először választania kell egy műveletet (create, read, update, ...) mielőtt megadja a feltételeket.\",\n\t\"Settings.permissions.conditions.none-selected\": \"Bármikor\",\n\t\"Settings.permissions.conditions.or\": \"VAGY\",\n\t\"Settings.permissions.conditions.when\": \"Mikor\",\n\t\"Settings.permissions.select-all-by-permission\": \"Minden {label} hozzáféres kiválasztása\",\n\t\"Settings.permissions.select-by-permission\": \"{label} hozzáféres kiválasztása\",\n\t\"Settings.permissions.users.create\": \"Új felhasználó meghívása\",\n\t\"Settings.permissions.users.email\": \"Email\",\n\t\"Settings.permissions.users.firstname\": \"Keresztnév\",\n\t\"Settings.permissions.users.lastname\": \"Vezetéknév\",\n\t\"Settings.permissions.users.user-status\": \"Felhasználói állapot\",\n\t\"Settings.permissions.users.roles\": \"Szerepek\",\n\t\"Settings.permissions.users.username\": \"Felhasználónév\",\n\t\"Settings.permissions.users.active\": \"Aktív\",\n\t\"Settings.permissions.users.inactive\": \"Inaktív\",\n\t\"Settings.permissions.users.form.sso\": \"Csatlakozas SSO-val\",\n\t\"Settings.permissions.users.form.sso.description\": \"Ha engedélyezve van (ON), a felhasználók bejelentkezhetnek SSO-n keresztül\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"Minden felhasználó, aki hozzáfér a Strapi adminisztrációs panelhez\",\n\t\"Settings.permissions.users.tabs.label\": \"Hozzáférések Tab\",\n\t\"Settings.permissions.users.strapi-super-admin\": \"Super Adminisztrátor\",\n\t\"Settings.permissions.users.strapi-editor\": \"Szerkesztő\",\n\t\"Settings.permissions.users.strapi-author\": \"Szerző\",\n\t\"Settings.profile.form.notify.data.loaded\": \"Profiladatok betöltve\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"A kiválasztott felület nyelvének törlése\",\n\t\"Settings.profile.form.section.experience.here\": \"itt\",\n\t\"Settings.profile.form.section.experience.documentation\": \"dokumentáció\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"A felület nyelve\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Ez csak a saját felületét jeleníti meg a kiválasztott nyelven.\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"A kiválasztás csak az Ön számára módosítja a felület nyelvét. Kérjük, olvassa el ezt a {document}, hogy más nyelveket a csapata számára is elérhetővé tehesse.\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"Felület mód\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"Megjeleníti a felhasználói felületedet a kiválasztott módban.\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{name} mód\",\n\tlight: light,\n\tdark: dark,\n\t\"Settings.profile.form.section.experience.title\": \"Tapasztalat\",\n\t\"Settings.profile.form.section.head.title\": \"Felhasználói profil\",\n\t\"Settings.profile.form.section.profile.page.title\": \"Profil oldal\",\n\t\"Settings.roles.create.description\": \"Határozza meg a szerephezkörhöz biztosított jogokat\",\n\t\"Settings.roles.create.title\": \"Szerepkör létrehozása\",\n\t\"Settings.roles.created\": \"A szerepkör létrejött\",\n\t\"Settings.roles.edit.title\": \"Szerepkör módosítása\",\n\t\"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# felhasználó} one {# felhasználó} other {# felhasználók}} ezzel a szereppel\",\n\t\"Settings.roles.form.created\": \"Létrehozva\",\n\t\"Settings.roles.form.description\": \"A szerepkör neve és leírása\",\n\t\"Settings.roles.form.permission.property-label\": \"{label} hozzáfére's\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"Mezők hozzáférései\",\n\t\"Settings.roles.form.permissions.create\": \"Létrehoz\",\n\t\"Settings.roles.form.permissions.delete\": \"Töröl\",\n\t\"Settings.roles.form.permissions.publish\": \"Közzétesz\",\n\t\"Settings.roles.form.permissions.read\": \"Olvasás\",\n\t\"Settings.roles.form.permissions.update\": \"Frissítés\",\n\t\"Settings.roles.list.button.add\": \"Új szerepkör hozzáadása\",\n\t\"Settings.roles.list.description\": \"Szerepkörök listája\",\n\t\"Settings.roles.title.singular\": \"Szerepkör\",\n\t\"Settings.sso.description\": \"Konfigurálja az egyszeri bejelentkezés funkció beállításait.\",\n\t\"Settings.sso.form.defaultRole.description\": \"Az új hitelesített felhasználót a kiválasztott szerepkörhöz csatolja\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"Nincs megfelelő engedélye az adminisztrátori szerepkörök olvasásához\",\n\t\"Settings.sso.form.defaultRole.label\": \"Alapértelmezett szerepkör\",\n\t\"Settings.sso.form.registration.description\": \"Egyszeri bejelentkezéskor, ha nincs fiók, hozzon létre új felhasználót\",\n\t\"Settings.sso.form.registration.label\": \"Automatikus regisztráció\",\n\t\"Settings.sso.title\": \"Egyszeri bejelentkezés\",\n\t\"Settings.webhooks.create\": \"Webhook létrehozása\",\n\t\"Settings.webhooks.create.header\": \"Új fejléc létrehozása\",\n\t\"Settings.webhooks.created\": \"Webhook létrehozva\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"Ez az esemény csak olyan tartalmak esetében létezik, amelyeknél engedélyezve van a Piszkozat/Közzététel rendszer\",\n\t\"Settings.webhooks.events.create\": \"Létrehoz\",\n\t\"Settings.webhooks.events.update\": \"Frissít\",\n\t\"Settings.webhooks.form.events\": \"Esemnények\",\n\t\"Settings.webhooks.form.headers\": \"Fejléc\",\n\t\"Settings.webhooks.form.url\": \"Url\",\n\t\"Settings.webhooks.headers.remove\": \"Fejlésor eltávolítása {number}\",\n\t\"Settings.webhooks.key\": \"Kulcs\",\n\t\"Settings.webhooks.list.button.add\": \"Új webhook létrehozása\",\n\t\"Settings.webhooks.list.description\": \"Értesítések a POST módosításairól\",\n\t\"Settings.webhooks.list.empty.description\": \"Nem található webhook\",\n\t\"Settings.webhooks.list.empty.link\": \"Tekintse meg dokumentációnkat\",\n\t\"Settings.webhooks.list.empty.title\": \"Még nincsenek webhookok\",\n\t\"Settings.webhooks.list.th.actions\": \"Műveletek\",\n\t\"Settings.webhooks.list.th.status\": \"Státusz\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhook-ok\",\n\t\"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# elem} other {# elemek}} kiválasztva\",\n\t\"Settings.webhooks.trigger\": \"Kapcsoló\",\n\t\"Settings.webhooks.trigger.cancel\": \"Kapcsoló törlése\",\n\t\"Settings.webhooks.trigger.pending\": \"Folyamatban…\",\n\t\"Settings.webhooks.trigger.save\": \"Kérjük mentse\",\n\t\"Settings.webhooks.trigger.success\": \"Sikerült!\",\n\t\"Settings.webhooks.trigger.success.label\": \"A kapcsoló sikerült\",\n\t\"Settings.webhooks.trigger.test\": \"Teszt-kapcsoló\",\n\t\"Settings.webhooks.trigger.title\": \"Először mentsen\",\n\t\"Settings.webhooks.value\": \"Érték\",\n\t\"Usecase.back-end\": \"Back-end fejlesztő\",\n\t\"Usecase.button.skip\": \"Kérdezés kihagyása\",\n\t\"Usecase.content-creator\": \"Tartalomkészítő\",\n\t\"Usecase.front-end\": \"Front-end fejlesztő\",\n\t\"Usecase.full-stack\": \"Teljeskörű fejlesztő\",\n\t\"Usecase.input.work-type\": \"Milyen típusú munkát végzel?\",\n\t\"Usecase.notification.success.project-created\": \"A projekt sikeresen létrehozva\",\n\t\"Usecase.other\": \"Egyéb\",\n\t\"Usecase.title\": \"Mesélj egy kicsit magadról\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"Felhasználók & Engedélyek\",\n\t\"Users.components.List.empty\": \"Nincsenek felhasználók...\",\n\t\"Users.components.List.empty.withFilters\": \"Nincs a beállított szűrőknek megfelelő felhasználó..\",\n\t\"Users.components.List.empty.withSearch\": \"Nincs a keresének megfelelő felhasználó ({search})...\",\n\t\"admin.pages.MarketPlacePage.head\": \"Piactér - Plugin-ok\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"Ön offline állapotban van\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"Csatlakoznia kell az internethez a Strapi Market eléréséhez.\",\n\t\"admin.pages.MarketPlacePage.plugins\": \"Bővítmények\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"Telepítési parancs másolása\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"A telepítési parancs készen áll a terminálba való bemásolásra\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"További információk\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"{pluginName} bővítmény további információi\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"További információk\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"Telepítve\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Készítette: Strapi\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Bővítmény hitelesítve a Strapi által\",\n\t\"admin.pages.MarketPlacePage.plugin.version\": \"Frissítsd a Strapi verziód: \\\"{strapiAppVersion}\\\" erre: \\\"{versionRange}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.version.null\": \"Nem sikerült ellenőrizni a kompatibilitást a Strapi verzióddal: \\\"{strapiAppVersion}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.githubStars\": \"Ezt a plugint {starsCount} csillagra jelölték a GitHub-on\",\n\t\"admin.pages.MarketPlacePage.plugin.downloads\": \"Ezt a plugint hetente {downloadsCount} alkalommal töltik le\",\n\t\"admin.pages.MarketPlacePage.providers\": \"Szolgáltatók\",\n\t\"admin.pages.MarketPlacePage.provider.githubStars\": \"Ezt a szolgáltatót {starsCount} csillagra jelölték a GitHub-on\",\n\t\"admin.pages.MarketPlacePage.provider.downloads\": \"Ezt a szolgáltatót hetente {downloadsCount} alkalommal töltik le\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"Keresés törlése\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \"Nincs találat erre: \\\"{target}\\\"\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"Keresés\",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"Plugin küldése\",\n\t\"admin.pages.MarketPlacePage.submit.provider.link\": \"Provider beküldése\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \"Hozzon ki többet a Strapi-ból\",\n\t\"admin.pages.MarketPlacePage.tab-group.label\": \"Strapi pluginek és szolgáltatók\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"Hiányzik egy plugin?\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"Mondd el, milyen pluginra van szükséged, és tájékoztatjuk a közösségi plugin fejlesztőinket, hogy esetleg ötletet meríthessenek belőle!\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical\": \"Betűrendes rendezés\",\n\t\"admin.pages.MarketPlacePage.sort.newest\": \"Legújabb\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Rendezés betűrend szerint\",\n\t\"admin.pages.MarketPlacePage.sort.newest.selected\": \"Rendezés legújabbak szerint\",\n\t\"admin.pages.MarketPlacePage.sort.githubStars\": \"GitHub csillagok száma\",\n\t\"admin.pages.MarketPlacePage.sort.githubStars.selected\": \"Rendezés GitHub csillagok szerint\",\n\t\"admin.pages.MarketPlacePage.sort.npmDownloads\": \"Letöltések száma\",\n\t\"admin.pages.MarketPlacePage.sort.npmDownloads.selected\": \"Rendezés npm letöltések szerint\",\n\t\"admin.pages.MarketPlacePage.filters.collections\": \"Gyűjtemények\",\n\t\"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"{count, plural, =0 {Nincsenek gyűjtemények} one {# gyűjtemény} other {# gyűjtemények}} kiválasztva\",\n\t\"admin.pages.MarketPlacePage.filters.categories\": \"Kategóriák\",\n\t\"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count, plural, =0 {Nincsenek kategóriák} one {# kategória} other {# kategóriák}} kiválasztva\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"Másolás a vágólapra\",\n\t\"app.component.search.label\": \"{target} keresése\",\n\t\"app.component.table.duplicate\": \"{target} duplikálása\",\n\t\"app.component.table.edit\": \"{target} szerkesztése\",\n\t\"app.component.table.select.one-entry\": \"{target} kiválasztása\",\n\t\"app.components.BlockLink.blog\": \"Blog\",\n\t\"app.components.BlockLink.blog.content\": \"Olvassa el a legfrissebb híreket a Strapiról és az ökoszisztémáról.\",\n\t\"app.components.BlockLink.code\": \"Kód példák\",\n\t\"app.components.BlockLink.code.content\": \"Tanuljon a közösség által fejlesztett valós projektek segítségével.\",\n\t\"app.components.BlockLink.documentation.content\": \"Fedezze fel az alapvető fogalmakat, útmutatókat és utasításokat.\",\n\t\"app.components.BlockLink.tutorial\": \"Oktatóanyagok\",\n\t\"app.components.BlockLink.tutorial.content\": \"Kövesse az utasításokat a Strapi használatához és testreszabásához.\",\n\t\"app.components.Button.cancel\": \"Mégsem\",\n\t\"app.components.Button.confirm\": \"Megerősítés\",\n\t\"app.components.Button.reset\": \"Visszaállítás\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"Hamarosan\",\n\t\"app.components.ConfirmDialog.title\": \"Megerősítés\",\n\t\"app.components.DownloadInfo.download\": \"Letöltés folyamatban...\",\n\t\"app.components.DownloadInfo.text\": \"Ez eltarthat egy percig. Köszönjük a türelmét.\",\n\t\"app.components.EmptyAttributes.title\": \"Még nincsenek mezők\",\n\t\"app.components.EmptyStateLayout.content-document\": \"Nem található tartalom\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"Nincs megfelelő jogosultsága a tartalomhozhoz\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>Hozz létre és kezelj minden tartalmat itt a Tartalomkezelőben.</p><p>Például: A Blog weboldal példáját folytatva, írhatsz egy Cikket, mentheted és publikálhatod úgy, ahogy szeretnéd.</p><p>💡 Gyors tipp - Ne felejtsd el publikálni a létrehozott tartalmat.</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"⚡️ Tartalom létrehozása\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>Szuper, még egy lépés van hátra!</p><b>🚀 Lásd a tartalmat működés közben</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"API tesztelése\",\n\t\"app.components.GuidedTour.CM.success.title\": \"2. lépés: Kész ✅\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>A Gyűjtemény típusok segítségével több bejegyzést tudsz kezelni, míg az Egy típusok a csak egy bejegyzés kezelésére alkalmasak.</p> <p>Például: Egy Blog weboldalnál a Cikkek lennek egy Gyűjtemény típus, míg a Honlap lenne egy Egy típus.</p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"Hozz létre egy Gyűjtemény típust\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 Hozz létre első Gyűjtemény típust\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>Jól haladsz!</p><b>⚡️ Mit szeretnél megosztani a világgal?</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"1. lépés: Kész ✅\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>Hozz létre itt egy hitelesítési token-t, és töltsd le az általad létrehozott tartalmat.</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"API Token generálása\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Lásd a tartalmat működés közben\",\n\t\"app.components.GuidedTour.apiTokens.success.content\": \"<p>Lásd a tartalmat működés közben az HTTP kéréssel:</p><ul><li><p>Erre a URL-re: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Ezzel a fejléccel: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>További lehetőségek a tartalommal való interakcióhoz, lásd a <documentationLink>dokumentációt</documentationLink>.</p>\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"Menj vissza a főoldalra\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"3. lépés: befejezve ✅\",\n\t\"app.components.GuidedTour.create-content\": \"Tartalom létrehozása\",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️ Mire szeretnéd megosztani a világgal?\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"Menj a Content type Builder-be\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 Építsd fel a tartalom struktúráját\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"API tesztelése\",\n\t\"app.components.GuidedTour.skip\": \"A túra átugrása\",\n\t\"app.components.GuidedTour.title\": \"3 lépés a kezdéshez\",\n\t\"app.components.HomePage.button.blog\": \"Bővebben a blogon\",\n\t\"app.components.HomePage.community\": \"Csatlakozz a közösséghez\",\n\t\"app.components.HomePage.community.content\": \"Beszélgessen a csapattagokkal, a közreműködőkkel és a fejlesztőkkel különböző csatornákon.\",\n\t\"app.components.HomePage.create\": \"Hozza létre az első tartalomtípust\",\n\t\"app.components.HomePage.roadmap\": \"Tekintse meg terveinket\",\n\t\"app.components.HomePage.welcome\": \"Üdvözöljük a fedélzeten 👋\",\n\t\"app.components.HomePage.welcome.again\": \"Üdvözöljük 👋\",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Gratulálunk! Első rendszergazdaként jelentkezett be. A Strapi által nyújtott funkciók felfedezéséhez javasoljuk, hogy hozza létre első tartalomtípusát!\",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"Reméljük, hogy jól halad a projektje! Olvassa el a Strapi legfrissebb híreit. Visszajelzései alapján mindent megteszünk, hogy javítsuk a terméket.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"problémák.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" vagy írjon \",\n\t\"app.components.ImgPreview.hint\": \"Húzza a fájlt erre a területre, vagy {browse} a feltöltendő fájlért\",\n\t\"app.components.ImgPreview.hint.browse\": \"tallózás\",\n\t\"app.components.InputFile.newFile\": \"Új fájl hozzáadása\",\n\t\"app.components.InputFileDetails.open\": \"Megnyitás új lapon\",\n\t\"app.components.InputFileDetails.originalName\": \"Eredeti név:\",\n\t\"app.components.InputFileDetails.remove\": \"Fájl eltávolítása\",\n\t\"app.components.InputFileDetails.size\": \"Méret:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"A bővítmény letöltése és telepítése eltarthat néhány másodpercig.\",\n\t\"app.components.InstallPluginPage.Download.title\": \"Letöltés...\",\n\t\"app.components.InstallPluginPage.description\": \"Bővítse alkalmazását erőfeszítés nélkül.\",\n\t\"app.components.LeftMenu.collapse\": \"A navigációs sáv összecsukása\",\n\t\"app.components.LeftMenu.expand\": \"A navigációs sáv kinyitása\",\n\t\"app.components.LeftMenu.general\": \"Általános\",\n\t\"app.components.LeftMenu.logout\": \"Kijelentkezés\",\n\t\"app.components.LeftMenu.logo.alt\": \"Alkalmazás logó\",\n\t\"app.components.LeftMenu.plugins\": \"Bővítmények\",\n\t\"app.components.LeftMenu.navbrand.title\": \"Strapi Műszerfal\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"Munkaterület\",\n\t\"app.components.LeftMenuFooter.help\": \"Segítség\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Powered by \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Gyűjtemény típusai\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Beállítások\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Általános\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Nincs bővítmény telepítve\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Bővítmények\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Egyedülálló típusok\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"A bővítmény eltávolítása eltarthat néhány másodpercig.\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"Eltávolítás\",\n\t\"app.components.ListPluginsPage.description\": \"A telepített bővítmények listája.\",\n\t\"app.components.ListPluginsPage.head.title\": \"A bővítmények listája\",\n\t\"app.components.Logout.logout\": \"Kijelentkezés\",\n\t\"app.components.Logout.profile\": \"Profil\",\n\t\"app.components.MarketplaceBanner\": \"Fedezze fel a közösség által épített modulokat, és még sok más fantasztikus dolgot, amik segítenek a projekt elindításában.\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"a strapi rocket logo\",\n\t\"app.components.MarketplaceBanner.link\": \"Nézze meg most\",\n\t\"app.components.NotFoundPage.back\": \"Vissza a kezdőoldalra\",\n\t\"app.components.NotFoundPage.description\": \"Nem található\",\n\t\"app.components.Official\": \"Hivatalos\",\n\t\"app.components.Onboarding.help.button\": \"Súgó gomb\",\n\t\"app.components.Onboarding.label.completed\": \"% elkészült\",\n\t\"app.components.Onboarding.title\": \"Bemutató videók\",\n\t\"app.components.PluginCard.Button.label.download\": \"Letöltés\",\n\t\"app.components.PluginCard.Button.label.install\": \"Már telepítve van\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Az automatikus újratöltés funkciót engedélyezni kell. Kérjük, indítsa el az alkalmazást ezzel a paranccsal: `yarn develop`.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Megértettem!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Biztonsági okokból egy plugin csak fejlesztői környezetben tölthető le.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"A letöltés nem lehetséges\",\n\t\"app.components.PluginCard.compatible\": \"Kompatibilis az alkalmazásoddal\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Kompatibilis a közösséggel\",\n\t\"app.components.PluginCard.more-details\": \"További részletek\",\n\t\"app.components.ToggleCheckbox.off-label\": \"Kikapcsol\",\n\t\"app.components.ToggleCheckbox.on-label\": \"Bekapcsol\",\n\t\"app.components.Users.MagicLink.connect\": \"Másolja ki és ossza meg ezt a linket, hogy hozzáférést biztosítson ehhez a felhasználóhoz\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"Küldje el ezt a linket a felhasználónak. Az első bejelentkezés történhet SSO szolgáltatón keresztül\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"Felhasználói adatok\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"A felhasználó szerepkörei\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Egy felhasználónak lehet egy, illetve több szerepköre is\",\n\t\"app.components.Users.SortPicker.button-label\": \"Rendezés\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"Email (A - Z)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"Email (Z - A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"Keresztnév (A - Z)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"Keresztnév (Z - A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"Vezetéknév (A - Z)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"Vezetéknév (Z - A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"Felhasználónév (A - Z)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"Felhasználónév (Z - A)\",\n\t\"app.components.listPlugins.button\": \"Új bővítmény hozzáadása\",\n\t\"app.components.listPlugins.title.none\": \"Nincs telepítve bővítmény\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Hiba történt a bővítmény eltávolítása közben\",\n\t\"app.containers.App.notification.error.init\": \"Hiba történt az API kérése közben\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Ha nem kapja meg ezt a linket, forduljon az adminisztrátorhoz.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Eltarthat néhány percig, amíg megkapja a jelszó-helyreállítási linket.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email elküdlve\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"Aktív\",\n\t\"app.containers.Users.EditPage.header.label\": \"{name} módosítása\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"Felhasználó szerkesztése\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"A hozzárendelt szerepkörök\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"Felhasználó meghívása\",\n\t\"app.links.configure-view\": \"A nézet testreszabása\",\n\t\"app.page.not.found\": \"Hoppá! Úgy tűnik, nem találjuk a keresett oldalt...\",\n\t\"app.static.links.cheatsheet\": \"Puska\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"Szűrő hozzáadása\",\n\t\"app.utils.close-label\": \"Bezárás\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.duplicate\": \"Duplikálás\",\n\t\"app.utils.edit\": \"Szerkesztés\",\n\t\"app.utils.errors.file-too-big.message\": \"A fájl mérete túl nagy\",\n\t\"app.utils.filter-value\": \"Szűrési érték\",\n\t\"app.utils.filters\": \"Szűrők\",\n\t\"app.utils.notify.data-loaded\": \"A {target} betöltődött\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"Közzétesz\",\n\t\"app.utils.select-all\": \"Minden kiválasztása\",\n\t\"app.utils.select-field\": \"Mező kiválasztása\",\n\t\"app.utils.select-filter\": \"Szűrő kiválasztása\",\n\t\"app.utils.unpublish\": \"Közzététel visszavonása\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"Ez a tartalom jelenleg fejlesztés alatt áll, és néhány héten belül újra elérhető lesz!\",\n\t\"component.Input.error.validation.integer\": \"Az értéknek egész számnak kell lennie\",\n\t\"components.AutoReloadBlocker.description\": \"Futtassa a Strapit a következő parancsok egyikével:\",\n\t\"components.AutoReloadBlocker.header\": \"Ehhez a bővítményhez töltse be újra a funkciót.\",\n\t\"components.ErrorBoundary.title\": \"Valami elromlott...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"tartalmazza\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"tartalmazza (nem nagybetű érzékeny)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"erre végződik\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"erre végződik (nem nagybetű érzékeny)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"egyenlő\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"egyenlő (nem nagybetű érzékeny)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"nagyobb, mint\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"nagyobb, vagy egyenlő, mint\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"kisebb, mint\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"kisebb, vagy egyenlő, mint\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"nem egyenlő\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"nem egyenlő (nem nagybetű érzékeny)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"nem tartalmazza\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"nem tartalmazza (nem nagybetű érzékeny)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"nem null\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"null\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"ezzel kezdődik\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"ezzel kezdődik (nem nagybetű érzékeny)\",\n\t\"components.Input.error.attribute.key.taken\": \"Ez az érték már létezik\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Nem lehet egyenlő\",\n\t\"components.Input.error.attribute.taken\": \"Ez a mezőnév már létezik\",\n\t\"components.Input.error.contain.lowercase\": \"A jelszónak tartalmaznia kell legalább egy kisbetűt\",\n\t\"components.Input.error.contain.number\": \"A jelszónak tartalmaznia kell legalább egy számot\",\n\t\"components.Input.error.contain.uppercase\": \"A jelszónak tartalmaznia kell legalább egy nagybetűt\",\n\t\"components.Input.error.contentTypeName.taken\": \"Ez a név már létezik\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"A jelszavak nem egyeznek\",\n\t\"components.Input.error.validation.email\": \"Érvénytelen e-mail\",\n\t\"components.Input.error.validation.json\": \"Hibás JSON formátum\",\n\t\"components.Input.error.validation.lowercase\": \"Az értéknek kisbetűs karakterláncnak kell lennie\",\n\t\"components.Input.error.validation.max\": \"A megadott érték túl nagy {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"A megadott érték túl hosszú {max}.\",\n\t\"components.Input.error.validation.min\": \"A megadott érték túl alacsony {min}.\",\n\t\"components.Input.error.validation.minLength\": \"A megadott érték túl rövid {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"Nem lehet felsőbbrendű\",\n\t\"components.Input.error.validation.regex\": \"A megadott érték formátuma nem megfelelő.\",\n\t\"components.Input.error.validation.required\": \"Ez az érték kötelező.\",\n\t\"components.Input.error.validation.unique\": \"Ez az érték már használatban van.\",\n\t\"components.InputSelect.option.placeholder\": \"Válasszon itt\",\n\t\"components.ListRow.empty\": \"Nincsenek megjelenítendő adatok.\",\n\t\"components.NotAllowedInput.text\": \"Nincs jogosultsága a mező megtekintéséhez\",\n\t\"components.OverlayBlocker.description\": \"Olyan funkciót használ, amelynek újra kell indítania a szervert. Kérjük, várja meg, amíg a szerver feláll.\",\n\t\"components.OverlayBlocker.description.serverError\": \"A szervernek újra kellett volna indulnia, kérjük, ellenőrizze a logokat a terminálban.\",\n\t\"components.OverlayBlocker.title\": \"Újraindításra vár...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Az újraindítás a vártnál tovább tart\",\n\t\"components.PageFooter.select\": \"Bejegyzések oldalanként\",\n\t\"components.ProductionBlocker.description\": \"Biztonsági okokból le kell tiltanunk ezt a bővítményt más környezetekben.\",\n\t\"components.ProductionBlocker.header\": \"Ez a bővítmény csak fejlesztői környezetben érhető el.\",\n\t\"components.Search.placeholder\": \"Keresés...\",\n\t\"components.TableHeader.sort\": \"Rendezés {label} szerint\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown mód\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"Előnézet mód\",\n\t\"components.Wysiwyg.collapse\": \"Összecsuk\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"Cím H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"Cím H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"Cím H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"Cím H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"Cím H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"Cím H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Cím hozzádása\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"karakterek\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Kinyit\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Fájlok behúzása, beillesztése a vágólapról vagy {browse}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"Válassza ki őket\",\n\t\"components.pagination.go-to\": \"Ugrás a(z) {page} oldalra\",\n\t\"components.pagination.go-to-next\": \"Ugrás a következő oldalra\",\n\t\"components.pagination.go-to-previous\": \"Ugrás az előző oldalra\",\n\t\"components.pagination.remaining-links\": \"És {number} további link\",\n\t\"components.popUpWarning.button.cancel\": \"Mégsem\",\n\t\"components.popUpWarning.button.confirm\": \"Megerősítés\",\n\t\"components.popUpWarning.message\": \"Biztosan törölni szeretné?\",\n\t\"components.popUpWarning.title\": \"Erősítse meg\",\n\t\"form.button.continue\": \"Folytatás\",\n\t\"form.button.done\": \"Kész\",\n\t\"global.search\": \"Keresés\",\n\t\"global.actions\": \"Műveletek\",\n\t\"global.back\": \"Vissza\",\n\t\"global.cancel\": \"Mégsem\",\n\t\"global.change-password\": \"Jelszó megváltoztatása\",\n\t\"global.content-manager\": \"Tartalomkezelő\",\n\t\"global.continue\": \"Folytatás\",\n\t\"global.delete\": \"Törlés\",\n\t\"global.delete-target\": \"{target} törlése\",\n\t\"global.description\": \"Leírás\",\n\t\"global.details\": \"Részletek\",\n\t\"global.disabled\": \"Letiltva\",\n\t\"global.documentation\": \"Dokumentáció\",\n\t\"global.enabled\": \"Engedélyezve\",\n\t\"global.finish\": \"Befejezés\",\n\t\"global.marketplace\": \"Piactér\",\n\t\"global.name\": \"Név\",\n\t\"global.none\": \"Nincs\",\n\t\"global.password\": \"Jelszó\",\n\t\"global.plugins\": \"Bővítmények\",\n\t\"global.plugins.content-manager\": \"Tartalomkezelő\",\n\t\"global.plugins.content-manager.description\": \"Gyors módja annak, hogy megtekintse, szerkesztse és törölje az adatokat az adatbázisában.\",\n\t\"global.plugins.content-type-builder\": \"Tartalomtípus-építő\",\n\t\"global.plugins.content-type-builder.description\": \"Modellezze az API adatszerkezetét. Hozzon létre új mezőket és relationokat csak egy perc alatt. A fájlok automatikusan létrehozódnak és frissülnek a projektjében.\",\n\t\"global.plugins.email\": \"E-mail\",\n\t\"global.plugins.email.description\": \"Állítsa be az alkalmazást, hogy e-maileket küldjön.\",\n\t\"global.plugins.upload\": \"Médiatár\",\n\t\"global.plugins.upload.description\": \"Médiafájlok kezelése.\",\n\t\"global.plugins.graphql\": \"GraphQL\",\n\t\"global.plugins.graphql.description\": \"GraphQL végpont hozzáadása alapértelmezett API metódusokkal.\",\n\t\"global.plugins.documentation\": \"Dokumentáció\",\n\t\"global.plugins.documentation.description\": \"OpenAPI Dokumentum létrehozása és API megjelenítése SWAGGER UI-val.\",\n\t\"global.plugins.i18n\": \"Nemzetköziítés\",\n\t\"global.plugins.i18n.description\": \"Ez a plugin lehetővé teszi különböző nyelveken történő tartalom létrehozását, olvasását és frissítését, tanto az Admin Panelból, mint az API-ból.\",\n\t\"global.plugins.sentry\": \"Sentry\",\n\t\"global.plugins.sentry.description\": \"Strapi hibaesemények küldése a Sentry-be.\",\n\t\"global.plugins.users-permissions\": \"Szerepek & Engedélyek\",\n\t\"global.plugins.users-permissions.description\": \"API védelme teljes hitelesítési folyamattal JWT alapján. Ez a plugin egyúttal olyan ACL stratégiát is tartalmaz, amely lehetővé teszi a felhasználói csoportok közötti engedélyek kezelését.\",\n\t\"global.profile\": \"Profil\",\n\t\"global.prompt.unsaved\": \"Biztos, hogy elhagyja ezt az oldalt? Az összes módosítása elveszik\",\n\t\"global.reset-password\": \"Jelszó visszaállítása\",\n\t\"global.roles\": \"Szerepek\",\n\t\"global.save\": \"Mentés\",\n\t\"global.see-more\": \"Továbbiak megtekintése\",\n\t\"global.select\": \"Kiválasztás\",\n\t\"global.select-all-entries\": \"Az összes bejegyzés kiválasztása\",\n\t\"global.settings\": \"Beállítások\",\n\t\"global.type\": \"Típus\",\n\t\"global.users\": \"Felhasználók\",\n\t\"notification.contentType.relations.conflict\": \"A tartalomtípusnak ellenkező kapcsolatai vannak\",\n\t\"notification.default.title\": \"Információ:\",\n\t\"notification.error\": \"Hiba lépett fel\",\n\t\"notification.error.layout\": \"Nem sikerült lekérni az elrendezést\",\n\t\"notification.form.error.fields\": \"Az űrlap kitöltése hibás\",\n\t\"notification.form.success.fields\": \"Változtatások elmentve\",\n\t\"notification.link-copied\": \"A link a vágólapra másolva\",\n\t\"notification.permission.not-allowed-read\": \"Ezt a dokumentumot nem tekintheti meg\",\n\t\"notification.success.delete\": \"Az elemet törölték\",\n\t\"notification.success.saved\": \"Mentve\",\n\t\"notification.success.title\": \"Sikeres:\",\n\t\"notification.success.apitokencreated\": \"API Token sikeresen létrehozva\",\n\t\"notification.success.apitokenedited\": \"API Token sikeresen szerkesztve\",\n\t\"notification.error.tokennamenotunique\": \"Név már hozzárendelve egy másik tokenhez\",\n\t\"notification.version.update.message\": \"Megjelent a Strapi új verziója!\",\n\t\"notification.warning.title\": \"Figyelmeztetés:\",\n\t\"notification.warning.404\": \"404 - Nem található\",\n\tor: or,\n\t\"request.error.model.unknown\": \"Ez a modell nem létezik\",\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, hu as default, light, or, skipToContent, submit };\n//# sourceMappingURL=hu-DaDd6FHR.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,kDAAkD;AAAA,EAClD,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,gDAAgD;AAAA,EAChD,yCAAyC;AAAA,EACzC,oDAAoD;AAAA,EACpD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,2CAA2C;AAAA,EAC3C,0DAA0D;AAAA,EAC1D,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,0DAA0D;AAAA,EAC1D,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACD;", "names": []}