import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/zh-Hans-BHilK-yc.mjs
var zhHans = {
  "BoundRoute.title": "绑定路由到",
  "EditForm.inputSelect.description.role": "新验证身份的用户将被赋予所选角色。",
  "EditForm.inputSelect.label.role": "认证用户的默认角色",
  "EditForm.inputToggle.description.email": "不允许用户使用不同的认证提供商但相同的电子邮件地址来创建多个账户。",
  "EditForm.inputToggle.description.email-confirmation": "启用（开）后，新注册的用户会收到一封确认电子邮件。",
  "EditForm.inputToggle.description.email-confirmation-redirection": "确认您的电子邮件后，选择将您重定向到的位置。",
  "EditForm.inputToggle.description.email-reset-password": "应用程序的重置密码页面的网址",
  "EditForm.inputToggle.description.sign-up": "当禁用（关）时，注册过程将被禁止。任何人无论使用任何的供应商都不可以订阅。",
  "EditForm.inputToggle.label.email": "每个电子邮件地址对应一个账户",
  "EditForm.inputToggle.label.email-confirmation": "启用电子邮件确认",
  "EditForm.inputToggle.label.email-confirmation-redirection": "重定向网址",
  "EditForm.inputToggle.label.email-reset-password": "重置密码页面网址",
  "EditForm.inputToggle.label.sign-up": "启用注册",
  "EditForm.inputToggle.placeholder.email-confirmation-redirection": "例如: https://yourfrontend.com/email-confirmation-redirection",
  "EditForm.inputToggle.placeholder.email-reset-password": "例如: https://yourfrontend.com/reset-password",
  "EditPage.form.roles": "角色详情",
  "Email.template.data.loaded": "电子邮件模板已加载",
  "Email.template.email_confirmation": "邮箱地址确认",
  "Email.template.form.edit.label": "编辑模板",
  "Email.template.table.action.label": "操作",
  "Email.template.table.icon.label": "图标",
  "Email.template.table.name.label": "名称",
  "Form.advancedSettings.data.loaded": "高级设置数据已加载",
  "HeaderNav.link.advancedSettings": "高级设置",
  "HeaderNav.link.emailTemplates": "电子邮件模板",
  "HeaderNav.link.providers": "提供商",
  "Plugin.permissions.plugins.description": "定义 {name} 插件所有允许的操作。",
  "Plugins.header.description": "下面只列出路由绑定的操作。",
  "Plugins.header.title": "权限",
  "Policies.header.hint": "选择应用程序或插件对应的操作，然后点击齿轮图标显示绑定的路由",
  "Policies.header.title": "高级设置",
  "PopUpForm.Email.email_templates.inputDescription": "如果你不确定如何使用变量， {link}",
  "PopUpForm.Email.link.documentation": "查看我们的文档",
  "PopUpForm.Email.options.from.email.label": "发件人地址",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "发件人名称",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "消息",
  "PopUpForm.Email.options.object.label": "主题",
  "PopUpForm.Email.options.object.placeholder": "请为%APP_NAME%确认邮箱地址",
  "PopUpForm.Email.options.response_email.label": "回复邮件",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "如果禁用，用户将无法使用此供应商。",
  "PopUpForm.Providers.enabled.label": "启用",
  "PopUpForm.Providers.key.label": "客户端 ID",
  "PopUpForm.Providers.key.placeholder": "文本",
  "PopUpForm.Providers.redirectURL.front-end.label": "重定向网址",
  "PopUpForm.Providers.redirectURL.label": "添加到{provider}应用配置的跳转网址",
  "PopUpForm.Providers.secret.label": "客户端秘钥",
  "PopUpForm.Providers.secret.placeholder": "文本",
  "PopUpForm.Providers.subdomain.label": "主机URI（子域名）",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "编辑电子邮件模版",
  "PopUpForm.header.edit.providers": "编辑提供商",
  "Providers.data.loaded": "提供商已加载",
  "Providers.status": "状态",
  "Roles.empty": "您还没有任何角色。",
  "Roles.empty.search": "没有与搜索相匹配的角色。",
  "Settings.roles.deleted": "角色已被删除",
  "Settings.roles.edited": "角色编辑完成",
  "Settings.section-label": "用户及权限插件",
  "components.Input.error.validation.email": "这是一个无效的电子邮件",
  "components.Input.error.validation.json": "这不符合JSON格式",
  "components.Input.error.validation.max": "值过高。",
  "components.Input.error.validation.maxLength": "值过长。",
  "components.Input.error.validation.min": "值太低。",
  "components.Input.error.validation.minLength": "值太短。",
  "components.Input.error.validation.minSupMax": "不能超过上限",
  "components.Input.error.validation.regex": "该值不符合正则表达式。",
  "components.Input.error.validation.required": "该值为必填项。",
  "components.Input.error.validation.unique": "该值已被使用。",
  "notification.success.submit": "设置已被更新",
  "page.title": "设置 - 角色",
  "plugin.description.long": "使用基于 JWT 的完整身份验证过程来保护 API。这个插件还有一个 ACL 策略，允许你管理用户组之间的权限。",
  "plugin.description.short": "使用基于 JWT 的完整身份验证过程保护 API",
  "plugin.name": "用户及权限插件",
  "popUpWarning.button.cancel": "取消",
  "popUpWarning.button.confirm": "确认",
  "popUpWarning.title": "请确认",
  "popUpWarning.warning.cancel": "你确定你要取消你的修改？"
};
export {
  zhHans as default
};
//# sourceMappingURL=zh-Hans-BHilK-yc-JYC6IFUW.js.map
