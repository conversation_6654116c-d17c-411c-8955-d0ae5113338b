import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/ru-C34i9wJx.mjs
var Analytics = "Аналитика";
var Documentation = "Документация";
var Email = "Email";
var Password = "Пароль";
var Provider = "Провайдер";
var ResetPasswordToken = "Сброс токена пароля";
var Role = "Роль";
var Username = "Имя пользователя";
var Users = "Пользователи";
var anErrorOccurred = "Упс! Что-то пошло не так. Пожалуйста, попробуйте ещё раз.";
var clearLabel = "Очистить";
var dark = "Тёмная";
var light = "Светлая";
var or = "ИЛИ";
var selectButtonTitle = "Выбрать";
var skipToContent = "Перейти к содержимому";
var submit = "Отправить";
var ru = {
  Analytics,
  "Auth.components.Oops.text": "Ваш аккаунт был заморожен.",
  "Auth.components.Oops.text.admin": "Если это ошибка, пожалуйста, обратитесь к администратору.",
  "Auth.components.Oops.title": "Упс...",
  "Auth.form.active.label": "Активно",
  "Auth.form.button.forgot-password": "Отправить письмо",
  "Auth.form.button.go-home": "ВЕРНУТЬСЯ НА ГЛАВНУЮ",
  "Auth.form.button.login": "Войти",
  "Auth.form.button.login.providers.error": "Мы не можем подключить вас через выбранного провайдера.",
  "Auth.form.button.login.strapi": "Войти через Strapi",
  "Auth.form.button.password-recovery": "Восстановление пароля",
  "Auth.form.button.register": "Давайте начнём",
  "Auth.form.confirmPassword.label": "Подтверждение пароля",
  "Auth.form.currentPassword.label": "Текущий пароль",
  "Auth.form.email.label": "Email",
  "Auth.form.email.placeholder": "<EMAIL>",
  "Auth.form.error.blocked": "Ваш аккаунт был заблокирован администратором.",
  "Auth.form.error.code.provide": "Указан неверный код.",
  "Auth.form.error.confirmed": "Ваш email не подтверждён.",
  "Auth.form.error.email.invalid": "Неправильный email.",
  "Auth.form.error.email.provide": "Укажите своё имя пользователя или email.",
  "Auth.form.error.email.taken": "Данный email уже используется.",
  "Auth.form.error.invalid": "Неверный логин или пароль.",
  "Auth.form.error.params.provide": "Предоставлены неверные параметры.",
  "Auth.form.error.password.format": "Пароль не может содержать символ `$` больше трёх раз.",
  "Auth.form.error.password.local": "Этот пользователь никогда не задавал пароль, пожалуйста, войдите в систему через провайдера, используемого при создании учётной записи.",
  "Auth.form.error.password.matching": "Пароли не совпадают.",
  "Auth.form.error.password.provide": "Укажите свой пароль.",
  "Auth.form.error.ratelimit": "Слишком много попыток, повторите через минуту.",
  "Auth.form.error.user.not-exist": "Этот email не существует.",
  "Auth.form.error.username.taken": "Имя пользователя уже используется.",
  "Auth.form.firstname.label": "Имя",
  "Auth.form.firstname.placeholder": "Иван",
  "Auth.form.forgot-password.email.label": "Введите ваш email",
  "Auth.form.forgot-password.email.label.success": "Письмо успешно отправлено на email",
  "Auth.form.lastname.label": "Фамилия",
  "Auth.form.lastname.placeholder": "Иванов",
  "Auth.form.password.hide-password": "Скрыть пароль",
  "Auth.form.password.hint": "Должно быть не менее 8 символов, минимум 1 прописная буква, 1 строчная буква и 1 цифра",
  "Auth.form.password.show-password": "Показать пароль",
  "Auth.form.register.news.label": "Хочу быть в курсе новых функций и предстоящих улучшений (делая это, вы принимаете {terms} и {policy}).",
  "Auth.form.register.subtitle": "Учётные данные используются только для аутентификации в Strapi. Все сохраненные данные будут храниться в вашей базе данных.",
  "Auth.form.rememberMe.label": "Запомнить меня",
  "Auth.form.username.label": "Имя пользователя",
  "Auth.form.username.placeholder": "Ivan_Ivanov",
  "Auth.form.welcome.subtitle": "Войдите в свою учётную запись Strapi",
  "Auth.form.welcome.title": "Добро пожаловать в Strapi!",
  "Auth.link.forgot-password": "Забыли пароль?",
  "Auth.link.ready": "Готовы войти?",
  "Auth.link.signin": "Войти",
  "Auth.link.signin.account": "Уже есть аккаунт?",
  "Auth.login.sso.divider": "Или войдите в систему с помощью",
  "Auth.login.sso.loading": "Загрузка провайдеров...",
  "Auth.login.sso.subtitle": "Войдите в свою учётную запись через SSO",
  "Auth.privacy-policy-agreement.policy": "Политику конфиденциальности",
  "Auth.privacy-policy-agreement.terms": "Условия использования",
  "Auth.reset-password.title": "Сброс пароля",
  "Content Manager": "Редактор контента",
  "Content Type Builder": "Конструктор типов содержимого",
  Documentation,
  Email,
  "Files Upload": "Загрузка файлов",
  "HomePage.head.title": "Домашняя страница",
  "HomePage.roadmap": "Смотрите нашу дорожную карту",
  "HomePage.welcome.congrats": "Поздравляем!",
  "HomePage.welcome.congrats.content": "Вы вошли как первый администратор. Чтобы открыть для себя мощные функции, предоставляемые Strapi,",
  "HomePage.welcome.congrats.content.bold": "мы рекомендуем вам создать свой первый тип коллекции.",
  "Media Library": "Библиотека медиа",
  "New entry": "Новая запись",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "Роли и Разрешения",
  "Roles.ListPage.notification.delete-all-not-allowed": "Некоторые роли нельзя было удалить, так как они связаны с пользователями",
  "Roles.ListPage.notification.delete-not-allowed": "Невозможно удалить роль, если она связана с пользователями",
  "Roles.RoleRow.select-all": "Выберите {name} для групповых действий",
  "Roles.RoleRow.user-count": "{number, plural, =0{# пользователей} one{# пользователь} few{# пользователя} many {# пользователей}}",
  "Roles.components.List.empty.withSearch": "Нет роли, соответствующей поиску ({search})...",
  "Settings.PageTitle": "Настройки — {name}",
  "Settings.apiTokens.ListView.headers.createdAt": "Cоздан в",
  "Settings.apiTokens.ListView.headers.description": "Описание",
  "Settings.apiTokens.ListView.headers.lastUsedAt": "Последнее использование",
  "Settings.apiTokens.ListView.headers.name": "Название",
  "Settings.apiTokens.ListView.headers.type": "Тип токена",
  "Settings.apiTokens.addFirstToken": "Добавьте свой первый API-токен",
  "Settings.apiTokens.addNewToken": "Добавить новый API-токен",
  "Settings.apiTokens.create": "Создайте новый API-токен",
  "Settings.apiTokens.createPage.BoundRoute.title": "Связанный маршрут к",
  "Settings.apiTokens.createPage.permissions.description": "Ниже перечислены только действия связанные с маршрутом.",
  "Settings.apiTokens.createPage.permissions.header.hint": "Выберите действия для приложения или плагина и нажмите на значок шестеренки, чтобы отобразить связанный маршрут",
  "Settings.apiTokens.createPage.permissions.header.title": "Дополнительные настройки",
  "Settings.apiTokens.createPage.permissions.title": "Разрешения",
  "Settings.apiTokens.createPage.title": "Создать API-токен",
  "Settings.apiTokens.description": "Список сгенерированных токенов для использования API",
  "Settings.apiTokens.emptyStateLayout": "У вас ещё нет контента...",
  "Settings.apiTokens.lastHour": "последний час",
  "Settings.apiTokens.regenerate": "Перегенерировать",
  "Settings.apiTokens.title": "API-токены",
  "Settings.application.customization": "Персонализация",
  "Settings.application.customization.auth-logo.carousel-hint": "Заменить логотип на страницах аутентификации",
  "Settings.application.customization.carousel-hint": "Изменить логотип панели администратора (максимальный размер: {dimension}x{dimension}, максимальный размер файла: {size}KB)",
  "Settings.application.customization.carousel-slide.label": "Слайд с логотипом",
  "Settings.application.customization.carousel.auth-logo.title": "Логотип на странице авторизации",
  "Settings.application.customization.carousel.change-action": "Изменить логотип",
  "Settings.application.customization.carousel.menu-logo.title": "Логотип в меню",
  "Settings.application.customization.carousel.reset-action": "Сброс логотипа",
  "Settings.application.customization.carousel.title": "Логотип",
  "Settings.application.customization.menu-logo.carousel-hint": "Замените логотип в главном меню",
  "Settings.application.customization.modal.cancel": "Отмена",
  "Settings.application.customization.modal.pending": "Ожидание логотипа",
  "Settings.application.customization.modal.pending.card-badge": "изображение",
  "Settings.application.customization.modal.pending.choose-another": "Выберите другой логотип",
  "Settings.application.customization.modal.pending.subtitle": "Управление выбранным логотипом перед его загрузкой",
  "Settings.application.customization.modal.pending.title": "Логотип готов к загрузке",
  "Settings.application.customization.modal.pending.upload": "Загрузить логотип",
  "Settings.application.customization.modal.tab.label": "Как вы хотите загрузить свои ассеты?",
  "Settings.application.customization.modal.upload": "Загрузить логотип",
  "Settings.application.customization.modal.upload.cta.browse": "Просмотр файлов",
  "Settings.application.customization.modal.upload.drag-drop": "Перетащите сюда или",
  "Settings.application.customization.modal.upload.error-format": "Загружен неправильный формат (разрешены только форматы: jpeg, jpg, png, svg).",
  "Settings.application.customization.modal.upload.error-network": "Ошибка сети",
  "Settings.application.customization.modal.upload.error-size": "Загруженный файл слишком большой (максимальный размер: {dimension}x{dimension}, максимальный размер файла: {size}KB)",
  "Settings.application.customization.modal.upload.file-validation": "Максимальный размер: {dimension}x{dimension}, максимальный размер файла: {size}KB",
  "Settings.application.customization.modal.upload.from-computer": "С компьютера",
  "Settings.application.customization.modal.upload.from-url": "По ссылке",
  "Settings.application.customization.modal.upload.from-url.input-label": "URL",
  "Settings.application.customization.modal.upload.next": "Далее",
  "Settings.application.customization.size-details": "Максимальное разрешение: {dimension}×{dimension}, максимальный размер файла: {size}KB",
  "Settings.application.description": "Глобальная информация панели администратора",
  "Settings.application.edition-title": "Текущий план",
  "Settings.application.ee-or-ce": "{communityEdition, select, true {Community Edition} other {Enterprise Edition}}",
  "Settings.application.ee.admin-seats.add-seats": "{isHostedOnStrapiCloud, select, true {Добавить места} other {Обратитесь в отдел продаж}}",
  "Settings.application.ee.admin-seats.at-limit-tooltip": "При исчерпании лимита: добавьте места, чтобы пригласить больше пользователей",
  "Settings.application.ee.admin-seats.count": "<text>{enforcementUserCount}</text>/{permittedSeats}",
  "Settings.application.get-help": "Получить помощь",
  "Settings.application.link-pricing": "Посмотреть все тарифы",
  "Settings.application.link-upgrade": "Обновить ваше приложение",
  "Settings.application.node-version": "Версия Node",
  "Settings.application.strapi-version": "Версия Strapi",
  "Settings.application.strapiVersion": "Версия Strapi",
  "Settings.application.title": "Обзор",
  "Settings.error": "Ошибка",
  "Settings.global": "Глобальные Настройки",
  "Settings.permissions": "Панель администратора",
  "Settings.permissions.auditLogs.action": "Действие",
  "Settings.permissions.auditLogs.admin.auth.success": "Вход администратора",
  "Settings.permissions.auditLogs.admin.logout": "Выход администратора",
  "Settings.permissions.auditLogs.component.create": "Создать компонент",
  "Settings.permissions.auditLogs.component.delete": "Удалить компонент",
  "Settings.permissions.auditLogs.component.update": "Обновить компонент",
  "Settings.permissions.auditLogs.content-type.create": "Создать тип контента",
  "Settings.permissions.auditLogs.content-type.delete": "Удалить тип контента",
  "Settings.permissions.auditLogs.content-type.update": "Обновить тип контента",
  "Settings.permissions.auditLogs.date": "Дата",
  "Settings.permissions.auditLogs.details": "Детали лога",
  "Settings.permissions.auditLogs.entry.create": "Создать {model, select, undefined {} other { ({model})}}",
  "Settings.permissions.auditLogs.entry.delete": "Удалить {model, select, undefined {} other { ({model})}}",
  "Settings.permissions.auditLogs.entry.publish": "Опубликовать {model, select, undefined {} other {({model})}}",
  "Settings.permissions.auditLogs.entry.unpublish": "Сделать непубличным {model, select, undefined {} other { ({model})}}",
  "Settings.permissions.auditLogs.entry.update": "Обновить {model, select, undefined {} other { ({model})}}",
  "Settings.permissions.auditLogs.filters.combobox.aria-label": "Выполните поиск и выберите вариант для фильтрации",
  "Settings.permissions.auditLogs.listview.header.subtitle": "Журналы всех действий, которые произошли в вашей среде",
  "Settings.permissions.auditLogs.media.create": "Создать медиафайл",
  "Settings.permissions.auditLogs.media.delete": "Удалить медиафайл",
  "Settings.permissions.auditLogs.media.update": "Обновить медиафайл",
  "Settings.permissions.auditLogs.payload": "Полезная нагрузка",
  "Settings.permissions.auditLogs.permission.create": "Создать разрешение",
  "Settings.permissions.auditLogs.permission.delete": "Удалить разрешение",
  "Settings.permissions.auditLogs.permission.update": "Обновить разрешение",
  "Settings.permissions.auditLogs.role.create": "Создать роль",
  "Settings.permissions.auditLogs.role.delete": "Удалить роль",
  "Settings.permissions.auditLogs.role.update": "Обновить роль",
  "Settings.permissions.auditLogs.user": "Пользователь",
  "Settings.permissions.auditLogs.user.create": "Создать пользователя",
  "Settings.permissions.auditLogs.user.delete": "Удалить пользователя",
  "Settings.permissions.auditLogs.user.fullname": "{firstname} {lastname}",
  "Settings.permissions.auditLogs.user.update": "Обновить пользователя",
  "Settings.permissions.auditLogs.userId": "ID пользователя",
  "Settings.permissions.category": "Настройки разрешений для {category}",
  "Settings.permissions.category.plugins": "Настройки разрешений для плагина {category}",
  "Settings.permissions.conditions.anytime": "Всегда",
  "Settings.permissions.conditions.apply": "Применить",
  "Settings.permissions.conditions.can": "Можно",
  "Settings.permissions.conditions.conditions": "Условия",
  "Settings.permissions.conditions.define-conditions": "Определить условия",
  "Settings.permissions.conditions.links": "Ссылки",
  "Settings.permissions.conditions.no-actions": "Сначала вам нужно выбрать действия (создать, прочитать, обновить, ...), прежде чем определять условия для них.",
  "Settings.permissions.conditions.none-selected": "Любое время",
  "Settings.permissions.conditions.or": "ИЛИ",
  "Settings.permissions.conditions.when": "Когда",
  "Settings.permissions.select-all-by-permission": "Выбрать все разрешения {label}",
  "Settings.permissions.select-by-permission": "Выберите разрешения {label}",
  "Settings.permissions.users.active": "Активен",
  "Settings.permissions.users.create": "Создать нового пользователя",
  "Settings.permissions.users.email": "Email",
  "Settings.permissions.users.firstname": "Имя",
  "Settings.permissions.users.form.sso": "Соединить с SSO",
  "Settings.permissions.users.form.sso.description": "Когда включено, пользователи смогут входить через SSO",
  "Settings.permissions.users.inactive": "Неактивен",
  "Settings.permissions.users.lastname": "Фамилия",
  "Settings.permissions.users.listview.header.subtitle": "Все пользователи, имеющие доступ к панели администратора Strapi",
  "Settings.permissions.users.roles": "Роли",
  "Settings.permissions.users.sso.provider.error": "Произошла ошибка при запросе настроек единого входа (SSO)",
  "Settings.permissions.users.strapi-author": "Автор",
  "Settings.permissions.users.strapi-editor": "Редактор",
  "Settings.permissions.users.strapi-super-admin": "Супер-администратор",
  "Settings.permissions.users.tabs.label": "Вкладки Разрешения",
  "Settings.permissions.users.user-status": "Статус пользователя",
  "Settings.permissions.users.username": "Имя пользователя",
  "Settings.profile.form.notify.data.loaded": "Данные вашего профиля загружены",
  "Settings.profile.form.section.experience.clear.select": "Очистить выбранный язык интерфейса",
  "Settings.profile.form.section.experience.here": "здесь",
  "Settings.profile.form.section.experience.interfaceLanguage": "Язык интерфейса",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "Только ваш интерфейс будет отображаться на выбранном языке.",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "Изменения предпочтений будут касаться только вас. Дополнительная информация доступна {here}.",
  "Settings.profile.form.section.experience.mode.hint": "Отображает ваш интерфейс в выбранной теме.",
  "Settings.profile.form.section.experience.mode.label": "Тема интерфейса",
  "Settings.profile.form.section.experience.mode.option-label": "{name} тема",
  "Settings.profile.form.section.experience.title": "Опыт",
  "Settings.profile.form.section.head.title": "Профиль пользователя",
  "Settings.profile.form.section.profile.page.title": "Страница профиля",
  "Settings.roles.create.description": "Определите права, предоставленные ролью",
  "Settings.roles.create.title": "Создать роль",
  "Settings.roles.created": "Роль создана",
  "Settings.roles.edit.title": "Изменить роль",
  "Settings.roles.form.button.users-with-role": "Пользователи с этой ролью",
  "Settings.roles.form.created": "Создано",
  "Settings.roles.form.description": "Название и описание роли",
  "Settings.roles.form.permission.property-label": "{label} разрешения",
  "Settings.roles.form.permissions.attributesPermissions": "Разрешения полей",
  "Settings.roles.form.permissions.create": "Создать",
  "Settings.roles.form.permissions.delete": "Удалить",
  "Settings.roles.form.permissions.publish": "Опубликовать",
  "Settings.roles.form.permissions.read": "Прочесть",
  "Settings.roles.form.permissions.update": "Обновить",
  "Settings.roles.list.button.add": "Добавить новую роль",
  "Settings.roles.list.description": "Список ролей",
  "Settings.roles.title.singular": "роль",
  "Settings.sso.description": "Настройте параметры для функции единого входа.",
  "Settings.sso.form.defaultRole.description": "Присоединит нового аутентифицированного пользователя к выбранной роли",
  "Settings.sso.form.defaultRole.description-not-allowed": "У вас должно быть разрешение на чтение ролей администратора",
  "Settings.sso.form.defaultRole.label": "Роль по умолчанию",
  "Settings.sso.form.localAuthenticationLock.description": "Выберите роли, для которых вы хотите отключить локальную проверку аутентификацию",
  "Settings.sso.form.localAuthenticationLock.label": "Блокировать локальную аутентификацию",
  "Settings.sso.form.registration.description": "Создать нового пользователя при входе через SSO, если учётной записи нет",
  "Settings.sso.form.registration.label": "Авто-регистрация",
  "Settings.sso.title": "Функция единого входа",
  "Settings.tokens.Button.cancel": "Отмена",
  "Settings.tokens.Button.regenerate": "Восстановить",
  "Settings.tokens.ListView.headers.createdAt": "Создан",
  "Settings.tokens.ListView.headers.description": "Описание",
  "Settings.tokens.ListView.headers.lastUsedAt": "Последний раз использовался",
  "Settings.tokens.ListView.headers.name": "Название",
  "Settings.tokens.RegenerateDialog.title": "Восстановить токен",
  "Settings.tokens.copy.editMessage": "Из соображений безопасности вы можете увидеть свой токен только один раз.",
  "Settings.tokens.copy.editTitle": "Этот токен больше не доступен.",
  "Settings.tokens.copy.lastWarning": "Обязательно скопируйте этот токен, вы больше не сможете его увидеть!",
  "Settings.tokens.duration.30-days": "30 дней",
  "Settings.tokens.duration.7-days": "7 дней",
  "Settings.tokens.duration.90-days": "90 дней",
  "Settings.tokens.duration.expiration-date": "Срок действия",
  "Settings.tokens.duration.unlimited": "Неограниченный",
  "Settings.tokens.form.description": "Описание",
  "Settings.tokens.form.duration": "Срок действия токена",
  "Settings.tokens.form.name": "Название",
  "Settings.tokens.form.type": "Тип токена",
  "Settings.tokens.notification.copied": "Токен скопирован в буфер обмена.",
  "Settings.tokens.popUpWarning.message": "Вы уверены, что хотите восстановить этот токен?",
  "Settings.tokens.regenerate": "Перегенерировать",
  "Settings.tokens.types.custom": "Пользовательский тип",
  "Settings.tokens.types.full-access": "Полный доступ",
  "Settings.tokens.types.read-only": "Только для чтения",
  "Settings.transferTokens.ListView.headers.type": "Тип токена",
  "Settings.transferTokens.addFirstToken": "Добавьте свой первый токен для передачи",
  "Settings.transferTokens.addNewToken": "Добавить новый токен для передачи",
  "Settings.transferTokens.create": "Создать новый токен для передачи",
  "Settings.transferTokens.createPage.title": "Создать передачу токена",
  "Settings.transferTokens.description": "Список сгенерированных токенов для передачи",
  "Settings.transferTokens.emptyStateLayout": "У вас ещё нет никакого контента...",
  "Settings.transferTokens.title": "Передача токенов",
  "Settings.webhooks.create": "Создание webhook`а",
  "Settings.webhooks.create.header": "Создание нового заголовка",
  "Settings.webhooks.created": "Webhook создан",
  "Settings.webhooks.event.publish-tooltip": "Это событие существует только для содержимого с включенной системой Черновиков/Публикаций",
  "Settings.webhooks.event.select": "Выберите событие",
  "Settings.webhooks.events.create": "Создание",
  "Settings.webhooks.events.delete": "Удалить вебхук",
  "Settings.webhooks.events.isLoading": "Загрузка событий",
  "Settings.webhooks.events.update": "Обновление",
  "Settings.webhooks.form.events": "События",
  "Settings.webhooks.form.headers": "Заголовки",
  "Settings.webhooks.form.url": "URL",
  "Settings.webhooks.headers.remove": "Удалить строку заголовка {number}",
  "Settings.webhooks.key": "Ключ",
  "Settings.webhooks.list.button.add": "Добавить новый webhook",
  "Settings.webhooks.list.description": "Уведомления с помощью POST событий",
  "Settings.webhooks.list.empty.description": "Добавить первый в этот список",
  "Settings.webhooks.list.empty.link": "Просмотреть документацию",
  "Settings.webhooks.list.empty.title": "Пока ещё нет ни одного webhook'а",
  "Settings.webhooks.list.loading.success": "Вебхуки были загружены",
  "Settings.webhooks.list.th.actions": "действия",
  "Settings.webhooks.list.th.status": "статус",
  "Settings.webhooks.singular": "webhook",
  "Settings.webhooks.title": "Webhook'и",
  "Settings.webhooks.to.delete": "{webhooksToDeleteLength, plural, =0{# ассетов} one{# ассет} few{# ассета} many {# ассетов}} выбраны",
  "Settings.webhooks.trigger": "Триггер",
  "Settings.webhooks.trigger.cancel": "Отмена триггера",
  "Settings.webhooks.trigger.pending": "Ожидание…",
  "Settings.webhooks.trigger.save": "Пожалуйста сохраните триггер",
  "Settings.webhooks.trigger.success": "Успех!",
  "Settings.webhooks.trigger.success.label": "Триггер выполнен",
  "Settings.webhooks.trigger.test": "Тест триггер",
  "Settings.webhooks.trigger.title": "Сохранить перед триггером",
  "Settings.webhooks.validation.key": "Ключ обязателен",
  "Settings.webhooks.validation.name.regex": "Имя должно начинаться с буквы и содержать только буквы, цифры, пробелы и подчеркивания",
  "Settings.webhooks.validation.name.required": "Имя обязательно",
  "Settings.webhooks.validation.url.regex": "Значение должно быть допустимым URL-адресом",
  "Settings.webhooks.validation.url.required": "URL-адрес обязателен",
  "Settings.webhooks.validation.value": "Значение обязательно",
  "Settings.webhooks.value": "Значение",
  "Usecase.back-end": "Back-end разработчик",
  "Usecase.button.skip": "Пропустить этот вопрос",
  "Usecase.content-creator": "Создатель контента",
  "Usecase.front-end": "Front-end разработчик",
  "Usecase.full-stack": "Full-stack разработчик",
  "Usecase.input.work-type": "Какой тип работы вы выполняете?",
  "Usecase.notification.success.project-created": "Проект успешно создан",
  "Usecase.other": "Другое",
  "Usecase.title": "Расскажите нам немного больше о себе",
  Username,
  "Users & Permissions": "Пользователи и Разрешения",
  Users,
  "Users.components.List.empty": "Нет пользователей...",
  "Users.components.List.empty.withFilters": "Нет пользователей с применёнными фильтрами...",
  "Users.components.List.empty.withSearch": "Нет пользователей, соответствующих запросу ({search})...",
  "admin.pages.MarketPlacePage.filters.categories": "Категории",
  "admin.pages.MarketPlacePage.filters.categoriesSelected": "Выбрано {count, plural, =0{# категорий} one{# категория} few{# категории} many {# категорий}}",
  "admin.pages.MarketPlacePage.filters.collections": "Коллекции",
  "admin.pages.MarketPlacePage.filters.collectionsSelected": "Выбрано {count, plural, =0{# Коллекций} one{# Коллекция} few{# Коллекции} many {# Коллекций}}",
  "admin.pages.MarketPlacePage.head": "Маркет плагинов — Плагины",
  "admin.pages.MarketPlacePage.missingPlugin.description": "Расскажите нам, какой плагин вы ищете, и мы сообщим об этом разработчикам плагинов нашего сообщества на случай, если они ищут вдохновение!",
  "admin.pages.MarketPlacePage.missingPlugin.title": "Вам не хватает плагина?",
  "admin.pages.MarketPlacePage.offline.subtitle": "Для доступа к Маркету Strapi необходимо подключение к Интернету.",
  "admin.pages.MarketPlacePage.offline.title": "Вы не в сети",
  "admin.pages.MarketPlacePage.plugin.copy": "Скопируйте команду установки",
  "admin.pages.MarketPlacePage.plugin.copy.success": "Команда установки готова к вставке в терминал",
  "admin.pages.MarketPlacePage.plugin.downloads": "Этот плагин был загружен {downloadsCount} раз(-а) за неделю",
  "admin.pages.MarketPlacePage.plugin.githubStars": "Этот плагин имеет {starsCount} звёзд на GitHub",
  "admin.pages.MarketPlacePage.plugin.info": "Узнать больше",
  "admin.pages.MarketPlacePage.plugin.info.label": "Узнать больше про {pluginName}",
  "admin.pages.MarketPlacePage.plugin.info.text": "Узнать больше",
  "admin.pages.MarketPlacePage.plugin.installed": "Установлено",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "Сделано Strapi",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "Плагин проверен командой Strapi",
  "admin.pages.MarketPlacePage.plugin.version": 'Обновите вашу версию Strapi: "{strapiAppVersion}" до: "{versionRange}"',
  "admin.pages.MarketPlacePage.plugin.version.null": 'Невозможно проверить совместимость с вашей версией Strapi: "{strapiAppVersion}"',
  "admin.pages.MarketPlacePage.plugins": "Плагины",
  "admin.pages.MarketPlacePage.provider.downloads": "Этот провайдер был загружен {downloadsCount} раз(-а) за неделю",
  "admin.pages.MarketPlacePage.provider.githubStars": "Этот провайдер имеет {starsCount} звёзд на GitHub",
  "admin.pages.MarketPlacePage.providers": "Провайдеры",
  "admin.pages.MarketPlacePage.search.clear": "Очистить поиск",
  "admin.pages.MarketPlacePage.search.empty": 'Нет результатов для "{target}"',
  "admin.pages.MarketPlacePage.search.placeholder": "Найти плагин",
  "admin.pages.MarketPlacePage.sort.alphabetical": "По алфавиту",
  "admin.pages.MarketPlacePage.sort.alphabetical.selected": "Сортировать по алфавиту",
  "admin.pages.MarketPlacePage.sort.githubStars": "По звёздам на GitHub",
  "admin.pages.MarketPlacePage.sort.githubStars.selected": "Сортировать по звёздам на GitHub",
  "admin.pages.MarketPlacePage.sort.label": "Сортировка",
  "admin.pages.MarketPlacePage.sort.newest": "Новые",
  "admin.pages.MarketPlacePage.sort.newest.selected": "Сортировать по новейшим",
  "admin.pages.MarketPlacePage.sort.npmDownloads": "Количество загрузок",
  "admin.pages.MarketPlacePage.sort.npmDownloads.selected": "Сортировать по npm загрузкам",
  "admin.pages.MarketPlacePage.submit.plugin.link": "Отправить плагин",
  "admin.pages.MarketPlacePage.submit.provider.link": "Отправить провайдера",
  "admin.pages.MarketPlacePage.subtitle": "Получите больше от Strapi",
  "admin.pages.MarketPlacePage.tab-group.label": "Плагины и провайдеры для Strapi",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "Копировать в буфер обмена",
  "app.component.search.label": "Искать {target}",
  "app.component.table.duplicate": "Дубликат {target}",
  "app.component.table.edit": "Редактировать {target}",
  "app.component.table.read": "Читать {target}",
  "app.component.table.select.one-entry": "Выбрать {target}",
  "app.component.table.view": "{target} детали",
  "app.components.BlockLink.blog": "Блог",
  "app.components.BlockLink.blog.content": "Читайте последние новости о Strapi и экосистеме.",
  "app.components.BlockLink.cloud": "Strapi Cloud",
  "app.components.BlockLink.cloud.content": "Полностью настраиваемая платформа для совместной работы, повышающая скорость работы вашей команды.",
  "app.components.BlockLink.code": "Примеры кода",
  "app.components.BlockLink.code.content": "Учитесь, тестируя реальные проекты, разработанные сообществом.",
  "app.components.BlockLink.documentation.content": "Откройте для себя основные понятия, руководства и инструкции.",
  "app.components.BlockLink.tutorial": "Учебные материалы",
  "app.components.BlockLink.tutorial.content": "Следуйте пошаговым инструкциям по использованию и настройке Strapi.",
  "app.components.Button.cancel": "Отменить",
  "app.components.Button.confirm": "Подтвердить",
  "app.components.Button.reset": "Сброс",
  "app.components.ComingSoonPage.comingSoon": "Скоро",
  "app.components.ConfirmDialog.title": "Подтверждение",
  "app.components.DownloadInfo.download": "Выполняется загрузка...",
  "app.components.DownloadInfo.text": "Это может занять около минуты. Спасибо за ваше терпение.",
  "app.components.EmptyAttributes.title": "Пока нет полей",
  "app.components.EmptyStateLayout.content-document": "Данные не найдены",
  "app.components.EmptyStateLayout.content-permissions": "У вас нет прав доступа к этому содержимому",
  "app.components.GuidedTour.CM.create.content": "<p>Создавайте и управляйте всем содержимым здесь, в Редакторе контента.</p><p>Например, если взять пример с сайтом-блогом, можно написать статью, сохранить и опубликовать её по своему усмотрению.</p><p>💡 Краткий совет — не забудьте нажать опубликовать для публикации созданного вами контента.</p>",
  "app.components.GuidedTour.CM.create.title": "⚡️ Создавайте контент",
  "app.components.GuidedTour.CM.success.content": "<p>Потрясающе, остался последний шаг!</p><b>🚀 Посмотрите как в итоге выглядит контент</b>",
  "app.components.GuidedTour.CM.success.cta.title": "Протестируйте API",
  "app.components.GuidedTour.CM.success.title": "Шаг 2: Завершено ✅",
  "app.components.GuidedTour.CTB.create.content": '<p>Типы "Коллекция" помогают управлять несколькими записями, типы "Одиночная запись" подходят для управления только одной записью.</p> <p>Например: Для сайта-блога статьи будут типом "Коллекция", а домашняя страница — типом "Одиночная запись".</p>',
  "app.components.GuidedTour.CTB.create.cta.title": "Создайте тип записей Коллекция",
  "app.components.GuidedTour.CTB.create.title": "🧠 Создайте первый тип записей Коллекция",
  "app.components.GuidedTour.CTB.success.content": "<p>Так держать!</p><b>⚡️ Чем бы вы хотели поделиться с миром?</b>",
  "app.components.GuidedTour.CTB.success.title": "Шаг 1: Завершено ✅",
  "app.components.GuidedTour.apiTokens.create.content": "<p>Сгенерируйте здесь токен аутентификации и получите только что созданный контент.</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "Сгенерируйте API-токен",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 Посмотрите на контент в действии",
  "app.components.GuidedTour.apiTokens.success.content": "<p>Посмотрите содержимое в действии, сделав HTTP-запрос:</p><ul><li><p>По этому URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>С заголовком: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>О дополнительных способах взаимодействия с контентом смотрите в <documentationLink>документации</documentationLink>.</p>",
  "app.components.GuidedTour.apiTokens.success.cta.title": "Вернуться на главную страницу",
  "app.components.GuidedTour.apiTokens.success.title": "Шаг 3: Завершено ✅",
  "app.components.GuidedTour.create-content": "Создавайте контент",
  "app.components.GuidedTour.home.CM.title": "⚡️ Чем бы вы хотели поделиться с миром?",
  "app.components.GuidedTour.home.CTB.cta.title": "Перейти к Редактору типов контента",
  "app.components.GuidedTour.home.CTB.title": "🧠 Постройте структуру контента",
  "app.components.GuidedTour.home.apiTokens.cta.title": "Протестируйте API",
  "app.components.GuidedTour.skip": "Пропустить тур",
  "app.components.GuidedTour.title": "3 шага для начала работы",
  "app.components.HomePage.button.blog": "Смотрите больше в нашем блоге",
  "app.components.HomePage.community": "Присоединиться к нашему сообществу",
  "app.components.HomePage.community.content": "Участвуйте в обсуждениях с членами команды и разработчиками.",
  "app.components.HomePage.create": "Создайте свой первый тип контента",
  "app.components.HomePage.roadmap": "Ознакомьтесь с нашей дорожной картой",
  "app.components.HomePage.welcome": "Добро пожаловать на борт 👋",
  "app.components.HomePage.welcome.again": "Добро пожаловать 👋",
  "app.components.HomePage.welcomeBlock.content": "Поздравляем! Вы вошли в систему как первый администратор. Чтобы открыть для себя мощные функции, предоставляемые Strapi, мы рекомендуем вам создать свой первый тип контента!",
  "app.components.HomePage.welcomeBlock.content.again": "Надеемся, что вы делаете успехи в вашем проекте... Следите за последними новостями о Strapi. Мы стараемся изо всех сил, чтобы улучшить продукт, основываясь на ваших пожеланиях.",
  "app.components.HomePage.welcomeBlock.content.issues": "проблемах.",
  "app.components.HomePage.welcomeBlock.content.raise": " или сообщать о ",
  "app.components.ImgPreview.hint": "Перетащите файл в эту область или {browse} файл для загрузки",
  "app.components.ImgPreview.hint.browse": "выберите",
  "app.components.InputFile.newFile": "Добавить новый файл",
  "app.components.InputFileDetails.open": "Открыть в новой вкладке",
  "app.components.InputFileDetails.originalName": "Оригинальное название:",
  "app.components.InputFileDetails.remove": "Удалить этот файл",
  "app.components.InputFileDetails.size": "Размер:",
  "app.components.InstallPluginPage.Download.description": "Для загрузки и установки плагина может потребоваться несколько секунд.",
  "app.components.InstallPluginPage.Download.title": "Загрузка...",
  "app.components.InstallPluginPage.description": "Расширяйте ваше приложение без особых усилий.",
  "app.components.LeftMenu.collapse": "Свернуть панель навигации",
  "app.components.LeftMenu.expand": "Развернуть панель навигации",
  "app.components.LeftMenu.general": "Общее",
  "app.components.LeftMenu.logo.alt": "Логотип приложения",
  "app.components.LeftMenu.logout": "Выйти",
  "app.components.LeftMenu.navbrand.title": "Панель управления Strapi",
  "app.components.LeftMenu.navbrand.workplace": "Рабочая область",
  "app.components.LeftMenu.plugins": "Плагины",
  "app.components.LeftMenuFooter.help": "Помощь",
  "app.components.LeftMenuFooter.poweredBy": "Работает на ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "Типы Коллекций",
  "app.components.LeftMenuLinkContainer.configuration": "Настройки",
  "app.components.LeftMenuLinkContainer.general": "Общие",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "Нет установленных плагинов",
  "app.components.LeftMenuLinkContainer.plugins": "Плагины",
  "app.components.LeftMenuLinkContainer.singleTypes": "Страницы",
  "app.components.ListPluginsPage.deletePlugin.description": "Удаление плагина может занять несколько секунд.",
  "app.components.ListPluginsPage.deletePlugin.title": "Удаление",
  "app.components.ListPluginsPage.description": "Список установленных плагинов в проекте.",
  "app.components.ListPluginsPage.head.title": "Список плагинов",
  "app.components.Logout.logout": "Выйти",
  "app.components.Logout.profile": "Профиль",
  "app.components.MarketplaceBanner": "На Маркете Strapi вы найдете плагины, созданные сообществом, и множество других удивительных вещей для запуска вашего проекта.",
  "app.components.MarketplaceBanner.image.alt": "A Strapi rocket logo",
  "app.components.MarketplaceBanner.link": "Посмотреть",
  "app.components.NotFoundPage.back": "Вернуться на главную",
  "app.components.NotFoundPage.description": "Не найдено",
  "app.components.NpsSurvey.banner-title": "Насколько велика вероятность того, что вы порекомендуете Strapi другу или коллеге?",
  "app.components.NpsSurvey.dismiss-survey-label": "Отказаться от опроса",
  "app.components.NpsSurvey.feedback-question": "Есть ли у вас какие-либо предложения по улучшению?",
  "app.components.NpsSurvey.feedback-response": "Большое вам спасибо за ваш отзыв!",
  "app.components.NpsSurvey.happy-to-recommend": "Чрезвычайно вероятно",
  "app.components.NpsSurvey.no-recommendation": "Совсем маловероятно",
  "app.components.NpsSurvey.submit-feedback": "Отправить отзыв",
  "app.components.Official": "Официальный",
  "app.components.Onboarding.help.button": "Кнопка помощь",
  "app.components.Onboarding.label.completed": "% завершено",
  "app.components.Onboarding.link.build-content": "Создайте архитектуру контента",
  "app.components.Onboarding.link.manage-content": "Добавляйте и управляйте контентом",
  "app.components.Onboarding.link.manage-media": "Управляйте медиа",
  "app.components.Onboarding.link.more-videos": "Смотреть больше видео",
  "app.components.Onboarding.title": "Смотреть видео о начале работы",
  "app.components.PluginCard.Button.label.download": "Скачать",
  "app.components.PluginCard.Button.label.install": "Уже установлено",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "Функция autoReload (автоматической перезагрузки) должна быть включена. Пожалуйста, запустите ваше приложение с помощью `yarn develop`.",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "Я понимаю!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "В целях безопасности плагин может быть загружен только в среде разработки.",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "Загрузка невозможна",
  "app.components.PluginCard.compatible": "Совместимо с вашим приложением",
  "app.components.PluginCard.compatibleCommunity": "Совместимо с сообществом",
  "app.components.PluginCard.more-details": "Больше деталей",
  "app.components.ToggleCheckbox.off-label": "Нет",
  "app.components.ToggleCheckbox.on-label": "Да",
  "app.components.Users.MagicLink.connect": "Отправьте эту ссылку пользователю, чтобы предоставить ему доступ",
  "app.components.Users.MagicLink.connect.sso": "Отправьте эту ссылку пользователю, первый вход может быть осуществлен через провайдера SSO",
  "app.components.Users.ModalCreateBody.block-title.details": "Детали",
  "app.components.Users.ModalCreateBody.block-title.roles": "Роли пользователя",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "Пользователь может иметь одну или несколько ролей",
  "app.components.Users.SortPicker.button-label": "Сортировать по",
  "app.components.Users.SortPicker.sortby.email_asc": "Электронная почта (от А до Я)",
  "app.components.Users.SortPicker.sortby.email_desc": "Электронная почта (от Я до А)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "Имя (от А до Я)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "Имя (от Я до А)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "Фамилия (от А до Я)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "Фамилия (от Я до А)",
  "app.components.Users.SortPicker.sortby.username_asc": "Имя пользователя (от А до Я)",
  "app.components.Users.SortPicker.sortby.username_desc": "Имя пользователя (от Я до А)",
  "app.components.listPlugins.button": "Добавить новый плагин",
  "app.components.listPlugins.title.none": "Нет установленных плагинов",
  "app.components.listPluginsPage.deletePlugin.error": "Произошла ошибка при удалении плагина",
  "app.containers.App.notification.error.init": "Произошла ошибка при запросе к API",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "Если вы не получили эту ссылку, обратитесь к администратору.",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "Получение ссылки для восстановления пароля может занять несколько минут...",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "Письмо отправлено",
  "app.containers.Users.EditPage.form.active.label": "Активный",
  "app.containers.Users.EditPage.header.label": "Редактировать {name}",
  "app.containers.Users.EditPage.header.label-loading": "Редактировать пользователя",
  "app.containers.Users.EditPage.roles-bloc-title": "Атрибуты ролей",
  "app.containers.Users.ModalForm.footer.button-success": "Создать пользователя",
  "app.links.configure-view": "Настройка представления",
  "app.page.not.found": "Упс! Мы не можем найти страницу, которую вы ищете...",
  "app.static.links.cheatsheet": "Шпаргалка",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "Добавить фильтр",
  "app.utils.close-label": "Закрыть",
  "app.utils.defaultMessage": " ",
  "app.utils.delete": "Удалить",
  "app.utils.duplicate": "Дублировать",
  "app.utils.edit": "Редактировать",
  "app.utils.errors.file-too-big.message": "Файл слишком большой",
  "app.utils.filter-value": "Значение фильтра",
  "app.utils.filters": "Фильтры",
  "app.utils.notify.data-loaded": "{target} загружена",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "Опубликовать",
  "app.utils.published": "Опубликовано",
  "app.utils.ready-to-publish": "Готово к публикации",
  "app.utils.refresh": "Обновить",
  "app.utils.select-all": "Выбрать все",
  "app.utils.select-field": "Выберите поле",
  "app.utils.select-filter": "Выберите фильтр",
  "app.utils.unpublish": "Отменить публикацию",
  clearLabel,
  "coming.soon": "Содержимое находится в стадии разработки и через несколько недель оно будет возвращено!",
  "component.Input.error.validation.integer": "Значение должно быть целочисленным",
  "components.AutoReloadBlocker.description": "Запустите Strapi с помощью одной из следующих команд:",
  "components.AutoReloadBlocker.header": "Для этого плагина требуется функция перезагрузки.",
  "components.ErrorBoundary.title": "Что-то пошло не так...",
  "components.FilterOptions.FILTER_TYPES.$contains": "содержит",
  "components.FilterOptions.FILTER_TYPES.$containsi": "содержит (без учета регистра)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "заканчивается на",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "заканчивается на (без учета регистра)",
  "components.FilterOptions.FILTER_TYPES.$eq": "равно",
  "components.FilterOptions.FILTER_TYPES.$eqi": "равно (без учета регистра)",
  "components.FilterOptions.FILTER_TYPES.$gt": "больше, чем",
  "components.FilterOptions.FILTER_TYPES.$gte": "больше или равно",
  "components.FilterOptions.FILTER_TYPES.$lt": "меньше, чем",
  "components.FilterOptions.FILTER_TYPES.$lte": "меньше или равно",
  "components.FilterOptions.FILTER_TYPES.$ne": "не",
  "components.FilterOptions.FILTER_TYPES.$nei": "не (без учета регистра)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "не содержит",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "не содержит (без учета регистра)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "задано",
  "components.FilterOptions.FILTER_TYPES.$null": "не задано",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "начинается с",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "начинается с (без учета регистра)",
  "components.Input.error.attribute.key.taken": "Это значение уже существует",
  "components.Input.error.attribute.sameKeyAndName": "Не может быть одинаковым",
  "components.Input.error.attribute.taken": "Поле с таким названием уже существует",
  "components.Input.error.contain.lowercase": "Пароль должен содержать хотя бы одну прописную букву",
  "components.Input.error.contain.number": "Пароль должен содержать хотя бы одну цифру",
  "components.Input.error.contain.uppercase": "Пароль должен содержать хотя бы одну заглавную букву",
  "components.Input.error.contentTypeName.taken": "Это название уже существует",
  "components.Input.error.custom-error": "{errorMessage} ",
  "components.Input.error.password.noMatch": "Пароли не совпадают",
  "components.Input.error.validation.email": "Неправильный или написанный с ошибкой email",
  "components.Input.error.validation.email.withField": "Поле {field} — содержит неверный адрес электронной почты",
  "components.Input.error.validation.json": "Не соответствует JSON формату",
  "components.Input.error.validation.json.withField": "Поле {field} — не соответствует формату JSON",
  "components.Input.error.validation.lowercase": "Значение должно быть строкой в нижнем регистре",
  "components.Input.error.validation.lowercase.withField": "Поле {field} — должно быть строкой в нижнем регистре (без заглавных букв)",
  "components.Input.error.validation.max": "Значение слишком большое.",
  "components.Input.error.validation.max.withField": "Поле {field} — слишком велико.",
  "components.Input.error.validation.maxLength": "Значение слишком длинное.",
  "components.Input.error.validation.maxLength.withField": "Поле {field} — слишком длинное.",
  "components.Input.error.validation.min": "Значение слишком маленькое.",
  "components.Input.error.validation.min.withField": "Поле {field} — слишком мало.",
  "components.Input.error.validation.minLength": "Значение слишком короткое.",
  "components.Input.error.validation.minLength.withField": "Поле {field} — слишком короткое.",
  "components.Input.error.validation.minSupMax": "Не может быть больше",
  "components.Input.error.validation.minSupMax.withField": "Поле {field} — не может быть выше",
  "components.Input.error.validation.regex": "Значение не соответствует регулярному выражению.",
  "components.Input.error.validation.regex.withField": "Поле {field} — не соответствует формату регулярных выражений.",
  "components.Input.error.validation.required": "Обязательное значение.",
  "components.Input.error.validation.required.withField": "Поле {field} — обязательное.",
  "components.Input.error.validation.unique": "Это значение уже используется.",
  "components.Input.error.validation.unique.withField": "Поле {field} — уже используется.",
  "components.InputSelect.option.placeholder": "Выберите здесь",
  "components.ListRow.empty": "Нет данных для отображения.",
  "components.NotAllowedInput.text": "Нет прав на просмотр этого поля",
  "components.OverlayBlocker.description": "Вы используете функцию, для которой требуется перезагрузка сервера. Пожалуйста, подождите, пока сервер не перезагрузится.",
  "components.OverlayBlocker.description.serverError": "Сервер должен был перезагрузиться, пожалуйста, проверьте ваши логи в терминале.",
  "components.OverlayBlocker.title": "Ожидание перезагрузки...",
  "components.OverlayBlocker.title.serverError": "Перезагрузка занимает больше времени, чем ожидалось",
  "components.PageFooter.select": "записей на странице",
  "components.ProductionBlocker.description": "В целях безопасности мы должны отключить этот плагин в других средах.",
  "components.ProductionBlocker.header": "Этот плагин доступен только на стадии разработки.",
  "components.Search.placeholder": "Поиск...",
  "components.TableHeader.sort": "Сортировать по {label}",
  "components.ViewSettings.tooltip": "Настройки просмотра",
  "components.Wysiwyg.ToggleMode.markdown-mode": "Режим разметки",
  "components.Wysiwyg.ToggleMode.preview-mode": "Режим предпросмотра",
  "components.Wysiwyg.collapse": "Свернуть",
  "components.Wysiwyg.selectOptions.H1": "Заголовок H1",
  "components.Wysiwyg.selectOptions.H2": "Заголовок H2",
  "components.Wysiwyg.selectOptions.H3": "Заголовок H3",
  "components.Wysiwyg.selectOptions.H4": "Заголовок H4",
  "components.Wysiwyg.selectOptions.H5": "Заголовок H5",
  "components.Wysiwyg.selectOptions.H6": "Заголовок H6",
  "components.Wysiwyg.selectOptions.title": "Добавить заголовок",
  "components.WysiwygBottomControls.charactersIndicators": "символов",
  "components.WysiwygBottomControls.fullscreen": "Развернуть",
  "components.WysiwygBottomControls.uploadFiles": "Перетащите файлы в эту область, вставьте из буфера обмена или {browse}.",
  "components.WysiwygBottomControls.uploadFiles.browse": "выберите их",
  "components.pagination.go-to": "Перейти на страницу {page}",
  "components.pagination.go-to-next": "Перейти на следующую страницу",
  "components.pagination.go-to-previous": "Перейти на предыдущую страницу",
  "components.pagination.remaining-links": "И {number} других ссылок",
  "components.popUpWarning.button.cancel": "Нет, отменить",
  "components.popUpWarning.button.confirm": "Да, подтвердить",
  "components.popUpWarning.message": "Вы уверены, что хотите удалить это?",
  "components.popUpWarning.title": "Пожалуйста, подтвердите",
  dark,
  "form.button.continue": "Продолжить",
  "form.button.done": "Выполнено",
  "global.actions": "Действия",
  "global.auditLogs": "Аудит логов",
  "global.back": "Назад",
  "global.cancel": "Отмена",
  "global.change-password": "Сменить пароль",
  "global.close": "Закрыть",
  "global.content-manager": "Редактор контента",
  "global.continue": "Продолжить",
  "global.delete": "Удалить",
  "global.delete-target": "Удалить {target}",
  "global.description": "Описание",
  "global.details": "Подробности",
  "global.disabled": "Отключено",
  "global.documentation": "Документация",
  "global.enabled": "Включено",
  "global.finish": "Готово",
  "global.fullname": "{firstname} {lastname}",
  "global.marketplace": "Маркет",
  "global.name": "Имя",
  "global.none": "Нет",
  "global.password": "Пароль",
  "global.plugins": "Плагины",
  "global.plugins.content-manager": "Редактор контента",
  "global.plugins.content-manager.description": "Быстрый способ просмотра, редактирования и удаления данных в вашей базе данных.",
  "global.plugins.content-type-builder": "Конструктор типов содержимого",
  "global.plugins.content-type-builder.description": "Моделируйте структуру данных вашего API. Создавайте новые поля и отношения всего за минуту. Файлы автоматически создаются и обновляются в вашем проекте.",
  "global.plugins.documentation": "Документация",
  "global.plugins.documentation.description": "Создайте документ OpenAPI и визуализируйте свой API с помощью пользовательского интерфейса SWAGGER.",
  "global.plugins.email": "Email",
  "global.plugins.email.description": "Настройте своё приложение для отправки электронной почты.",
  "global.plugins.graphql": "GraphQL",
  "global.plugins.graphql.description": "Добавить конечную точку GraphQL с методами API по умолчанию.",
  "global.plugins.i18n": "Интернационализация",
  "global.plugins.i18n.description": "Этот плагин позволяет создавать, читать и обновлять контент на разных языках, как из панели администратора, так и с помощью API.",
  "global.plugins.sentry": "Sentry",
  "global.plugins.sentry.description": "Отправка событий об ошибках Strapi в Sentry.",
  "global.plugins.upload": "Библиотека медиа",
  "global.plugins.upload.description": "Управление медиафайлами.",
  "global.plugins.users-permissions": "Роли и Разрешения",
  "global.plugins.users-permissions.description": "Защитите свой API с помощью полного процесса аутентификации на основе JWT. Этот плагин также поставляется со стратегией ACL, которая позволяет вам управлять разрешениями между группами пользователей.",
  "global.profile": "Профиль",
  "global.prompt.unsaved": "Вы уверены, что хотите покинуть эту страницу? Все ваши модификации будут потеряны",
  "global.reset-password": "Сброс пароля",
  "global.roles": "Роли",
  "global.save": "Сохранить",
  "global.search": "Поиск",
  "global.see-more": "Подробнее",
  "global.select": "Выбрать",
  "global.select-all-entries": "Выбрать все записи",
  "global.settings": "Настройки",
  "global.type": "Тип",
  "global.users": "Пользователи",
  light,
  "notification.contentType.relations.conflict": "Тип контента имеет конфликтующие отношения",
  "notification.default.title": "Информация:",
  "notification.ee.warning.at-seat-limit.title": "{licenseLimitStatus, select, OVER_LIMIT {БОЛЬШЕ ЧЕМ} AT_LIMIT {РОВНО}} seat limit ({currentUserCount}/{permittedSeats})",
  "notification.ee.warning.over-.message": "Добавить места чтобы {licenseLimitStatus, select, OVER_LIMIT {пригласить} AT_LIMIT {повторно подкючить}} пользователей. Если вы уже сделали это, но это ещё не отображается в Strapi, обязательно перезапустите своё приложение.",
  "notification.error": "Произошла ошибка",
  "notification.error.invalid.configuration": "У вас неправильная конфигурация настроек, проверьте журнал вашего сервера для получения дополнительной информации.",
  "notification.error.layout": "Не удалось получить макет",
  "notification.error.tokennamenotunique": "Имя уже присвоено другому токену",
  "notification.form.error.fields": "Форма содержит некоторые ошибки",
  "notification.form.success.fields": "Изменения сохранены",
  "notification.link-copied": "Ссылка скопирована в буфер обмена",
  "notification.permission.not-allowed-read": "Вам не разрешено просматривать этот документ",
  "notification.success.apitokencreated": "API-токен успешно создан",
  "notification.success.apitokenedited": "API-токен успешно отредактирован",
  "notification.success.delete": "Элемент удален",
  "notification.success.saved": "Сохранено",
  "notification.success.title": "Успех:",
  "notification.success.transfertokencreated": "Токен для передачи успешно создан",
  "notification.success.transfertokenedited": "Токен для передачи успешно изменён",
  "notification.version.update.message": "Доступна новая версия Strapi!",
  "notification.warning.404": "404 — Не найдено",
  "notification.warning.title": "Внимание:",
  or,
  "request.error.model.unknown": "Модель данных не существует",
  selectButtonTitle,
  skipToContent,
  submit
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  dark,
  ru as default,
  light,
  or,
  selectButtonTitle,
  skipToContent,
  submit
};
//# sourceMappingURL=ru-C34i9wJx-RO66O6N3.js.map
