{"version": 3, "sources": ["../../../@strapi/upload/admin/src/utils/getBreadcrumbDataML.ts", "../../../@strapi/upload/admin/src/components/FolderCard/FolderCardCheckbox/FolderCardCheckbox.tsx", "../../../@strapi/upload/admin/src/pages/App/MediaLibrary/components/BulkDeleteButton.jsx", "../../../@strapi/upload/admin/src/hooks/useBulkMove.ts", "../../../@strapi/upload/admin/src/components/BulkMoveDialog/BulkMoveDialog.tsx", "../../../@strapi/upload/admin/src/pages/App/MediaLibrary/components/BulkMoveButton.jsx", "../../../@strapi/upload/admin/src/pages/App/MediaLibrary/components/BulkActions.jsx", "../../../@strapi/upload/admin/src/pages/App/MediaLibrary/components/EmptyOrNoPermissions.jsx", "../../../@strapi/upload/admin/src/pages/App/MediaLibrary/components/Filters.jsx", "../../../@strapi/upload/admin/src/pages/App/MediaLibrary/components/Header.jsx", "../../../@strapi/upload/admin/src/pages/App/MediaLibrary/index.jsx", "../../../@strapi/upload/admin/src/pages/App/index.jsx"], "sourcesContent": ["import { getFolderURL } from './getFolderURL';\nimport { getTrad } from './getTrad';\nimport type { Query } from '../../../shared/contracts/files';\nimport type { Folder } from '../../../shared/contracts/folders';\nimport type { MessageDescriptor } from 'react-intl';\n\ninterface GetBreadcrumbDataMLProps {\n  folder: Folder;\n  options: {\n    pathname: string;\n    query?: Query;\n  };\n}\n\ninterface GetBreadcrumbDataMLReturn {\n  id: number | null;\n  label: string | MessageDescriptor;\n  href?: string;\n}\n\ntype BreadcrumbData = GetBreadcrumbDataMLReturn | [];\n\nexport const getBreadcrumbDataML = (\n  folder: GetBreadcrumbDataMLProps['folder'] | null,\n  { pathname, query }: GetBreadcrumbDataMLProps['options']\n) => {\n  let data: BreadcrumbData[] = [\n    {\n      id: null,\n      label: { id: getTrad('plugin.name'), defaultMessage: 'Media Library' },\n      href: folder ? getFolderURL(pathname, query || {}) : undefined,\n    },\n  ];\n\n  if (folder?.parent && typeof folder?.parent !== 'number' && folder?.parent?.parent) {\n    data.push([]);\n  }\n\n  if (folder?.parent && typeof folder.parent !== 'number') {\n    data.push({\n      id: folder.parent.id,\n      label: folder.parent.name,\n      href: getFolderURL(pathname, query || {}, {\n        folder: folder.parent.id?.toString(),\n        folderPath: folder.parent.path,\n      }),\n    });\n  }\n\n  if (folder) {\n    data.push({\n      id: folder.id,\n      label: folder.name,\n    });\n  }\n\n  return data;\n};\n\nexport default getBreadcrumbDataML;\n", "import { Checkbox, Box, CheckboxProps } from '@strapi/design-system';\n\nimport { useFolderCard } from '../contexts/FolderCard';\n\nexport const FolderCardCheckbox = (props: CheckboxProps) => {\n  const { id } = useFolderCard();\n\n  return (\n    <Box position=\"relative\" zIndex={2}>\n      <Checkbox aria-labelledby={`${id}-title`} {...props} />\n    </Box>\n  );\n};\n", "import * as React from 'react';\n\nimport { ConfirmDialog } from '@strapi/admin/strapi-admin';\nimport { Button, Dialog } from '@strapi/design-system';\nimport { Trash } from '@strapi/icons';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport { AssetDefinition, FolderDefinition } from '../../../../constants';\nimport { useBulkRemove } from '../../../../hooks/useBulkRemove';\n\nexport const BulkDeleteButton = ({ selected, onSuccess }) => {\n  const { formatMessage } = useIntl();\n  const { remove } = useBulkRemove();\n\n  const handleConfirmRemove = async () => {\n    await remove(selected);\n    onSuccess();\n  };\n\n  return (\n    <Dialog.Root>\n      <Dialog.Trigger>\n        <Button variant=\"danger-light\" size=\"S\" startIcon={<Trash />}>\n          {formatMessage({ id: 'global.delete', defaultMessage: 'Delete' })}\n        </Button>\n      </Dialog.Trigger>\n      <ConfirmDialog onConfirm={handleConfirmRemove} />\n    </Dialog.Root>\n  );\n};\n\nBulkDeleteButton.propTypes = {\n  selected: PropTypes.arrayOf(AssetDefinition, FolderDefinition).isRequired,\n  onSuccess: PropTypes.func.isRequired,\n};\n", "import { useNotification, useFetchClient } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\nimport { useMutation, useQueryClient } from 'react-query';\nimport { File, BulkMoveFiles } from '../../../shared/contracts/files';\nimport { Folder, BulkMoveFolders } from '../../../shared/contracts/folders';\n\nimport pluginId from '../pluginId';\nimport { getTrad } from '../utils';\n\nexport interface FolderWithType extends Folder {\n  type: string;\n}\n\nexport interface FileWithType extends File {\n  type: string;\n}\n\ninterface BulkMoveParams {\n  destinationFolderId: number | string;\n  filesAndFolders: Array<FolderWithType | FileWithType>;\n}\n\n// Define the shape of the accumulator object\ntype Payload = {\n  fileIds?: number[];\n  folderIds?: number[];\n};\n\nexport const useBulkMove = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const queryClient = useQueryClient();\n  const { post } = useFetchClient();\n\n  const bulkMoveQuery = ({ destinationFolderId, filesAndFolders }: BulkMoveParams) => {\n    const payload = filesAndFolders.reduce<Payload>((acc, selected) => {\n      const { id, type } = selected;\n      const key = type === 'asset' ? 'fileIds' : 'folderIds';\n\n      if (!acc[key]) {\n        acc[key] = [];\n      }\n\n      acc[key]!.push(id);\n\n      return acc;\n    }, {});\n\n    return post('/upload/actions/bulk-move', { ...payload, destinationFolderId });\n  };\n\n  const mutation = useMutation<\n    BulkMoveFolders.Response | BulkMoveFiles.Response,\n    BulkMoveFolders.Response['error'] | BulkMoveFiles.Response['error'],\n    BulkMoveParams\n  >(bulkMoveQuery, {\n    onSuccess(res) {\n      const {\n        data: { data },\n      } = res;\n\n      if (data?.files?.length > 0) {\n        queryClient.refetchQueries([pluginId, 'assets'], { active: true });\n        queryClient.refetchQueries([pluginId, 'asset-count'], { active: true });\n      }\n\n      // folders need to be re-fetched in any case, because assets might have been\n      // moved into a sub-folder and therefore the count needs to be updated\n      queryClient.refetchQueries([pluginId, 'folders'], { active: true });\n\n      toggleNotification({\n        type: 'success',\n        message: formatMessage({\n          id: getTrad('modal.move.success-label'),\n          defaultMessage: 'Elements have been moved successfully.',\n        }),\n      });\n    },\n  });\n\n  const move = (\n    destinationFolderId: number | string,\n    filesAndFolders: Array<FolderWithType | FileWithType>\n  ) => mutation.mutateAsync({ destinationFolderId, filesAndFolders });\n\n  return { ...mutation, move };\n};\n", "import { Button, Flex, <PERSON>rid, <PERSON>, Loader, Modal, Typography } from '@strapi/design-system';\nimport { Form, Formik, FormikErrors } from 'formik';\nimport isEmpty from 'lodash/isEmpty';\nimport { useIntl } from 'react-intl';\n\nimport { useBulkMove } from '../../hooks/useBulkMove';\nimport { useFolderStructure } from '../../hooks/useFolderStructure';\nimport { getTrad, normalizeAPIError } from '../../utils';\nimport SelectTree from '../SelectTree';\nimport type { OptionSelectTree } from '../SelectTree/SelectTree';\nimport { File } from '../../../../shared/contracts/files';\nimport type { Folder } from '../../../../shared/contracts/folders';\nimport type { FetchError } from '@strapi/admin/strapi-admin';\n\ntype InitialFormData = {\n  destination:\n    | {\n        value: string | number;\n        label: string;\n      }\n    | string;\n};\n\ninterface FolderWithType extends Folder {\n  type: string;\n}\n\ninterface FileWithType extends File {\n  type: string;\n}\n\nexport interface BulkMoveDialogProps {\n  onClose: () => void;\n  selected?: Array<FolderWithType | FileWithType>;\n  currentFolder?: FolderWithType;\n}\n\nexport const BulkMoveDialog = ({ onClose, selected = [], currentFolder }: BulkMoveDialogProps) => {\n  const { formatMessage } = useIntl();\n  const { data: folderStructure, isLoading } = useFolderStructure();\n  const { move } = useBulkMove();\n\n  if (!folderStructure) {\n    return null;\n  }\n\n  const handleSubmit = async (\n    values: InitialFormData,\n    { setErrors }: { setErrors: (errors: FormikErrors<InitialFormData>) => void }\n  ) => {\n    try {\n      if (typeof values.destination !== 'string') {\n        const destinationValue = values.destination.value;\n        await move(destinationValue, selected);\n        onClose();\n      }\n    } catch (error) {\n      const normalizedError = normalizeAPIError(error as FetchError)!;\n\n      if (normalizedError && 'errors' in normalizedError) {\n        const formikErrors = normalizedError.errors?.reduce<Record<string, string>>(\n          (acc, error) => {\n            acc[error.values?.path?.length || 'destination'] = error.defaultMessage;\n\n            return acc;\n          },\n          {}\n        );\n\n        if (!isEmpty(formikErrors)) {\n          setErrors(formikErrors);\n        }\n      }\n    }\n  };\n\n  if (isLoading) {\n    return (\n      <Modal.Content>\n        <Modal.Body>\n          <Flex justifyContent=\"center\" paddingTop={4} paddingBottom={4}>\n            <Loader>\n              {formatMessage({\n                id: getTrad('content.isLoading'),\n                defaultMessage: 'Content is loading.',\n              })}\n            </Loader>\n          </Flex>\n        </Modal.Body>\n      </Modal.Content>\n    );\n  }\n\n  const initialFormData: InitialFormData = {\n    destination: {\n      value: currentFolder?.id || '',\n      label: currentFolder?.name || folderStructure[0].label,\n    },\n  };\n\n  return (\n    <Modal.Content>\n      <Formik validateOnChange={false} onSubmit={handleSubmit} initialValues={initialFormData}>\n        {({ values, errors, setFieldValue }) => (\n          <Form noValidate>\n            <Modal.Header>\n              <Modal.Title>\n                {formatMessage({\n                  id: getTrad('modal.folder.move.title'),\n                  defaultMessage: 'Move elements to',\n                })}\n              </Modal.Title>\n            </Modal.Header>\n\n            <Modal.Body>\n              <Grid.Root gap={4}>\n                <Grid.Item xs={12} col={12} direction=\"column\" alignItems=\"stretch\">\n                  <Field.Root id=\"folder-destination\">\n                    <Field.Label>\n                      {formatMessage({\n                        id: getTrad('form.input.label.folder-location'),\n                        defaultMessage: 'Location',\n                      })}\n                    </Field.Label>\n\n                    <SelectTree\n                      options={folderStructure as OptionSelectTree[]}\n                      onChange={(value: InitialFormData['destination']) => {\n                        setFieldValue('destination', value);\n                      }}\n                      defaultValue={\n                        typeof values.destination !== 'string' ? values.destination : undefined\n                      }\n                      name=\"destination\"\n                      menuPortalTarget={document.querySelector('body')}\n                      inputId=\"folder-destination\"\n                      error={errors?.destination}\n                      ariaErrorMessage=\"destination-error\"\n                    />\n\n                    {errors.destination && (\n                      <Typography variant=\"pi\" tag=\"p\" textColor=\"danger600\">\n                        {errors.destination}\n                      </Typography>\n                    )}\n                  </Field.Root>\n                </Grid.Item>\n              </Grid.Root>\n            </Modal.Body>\n\n            <Modal.Footer>\n              <Modal.Close>\n                <Button variant=\"tertiary\" name=\"cancel\">\n                  {formatMessage({ id: 'cancel', defaultMessage: 'Cancel' })}\n                </Button>\n              </Modal.Close>\n              <Button type=\"submit\" loading={isLoading}>\n                {formatMessage({ id: 'modal.folder.move.submit', defaultMessage: 'Move' })}\n              </Button>\n            </Modal.Footer>\n          </Form>\n        )}\n      </Formik>\n    </Modal.Content>\n  );\n};\n", "import React, { useState } from 'react';\n\nimport { Button, Modal } from '@strapi/design-system';\nimport { Folder } from '@strapi/icons';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport { BulkMoveDialog } from '../../../../components/BulkMoveDialog';\nimport { AssetDefinition, FolderDefinition } from '../../../../constants';\n\nexport const BulkMoveButton = ({ selected, onSuccess, currentFolder }) => {\n  const { formatMessage } = useIntl();\n  const [showConfirmDialog, setShowConfirmDialog] = useState(false);\n\n  const handleConfirmMove = () => {\n    setShowConfirmDialog(false);\n    onSuccess();\n  };\n\n  return (\n    <Modal.Root open={showConfirmDialog} onOpenChange={setShowConfirmDialog}>\n      <Modal.Trigger>\n        <Button variant=\"secondary\" size=\"S\" startIcon={<Folder />}>\n          {formatMessage({ id: 'global.move', defaultMessage: 'Move' })}\n        </Button>\n      </Modal.Trigger>\n      <BulkMoveDialog\n        currentFolder={currentFolder}\n        onClose={handleConfirmMove}\n        selected={selected}\n      />\n    </Modal.Root>\n  );\n};\n\nBulkMoveButton.defaultProps = {\n  currentFolder: undefined,\n  selected: [],\n};\n\nBulkMoveButton.propTypes = {\n  onSuccess: PropTypes.func.isRequired,\n  currentFolder: FolderDefinition,\n  selected: PropTypes.arrayOf(AssetDefinition, FolderDefinition),\n};\n", "import React from 'react';\n\nimport { Flex, Typography } from '@strapi/design-system';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport { AssetDefinition, FolderDefinition } from '../../../../constants';\nimport { getTrad } from '../../../../utils';\n\nimport { BulkDeleteButton } from './BulkDeleteButton';\nimport { BulkMoveButton } from './BulkMoveButton';\n\nexport const BulkActions = ({ selected, onSuccess, currentFolder }) => {\n  const { formatMessage } = useIntl();\n  const numberAssets = selected.reduce(function (_this, val) {\n    return val?.type === 'folder' ? _this + val.files.count : _this + 1;\n  }, 0);\n\n  return (\n    <Flex gap={2} paddingBottom={5}>\n      <Typography variant=\"epsilon\" textColor=\"neutral600\">\n        {formatMessage(\n          {\n            id: getTrad('list.assets.selected'),\n            defaultMessage:\n              '{numberFolders, plural, one {1 folder} other {# folders}} - {numberAssets, plural, one {1 asset} other {# assets}} selected',\n          },\n          {\n            numberFolders: selected.filter(({ type }) => type === 'folder').length,\n            numberAssets,\n          }\n        )}\n      </Typography>\n\n      <BulkDeleteButton selected={selected} onSuccess={onSuccess} />\n      <BulkMoveButton currentFolder={currentFolder} selected={selected} onSuccess={onSuccess} />\n    </Flex>\n  );\n};\n\nBulkActions.defaultProps = {\n  currentFolder: undefined,\n  selected: [],\n};\n\nBulkActions.propTypes = {\n  onSuccess: PropTypes.func.isRequired,\n  currentFolder: FolderDefinition,\n  selected: PropTypes.arrayOf(AssetDefinition, FolderDefinition),\n};\n", "import React from 'react';\n\nimport { Button } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyPermissions } from '@strapi/icons/symbols';\nimport PropTypes from 'prop-types';\nimport { useIntl } from 'react-intl';\n\nimport { EmptyAssets } from '../../../../components/EmptyAssets';\nimport { getTrad } from '../../../../utils';\n\nconst getContentIntlMessage = ({ isFiltering, canCreate, canRead }) => {\n  if (isFiltering) {\n    return {\n      id: 'list.assets-empty.title-withSearch',\n      defaultMessage: 'There are no elements with the applied filters',\n    };\n  }\n\n  if (canRead) {\n    if (canCreate) {\n      return {\n        id: 'list.assets.empty-upload',\n        defaultMessage: 'Upload your first assets...',\n      };\n    }\n\n    return {\n      id: 'list.assets.empty',\n      defaultMessage: 'Media Library is empty',\n    };\n  }\n\n  return {\n    id: 'header.actions.no-permissions',\n    defaultMessage: 'No permissions to view',\n  };\n};\n\nexport const EmptyOrNoPermissions = ({ canCreate, isFiltering, canRead, onActionClick }) => {\n  const { formatMessage } = useIntl();\n  const content = getContentIntlMessage({ isFiltering, canCreate, canRead });\n\n  return (\n    <EmptyAssets\n      icon={!canRead ? EmptyPermissions : undefined}\n      action={\n        canCreate &&\n        !isFiltering && (\n          <Button variant=\"secondary\" startIcon={<Plus />} onClick={onActionClick}>\n            {formatMessage({\n              id: getTrad('header.actions.add-assets'),\n              defaultMessage: 'Add new assets',\n            })}\n          </Button>\n        )\n      }\n      content={formatMessage({\n        ...content,\n        id: getTrad(content.id),\n      })}\n    />\n  );\n};\n\nEmptyOrNoPermissions.propTypes = {\n  canCreate: PropTypes.bool.isRequired,\n  canRead: PropTypes.bool.isRequired,\n  isFiltering: PropTypes.bool.isRequired,\n  onActionClick: PropTypes.func.isRequired,\n};\n", "import * as React from 'react';\n\nimport { useTracking, useQueryParams } from '@strapi/admin/strapi-admin';\nimport { Button, Popover } from '@strapi/design-system';\nimport { Filter } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\n\nimport FilterList from '../../../../components/FilterList';\nimport FilterPopover from '../../../../components/FilterPopover';\nimport { displayedFilters } from '../../../../utils';\n\nexport const Filters = () => {\n  const [open, setOpen] = React.useState(false);\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const [{ query }, setQuery] = useQueryParams();\n  const filters = query?.filters?.$and || [];\n\n  const handleRemoveFilter = (nextFilters) => {\n    setQuery({ filters: { $and: nextFilters }, page: 1 });\n  };\n\n  const handleSubmit = (filters) => {\n    trackUsage('didFilterMediaLibraryElements', {\n      location: 'content-manager',\n      filter: Object.keys(filters[filters.length - 1])[0],\n    });\n    setQuery({ filters: { $and: filters }, page: 1 });\n  };\n\n  return (\n    <Popover.Root open={open} onOpenChange={setOpen}>\n      <Popover.Trigger>\n        <Button variant=\"tertiary\" startIcon={<Filter />} size=\"S\">\n          {formatMessage({ id: 'app.utils.filters', defaultMessage: 'Filters' })}\n        </Button>\n      </Popover.Trigger>\n      <FilterPopover\n        displayedFilters={displayedFilters}\n        filters={filters}\n        onSubmit={handleSubmit}\n        onToggle={setOpen}\n      />\n      <FilterList\n        appliedFilters={filters}\n        filtersSchema={displayedFilters}\n        onRemoveFilter={handleRemoveFilter}\n      />\n    </Popover.Root>\n  );\n};\n", "import React from 'react';\n\nimport { useQueryParams, Layouts } from '@strapi/admin/strapi-admin';\nimport { But<PERSON>, Flex, Link } from '@strapi/design-system';\nimport { ArrowLeft, Plus } from '@strapi/icons';\nimport PropTypes from 'prop-types';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { useLocation, NavLink } from 'react-router-dom';\n\nimport { Breadcrumbs } from '../../../../components/Breadcrumbs';\nimport { BreadcrumbsDefinition, FolderDefinition } from '../../../../constants';\nimport { getTrad } from '../../../../utils';\n\nexport const Header = ({\n  breadcrumbs,\n  canCreate,\n  folder,\n  onToggleEditFolderDialog,\n  onToggleUploadAssetDialog,\n}) => {\n  const { formatMessage } = useIntl();\n  const { pathname } = useLocation();\n  const [{ query }] = useQueryParams();\n  const backQuery = {\n    ...query,\n    folder: folder?.parent?.id ?? undefined,\n    folderPath: folder?.parent?.path ?? undefined,\n  };\n\n  return (\n    <Layouts.Header\n      title={formatMessage({\n        id: getTrad('plugin.name'),\n        defaultMessage: `Media Library`,\n      })}\n      subtitle={\n        breadcrumbs &&\n        folder && (\n          <Breadcrumbs\n            tag=\"nav\"\n            label={formatMessage({\n              id: getTrad('header.breadcrumbs.nav.label'),\n              defaultMessage: 'Folders navigation',\n            })}\n            breadcrumbs={breadcrumbs}\n            currentFolderId={folder?.id}\n          />\n        )\n      }\n      navigationAction={\n        folder && (\n          <Link\n            tag={NavLink}\n            startIcon={<ArrowLeft />}\n            to={`${pathname}?${stringify(backQuery, { encode: false })}`}\n          >\n            {formatMessage({\n              id: getTrad('header.actions.folder-level-up'),\n              defaultMessage: 'Back',\n            })}\n          </Link>\n        )\n      }\n      primaryAction={\n        canCreate && (\n          <Flex gap={2}>\n            <Button startIcon={<Plus />} variant=\"secondary\" onClick={onToggleEditFolderDialog}>\n              {formatMessage({\n                id: getTrad('header.actions.add-folder'),\n                defaultMessage: 'Add new folder',\n              })}\n            </Button>\n\n            <Button startIcon={<Plus />} onClick={onToggleUploadAssetDialog}>\n              {formatMessage({\n                id: getTrad('header.actions.add-assets'),\n                defaultMessage: 'Add new assets',\n              })}\n            </Button>\n          </Flex>\n        )\n      }\n    />\n  );\n};\n\nHeader.defaultProps = {\n  breadcrumbs: false,\n  folder: null,\n};\n\nHeader.propTypes = {\n  breadcrumbs: PropTypes.oneOfType([BreadcrumbsDefinition, PropTypes.bool]),\n  canCreate: PropTypes.bool.isRequired,\n  folder: FolderDefinition,\n  onToggleEditFolderDialog: PropTypes.func.isRequired,\n  onToggleUploadAssetDialog: PropTypes.func.isRequired,\n};\n", "import React, { useRef, useState } from 'react';\n\nimport {\n  Page,\n  SearchInput,\n  Pagination,\n  useTracking,\n  useQueryParams,\n  Layouts,\n} from '@strapi/admin/strapi-admin';\nimport {\n  Checkbox,\n  Box,\n  Divider,\n  Flex,\n  IconButton,\n  Typography,\n  VisuallyHidden,\n  Grid,\n} from '@strapi/design-system';\nimport { Cog, GridFour as GridIcon, List, Pencil } from '@strapi/icons';\nimport { stringify } from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Link as ReactRouterLink, useNavigate, useLocation } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { AssetGridList } from '../../../components/AssetGridList';\nimport { EditAssetDialog } from '../../../components/EditAssetDialog';\nimport { EditFolderDialog } from '../../../components/EditFolderDialog';\nimport {\n  FolderCard,\n  FolderCardBody,\n  FolderCardBodyAction,\n  FolderCardCheckbox,\n} from '../../../components/FolderCard';\nimport { FolderGridList } from '../../../components/FolderGridList';\nimport SortPicker from '../../../components/SortPicker';\nimport { TableList } from '../../../components/TableList';\nimport { UploadAssetDialog } from '../../../components/UploadAssetDialog/UploadAssetDialog';\nimport { localStorageKeys, viewOptions } from '../../../constants';\nimport { useAssets } from '../../../hooks/useAssets';\nimport { useFolder } from '../../../hooks/useFolder';\nimport { useFolders } from '../../../hooks/useFolders';\nimport { useMediaLibraryPermissions } from '../../../hooks/useMediaLibraryPermissions';\nimport { usePersistentState } from '../../../hooks/usePersistentState';\nimport { useSelectionState } from '../../../hooks/useSelectionState';\nimport { containsAssetFilter, getBreadcrumbDataML, getFolderURL, getTrad } from '../../../utils';\n\nimport { BulkActions } from './components/BulkActions';\nimport { EmptyOrNoPermissions } from './components/EmptyOrNoPermissions';\nimport { Filters } from './components/Filters';\nimport { Header } from './components/Header';\n\nconst BoxWithHeight = styled(Box)`\n  height: 3.2rem;\n  display: flex;\n  align-items: center;\n`;\n\nconst TypographyMaxWidth = styled(Typography)`\n  max-width: 100%;\n`;\n\nconst ActionContainer = styled(Box)`\n  svg {\n    path {\n      fill: ${({ theme }) => theme.colors.neutral500};\n    }\n  }\n`;\n\nexport const MediaLibrary = () => {\n  const navigate = useNavigate();\n  const {\n    canRead,\n    canCreate,\n    canUpdate,\n    canCopyLink,\n    canDownload,\n    canConfigureView,\n    isLoading: permissionsLoading,\n  } = useMediaLibraryPermissions();\n  const currentFolderToEditRef = useRef();\n  const { formatMessage } = useIntl();\n  const { pathname } = useLocation();\n  const { trackUsage } = useTracking();\n  const [{ query }, setQuery] = useQueryParams();\n  const isFiltering = Boolean(query._q || query.filters);\n  const [view, setView] = usePersistentState(localStorageKeys.view, viewOptions.GRID);\n  const isGridView = view === viewOptions.GRID;\n\n  const {\n    data: assetsData,\n    isLoading: assetsLoading,\n    errors: assetsError,\n  } = useAssets({\n    skipWhen: !canRead,\n    query,\n  });\n\n  const {\n    data: foldersData,\n    isLoading: foldersLoading,\n    errors: foldersError,\n  } = useFolders({\n    enabled: canRead && assetsData?.pagination?.page === 1 && !containsAssetFilter(query),\n    query,\n  });\n\n  const {\n    data: currentFolder,\n    isLoading: isCurrentFolderLoading,\n    error: currentFolderError,\n  } = useFolder(query?.folder, {\n    enabled: canRead && !!query?.folder,\n  });\n\n  // Folder was not found: redirect to the media library root\n  if (currentFolderError?.response?.status === 404) {\n    navigate(pathname);\n  }\n\n  const folders =\n    foldersData?.map((folder) => ({\n      ...folder,\n      type: 'folder',\n      folderURL: getFolderURL(pathname, query, folder.id),\n      isSelectable: canUpdate,\n    })) ?? [];\n  const folderCount = folders?.length || 0;\n  const assets =\n    assetsData?.results?.map((asset) => ({ ...asset, type: 'asset', isSelectable: canUpdate })) ||\n    [];\n  const assetCount = assets?.length ?? 0;\n  const totalAssetCount = assetsData?.pagination?.total;\n\n  const isLoading = isCurrentFolderLoading || foldersLoading || permissionsLoading || assetsLoading;\n  const [showUploadAssetDialog, setShowUploadAssetDialog] = useState(false);\n  const [showEditFolderDialog, setShowEditFolderDialog] = useState(false);\n  const [assetToEdit, setAssetToEdit] = useState(undefined);\n  const [folderToEdit, setFolderToEdit] = useState(undefined);\n  const [selected, { selectOne, selectAll }] = useSelectionState(['type', 'id'], []);\n  const indeterminateBulkSelect =\n    selected?.length > 0 && selected?.length !== assetCount + folderCount;\n  const toggleUploadAssetDialog = () => setShowUploadAssetDialog((prev) => !prev);\n  const toggleEditFolderDialog = ({ created = false } = {}) => {\n    // folders are only displayed on the first page, therefore\n    // we have to navigate the user to that page, in case a folder\n    // was created successfully in order for them to see it\n    if (created && query?.page !== '1') {\n      setQuery({\n        ...query,\n        page: 1,\n      });\n    }\n\n    setShowEditFolderDialog((prev) => !prev);\n  };\n\n  const handleBulkSelect = (checked, elements) => {\n    if (checked) {\n      trackUsage('didSelectAllMediaLibraryElements');\n    }\n\n    selectAll(elements);\n  };\n\n  const handleChangeSort = (value) => {\n    trackUsage('didSortMediaLibraryElements', {\n      location: 'upload',\n      sort: value,\n    });\n    setQuery({ sort: value });\n  };\n\n  const handleEditFolder = (folder) => {\n    setFolderToEdit(folder);\n    setShowEditFolderDialog(true);\n  };\n\n  const handleEditFolderClose = (payload) => {\n    setFolderToEdit(null);\n    toggleEditFolderDialog(payload);\n\n    if (currentFolderToEditRef.current) {\n      currentFolderToEditRef.current.focus();\n    }\n  };\n\n  const handleAssetDeleted = (numberOfAssets) => {\n    if (\n      numberOfAssets === assetCount &&\n      assetsData.pagination.page === assetsData.pagination.pageCount &&\n      assetsData.pagination.page > 1\n    ) {\n      setQuery({\n        ...query,\n        page: assetsData.pagination.page - 1,\n      });\n    }\n  };\n\n  const handleBulkActionSuccess = () => {\n    selectAll();\n\n    handleAssetDeleted(selected.length);\n  };\n\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (assetsError || foldersError) {\n    return <Page.Error />;\n  }\n\n  return (\n    <Layouts.Root>\n      <Page.Main>\n        <Header\n          breadcrumbs={\n            !isCurrentFolderLoading && getBreadcrumbDataML(currentFolder, { pathname, query })\n          }\n          canCreate={canCreate}\n          onToggleEditFolderDialog={toggleEditFolderDialog}\n          onToggleUploadAssetDialog={toggleUploadAssetDialog}\n          folder={currentFolder}\n        />\n        <Layouts.Action\n          startActions={\n            <>\n              {canUpdate && isGridView && (assetCount > 0 || folderCount > 0) && (\n                <BoxWithHeight\n                  paddingLeft={2}\n                  paddingRight={2}\n                  background=\"neutral0\"\n                  hasRadius\n                  borderColor=\"neutral200\"\n                >\n                  <Checkbox\n                    aria-label={formatMessage({\n                      id: getTrad('bulk.select.label'),\n                      defaultMessage: 'Select all folders & assets',\n                    })}\n                    checked={\n                      indeterminateBulkSelect\n                        ? 'indeterminate'\n                        : (assetCount > 0 || folderCount > 0) &&\n                          selected.length === assetCount + folderCount\n                    }\n                    onCheckedChange={(e) => handleBulkSelect(e, [...assets, ...folders])}\n                  />\n                </BoxWithHeight>\n              )}\n              {canRead && isGridView && (\n                <SortPicker value={query?.sort} onChangeSort={handleChangeSort} />\n              )}\n              {canRead && <Filters />}\n            </>\n          }\n          endActions={\n            <>\n              {canConfigureView ? (\n                <ActionContainer paddingTop={1} paddingBottom={1}>\n                  <IconButton\n                    tag={ReactRouterLink}\n                    to={{\n                      pathname: `${pathname}/configuration`,\n                      search: stringify(query, { encode: false }),\n                    }}\n                    label={formatMessage({\n                      id: 'app.links.configure-view',\n                      defaultMessage: 'Configure the view',\n                    })}\n                  >\n                    <Cog />\n                  </IconButton>\n                </ActionContainer>\n              ) : null}\n              <ActionContainer paddingTop={1} paddingBottom={1}>\n                <IconButton\n                  label={\n                    isGridView\n                      ? formatMessage({\n                          id: getTrad('view-switch.list'),\n                          defaultMessage: 'List View',\n                        })\n                      : formatMessage({\n                          id: getTrad('view-switch.grid'),\n                          defaultMessage: 'Grid View',\n                        })\n                  }\n                  onClick={() => setView(isGridView ? viewOptions.LIST : viewOptions.GRID)}\n                >\n                  {isGridView ? <List /> : <GridIcon />}\n                </IconButton>\n              </ActionContainer>\n              <SearchInput\n                label={formatMessage({\n                  id: getTrad('search.label'),\n                  defaultMessage: 'Search for an asset',\n                })}\n                trackedEvent=\"didSearchMediaLibraryElements\"\n                trackedEventDetails={{ location: 'upload' }}\n              />\n            </>\n          }\n        />\n\n        <Layouts.Content>\n          {selected.length > 0 && (\n            <BulkActions\n              currentFolder={currentFolder}\n              selected={selected}\n              onSuccess={handleBulkActionSuccess}\n            />\n          )}\n\n          {folderCount === 0 && assetCount === 0 && (\n            <EmptyOrNoPermissions\n              canCreate={canCreate}\n              canRead={canRead}\n              isFiltering={isFiltering}\n              onActionClick={toggleUploadAssetDialog}\n            />\n          )}\n\n          {/* TODO: fix AssetListTable should handle no assets views (loading) */}\n          {canRead && !isGridView && (assetCount > 0 || folderCount > 0) && (\n            <TableList\n              assetCount={assetCount}\n              folderCount={folderCount}\n              indeterminate={indeterminateBulkSelect}\n              onChangeSort={handleChangeSort}\n              onChangeFolder={(folderID, folderPath) =>\n                navigate(getFolderURL(pathname, query, { folder: folderID, folderPath }))\n              }\n              onEditAsset={setAssetToEdit}\n              onEditFolder={handleEditFolder}\n              onSelectOne={selectOne}\n              onSelectAll={handleBulkSelect}\n              rows={[...folders, ...assets]}\n              selected={selected}\n              shouldDisableBulkSelect={!canUpdate}\n              sortQuery={query?.sort ?? ''}\n            />\n          )}\n\n          {canRead && isGridView && (\n            <>\n              {folderCount > 0 && (\n                <FolderGridList\n                  title={\n                    // Folders title should only appear if:\n                    // user is filtering and there are assets to display, to divide both type of elements\n                    // user is not filtering\n                    (((isFiltering && assetCount > 0) || !isFiltering) &&\n                      formatMessage(\n                        {\n                          id: getTrad('list.folders.title'),\n                          defaultMessage: 'Folders ({count})',\n                        },\n                        { count: folderCount }\n                      )) ||\n                    ''\n                  }\n                >\n                  {folders.map((folder) => {\n                    const selectedFolders = selected.filter(({ type }) => type === 'folder');\n                    const isSelected = !!selectedFolders.find(\n                      (currentFolder) => currentFolder.id === folder.id\n                    );\n\n                    const url = getFolderURL(pathname, query, {\n                      folder: folder?.id,\n                      folderPath: folder?.path,\n                    });\n\n                    return (\n                      <Grid.Item\n                        col={3}\n                        key={`folder-${folder.id}`}\n                        direction=\"column\"\n                        alignItems=\"stretch\"\n                      >\n                        <FolderCard\n                          ref={\n                            folderToEdit && folder.id === folderToEdit.id\n                              ? currentFolderToEditRef\n                              : undefined\n                          }\n                          ariaLabel={folder.name}\n                          id={`folder-${folder.id}`}\n                          to={url}\n                          startAction={\n                            selectOne && folder.isSelectable ? (\n                              <FolderCardCheckbox\n                                data-testid={`folder-checkbox-${folder.id}`}\n                                checked={isSelected}\n                                onCheckedChange={() => selectOne(folder)}\n                              />\n                            ) : null\n                          }\n                          cardActions={\n                            <IconButton\n                              aria-label={formatMessage({\n                                id: getTrad('list.folder.edit'),\n                                defaultMessage: 'Edit folder',\n                              })}\n                              onClick={() => handleEditFolder(folder)}\n                            >\n                              <Pencil />\n                            </IconButton>\n                          }\n                        >\n                          <FolderCardBody>\n                            <FolderCardBodyAction to={url}>\n                              <Flex tag=\"h2\" direction=\"column\" alignItems=\"start\" maxWidth=\"100%\">\n                                <TypographyMaxWidth\n                                  fontWeight=\"semiBold\"\n                                  textColor=\"neutral800\"\n                                  ellipsis\n                                >\n                                  {folder.name}\n                                  <VisuallyHidden>:</VisuallyHidden>\n                                </TypographyMaxWidth>\n\n                                <TypographyMaxWidth\n                                  tag=\"span\"\n                                  textColor=\"neutral600\"\n                                  variant=\"pi\"\n                                  ellipsis\n                                >\n                                  {formatMessage(\n                                    {\n                                      id: getTrad('list.folder.subtitle'),\n                                      defaultMessage:\n                                        '{folderCount, plural, =0 {# folder} one {# folder} other {# folders}}, {filesCount, plural, =0 {# asset} one {# asset} other {# assets}}',\n                                    },\n                                    {\n                                      folderCount: folder.children.count,\n                                      filesCount: folder.files.count,\n                                    }\n                                  )}\n                                </TypographyMaxWidth>\n                              </Flex>\n                            </FolderCardBodyAction>\n                          </FolderCardBody>\n                        </FolderCard>\n                      </Grid.Item>\n                    );\n                  })}\n                </FolderGridList>\n              )}\n\n              {assetCount > 0 && folderCount > 0 && (\n                <Box paddingTop={6} paddingBottom={4}>\n                  <Divider />\n                </Box>\n              )}\n\n              {assetCount > 0 && (\n                <AssetGridList\n                  assets={assets}\n                  onEditAsset={setAssetToEdit}\n                  onSelectAsset={selectOne}\n                  selectedAssets={selected.filter(({ type }) => type === 'asset')}\n                  title={\n                    // Assets title should only appear if:\n                    // - user is not filtering\n                    // - user is filtering and there are folders to display, to separate them\n                    // - user is on page 1 since folders won't appear on any other page than the first one (no need to visually separate them)\n                    ((!isFiltering || (isFiltering && folderCount > 0)) &&\n                      assetsData?.pagination?.page === 1 &&\n                      formatMessage(\n                        {\n                          id: getTrad('list.assets.title'),\n                          defaultMessage: 'Assets ({count})',\n                        },\n                        { count: totalAssetCount }\n                      )) ||\n                    ''\n                  }\n                />\n              )}\n            </>\n          )}\n          <Pagination.Root {...assetsData.pagination}>\n            <Pagination.PageSize />\n            <Pagination.Links />\n          </Pagination.Root>\n        </Layouts.Content>\n      </Page.Main>\n      {showUploadAssetDialog && (\n        <UploadAssetDialog\n          open={showUploadAssetDialog}\n          onClose={toggleUploadAssetDialog}\n          trackedLocation=\"upload\"\n          folderId={query?.folder}\n        />\n      )}\n      {showEditFolderDialog && (\n        <EditFolderDialog\n          open={showEditFolderDialog}\n          onClose={handleEditFolderClose}\n          folder={folderToEdit}\n          parentFolderId={query?.folder}\n          location=\"upload\"\n        />\n      )}\n      {assetToEdit && (\n        <EditAssetDialog\n          onClose={(editedAsset) => {\n            // The asset has been deleted\n            if (editedAsset === null) {\n              handleAssetDeleted(1);\n            }\n\n            setAssetToEdit(undefined);\n          }}\n          open={!!assetToEdit}\n          asset={assetToEdit}\n          canUpdate={canUpdate}\n          canCopyLink={canCopyLink}\n          canDownload={canDownload}\n          trackedLocation=\"upload\"\n        />\n      )}\n    </Layouts.Root>\n  );\n};\n", "import React, { lazy, Suspense, useEffect } from 'react';\n\nimport { Page, useQueryParams } from '@strapi/admin/strapi-admin';\nimport { useIntl } from 'react-intl';\nimport { Route, Routes } from 'react-router-dom';\n\nimport { useConfig } from '../../hooks/useConfig';\nimport { getTrad } from '../../utils';\n\nimport { MediaLibrary } from './MediaLibrary';\n\nconst ConfigureTheView = lazy(() => import('./ConfigureTheView'));\n\nconst Upload = () => {\n  const {\n    config: { isLoading, isError, data: config },\n  } = useConfig();\n\n  const [{ rawQuery }, setQuery] = useQueryParams();\n  const { formatMessage } = useIntl();\n  const title = formatMessage({ id: getTrad('plugin.name'), defaultMessage: 'Media Library' });\n\n  useEffect(() => {\n    if (isLoading || isError || rawQuery) {\n      return;\n    }\n    setQuery({ sort: config.sort, page: 1, pageSize: config.pageSize });\n  }, [isLoading, isError, config, rawQuery, setQuery]);\n\n  if (isLoading) {\n    return (\n      <>\n        <Page.Title>{title}</Page.Title>\n        <Page.Loading />\n      </>\n    );\n  }\n\n  return (\n    <Page.Main>\n      {rawQuery ? (\n        <Suspense fallback={<Page.Loading />}>\n          <Routes>\n            <Route index element={<MediaLibrary />} />\n            <Route path=\"configuration\" element={<ConfigureTheView config={config} />} />\n          </Routes>\n        </Suspense>\n      ) : null}\n    </Page.Main>\n  );\n};\n\nexport default Upload;\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsBO,IAAM,sBAAsB,CACjC,QACA,EAAE,UAAU,MAAA,MACT;;AACH,MAAI,OAAyB;IAC3B;MACE,IAAI;MACJ,OAAO,EAAE,IAAI,QAAQ,aAAa,GAAG,gBAAgB,gBAAgB;MACrE,MAAM,SAAS,aAAa,UAAU,SAAS,CAAA,CAAE,IAAI;IACvD;EAAA;AAGE,OAAA,iCAAQ,WAAU,QAAO,iCAAQ,YAAW,cAAY,sCAAQ,WAAR,mBAAgB,SAAQ;AAC7E,SAAA,KAAK,CAAA,CAAE;EACd;AAEA,OAAI,iCAAQ,WAAU,OAAO,OAAO,WAAW,UAAU;AACvD,SAAK,KAAK;MACR,IAAI,OAAO,OAAO;MAClB,OAAO,OAAO,OAAO;MACrB,MAAM,aAAa,UAAU,SAAS,CAAA,GAAI;QACxC,SAAQ,YAAO,OAAO,OAAd,mBAAkB;QAC1B,YAAY,OAAO,OAAO;MAAA,CAC3B;IAAA,CACF;EACH;AAEA,MAAI,QAAQ;AACV,SAAK,KAAK;MACR,IAAI,OAAO;MACX,OAAO,OAAO;IAAA,CACf;EACH;AAEO,SAAA;AACT;ACrDa,IAAA,qBAAqB,CAAC,UAAyB;AACpD,QAAA,EAAE,GAAA,IAAO,cAAA;AAEf,aACG,wBAAA,KAAA,EAAI,UAAS,YAAW,QAAQ,GAC/B,cAAA,wBAAC,cAAS,EAAA,mBAAiB,GAAG,EAAE,UAAW,GAAG,MAAA,CAAO,EACvD,CAAA;AAEJ;ACDO,IAAM,mBAAmB,CAAC,EAAE,UAAU,UAAA,MAAgB;AACrD,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,OAAA,IAAW,cAAA;AAEnB,QAAM,sBAAsB,YAAY;AACtC,UAAM,OAAO,QAAQ;AACX,cAAA;EAAA;AAIV,aAAA,yBAAC,OAAO,MAAP,EACC,UAAA;QAAC,wBAAA,OAAO,SAAP,EACC,cAAA,wBAAC,QAAA,EAAO,SAAQ,gBAAe,MAAK,KAAI,eAAA,wBAAY,cAAM,CAAA,CAAA,GACvD,UAAA,cAAc,EAAE,IAAI,iBAAiB,gBAAgB,SAAA,CAAU,EAAA,CAClE,EACF,CAAA;QACA,wBAAC,eAAc,EAAA,WAAW,oBAAqB,CAAA;EACjD,EAAA,CAAA;AAEJ;AAEA,iBAAiB,YAAY;EAC3B,UAAU,kBAAAA,QAAU,QAAQ,iBAAiB,gBAAgB,EAAE;EAC/D,WAAW,kBAAAA,QAAU,KAAK;AAC5B;ACPO,IAAM,cAAc,MAAM;AACzB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,mBAAA,IAAuB,gBAAA;AAC/B,QAAM,cAAc,eAAA;AACd,QAAA,EAAE,KAAA,IAAS,eAAA;AAEjB,QAAM,gBAAgB,CAAC,EAAE,qBAAqB,gBAAA,MAAsC;AAClF,UAAM,UAAU,gBAAgB,OAAgB,CAAC,KAAK,aAAa;AAC3D,YAAA,EAAE,IAAI,KAAS,IAAA;AACf,YAAA,MAAM,SAAS,UAAU,YAAY;AAEvC,UAAA,CAAC,IAAI,GAAG,GAAG;AACT,YAAA,GAAG,IAAI,CAAA;MACb;AAEI,UAAA,GAAG,EAAG,KAAK,EAAE;AAEV,aAAA;IACT,GAAG,CAAE,CAAA;AAEL,WAAO,KAAK,6BAA6B,EAAE,GAAG,SAAS,oBAAqB,CAAA;EAAA;AAGxE,QAAA,WAAW,YAIf,eAAe;IACf,UAAU,KAAK;;AACP,YAAA;QACJ,MAAM,EAAE,KAAK;MACX,IAAA;AAEA,YAAA,kCAAM,UAAN,mBAAa,UAAS,GAAG;AACf,oBAAA,eAAe,CAAC,UAAU,QAAQ,GAAG,EAAE,QAAQ,KAAA,CAAM;AACrD,oBAAA,eAAe,CAAC,UAAU,aAAa,GAAG,EAAE,QAAQ,KAAA,CAAM;MACxE;AAIY,kBAAA,eAAe,CAAC,UAAU,SAAS,GAAG,EAAE,QAAQ,KAAA,CAAM;AAE/C,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI,QAAQ,0BAA0B;UACtC,gBAAgB;QAAA,CACjB;MAAA,CACF;IACH;EAAA,CACD;AAEK,QAAA,OAAO,CACX,qBACA,oBACG,SAAS,YAAY,EAAE,qBAAqB,gBAAA,CAAiB;AAE3D,SAAA,EAAE,GAAG,UAAU,KAAA;AACxB;ACjDa,IAAA,iBAAiB,CAAC,EAAE,SAAS,WAAW,CAAC,GAAG,cAAA,MAAyC;AAC1F,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,EAAE,MAAM,iBAAiB,UAAA,IAAc,mBAAmB;AAC1D,QAAA,EAAE,KAAA,IAAS,YAAA;AAEjB,MAAI,CAAC,iBAAiB;AACb,WAAA;EACT;AAEA,QAAM,eAAe,OACnB,QACA,EAAE,UAAA,MACC;;AACC,QAAA;AACE,UAAA,OAAO,OAAO,gBAAgB,UAAU;AACpC,cAAA,mBAAmB,OAAO,YAAY;AACtC,cAAA,KAAK,kBAAkB,QAAQ;AAC7B,gBAAA;MACV;IAAA,SACO,OAAO;AACR,YAAA,kBAAkB,kBAAkB,KAAmB;AAEzD,UAAA,mBAAmB,YAAY,iBAAiB;AAC5C,cAAA,gBAAe,qBAAgB,WAAhB,mBAAwB;UAC3C,CAAC,KAAKC,WAAU;;AACd,kBAAIA,MAAAA,MAAAA,OAAM,WAANA,gBAAAA,IAAc,SAAdA,mBAAoB,WAAU,aAAa,IAAIA,OAAM;AAElD,mBAAA;UACT;UACA,CAAC;;AAGC,YAAA,KAAC,eAAAC,SAAQ,YAAY,GAAG;AAC1B,oBAAU,YAAY;QACxB;MACF;IACF;EAAA;AAGF,MAAI,WAAW;AACb,eAAA,wBACG,MAAM,SAAN,EACC,cAAC,wBAAA,MAAM,MAAN,EACC,cAAA,wBAAC,MAAK,EAAA,gBAAe,UAAS,YAAY,GAAG,eAAe,GAC1D,cAAA,wBAAC,QAAA,EACE,UAAc,cAAA;MACb,IAAI,QAAQ,mBAAmB;MAC/B,gBAAgB;IACjB,CAAA,EAAA,CACH,EACF,CAAA,EACF,CAAA,EACF,CAAA;EAEJ;AAEA,QAAM,kBAAmC;IACvC,aAAa;MACX,QAAO,+CAAe,OAAM;MAC5B,QAAO,+CAAe,SAAQ,gBAAgB,CAAC,EAAE;IACnD;EAAA;AAIA,aAAA,wBAAC,MAAM,SAAN,EACC,cAAA,wBAAC,QAAO,EAAA,kBAAkB,OAAO,UAAU,cAAc,eAAe,iBACrE,UAAA,CAAC,EAAE,QAAQ,QAAQ,cAAA,UAClB,yBAAC,MAAK,EAAA,YAAU,MACd,UAAA;QAAA,wBAAC,MAAM,QAAN,EACC,cAAA,wBAAC,MAAM,OAAN,EACE,UAAc,cAAA;MACb,IAAI,QAAQ,yBAAyB;MACrC,gBAAgB;IAAA,CACjB,EAAA,CACH,EACF,CAAA;QAEA,wBAAC,MAAM,MAAN,EACC,cAAA,wBAAC,KAAK,MAAL,EAAU,KAAK,GACd,cAAA,wBAAC,KAAK,MAAL,EAAU,IAAI,IAAI,KAAK,IAAI,WAAU,UAAS,YAAW,WACxD,cAAA,yBAAC,MAAM,MAAN,EAAW,IAAG,sBACb,UAAA;UAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;QACb,IAAI,QAAQ,kCAAkC;QAC9C,gBAAgB;MACjB,CAAA,EAAA,CACH;UAEA;QAAC;QAAA;UACC,SAAS;UACT,UAAU,CAAC,UAA0C;AACnD,0BAAc,eAAe,KAAK;UACpC;UACA,cACE,OAAO,OAAO,gBAAgB,WAAW,OAAO,cAAc;UAEhE,MAAK;UACL,kBAAkB,SAAS,cAAc,MAAM;UAC/C,SAAQ;UACR,OAAO,iCAAQ;UACf,kBAAiB;QAAA;MACnB;MAEC,OAAO,mBACN,wBAAC,YAAW,EAAA,SAAQ,MAAK,KAAI,KAAI,WAAU,aACxC,UAAA,OAAO,YACV,CAAA;IAAA,EAAA,CAEJ,EAAA,CACF,EACF,CAAA,EAAA,CACF;QAEA,yBAAC,MAAM,QAAN,EACC,UAAA;UAAA,wBAAC,MAAM,OAAN,EACC,cAAC,wBAAA,QAAA,EAAO,SAAQ,YAAW,MAAK,UAC7B,UAAA,cAAc,EAAE,IAAI,UAAU,gBAAgB,SAAS,CAAC,EAC3D,CAAA,EAAA,CACF;UACC,wBAAA,QAAA,EAAO,MAAK,UAAS,SAAS,WAC5B,UAAc,cAAA,EAAE,IAAI,4BAA4B,gBAAgB,OAAA,CAAQ,EAC3E,CAAA;IAAA,EAAA,CACF;EAAA,EAAA,CACF,EAAA,CAEJ,EACF,CAAA;AAEJ;AC3JO,IAAM,iBAAiB,CAAC,EAAE,UAAU,WAAW,cAAA,MAAoB;AAClE,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,CAAC,mBAAmB,oBAAoB,QAAI,uBAAS,KAAK;AAEhE,QAAM,oBAAoB,MAAM;AAC9B,yBAAqB,KAAK;AAChB,cAAA;EAAA;AAGZ,aAAA,yBACG,MAAM,MAAN,EAAW,MAAM,mBAAmB,cAAc,sBACjD,UAAA;QAAC,wBAAA,MAAM,SAAN,EACC,cAAA,wBAAC,QAAA,EAAO,SAAQ,aAAY,MAAK,KAAI,eAAA,wBAAY,eAAO,CAAA,CAAA,GACrD,UAAA,cAAc,EAAE,IAAI,eAAe,gBAAgB,OAAA,CAAQ,EAAA,CAC9D,EACF,CAAA;QACA;MAAC;MAAA;QACC;QACA,SAAS;QACT;MAAA;IACF;EACF,EAAA,CAAA;AAEJ;AAEA,eAAe,eAAe;EAC5B,eAAe;EACf,UAAU,CAAC;AACb;AAEA,eAAe,YAAY;EACzB,WAAW,kBAAAF,QAAU,KAAK;EAC1B,eAAe;EACf,UAAU,kBAAAA,QAAU,QAAQ,iBAAiB,gBAAgB;AAC/D;AChCO,IAAM,cAAc,CAAC,EAAE,UAAU,WAAW,cAAA,MAAoB;AAC/D,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,eAAe,SAAS,OAAO,SAAU,OAAO,KAAK;AACzD,YAAO,2BAAK,UAAS,WAAW,QAAQ,IAAI,MAAM,QAAQ,QAAQ;EAAA,GACjE,CAAC;AAEJ,aACG,yBAAA,MAAA,EAAK,KAAK,GAAG,eAAe,GAC3B,UAAA;QAAA,wBAAC,YAAW,EAAA,SAAQ,WAAU,WAAU,cACrC,UAAA;MACC;QACE,IAAI,QAAQ,sBAAsB;QAClC,gBACE;MACJ;MACA;QACE,eAAe,SAAS,OAAO,CAAC,EAAE,KAAK,MAAM,SAAS,QAAQ,EAAE;QAChE;MACF;IAAA,EAAA,CAEJ;QAEA,wBAAC,kBAAiB,EAAA,UAAoB,UAAsB,CAAA;QAC3D,wBAAA,gBAAA,EAAe,eAA8B,UAAoB,UAAsB,CAAA;EAC1F,EAAA,CAAA;AAEJ;AAEA,YAAY,eAAe;EACzB,eAAe;EACf,UAAU,CAAC;AACb;AAEA,YAAY,YAAY;EACtB,WAAW,kBAAAA,QAAU,KAAK;EAC1B,eAAe;EACf,UAAU,kBAAAA,QAAU,QAAQ,iBAAiB,gBAAgB;AAC/D;ACtCA,IAAM,wBAAwB,CAAC,EAAE,aAAa,WAAW,QAAA,MAAc;AACrE,MAAI,aAAa;AACR,WAAA;MACL,IAAI;MACJ,gBAAgB;IAAA;EAEpB;AAEA,MAAI,SAAS;AACX,QAAI,WAAW;AACN,aAAA;QACL,IAAI;QACJ,gBAAgB;MAAA;IAEpB;AAEO,WAAA;MACL,IAAI;MACJ,gBAAgB;IAAA;EAEpB;AAEO,SAAA;IACL,IAAI;IACJ,gBAAgB;EAAA;AAEpB;AAEO,IAAM,uBAAuB,CAAC,EAAE,WAAW,aAAa,SAAS,cAAA,MAAoB;AACpF,QAAA,EAAE,cAAA,IAAkB,QAAA;AAC1B,QAAM,UAAU,sBAAsB,EAAE,aAAa,WAAW,QAAA,CAAS;AAGvE,aAAA;IAAC;IAAA;MACC,MAAM,CAAC,UAAU,eAAmB;MACpC,QACE,aACA,CAAC,mBAAA,wBACE,QAAO,EAAA,SAAQ,aAAY,eAAY,wBAAA,eAAA,CAAA,CAAK,GAAI,SAAS,eACvD,UAAc,cAAA;QACb,IAAI,QAAQ,2BAA2B;QACvC,gBAAgB;MACjB,CAAA,EAAA,CACH;MAGJ,SAAS,cAAc;QACrB,GAAG;QACH,IAAI,QAAQ,QAAQ,EAAE;MAAA,CACvB;IAAA;EAAA;AAGP;AAEA,qBAAqB,YAAY;EAC/B,WAAW,kBAAAA,QAAU,KAAK;EAC1B,SAAS,kBAAAA,QAAU,KAAK;EACxB,aAAa,kBAAAA,QAAU,KAAK;EAC5B,eAAe,kBAAAA,QAAU,KAAK;AAChC;AC3DO,IAAM,UAAU,MAAM;;AAC3B,QAAM,CAAC,MAAM,OAAO,IAAU,eAAS,KAAK;AACtC,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,CAAC,EAAE,MAAA,GAAS,QAAQ,IAAI,eAAe;AAC7C,QAAM,YAAU,oCAAO,YAAP,mBAAgB,SAAQ,CAAA;AAElC,QAAA,qBAAqB,CAAC,gBAAgB;AACjC,aAAA,EAAE,SAAS,EAAE,MAAM,YAAA,GAAe,MAAM,EAAA,CAAG;EAAA;AAGhD,QAAA,eAAe,CAACG,aAAY;AAChC,eAAW,iCAAiC;MAC1C,UAAU;MACV,QAAQ,OAAO,KAAKA,SAAQA,SAAQ,SAAS,CAAC,CAAC,EAAE,CAAC;IAAA,CACnD;AACQ,aAAA,EAAE,SAAS,EAAE,MAAMA,SAAAA,GAAW,MAAM,EAAA,CAAG;EAAA;AAGlD,aAAA,yBACG,QAAQ,MAAR,EAAa,MAAY,cAAc,SACtC,UAAA;QAAC,wBAAA,QAAQ,SAAR,EACC,cAAA,wBAAC,QAAA,EAAO,SAAQ,YAAW,eAAY,wBAAA,eAAA,CAAA,CAAO,GAAI,MAAK,KACpD,UAAA,cAAc,EAAE,IAAI,qBAAqB,gBAAgB,UAAA,CAAW,EAAA,CACvE,EACF,CAAA;QACA;MAAC;MAAA;QACC;QACA;QACA,UAAU;QACV,UAAU;MAAA;IACZ;QACA;MAAC;MAAA;QACC,gBAAgB;QAChB,eAAe;QACf,gBAAgB;MAAA;IAClB;EACF,EAAA,CAAA;AAEJ;ACpCO,IAAM,SAAS,CAAC;EACrB;EACA;EACA;EACA;EACA;AACF,MAAM;;AACE,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,SAAA,IAAa,YAAA;AACrB,QAAM,CAAC,EAAE,MAAA,CAAO,IAAI,eAAe;AACnC,QAAM,YAAY;IAChB,GAAG;IACH,UAAQ,sCAAQ,WAAR,mBAAgB,OAAM;IAC9B,cAAY,sCAAQ,WAAR,mBAAgB,SAAQ;EAAA;AAIpC,aAAA;IAAC,QAAQ;IAAR;MACC,OAAO,cAAc;QACnB,IAAI,QAAQ,aAAa;QACzB,gBAAgB;MAAA,CACjB;MACD,UACE,eACA,cACE;QAAC;QAAA;UACC,KAAI;UACJ,OAAO,cAAc;YACnB,IAAI,QAAQ,8BAA8B;YAC1C,gBAAgB;UAAA,CACjB;UACD;UACA,iBAAiB,iCAAQ;QAAA;MAC3B;MAGJ,kBACE,cACE;QAACC;QAAA;UACC,KAAK;UACL,eAAA,wBAAY,eAAU,CAAA,CAAA;UACtB,IAAI,GAAG,QAAQ,QAAI,qBAAU,WAAW,EAAE,QAAQ,MAAO,CAAA,CAAC;UAEzD,UAAc,cAAA;YACb,IAAI,QAAQ,gCAAgC;YAC5C,gBAAgB;UAAA,CACjB;QAAA;MACH;MAGJ,eACE,iBACG,yBAAA,MAAA,EAAK,KAAK,GACT,UAAA;YAAC,wBAAA,QAAA,EAAO,eAAY,wBAAA,eAAA,CAAA,CAAK,GAAI,SAAQ,aAAY,SAAS,0BACvD,UAAc,cAAA;UACb,IAAI,QAAQ,2BAA2B;UACvC,gBAAgB;QACjB,CAAA,EAAA,CACH;YAEA,wBAAC,QAAA,EAAO,eAAW,wBAAC,eAAA,CAAK,CAAA,GAAI,SAAS,2BACnC,UAAc,cAAA;UACb,IAAI,QAAQ,2BAA2B;UACvC,gBAAgB;QACjB,CAAA,EAAA,CACH;MAAA,EAAA,CACF;IAAA;EAAA;AAKV;AAEA,OAAO,eAAe;EACpB,aAAa;EACb,QAAQ;AACV;AAEA,OAAO,YAAY;EACjB,aAAa,kBAAAJ,QAAU,UAAU,CAAC,uBAAuB,kBAAAA,QAAU,IAAI,CAAC;EACxE,WAAW,kBAAAA,QAAU,KAAK;EAC1B,QAAQ;EACR,0BAA0B,kBAAAA,QAAU,KAAK;EACzC,2BAA2B,kBAAAA,QAAU,KAAK;AAC5C;AC7CA,IAAM,gBAAgB,GAAO,GAAG;;;;;AAMhC,IAAM,qBAAqB,GAAO,UAAU;;;AAI5C,IAAM,kBAAkB,GAAO,GAAG;;;cAGpB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;;AAK7C,IAAM,eAAe,MAAM;;AAChC,QAAM,WAAW,YAAA;AACX,QAAA;IACJ;IACA;IACA;IACA;IACA;IACA;IACA,WAAW;EAAA,IACT,2BAA2B;AAC/B,QAAM,6BAAyB,qBAAA;AACzB,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,EAAE,SAAA,IAAa,YAAA;AACf,QAAA,EAAE,WAAA,IAAe,YAAA;AACvB,QAAM,CAAC,EAAE,MAAA,GAAS,QAAQ,IAAI,eAAe;AAC7C,QAAM,cAAc,QAAQ,MAAM,MAAM,MAAM,OAAO;AAC/C,QAAA,CAAC,MAAM,OAAO,IAAI,mBAAmB,iBAAiB,MAAM,YAAY,IAAI;AAC5E,QAAA,aAAa,SAAS,YAAY;AAElC,QAAA;IACJ,MAAM;IACN,WAAW;IACX,QAAQ;EAAA,IACN,UAAU;IACZ,UAAU,CAAC;IACX;EAAA,CACD;AAEK,QAAA;IACJ,MAAM;IACN,WAAW;IACX,QAAQ;EAAA,IACN,WAAW;IACb,SAAS,aAAW,8CAAY,eAAZ,mBAAwB,UAAS,KAAK,CAAC,oBAAoB,KAAK;IACpF;EAAA,CACD;AAEK,QAAA;IACJ,MAAM;IACN,WAAW;IACX,OAAO;EAAA,IACL,UAAU,+BAAO,QAAQ;IAC3B,SAAS,WAAW,CAAC,EAAC,+BAAO;EAAA,CAC9B;AAGG,QAAA,8DAAoB,aAApB,mBAA8B,YAAW,KAAK;AAChD,aAAS,QAAQ;EACnB;AAEA,QAAM,WACJ,2CAAa,IAAI,CAAC,YAAY;IAC5B,GAAG;IACH,MAAM;IACN,WAAW,aAAa,UAAU,OAAO,OAAO,EAAE;IAClD,cAAc;EAAA,QACT,CAAA;AACH,QAAA,eAAc,mCAAS,WAAU;AACvC,QAAM,WACJ,8CAAY,YAAZ,mBAAqB,IAAI,CAAC,WAAW,EAAE,GAAG,OAAO,MAAM,SAAS,cAAc,UAAU,QACxF,CAAA;AACI,QAAA,cAAa,iCAAQ,WAAU;AAC/B,QAAA,mBAAkB,8CAAY,eAAZ,mBAAwB;AAE1C,QAAA,YAAY,0BAA0B,kBAAkB,sBAAsB;AACpF,QAAM,CAAC,uBAAuB,wBAAwB,QAAI,uBAAS,KAAK;AACxE,QAAM,CAAC,sBAAsB,uBAAuB,QAAI,uBAAS,KAAK;AACtE,QAAM,CAAC,aAAa,cAAc,QAAI,uBAAS,MAAS;AACxD,QAAM,CAAC,cAAc,eAAe,QAAI,uBAAS,MAAS;AAC1D,QAAM,CAAC,UAAU,EAAE,WAAW,UAAW,CAAA,IAAI,kBAAkB,CAAC,QAAQ,IAAI,GAAG,CAAE,CAAA;AACjF,QAAM,2BACJ,qCAAU,UAAS,MAAK,qCAAU,YAAW,aAAa;AAC5D,QAAM,0BAA0B,MAAM,yBAAyB,CAAC,SAAS,CAAC,IAAI;AAC9E,QAAM,yBAAyB,CAAC,EAAE,UAAU,MAAM,IAAI,CAAA,MAAO;AAIvD,QAAA,YAAW,+BAAO,UAAS,KAAK;AACzB,eAAA;QACP,GAAG;QACH,MAAM;MAAA,CACP;IACH;AAEwB,4BAAA,CAAC,SAAS,CAAC,IAAI;EAAA;AAGnC,QAAA,mBAAmB,CAAC,SAAS,aAAa;AAC9C,QAAI,SAAS;AACX,iBAAW,kCAAkC;IAC/C;AAEA,cAAU,QAAQ;EAAA;AAGd,QAAA,mBAAmB,CAAC,UAAU;AAClC,eAAW,+BAA+B;MACxC,UAAU;MACV,MAAM;IAAA,CACP;AACQ,aAAA,EAAE,MAAM,MAAA,CAAO;EAAA;AAGpB,QAAA,mBAAmB,CAAC,WAAW;AACnC,oBAAgB,MAAM;AACtB,4BAAwB,IAAI;EAAA;AAGxB,QAAA,wBAAwB,CAAC,YAAY;AACzC,oBAAgB,IAAI;AACpB,2BAAuB,OAAO;AAE9B,QAAI,uBAAuB,SAAS;AAClC,6BAAuB,QAAQ,MAAA;IACjC;EAAA;AAGI,QAAA,qBAAqB,CAAC,mBAAmB;AAE3C,QAAA,mBAAmB,cACnB,WAAW,WAAW,SAAS,WAAW,WAAW,aACrD,WAAW,WAAW,OAAO,GAC7B;AACS,eAAA;QACP,GAAG;QACH,MAAM,WAAW,WAAW,OAAO;MAAA,CACpC;IACH;EAAA;AAGF,QAAM,0BAA0B,MAAM;AAC1B,cAAA;AAEV,uBAAmB,SAAS,MAAM;EAAA;AAGpC,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAa,CAAA;EACvB;AAEA,MAAI,eAAe,cAAc;AACxB,eAAA,wBAAC,KAAK,OAAL,CAAW,CAAA;EACrB;AAGE,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAC,yBAAA,KAAK,MAAL,EACC,UAAA;UAAA;QAAC;QAAA;UACC,aACE,CAAC,0BAA0B,oBAAoB,eAAe,EAAE,UAAU,MAAA,CAAO;UAEnF;UACA,0BAA0B;UAC1B,2BAA2B;UAC3B,QAAQ;QAAA;MACV;UACA;QAAC,QAAQ;QAAR;UACC,kBAEK,yBAAA,6BAAA,EAAA,UAAA;YAAA,aAAa,eAAe,aAAa,KAAK,cAAc,UAC3D;cAAC;cAAA;gBACC,aAAa;gBACb,cAAc;gBACd,YAAW;gBACX,WAAS;gBACT,aAAY;gBAEZ,cAAA;kBAAC;kBAAA;oBACC,cAAY,cAAc;sBACxB,IAAI,QAAQ,mBAAmB;sBAC/B,gBAAgB;oBAAA,CACjB;oBACD,SACE,0BACI,mBACC,aAAa,KAAK,cAAc,MACjC,SAAS,WAAW,aAAa;oBAEvC,iBAAiB,CAAC,MAAM,iBAAiB,GAAG,CAAC,GAAG,QAAQ,GAAG,OAAO,CAAC;kBAAA;gBACrE;cAAA;YACF;YAED,WAAW,kBACT,wBAAA,YAAA,EAAW,OAAO,+BAAO,MAAM,cAAc,iBAAA,CAAkB;YAEjE,eAAA,wBAAY,SAAQ,CAAA,CAAA;UAAA,EAAA,CACvB;UAEF,gBAEK,yBAAA,6BAAA,EAAA,UAAA;YAAA,uBACE,wBAAA,iBAAA,EAAgB,YAAY,GAAG,eAAe,GAC7C,cAAA;cAAC;cAAA;gBACC,KAAKK;gBACL,IAAI;kBACF,UAAU,GAAG,QAAQ;kBACrB,YAAQ,qBAAU,OAAO,EAAE,QAAQ,MAAA,CAAO;gBAC5C;gBACA,OAAO,cAAc;kBACnB,IAAI;kBACJ,gBAAgB;gBAAA,CACjB;gBAED,cAAA,wBAAC,eAAI,CAAA,CAAA;cAAA;YAAA,EAAA,CAET,IACE;gBACH,wBAAA,iBAAA,EAAgB,YAAY,GAAG,eAAe,GAC7C,cAAA;cAAC;cAAA;gBACC,OACE,aACI,cAAc;kBACZ,IAAI,QAAQ,kBAAkB;kBAC9B,gBAAgB;gBACjB,CAAA,IACD,cAAc;kBACZ,IAAI,QAAQ,kBAAkB;kBAC9B,gBAAgB;gBAAA,CACjB;gBAEP,SAAS,MAAM,QAAQ,aAAa,YAAY,OAAO,YAAY,IAAI;gBAEtE,UAAa,iBAAA,wBAAC,eAAK,CAAA,CAAA,QAAA,wBAAMC,eAAS,CAAA,CAAA;cAAA;YAAA,EAAA,CAEvC;gBACA;cAAC;cAAA;gBACC,OAAO,cAAc;kBACnB,IAAI,QAAQ,cAAc;kBAC1B,gBAAgB;gBAAA,CACjB;gBACD,cAAa;gBACb,qBAAqB,EAAE,UAAU,SAAS;cAAA;YAC5C;UAAA,EAAA,CACF;QAAA;MAEJ;UAEA,yBAAC,QAAQ,SAAR,EACE,UAAA;QAAA,SAAS,SAAS,SACjB;UAAC;UAAA;YACC;YACA;YACA,WAAW;UAAA;QACb;QAGD,gBAAgB,KAAK,eAAe,SACnC;UAAC;UAAA;YACC;YACA;YACA;YACA,eAAe;UAAA;QACjB;QAID,WAAW,CAAC,eAAe,aAAa,KAAK,cAAc,UAC1D;UAAC;UAAA;YACC;YACA;YACA,eAAe;YACf,cAAc;YACd,gBAAgB,CAAC,UAAU,eACzB,SAAS,aAAa,UAAU,OAAO,EAAE,QAAQ,UAAU,WAAY,CAAA,CAAC;YAE1E,aAAa;YACb,cAAc;YACd,aAAa;YACb,aAAa;YACb,MAAM,CAAC,GAAG,SAAS,GAAG,MAAM;YAC5B;YACA,yBAAyB,CAAC;YAC1B,YAAW,+BAAO,SAAQ;UAAA;QAC5B;QAGD,WAAW,kBAEP,yBAAA,6BAAA,EAAA,UAAA;UAAA,cAAc,SACb;YAAC;YAAA;cACC;;;;iBAIK,eAAe,aAAa,KAAM,CAAC,gBACpC;kBACE;oBACE,IAAI,QAAQ,oBAAoB;oBAChC,gBAAgB;kBAClB;kBACA,EAAE,OAAO,YAAY;gBAAA,KAEzB;;cAGD,UAAA,QAAQ,IAAI,CAAC,WAAW;AACjB,sBAAA,kBAAkB,SAAS,OAAO,CAAC,EAAE,KAAA,MAAW,SAAS,QAAQ;AACjE,sBAAA,aAAa,CAAC,CAAC,gBAAgB;kBACnC,CAACC,mBAAkBA,eAAc,OAAO,OAAO;gBAAA;AAG3C,sBAAA,MAAM,aAAa,UAAU,OAAO;kBACxC,QAAQ,iCAAQ;kBAChB,YAAY,iCAAQ;gBAAA,CACrB;AAGC,2BAAA;kBAAC,KAAK;kBAAL;oBACC,KAAK;oBAEL,WAAU;oBACV,YAAW;oBAEX,cAAA;sBAAC;sBAAA;wBACC,KACE,gBAAgB,OAAO,OAAO,aAAa,KACvC,yBACA;wBAEN,WAAW,OAAO;wBAClB,IAAI,UAAU,OAAO,EAAE;wBACvB,IAAI;wBACJ,aACE,aAAa,OAAO,mBAClB;0BAAC;0BAAA;4BACC,eAAa,mBAAmB,OAAO,EAAE;4BACzC,SAAS;4BACT,iBAAiB,MAAM,UAAU,MAAM;0BAAA;wBAAA,IAEvC;wBAEN,iBACE;0BAAC;0BAAA;4BACC,cAAY,cAAc;8BACxB,IAAI,QAAQ,kBAAkB;8BAC9B,gBAAgB;4BAAA,CACjB;4BACD,SAAS,MAAM,iBAAiB,MAAM;4BAEtC,cAAA,wBAAC,eAAO,CAAA,CAAA;0BAAA;wBACV;wBAGF,cAAC,wBAAA,gBAAA,EACC,cAAC,wBAAA,sBAAA,EAAqB,IAAI,KACxB,cAAA,yBAAC,MAAK,EAAA,KAAI,MAAK,WAAU,UAAS,YAAW,SAAQ,UAAS,QAC5D,UAAA;8BAAA;4BAAC;4BAAA;8BACC,YAAW;8BACX,WAAU;8BACV,UAAQ;8BAEP,UAAA;gCAAO,OAAA;oCACR,wBAAC,gBAAA,EAAe,UAAC,IAAA,CAAA;8BAAA;4BAAA;0BACnB;8BAEA;4BAAC;4BAAA;8BACC,KAAI;8BACJ,WAAU;8BACV,SAAQ;8BACR,UAAQ;8BAEP,UAAA;gCACC;kCACE,IAAI,QAAQ,sBAAsB;kCAClC,gBACE;gCACJ;gCACA;kCACE,aAAa,OAAO,SAAS;kCAC7B,YAAY,OAAO,MAAM;gCAC3B;8BACF;4BAAA;0BACF;wBAAA,EACF,CAAA,EACF,CAAA,EAAA,CACF;sBAAA;oBACF;kBAAA;kBAnEK,UAAU,OAAO,EAAE;gBAAA;cAoE1B,CAEH;YAAA;UACH;UAGD,aAAa,KAAK,cAAc,SAC9B,wBAAA,KAAA,EAAI,YAAY,GAAG,eAAe,GACjC,cAAC,wBAAA,SAAA,CAAQ,CAAA,EAAA,CACX;UAGD,aAAa,SACZ;YAAC;YAAA;cACC;cACA,aAAa;cACb,eAAe;cACf,gBAAgB,SAAS,OAAO,CAAC,EAAE,KAAK,MAAM,SAAS,OAAO;cAC9D;;;;;iBAKI,CAAC,eAAgB,eAAe,cAAc,QAC9C,8CAAY,eAAZ,mBAAwB,UAAS,KACjC;kBACE;oBACE,IAAI,QAAQ,mBAAmB;oBAC/B,gBAAgB;kBAClB;kBACA,EAAE,OAAO,gBAAgB;gBAAA,KAE7B;;YAAA;UAEJ;QAAA,EAAA,CAEJ;YAAA,yBAED,WAAW,MAAX,EAAiB,GAAG,WAAW,YAC9B,UAAA;cAAC,wBAAA,WAAW,UAAX,CAAA,CAAoB;cACrB,wBAAC,WAAW,OAAX,CAAA,CAAiB;QAAA,EAAA,CACpB;MAAA,EAAA,CACF;IAAA,EAAA,CACF;IACC,6BACC;MAAC;MAAA;QACC,MAAM;QACN,SAAS;QACT,iBAAgB;QAChB,UAAU,+BAAO;MAAA;IACnB;IAED,4BACC;MAAC;MAAA;QACC,MAAM;QACN,SAAS;QACT,QAAQ;QACR,gBAAgB,+BAAO;QACvB,UAAS;MAAA;IACX;IAED,mBACC;MAAC;MAAA;QACC,SAAS,CAAC,gBAAgB;AAExB,cAAI,gBAAgB,MAAM;AACxB,+BAAmB,CAAC;UACtB;AAEA,yBAAe,MAAS;QAC1B;QACA,MAAM,CAAC,CAAC;QACR,OAAO;QACP;QACA;QACA;QACA,iBAAgB;MAAA;IAClB;EAEJ,EAAA,CAAA;AAEJ;ACvgBA,IAAM,uBAAmB,mBAAK,MAAM,OAAO,8BAAoB,CAAC;AAEhE,IAAM,SAAS,MAAM;AACb,QAAA;IACJ,QAAQ,EAAE,WAAW,SAAS,MAAM,OAAO;EAAA,IACzC,UAAU;AAEd,QAAM,CAAC,EAAE,SAAA,GAAY,QAAQ,IAAI,eAAe;AAC1C,QAAA,EAAE,cAAA,IAAkB,QAAA;AACpB,QAAA,QAAQ,cAAc,EAAE,IAAI,QAAQ,aAAa,GAAG,gBAAgB,gBAAA,CAAiB;AAE3F,8BAAU,MAAM;AACV,QAAA,aAAa,WAAW,UAAU;AACpC;IACF;AACS,aAAA,EAAE,MAAM,OAAO,MAAM,MAAM,GAAG,UAAU,OAAO,SAAA,CAAU;EAAA,GACjE,CAAC,WAAW,SAAS,QAAQ,UAAU,QAAQ,CAAC;AAEnD,MAAI,WAAW;AACb,eAEI,yBAAA,6BAAA,EAAA,UAAA;UAAC,wBAAA,KAAK,OAAL,EAAY,UAAM,MAAA,CAAA;UACnB,wBAAC,KAAK,SAAL,CAAA,CAAa;IAChB,EAAA,CAAA;EAEJ;AAEA,aACG,wBAAA,KAAK,MAAL,EACE,UAAA,eACE,wBAAA,uBAAA,EAAS,cAAU,wBAAC,KAAK,SAAL,CAAA,CAAa,GAChC,cAAA,yBAAC,QACC,EAAA,UAAA;QAAA,wBAAC,OAAA,EAAM,OAAK,MAAC,aAAS,wBAAC,cAAA,CAAa,CAAA,EAAA,CAAI;QACxC,wBAAC,OAAA,EAAM,MAAK,iBAAgB,aAAU,wBAAA,kBAAA,EAAiB,OAAgB,CAAA,EAAA,CAAI;EAC7E,EAAA,CAAA,EACF,CAAA,IACE,KACN,CAAA;AAEJ;", "names": ["PropTypes", "error", "isEmpty", "filters", "Link", "ReactRouterLink", "GridIcon", "currentFolder"]}