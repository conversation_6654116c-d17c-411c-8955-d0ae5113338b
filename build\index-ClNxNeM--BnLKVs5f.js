import{hw as Ge,hL as Ve,hM as G,hN as ee,hO as We,hP as ze,g8 as T,u as Ye,r as f,gT as Ke,hE as Je,hQ as Xe,hR as Ze,hS as es,gp as oe,hF as ss,hT as ts,hU as rs,gV as ns,m as e,n as M,fw as is,fx as H,ba as ce,a as U,H as as,ay as os,b as I,e as cs,P as w,f as le,fq as ls,d as ds,g as W,L as k,bK as us,h as O,b0 as hs,_ as de,b1 as gs,T as ps,o as ms,p as ue,q as $,s as b,V as he,al as ge,t as fs,v as N,w as y,S as B,Y as bs,F as xs,I as js,b3 as ys,bM as vs,b5 as Rs,b6 as Cs,c as pe,eY as V,aX as me,A as fe,N as be,G as S,x as v,z as xe,af as je,gr as Q,g7 as se,gG as D,D as Ms,J as P,a6 as te,hb as Ss,ej as Os,i as ws,l as re,k as ne,at as Es,bh as ks,E as x}from"./strapi-YzJfjJ2z.js";import{F as ye,a as ve}from"./formik.esm-C9nD4K1V.js";import{u as E}from"./index-dwdMDrmv.js";import{s as ie}from"./sortBy-vMk_GNbv.js";import{f as Re}from"./immer.esm-DiDhQEzX.js";import{_ as Ps}from"./_arrayIncludesWith-BNzMLSv9.js";import{t as Ls,m as As}from"./tail-B0SLYYv9.js";import"./_baseMap-LzQFtWYw.js";import"./_baseEach-BkkNIx9z.js";var Ts=function(s){Ge(t,s);function t(n,a){var r;return r=s.call(this)||this,r.client=n,r.queries=[],r.result=[],r.observers=[],r.observersMap={},a&&r.setQueries(a),r}var i=t.prototype;return i.onSubscribe=function(){var a=this;this.listeners.length===1&&this.observers.forEach(function(r){r.subscribe(function(o){a.onUpdate(r,o)})})},i.onUnsubscribe=function(){this.listeners.length||this.destroy()},i.destroy=function(){this.listeners=[],this.observers.forEach(function(a){a.destroy()})},i.setQueries=function(a,r){this.queries=a,this.updateObservers(r)},i.getCurrentResult=function(){return this.result},i.getOptimisticResult=function(a){return this.findMatchingObservers(a).map(function(r){return r.observer.getOptimisticResult(r.defaultedQueryOptions)})},i.findMatchingObservers=function(a){var r=this,o=this.observers,l=a.map(function(h){return r.client.defaultQueryObserverOptions(h)}),d=l.flatMap(function(h){var j=o.find(function(R){return R.options.queryHash===h.queryHash});return j!=null?[{defaultedQueryOptions:h,observer:j}]:[]}),p=d.map(function(h){return h.defaultedQueryOptions.queryHash}),g=l.filter(function(h){return!p.includes(h.queryHash)}),u=o.filter(function(h){return!d.some(function(j){return j.observer===h})}),c=g.map(function(h,j){if(h.keepPreviousData){var R=u[j];if(R!==void 0)return{defaultedQueryOptions:h,observer:R}}return{defaultedQueryOptions:h,observer:r.getObserver(h)}}),m=function(j,R){return l.indexOf(j.defaultedQueryOptions)-l.indexOf(R.defaultedQueryOptions)};return d.concat(c).sort(m)},i.getObserver=function(a){var r=this.client.defaultQueryObserverOptions(a),o=this.observersMap[r.queryHash];return o??new Ve(this.client,r)},i.updateObservers=function(a){var r=this;G.batch(function(){var o=r.observers,l=r.findMatchingObservers(r.queries);l.forEach(function(c){return c.observer.setOptions(c.defaultedQueryOptions,a)});var d=l.map(function(c){return c.observer}),p=Object.fromEntries(d.map(function(c){return[c.options.queryHash,c]})),g=d.map(function(c){return c.getCurrentResult()}),u=d.some(function(c,m){return c!==o[m]});o.length===d.length&&!u||(r.observers=d,r.observersMap=p,r.result=g,r.hasListeners()&&(ee(o,d).forEach(function(c){c.destroy()}),ee(d,o).forEach(function(c){c.subscribe(function(m){r.onUpdate(c,m)})}),r.notify()))})},i.onUpdate=function(a,r){var o=this.observers.indexOf(a);o!==-1&&(this.result=We(this.result,o,r),this.notify())},i.notify=function(){var a=this;G.batch(function(){a.listeners.forEach(function(r){r(a.result)})})},t}(ze);function Ds(s){var t=T.useRef(!1),i=T.useState(0),n=i[1],a=Ye(),r=f.useMemo(function(){return s.map(function(p){var g=a.defaultQueryObserverOptions(p);return g.optimisticResults=!0,g})},[s,a]),o=T.useState(function(){return new Ts(a,r)}),l=o[0],d=l.getOptimisticResult(r);return T.useEffect(function(){t.current=!0;var p=l.subscribe(G.batchCalls(function(){t.current&&n(function(g){return g+1})}));return function(){t.current=!1,p()}},[l]),T.useEffect(function(){l.setQueries(r,{listeners:!1})},[r,l]),d}var Is=Xe,_s=Ze,qs=Ps,$s=Ke,Ns=Je,Qs=es,Us=200;function Bs(s,t,i,n){var a=-1,r=_s,o=!0,l=s.length,d=[],p=t.length;if(!l)return d;i&&(t=$s(t,Ns(i))),n?(r=qs,o=!1):t.length>=Us&&(r=Qs,o=!1,t=new Is(t));e:for(;++a<l;){var g=s[a],u=i==null?g:i(g);if(g=n||g!==0?g:0,o&&u===u){for(var c=p;c--;)if(t[c]===u)continue e;d.push(g)}else r(t,u,n)||d.push(g)}return d}var Fs=Bs,Hs=Fs,Gs=ss,Vs=ts,Ws=Gs(function(s,t){return Vs(s)?Hs(s,t):[]}),zs=Ws;const Ys=oe(zs);var Ks=ns,Js=rs;function Xs(s,t,i){return s&&s.length?(t=i||t===void 0?1:Js(t),Ks(s,0,t<0?0:t)):[]}var Zs=Xs;const et=oe(Zs),Ce=f.createContext({}),Me=({children:s,value:t})=>e.jsx(Ce.Provider,{value:t,children:s}),z=()=>f.useContext(Ce);Me.propTypes={children:x.node.isRequired,value:x.object.isRequired};function st(s){switch(s){case"application":return"Application";case"plugin::content-manager":return"Content manager";case"plugin::content-type-builder":return"Content types builder";case"plugin::documentation":return"Documentation";case"plugin::email":return"Email";case"plugin::i18n":return"i18n";case"plugin::upload":return"Upload";case"plugin::users-permissions":return"Users-permissions";default:return Ms(s.replace("api::","").replace("plugin::",""))}}const tt=(s,t)=>{const i=Object.keys(t).sort().map(n=>({name:n,isOpen:!1}));return{...s,collapses:i}},ae=Ss`
  background: ${s=>s.theme.colors.primary100};

  #cog {
    opacity: 1;
  }
`,rt=B(P)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  #cog {
    opacity: 0;
    path {
      fill: ${s=>s.theme.colors.primary600};
    }
  }

  /* Show active style both on hover and when the action is selected */
  ${s=>s.isActive&&ae}
  &:hover {
    ${ae}
  }
`,nt=B.div`
  flex: 1;
  align-self: center;
  border-top: 1px solid ${({theme:s})=>s.colors.neutral150};
`,Se=({subCategory:s})=>{const{formatMessage:t}=E(),{onChange:i,onChangeSelectAll:n,onSelectedAction:a,selectedAction:r,modifiedData:o}=z(),l=f.useMemo(()=>Q(o,s.name,{}),[o,s]),d=f.useMemo(()=>Object.values(l).every(c=>c.enabled===!0),[l]),p=f.useMemo(()=>Object.values(l).some(c=>c.enabled===!0)&&!d,[l,d]),g=f.useCallback(({target:{name:c}})=>{n({target:{name:c,value:!d}})},[d,n]),u=f.useCallback(c=>r===c,[r]);return e.jsxs(P,{children:[e.jsxs(y,{justifyContent:"space-between",alignItems:"center",children:[e.jsx(P,{paddingRight:4,children:e.jsx(b,{variant:"sigma",textColor:"neutral600",children:s.label})}),e.jsx(nt,{}),e.jsx(P,{paddingLeft:4,children:e.jsx(te,{name:s.name,checked:p?"indeterminate":d,onCheckedChange:c=>g({target:{name:s.name,value:c}}),children:t({id:"app.utils.select-all",defaultMessage:"Select all"})})})]}),e.jsx(y,{paddingTop:6,paddingBottom:6,children:e.jsx(S.Root,{gap:2,style:{flex:1},children:s.actions.map(c=>{const m=`${c.name}.enabled`;return e.jsx(S.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(rt,{isActive:u(c.name),padding:2,hasRadius:!0,children:[e.jsx(te,{checked:Q(o,m,!1),name:m,onCheckedChange:h=>i({target:{name:m,value:h}}),children:c.label}),e.jsxs("button",{type:"button",onClick:()=>a(c.name),style:{display:"inline-flex",alignItems:"center"},children:[e.jsx(he,{tag:"span",children:t({id:"app.utils.show-bound-route",defaultMessage:"Show bound route for {route}"},{route:c.name})}),e.jsx(Os,{id:"cog"})]})]})},c.name)})})})]})};Se.propTypes={subCategory:x.object.isRequired};const Oe=({name:s,permissions:t})=>{const i=f.useMemo(()=>ie(Object.values(t.controllers).reduce((n,a,r)=>{const o=`${s}.controllers.${Object.keys(t.controllers)[r]}`,l=ie(Object.keys(a).reduce((d,p)=>[...d,{...a[p],label:p,name:`${o}.${p}`}],[]),"label");return[...n,{actions:l,label:Object.keys(t.controllers)[r],name:o}]},[]),"label"),[s,t]);return e.jsx(P,{padding:6,children:i.map(n=>e.jsx(Se,{subCategory:n},n.name))})};Oe.propTypes={name:x.string.isRequired,permissions:x.object.isRequired};const it={collapses:[]},at=(s,t)=>Re(s,i=>{switch(t.type){case"TOGGLE_COLLAPSE":{i.collapses=s.collapses.map((n,a)=>a===t.index?{...n,isOpen:!n.isOpen}:{...n,isOpen:!1});break}default:return i}}),ot=()=>{const{modifiedData:s}=z(),{formatMessage:t}=E(),[{collapses:i}]=f.useReducer(at,it,n=>tt(n,s));return e.jsx(D.Root,{size:"M",children:e.jsx(y,{direction:"column",alignItems:"stretch",gap:1,children:i.map((n,a)=>e.jsxs(D.Item,{value:n.name,children:[e.jsx(D.Header,{variant:a%2===0?"secondary":void 0,children:e.jsx(D.Trigger,{caretPosition:"right",description:t({id:"users-permissions.Plugin.permissions.plugins.description",defaultMessage:"Define all allowed actions for the {name} plugin."},{name:n.name}),children:st(n.name)})}),e.jsx(D.Content,{children:e.jsx(Oe,{permissions:s[n.name],name:n.name})})]},n.name))})})},ct=s=>{switch(s){case"POST":return{text:"success600",border:"success200",background:"success100"};case"GET":return{text:"secondary600",border:"secondary200",background:"secondary100"};case"PUT":return{text:"warning600",border:"warning200",background:"warning100"};case"DELETE":return{text:"danger600",border:"danger200",background:"danger100"};default:return{text:"neutral600",border:"neutral200",background:"neutral100"}}},lt=B(P)`
  margin: -1px;
  border-radius: ${({theme:s})=>s.spaces[1]} 0 0 ${({theme:s})=>s.spaces[1]};
`;function Y({route:s}){const{formatMessage:t}=E(),{method:i,handler:n,path:a}=s,r=a?Ls(a.split("/")):[],[o="",l=""]=n?n.split("."):[],d=ct(s.method);return e.jsxs(y,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsxs(b,{variant:"delta",tag:"h3",children:[t({id:"users-permissions.BoundRoute.title",defaultMessage:"Bound route to"})," ",e.jsx("span",{children:o}),e.jsxs(b,{variant:"delta",textColor:"primary600",children:[".",l]})]}),e.jsxs(y,{hasRadius:!0,background:"neutral0",borderColor:"neutral200",gap:0,children:[e.jsx(lt,{background:d.background,borderColor:d.border,padding:2,children:e.jsx(b,{fontWeight:"bold",textColor:d.text,children:i})}),e.jsx(P,{paddingLeft:2,paddingRight:2,children:As(r,p=>e.jsxs(b,{textColor:p.includes(":")?"neutral600":"neutral900",children:["/",p]},p))})]})]})}Y.defaultProps={route:{handler:"Nocontroller.error",method:"GET",path:"/there-is-no-path"}};Y.propTypes={route:x.shape({handler:x.string,method:x.string,path:x.string})};const dt=()=>{const{formatMessage:s}=E(),{selectedAction:t,routes:i}=z(),n=Ys(t.split("."),"controllers"),a=Q(i,n[0]),r=n.slice(1).join("."),o=V(a)?[]:a.filter(l=>l.handler.endsWith(r));return e.jsx(S.Item,{col:5,background:"neutral150",paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,style:{minHeight:"100%"},direction:"column",alignItems:"stretch",children:t?e.jsx(y,{direction:"column",alignItems:"stretch",gap:2,children:o.map((l,d)=>e.jsx(Y,{route:l},d))}):e.jsxs(y,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsx(b,{variant:"delta",tag:"h3",children:s({id:"users-permissions.Policies.header.title",defaultMessage:"Advanced settings"})}),e.jsx(b,{tag:"p",textColor:"neutral600",children:s({id:"users-permissions.Policies.header.hint",defaultMessage:"Select the application's actions or the plugin's actions and click on the cog icon to display the bound route"})})]})})},ut=(s,t,i)=>({...s,initialData:t,modifiedData:t,routes:i}),ht={initialData:{},modifiedData:{},routes:{},selectedAction:"",policies:[]},gt=(s,t)=>Re(s,i=>{switch(t.type){case"ON_CHANGE":{const n=t.keys.length,a=t.keys[n-1]==="enabled";if(t.value&&a){const r=et(t.keys,n-1).join(".");i.selectedAction=r}se(i,["modifiedData",...t.keys],t.value);break}case"ON_CHANGE_SELECT_ALL":{const n=["modifiedData",...t.keys],a=Q(s,n,{}),r=Object.keys(a).reduce((o,l)=>(o[l]={...a[l],enabled:t.value},o),{});se(i,n,r);break}case"ON_RESET":{i.modifiedData=s.initialData;break}case"ON_SUBMIT_SUCCEEDED":{i.initialData=s.modifiedData;break}case"SELECT_ACTION":{const{actionToSelect:n}=t;i.selectedAction=n===s.selectedAction?"":n;break}default:return i}}),we=f.forwardRef(({permissions:s,routes:t},i)=>{const{formatMessage:n}=E(),[a,r]=f.useReducer(gt,ht,g=>ut(g,s,t));f.useImperativeHandle(i,()=>({getPermissions(){return{permissions:a.modifiedData}},resetForm(){r({type:"ON_RESET"})},setFormAfterSubmit(){r({type:"ON_SUBMIT_SUCCEEDED"})}}));const p={...a,onChange:({target:{name:g,value:u}})=>r({type:"ON_CHANGE",keys:g.split("."),value:u==="empty__string_value"?"":u}),onChangeSelectAll:({target:{name:g,value:u}})=>r({type:"ON_CHANGE_SELECT_ALL",keys:g.split("."),value:u}),onSelectedAction:g=>r({type:"SELECT_ACTION",actionToSelect:g})};return e.jsx(Me,{value:p,children:e.jsxs(S.Root,{gap:0,shadow:"filterShadow",hasRadius:!0,background:"neutral0",children:[e.jsx(S.Item,{col:7,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,direction:"column",alignItems:"stretch",children:e.jsxs(y,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsxs(y,{direction:"column",alignItems:"stretch",gap:2,children:[e.jsx(b,{variant:"delta",tag:"h2",children:n({id:O("Plugins.header.title"),defaultMessage:"Permissions"})}),e.jsx(b,{tag:"p",textColor:"neutral600",children:n({id:O("Plugins.header.description"),defaultMessage:"Only actions bound by a route are listed below."})})]}),e.jsx(ot,{})]})}),e.jsx(dt,{})]})})});we.propTypes={permissions:x.object.isRequired,routes:x.object.isRequired};const Ee=f.memo(we),ke=ws().shape({name:re().required(ne.required.id),description:re().required(ne.required.id)}),pt=s=>Object.keys(s).reduce((t,i)=>{const n=s[i].controllers,a=Object.keys(n).reduce((r,o)=>(V(n[o])||(r[o]=n[o]),r),{});return V(a)||(t[i]={controllers:a}),t},{}),Pe=()=>{const{toggleNotification:s}=U(),{get:t}=I(),{formatAPIError:i}=pe(O),[{data:n,isLoading:a,error:r,refetch:o},{data:l,isLoading:d,error:p,refetch:g}]=Ds([{queryKey:["users-permissions","permissions"],async queryFn(){const{data:{permissions:m}}=await t("/users-permissions/permissions");return m}},{queryKey:["users-permissions","routes"],async queryFn(){const{data:{routes:m}}=await t("/users-permissions/routes");return m}}]),u=async()=>{await Promise.all([o(),g()])};f.useEffect(()=>{r&&s({type:"danger",message:i(r)})},[s,r,i]),f.useEffect(()=>{p&&s({type:"danger",message:i(p)})},[s,p,i]);const c=a||d;return{permissions:n?pt(n):{},routes:l??{},getData:u,isLoading:c}},mt=()=>{const{formatMessage:s}=E(),{toggleNotification:t}=U(),i=ge(),{isLoading:n,permissions:a,routes:r}=Pe(),{trackUsage:o}=ce(),l=f.useRef(),{post:d}=I(),p=W(u=>d("/users-permissions/roles",u),{onError(){t({type:"danger",message:s({id:"notification.error",defaultMessage:"An error occurred"})})},onSuccess(){o("didCreateRole"),t({type:"success",message:s({id:O("Settings.roles.created"),defaultMessage:"Role created"})}),i(-1)}}),g=async u=>{const c=l.current.getPermissions();await p.mutate({...u,...c,users:[]})};return e.jsxs(me,{children:[e.jsx(M.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(ye,{enableReinitialize:!0,initialValues:{name:"",description:""},onSubmit:g,validationSchema:ke,children:({handleSubmit:u,values:c,handleChange:m,errors:h})=>e.jsxs(ve,{noValidate:!0,onSubmit:u,children:[e.jsx(k.Header,{primaryAction:!n&&e.jsx(fe,{type:"submit",loading:p.isLoading,startIcon:e.jsx(be,{}),children:s({id:"global.save",defaultMessage:"Save"})}),title:s({id:"Settings.roles.create.title",defaultMessage:"Create a role"}),subtitle:s({id:"Settings.roles.create.description",defaultMessage:"Define the rights given to the role"})}),e.jsx(k.Content,{children:e.jsxs(y,{background:"neutral0",direction:"column",alignItems:"stretch",gap:7,hasRadius:!0,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,shadow:"filterShadow",children:[e.jsxs(y,{direction:"column",alignItems:"stretch",children:[e.jsx(b,{variant:"delta",tag:"h2",children:s({id:O("EditPage.form.roles"),defaultMessage:"Role details"})}),e.jsxs(S.Root,{gap:4,children:[e.jsx(S.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(v.Root,{name:"name",error:h?.name?s({id:h.name,defaultMessage:"Name is required"}):!1,required:!0,children:[e.jsx(v.Label,{children:s({id:"global.name",defaultMessage:"Name"})}),e.jsx(xe,{value:c.name||"",onChange:m}),e.jsx(v.Error,{})]})}),e.jsx(S.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(v.Root,{name:"description",error:h?.description?s({id:h.description,defaultMessage:"Description is required"}):!1,required:!0,children:[e.jsx(v.Label,{children:s({id:"global.description",defaultMessage:"Description"})}),e.jsx(je,{value:c.description||"",onChange:m}),e.jsx(v.Error,{})]})})]})]}),!n&&e.jsx(Ee,{ref:l,permissions:a,routes:r})]})})]})})]})},ft=()=>e.jsx(M.Protect,{permissions:w.createRole,children:e.jsx(mt,{})}),bt=()=>{const{formatMessage:s}=E(),{toggleNotification:t}=U(),{params:{id:i}}=Es("/settings/users-permissions/roles/:id"),{get:n}=I(),{isLoading:a,routes:r}=Pe(),{data:o,isLoading:l,refetch:d}=le(["users-permissions","role",i],async()=>{const{data:{role:h}}=await n(`/users-permissions/roles/${i}`);return h}),p=f.useRef(),{put:g}=I(),{formatAPIError:u}=pe(),c=W(h=>g(`/users-permissions/roles/${i}`,h),{onError(h){t({type:"danger",message:u(h)})},async onSuccess(){t({type:"success",message:s({id:O("Settings.roles.created"),defaultMessage:"Role edited"})}),await d()}}),m=async h=>{const j=p.current.getPermissions();await c.mutate({...h,...j,users:[]})};return l?e.jsx(M.Loading,{}):e.jsxs(me,{children:[e.jsx(M.Title,{children:s({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(ye,{enableReinitialize:!0,initialValues:{name:o.name,description:o.description},onSubmit:m,validationSchema:ke,children:({handleSubmit:h,values:j,handleChange:R,errors:L})=>e.jsxs(ve,{noValidate:!0,onSubmit:h,children:[e.jsx(k.Header,{primaryAction:a?null:e.jsx(fe,{disabled:o.code==="strapi-super-admin",type:"submit",loading:c.isLoading,startIcon:e.jsx(be,{}),children:s({id:"global.save",defaultMessage:"Save"})}),title:o.name,subtitle:o.description,navigationAction:e.jsx(ks,{})}),e.jsx(k.Content,{children:e.jsxs(y,{background:"neutral0",direction:"column",alignItems:"stretch",gap:7,hasRadius:!0,paddingTop:6,paddingBottom:6,paddingLeft:7,paddingRight:7,shadow:"filterShadow",children:[e.jsxs(y,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsx(b,{variant:"delta",tag:"h2",children:s({id:O("EditPage.form.roles"),defaultMessage:"Role details"})}),e.jsxs(S.Root,{gap:4,children:[e.jsx(S.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(v.Root,{name:"name",error:L?.name?s({id:L.name,defaultMessage:"Name is required"}):!1,required:!0,children:[e.jsx(v.Label,{children:s({id:"global.name",defaultMessage:"Name"})}),e.jsx(xe,{value:j.name||"",onChange:R}),e.jsx(v.Error,{})]})}),e.jsx(S.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(v.Root,{name:"description",error:L?.description?s({id:L.description,defaultMessage:"Description is required"}):!1,required:!0,children:[e.jsx(v.Label,{children:s({id:"global.description",defaultMessage:"Description"})}),e.jsx(je,{value:j.description||"",onChange:R}),e.jsx(v.Error,{})]})})]})]}),!a&&e.jsx(Ee,{ref:p,permissions:o.permissions,routes:r})]})})]})})]})},xt=()=>e.jsx(M.Protect,{permissions:w.updateRole,children:e.jsx(bt,{})}),jt=B(bs)`
  align-items: center;
  height: 3.2rem;
  width: 3.2rem;
  display: flex;
  justify-content: center;
  padding: ${({theme:s})=>`${s.spaces[2]}`};

  svg {
    height: 1.6rem;
    width: 1.6rem;

    path {
      fill: ${({theme:s})=>s.colors.neutral500};
    }
  }

  &:hover,
  &:focus {
    svg {
      path {
        fill: ${({theme:s})=>s.colors.neutral800};
      }
    }
  }
`,K=({sortedRoles:s,canDelete:t,canUpdate:i,setRoleToDelete:n,onDelete:a})=>{const{formatMessage:r}=E(),o=ge(),[l,d]=a,p=u=>t&&!["public","authenticated"].includes(u.type),g=u=>{n(u),d(!l)};return e.jsx(fs,{children:s?.map(u=>e.jsxs(ue,{onClick:()=>o(u.id.toString()),children:[e.jsx(N,{width:"20%",children:e.jsx(b,{children:u.name})}),e.jsx(N,{width:"50%",children:e.jsx(b,{children:u.description})}),e.jsx(N,{width:"30%",children:e.jsx(b,{children:r({id:"Roles.RoleRow.user-count",defaultMessage:"{number, plural, =0 {# user} one {# user} other {# users}}"},{number:u.nb_users})})}),e.jsx(N,{children:e.jsxs(y,{justifyContent:"end",onClick:c=>c.stopPropagation(),children:[i?e.jsx(jt,{tag:de,to:u.id.toString(),"aria-label":r({id:"app.component.table.edit",defaultMessage:"Edit {target}"},{target:`${u.name}`}),children:e.jsx(xs,{})}):null,p(u)&&e.jsx(js,{onClick:()=>g(u.id.toString()),variant:"ghost",label:r({id:"global.delete-target",defaultMessage:"Delete {target}"},{target:`${u.name}`}),children:e.jsx(ys,{})})]})})]},u.name))})};K.defaultProps={canDelete:!1,canUpdate:!1};K.propTypes={onDelete:x.array.isRequired,setRoleToDelete:x.func.isRequired,sortedRoles:x.array.isRequired,canDelete:x.bool,canUpdate:x.bool};const yt=()=>{const{trackUsage:s}=ce(),{formatMessage:t,locale:i}=E(),{toggleNotification:n}=U(),{notifyStatus:a}=as(),[{query:r}]=os(),o=r?._q||"",[l,d]=f.useState(!1),[p,g]=f.useState(),{del:u,get:c}=I(),{isLoading:m,allowedActions:{canRead:h,canDelete:j,canCreate:R,canUpdate:L}}=cs({create:w.createRole,read:w.readRoles,update:w.updateRole,delete:w.deleteRole}),{isLoading:Le,data:{roles:J},isFetching:Ae,refetch:Te}=le("get-roles",()=>qe(n,t,a),{initialData:{},enabled:h}),{contains:X}=ls(i,{sensitivity:"base"}),Z=ds(i,{sensitivity:"base"}),De=Le||Ae||m,Ie=()=>{d(!l)},_e=async(C,A,F)=>{try{await u(`/users-permissions/roles/${C}`)}catch{F({type:"danger",message:A({id:"notification.error",defaultMessage:"An error occured"})})}},qe=async(C,A,F)=>{try{const{data:q}=await c("/users-permissions/roles");return F("The roles have loaded successfully"),q}catch(q){throw C({type:"danger",message:A({id:"notification.error",defaultMessage:"An error occurred"})}),new Error(q)}},$e={roles:{id:O("Roles.empty"),defaultMessage:"You don't have any roles yet."},search:{id:O("Roles.empty.search"),defaultMessage:"No roles match the search."}},Ne=t({id:"global.roles",defaultMessage:"Roles"}),Qe=W(C=>_e(C,t,n),{async onSuccess(){await Te()}}),Ue=async()=>{await Qe.mutateAsync(p),d(!l)},_=(J||[]).filter(C=>X(C.name,o)||X(C.description,o)).sort((C,A)=>Z.compare(C.name,A.name)||Z.compare(C.description,A.description)),Be=o&&!_.length?"search":"roles",Fe=4,He=(J?.length||0)+1;return De?e.jsx(M.Loading,{}):e.jsxs(k.Root,{children:[e.jsx(M.Title,{children:t({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:Ne})}),e.jsxs(M.Main,{children:[e.jsx(k.Header,{title:t({id:"global.roles",defaultMessage:"Roles"}),subtitle:t({id:"Settings.roles.list.description",defaultMessage:"List of roles"}),primaryAction:R?e.jsx(us,{to:"new",tag:de,onClick:()=>s("willCreateRole"),startIcon:e.jsx(hs,{}),size:"S",children:t({id:O("List.button.roles"),defaultMessage:"Add new role"})}):null}),e.jsx(k.Action,{startActions:e.jsx(gs,{label:t({id:"app.component.search.label",defaultMessage:"Search"})})}),e.jsxs(k.Content,{children:[!h&&e.jsx(M.NoPermissions,{}),h&&_&&_?.length?e.jsxs(ps,{colCount:Fe,rowCount:He,children:[e.jsx(ms,{children:e.jsxs(ue,{children:[e.jsx($,{children:e.jsx(b,{variant:"sigma",textColor:"neutral600",children:t({id:"global.name",defaultMessage:"Name"})})}),e.jsx($,{children:e.jsx(b,{variant:"sigma",textColor:"neutral600",children:t({id:"global.description",defaultMessage:"Description"})})}),e.jsx($,{children:e.jsx(b,{variant:"sigma",textColor:"neutral600",children:t({id:"global.users",defaultMessage:"Users"})})}),e.jsx($,{children:e.jsx(he,{children:t({id:"global.actions",defaultMessage:"Actions"})})})]})}),e.jsx(K,{sortedRoles:_,canDelete:j,canUpdate:L,permissions:w,setRoleToDelete:g,onDelete:[l,d]})]}):e.jsx(vs,{content:t($e[Be])})]}),e.jsx(Rs.Root,{open:l,onOpenChange:Ie,children:e.jsx(Cs,{onConfirm:Ue})})]})]})},vt=()=>e.jsx(M.Protect,{permissions:w.accessRoles,children:e.jsx(yt,{})}),Lt=()=>e.jsx(M.Protect,{permissions:w.accessRoles,children:e.jsxs(is,{children:[e.jsx(H,{index:!0,element:e.jsx(vt,{})}),e.jsx(H,{path:"new",element:e.jsx(ft,{})}),e.jsx(H,{path:":id",element:e.jsx(xt,{})})]})});export{Lt as default};
