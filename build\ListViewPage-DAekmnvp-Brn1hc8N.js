import{ba as W,al as me,cG as A,a as ge,c as fe,eK as w,cN as K,cI as se,r as S,a1 as he,ay as $,cK as xe,bl as D,eO as je,cO as ye,c9 as be,eP as Ce,m as e,n as F,S as Y,L as Q,bh as Se,A as Le,b0 as Te,aU as Ie,b1 as Me,bV as Re,eQ as Ae,eR as ne,az as ae,cJ as Ee,d as ie,eS as Fe,cR as J,aC as ke,aD as we,aE as Oe,aF as N,eT as Pe,e as re,aj as q,I as ve,ej as Be,w as k,bK as De,_ as $e,dN as He,eU as Ue,s as L,bI as Ne,a6 as Ve,eV as We,aG as y,eW as _e,eX as qe,cQ as ze,eY as z,aB as Ge,eZ as Qe,aT as Z,e_ as M,e$ as Ke,H as Ye,aI as Je,f0 as oe,f1 as Ze,aH as G,b9 as Xe,cE as et,cF as tt}from"./strapi-YzJfjJ2z.js";import{u as le,a as st,p as X}from"./useDebounce-DmuSJIF3-BaTG1oIL.js";import{g as ce,u as nt}from"./relations-sRERvWmr-drnKn_J5.js";import{u as at}from"./hooks-E5u1mcgM-Ce79Lxm5.js";const ee=["json","component","media","richtext","dynamiczone","password","blocks"],it=["createdAt","updatedAt"],rt=[...ne,"strapi_assignee"],ot=({disabled:t,schema:n})=>{const{attributes:s,uid:a,options:o}=n,{formatMessage:l,locale:x}=A(),{trackUsage:f}=W(),r=Re("FiltersImpl",c=>c.permissions),[{query:h}]=$(),{schemas:g}=Ae(),T=S.useMemo(()=>r.filter(c=>c.action==="admin::users.read"&&c.subject===null).length>0,[r]),I=(h?.filters?.$and??[]).reduce((c,E)=>{const[b,m]=Object.entries(E)[0];if(typeof m.id!="object")return c;const R=m.id.$eq||m.id.$ne;return R&&rt.includes(b)&&!c.includes(R)&&c.push(R),c},[]),{data:j,isLoading:u}=ae({filters:{id:{$in:I}}},{skip:I.length===0||!T}),{users:d=[]}=j??{},{metadata:O}=Ee(a,{selectFromResult:({data:c})=>({metadata:c?.contentType.metadatas??{}})}),H=ie(x,{sensitivity:"base"}),P=S.useMemo(()=>{const[{properties:{fields:c=[]}={fields:[]}}]=r.filter(b=>b.action==="plugin::content-manager.explorer.read"&&b.subject===a);return["id",...c.filter(b=>{const m=s[b]??{};return m.type&&!ee.includes(m.type)}),...it,...T?ne:[]].map(b=>{const m=s[b];if(ee.includes(m.type))return null;const{mainField:R="",label:_}=O[b].list;let i={name:b,label:_??"",mainField:Fe(m,R,{schemas:g,components:{}}),type:m.type};return m.type==="relation"&&"target"in m&&m.target==="admin::user"&&(i={...i,input:lt,options:d.map(C=>({label:J(C),value:C.id.toString()})),operators:[{label:l({id:"components.FilterOptions.FILTER_TYPES.$eq",defaultMessage:"is"}),value:"$eq"},{label:l({id:"components.FilterOptions.FILTER_TYPES.$ne",defaultMessage:"is not"}),value:"$ne"}],mainField:{name:"id",type:"integer"}}),m.type==="enumeration"&&(i={...i,options:m.enum.map(C=>({label:C,value:C}))}),i}).filter(Boolean).toSorted((b,m)=>H.compare(b.label,m.label))},[r,T,a,s,O,g,d,l,H]),U=c=>{c&&f("willFilterEntries")},v=c=>{const E=s[c.name];E&&f("didFilterEntries",{useRelation:E.type==="relation"})};return e.jsxs(N.Root,{disabled:t,options:P,onOpenChange:U,onChange:v,children:[e.jsx(N.Trigger,{}),e.jsx(N.Popover,{}),e.jsx(N.List,{})]})},lt=({name:t})=>{const[n,s]=S.useState(10),[a,o]=S.useState(""),{formatMessage:l}=A(),x=st(a,300),{data:f,isLoading:r}=ae({pageSize:n,_q:x}),h=ke(t),g=d=>{d||s(10)},{users:T=[],pagination:I}=f??{},{pageCount:j=1,page:u=1}=I??{};return e.jsx(we,{value:h.value,"aria-label":l({id:"content-manager.components.Filters.usersSelect.label",defaultMessage:"Search and select a user to filter"}),onOpenChange:g,onChange:d=>h.onChange(t,d),loading:r,onLoadMore:()=>s(n+10),hasMoreItems:u<j,onInputChange:d=>{o(d.currentTarget.value)},children:T.map(d=>e.jsx(Oe,{value:d.id.toString(),children:J(d)},d.id))})},V=({type:t,value:n})=>{const{formatDate:s,formatTime:a,formatNumber:o}=A();let l=n;if(t==="date"&&(l=s(Ge(n),{dateStyle:"full"})),t==="datetime"&&(l=s(n,{dateStyle:"full",timeStyle:"short"})),t==="time"){const[x,f,r]=n.split(":"),h=new Date;h.setHours(x),h.setMinutes(f),h.setSeconds(r),l=a(h,{timeStyle:"short"})}return["float","decimal"].includes(t)&&(l=o(n,{maximumFractionDigits:20})),["integer","biginteger"].includes(t)&&(l=o(n,{maximumFractionDigits:0})),Qe(l)},ct=({content:t,mainField:n})=>n?e.jsx(Z,{label:t[n.name],children:e.jsx(L,{maxWidth:"25rem",textColor:"neutral800",ellipsis:!0,children:e.jsx(V,{type:n.type,value:t[n.name]})})}):null,ut=({content:t,mainField:n})=>{const{formatMessage:s}=A();return n?e.jsxs(M.Root,{children:[e.jsxs(M.Trigger,{onClick:a=>a.stopPropagation(),children:[e.jsx(Ke,{children:t.length}),s({id:"content-manager.containers.list.items",defaultMessage:"{number, plural, =0 {items} one {item} other {items}}"},{number:t.length})]}),e.jsx(M.Content,{children:t.map(a=>e.jsx(M.Item,{disabled:!0,children:e.jsx(L,{maxWidth:"50rem",ellipsis:!0,children:e.jsx(V,{type:n.type,value:a[n.name]})})},a.id))})]}):null},dt=t=>t&&t[0]==="."?t.substring(1):t,ue=({url:t,mime:n,alternativeText:s,name:a,ext:o,formats:l})=>{const x=X(t);if(n.includes("image")){const h=l?.thumbnail?.url,g=X(h)||x;return e.jsx(oe.Item,{src:g,alt:s||a,fallback:s||a,preview:!0})}const f=dt(o),r=a.length>100?`${a.substring(0,100)}...`:a;return e.jsx(Z,{description:r,children:e.jsx(de,{children:f})})},de=({children:t})=>e.jsx(k,{tag:"span",position:"relative",borderRadius:"50%",width:"26px",height:"26px",borderColor:"neutral200",background:"neutral150",paddingLeft:"1px",justifyContent:"center",alignItems:"center",children:e.jsx(pt,{variant:"sigma",textColor:"neutral600",children:t})}),pt=Y(L)`
  font-size: 0.9rem;
  line-height: 0.9rem;
`,mt=({content:t})=>e.jsx(oe.Group,{children:t.map((n,s)=>{const a=`${n.id}${s}`;if(s===3){const o=`+${t.length-3}`;return e.jsx(de,{children:o},a)}return s>3?null:e.jsx(ue,{...n},a)})}),gt=({mainField:t,content:n})=>e.jsx(L,{maxWidth:"50rem",textColor:"neutral800",ellipsis:!0,children:ce(n,t)}),ft=({mainField:t,content:n,rowId:s,name:a})=>{const{model:o}=K(),{formatMessage:l}=A(),{notifyStatus:x}=Ye(),[f,r]=S.useState(!1),[h]=a.split("."),{data:g,isLoading:T}=nt({model:o,id:s,targetField:h},{skip:!f,refetchOnMountOrArgChange:!0}),I=Array.isArray(n)?n.length:n.count;return S.useEffect(()=>{g&&x(l({id:w("DynamicTable.relation-loaded"),defaultMessage:"Relations have been loaded"}))},[g,l,x]),e.jsxs(M.Root,{onOpenChange:j=>r(j),children:[e.jsx(M.Trigger,{onClick:j=>j.stopPropagation(),children:e.jsx(L,{style:{cursor:"pointer"},textColor:"neutral800",fontWeight:"regular",children:I>0?l({id:"content-manager.containers.list.items",defaultMessage:"{number} {number, plural, =0 {items} one {item} other {items}}"},{number:I}):"-"})}),e.jsxs(M.Content,{children:[T&&e.jsx(M.Item,{disabled:!0,children:e.jsx(Je,{small:!0,children:l({id:w("ListViewTable.relation-loading"),defaultMessage:"Relations are loading"})})}),g?.results&&e.jsxs(e.Fragment,{children:[g.results.map(j=>e.jsx(M.Item,{disabled:!0,children:e.jsx(L,{maxWidth:"50rem",ellipsis:!0,children:ce(j,t)})},j.documentId)),g?.pagination&&g?.pagination.total>10&&e.jsx(M.Item,{"aria-disabled":!0,"aria-label":l({id:w("ListViewTable.relation-more"),defaultMessage:"This relation contains more entities than displayed"}),children:e.jsx(L,{children:"…"})})]})]})]})},ht=({content:t,mainField:n,attribute:s,rowId:a,name:o})=>{if(!xt(t,n,s))return e.jsx(L,{textColor:"neutral800",paddingLeft:s.type==="relation"?"1.6rem":0,paddingRight:s.type==="relation"?"1.6rem":0,children:"-"});switch(s.type){case"media":return s.multiple?e.jsx(mt,{content:t}):e.jsx(ue,{...t});case"relation":return pe(s.relation)?e.jsx(gt,{mainField:n,content:t}):e.jsx(ft,{rowId:a,mainField:n,content:t,name:o});case"component":return s.repeatable?e.jsx(ut,{mainField:n,content:t}):e.jsx(ct,{mainField:n,content:t});case"string":return e.jsx(Z,{description:t,children:e.jsx(L,{maxWidth:"30rem",ellipsis:!0,textColor:"neutral800",children:e.jsx(V,{type:s.type,value:t})})});default:return e.jsx(L,{maxWidth:"30rem",ellipsis:!0,textColor:"neutral800",children:e.jsx(V,{type:s.type,value:t})})}},xt=(t,n,s)=>{if(s.type==="component"){if(s.repeatable||!n)return t?.length>0;const a=t?.[n.name];return n.name==="id"&&![void 0,null].includes(a)?!0:!z(a)}return s.type==="relation"?pe(s.relation)?!z(t):Array.isArray(t)?t.length>0:t?.count>0:["integer","decimal","float","number"].includes(s.type)?typeof t=="number":s.type==="boolean"?t!==null:!z(t)},pe=t=>["oneToOne","manyToOne","oneToOneMorph"].includes(t),jt=t=>{const n=at(l=>l.admin_app.permissions.contentManager?.collectionTypesConfigurations??[]),[{query:s}]=$(),{formatMessage:a}=A(),{allowedActions:{canConfigureView:o}}=re(n);return e.jsxs(q.Root,{children:[e.jsx(q.Trigger,{children:e.jsx(ve,{label:a({id:"components.ViewSettings.tooltip",defaultMessage:"View Settings"}),children:e.jsx(Be,{})})}),e.jsx(q.Content,{side:"bottom",align:"end",sideOffset:4,children:e.jsxs(k,{alignItems:"stretch",direction:"column",padding:3,gap:3,children:[o?e.jsx(De,{size:"S",startIcon:e.jsx(He,{}),variant:"secondary",tag:$e,to:{pathname:"configurations/list",search:s.plugins?D.stringify({plugins:s.plugins},{encode:!1}):""},children:a({id:"app.links.configure-view",defaultMessage:"Configure the view"})}):null,e.jsx(yt,{...t})]})})]})},yt=({headers:t=[],resetHeaders:n,setHeaders:s})=>{const{trackUsage:a}=W(),{formatMessage:o,locale:l}=A(),{schema:x,model:f}=K(),{list:r}=se(f),h=ie(l,{sensitivity:"base"}),g=x?.attributes??{},T=Object.keys(g).filter(u=>Ue(g[u])).map(u=>({name:u,label:r.metadatas[u]?.label??""})).sort((u,d)=>h.compare(u.label,d.label)),I=u=>{a("didChangeDisplayedFields");const d=t.includes(u)?t.filter(O=>O!==u):[...t,u];s(d)},j=()=>{n()};return e.jsxs(k,{tag:"fieldset",direction:"column",alignItems:"stretch",gap:3,borderWidth:0,children:[e.jsxs(k,{justifyContent:"space-between",children:[e.jsx(L,{tag:"legend",variant:"pi",fontWeight:"bold",children:o({id:"containers.list.displayedFields",defaultMessage:"Displayed fields"})}),e.jsx(Ne,{onClick:j,children:o({id:"app.components.Button.reset",defaultMessage:"Reset"})})]}),e.jsx(k,{direction:"column",alignItems:"stretch",children:T.map(u=>{const d=t.includes(u.name);return e.jsx(k,{wrap:"wrap",gap:2,background:d?"primary100":"transparent",hasRadius:!0,padding:2,children:e.jsx(Ve,{onCheckedChange:()=>I(u.name),checked:d,name:u.name,children:e.jsx(L,{fontSize:1,children:u.label})})},u.name)})})]})},{INJECT_COLUMN_IN_TABLE:bt}=Ce,Ct=Y(Q.Header)`
  overflow-wrap: anywhere;
`,St=()=>{const{trackUsage:t}=W(),n=me(),{formatMessage:s}=A(),{toggleNotification:a}=ge(),{_unstableFormatAPIError:o}=fe(w),{collectionType:l,model:x,schema:f}=K(),{list:r}=se(x),[h,g]=S.useState([]),T=le(r.layout);S.useEffect(()=>{he(T,r.layout)||g(r.layout)},[r.layout,T]);const I=i=>{g(We(i,f.attributes,r.metadatas))},[{query:j}]=$({page:"1",pageSize:r.settings.pageSize.toString(),sort:r.settings.defaultSortBy?`${r.settings.defaultSortBy}:${r.settings.defaultSortOrder}`:""}),u=S.useMemo(()=>xe(j),[j]),d=S.useMemo(()=>D.stringify(u,{encode:!0,encodeValuesOnly:!0}),[u]),O=S.useMemo(()=>{const i=d.split("&").map(C=>{const[p,B]=C.split("=");return{[p]:B}});return Object.assign({},...i)},[d]),{data:H,error:P,isFetching:U}=je({model:x,params:O});S.useEffect(()=>{P&&a({type:"danger",message:o(P)})},[P,o,a]);const{results:v=[],pagination:c}=H??{};S.useEffect(()=>{c&&c.pageCount>0&&c.page>c.pageCount&&n({search:D.stringify({...j,page:c.pageCount})},{replace:!0})},[c,s,j,n]);const{canCreate:E}=ye("ListViewPage",({canCreate:i})=>({canCreate:i})),b=be("ListViewPage",({runHookWaterfall:i})=>i),m=S.useMemo(()=>{const C=b(bt,{displayedHeaders:h,layout:r}).displayedHeaders.map(p=>{const B=typeof p.label=="string"?{id:`content-manager.content-types.${x}.${p.name}`,defaultMessage:p.label}:p.label;return{...p,label:s(B),name:`${p.name}${p.mainField?.name?`.${p.mainField.name}`:""}`}});return f?.options?.draftAndPublish&&C.push({attribute:{type:"custom"},name:"status",label:s({id:w("containers.list.table-headers.status"),defaultMessage:"status"}),searchable:!1,sortable:!1}),C},[h,s,r,b,f?.options?.draftAndPublish,x]);if(U)return e.jsx(F.Loading,{});if(P)return e.jsx(F.Error,{});const R=f?.info.displayName??"Untitled",_=i=>()=>{t("willEditEntryFromList"),n({pathname:i.toString(),search:D.stringify({plugins:j.plugins})})};return e.jsxs(F.Main,{children:[e.jsx(F.Title,{children:`${R}`}),e.jsx(Ct,{primaryAction:E?e.jsx(te,{}):null,subtitle:s({id:w("pages.ListView.header-subtitle"),defaultMessage:"{number, plural, =0 {# entries} one {# entry} other {# entries}} found"},{number:c?.total}),title:R,navigationAction:e.jsx(Se,{})}),e.jsx(Q.Action,{endActions:e.jsxs(e.Fragment,{children:[e.jsx(Pe,{area:"listView.actions"}),e.jsx(jt,{setHeaders:I,resetHeaders:()=>g(r.layout),headers:h.map(i=>i.name)})]}),startActions:e.jsxs(e.Fragment,{children:[r.settings.searchable&&e.jsx(Me,{disabled:v.length===0,label:s({id:"app.component.search.label",defaultMessage:"Search for {target}"},{target:R}),placeholder:s({id:"global.search",defaultMessage:"Search"}),trackedEvent:"didSearch"}),r.settings.filterable&&f?e.jsx(ot,{disabled:v.length===0,schema:f}):null]})}),e.jsx(Q.Content,{children:e.jsxs(k,{gap:4,direction:"column",alignItems:"stretch",children:[e.jsxs(y.Root,{rows:v,headers:m,isLoading:U,children:[e.jsx(Tt,{}),e.jsxs(y.Content,{children:[e.jsxs(y.Head,{children:[e.jsx(y.HeaderCheckboxCell,{}),m.map(i=>e.jsx(y.HeaderCell,{...i},i.name))]}),e.jsx(y.Loading,{}),e.jsx(y.Empty,{action:E?e.jsx(te,{variant:"secondary"}):null}),e.jsx(y.Body,{children:v.map(i=>e.jsxs(y.Row,{cursor:"pointer",onClick:_(i.documentId),children:[e.jsx(y.CheckboxCell,{id:i.id}),m.map(({cellFormatter:C,...p})=>{if(p.name==="status"){const{status:B}=i;return e.jsx(y.Cell,{children:e.jsx(ze,{status:B,maxWidth:"min-content"})},p.name)}return["createdBy","updatedBy"].includes(p.name.split(".")[0])?e.jsx(y.Cell,{children:e.jsx(L,{textColor:"neutral800",children:i[p.name.split(".")[0]]?J(i[p.name.split(".")[0]]):"-"})},p.name):typeof C=="function"?e.jsx(y.Cell,{children:C(i,p,{collectionType:l,model:x})},p.name):e.jsx(y.Cell,{children:e.jsx(ht,{content:i[p.name.split(".")[0]],rowId:i.documentId,...p})},p.name)}),e.jsx(Lt,{onClick:C=>C.stopPropagation(),children:e.jsx(Ze,{document:i})})]},i.id))})]})]}),e.jsxs(G.Root,{...c,onPageSizeChange:()=>t("willChangeNumberOfEntriesPerPage"),children:[e.jsx(G.PageSize,{}),e.jsx(G.Links,{})]})]})})]})},Lt=Y(y.Cell)`
  display: flex;
  justify-content: flex-end;
`,Tt=()=>{const t=_e("TableActionsBar",o=>o.selectRow),[{query:n}]=$(),s=n?.plugins?.i18n?.locale,a=le(s);return S.useEffect(()=>{a!==s&&t([])},[t,a,s]),e.jsx(y.ActionBar,{children:e.jsx(qe,{})})},te=({variant:t})=>{const{formatMessage:n}=A(),{trackUsage:s}=W(),[{query:a}]=$();return e.jsx(Le,{variant:t,tag:Ie,onClick:()=>{s("willCreateEntry",{status:"draft"})},startIcon:e.jsx(Te,{}),style:{textDecoration:"none"},to:{pathname:"create",search:D.stringify({plugins:a.plugins})},minWidth:"max-content",marginLeft:2,children:n({id:w("HeaderLayout.button.label-add-entry"),defaultMessage:"Create new entry"})})},Et=()=>{const{slug:t=""}=Xe(),{permissions:n=[],isLoading:s,error:a}=re(et.map(o=>({action:o,subject:t})));return s?e.jsx(F.Loading,{}):a||!t?e.jsx(F.Error,{}):e.jsx(F.Protect,{permissions:n,children:({permissions:o})=>e.jsx(tt,{permissions:o,children:e.jsx(St,{})})})};export{St as ListViewPage,Et as ProtectedListViewPage};
