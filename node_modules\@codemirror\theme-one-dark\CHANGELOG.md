## 6.1.2 (2023-04-17)

### Bug fixes

Make sure the selection background styling overrides the base style rules.

## 6.1.1 (2023-02-17)

### Bug fixes

Remove the outline on matching brackets, because that could cover the native cursor on Chrome.

## 6.1.0 (2022-09-12)

### New features

Export a `color` object holding the colors used in the theme.

## 6.0.0 (2022-06-08)

### Breaking changes

Update dependencies to 6.0.0

## 0.20.0 (2022-04-20)

### Breaking changes

Update dependencies to 0.20.0

## 0.19.1 (2021-11-06)

### Bug fixes

Give tooltips a somewhat lighter background so that they don't blend into the editor background.

## 0.19.0 (2021-08-11)

### Breaking changes

Update dependencies to 0.19.0

## 0.18.1 (2021-05-15)

### Bug fixes

Include styling for the active line gutter.

## 0.18.0 (2021-03-03)

### Breaking changes

Update dependencies to 0.18.

## 0.17.5 (2021-02-10)

### Bug fixes

Increase contrast on the color used for comments and links.

## 0.17.4 (2021-01-18)

### Bug fixes

Fix the background color for the fold placeholder.

Improve background colors, make autocompletion dropdown more readable.

## 0.17.3 (2021-01-14)

### Bug fixes

Make the selection background grey, rather than dark green.

## 0.17.2 (2021-01-06)

### New features

The package now also exports a CommonJS module.

## 0.17.1 (2021-01-03)

### Bug fixes

Fix an issue where the active completion isn't readable on Chrome.

## 0.17.0 (2020-12-29)

### Breaking changes

First numbered release.

