/* @generated */
// prettier-ignore
if (Intl.DisplayNames && typeof Intl.DisplayNames.__addLocaleData === 'function') {
  Intl.DisplayNames.__addLocaleData({
  "data": {
    "patterns": {
      "locale": "{0} ({1})"
    },
    "types": {
      "calendar": {
        "long": {
          "buddhist": "ᏊᏗᏍᏘ ᏅᏙ ᏗᏎᏍᏗ",
          "chinese": "ᏓᎶᏂᎨᏍᏛ ᏅᏙ ᏗᏎᏍᏗ",
          "coptic": "ᎧᏘ ᏅᏙ ᏗᏎᏍᏗ",
          "dangi": "ᏓᏂᎩ ᏅᏙ ᏗᏎᏍᏗ",
          "ethiopic": "ᎢᏗᏯᏈᎩ ᏅᏙ ᏗᏎᏍᏗ",
          "ethiopic-amete-alem": "ᎡᏘᎣᏈᎠ ᎠᎺᏖ ᎠᎴᎻ ᏅᏙ ᏗᏎᏍᏗ",
          "gregorian": "ᎩᎴᎪᎵᎠᏂ ᏅᏙ ᏗᏎᏍᏗ",
          "hebrew": "ᎠᏂᏈᎷ ᏅᏙ ᏗᏎᏍᏗ",
          "islamic": "ᎢᏍᎳᎻᎩ ᏅᏙ ᏗᏎᏍᏗ",
          "islamic-civil": "ᎢᏌᎳᎻ ᏅᏙ ᏗᏎᏍᏗ (ᏴᏫ ᎡᏆᎩ)",
          "islamic-umalqura": "ᎢᏌᎳᎻ ᏅᏙ ᏗᏎᏍᏗ (ᎥᎻ ᎠᎵ-ᏊᎳ))",
          "iso8601": "ISO-8601 ᏅᏙ ᏗᏎᏍᏗ",
          "japanese": "ᏣᏆᏂᏏ ᏅᏙ ᏗᏎᏍᏗ",
          "persian": "ᏇᏏᎠᏂ ᏅᏙ ᏗᏎᏍᏗ",
          "roc": "ᏍᎦᏚᎩ ᎾᎿ ᏓᎶᏂᎨᏍᏛ ᏅᏙ ᏗᏎᏍᏗ"
        },
        "narrow": {
        },
        "short": {
        }
      },
      "currency": {
        "long": {
          "AED": "ᏌᏊ ᎢᏳᎾᎵᏍᏔᏅ ᎡᎳᏈ ᎢᎹᎵᏘᏏ ᎠᏕᎳ",
          "AFN": "ᎠᏫᎨᏂᏍᏖᏂ ᎠᏕᎳ",
          "ALL": "ᎠᎵᏇᏂᏯ ᎠᏕᎳ",
          "AMD": "ᎠᎵᎻᏂᎠ ᎠᏕᎳ",
          "ANG": "ᎾᏍᎩᏁᏛᎳᏂ ᎠᏂᏘᎵᏏ ᎠᏕᎳ",
          "AOA": "ᎠᏂᎪᎳ ᎠᏕᎳ",
          "ARS": "ᎠᏥᏂᏘᏂᎠ ᎠᏕᎳ",
          "AUD": "ᎡᎳᏗᏜ ᎠᏕᎳ",
          "AWG": "ᎠᎷᏆ ᎠᏕᎳ",
          "AZN": "ᎠᏏᎵᏆᏌᏂ ᎠᏕᎳ",
          "BAM": "ᏉᏏᏂᎠ-ᎲᏤᎪᏫ ᎦᏁᏟᏴᏍᏔᏅ ᎠᏕᎳ",
          "BBD": "ᏆᏇᏙᏍ ᎠᏕᎳ",
          "BDT": "ᏆᏂᎦᎵᏕᏍ ᎠᏕᎳ",
          "BGN": "ᏊᎵᎨᎵᎠ ᎠᏕᎳ",
          "BHD": "ᏆᎭᎴᎢᏂ ᎠᏕᎳ",
          "BIF": "ᏋᎷᏂᏗ ᎠᏕᎳ",
          "BMD": "ᏆᏊᏓ ᎠᏕᎳ",
          "BND": "ᏊᎾᎢ ᎠᏕᎳ",
          "BOB": "ᏉᎵᏫᎠ ᎠᏕᎳ",
          "BRL": "ᏆᏏᎵᎢ ᎠᏕᎳ",
          "BSD": "ᏆᎭᎹ ᎠᏕᎳ",
          "BTN": "ᏊᏔᏂ ᎠᏕᎳ",
          "BWP": "ᏆᏣᏩᎾ ᎠᏕᎳ",
          "BYN": "ᏇᎳᎷᏍ ᎠᏕᎳ",
          "BYR": "ᏇᎳᎷᏍ ᎠᏕᎳ (2000–2016)",
          "BZD": "ᏇᎵᏍ ᎠᏕᎳ",
          "CAD": "ᎨᎾᏓ ᎠᏕᎳ",
          "CDF": "ᎧᏂᎪ ᎠᏕᎳ",
          "CHF": "ᏍᏫᏏ ᎠᏕᎳ",
          "CLP": "ᏥᎵ ᎠᏕᎳ",
          "CNH": "ᏣᏂᏏ ᎠᏕᎳ (ᏓᎹᏳᏟᏗ)",
          "CNY": "ᏓᎶᏂᎨ ᎠᏕᎳ",
          "COP": "ᎪᎸᎻᏈᎢᎠ ᎠᏕᎳ",
          "CRC": "ᎪᏍᏓᎵᎧ ᎠᏕᎳ",
          "CUC": "ᎫᏆ ᎦᏁᏟᏴᏍᏔᏅ ᎠᏕᎳ",
          "CUP": "ᎫᏆ ᎠᏕᎳ",
          "CVE": "ᎢᎬᎾᏕᎾ ᎢᏤᏳᏍᏗ ᎠᏕᎳ",
          "CZK": "ᏤᎩ ᎠᏕᎳ",
          "DJF": "ᏥᏊᏗ ᎠᏕᎳ",
          "DKK": "ᏕᏂᏍ ᎠᏕᎳ",
          "DOP": "ᏙᎻᏂᎧᏂ ᎠᏕᎳ",
          "DZD": "ᎠᎵᏥᎵᏯ ᎠᏕᎳ",
          "EGP": "ᎢᏥᏈᎢ ᎠᏕᎳ",
          "ERN": "ᎡᎵᏟᏯ ᎠᏕᎳ",
          "ESP": "",
          "ETB": "ᎢᏗᎣᏈᎠ ᎠᏕᎳ",
          "EUR": "ᏳᎳᏛ ᎠᏕᎳ",
          "FJD": "ᏫᎩ ᎠᏕᎳ",
          "FKP": "ᏩᎩᎤ ᏚᎦᏚᏛᎢ ᎠᏕᎳ",
          "GBP": "ᎩᎵᏏᏲ ᎠᏕᎳ",
          "GEL": "ᏣᎠᏥᎢ ᎠᏕᎳ",
          "GHS": "ᎦᎠᎾ ᎠᏕᎳ",
          "GIP": "ᏥᏆᎵᏓ ᎠᏕᎳ",
          "GMD": "ᎦᎹᏈᎢᎠ ᎠᏕᎳ",
          "GNF": "ᎩᎢᏂ ᎠᏕᎳ",
          "GTQ": "ᏆᏖᎹᎳ ᎠᏕᎳ",
          "GYD": "ᎦᏯᎾ ᎠᏕᎳ",
          "HKD": "ᎰᏂᎩ ᎪᏂᎩ ᎠᏕᎳ",
          "HNL": "ᎭᏂᏚᎳᏍ ᎠᏕᎳ",
          "HRK": "ᎧᎶᎡᏏᎠ ᎠᏕᎳ",
          "HTG": "ᎮᏘ ᎠᏕᎳ",
          "HUF": "ᎲᏂᎦᎵ ᎠᏕᎳ",
          "IDR": "ᎢᏂᏙᏂᏍᏯ ᎠᏕᎳ",
          "ILS": "ᎢᏏᎵᏱ ᎢᏤ ᎠᏕᎳ",
          "INR": "ᎢᏂᏗᎢᎠ ᎠᏕᎳ",
          "IQD": "ᎢᎳᎩ ᎠᏕᎳ",
          "IRR": "ᎢᎴᏂ ᎠᏕᎳ",
          "ISK": "ᏧᏁᏍᏓᎸᎯ ᎠᏕᎳ",
          "JMD": "ᏣᎺᎢᎧ ᎠᏕᎳ",
          "JOD": "ᏦᏓᏂ ᎠᏕᎳ",
          "JPY": "ᏣᏩᏂᏏ ᎠᏕᎳ",
          "KES": "ᎨᏂᏯ ᎠᏕᎳ",
          "KGS": "ᎩᎵᏣᎢᏍ ᎠᏕᎳ",
          "KHR": "ᎧᎹᏉᏗᎠᏂ ᎠᏕᎳ",
          "KMF": "ᎪᎼᎳᏍ ᎠᏕᎳ",
          "KPW": "ᏧᏴᏢ ᎪᎵᎠ ᎠᏕᎳ",
          "KRW": "ᏧᎦᎾᏮ ᎪᎵᎠ ᎠᏕᎳ",
          "KWD": "ᎫᏪᎢᏘ ᎠᏕᎳ",
          "KYD": "ᎨᎢᎹᏂ ᏚᎦᏚᏛᎢ ᎠᏕᎳ",
          "KZT": "ᎧᏎᎧᏍᏕᏂ ᎠᏕᎳ",
          "LAK": "ᎳᎣ ᎠᏕᎳ",
          "LBP": "ᎴᏆᎾᏂ ᎠᏕᎳ",
          "LKR": "ᏍᎵ ᎳᏂᎧ ᎠᏕᎳ",
          "LRD": "ᎳᏈᎵᏯ ᎠᏕᎳ",
          "LSL": "ᎴᏐᏠ ᎶᏘ",
          "LTL": "",
          "LVL": "",
          "LYD": "ᎵᏈᏯ ᎠᏕᎳ",
          "MAD": "ᎼᎶᎪ ᎠᏕᎳ",
          "MDL": "ᎹᎵᏙᏫᎠ ᎠᏕᎳ",
          "MGA": "ᎹᎳᎦᏏ ᎠᏕᎳ",
          "MKD": "ᎹᏎᏙᏂᎠ ᎠᏕᎳ",
          "MMK": "ᎹᏯᎹᎵ ᎠᏕᎳ",
          "MNT": "ᎹᏂᎪᎵᎠ ᎠᏕᎳ",
          "MOP": "ᎹᎧᎣ ᎠᏕᎳ",
          "MRO": "ᎹᏈᏔᏂᎠ ᎠᏕᎳ (1973–2017)",
          "MRU": "ᎹᏈᏔᏂᎠ ᎠᏕᎳ",
          "MUR": "ᎹᏘᎢᏯ ᎠᏕᎳ",
          "MVR": "ᎹᎵᏗᏫᏍ ᎠᏕᎳ",
          "MWK": "ᎹᎳᏫ ᎠᏕᎳ",
          "MXN": "ᏍᏆᏂ ᎠᏕᎳ",
          "MYR": "ᎹᎴᏏᎢᎠ ᎠᏕᎳ",
          "MZN": "ᎼᏎᎻᏇᎩ ᎠᏕᎳ",
          "NAD": "ᎾᎻᏈᎢᏯ ᎠᏕᎳ",
          "NGN": "ᏂᏥᎵᏯ ᎠᏕᎳ",
          "NIO": "ᏂᎧᎳᏆ ᎠᏕᎳ",
          "NOK": "ᏃᏪ ᎠᏕᎳ",
          "NPR": "ᏁᏆᎵ ᎠᏕᎳ",
          "NZD": "ᎢᏤ ᏏᎢᎴᏂᏗ ᎠᏕᎳ",
          "OMR": "ᎣᎺᏂ ᎠᏕᎳ",
          "PAB": "ᏆᎾᎹ ᎠᏕᎳ",
          "PEN": "ᏇᎷ ᎠᏕᎳ",
          "PGK": "ᏆᏇ ᎢᏤ ᎩᎢᏂ ᎠᏕᎳ",
          "PHP": "ᎠᏂᏈᎵᎩᏃ ᎠᏕᎳ",
          "PKR": "ᏆᎩᏍᏖᏂ ᎠᏕᎳ",
          "PLN": "ᏉᎳᏂ ᎠᏕᎳ",
          "PYG": "ᏆᎳᏇᎢᏯ ᎠᏕᎳ",
          "QAR": "ᎧᏔᎵ ᎠᏕᎳ",
          "RON": "ᎶᎹᏂᏯ ᎠᏕᎳ",
          "RSD": "ᏒᏈᏯ ᎠᏕᎳ",
          "RUB": "ᏲᏂᎢ ᎠᏕᎳ",
          "RWF": "ᎶᏩᏂᏓ ᎠᏕᎳ",
          "SAR": "ᏌᎤᏗ ᎠᏕᎳ",
          "SBD": "ᏐᎶᎹᏂ ᏚᎦᏚᏛᎢ ᎠᏕᎳ",
          "SCR": "ᏏᎡᏥᎵᏍ ᎠᏕᎳ",
          "SDG": "ᏑᏕᏂ ᎠᏕᎳ",
          "SEK": "ᏍᏫᏕᏂ ᎠᏕᎳ",
          "SGD": "ᏏᏂᎦᏉᎵ ᎠᏕᎳ",
          "SHP": "ᎤᏓᏅᏘ ᎮᎵᎾ ᎠᏕᎳ",
          "SLL": "ᏏᎡᎳᎴᎣᏂ ᎠᏕᎳ",
          "SOS": "ᏐᎹᎵ ᎠᏕᎳ",
          "SRD": "ᏒᎵᎾᎻ ᎠᏕᎳ",
          "SSP": "ᏧᎦᎾᏮ ᏑᏕᏂ ᎠᏕᎳ",
          "STD": "ᏌᎣᏙᎺ ᎠᎴ ᏈᏂᏏᏇ ᎠᏕᎳ (1977–2017)",
          "STN": "ᏌᎣᏙᎺ & ᏈᏂᏏᏇ ᎠᏕᎳ",
          "SYP": "ᏏᎵᎠ ᎠᏕᎳ",
          "SZL": "ᏍᏩᏏ ᎠᏕᎳ",
          "THB": "ᏔᏯᎴᏂ ᎠᏕᎳ",
          "TJS": "ᏔᏥᎩᏍᏕᏂ ᎠᏕᎳ",
          "TMT": "ᏛᎵᎩᎺᏂᏍᏔᏂ ᎠᏕᎳ",
          "TND": "ᏚᏂᏏᏍᎠ ᎠᏕᎳ",
          "TOP": "ᏔᏂᎪ ᎠᏕᎳ",
          "TRY": "ᎬᏃ ᎠᏕᎳ",
          "TTD": "ᏟᏂᏕᏗ & ᏙᏆᎪ ᎠᏕᎳ",
          "TWD": "ᎢᏤ ᏔᎢᏩᏂ ᎠᏕᎳ",
          "TZS": "ᏖᏂᏏᏂᏯ ᎠᏕᎳ",
          "UAH": "ᏳᎧᎴᏂ ᎠᏕᎳ",
          "UGX": "ᏳᎦᏂᏓ ᎠᏕᎳ",
          "USD": "US ᎠᏕᎳ",
          "UYU": "ᏳᎷᏇ ᎠᏕᎳ",
          "UZS": "ᎤᏍᏇᎩᏍᏖᏂ ᎠᏕᎳ",
          "VEF": "ᏪᏁᏑᏪ ᎠᏕᎳ (2008–2018)",
          "VES": "ᏪᏁᏑᏪ ᎠᏕᎳ",
          "VND": "ᏫᎡᏘᎾᎻᏍ ᎠᏕᎳ",
          "VUV": "ᏩᏂᎤᏩᏚ ᎠᏕᎳ",
          "WST": "ᏌᎼᎠ ᎠᏕᎳ",
          "XAF": "ᎠᏰᏟ ᎬᎿᎨᏍᏛ CFA ᎠᏕᎳ",
          "XCD": "ᏗᎧᎸᎬ ᎨᏆᏙᏯ ᎠᏕᎳ",
          "XOF": "ᏭᏕᎵᎬ ᎬᎿᎨᏍᏛ CFA ᎠᏕᎳ",
          "XPF": "CFP ᎠᏕᎳ",
          "XXX": "ᏄᏬᎵᏍᏛᎾ ᎠᏕᎳ",
          "YER": "ᏰᎺᏂ ᎠᏕᎳ",
          "ZAR": "ᏧᎦᎾᏮ ᎬᎿᎨᏍᏛ ᎠᏕᎳ",
          "ZMW": "ᏏᎻᏆᏇ ᎠᏕᎳ"
        },
        "narrow": {
        },
        "short": {
        }
      },
      "dateTimeField": {
        "long": {
          "day": "ᎢᎦ",
          "dayOfYear": "ᎢᎦ ᎤᏕᏘᏴᏌᏗᏒᎢ",
          "dayperiod": "ᏌᎾᎴ/ᏒᎯᏱ",
          "era": "ᏗᏓᎴᏂᏍᎬ",
          "hour": "ᏑᏟᎶᏓ",
          "minute": "ᎢᏯᏔᏬᏍᏔᏅ",
          "month": "ᎧᎸᎢ",
          "quarter": "ᎩᏄᏙᏗ",
          "second": "ᎠᏎᏢ",
          "timeZoneName": "ᏂᎬᎾᏛ ᏧᏓᎴᏅᏓ ᏓᏟᎢᎵᏍᏒᎢ",
          "weekOfMonth": "ᏒᎾᏙᏓᏆᏍᏗ ᎧᎸᎢ",
          "weekOfYear": "ᏒᎾᏙᏓᏆᏍᏗ",
          "weekday": "ᎢᎦ ᏕᎨᏌᏗᏒ",
          "weekdayOfMonth": "ᏒᎾᏙᏓᏆᏍᏗ ᎢᎦ ᎧᎸᎢ",
          "year": "ᎤᏕᏘᏴᏌᏗᏒᎢ"
        },
        "narrow": {
          "day": "ᎢᎦ",
          "dayOfYear": "ᎢᎦ ᎤᏕ.",
          "dayperiod": "ᏌᎾᎴ/ᏒᎯᏱ",
          "era": "ᏗᏓᎴᏂᏍᎬ",
          "hour": "ᏑᏟ.",
          "minute": "ᎢᏯᏔ.",
          "month": "ᎧᎸ.",
          "quarter": "ᎩᏄᏘ.",
          "second": "ᎠᏎ.",
          "timeZoneName": "ᏂᎬᎾᏛ ᏧᏓᎴᏅᏓ",
          "weekOfMonth": "ᎡᎾ. ᎧᎸ.",
          "weekOfYear": "ᏒᎾ.",
          "weekday": "ᎢᎦ ᏕᎨ.",
          "weekdayOfMonth": "ᏒᎾ. ᎢᎦ ᎧᎸ.",
          "year": "ᎤᏕ."
        },
        "short": {
          "day": "ᎢᎦ",
          "dayOfYear": "ᎢᎦ ᎤᏕ.",
          "dayperiod": "ᏌᎾᎴ/ᏒᎯᏱ",
          "era": "ᏗᏓᎴᏂᏍᎬ",
          "hour": "ᏑᏟ.",
          "minute": "ᎢᏯᏔ.",
          "month": "ᎧᎸ.",
          "quarter": "ᎩᏄᏘ.",
          "second": "ᎠᏎ.",
          "timeZoneName": "ᏂᎬᎾᏛ ᏧᏓᎴᏅᏓ",
          "weekOfMonth": "ᎡᎾ. ᎧᎸ.",
          "weekOfYear": "ᏒᎾ.",
          "weekday": "ᎢᎦ ᏕᎨ.",
          "weekdayOfMonth": "ᏒᎾ. ᎢᎦ ᎧᎸ.",
          "year": "ᎤᏕ."
        }
      },
      "language": {
        "dialect": {
          "long": {
            "aa": "ᎠᏩᎳ",
            "ab": "ᎠᏆᏏᎠᏂ",
            "ace": "ᎠᏥᏂᏏ",
            "ada": "ᎠᏓᎾᎦᎺ",
            "ady": "ᎠᏗᎨ",
            "af": "ᎠᎬᎿᎨᏍᏛ",
            "agq": "ᎠᎨᎹ",
            "ain": "ᎠᏱᏄ",
            "ak": "ᎠᎧᎾ",
            "ale": "ᎠᎵᎤᏘ",
            "alt": "ᏧᎦᎾᏮ ᏗᏜ ᎠᎵᏔᎢ",
            "am": "ᎠᎹᎭᎵᎩ",
            "an": "ᎠᏩᎪᏂᏏ",
            "ann": "ᎣᏉᎶ",
            "anp": "ᎠᎾᎩᎧ",
            "ar": "ᎡᎳᏈ",
            "ar-001": "ᎪᎯᏊ ᎢᎬᏥᎩ ᎠᏟᎶᏍᏗ ᎡᎳᏈ",
            "arn": "ᎹᏊᏤ",
            "arp": "ᎠᏩᏈᎰ",
            "ars": "ᎾᏣᏗ ᎠᎳᏈ",
            "as": "ᎠᏌᎻᏏ",
            "asa": "ᎠᏑ",
            "ast": "ᎠᏍᏚᎵᎠᏂ",
            "atj": "ᎠᏂᏘᎧᎺᏆ",
            "av": "ᎠᏩᎵᎧ",
            "awa": "ᎠᏩᏗ",
            "ay": "ᎠᏱᎹᎳ",
            "az": "ᎠᏎᏆᏣᏂ",
            "ba": "ᏆᏍᎯᎩᎠ",
            "ban": "ᏆᎵᏁᏏ",
            "bas": "ᏆᏌᎠ",
            "be": "ᏇᎳᎷᏏ",
            "bem": "ᏇᎹᏆ",
            "bez": "ᏇᎾ",
            "bg": "ᏊᎵᎨᎵᎠᏂ",
            "bho": "ᏉᏣᏊᎵ",
            "bi": "ᏈᏍᎳᎹ",
            "bin": "ᏈᏂ",
            "bla": "ᏏᎩᏏᎧ",
            "bm": "ᏆᎻᏆᎳ",
            "bn": "ᏇᏂᎦᎳ",
            "bo": "ᏘᏇᏔᏂ",
            "br": "ᏇᏙᏂ",
            "brx": "ᏉᏙ",
            "bs": "ᏆᏍᏂᎠᏂ",
            "bug": "ᏈᎥᎩᏂᏍ",
            "byn": "ᏟᏂ",
            "ca": "ᎨᏔᎳᏂ",
            "cay": "ᎧᏳᎦ",
            "ccp": "ᏣᎧᎹ",
            "ce": "ᏤᏤᏂ",
            "ceb": "ᏎᏆᏃ",
            "cgg": "ᏥᎦ",
            "ch": "ᏣᎼᎶ",
            "chk": "ᏧᎨᏎ",
            "chm": "ᎹᎵ",
            "cho": "ᎠᏣᏓ",
            "chp": "ᎠᏥᏇᏯᏂ",
            "chr": "ᏣᎳᎩ",
            "chy": "ᏣᏰᏂ",
            "ckb": "ᎠᏰᏟ ᎫᏗᏏ",
            "clc": "ᏥᎸᎪᏘᎾ",
            "co": "ᎪᎵᏍᎢᎧᏂ",
            "crg": "ᎻᏥᏩ",
            "crj": "ᏧᎦᏃᏮ ᏗᎧᎸᎬ Ꮯ",
            "crk": "ᏠᎨᏏ Ꮯ",
            "crl": "ᏧᏴᏢ ᏗᎧᎸᎬ Ꮯ",
            "crm": "ᎠᏫ ᎡᏆ Ꮯ",
            "crr": "ᎠᎵᎦᏂᏈᎠᎾ",
            "crs": "ᏎᏎᎵᏩ ᏟᏲᎵ ᎠᏂᎦᎸ",
            "cs": "ᏤᎩ",
            "csw": "ᏌᎷᏱ Ꮯ",
            "cu": "ᏧᏂᎳᏫᏍᏗ ᏍᎳᏫᎪ",
            "cv": "ᏧᏩᏏ",
            "cy": "ᏪᎵᏏ",
            "da": "ᏕᏂᏍ",
            "dak": "ᏓᎪᏔ",
            "dar": "ᏓᎳᏆ",
            "dav": "ᏔᎢᏔ",
            "de": "ᏙᎢᏥ",
            "de-AT": "ᎠᏟᏯᏂ ᎠᏂᏓᏥ",
            "de-CH": "ᏍᏫᏏ ᎦᎸᎳᏗ ᎠᏂᏓᏥ",
            "dgr": "ᎩᏟ ᎤᏄᎳᏥ",
            "dje": "ᏌᎹ",
            "doi": "ᏙᎦᎵ",
            "dsb": "ᎡᎳᏗ ᏐᏈᎠᏂ",
            "dua": "ᏚᎠᎳ",
            "dv": "ᏗᏪᎯ",
            "dyo": "ᏦᎳ-ᏬᏱ",
            "dz": "ᏓᏐᏅᎧ",
            "dzg": "ᏓᏌᎦ",
            "ebu": "ᎡᎻᏊ",
            "ee": "ᎡᏪ",
            "efi": "ᎡᏫᎩ",
            "eka": "ᎨᎧᏧᎧ",
            "el": "ᎠᏂᎪᎢ",
            "en": "ᎩᎵᏏ",
            "en-AU": "ᎡᎳᏗᏜ ᎩᎵᏏ",
            "en-CA": "ᎨᎾᏓ ᎩᎵᏏ",
            "en-GB": "ᎩᎵᏏᏲ ᎩᎵᏏ",
            "en-US": "ᎠᎹᏰᏟ ᎩᎵᏏ",
            "eo": "ᎡᏍᏇᎳᏂᏙ",
            "es": "ᏍᏆᏂ",
            "es-419": "ᏔᏘᏂ ᎠᎹᏰᏟ ᏍᏆᏂ",
            "es-ES": "ᎠᏂᏍᏆᏂᏱ ᏍᏆᏂ",
            "es-MX": "ᏍᏆᏂᏱ ᏍᏆᏂ",
            "et": "ᎡᏍᏙᏂᎠᏂ",
            "eu": "ᏆᏍᎨ",
            "ewo": "ᎡᏬᏂᏙ",
            "fa": "ᏇᏏᎠᏂ",
            "fa-AF": "ᏓᎵ",
            "ff": "ᏊᎳᏂ",
            "fi": "ᏈᏂᏍ",
            "fil": "ᎠᏈᎵᎩ",
            "fj": "ᏫᏥᎠᏂ",
            "fo": "ᏇᎶᎡᏍ",
            "fon": "ᏠᏂ",
            "fr": "ᎦᎸᏥ",
            "fr-CA": "ᎨᎾᏓ ᎦᎸᏥ",
            "fr-CH": "ᏍᏫᏏ ᎦᎸᏥ",
            "frc": "ᎨᏨᏂ ᎦᎸᏥ",
            "frr": "ᏧᏴᏢ ᎷᏈ",
            "fur": "ᏞᎤᎵᎠᏂ",
            "fy": "ᏭᏕᎵᎬ ᏗᏜ ᏟᏏᎠᏂ",
            "ga": "ᎨᎵᎩ",
            "gaa": "Ꭶ",
            "gd": "ᏍᎦᏗ ᎨᎵᎩ",
            "gez": "ᎩᏏ",
            "gil": "ᎩᏇᏘᏏ",
            "gl": "ᎦᎵᏏᎠᏂ",
            "gn": "ᏆᎳᏂ",
            "gor": "ᎪᎶᏂᏔᏃ",
            "gsw": "ᏍᏫᏏ ᎠᏂᏓᏥ",
            "gu": "ᎫᏣᎳᏘ",
            "guz": "ᎫᏏ",
            "gv": "ᎹᎾᎧᏏ",
            "gwi": "ᏈᏥᏂ",
            "ha": "ᎭᎤᏌ",
            "hai": "ᎭᏱᏓ",
            "haw": "ᎭᏩᎼ",
            "hax": "ᏧᎦᏃᏮ ᏗᏜ ᎭᏱᏓ",
            "he": "ᎠᏂᏈᎷ",
            "hi": "ᎯᏂᏗ",
            "hil": "ᎯᎵᎨᎾᏂ",
            "hmn": "ᎭᎼᏂᎩ",
            "hr": "ᎧᎶᎡᏏᏂ",
            "hsb": "ᎦᎸᎳᏗᎨ ᏐᏈᎠᏂ",
            "ht": "ᎮᏏᎠᏂ ᏟᏲᎵ",
            "hu": "ᎲᏂᎦᎵᎠᏂ",
            "hup": "ᎠᏂᎱᏆ",
            "hur": "ᎭᎵᎪᎺᎴᎻ",
            "hy": "ᎠᎳᎻᎠᏂ",
            "hz": "ᎮᎴᎶ",
            "ia": "ᎠᏰᏟ ᎦᏬᏂᎯᏍᏗ",
            "iba": "ᎢᏆᏂ",
            "ibb": "ᎢᏈᏈᎣ",
            "id": "ᎢᏂᏙᏂᏏᎠ",
            "ig": "ᎢᎦᎪ",
            "ii": "ᏏᏧᏩᏂ Ᏹ",
            "ikt": "ᏭᏕᎵᎬ ᎨᎾᏓ ᎢᏄᎩᏘᏚᏘ",
            "ilo": "ᎢᎶᎪ",
            "inh": "ᎢᏂᎫᏏ",
            "io": "ᎢᏙ",
            "is": "ᏧᏁᏍᏓᎸᎯᎢᎩ",
            "it": "ᎬᏩᎵᏲᏥᎢ",
            "iu": "ᎢᏄᎦᏘᏚ",
            "ja": "ᏣᏩᏂᏏ",
            "jbo": "ᎶᏣᏆᏂ",
            "jgo": "ᎾᎪᏆ",
            "jmc": "ᎹᏣᎺ",
            "jv": "ᏆᏌ ᏣᏩ",
            "ka": "ᏦᏥᎠᏂ",
            "kab": "ᎧᏈᎴ",
            "kac": "ᎧᏥᏂ",
            "kaj": "ᏥᏧ",
            "kam": "ᎧᎻᏆ",
            "kbd": "ᎧᏆᏗᎠᏂ",
            "kcg": "ᏔᏯᏆ",
            "kde": "ᎹᎪᏕ",
            "kea": "ᎧᏊᏪᏗᎠᏄ",
            "kfo": "ᎪᎶ",
            "kgp": "ᎨᏂᎨᏂ",
            "kha": "ᎧᏏ",
            "khq": "ᎪᏱᎳ ᏥᏂ",
            "ki": "ᎩᎫᏳ",
            "kj": "ᎫᏩᏂᎠᎹ",
            "kk": "ᎧᏌᎧ",
            "kkj": "ᎧᎪ",
            "kl": "ᎧᎳᎵᏑᏘ",
            "kln": "ᎧᎴᏂᏥᏂ",
            "km": "ᎩᎻᎷ",
            "kmb": "ᎩᎻᏊᏚ",
            "kn": "ᎧᎾᏓ",
            "ko": "ᎪᎵᎠᏂ",
            "kok": "ᎧᏂᎧᏂ",
            "kpe": "ᏇᎴ",
            "kr": "ᎧᏄᎵ",
            "krc": "ᎧᎳᏣᏱ-ᏆᎵᎧᎵ",
            "krl": "ᎧᎴᎵᎠᏂ",
            "kru": "ᎫᎷᎩ",
            "ks": "ᎧᏏᎻᎵ",
            "ksb": "ᏝᎻᏆᎸ",
            "ksf": "ᏆᏫᎠ",
            "ksh": "ᎪᎶᏂᎠᏂ",
            "ku": "ᎫᏗᏏ",
            "kum": "ᎫᎻᎧ",
            "kv": "ᎪᎻ",
            "kw": "ᏎᎷᎭ",
            "kwk": "ᏆᏆᎳ",
            "ky": "ᎩᎵᏣᎢᏍ",
            "la": "ᎳᏘᏂ",
            "lad": "ᎳᏗᏃ",
            "lag": "ᎳᏂᎩ",
            "lb": "ᎸᎦᏏᎻᏋᎢᏍ",
            "lez": "ᎴᏏᎦᏂ",
            "lg": "ᎦᏂᏓ",
            "li": "ᎴᎹᏊᎵᏏ",
            "lil": "ᎵᎶᎡᏘ",
            "lkt": "ᎳᎪᏓ",
            "ln": "ᎵᏂᎦᎳ",
            "lo": "ᎳᎣ",
            "lou": "ᎷᏫᏏᎡᎾ ᎦᏬᏂᎯᏍᏗ",
            "loz": "ᎶᏏ",
            "lrc": "ᏧᏴᏢ ᏗᏜ ᎷᎵ",
            "lsm": "ᏌᎠᎻᎠ",
            "lt": "ᎵᏚᏩᏂᎠᏂ",
            "lu": "ᎷᏆ-ᎧᏔᎦ",
            "lua": "ᎷᏆ-ᎷᎷᎠ",
            "lun": "ᎷᎾᏓ",
            "luo": "ᎷᎣ",
            "lus": "ᎻᏐ",
            "luy": "ᎷᏱᎠ",
            "lv": "ᎳᏘᏫᎠᏂ",
            "mad": "ᎹᏚᎴᏏ",
            "mag": "ᎹᎦᎯ",
            "mai": "ᎹᏟᎵ",
            "mak": "ᎹᎧᏌ",
            "mas": "ᎹᏌᏱ",
            "mdf": "ᎼᎧᏌ",
            "men": "ᎺᎾᏕ",
            "mer": "ᎺᎷ",
            "mfe": "ᎼᎵᏏᎡᏂ",
            "mg": "ᎹᎳᎦᏏ",
            "mgh": "ᎹᎫᏩ-ᎻᏙ",
            "mgo": "ᎺᎳ’",
            "mh": "ᎹᏌᎵᏏ",
            "mi": "ᎹᏫ",
            "mic": "ᎻᎧᎹᎩ",
            "min": "ᎻᎾᎧᏆᎤ",
            "mk": "ᎹᏎᏙᏂᎠᏂ",
            "ml": "ᎹᎳᏯᎳᎻ",
            "mn": "ᎹᏂᎪᎵᎠᏂ",
            "mni": "ᎺᏂᏉᎵ",
            "moe": "ᎢᏄ-ᎠᏱᏵᏂ",
            "moh": "ᎼᎭᎩ",
            "mos": "ᎼᏍᏏ",
            "mr": "ᎹᎳᏘ",
            "ms": "ᎹᎴ",
            "mt": "ᎹᎵᏘᏍ",
            "mua": "ᎽᏂᏓᎩ",
            "mul": "ᏧᏈᏍᏗ ᏗᎦᏬᏂᎯᏍᏗ",
            "mus": "ᎠᎫᏌ",
            "mwl": "ᎻᎳᏕᏏ",
            "my": "ᏋᎻᏍ",
            "myv": "ᎡᏏᏯ",
            "mzn": "ᎹᏌᏕᎳᏂ",
            "na": "ᏃᎤᎷ",
            "nap": "ᏂᏯᏆᎵᏔᏂ",
            "naq": "ᎾᎹ",
            "nb": "ᏃᎵᏪᏥᏂ ᏉᎧᎹᎵ",
            "nd": "ᏧᏴᏢ ᏂᏕᏇᎴ",
            "nds": "ᎡᎳᏗ ᎠᏂᏓᏥ",
            "nds-NL": "ᎡᎳᏗ ᏁᏛᎳᏂ",
            "ne": "ᏁᏆᎵ",
            "new": "ᏁᏩᎵ",
            "ng": "ᎾᏙᎦ",
            "nia": "ᏂᎠᏏ",
            "niu": "ᏂᏳᏫᏯᏂ",
            "nl": "ᏛᏥ",
            "nl-BE": "ᏊᎵᏥᎥᎻ ᏛᏥ",
            "nmg": "ᏆᏏᏲ",
            "nn": "ᏃᎵᏪᏥᏂ ᎾᎵᏍᎩ",
            "nnh": "ᎾᏥᏰᎹᏊᏂ",
            "no": "ᏃᎵᏪᏥᏂ",
            "nog": "ᏃᎦᏱ",
            "nqo": "ᎾᎪ",
            "nr": "ᏧᎦᎾᏮ ᏂᏕᏇᎴ",
            "nso": "ᏧᏴᏢ ᏗᏜ ᏐᏠ",
            "nus": "ᏄᏪᎵ",
            "nv": "ᎾᏩᎰ",
            "ny": "ᏂᏯᏂᏣ",
            "nyn": "ᏂᏯᎾᎪᎴ",
            "oc": "ᎠᏏᏔᏂ",
            "ojb": "ᏧᏴᏢ ᏭᏕᎵᎬ ᎣᏥᏆ",
            "ojc": "ᎠᏰᏟ ᎣᏥᏆ",
            "ojs": "ᎣᏥ-Ꮯ",
            "ojw": "ᏭᏕᎵᎬ ᏗᏜ ᎣᏥᏆ",
            "oka": "ᎣᎧᎾᎦᏂ",
            "om": "ᎣᎶᎼ",
            "or": "ᎣᏗᎠ",
            "os": "ᎣᏎᏘᎧ",
            "pa": "ᏡᏂᏣᏈ",
            "pag": "ᏇᎦᏏᎠᏂ",
            "pam": "ᏆᎹᏆᎾᎦ",
            "pap": "ᏆᏈᏯᎺᎾᏙ",
            "pau": "ᏆᎳᎤᏩᏂ",
            "pcm": "ᎾᎩᎵᎠᏂ ᏈᏥᏂ",
            "pis": "ᏈᏥᎾ",
            "pl": "ᏉᎵᏍ",
            "pqm": "ᎹᎵᏏᏘ-ᏇᏌᎹᏉᏗ",
            "prg": "ᏡᏏᎠᏂ",
            "ps": "ᏆᏍᏙ",
            "pt": "ᏉᏧᎩᏍ",
            "pt-BR": "ᏆᏏᎵᎢ ᏉᏧᎩᏍ",
            "pt-PT": "ᏳᎳᏈ ᏉᏧᎩᏍ",
            "qu": "ᎨᏧᏩ",
            "quc": "ᎩᏤ",
            "rap": "ᎳᏆᏄᏫ",
            "rar": "ᎳᎶᏙᎾᎦᏂ",
            "rhg": "ᎶᎯᏂᏯ",
            "rm": "ᎠᏂᎶᎺᏂ",
            "rn": "ᎷᏂᏗ",
            "ro": "ᎶᎹᏂᎠᏂ",
            "ro-MD": "ᎹᎵᏙᏫᎠ ᏣᎹᏂᎠᏂ",
            "rof": "ᎶᎹᏉ",
            "ru": "ᏲᏅᎯ",
            "rup": "ᎠᏬᎹᏂᎠᏂ",
            "rw": "ᎩᏂᏯᏩᏂᏓ",
            "rwk": "Ꮖ",
            "sa": "ᏍᏂᏍᎩᏗ",
            "sad": "ᏌᏅᏓᏫ",
            "sah": "ᏌᎧᎾ",
            "saq": "ᏌᎹᏊᎷ",
            "sat": "ᏌᏂᏔᎵ",
            "sba": "ᎾᎦᎹᏇ",
            "sbp": "ᏌᏁᎫ",
            "sc": "ᏌᏗᏂᎠᏂ",
            "scn": "ᏏᏏᎵᎠᏂ",
            "sco": "ᏍᎦᏗ",
            "sd": "ᏏᏂᏗ",
            "se": "ᏧᏴᏢ ᏗᏜ ᏌᎻ",
            "see": "ᏏᏂᎦ",
            "seh": "ᏎᎾ",
            "ses": "ᎪᏱᎳᏈᎶ ᏎᏂ",
            "sg": "ᏌᏂᎪ",
            "shi": "ᏔᏤᎵᎯᏘ",
            "shn": "ᏝᏂ",
            "si": "ᏏᎾᎭᎳ",
            "sk": "ᏍᎶᏩᎩ",
            "sl": "ᏍᎶᏫᏂᎠᏂ",
            "slh": "ᏧᎦᏃᏮ ᏗᏜ ᎷᏑᏘᏏᏗ",
            "sm": "ᏌᎼᏯᏂ",
            "sma": "ᏧᎦᎾᏮ ᏗᏜ ᏌᎻ",
            "smj": "ᎷᎴ ᏌᎻ",
            "smn": "ᎢᎾᎵ ᏌᎻ",
            "sms": "ᏍᎪᎵᏘ ᏌᎻ",
            "sn": "ᏠᎾ",
            "snk": "ᏐᏂᏂᎨ",
            "so": "ᏐᎹᎵ",
            "sq": "ᎠᎵᏇᏂ",
            "sr": "ᏒᏈᎠᏂ",
            "srn": "ᏏᎳᎾᏂ ᏙᏃᎪ",
            "ss": "ᏍᏩᏘ",
            "ssy": "ᏌᎰ",
            "st": "ᏧᎦᎾᏮ ᏗᏜ ᏐᏠ",
            "str": "ᏌᎵᏏ",
            "su": "ᏑᏂᏓᏂᏏ",
            "suk": "ᏑᎫᎹ",
            "sv": "ᏍᏫᏗᏏ",
            "sw": "ᏍᏩᎯᎵ",
            "sw-CD": "ᎧᏂᎪ ᏍᏩᎯᎵ",
            "swb": "ᎪᎼᎵᎠᏂ",
            "syr": "ᏏᎵᎠᎩ",
            "ta": "ᏔᎻᎵ",
            "tce": "ᏧᎦᏃᏮ ᏚᏦᏁ",
            "te": "ᏖᎷᎦ",
            "tem": "ᏘᎹᏁ",
            "teo": "ᏖᏐ",
            "tet": "ᏖᏚᎼ",
            "tg": "ᏔᏥᎩ",
            "tgx": "ᏔᎩᏏ",
            "th": "ᏔᏱ",
            "tht": "ᏔᏝᎾ",
            "ti": "ᏘᎩᎵᏂᎠ",
            "tig": "ᏢᏓᏥ",
            "tk": "ᎠᏂᎬᎾ",
            "tlh": "ᏟᎦᎾ",
            "tli": "ᏟᎩᏘ",
            "tn": "ᏧᏩᎾ",
            "to": "ᏙᎾᎦᏂ",
            "tok": "ᏙᎩ ᏉᎾ",
            "tpi": "ᏙᎩ ᏈᏏᏂ",
            "tr": "ᎠᎬᎾ",
            "trv": "ᏔᎶᎪ",
            "ts": "ᏦᎾᎦ",
            "tt": "ᏔᏔ",
            "ttm": "ᏧᏴᏢ ᏗᏜ ᏚᏦᏁ",
            "tum": "ᏛᎹᏊᎧ",
            "tvl": "ᏚᏩᎷ",
            "twq": "ᏔᏌᏩᎩ",
            "ty": "ᏔᎯᏘᎠᏂ",
            "tyv": "ᏚᏫᏂᎠᏂ",
            "tzm": "ᎠᏰᏟ ᎡᎶᎯ ᏓᏟᎶᏍᏗᏓᏅᎢ ᏔᎹᏏᏘ",
            "udm": "ᎤᏚᎷᏘ",
            "ug": "ᏫᎦ",
            "uk": "ᏳᎧᎴᏂᎠᏂ",
            "umb": "ᎤᎹᏊᏅᏚ",
            "und": "ᏄᏬᎵᏍᏛᎾ ᎦᏬᏂᎯᏍᏗ",
            "ur": "ᎤᎵᏚ",
            "uz": "ᎤᏍᏇᎩ",
            "vai": "ᏩᏱ",
            "ve": "ᏫᏂᏓ",
            "vi": "ᏫᎡᏘᎾᎻᏍ",
            "vo": "ᏬᎳᏊᎩ",
            "vun": "ᏭᎾᏦ",
            "wa": "ᏩᎷᎾ",
            "wae": "ᏩᎵᏎᎵ",
            "wal": "ᏬᎳᏱᏔ",
            "war": "ᏩᎴ",
            "wo": "ᏬᎶᏫ",
            "wuu": "Ꮽ ᏓᎶᏂᎨᏍᏛ",
            "xal": "ᎧᎳᎻᎧ",
            "xh": "ᏠᏌ",
            "xog": "ᏐᎦ",
            "yav": "ᏰᎾᎦᏇᏂ",
            "ybb": "ᏰᎹᏋ",
            "yi": "ᏱᏗᏍ",
            "yo": "ᏲᏄᏆ",
            "yrl": "ᏂᎾᎦᏚ",
            "yue": "ᎨᎾᏙᏂᏏ",
            "zgh": "ᎠᏟᎶᏍᏗ ᎼᎶᎪ ᏔᎹᏏᏘ",
            "zh": "ᏓᎶᏂᎨ",
            "zh-Hans": "ᎠᎯᏗᎨ ᏓᎶᏂᎨ",
            "zh-Hant": "ᎤᏦᏍᏗ ᏓᎶᏂᎨ",
            "zu": "ᏑᎷ",
            "zun": "ᏑᏂ",
            "zxx": "Ꮭ ᎦᏬᏂᎯᏍᏗ ᎦᎸᏛᎢ ᏱᎩ",
            "zza": "ᏌᏌ"
          },
          "narrow": {
          },
          "short": {
            "az": "ᎠᏎᎵ",
            "en-GB": "UK ᎩᎵᏏ",
            "en-US": "US ᎩᎵᏏ"
          }
        },
        "standard": {
          "long": {
            "aa": "ᎠᏩᎳ",
            "ab": "ᎠᏆᏏᎠᏂ",
            "ace": "ᎠᏥᏂᏏ",
            "ada": "ᎠᏓᎾᎦᎺ",
            "ady": "ᎠᏗᎨ",
            "af": "ᎠᎬᎿᎨᏍᏛ",
            "agq": "ᎠᎨᎹ",
            "ain": "ᎠᏱᏄ",
            "ak": "ᎠᎧᎾ",
            "ale": "ᎠᎵᎤᏘ",
            "alt": "ᏧᎦᎾᏮ ᏗᏜ ᎠᎵᏔᎢ",
            "am": "ᎠᎹᎭᎵᎩ",
            "an": "ᎠᏩᎪᏂᏏ",
            "ann": "ᎣᏉᎶ",
            "anp": "ᎠᎾᎩᎧ",
            "ar": "ᎡᎳᏈ",
            "ar-001": "ᎡᎳᏈ (ᎡᎶᎯ)",
            "arn": "ᎹᏊᏤ",
            "arp": "ᎠᏩᏈᎰ",
            "ars": "ᎾᏣᏗ ᎠᎳᏈ",
            "as": "ᎠᏌᎻᏏ",
            "asa": "ᎠᏑ",
            "ast": "ᎠᏍᏚᎵᎠᏂ",
            "atj": "ᎠᏂᏘᎧᎺᏆ",
            "av": "ᎠᏩᎵᎧ",
            "awa": "ᎠᏩᏗ",
            "ay": "ᎠᏱᎹᎳ",
            "az": "ᎠᏎᏆᏣᏂ",
            "ba": "ᏆᏍᎯᎩᎠ",
            "ban": "ᏆᎵᏁᏏ",
            "bas": "ᏆᏌᎠ",
            "be": "ᏇᎳᎷᏏ",
            "bem": "ᏇᎹᏆ",
            "bez": "ᏇᎾ",
            "bg": "ᏊᎵᎨᎵᎠᏂ",
            "bho": "ᏉᏣᏊᎵ",
            "bi": "ᏈᏍᎳᎹ",
            "bin": "ᏈᏂ",
            "bla": "ᏏᎩᏏᎧ",
            "bm": "ᏆᎻᏆᎳ",
            "bn": "ᏇᏂᎦᎳ",
            "bo": "ᏘᏇᏔᏂ",
            "br": "ᏇᏙᏂ",
            "brx": "ᏉᏙ",
            "bs": "ᏆᏍᏂᎠᏂ",
            "bug": "ᏈᎥᎩᏂᏍ",
            "byn": "ᏟᏂ",
            "ca": "ᎨᏔᎳᏂ",
            "cay": "ᎧᏳᎦ",
            "ccp": "ᏣᎧᎹ",
            "ce": "ᏤᏤᏂ",
            "ceb": "ᏎᏆᏃ",
            "cgg": "ᏥᎦ",
            "ch": "ᏣᎼᎶ",
            "chk": "ᏧᎨᏎ",
            "chm": "ᎹᎵ",
            "cho": "ᎠᏣᏓ",
            "chp": "ᎠᏥᏇᏯᏂ",
            "chr": "ᏣᎳᎩ",
            "chy": "ᏣᏰᏂ",
            "ckb": "ᎠᏰᏟ ᎫᏗᏏ",
            "clc": "ᏥᎸᎪᏘᎾ",
            "co": "ᎪᎵᏍᎢᎧᏂ",
            "crg": "ᎻᏥᏩ",
            "crj": "ᏧᎦᏃᏮ ᏗᎧᎸᎬ Ꮯ",
            "crk": "ᏠᎨᏏ Ꮯ",
            "crl": "ᏧᏴᏢ ᏗᎧᎸᎬ Ꮯ",
            "crm": "ᎠᏫ ᎡᏆ Ꮯ",
            "crr": "ᎠᎵᎦᏂᏈᎠᎾ",
            "crs": "ᏎᏎᎵᏩ ᏟᏲᎵ ᎠᏂᎦᎸ",
            "cs": "ᏤᎩ",
            "csw": "ᏌᎷᏱ Ꮯ",
            "cu": "ᏧᏂᎳᏫᏍᏗ ᏍᎳᏫᎪ",
            "cv": "ᏧᏩᏏ",
            "cy": "ᏪᎵᏏ",
            "da": "ᏕᏂᏍ",
            "dak": "ᏓᎪᏔ",
            "dar": "ᏓᎳᏆ",
            "dav": "ᏔᎢᏔ",
            "de": "ᏙᎢᏥ",
            "de-AT": "ᏙᎢᏥ (ᎠᏍᏟᏯ)",
            "de-CH": "ᏙᎢᏥ (ᏍᏫᏍ)",
            "dgr": "ᎩᏟ ᎤᏄᎳᏥ",
            "dje": "ᏌᎹ",
            "doi": "ᏙᎦᎵ",
            "dsb": "ᎡᎳᏗ ᏐᏈᎠᏂ",
            "dua": "ᏚᎠᎳ",
            "dv": "ᏗᏪᎯ",
            "dyo": "ᏦᎳ-ᏬᏱ",
            "dz": "ᏓᏐᏅᎧ",
            "dzg": "ᏓᏌᎦ",
            "ebu": "ᎡᎻᏊ",
            "ee": "ᎡᏪ",
            "efi": "ᎡᏫᎩ",
            "eka": "ᎨᎧᏧᎧ",
            "el": "ᎠᏂᎪᎢ",
            "en": "ᎩᎵᏏ",
            "en-AU": "ᎩᎵᏏ (ᎡᎳᏗᏜ)",
            "en-CA": "ᎩᎵᏏ (ᎨᎾᏓ)",
            "en-GB": "ᎩᎵᏏ (ᎩᎵᏏᏲ)",
            "en-US": "ᎩᎵᏏ (ᏌᏊ ᎢᏳᎾᎵᏍᏔᏅ ᏍᎦᏚᎩ)",
            "eo": "ᎡᏍᏇᎳᏂᏙ",
            "es": "ᏍᏆᏂ",
            "es-419": "ᏍᏆᏂ (ᎳᏘᏂ ᎠᎹᏰᏟ)",
            "es-ES": "ᏍᏆᏂ (ᎠᏂᏍᏆᏂᏱ)",
            "es-MX": "ᏍᏆᏂ (ᎠᏂᏍᏆᏂ)",
            "et": "ᎡᏍᏙᏂᎠᏂ",
            "eu": "ᏆᏍᎨ",
            "ewo": "ᎡᏬᏂᏙ",
            "fa": "ᏇᏏᎠᏂ",
            "fa-AF": "ᏇᏏᎠᏂ (ᎠᏫᎨᏂᏍᏖᏂ)",
            "ff": "ᏊᎳᏂ",
            "fi": "ᏈᏂᏍ",
            "fil": "ᎠᏈᎵᎩ",
            "fj": "ᏫᏥᎠᏂ",
            "fo": "ᏇᎶᎡᏍ",
            "fon": "ᏠᏂ",
            "fr": "ᎦᎸᏥ",
            "fr-CA": "ᎦᎸᏥ (ᎨᎾᏓ)",
            "fr-CH": "ᎦᎸᏥ (ᏍᏫᏍ)",
            "frc": "ᎨᏨᏂ ᎦᎸᏥ",
            "frr": "ᏧᏴᏢ ᎷᏈ",
            "fur": "ᏞᎤᎵᎠᏂ",
            "fy": "ᏭᏕᎵᎬ ᏗᏜ ᏟᏏᎠᏂ",
            "ga": "ᎨᎵᎩ",
            "gaa": "Ꭶ",
            "gd": "ᏍᎦᏗ ᎨᎵᎩ",
            "gez": "ᎩᏏ",
            "gil": "ᎩᏇᏘᏏ",
            "gl": "ᎦᎵᏏᎠᏂ",
            "gn": "ᏆᎳᏂ",
            "gor": "ᎪᎶᏂᏔᏃ",
            "gsw": "ᏍᏫᏏ ᎠᏂᏓᏥ",
            "gu": "ᎫᏣᎳᏘ",
            "guz": "ᎫᏏ",
            "gv": "ᎹᎾᎧᏏ",
            "gwi": "ᏈᏥᏂ",
            "ha": "ᎭᎤᏌ",
            "hai": "ᎭᏱᏓ",
            "haw": "ᎭᏩᎼ",
            "hax": "ᏧᎦᏃᏮ ᏗᏜ ᎭᏱᏓ",
            "he": "ᎠᏂᏈᎷ",
            "hi": "ᎯᏂᏗ",
            "hil": "ᎯᎵᎨᎾᏂ",
            "hmn": "ᎭᎼᏂᎩ",
            "hr": "ᎧᎶᎡᏏᏂ",
            "hsb": "ᎦᎸᎳᏗᎨ ᏐᏈᎠᏂ",
            "ht": "ᎮᏏᎠᏂ ᏟᏲᎵ",
            "hu": "ᎲᏂᎦᎵᎠᏂ",
            "hup": "ᎠᏂᎱᏆ",
            "hur": "ᎭᎵᎪᎺᎴᎻ",
            "hy": "ᎠᎳᎻᎠᏂ",
            "hz": "ᎮᎴᎶ",
            "ia": "ᎠᏰᏟ ᎦᏬᏂᎯᏍᏗ",
            "iba": "ᎢᏆᏂ",
            "ibb": "ᎢᏈᏈᎣ",
            "id": "ᎢᏂᏙᏂᏏᎠ",
            "ig": "ᎢᎦᎪ",
            "ii": "ᏏᏧᏩᏂ Ᏹ",
            "ikt": "ᏭᏕᎵᎬ ᎨᎾᏓ ᎢᏄᎩᏘᏚᏘ",
            "ilo": "ᎢᎶᎪ",
            "inh": "ᎢᏂᎫᏏ",
            "io": "ᎢᏙ",
            "is": "ᏧᏁᏍᏓᎸᎯᎢᎩ",
            "it": "ᎬᏩᎵᏲᏥᎢ",
            "iu": "ᎢᏄᎦᏘᏚ",
            "ja": "ᏣᏩᏂᏏ",
            "jbo": "ᎶᏣᏆᏂ",
            "jgo": "ᎾᎪᏆ",
            "jmc": "ᎹᏣᎺ",
            "jv": "ᏆᏌ ᏣᏩ",
            "ka": "ᏦᏥᎠᏂ",
            "kab": "ᎧᏈᎴ",
            "kac": "ᎧᏥᏂ",
            "kaj": "ᏥᏧ",
            "kam": "ᎧᎻᏆ",
            "kbd": "ᎧᏆᏗᎠᏂ",
            "kcg": "ᏔᏯᏆ",
            "kde": "ᎹᎪᏕ",
            "kea": "ᎧᏊᏪᏗᎠᏄ",
            "kfo": "ᎪᎶ",
            "kgp": "ᎨᏂᎨᏂ",
            "kha": "ᎧᏏ",
            "khq": "ᎪᏱᎳ ᏥᏂ",
            "ki": "ᎩᎫᏳ",
            "kj": "ᎫᏩᏂᎠᎹ",
            "kk": "ᎧᏌᎧ",
            "kkj": "ᎧᎪ",
            "kl": "ᎧᎳᎵᏑᏘ",
            "kln": "ᎧᎴᏂᏥᏂ",
            "km": "ᎩᎻᎷ",
            "kmb": "ᎩᎻᏊᏚ",
            "kn": "ᎧᎾᏓ",
            "ko": "ᎪᎵᎠᏂ",
            "kok": "ᎧᏂᎧᏂ",
            "kpe": "ᏇᎴ",
            "kr": "ᎧᏄᎵ",
            "krc": "ᎧᎳᏣᏱ-ᏆᎵᎧᎵ",
            "krl": "ᎧᎴᎵᎠᏂ",
            "kru": "ᎫᎷᎩ",
            "ks": "ᎧᏏᎻᎵ",
            "ksb": "ᏝᎻᏆᎸ",
            "ksf": "ᏆᏫᎠ",
            "ksh": "ᎪᎶᏂᎠᏂ",
            "ku": "ᎫᏗᏏ",
            "kum": "ᎫᎻᎧ",
            "kv": "ᎪᎻ",
            "kw": "ᏎᎷᎭ",
            "kwk": "ᏆᏆᎳ",
            "ky": "ᎩᎵᏣᎢᏍ",
            "la": "ᎳᏘᏂ",
            "lad": "ᎳᏗᏃ",
            "lag": "ᎳᏂᎩ",
            "lb": "ᎸᎦᏏᎻᏋᎢᏍ",
            "lez": "ᎴᏏᎦᏂ",
            "lg": "ᎦᏂᏓ",
            "li": "ᎴᎹᏊᎵᏏ",
            "lil": "ᎵᎶᎡᏘ",
            "lkt": "ᎳᎪᏓ",
            "ln": "ᎵᏂᎦᎳ",
            "lo": "ᎳᎣ",
            "lou": "ᎷᏫᏏᎡᎾ ᎦᏬᏂᎯᏍᏗ",
            "loz": "ᎶᏏ",
            "lrc": "ᏧᏴᏢ ᏗᏜ ᎷᎵ",
            "lsm": "ᏌᎠᎻᎠ",
            "lt": "ᎵᏚᏩᏂᎠᏂ",
            "lu": "ᎷᏆ-ᎧᏔᎦ",
            "lua": "ᎷᏆ-ᎷᎷᎠ",
            "lun": "ᎷᎾᏓ",
            "luo": "ᎷᎣ",
            "lus": "ᎻᏐ",
            "luy": "ᎷᏱᎠ",
            "lv": "ᎳᏘᏫᎠᏂ",
            "mad": "ᎹᏚᎴᏏ",
            "mag": "ᎹᎦᎯ",
            "mai": "ᎹᏟᎵ",
            "mak": "ᎹᎧᏌ",
            "mas": "ᎹᏌᏱ",
            "mdf": "ᎼᎧᏌ",
            "men": "ᎺᎾᏕ",
            "mer": "ᎺᎷ",
            "mfe": "ᎼᎵᏏᎡᏂ",
            "mg": "ᎹᎳᎦᏏ",
            "mgh": "ᎹᎫᏩ-ᎻᏙ",
            "mgo": "ᎺᎳ’",
            "mh": "ᎹᏌᎵᏏ",
            "mi": "ᎹᏫ",
            "mic": "ᎻᎧᎹᎩ",
            "min": "ᎻᎾᎧᏆᎤ",
            "mk": "ᎹᏎᏙᏂᎠᏂ",
            "ml": "ᎹᎳᏯᎳᎻ",
            "mn": "ᎹᏂᎪᎵᎠᏂ",
            "mni": "ᎺᏂᏉᎵ",
            "moe": "ᎢᏄ-ᎠᏱᏵᏂ",
            "moh": "ᎼᎭᎩ",
            "mos": "ᎼᏍᏏ",
            "mr": "ᎹᎳᏘ",
            "ms": "ᎹᎴ",
            "mt": "ᎹᎵᏘᏍ",
            "mua": "ᎽᏂᏓᎩ",
            "mul": "ᏧᏈᏍᏗ ᏗᎦᏬᏂᎯᏍᏗ",
            "mus": "ᎠᎫᏌ",
            "mwl": "ᎻᎳᏕᏏ",
            "my": "ᏋᎻᏍ",
            "myv": "ᎡᏏᏯ",
            "mzn": "ᎹᏌᏕᎳᏂ",
            "na": "ᏃᎤᎷ",
            "nap": "ᏂᏯᏆᎵᏔᏂ",
            "naq": "ᎾᎹ",
            "nb": "ᏃᎵᏪᏥᏂ ᏉᎧᎹᎵ",
            "nd": "ᏧᏴᏢ ᏂᏕᏇᎴ",
            "nds": "ᎡᎳᏗ ᎠᏂᏓᏥ",
            "nds-NL": "ᎡᎳᏗ ᎠᏂᏓᏥ (ᏁᏛᎳᏂ)",
            "ne": "ᏁᏆᎵ",
            "new": "ᏁᏩᎵ",
            "ng": "ᎾᏙᎦ",
            "nia": "ᏂᎠᏏ",
            "niu": "ᏂᏳᏫᏯᏂ",
            "nl": "ᏛᏥ",
            "nl-BE": "ᏛᏥ (ᏇᎵᏥᎥᎻ)",
            "nmg": "ᏆᏏᏲ",
            "nn": "ᏃᎵᏪᏥᏂ ᎾᎵᏍᎩ",
            "nnh": "ᎾᏥᏰᎹᏊᏂ",
            "no": "ᏃᎵᏪᏥᏂ",
            "nog": "ᏃᎦᏱ",
            "nqo": "ᎾᎪ",
            "nr": "ᏧᎦᎾᏮ ᏂᏕᏇᎴ",
            "nso": "ᏧᏴᏢ ᏗᏜ ᏐᏠ",
            "nus": "ᏄᏪᎵ",
            "nv": "ᎾᏩᎰ",
            "ny": "ᏂᏯᏂᏣ",
            "nyn": "ᏂᏯᎾᎪᎴ",
            "oc": "ᎠᏏᏔᏂ",
            "ojb": "ᏧᏴᏢ ᏭᏕᎵᎬ ᎣᏥᏆ",
            "ojc": "ᎠᏰᏟ ᎣᏥᏆ",
            "ojs": "ᎣᏥ-Ꮯ",
            "ojw": "ᏭᏕᎵᎬ ᏗᏜ ᎣᏥᏆ",
            "oka": "ᎣᎧᎾᎦᏂ",
            "om": "ᎣᎶᎼ",
            "or": "ᎣᏗᎠ",
            "os": "ᎣᏎᏘᎧ",
            "pa": "ᏡᏂᏣᏈ",
            "pag": "ᏇᎦᏏᎠᏂ",
            "pam": "ᏆᎹᏆᎾᎦ",
            "pap": "ᏆᏈᏯᎺᎾᏙ",
            "pau": "ᏆᎳᎤᏩᏂ",
            "pcm": "ᎾᎩᎵᎠᏂ ᏈᏥᏂ",
            "pis": "ᏈᏥᎾ",
            "pl": "ᏉᎵᏍ",
            "pqm": "ᎹᎵᏏᏘ-ᏇᏌᎹᏉᏗ",
            "prg": "ᏡᏏᎠᏂ",
            "ps": "ᏆᏍᏙ",
            "pt": "ᏉᏧᎩᏍ",
            "pt-BR": "ᏉᏧᎩᏍ (ᏆᏏᎵ)",
            "pt-PT": "ᏉᏧᎩᏍ (ᏉᏥᎦᎳ)",
            "qu": "ᎨᏧᏩ",
            "quc": "ᎩᏤ",
            "rap": "ᎳᏆᏄᏫ",
            "rar": "ᎳᎶᏙᎾᎦᏂ",
            "rhg": "ᎶᎯᏂᏯ",
            "rm": "ᎠᏂᎶᎺᏂ",
            "rn": "ᎷᏂᏗ",
            "ro": "ᎶᎹᏂᎠᏂ",
            "ro-MD": "ᎶᎹᏂᎠᏂ (ᎹᎵᏙᏫᎠ)",
            "rof": "ᎶᎹᏉ",
            "ru": "ᏲᏅᎯ",
            "rup": "ᎠᏬᎹᏂᎠᏂ",
            "rw": "ᎩᏂᏯᏩᏂᏓ",
            "rwk": "Ꮖ",
            "sa": "ᏍᏂᏍᎩᏗ",
            "sad": "ᏌᏅᏓᏫ",
            "sah": "ᏌᎧᎾ",
            "saq": "ᏌᎹᏊᎷ",
            "sat": "ᏌᏂᏔᎵ",
            "sba": "ᎾᎦᎹᏇ",
            "sbp": "ᏌᏁᎫ",
            "sc": "ᏌᏗᏂᎠᏂ",
            "scn": "ᏏᏏᎵᎠᏂ",
            "sco": "ᏍᎦᏗ",
            "sd": "ᏏᏂᏗ",
            "se": "ᏧᏴᏢ ᏗᏜ ᏌᎻ",
            "see": "ᏏᏂᎦ",
            "seh": "ᏎᎾ",
            "ses": "ᎪᏱᎳᏈᎶ ᏎᏂ",
            "sg": "ᏌᏂᎪ",
            "shi": "ᏔᏤᎵᎯᏘ",
            "shn": "ᏝᏂ",
            "si": "ᏏᎾᎭᎳ",
            "sk": "ᏍᎶᏩᎩ",
            "sl": "ᏍᎶᏫᏂᎠᏂ",
            "slh": "ᏧᎦᏃᏮ ᏗᏜ ᎷᏑᏘᏏᏗ",
            "sm": "ᏌᎼᏯᏂ",
            "sma": "ᏧᎦᎾᏮ ᏗᏜ ᏌᎻ",
            "smj": "ᎷᎴ ᏌᎻ",
            "smn": "ᎢᎾᎵ ᏌᎻ",
            "sms": "ᏍᎪᎵᏘ ᏌᎻ",
            "sn": "ᏠᎾ",
            "snk": "ᏐᏂᏂᎨ",
            "so": "ᏐᎹᎵ",
            "sq": "ᎠᎵᏇᏂ",
            "sr": "ᏒᏈᎠᏂ",
            "srn": "ᏏᎳᎾᏂ ᏙᏃᎪ",
            "ss": "ᏍᏩᏘ",
            "ssy": "ᏌᎰ",
            "st": "ᏧᎦᎾᏮ ᏗᏜ ᏐᏠ",
            "str": "ᏌᎵᏏ",
            "su": "ᏑᏂᏓᏂᏏ",
            "suk": "ᏑᎫᎹ",
            "sv": "ᏍᏫᏗᏏ",
            "sw": "ᏍᏩᎯᎵ",
            "sw-CD": "ᏍᏩᎯᎵ (ᎧᏂᎪ - ᎨᏂᏝᏌ)",
            "swb": "ᎪᎼᎵᎠᏂ",
            "syr": "ᏏᎵᎠᎩ",
            "ta": "ᏔᎻᎵ",
            "tce": "ᏧᎦᏃᏮ ᏚᏦᏁ",
            "te": "ᏖᎷᎦ",
            "tem": "ᏘᎹᏁ",
            "teo": "ᏖᏐ",
            "tet": "ᏖᏚᎼ",
            "tg": "ᏔᏥᎩ",
            "tgx": "ᏔᎩᏏ",
            "th": "ᏔᏱ",
            "tht": "ᏔᏝᎾ",
            "ti": "ᏘᎩᎵᏂᎠ",
            "tig": "ᏢᏓᏥ",
            "tk": "ᎠᏂᎬᎾ",
            "tlh": "ᏟᎦᎾ",
            "tli": "ᏟᎩᏘ",
            "tn": "ᏧᏩᎾ",
            "to": "ᏙᎾᎦᏂ",
            "tok": "ᏙᎩ ᏉᎾ",
            "tpi": "ᏙᎩ ᏈᏏᏂ",
            "tr": "ᎠᎬᎾ",
            "trv": "ᏔᎶᎪ",
            "ts": "ᏦᎾᎦ",
            "tt": "ᏔᏔ",
            "ttm": "ᏧᏴᏢ ᏗᏜ ᏚᏦᏁ",
            "tum": "ᏛᎹᏊᎧ",
            "tvl": "ᏚᏩᎷ",
            "twq": "ᏔᏌᏩᎩ",
            "ty": "ᏔᎯᏘᎠᏂ",
            "tyv": "ᏚᏫᏂᎠᏂ",
            "tzm": "ᎠᏰᏟ ᎡᎶᎯ ᏓᏟᎶᏍᏗᏓᏅᎢ ᏔᎹᏏᏘ",
            "udm": "ᎤᏚᎷᏘ",
            "ug": "ᏫᎦ",
            "uk": "ᏳᎧᎴᏂᎠᏂ",
            "umb": "ᎤᎹᏊᏅᏚ",
            "und": "ᏄᏬᎵᏍᏛᎾ ᎦᏬᏂᎯᏍᏗ",
            "ur": "ᎤᎵᏚ",
            "uz": "ᎤᏍᏇᎩ",
            "vai": "ᏩᏱ",
            "ve": "ᏫᏂᏓ",
            "vi": "ᏫᎡᏘᎾᎻᏍ",
            "vo": "ᏬᎳᏊᎩ",
            "vun": "ᏭᎾᏦ",
            "wa": "ᏩᎷᎾ",
            "wae": "ᏩᎵᏎᎵ",
            "wal": "ᏬᎳᏱᏔ",
            "war": "ᏩᎴ",
            "wo": "ᏬᎶᏫ",
            "wuu": "Ꮽ ᏓᎶᏂᎨᏍᏛ",
            "xal": "ᎧᎳᎻᎧ",
            "xh": "ᏠᏌ",
            "xog": "ᏐᎦ",
            "yav": "ᏰᎾᎦᏇᏂ",
            "ybb": "ᏰᎹᏋ",
            "yi": "ᏱᏗᏍ",
            "yo": "ᏲᏄᏆ",
            "yrl": "ᏂᎾᎦᏚ",
            "yue": "ᎨᎾᏙᏂᏏ",
            "zgh": "ᎠᏟᎶᏍᏗ ᎼᎶᎪ ᏔᎹᏏᏘ",
            "zh": "ᏓᎶᏂᎨ",
            "zh-Hans": "ᎠᎯᏗᎨ ᏓᎶᏂᎨ",
            "zh-Hant": "ᎤᏦᏍᏗ ᏓᎶᏂᎨ",
            "zu": "ᏑᎷ",
            "zun": "ᏑᏂ",
            "zxx": "Ꮭ ᎦᏬᏂᎯᏍᏗ ᎦᎸᏛᎢ ᏱᎩ",
            "zza": "ᏌᏌ"
          },
          "narrow": {
          },
          "short": {
            "az": "ᎠᏎᎵ",
            "en-GB": "ᎩᎵᏏ (UK)",
            "en-US": "ᎩᎵᏏ (US)"
          }
        }
      },
      "region": {
        "long": {
          "001": "ᎡᎶᎯ",
          "002": "ᎬᎿᎨᏍᏛ",
          "003": "ᏧᏴᏢ ᎠᎹᏰᏟ",
          "005": "ᏧᎦᏃᏮ ᎠᎺᎵᎦ",
          "009": "ᎣᏏᏰᏂᎠ",
          "011": "ᏭᏕᎵᎬ ᏗᏜ ᎬᎿᎨᏍᏛ",
          "013": "ᎠᏰᏟ ᎠᎹᏰᏟ",
          "014": "ᏗᎧᎸᎬ ᏗᏜ ᎬᎿᎨᏍᏛ",
          "015": "ᏧᏴᏢ ᏗᏜ ᎬᎿᎨᏍᏛ",
          "017": "ᎠᏰᏟ ᎬᎿᎨᏍᏛ",
          "018": "ᏧᎦᎾᏮ ᏗᏜ ᎬᎿᎨᏍᏛ",
          "019": "ᎠᎺᎵᎦᎢ",
          "021": "ᏧᏴᏢ ᏗᏜ ᎠᎹᏰᏟ",
          "029": "ᎨᏆᏙᏯ",
          "030": "ᏗᎧᎸᎬ ᏗᏜ ᏓᎶᏂᎨᏍᏛ",
          "034": "ᏧᎦᎾᏮ ᏗᏜ ᏓᎶᏂᎨᏍᏛ",
          "035": "ᏧᎦᎾᏮ ᏗᎧᎸᎬ ᏓᎶᏂᎨᏍᏛ",
          "039": "ᏧᎦᎾᏮ ᏗᏜ ᏳᎳᏛ",
          "053": "ᎠᏍᏔᎴᏏᎠ",
          "054": "ᎺᎳᏁᏏᎠ",
          "057": "ᎠᏰᏟ ᏧᎾᎵᎪᎯ ᎾᎿ ᎹᎢᏉᏂᏏᏯ ᎢᎬᎾᏕᎾ",
          "061": "ᏆᎵᏂᏏᎠ",
          "142": "ᏓᎶᎾᎨᏍᏛ",
          "143": "ᎠᏰᏟ ᏓᎶᏂᎨᏍᏛ",
          "145": "ᏭᏕᎵᎬ ᏗᏜ ᏓᎶᏂᎨᏍᏛ",
          "150": "ᏳᎳᏛ",
          "151": "ᏗᎧᎸᎬ ᏗᏜ ᏳᎳᏛ",
          "154": "ᏧᏴᏢ ᏗᏜ ᏳᎳᏛ",
          "155": "ᏭᏕᎵᎬ ᏗᏜ ᏳᎳᏛ",
          "202": "ᎭᏫᏂ-ᏌᎭᏩ ᎬᎿᎨᏍᏛ",
          "419": "ᎳᏘᏂ ᎠᎹᏰᏟ",
          "AC": "ᎤᎵᏌᎳᏓᏅ ᎤᎦᏚᏛᎢ",
          "AD": "ᎠᏂᏙᎳ",
          "AE": "ᏌᏊ ᎢᏳᎾᎵᏍᏔᏅ ᎡᎳᏈ ᎢᎹᎵᏘᏏ",
          "AF": "ᎠᏫᎨᏂᏍᏖᏂ",
          "AG": "ᎤᏪᏘ & ᏆᏊᏓ",
          "AI": "ᎠᏂᎩᎳ",
          "AL": "ᎠᎵᏇᏂᏯ",
          "AM": "ᎠᎵᎻᏂᎠ",
          "AO": "ᎠᏂᎪᎳ",
          "AQ": "ᏧᏁᏍᏓᎸ",
          "AR": "ᎠᏥᏂᏘᏂᎠ",
          "AS": "ᎠᎺᎵᎧ ᏌᎼᎠ",
          "AT": "ᎠᏍᏟᏯ",
          "AU": "ᎡᎳᏗᏜ",
          "AW": "ᎠᎷᏆ",
          "AX": "ᎣᎴᏅᏓ ᏚᎦᏚᏛᎢ",
          "AZ": "ᎠᏎᏆᏣᏂ",
          "BA": "ᏉᏏᏂᎠ & ᎲᏤᎪᏫᎾ",
          "BB": "ᏆᏇᏙᏍ",
          "BD": "ᏆᏂᎦᎵᏕᏍ",
          "BE": "ᏇᎵᏥᎥᎻ",
          "BF": "ᏋᎩᎾ ᏩᏐ",
          "BG": "ᏊᎵᎨᎵᎠ",
          "BH": "ᏆᎭᎴᎢᏂ",
          "BI": "ᏋᎷᏂᏗ",
          "BJ": "ᏆᏂᎢᏂ",
          "BL": "ᎤᏓᏅᏘ ᏆᏕᎳᎻ",
          "BM": "ᏆᏊᏓ",
          "BN": "ᏊᎾᎢ",
          "BO": "ᏉᎵᏫᎠ",
          "BQ": "ᎧᎵᏈᎢᏂᎯ ᎾᏍᎩᏁᏛᎳᏂ",
          "BR": "ᏆᏏᎵ",
          "BS": "ᎾᏍᎩ ᏆᎭᎹᏍ",
          "BT": "ᏊᏔᏂ",
          "BV": "ᏊᏪ ᎤᎦᏚᏛᎢ",
          "BW": "ᏆᏣᏩᎾ",
          "BY": "ᏇᎳᎷᏍ",
          "BZ": "ᏇᎵᏍ",
          "CA": "ᎨᎾᏓ",
          "CC": "ᎪᎪᏍ (ᎩᎵᏂ) ᏚᎦᏚᏛᎢ",
          "CD": "ᎧᏂᎪ - ᎨᏂᏝᏌ",
          "CF": "ᎬᎿᎨᏍᏛ ᎠᏰᏟ ᏍᎦᏚᎩ",
          "CG": "ᎧᏂᎪ - ᏆᏌᏩᎵ",
          "CH": "ᏍᏫᏍ",
          "CI": "ᎢᏬᎵ ᎾᎿ ᎠᎹᏳᎶᏗ",
          "CK": "ᎠᏓᏍᏓᏴᎲᏍᎩ ᏚᎦᏚᏛᎢ",
          "CL": "ᏥᎵ",
          "CM": "ᎧᎹᎷᏂ",
          "CN": "ᏓᎶᏂᎨᏍᏛ",
          "CO": "ᎪᎸᎻᏈᎢᎠ",
          "CP": "ᎦᏂᏴᏔᏅᎣᏓᎸ ᎤᎦᏚᏛᎢ",
          "CR": "ᎪᏍᏓ ᎵᎧ",
          "CU": "ᎫᏆ",
          "CV": "ᎢᎬᎾᏕᎾ ᎢᏤᏳᏍᏗ",
          "CW": "ᎫᎳᎨᎣ",
          "CX": "ᏓᏂᏍᏓᏲᎯᎲ ᎤᎦᏚᏛᎢ",
          "CY": "ᏌᎢᏆᏍ",
          "CZ": "ᏤᎩᎠ",
          "DE": "ᎠᏂᏛᏥ",
          "DG": "ᏗᏰᎪ ᎦᏏᏯ",
          "DJ": "ᏥᏊᏗ",
          "DK": "ᏗᏂᎹᎦ",
          "DM": "ᏙᎻᏂᎧ",
          "DO": "ᏙᎻᏂᎧᏂ ᏍᎦᏚᎩ",
          "DZ": "ᎠᎵᏥᎵᏯ",
          "EA": "ᏑᏔ ᎠᎴ ᎺᎵᏯ",
          "EC": "ᎡᏆᏙᎵ",
          "EE": "ᎡᏍᏙᏂᏯ",
          "EG": "ᎢᏥᏈᎢ",
          "EH": "ᏭᏕᎵᎬ ᏗᏜ ᏌᎮᎳ",
          "ER": "ᎡᎵᏟᏯ",
          "ES": "ᎠᏂᏍᏆᏂᏱ",
          "ET": "ᎢᏗᎣᏈᎠ",
          "EU": "ᏳᎳᏛ ᎠᏂᎤᎾᏓᏡᎬ",
          "EZ": "ᏳᎶᎠᏍᏓᏅᏅ",
          "FI": "ᏫᏂᎦᏙᎯ",
          "FJ": "ᏫᏥ",
          "FK": "ᏩᎩ ᏚᎦᏚᏛᎢ",
          "FM": "ᎹᎢᏉᏂᏏᏯ",
          "FO": "ᏪᎶ ᏚᎦᏚᏛᎢ",
          "FR": "ᎦᎸᏥᏱ",
          "GA": "ᎦᏉᏂ",
          "GB": "ᎩᎵᏏᏲ",
          "GD": "ᏋᎾᏓ",
          "GE": "ᏣᎠᏥᎢ",
          "GF": "ᎠᏂᎦᎸᏥ ᎩᎠ",
          "GG": "ᎬᏂᏏ",
          "GH": "ᎦᎠᎾ",
          "GI": "ᏥᏆᎵᏓ",
          "GL": "ᎢᏤᏍᏛᏱ",
          "GM": "ᎦᎹᏈᎢᎠ",
          "GN": "ᎩᎢᏂ",
          "GP": "ᏩᏓᎷᏇ",
          "GQ": "ᎡᏆᏙᎵᎠᎵ ᎩᎢᏂ",
          "GR": "ᎪᎢᎯ",
          "GS": "ᏧᎦᏃᏮ ᏣᎠᏥᎢ ᎠᎴ ᎾᏍᎩ ᏧᎦᏃᏮ ᎠᏍᏛᎭᏟ ᏚᎦᏚᏛᎢ",
          "GT": "ᏩᏔᎹᎳ",
          "GU": "ᏆᎻ",
          "GW": "ᎩᎢᏂ-ᏈᏌᎤᏫ",
          "GY": "ᎦᏯᎾ",
          "HK": "ᎰᏂᎩ ᎪᏂᎩ ᎤᏓᏤᎵᏓ ᏧᏂᎸᏫᏍᏓᏁᏗ ᎢᎬᎾᏕᎾ ᏓᎶᏂᎨᏍᏛ",
          "HM": "ᎲᏗ ᎤᎦᏚᏛᎢ ᎠᎴ ᎺᎩᏓᎾᎵᏗ ᏚᎦᏚᏛᎢ",
          "HN": "ᎭᏂᏚᎳᏍ",
          "HR": "ᎧᎶᎡᏏᎠ",
          "HT": "ᎮᎢᏘ",
          "HU": "ᎲᏂᎦᎵ",
          "IC": "ᏥᏍᏆ ᏚᎦᏚᏛᎢ",
          "ID": "ᎢᏂᏙᏂᏍᏯ",
          "IE": "ᎠᏲᎳᏂ",
          "IL": "ᎢᏏᎵᏱ",
          "IM": "ᎤᏍᏗ ᎤᎦᏚᏛᎢ ᎾᎿ ᎠᏍᎦᏯ",
          "IN": "ᎢᏅᏗᎾ",
          "IO": "ᏈᏗᏏ ᏴᏫᏯ ᎠᎺᏉ ᎢᎬᎾᏕᏅ",
          "IQ": "ᎢᎳᎩ",
          "IR": "ᎢᎴᏂ",
          "IS": "ᏧᏁᏍᏓᎸᎯ",
          "IT": "ᎢᏔᎵ",
          "JE": "ᏨᎵᏏ",
          "JM": "ᏣᎺᎢᎧ",
          "JO": "ᏦᏓᏂ",
          "JP": "ᏣᏩᏂᏏ",
          "KE": "ᎨᏂᏯ",
          "KG": "ᎩᎵᏣᎢᏍ",
          "KH": "ᎧᎹᏉᏗᎠᏂ",
          "KI": "ᎧᎵᏆᏘ",
          "KM": "ᎪᎼᎳᏍ",
          "KN": "ᎤᏓᏅᏘ ᎨᏘᏏ ᎠᎴ ᏁᏪᏏ",
          "KP": "ᏧᏴᏢ ᎪᎵᎠ",
          "KR": "ᏧᎦᏃᏮ ᎪᎵᎠ",
          "KW": "ᎫᏪᎢᏘ",
          "KY": "ᎨᎢᎹᏂ ᏚᎦᏚᏛᎢ",
          "KZ": "ᎧᏎᎧᏍᏕᏂ",
          "LA": "ᎴᎣᏍ",
          "LB": "ᎴᏆᎾᏂ",
          "LC": "ᎤᏓᏅᏘ ᎷᏏᏯ",
          "LI": "ᎵᎦᏗᏂᏍᏓᏂ",
          "LK": "ᏍᎵ ᎳᏂᎧ",
          "LR": "ᎳᏈᎵᏯ",
          "LS": "ᎴᏐᏙ",
          "LT": "ᎵᏗᏪᏂᎠ",
          "LU": "ᎸᎧᏎᏋᎩ",
          "LV": "ᎳᏘᏫᎠ",
          "LY": "ᎵᏈᏯ",
          "MA": "ᎼᎶᎪ",
          "MC": "ᎹᎾᎪ",
          "MD": "ᎹᎵᏙᏫᎠ",
          "ME": "ᎼᏂᏔᏁᎦᎶ",
          "MF": "ᎤᏓᏅᏘ ᏡᏡ",
          "MG": "ᎹᏓᎦᏍᎧᎵ",
          "MH": "ᎹᏌᎵ ᏚᎦᏚᏛᎢ",
          "MK": "ᏧᏴᏜ ᎹᏎᏙᏂᏯ",
          "ML": "ᎹᎵ",
          "MM": "ᎹᏯᎹᎵ (ᏇᎵᎹ)",
          "MN": "ᎹᏂᎪᎵᎠ",
          "MO": "ᎹᎧᎣ (ᎤᏓᏤᎵᏓ ᏧᏂᎸᏫᏍᏓᏁᏗ ᎢᎬᎾᏕᎾ) ᏣᎢ",
          "MP": "ᏧᏴᏢ ᏗᏜ ᎹᎵᎠᎾ ᏚᎦᏚᏛᎢ",
          "MQ": "ᎹᏘᏂᎨ",
          "MR": "ᎹᏘᎢᏯ",
          "MS": "ᎹᏂᏘᏌᎳᏗ",
          "MT": "ᎹᎵᏔ",
          "MU": "ᎼᎵᏏᎥᏍ",
          "MV": "ᎹᎵᏗᏫᏍ",
          "MW": "ᎹᎳᏫ",
          "MX": "ᎠᏂᏍᏆᏂ",
          "MY": "ᎹᎴᏏᎢᎠ",
          "MZ": "ᎼᏎᎻᏇᎩ",
          "NA": "ᎾᎻᏈᎢᏯ",
          "NC": "ᎢᏤ ᎧᎵᏙᏂᎠᏂ",
          "NE": "ᎾᎢᏨ",
          "NF": "ᏃᎵᏬᎵᎩ ᎤᎦᏚᏛᎢ",
          "NG": "ᏂᏥᎵᏯ",
          "NI": "ᏂᎧᎳᏆ",
          "NL": "ᏁᏛᎳᏂ",
          "NO": "ᏃᏪ",
          "NP": "ᏁᏆᎵ",
          "NR": "ᏃᎤᎷ",
          "NU": "ᏂᏳ",
          "NZ": "ᎢᏤ ᏏᎢᎴᏂᏗ",
          "OM": "ᎣᎺᏂ",
          "PA": "ᏆᎾᎹ",
          "PE": "ᏇᎷ",
          "PF": "ᎠᏂᎦᎸᏥ ᏆᎵᏂᏏᎠ",
          "PG": "ᏆᏇ ᎢᏤ ᎩᎢᏂ",
          "PH": "ᎠᏂᏈᎵᎩᏃ",
          "PK": "ᏆᎩᏍᏖᏂ",
          "PL": "ᏉᎳᏂ",
          "PM": "ᎤᏓᏅᏘ ᏈᏰ ᎠᎴ ᎻᏇᎶᏂ",
          "PN": "ᏈᎧᎵᏂ ᏚᎦᏚᏛᎢ",
          "PR": "ᏇᎡᏙ ᎵᎢᎪ",
          "PS": "ᏆᎴᏍᏗᏂᎠᏂ ᏄᎬᏫᏳᏌᏕᎩ",
          "PT": "ᏉᏥᎦᎳ",
          "PW": "ᏆᎴᎠᏫ",
          "PY": "ᏆᎳᏇᎢᏯ",
          "QA": "ᎧᏔᎵ",
          "QO": "ᎠᏍᏛ ᎣᏏᏰᏂᎠ",
          "RE": "ᎴᏳᏂᎠᏂ",
          "RO": "ᎶᎹᏂᏯ",
          "RS": "ᏒᏈᏯ",
          "RU": "ᏲᏂᎢ",
          "RW": "ᎶᏩᏂᏓ",
          "SA": "ᏌᎤᏗ ᎡᎴᏈᎠ",
          "SB": "ᏐᎶᎹᏂ ᏚᎦᏚᏛᎢ",
          "SC": "ᏏᎡᏥᎵᏍ",
          "SD": "ᏑᏕᏂ",
          "SE": "ᏍᏫᏕᏂ",
          "SG": "ᏏᏂᎦᏉᎵ",
          "SH": "ᎤᏓᏅᏘ ᎮᎵᎾ",
          "SI": "ᏍᎶᏫᏂᎠ",
          "SJ": "ᏍᏩᎵᏆᎵᏗ ᎠᎴ ᏤᏂ ᎹᏰᏂ",
          "SK": "ᏍᎶᏩᎩᎠ",
          "SL": "ᏏᎡᎳ ᎴᎣᏂ",
          "SM": "ᎤᏓᏅᏘ ᎹᎵᎢᏃ",
          "SN": "ᏏᏂᎦᎵ",
          "SO": "ᏐᎹᎵ",
          "SR": "ᏒᎵᎾᎻ",
          "SS": "ᏧᎦᎾᏮ ᏑᏕᏂ",
          "ST": "ᏌᎣ ᏙᎺ ᎠᎴ ᏈᏂᏏᏇ",
          "SV": "ᎡᎵᏌᎵᏆᏙᎵ",
          "SX": "ᏏᏂᏘ ᎹᏘᏂ",
          "SY": "ᏏᎵᎠ",
          "SZ": "ᎡᏍᏩᏘᏂ",
          "TA": "ᏟᏍᏛᏂ Ꮣ ᎫᎾᎭ",
          "TC": "ᎠᏂᏛᎵᎩ ᎠᎴ ᎨᎢᎪ ᏚᎦᏚᏛᎢ",
          "TD": "ᏣᏗ",
          "TF": "ᎠᏂᎦᎸᏥ ᏧᎦᎾᏮ ᎦᏙᎯ ᎤᎵᏍᏛᎢ",
          "TG": "ᏙᎪ",
          "TH": "ᏔᏯᎴᏂ",
          "TJ": "ᏔᏥᎩᏍᏕᏂ",
          "TK": "ᏙᎨᎳᏭ",
          "TL": "ᏘᎼᎵ-ᎴᏍᏖ",
          "TM": "ᏛᎵᎩᎺᏂᏍᏔᏂ",
          "TN": "ᏚᏂᏏᏍᎠ",
          "TO": "ᏙᎾᎦ",
          "TR": "ᎬᏃ",
          "TT": "ᏟᏂᏕᏗ ᎠᎴ ᏙᏆᎪ",
          "TV": "ᏚᏩᎷ",
          "TW": "ᏔᎢᏩᏂ",
          "TZ": "ᏖᏂᏏᏂᏯ",
          "UA": "ᏳᎧᎴᏂ",
          "UG": "ᏳᎦᏂᏓ",
          "UM": "U.S. ᎠᏍᏛ ᏚᎦᏚᏛᎢ",
          "UN": "ᏌᏊ ᎢᏳᎾᎵᏍᏔᏅ ᎠᏰᎵ ᏚᎾᏙᏢᏒ",
          "US": "ᏌᏊ ᎢᏳᎾᎵᏍᏔᏅ ᏍᎦᏚᎩ",
          "UY": "ᏳᎷᏇ",
          "UZ": "ᎤᏍᏇᎩᏍᏖᏂ",
          "VA": "ᎠᏥᎳᏁᏠ ᎦᏚᎲ",
          "VC": "ᎤᏓᏅᏘ ᏫᏂᏏᏂᏗ ᎠᎴ ᎾᏍᎩ ᏇᎾᏗᏁᏍ",
          "VE": "ᏪᏁᏑᏪᎳ",
          "VG": "ᏈᏗᏍ ᎠᏒᏂᎸ ᏂᎨᏒᎾ ᏚᎦᏚᏛᎢ",
          "VI": "U.S. ᎠᏒᏂᎸ ᏂᎨᏒᎾ ᏚᎦᏚᏛᎢ",
          "VN": "ᏫᎡᏘᎾᎻ",
          "VU": "ᏩᏂᎤᏩᏚ",
          "WF": "ᏩᎵᏍ ᎠᎴ ᏊᏚᎾ",
          "WS": "ᏌᎼᎠ",
          "XA": "ᏡᏙ-ᏄᏍᏛᎢᎥᎧᏁᎬᎢ",
          "XB": "ᏡᏙ-ᏈᏗ",
          "XK": "ᎪᏐᏉ",
          "YE": "ᏰᎺᏂ",
          "YT": "ᎺᏯᏖ",
          "ZA": "ᏧᎦᎾᏮ ᎬᎿᎨᏍᏛ",
          "ZM": "ᏌᎻᏈᏯ",
          "ZW": "ᏏᎻᏆᏇ",
          "ZZ": "ᏄᏬᎵᏍᏛᎾ ᎤᏔᏂᏗᎦᏙᎯ"
        },
        "narrow": {
        },
        "short": {
          "GB": "UK",
          "HK": "ᎰᏂᎩ ᎪᏂᎩ",
          "MO": "ᎹᎧᎣ",
          "PS": "ᏆᎴᏍᏗᏂ",
          "US": "US"
        }
      },
      "script": {
        "long": {
          "Adlm": "ᎠᏓᎳᎻ",
          "Arab": "ᎡᎳᏈᎩ",
          "Aran": "ᎾᏍᏔᎵᏆ",
          "Armn": "ᎠᎳᎻᎠᏂ",
          "Beng": "ᏇᏂᎦᎠ",
          "Bopo": "ᏆᏉᎼᏬ",
          "Brai": "ᏗᏂᎨᏫ ᎤᏃᏪᎶᏙᏗ",
          "Cakm": "ᏣᎧᎹ",
          "Cans": "ᏌᏊ ᎨᎾᏓ ᎠᏂᏴᏫᏯ ᏗᎪᏪᎸ",
          "Cher": "ᏣᎳᎩ",
          "Cyrl": "ᏲᏂᎢ ᏗᎪᏪᎵ",
          "Deva": "ᏕᏫᎾᎦᎵ",
          "Ethi": "ᎢᏗᏯᏈᎩ",
          "Geor": "ᏦᏥᎠᏂ",
          "Grek": "ᎪᎢ",
          "Gujr": "ᎫᏣᎳᏘ",
          "Guru": "ᎬᎹᎩ",
          "Hanb": "ᎭᏂ ᎾᎿ ᏆᏉᎼᏬ",
          "Hang": "ᎭᏂᎫᎵ",
          "Hani": "ᎭᏂ",
          "Hans": "ᎠᎯᏗᎨ",
          "Hant": "ᎤᏦᏍᏗ",
          "Hebr": "ᎠᏂᏈᎵ",
          "Hira": "ᎯᎳᎦᎾ",
          "Hrkt": "ᏣᏩᏂᏏ ᏧᏃᏴᎩ",
          "Jamo": "ᏣᎼ",
          "Jpan": "ᏣᏆᏂᏏ",
          "Kana": "ᎧᏔᎧᎾ",
          "Khmr": "ᎩᎻᎷ",
          "Knda": "ᎧᎾᏓ",
          "Kore": "ᎪᎵᎠᏂ",
          "Laoo": "ᎳᎣ",
          "Latn": "ᎳᏘᏂ",
          "Mlym": "ᎹᎳᏯᎳᎻ",
          "Mong": "ᎹᏂᎪᎵᎠᏂ",
          "Mtei": "ᎺᏖ ᎹᏰᎩ",
          "Mymr": "ᎹᎡᏂᎹᎳ",
          "Nkoo": "ᎾᎪ",
          "Olck": "ᎣᎵ ᏥᎩ",
          "Orya": "ᎣᏗᎠ",
          "Rohg": "ᎭᏂᏫ",
          "Sinh": "ᏏᏅᎭᎳ",
          "Sund": "ᏚᏓᏂᏎ",
          "Syrc": "ᏏᎵᎡᎩ",
          "Taml": "ᏔᎻᎵ",
          "Telu": "ᏖᎷᎦ",
          "Tfng": "ᏘᏫᎾᎦ",
          "Thaa": "ᏔᎠᎾ",
          "Thai": "ᏔᏱ ᏔᏯᎴᏂ",
          "Tibt": "ᏘᏇᏔᏂ",
          "Vaii": "ᏩᏱ",
          "Yiii": "Ᏹ",
          "Zmth": "ᎠᏰᎦᎴᏴᏫᏍᎩ ᎠᎤᏓᏗᏍᏙᏗ",
          "Zsye": "ᎡᎼᏥ",
          "Zsym": "ᏗᎬᏟᎶᏍᏙᏗ",
          "Zxxx": "ᎪᏪᎳᏅ ᏂᎨᏒᎾ",
          "Zyyy": "ᏯᏃᏉ ᏱᎬᏍᏛᏭ",
          "Zzzz": "ᏄᏬᎵᏍᏛᎾ ᎠᏍᏓᏩᏛᏍᏙᏗ"
        },
        "narrow": {
        },
        "short": {
        }
      }
    }
  },
  "locale": "chr"
})
}