import{b9 as $,a as F,au as T,al as k,r as D,ba as N,c as B,bb as q,bc as V,bd as G,be as O,m as e,n as p,aX as _,bf as H,bg as Q,L as b,bh as U,w as u,A as y,N as z,J as l,s as R,S as J,G as g,x as d,z as W,af as X,i as K,l as P,k as M,aO as S,ax as Y}from"./strapi-YzJfjJ2z.js";import{P as Z}from"./Permissions-p_cREK0b-DUEXRPxk.js";import{f as ee}from"./index-CJ4ZBILS.js";import"./groupBy-BivwhTv9.js";import"./_baseEach-BkkNIx9z.js";import"./index-BRVyLNfZ.js";const se=K().shape({name:P().required(M.required.id),description:P().required(M.required.id)}),re=()=>{const{id:s}=$(),{toggleNotification:o}=F(),{formatMessage:r}=T(),w=k(),m=D.useRef(null),{trackUsage:h}=N(),{_unstableFormatAPIError:f,_unstableFormatValidationErrors:x}=B(),{isLoading:C,currentData:j}=q({role:s??""}),{currentData:E,isLoading:v}=V({id:s},{skip:!s,refetchOnMountOrArgChange:!0}),[A]=G(),[I]=O(),L=async(c,t)=>{try{h(s?"willDuplicateRole":"willCreateNewRole");const a=await A(c);if("error"in a){S(a.error)&&a.error.name==="ValidationError"?t.setErrors(x(a.error)):o({type:"danger",message:f(a.error)});return}const{permissionsToSend:n}=m.current?.getPermissions()??{};if(a.data.id&&Array.isArray(n)&&n.length>0){const i=await I({id:a.data.id,permissions:n});if("error"in i){S(i.error)&&i.error.name==="ValidationError"?t.setErrors(x(i.error)):o({type:"danger",message:f(i.error)});return}}o({type:"success",message:r({id:"Settings.roles.created",defaultMessage:"created"})}),w(`../roles/${a.data.id.toString()}`,{replace:!0})}catch{o({type:"danger",message:r({id:"notification.error",defaultMessage:"An error occurred"})})}};return C&&v||!j?e.jsx(p.Loading,{}):e.jsxs(_,{children:[e.jsx(p.Title,{children:r({id:"Settings.PageTitle",defaultMessage:"Settings - {name}"},{name:"Roles"})}),e.jsx(H,{initialValues:{name:"",description:`${r({id:"Settings.roles.form.created",defaultMessage:"Created"})} ${ee(new Date,"PPP")}`},onSubmit:L,validationSchema:se,validateOnChange:!1,children:({values:c,errors:t,handleReset:a,handleChange:n,isSubmitting:i})=>e.jsx(Q,{children:e.jsxs(e.Fragment,{children:[e.jsx(b.Header,{primaryAction:e.jsxs(u,{gap:2,children:[e.jsx(y,{variant:"secondary",onClick:()=>{a(),m.current?.resetForm()},children:r({id:"app.components.Button.reset",defaultMessage:"Reset"})}),e.jsx(y,{type:"submit",loading:i,startIcon:e.jsx(z,{}),children:r({id:"global.save",defaultMessage:"Save"})})]}),title:r({id:"Settings.roles.create.title",defaultMessage:"Create a role"}),subtitle:r({id:"Settings.roles.create.description",defaultMessage:"Define the rights given to the role"}),navigationAction:e.jsx(U,{})}),e.jsx(b.Content,{children:e.jsxs(u,{direction:"column",alignItems:"stretch",gap:6,children:[e.jsx(l,{background:"neutral0",padding:6,shadow:"filterShadow",hasRadius:!0,children:e.jsxs(u,{direction:"column",alignItems:"stretch",gap:4,children:[e.jsxs(u,{justifyContent:"space-between",children:[e.jsxs(l,{children:[e.jsx(l,{children:e.jsx(R,{fontWeight:"bold",children:r({id:"global.details",defaultMessage:"Details"})})}),e.jsx(l,{children:e.jsx(R,{variant:"pi",textColor:"neutral600",children:r({id:"Settings.roles.form.description",defaultMessage:"Name and description of the role"})})})]}),e.jsx(ae,{children:r({id:"Settings.roles.form.button.users-with-role",defaultMessage:"{number, plural, =0 {# users} one {# user} other {# users}} with this role"},{number:0})})]}),e.jsxs(g.Root,{gap:4,children:[e.jsx(g.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(d.Root,{name:"name",error:t.name&&r({id:t.name}),required:!0,children:[e.jsx(d.Label,{children:r({id:"global.name",defaultMessage:"Name"})}),e.jsx(W,{onChange:n,value:c.name}),e.jsx(d.Error,{})]})}),e.jsx(g.Item,{col:6,direction:"column",alignItems:"stretch",children:e.jsxs(d.Root,{name:"description",error:t.description&&r({id:t.description}),children:[e.jsx(d.Label,{children:r({id:"global.description",defaultMessage:"Description"})}),e.jsx(X,{onChange:n,value:c.description})]})})]})]})}),e.jsx(l,{shadow:"filterShadow",hasRadius:!0,children:e.jsx(Z,{isFormDisabled:!1,ref:m,permissions:E,layout:j})})]})})]})})})]})},ae=J.div`
  border: 1px solid ${({theme:s})=>s.colors.primary200};
  background: ${({theme:s})=>s.colors.primary100};
  padding: ${({theme:s})=>`${s.spaces[2]} ${s.spaces[4]}`};
  color: ${({theme:s})=>s.colors.primary600};
  border-radius: ${({theme:s})=>s.borderRadius};
  font-size: 1.2rem;
  font-weight: bold;
`,ce=()=>{const s=Y(o=>o.admin_app.permissions.settings?.roles.create);return e.jsx(p.Protect,{permissions:s,children:e.jsx(re,{})})};export{re as CreatePage,ce as ProtectedCreatePage};
