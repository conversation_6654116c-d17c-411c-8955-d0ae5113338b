{"version": 3, "sources": ["../../../@strapi/content-manager/dist/_chunks/uk-CR-zDhAY.mjs"], "sourcesContent": ["const groups = \"Групи\";\nconst models = \"Collection Types\";\nconst pageNotFound = \"Сторінка не знайдена\";\nconst uk = {\n  \"EditRelations.title\": \"Зв'язок\",\n  \"api.id\": \"API ID\",\n  \"components.AddFilterCTA.add\": \"Фільтри\",\n  \"components.AddFilterCTA.hide\": \"Фільтри\",\n  \"components.DraggableAttr.edit\": \"Натисніть щоб змінити\",\n  \"components.DynamicZone.pick-compo\": \"Виберіть один компонент\",\n  \"components.EmptyAttributesBlock.button\": \"Перейти до налаштувань\",\n  \"components.EmptyAttributesBlock.description\": \"Ви можете змінити свої налаштування\",\n  \"components.FieldItem.linkToComponentLayout\": \"Встановити макет компоненту\",\n  \"components.FilterOptions.button.apply\": \"Застосувати\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Застосувати\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Очистити все\",\n  \"components.FiltersPickWrapper.PluginHeader.description\": \"Вкажіть умови фільтрації записів\",\n  \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Фільтри\",\n  \"components.FiltersPickWrapper.hide\": \"Сховати\",\n  \"components.LimitSelect.itemsPerPage\": \"Елементів на сторінку\",\n  \"components.Search.placeholder\": \"Пошук записів...\",\n  \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Налаштуйте, як буде виглядати екран редагування.\",\n  \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Визначте параметри вигяду списку.\",\n  \"components.SettingsViewWrapper.pluginHeader.title\": \"Налаштуйте вигляд - {name}\",\n  \"components.TableDelete.delete\": \"Видалити все\",\n  \"components.TableDelete.deleteSelected\": \"Видалити обране\",\n  \"components.TableEmpty.withFilters\": \"Немає {contentType} з обраними фільтрами...\",\n  \"components.TableEmpty.withSearch\": 'Немає {contentType}, які відповідають пошуку \"{search}\"...',\n  \"components.TableEmpty.withoutFilter\": \"Немає {contentType}...\",\n  \"components.empty-repeatable\": \"Немає записів. Натисніть кнопку нижче, щоб додати нову.\",\n  \"components.notification.info.maximum-requirement\": \"Ви досягли максимальної кількості полей\",\n  \"components.notification.info.minimum-requirement\": \"Поле було додано, щоб відповідати мінімальним вимогам\",\n  \"components.reset-entry\": \"Скинути запис\",\n  \"components.uid.apply\": \"Вибрати\",\n  \"components.uid.available\": \"Доступний\",\n  \"components.uid.regenerate\": \"Згенерувати\",\n  \"components.uid.suggested\": \"Рекомендоване\",\n  \"components.uid.unavailable\": \"Недоступний\",\n  \"containers.Edit.Link.Layout\": \"Налаштувати компонування\",\n  \"containers.Edit.Link.Model\": \"Змінити Collection Type\",\n  \"containers.Edit.addAnItem\": \"Додати елемент...\",\n  \"containers.Edit.clickToJump\": \"Натисніть щоб перейти до запису\",\n  \"containers.Edit.delete\": \"Видалити\",\n  \"containers.Edit.editing\": \"Редагування...\",\n  \"containers.Edit.pluginHeader.title.new\": \"Створити запис\",\n  \"containers.Edit.reset\": \"Скинути\",\n  \"containers.Edit.returnList\": \"Повернутися до списку\",\n  \"containers.Edit.seeDetails\": \"Докладніше\",\n  \"containers.Edit.submit\": \"Зберегти\",\n  \"containers.EditSettingsView.modal-form.edit-field\": \"Налаштуйте поле\",\n  \"containers.EditView.notification.errors\": \"Форма містить деякі помилки\",\n  \"containers.Home.introduction\": \"Щоб редагувати ваші записи, перейдіть за посиланням в лівому меню. Цей плагін не має належного способу редагування налаштувань і все ще активно розробляється.\",\n  \"containers.Home.pluginHeaderDescription\": \"Керуйте своїми записами за допомогою потужного та красивого інтерфейсу.\",\n  \"containers.Home.pluginHeaderTitle\": \"Контент-менеджер\",\n  \"containers.List.errorFetchRecords\": \"Помилка\",\n  \"containers.list.displayedFields\": \"Показувати поля\",\n  \"containers.ListSettingsView.modal-form.edit-label\": \"Налаштуйте підпис\",\n  \"containers.SettingPage.add.field\": \"Додати ще одне поле\",\n  \"containers.SettingPage.attributes\": \"Поля атрибутів\",\n  \"containers.SettingPage.attributes.description\": \"Визначте порядок атрибутів\",\n  \"containers.SettingPage.editSettings.description\": \"Перетягніть поля щоб налаштувати вигляд.\",\n  \"containers.SettingPage.editSettings.entry.title\": \"Заголовок запису\",\n  \"containers.SettingPage.editSettings.entry.title.description\": \"Встановіть поле, яке буде відображати запис.\",\n  \"containers.SettingPage.editSettings.title\": \"Змінити вигляд (налаштування)\",\n  \"containers.SettingPage.layout\": \"Компонування\",\n  \"containers.SettingPage.listSettings.description\": \"Налаштуйте параметри для цього Collection Type\",\n  \"containers.SettingPage.listSettings.title\": \"Список (налаштування)\",\n  \"containers.SettingPage.pluginHeaderDescription\": \"Налаштуйте конкретні параметри для цього Collection Type\",\n  \"containers.SettingPage.settings\": \"Налаштування\",\n  \"containers.SettingPage.view\": \"Вигляд\",\n  \"containers.SettingViewModel.pluginHeader.title\": \"Контент-менеджер - {name}\",\n  \"containers.SettingsPage.Block.contentType.description\": \"Налаштуйте конкретні параметри\",\n  \"containers.SettingsPage.Block.contentType.title\": \"Collection Types\",\n  \"containers.SettingsPage.Block.generalSettings.description\": \"Налаштуйте параметри за замовчуванням для свого Collection Types\",\n  \"containers.SettingsPage.Block.generalSettings.title\": \"Загальне\",\n  \"containers.SettingsPage.pluginHeaderDescription\": \"Налаштуйте параметри для всіх ваших Collection Types та груп\",\n  \"containers.SettingsView.list.subtitle\": \"Налаштуйте компонування та відображення ваших Collection Types та груп\",\n  \"containers.SettingsView.list.title\": \"Налаштування відображення\",\n  \"emptyAttributes.button\": \"Перейдіть до конструктора Collection Type\",\n  \"emptyAttributes.description\": \"Додайте перше поле в ваш Collection Type\",\n  \"emptyAttributes.title\": \"Поки що немає полей\",\n  \"error.attribute.key.taken\": \"Значення вже існує\",\n  \"error.attribute.sameKeyAndName\": \"Не може співпадати\",\n  \"error.attribute.taken\": \"Це поле вже існує\",\n  \"error.contentTypeName.taken\": \"Це ім'я вже існує\",\n  \"error.model.fetch\": \"Під час завантаження конфігурації моделей сталася помилка.\",\n  \"error.record.create\": \"Під час створення запису сталася помилка.\",\n  \"error.record.delete\": \"Під час видалення запису сталася помилка.\",\n  \"error.record.fetch\": \"Під час завантаження запису сталася помилка.\",\n  \"error.record.update\": \"Під час оновлення запису сталася помилка.\",\n  \"error.records.count\": \"Під час завантаження кількості записів сталася помилка.\",\n  \"error.records.fetch\": \"Під час завантаження записів сталася помилка.\",\n  \"error.schema.generation\": \"Під час створення схеми сталася помилка.\",\n  \"error.validation.json\": \"Це не відпоідає формату JSON\",\n  \"error.validation.max\": \"Значення занадто велике.\",\n  \"error.validation.maxLength\": \"Значення занадто довге.\",\n  \"error.validation.min\": \"Значення занадто мале.\",\n  \"error.validation.minLength\": \"Значення занадто коротке.\",\n  \"error.validation.minSupMax\": \"Не може бути більше\",\n  \"error.validation.regex\": \"Значення не відповідає регулярному виразу.\",\n  \"error.validation.required\": \"Це обов'язкове поле.\",\n  \"form.Input.bulkActions\": \"Дозволити масові дії\",\n  \"form.Input.defaultSort\": \"Сортування за замовчуванням\",\n  \"form.Input.description\": \"Опис\",\n  \"form.Input.description.placeholder\": \"Ім'я, для відображення в профілі\",\n  \"form.Input.editable\": \"Редагуєме поле\",\n  \"form.Input.filters\": \"Увімкнути фільтри\",\n  \"form.Input.label\": \"Підпис\",\n  \"form.Input.label.inputDescription\": \"Це значення змінить підпис, який відображується у заголовку таблиці\",\n  \"form.Input.pageEntries\": \"записів на сторінці\",\n  \"form.Input.pageEntries.inputDescription\": \"Зауважте, що ви можете змінити це значення на сторінці налаштувань Collection Type.\",\n  \"form.Input.placeholder\": \"Плейсхолдер\",\n  \"form.Input.placeholder.placeholder\": \"Моє значення\",\n  \"form.Input.search\": \"Увімкнути пошук\",\n  \"form.Input.search.field\": \"Дозволити шукати за цим полем\",\n  \"form.Input.sort.field\": \"Дозволити сортувати за цим полем\",\n  \"form.Input.wysiwyg\": \"Відображувати як WYSIWYG\",\n  \"global.displayedFields\": \"Відображені поля\",\n  groups,\n  \"groups.numbered\": \"Групи ({number})\",\n  models,\n  \"models.numbered\": \"Collection Types ({number})\",\n  \"notification.error.displayedFields\": \"Потрібне хоча б одне поле для відображення.\",\n  \"notification.error.relationship.fetch\": \"Під час завантаження зв'язків сталася помилка.\",\n  \"notification.info.SettingPage.disableSort\": \"Потріен хоча б один атрібут дозволений для сортування.\",\n  \"notification.info.minimumFields\": \"Необхідно відобразити хоча б одне поле.\",\n  \"notification.upload.error\": \"Під час завантаження файлів сталася помилка.\",\n  pageNotFound,\n  \"plugin.description.long\": \"Швидкий спосіб перегляду, редагування та видалення даних у вашій базі даних.\",\n  \"plugin.description.short\": \"Швидкий спосіб перегляду, редагування та видалення даних у вашій базі даних.\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"Ви впевнені, що хочете видалити цей запис?\",\n  \"popUpWarning.bodyMessage.contentType.delete.all\": \"Ви впевнені, що хочете видалити ці записи?\",\n  \"popUpWarning.warning.cancelAllSettings\": \"Ви впевнені, що хочете скасувати свої зміни?\",\n  \"popUpWarning.warning.updateAllSettings\": \"Це змінить всі ваші налаштування\",\n  \"success.record.delete\": \"Видалено\",\n  \"success.record.save\": \"Збережено\"\n};\nexport {\n  uk as default,\n  groups,\n  models,\n  pageNotFound\n};\n//# sourceMappingURL=uk-CR-zDhAY.mjs.map\n"], "mappings": ";;;AAAA,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,KAAK;AAAA,EACT,uBAAuB;AAAA,EACvB,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,uBAAuB;AACzB;", "names": []}