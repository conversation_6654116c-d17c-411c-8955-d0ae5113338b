import{gS as n,gU as A,gp as p,hA as c}from"./strapi-YzJfjJ2z.js";import{_ as u}from"./_baseEach-BkkNIx9z.js";function b(r,e,a,g){for(var t=-1,o=r==null?0:r.length;++t<o;){var s=r[t];e(g,s,a(s),r)}return g}var f=b,i=u;function v(r,e,a,g){return i(r,function(t,o,s){e(g,t,a(t),s)}),g}var h=v,_=f,y=h,m=A,$=n;function j(r,e){return function(a,g){var t=$(a)?_:y,o=e?e():{};return t(a,r,m(g),o)}}var w=j,x=c,B=w,E=Object.prototype,O=E.hasOwnProperty,P=B(function(r,e,a){O.call(r,a)?r[a].push(e):x(r,a,[e])}),l=P;const d=p(l);export{d as g};
