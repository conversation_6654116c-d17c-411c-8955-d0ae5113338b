{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/sv-D0vPVUAT.mjs"], "sourcesContent": ["const Analytics = \"Analytics\";\nconst Documentation = \"Dokumentation\";\nconst Email = \"E-post\";\nconst Password = \"Lösenord\";\nconst Provider = \"Tjänst\";\nconst ResetPasswordToken = \"Återställ lösenord\";\nconst Role = \"Roll\";\nconst light = \"Ljust\";\nconst dark = \"Mörkt\";\nconst Username = \"Användarnamn\";\nconst Users = \"Användare\";\nconst anErrorOccurred = \"<PERSON>psan! Något gick fel. Försök igen.\";\nconst clearLabel = \"Rensa\";\nconst or = \"ELLER\";\nconst skipToContent = \"Hoppa till innehållet\";\nconst submit = \"Skicka\";\nconst sv = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"Ditt konto har blivit avstängt.\",\n\t\"Auth.components.Oops.text.admin\": \"Kontakta din administratör om du tror detta är av misstag.\",\n\t\"Auth.components.Oops.title\": \"Oj då...\",\n\t\"Auth.form.active.label\": \"Aktiv\",\n\t\"Auth.form.button.forgot-password\": \"Skicka e-post\",\n\t\"Auth.form.button.go-home\": \"GÅ TILLBAKA HEM\",\n\t\"Auth.form.button.login\": \"Logga in\",\n\t\"Auth.form.button.login.providers.error\": \"Vi kan inte logga in dig med den valda tjänsten.\",\n\t\"Auth.form.button.login.strapi\": \"Logga in via Strapi\",\n\t\"Auth.form.button.password-recovery\": \"Återställning av lösenord\",\n\t\"Auth.form.button.register\": \"Låt oss börja\",\n\t\"Auth.form.confirmPassword.label\": \"Bekräfta lösenord\",\n\t\"Auth.form.currentPassword.label\": \"Nuvarande lösenord\",\n\t\"Auth.form.email.label\": \"E-post\",\n\t\"Auth.form.email.placeholder\": \"t.ex. <EMAIL>\",\n\t\"Auth.form.error.blocked\": \"Ditt konto har blockerats en administratör.\",\n\t\"Auth.form.error.code.provide\": \"Felaktig kod angiven.\",\n\t\"Auth.form.error.confirmed\": \"E-postadressen för ditt konto är inte verifierad.\",\n\t\"Auth.form.error.email.invalid\": \"Denna e-postadress är ogiltig.\",\n\t\"Auth.form.error.email.provide\": \"Ange användarnamn eller e-postadress.\",\n\t\"Auth.form.error.email.taken\": \"E-postadressen är upptagen.\",\n\t\"Auth.form.error.invalid\": \"Användarnamn/e-postadress eller lösenordet är felaktigt.\",\n\t\"Auth.form.error.params.provide\": \"Felaktiga parametrar angivna.\",\n\t\"Auth.form.error.password.format\": \"Ditt lösenord får inte innehålla symbolen `$` mer än tre gånger.\",\n\t\"Auth.form.error.password.local\": \"Denna användare har inte ett lokalt lösenord, logga in via den tjänsten som användes när kontot skapades.\",\n\t\"Auth.form.error.password.matching\": \"Lösenorden stämmer inte överens.\",\n\t\"Auth.form.error.password.provide\": \"Ange ditt lösenord.\",\n\t\"Auth.form.error.ratelimit\": \"För många försök, försök igen om en minut.\",\n\t\"Auth.form.error.user.not-exist\": \"Det här e-postadress finns inte.\",\n\t\"Auth.form.error.username.taken\": \"Användarnamnet är redan upptaget.\",\n\t\"Auth.form.firstname.label\": \"Förnamn\",\n\t\"Auth.form.firstname.placeholder\": \"t.ex. Kai\",\n\t\"Auth.form.forgot-password.email.label\": \"Ange din e-postadress\",\n\t\"Auth.form.forgot-password.email.label.success\": \"E-post har skickats till\",\n\t\"Auth.form.lastname.label\": \"Efternamn\",\n\t\"Auth.form.lastname.placeholder\": \"t.ex. Doe\",\n\t\"Auth.form.password.hide-password\": \"Dölj lösenord\",\n\t\"Auth.form.password.hint\": \"Måste bestå av minst 8 tecken, en stor bokstav, en liten bokstav och en siffra\",\n\t\"Auth.form.password.show-password\": \"Visa lösenord\",\n\t\"Auth.form.register.news.label\": \"Håll mig uppdaterad om nya funktioner och förbättringar (genom att göra detta godkänner du {terms} och {policy}).\",\n\t\"Auth.form.register.subtitle\": \"Inloggningsuppgifter används bara för att logga in i Strapi. All data kommer att sparas i din databas.\",\n\t\"Auth.form.rememberMe.label\": \"Kom ihåg mig\",\n\t\"Auth.form.username.label\": \"Användarnamn\",\n\t\"Auth.form.username.placeholder\": \"t.ex. Kai_Doe\",\n\t\"Auth.form.welcome.subtitle\": \"Logga in till ett Strapi-konto\",\n\t\"Auth.form.welcome.title\": \"Välkommen till Strapi!\",\n\t\"Auth.link.forgot-password\": \"Glömt ditt lösenord?\",\n\t\"Auth.link.ready\": \"Redo att logga in?\",\n\t\"Auth.link.signin\": \"Logga in\",\n\t\"Auth.link.signin.account\": \"Har du redan ett konto?\",\n\t\"Auth.login.sso.divider\": \"Eller logga in med\",\n\t\"Auth.login.sso.loading\": \"Laddar inloggningstjänster...\",\n\t\"Auth.login.sso.subtitle\": \"Logga in med SSO\",\n\t\"Auth.privacy-policy-agreement.policy\": \"integritetspolicyn\",\n\t\"Auth.privacy-policy-agreement.terms\": \"villkoren\",\n\t\"Auth.reset-password.title\": \"Återställ lösenord\",\n\t\"Content Manager\": \"Innehållshantering\",\n\t\"Content Type Builder\": \"Innehållstypsbyggare\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Filuppladdning\",\n\t\"HomePage.head.title\": \"Hemsida\",\n\t\"HomePage.roadmap\": \"Se vår roadmap\",\n\t\"HomePage.welcome.congrats\": \"Grattis!\",\n\t\"HomePage.welcome.congrats.content\": \"Du är inloggad som administratör. För att upptäcka de kraftfulla funktionerna från Strapi,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"vi rekommenderar dig att skapa din första innehållstyp.\",\n\t\"Media Library\": \"Mediebibliotek\",\n\t\"New entry\": \"Nytt inlägg\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"Roller & Behörigheter\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"Några roller kunde inte tas bort eftersom de är kopplade till minst en användare\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"En roll kan inte tas bort om den är kopplad till någon användare\",\n\t\"Roles.RoleRow.select-all\": \"Välj {name} för massåtgärder\",\n\t\"Roles.RoleRow.user-count\": \"användare\",\n\t\"Roles.components.List.empty.withSearch\": \"Det finns inga roller som matchar sökningen ({search})...\",\n\t\"Settings.PageTitle\": \"Inställningar - {name}\",\n\t\"Settings.apiTokens.addFirstToken\": \"Lägg till din första API-token\",\n\t\"Settings.apiTokens.addNewToken\": \"Lägg till en ny API-token\",\n\t\"Settings.tokens.copy.editMessage\": \"Av säkerhetsskäl kan du bara se din token en gång.\",\n\t\"Settings.tokens.copy.editTitle\": \"Denna token är inte längre tillgänglig.\",\n\t\"Settings.tokens.copy.lastWarning\": \"Kopiera denna token nu, den kommer inte visas igen!\",\n\t\"Settings.apiTokens.create\": \"Skapa en ny API-token\",\n\t\"Settings.apiTokens.description\": \"Lista över genererade tokens för att använda API:t\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"Det finns inget innehåll ännu...\",\n\t\"Settings.apiTokens.ListView.headers.name\": \"Name\",\n\t\"Settings.apiTokens.ListView.headers.description\": \"Description\",\n\t\"Settings.apiTokens.ListView.headers.type\": \"Token type\",\n\t\"Settings.apiTokens.ListView.headers.createdAt\": \"Created at\",\n\t\"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Last used\",\n\t\"Settings.tokens.notification.copied\": \"Token har kopierats.\",\n\t\"Settings.apiTokens.title\": \"API-tokens\",\n\t\"Settings.tokens.types.full-access\": \"Full åtkomst\",\n\t\"Settings.tokens.types.read-only\": \"Skrivskyddad\",\n\t\"Settings.tokens.duration.7-days\": \"7 dagar\",\n\t\"Settings.tokens.duration.30-days\": \"30 dagar\",\n\t\"Settings.tokens.duration.90-days\": \"90 dagar\",\n\t\"Settings.tokens.duration.unlimited\": \"Evig\",\n\t\"Settings.tokens.form.duration\": \"Giltighetstid för token\",\n\t\"Settings.tokens.form.type\": \"Token-typ\",\n\t\"Settings.tokens.duration.expiration-date\": \"Utgångsdatum\",\n\t\"Settings.apiTokens.createPage.permissions.title\": \"Behörigheter\",\n\t\"Settings.apiTokens.createPage.permissions.description\": \"Endast åtgärder som är kopplade till en rutt listas nedan.\",\n\t\"Settings.tokens.RegenerateDialog.title\": \"Återställ token\",\n\t\"Settings.tokens.popUpWarning.message\": \"Är du säker på att du vill återställa denna token?\",\n\t\"Settings.tokens.Button.cancel\": \"Avbryt\",\n\t\"Settings.tokens.Button.regenerate\": \"Återställ\",\n\t\"Settings.application.description\": \"Adminpanelens globala information\",\n\t\"Settings.application.edition-title\": \"aktiv version\",\n\t\"Settings.application.get-help\": \"Få hjälp\",\n\t\"Settings.application.link-pricing\": \"Se alla prisplaner\",\n\t\"Settings.application.link-upgrade\": \"Uppgradera din adminpanel\",\n\t\"Settings.application.node-version\": \"node-version\",\n\t\"Settings.application.strapi-version\": \"strapi-version\",\n\t\"Settings.application.strapiVersion\": \"strapi-version\",\n\t\"Settings.application.title\": \"Översikt\",\n\t\"Settings.application.customization\": \"Anpassning\",\n\t\"Settings.application.customization.carousel.title\": \"Logga\",\n\t\"Settings.application.customization.carousel.change-action\": \"Ändra logga\",\n\t\"Settings.application.customization.carousel.reset-action\": \"Återställ logga\",\n\t\"Settings.application.customization.carousel-slide.label\": \"Logotyphjul\",\n\t\"Settings.application.customization.carousel-hint\": \"Ändra adminpanelens logotyp (Högsta upplösning: {dimension}x{dimension}, Största filstorlek: {size}KB)\",\n\t\"Settings.application.customization.modal.cancel\": \"Avbryt\",\n\t\"Settings.application.customization.modal.upload\": \"Ladda upp logga\",\n\t\"Settings.application.customization.modal.tab.label\": \"Hur vill du ladda dina mediefiler?\",\n\t\"Settings.application.customization.modal.upload.from-computer\": \"Från datorn\",\n\t\"Settings.application.customization.modal.upload.file-validation\": \"Högsta upplösning: {dimension}x{dimension}, Största filstorlek: {size}KB\",\n\t\"Settings.application.customization.modal.upload.error-format\": \"Fel format uppladdat (tillåtna format: jpeg, jpg, png, svg).\",\n\t\"Settings.application.customization.modal.upload.error-size\": \"Den uppladdade filen är för stor (Högsta upplösning: {dimension}x{dimension}, Största filstorlek: {size}KB)\",\n\t\"Settings.application.customization.modal.upload.error-network\": \"Nätverksfel\",\n\t\"Settings.application.customization.modal.upload.cta.browse\": \"Bläddra bland filer\",\n\t\"Settings.application.customization.modal.upload.drag-drop\": \"Dra och släpp här eller\",\n\t\"Settings.application.customization.modal.upload.from-url\": \"Från URL\",\n\t\"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n\t\"Settings.application.customization.modal.upload.next\": \"Nästa\",\n\t\"Settings.application.customization.modal.pending\": \"Ändra logga\",\n\t\"Settings.application.customization.modal.pending.choose-another\": \"Välj ny logga\",\n\t\"Settings.application.customization.modal.pending.title\": \"Loggan är redo att laddas upp\",\n\t\"Settings.application.customization.modal.pending.subtitle\": \"Ändra den valda loggan innan du laddar upp den\",\n\t\"Settings.application.customization.modal.pending.upload\": \"Ladda upp ny logga\",\n\t\"Settings.application.customization.modal.pending.card-badge\": \"bild\",\n\t\"Settings.error\": \"Fel\",\n\t\"Settings.global\": \"Globala inställningar\",\n\t\"Settings.permissions\": \"Administrationspanel\",\n\t\"Settings.permissions.category\": \"Behörighetsinställningar för {category}\",\n\t\"Settings.permissions.category.plugins\": \"Behörighetsinställningar för {category}-plugin-programmet\",\n\t\"Settings.permissions.conditions.anytime\": \"När som helst\",\n\t\"Settings.permissions.conditions.apply\": \"Använd\",\n\t\"Settings.permissions.conditions.can\": \"Kan\",\n\t\"Settings.permissions.conditions.conditions\": \"Definiera behörigheter\",\n\t\"Settings.permissions.conditions.links\": \"Länkar\",\n\t\"Settings.permissions.conditions.no-actions\": \"Du måste först välja åtgärder (skapa, läsa, uppdatera, ...) innan du definierar behörigheter för dem.\",\n\t\"Settings.permissions.conditions.none-selected\": \"När som helst\",\n\t\"Settings.permissions.conditions.or\": \"ELLER\",\n\t\"Settings.permissions.conditions.when\": \"När\",\n\t\"Settings.permissions.select-all-by-permission\": \"Välj alla behörighet för {label}\",\n\t\"Settings.permissions.select-by-permission\": \"Välj behörighet för {label}\",\n\t\"Settings.permissions.users.create\": \"Bjud in användare\",\n\t\"Settings.permissions.users.email\": \"Email\",\n\t\"Settings.permissions.users.firstname\": \"Firstname\",\n\t\"Settings.permissions.users.lastname\": \"Lastname\",\n\t\"Settings.permissions.users.user-status\": \"User status\",\n\t\"Settings.permissions.users.roles\": \"Roller\",\n\t\"Settings.permissions.users.username\": \"Användarnamn\",\n\t\"Settings.permissions.users.active\": \"Aktiv\",\n\t\"Settings.permissions.users.inactive\": \"Inaktiv\",\n\t\"Settings.permissions.users.form.sso\": \"Anslut med SSO\",\n\t\"Settings.permissions.users.form.sso.description\": \"När aktivt (PÅ) kan användare logga in med SSO\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"Alla användare som har tillgång till Strapi-adminpanel\",\n\t\"Settings.permissions.users.tabs.label\": \"Behörighetsfliken\",\n\t\"Settings.permissions.users.strapi-super-admin\": \"Super Admin\",\n\t\"Settings.permissions.users.strapi-editor\": \"Redigerare\",\n\t\"Settings.permissions.users.strapi-author\": \"Författare\",\n\t\"Settings.profile.form.notify.data.loaded\": \"Din profildata har laddats\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"Återställ det valda gränssnittsspråket\",\n\t\"Settings.profile.form.section.experience.here\": \"här\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"Gränssnittsspråk\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Detta kommer bara att visa ditt egna gränssnitt på det valda språket.\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Ändringar av inställningar kommer bara att gälla dig. Mer information finns {here}.\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"Gränssnittsläge\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"Visar ditt gränssnitt i det valda läget.\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{name}-läge\",\n\tlight: light,\n\tdark: dark,\n\t\"Settings.profile.form.section.experience.title\": \"Upplevelse\",\n\t\"Settings.profile.form.section.head.title\": \"Användarprofil\",\n\t\"Settings.profile.form.section.profile.page.title\": \"Profilsida\",\n\t\"Settings.roles.create.description\": \"Definiera de rättigheter som ges till rollen\",\n\t\"Settings.roles.create.title\": \"Skapa en roll\",\n\t\"Settings.roles.created\": \"Roll skapad\",\n\t\"Settings.roles.edit.title\": \"Redigera en roll\",\n\t\"Settings.roles.form.button.users-with-role\": \"användare med denna rollen\",\n\t\"Settings.roles.form.created\": \"Skapad\",\n\t\"Settings.roles.form.description\": \"Namn och beskrivning av rollen\",\n\t\"Settings.roles.form.permission.property-label\": \"{label}-behörigheter\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"Fältbehörigheter\",\n\t\"Settings.roles.form.permissions.create\": \"Skapa\",\n\t\"Settings.roles.form.permissions.delete\": \"Ta bort\",\n\t\"Settings.roles.form.permissions.publish\": \"Publicera\",\n\t\"Settings.roles.form.permissions.read\": \"Läsa\",\n\t\"Settings.roles.form.permissions.update\": \"Uppdatera\",\n\t\"Settings.roles.list.button.add\": \"Lägg till ny roll\",\n\t\"Settings.roles.list.description\": \"Lista över roller\",\n\t\"Settings.roles.title.singular\": \"roll\",\n\t\"Settings.sso.description\": \"Konfigurera inställningarna för Single Sign-On.\",\n\t\"Settings.sso.form.defaultRole.description\": \"Den kommer att koppla den nya inloggade användaren till den valda rollen\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"Du måste ha behörighet att läsa administratörsrollerna\",\n\t\"Settings.sso.form.defaultRole.label\": \"Standardroll\",\n\t\"Settings.sso.form.registration.description\": \"Skapa ny användare med SSO-inloggning om kontot saknas\",\n\t\"Settings.sso.form.registration.label\": \"Auto-registrering\",\n\t\"Settings.sso.title\": \"Single Sign-On\",\n\t\"Settings.webhooks.create\": \"Skapa en webhook\",\n\t\"Settings.webhooks.create.header\": \"Skapa ny header\",\n\t\"Settings.webhooks.created\": \"Webhook skapad\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"Den här händelsen finns endast för innehåll med funktionen Utkast/Publicera aktiverat\",\n\t\"Settings.webhooks.events.create\": \"Skapa\",\n\t\"Settings.webhooks.events.update\": \"Uppdatera\",\n\t\"Settings.webhooks.form.events\": \"Händelser\",\n\t\"Settings.webhooks.form.headers\": \"Headers\",\n\t\"Settings.webhooks.form.url\": \"Url\",\n\t\"Settings.webhooks.headers.remove\": \"Ta bort header-rad {number}\",\n\t\"Settings.webhooks.key\": \"Nyckel\",\n\t\"Settings.webhooks.list.button.add\": \"Skapa ny webhook\",\n\t\"Settings.webhooks.list.description\": \"Få notiser för POST-ändringar\",\n\t\"Settings.webhooks.list.empty.description\": \"Inga webhooks hittades\",\n\t\"Settings.webhooks.list.empty.link\": \"Se vår dokumentation\",\n\t\"Settings.webhooks.list.empty.title\": \"Det finns inga webhooks än\",\n\t\"Settings.webhooks.list.th.actions\": \"åtgärder\",\n\t\"Settings.webhooks.list.th.status\": \"status\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhooks\",\n\t\"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# webhook} other {# webhooks}} valda\",\n\t\"Settings.webhooks.trigger\": \"Utlösare\",\n\t\"Settings.webhooks.trigger.cancel\": \"Avbryt utlösaren\",\n\t\"Settings.webhooks.trigger.pending\": \"Pågår…\",\n\t\"Settings.webhooks.trigger.save\": \"Spara innan du utlöser\",\n\t\"Settings.webhooks.trigger.success\": \"Klart!\",\n\t\"Settings.webhooks.trigger.success.label\": \"Utlösare lyckades\",\n\t\"Settings.webhooks.trigger.test\": \"Testutlösare\",\n\t\"Settings.webhooks.trigger.title\": \"Spara före utlösning\",\n\t\"Settings.webhooks.value\": \"Värde\",\n\t\"Usecase.back-end\": \"Back-end utvecklare\",\n\t\"Usecase.button.skip\": \"Hoppa över denna frågan\",\n\t\"Usecase.content-creator\": \"Innehållsskapare\",\n\t\"Usecase.front-end\": \"Front-end utvecklare\",\n\t\"Usecase.full-stack\": \"Full-stack utvecklare\",\n\t\"Usecase.input.work-type\": \"Vad jobbar du som?\",\n\t\"Usecase.notification.success.project-created\": \"Projektet har skapats\",\n\t\"Usecase.other\": \"Annat\",\n\t\"Usecase.title\": \"Berätta lite mer om dig själv\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"Användare och behörigheter\",\n\t\"Users.components.List.empty\": \"Det finns inga användare...\",\n\t\"Users.components.List.empty.withFilters\": \"Det finns inga användare som matchar filtreringen...\",\n\t\"Users.components.List.empty.withSearch\": \"Det finns inga användare som matchar sökningen ({search})...\",\n\t\"admin.pages.MarketPlacePage.head\": \"Plugin-marknad\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"Du är offline\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"Du måste vara ansluten till Internet för att komma åt Strapi-marknaden.\",\n\t\"admin.pages.MarketPlacePage.plugins\": \"Plugins\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"Kopiera installationskommando\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"Installationskommandot redo att klistras in i din terminal\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"Läs mer\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"Läs mer om {pluginName}\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"Läs mer\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"Installerat\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Skapat av Strapi\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Plugin verifierat av Strapi\",\n\t\"admin.pages.MarketPlacePage.plugin.version\": \"Uppdatera din strapi-version: \\\"{strapiAppVersion}\\\" till: \\\"{versionRange}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.version.null\": \"Det går inte att verifiera kompatibiliteten med din Strapi-version: \\\"{strapiAppVersion}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.githubStars\": \"Detta plugin stjärnmarkerades {starsCount} på GitHub\",\n\t\"admin.pages.MarketPlacePage.plugin.downloads\": \"Detta plugin har {downloadsCount} nedladdningar per vecka\",\n\t\"admin.pages.MarketPlacePage.providers\": \"Tjänster\",\n\t\"admin.pages.MarketPlacePage.provider.githubStars\": \"Den här leverantören stjärnmärktes med {starsCount} på GitHub\",\n\t\"admin.pages.MarketPlacePage.provider.downloads\": \"Denna tjänsten har {downloadsCount} nedladdningar per vecka\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"Rensa sökningen\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \"Inget resultat för \\\"{target}\\\"\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"Sök\",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"Skicka plugin\",\n\t\"admin.pages.MarketPlacePage.submit.provider.link\": \"Skicka tjänst\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \"Få ut mer av Strapi\",\n\t\"admin.pages.MarketPlacePage.tab-group.label\": \"Plugins och leverantörer för Strapi\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"Saknar du ett plugin?\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"Berätta vilket plugin du letar efter så kan vi meddela våra plugin-utvecklare om de letar efter inspiration!\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical\": \"Alfabetisk ordning\",\n\t\"admin.pages.MarketPlacePage.sort.newest\": \"Nyast\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Sortera i alfabetisk ordning\",\n\t\"admin.pages.MarketPlacePage.sort.newest.selected\": \"Sortera efter ålder\",\n\t\"admin.pages.MarketPlacePage.filters.collections\": \"Samlingar\",\n\t\"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"{count, plural, =0 {Inga samlingar valda} one {# samling vald} other {# samlingar valda}}\",\n\t\"admin.pages.MarketPlacePage.filters.categories\": \"Kategorier\",\n\t\"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count, plural, =0 {Inga kategorier valda} one {# kategori vald} other {# kategorier valda}}\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"Kopiera till urklipp\",\n\t\"app.component.search.label\": \"Söka efter {target}\",\n\t\"app.component.table.duplicate\": \"Duplicera {target}\",\n\t\"app.component.table.edit\": \"Redigera {target}\",\n\t\"app.component.table.select.one-entry\": \"Välj {target}\",\n\t\"app.components.BlockLink.blog\": \"Blogg\",\n\t\"app.components.BlockLink.blog.content\": \"Läs de senaste nyheterna om Strapi och Strapi-ekosystemet.\",\n\t\"app.components.BlockLink.code\": \"Kodexempel\",\n\t\"app.components.BlockLink.code.content\": \"Lär dig genom att testa riktiga projekt som utvecklade av Strapi-communityn.\",\n\t\"app.components.BlockLink.documentation.content\": \"Upptäck de viktigaste koncepten, guiderna och instruktionerna.\",\n\t\"app.components.BlockLink.tutorial\": \"Guider\",\n\t\"app.components.BlockLink.tutorial.content\": \"Följ steg-för-steg-instruktionerna för att använda och anpassa Strapi.\",\n\t\"app.components.Button.cancel\": \"Avbryt\",\n\t\"app.components.Button.confirm\": \"Bekräfta\",\n\t\"app.components.Button.reset\": \"Återställ\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"Kommer snart\",\n\t\"app.components.ConfirmDialog.title\": \"Bekräftelse\",\n\t\"app.components.DownloadInfo.download\": \"Nedladdning pågår...\",\n\t\"app.components.DownloadInfo.text\": \"Detta kan ta någon minut. Tack för ditt tålamod.\",\n\t\"app.components.EmptyAttributes.title\": \"Det finns inga fält än\",\n\t\"app.components.EmptyStateLayout.content-document\": \"Inget innehåll hittades\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"Du har inte behörighet att komma åt detta innehållet\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>Skapa och hantera allt innehåll här i innehållshanteraren.</p><p>För att ta exemplet bloggwebbplats vidare kan man skriva en artikel, spara och publicera den som de vill.</p><p>💡 Tips - Glöm inte att trycka på publicera på det innehåll du skapar.</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"⚡️ Skapa innehåll\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>Perfekt, ett sista steg kvar!</p><b>🚀  Se ditt innehåll live</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"Testa API:t\",\n\t\"app.components.GuidedTour.CM.success.title\": \"Steg 2: Avklarat ✅\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>Samlingstyper hjälper dig att hantera flera poster, Engångstyper är lämpliga för att hantera bara en post.</p> <p>För en bloggwebbplats skulle \\\"Artiklar\\\" vara en samlingstyp medan en \\\"Startsida\\\" skulle vara en engångstyp.</p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"Skapa en samlingstyp\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 Skapa din första samlingstyp\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>Bra jobbat!</p><b>⚡️ Vad skulle du vilja dela med dig till världen?</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"Steg 1: Avklarat ✅\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>Skapa en autentiseringstoken här och hämta innehållet du just skapat.</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"Skapa en API-token\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Se innehåll live\",\n\t\"app.components.GuidedTour.apiTokens.success.content\": \"<p>Se innehåll live genom att göra en HTTP-förfrågan:</p><ul><li><p>Till denna URLen: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Med headern: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>För fler sätt att interagera med innehållet, se <documentationLink>dokumentationen</documentationLink>.</p>\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"Gå tillbaka till startsidan\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"Steg 3: Avklarat ✅\",\n\t\"app.components.GuidedTour.create-content\": \"Skapa innehåll\",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️ Vad skulle du vilja dela med dig till världen?\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"Gå till innehållstypsskaparen\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 Bygg en innehållsstruktur\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"Testa API:t\",\n\t\"app.components.GuidedTour.skip\": \"Hoppa över guiden\",\n\t\"app.components.GuidedTour.title\": \"3 steg för att komma igång\",\n\t\"app.components.HomePage.button.blog\": \"SE MER PÅ BLOGGEN\",\n\t\"app.components.HomePage.community\": \"Hitta gemenskapen på webben\",\n\t\"app.components.HomePage.community.content\": \"Diskutera med teammedlemmar, bidragsgivare och utvecklare på olika kanaler.\",\n\t\"app.components.HomePage.create\": \"Skapa din första innehållstyp\",\n\t\"app.components.HomePage.roadmap\": \"Se vår roadmap\",\n\t\"app.components.HomePage.welcome\": \"Välkommen ombord!\",\n\t\"app.components.HomePage.welcome.again\": \"Välkommen \",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Vi är glada över att ha dig som en del av communityn. Vi letar ständigt efter feedback så skicka oss DM på \",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"Vi hoppas att kommer framåt med ditt projekt... Läs gärna de senaste nyheterna om Strapi. Vi gör vårt bästa för att förbättra produkten baserat på din feedback.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"problem.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" eller skapa \",\n\t\"app.components.ImgPreview.hint\": \"Dra och släpp din fil i det här området eller {bläddra} efter en fil att ladda upp\",\n\t\"app.components.ImgPreview.hint.browse\": \"bläddra\",\n\t\"app.components.InputFile.newFile\": \"Lägg till ny fil\",\n\t\"app.components.InputFileDetails.open\": \"Öppna i en ny flik\",\n\t\"app.components.InputFileDetails.originalName\": \"Originalnamn:\",\n\t\"app.components.InputFileDetails.remove\": \"Ta bort den här filen\",\n\t\"app.components.InputFileDetails.size\": \"Storlek:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"Det kan ta några sekunder att ladda ner och installera pluginet\",\n\t\"app.components.InstallPluginPage.Download.title\": \"Laddar ner...\",\n\t\"app.components.InstallPluginPage.description\": \"Utöka din app enkelt.\",\n\t\"app.components.LeftMenu.collapse\": \"Dölj navigeringsfältet\",\n\t\"app.components.LeftMenu.expand\": \"Expandera navigeringsfältet\",\n\t\"app.components.LeftMenu.general\": \"Allmänt\",\n\t\"app.components.LeftMenu.logout\": \"Logga ut\",\n\t\"app.components.LeftMenu.logo.alt\": \"Applikationslogga\",\n\t\"app.components.LeftMenu.plugins\": \"Plugins\",\n\t\"app.components.LeftMenu.navbrand.title\": \"Strapi Dashboard\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"Arbetsplats\",\n\t\"app.components.LeftMenuFooter.help\": \"Hjälp\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Drivs av \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Samlingstyper\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Konfigurationer\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Allmänt\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Inga plugins installerade än\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Plugins\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Engångstyper\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"Det kan ta några sekunder att avinstallera plugin-programmet.\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"Avinstallerar\",\n\t\"app.components.ListPluginsPage.description\": \"Lista över de installerade plugin-programmen i projektet.\",\n\t\"app.components.ListPluginsPage.head.title\": \"Lista plugins\",\n\t\"app.components.Logout.logout\": \"Logga ut\",\n\t\"app.components.Logout.profile\": \"Profil\",\n\t\"app.components.MarketplaceBanner\": \"Upptäck plugins som skapats av Strapi-communityn, och många fler fantastiska saker för att rivstarta ditt projekt, på Strapi Awesome.\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"en strapi-raket-logga\",\n\t\"app.components.MarketplaceBanner.link\": \"Besök nu\",\n\t\"app.components.NotFoundPage.back\": \"Tillbaka till startsidan\",\n\t\"app.components.NotFoundPage.description\": \"Hittades inte\",\n\t\"app.components.Official\": \"Officiell\",\n\t\"app.components.Onboarding.help.button\": \"Hjälpknapp\",\n\t\"app.components.Onboarding.label.completed\": \"% klar\",\n\t\"app.components.Onboarding.title\": \"Kom igång-videos\",\n\t\"app.components.PluginCard.Button.label.download\": \"Ladda ner\",\n\t\"app.components.PluginCard.Button.label.install\": \"Redan installerad\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"AutoReload-funktionen måste vara inaktiverad. Starta din app med `yarn develop`.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Jag förstår!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Av säkerhetsskäl kan ett plugin bara laddas ner i en utvecklingsmiljö.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Nedladdning är inte möjlig\",\n\t\"app.components.PluginCard.compatible\": \"Kompatibel med din app\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Kompatibel med communityn\",\n\t\"app.components.PluginCard.more-details\": \"Mer detaljer\",\n\t\"app.components.ToggleCheckbox.off-label\": \"Av\",\n\t\"app.components.ToggleCheckbox.on-label\": \"På\",\n\t\"app.components.Users.MagicLink.connect\": \"Kopiera och dela denna länk för att ge åtkomst till denna användare\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"Skicka denna länk till användaren, den första inloggningen kan göras via en SSO-tjänst\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"Användarinformation\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"Användarens roller\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"En användare kan ha en eller flera roller\",\n\t\"app.components.Users.SortPicker.button-label\": \"Sortera efter\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"E-post (A till Ö)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"E-post (Ö till A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"Förnamn (A till Ö)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"Förnamn (Ö till A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"Efternamn (A till Ö)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"Efternamn (Ö till A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"Användarnamn (A till Ö)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"Användarnamn (Ö till A)\",\n\t\"app.components.listPlugins.button\": \"Lägg till nytt plugin\",\n\t\"app.components.listPlugins.title.none\": \"Inga plugins installerade\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Ett fel inträffade vid avinstallation av pluginet\",\n\t\"app.containers.App.notification.error.init\": \"Ett fel uppstod när API:t begärdes\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Om du inte får den här länken, kontakta din administratör.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Det kan ta några minuter att få lösenordsåterställningslänken.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email skickat\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"Aktiv\",\n\t\"app.containers.Users.EditPage.header.label\": \"Redigera {name}\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"Redigera användare\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"Tilldelade roller\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"Bjud in användare\",\n\t\"app.links.configure-view\": \"Konfigurera vyn\",\n\t\"app.page.not.found\": \"Hoppsan! Vi kan inte hitta sidan du letar efter...\",\n\t\"app.static.links.cheatsheet\": \"CheatSheet\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"Lägg till filter\",\n\t\"app.utils.close-label\": \"Stäng\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.duplicate\": \"Duplicera\",\n\t\"app.utils.edit\": \"Redigera\",\n\t\"app.utils.delete\": \"Delete\",\n\t\"app.utils.errors.file-too-big.message\": \"Filen är för stor\",\n\t\"app.utils.filter-value\": \"Filtervärde\",\n\t\"app.utils.filters\": \"Filter\",\n\t\"app.utils.notify.data-loaded\": \"{target} har laddats\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"Publicera\",\n\t\"app.utils.select-all\": \"Välj alla\",\n\t\"app.utils.select-field\": \"Välj fält\",\n\t\"app.utils.select-filter\": \"Välj filter\",\n\t\"app.utils.unpublish\": \"Avpublicera\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"Det här innehållet är för närvarande under uppbyggnad och kommer tillbaka om några veckor!\",\n\t\"component.Input.error.validation.integer\": \"Värdet måste vara ett heltal\",\n\t\"components.AutoReloadBlocker.description\": \"Kör Strapi med ett av de följande kommandon:\",\n\t\"components.AutoReloadBlocker.header\": \"Reload-funktionen krävs för detta plugin.\",\n\t\"components.ErrorBoundary.title\": \"Någonting gick fel...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"innehåller\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"innehåller (okänslig för skiftläge)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"slutar med\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"är\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"är (okänslig för skiftläge)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"är större än\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"är större än eller lika med\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"är lägre än\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"är lägre än eller lika med\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"är inte\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"är inte (okänslig för skiftläge)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"innehåller inte\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"innehåller inte (okänslig för skiftläge)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"är inte null\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"är null\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"börjar med\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"börjar med (okänslig för skiftläge)\",\n\t\"components.Input.error.attribute.key.taken\": \"Detta värde finns redan\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Kan inte vara lika\",\n\t\"components.Input.error.attribute.taken\": \"Detta fältnamn finns redan\",\n\t\"components.Input.error.contain.lowercase\": \"Lösenordet måste innehålla minst en liten bokstav\",\n\t\"components.Input.error.contain.number\": \"lösenordet måste innehålla minst en siffra\",\n\t\"components.Input.error.contain.uppercase\": \"Lösenordet måste innehålla minst en stor bokstav\",\n\t\"components.Input.error.contentTypeName.taken\": \"Detta namn finns redan\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"Lösenorden stämmer inte överens\",\n\t\"components.Input.error.validation.email\": \"Detta är inte en e-post\",\n\t\"components.Input.error.validation.json\": \"Detta stämmer inte med JSON-formatet\",\n\t\"components.Input.error.validation.lowercase\": \"Värdet får bara bestå av små bokstäver\",\n\t\"components.Input.error.validation.max\": \"Värdet är för högt {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"Värdet är för långt {max}.\",\n\t\"components.Input.error.validation.min\": \"Värdet är för lågt {min}.\",\n\t\"components.Input.error.validation.minLength\": \"Värdet är för kort {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"Kan inte vara överlägsen\",\n\t\"components.Input.error.validation.regex\": \"Värdet matchar inte regexet.\",\n\t\"components.Input.error.validation.required\": \"Detta värde krävs.\",\n\t\"components.Input.error.validation.unique\": \"Detta värde används redan.\",\n\t\"components.InputSelect.option.placeholder\": \"Välj här\",\n\t\"components.ListRow.empty\": \"Det finns inga data som ska visas.\",\n\t\"components.NotAllowedInput.text\": \"Ingen behörighet att se detta fält\",\n\t\"components.OverlayBlocker.description\": \"Du använder en funktion som behöver servern att starta om. Vänta tills servern är igång.\",\n\t\"components.OverlayBlocker.description.serverError\": \"Servern borde ha startat om, kontrollera dina loggar i terminalen.\",\n\t\"components.OverlayBlocker.title\": \"Väntar på omstart...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Omstarten tar längre tid än väntat\",\n\t\"components.PageFooter.select\": \"poster per sida\",\n\t\"components.ProductionBlocker.description\": \"För säkerhetsskäl måste vi inaktivera detta plugin i andra miljöer.\",\n\t\"components.ProductionBlocker.header\": \"Denna plugin är endast tillgänglig under utveckling.\",\n\t\"components.Search.placeholder\": \"Sök...\",\n\t\"components.TableHeader.sort\": \"Sortera på {label}\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown-läge\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"Visuellt läge\",\n\t\"components.Wysiwyg.collapse\": \"Fäll ihop\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"Titel H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"Titel H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"Titel H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"Titel H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"Titel H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"Titel H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Lägg till en titel\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"Tecken\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Expandera\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Dra och släpp filer, klistra in från urklipp eller {bläddra}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"välj dem\",\n\t\"components.pagination.go-to\": \"Gå till sidan {page}\",\n\t\"components.pagination.go-to-next\": \"Gå till nästa sida\",\n\t\"components.pagination.go-to-previous\": \"Gå till förra sidan\",\n\t\"components.pagination.remaining-links\": \"Och {number} andra länkar\",\n\t\"components.popUpWarning.button.cancel\": \"Nej, avbryt\",\n\t\"components.popUpWarning.button.confirm\": \"Ja, bekräfta\",\n\t\"components.popUpWarning.message\": \"Är du säker på att du vill radera detta?\",\n\t\"components.popUpWarning.title\": \"Var god bekräfta\",\n\t\"form.button.continue\": \"Fortsätt\",\n\t\"form.button.done\": \"Klar\",\n\t\"global.search\": \"Sök\",\n\t\"global.actions\": \"Åtgärder\",\n\t\"global.back\": \"Backa\",\n\t\"global.cancel\": \"Avbryt\",\n\t\"global.change-password\": \"Ändra lösenord\",\n\t\"global.content-manager\": \"Innehållshanterare\",\n\t\"global.continue\": \"Fortsätt\",\n\t\"global.delete\": \"Ta bort\",\n\t\"global.delete-target\": \"Ta bort {target}\",\n\t\"global.description\": \"Beskrivning\",\n\t\"global.details\": \"Detaljer\",\n\t\"global.disabled\": \"Inaktiverad\",\n\t\"global.documentation\": \"Dokumentation\",\n\t\"global.enabled\": \"Aktiv\",\n\t\"global.finish\": \"Slutför\",\n\t\"global.marketplace\": \"Marketplace\",\n\t\"global.name\": \"Namn\",\n\t\"global.none\": \"Ingen\",\n\t\"global.password\": \"Lösenord\",\n\t\"global.plugins\": \"Plugins\",\n\t\"global.plugins.content-manager\": \"Innehållshanterare\",\n\t\"global.plugins.content-manager.description\": \"Snabbt sätt att se, redigera och ta bort data i din databas.\",\n\t\"global.plugins.content-type-builder\": \"Innehållstypsskapare\",\n\t\"global.plugins.content-type-builder.description\": \"Modellera datastrukturen för ditt API. Skapa nya fält och relationer snabbt. Filerna skapas och uppdateras automatiskt i ditt projekt.\",\n\t\"global.plugins.email\": \"Email\",\n\t\"global.plugins.email.description\": \"Konfigurera din applikation för att skicka e-post.\",\n\t\"global.plugins.upload\": \"Mediebibliotek\",\n\t\"global.plugins.upload.description\": \"Mediafilhantering.\",\n\t\"global.plugins.graphql\": \"GraphQL\",\n\t\"global.plugins.graphql.description\": \"Lägger till GraphQL-endpoint med standard-API-metoder.\",\n\t\"global.plugins.documentation\": \"Dokumentation\",\n\t\"global.plugins.documentation.description\": \"Skapa ett OpenAPI-dokument och visualisera ditt API med SWAGGER UI.\",\n\t\"global.plugins.i18n\": \"Internationalisering\",\n\t\"global.plugins.i18n.description\": \"Denna plugin gör det möjligt att skapa, läsa och uppdatera innehåll på olika språk, både från adminpanelen och från API:et.\",\n\t\"global.plugins.sentry\": \"Sentry\",\n\t\"global.plugins.sentry.description\": \"Skicka Strapi-fel till Sentry.\",\n\t\"global.plugins.users-permissions\": \"Roller och behörigheter\",\n\t\"global.plugins.users-permissions.description\": \"Skydda ditt API med en autentiseringsprocess baserad på JWT. Detta plugin kommer också med ACL som låter dig hantera behörigheterna mellan användargrupperna.\",\n\t\"global.profile\": \"Profil\",\n\t\"global.prompt.unsaved\": \"Är du säker på att du vill lämna den här sidan? Alla dina ändringar kommer att gå förlorade\",\n\t\"global.reset-password\": \"Återställ lösenord\",\n\t\"global.roles\": \"Roller\",\n\t\"global.save\": \"Spara\",\n\t\"global.see-more\": \"Se mer\",\n\t\"global.select\": \"Välj\",\n\t\"global.select-all-entries\": \"Välj alla poster\",\n\t\"global.settings\": \"Inställningar\",\n\t\"global.type\": \"Typ\",\n\t\"global.users\": \"Användare\",\n\t\"notification.contentType.relations.conflict\": \"Innehållstypen har inkompatibla relationer\",\n\t\"notification.default.title\": \"Information:\",\n\t\"notification.error\": \"Ett fel uppstod\",\n\t\"notification.error.layout\": \"Det gick inte att hämta layouten\",\n\t\"notification.form.error.fields\": \"Formuläret innehåller fel\",\n\t\"notification.form.success.fields\": \"Ändringar sparade\",\n\t\"notification.link-copied\": \"Länken har kopierats till urklipp\",\n\t\"notification.permission.not-allowed-read\": \"Du får inte se detta dokument\",\n\t\"notification.success.delete\": \"Objektet har tagits bort\",\n\t\"notification.success.saved\": \"Sparat\",\n\t\"notification.success.title\": \"Lyckat:\",\n\t\"notification.success.apitokencreated\": \"API-token skapad\",\n\t\"notification.success.apitokenedited\": \"API-token uppdaterad\",\n\t\"notification.error.tokennamenotunique\": \"Namnet används redan av en annan token\",\n\t\"notification.version.update.message\": \"En ny version av Strapi är tillgänglig!\",\n\t\"notification.warning.title\": \"Varning:\",\n\t\"notification.warning.404\": \"404 - sidan finns inte\",\n\tor: or,\n\t\"request.error.model.unknown\": \"Denna modellen finns inte\",\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, sv as default, light, or, skipToContent, submit };\n//# sourceMappingURL=sv-D0vPVUAT.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,kDAAkD;AAAA,EAClD,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,gDAAgD;AAAA,EAChD,yCAAyC;AAAA,EACzC,oDAAoD;AAAA,EACpD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,2CAA2C;AAAA,EAC3C,0DAA0D;AAAA,EAC1D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACD;", "names": []}