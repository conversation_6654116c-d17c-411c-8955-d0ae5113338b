import{buildOr as t,buildAnd as n,CompoundCondition as e}from"@ucast/mongo2js";function r(t){return Array.isArray(t)?t:[t]}function o(t,n,e){let r=t;let o=n;if(-1!==n.indexOf(".")){const e=n.split(".");o=e.pop();r=e.reduce(((t,n)=>{t[n]=t[n]||{};return t[n]}),t)}r[o]=e}Object.hasOwn||Object.prototype.hasOwnProperty.call.bind(Object.prototype.hasOwnProperty);function c(t,n,e,r){const o={};const c=t.rulesFor(n,e);for(let t=0;t<c.length;t++){const n=c[t];const e=n.inverted?"$and":"$or";if(!n.conditions)if(n.inverted)break;else{delete o[e];return o}else{o[e]=o[e]||[];o[e].push(r(n))}}return o.$or?o:null}function u(t){if(!t.ast)throw new Error(`Ability rule "${JSON.stringify(t)}" does not have "ast" property. So, cannot be used to generate AST`);return t.inverted?new e("not",[t.ast]):t.ast}function i(e,r,o){const i=c(e,r,o,u);if(null===i)return null;if(!i.$and)return i.$or?t(i.$or):n([]);if(i.$or)i.$and.push(t(i.$or));return n(i.$and)}function f(t,n,e){return t.rulesFor(n,e).reduce(((t,n)=>{if(n.inverted||!n.conditions)return t;return Object.keys(n.conditions).reduce(((t,e)=>{const r=n.conditions[e];if(!r||r.constructor!==Object)o(t,e,r);return t}),t)}),{})}function s(t,n,e,r){const o=t.detectSubjectType(e);const c=t.possibleRulesFor(n,o);const u=new Set;const i=u.delete.bind(u);const f=u.add.bind(u);let s=c.length;while(s--){const t=c[s];if(t.matchesConditions(e)){const n=t.inverted?i:f;r.fieldsFrom(t).forEach(n)}}return Array.from(u)}const l=t=>Array.isArray(t)?t.join(","):t;function a(t,n){return t.map((t=>{const e=[l(t.action||t.actions),"function"===typeof n?r(t.subject).map(n).join(","):l(t.subject),t.conditions||0,t.inverted?1:0,t.fields?l(t.fields):0,t.reason||""];while(e.length>0&&!e[e.length-1])e.pop();return e}))}function b(t,n){return t.map((([t,e,r,o,c,u])=>{const i=e.split(",");const f={inverted:!!o,action:t.split(","),subject:"function"===typeof n?i.map(n):i};if(r)f.conditions=r;if(c)f.fields=c.split(",");if(u)f.reason=u;return f}))}export{a as packRules,s as permittedFieldsOf,i as rulesToAST,f as rulesToFields,c as rulesToQuery,b as unpackRules};
//# sourceMappingURL=extra.mjs.map
