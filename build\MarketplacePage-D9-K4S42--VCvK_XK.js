import{au as I,ba as $e,a as Ve,ay as At,r as U,b$ as Lt,H as kt,f as Ye,bl as We,m as n,L as ce,aX as Ot,bK as yt,ja as Nt,w as _,J as M,s as z,n as ue,cn as K,iN as _t,S as xe,a9 as It,aa as Vt,aj as Re,A as _e,ak as Dt,aL as Ft,aM as Gt,jb as Ue,bE as Xe,bN as Bt,G as Qe,iC as Ht,c0 as qt,d7 as zt,aS as Yt,jc as Wt,aT as Ie,hu as Ut,jd as Xt,bT as jt,bq as Qt,N as Jt,b2 as Je,aH as Ee,br as Kt,je as Zt,ax as es}from"./strapi-YzJfjJ2z.js";import{p as he,s as L,c as F,l as De,r as ye,a as St,d as Pt,b as wt,i as ts,v as ss}from"./lt-Cs1sdUvq.js";const rs=he,as=(t,e)=>{const s=rs(t.trim().replace(/^[=v]+/,""),e);return s?s.version:null};var ns=as;const Ke=L,is=(t,e,s,r,a)=>{typeof s=="string"&&(a=r,r=s,s=void 0);try{return new Ke(t instanceof Ke?t.version:t,s).inc(e,r,a).version}catch{return null}};var os=is;const Ze=he,ls=(t,e)=>{const s=Ze(t,null,!0),r=Ze(e,null,!0),a=s.compare(r);if(a===0)return null;const i=a>0,o=i?s:r,h=i?r:s,u=!!o.prerelease.length;if(!!h.prerelease.length&&!u)return!h.patch&&!h.minor?"major":o.patch?"patch":o.minor?"minor":"major";const l=u?"pre":"";return s.major!==r.major?l+"major":s.minor!==r.minor?l+"minor":s.patch!==r.patch?l+"patch":"prerelease"};var cs=ls;const us=L,hs=(t,e)=>new us(t,e).major;var ds=hs;const fs=L,gs=(t,e)=>new fs(t,e).minor;var ps=gs;const ms=L,vs=(t,e)=>new ms(t,e).patch;var $s=vs;const xs=he,ys=(t,e)=>{const s=xs(t,e);return s&&s.prerelease.length?s.prerelease:null};var js=ys;const Ss=F,Ps=(t,e,s)=>Ss(e,t,s);var ws=Ps;const bs=F,Rs=(t,e)=>bs(t,e,!0);var Es=Rs;const et=L,Cs=(t,e,s)=>{const r=new et(t,s),a=new et(e,s);return r.compare(a)||r.compareBuild(a)};var Fe=Cs;const Ts=Fe,Ms=(t,e)=>t.sort((s,r)=>Ts(s,r,e));var As=Ms;const Ls=Fe,ks=(t,e)=>t.sort((s,r)=>Ls(r,s,e));var Os=ks;const Ns=F,_s=(t,e,s)=>Ns(t,e,s)>0;var je=_s;const Is=F,Vs=(t,e,s)=>Is(t,e,s)===0;var bt=Vs;const Ds=F,Fs=(t,e,s)=>Ds(t,e,s)!==0;var Rt=Fs;const Gs=F,Bs=(t,e,s)=>Gs(t,e,s)>=0;var Ge=Bs;const Hs=F,qs=(t,e,s)=>Hs(t,e,s)<=0;var Be=qs;const zs=bt,Ys=Rt,Ws=je,Us=Ge,Xs=De,Qs=Be,Js=(t,e,s,r)=>{switch(e){case"===":return typeof t=="object"&&(t=t.version),typeof s=="object"&&(s=s.version),t===s;case"!==":return typeof t=="object"&&(t=t.version),typeof s=="object"&&(s=s.version),t!==s;case"":case"=":case"==":return zs(t,s,r);case"!=":return Ys(t,s,r);case">":return Ws(t,s,r);case">=":return Us(t,s,r);case"<":return Xs(t,s,r);case"<=":return Qs(t,s,r);default:throw new TypeError(`Invalid operator: ${e}`)}};var Et=Js;const Ks=L,Zs=he,{safeRe:fe,t:ge}=ye,er=(t,e)=>{if(t instanceof Ks)return t;if(typeof t=="number"&&(t=String(t)),typeof t!="string")return null;e=e||{};let s=null;if(!e.rtl)s=t.match(fe[ge.COERCE]);else{let r;for(;(r=fe[ge.COERCERTL].exec(t))&&(!s||s.index+s[0].length!==t.length);)(!s||r.index+r[0].length!==s.index+s[0].length)&&(s=r),fe[ge.COERCERTL].lastIndex=r.index+r[1].length+r[2].length;fe[ge.COERCERTL].lastIndex=-1}return s===null?null:Zs(`${s[2]}.${s[3]||"0"}.${s[4]||"0"}`,e)};var tr=er,Ce,tt;function sr(){return tt||(tt=1,Ce=function(t){t.prototype[Symbol.iterator]=function*(){for(let e=this.head;e;e=e.next)yield e.value}}),Ce}var rr=S;S.Node=X;S.create=S;function S(t){var e=this;if(e instanceof S||(e=new S),e.tail=null,e.head=null,e.length=0,t&&typeof t.forEach=="function")t.forEach(function(a){e.push(a)});else if(arguments.length>0)for(var s=0,r=arguments.length;s<r;s++)e.push(arguments[s]);return e}S.prototype.removeNode=function(t){if(t.list!==this)throw new Error("removing node which does not belong to this list");var e=t.next,s=t.prev;return e&&(e.prev=s),s&&(s.next=e),t===this.head&&(this.head=e),t===this.tail&&(this.tail=s),t.list.length--,t.next=null,t.prev=null,t.list=null,e};S.prototype.unshiftNode=function(t){if(t!==this.head){t.list&&t.list.removeNode(t);var e=this.head;t.list=this,t.next=e,e&&(e.prev=t),this.head=t,this.tail||(this.tail=t),this.length++}};S.prototype.pushNode=function(t){if(t!==this.tail){t.list&&t.list.removeNode(t);var e=this.tail;t.list=this,t.prev=e,e&&(e.next=t),this.tail=t,this.head||(this.head=t),this.length++}};S.prototype.push=function(){for(var t=0,e=arguments.length;t<e;t++)nr(this,arguments[t]);return this.length};S.prototype.unshift=function(){for(var t=0,e=arguments.length;t<e;t++)ir(this,arguments[t]);return this.length};S.prototype.pop=function(){if(this.tail){var t=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,t}};S.prototype.shift=function(){if(this.head){var t=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,t}};S.prototype.forEach=function(t,e){e=e||this;for(var s=this.head,r=0;s!==null;r++)t.call(e,s.value,r,this),s=s.next};S.prototype.forEachReverse=function(t,e){e=e||this;for(var s=this.tail,r=this.length-1;s!==null;r--)t.call(e,s.value,r,this),s=s.prev};S.prototype.get=function(t){for(var e=0,s=this.head;s!==null&&e<t;e++)s=s.next;if(e===t&&s!==null)return s.value};S.prototype.getReverse=function(t){for(var e=0,s=this.tail;s!==null&&e<t;e++)s=s.prev;if(e===t&&s!==null)return s.value};S.prototype.map=function(t,e){e=e||this;for(var s=new S,r=this.head;r!==null;)s.push(t.call(e,r.value,this)),r=r.next;return s};S.prototype.mapReverse=function(t,e){e=e||this;for(var s=new S,r=this.tail;r!==null;)s.push(t.call(e,r.value,this)),r=r.prev;return s};S.prototype.reduce=function(t,e){var s,r=this.head;if(arguments.length>1)s=e;else if(this.head)r=this.head.next,s=this.head.value;else throw new TypeError("Reduce of empty list with no initial value");for(var a=0;r!==null;a++)s=t(s,r.value,a),r=r.next;return s};S.prototype.reduceReverse=function(t,e){var s,r=this.tail;if(arguments.length>1)s=e;else if(this.tail)r=this.tail.prev,s=this.tail.value;else throw new TypeError("Reduce of empty list with no initial value");for(var a=this.length-1;r!==null;a--)s=t(s,r.value,a),r=r.prev;return s};S.prototype.toArray=function(){for(var t=new Array(this.length),e=0,s=this.head;s!==null;e++)t[e]=s.value,s=s.next;return t};S.prototype.toArrayReverse=function(){for(var t=new Array(this.length),e=0,s=this.tail;s!==null;e++)t[e]=s.value,s=s.prev;return t};S.prototype.slice=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var s=new S;if(e<t||e<0)return s;t<0&&(t=0),e>this.length&&(e=this.length);for(var r=0,a=this.head;a!==null&&r<t;r++)a=a.next;for(;a!==null&&r<e;r++,a=a.next)s.push(a.value);return s};S.prototype.sliceReverse=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var s=new S;if(e<t||e<0)return s;t<0&&(t=0),e>this.length&&(e=this.length);for(var r=this.length,a=this.tail;a!==null&&r>e;r--)a=a.prev;for(;a!==null&&r>t;r--,a=a.prev)s.push(a.value);return s};S.prototype.splice=function(t,e,...s){t>this.length&&(t=this.length-1),t<0&&(t=this.length+t);for(var r=0,a=this.head;a!==null&&r<t;r++)a=a.next;for(var i=[],r=0;a&&r<e;r++)i.push(a.value),a=this.removeNode(a);a===null&&(a=this.tail),a!==this.head&&a!==this.tail&&(a=a.prev);for(var r=0;r<s.length;r++)a=ar(this,a,s[r]);return i};S.prototype.reverse=function(){for(var t=this.head,e=this.tail,s=t;s!==null;s=s.prev){var r=s.prev;s.prev=s.next,s.next=r}return this.head=e,this.tail=t,this};function ar(t,e,s){var r=e===t.head?new X(s,null,e,t):new X(s,e,e.next,t);return r.next===null&&(t.tail=r),r.prev===null&&(t.head=r),t.length++,r}function nr(t,e){t.tail=new X(e,t.tail,null,t),t.head||(t.head=t.tail),t.length++}function ir(t,e){t.head=new X(e,null,t.head,t),t.tail||(t.tail=t.head),t.length++}function X(t,e,s,r){if(!(this instanceof X))return new X(t,e,s,r);this.list=r,this.value=t,e?(e.next=this,this.prev=e):this.prev=null,s?(s.prev=this,this.next=s):this.next=null}try{sr()(S)}catch{}const or=rr,Y=Symbol("max"),B=Symbol("length"),Z=Symbol("lengthCalculator"),le=Symbol("allowStale"),W=Symbol("maxAge"),G=Symbol("dispose"),st=Symbol("noDisposeOnSet"),C=Symbol("lruList"),N=Symbol("cache"),Ct=Symbol("updateAgeOnGet"),Te=()=>1;class lr{constructor(e){if(typeof e=="number"&&(e={max:e}),e||(e={}),e.max&&(typeof e.max!="number"||e.max<0))throw new TypeError("max must be a non-negative number");this[Y]=e.max||1/0;const s=e.length||Te;if(this[Z]=typeof s!="function"?Te:s,this[le]=e.stale||!1,e.maxAge&&typeof e.maxAge!="number")throw new TypeError("maxAge must be a number");this[W]=e.maxAge||0,this[G]=e.dispose,this[st]=e.noDisposeOnSet||!1,this[Ct]=e.updateAgeOnGet||!1,this.reset()}set max(e){if(typeof e!="number"||e<0)throw new TypeError("max must be a non-negative number");this[Y]=e||1/0,ie(this)}get max(){return this[Y]}set allowStale(e){this[le]=!!e}get allowStale(){return this[le]}set maxAge(e){if(typeof e!="number")throw new TypeError("maxAge must be a non-negative number");this[W]=e,ie(this)}get maxAge(){return this[W]}set lengthCalculator(e){typeof e!="function"&&(e=Te),e!==this[Z]&&(this[Z]=e,this[B]=0,this[C].forEach(s=>{s.length=this[Z](s.value,s.key),this[B]+=s.length})),ie(this)}get lengthCalculator(){return this[Z]}get length(){return this[B]}get itemCount(){return this[C].length}rforEach(e,s){s=s||this;for(let r=this[C].tail;r!==null;){const a=r.prev;rt(this,e,r,s),r=a}}forEach(e,s){s=s||this;for(let r=this[C].head;r!==null;){const a=r.next;rt(this,e,r,s),r=a}}keys(){return this[C].toArray().map(e=>e.key)}values(){return this[C].toArray().map(e=>e.value)}reset(){this[G]&&this[C]&&this[C].length&&this[C].forEach(e=>this[G](e.key,e.value)),this[N]=new Map,this[C]=new or,this[B]=0}dump(){return this[C].map(e=>me(this,e)?!1:{k:e.key,v:e.value,e:e.now+(e.maxAge||0)}).toArray().filter(e=>e)}dumpLru(){return this[C]}set(e,s,r){if(r=r||this[W],r&&typeof r!="number")throw new TypeError("maxAge must be a number");const a=r?Date.now():0,i=this[Z](s,e);if(this[N].has(e)){if(i>this[Y])return te(this,this[N].get(e)),!1;const u=this[N].get(e).value;return this[G]&&(this[st]||this[G](e,u.value)),u.now=a,u.maxAge=r,u.value=s,this[B]+=i-u.length,u.length=i,this.get(e),ie(this),!0}const o=new cr(e,s,i,a,r);return o.length>this[Y]?(this[G]&&this[G](e,s),!1):(this[B]+=o.length,this[C].unshift(o),this[N].set(e,this[C].head),ie(this),!0)}has(e){if(!this[N].has(e))return!1;const s=this[N].get(e).value;return!me(this,s)}get(e){return Me(this,e,!0)}peek(e){return Me(this,e,!1)}pop(){const e=this[C].tail;return e?(te(this,e),e.value):null}del(e){te(this,this[N].get(e))}load(e){this.reset();const s=Date.now();for(let r=e.length-1;r>=0;r--){const a=e[r],i=a.e||0;if(i===0)this.set(a.k,a.v);else{const o=i-s;o>0&&this.set(a.k,a.v,o)}}}prune(){this[N].forEach((e,s)=>Me(this,s,!1))}}const Me=(t,e,s)=>{const r=t[N].get(e);if(r){const a=r.value;if(me(t,a)){if(te(t,r),!t[le])return}else s&&(t[Ct]&&(r.value.now=Date.now()),t[C].unshiftNode(r));return a.value}},me=(t,e)=>{if(!e||!e.maxAge&&!t[W])return!1;const s=Date.now()-e.now;return e.maxAge?s>e.maxAge:t[W]&&s>t[W]},ie=t=>{if(t[B]>t[Y])for(let e=t[C].tail;t[B]>t[Y]&&e!==null;){const s=e.prev;te(t,e),e=s}},te=(t,e)=>{if(e){const s=e.value;t[G]&&t[G](s.key,s.value),t[B]-=s.length,t[N].delete(s.key),t[C].removeNode(e)}};class cr{constructor(e,s,r,a,i){this.key=e,this.value=s,this.length=r,this.now=a,this.maxAge=i||0}}const rt=(t,e,s,r)=>{let a=s.value;me(t,a)&&(te(t,s),t[le]||(a=void 0)),a&&e.call(r,a.value,a.key,t)};var ur=lr,Ae,at;function V(){if(at)return Ae;at=1;class t{constructor(c,$){if($=r($),c instanceof t)return c.loose===!!$.loose&&c.includePrerelease===!!$.includePrerelease?c:new t(c.raw,$);if(c instanceof a)return this.raw=c.value,this.set=[[c]],this.format(),this;if(this.options=$,this.loose=!!$.loose,this.includePrerelease=!!$.includePrerelease,this.raw=c.trim().split(/\s+/).join(" "),this.set=this.raw.split("||").map(m=>this.parseRange(m.trim())).filter(m=>m.length),!this.set.length)throw new TypeError(`Invalid SemVer Range: ${this.raw}`);if(this.set.length>1){const m=this.set[0];if(this.set=this.set.filter(v=>!O(v[0])),this.set.length===0)this.set=[m];else if(this.set.length>1){for(const v of this.set)if(v.length===1&&H(v[0])){this.set=[v];break}}}this.format()}format(){return this.range=this.set.map(c=>c.join(" ").trim()).join("||").trim(),this.range}toString(){return this.range}parseRange(c){const m=((this.options.includePrerelease&&y)|(this.options.loose&&g))+":"+c,v=s.get(m);if(v)return v;const p=this.options.loose,x=p?h[u.HYPHENRANGELOOSE]:h[u.HYPHENRANGE];c=c.replace(x,k(this.options.includePrerelease)),i("hyphen replace",c),c=c.replace(h[u.COMPARATORTRIM],P),i("comparator trim",c),c=c.replace(h[u.TILDETRIM],l),i("tilde trim",c),c=c.replace(h[u.CARETTRIM],d),i("caret trim",c);let w=c.split(" ").map(R=>J(R,this.options)).join(" ").split(/\s+/).map(R=>T(R,this.options));p&&(w=w.filter(R=>(i("loose invalid filter",R,this.options),!!R.match(h[u.COMPARATORLOOSE])))),i("range list",w);const j=new Map,b=w.map(R=>new a(R,this.options));for(const R of b){if(O(R))return[R];j.set(R.value,R)}j.size>1&&j.has("")&&j.delete("");const A=[...j.values()];return s.set(m,A),A}intersects(c,$){if(!(c instanceof t))throw new TypeError("a Range is required");return this.set.some(m=>Q(m,$)&&c.set.some(v=>Q(v,$)&&m.every(p=>v.every(x=>p.intersects(x,$)))))}test(c){if(!c)return!1;if(typeof c=="string")try{c=new o(c,this.options)}catch{return!1}for(let $=0;$<this.set.length;$++)if(be(this.set[$],c,this.options))return!0;return!1}}Ae=t;const e=ur,s=new e({max:1e3}),r=wt,a=Se(),i=Pt,o=L,{safeRe:h,t:u,comparatorTrimReplace:P,tildeTrimReplace:l,caretTrimReplace:d}=ye,{FLAG_INCLUDE_PRERELEASE:y,FLAG_LOOSE:g}=St,O=f=>f.value==="<0.0.0-0",H=f=>f.value==="",Q=(f,c)=>{let $=!0;const m=f.slice();let v=m.pop();for(;$&&m.length;)$=m.every(p=>v.intersects(p,c)),v=m.pop();return $},J=(f,c)=>(i("comp",f,c),f=q(f,c),i("caret",f),f=se(f,c),i("tildes",f),f=ae(f,c),i("xrange",f),f=de(f,c),i("stars",f),f),E=f=>!f||f.toLowerCase()==="x"||f==="*",se=(f,c)=>f.trim().split(/\s+/).map($=>re($,c)).join(" "),re=(f,c)=>{const $=c.loose?h[u.TILDELOOSE]:h[u.TILDE];return f.replace($,(m,v,p,x,w)=>{i("tilde",f,m,v,p,x,w);let j;return E(v)?j="":E(p)?j=`>=${v}.0.0 <${+v+1}.0.0-0`:E(x)?j=`>=${v}.${p}.0 <${v}.${+p+1}.0-0`:w?(i("replaceTilde pr",w),j=`>=${v}.${p}.${x}-${w} <${v}.${+p+1}.0-0`):j=`>=${v}.${p}.${x} <${v}.${+p+1}.0-0`,i("tilde return",j),j})},q=(f,c)=>f.trim().split(/\s+/).map($=>D($,c)).join(" "),D=(f,c)=>{i("caret",f,c);const $=c.loose?h[u.CARETLOOSE]:h[u.CARET],m=c.includePrerelease?"-0":"";return f.replace($,(v,p,x,w,j)=>{i("caret",f,v,p,x,w,j);let b;return E(p)?b="":E(x)?b=`>=${p}.0.0${m} <${+p+1}.0.0-0`:E(w)?p==="0"?b=`>=${p}.${x}.0${m} <${p}.${+x+1}.0-0`:b=`>=${p}.${x}.0${m} <${+p+1}.0.0-0`:j?(i("replaceCaret pr",j),p==="0"?x==="0"?b=`>=${p}.${x}.${w}-${j} <${p}.${x}.${+w+1}-0`:b=`>=${p}.${x}.${w}-${j} <${p}.${+x+1}.0-0`:b=`>=${p}.${x}.${w}-${j} <${+p+1}.0.0-0`):(i("no pr"),p==="0"?x==="0"?b=`>=${p}.${x}.${w}${m} <${p}.${x}.${+w+1}-0`:b=`>=${p}.${x}.${w}${m} <${p}.${+x+1}.0-0`:b=`>=${p}.${x}.${w} <${+p+1}.0.0-0`),i("caret return",b),b})},ae=(f,c)=>(i("replaceXRanges",f,c),f.split(/\s+/).map($=>we($,c)).join(" ")),we=(f,c)=>{f=f.trim();const $=c.loose?h[u.XRANGELOOSE]:h[u.XRANGE];return f.replace($,(m,v,p,x,w,j)=>{i("xRange",f,m,v,p,x,w,j);const b=E(p),A=b||E(x),R=A||E(w),ne=R;return v==="="&&ne&&(v=""),j=c.includePrerelease?"-0":"",b?v===">"||v==="<"?m="<0.0.0-0":m="*":v&&ne?(A&&(x=0),w=0,v===">"?(v=">=",A?(p=+p+1,x=0,w=0):(x=+x+1,w=0)):v==="<="&&(v="<",A?p=+p+1:x=+x+1),v==="<"&&(j="-0"),m=`${v+p}.${x}.${w}${j}`):A?m=`>=${p}.0.0${j} <${+p+1}.0.0-0`:R&&(m=`>=${p}.${x}.0${j} <${p}.${+x+1}.0-0`),i("xRange return",m),m})},de=(f,c)=>(i("replaceStars",f,c),f.trim().replace(h[u.STAR],"")),T=(f,c)=>(i("replaceGTE0",f,c),f.trim().replace(h[c.includePrerelease?u.GTE0PRE:u.GTE0],"")),k=f=>(c,$,m,v,p,x,w,j,b,A,R,ne,Ja)=>(E(m)?$="":E(v)?$=`>=${m}.0.0${f?"-0":""}`:E(p)?$=`>=${m}.${v}.0${f?"-0":""}`:x?$=`>=${$}`:$=`>=${$}${f?"-0":""}`,E(b)?j="":E(A)?j=`<${+b+1}.0.0-0`:E(R)?j=`<${b}.${+A+1}.0-0`:ne?j=`<=${b}.${A}.${R}-${ne}`:f?j=`<${b}.${A}.${+R+1}-0`:j=`<=${j}`,`${$} ${j}`.trim()),be=(f,c,$)=>{for(let m=0;m<f.length;m++)if(!f[m].test(c))return!1;if(c.prerelease.length&&!$.includePrerelease){for(let m=0;m<f.length;m++)if(i(f[m].semver),f[m].semver!==a.ANY&&f[m].semver.prerelease.length>0){const v=f[m].semver;if(v.major===c.major&&v.minor===c.minor&&v.patch===c.patch)return!0}return!1}return!0};return Ae}var Le,nt;function Se(){if(nt)return Le;nt=1;const t=Symbol("SemVer ANY");class e{static get ANY(){return t}constructor(l,d){if(d=s(d),l instanceof e){if(l.loose===!!d.loose)return l;l=l.value}l=l.trim().split(/\s+/).join(" "),o("comparator",l,d),this.options=d,this.loose=!!d.loose,this.parse(l),this.semver===t?this.value="":this.value=this.operator+this.semver.version,o("comp",this)}parse(l){const d=this.options.loose?r[a.COMPARATORLOOSE]:r[a.COMPARATOR],y=l.match(d);if(!y)throw new TypeError(`Invalid comparator: ${l}`);this.operator=y[1]!==void 0?y[1]:"",this.operator==="="&&(this.operator=""),y[2]?this.semver=new h(y[2],this.options.loose):this.semver=t}toString(){return this.value}test(l){if(o("Comparator.test",l,this.options.loose),this.semver===t||l===t)return!0;if(typeof l=="string")try{l=new h(l,this.options)}catch{return!1}return i(l,this.operator,this.semver,this.options)}intersects(l,d){if(!(l instanceof e))throw new TypeError("a Comparator is required");return this.operator===""?this.value===""?!0:new u(l.value,d).test(this.value):l.operator===""?l.value===""?!0:new u(this.value,d).test(l.semver):(d=s(d),d.includePrerelease&&(this.value==="<0.0.0-0"||l.value==="<0.0.0-0")||!d.includePrerelease&&(this.value.startsWith("<0.0.0")||l.value.startsWith("<0.0.0"))?!1:!!(this.operator.startsWith(">")&&l.operator.startsWith(">")||this.operator.startsWith("<")&&l.operator.startsWith("<")||this.semver.version===l.semver.version&&this.operator.includes("=")&&l.operator.includes("=")||i(this.semver,"<",l.semver,d)&&this.operator.startsWith(">")&&l.operator.startsWith("<")||i(this.semver,">",l.semver,d)&&this.operator.startsWith("<")&&l.operator.startsWith(">")))}}Le=e;const s=wt,{safeRe:r,t:a}=ye,i=Et,o=Pt,h=L,u=V();return Le}const hr=V(),dr=(t,e,s)=>{try{e=new hr(e,s)}catch{return!1}return e.test(t)};var Pe=dr;const fr=V(),gr=(t,e)=>new fr(t,e).set.map(s=>s.map(r=>r.value).join(" ").trim().split(" "));var pr=gr;const mr=L,vr=V(),$r=(t,e,s)=>{let r=null,a=null,i=null;try{i=new vr(e,s)}catch{return null}return t.forEach(o=>{i.test(o)&&(!r||a.compare(o)===-1)&&(r=o,a=new mr(r,s))}),r};var xr=$r;const yr=L,jr=V(),Sr=(t,e,s)=>{let r=null,a=null,i=null;try{i=new jr(e,s)}catch{return null}return t.forEach(o=>{i.test(o)&&(!r||a.compare(o)===1)&&(r=o,a=new yr(r,s))}),r};var Pr=Sr;const ke=L,wr=V(),it=je,br=(t,e)=>{t=new wr(t,e);let s=new ke("0.0.0");if(t.test(s)||(s=new ke("0.0.0-0"),t.test(s)))return s;s=null;for(let r=0;r<t.set.length;++r){const a=t.set[r];let i=null;a.forEach(o=>{const h=new ke(o.semver.version);switch(o.operator){case">":h.prerelease.length===0?h.patch++:h.prerelease.push(0),h.raw=h.format();case"":case">=":(!i||it(h,i))&&(i=h);break;case"<":case"<=":break;default:throw new Error(`Unexpected operation: ${o.operator}`)}}),i&&(!s||it(s,i))&&(s=i)}return s&&t.test(s)?s:null};var Rr=br;const Er=V(),Cr=(t,e)=>{try{return new Er(t,e).range||"*"}catch{return null}};var Tr=Cr;const Mr=L,Tt=Se(),{ANY:Ar}=Tt,Lr=V(),kr=Pe,ot=je,lt=De,Or=Be,Nr=Ge,_r=(t,e,s,r)=>{t=new Mr(t,r),e=new Lr(e,r);let a,i,o,h,u;switch(s){case">":a=ot,i=Or,o=lt,h=">",u=">=";break;case"<":a=lt,i=Nr,o=ot,h="<",u="<=";break;default:throw new TypeError('Must provide a hilo val of "<" or ">"')}if(kr(t,e,r))return!1;for(let P=0;P<e.set.length;++P){const l=e.set[P];let d=null,y=null;if(l.forEach(g=>{g.semver===Ar&&(g=new Tt(">=0.0.0")),d=d||g,y=y||g,a(g.semver,d.semver,r)?d=g:o(g.semver,y.semver,r)&&(y=g)}),d.operator===h||d.operator===u||(!y.operator||y.operator===h)&&i(t,y.semver))return!1;if(y.operator===u&&o(t,y.semver))return!1}return!0};var He=_r;const Ir=He,Vr=(t,e,s)=>Ir(t,e,">",s);var Dr=Vr;const Fr=He,Gr=(t,e,s)=>Fr(t,e,"<",s);var Br=Gr;const ct=V(),Hr=(t,e,s)=>(t=new ct(t,s),e=new ct(e,s),t.intersects(e,s));var qr=Hr;const zr=Pe,Yr=F;var Wr=(t,e,s)=>{const r=[];let a=null,i=null;const o=t.sort((l,d)=>Yr(l,d,s));for(const l of o)zr(l,e,s)?(i=l,a||(a=l)):(i&&r.push([a,i]),i=null,a=null);a&&r.push([a,null]);const h=[];for(const[l,d]of r)l===d?h.push(l):!d&&l===o[0]?h.push("*"):d?l===o[0]?h.push(`<=${d}`):h.push(`${l} - ${d}`):h.push(`>=${l}`);const u=h.join(" || "),P=typeof e.raw=="string"?e.raw:String(e);return u.length<P.length?u:e};const ut=V(),qe=Se(),{ANY:Oe}=qe,oe=Pe,ze=F,Ur=(t,e,s={})=>{if(t===e)return!0;t=new ut(t,s),e=new ut(e,s);let r=!1;e:for(const a of t.set){for(const i of e.set){const o=Qr(a,i,s);if(r=r||o!==null,o)continue e}if(r)return!1}return!0},Xr=[new qe(">=0.0.0-0")],ht=[new qe(">=0.0.0")],Qr=(t,e,s)=>{if(t===e)return!0;if(t.length===1&&t[0].semver===Oe){if(e.length===1&&e[0].semver===Oe)return!0;s.includePrerelease?t=Xr:t=ht}if(e.length===1&&e[0].semver===Oe){if(s.includePrerelease)return!0;e=ht}const r=new Set;let a,i;for(const g of t)g.operator===">"||g.operator===">="?a=dt(a,g,s):g.operator==="<"||g.operator==="<="?i=ft(i,g,s):r.add(g.semver);if(r.size>1)return null;let o;if(a&&i){if(o=ze(a.semver,i.semver,s),o>0)return null;if(o===0&&(a.operator!==">="||i.operator!=="<="))return null}for(const g of r){if(a&&!oe(g,String(a),s)||i&&!oe(g,String(i),s))return null;for(const O of e)if(!oe(g,String(O),s))return!1;return!0}let h,u,P,l,d=i&&!s.includePrerelease&&i.semver.prerelease.length?i.semver:!1,y=a&&!s.includePrerelease&&a.semver.prerelease.length?a.semver:!1;d&&d.prerelease.length===1&&i.operator==="<"&&d.prerelease[0]===0&&(d=!1);for(const g of e){if(l=l||g.operator===">"||g.operator===">=",P=P||g.operator==="<"||g.operator==="<=",a){if(y&&g.semver.prerelease&&g.semver.prerelease.length&&g.semver.major===y.major&&g.semver.minor===y.minor&&g.semver.patch===y.patch&&(y=!1),g.operator===">"||g.operator===">="){if(h=dt(a,g,s),h===g&&h!==a)return!1}else if(a.operator===">="&&!oe(a.semver,String(g),s))return!1}if(i){if(d&&g.semver.prerelease&&g.semver.prerelease.length&&g.semver.major===d.major&&g.semver.minor===d.minor&&g.semver.patch===d.patch&&(d=!1),g.operator==="<"||g.operator==="<="){if(u=ft(i,g,s),u===g&&u!==i)return!1}else if(i.operator==="<="&&!oe(i.semver,String(g),s))return!1}if(!g.operator&&(i||a)&&o!==0)return!1}return!(a&&P&&!i&&o!==0||i&&l&&!a&&o!==0||y||d)},dt=(t,e,s)=>{if(!t)return e;const r=ze(t.semver,e.semver,s);return r>0?t:r<0||e.operator===">"&&t.operator===">="?e:t},ft=(t,e,s)=>{if(!t)return e;const r=ze(t.semver,e.semver,s);return r<0?t:r>0||e.operator==="<"&&t.operator==="<="?e:t};var Jr=Ur;const Ne=ye,gt=St,Kr=L,pt=ts,Zr=he,ea=ss,ta=ns,sa=os,ra=cs,aa=ds,na=ps,ia=$s,oa=js,la=F,ca=ws,ua=Es,ha=Fe,da=As,fa=Os,ga=je,pa=De,ma=bt,va=Rt,$a=Ge,xa=Be,ya=Et,ja=tr,Sa=Se(),Pa=V(),wa=Pe,ba=pr,Ra=xr,Ea=Pr,Ca=Rr,Ta=Tr,Ma=He,Aa=Dr,La=Br,ka=qr,Oa=Wr,Na=Jr;var ve={parse:Zr,valid:ea,clean:ta,inc:sa,diff:ra,major:aa,minor:na,patch:ia,prerelease:oa,compare:la,rcompare:ca,compareLoose:ua,compareBuild:ha,sort:da,rsort:fa,gt:ga,lt:pa,eq:ma,neq:va,gte:$a,lte:xa,cmp:ya,coerce:ja,Comparator:Sa,Range:Pa,satisfies:wa,toComparators:ba,maxSatisfying:Ra,minSatisfying:Ea,minVersion:Ca,validRange:Ta,outside:Ma,gtr:Aa,ltr:La,intersects:ka,simplifyRange:Oa,subset:Na,SemVer:Kr,re:Ne.re,src:Ne.src,tokens:Ne.t,SEMVER_SPEC_VERSION:gt.SEMVER_SPEC_VERSION,RELEASE_TYPES:gt.RELEASE_TYPES,compareIdentifiers:pt.compareIdentifiers,rcompareIdentifiers:pt.rcompareIdentifiers};function _a(t,e){const[s,r]=U.useState(t);return U.useEffect(()=>{const a=setTimeout(()=>{r(t)},e);return()=>{clearTimeout(a)}},[t,e]),s}const Ia=({handleSelectClear:t,handleSelectChange:e,npmPackageType:s,possibleCategories:r,possibleCollections:a,query:i})=>{const{formatMessage:o}=I(),h=(u,P)=>{const l={[P]:(i[P]??[]).filter(d=>d!==u)};e(l)};return n.jsxs(Re.Root,{children:[n.jsx(Re.Trigger,{children:n.jsx(_e,{variant:"tertiary",startIcon:n.jsx(Dt,{}),children:o({id:"app.utils.filters",defaultMessage:"Filters"})})}),n.jsx(Re.Content,{sideOffset:4,children:n.jsxs(_,{padding:3,direction:"column",alignItems:"stretch",gap:1,children:[n.jsx(mt,{message:o({id:"admin.pages.MarketPlacePage.filters.collections",defaultMessage:"Collections"}),value:i?.collections||[],onChange:u=>{e({collections:u})},onClear:()=>t("collections"),possibleFilters:a,customizeContent:u=>o({id:"admin.pages.MarketPlacePage.filters.collectionsSelected",defaultMessage:"{count, plural, =0 {No collections} one {# collection} other {# collections}} selected"},{count:u?.length??0})}),s==="plugin"&&n.jsx(mt,{message:o({id:"admin.pages.MarketPlacePage.filters.categories",defaultMessage:"Categories"}),value:i?.categories||[],onChange:u=>{e({categories:u})},onClear:()=>t("categories"),possibleFilters:r,customizeContent:u=>o({id:"admin.pages.MarketPlacePage.filters.categoriesSelected",defaultMessage:"{count, plural, =0 {No categories} one {# category} other {# categories}} selected"},{count:u?.length??0})})]})}),i.collections?.map(u=>n.jsx(M,{padding:1,children:n.jsx(Ue,{icon:n.jsx(Xe,{}),onClick:()=>h(u,"collections"),children:u})},u)),s==="plugin"&&i.categories?.map(u=>n.jsx(M,{padding:1,children:n.jsx(Ue,{icon:n.jsx(Xe,{}),onClick:()=>h(u,"categories"),children:u})},u))]})},mt=({message:t,value:e,onChange:s,possibleFilters:r,onClear:a,customizeContent:i})=>n.jsx(Ft,{"data-testid":`${t}-button`,"aria-label":t,placeholder:t,onChange:s,onClear:a,value:e,customizeContent:i,children:Object.entries(r).map(([o,h])=>n.jsx(Gt,{"data-testid":`${o}-${h}`,value:o,children:`${o} (${h})`},o))}),Va=xe(z)`
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
`,Da=({npmPackage:t,isInstalled:e,useYarn:s,isInDevelopmentMode:r,npmPackageType:a,strapiAppVersion:i})=>{const{attributes:o}=t,{formatMessage:h}=I(),{trackUsage:u}=$e(),P=s?`yarn add ${o.npmPackageName}`:`npm install ${o.npmPackageName}`,l=h({id:"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi",defaultMessage:"Made by Strapi"}),d=`https://market.strapi.io/${Ht.plural(a)}/${o.slug}`,y=ve.validRange(o.strapiVersion),g=y?ve.satisfies(i??"",y):!1;return n.jsxs(_,{direction:"column",justifyContent:"space-between",paddingTop:4,paddingRight:4,paddingBottom:4,paddingLeft:4,hasRadius:!0,background:"neutral0",shadow:"tableShadow",height:"100%",alignItems:"normal","data-testid":"npm-package-card",children:[n.jsxs(M,{children:[n.jsxs(_,{direction:"row",justifyContent:"space-between",alignItems:"flex-start",children:[n.jsx(M,{tag:"img",src:o.logo.url,alt:`${o.name} logo`,hasRadius:!0,width:11,height:11}),n.jsx(Ba,{githubStars:o.githubStars,npmDownloads:o.npmDownloads,npmPackageType:a})]}),n.jsx(M,{paddingTop:4,children:n.jsx(z,{tag:"h3",variant:"delta",children:n.jsxs(_,{alignItems:"center",gap:o.validated&&!o.madeByStrapi?2:1,children:[o.name,o.validated&&!o.madeByStrapi&&n.jsx(Ie,{description:h({id:"admin.pages.MarketPlacePage.plugin.tooltip.verified",defaultMessage:"Plugin verified by Strapi"}),children:n.jsx(Ut,{fill:"success600"})}),o.madeByStrapi&&n.jsx(Ie,{description:l,children:n.jsx(M,{tag:"img",src:Xt,alt:l,width:6,height:"auto"})})]})})}),n.jsx(M,{paddingTop:2,children:n.jsx(Va,{tag:"p",variant:"omega",textColor:"neutral600",children:o.description})})]}),n.jsxs(_,{gap:2,style:{alignSelf:"flex-end"},paddingTop:6,children:[n.jsx(yt,{size:"S",href:d,isExternal:!0,endIcon:n.jsx(jt,{}),"aria-label":h({id:"admin.pages.MarketPlacePage.plugin.info.label",defaultMessage:"Learn more about {pluginName}"},{pluginName:o.name}),variant:"tertiary",onClick:()=>u("didPluginLearnMore"),children:h({id:"admin.pages.MarketPlacePage.plugin.info.text",defaultMessage:"More"})}),n.jsx(Fa,{isInstalled:e,isInDevelopmentMode:r,isCompatible:g,commandToCopy:P,strapiAppVersion:i,strapiPeerDepVersion:o.strapiVersion,pluginName:o.name})]})]})},Fa=({isInstalled:t,isInDevelopmentMode:e,isCompatible:s,commandToCopy:r,strapiAppVersion:a,strapiPeerDepVersion:i,pluginName:o})=>{const{toggleNotification:h}=Ve(),{formatMessage:u}=I(),{trackUsage:P}=$e(),{copy:l}=Qt(),d=async()=>{await l(r)&&(P("willInstallPlugin"),h({type:"success",message:u({id:"admin.pages.MarketPlacePage.plugin.copy.success"})}))};return t?n.jsxs(_,{gap:2,paddingLeft:4,children:[n.jsx(Jt,{width:"1.2rem",height:"1.2rem",color:"success600"}),n.jsx(z,{variant:"omega",textColor:"success600",fontWeight:"bold",children:u({id:"admin.pages.MarketPlacePage.plugin.installed",defaultMessage:"Installed"})})]}):e&&s!==!1?n.jsx(Ga,{strapiAppVersion:a,strapiPeerDepVersion:i,handleCopy:d,pluginName:o}):null},Ga=({strapiPeerDepVersion:t,strapiAppVersion:e,handleCopy:s,pluginName:r})=>{const{formatMessage:a}=I(),i=ve.validRange(t),o=ve.satisfies(e??"",i??""),h=a({id:"admin.pages.MarketPlacePage.plugin.copy",defaultMessage:"Copy install command"});return e&&(!i||!o)?n.jsx(Ie,{"data-testid":`tooltip-${r}`,label:a({id:"admin.pages.MarketPlacePage.plugin.version",defaultMessage:'Update your Strapi version: "{strapiAppVersion}" to: "{versionRange}"'},{strapiAppVersion:e,versionRange:i}),children:n.jsx("span",{children:n.jsx(_e,{size:"S",startIcon:n.jsx(Je,{}),variant:"secondary",onClick:s,disabled:!o,children:h})})}):n.jsx(_e,{size:"S",startIcon:n.jsx(Je,{}),variant:"secondary",onClick:s,children:h})},Ba=({githubStars:t=0,npmDownloads:e=0,npmPackageType:s})=>{const{formatMessage:r}=I();return n.jsxs(_,{gap:1,children:[!!t&&n.jsxs(n.Fragment,{children:[n.jsx(qt,{height:"1.2rem",width:"1.2rem","aria-hidden":!0}),n.jsx(zt,{height:"1.2rem",width:"1.2rem",fill:"warning500","aria-hidden":!0}),n.jsx("p",{"aria-label":r({id:`admin.pages.MarketPlacePage.${s}.githubStars`,defaultMessage:"This {package} was starred {starsCount} on GitHub"},{starsCount:t,package:s}),children:n.jsx(z,{variant:"pi",textColor:"neutral800",children:t})}),n.jsx(Ha,{})]}),n.jsx(Wt,{height:"1.2rem",width:"1.2rem","aria-hidden":!0}),n.jsx("p",{"aria-label":r({id:`admin.pages.MarketPlacePage.${s}.downloads`,defaultMessage:"This {package} has {downloadsCount} weekly downloads"},{downloadsCount:e,package:s}),children:n.jsx(z,{variant:"pi",textColor:"neutral800",children:e})})]})},Ha=xe(Yt)`
  width: 1.2rem;
  transform: rotate(90deg);
`,vt=({status:t,npmPackages:e=[],installedPackageNames:s=[],useYarn:r,isInDevelopmentMode:a,npmPackageType:i,strapiAppVersion:o,debouncedSearch:h})=>{const{formatMessage:u}=I();if(t==="error")return n.jsx(ue.Error,{});if(t==="loading")return n.jsx(ue.Loading,{});const P=u({id:"admin.pages.MarketPlacePage.search.empty",defaultMessage:'No result for "{target}"'},{target:h});return e.length===0?n.jsxs(M,{position:"relative",children:[n.jsx(ce.Grid,{size:"M",children:Array(12).fill(null).map((l,d)=>n.jsx(qa,{height:"234px",hasRadius:!0},d))}),n.jsx(M,{position:"absolute",top:11,width:"100%",children:n.jsxs(_,{alignItems:"center",justifyContent:"center",direction:"column",children:[n.jsx(Bt,{width:"160px",height:"88px"}),n.jsx(M,{paddingTop:6,children:n.jsx(z,{variant:"delta",tag:"p",textColor:"neutral600",children:P})})]})})]}):n.jsx(Qe.Root,{gap:4,children:e.map(l=>n.jsx(Qe.Item,{col:4,s:6,xs:12,style:{height:"100%"},direction:"column",alignItems:"stretch",children:n.jsx(Da,{npmPackage:l,isInstalled:s.includes(l.attributes.npmPackageName),useYarn:r,isInDevelopmentMode:a,npmPackageType:i,strapiAppVersion:o})},l.id))})},qa=xe(M)`
  background: ${({theme:t})=>`linear-gradient(180deg, rgba(234, 234, 239, 0) 0%, ${t.colors.neutral150} 100%)`};
  opacity: 0.33;
`,Mt=({isOnline:t,npmPackageType:e="plugin"})=>{const{formatMessage:s}=I(),{trackUsage:r}=$e(),a=e==="provider"?"didSubmitProvider":"didSubmitPlugin";return n.jsx(ce.Header,{title:s({id:"global.marketplace",defaultMessage:"Marketplace"}),subtitle:s({id:"admin.pages.MarketPlacePage.subtitle",defaultMessage:"Get more out of Strapi"}),primaryAction:t&&n.jsx(yt,{startIcon:n.jsx(Nt,{}),variant:"tertiary",href:`https://market.strapi.io/submit-${e}`,onClick:()=>r(a),isExternal:!0,children:s({id:`admin.pages.MarketPlacePage.submit.${e}.link`,defaultMessage:`Submit ${e}`})})})},za=()=>{const{formatMessage:t}=I();return n.jsx(ce.Root,{children:n.jsxs(Ot,{children:[n.jsx(Mt,{}),n.jsxs(_,{width:"100%",direction:"column",alignItems:"center",justifyContent:"center",paddingTop:"12rem",children:[n.jsx(M,{paddingBottom:2,children:n.jsx(z,{textColor:"neutral700",variant:"alpha",children:t({id:"admin.pages.MarketPlacePage.offline.title",defaultMessage:"You are offline"})})}),n.jsx(M,{paddingBottom:6,children:n.jsx(z,{textColor:"neutral700",variant:"epsilon",children:t({id:"admin.pages.MarketPlacePage.offline.subtitle",defaultMessage:"You need to be connected to the Internet to access Strapi Market."})})}),n.jsxs("svg",{width:"88",height:"88",viewBox:"0 0 88 88",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:[n.jsx("rect",{x:".5",y:".5",width:"87",height:"87",rx:"43.5",fill:"#F0F0FF"}),n.jsx("path",{d:"M34 39.3h-4c-2.6 0-4.7 1-6.6 2.8a9 9 0 0 0-2.7 6.6 9 9 0 0 0 2.7 6.6A9 9 0 0 0 30 58h22.8L34 39.3Zm-11-11 3-3 39 39-3 3-4.7-4.6H30a13.8 13.8 0 0 1-14-14c0-3.8 1.3-7 4-9.7 2.6-2.7 5.7-4.2 9.5-4.3L23 28.2Zm38.2 11.1c3 .2 5.5 1.5 7.6 3.7A11 11 0 0 1 72 51c0 4-1.6 7.2-5 9.5l-3.3-3.4a6.5 6.5 0 0 0 3.6-6.1c0-1.9-.7-3.5-2-5-1.5-1.3-3.1-2-5-2h-3.5v-1.2c0-3.6-1.2-6.6-3.7-9a13 13 0 0 0-15-2.3L34.6 28a17 17 0 0 1 20.3 1.5c3.5 2.7 5.5 6 6.3 10Z",fill:"#4945FF"}),n.jsx("rect",{x:".5",y:".5",width:"87",height:"87",rx:"43.5",stroke:"#D9D8FF"})]})]})]})})},$t={"name:asc":{selected:{id:"admin.pages.MarketPlacePage.sort.alphabetical.selected",defaultMessage:"Sort by alphabetical order"},option:{id:"admin.pages.MarketPlacePage.sort.alphabetical",defaultMessage:"Alphabetical order"}},"submissionDate:desc":{selected:{id:"admin.pages.MarketPlacePage.sort.newest.selected",defaultMessage:"Sort by newest"},option:{id:"admin.pages.MarketPlacePage.sort.newest",defaultMessage:"Newest"}},"githubStars:desc":{selected:{id:"admin.pages.MarketPlacePage.sort.githubStars.selected",defaultMessage:"Sort by GitHub stars"},option:{id:"admin.pages.MarketPlacePage.sort.githubStars",defaultMessage:"Number of GitHub stars"}},"npmDownloads:desc":{selected:{id:"admin.pages.MarketPlacePage.sort.npmDownloads.selected",defaultMessage:"Sort by npm downloads"},option:{id:"admin.pages.MarketPlacePage.sort.npmDownloads",defaultMessage:"Number of downloads"}}},Ya=({sortQuery:t,handleSelectChange:e})=>{const{formatMessage:s}=I();return n.jsx(Wa,{children:n.jsx(It,{value:t,customizeContent:()=>s($t[t].selected),onChange:r=>{e({sort:r})},"aria-label":s({id:"admin.pages.MarketPlacePage.sort.label",defaultMessage:"Sort by"}),size:"S",children:Object.entries($t).map(([r,a])=>n.jsx(Vt,{value:r,children:s(a.option)},r))})})},Wa=xe(M)`
  font-weight: ${({theme:t})=>t.fontWeights.semiBold};

  span {
    font-size: ${({theme:t})=>t.fontSizes[1]};
  }
`,xt="https://market-api.strapi.io";function Ua({npmPackageType:t,debouncedSearch:e,query:s,tabQuery:r,strapiVersion:a}){const{notifyStatus:i}=kt(),{formatMessage:o}=I(),{toggleNotification:h}=Ve(),u=o({id:"global.marketplace",defaultMessage:"Marketplace"}),P=()=>{i(o({id:"app.utils.notify.data-loaded",defaultMessage:"The {target} has loaded"},{target:u}))},l={page:s?.page||1,pageSize:s?.pageSize||24},d={...r.plugin,pagination:l,search:e,version:a},{data:y,status:g}=Ye(["marketplace","plugins",d],async()=>{try{const q=We.stringify(d),D=await fetch(`${xt}/plugins?${q}`);if(!D.ok)throw new Error("Failed to fetch marketplace plugins.");return await D.json()}catch{}return null},{onSuccess(){P()},onError(){h({type:"danger",message:o({id:"notification.error",defaultMessage:"An error occured"})})}}),O={...r.provider,pagination:l,search:e,version:a},{data:H,status:Q}=Ye(["marketplace","providers",O],async()=>{const q=We.stringify(O),D=await fetch(`${xt}/providers?${q}`);if(!D.ok)throw new Error("Failed to fetch marketplace providers.");return await D.json()},{onSuccess(){P()},onError(){h({type:"danger",message:o({id:"notification.error",defaultMessage:"An error occured"})})}}),J=t==="plugin"?y:H,E=J?.meta.collections??{},se=y?.meta.categories??{},{pagination:re}=J?.meta??{};return{pluginsResponse:y,providersResponse:H,pluginsStatus:g,providersStatus:Q,possibleCollections:E,possibleCategories:se,pagination:re}}const Xa=()=>{const t=typeof navigator<"u"&&typeof navigator.onLine=="boolean"?navigator.onLine:!0,[e,s]=U.useState(t),r=()=>s(!0),a=()=>s(!1);return U.useEffect(()=>(window.addEventListener("online",r),window.addEventListener("offline",a),()=>{window.removeEventListener("online",r),window.removeEventListener("offline",a)}),[]),e},ee="plugin",pe="provider",Qa=()=>{const{formatMessage:t}=I(),{trackUsage:e}=$e(),{toggleNotification:s}=Ve(),[{query:r},a]=At(),i=_a(r?.search,500)||"",{autoReload:o,dependencies:h,useYarn:u,strapiVersion:P}=Lt("MarketplacePage",T=>T),l=Xa(),d=r?.npmPackageType||ee,[y,g]=U.useState({plugin:d===ee?{...r}:{},provider:d===pe?{...r}:{}});U.useEffect(()=>{e("didGoToMarketplace")},[e]),U.useEffect(()=>{o||s({type:"info",message:t({id:"admin.pages.MarketPlacePage.production",defaultMessage:"Manage plugins from the development environment"})})},[s,o,t]);const{pluginsResponse:O,providersResponse:H,pluginsStatus:Q,providersStatus:J,possibleCollections:E,possibleCategories:se,pagination:re}=Ua({npmPackageType:d,debouncedSearch:i,query:r,tabQuery:y,strapiVersion:P});if(!l)return n.jsx(za,{});const q=T=>{const k=T===ee||T===pe?T:ee,be=y[k]&&Object.keys(y[k]).length;a(be?{...y[k],search:r?.search||"",npmPackageType:k,page:1}:{npmPackageType:k,collections:[],categories:[],sort:"name:asc",page:1,search:r?.search||""})},D=T=>{a({...T,page:1}),g(k=>({...k,[d]:{...k[d],...T}}))},ae=T=>{a({[T]:[],page:void 0},"remove"),g(k=>({...k,[d]:{}}))},we=({sort:T})=>D({sort:T}),de=Object.keys(h??{});return n.jsx(ce.Root,{children:n.jsxs(ue.Main,{children:[n.jsx(ue.Title,{children:t({id:"admin.pages.MarketPlacePage.head",defaultMessage:"Marketplace - Plugins"})}),n.jsx(Mt,{isOnline:l,npmPackageType:d}),n.jsx(ce.Content,{children:n.jsxs(K.Root,{variant:"simple",onValueChange:q,value:d,children:[n.jsxs(_,{justifyContent:"space-between",paddingBottom:4,children:[n.jsxs(K.List,{"aria-label":t({id:"admin.pages.MarketPlacePage.tab-group.label",defaultMessage:"Plugins and Providers for Strapi"}),children:[n.jsxs(K.Trigger,{value:ee,children:[t({id:"admin.pages.MarketPlacePage.plugins",defaultMessage:"Plugins"})," ",O?`(${O.meta.pagination.total})`:"..."]}),n.jsxs(K.Trigger,{value:pe,children:[t({id:"admin.pages.MarketPlacePage.providers",defaultMessage:"Providers"})," ",H?`(${H.meta.pagination.total})`:"..."]})]}),n.jsx(M,{width:"25%",children:n.jsx(_t,{name:"searchbar",onClear:()=>a({search:"",page:1}),value:r?.search,onChange:T=>a({search:T.target.value,page:1}),clearLabel:t({id:"admin.pages.MarketPlacePage.search.clear",defaultMessage:"Clear the search"}),placeholder:t({id:"admin.pages.MarketPlacePage.search.placeholder",defaultMessage:"Search"}),children:t({id:"admin.pages.MarketPlacePage.search.placeholder",defaultMessage:"Search"})})})]}),n.jsxs(_,{paddingBottom:4,gap:2,children:[n.jsx(Ya,{sortQuery:r?.sort||"name:asc",handleSelectChange:we}),n.jsx(Ia,{npmPackageType:d,possibleCollections:E,possibleCategories:se,query:r||{},handleSelectChange:D,handleSelectClear:ae})]}),n.jsx(K.Content,{value:ee,children:n.jsx(vt,{npmPackages:O?.data,status:Q,installedPackageNames:de,useYarn:u,isInDevelopmentMode:o,npmPackageType:"plugin",strapiAppVersion:P,debouncedSearch:i})}),n.jsx(K.Content,{value:pe,children:n.jsx(vt,{npmPackages:H?.data,status:J,installedPackageNames:de,useYarn:u,isInDevelopmentMode:o,npmPackageType:"provider",debouncedSearch:i})}),n.jsxs(Ee.Root,{...re,defaultPageSize:24,children:[n.jsx(Ee.PageSize,{options:["12","24","50","100"]}),n.jsx(Ee.Links,{})]}),n.jsx(M,{paddingTop:8,children:n.jsx("a",{href:"https://strapi.canny.io/plugin-requests",target:"_blank",rel:"noopener noreferrer nofollow",style:{textDecoration:"none"},onClick:()=>e("didMissMarketplacePlugin"),children:n.jsx(Kt,{title:t({id:"admin.pages.MarketPlacePage.missingPlugin.title",defaultMessage:"Documentation"}),subtitle:t({id:"admin.pages.MarketPlacePage.missingPlugin.description",defaultMessage:"Tell us what plugin you are looking for and we'll let our community plugin developers know in case they are in search for inspiration!"}),icon:n.jsx(Zt,{}),iconBackground:"alternative100",endAction:n.jsx(jt,{fill:"neutral600",width:"1.2rem",height:"1.2rem",style:{marginLeft:"0.8rem"}})})})})]})})]})})},en=()=>{const t=es(e=>e.admin_app.permissions.marketplace?.main);return n.jsx(ue.Protect,{permissions:t,children:n.jsx(Qa,{})})};export{Qa as MarketplacePage,en as ProtectedMarketplacePage};
