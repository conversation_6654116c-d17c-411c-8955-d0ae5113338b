{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/id-cH3Ovozx.mjs"], "sourcesContent": ["const Analytics = \"Analisis\";\nconst Documentation = \"Dokumentasi\";\nconst Email = \"Email\";\nconst Password = \"Kada sandi\";\nconst Provider = \"Penyedia\";\nconst ResetPasswordToken = \"Setel Ulang Token Sandi\";\nconst Role = \"Peran\";\nconst Username = \"Nama pengguna\";\nconst Users = \"Pengguna\";\nconst id = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"Akun anda telah disuspen\",\n\t\"Auth.form.button.forgot-password\": \"Kirim Email\",\n\t\"Auth.form.button.go-home\": \"KE BERANDA\",\n\t\"Auth.form.button.login\": \"Masuk\",\n\t\"Auth.form.button.register\": \"AYO MULAI\",\n\t\"Auth.form.confirmPassword.label\": \"Konfirmasi Kata sandi\",\n\t\"Auth.form.email.label\": \"Email\",\n\t\"Auth.form.email.placeholder\": \"<EMAIL>\",\n\t\"Auth.form.error.blocked\": \"Akun anda diblokir administator.\",\n\t\"Auth.form.error.code.provide\": \"<PERSON>de yang anda masukkan salah.\",\n\t\"Auth.form.error.confirmed\": \"Email anda belum dikonfirmasi.\",\n\t\"Auth.form.error.email.invalid\": \"Email tidak valid.\",\n\t\"Auth.form.error.email.provide\": \"Harap berikan nama pengguna atau email.\",\n\t\"Auth.form.error.email.taken\": \"Email sudah digunakan.\",\n\t\"Auth.form.error.invalid\": \"Pengenal atau kata sandi tidak valid.\",\n\t\"Auth.form.error.params.provide\": \"Parameter yang salah.\",\n\t\"Auth.form.error.password.format\": \"Kata sandi Anda tidak boleh mengandung simbol `$` lebih dari tiga kali.\",\n\t\"Auth.form.error.password.local\": \"Pengguna ini tidak pernah menyetel kata sandi lokal, harap masuk melalui penyedia yang digunakan selama pembuatan akun.\",\n\t\"Auth.form.error.password.matching\": \"Sandi tidak cocok.\",\n\t\"Auth.form.error.password.provide\": \"Harap berikan sandi Anda.\",\n\t\"Auth.form.error.ratelimit\": \"Terlalu banyak upaya, coba lagi dalam satu menit.\",\n\t\"Auth.form.error.user.not-exist\": \"Email ini tidak terdaftar.\",\n\t\"Auth.form.error.username.taken\": \"Nama pengguna sudah dipakai.\",\n\t\"Auth.form.firstname.label\": \"Nama depan\",\n\t\"Auth.form.firstname.placeholder\": \"Kai\",\n\t\"Auth.form.forgot-password.email.label\": \"Masukkan email Anda\",\n\t\"Auth.form.forgot-password.email.label.success\": \"Email berhasil dikirim ke\",\n\t\"Auth.form.lastname.label\": \"Nama belakang\",\n\t\"Auth.form.lastname.placeholder\": \"Doe\",\n\t\"Auth.form.register.news.label\": \"Terus kabari saya tentang fitur baru dan peningkatan yang akan datang (dengan melakukan ini, Anda menerima {syarat} dan {kebijakan}).\",\n\t\"Auth.form.rememberMe.label\": \"Ingat saya\",\n\t\"Auth.form.username.label\": \"Nama pengguna\",\n\t\"Auth.form.username.placeholder\": \"Kai Doe\",\n\t\"Auth.link.forgot-password\": \"Lupa kata sandi?\",\n\t\"Auth.link.ready\": \"Siap masuk?\",\n\t\"Auth.link.signin\": \"Masuk\",\n\t\"Auth.link.signin.account\": \"Sudak memiliki akun?\",\n\t\"Auth.privacy-policy-agreement.policy\": \"kebijakan privasi\",\n\t\"Auth.privacy-policy-agreement.terms\": \"istilah\",\n\t\"Content Manager\": \"Pengelola Konten\",\n\t\"Content Type Builder\": \"Pembuat Tipe Konten\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Unggah File\",\n\t\"HomePage.head.title\": \"Beranda\",\n\t\"HomePage.roadmap\": \"Lihat roadmap kami\",\n\t\"HomePage.welcome.congrats\": \"Selamat!\",\n\t\"HomePage.welcome.congrats.content\": \"Anda masuk sebagai administrator pertama. Untuk mengetahui fitur-fitur canggih yang disediakan Strapi,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"kami menyarankan Anda untuk membuat Jenis-Koleksi pertama Anda.\",\n\t\"Media Library\": \"Pustaka Media\",\n\t\"New entry\": \"Masukan baru\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"Peran & Izin\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"Beberapa peran tidak dapat dihapus karena dikaitkan dengan pengguna\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"Peran tidak dapat dihapus jika dikaitkan dengan pengguna\",\n\t\"Roles.components.List.empty.withSearch\": \"Tidak ada peran yang sesuai dengan pencarian ({search}) ...\",\n\t\"Settings.PageTitle\": \"Pengaturan - {name}\",\n\t\"Settings.error\": \"Error\",\n\t\"Settings.global\": \"Pengaturan Global\",\n\t\"Settings.permissions\": \"Panel administrasi\",\n\t\"Settings.permissions.category\": \"Setelan izin untuk {category}\",\n\t\"Settings.permissions.conditions.anytime\": \"Kapan saja\",\n\t\"Settings.permissions.conditions.apply\": \"Terapkan\",\n\t\"Settings.permissions.conditions.can\": \"Bisa\",\n\t\"Settings.permissions.conditions.conditions\": \"Tentukan kondisi\",\n\t\"Settings.permissions.conditions.links\": \"Tautan\",\n\t\"Settings.permissions.conditions.no-actions\": \"Tidak ada tindakan\",\n\t\"Settings.permissions.conditions.or\": \"ATAU\",\n\t\"Settings.permissions.conditions.when\": \"Ketika\",\n\t\"Settings.permissions.users.create\": \"Buat pengguna Baru\",\n\t\"Settings.permissions.users.email\": \"Email\",\n\t\"Settings.permissions.users.firstname\": \"Nama depan\",\n\t\"Settings.permissions.users.lastname\": \"Nama belakang\",\n\t\"Settings.roles.create.description\": \"Tentukan hak yang diberikan untuk peran tersebut\",\n\t\"Settings.roles.create.title\": \"Buat peran\",\n\t\"Settings.roles.created\": \"Peran dibuat\",\n\t\"Settings.roles.edit.title\": \"Ubah peran\",\n\t\"Settings.roles.form.button.users-with-role\": \"Users with this role\",\n\t\"Settings.roles.form.created\": \"Dibuat\",\n\t\"Settings.roles.form.description\": \"Nama dan deskripsi peran\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"Izin bidang\",\n\t\"Settings.roles.form.permissions.create\": \"Buat\",\n\t\"Settings.roles.form.permissions.delete\": \"Hapus\",\n\t\"Settings.roles.form.permissions.publish\": \"Terbitkan\",\n\t\"Settings.roles.form.permissions.read\": \"Baca\",\n\t\"Settings.roles.form.permissions.update\": \"Perbarui\",\n\t\"Settings.roles.list.button.add\": \"Tambah peran baru\",\n\t\"Settings.roles.list.description\": \"Daftar peran\",\n\t\"Settings.roles.title.singular\": \"peran\",\n\t\"Settings.webhooks.create\": \"Buat webhook\",\n\t\"Settings.webhooks.create.header\": \"Buat tajuk baru\",\n\t\"Settings.webhooks.created\": \"Webhook dibuat\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"Acara ini hanya ada untuk konten dengan sistem Draf / Terbit diaktifkan\",\n\t\"Settings.webhooks.events.create\": \"Buat\",\n\t\"Settings.webhooks.events.update\": \"Perbarui\",\n\t\"Settings.webhooks.form.events\": \"Acara\",\n\t\"Settings.webhooks.form.headers\": \"Header\",\n\t\"Settings.webhooks.form.url\": \"Url\",\n\t\"Settings.webhooks.key\": \"Kunci\",\n\t\"Settings.webhooks.list.button.add\": \"Buat webhook baru\",\n\t\"Settings.webhooks.list.description\": \"Dapatkan notifikasi perubahan POST.\",\n\t\"Settings.webhooks.list.empty.description\": \"Tambahkan yang pertama Anda ke daftar ini.\",\n\t\"Settings.webhooks.list.empty.link\": \"Lihat dokumentasi kami\",\n\t\"Settings.webhooks.list.empty.title\": \"Belum ada webhook\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhooks\",\n\t\"Settings.webhooks.trigger\": \"Pemicu\",\n\t\"Settings.webhooks.trigger.cancel\": \"Batalkan pemicu\",\n\t\"Settings.webhooks.trigger.pending\": \"Menunggu…\",\n\t\"Settings.webhooks.trigger.save\": \"Harap simpan untuk memicu\",\n\t\"Settings.webhooks.trigger.success\": \"Sukses!\",\n\t\"Settings.webhooks.trigger.success.label\": \"Pemicu sukses\",\n\t\"Settings.webhooks.trigger.test\": \"Test-pemicu\",\n\t\"Settings.webhooks.trigger.title\": \"Simpan sebelum Memicu\",\n\t\"Settings.webhooks.value\": \"Isi\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"Peran & Ijin\",\n\t\"Users.components.List.empty\": \"Tidak ada pengguna...\",\n\t\"Users.components.List.empty.withFilters\": \"Tidak ada pengguna dengan filter yang diterapkan...\",\n\t\"Users.components.List.empty.withSearch\": \"Tidak ada pengguna yang sesuai dengan pencarian ({search})...\",\n\t\"app.components.BlockLink.code\": \"Contoh kode\",\n\t\"app.components.Button.cancel\": \"Batal\",\n\t\"app.components.Button.reset\": \"Atur ulang\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"Segera hadir\",\n\t\"app.components.DownloadInfo.download\": \"Unduhan sedang berlangsung...\",\n\t\"app.components.DownloadInfo.text\": \"Ini bisa memakan waktu satu menit. Terima kasih atas kesabaran Anda.\",\n\t\"app.components.EmptyAttributes.title\": \"Belum ada bidang\",\n\t\"app.components.HomePage.button.blog\": \"LIHAT LEBIH BANYAK DI BLOG\",\n\t\"app.components.HomePage.community\": \"Temukan komunitas di web\",\n\t\"app.components.HomePage.community.content\": \"Diskusikan dengan anggota tim, kontributor dan pengembang di saluran yang berbeda.\",\n\t\"app.components.HomePage.create\": \"Buat Jenis Konten pertama Anda\",\n\t\"app.components.HomePage.welcome\": \"Selamat bergabung!\",\n\t\"app.components.HomePage.welcome.again\": \"Selamat \",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Kami senang Anda menjadi bagian dari komunitas. Kami terus mencari masukan, jadi jangan ragu untuk mengirimkan DM kepada kami \",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"Kami berharap Anda membuat kemajuan dalam proyek Anda ... Jangan ragu untuk membaca berita terbaru tentang Strapi. Kami memberikan yang terbaik untuk meningkatkan produk berdasarkan umpan balik Anda.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"masalah.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" atau naikkan \",\n\t\"app.components.ImgPreview.hint\": \"Tarik & lepas file Anda ke area ini atau {browse} file untuk diupload\",\n\t\"app.components.ImgPreview.hint.browse\": \"telusuri\",\n\t\"app.components.InputFile.newFile\": \"Tambahkan file baru\",\n\t\"app.components.InputFileDetails.open\": \"Buka di tab baru\",\n\t\"app.components.InputFileDetails.originalName\": \"Nama asli:\",\n\t\"app.components.InputFileDetails.remove\": \"Hapus file ini\",\n\t\"app.components.InputFileDetails.size\": \"Ukuran:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"Mungkin perlu beberapa detik untuk mengunduh dan memasang plugin.\",\n\t\"app.components.InstallPluginPage.Download.title\": \"Mengunduh...\",\n\t\"app.components.InstallPluginPage.description\": \"Perluas aplikasi Anda dengan mudah.\",\n\t\"app.components.LeftMenuFooter.help\": \"Bantuan\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Dipersembahkan oleh \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Jenis Koleksi\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Konfigurasi\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Umum\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Belum ada plugin yang terpasang\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Plugin\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Jenis Tunggal\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"Mungkin perlu beberapa detik untuk mencopot pemasangan plugin.\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"Menghapus instalasi\",\n\t\"app.components.ListPluginsPage.description\": \"Daftar plugin yang diinstal dalam proyek.\",\n\t\"app.components.ListPluginsPage.head.title\": \"Daftar plugin\",\n\t\"app.components.Logout.logout\": \"Keluar\",\n\t\"app.components.Logout.profile\": \"Profil\",\n\t\"app.components.NotFoundPage.back\": \"Kembali ke beranda\",\n\t\"app.components.NotFoundPage.description\": \"Tidak Ditemukan\",\n\t\"app.components.Official\": \"Resmi\",\n\t\"app.components.Onboarding.label.completed\": \"% selesai\",\n\t\"app.components.Onboarding.title\": \"Panduan Memulai\",\n\t\"app.components.PluginCard.Button.label.download\": \"Unduh\",\n\t\"app.components.PluginCard.Button.label.install\": \"Sudah terpasang\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"Fitur autoReload harus diaktifkan. Silakan mulai aplikasi Anda dengan `yarn develop`.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Saya mengeri!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Untuk alasan keamanan, plugin hanya dapat diunduh di lingkungan pengembangan.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Mendownload tidak mungkin\",\n\t\"app.components.PluginCard.compatible\": \"Kompatibel dengan aplikasi Anda\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Kompatibel dengan komunitas\",\n\t\"app.components.PluginCard.more-details\": \"Keterangan lebih lanjut\",\n\t\"app.components.Users.MagicLink.connect\": \"Kirim tautan ini ke pengguna agar mereka dapat terhubung.\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"Detail\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"Peran pengguna\",\n\t\"app.components.Users.SortPicker.button-label\": \"Urutkan\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"Email (A to Z)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"Email (Z to A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"Nama depan (A to Z)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"Nama depan (Z to A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"Nama belakang (A to Z)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"Nama belakang (Z to A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"Nama pengguna (A to Z)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"Nama pengguna (Z to A)\",\n\t\"app.components.listPlugins.button\": \"Tambah Plugin Baru\",\n\t\"app.components.listPlugins.title.none\": \"Tidak ada plugin terpasang\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Terjadi kesalahan saat mencopot pengaya\",\n\t\"app.containers.App.notification.error.init\": \"Terjadi kesalahan saat meminta API\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Jika Anda tidak menerima tautan ini, harap hubungi administrator Anda.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Perlu waktu beberapa menit untuk menerima tautan pemulihan kata sandi Anda.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"Email terkirim\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"Aktif\",\n\t\"app.containers.Users.EditPage.header.label\": \"Ubah {name}\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"Ubah pengguna\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"Peran yang diatribusikan\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"Buat pengguna\",\n\t\"app.links.configure-view\": \"Konfigurasi tampilan\",\n\t\"app.static.links.cheatsheet\": \"Contekan\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"Tambahkan filter\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.errors.file-too-big.message\": \"File terlalu besar\",\n\t\"app.utils.filters\": \"Filter\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"Terbit\",\n\t\"app.utils.select-all\": \"Pilih semua\",\n\t\"app.utils.unpublish\": \"Batal terbit\",\n\t\"component.Input.error.validation.integer\": \"Nilainya harus berupa bilangan bulat\",\n\t\"components.AutoReloadBlocker.description\": \"Jalankan Strapi dengan salah satu dari perintah berikut:\",\n\t\"components.AutoReloadBlocker.header\": \"Fitur muat ulang diperlukan untuk plugin ini.\",\n\t\"components.ErrorBoundary.title\": \"Ada yang salah...\",\n\t\"components.Input.error.attribute.key.taken\": \"Nilai ini sudah ada\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Tidak bisa sama\",\n\t\"components.Input.error.attribute.taken\": \"Nama bidang ini sudah ada\",\n\t\"components.Input.error.contain.lowercase\": \"Kata sandi harus mengandung setidaknya satu karakter huruf kecil\",\n\t\"components.Input.error.contain.number\": \"Kata sandi harus mengandung setidaknya satu angka\",\n\t\"components.Input.error.contain.uppercase\": \"Kata sandi harus mengandung setidaknya satu karakter huruf besar\",\n\t\"components.Input.error.contentTypeName.taken\": \"Nama ini sudah ada\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"Sandi tidak cocok\",\n\t\"components.Input.error.validation.email\": \"Ini bukan email\",\n\t\"components.Input.error.validation.json\": \"Ini tidak cocok dengan format JSON\",\n\t\"components.Input.error.validation.max\": \"Nilainya terlalu tinggi {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"Nilainya terlalu panjang {max}.\",\n\t\"components.Input.error.validation.min\": \"Nilainya terlalu rendah {min}.\",\n\t\"components.Input.error.validation.minLength\": \"Nilainya terlalu pendek {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"Tidak bisa lebih unggul\",\n\t\"components.Input.error.validation.regex\": \"Nilainya tidak cocok dengan regex.\",\n\t\"components.Input.error.validation.required\": \"Nilai ini wajib diisi.\",\n\t\"components.Input.error.validation.unique\": \"Nilai ini sudah digunakan.\",\n\t\"components.InputSelect.option.placeholder\": \"Pilih di sini\",\n\t\"components.ListRow.empty\": \"Tidak ada data untuk ditampilkan.\",\n\t\"components.OverlayBlocker.description\": \"Anda menggunakan fitur yang membutuhkan server untuk dimulai ulang. Harap tunggu sampai server habis.\",\n\t\"components.OverlayBlocker.description.serverError\": \"Server seharusnya telah dimulai ulang, harap periksa log Anda di terminal.\",\n\t\"components.OverlayBlocker.title\": \"Menunggu untuk restart ...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Mulai ulang membutuhkan waktu lebih lama dari yang diharapkan\",\n\t\"components.PageFooter.select\": \"entri per halaman\",\n\t\"components.ProductionBlocker.description\": \"Untuk tujuan keamanan, kami harus menonaktifkan plugin ini di lingkungan lain.\",\n\t\"components.ProductionBlocker.header\": \"Plugin ini hanya tersedia dalam pengembangan.\",\n\t\"components.Search.placeholder\": \"Cari...\",\n\t\"components.Wysiwyg.collapse\": \"Gulung\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"Judul H1\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"Judul H2\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"Judul H3\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"Judul H4\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"Judul H5\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"Judul H6\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Tambahkan judul\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"karakter\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Perbesar\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Tarik & lepas file, tempel dari clipboard atau {browse}.\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"pilih mereka\",\n\t\"components.popUpWarning.button.cancel\": \"Tidak, batalkan\",\n\t\"components.popUpWarning.button.confirm\": \"Ya, konfirmasi\",\n\t\"components.popUpWarning.message\": \"Apa kamu yakin ingin menghapus ini?\",\n\t\"components.popUpWarning.title\": \"Mohon konfirmasi\",\n\t\"form.button.done\": \"Selesai\",\n\t\"global.prompt.unsaved\": \"Anda yakin ingin meninggalkan halaman ini? Semua modifikasi Anda akan hilang\",\n\t\"notification.contentType.relations.conflict\": \"Jenis konten memiliki hubungan yang saling bertentangan\",\n\t\"notification.error\": \"Terjadi kesalahan\",\n\t\"notification.error.layout\": \"Tidak dapat mengambil tata letak\",\n\t\"notification.form.error.fields\": \"Formulir tersebut mengandung beberapa kesalahan\",\n\t\"notification.form.success.fields\": \"Perubahan tersimpan\",\n\t\"notification.link-copied\": \"Tautan disalin ke papan klip\",\n\t\"notification.permission.not-allowed-read\": \"Anda tidak diizinkan untuk melihat dokumen ini\",\n\t\"notification.success.delete\": \"Item telah dihapus\",\n\t\"notification.success.saved\": \"Disimpan\",\n\t\"request.error.model.unknown\": \"Model ini tidak ada\"\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, id as default };\n//# sourceMappingURL=id-cH3Ovozx.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,iCAAiC;AAAA,EACjC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qBAAqB;AAAA,EACrB,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,uBAAuB;AAAA,EACvB,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,oBAAoB;AAAA,EACpB,yBAAyB;AAAA,EACzB,+CAA+C;AAAA,EAC/C,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,+BAA+B;AAChC;", "names": []}