{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/ApiTokens/ListView.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport { EmptyStateLayout, LinkButton } from '@strapi/design-system';\nimport { Plus } from '@strapi/icons';\nimport { EmptyDocuments } from '@strapi/icons/symbols';\nimport { Data } from '@strapi/types';\nimport * as qs from 'qs';\nimport { useIntl } from 'react-intl';\nimport { Link, useNavigate } from 'react-router-dom';\n\nimport { useGuidedTour } from '../../../../components/GuidedTour/Provider';\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useTypedSelector } from '../../../../core/store/hooks';\nimport { useNotification } from '../../../../features/Notifications';\nimport { useTracking } from '../../../../features/Tracking';\nimport { useAPIErrorHandler } from '../../../../hooks/useAPIErrorHandler';\nimport { useOnce } from '../../../../hooks/useOnce';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport { useDeleteAPITokenMutation, useGetAPITokensQuery } from '../../../../services/apiTokens';\nimport { API_TOKEN_TYPE } from '../../components/Tokens/constants';\nimport { Table } from '../../components/Tokens/Table';\n\nconst TABLE_HEADERS = [\n  {\n    name: 'name',\n    label: {\n      id: 'Settings.apiTokens.ListView.headers.name',\n      defaultMessage: 'Name',\n    },\n    sortable: true,\n  },\n  {\n    name: 'description',\n    label: {\n      id: 'Settings.apiTokens.ListView.headers.description',\n      defaultMessage: 'Description',\n    },\n    sortable: false,\n  },\n  {\n    name: 'createdAt',\n    label: {\n      id: 'Settings.apiTokens.ListView.headers.createdAt',\n      defaultMessage: 'Created at',\n    },\n    sortable: false,\n  },\n  {\n    name: 'lastUsedAt',\n    label: {\n      id: 'Settings.apiTokens.ListView.headers.lastUsedAt',\n      defaultMessage: 'Last used',\n    },\n    sortable: false,\n  },\n];\n\nexport const ListView = () => {\n  const { formatMessage } = useIntl();\n  const { toggleNotification } = useNotification();\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['api-tokens']\n  );\n  const {\n    allowedActions: { canRead, canCreate, canDelete, canUpdate },\n  } = useRBAC(permissions);\n  const navigate = useNavigate();\n  const { trackUsage } = useTracking();\n  const startSection = useGuidedTour('ListView', (state) => state.startSection);\n  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();\n\n  React.useEffect(() => {\n    startSection('apiTokens');\n  }, [startSection]);\n\n  React.useEffect(() => {\n    navigate({ search: qs.stringify({ sort: 'name:ASC' }, { encode: false }) });\n  }, [navigate]);\n\n  const headers = TABLE_HEADERS.map((header) => ({\n    ...header,\n    label: formatMessage(header.label),\n  }));\n\n  useOnce(() => {\n    trackUsage('willAccessTokenList', {\n      tokenType: API_TOKEN_TYPE,\n    });\n  });\n\n  const { data: apiTokens = [], isLoading, error } = useGetAPITokensQuery();\n\n  React.useEffect(() => {\n    if (error) {\n      toggleNotification({\n        type: 'danger',\n        message: formatAPIError(error),\n      });\n    }\n  }, [error, formatAPIError, toggleNotification]);\n\n  React.useEffect(() => {\n    trackUsage('didAccessTokenList', { number: apiTokens.length, tokenType: API_TOKEN_TYPE });\n  }, [apiTokens, trackUsage]);\n\n  const [deleteToken] = useDeleteAPITokenMutation();\n\n  const handleDelete = async (id: Data.ID) => {\n    try {\n      const res = await deleteToken(id);\n\n      if ('error' in res) {\n        toggleNotification({\n          type: 'danger',\n          message: formatAPIError(res.error),\n        });\n\n        return;\n      }\n\n      trackUsage('didDeleteToken');\n    } catch {\n      toggleNotification({\n        type: 'danger',\n        message: formatMessage({\n          id: 'notification.error',\n          defaultMessage: 'Something went wrong',\n        }),\n      });\n    }\n  };\n\n  return (\n    <>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          { name: 'API Tokens' }\n        )}\n      </Page.Title>\n      <Layouts.Header\n        title={formatMessage({ id: 'Settings.apiTokens.title', defaultMessage: 'API Tokens' })}\n        subtitle={formatMessage({\n          id: 'Settings.apiTokens.description',\n          defaultMessage: 'List of generated tokens to consume the API',\n        })}\n        primaryAction={\n          canCreate && (\n            <LinkButton\n              tag={Link}\n              data-testid=\"create-api-token-button\"\n              startIcon={<Plus />}\n              size=\"S\"\n              onClick={() =>\n                trackUsage('willAddTokenFromList', {\n                  tokenType: API_TOKEN_TYPE,\n                })\n              }\n              to=\"/settings/api-tokens/create\"\n            >\n              {formatMessage({\n                id: 'Settings.apiTokens.create',\n                defaultMessage: 'Create new API Token',\n              })}\n            </LinkButton>\n          )\n        }\n      />\n      {!canRead ? (\n        <Page.NoPermissions />\n      ) : (\n        <Page.Main aria-busy={isLoading}>\n          <Layouts.Content>\n            {apiTokens.length > 0 && (\n              <Table\n                permissions={{ canRead, canDelete, canUpdate }}\n                headers={headers}\n                isLoading={isLoading}\n                onConfirmDelete={handleDelete}\n                tokens={apiTokens}\n                tokenType={API_TOKEN_TYPE}\n              />\n            )}\n            {canCreate && apiTokens.length === 0 ? (\n              <EmptyStateLayout\n                icon={<EmptyDocuments width=\"16rem\" />}\n                content={formatMessage({\n                  id: 'Settings.apiTokens.addFirstToken',\n                  defaultMessage: 'Add your first API Token',\n                })}\n                action={\n                  <LinkButton\n                    tag={Link}\n                    variant=\"secondary\"\n                    startIcon={<Plus />}\n                    to=\"/settings/api-tokens/create\"\n                  >\n                    {formatMessage({\n                      id: 'Settings.apiTokens.addNewToken',\n                      defaultMessage: 'Add new API Token',\n                    })}\n                  </LinkButton>\n                }\n              />\n            ) : null}\n            {!canCreate && apiTokens.length === 0 ? (\n              <EmptyStateLayout\n                icon={<EmptyDocuments width=\"16rem\" />}\n                content={formatMessage({\n                  id: 'Settings.apiTokens.emptyStateLayout',\n                  defaultMessage: 'You don’t have any content yet...',\n                })}\n              />\n            ) : null}\n          </Layouts.Content>\n        </Page.Main>\n      )}\n    </>\n  );\n};\n\nexport const ProtectedListView = () => {\n  const permissions = useTypedSelector(\n    (state) => state.admin_app.permissions.settings?.['api-tokens'].main\n  );\n\n  return (\n    <Page.Protect permissions={permissions}>\n      <ListView />\n    </Page.Protect>\n  );\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuBA,IAAM,gBAAgB;EACpB;IACE,MAAM;IACN,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,UAAU;EAAA;EAEZ;IACE,MAAM;IACN,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,UAAU;EAAA;EAEZ;IACE,MAAM;IACN,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,UAAU;EAAA;EAEZ;IACE,MAAM;IACN,OAAO;MACL,IAAI;MACJ,gBAAgB;IAAA;IAElB,UAAU;EAAA;AAEd;AAEO,IAAM,WAAW,MAAM;AACtB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,mBAAmB,IAAI,gBAAgB;AAC/C,QAAM,cAAc;IAClB,CAAC,UAAU;;AAAA,yBAAM,UAAU,YAAY,aAA5B,mBAAuC;;EAAY;AAE1D,QAAA;IACJ,gBAAgB,EAAE,SAAS,WAAW,WAAW,UAAU;EAAA,IACzD,QAAQ,WAAW;AACvB,QAAM,WAAW,YAAY;AACvB,QAAA,EAAE,WAAW,IAAI,YAAY;AACnC,QAAM,eAAe,cAAc,YAAY,CAAC,UAAU,MAAM,YAAY;AAC5E,QAAM,EAAE,yBAAyB,eAAe,IAAI,mBAAmB;AAEvE,EAAM,gBAAU,MAAM;AACpB,iBAAa,WAAW;EAAA,GACvB,CAAC,YAAY,CAAC;AAEjB,EAAM,gBAAU,MAAM;AACpB,aAAS,EAAE,QAAW,aAAU,EAAE,MAAM,WAAW,GAAG,EAAE,QAAQ,MAAM,CAAC,EAAA,CAAG;EAAA,GACzE,CAAC,QAAQ,CAAC;AAEb,QAAM,UAAU,cAAc,IAAI,CAAC,YAAY;IAC7C,GAAG;IACH,OAAO,cAAc,OAAO,KAAK;EAAA,EACjC;AAEF,UAAQ,MAAM;AACZ,eAAW,uBAAuB;MAChC,WAAW;IAAA,CACZ;EAAA,CACF;AAEK,QAAA,EAAE,MAAM,YAAY,CAAA,GAAI,WAAW,MAAA,IAAU,qBAAqB;AAExE,EAAM,gBAAU,MAAM;AACpB,QAAI,OAAO;AACU,yBAAA;QACjB,MAAM;QACN,SAAS,eAAe,KAAK;MAAA,CAC9B;IAAA;EACH,GACC,CAAC,OAAO,gBAAgB,kBAAkB,CAAC;AAE9C,EAAM,gBAAU,MAAM;AACpB,eAAW,sBAAsB,EAAE,QAAQ,UAAU,QAAQ,WAAW,eAAA,CAAgB;EAAA,GACvF,CAAC,WAAW,UAAU,CAAC;AAEpB,QAAA,CAAC,WAAW,IAAI,0BAA0B;AAE1C,QAAA,eAAe,OAAO,OAAgB;AACtC,QAAA;AACI,YAAA,MAAM,MAAM,YAAY,EAAE;AAEhC,UAAI,WAAW,KAAK;AACC,2BAAA;UACjB,MAAM;UACN,SAAS,eAAe,IAAI,KAAK;QAAA,CAClC;AAED;MAAA;AAGF,iBAAW,gBAAgB;IAAA,QACrB;AACa,yBAAA;QACjB,MAAM;QACN,SAAS,cAAc;UACrB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA,CACF;IAAA;EACH;AAGF,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE,EAAE,MAAM,aAAa;IAAA,EAEzB,CAAA;QACA;MAAC,QAAQ;MAAR;QACC,OAAO,cAAc,EAAE,IAAI,4BAA4B,gBAAgB,aAAA,CAAc;QACrF,UAAU,cAAc;UACtB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QACD,eACE,iBACE;UAAC;UAAA;YACC,KAAK;YACL,eAAY;YACZ,eAAA,wBAAY,eAAK,CAAA,CAAA;YACjB,MAAK;YACL,SAAS,MACP,WAAW,wBAAwB;cACjC,WAAW;YAAA,CACZ;YAEH,IAAG;YAEF,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB;UAAA;QAAA;MACH;IAAA;IAIL,CAAC,cACA,wBAAC,KAAK,eAAL,CAAA,CAAmB,QAEpB,wBAAC,KAAK,MAAL,EAAU,aAAW,WACpB,cAAC,yBAAA,QAAQ,SAAR,EACE,UAAA;MAAA,UAAU,SAAS,SAClB;QAAC;QAAA;UACC,aAAa,EAAE,SAAS,WAAW,UAAU;UAC7C;UACA;UACA,iBAAiB;UACjB,QAAQ;UACR,WAAW;QAAA;MAAA;MAGd,aAAa,UAAU,WAAW,QACjC;QAAC;QAAA;UACC,UAAM,wBAAC,cAAe,EAAA,OAAM,QAAQ,CAAA;UACpC,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,YACE;YAAC;YAAA;cACC,KAAK;cACL,SAAQ;cACR,eAAA,wBAAY,eAAK,CAAA,CAAA;cACjB,IAAG;cAEF,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB;YAAA;UAAA;QACH;MAAA,IAGF;MACH,CAAC,aAAa,UAAU,WAAW,QAClC;QAAC;QAAA;UACC,UAAM,wBAAC,cAAe,EAAA,OAAM,QAAQ,CAAA;UACpC,SAAS,cAAc;YACrB,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA;MAAA,IAED;IAAA,EAAA,CACN,EACF,CAAA;EAAA,EAEJ,CAAA;AAEJ;AAEO,IAAM,oBAAoB,MAAM;AACrC,QAAM,cAAc;IAClB,CAAC,UAAU;;AAAA,yBAAM,UAAU,YAAY,aAA5B,mBAAuC,cAAc;;EAAA;AAGlE,aAAA,wBACG,KAAK,SAAL,EAAa,aACZ,cAAA,wBAAC,UAAA,CAAA,CAAS,EACZ,CAAA;AAEJ;", "names": []}