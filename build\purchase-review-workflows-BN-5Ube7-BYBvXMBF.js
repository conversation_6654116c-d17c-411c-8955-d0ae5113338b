import{gn as s,m as e,L as t,aX as r,J as o,bM as i,bK as n,bT as l,bU as d}from"./strapi-YzJfjJ2z.js";const f=()=>{const{formatMessage:a}=s();return e.jsx(t.Root,{children:e.jsxs(r,{children:[e.jsx(t.<PERSON><PERSON>,{title:a({id:"Settings.review-workflows.list.page.title",defaultMessage:"Review Workflows"}),subtitle:a({id:"Settings.review-workflows.list.page.subtitle",defaultMessage:"Manage your content review process"})}),e.jsx(o,{paddingLeft:10,paddingRight:10,children:e.jsx(i,{icon:e.jsx(d,{width:"16rem"}),content:a({id:"Settings.review-workflows.not-available",defaultMessage:"Review Workflows is only available as part of a paid plan. Upgrade to create and manage workflows."}),action:e.jsx(n,{variant:"default",endIcon:e.jsx(l,{}),href:"https://strp.cc/3tdNfJq",isExternal:!0,target:"_blank",children:a({id:"global.learn-more",defaultMessage:"Learn more"})})})})]})})};export{f as PurchaseReviewWorkflows};
