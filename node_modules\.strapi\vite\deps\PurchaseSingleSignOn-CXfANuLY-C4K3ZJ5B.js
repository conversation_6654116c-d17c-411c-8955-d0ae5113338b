import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$H
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  EmptyStateLayout,
  LinkButton,
  Main,
  useIntl
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$3t
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/PurchaseSingleSignOn-CXfANuLY.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var PurchaseSingleSignOn = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsx)(Layouts.Root, { children: (0, import_jsx_runtime.jsxs)(Main, { children: [
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: "Settings.sso.title",
          defaultMessage: "Single Sign-On"
        }),
        subtitle: formatMessage({
          id: "Settings.sso.subTitle",
          defaultMessage: "Configure the settings for the Single Sign-On feature."
        })
      }
    ),
    (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 10, paddingRight: 10, children: (0, import_jsx_runtime.jsx)(
      EmptyStateLayout,
      {
        icon: (0, import_jsx_runtime.jsx)(ForwardRef$H, { width: "16rem" }),
        content: formatMessage({
          id: "Settings.sso.not-available",
          defaultMessage: "SSO is only available as part of a paid plan. Upgrade to configure additional sign-in & sign-up methods for your administration panel."
        }),
        action: (0, import_jsx_runtime.jsx)(
          LinkButton,
          {
            variant: "default",
            endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3t, {}),
            href: "https://strp.cc/46Fk1BA",
            isExternal: true,
            target: "_blank",
            children: formatMessage({
              id: "global.learn-more",
              defaultMessage: "Learn more"
            })
          }
        )
      }
    ) })
  ] }) });
};
export {
  PurchaseSingleSignOn
};
//# sourceMappingURL=PurchaseSingleSignOn-CXfANuLY-C4K3ZJ5B.js.map
