import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/sa-CHD-r_h-.mjs
var Analytics = "विश्लेषिकी";
var Documentation = "दस्तावेजीकरणम्";
var Email = "ईमेल";
var Password = "समाभाष्";
var Provider = "प्रदाता";
var ResetPasswordToken = "पासवर्ड टोकन रीसेट कुर्वन्तु ";
var Role = "भूमिका";
var Username = "उपयोक्तृनाम";
var Users = "उपयोक्तारः";
var anErrorOccurred = "वूप्स्! किमपि भ्रष्टं जातम्। कृपया, पुनः प्रयासं कुरुत।";
var clearLabel = "स्पष्ट करें";
var or = "अथवा";
var skipToContent = "सामग्री प्रति गच्छतु";
var submit = "सबमिट";
var sa = {
  Analytics,
  "Auth.components.Oops.text": "भवतः खाता स्थगितम् अस्ति",
  "Auth.components.Oops.text.admin": "यदि एषा त्रुटिः अस्ति तर्हि कृपया स्वप्रशासकेन सह सम्पर्कं कुर्वन्तु",
  "Auth.components.Oops.title": "ऊप्स्..",
  "Auth.form.button.forgot-password": "ईमेल प्रेषयन्तु",
  "Auth.form.button.go-home": "गृहं प्रति गच्छतु",
  "Auth.form.button.login": "लॉगिन इति",
  "Auth.form.button.login.providers.error": "चयनितप्रदातृद्वारा भवन्तं संयोजयितुं न शक्नुमः।",
  "Auth.form.button.login.strapi": "स्ट्रैपी मार्गेण लॉग इन कुर्वन्तु",
  "Auth.form.button.password-recovery": "पासवर्ड पुनर्प्राप्ति",
  "Auth.form.button.register": "आरभेम",
  "Auth.form.confirmPassword.label": "पुष्टिकरण गुप्तशब्द",
  "Auth.form.currentPassword.label": "वर्तमान गुप्तशब्द",
  "Auth.form.email.label": "ईमेल",
  "Auth.form.email.placeholder": "<EMAIL> इति",
  "Auth.form.error.blocked": "भवतः खाता प्रशासकेन अवरुद्धः अस्ति।",
  "Auth.form.error.code.provide": "अशुद्धः कोडः प्रदत्तः।",
  "Auth.form.error.confirmed": "भवतः खातेः ईमेलः सत्यापितः नास्ति।",
  "Auth.form.error.email.invalid": "एतत् ईमेल अमान्यम् अस्ति ।",
  "Auth.form.error.email.provide": "कृपया स्वस्य उपयोक्तृनाम अथवा स्वस्य ईमेलं प्रदातव्यम्।",
  "Auth.form.error.email.taken": "ईमेल पूर्वमेव गृहीतम् अस्ति।",
  "Auth.form.error.invalid": "अमान्य परिचयकर्ता अथवा गुप्तशब्द।",
  "Auth.form.error.params.provide": "अशुद्धानि मापदण्डानि प्रदत्तानि आसन्।",
  "Auth.form.error.password.format": "भवतः गुप्तशब्दे '$' चिह्नं त्रिवारं अधिकं भवितुं न शक्यते ।",
  "Auth.form.error.password.local": "अयं उपयोक्ता कदापि स्थानीयगुप्तशब्दं न सेट् कृतवान्, कृपया खातानिर्माणकाले उपयुज्यमानस्य प्रदातुः माध्यमेन प्रवेशं कुर्वन्तु ।",
  "Auth.form.error.password.matching": "गुप्तशब्दाः न मेलन्ति।",
  "Auth.form.error.password.provide": "कृपया स्वस्य कूटशब्दं प्रदातव्यम्।",
  "Auth.form.error.ratelimit": "अत्यधिकप्रयासाः, कृपया एकनिमेषेण पुनः प्रयासं कुर्वन्तु।",
  "Auth.form.error.user.not-exist": "इदं ईमेल नास्ति।",
  "Auth.form.error.username.taken": "उपयोक्तृनाम पूर्वमेव गृहीतम् अस्ति।",
  "Auth.form.firstname.label": "प्रथम नाम्ना",
  "Auth.form.firstname.placeholder": "उदा. कै",
  "Auth.form.forgot-password.email.label": "स्वस्य ईमेलं प्रविशतु",
  "Auth.form.forgot-password.email.label.success": "ईमेल सफलतया प्रेषितम्",
  "Auth.form.lastname.label": "अंतिम नाम्ना",
  "Auth.form.lastname.placeholder": "उदा. डोए",
  "Auth.form.password.hide-password": "गुप्तशब्दं गोपयतु",
  "Auth.form.password.hint": "न्यूनातिन्यूनं ८ वर्णाः, १ बृहत्, १ लघुः & १ संख्याः भवितुमर्हति",
  "Auth.form.password.show-password": "गुप्तशब्दं दर्शयतु",
  "Auth.form.register.news.label": "नूतनानां विशेषतानां & आगामिसुधारानाम् विषये मां अद्यतनं कुरुत (एतत् कृत्वा भवान् {उपधा} तथा {नीति} स्वीकुर्वति)।",
  "Auth.form.register.subtitle": "प्रमाणपत्राणि केवलं स्ट्रैपी मध्ये प्रमाणीकरणाय उपयुज्यन्ते। सर्वाणि रक्षितानि दत्तांशानि भवतः दत्तांशकोशे संगृहीतानि भविष्यन्ति।",
  "Auth.form.rememberMe.label": "मां स्मर्यताम्",
  "Auth.form.username.label": "उपयोक्तृनाम",
  "Auth.form.username.placeholder": "उदा. कै_दोए",
  "Auth.form.welcome.subtitle": "भवतः स्ट्रैपी  खाते प्रवेश कुर्वन्तु",
  "Auth.form.welcome.title": "स्ट्रैपी इत्यत्र स्वागतम् !",
  "Auth.link.forgot-password": "भवतः गुप्तशब्दं विस्मृतवान् वा?",
  "Auth.link.ready": "Rप्रवेशार्थं सज्जाः सन्ति वा?",
  "Auth.link.signin": "साइन इन इति",
  "Auth.link.signin.account": "पूर्वमेव खाता अस्ति वा ?",
  "Auth.login.sso.divider": "अथवा लॉगिन इति ",
  "Auth.login.sso.loading": "प्रदाता लोड हो रहे हैं...",
  "Auth.login.sso.subtitle": "SSO मार्गेण स्वखाते प्रवेशं कुर्वन्तु",
  "Auth.privacy-policy-agreement.policy": "गोपनीयता नीति",
  "Auth.privacy-policy-agreement.terms": "उपधा",
  "Auth.reset-password.title": "गुप्तशब्दं पुनः सेट् कुर्वन्तु",
  "Content Manager": "सामग्री प्रबन्धक:",
  "Content Type Builder": "सामग्री-प्रकार निर्माता",
  Documentation,
  Email,
  "Files Upload": "सञ्चिकाः अपलोड् कुर्वन्तु",
  "HomePage.head.title": "मुखपृष्ठ",
  "HomePage.roadmap": "अस्माकं मार्गचित्रं पश्यन्तु",
  "HomePage.welcome.congrats": "भिनन्दनम् !",
  "HomePage.welcome.congrats.content": "भवान् प्रथमप्रशासकरूपेण प्रवेशितः अस्ति । स्ट्रैपी इत्यनेन प्रदत्तानां शक्तिशालिनां विशेषतानां आविष्कारार्थं इति ।",
  "HomePage.welcome.congrats.content.bold": "वयं भवन्तं प्रथमं संग्रह-प्रकार रचयितुं अनुशंसयामः।",
  "Media Library": "मीडिया पुस्तकालय",
  "New entry": "नवीन प्रविष्टिः",
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  "Roles & Permissions": "भूमिकाएँ एवं अनुमतियाँ",
  "Roles.ListPage.notification.delete-all-not-allowed": "काश्चन भूमिकाः उपयोक्तृभिः सह सम्बद्धाः इति कारणतः लोपयितुं न शक्यते स्म",
  "Roles.ListPage.notification.delete-not-allowed": "उपयोक्तृभिः सह सम्बद्धा चेत् भूमिका विलोपयितुं न शक्यते",
  "Roles.RoleRow.select-all": "बल्क क्रियाणां कृते {name} चिनोतु",
  "Roles.RoleRow.user-count": "{संख्या, बहुवचनम्, =0 {# उपयोक्ता} एकः {# उपयोक्ता} अन्ये {# उपयोक्तारः}}",
  "Roles.components.List.empty.withSearch": "अन्वेषणस्य ({अन्वेषण}) अनुरूपं भूमिका नास्ति...",
  "Settings.PageTitle": "सेटिंग्स् - {नाम}",
  "Settings.apiTokens.addFirstToken": "स्वस्य प्रथमं एपिआइ टोकनं योजयन्तु",
  "Settings.apiTokens.addNewToken": "नव एपीआई टोकन जोड़ें",
  "Settings.tokens.copy.editMessage": "सुरक्षाकारणात्, भवान् केवलं एकवारं एव स्वस्य टोकनं द्रष्टुं शक्नोति।",
  "Settings.tokens.copy.editTitle": "इदं टोकनम् इतः परं सुलभं नास्ति।",
  "Settings.tokens.copy.lastWarning": "एतत् टोकनं प्रतिलिख्यताम् अवश्यं कुरुत, पुनः द्रष्टुं न शक्ष्यति!",
  "Settings.apiTokens.create": "नवीन एपिआइ टोकन रचयतु",
  "Settings.apiTokens.description": "एपिआइ उपभोगार्थं उत्पन्नटोकनस्य सूची",
  "Settings.apiTokens.emptyStateLayout": "भवतः अद्यापि किमपि सामग्री नास्ति...",
  "Settings.tokens.notification.copied": "टोकनः क्लिप्बोर्ड् मध्ये प्रतिलिपितः।",
  "Settings.apiTokens.title": "एपीआई टोकन",
  "Settings.tokens.types.full-access": "पूर्णाभिगमनम्",
  "Settings.tokens.types.read-only": "केवल-पठनीयम्",
  "Settings.application.description": "प्रशासनपटलस्य वैश्विकसूचना",
  "Settings.application.edition-title": "वर्तमान योजना",
  "Settings.application.get-help": "सहायतां प्राप्नुवन्तु",
  "Settings.application.link-pricing": "सर्वमूल्यनिर्धारणयोजनानि पश्यन्तु",
  "Settings.application.link-upgrade": "स्वप्रशासकपटलस्य उन्नयनम्",
  "Settings.application.node-version": "नोड संस्करणम्",
  "Settings.application.strapi-version": "स्ट्रैपी संस्करणम्",
  "Settings.application.strapiVersion": "स्ट्रैपीसंस्करण",
  "Settings.application.title": "अवलोकन",
  "Settings.error": "त्रुटि",
  "Settings.global": "वैश्विक सेटिंग्स्",
  "Settings.permissions": "प्रशासनपटल",
  "Settings.permissions.category": "{कोटी} कृते अनुमतिसेटिंग्स्",
  "Settings.permissions.category.plugins": "{कोटी} प्लगिन् कृते अनुमतिसेटिंग्स्",
  "Settings.permissions.conditions.anytime": "कदापि",
  "Settings.permissions.conditions.apply": "प्रयोजयन्तु",
  "Settings.permissions.conditions.can": "कर्तुं शक्नुवन्ति",
  "Settings.permissions.conditions.conditions": "शर्ताः परिभाषयन्तु",
  "Settings.permissions.conditions.links": "लिङ्क्स्",
  "Settings.permissions.conditions.no-actions": "भवतः प्रथमं क्रियाः (निर्माणं, पठनं, अद्यतनीकरणं, ...) चयनं कर्तव्यं तेषु शर्ताः परिभाषयितुं पूर्वं।",
  "Settings.permissions.conditions.none-selected": "कदापि",
  "Settings.permissions.conditions.or": "वा",
  "Settings.permissions.conditions.when": "कदा",
  "Settings.permissions.select-all-by-permission": "सर्व {लेबल} अनुमतिः चिनोतु",
  "Settings.permissions.select-by-permission": "{नामपत्र} अनुमतिं चिनोतु",
  "Settings.permissions.users.create": "नवप्रयोक्तारं आमन्त्रयतु",
  "Settings.permissions.users.email": "ईमेल",
  "Settings.permissions.users.firstname": "प्रथमनाम",
  "Settings.permissions.users.lastname": "अन्तिनाम",
  "Settings.permissions.users.form.sso": "SSO इत्यनेन सह संयोजयन्तु",
  "Settings.permissions.users.form.sso.description": "यदा सक्षम (ON) भवति तदा उपयोक्तारः SSO मार्गेण प्रवेशं कर्तुं शक्नुवन्ति",
  "Settings.permissions.users.listview.header.subtitle": "सर्वप्रयोक्तारः येषां Strapi व्यवस्थापकपटले प्रवेशः अस्ति",
  "Settings.permissions.users.tabs.label": "टैब्स् अनुमतिः",
  "Settings.profile.form.notify.data.loaded": "भवतः प्रोफाइल-दत्तांशः लोड् कृतः",
  "Settings.profile.form.section.experience.clear.select": "चयनित-अन्तरफलक-भाषां स्वच्छं कुरुत",
  "Settings.profile.form.section.experience.here": "अत्र",
  "Settings.profile.form.section.experience.interfaceLanguage": "अन्तरफलकभाषा",
  "Settings.profile.form.section.experience.interfaceLanguage.hint": "एतत् केवलं चयनितभाषायां भवतः स्वकीयं अन्तरफलकं प्रदर्शयिष्यति।",
  "Settings.profile.form.section.experience.interfaceLanguageHelp": "प्राथमिकतापरिवर्तनानि केवलं भवतः कृते एव प्रवर्तन्ते। अधिका सूचना {अत्र} उपलभ्यते।",
  "Settings.profile.form.section.experience.mode.label": "अन्तरफलक मोड",
  "Settings.profile.form.section.experience.mode.hint": "चयनितविधाने भवतः अन्तरफलकं प्रदर्शयति।",
  "Settings.profile.form.section.experience.mode.option-label": "{नाम} मोड",
  "Settings.profile.form.section.experience.title": "अनुभव",
  "Settings.profile.form.section.head.title": "उपयोक्तृप्रोफाइल",
  "Settings.profile.form.section.profile.page.title": "प्रोफाइल पृष्ठ",
  "Settings.roles.create.description": "भूमिकायां दत्तान् अधिकारान् परिभाषयतु",
  "Settings.roles.create.title": "भूमिका रचयतु",
  "Settings.roles.created": "भूमिका निर्मितम्",
  "Settings.roles.edit.title": "एकां भूमिकां सम्पादयतु",
  "Settings.roles.form.button.users-with-role": "{संख्या, बहुवचनम्, =0 {# उपयोक्तारः} एकः {# उपयोक्तारः} अन्ये {# उपयोक्तारः}} एतया भूमिकायाः सह",
  "Settings.roles.form.created": "निर्मितम्",
  "Settings.roles.form.description": "भूमिकायाः नाम वर्णनं च",
  "Settings.roles.form.permission.property-label": "{लेबल} अनुमतिः",
  "Settings.roles.form.permissions.attributesPermissions": "क्षेत्राणि अनुमतिः",
  "Settings.roles.form.permissions.create": "रचयतु",
  "Settings.roles.form.permissions.delete": "लुप्",
  "Settings.roles.form.permissions.publish": "प्रकाशनम्",
  "Settings.roles.form.permissions.read": "पठन्तु",
  "Settings.roles.form.permissions.update": "अद्यतन",
  "Settings.roles.list.button.add": "नवीन भूमिका योजयन्तु",
  "Settings.roles.list.description": "भूमिकानां सूची",
  "Settings.roles.title.singular": "भूमिका",
  "Settings.sso.description": "एकल-प्रवेश-विशेषतायाः सेटिङ्ग्स् विन्यस्यताम्।",
  "Settings.sso.form.defaultRole.description": "इदं चयनितभूमिकायां नूतनं प्रमाणीकृतं उपयोक्तारं संलग्नं करिष्यति",
  "Settings.sso.form.defaultRole.description-not-allowed": "प्रशासकभूमिकाः पठितुं भवतः अनुमतिः आवश्यकी अस्ति",
  "Settings.sso.form.defaultRole.label": "पूर्वनिर्धारित भूमिका",
  "Settings.sso.form.registration.description": "यदि खाता नास्ति तर्हि SSO प्रवेशे नूतनं उपयोक्तारं रचयतु",
  "Settings.sso.form.registration.label": "स्वयं-पञ्जीकरणम्",
  "Settings.sso.title": "एकल-साइन-ऑन",
  "Settings.webhooks.create": "जालपुटं रचयतु",
  "Settings.webhooks.create.header": "नवीनशीर्षकं रचयतु",
  "Settings.webhooks.created": "जालपुटं निर्मितम्",
  "Settings.webhooks.event.publish-tooltip": "एषा घटना केवलं मसौदा/प्रकाशनप्रणाली सक्षमीकृतसामग्रीणां कृते विद्यते",
  "Settings.webhooks.events.create": "रचयतु",
  "Settings.webhooks.events.update": "अद्यतन",
  "Settings.webhooks.form.events": "इवेण्ट्स्",
  "Settings.webhooks.form.headers": "शीर्षकाणि",
  "Settings.webhooks.form.url": "यूआरएल",
  "Settings.webhooks.headers.remove": "शीर्षकपङ्क्तिं {संख्या} निष्कासयतु",
  "Settings.webhooks.key": "की",
  "Settings.webhooks.list.button.add": "नवीन जालपुटं रचयतु",
  "Settings.webhooks.list.description": "POST परिवर्तनसूचनाः प्राप्नुवन्तु",
  "Settings.webhooks.list.empty.description": "कोऽपि जालपुटाः न प्राप्ताः",
  "Settings.webhooks.list.empty.link": "अस्माकं दस्तावेजीकरणं पश्यन्तु",
  "Settings.webhooks.list.empty.title": "अद्यापि कोऽपि जालपुटः नास्ति",
  "Settings.webhooks.list.th.actions": "क्रियाः",
  "Settings.webhooks.list.th.status": "स्थितिः",
  "Settings.webhooks.singular": "वेबहूक",
  "Settings.webhooks.title": "वेबहूक्स",
  "Settings.webhooks.to.delete": "बहुवचनम्, एकं {# सम्पत्ति} अन्ये {# सम्पत्तिः}} चयनितम्",
  "Settings.webhooks.trigger": "ट्रिगर",
  "Settings.webhooks.trigger.cancel": "ट्रिगर रद्द करें",
  "Settings.webhooks.trigger.pending": "लंबित...",
  "Settings.webhooks.trigger.save": "कृपया ट्रिगर कृते रक्षतु",
  "Settings.webhooks.trigger.success": "सफलता!",
  "Settings.webhooks.trigger.success.label": "ट्रिगर सफलः अभवत्",
  "Settings.webhooks.trigger.test": "परीक्षण-ट्रिगर",
  "Settings.webhooks.trigger.title": "ट्रिगरात् पूर्वं रक्षतु",
  "Settings.webhooks.value": "मूल्यम्",
  "Usecase.back-end": "पृष्ठ-अन्तविकासकः",
  "Usecase.button.skip": "एतत् प्रश्नं त्यजतु",
  "Usecase.content-creator": "सामग्री निर्माता",
  "Usecase.front-end": "अग्र-अन्त-विकासकः",
  "Usecase.full-stack": "पूर्ण-स्टैक विकासक",
  "Usecase.input.work-type": "भवन्तः किं प्रकारस्य कार्यं कुर्वन्ति?",
  "Usecase.notification.success.project-created": "परियोजना सफलतया निर्मितवती",
  "Usecase.other": "अन्य",
  "Usecase.title": "स्वविषये किञ्चित् अधिकं वदतु",
  Username,
  Users,
  "Users & Permissions": "उपयोक्तारः अनुमतिः च",
  "Users.components.List.empty": "प्रयोक्तारः नास्ति...",
  "Users.components.List.empty.withFilters": "प्रयुक्तैः फ़िल्टरैः सह उपयोक्तारः नास्ति...",
  "Users.components.List.empty.withSearch": "अन्वेषणस्य ({search}) अनुरूपाः उपयोक्तारः नास्ति...",
  "admin.pages.MarketPlacePage.head": "बाजारस्थानम् - प्लगिन्",
  "admin.pages.MarketPlacePage.offline.title": "भवन्तः अफलाइनाः सन्ति",
  "admin.pages.MarketPlacePage.offline.subtitle": "Strapi Market - मध्ये प्रवेशार्थं भवान् अन्तर्जालसङ्गणकेन सह सम्बद्धः भवितुम् अर्हति ।",
  "admin.pages.MarketPlacePage.plugin.copy": "संस्थापन आदेशं प्रतिलिख्यताम्",
  "admin.pages.MarketPlacePage.plugin.copy.success": "स्वस्य टर्मिनले चिनोतुम् सज्जं आदेशं संस्थापयतु",
  "admin.pages.MarketPlacePage.plugin.info": "अधिकं ज्ञातुं",
  "admin.pages.MarketPlacePage.plugin.info.label": "{pluginName} विषये अधिकं ज्ञातुं",
  "admin.pages.MarketPlacePage.plugin.info.text": "अधिकं ज्ञातुं",
  "admin.pages.MarketPlacePage.plugin.installed": "स्थापितं",
  "admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi": "स्ट्रैपी द्वारा निर्मित",
  "admin.pages.MarketPlacePage.plugin.tooltip.verified": "प्लगिन् स्ट्रैपी द्वारा सत्यापित",
  "admin.pages.MarketPlacePage.search.clear": "प्लगइन अन्वेषणं स्वच्छं कुरुत",
  "admin.pages.MarketPlacePage.search.empty": '"{लक्ष्य}" कृते कोऽपि परिणामः नास्ति',
  "admin.pages.MarketPlacePage.search.placeholder": "प्लगिन् अन्वेष्टुम्",
  "admin.pages.MarketPlacePage.submit.plugin.link": "स्वस्य प्लगइनं प्रस्तौतु",
  "admin.pages.MarketPlacePage.subtitle": "स्ट्रैपी इत्यस्मात् अधिकं प्राप्तुम्",
  "admin.pages.MarketPlacePage.missingPlugin.title": "प्लगइनं गम्यते?",
  "admin.pages.MarketPlacePage.missingPlugin.description": "अस्मान् कथयतु यत् भवान् किं प्लगिन् अन्विष्यति तथा च वयं अस्माकं समुदायस्य प्लगिन् विकासकान् ज्ञापयिष्यामः यद्यपि ते प्रेरणायाः अन्वेषणं कुर्वन्ति!",
  anErrorOccurred,
  "app.component.CopyToClipboard.label": "क्लिपबोर्ड मध्ये प्रतिलिपिं कुर्वन्तु",
  "app.component.search.label": "{लक्ष्य} इति अन्वेषणं कुर्वन्तु",
  "app.component.table.duplicate": "{लक्ष्य} डुप्लिकेट करें",
  "app.component.table.edit": "{लक्ष्य} सम्पादयतु",
  "app.component.table.select.one-entry": "{लक्ष्य} चयनं कुर्वन्तु",
  "app.components.BlockLink.blog": "ब्लॉग",
  "app.components.BlockLink.blog.content": "स्ट्रैपी तथा पारिस्थितिकीतन्त्रस्य विषये नवीनतमवार्ताः पठन्तु।",
  "app.components.BlockLink.code": "कोड उदाहरणानि",
  "app.components.BlockLink.code.content": "समुदायस्य विकासेन वास्तविकपरियोजनानां परीक्षणेन शिक्षन्तु।",
  "app.components.BlockLink.documentation.content": "अत्यावश्यकसंकल्पनाः, मार्गदर्शिकाः, निर्देशाः च अन्वेषयन्तु।",
  "app.components.BlockLink.tutorial": "पाठ्यक्रम",
  "app.components.BlockLink.tutorial.content": "Strapi इत्यस्य उपयोगाय अनुकूलितुं च चरण-दर-चरण-निर्देशानां अनुसरणं कुर्वन्तु।",
  "app.components.Button.cancel": "रद्द करें",
  "app.components.Button.confirm": "पुष्टि करें",
  "app.components.Button.reset": "पुनर्स्थापनम्",
  "app.components.ComingSoonPage.comingSoon": "शीघ्रमेव आगमिष्यति",
  "app.components.ConfirmDialog.title": "पुष्टिकरणम्",
  "app.components.DownloadInfo.download": "अवलोकनं प्रचलति...",
  "app.components.DownloadInfo.text": "एतत् एकं निमेषं यावत् समयं गृह्णीयात्। भवतः धैर्यस्य कृते धन्यवादः।",
  "app.components.EmptyAttributes.title": "अद्यापि क्षेत्राणि नास्ति",
  "app.components.EmptyStateLayout.content-document": "कोऽपि सामग्री न प्राप्ता",
  "app.components.EmptyStateLayout.content-permissions": "तत् सामग्रीं प्राप्तुं भवतः अनुमतिः नास्ति",
  "app.components.GuidedTour.CM.create.content": "<p>अत्र सामग्रीप्रबन्धके सर्वाणि सामग्रीनि रचयन्तु प्रबन्धयन्तु च।</p><p>उदाहरणम्: ब्लॉग् वेबसाइट् उदाहरणम् अग्रे गृहीत्वा, कश्चन लेखितुं शक्नोति लेखं, यथा रोचते तथा रक्षित्वा प्रकाशयन्तु।</p><p>💡 त्वरितसूचना - भवता निर्मितसामग्रीयां प्रकाशनं मारयितुं न विस्मरन्तु।</p>",
  "app.components.GuidedTour.CM.create.title": "⚡️ सामग्री बनाएँ",
  "app.components.GuidedTour.CM.success.content": "<p>भयानकं, एकं अन्तिमं सोपानं गन्तव्यम्!</p><b>🚀 कार्ये सामग्रीं पश्यन्तु</b>",
  "app.components.GuidedTour.CM.success.cta.title": "एपीआई परीक्षणं कुर्वन्तु",
  "app.components.GuidedTour.CM.success.title": "चरण 2: सम्पन्न ✅",
  "app.components.GuidedTour.CTB.create.content": "<p>संग्रहप्रकाराः भवन्तं अनेकप्रविष्टीनां प्रबन्धने सहायकाः भवन्ति, एकप्रकाराः केवलं एकस्य प्रविष्टिप्रबन्धनाय उपयुक्ताः भवन्ति ।</p> <p>उदाहरणम्: ब्लॉगजालस्थलस्य कृते, लेखाः संग्रहप्रकारः स्यात् यदा तु मुखपृष्ठं एकलप्रकारः स्यात् ।</p>",
  "app.components.GuidedTour.CTB.create.cta.title": "एकं संग्रहप्रकारं निर्मायताम्",
  "app.components.GuidedTour.CTB.create.title": "🧠 प्रथमं संग्रहप्रकारं रचयतु",
  "app.components.GuidedTour.CTB.success.content": "<p>सुप्रचलति!</p><b>⚡️ भवान् जगति किं साझां कर्तुम् इच्छति?</b>",
  "app.components.GuidedTour.CTB.success.title": "चरणम् 1: सम्पन्नम् ✅",
  "app.components.GuidedTour.apiTokens.create.content": "<p>अत्र प्रमाणीकरणचिह्नं जनयतु तथा च भवता अधुना निर्मितां सामग्रीं पुनः प्राप्तुम्।</p>",
  "app.components.GuidedTour.apiTokens.create.cta.title": "एपीआई टोकन उत्पन्न करें",
  "app.components.GuidedTour.apiTokens.create.title": "🚀 सामग्रीं क्रियायां पश्यन्तु",
  "app.components.GuidedTour.apiTokens.success.content": "<p>HTTP अनुरोधं कृत्वा कार्ये सामग्रीं पश्यन्तु:</p><ul><li><p>अस्मिन् URL प्रति: <light>https: //'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>शीर्षक के साथ: <light>अधिकार: वाहक '<'. YOUR_API_TOKEN'>'</light></p></li></ul><p>सामग्रीभिः सह अन्तरक्रियायाः अधिकमार्गाणां कृते <documentationLink>documentation</documentationLink> पश्यन्तु ।</p>",
  "app.components.GuidedTour.apiTokens.success.cta.title": "मुखपृष्ठं प्रति गच्छतु",
  "app.components.GuidedTour.apiTokens.success.title": "चरण 3: सम्पन्न ✅",
  "app.components.GuidedTour.create-content": "सामग्री रचयतु",
  "app.components.GuidedTour.home.CM.title": "⚡️ भवान् विश्वेन सह किं साझां कर्तुम् इच्छति?",
  "app.components.GuidedTour.home.CTB.cta.title": "सामग्री प्रकार निर्माता पर जाएँ",
  "app.components.GuidedTour.home.CTB.title": "🧠 सामग्री संरचना का निर्माण",
  "app.components.GuidedTour.home.apiTokens.cta.title": "एपीआई परीक्षणं कुर्वन्तु",
  "app.components.GuidedTour.skip": "भ्रमणं त्यजतु",
  "app.components.GuidedTour.title": "आरम्भार्थं ३ चरणाः",
  "app.components.HomePage.button.blog": "ब्लॉग् मध्ये अधिकं पश्यन्तु",
  "app.components.HomePage.community": "समुदाये सम्मिलितं भवतु",
  "app.components.HomePage.community.content": "विभिन्न-चैनेल्-मध्ये दलस्य सदस्यैः, योगदातृभिः, विकासकैः च सह चर्चां कुर्वन्तु।",
  "app.components.HomePage.create": "स्वस्य प्रथमं सामग्रीप्रकारं रचयतु",
  "app.components.HomePage.roadmap": "अस्माकं मार्गचित्रं पश्यन्तु",
  "app.components.HomePage.welcome": "जहाज पर स्वागतम् 👋",
  "app.components.HomePage.welcome.again": "स्वागतम् 👋",
  "app.components.HomePage.welcomeBlock.content": "अभिनन्दनम्! भवान् प्रथमप्रशासकरूपेण लॉग् कृतः अस्ति। Strapi द्वारा प्रदत्तानां शक्तिशालिनां विशेषतानां आविष्कारार्थं, वयं भवतां प्रथमं सामग्रीप्रकारं निर्मातुं अनुशंसयामः!",
  "app.components.HomePage.welcomeBlock.content.again": "अस्माकं आशास्ति यत् भवान् स्वस्य परियोजनायां प्रगतिम् करोति! Strapi विषये नवीनतमवार्ताः पठितुं निःशङ्कं भवन्तु। भवतः प्रतिक्रियायाः आधारेण उत्पादस्य सुधारार्थं वयं सर्वोत्तमं ददामः।",
  "app.components.HomePage.welcomeBlock.content.issues": "मुद्दे।",
  "app.components.HomePage.welcomeBlock.content.raise": " अथवा ",
  "app.components.ImgPreview.hint": "स्वसञ्चिकां अस्मिन् क्षेत्रे कर्षयतु & पातयतु अथवा अपलोड् कर्तुं सञ्चिकायाः ​​कृते {browse} कुर्वन्तु",
  "app.components.ImgPreview.hint.browse": "ब्राउज़ करें",
  "app.components.InputFile.newFile": "नवीनसञ्चिकां योजयन्तु",
  "app.components.InputFileDetails.open": "नवीन ट्याब् मध्ये उद्घाट्यताम्",
  "app.components.InputFileDetails.originalName": "मूल नाम:",
  "app.components.InputFileDetails.remove": "एताम् सञ्चिकां निष्कासयतु",
  "app.components.InputFileDetails.size": "आकार:",
  "app.components.InstallPluginPage.Download.description": "प्लगिन् डाउनलोड् कृत्वा संस्थापयितुं कतिचन सेकेण्ड् यावत् समयः भवितुं शक्नोति।",
  "app.components.InstallPluginPage.Download.title": "अवलोकनं भवति...",
  "app.components.InstallPluginPage.description": "अप्रयत्नेन स्वस्य एप्लिकेशनं विस्तारयतु।",
  "app.components.LeftMenu.collapse": "नवपट्टिकां संकुचयतु",
  "app.components.LeftMenu.expand": "नवपट्टिकां विस्तारयतु",
  "app.components.LeftMenu.logout": "लॉगआउट",
  "app.components.LeftMenu.navbrand.title": "स्ट्रैपी डैशबोर्ड",
  "app.components.LeftMenu.navbrand.workplace": "कार्यस्थानम्",
  "app.components.LeftMenuFooter.help": "सहायता",
  "app.components.LeftMenuFooter.poweredBy": "द्वारा संचालितम् ",
  "app.components.LeftMenuLinkContainer.collectionTypes": "संग्रह प्रकार",
  "app.components.LeftMenuLinkContainer.configuration": "विन्यासाः",
  "app.components.LeftMenuLinkContainer.general": "सामान्य",
  "app.components.LeftMenuLinkContainer.noPluginsInstalled": "अद्यापि कोऽपि प्लगिन् संस्थापितः नास्ति",
  "app.components.LeftMenuLinkContainer.plugins": "प्लगिन्स्",
  "app.components.LeftMenuLinkContainer.singleTypes": "एकल प्रकार",
  "app.components.ListPluginsPage.deletePlugin.description": "प्लगिन् विस्थापयितुं कतिचन सेकेण्ड् यावत् समयः भवितुं शक्नोति।",
  "app.components.ListPluginsPage.deletePlugin.title": "अस्थापनम्",
  "app.components.ListPluginsPage.description": "प्रकल्पे संस्थापितानां प्लगिन्स् सूची।",
  "app.components.ListPluginsPage.head.title": "प्लगिन्स् सूचीकरणम्",
  "app.components.Logout.logout": "लॉगआउट",
  "app.components.Logout.profile": "प्रोफाइल",
  "app.components.MarketplaceBanner": "समुदायेन निर्मिताः प्लगिन्स्, अपि च भवतः परियोजनायाः किकस्टार्ट् कर्तुं अनेकानि भयानकवस्तूनि, Strapi Awesome इत्यत्र अन्वेष्यताम्।",
  "app.components.MarketplaceBanner.image.alt": "एक स्ट्रैपी रॉकेट लोगो",
  "app.components.MarketplaceBanner.link": "अधुना तत् पश्यन्तु",
  "app.components.NotFoundPage.back": "मुखपृष्ठं प्रति गच्छतु",
  "app.components.NotFoundPage.description": "न प्राप्तम्",
  "app.components.Official": "आधिकारिक",
  "app.components.Onboarding.help.button": "सहायता बटन",
  "app.components.Onboarding.label.completed": "% पूर्णम्",
  "app.components.Onboarding.title": "प्रारम्भं कुर्वन्तु वीडियो",
  "app.components.PluginCard.Button.label.download": "अवलोकन",
  "app.components.PluginCard.Button.label.install": "पूर्वमेव संस्थापितम्",
  "app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed": "autoReload सुविधां सक्षमं कर्तुं आवश्यकम्। कृपया `yarn develop` इत्यनेन स्वस्य एप् आरभत।",
  "app.components.PluginCard.PopUpWarning.install.impossible.confirm": "अहं अवगच्छामि!",
  "app.components.PluginCard.PopUpWarning.install.impossible.environment": "सुरक्षाकारणात्, प्लगिन् केवलं विकासवातावरणे एव डाउनलोड् कर्तुं शक्यते।",
  "app.components.PluginCard.PopUpWarning.install.impossible.title": "अवलोकनं असम्भवम्",
  "app.components.PluginCard.compatible": "भवतः एप्लिकेशनेन सह संगतम्",
  "app.components.PluginCard.compatibleCommunity": "समुदायेन सह संगतम्",
  "app.components.PluginCard.more-details": "अधिकविवरणम्",
  "app.components.ToggleCheckbox.off-label": "मिथ्या",
  "app.components.ToggleCheckbox.on-label": "सत्यम्",
  "app.components.Users.MagicLink.connect": "अस्य उपयोक्त्रे प्रवेशं दातुं एतत् लिङ्क् प्रतिलिख्य साझां कुर्वन्तु",
  "app.components.Users.MagicLink.connect.sso": "इदं लिङ्कं उपयोक्त्रे प्रेषयन्तु, प्रथमं प्रवेशं SSO प्रदातृद्वारा कर्तुं शक्यते",
  "app.components.Users.ModalCreateBody.block-title.details": "उपयोक्तृविवरणम्",
  "app.components.Users.ModalCreateBody.block-title.roles": "उपयोक्तृ भूमिकाः",
  "app.components.Users.ModalCreateBody.block-title.roles.description": "एकस्य उपयोक्तुः एकं वा अनेकं वा भूमिकां भवितुम् अर्हति",
  "app.components.Users.SortPicker.button-label": "द्वारा क्रमबद्ध करें",
  "app.components.Users.SortPicker.sortby.email_asc": "ईमेल (ए तः जेड)",
  "app.components.Users.SortPicker.sortby.email_desc": "ईमेल (Z to A)",
  "app.components.Users.SortPicker.sortby.firstname_asc": "प्रथम नाम (ए से जेड)",
  "app.components.Users.SortPicker.sortby.firstname_desc": "प्रथम नाम (Z to A)",
  "app.components.Users.SortPicker.sortby.lastname_asc": "अंतिम नाम (ए से जेड)",
  "app.components.Users.SortPicker.sortby.lastname_desc": "अन्तिमनाम (Z to A)",
  "app.components.Users.SortPicker.sortby.username_asc": "उपयोक्तृनाम (ए से जेड)",
  "app.components.Users.SortPicker.sortby.username_desc": "उपयोक्तृनाम (Z to A)",
  "app.components.listPlugins.button": "नवीन प्लगइन जोड़ें",
  "app.components.listPlugins.title.none": "कोऽपि प्लगिन्स् संस्थापिताः न सन्ति",
  "app.components.listPluginsPage.deletePlugin.error": "प्लगिन् विस्थापयति समये त्रुटिः अभवत्",
  "app.containers.App.notification.error.init": "एपिआइ अनुरोधं कुर्वन् त्रुटिः अभवत्",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin": "यदि भवान् एतत् लिङ्क् न प्राप्नोति तर्हि कृपया स्वप्रशासकेन सह सम्पर्कं कुर्वन्तु।",
  "app.containers.AuthPage.ForgotPasswordSuccess.text.email": "भवतः गुप्तशब्दपुनर्प्राप्तिलिङ्क् प्राप्तुं कतिपयानि निमेषाणि यावत् समयः भवितुं शक्नोति।",
  "app.containers.AuthPage.ForgotPasswordSuccess.title": "ईमेल प्रेषितम्",
  "app.containers.Users.EditPage.form.active.label": "सक्रियम्",
  "app.containers.Users.EditPage.header.label": "{नाम} सम्पादयतु",
  "app.containers.Users.EditPage.header.label-loading": "उपयोक्तारं सम्पादयतु",
  "app.containers.Users.EditPage.roles-bloc-title": "विशेषित भूमिकाएँ",
  "app.containers.Users.ModalForm.footer.button-success": "उपयोक्तारं आमन्त्रयतु",
  "app.links.configure-view": "दृश्यं विन्यस्यताम्",
  "app.page.not.found": "अफ! भवन्तः यत् पृष्ठं भ्रमन्ति तत् वयं न प्राप्नुमः इव दृश्यन्ते...",
  "app.static.links.cheasheet": "चीटशीट",
  "app.utils.SelectOption.defaultMessage": " ",
  "app.utils.add-filter": "फ़िल्टर जोड़ें",
  "app.utils.close-label": "बन्द करें",
  "app.utils.defaultMessage": " ",
  "app.utils.duplicate": "डुप्लिकेट",
  "app.utils.edit": "सम्पादन",
  "app.utils.errors.file-too-big.message": "सञ्चिका अतीव विशाला अस्ति",
  "app.utils.filter-value": "फ़िल्टर मान",
  "app.utils.filters": "फ़िल्टर",
  "app.utils.notify.data-loaded": "{लक्ष्य} लोड् कृतम्",
  "app.utils.placeholder.defaultMessage": " ",
  "app.utils.publish": "प्रकाशित करें",
  "app.utils.select-all": "सर्वं चयनं कुरुत",
  "app.utils.select-field": "क्षेत्रं चयनं कुर्वन्तु",
  "app.utils.select-filter": "फ़िल्टर चयन करें",
  "app.utils.unpublish": "अप्रकाशित करें",
  clearLabel,
  "coming.soon": "इयं सामग्री सम्प्रति निर्माणाधीना अस्ति, कतिपयेषु सप्ताहेषु पुनः आगमिष्यति!",
  "component.Input.error.validation.integer": "मूल्यं पूर्णाङ्कं भवितुमर्हति",
  "components.AutoReloadBlocker.description": "निम्नलिखित आदेशेषु एकेन सह Strapi चालयन्तु:",
  "components.AutoReloadBlocker.header": "अस्य प्लगिन् कृते पुनः लोड्-विशेषता आवश्यकी अस्ति।",
  "components.ErrorBoundary.title": "किञ्चित् त्रुटिः अभवत्...",
  "components.FilterOptions.FILTER_TYPES.$contains": "शामिल है",
  "components.FilterOptions.FILTER_TYPES.$containsi": "शामिल है (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$endsWith": "सहितं समाप्तं भवति",
  "components.FilterOptions.FILTER_TYPES.$endsWithi": "सहितं समाप्तं भवति (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$eq": "हैं",
  "components.FilterOptions.FILTER_TYPES.$eqi": "हैं (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$gt": "अपेक्षया अधिकम् अस्ति",
  "components.FilterOptions.FILTER_TYPES.$gte": "अधिकं वा समानं वा अस्ति",
  "components.FilterOptions.FILTER_TYPES.$lt": "अपेक्षया न्यूनम् अस्ति",
  "components.FilterOptions.FILTER_TYPES.$lte": "अपेक्षया न्यूनं वा समानं वा अस्ति",
  "components.FilterOptions.FILTER_TYPES.$ne": "न भवति",
  "components.FilterOptions.FILTER_TYPES.$nei": "न भवति (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notContains": "नास्ति",
  "components.FilterOptions.FILTER_TYPES.$notContainsi": "नास्ति (case insensitive)",
  "components.FilterOptions.FILTER_TYPES.$notNull": "शून्यं नास्ति",
  "components.FilterOptions.FILTER_TYPES.$null": "शून्य है",
  "components.FilterOptions.FILTER_TYPES.$startsWith": "सहितं आरभ्यते",
  "components.FilterOptions.FILTER_TYPES.$startsWithi": "सहितं आरभ्यते (case insensitive)",
  "components.Input.error.attribute.key.taken": "एतत् मूल्यं पूर्वमेव अस्ति",
  "components.Input.error.attribute.sameKeyAndName": "समानं न भवितुम् अर्हति",
  "components.Input.error.attribute.taken": "एतत् क्षेत्रनाम पूर्वमेव अस्ति",
  "components.Input.error.contain.lowercase": "गुप्तशब्दे न्यूनातिन्यूनम् एकं लघुवर्णं भवितुमर्हति",
  "components.Input.error.contain.number": "गुप्तशब्दे न्यूनातिन्यूनम् एकः संख्या भवितुमर्हति",
  "components.Input.error.contain.uppercase": "गुप्तशब्दे न्यूनातिन्यूनम् एकं दीर्घवर्णं भवितुमर्हति",
  "components.Input.error.contentTypeName.taken": "एतत् नाम पूर्वमेव अस्ति",
  "components.Input.error.custom-error": "{त्रुटिसंदेश} ",
  "components.Input.error.password.noMatch": "गुप्तशब्दाः न मेलन्ति",
  "components.Input.error.validation.email": "एषः अमान्यः ईमेलः अस्ति",
  "components.Input.error.validation.json": "एतत् JSON प्रारूपेण सह न मेलति",
  "components.Input.error.validation.lowercase": "मूल्यं लघुवर्णीयं स्ट्रिंग् भवितुमर्हति",
  "components.Input.error.validation.max": "मूल्यं बहु उच्चम् अस्ति। {max}",
  "components.Input.error.validation.maxLength": "मूल्यं बहु दीर्घम् अस्ति। {max}",
  "components.Input.error.validation.min": "मूल्यं बहु न्यूनम् अस्ति। {min}",
  "components.Input.error.validation.minLength": "मूल्यम् अतीव लघु अस्ति। {min}",
  "components.Input.error.validation.minSupMax": "श्रेष्ठं न भवितुम् अर्हति",
  "components.Input.error.validation.regex": "मूल्यं regex इत्यनेन सह न मेलति।",
  "components.Input.error.validation.required": "एतत् मूल्यम् आवश्यकम्।",
  "components.Input.error.validation.unique": "एतत् मूल्यं पूर्वमेव उपयुज्यते।",
  "components.InputSelect.option.placeholder": "अत्र चिनोतु",
  "components.ListRow.empty": "प्रदर्शनीयः कोऽपि दत्तांशः नास्ति।",
  "components.NotAllowedInput.text": "एतत् क्षेत्रं द्रष्टुं कोऽपि अनुमतिः नास्ति",
  "components.OverlayBlocker.description": "भवन्तः एकं विशेषतां उपयुञ्जते यस्य सर्वरस्य पुनः आरम्भस्य आवश्यकता अस्ति। कृपया सर्वरस्य उपरि यावत् प्रतीक्ष्यताम्।",
  "components.OverlayBlocker.description.serverError": "सर्वरः पुनः आरम्भः भवितुम् अर्हति स्म, कृपया टर्मिनल् मध्ये स्वस्य लॉग्स् परीक्ष्यताम्।",
  "components.OverlayBlocker.title": "पुनः आरम्भस्य प्रतीक्षा अस्ति...",
  "components.OverlayBlocker.title.serverError": "पुनः आरम्भः अपेक्षितापेक्षया अधिकं समयं गृह्णाति",
  "components.PageFooter.select": "प्रति पृष्ठ प्रविष्टियाँ",
  "components.ProductionBlocker.description": "सुरक्षाप्रयोजनार्थं अस्माभिः अन्येषु वातावरणेषु एतत् प्लगिन् निष्क्रियं कर्तव्यम्।",
  "components.ProductionBlocker.header": "इदं प्लगिन् केवलं विकासे एव उपलभ्यते।",
  "components.Search.placeholder": "अन्वेषणं...",
  "components.TableHeader.sort": "{लेबल} पर क्रमबद्ध करें",
  "components.Wysiwyg.ToggleMode.markdown-mode": "मार्कडाउन मोड",
  "components.Wysiwyg.ToggleMode.preview-mode": "पूर्वावलोकन मोड",
  "components.Wysiwyg.collapse": "संकुचितम्",
  "components.Wysiwyg.selectOptions.H1": "शीर्षक H1",
  "components.Wysiwyg.selectOptions.H2": "शीर्षक H2",
  "components.Wysiwyg.selectOptions.H3": "शीर्षक H3",
  "components.Wysiwyg.selectOptions.H4": "शीर्षक H4",
  "components.Wysiwyg.selectOptions.H5": "शीर्षक H5",
  "components.Wysiwyg.selectOptions.H6": "शीर्षक H6",
  "components.Wysiwyg.selectOptions.title": "एकं शीर्षकं योजयन्तु",
  "components.WysiwygBottomControls.charactersIndicators": "वर्ण",
  "components.WysiwygBottomControls.fullscreen": "विस्तार",
  "components.WysiwygBottomControls.uploadFiles": "सञ्चिकाः कर्षयतु & पातयतु, क्लिप्बोर्डतः चिनोतु अथवा {ब्राउज्} करोतु।",
  "components.WysiwygBottomControls.uploadFiles.browse": "तेषां चयनं कुर्वन्तु",
  "components.pagination.go-to": "पृष्ठं {पृष्ठं} प्रति गच्छतु",
  "components.pagination.go-to-next": "अग्रे पृष्ठं प्रति गच्छतु",
  "components.pagination.go-to-previous": "पूर्वपृष्ठं गच्छतु",
  "components.pagination.remaining-links": "अन्ये च {संख्या} अन्ये लिङ्कानि",
  "components.popUpWarning.button.cancel": "न, रद्द करें",
  "components.popUpWarning.button.confirm": "हाँ, पुष्टि करें",
  "components.popUpWarning.message": "किं भवान् निश्चयेन एतत् विलोपयितुम् इच्छति?",
  "components.popUpWarning.title": "कृपया पुष्टि करें",
  "form.button.continue": "अग्रेसर",
  "form.button.done": "कृतम्",
  "global.actions": "क्रियाः",
  "global.back": "पृष्ठम्",
  "global.change-password": "गुप्तशब्दं परिवर्तयतु",
  "global.content-manager": "सामग्री प्रबन्धक",
  "global.continue": "अग्रे गच्छतु",
  "global.delete": "विलोपनम्",
  "global.delete-target": "{लक्ष्य} को हटाएँ",
  "global.description": "विवरण",
  "global.details": "विवरणम्",
  "global.disabled": "अक्षम",
  "global.documentation": "दस्तावेजीकरणम्",
  "global.enabled": "सक्षमम्",
  "global.finish": "समाप्त",
  "global.marketplace": "बाजारस्थान",
  "global.name": "नाम",
  "global.none": "कोऽपि नास्ति",
  "global.password": "गुप्तशब्द",
  "global.plugins": "प्लगिन्स्",
  "global.profile": "प्रोफाइल",
  "global.prompt.unsaved": "किं भवान् निश्चयेन एतत् पृष्ठं त्यक्तुम् इच्छति? भवतः सर्वे परिवर्तनानि नष्टानि भविष्यन्ति",
  "global.reset-password": "गुप्तशब्दं पुनः सेट् कुर्वन्तु",
  "global.roles": "भूमिका",
  "global.save": "रक्षतु",
  "global.see-more": "अधिकं पश्यन्तु",
  "global.select": "चयन",
  "global.select-all-entries": "सर्वप्रविष्टीनां चयनं कुर्वन्तु",
  "global.settings": "सेटिंग्स्",
  "global.type": "प्रकार",
  "global.users": "उपयोक्तारः",
  "notification.contentType.relations.conflict": "सामग्रीप्रकारस्य परस्परविरोधिनः सम्बन्धाः सन्ति",
  "notification.default.title": "सूचना:",
  "notification.error": "त्रुटिः अभवत्",
  "notification.error.layout": "विन्यासं पुनः प्राप्तुं न शक्यते",
  "notification.form.error.fields": "प्रपत्रे केचन त्रुटयः सन्ति",
  "notification.form.success.fields": "परिवर्तनानि रक्षितानि",
  "notification.link-copied": "लिङ्क् क्लिप्बोर्ड् मध्ये प्रतिलिपिता",
  "notification.permission.not-allowed-read": "भवता एतत् दस्तावेजं द्रष्टुं न अनुमतम्",
  "notification.success.delete": "द्रव्यं विलोपितम्",
  "notification.success.saved": "रक्षितम्",
  "notification.success.title": "सफलता:",
  "notification.version.update.message": "Strapi इत्यस्य नूतनं संस्करणं उपलब्धम् अस्ति!",
  "notification.warning.title": "चेतावनी:",
  or,
  "request.error.model.unknown": "एतत् प्रतिरूपं नास्ति",
  skipToContent,
  submit
};
export {
  Analytics,
  Documentation,
  Email,
  Password,
  Provider,
  ResetPasswordToken,
  Role,
  Username,
  Users,
  anErrorOccurred,
  clearLabel,
  sa as default,
  or,
  skipToContent,
  submit
};
//# sourceMappingURL=sa-CHD-r_h--UGSWSUQL.js.map
