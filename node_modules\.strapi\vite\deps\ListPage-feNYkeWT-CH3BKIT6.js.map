{"version": 3, "sources": ["../../../@strapi/admin/ee/admin/src/pages/SettingsPage/pages/Users/<USER>"], "sourcesContent": ["import { ListPageCE } from '../../../../../../../admin/src/pages/Settings/pages/Users/<USER>';\nimport { useLicenseLimitNotification } from '../../../../hooks/useLicenseLimitNotification';\n\nexport const UserListPageEE = () => {\n  useLicenseLimitNotification();\n\n  return <ListPageCE />;\n};\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAGO,IAAM,iBAAiB,MAAM;AACN,8BAAA;AAE5B,aAAA,wBAAQ,YAAW,CAAA,CAAA;AACrB;", "names": []}