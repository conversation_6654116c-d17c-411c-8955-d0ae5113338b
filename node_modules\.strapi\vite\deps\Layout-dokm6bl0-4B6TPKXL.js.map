{"version": 3, "sources": ["../../../@strapi/admin/admin/src/hooks/useSettingsMenu.ts", "../../../@strapi/admin/admin/src/pages/Settings/components/SettingsNav.tsx", "../../../@strapi/admin/admin/src/pages/Settings/Layout.tsx"], "sourcesContent": ["import * as React from 'react';\n\nimport sortBy from 'lodash/sortBy';\nimport { useSelector } from 'react-redux';\n\nimport { SETTINGS_LINKS_CE, SettingsMenuLink } from '../constants';\nimport { useAppInfo } from '../features/AppInfo';\nimport { useAuth } from '../features/Auth';\nimport { useStrapiApp } from '../features/StrapiApp';\nimport { selectAdminPermissions } from '../selectors';\nimport { PermissionMap } from '../types/permissions';\n\nimport { useEnterprise } from './useEnterprise';\n\nimport type {\n  StrapiAppSetting,\n  StrapiAppSettingLink as IStrapiAppSettingLink,\n} from '../core/apis/router';\n\nconst formatLinks = (menu: SettingsMenuSection[]): SettingsMenuSectionWithDisplayedLinks[] =>\n  menu.map((menuSection) => {\n    const formattedLinks = menuSection.links.map((link) => ({\n      ...link,\n      isDisplayed: false,\n    }));\n\n    return { ...menuSection, links: formattedLinks };\n  });\n\ninterface SettingsMenuLinkWithPermissions extends SettingsMenuLink {\n  permissions: IStrapiAppSettingLink['permissions'];\n  hasNotification?: boolean;\n}\n\ninterface StrapiAppSettingsLink extends IStrapiAppSettingLink {\n  licenseOnly?: never;\n  hasNotification?: never;\n}\n\ninterface SettingsMenuSection extends Omit<StrapiAppSetting, 'links'> {\n  links: Array<SettingsMenuLinkWithPermissions | StrapiAppSettingsLink>;\n}\n\ninterface SettingsMenuLinkWithPermissionsAndDisplayed extends SettingsMenuLinkWithPermissions {\n  isDisplayed: boolean;\n}\n\ninterface StrapiAppSettingLinkWithDisplayed extends StrapiAppSettingsLink {\n  isDisplayed: boolean;\n}\n\ninterface SettingsMenuSectionWithDisplayedLinks extends Omit<SettingsMenuSection, 'links'> {\n  links: Array<SettingsMenuLinkWithPermissionsAndDisplayed | StrapiAppSettingLinkWithDisplayed>;\n}\n\ntype SettingsMenu = SettingsMenuSectionWithDisplayedLinks[];\n\nconst useSettingsMenu = (): {\n  isLoading: boolean;\n  menu: SettingsMenu;\n} => {\n  const [{ isLoading, menu }, setData] = React.useState<{\n    isLoading: boolean;\n    menu: SettingsMenu;\n  }>({\n    isLoading: true,\n    menu: [],\n  });\n  const checkUserHasPermission = useAuth(\n    'useSettingsMenu',\n    (state) => state.checkUserHasPermissions\n  );\n  const shouldUpdateStrapi = useAppInfo('useSettingsMenu', (state) => state.shouldUpdateStrapi);\n  const settings = useStrapiApp('useSettingsMenu', (state) => state.settings);\n  const permissions = useSelector(selectAdminPermissions);\n\n  /**\n   * memoize the return value of this function to avoid re-computing it on every render\n   * because it's used in an effect it ends up re-running recursively.\n   */\n  const ceLinks = React.useMemo(() => SETTINGS_LINKS_CE(), []);\n\n  const { admin: adminLinks, global: globalLinks } = useEnterprise(\n    ceLinks,\n    async () => (await import('../../../ee/admin/src/constants')).SETTINGS_LINKS_EE(),\n    {\n      combine(ceLinks, eeLinks) {\n        return {\n          admin: [...eeLinks.admin, ...ceLinks.admin],\n          global: [...ceLinks.global, ...eeLinks.global],\n        };\n      },\n      defaultValue: {\n        admin: [],\n        global: [],\n      },\n    }\n  );\n\n  const addPermissions = React.useCallback(\n    (link: SettingsMenuLink) => {\n      if (!link.id) {\n        throw new Error('The settings menu item must have an id attribute.');\n      }\n\n      return {\n        ...link,\n        permissions: permissions.settings?.[link.id as keyof PermissionMap['settings']]?.main ?? [],\n      } satisfies SettingsMenuLinkWithPermissions;\n    },\n    [permissions.settings]\n  );\n\n  React.useEffect(() => {\n    const getData = async () => {\n      interface MenuLinkPermission {\n        hasPermission: boolean;\n        sectionIndex: number;\n        linkIndex: number;\n      }\n\n      const buildMenuPermissions = (sections: SettingsMenuSectionWithDisplayedLinks[]) =>\n        Promise.all(\n          sections.reduce<Promise<MenuLinkPermission>[]>((acc, section, sectionIndex) => {\n            const linksWithPermissions = section.links.map(async (link, linkIndex) => ({\n              hasPermission: (await checkUserHasPermission(link.permissions)).length > 0,\n              sectionIndex,\n              linkIndex,\n            }));\n\n            return [...acc, ...linksWithPermissions];\n          }, [])\n        );\n\n      const menuPermissions = await buildMenuPermissions(sections);\n\n      setData((prev) => {\n        return {\n          ...prev,\n          isLoading: false,\n          menu: sections.map((section, sectionIndex) => ({\n            ...section,\n            links: section.links.map((link, linkIndex) => {\n              const permission = menuPermissions.find(\n                (permission) =>\n                  permission.sectionIndex === sectionIndex && permission.linkIndex === linkIndex\n              );\n\n              return {\n                ...link,\n                isDisplayed: Boolean(permission?.hasPermission),\n              };\n            }),\n          })),\n        };\n      });\n    };\n\n    const { global, ...otherSections } = settings;\n    const sections = formatLinks([\n      {\n        ...global,\n        links: sortBy([...global.links, ...globalLinks.map(addPermissions)], (link) => link.id).map(\n          (link) => ({\n            ...link,\n            hasNotification: link.id === '000-application-infos' && shouldUpdateStrapi,\n          })\n        ),\n      },\n      {\n        id: 'permissions',\n        intlLabel: { id: 'Settings.permissions', defaultMessage: 'Administration Panel' },\n        links: adminLinks.map(addPermissions),\n      },\n      ...Object.values(otherSections),\n    ]);\n\n    getData();\n  }, [\n    adminLinks,\n    globalLinks,\n    settings,\n    shouldUpdateStrapi,\n    addPermissions,\n    checkUserHasPermission,\n  ]);\n\n  return {\n    isLoading,\n    menu: menu.map((menuItem) => ({\n      ...menuItem,\n      links: menuItem.links.filter((link) => link.isDisplayed),\n    })),\n  };\n};\n\nexport { useSettingsMenu };\nexport type { SettingsMenu };\n", "import {\n  SubNav,\n  SubNavHeader,\n  SubNavLink,\n  SubNavSection,\n  SubNavSections,\n} from '@strapi/design-system';\nimport { Lightning } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { NavLink, useLocation } from 'react-router-dom';\nimport { styled } from 'styled-components';\n\nimport { useTracking } from '../../../features/Tracking';\nimport { SettingsMenu } from '../../../hooks/useSettingsMenu';\n\nconst CustomIcon = styled(Lightning)`\n  right: 15px;\n  position: absolute;\n  bottom: 50%;\n  transform: translateY(50%);\n\n  path {\n    fill: ${({ theme }) => theme.colors.warning500};\n  }\n`;\n\nconst Link = styled(SubNavLink)`\n  &.active ${CustomIcon} {\n    right: 13px;\n  }\n`;\n\ninterface SettingsNavProps {\n  menu: SettingsMenu;\n}\n\nconst SettingsNav = ({ menu }: SettingsNavProps) => {\n  const { formatMessage } = useIntl();\n  const { trackUsage } = useTracking();\n  const { pathname } = useLocation();\n\n  const filteredMenu = menu.filter(\n    (section) => !section.links.every((link) => link.isDisplayed === false)\n  );\n\n  const sections = filteredMenu.map((section) => {\n    return {\n      ...section,\n      title: section.intlLabel,\n      links: section.links.map((link) => {\n        return {\n          ...link,\n          title: link.intlLabel,\n          name: link.id,\n        };\n      }),\n    };\n  });\n\n  const label = formatMessage({\n    id: 'global.settings',\n    defaultMessage: 'Settings',\n  });\n\n  const handleClickOnLink = (destination: string) => () => {\n    trackUsage('willNavigate', { from: pathname, to: destination });\n  };\n\n  return (\n    <SubNav aria-label={label}>\n      <SubNavHeader label={label} />\n      <SubNavSections>\n        {sections.map((section) => (\n          <SubNavSection key={section.id} label={formatMessage(section.intlLabel)}>\n            {section.links.map((link) => {\n              return (\n                <Link\n                  tag={NavLink}\n                  withBullet={link.hasNotification}\n                  to={link.to}\n                  onClick={handleClickOnLink(link.to)}\n                  key={link.id}\n                  position=\"relative\"\n                >\n                  {formatMessage(link.intlLabel)}\n                  {link?.licenseOnly && <CustomIcon width=\"1.5rem\" height=\"1.5rem\" />}\n                </Link>\n              );\n            })}\n          </SubNavSection>\n        ))}\n      </SubNavSections>\n    </SubNav>\n  );\n};\n\nexport { SettingsNav };\nexport type { SettingsNavProps };\n", "import { useIntl } from 'react-intl';\nimport { Navigate, Outlet, useMatch } from 'react-router-dom';\n\nimport { Layouts } from '../../components/Layouts/Layout';\nimport { Page } from '../../components/PageHelpers';\nimport { useSettingsMenu } from '../../hooks/useSettingsMenu';\n\nimport { SettingsNav } from './components/SettingsNav';\n\nconst Layout = () => {\n  /**\n   * This ensures we're capturing the settingId from the URL\n   * but also lets any nesting after that pass.\n   */\n  const match = useMatch('/settings/:settingId/*');\n  const { formatMessage } = useIntl();\n  const { isLoading, menu } = useSettingsMenu();\n\n  // Since the useSettingsMenu hook can make API calls in order to check the links permissions\n  // We need to add a loading state to prevent redirecting the user while permissions are being checked\n  if (isLoading) {\n    return <Page.Loading />;\n  }\n\n  if (!match?.params.settingId) {\n    return <Navigate to=\"application-infos\" />;\n  }\n\n  return (\n    <Layouts.Root sideNav={<SettingsNav menu={menu} />}>\n      <Page.Title>\n        {formatMessage({\n          id: 'global.settings',\n          defaultMessage: 'Settings',\n        })}\n      </Page.Title>\n      <Outlet />\n    </Layouts.Root>\n  );\n};\n\nexport { Layout };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAmBA,IAAM,cAAc,CAAC,SACnB,KAAK,IAAI,CAAC,gBAAgB;AACxB,QAAM,iBAAiB,YAAY,MAAM,IAAI,CAAC,UAAU;IACtD,GAAG;IACH,aAAa;EAAA,EACb;AAEF,SAAO,EAAE,GAAG,aAAa,OAAO,eAAe;AACjD,CAAC;AA8BH,IAAM,kBAAkB,MAGnB;AACG,QAAA,CAAC,EAAE,WAAW,KAAA,GAAQ,OAAO,IAAU,eAG1C;IACD,WAAW;IACX,MAAM,CAAA;EAAC,CACR;AACD,QAAM,yBAAyB;IAC7B;IACA,CAAC,UAAU,MAAM;EAAA;AAEnB,QAAM,qBAAqB,WAAW,mBAAmB,CAAC,UAAU,MAAM,kBAAkB;AAC5F,QAAM,WAAW,aAAa,mBAAmB,CAAC,UAAU,MAAM,QAAQ;AACpE,QAAA,cAAc,YAAY,sBAAsB;AAMtD,QAAM,UAAgB,cAAQ,MAAM,kBAAkB,GAAG,CAAA,CAAE;AAE3D,QAAM,EAAE,OAAO,YAAY,QAAQ,YAAA,IAAgB;IACjD;IACA,aAAa,MAAM,OAAO,8BAAiC,EAAA,KAAA,OAAA,EAAA,CAAA,GAAG,kBAAkB;IAChF;MACE,QAAQA,UAAS,SAAS;AACjB,eAAA;UACL,OAAO,CAAC,GAAG,QAAQ,OAAO,GAAGA,SAAQ,KAAK;UAC1C,QAAQ,CAAC,GAAGA,SAAQ,QAAQ,GAAG,QAAQ,MAAM;QAAA;MAC/C;MAEF,cAAc;QACZ,OAAO,CAAA;QACP,QAAQ,CAAA;MAAC;IACX;EACF;AAGF,QAAM,iBAAuB;IAC3B,CAAC,SAA2B;;AACtB,UAAA,CAAC,KAAK,IAAI;AACN,cAAA,IAAI,MAAM,mDAAmD;MAAA;AAG9D,aAAA;QACL,GAAG;QACH,eAAa,uBAAY,aAAZ,mBAAuB,KAAK,QAA5B,mBAAoE,SAAQ,CAAA;MAAC;IAC5F;IAEF,CAAC,YAAY,QAAQ;EAAA;AAGvB,EAAM,gBAAU,MAAM;AACpB,UAAM,UAAU,YAAY;AAOpB,YAAA,uBAAuB,CAACC,cAC5B,QAAQ;QACNA,UAAS,OAAsC,CAAC,KAAK,SAAS,iBAAiB;AAC7E,gBAAM,uBAAuB,QAAQ,MAAM,IAAI,OAAO,MAAM,eAAe;YACzE,gBAAgB,MAAM,uBAAuB,KAAK,WAAW,GAAG,SAAS;YACzE;YACA;UAAA,EACA;AAEF,iBAAO,CAAC,GAAG,KAAK,GAAG,oBAAoB;QAAA,GACtC,CAAA,CAAE;MAAA;AAGH,YAAA,kBAAkB,MAAM,qBAAqB,QAAQ;AAE3D,cAAQ,CAAC,SAAS;AACT,eAAA;UACL,GAAG;UACH,WAAW;UACX,MAAM,SAAS,IAAI,CAAC,SAAS,kBAAkB;YAC7C,GAAG;YACH,OAAO,QAAQ,MAAM,IAAI,CAAC,MAAM,cAAc;AAC5C,oBAAM,aAAa,gBAAgB;gBACjC,CAACC,gBACCA,YAAW,iBAAiB,gBAAgBA,YAAW,cAAc;cAAA;AAGlE,qBAAA;gBACL,GAAG;gBACH,aAAa,QAAQ,yCAAY,aAAa;cAAA;YAChD,CACD;UAAA,EACD;QAAA;MACJ,CACD;IAAA;AAGH,UAAM,EAAE,QAAQ,GAAG,cAAA,IAAkB;AACrC,UAAM,WAAW,YAAY;MAC3B;QACE,GAAG;QACH,WAAO,cAAAC,SAAO,CAAC,GAAG,OAAO,OAAO,GAAG,YAAY,IAAI,cAAc,CAAC,GAAG,CAAC,SAAS,KAAK,EAAE,EAAE;UACtF,CAAC,UAAU;YACT,GAAG;YACH,iBAAiB,KAAK,OAAO,2BAA2B;UAAA;QAC1D;MACF;MAEF;QACE,IAAI;QACJ,WAAW,EAAE,IAAI,wBAAwB,gBAAgB,uBAAuB;QAChF,OAAO,WAAW,IAAI,cAAc;MAAA;MAEtC,GAAG,OAAO,OAAO,aAAa;IAAA,CAC/B;AAEO,YAAA;EAAA,GACP;IACD;IACA;IACA;IACA;IACA;IACA;EAAA,CACD;AAEM,SAAA;IACL;IACA,MAAM,KAAK,IAAI,CAAC,cAAc;MAC5B,GAAG;MACH,OAAO,SAAS,MAAM,OAAO,CAAC,SAAS,KAAK,WAAW;IAAA,EACvD;EAAA;AAEN;ACnLA,IAAM,aAAa,GAAO,aAAS;;;;;;;YAOvB,CAAC,EAAE,MAAA,MAAY,MAAM,OAAO,UAAU;;;AAIlD,IAAM,OAAO,GAAO,UAAU;aACjB,UAAU;;;;AASvB,IAAM,cAAc,CAAC,EAAE,KAAA,MAA6B;AAC5C,QAAA,EAAE,cAAc,IAAI,QAAQ;AAC5B,QAAA,EAAE,WAAW,IAAI,YAAY;AAC7B,QAAA,EAAE,SAAS,IAAI,YAAY;AAEjC,QAAM,eAAe,KAAK;IACxB,CAAC,YAAY,CAAC,QAAQ,MAAM,MAAM,CAAC,SAAS,KAAK,gBAAgB,KAAK;EAAA;AAGxE,QAAM,WAAW,aAAa,IAAI,CAAC,YAAY;AACtC,WAAA;MACL,GAAG;MACH,OAAO,QAAQ;MACf,OAAO,QAAQ,MAAM,IAAI,CAAC,SAAS;AAC1B,eAAA;UACL,GAAG;UACH,OAAO,KAAK;UACZ,MAAM,KAAK;QAAA;MACb,CACD;IAAA;EACH,CACD;AAED,QAAM,QAAQ,cAAc;IAC1B,IAAI;IACJ,gBAAgB;EAAA,CACjB;AAEK,QAAA,oBAAoB,CAAC,gBAAwB,MAAM;AACvD,eAAW,gBAAgB,EAAE,MAAM,UAAU,IAAI,YAAA,CAAa;EAAA;AAI9D,aAAA,yBAAC,QAAO,EAAA,cAAY,OAClB,UAAA;QAAA,wBAAC,cAAA,EAAa,MAAc,CAAA;QAAA,wBAC3B,gBACE,EAAA,UAAA,SAAS,IAAI,CAAC,gBACZ,wBAAA,eAAA,EAA+B,OAAO,cAAc,QAAQ,SAAS,GACnE,UAAA,QAAQ,MAAM,IAAI,CAAC,SAAS;AAEzB,iBAAA;QAAC;QAAA;UACC,KAAK;UACL,YAAY,KAAK;UACjB,IAAI,KAAK;UACT,SAAS,kBAAkB,KAAK,EAAE;UAElC,UAAS;UAER,UAAA;YAAA,cAAc,KAAK,SAAS;aAC5B,6BAAM,oBAAe,wBAAC,YAAA,EAAW,OAAM,UAAS,QAAO,SAAS,CAAA;UAAA;QAAA;QAJ5D,KAAK;MAAA;IAKZ,CAEH,EAAA,GAfiB,QAAQ,EAgB5B,CACD,EACH,CAAA;EAAA,EACF,CAAA;AAEJ;ACrFA,IAAM,SAAS,MAAM;AAKb,QAAA,QAAQ,SAAS,wBAAwB;AACzC,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,EAAE,WAAW,KAAK,IAAI,gBAAgB;AAI5C,MAAI,WAAW;AACN,eAAA,wBAAC,KAAK,SAAL,CAAA,CAAa;EAAA;AAGnB,MAAA,EAAC,+BAAO,OAAO,YAAW;AACrB,eAAA,wBAAC,UAAS,EAAA,IAAG,oBAAoB,CAAA;EAAA;AAIxC,aAAA,yBAAC,QAAQ,MAAR,EAAa,aAAU,wBAAA,aAAA,EAAY,KAAA,CAAY,GAC9C,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAc,cAAA;MACb,IAAI;MACJ,gBAAgB;IAAA,CACjB,EACH,CAAA;QAAA,wBACC,QAAO,CAAA,CAAA;EAAA,EACV,CAAA;AAEJ;", "names": ["ceLinks", "sections", "permission", "sortBy"]}