module.exports = ({ env }) => ({
  'users-permissions': {
    config: {
      // jwtSecret is typically managed in users-permissions.js or .env
    },
  },
  'email': {
    config: {
      provider: 'nodemailer',
      providerOptions: {
        host: env('SMTP_HOST', 'smtp.163.com'),
        port: env.int('SMTP_PORT', 465),
        secure: true, // 使用 SSL
        auth: {
          user: env('SMTP_USERNAME'),
          pass: env('SMTP_PASSWORD'),
        },
        // 添加连接选项以提高兼容性
        connectionTimeout: 60000, // 60 秒连接超时
        greetingTimeout: 30000,   // 30 秒问候超时
        socketTimeout: 60000,     // 60 秒套接字超时
        // 忽略 TLS 证书错误（仅在必要时使用）
        tls: {
          rejectUnauthorized: false
        }
      },
      settings: {
        defaultFrom: env('SMTP_USERNAME', '<EMAIL>'),
        defaultReplyTo: env('SMTP_USERNAME', '<EMAIL>'),
      },
    },
  },
});
