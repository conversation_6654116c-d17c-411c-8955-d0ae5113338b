{"version": 3, "sources": ["../../../@strapi/content-type-builder/dist/_chunks/id-W1sKBFEw.mjs"], "sourcesContent": ["const configurations = \"konfigurasi\";\nconst from = \"dari\";\nconst id = {\n  \"attribute.boolean\": \"Boolean\",\n  \"attribute.boolean.description\": \"Ya atau tidak, 1 atau 0, benar atau salah\",\n  \"attribute.component\": \"Komponen\",\n  \"attribute.component.description\": \"Grup dari bidang yang dapat diulang\",\n  \"attribute.date\": \"Tanggal\",\n  \"attribute.date.description\": \"Date picker dengan jam, menit dan detik\",\n  \"attribute.datetime\": \"Tanggal waktu\",\n  \"attribute.dynamiczone\": \"Zona dinamis\",\n  \"attribute.dynamiczone.description\": \"Ambil konten dinamis ketika edit kontent\",\n  \"attribute.email\": \"Email\",\n  \"attribute.email.description\": \"Bidang email dengan format validasi\",\n  \"attribute.enumeration\": \"Pencacahan\",\n  \"attribute.enumeration.description\": \"Daftar nilai, lalu pilih satu\",\n  \"attribute.json\": \"JSON\",\n  \"attribute.json.description\": \"Data dalam format JSON\",\n  \"attribute.media\": \"Media\",\n  \"attribute.media.description\": \"File seperti gambar, video, dll\",\n  \"attribute.null\": \" \",\n  \"attribute.number\": \"Angka\",\n  \"attribute.number.description\": \"Angka (integer, pecahan, desimal)\",\n  \"attribute.password\": \"Kata sandi\",\n  \"attribute.password.description\": \"Kata sandi dengan enkripsi\",\n  \"attribute.relation\": \"Relasi\",\n  \"attribute.relation.description\": \"Mengacu pada Jenis Koleksi\",\n  \"attribute.richtext\": \"Rich teks\",\n  \"attribute.richtext.description\": \"Editor teks kaya dengan opsi pemformatan\",\n  \"attribute.text\": \"Teks\",\n  \"attribute.text.description\": \"Teks kecil atau panjang seperti judul atau deskripsi\",\n  \"attribute.time\": \"Waktu\",\n  \"attribute.timestamp\": \"Timestamp\",\n  \"attribute.uid\": \"UID\",\n  \"attribute.uid.description\": \"Pengenal unik\",\n  \"button.attributes.add.another\": \"Tambahkan bidang lain\",\n  \"button.component.add\": \"Tambahkan komponen\",\n  \"button.component.create\": \"Buat komponen baru\",\n  \"button.model.create\": \"Buat jenis koleksi baru\",\n  \"button.single-types.create\": \"Buat tipe tunggal baru\",\n  \"component.repeatable\": \"(berulang)\",\n  \"components.componentSelect.no-component-available\": \"Anda telah menambahkan semua komponen Anda\",\n  \"components.componentSelect.no-component-available.with-search\": \"Tidak ada komponen yang cocok dengan pencarian Anda\",\n  \"components.componentSelect.value-component\": \"{number} komponen dipilih (ketik untuk mencari komponen)\",\n  \"components.componentSelect.value-components\": \"{number} komponen dipilih\",\n  configurations,\n  \"contentType.collectionName.description\": \"Berguna jika nama Jenis Konten dan nama tabel Anda berbeda\",\n  \"contentType.collectionName.label\": \"Nama koleksi\",\n  \"contentType.displayName.label\": \"Nama tampilan\",\n  \"contentType.kind.change.warning\": \"Anda baru saja mengubah jenis tipe konten: API akan disetel ulang (rute, pengontrol, dan layanan akan ditimpa).\",\n  \"error.attributeName.reserved-name\": \"Nama ini tidak dapat digunakan dalam tipe konten Anda karena dapat merusak fungsi lainnya\",\n  \"error.contentTypeName.reserved-name\": \"Nama ini tidak dapat digunakan dalam proyek Anda karena dapat merusak fungsi lainnya\",\n  \"error.validation.enum-duplicate\": \"Nilai duplikat tidak diperbolehkan\",\n  \"error.validation.minSupMax\": \"Tidak bisa lebih unggul\",\n  \"error.validation.regex\": \"Pola ekspresi reguler tidak valid\",\n  \"error.validation.relation.targetAttribute-taken\": \"Nama ini ada di target\",\n  \"form.attribute.component.option.add\": \"Tambahkan komponen\",\n  \"form.attribute.component.option.create\": \"Buat komponen baru\",\n  \"form.attribute.component.option.create.description\": \"Sebuah komponen dibagikan ke seluruh tipe dan komponen, itu akan tersedia dan dapat diakses di mana saja.\",\n  \"form.attribute.component.option.repeatable\": \"Komponen yang dapat diulang\",\n  \"form.attribute.component.option.repeatable.description\": \"Terbaik untuk beberapa contoh (larik) bahan, tag meta, dll ..\",\n  \"form.attribute.component.option.reuse-existing\": \"Gunakan komponen yang ada\",\n  \"form.attribute.component.option.reuse-existing.description\": \"Gunakan kembali komponen yang sudah dibuat untuk menjaga data Anda tetap konsisten di seluruh tipe konten.\",\n  \"form.attribute.component.option.single\": \"Komponen tunggal\",\n  \"form.attribute.component.option.single.description\": \"Paling baik untuk mengelompokkan bidang seperti alamat lengkap, informasi utama, dll ...\",\n  \"form.attribute.item.customColumnName\": \"Nama kolom kustom\",\n  \"form.attribute.item.customColumnName.description\": \"Ini berguna untuk mengganti nama kolom database dalam format yang lebih komprehensif untuk respons API\",\n  \"form.attribute.item.defineRelation.fieldName\": \"Nama bidang\",\n  \"form.attribute.item.enumeration.graphql\": \"Nama pengganti untuk GraphQL\",\n  \"form.attribute.item.enumeration.graphql.description\": \"Memungkinkan Anda untuk mengganti nama default yang dibuat untuk GraphQL \",\n  \"form.attribute.item.enumeration.placeholder\": \"Misal:\\npagi\\nsiang\\nsore\",\n  \"form.attribute.item.enumeration.rules\": \"Nilai (satu baris per nilai)\",\n  \"form.attribute.item.maximum\": \"Nilai maksimum\",\n  \"form.attribute.item.maximumLength\": \"Panjang maksimum\",\n  \"form.attribute.item.minimum\": \"Nilai minimum\",\n  \"form.attribute.item.minimumLength\": \"Panjang minimum\",\n  \"form.attribute.item.number.type\": \"Format angka\",\n  \"form.attribute.item.number.type.biginteger\": \"big integer (misal: 123456789)\",\n  \"form.attribute.item.number.type.decimal\": \"desimal (misal: 2.22)\",\n  \"form.attribute.item.number.type.integer\": \"integer (misal: 10)\",\n  \"form.attribute.item.privateField\": \"Bidang pribadi\",\n  \"form.attribute.item.privateField.description\": \"Bidang ini tidak akan ditampilkan di request API\",\n  \"form.attribute.item.requiredField\": \"Bidang wajib\",\n  \"form.attribute.item.requiredField.description\": \"Bidang ini wajib diisi untuk meneruskan data\",\n  \"form.attribute.item.text.regex\": \"Pola RegExp\",\n  \"form.attribute.item.text.regex.description\": \"Teks ekspresi reguler\",\n  \"form.attribute.item.uniqueField\": \"Bidang unik\",\n  \"form.attribute.item.uniqueField.description\": \"Anda tidak dapat membuat entri jika sudah ada entri dengan konten identik\",\n  \"form.attribute.media.allowed-types\": \"Pilih jenis media yang diizinkan\",\n  \"form.attribute.media.allowed-types.option-files\": \"File\",\n  \"form.attribute.media.allowed-types.option-images\": \"Gambar\",\n  \"form.attribute.media.allowed-types.option-videos\": \"Video\",\n  \"form.attribute.media.option.multiple\": \"Banyak media\",\n  \"form.attribute.media.option.multiple.description\": \"Paling baik untuk pengunduhan penggeser, komidi putar, atau banyak file\",\n  \"form.attribute.media.option.single\": \"Media tunggal\",\n  \"form.attribute.media.option.single.description\": \"Terbaik untuk avatar, gambar profil, atau sampul\",\n  \"form.attribute.settings.default\": \"Nilai default\",\n  \"form.attribute.text.option.long-text\": \"Teks panjang\",\n  \"form.attribute.text.option.long-text.description\": \"Terbaik untuk deskripsi, biografi. Pencarian yang tepat dinonaktifkan.\",\n  \"form.attribute.text.option.short-text\": \"Teks pendek\",\n  \"form.attribute.text.option.short-text.description\": \"Terbaik untuk judul, nama, tautan (URL). Ini juga memungkinkan pencarian yang tepat di lapangan.\",\n  \"form.button.add-components-to-dynamiczone\": \"Tambahkan komponen ke zona\",\n  \"form.button.add-field\": \"Tambahkan bidang lain\",\n  \"form.button.add-first-field-to-created-component\": \"Tambahkan bidang pertama ke komponen\",\n  \"form.button.add.field.to.collectionType\": \"Tambahkan bidang lain ke jenis koleksi ini\",\n  \"form.button.add.field.to.component\": \"Tambahkan bidang lain ke komponen ini\",\n  \"form.button.add.field.to.contentType\": \"Tambahkan bidang lain ke jenis konten ini\",\n  \"form.button.add.field.to.singleType\": \"Tambahkan bidang lain ke jenis tunggal ini\",\n  \"form.button.cancel\": \"Batal\",\n  \"form.button.collection-type.description\": \"Paling baik untuk berbagai contoh seperti artikel, produk, komentar, dll.\",\n  \"form.button.configure-component\": \"Konfigurasikan komponen\",\n  \"form.button.configure-view\": \"Konfigurasi tampilan\",\n  \"form.button.select-component\": \"Pilih sebuah komponen\",\n  \"form.button.single-type.description\": \"Terbaik untuk satu contoh seperti tentang kami, beranda, dll.\",\n  from,\n  \"modalForm.attribute.form.base.name.description\": \"Tidak ada spasi yang diperbolehkan untuk nama atribut\",\n  \"modalForm.attribute.form.base.name.placeholder\": \"Misalnya Siput, urlSeo, urlKanonis\",\n  \"modalForm.attribute.target-field\": \"Bidang terlampir\",\n  \"modalForm.attributes.select-component\": \"Pilih komponen\",\n  \"modalForm.attributes.select-components\": \"Pilih komponen\",\n  \"modalForm.component.header-create\": \"Buat komponen\",\n  \"modalForm.components.create-component.category.label\": \"Pilih kategori atau masukkan nama untuk membuat yang baru\",\n  \"modalForm.components.icon.label\": \"Ikon\",\n  \"modalForm.editCategory.base.name.description\": \"Tidak ada spasi yang diperbolehkan untuk nama kategori\",\n  \"modalForm.header-edit\": \"Edit {name}\",\n  \"modalForm.header.categories\": \"Kategori\",\n  \"modalForm.header.back\": \"Kembali\",\n  \"modalForm.singleType.header-create\": \"Buat jenis tunggal\",\n  \"modalForm.sub-header.addComponentToDynamicZone\": \"Tambahkan komponen baru ke zona dinamis\",\n  \"modalForm.sub-header.attribute.create\": \"Tambah bidang {type}\",\n  \"modalForm.sub-header.attribute.create.step\": \"Tambah komponen ({step}/2)\",\n  \"modalForm.sub-header.attribute.edit\": \"Edit {name}\",\n  \"modalForm.sub-header.chooseAttribute.collectionType\": \"Pilih bidang untuk jenis koleksi Anda\",\n  \"modalForm.sub-header.chooseAttribute.component\": \"Pilih bidang untuk komponen Anda\",\n  \"modalForm.sub-header.chooseAttribute.singleType\": \"Pilih bidang untuk tipe tunggal Anda\",\n  \"modelPage.attribute.relation-polymorphic\": \"Hubungan (polimorfik)\",\n  \"modelPage.attribute.relationWith\": \"Hubungan dengan\",\n  \"notification.info.autoreaload-disable\": \"Fitur autoReload diperlukan untuk menggunakan plugin ini. Mulai server Anda dengan `strapi develop`\",\n  \"notification.info.creating.notSaved\": \"Harap simpan pekerjaan Anda sebelum membuat jenis atau komponen koleksi baru\",\n  \"plugin.description.long\": \"Buat model struktur data API Anda. Buat bidang dan relasi baru hanya dalam satu menit. File secara otomatis dibuat dan diperbarui dalam proyek Anda.\",\n  \"plugin.description.short\": \"Buat model struktur data API Anda.\",\n  \"popUpForm.navContainer.advanced\": \"Pengaturan lanjut\",\n  \"popUpForm.navContainer.base\": \"Pengaturan dasar\",\n  \"popUpWarning.bodyMessage.cancel-modifications\": \"Anda yakin ingin membatalkan modifikasi Anda?\",\n  \"popUpWarning.bodyMessage.cancel-modifications.with-components\": \"Anda yakin ingin membatalkan modifikasi Anda? Beberapa komponen telah dibuat atau dimodifikasi ...\",\n  \"popUpWarning.bodyMessage.category.delete\": \"Anda yakin ingin menghapus kategori ini? Semua komponen juga akan dihapus.\",\n  \"popUpWarning.bodyMessage.component.delete\": \"Anda yakin ingin menghapus komponen ini?\",\n  \"popUpWarning.bodyMessage.contentType.delete\": \"Anda yakin ingin menghapus jenis koleksi ini?\",\n  \"popUpWarning.draft-publish.button.confirm\": \"Ya, non-aktifkan\",\n  \"popUpWarning.draft-publish.message\": \"Jika Anda menonaktifkan sistem Draf / Terbitkan, draf Anda akan dihapus.\",\n  \"popUpWarning.draft-publish.second-message\": \"Anda yakin ingin menonaktifkannya?\",\n  \"prompt.unsaved\": \"Anda yakin ingin pergi? Semua modifikasi Anda akan hilang.\",\n  \"relation.attributeName.placeholder\": \"Misal: penulis, kategori, tag\",\n  \"relation.manyToMany\": \"memiliki dan menjadi milik banyak orang\",\n  \"relation.manyToOne\": \"memiliki banyak\",\n  \"relation.manyWay\": \"memiliki banyak\",\n  \"relation.oneToMany\": \"memiliki banyak orang\",\n  \"relation.oneToOne\": \"memiliki dan menjadi milik satu\",\n  \"relation.oneWay\": \"memiliki satu\"\n};\nexport {\n  configurations,\n  id as default,\n  from\n};\n//# sourceMappingURL=id-W1sKBFEw.mjs.map\n"], "mappings": ";;;AAAA,IAAM,iBAAiB;AACvB,IAAM,OAAO;AACb,IAAM,KAAK;AAAA,EACT,qBAAqB;AAAA,EACrB,iCAAiC;AAAA,EACjC,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,mBAAmB;AAAA,EACnB,+BAA+B;AAAA,EAC/B,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,gCAAgC;AAAA,EAChC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,sBAAsB;AAAA,EACtB,kCAAkC;AAAA,EAClC,kBAAkB;AAAA,EAClB,8BAA8B;AAAA,EAC9B,kBAAkB;AAAA,EAClB,uBAAuB;AAAA,EACvB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,qDAAqD;AAAA,EACrD,iEAAiE;AAAA,EACjE,8CAA8C;AAAA,EAC9C,+CAA+C;AAAA,EAC/C;AAAA,EACA,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,mDAAmD;AAAA,EACnD,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,0DAA0D;AAAA,EAC1D,kDAAkD;AAAA,EAClD,8DAA8D;AAAA,EAC9D,0CAA0C;AAAA,EAC1C,sDAAsD;AAAA,EACtD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,gDAAgD;AAAA,EAChD,2CAA2C;AAAA,EAC3C,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,8CAA8C;AAAA,EAC9C,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,sCAAsC;AAAA,EACtC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,6CAA6C;AAAA,EAC7C,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,2CAA2C;AAAA,EAC3C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,sBAAsB;AAAA,EACtB,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,gCAAgC;AAAA,EAChC,uCAAuC;AAAA,EACvC;AAAA,EACA,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,qCAAqC;AAAA,EACrC,wDAAwD;AAAA,EACxD,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,sCAAsC;AAAA,EACtC,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,iDAAiD;AAAA,EACjD,iEAAiE;AAAA,EACjE,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,kBAAkB;AAAA,EAClB,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,sBAAsB;AAAA,EACtB,qBAAqB;AAAA,EACrB,mBAAmB;AACrB;", "names": []}