import {
  Table
} from "./chunk-Q6W7NBRE.js";
import {
  useOnce
} from "./chunk-HXEM7AIZ.js";
import {
  useDeleteTransferTokenMutation,
  useGetTransferTokensQuery
} from "./chunk-EXEC23EU.js";
import {
  TRANSFER_TOKEN_TYPE
} from "./chunk-P2CO6HJH.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Layouts
} from "./chunk-ELTZWS66.js";
import {
  ForwardRef$J,
  Page,
  useAPIErrorHandler,
  useNotification,
  useRBAC,
  useTracking,
  useTypedSelector
} from "./chunk-4C2ZQ5OG.js";
import {
  EmptyStateLayout,
  LinkButton,
  require_lib,
  useIntl
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  Link,
  useNavigate
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1d
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/ListView-cbl5ESj2.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var qs = __toESM(require_lib(), 1);
var tableHeaders = [
  {
    name: "name",
    label: {
      id: "Settings.tokens.ListView.headers.name",
      defaultMessage: "Name"
    },
    sortable: true
  },
  {
    name: "description",
    label: {
      id: "Settings.tokens.ListView.headers.description",
      defaultMessage: "Description"
    },
    sortable: false
  },
  {
    name: "createdAt",
    label: {
      id: "Settings.tokens.ListView.headers.createdAt",
      defaultMessage: "Created at"
    },
    sortable: false
  },
  {
    name: "lastUsedAt",
    label: {
      id: "Settings.tokens.ListView.headers.lastUsedAt",
      defaultMessage: "Last used"
    },
    sortable: false
  }
];
var ListView = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const permissions = useTypedSelector(
    (state) => {
      var _a;
      return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["transfer-tokens"];
    }
  );
  const {
    isLoading: isLoadingRBAC,
    allowedActions: { canCreate, canDelete, canUpdate, canRead }
  } = useRBAC(permissions);
  const navigate = useNavigate();
  const { trackUsage } = useTracking();
  const { _unstableFormatAPIError: formatAPIError } = useAPIErrorHandler();
  React.useEffect(() => {
    navigate({ search: qs.stringify({ sort: "name:ASC" }, { encode: false }) });
  }, [navigate]);
  useOnce(() => {
    trackUsage("willAccessTokenList", {
      tokenType: TRANSFER_TOKEN_TYPE
    });
  });
  const headers = tableHeaders.map((header) => ({
    ...header,
    label: formatMessage(header.label)
  }));
  const {
    data: transferTokens = [],
    isLoading: isLoadingTokens,
    error
  } = useGetTransferTokensQuery(void 0, {
    skip: !canRead
  });
  React.useEffect(() => {
    if (transferTokens) {
      trackUsage("didAccessTokenList", {
        number: transferTokens.length,
        tokenType: TRANSFER_TOKEN_TYPE
      });
    }
  }, [trackUsage, transferTokens]);
  React.useEffect(() => {
    if (error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    }
  }, [error, formatAPIError, toggleNotification]);
  const [deleteToken] = useDeleteTransferTokenMutation();
  const handleDelete = async (id) => {
    try {
      const res = await deleteToken(id);
      if ("error" in res) {
        toggleNotification({
          type: "danger",
          message: formatAPIError(res.error)
        });
      }
    } catch {
      toggleNotification({
        type: "danger",
        message: formatMessage({ id: "notification.error", defaultMessage: "An error occured" })
      });
    }
  };
  const isLoading = isLoadingTokens || isLoadingRBAC;
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      {
        name: "Transfer Tokens"
      }
    ) }),
    (0, import_jsx_runtime.jsx)(
      Layouts.Header,
      {
        title: formatMessage({
          id: "Settings.transferTokens.title",
          defaultMessage: "Transfer Tokens"
        }),
        subtitle: formatMessage({
          id: "Settings.transferTokens.description",
          defaultMessage: '"List of generated transfer tokens"'
          // TODO change this message
        }),
        primaryAction: canCreate ? (0, import_jsx_runtime.jsx)(
          LinkButton,
          {
            role: "button",
            tag: Link,
            "data-testid": "create-transfer-token-button",
            startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
            size: "S",
            onClick: () => trackUsage("willAddTokenFromList", {
              tokenType: TRANSFER_TOKEN_TYPE
            }),
            to: "/settings/transfer-tokens/create",
            children: formatMessage({
              id: "Settings.transferTokens.create",
              defaultMessage: "Create new Transfer Token"
            })
          }
        ) : void 0
      }
    ),
    !canRead ? (0, import_jsx_runtime.jsx)(Page.NoPermissions, {}) : (0, import_jsx_runtime.jsx)(Page.Main, { "aria-busy": isLoading, children: (0, import_jsx_runtime.jsxs)(Layouts.Content, { children: [
      transferTokens.length > 0 && (0, import_jsx_runtime.jsx)(
        Table,
        {
          permissions: { canRead, canDelete, canUpdate },
          headers,
          isLoading,
          onConfirmDelete: handleDelete,
          tokens: transferTokens,
          tokenType: TRANSFER_TOKEN_TYPE
        }
      ),
      canCreate && transferTokens.length === 0 ? (0, import_jsx_runtime.jsx)(
        EmptyStateLayout,
        {
          action: (0, import_jsx_runtime.jsx)(
            LinkButton,
            {
              tag: Link,
              variant: "secondary",
              startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
              to: "/settings/transfer-tokens/create",
              children: formatMessage({
                id: "Settings.transferTokens.addNewToken",
                defaultMessage: "Add new Transfer Token"
              })
            }
          ),
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: "16rem" }),
          content: formatMessage({
            id: "Settings.transferTokens.addFirstToken",
            defaultMessage: "Add your first Transfer Token"
          })
        }
      ) : null,
      !canCreate && transferTokens.length === 0 ? (0, import_jsx_runtime.jsx)(
        EmptyStateLayout,
        {
          icon: (0, import_jsx_runtime.jsx)(ForwardRef$J, { width: "16rem" }),
          content: formatMessage({
            id: "Settings.transferTokens.emptyStateLayout",
            defaultMessage: "You don’t have any content yet..."
          })
        }
      ) : null
    ] }) })
  ] });
};
var ProtectedListView = () => {
  const permissions = useTypedSelector(
    (state) => {
      var _a;
      return (_a = state.admin_app.permissions.settings) == null ? void 0 : _a["transfer-tokens"].main;
    }
  );
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions, children: (0, import_jsx_runtime.jsx)(ListView, {}) });
};
export {
  ListView,
  ProtectedListView
};
//# sourceMappingURL=ListView-cbl5ESj2-TBC25HIB.js.map
