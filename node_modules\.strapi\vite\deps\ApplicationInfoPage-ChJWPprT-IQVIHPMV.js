import {
  selectAdminPermissions
} from "./chunk-4SE2T7NO.js";
import {
  Layouts,
  useEnterprise
} from "./chunk-ELTZWS66.js";
import {
  AxiosError,
  Page,
  axios_default,
  useAppInfo,
  useConfiguration,
  useRBAC,
  useTracking
} from "./chunk-4C2ZQ5OG.js";
import {
  $c512c27ab02ef895$export$fd42f52fd3ae1109,
  Box,
  Button,
  Card,
  CardAsset,
  CardBadge,
  CardBody,
  CardContent,
  CardHeader,
  CardSubtitle,
  CardTitle,
  CarouselActions,
  CarouselInput,
  CarouselSlide,
  Field,
  Flex,
  Grid,
  IconButton,
  Link,
  Modal,
  Tabs,
  TextInput,
  Typography,
  useIntl,
  useSelector
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1b,
  ForwardRef$1d,
  ForwardRef$3t,
  ForwardRef$4p,
  ForwardRef$51
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __publicField,
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/admin/dist/admin/ApplicationInfoPage-ChJWPprT.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var DIMENSION = 750;
var SIZE = 100;
var ACCEPTED_FORMAT = ["image/jpeg", "image/png", "image/svg+xml"];
var FILE_FORMAT_ERROR_MESSAGE = {
  id: "Settings.application.customization.modal.upload.error-format",
  defaultMessage: "Wrong format uploaded (accepted formats only: jpeg, jpg, png, svg)."
};
var FILE_SIZING_ERROR_MESSAGE = {
  id: "Settings.application.customization.modal.upload.error-size",
  defaultMessage: "The file uploaded is too large (max dimension: {dimension}x{dimension}, max file size: {size}KB)"
};
var parseFileMetadatas = async (file) => {
  const isFormatAuthorized = ACCEPTED_FORMAT.includes(file.type);
  if (!isFormatAuthorized) {
    throw new ParsingFileError("File format", FILE_FORMAT_ERROR_MESSAGE);
  }
  const fileDimensions = await new Promise((resolve) => {
    const reader = new FileReader();
    reader.onload = () => {
      const img = new Image();
      img.onload = () => {
        resolve({ width: img.width, height: img.height });
      };
      img.src = reader.result;
    };
    reader.readAsDataURL(file);
  });
  const areDimensionsAuthorized = fileDimensions.width <= DIMENSION && fileDimensions.height <= DIMENSION;
  if (!areDimensionsAuthorized) {
    throw new ParsingFileError("File sizing", FILE_SIZING_ERROR_MESSAGE);
  }
  const asset = {
    ext: file.name.split(".").pop(),
    size: file.size / 1e3,
    name: file.name,
    url: URL.createObjectURL(file),
    rawFile: file,
    width: fileDimensions.width,
    height: fileDimensions.height
  };
  const isSizeAuthorized = asset.size <= SIZE;
  if (!isSizeAuthorized) {
    throw new ParsingFileError("File sizing", FILE_SIZING_ERROR_MESSAGE);
  }
  return asset;
};
var ParsingFileError = class extends Error {
  constructor(message, displayMessage, options) {
    super(message, options);
    __publicField(this, "displayMessage");
    this.displayMessage = displayMessage;
  }
};
var [LogoInputContextProvider, useLogoInputContext] = $c512c27ab02ef895$export$fd42f52fd3ae1109("LogoInput");
var LogoInput = ({
  canUpdate,
  customLogo,
  defaultLogo,
  hint,
  label,
  onChangeLogo
}) => {
  const [localImage, setLocalImage] = React.useState();
  const [currentStep, setCurrentStep] = React.useState();
  const { formatMessage } = useIntl();
  const handleClose = () => {
    setLocalImage(void 0);
    setCurrentStep(void 0);
  };
  return (0, import_jsx_runtime.jsx)(
    Modal.Root,
    {
      open: !!currentStep,
      onOpenChange: (state) => {
        if (state === false) {
          handleClose();
        }
      },
      children: (0, import_jsx_runtime.jsxs)(
        LogoInputContextProvider,
        {
          setLocalImage,
          localImage,
          goToStep: setCurrentStep,
          onClose: handleClose,
          children: [
            (0, import_jsx_runtime.jsx)(
              CarouselInput,
              {
                label,
                selectedSlide: 0,
                hint,
                previousLabel: "",
                nextLabel: "",
                onNext: () => {
                },
                onPrevious: () => {
                },
                secondaryLabel: (customLogo == null ? void 0 : customLogo.name) || "logo.png",
                actions: (0, import_jsx_runtime.jsxs)(CarouselActions, { children: [
                  (0, import_jsx_runtime.jsx)(Modal.Trigger, { children: (0, import_jsx_runtime.jsx)(
                    IconButton,
                    {
                      disabled: !canUpdate,
                      onClick: () => setCurrentStep("upload"),
                      label: formatMessage({
                        id: "Settings.application.customization.carousel.change-action",
                        defaultMessage: "Change logo"
                      }),
                      children: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {})
                    }
                  ) }),
                  (customLogo == null ? void 0 : customLogo.url) && (0, import_jsx_runtime.jsx)(
                    IconButton,
                    {
                      disabled: !canUpdate,
                      onClick: () => onChangeLogo(null),
                      label: formatMessage({
                        id: "Settings.application.customization.carousel.reset-action",
                        defaultMessage: "Reset logo"
                      }),
                      children: (0, import_jsx_runtime.jsx)(ForwardRef$51, {})
                    }
                  )
                ] }),
                children: (0, import_jsx_runtime.jsx)(
                  CarouselSlide,
                  {
                    label: formatMessage({
                      id: "Settings.application.customization.carousel-slide.label",
                      defaultMessage: "Logo slide"
                    }),
                    children: (0, import_jsx_runtime.jsx)(
                      Box,
                      {
                        maxHeight: "40%",
                        maxWidth: "40%",
                        tag: "img",
                        src: (customLogo == null ? void 0 : customLogo.url) || defaultLogo,
                        alt: formatMessage({
                          id: "Settings.application.customization.carousel.title",
                          defaultMessage: "Logo"
                        })
                      }
                    )
                  }
                )
              }
            ),
            (0, import_jsx_runtime.jsxs)(Modal.Content, { children: [
              (0, import_jsx_runtime.jsx)(Modal.Header, { children: (0, import_jsx_runtime.jsx)(Modal.Title, { children: formatMessage(
                currentStep === "upload" ? {
                  id: "Settings.application.customization.modal.upload",
                  defaultMessage: "Upload logo"
                } : {
                  id: "Settings.application.customization.modal.pending",
                  defaultMessage: "Pending logo"
                }
              ) }) }),
              currentStep === "upload" ? (0, import_jsx_runtime.jsx)(AddLogoDialog, {}) : (0, import_jsx_runtime.jsx)(PendingLogoDialog, { onChangeLogo })
            ] })
          ]
        }
      )
    }
  );
};
var AddLogoDialog = () => {
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Tabs.Root, { variant: "simple", defaultValue: "computer", children: [
    (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 8, paddingRight: 8, children: (0, import_jsx_runtime.jsxs)(
      Tabs.List,
      {
        "aria-label": formatMessage({
          id: "Settings.application.customization.modal.tab.label",
          defaultMessage: "How do you want to upload your assets?"
        }),
        children: [
          (0, import_jsx_runtime.jsx)(Tabs.Trigger, { value: "computer", children: formatMessage({
            id: "Settings.application.customization.modal.upload.from-computer",
            defaultMessage: "From computer"
          }) }),
          (0, import_jsx_runtime.jsx)(Tabs.Trigger, { value: "url", children: formatMessage({
            id: "Settings.application.customization.modal.upload.from-url",
            defaultMessage: "From url"
          }) })
        ]
      }
    ) }),
    (0, import_jsx_runtime.jsx)(Tabs.Content, { value: "computer", children: (0, import_jsx_runtime.jsx)(ComputerForm, {}) }),
    (0, import_jsx_runtime.jsx)(Tabs.Content, { value: "url", children: (0, import_jsx_runtime.jsx)(URLForm, {}) })
  ] });
};
var URLForm = () => {
  const { formatMessage } = useIntl();
  const [logoUrl, setLogoUrl] = React.useState("");
  const [error, setError] = React.useState();
  const { setLocalImage, goToStep, onClose } = useLogoInputContext("URLForm");
  const handleChange = (e) => {
    setLogoUrl(e.target.value);
  };
  const handleSubmit = async (event) => {
    event.preventDefault();
    const data = new FormData(event.target);
    const url = data.get("logo-url");
    if (!url) {
      return;
    }
    try {
      const res = await axios_default.get(url.toString(), { responseType: "blob", timeout: 8e3 });
      const file = new File([res.data], res.config.url ?? "", {
        type: res.headers["content-type"]
      });
      const asset = await parseFileMetadatas(file);
      setLocalImage(asset);
      goToStep("pending");
    } catch (err) {
      if (err instanceof AxiosError) {
        setError(
          formatMessage({
            id: "Settings.application.customization.modal.upload.error-network",
            defaultMessage: "Network error"
          })
        );
      } else if (err instanceof ParsingFileError) {
        setError(formatMessage(err.displayMessage, { size: SIZE, dimension: DIMENSION }));
      } else {
        throw err;
      }
    }
  };
  return (0, import_jsx_runtime.jsxs)("form", { onSubmit: handleSubmit, children: [
    (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 8, paddingRight: 8, paddingTop: 6, paddingBottom: 6, children: (0, import_jsx_runtime.jsxs)(Field.Root, { error, name: "logo-url", children: [
      (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
        id: "Settings.application.customization.modal.upload.from-url.input-label",
        defaultMessage: "URL"
      }) }),
      (0, import_jsx_runtime.jsx)(TextInput, { onChange: handleChange, value: logoUrl }),
      (0, import_jsx_runtime.jsx)(Field.Error, {})
    ] }) }),
    (0, import_jsx_runtime.jsxs)(Modal.Footer, { children: [
      (0, import_jsx_runtime.jsx)(Button, { onClick: onClose, variant: "tertiary", children: formatMessage({ id: "app.components.Button.cancel", defaultMessage: "Cancel" }) }),
      (0, import_jsx_runtime.jsx)(Button, { type: "submit", children: formatMessage({
        id: "Settings.application.customization.modal.upload.next",
        defaultMessage: "Next"
      }) })
    ] })
  ] });
};
var ComputerForm = () => {
  const { formatMessage } = useIntl();
  const [dragOver, setDragOver] = React.useState(false);
  const [fileError, setFileError] = React.useState();
  const inputRef = React.useRef(null);
  const id = React.useId();
  const { setLocalImage, goToStep, onClose } = useLogoInputContext("ComputerForm");
  const handleDragEnter = () => {
    setDragOver(true);
  };
  const handleDragLeave = () => {
    setDragOver(false);
  };
  const handleClick = (e) => {
    e.preventDefault();
    inputRef.current.click();
  };
  const handleChange = async () => {
    handleDragLeave();
    if (!inputRef.current.files) {
      return;
    }
    const [file] = inputRef.current.files;
    try {
      const asset = await parseFileMetadatas(file);
      setLocalImage(asset);
      goToStep("pending");
    } catch (err) {
      if (err instanceof ParsingFileError) {
        setFileError(formatMessage(err.displayMessage, { size: SIZE, dimension: DIMENSION }));
        inputRef.current.focus();
      } else {
        throw err;
      }
    }
  };
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)("form", { children: (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 8, paddingRight: 8, paddingTop: 6, paddingBottom: 6, children: (0, import_jsx_runtime.jsx)(Field.Root, { name: id, error: fileError, children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 2, children: [
      (0, import_jsx_runtime.jsxs)(
        Flex,
        {
          paddingTop: 9,
          paddingBottom: 7,
          hasRadius: true,
          justifyContent: "center",
          direction: "column",
          background: dragOver ? "primary100" : "neutral100",
          borderColor: dragOver ? "primary500" : fileError ? "danger600" : "neutral300",
          borderStyle: "dashed",
          borderWidth: "1px",
          position: "relative",
          onDragEnter: handleDragEnter,
          onDragLeave: handleDragLeave,
          children: [
            (0, import_jsx_runtime.jsx)(ForwardRef$1b, { fill: "primary600", width: "6rem", height: "6rem", "aria-hidden": true }),
            (0, import_jsx_runtime.jsx)(Box, { paddingTop: 3, paddingBottom: 5, children: (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "label", htmlFor: id, children: formatMessage({
              id: "Settings.application.customization.modal.upload.drag-drop",
              defaultMessage: "Drag and Drop here or"
            }) }) }),
            (0, import_jsx_runtime.jsx)(Box, { position: "relative", children: (0, import_jsx_runtime.jsx)(
              FileInput,
              {
                accept: ACCEPTED_FORMAT.join(", "),
                type: "file",
                name: "files",
                tabIndex: -1,
                onChange: handleChange,
                ref: inputRef,
                id
              }
            ) }),
            (0, import_jsx_runtime.jsx)(Button, { type: "button", onClick: handleClick, children: formatMessage({
              id: "Settings.application.customization.modal.upload.cta.browse",
              defaultMessage: "Browse files"
            }) }),
            (0, import_jsx_runtime.jsx)(Box, { paddingTop: 6, children: (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", textColor: "neutral600", children: formatMessage(
              {
                id: "Settings.application.customization.modal.upload.file-validation",
                defaultMessage: "Max dimension: {dimension}x{dimension}, Max size: {size}KB"
              },
              { size: SIZE, dimension: DIMENSION }
            ) }) })
          ]
        }
      ),
      (0, import_jsx_runtime.jsx)(Field.Error, {})
    ] }) }) }) }),
    (0, import_jsx_runtime.jsx)(Modal.Footer, { children: (0, import_jsx_runtime.jsx)(Button, { onClick: onClose, variant: "tertiary", children: formatMessage({ id: "app.components.Button.cancel", defaultMessage: "Cancel" }) }) })
  ] });
};
var FileInput = dt(Field.Input)`
  opacity: 0;
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
`;
var PendingLogoDialog = ({ onChangeLogo }) => {
  const { formatMessage } = useIntl();
  const { localImage, setLocalImage, goToStep, onClose } = useLogoInputContext("PendingLogoDialog");
  const handleGoBack = () => {
    setLocalImage(void 0);
    goToStep("upload");
  };
  const handleUpload = () => {
    if (localImage) {
      onChangeLogo(localImage);
    }
    onClose();
  };
  return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
    (0, import_jsx_runtime.jsx)(Modal.Body, { children: (0, import_jsx_runtime.jsxs)(Box, { paddingLeft: 8, paddingRight: 8, paddingTop: 6, paddingBottom: 6, children: [
      (0, import_jsx_runtime.jsxs)(Flex, { justifyContent: "space-between", paddingBottom: 6, children: [
        (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "flex-start", children: [
          (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", fontWeight: "bold", children: formatMessage({
            id: "Settings.application.customization.modal.pending.title",
            defaultMessage: "Logo ready to upload"
          }) }),
          (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", textColor: "neutral500", children: formatMessage({
            id: "Settings.application.customization.modal.pending.subtitle",
            defaultMessage: "Manage the chosen logo before uploading it"
          }) })
        ] }),
        (0, import_jsx_runtime.jsx)(Button, { onClick: handleGoBack, variant: "secondary", children: formatMessage({
          id: "Settings.application.customization.modal.pending.choose-another",
          defaultMessage: "Choose another logo"
        }) })
      ] }),
      (0, import_jsx_runtime.jsx)(Box, { maxWidth: `18rem`, children: (localImage == null ? void 0 : localImage.url) ? (0, import_jsx_runtime.jsx)(ImageCardAsset, { asset: localImage }) : null })
    ] }) }),
    (0, import_jsx_runtime.jsxs)(Modal.Footer, { children: [
      (0, import_jsx_runtime.jsx)(Modal.Close, { children: (0, import_jsx_runtime.jsx)(Button, { onClick: onClose, variant: "tertiary", children: formatMessage({
        id: "Settings.application.customization.modal.cancel",
        defaultMessage: "Cancel"
      }) }) }),
      (0, import_jsx_runtime.jsx)(Button, { onClick: handleUpload, children: formatMessage({
        id: "Settings.application.customization.modal.pending.upload",
        defaultMessage: "Upload logo"
      }) })
    ] })
  ] });
};
var ImageCardAsset = ({ asset }) => {
  var _a;
  const { formatMessage } = useIntl();
  return (0, import_jsx_runtime.jsxs)(Card, { children: [
    (0, import_jsx_runtime.jsx)(CardHeader, { children: (0, import_jsx_runtime.jsx)(CardAsset, { size: "S", src: asset.url }) }),
    (0, import_jsx_runtime.jsxs)(CardBody, { children: [
      (0, import_jsx_runtime.jsxs)(CardContent, { children: [
        (0, import_jsx_runtime.jsx)(CardTitle, { children: asset.name }),
        (0, import_jsx_runtime.jsx)(CardSubtitle, { children: `${(_a = asset.ext) == null ? void 0 : _a.toUpperCase()} - ${asset.width}✕${asset.height}` })
      ] }),
      (0, import_jsx_runtime.jsx)(CardBadge, { children: formatMessage({
        id: "Settings.application.customization.modal.pending.card-badge",
        defaultMessage: "image"
      }) })
    ] })
  ] });
};
var AdminSeatInfoCE = () => null;
var ApplicationInfoPage = () => {
  const { trackUsage } = useTracking();
  const { formatMessage } = useIntl();
  const { logos: serverLogos, updateProjectSettings } = useConfiguration("ApplicationInfoPage");
  const [logos, setLogos] = React.useState({ menu: serverLogos.menu, auth: serverLogos.auth });
  const { settings } = useSelector(selectAdminPermissions);
  const communityEdition = useAppInfo("ApplicationInfoPage", (state) => state.communityEdition);
  const latestStrapiReleaseTag = useAppInfo(
    "ApplicationInfoPage",
    (state) => state.latestStrapiReleaseTag
  );
  const nodeVersion = useAppInfo("ApplicationInfoPage", (state) => state.nodeVersion);
  const shouldUpdateStrapi = useAppInfo("ApplicationInfoPage", (state) => state.shouldUpdateStrapi);
  const strapiVersion = useAppInfo("ApplicationInfoPage", (state) => state.strapiVersion);
  const AdminSeatInfo = useEnterprise(
    AdminSeatInfoCE,
    async () => (await import("./AdminSeatInfo-BtXH5sC--E6R2VU5T.js")).AdminSeatInfoEE
  );
  const {
    allowedActions: { canRead, canUpdate }
  } = useRBAC(settings ? settings["project-settings"] : {});
  const handleSubmit = (e) => {
    e.preventDefault();
    updateProjectSettings({
      authLogo: logos.auth.custom ?? null,
      menuLogo: logos.menu.custom ?? null
    });
  };
  const handleChangeLogo = (logo) => (newLogo) => {
    if (newLogo === null) {
      trackUsage("didClickResetLogo", {
        logo
      });
    }
    setLogos((prev) => ({
      ...prev,
      [logo]: {
        ...prev[logo],
        custom: newLogo
      }
    }));
  };
  React.useEffect(() => {
    setLogos({
      menu: serverLogos.menu,
      auth: serverLogos.auth
    });
  }, [serverLogos]);
  if (!AdminSeatInfo) {
    return null;
  }
  const isSaveDisabled = logos.auth.custom === serverLogos.auth.custom && logos.menu.custom === serverLogos.menu.custom;
  return (0, import_jsx_runtime.jsxs)(Layouts.Root, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      {
        name: formatMessage({
          id: "Settings.application.header",
          defaultMessage: "Application"
        })
      }
    ) }),
    (0, import_jsx_runtime.jsx)(Page.Main, { children: (0, import_jsx_runtime.jsxs)("form", { onSubmit: handleSubmit, children: [
      (0, import_jsx_runtime.jsx)(
        Layouts.Header,
        {
          title: formatMessage({
            id: "Settings.application.title",
            defaultMessage: "Overview"
          }),
          subtitle: formatMessage({
            id: "Settings.application.description",
            defaultMessage: "Administration panel’s global information"
          }),
          primaryAction: canUpdate && (0, import_jsx_runtime.jsx)(Button, { disabled: isSaveDisabled, type: "submit", startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}), children: formatMessage({ id: "global.save", defaultMessage: "Save" }) })
        }
      ),
      (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 6, children: [
        (0, import_jsx_runtime.jsxs)(
          Flex,
          {
            direction: "column",
            alignItems: "stretch",
            gap: 4,
            hasRadius: true,
            background: "neutral0",
            shadow: "tableShadow",
            paddingTop: 6,
            paddingBottom: 6,
            paddingRight: 7,
            paddingLeft: 7,
            children: [
              (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h3", children: formatMessage({
                id: "global.details",
                defaultMessage: "Details"
              }) }),
              (0, import_jsx_runtime.jsxs)(Grid.Root, { gap: 5, tag: "dl", children: [
                (0, import_jsx_runtime.jsxs)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "start", children: [
                  (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", tag: "dt", children: formatMessage({
                    id: "Settings.application.strapiVersion",
                    defaultMessage: "strapi version"
                  }) }),
                  (0, import_jsx_runtime.jsxs)(Flex, { gap: 3, direction: "column", alignItems: "start", tag: "dd", children: [
                    (0, import_jsx_runtime.jsxs)(Typography, { children: [
                      "v",
                      strapiVersion
                    ] }),
                    shouldUpdateStrapi && (0, import_jsx_runtime.jsx)(
                      Link,
                      {
                        href: `https://github.com/strapi/strapi/releases/tag/${latestStrapiReleaseTag}`,
                        endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3t, {}),
                        children: formatMessage({
                          id: "Settings.application.link-upgrade",
                          defaultMessage: "Upgrade your admin panel"
                        })
                      }
                    )
                  ] })
                ] }),
                (0, import_jsx_runtime.jsxs)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "start", children: [
                  (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", tag: "dt", children: formatMessage({
                    id: "Settings.application.edition-title",
                    defaultMessage: "current plan"
                  }) }),
                  (0, import_jsx_runtime.jsxs)(Flex, { gap: 3, direction: "column", alignItems: "start", tag: "dd", children: [
                    (0, import_jsx_runtime.jsx)(Typography, { children: formatMessage(
                      {
                        id: "Settings.application.ee-or-ce",
                        defaultMessage: "{communityEdition, select, true {Community Edition} other {Enterprise Edition}}"
                      },
                      { communityEdition }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Link, { href: "https://strapi.io/pricing-self-hosted", endIcon: (0, import_jsx_runtime.jsx)(ForwardRef$3t, {}), children: formatMessage({
                      id: "Settings.application.link-pricing",
                      defaultMessage: "See all pricing plans"
                    }) })
                  ] })
                ] }),
                (0, import_jsx_runtime.jsxs)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "start", children: [
                  (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", tag: "dt", children: formatMessage({
                    id: "Settings.application.node-version",
                    defaultMessage: "node version"
                  }) }),
                  (0, import_jsx_runtime.jsx)(Typography, { tag: "dd", children: nodeVersion })
                ] }),
                (0, import_jsx_runtime.jsx)(AdminSeatInfo, {})
              ] })
            ]
          }
        ),
        canRead && (0, import_jsx_runtime.jsxs)(
          Box,
          {
            hasRadius: true,
            background: "neutral0",
            shadow: "tableShadow",
            paddingTop: 6,
            paddingBottom: 6,
            paddingRight: 7,
            paddingLeft: 7,
            children: [
              (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h3", children: formatMessage({
                id: "Settings.application.customization",
                defaultMessage: "Customization"
              }) }),
              (0, import_jsx_runtime.jsx)(Typography, { variant: "pi", textColor: "neutral600", children: formatMessage(
                {
                  id: "Settings.application.customization.size-details",
                  defaultMessage: "Max dimension: {dimension}×{dimension}, Max file size: {size}KB"
                },
                { dimension: DIMENSION, size: SIZE }
              ) }),
              (0, import_jsx_runtime.jsxs)(Grid.Root, { paddingTop: 4, gap: 4, children: [
                (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(
                  LogoInput,
                  {
                    canUpdate,
                    customLogo: logos.menu.custom,
                    defaultLogo: logos.menu.default,
                    hint: formatMessage({
                      id: "Settings.application.customization.menu-logo.carousel-hint",
                      defaultMessage: "Replace the logo in the main navigation"
                    }),
                    label: formatMessage({
                      id: "Settings.application.customization.carousel.menu-logo.title",
                      defaultMessage: "Menu logo"
                    }),
                    onChangeLogo: handleChangeLogo("menu")
                  }
                ) }),
                (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsx)(
                  LogoInput,
                  {
                    canUpdate,
                    customLogo: logos.auth.custom,
                    defaultLogo: logos.auth.default,
                    hint: formatMessage({
                      id: "Settings.application.customization.auth-logo.carousel-hint",
                      defaultMessage: "Replace the logo in the authentication pages"
                    }),
                    label: formatMessage({
                      id: "Settings.application.customization.carousel.auth-logo.title",
                      defaultMessage: "Auth logo"
                    }),
                    onChangeLogo: handleChangeLogo("auth")
                  }
                ) })
              ] })
            ]
          }
        )
      ] }) })
    ] }) })
  ] });
};
export {
  ApplicationInfoPage
};
//# sourceMappingURL=ApplicationInfoPage-ChJWPprT-IQVIHPMV.js.map
