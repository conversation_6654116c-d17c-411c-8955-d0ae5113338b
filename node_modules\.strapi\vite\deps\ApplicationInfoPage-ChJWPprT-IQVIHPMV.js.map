{"version": 3, "sources": ["../../../@strapi/admin/admin/src/pages/Settings/pages/ApplicationInfo/utils/constants.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApplicationInfo/utils/files.ts", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApplicationInfo/components/LogoInput.tsx", "../../../@strapi/admin/admin/src/pages/Settings/pages/ApplicationInfo/ApplicationInfoPage.tsx"], "sourcesContent": ["export const DIMENSION = 750;\nexport const SIZE = 100;\nexport const ACCEPTED_FORMAT = ['image/jpeg', 'image/png', 'image/svg+xml'];\n", "import { ACCEPTED_FORMAT, DIMENSION, SIZE } from './constants';\n\nimport type { MessageDescriptor } from 'react-intl';\n\nconst FILE_FORMAT_ERROR_MESSAGE = {\n  id: 'Settings.application.customization.modal.upload.error-format',\n  defaultMessage: 'Wrong format uploaded (accepted formats only: jpeg, jpg, png, svg).',\n};\n\nconst FILE_SIZING_ERROR_MESSAGE = {\n  id: 'Settings.application.customization.modal.upload.error-size',\n  defaultMessage:\n    'The file uploaded is too large (max dimension: {dimension}x{dimension}, max file size: {size}KB)',\n};\n\ninterface ImageDimensions {\n  height: number;\n  width: number;\n}\n\ninterface ImageAsset extends ImageDimensions {\n  ext: string | undefined;\n  size: number;\n  name: string;\n  url: string;\n  rawFile: File;\n}\n\nconst parseFileMetadatas = async (file: File): Promise<ImageAsset> => {\n  const isFormatAuthorized = ACCEPTED_FORMAT.includes(file.type);\n\n  if (!isFormatAuthorized) {\n    throw new ParsingFileError('File format', FILE_FORMAT_ERROR_MESSAGE);\n  }\n\n  const fileDimensions = await new Promise<ImageDimensions>((resolve) => {\n    const reader = new FileReader();\n    reader.onload = () => {\n      const img = new Image();\n      img.onload = () => {\n        resolve({ width: img.width, height: img.height });\n      };\n      img.src = reader.result as string;\n    };\n    reader.readAsDataURL(file);\n  });\n\n  const areDimensionsAuthorized =\n    fileDimensions.width <= DIMENSION && fileDimensions.height <= DIMENSION;\n\n  if (!areDimensionsAuthorized) {\n    throw new ParsingFileError('File sizing', FILE_SIZING_ERROR_MESSAGE);\n  }\n\n  const asset = {\n    ext: file.name.split('.').pop(),\n    size: file.size / 1000,\n    name: file.name,\n    url: URL.createObjectURL(file),\n    rawFile: file,\n    width: fileDimensions.width,\n    height: fileDimensions.height,\n  };\n\n  const isSizeAuthorized = asset.size <= SIZE;\n\n  if (!isSizeAuthorized) {\n    throw new ParsingFileError('File sizing', FILE_SIZING_ERROR_MESSAGE);\n  }\n\n  return asset;\n};\n\nclass ParsingFileError extends Error {\n  displayMessage: MessageDescriptor;\n\n  constructor(message: string, displayMessage: MessageDescriptor, options?: ErrorOptions) {\n    super(message, options);\n    this.displayMessage = displayMessage;\n  }\n}\n\nexport { parseFileMetadatas, ParsingFileError };\nexport type { ImageAsset };\n", "import * as React from 'react';\n\nimport { createContext } from '@radix-ui/react-context';\nimport {\n  Box,\n  Button,\n  ButtonProps,\n  Card,\n  CardAsset,\n  CardBadge,\n  CardBody,\n  CardContent,\n  CardHeader,\n  CardSubtitle,\n  CardTitle,\n  CarouselActions,\n  CarouselInput,\n  CarouselInputProps,\n  CarouselSlide,\n  Field,\n  Flex,\n  IconButton,\n  Modal,\n  Tabs,\n  TextInput,\n  TextInputProps,\n  Typography,\n} from '@strapi/design-system';\nimport { PlusCircle, Plus, ArrowClockwise } from '@strapi/icons';\nimport axios, { AxiosError } from 'axios';\nimport { useIntl } from 'react-intl';\nimport { styled } from 'styled-components';\n\nimport { ConfigurationContextValue } from '../../../../../features/Configuration';\nimport { ACCEPTED_FORMAT, DIMENSION, SIZE } from '../utils/constants';\nimport { ImageAsset, ParsingFileError, parseFileMetadatas } from '../utils/files';\n\n/* -------------------------------------------------------------------------------------------------\n * LogoInputContext\n * -----------------------------------------------------------------------------------------------*/\n\ninterface LogoInputContextValue {\n  localImage: ImageAsset | undefined;\n  goToStep: (step: Step) => void;\n  onClose: () => void;\n  setLocalImage: (asset: ImageAsset | undefined) => void;\n}\n\nconst [LogoInputContextProvider, useLogoInputContext] =\n  createContext<LogoInputContextValue>('LogoInput');\n\n/* -------------------------------------------------------------------------------------------------\n * LogoInput\n * -----------------------------------------------------------------------------------------------*/\n\ninterface LogoInputProps\n  extends Pick<PendingLogoDialogProps, 'onChangeLogo'>,\n    Pick<CarouselInputProps, 'label' | 'hint'> {\n  canUpdate: boolean;\n  customLogo?: ConfigurationContextValue['logos']['auth']['custom'];\n  defaultLogo: string;\n}\n\ntype Step = 'pending' | 'upload' | undefined;\n\nconst LogoInput = ({\n  canUpdate,\n  customLogo,\n  defaultLogo,\n  hint,\n  label,\n  onChangeLogo,\n}: LogoInputProps) => {\n  const [localImage, setLocalImage] = React.useState<ImageAsset | undefined>();\n  const [currentStep, setCurrentStep] = React.useState<Step>();\n  const { formatMessage } = useIntl();\n\n  const handleClose = () => {\n    setLocalImage(undefined);\n    setCurrentStep(undefined);\n  };\n\n  return (\n    <Modal.Root\n      open={!!currentStep}\n      onOpenChange={(state) => {\n        if (state === false) {\n          handleClose();\n        }\n      }}\n    >\n      <LogoInputContextProvider\n        setLocalImage={setLocalImage}\n        localImage={localImage}\n        goToStep={setCurrentStep}\n        onClose={handleClose}\n      >\n        <CarouselInput\n          label={label}\n          selectedSlide={0}\n          hint={hint}\n          // Carousel is used here for a single media,\n          // we don't need previous and next labels but these props are required\n          previousLabel=\"\"\n          nextLabel=\"\"\n          onNext={() => {}}\n          onPrevious={() => {}}\n          secondaryLabel={customLogo?.name || 'logo.png'}\n          actions={\n            <CarouselActions>\n              <Modal.Trigger>\n                <IconButton\n                  disabled={!canUpdate}\n                  onClick={() => setCurrentStep('upload')}\n                  label={formatMessage({\n                    id: 'Settings.application.customization.carousel.change-action',\n                    defaultMessage: 'Change logo',\n                  })}\n                >\n                  <Plus />\n                </IconButton>\n              </Modal.Trigger>\n              {customLogo?.url && (\n                <IconButton\n                  disabled={!canUpdate}\n                  onClick={() => onChangeLogo(null)}\n                  label={formatMessage({\n                    id: 'Settings.application.customization.carousel.reset-action',\n                    defaultMessage: 'Reset logo',\n                  })}\n                >\n                  <ArrowClockwise />\n                </IconButton>\n              )}\n            </CarouselActions>\n          }\n        >\n          <CarouselSlide\n            label={formatMessage({\n              id: 'Settings.application.customization.carousel-slide.label',\n              defaultMessage: 'Logo slide',\n            })}\n          >\n            <Box\n              maxHeight=\"40%\"\n              maxWidth=\"40%\"\n              tag=\"img\"\n              src={customLogo?.url || defaultLogo}\n              alt={formatMessage({\n                id: 'Settings.application.customization.carousel.title',\n                defaultMessage: 'Logo',\n              })}\n            />\n          </CarouselSlide>\n        </CarouselInput>\n        <Modal.Content>\n          <Modal.Header>\n            <Modal.Title>\n              {formatMessage(\n                currentStep === 'upload'\n                  ? {\n                      id: 'Settings.application.customization.modal.upload',\n                      defaultMessage: 'Upload logo',\n                    }\n                  : {\n                      id: 'Settings.application.customization.modal.pending',\n                      defaultMessage: 'Pending logo',\n                    }\n              )}\n            </Modal.Title>\n          </Modal.Header>\n          {currentStep === 'upload' ? (\n            <AddLogoDialog />\n          ) : (\n            <PendingLogoDialog onChangeLogo={onChangeLogo} />\n          )}\n        </Modal.Content>\n      </LogoInputContextProvider>\n    </Modal.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * AddLogoDialog\n * -----------------------------------------------------------------------------------------------*/\n\nconst AddLogoDialog = () => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Tabs.Root variant=\"simple\" defaultValue=\"computer\">\n      <Box paddingLeft={8} paddingRight={8}>\n        <Tabs.List\n          aria-label={formatMessage({\n            id: 'Settings.application.customization.modal.tab.label',\n            defaultMessage: 'How do you want to upload your assets?',\n          })}\n        >\n          <Tabs.Trigger value=\"computer\">\n            {formatMessage({\n              id: 'Settings.application.customization.modal.upload.from-computer',\n              defaultMessage: 'From computer',\n            })}\n          </Tabs.Trigger>\n          <Tabs.Trigger value=\"url\">\n            {formatMessage({\n              id: 'Settings.application.customization.modal.upload.from-url',\n              defaultMessage: 'From url',\n            })}\n          </Tabs.Trigger>\n        </Tabs.List>\n      </Box>\n      <Tabs.Content value=\"computer\">\n        <ComputerForm />\n      </Tabs.Content>\n      <Tabs.Content value=\"url\">\n        <URLForm />\n      </Tabs.Content>\n    </Tabs.Root>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * URLForm\n * -----------------------------------------------------------------------------------------------*/\n\nconst URLForm = () => {\n  const { formatMessage } = useIntl();\n  const [logoUrl, setLogoUrl] = React.useState('');\n  const [error, setError] = React.useState<string>();\n  const { setLocalImage, goToStep, onClose } = useLogoInputContext('URLForm');\n\n  const handleChange: TextInputProps['onChange'] = (e) => {\n    setLogoUrl(e.target.value);\n  };\n\n  const handleSubmit: React.FormEventHandler<HTMLFormElement> = async (event) => {\n    event.preventDefault();\n\n    const data = new FormData(event.target as HTMLFormElement);\n\n    const url = data.get('logo-url');\n\n    if (!url) {\n      return;\n    }\n\n    try {\n      const res = await axios.get(url.toString(), { responseType: 'blob', timeout: 8000 });\n\n      const file = new File([res.data], res.config.url ?? '', {\n        type: res.headers['content-type'],\n      });\n\n      const asset = await parseFileMetadatas(file);\n\n      setLocalImage(asset);\n      goToStep('pending');\n    } catch (err) {\n      if (err instanceof AxiosError) {\n        setError(\n          formatMessage({\n            id: 'Settings.application.customization.modal.upload.error-network',\n            defaultMessage: 'Network error',\n          })\n        );\n      } else if (err instanceof ParsingFileError) {\n        setError(formatMessage(err.displayMessage, { size: SIZE, dimension: DIMENSION }));\n      } else {\n        throw err;\n      }\n    }\n  };\n\n  return (\n    <form onSubmit={handleSubmit}>\n      <Box paddingLeft={8} paddingRight={8} paddingTop={6} paddingBottom={6}>\n        <Field.Root error={error} name=\"logo-url\">\n          <Field.Label>\n            {formatMessage({\n              id: 'Settings.application.customization.modal.upload.from-url.input-label',\n              defaultMessage: 'URL',\n            })}\n          </Field.Label>\n          <TextInput onChange={handleChange} value={logoUrl} />\n          <Field.Error />\n        </Field.Root>\n      </Box>\n      <Modal.Footer>\n        <Button onClick={onClose} variant=\"tertiary\">\n          {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n        </Button>\n        <Button type=\"submit\">\n          {formatMessage({\n            id: 'Settings.application.customization.modal.upload.next',\n            defaultMessage: 'Next',\n          })}\n        </Button>\n      </Modal.Footer>\n    </form>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ComputerForm\n * -----------------------------------------------------------------------------------------------*/\n\nconst ComputerForm = () => {\n  const { formatMessage } = useIntl();\n  const [dragOver, setDragOver] = React.useState(false);\n  const [fileError, setFileError] = React.useState<string>();\n  const inputRef = React.useRef<HTMLInputElement>(null!);\n  const id = React.useId();\n\n  const { setLocalImage, goToStep, onClose } = useLogoInputContext('ComputerForm');\n\n  const handleDragEnter = () => {\n    setDragOver(true);\n  };\n  const handleDragLeave = () => {\n    setDragOver(false);\n  };\n\n  const handleClick: ButtonProps['onClick'] = (e) => {\n    e.preventDefault();\n    inputRef.current.click();\n  };\n\n  const handleChange = async () => {\n    handleDragLeave();\n\n    if (!inputRef.current.files) {\n      return;\n    }\n\n    const [file] = inputRef.current.files;\n\n    try {\n      const asset = await parseFileMetadatas(file);\n      setLocalImage(asset);\n      goToStep('pending');\n    } catch (err) {\n      if (err instanceof ParsingFileError) {\n        setFileError(formatMessage(err.displayMessage, { size: SIZE, dimension: DIMENSION }));\n        inputRef.current.focus();\n      } else {\n        throw err;\n      }\n    }\n  };\n\n  return (\n    <>\n      <form>\n        <Box paddingLeft={8} paddingRight={8} paddingTop={6} paddingBottom={6}>\n          <Field.Root name={id} error={fileError}>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={2}>\n              <Flex\n                paddingTop={9}\n                paddingBottom={7}\n                hasRadius\n                justifyContent=\"center\"\n                direction=\"column\"\n                background={dragOver ? 'primary100' : 'neutral100'}\n                borderColor={dragOver ? 'primary500' : fileError ? 'danger600' : 'neutral300'}\n                borderStyle=\"dashed\"\n                borderWidth=\"1px\"\n                position=\"relative\"\n                onDragEnter={handleDragEnter}\n                onDragLeave={handleDragLeave}\n              >\n                <PlusCircle fill=\"primary600\" width=\"6rem\" height=\"6rem\" aria-hidden />\n                <Box paddingTop={3} paddingBottom={5}>\n                  <Typography variant=\"delta\" tag=\"label\" htmlFor={id}>\n                    {formatMessage({\n                      id: 'Settings.application.customization.modal.upload.drag-drop',\n                      defaultMessage: 'Drag and Drop here or',\n                    })}\n                  </Typography>\n                </Box>\n                <Box position=\"relative\">\n                  <FileInput\n                    accept={ACCEPTED_FORMAT.join(', ')}\n                    type=\"file\"\n                    name=\"files\"\n                    tabIndex={-1}\n                    onChange={handleChange}\n                    ref={inputRef}\n                    id={id}\n                  />\n                </Box>\n                <Button type=\"button\" onClick={handleClick}>\n                  {formatMessage({\n                    id: 'Settings.application.customization.modal.upload.cta.browse',\n                    defaultMessage: 'Browse files',\n                  })}\n                </Button>\n                <Box paddingTop={6}>\n                  <Typography variant=\"pi\" textColor=\"neutral600\">\n                    {formatMessage(\n                      {\n                        id: 'Settings.application.customization.modal.upload.file-validation',\n                        defaultMessage:\n                          'Max dimension: {dimension}x{dimension}, Max size: {size}KB',\n                      },\n                      { size: SIZE, dimension: DIMENSION }\n                    )}\n                  </Typography>\n                </Box>\n              </Flex>\n              <Field.Error />\n            </Flex>\n          </Field.Root>\n        </Box>\n      </form>\n      <Modal.Footer>\n        <Button onClick={onClose} variant=\"tertiary\">\n          {formatMessage({ id: 'app.components.Button.cancel', defaultMessage: 'Cancel' })}\n        </Button>\n      </Modal.Footer>\n    </>\n  );\n};\n\nconst FileInput = styled(Field.Input)`\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  bottom: 0;\n  left: 0;\n  right: 0;\n  z-index: 1;\n`;\n\n/* -------------------------------------------------------------------------------------------------\n * PendingLogoDialog\n * -----------------------------------------------------------------------------------------------*/\n\ninterface PendingLogoDialogProps {\n  onChangeLogo: (file: ImageAsset | null) => void;\n}\n\nconst PendingLogoDialog = ({ onChangeLogo }: PendingLogoDialogProps) => {\n  const { formatMessage } = useIntl();\n  const { localImage, setLocalImage, goToStep, onClose } = useLogoInputContext('PendingLogoDialog');\n\n  const handleGoBack = () => {\n    setLocalImage(undefined);\n    goToStep('upload');\n  };\n\n  const handleUpload = () => {\n    if (localImage) {\n      onChangeLogo(localImage);\n    }\n    onClose();\n  };\n\n  return (\n    <>\n      <Modal.Body>\n        <Box paddingLeft={8} paddingRight={8} paddingTop={6} paddingBottom={6}>\n          <Flex justifyContent=\"space-between\" paddingBottom={6}>\n            <Flex direction=\"column\" alignItems=\"flex-start\">\n              <Typography variant=\"pi\" fontWeight=\"bold\">\n                {formatMessage({\n                  id: 'Settings.application.customization.modal.pending.title',\n                  defaultMessage: 'Logo ready to upload',\n                })}\n              </Typography>\n              <Typography variant=\"pi\" textColor=\"neutral500\">\n                {formatMessage({\n                  id: 'Settings.application.customization.modal.pending.subtitle',\n                  defaultMessage: 'Manage the chosen logo before uploading it',\n                })}\n              </Typography>\n            </Flex>\n            <Button onClick={handleGoBack} variant=\"secondary\">\n              {formatMessage({\n                id: 'Settings.application.customization.modal.pending.choose-another',\n                defaultMessage: 'Choose another logo',\n              })}\n            </Button>\n          </Flex>\n          <Box maxWidth={`18rem`}>\n            {localImage?.url ? <ImageCardAsset asset={localImage} /> : null}\n          </Box>\n        </Box>\n      </Modal.Body>\n      <Modal.Footer>\n        <Modal.Close>\n          <Button onClick={onClose} variant=\"tertiary\">\n            {formatMessage({\n              id: 'Settings.application.customization.modal.cancel',\n              defaultMessage: 'Cancel',\n            })}\n          </Button>\n        </Modal.Close>\n        <Button onClick={handleUpload}>\n          {formatMessage({\n            id: 'Settings.application.customization.modal.pending.upload',\n            defaultMessage: 'Upload logo',\n          })}\n        </Button>\n      </Modal.Footer>\n    </>\n  );\n};\n\n/* -------------------------------------------------------------------------------------------------\n * ImageCardAsset\n * -----------------------------------------------------------------------------------------------*/\n\ninterface ImageCardAssetProps {\n  asset: ImageAsset;\n}\n\nconst ImageCardAsset = ({ asset }: ImageCardAssetProps) => {\n  const { formatMessage } = useIntl();\n\n  return (\n    <Card>\n      <CardHeader>\n        <CardAsset size=\"S\" src={asset.url} />\n      </CardHeader>\n      <CardBody>\n        <CardContent>\n          <CardTitle>{asset.name}</CardTitle>\n          <CardSubtitle>\n            {`${asset.ext?.toUpperCase()} - ${asset.width}✕${asset.height}`}\n          </CardSubtitle>\n        </CardContent>\n        <CardBadge>\n          {formatMessage({\n            id: 'Settings.application.customization.modal.pending.card-badge',\n            defaultMessage: 'image',\n          })}\n        </CardBadge>\n      </CardBody>\n    </Card>\n  );\n};\n\nexport { LogoInput };\nexport type { LogoInputProps };\n", "import * as React from 'react';\n\nimport { Box, Button, Flex, Grid, Link, Typography } from '@strapi/design-system';\nimport { Check, ExternalLink } from '@strapi/icons';\nimport { useIntl } from 'react-intl';\nimport { useSelector } from 'react-redux';\n\nimport { Layouts } from '../../../../components/Layouts/Layout';\nimport { Page } from '../../../../components/PageHelpers';\nimport { useAppInfo } from '../../../../features/AppInfo';\nimport { useConfiguration } from '../../../../features/Configuration';\nimport { useTracking } from '../../../../features/Tracking';\nimport { useEnterprise } from '../../../../hooks/useEnterprise';\nimport { useRBAC } from '../../../../hooks/useRBAC';\nimport { selectAdminPermissions } from '../../../../selectors';\n\nimport { LogoInput, LogoInputProps } from './components/LogoInput';\nimport { DIMENSION, SIZE } from './utils/constants';\n\nconst AdminSeatInfoCE = () => null;\n\n/* -------------------------------------------------------------------------------------------------\n * ApplicationInfoPage\n * -----------------------------------------------------------------------------------------------*/\n\nconst ApplicationInfoPage = () => {\n  const { trackUsage } = useTracking();\n  const { formatMessage } = useIntl();\n  const { logos: serverLogos, updateProjectSettings } = useConfiguration('ApplicationInfoPage');\n  const [logos, setLogos] = React.useState({ menu: serverLogos.menu, auth: serverLogos.auth });\n  const { settings } = useSelector(selectAdminPermissions);\n\n  const communityEdition = useAppInfo('ApplicationInfoPage', (state) => state.communityEdition);\n  const latestStrapiReleaseTag = useAppInfo(\n    'ApplicationInfoPage',\n    (state) => state.latestStrapiReleaseTag\n  );\n  const nodeVersion = useAppInfo('ApplicationInfoPage', (state) => state.nodeVersion);\n  const shouldUpdateStrapi = useAppInfo('ApplicationInfoPage', (state) => state.shouldUpdateStrapi);\n  const strapiVersion = useAppInfo('ApplicationInfoPage', (state) => state.strapiVersion);\n\n  const AdminSeatInfo = useEnterprise(\n    AdminSeatInfoCE,\n    async () =>\n      (\n        await import(\n          '../../../../../../ee/admin/src/pages/SettingsPage/pages/ApplicationInfoPage/components/AdminSeatInfo'\n        )\n      ).AdminSeatInfoEE\n  );\n\n  const {\n    allowedActions: { canRead, canUpdate },\n  } = useRBAC(settings ? settings['project-settings'] : {});\n\n  const handleSubmit: React.FormEventHandler<HTMLFormElement> = (e) => {\n    e.preventDefault();\n\n    updateProjectSettings({\n      authLogo: logos.auth.custom ?? null,\n      menuLogo: logos.menu.custom ?? null,\n    });\n  };\n\n  const handleChangeLogo =\n    (logo: 'menu' | 'auth'): LogoInputProps['onChangeLogo'] =>\n    (newLogo) => {\n      /**\n       * If there's no newLogo value we can assume we're reseting.\n       */\n      if (newLogo === null) {\n        trackUsage('didClickResetLogo', {\n          logo,\n        });\n      }\n\n      setLogos((prev) => ({\n        ...prev,\n        [logo]: {\n          ...prev[logo],\n          custom: newLogo,\n        },\n      }));\n    };\n\n  React.useEffect(() => {\n    setLogos({\n      menu: serverLogos.menu,\n      auth: serverLogos.auth,\n    });\n  }, [serverLogos]);\n\n  // block rendering until the EE component is fully loaded\n  if (!AdminSeatInfo) {\n    return null;\n  }\n\n  const isSaveDisabled =\n    logos.auth.custom === serverLogos.auth.custom && logos.menu.custom === serverLogos.menu.custom;\n\n  return (\n    <Layouts.Root>\n      <Page.Title>\n        {formatMessage(\n          { id: 'Settings.PageTitle', defaultMessage: 'Settings - {name}' },\n          {\n            name: formatMessage({\n              id: 'Settings.application.header',\n              defaultMessage: 'Application',\n            }),\n          }\n        )}\n      </Page.Title>\n      <Page.Main>\n        <form onSubmit={handleSubmit}>\n          <Layouts.Header\n            title={formatMessage({\n              id: 'Settings.application.title',\n              defaultMessage: 'Overview',\n            })}\n            subtitle={formatMessage({\n              id: 'Settings.application.description',\n              defaultMessage: 'Administration panel’s global information',\n            })}\n            primaryAction={\n              canUpdate && (\n                <Button disabled={isSaveDisabled} type=\"submit\" startIcon={<Check />}>\n                  {formatMessage({ id: 'global.save', defaultMessage: 'Save' })}\n                </Button>\n              )\n            }\n          />\n          <Layouts.Content>\n            <Flex direction=\"column\" alignItems=\"stretch\" gap={6}>\n              <Flex\n                direction=\"column\"\n                alignItems=\"stretch\"\n                gap={4}\n                hasRadius\n                background=\"neutral0\"\n                shadow=\"tableShadow\"\n                paddingTop={6}\n                paddingBottom={6}\n                paddingRight={7}\n                paddingLeft={7}\n              >\n                <Typography variant=\"delta\" tag=\"h3\">\n                  {formatMessage({\n                    id: 'global.details',\n                    defaultMessage: 'Details',\n                  })}\n                </Typography>\n\n                <Grid.Root gap={5} tag=\"dl\">\n                  <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"start\">\n                    <Typography variant=\"sigma\" textColor=\"neutral600\" tag=\"dt\">\n                      {formatMessage({\n                        id: 'Settings.application.strapiVersion',\n                        defaultMessage: 'strapi version',\n                      })}\n                    </Typography>\n                    <Flex gap={3} direction=\"column\" alignItems=\"start\" tag=\"dd\">\n                      <Typography>v{strapiVersion}</Typography>\n                      {shouldUpdateStrapi && (\n                        <Link\n                          href={`https://github.com/strapi/strapi/releases/tag/${latestStrapiReleaseTag}`}\n                          endIcon={<ExternalLink />}\n                        >\n                          {formatMessage({\n                            id: 'Settings.application.link-upgrade',\n                            defaultMessage: 'Upgrade your admin panel',\n                          })}\n                        </Link>\n                      )}\n                    </Flex>\n                  </Grid.Item>\n                  <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"start\">\n                    <Typography variant=\"sigma\" textColor=\"neutral600\" tag=\"dt\">\n                      {formatMessage({\n                        id: 'Settings.application.edition-title',\n                        defaultMessage: 'current plan',\n                      })}\n                    </Typography>\n                    <Flex gap={3} direction=\"column\" alignItems=\"start\" tag=\"dd\">\n                      <Typography>\n                        {formatMessage(\n                          {\n                            id: 'Settings.application.ee-or-ce',\n                            defaultMessage:\n                              '{communityEdition, select, true {Community Edition} other {Enterprise Edition}}',\n                          },\n                          { communityEdition }\n                        )}\n                      </Typography>\n                      <Link href=\"https://strapi.io/pricing-self-hosted\" endIcon={<ExternalLink />}>\n                        {formatMessage({\n                          id: 'Settings.application.link-pricing',\n                          defaultMessage: 'See all pricing plans',\n                        })}\n                      </Link>\n                    </Flex>\n                  </Grid.Item>\n\n                  <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"start\">\n                    <Typography variant=\"sigma\" textColor=\"neutral600\" tag=\"dt\">\n                      {formatMessage({\n                        id: 'Settings.application.node-version',\n                        defaultMessage: 'node version',\n                      })}\n                    </Typography>\n                    <Typography tag=\"dd\">{nodeVersion}</Typography>\n                  </Grid.Item>\n                  <AdminSeatInfo />\n                </Grid.Root>\n              </Flex>\n              {canRead && (\n                <Box\n                  hasRadius\n                  background=\"neutral0\"\n                  shadow=\"tableShadow\"\n                  paddingTop={6}\n                  paddingBottom={6}\n                  paddingRight={7}\n                  paddingLeft={7}\n                >\n                  <Typography variant=\"delta\" tag=\"h3\">\n                    {formatMessage({\n                      id: 'Settings.application.customization',\n                      defaultMessage: 'Customization',\n                    })}\n                  </Typography>\n                  <Typography variant=\"pi\" textColor=\"neutral600\">\n                    {formatMessage(\n                      {\n                        id: 'Settings.application.customization.size-details',\n                        defaultMessage:\n                          'Max dimension: {dimension}×{dimension}, Max file size: {size}KB',\n                      },\n                      { dimension: DIMENSION, size: SIZE }\n                    )}\n                  </Typography>\n                  <Grid.Root paddingTop={4} gap={4}>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <LogoInput\n                        canUpdate={canUpdate}\n                        customLogo={logos.menu.custom}\n                        defaultLogo={logos.menu.default}\n                        hint={formatMessage({\n                          id: 'Settings.application.customization.menu-logo.carousel-hint',\n                          defaultMessage: 'Replace the logo in the main navigation',\n                        })}\n                        label={formatMessage({\n                          id: 'Settings.application.customization.carousel.menu-logo.title',\n                          defaultMessage: 'Menu logo',\n                        })}\n                        onChangeLogo={handleChangeLogo('menu')}\n                      />\n                    </Grid.Item>\n                    <Grid.Item col={6} s={12} direction=\"column\" alignItems=\"stretch\">\n                      <LogoInput\n                        canUpdate={canUpdate}\n                        customLogo={logos.auth.custom}\n                        defaultLogo={logos.auth.default}\n                        hint={formatMessage({\n                          id: 'Settings.application.customization.auth-logo.carousel-hint',\n                          defaultMessage: 'Replace the logo in the authentication pages',\n                        })}\n                        label={formatMessage({\n                          id: 'Settings.application.customization.carousel.auth-logo.title',\n                          defaultMessage: 'Auth logo',\n                        })}\n                        onChangeLogo={handleChangeLogo('auth')}\n                      />\n                    </Grid.Item>\n                  </Grid.Root>\n                </Box>\n              )}\n            </Flex>\n          </Layouts.Content>\n        </form>\n      </Page.Main>\n    </Layouts.Root>\n  );\n};\n\nexport { ApplicationInfoPage };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAO,IAAM,YAAY;AAClB,IAAM,OAAO;AACb,IAAM,kBAAkB,CAAC,cAAc,aAAa,eAAe;ACE1E,IAAM,4BAA4B;EAChC,IAAI;EACJ,gBAAgB;AAClB;AAEA,IAAM,4BAA4B;EAChC,IAAI;EACJ,gBACE;AACJ;AAeA,IAAM,qBAAqB,OAAO,SAAoC;AACpE,QAAM,qBAAqB,gBAAgB,SAAS,KAAK,IAAI;AAE7D,MAAI,CAAC,oBAAoB;AACjB,UAAA,IAAI,iBAAiB,eAAe,yBAAyB;EAAA;AAGrE,QAAM,iBAAiB,MAAM,IAAI,QAAyB,CAAC,YAAY;AAC/D,UAAA,SAAS,IAAI,WAAW;AAC9B,WAAO,SAAS,MAAM;AACd,YAAA,MAAM,IAAI,MAAM;AACtB,UAAI,SAAS,MAAM;AACjB,gBAAQ,EAAE,OAAO,IAAI,OAAO,QAAQ,IAAI,OAAA,CAAQ;MAAA;AAElD,UAAI,MAAM,OAAO;IAAA;AAEnB,WAAO,cAAc,IAAI;EAAA,CAC1B;AAED,QAAM,0BACJ,eAAe,SAAS,aAAa,eAAe,UAAU;AAEhE,MAAI,CAAC,yBAAyB;AACtB,UAAA,IAAI,iBAAiB,eAAe,yBAAyB;EAAA;AAGrE,QAAM,QAAQ;IACZ,KAAK,KAAK,KAAK,MAAM,GAAG,EAAE,IAAI;IAC9B,MAAM,KAAK,OAAO;IAClB,MAAM,KAAK;IACX,KAAK,IAAI,gBAAgB,IAAI;IAC7B,SAAS;IACT,OAAO,eAAe;IACtB,QAAQ,eAAe;EAAA;AAGnB,QAAA,mBAAmB,MAAM,QAAQ;AAEvC,MAAI,CAAC,kBAAkB;AACf,UAAA,IAAI,iBAAiB,eAAe,yBAAyB;EAAA;AAG9D,SAAA;AACT;AAEA,IAAM,mBAAN,cAA+B,MAAM;EAGnC,YAAY,SAAiB,gBAAmC,SAAwB;AACtF,UAAM,SAAS,OAAO;AAHxB;AAIE,SAAK,iBAAiB;EAAA;AAE1B;AChCA,IAAM,CAAC,0BAA0B,mBAAmB,IAClD,0CAAqC,WAAW;AAgBlD,IAAM,YAAY,CAAC;EACjB;EACA;EACA;EACA;EACA;EACA;AACF,MAAsB;AACpB,QAAM,CAAC,YAAY,aAAa,IAAU,eAAiC;AAC3E,QAAM,CAAC,aAAa,cAAc,IAAU,eAAe;AACrD,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,QAAM,cAAc,MAAM;AACxB,kBAAc,MAAS;AACvB,mBAAe,MAAS;EAAA;AAIxB,aAAA;IAAC,MAAM;IAAN;MACC,MAAM,CAAC,CAAC;MACR,cAAc,CAAC,UAAU;AACvB,YAAI,UAAU,OAAO;AACP,sBAAA;QAAA;MACd;MAGF,cAAA;QAAC;QAAA;UACC;UACA;UACA,UAAU;UACV,SAAS;UAET,UAAA;gBAAA;cAAC;cAAA;gBACC;gBACA,eAAe;gBACf;gBAGA,eAAc;gBACd,WAAU;gBACV,QAAQ,MAAM;gBAAA;gBACd,YAAY,MAAM;gBAAA;gBAClB,iBAAgB,yCAAY,SAAQ;gBACpC,aAAA,yBACG,iBACC,EAAA,UAAA;sBAAC,wBAAA,MAAM,SAAN,EACC,cAAA;oBAAC;oBAAA;sBACC,UAAU,CAAC;sBACX,SAAS,MAAM,eAAe,QAAQ;sBACtC,OAAO,cAAc;wBACnB,IAAI;wBACJ,gBAAgB;sBAAA,CACjB;sBAED,cAAA,wBAAC,eAAK,CAAA,CAAA;oBAAA;kBAAA,EAEV,CAAA;mBACC,yCAAY,YACX;oBAAC;oBAAA;sBACC,UAAU,CAAC;sBACX,SAAS,MAAM,aAAa,IAAI;sBAChC,OAAO,cAAc;wBACnB,IAAI;wBACJ,gBAAgB;sBAAA,CACjB;sBAED,cAAA,wBAAC,eAAe,CAAA,CAAA;oBAAA;kBAAA;gBAClB,EAEJ,CAAA;gBAGF,cAAA;kBAAC;kBAAA;oBACC,OAAO,cAAc;sBACnB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBAED,cAAA;sBAAC;sBAAA;wBACC,WAAU;wBACV,UAAS;wBACT,KAAI;wBACJ,MAAK,yCAAY,QAAO;wBACxB,KAAK,cAAc;0BACjB,IAAI;0BACJ,gBAAgB;wBAAA,CACjB;sBAAA;oBAAA;kBACH;gBAAA;cACF;YAAA;gBAEF,yBAAC,MAAM,SAAN,EACC,UAAA;kBAAA,wBAAC,MAAM,QAAN,EACC,cAAC,wBAAA,MAAM,OAAN,EACE,UAAA;gBACC,gBAAgB,WACZ;kBACE,IAAI;kBACJ,gBAAgB;gBAAA,IAElB;kBACE,IAAI;kBACJ,gBAAgB;gBAAA;cAClB,EAAA,CAER,EACF,CAAA;cACC,gBAAgB,eACf,wBAAC,eAAA,CAAA,CAAc,QAEf,wBAAC,mBAAA,EAAkB,aAA4B,CAAA;YAAA,EAEnD,CAAA;UAAA;QAAA;MAAA;IACF;EAAA;AAGN;AAMA,IAAM,gBAAgB,MAAM;AACpB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,aAAA,yBACG,KAAK,MAAL,EAAU,SAAQ,UAAS,cAAa,YACvC,UAAA;QAAA,wBAAC,KAAI,EAAA,aAAa,GAAG,cAAc,GACjC,cAAA;MAAC,KAAK;MAAL;QACC,cAAY,cAAc;UACxB,IAAI;UACJ,gBAAgB;QAAA,CACjB;QAED,UAAA;cAAA,wBAAC,KAAK,SAAL,EAAa,OAAM,YACjB,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EACH,CAAA;cAAA,wBACC,KAAK,SAAL,EAAa,OAAM,OACjB,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EACH,CAAA;QAAA;MAAA;IAAA,EAEJ,CAAA;QACA,wBAAC,KAAK,SAAL,EAAa,OAAM,YAClB,cAAA,wBAAC,cAAA,CAAA,CAAa,EAChB,CAAA;QACA,wBAAC,KAAK,SAAL,EAAa,OAAM,OAClB,cAAA,wBAAC,SAAA,CAAA,CAAQ,EACX,CAAA;EAAA,EACF,CAAA;AAEJ;AAMA,IAAM,UAAU,MAAM;AACd,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,CAAC,SAAS,UAAU,IAAU,eAAS,EAAE;AAC/C,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAiB;AACjD,QAAM,EAAE,eAAe,UAAU,QAAQ,IAAI,oBAAoB,SAAS;AAEpE,QAAA,eAA2C,CAAC,MAAM;AAC3C,eAAA,EAAE,OAAO,KAAK;EAAA;AAGrB,QAAA,eAAwD,OAAO,UAAU;AAC7E,UAAM,eAAe;AAErB,UAAM,OAAO,IAAI,SAAS,MAAM,MAAyB;AAEnD,UAAA,MAAM,KAAK,IAAI,UAAU;AAE/B,QAAI,CAAC,KAAK;AACR;IAAA;AAGE,QAAA;AACF,YAAM,MAAM,MAAM,cAAM,IAAI,IAAI,SAAA,GAAY,EAAE,cAAc,QAAQ,SAAS,IAAA,CAAM;AAE7E,YAAA,OAAO,IAAI,KAAK,CAAC,IAAI,IAAI,GAAG,IAAI,OAAO,OAAO,IAAI;QACtD,MAAM,IAAI,QAAQ,cAAc;MAAA,CACjC;AAEK,YAAA,QAAQ,MAAM,mBAAmB,IAAI;AAE3C,oBAAc,KAAK;AACnB,eAAS,SAAS;IAAA,SACX,KAAK;AACZ,UAAI,eAAe,YAAY;AAC7B;UACE,cAAc;YACZ,IAAI;YACJ,gBAAgB;UAAA,CACjB;QAAA;MACH,WACS,eAAe,kBAAkB;AACjC,iBAAA,cAAc,IAAI,gBAAgB,EAAE,MAAM,MAAM,WAAW,UAAU,CAAC,CAAC;MAAA,OAC3E;AACC,cAAA;MAAA;IACR;EACF;AAIA,aAAA,yBAAC,QAAK,EAAA,UAAU,cACd,UAAA;QAAA,wBAAC,KAAI,EAAA,aAAa,GAAG,cAAc,GAAG,YAAY,GAAG,eAAe,GAClE,cAAA,yBAAC,MAAM,MAAN,EAAW,OAAc,MAAK,YAC7B,UAAA;UAAC,wBAAA,MAAM,OAAN,EACE,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;UACC,wBAAA,WAAA,EAAU,UAAU,cAAc,OAAO,QAAS,CAAA;UACnD,wBAAC,MAAM,OAAN,CAAA,CAAY;IAAA,EAAA,CACf,EACF,CAAA;QACA,yBAAC,MAAM,QAAN,EACC,UAAA;UAAA,wBAAC,QAAO,EAAA,SAAS,SAAS,SAAQ,YAC/B,UAAA,cAAc,EAAE,IAAI,gCAAgC,gBAAgB,SAAS,CAAC,EACjF,CAAA;UACC,wBAAA,QAAA,EAAO,MAAK,UACV,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;IAAA,EACF,CAAA;EAAA,EACF,CAAA;AAEJ;AAMA,IAAM,eAAe,MAAM;AACnB,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,CAAC,UAAU,WAAW,IAAU,eAAS,KAAK;AACpD,QAAM,CAAC,WAAW,YAAY,IAAU,eAAiB;AACnD,QAAA,WAAiB,aAAyB,IAAK;AAC/C,QAAA,KAAW,YAAM;AAEvB,QAAM,EAAE,eAAe,UAAU,QAAQ,IAAI,oBAAoB,cAAc;AAE/E,QAAM,kBAAkB,MAAM;AAC5B,gBAAY,IAAI;EAAA;AAElB,QAAM,kBAAkB,MAAM;AAC5B,gBAAY,KAAK;EAAA;AAGb,QAAA,cAAsC,CAAC,MAAM;AACjD,MAAE,eAAe;AACjB,aAAS,QAAQ,MAAM;EAAA;AAGzB,QAAM,eAAe,YAAY;AACf,oBAAA;AAEZ,QAAA,CAAC,SAAS,QAAQ,OAAO;AAC3B;IAAA;AAGF,UAAM,CAAC,IAAI,IAAI,SAAS,QAAQ;AAE5B,QAAA;AACI,YAAA,QAAQ,MAAM,mBAAmB,IAAI;AAC3C,oBAAc,KAAK;AACnB,eAAS,SAAS;IAAA,SACX,KAAK;AACZ,UAAI,eAAe,kBAAkB;AACtB,qBAAA,cAAc,IAAI,gBAAgB,EAAE,MAAM,MAAM,WAAW,UAAU,CAAC,CAAC;AACpF,iBAAS,QAAQ,MAAM;MAAA,OAClB;AACC,cAAA;MAAA;IACR;EACF;AAGF,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAC,wBAAA,QAAA,EACC,cAAC,wBAAA,KAAA,EAAI,aAAa,GAAG,cAAc,GAAG,YAAY,GAAG,eAAe,GAClE,cAAC,wBAAA,MAAM,MAAN,EAAW,MAAM,IAAI,OAAO,WAC3B,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;UAAA;QAAC;QAAA;UACC,YAAY;UACZ,eAAe;UACf,WAAS;UACT,gBAAe;UACf,WAAU;UACV,YAAY,WAAW,eAAe;UACtC,aAAa,WAAW,eAAe,YAAY,cAAc;UACjE,aAAY;UACZ,aAAY;UACZ,UAAS;UACT,aAAa;UACb,aAAa;UAEb,UAAA;gBAAC,wBAAA,eAAA,EAAW,MAAK,cAAa,OAAM,QAAO,QAAO,QAAO,eAAW,KAAC,CAAA;gBACpE,wBAAA,KAAA,EAAI,YAAY,GAAG,eAAe,GACjC,cAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,SAAQ,SAAS,IAC9C,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EAAA,CACH,EACF,CAAA;gBACA,wBAAC,KAAI,EAAA,UAAS,YACZ,cAAA;cAAC;cAAA;gBACC,QAAQ,gBAAgB,KAAK,IAAI;gBACjC,MAAK;gBACL,MAAK;gBACL,UAAU;gBACV,UAAU;gBACV,KAAK;gBACL;cAAA;YAAA,EAEJ,CAAA;gBAAA,wBACC,QAAO,EAAA,MAAK,UAAS,SAAS,aAC5B,UAAc,cAAA;cACb,IAAI;cACJ,gBAAgB;YAAA,CACjB,EACH,CAAA;gBACA,wBAAC,KAAA,EAAI,YAAY,GACf,cAAA,wBAAC,YAAW,EAAA,SAAQ,MAAK,WAAU,cAChC,UAAA;cACC;gBACE,IAAI;gBACJ,gBACE;cAAA;cAEJ,EAAE,MAAM,MAAM,WAAW,UAAU;YAAA,EAAA,CAEvC,EACF,CAAA;UAAA;QAAA;MAAA;UAEF,wBAAC,MAAM,OAAN,CAAA,CAAY;IAAA,EACf,CAAA,EAAA,CACF,EAAA,CACF,EACF,CAAA;QAAA,wBACC,MAAM,QAAN,EACC,cAAC,wBAAA,QAAA,EAAO,SAAS,SAAS,SAAQ,YAC/B,UAAA,cAAc,EAAE,IAAI,gCAAgC,gBAAgB,SAAS,CAAC,EAAA,CACjF,EACF,CAAA;EAAA,EACF,CAAA;AAEJ;AAEA,IAAM,YAAY,GAAO,MAAM,KAAK;;;;;;;;;AAkBpC,IAAM,oBAAoB,CAAC,EAAE,aAAA,MAA2C;AAChE,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,EAAE,YAAY,eAAe,UAAU,QAAQ,IAAI,oBAAoB,mBAAmB;AAEhG,QAAM,eAAe,MAAM;AACzB,kBAAc,MAAS;AACvB,aAAS,QAAQ;EAAA;AAGnB,QAAM,eAAe,MAAM;AACzB,QAAI,YAAY;AACd,mBAAa,UAAU;IAAA;AAEjB,YAAA;EAAA;AAGV,aAEI,yBAAA,6BAAA,EAAA,UAAA;QAAA,wBAAC,MAAM,MAAN,EACC,cAAA,yBAAC,KAAI,EAAA,aAAa,GAAG,cAAc,GAAG,YAAY,GAAG,eAAe,GAClE,UAAA;UAAA,yBAAC,MAAK,EAAA,gBAAe,iBAAgB,eAAe,GAClD,UAAA;YAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,cAClC,UAAA;cAAA,wBAAC,YAAW,EAAA,SAAQ,MAAK,YAAW,QACjC,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EACH,CAAA;cAAA,wBACC,YAAW,EAAA,SAAQ,MAAK,WAAU,cAChC,UAAc,cAAA;YACb,IAAI;YACJ,gBAAgB;UAAA,CACjB,EACH,CAAA;QAAA,EACF,CAAA;YAAA,wBACC,QAAO,EAAA,SAAS,cAAc,SAAQ,aACpC,UAAc,cAAA;UACb,IAAI;UACJ,gBAAgB;QAAA,CACjB,EACH,CAAA;MAAA,EACF,CAAA;UACA,wBAAC,KAAI,EAAA,UAAU,SACZ,WAAA,yCAAY,WAAM,wBAAC,gBAAe,EAAA,OAAO,WAAY,CAAA,IAAK,KAC7D,CAAA;IAAA,EAAA,CACF,EACF,CAAA;QACA,yBAAC,MAAM,QAAN,EACC,UAAA;UAAC,wBAAA,MAAM,OAAN,EACC,cAAA,wBAAC,QAAA,EAAO,SAAS,SAAS,SAAQ,YAC/B,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EAAA,CACH,EACF,CAAA;UACC,wBAAA,QAAA,EAAO,SAAS,cACd,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;IAAA,EACF,CAAA;EAAA,EACF,CAAA;AAEJ;AAUA,IAAM,iBAAiB,CAAC,EAAE,MAAA,MAAiC;;AACnD,QAAA,EAAE,cAAc,IAAI,QAAQ;AAElC,aAAA,yBACG,MACC,EAAA,UAAA;QAAC,wBAAA,YAAA,EACC,cAAA,wBAAC,WAAU,EAAA,MAAK,KAAI,KAAK,MAAM,IAAA,CAAK,EACtC,CAAA;QAAA,yBACC,UACC,EAAA,UAAA;UAAA,yBAAC,aACC,EAAA,UAAA;YAAC,wBAAA,WAAA,EAAW,UAAA,MAAM,KAAK,CAAA;YACtB,wBAAA,cAAA,EACE,UAAG,IAAA,WAAM,QAAN,mBAAW,aAAa,MAAM,MAAM,KAAK,IAAI,MAAM,MAAM,GAC/D,CAAA;MAAA,EACF,CAAA;UACA,wBAAC,WAAA,EACE,UAAc,cAAA;QACb,IAAI;QACJ,gBAAgB;MAAA,CACjB,EACH,CAAA;IAAA,EACF,CAAA;EAAA,EACF,CAAA;AAEJ;AC1gBA,IAAM,kBAAkB,MAAM;AAM9B,IAAM,sBAAsB,MAAM;AAC1B,QAAA,EAAE,WAAW,IAAI,YAAY;AAC7B,QAAA,EAAE,cAAc,IAAI,QAAQ;AAClC,QAAM,EAAE,OAAO,aAAa,sBAAsB,IAAI,iBAAiB,qBAAqB;AAC5F,QAAM,CAAC,OAAO,QAAQ,IAAU,eAAS,EAAE,MAAM,YAAY,MAAM,MAAM,YAAY,KAAA,CAAM;AAC3F,QAAM,EAAE,SAAA,IAAa,YAAY,sBAAsB;AAEvD,QAAM,mBAAmB,WAAW,uBAAuB,CAAC,UAAU,MAAM,gBAAgB;AAC5F,QAAM,yBAAyB;IAC7B;IACA,CAAC,UAAU,MAAM;EAAA;AAEnB,QAAM,cAAc,WAAW,uBAAuB,CAAC,UAAU,MAAM,WAAW;AAClF,QAAM,qBAAqB,WAAW,uBAAuB,CAAC,UAAU,MAAM,kBAAkB;AAChG,QAAM,gBAAgB,WAAW,uBAAuB,CAAC,UAAU,MAAM,aAAa;AAEtF,QAAM,gBAAgB;IACpB;IACA,aAEI,MAAM,OACJ,sCACF,GACA;EAAA;AAGA,QAAA;IACJ,gBAAgB,EAAE,SAAS,UAAU;EAAA,IACnC,QAAQ,WAAW,SAAS,kBAAkB,IAAI,CAAA,CAAE;AAElD,QAAA,eAAwD,CAAC,MAAM;AACnE,MAAE,eAAe;AAEK,0BAAA;MACpB,UAAU,MAAM,KAAK,UAAU;MAC/B,UAAU,MAAM,KAAK,UAAU;IAAA,CAChC;EAAA;AAGH,QAAM,mBACJ,CAAC,SACD,CAAC,YAAY;AAIX,QAAI,YAAY,MAAM;AACpB,iBAAW,qBAAqB;QAC9B;MAAA,CACD;IAAA;AAGH,aAAS,CAAC,UAAU;MAClB,GAAG;MACH,CAAC,IAAI,GAAG;QACN,GAAG,KAAK,IAAI;QACZ,QAAQ;MAAA;IACV,EACA;EAAA;AAGN,EAAM,gBAAU,MAAM;AACX,aAAA;MACP,MAAM,YAAY;MAClB,MAAM,YAAY;IAAA,CACnB;EAAA,GACA,CAAC,WAAW,CAAC;AAGhB,MAAI,CAAC,eAAe;AACX,WAAA;EAAA;AAGH,QAAA,iBACJ,MAAM,KAAK,WAAW,YAAY,KAAK,UAAU,MAAM,KAAK,WAAW,YAAY,KAAK;AAGxF,aAAA,yBAAC,QAAQ,MAAR,EACC,UAAA;QAAC,wBAAA,KAAK,OAAL,EACE,UAAA;MACC,EAAE,IAAI,sBAAsB,gBAAgB,oBAAoB;MAChE;QACE,MAAM,cAAc;UAClB,IAAI;UACJ,gBAAgB;QAAA,CACjB;MAAA;IACH,EAEJ,CAAA;QAAA,wBACC,KAAK,MAAL,EACC,cAAC,yBAAA,QAAA,EAAK,UAAU,cACd,UAAA;UAAA;QAAC,QAAQ;QAAR;UACC,OAAO,cAAc;YACnB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,UAAU,cAAc;YACtB,IAAI;YACJ,gBAAgB;UAAA,CACjB;UACD,eACE,iBACE,wBAAC,QAAA,EAAO,UAAU,gBAAgB,MAAK,UAAS,eAAA,wBAAY,eAAM,CAAA,CAAA,GAC/D,UAAA,cAAc,EAAE,IAAI,eAAe,gBAAgB,OAAA,CAAQ,EAC9D,CAAA;QAAA;MAAA;UAIN,wBAAC,QAAQ,SAAR,EACC,cAAA,yBAAC,MAAK,EAAA,WAAU,UAAS,YAAW,WAAU,KAAK,GACjD,UAAA;YAAA;UAAC;UAAA;YACC,WAAU;YACV,YAAW;YACX,KAAK;YACL,WAAS;YACT,YAAW;YACX,QAAO;YACP,YAAY;YACZ,eAAe;YACf,cAAc;YACd,aAAa;YAEb,UAAA;kBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB,EACH,CAAA;kBAAA,yBAEC,KAAK,MAAL,EAAU,KAAK,GAAG,KAAI,MACrB,UAAA;oBAAC,yBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,SACtD,UAAA;sBAAA,wBAAC,YAAA,EAAW,SAAQ,SAAQ,WAAU,cAAa,KAAI,MACpD,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBAAA,CACjB,EACH,CAAA;sBACA,yBAAC,MAAA,EAAK,KAAK,GAAG,WAAU,UAAS,YAAW,SAAQ,KAAI,MACtD,UAAA;wBAAA,yBAAC,YAAW,EAAA,UAAA;sBAAA;sBAAE;oBAAA,EAAc,CAAA;oBAC3B,0BACC;sBAAC;sBAAA;wBACC,MAAM,iDAAiD,sBAAsB;wBAC7E,aAAA,wBAAU,eAAa,CAAA,CAAA;wBAEtB,UAAc,cAAA;0BACb,IAAI;0BACJ,gBAAgB;wBAAA,CACjB;sBAAA;oBAAA;kBACH,EAEJ,CAAA;gBAAA,EACF,CAAA;oBACA,yBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,SACtD,UAAA;sBAAA,wBAAC,YAAA,EAAW,SAAQ,SAAQ,WAAU,cAAa,KAAI,MACpD,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBAAA,CACjB,EACH,CAAA;sBACA,yBAAC,MAAA,EAAK,KAAK,GAAG,WAAU,UAAS,YAAW,SAAQ,KAAI,MACtD,UAAA;wBAAA,wBAAC,YACE,EAAA,UAAA;sBACC;wBACE,IAAI;wBACJ,gBACE;sBAAA;sBAEJ,EAAE,iBAAiB;oBAAA,EAEvB,CAAA;wBACA,wBAAC,MAAA,EAAK,MAAK,yCAAwC,aAAU,wBAAA,eAAA,CAAA,CAAa,GACvE,UAAc,cAAA;sBACb,IAAI;sBACJ,gBAAgB;oBAAA,CACjB,EACH,CAAA;kBAAA,EACF,CAAA;gBAAA,EACF,CAAA;oBAEA,yBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,SACtD,UAAA;sBAAA,wBAAC,YAAA,EAAW,SAAQ,SAAQ,WAAU,cAAa,KAAI,MACpD,UAAc,cAAA;oBACb,IAAI;oBACJ,gBAAgB;kBAAA,CACjB,EACH,CAAA;sBACC,wBAAA,YAAA,EAAW,KAAI,MAAM,UAAY,YAAA,CAAA;gBAAA,EACpC,CAAA;oBAAA,wBACC,eAAc,CAAA,CAAA;cAAA,EACjB,CAAA;YAAA;UAAA;QAAA;QAED,eACC;UAAC;UAAA;YACC,WAAS;YACT,YAAW;YACX,QAAO;YACP,YAAY;YACZ,eAAe;YACf,cAAc;YACd,aAAa;YAEb,UAAA;kBAAA,wBAAC,YAAW,EAAA,SAAQ,SAAQ,KAAI,MAC7B,UAAc,cAAA;gBACb,IAAI;gBACJ,gBAAgB;cAAA,CACjB,EACH,CAAA;kBACC,wBAAA,YAAA,EAAW,SAAQ,MAAK,WAAU,cAChC,UAAA;gBACC;kBACE,IAAI;kBACJ,gBACE;gBAAA;gBAEJ,EAAE,WAAW,WAAW,MAAM,KAAK;cAAA,EAEvC,CAAA;kBAAA,yBACC,KAAK,MAAL,EAAU,YAAY,GAAG,KAAK,GAC7B,UAAA;oBAAC,wBAAA,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA;kBAAC;kBAAA;oBACC;oBACA,YAAY,MAAM,KAAK;oBACvB,aAAa,MAAM,KAAK;oBACxB,MAAM,cAAc;sBAClB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,OAAO,cAAc;sBACnB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,cAAc,iBAAiB,MAAM;kBAAA;gBAAA,EAEzC,CAAA;oBACA,wBAAC,KAAK,MAAL,EAAU,KAAK,GAAG,GAAG,IAAI,WAAU,UAAS,YAAW,WACtD,cAAA;kBAAC;kBAAA;oBACC;oBACA,YAAY,MAAM,KAAK;oBACvB,aAAa,MAAM,KAAK;oBACxB,MAAM,cAAc;sBAClB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,OAAO,cAAc;sBACnB,IAAI;sBACJ,gBAAgB;oBAAA,CACjB;oBACD,cAAc,iBAAiB,MAAM;kBAAA;gBAAA,EAEzC,CAAA;cAAA,EACF,CAAA;YAAA;UAAA;QAAA;MACF,EAAA,CAEJ,EACF,CAAA;IAAA,EAAA,CACF,EACF,CAAA;EAAA,EACF,CAAA;AAEJ;", "names": []}