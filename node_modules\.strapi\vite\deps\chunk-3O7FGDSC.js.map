{"version": 3, "sources": ["../../../@strapi/content-manager/admin/src/services/relations.ts", "../../../@strapi/content-manager/admin/src/utils/relations.ts"], "sourcesContent": ["import { generateNKeysBetween } from 'fractional-indexing';\n\nimport {\n  RelationResult as RelResult,\n  FindAvailable,\n  FindExisting,\n} from '../../../shared/contracts/relations';\n\nimport { contentManagerApi } from './api';\n\nimport type { Modules } from '@strapi/types';\nimport type { errors } from '@strapi/utils';\n\ninterface RelationResult extends RelResult {\n  __temp_key__: string;\n}\n\ntype GetRelationsResponse =\n  | {\n      results: Array<RelationResult>;\n      pagination: {\n        page: NonNullable<Modules.EntityService.Params.Pagination.PageNotation['page']>;\n        pageSize: NonNullable<Modules.EntityService.Params.Pagination.PageNotation['pageSize']>;\n        pageCount: number;\n        total: number;\n      } | null;\n      error?: never;\n    }\n  | {\n      results?: never;\n      pagination?: never;\n      error: errors.ApplicationError | errors.YupValidationError;\n    };\n\nconst relationsApi = contentManagerApi.injectEndpoints({\n  endpoints: (build) => ({\n    getRelations: build.query<\n      GetRelationsResponse,\n      FindExisting.Params & {\n        params?: FindExisting.Request['query'];\n      }\n    >({\n      query: ({ model, id, targetField, params }) => {\n        return {\n          url: `/content-manager/relations/${model}/${id}/${targetField}`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        };\n      },\n      serializeQueryArgs: (args) => {\n        const { endpointName, queryArgs } = args;\n        return {\n          endpointName,\n          model: queryArgs.model,\n          id: queryArgs.id,\n          targetField: queryArgs.targetField,\n          locale: queryArgs.params?.locale,\n          status: queryArgs.params?.status,\n        };\n      },\n      merge: (currentCache, newItems) => {\n        if (currentCache.pagination && newItems.pagination) {\n          if (currentCache.pagination.page < newItems.pagination.page) {\n            /**\n             * Relations will always have unique IDs, so we can therefore assume\n             * that we only need to push the new items to the cache.\n             *\n             * Push new items at the beginning as latest items are shown first\n             */\n            currentCache.results = [\n              ...prepareTempKeys(newItems.results, currentCache.results),\n              ...currentCache.results,\n            ];\n            currentCache.pagination = newItems.pagination;\n          } else if (newItems.pagination.page === 1) {\n            /**\n             * We're resetting the relations\n             */\n            currentCache.results = prepareTempKeys(newItems.results);\n            currentCache.pagination = newItems.pagination;\n          }\n        }\n      },\n      forceRefetch({ currentArg, previousArg }) {\n        if (!currentArg?.params && !previousArg?.params) {\n          return false;\n        }\n\n        return (\n          currentArg?.params?.page !== previousArg?.params?.page ||\n          currentArg?.params?.pageSize !== previousArg?.params?.pageSize\n        );\n      },\n      transformResponse: (response: FindExisting.Response) => {\n        if ('results' in response && response.results) {\n          return {\n            ...response,\n            results: prepareTempKeys(response.results.toReversed()),\n          };\n        } else {\n          return response;\n        }\n      },\n      providesTags: ['Relations'],\n    }),\n    searchRelations: build.query<\n      FindAvailable.Response,\n      FindAvailable.Params & {\n        params?: FindAvailable.Request['query'];\n      }\n    >({\n      query: ({ model, targetField, params }) => {\n        return {\n          url: `/content-manager/relations/${model}/${targetField}`,\n          method: 'GET',\n          config: {\n            params,\n          },\n        };\n      },\n      serializeQueryArgs: (args) => {\n        const { endpointName, queryArgs } = args;\n        return {\n          endpointName,\n          model: queryArgs.model,\n          targetField: queryArgs.targetField,\n          _q: queryArgs.params?._q,\n          idsToOmit: queryArgs.params?.idsToOmit,\n          idsToInclude: queryArgs.params?.idsToInclude,\n        };\n      },\n      merge: (currentCache, newItems) => {\n        if (currentCache.pagination && newItems.pagination) {\n          if (currentCache.pagination.page < newItems.pagination.page) {\n            /**\n             * Relations will always have unique IDs, so we can therefore assume\n             * that we only need to push the new items to the cache.\n             */\n            const existingIds = currentCache.results.map((item) => item.documentId);\n            const uniqueNewItems = newItems.results.filter(\n              (item) => !existingIds.includes(item.documentId)\n            );\n            currentCache.results.push(...uniqueNewItems);\n            currentCache.pagination = newItems.pagination;\n          } else if (newItems.pagination.page === 1) {\n            /**\n             * We're resetting the relations\n             */\n            currentCache.results = newItems.results;\n            currentCache.pagination = newItems.pagination;\n          }\n        }\n      },\n      forceRefetch({ currentArg, previousArg }) {\n        if (!currentArg?.params && !previousArg?.params) {\n          return false;\n        }\n\n        return (\n          currentArg?.params?.page !== previousArg?.params?.page ||\n          currentArg?.params?.pageSize !== previousArg?.params?.pageSize\n        );\n      },\n      transformResponse: (response: FindAvailable.Response) => {\n        if (response.results) {\n          return {\n            ...response,\n            results: response.results,\n          };\n        } else {\n          return response;\n        }\n      },\n    }),\n  }),\n});\n\n/**\n * @internal\n * @description Adds a `__temp_key__` to each relation item. This gives us\n * a stable identifier regardless of it's ids etc. that we can then use for drag and drop.\n */\nconst prepareTempKeys = (relations: RelResult[], existingRelations: RelationResult[] = []) => {\n  const [firstItem] = existingRelations.slice(0);\n  const keys = generateNKeysBetween(null, firstItem?.__temp_key__ ?? null, relations.length);\n\n  return relations.map((datum, index) => ({\n    ...datum,\n    __temp_key__: keys[index],\n  }));\n};\n\nconst { useGetRelationsQuery, useLazySearchRelationsQuery } = relationsApi;\n\nexport { useGetRelationsQuery, useLazySearchRelationsQuery };\nexport type { RelationResult };\n", "import type { MainField } from './attributes';\nimport type { RelationResult } from '../../../shared/contracts/relations';\n\n/**\n * @internal\n * @description Get the label of a relation, the contract has [key: string]: unknown,\n * so we need to check if the mainFieldKey is defined and if the relation has a value\n * under that property. If it does, we then verify it's type of string and return it.\n *\n * We fallback to the documentId.\n */\nconst getRelationLabel = (relation: RelationResult, mainField?: MainField): string => {\n  const label = mainField && relation[mainField.name] ? relation[mainField.name] : null;\n\n  if (typeof label === 'string') {\n    return label;\n  }\n\n  return relation.documentId;\n};\n\nexport { getRelationLabel };\n"], "mappings": ";;;;;;;;AAkCA,IAAM,eAAe,kBAAkB,gBAAgB;EACrD,WAAW,CAAC,WAAW;IACrB,cAAc,MAAM,MAKlB;MACA,OAAO,CAAC,EAAE,OAAO,IAAI,aAAa,OAAA,MAAa;AACtC,eAAA;UACL,KAAK,8BAA8B,KAAK,IAAI,EAAE,IAAI,WAAW;UAC7D,QAAQ;UACR,QAAQ;YACN;UACF;QAAA;MAEJ;MACA,oBAAoB,CAAC,SAAS;;AACtB,cAAA,EAAE,cAAc,UAAc,IAAA;AAC7B,eAAA;UACL;UACA,OAAO,UAAU;UACjB,IAAI,UAAU;UACd,aAAa,UAAU;UACvB,SAAQ,eAAU,WAAV,mBAAkB;UAC1B,SAAQ,eAAU,WAAV,mBAAkB;QAAA;MAE9B;MACA,OAAO,CAAC,cAAc,aAAa;AAC7B,YAAA,aAAa,cAAc,SAAS,YAAY;AAClD,cAAI,aAAa,WAAW,OAAO,SAAS,WAAW,MAAM;AAO3D,yBAAa,UAAU;cACrB,GAAG,gBAAgB,SAAS,SAAS,aAAa,OAAO;cACzD,GAAG,aAAa;YAAA;AAElB,yBAAa,aAAa,SAAS;UAC1B,WAAA,SAAS,WAAW,SAAS,GAAG;AAI5B,yBAAA,UAAU,gBAAgB,SAAS,OAAO;AACvD,yBAAa,aAAa,SAAS;UACrC;QACF;MACF;MACA,aAAa,EAAE,YAAY,YAAA,GAAe;;AACxC,YAAI,EAAC,yCAAY,WAAU,EAAC,2CAAa,SAAQ;AACxC,iBAAA;QACT;AAGE,iBAAA,8CAAY,WAAZ,mBAAoB,YAAS,gDAAa,WAAb,mBAAqB,WAClD,8CAAY,WAAZ,mBAAoB,gBAAa,gDAAa,WAAb,mBAAqB;MAE1D;MACA,mBAAmB,CAAC,aAAoC;AAClD,YAAA,aAAa,YAAY,SAAS,SAAS;AACtC,iBAAA;YACL,GAAG;YACH,SAAS,gBAAgB,SAAS,QAAQ,WAAA,CAAY;UAAA;QACxD,OACK;AACE,iBAAA;QACT;MACF;MACA,cAAc,CAAC,WAAW;IAAA,CAC3B;IACD,iBAAiB,MAAM,MAKrB;MACA,OAAO,CAAC,EAAE,OAAO,aAAa,OAAA,MAAa;AAClC,eAAA;UACL,KAAK,8BAA8B,KAAK,IAAI,WAAW;UACvD,QAAQ;UACR,QAAQ;YACN;UACF;QAAA;MAEJ;MACA,oBAAoB,CAAC,SAAS;;AACtB,cAAA,EAAE,cAAc,UAAc,IAAA;AAC7B,eAAA;UACL;UACA,OAAO,UAAU;UACjB,aAAa,UAAU;UACvB,KAAI,eAAU,WAAV,mBAAkB;UACtB,YAAW,eAAU,WAAV,mBAAkB;UAC7B,eAAc,eAAU,WAAV,mBAAkB;QAAA;MAEpC;MACA,OAAO,CAAC,cAAc,aAAa;AAC7B,YAAA,aAAa,cAAc,SAAS,YAAY;AAClD,cAAI,aAAa,WAAW,OAAO,SAAS,WAAW,MAAM;AAK3D,kBAAM,cAAc,aAAa,QAAQ,IAAI,CAAC,SAAS,KAAK,UAAU;AAChE,kBAAA,iBAAiB,SAAS,QAAQ;cACtC,CAAC,SAAS,CAAC,YAAY,SAAS,KAAK,UAAU;YAAA;AAEpC,yBAAA,QAAQ,KAAK,GAAG,cAAc;AAC3C,yBAAa,aAAa,SAAS;UAC1B,WAAA,SAAS,WAAW,SAAS,GAAG;AAIzC,yBAAa,UAAU,SAAS;AAChC,yBAAa,aAAa,SAAS;UACrC;QACF;MACF;MACA,aAAa,EAAE,YAAY,YAAA,GAAe;;AACxC,YAAI,EAAC,yCAAY,WAAU,EAAC,2CAAa,SAAQ;AACxC,iBAAA;QACT;AAGE,iBAAA,8CAAY,WAAZ,mBAAoB,YAAS,gDAAa,WAAb,mBAAqB,WAClD,8CAAY,WAAZ,mBAAoB,gBAAa,gDAAa,WAAb,mBAAqB;MAE1D;MACA,mBAAmB,CAAC,aAAqC;AACvD,YAAI,SAAS,SAAS;AACb,iBAAA;YACL,GAAG;YACH,SAAS,SAAS;UAAA;QACpB,OACK;AACE,iBAAA;QACT;MACF;IAAA,CACD;EAAA;AAEL,CAAC;AAOD,IAAM,kBAAkB,CAAC,WAAwB,oBAAsC,CAAA,MAAO;AAC5F,QAAM,CAAC,SAAS,IAAI,kBAAkB,MAAM,CAAC;AAC7C,QAAM,OAAO,qBAAqB,OAAM,uCAAW,iBAAgB,MAAM,UAAU,MAAM;AAEzF,SAAO,UAAU,IAAI,CAAC,OAAO,WAAW;IACtC,GAAG;IACH,cAAc,KAAK,KAAK;EACxB,EAAA;AACJ;AAEM,IAAA,EAAE,sBAAsB,4BAAA,IAAgC;ACvLxD,IAAA,mBAAmB,CAAC,UAA0B,cAAkC;AAC9E,QAAA,QAAQ,aAAa,SAAS,UAAU,IAAI,IAAI,SAAS,UAAU,IAAI,IAAI;AAE7E,MAAA,OAAO,UAAU,UAAU;AACtB,WAAA;EACT;AAEA,SAAO,SAAS;AAClB;", "names": []}