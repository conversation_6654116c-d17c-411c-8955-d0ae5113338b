{"version": 3, "file": "parser.d.ts", "sourceRoot": "", "sources": ["parser.ts"], "names": [], "mappings": "AAAA,OAAO,EAAY,WAAW,EAAC,MAAM,SAAS,CAAA;AAC9C,OAAO,EAIL,oBAAoB,EAMrB,MAAM,SAAS,CAAA;AAiBhB,MAAM,WAAW,QAAQ;IACvB,6CAA6C;IAC7C,MAAM,EAAE,MAAM,CAAA;IACd,IAAI,EAAE,MAAM,CAAA;IACZ,sDAAsD;IACtD,MAAM,EAAE,MAAM,CAAA;CACf;AAED,MAAM,WAAW,aAAa;IAC5B;;;;;OAKG;IACH,SAAS,CAAC,EAAE,OAAO,CAAA;IACnB;;;OAGG;IACH,mBAAmB,CAAC,EAAE,OAAO,CAAA;IAC7B;;;OAGG;IACH,oBAAoB,CAAC,EAAE,OAAO,CAAA;IAC9B;;;OAGG;IACH,eAAe,CAAC,EAAE,OAAO,CAAA;IACzB;;OAEG;IACH,MAAM,CAAC,EAAE,IAAI,CAAC,MAAM,CAAA;CACrB;AAED,MAAM,MAAM,MAAM,CAAC,CAAC,EAAE,CAAC,IAAI;IAAC,GAAG,EAAE,CAAC,CAAC;IAAC,GAAG,EAAE,IAAI,CAAA;CAAC,GAAG;IAAC,GAAG,EAAE,IAAI,CAAC;IAAC,GAAG,EAAE,CAAC,CAAA;CAAC,CAAA;AA+KpE,qBAAa,MAAM;IACjB,OAAO,CAAC,OAAO,CAAQ;IACvB,OAAO,CAAC,QAAQ,CAAU;IAC1B,OAAO,CAAC,MAAM,CAAC,CAAa;IAE5B,OAAO,CAAC,SAAS,CAAS;IAC1B,OAAO,CAAC,mBAAmB,CAAS;IACpC,OAAO,CAAC,oBAAoB,CAAC,CAAS;gBAE1B,OAAO,EAAE,MAAM,EAAE,OAAO,GAAE,aAAkB;IASxD,KAAK,IAAI,MAAM,CAAC,oBAAoB,EAAE,EAAE,WAAW,CAAC;IAOpD,OAAO,CAAC,YAAY;IA6DpB;;;;;;;;;;;;;;;;;OAiBG;IACH,OAAO,CAAC,QAAQ;IAkFhB;;OAEG;IACH,OAAO,CAAC,YAAY;IAUpB,OAAO,CAAC,YAAY;IAuCpB,wBAAwB,IAAI,MAAM,GAAG,IAAI;IAczC;;;;OAIG;IACH,OAAO,CAAC,aAAa;IAsDrB,OAAO,CAAC,gBAAgB;IAuBxB,OAAO,CAAC,aAAa;IAsFrB;;;OAGG;IACH,OAAO,CAAC,yBAAyB;IAejC,OAAO,CAAC,oBAAoB;IAyO5B,OAAO,CAAC,qBAAqB;IAe7B;;OAEG;IACH,OAAO,CAAC,6BAA6B;IAiDrC,OAAO,CAAC,6BAA6B;IAwBrC;;;;;;;;;OASG;IACH,OAAO,CAAC,6BAA6B;IA6GrC,OAAO,CAAC,sBAAsB;IAuC9B,OAAO,CAAC,MAAM;IAId,OAAO,CAAC,KAAK;IAIb,OAAO,CAAC,aAAa;IASrB;;;OAGG;IACH,OAAO,CAAC,IAAI;IAYZ,OAAO,CAAC,KAAK;IAcb,oDAAoD;IACpD,OAAO,CAAC,IAAI;IAgBZ;;;;;OAKG;IACH,OAAO,CAAC,MAAM;IAUd;;;OAGG;IACH,OAAO,CAAC,SAAS;IAYjB;;;OAGG;IACH,OAAO,CAAC,MAAM;IA0Bd,sFAAsF;IACtF,OAAO,CAAC,SAAS;IAMjB;;;OAGG;IACH,OAAO,CAAC,IAAI;CASb"}