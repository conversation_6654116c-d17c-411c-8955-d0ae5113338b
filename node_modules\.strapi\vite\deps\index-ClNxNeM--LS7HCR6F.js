import {
  Form,
  Formik,
  fn
} from "./chunk-SDRTZUB7.js";
import {
  useIntl
} from "./chunk-IKDDXE5L.js";
import {
  require_arrayIncludesWith
} from "./chunk-S5DFQGWM.js";
import {
  require_sortBy
} from "./chunk-RBFGK6LJ.js";
import {
  require_map,
  require_tail
} from "./chunk-KPWT7COX.js";
import "./chunk-U7XWYUVC.js";
import "./chunk-MM3DOGUR.js";
import "./chunk-6NLSGXWT.js";
import "./chunk-DC3UNANX.js";
import "./chunk-5ZFC5BBE.js";
import "./chunk-CH6LMMF3.js";
import "./chunk-C7H2BX76.js";
import "./chunk-3UWI42TF.js";
import "./chunk-EGRHWZRV.js";
import {
  require_prop_types
} from "./chunk-S4GMEU6I.js";
import "./chunk-XPB4KQQT.js";
import "./chunk-D63J2BWQ.js";
import "./chunk-IZBVLW72.js";
import "./chunk-M5EKOYPZ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-O6QFUROF.js";
import "./chunk-VOKTPSOW.js";
import "./chunk-N562NCQ4.js";
import "./chunk-HYNTGZ4P.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import {
  require_isEmpty
} from "./chunk-KKUAHZGP.js";
import "./chunk-VUQGR7S3.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  BackButton,
  ConfirmDialog,
  Layouts,
  SearchInput,
  create4 as create,
  create6 as create2,
  errorsTrads,
  require_baseSlice,
  require_upperFirst,
  useFetchClient,
  useMutation,
  useQueries,
  useQuery
} from "./chunk-ELTZWS66.js";
import {
  Page,
  require_baseRest,
  require_isArrayLikeObject,
  require_set,
  useAPIErrorHandler,
  useNotification,
  useQueryParams,
  useRBAC,
  useTracking
} from "./chunk-4C2ZQ5OG.js";
import {
  Accordion,
  Box,
  Button,
  CheckboxImpl,
  Dialog,
  EmptyStateLayout,
  Field,
  Flex,
  Grid,
  IconButton,
  Link,
  LinkButton,
  Main,
  Table,
  Tbody,
  Td,
  TextInput,
  Textarea,
  Th,
  Thead,
  Tr,
  Typography,
  VisuallyHidden,
  require_SetCache,
  require_arrayIncludes,
  require_arrayMap,
  require_baseUnary,
  require_cacheHas,
  require_get,
  require_toInteger,
  useCollator,
  useFilter,
  useNotifyAT
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import {
  NavLink,
  Route,
  Routes,
  useMatch,
  useNavigate
} from "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$1d,
  ForwardRef$1r,
  ForwardRef$41,
  ForwardRef$4p,
  ForwardRef$j
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import {
  dt,
  lt
} from "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  PERMISSIONS,
  getTrad
} from "./chunk-WE3SAIMN.js";
import {
  __commonJS,
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/lodash/_baseDifference.js
var require_baseDifference = __commonJS({
  "node_modules/lodash/_baseDifference.js"(exports, module) {
    var SetCache = require_SetCache();
    var arrayIncludes = require_arrayIncludes();
    var arrayIncludesWith = require_arrayIncludesWith();
    var arrayMap = require_arrayMap();
    var baseUnary = require_baseUnary();
    var cacheHas = require_cacheHas();
    var LARGE_ARRAY_SIZE = 200;
    function baseDifference(array, values, iteratee, comparator) {
      var index = -1, includes = arrayIncludes, isCommon = true, length = array.length, result = [], valuesLength = values.length;
      if (!length) {
        return result;
      }
      if (iteratee) {
        values = arrayMap(values, baseUnary(iteratee));
      }
      if (comparator) {
        includes = arrayIncludesWith;
        isCommon = false;
      } else if (values.length >= LARGE_ARRAY_SIZE) {
        includes = cacheHas;
        isCommon = false;
        values = new SetCache(values);
      }
      outer:
        while (++index < length) {
          var value = array[index], computed = iteratee == null ? value : iteratee(value);
          value = comparator || value !== 0 ? value : 0;
          if (isCommon && computed === computed) {
            var valuesIndex = valuesLength;
            while (valuesIndex--) {
              if (values[valuesIndex] === computed) {
                continue outer;
              }
            }
            result.push(value);
          } else if (!includes(values, computed, comparator)) {
            result.push(value);
          }
        }
      return result;
    }
    module.exports = baseDifference;
  }
});

// node_modules/lodash/without.js
var require_without = __commonJS({
  "node_modules/lodash/without.js"(exports, module) {
    var baseDifference = require_baseDifference();
    var baseRest = require_baseRest();
    var isArrayLikeObject = require_isArrayLikeObject();
    var without2 = baseRest(function(array, values) {
      return isArrayLikeObject(array) ? baseDifference(array, values) : [];
    });
    module.exports = without2;
  }
});

// node_modules/lodash/take.js
var require_take = __commonJS({
  "node_modules/lodash/take.js"(exports, module) {
    var baseSlice = require_baseSlice();
    var toInteger = require_toInteger();
    function take2(array, n, guard) {
      if (!(array && array.length)) {
        return [];
      }
      n = guard || n === void 0 ? 1 : toInteger(n);
      return baseSlice(array, 0, n < 0 ? 0 : n);
    }
    module.exports = take2;
  }
});

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/index-ClNxNeM-.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var React = __toESM(require_react(), 1);
var import_react = __toESM(require_react(), 1);
var import_prop_types = __toESM(require_prop_types(), 1);
var import_upperFirst = __toESM(require_upperFirst(), 1);
var import_sortBy = __toESM(require_sortBy(), 1);
var import_get = __toESM(require_get(), 1);
var import_isEmpty = __toESM(require_isEmpty(), 1);
var import_without = __toESM(require_without(), 1);
var import_map = __toESM(require_map(), 1);
var import_tail = __toESM(require_tail(), 1);
var import_set = __toESM(require_set(), 1);
var import_take = __toESM(require_take(), 1);
var UsersPermissions$2 = (0, import_react.createContext)({});
var UsersPermissionsProvider = ({ children, value }) => {
  return (0, import_jsx_runtime.jsx)(UsersPermissions$2.Provider, { value, children });
};
var useUsersPermissions = () => (0, import_react.useContext)(UsersPermissions$2);
UsersPermissionsProvider.propTypes = {
  children: import_prop_types.default.node.isRequired,
  value: import_prop_types.default.object.isRequired
};
function formatPluginName(pluginSlug) {
  switch (pluginSlug) {
    case "application":
      return "Application";
    case "plugin::content-manager":
      return "Content manager";
    case "plugin::content-type-builder":
      return "Content types builder";
    case "plugin::documentation":
      return "Documentation";
    case "plugin::email":
      return "Email";
    case "plugin::i18n":
      return "i18n";
    case "plugin::upload":
      return "Upload";
    case "plugin::users-permissions":
      return "Users-permissions";
    default:
      return (0, import_upperFirst.default)(pluginSlug.replace("api::", "").replace("plugin::", ""));
  }
}
var init$1 = (initialState2, permissions) => {
  const collapses = Object.keys(permissions).sort().map((name) => ({ name, isOpen: false }));
  return { ...initialState2, collapses };
};
var activeCheckboxWrapperStyles = lt`
  background: ${(props) => props.theme.colors.primary100};

  #cog {
    opacity: 1;
  }
`;
var CheckboxWrapper = dt(Box)`
  display: flex;
  justify-content: space-between;
  align-items: center;

  #cog {
    opacity: 0;
    path {
      fill: ${(props) => props.theme.colors.primary600};
    }
  }

  /* Show active style both on hover and when the action is selected */
  ${(props) => props.isActive && activeCheckboxWrapperStyles}
  &:hover {
    ${activeCheckboxWrapperStyles}
  }
`;
var Border = dt.div`
  flex: 1;
  align-self: center;
  border-top: 1px solid ${({ theme }) => theme.colors.neutral150};
`;
var SubCategory = ({ subCategory }) => {
  const { formatMessage } = useIntl();
  const { onChange, onChangeSelectAll, onSelectedAction, selectedAction, modifiedData } = useUsersPermissions();
  const currentScopedModifiedData = (0, import_react.useMemo)(() => {
    return (0, import_get.default)(modifiedData, subCategory.name, {});
  }, [modifiedData, subCategory]);
  const hasAllActionsSelected = (0, import_react.useMemo)(() => {
    return Object.values(currentScopedModifiedData).every((action) => action.enabled === true);
  }, [currentScopedModifiedData]);
  const hasSomeActionsSelected = (0, import_react.useMemo)(() => {
    return Object.values(currentScopedModifiedData).some((action) => action.enabled === true) && !hasAllActionsSelected;
  }, [currentScopedModifiedData, hasAllActionsSelected]);
  const handleChangeSelectAll = (0, import_react.useCallback)(
    ({ target: { name } }) => {
      onChangeSelectAll({ target: { name, value: !hasAllActionsSelected } });
    },
    [hasAllActionsSelected, onChangeSelectAll]
  );
  const isActionSelected = (0, import_react.useCallback)(
    (actionName) => {
      return selectedAction === actionName;
    },
    [selectedAction]
  );
  return (0, import_jsx_runtime.jsxs)(Box, { children: [
    (0, import_jsx_runtime.jsxs)(Flex, { justifyContent: "space-between", alignItems: "center", children: [
      (0, import_jsx_runtime.jsx)(Box, { paddingRight: 4, children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: subCategory.label }) }),
      (0, import_jsx_runtime.jsx)(Border, {}),
      (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 4, children: (0, import_jsx_runtime.jsx)(
        CheckboxImpl,
        {
          name: subCategory.name,
          checked: hasSomeActionsSelected ? "indeterminate" : hasAllActionsSelected,
          onCheckedChange: (value) => handleChangeSelectAll({ target: { name: subCategory.name, value } }),
          children: formatMessage({ id: "app.utils.select-all", defaultMessage: "Select all" })
        }
      ) })
    ] }),
    (0, import_jsx_runtime.jsx)(Flex, { paddingTop: 6, paddingBottom: 6, children: (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 2, style: { flex: 1 }, children: subCategory.actions.map((action) => {
      const name = `${action.name}.enabled`;
      return (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(CheckboxWrapper, { isActive: isActionSelected(action.name), padding: 2, hasRadius: true, children: [
        (0, import_jsx_runtime.jsx)(
          CheckboxImpl,
          {
            checked: (0, import_get.default)(modifiedData, name, false),
            name,
            onCheckedChange: (value) => onChange({ target: { name, value } }),
            children: action.label
          }
        ),
        (0, import_jsx_runtime.jsxs)(
          "button",
          {
            type: "button",
            onClick: () => onSelectedAction(action.name),
            style: { display: "inline-flex", alignItems: "center" },
            children: [
              (0, import_jsx_runtime.jsx)(VisuallyHidden, { tag: "span", children: formatMessage(
                {
                  id: "app.utils.show-bound-route",
                  defaultMessage: "Show bound route for {route}"
                },
                {
                  route: action.name
                }
              ) }),
              (0, import_jsx_runtime.jsx)(ForwardRef$41, { id: "cog" })
            ]
          }
        )
      ] }) }, action.name);
    }) }) })
  ] });
};
SubCategory.propTypes = {
  subCategory: import_prop_types.default.object.isRequired
};
var PermissionRow = ({ name, permissions }) => {
  const subCategories = (0, import_react.useMemo)(() => {
    return (0, import_sortBy.default)(
      Object.values(permissions.controllers).reduce((acc, curr, index) => {
        const currentName = `${name}.controllers.${Object.keys(permissions.controllers)[index]}`;
        const actions = (0, import_sortBy.default)(
          Object.keys(curr).reduce((acc2, current) => {
            return [
              ...acc2,
              {
                ...curr[current],
                label: current,
                name: `${currentName}.${current}`
              }
            ];
          }, []),
          "label"
        );
        return [
          ...acc,
          {
            actions,
            label: Object.keys(permissions.controllers)[index],
            name: currentName
          }
        ];
      }, []),
      "label"
    );
  }, [name, permissions]);
  return (0, import_jsx_runtime.jsx)(Box, { padding: 6, children: subCategories.map((subCategory) => (0, import_jsx_runtime.jsx)(SubCategory, { subCategory }, subCategory.name)) });
};
PermissionRow.propTypes = {
  name: import_prop_types.default.string.isRequired,
  permissions: import_prop_types.default.object.isRequired
};
var initialState$1 = {
  collapses: []
};
var reducer$1 = (state, action) => (
  // eslint-disable-next-line consistent-return
  fn(state, (draftState) => {
    switch (action.type) {
      case "TOGGLE_COLLAPSE": {
        draftState.collapses = state.collapses.map((collapse, index) => {
          if (index === action.index) {
            return { ...collapse, isOpen: !collapse.isOpen };
          }
          return { ...collapse, isOpen: false };
        });
        break;
      }
      default:
        return draftState;
    }
  })
);
var Permissions = () => {
  const { modifiedData } = useUsersPermissions();
  const { formatMessage } = useIntl();
  const [{ collapses }] = (0, import_react.useReducer)(reducer$1, initialState$1, (state) => init$1(state, modifiedData));
  return (0, import_jsx_runtime.jsx)(Accordion.Root, { size: "M", children: (0, import_jsx_runtime.jsx)(Flex, { direction: "column", alignItems: "stretch", gap: 1, children: collapses.map((collapse, index) => (0, import_jsx_runtime.jsxs)(Accordion.Item, { value: collapse.name, children: [
    (0, import_jsx_runtime.jsx)(Accordion.Header, { variant: index % 2 === 0 ? "secondary" : void 0, children: (0, import_jsx_runtime.jsx)(
      Accordion.Trigger,
      {
        caretPosition: "right",
        description: formatMessage(
          {
            id: "users-permissions.Plugin.permissions.plugins.description",
            defaultMessage: "Define all allowed actions for the {name} plugin."
          },
          { name: collapse.name }
        ),
        children: formatPluginName(collapse.name)
      }
    ) }),
    (0, import_jsx_runtime.jsx)(Accordion.Content, { children: (0, import_jsx_runtime.jsx)(PermissionRow, { permissions: modifiedData[collapse.name], name: collapse.name }) })
  ] }, collapse.name)) }) });
};
var getMethodColor = (verb) => {
  switch (verb) {
    case "POST": {
      return {
        text: "success600",
        border: "success200",
        background: "success100"
      };
    }
    case "GET": {
      return {
        text: "secondary600",
        border: "secondary200",
        background: "secondary100"
      };
    }
    case "PUT": {
      return {
        text: "warning600",
        border: "warning200",
        background: "warning100"
      };
    }
    case "DELETE": {
      return {
        text: "danger600",
        border: "danger200",
        background: "danger100"
      };
    }
    default: {
      return {
        text: "neutral600",
        border: "neutral200",
        background: "neutral100"
      };
    }
  }
};
var MethodBox = dt(Box)`
  margin: -1px;
  border-radius: ${({ theme }) => theme.spaces[1]} 0 0 ${({ theme }) => theme.spaces[1]};
`;
function BoundRoute({ route }) {
  const { formatMessage } = useIntl();
  const { method, handler: title, path } = route;
  const formattedRoute = path ? (0, import_tail.default)(path.split("/")) : [];
  const [controller = "", action = ""] = title ? title.split(".") : [];
  const colors = getMethodColor(route.method);
  return (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 2, children: [
    (0, import_jsx_runtime.jsxs)(Typography, { variant: "delta", tag: "h3", children: [
      formatMessage({
        id: "users-permissions.BoundRoute.title",
        defaultMessage: "Bound route to"
      }),
      " ",
      (0, import_jsx_runtime.jsx)("span", { children: controller }),
      (0, import_jsx_runtime.jsxs)(Typography, { variant: "delta", textColor: "primary600", children: [
        ".",
        action
      ] })
    ] }),
    (0, import_jsx_runtime.jsxs)(Flex, { hasRadius: true, background: "neutral0", borderColor: "neutral200", gap: 0, children: [
      (0, import_jsx_runtime.jsx)(MethodBox, { background: colors.background, borderColor: colors.border, padding: 2, children: (0, import_jsx_runtime.jsx)(Typography, { fontWeight: "bold", textColor: colors.text, children: method }) }),
      (0, import_jsx_runtime.jsx)(Box, { paddingLeft: 2, paddingRight: 2, children: (0, import_map.default)(formattedRoute, (value) => (0, import_jsx_runtime.jsxs)(Typography, { textColor: value.includes(":") ? "neutral600" : "neutral900", children: [
        "/",
        value
      ] }, value)) })
    ] })
  ] });
}
BoundRoute.defaultProps = {
  route: {
    handler: "Nocontroller.error",
    method: "GET",
    path: "/there-is-no-path"
  }
};
BoundRoute.propTypes = {
  route: import_prop_types.default.shape({
    handler: import_prop_types.default.string,
    method: import_prop_types.default.string,
    path: import_prop_types.default.string
  })
};
var Policies = () => {
  const { formatMessage } = useIntl();
  const { selectedAction, routes } = useUsersPermissions();
  const path = (0, import_without.default)(selectedAction.split("."), "controllers");
  const controllerRoutes = (0, import_get.default)(routes, path[0]);
  const pathResolved = path.slice(1).join(".");
  const displayedRoutes = (0, import_isEmpty.default)(controllerRoutes) ? [] : controllerRoutes.filter((o) => o.handler.endsWith(pathResolved));
  return (0, import_jsx_runtime.jsx)(
    Grid.Item,
    {
      col: 5,
      background: "neutral150",
      paddingTop: 6,
      paddingBottom: 6,
      paddingLeft: 7,
      paddingRight: 7,
      style: { minHeight: "100%" },
      direction: "column",
      alignItems: "stretch",
      children: selectedAction ? (0, import_jsx_runtime.jsx)(Flex, { direction: "column", alignItems: "stretch", gap: 2, children: displayedRoutes.map((route, key) => (
        // eslint-disable-next-line react/no-array-index-key
        (0, import_jsx_runtime.jsx)(BoundRoute, { route }, key)
      )) }) : (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 2, children: [
        (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h3", children: formatMessage({
          id: "users-permissions.Policies.header.title",
          defaultMessage: "Advanced settings"
        }) }),
        (0, import_jsx_runtime.jsx)(Typography, { tag: "p", textColor: "neutral600", children: formatMessage({
          id: "users-permissions.Policies.header.hint",
          defaultMessage: "Select the application's actions or the plugin's actions and click on the cog icon to display the bound route"
        }) })
      ] })
    }
  );
};
var init = (state, permissions, routes) => {
  return {
    ...state,
    initialData: permissions,
    modifiedData: permissions,
    routes
  };
};
var initialState = {
  initialData: {},
  modifiedData: {},
  routes: {},
  selectedAction: "",
  policies: []
};
var reducer = (state, action) => fn(state, (draftState) => {
  switch (action.type) {
    case "ON_CHANGE": {
      const keysLength = action.keys.length;
      const isChangingCheckbox = action.keys[keysLength - 1] === "enabled";
      if (action.value && isChangingCheckbox) {
        const selectedAction = (0, import_take.default)(action.keys, keysLength - 1).join(".");
        draftState.selectedAction = selectedAction;
      }
      (0, import_set.default)(draftState, ["modifiedData", ...action.keys], action.value);
      break;
    }
    case "ON_CHANGE_SELECT_ALL": {
      const pathToValue = ["modifiedData", ...action.keys];
      const oldValues = (0, import_get.default)(state, pathToValue, {});
      const updatedValues = Object.keys(oldValues).reduce((acc, current) => {
        acc[current] = { ...oldValues[current], enabled: action.value };
        return acc;
      }, {});
      (0, import_set.default)(draftState, pathToValue, updatedValues);
      break;
    }
    case "ON_RESET": {
      draftState.modifiedData = state.initialData;
      break;
    }
    case "ON_SUBMIT_SUCCEEDED": {
      draftState.initialData = state.modifiedData;
      break;
    }
    case "SELECT_ACTION": {
      const { actionToSelect } = action;
      draftState.selectedAction = actionToSelect === state.selectedAction ? "" : actionToSelect;
      break;
    }
    default:
      return draftState;
  }
});
var UsersPermissions = (0, import_react.forwardRef)(({ permissions, routes }, ref) => {
  const { formatMessage } = useIntl();
  const [state, dispatch] = (0, import_react.useReducer)(
    reducer,
    initialState,
    (state2) => init(state2, permissions, routes)
  );
  (0, import_react.useImperativeHandle)(ref, () => ({
    getPermissions() {
      return {
        permissions: state.modifiedData
      };
    },
    resetForm() {
      dispatch({ type: "ON_RESET" });
    },
    setFormAfterSubmit() {
      dispatch({ type: "ON_SUBMIT_SUCCEEDED" });
    }
  }));
  const handleChange = ({ target: { name, value } }) => dispatch({
    type: "ON_CHANGE",
    keys: name.split("."),
    value: value === "empty__string_value" ? "" : value
  });
  const handleChangeSelectAll = ({ target: { name, value } }) => dispatch({
    type: "ON_CHANGE_SELECT_ALL",
    keys: name.split("."),
    value
  });
  const handleSelectedAction = (actionToSelect) => dispatch({
    type: "SELECT_ACTION",
    actionToSelect
  });
  const providerValue = {
    ...state,
    onChange: handleChange,
    onChangeSelectAll: handleChangeSelectAll,
    onSelectedAction: handleSelectedAction
  };
  return (0, import_jsx_runtime.jsx)(UsersPermissionsProvider, { value: providerValue, children: (0, import_jsx_runtime.jsxs)(Grid.Root, { gap: 0, shadow: "filterShadow", hasRadius: true, background: "neutral0", children: [
    (0, import_jsx_runtime.jsx)(
      Grid.Item,
      {
        col: 7,
        paddingTop: 6,
        paddingBottom: 6,
        paddingLeft: 7,
        paddingRight: 7,
        direction: "column",
        alignItems: "stretch",
        children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 6, children: [
          (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 2, children: [
            (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
              id: getTrad("Plugins.header.title"),
              defaultMessage: "Permissions"
            }) }),
            (0, import_jsx_runtime.jsx)(Typography, { tag: "p", textColor: "neutral600", children: formatMessage({
              id: getTrad("Plugins.header.description"),
              defaultMessage: "Only actions bound by a route are listed below."
            }) })
          ] }),
          (0, import_jsx_runtime.jsx)(Permissions, {})
        ] })
      }
    ),
    (0, import_jsx_runtime.jsx)(Policies, {})
  ] }) });
});
UsersPermissions.propTypes = {
  permissions: import_prop_types.default.object.isRequired,
  routes: import_prop_types.default.object.isRequired
};
var UsersPermissions$1 = (0, import_react.memo)(UsersPermissions);
var createRoleSchema = create2().shape({
  name: create().required(errorsTrads.required.id),
  description: create().required(errorsTrads.required.id)
});
var cleanPermissions = (permissions) => Object.keys(permissions).reduce((acc, current) => {
  const currentPermission = permissions[current].controllers;
  const cleanedControllers = Object.keys(currentPermission).reduce((acc2, curr) => {
    if ((0, import_isEmpty.default)(currentPermission[curr])) {
      return acc2;
    }
    acc2[curr] = currentPermission[curr];
    return acc2;
  }, {});
  if ((0, import_isEmpty.default)(cleanedControllers)) {
    return acc;
  }
  acc[current] = { controllers: cleanedControllers };
  return acc;
}, {});
var usePlugins = () => {
  const { toggleNotification } = useNotification();
  const { get: get2 } = useFetchClient();
  const { formatAPIError } = useAPIErrorHandler(getTrad);
  const [
    {
      data: permissions,
      isLoading: isLoadingPermissions,
      error: permissionsError,
      refetch: refetchPermissions
    },
    { data: routes, isLoading: isLoadingRoutes, error: routesError, refetch: refetchRoutes }
  ] = useQueries([
    {
      queryKey: ["users-permissions", "permissions"],
      async queryFn() {
        const {
          data: { permissions: permissions2 }
        } = await get2(`/users-permissions/permissions`);
        return permissions2;
      }
    },
    {
      queryKey: ["users-permissions", "routes"],
      async queryFn() {
        const {
          data: { routes: routes2 }
        } = await get2(`/users-permissions/routes`);
        return routes2;
      }
    }
  ]);
  const refetchQueries = async () => {
    await Promise.all([refetchPermissions(), refetchRoutes()]);
  };
  (0, import_react.useEffect)(() => {
    if (permissionsError) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(permissionsError)
      });
    }
  }, [toggleNotification, permissionsError, formatAPIError]);
  (0, import_react.useEffect)(() => {
    if (routesError) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(routesError)
      });
    }
  }, [toggleNotification, routesError, formatAPIError]);
  const isLoading = isLoadingPermissions || isLoadingRoutes;
  return {
    // TODO: these return values need to be memoized, otherwise
    // they will create infinite rendering loops when used as
    // effect dependencies
    permissions: permissions ? cleanPermissions(permissions) : {},
    routes: routes ?? {},
    getData: refetchQueries,
    isLoading
  };
};
var CreatePage = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const navigate = useNavigate();
  const { isLoading: isLoadingPlugins, permissions, routes } = usePlugins();
  const { trackUsage } = useTracking();
  const permissionsRef = React.useRef();
  const { post } = useFetchClient();
  const mutation = useMutation((body) => post(`/users-permissions/roles`, body), {
    onError() {
      toggleNotification({
        type: "danger",
        message: formatMessage({
          id: "notification.error",
          defaultMessage: "An error occurred"
        })
      });
    },
    onSuccess() {
      trackUsage("didCreateRole");
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTrad("Settings.roles.created"),
          defaultMessage: "Role created"
        })
      });
      navigate(-1);
    }
  });
  const handleCreateRoleSubmit = async (data) => {
    const permissions2 = permissionsRef.current.getPermissions();
    await mutation.mutate({ ...data, ...permissions2, users: [] });
  };
  return (0, import_jsx_runtime.jsxs)(Main, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      { name: "Roles" }
    ) }),
    (0, import_jsx_runtime.jsx)(
      Formik,
      {
        enableReinitialize: true,
        initialValues: { name: "", description: "" },
        onSubmit: handleCreateRoleSubmit,
        validationSchema: createRoleSchema,
        children: ({ handleSubmit, values, handleChange, errors }) => (0, import_jsx_runtime.jsxs)(Form, { noValidate: true, onSubmit: handleSubmit, children: [
          (0, import_jsx_runtime.jsx)(
            Layouts.Header,
            {
              primaryAction: !isLoadingPlugins && (0, import_jsx_runtime.jsx)(Button, { type: "submit", loading: mutation.isLoading, startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}), children: formatMessage({
                id: "global.save",
                defaultMessage: "Save"
              }) }),
              title: formatMessage({
                id: "Settings.roles.create.title",
                defaultMessage: "Create a role"
              }),
              subtitle: formatMessage({
                id: "Settings.roles.create.description",
                defaultMessage: "Define the rights given to the role"
              })
            }
          ),
          (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(
            Flex,
            {
              background: "neutral0",
              direction: "column",
              alignItems: "stretch",
              gap: 7,
              hasRadius: true,
              paddingTop: 6,
              paddingBottom: 6,
              paddingLeft: 7,
              paddingRight: 7,
              shadow: "filterShadow",
              children: [
                (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", children: [
                  (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
                    id: getTrad("EditPage.form.roles"),
                    defaultMessage: "Role details"
                  }) }),
                  (0, import_jsx_runtime.jsxs)(Grid.Root, { gap: 4, children: [
                    (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
                      Field.Root,
                      {
                        name: "name",
                        error: (errors == null ? void 0 : errors.name) ? formatMessage({ id: errors.name, defaultMessage: "Name is required" }) : false,
                        required: true,
                        children: [
                          (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                            id: "global.name",
                            defaultMessage: "Name"
                          }) }),
                          (0, import_jsx_runtime.jsx)(TextInput, { value: values.name || "", onChange: handleChange }),
                          (0, import_jsx_runtime.jsx)(Field.Error, {})
                        ]
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
                      Field.Root,
                      {
                        name: "description",
                        error: (errors == null ? void 0 : errors.description) ? formatMessage({
                          id: errors.description,
                          defaultMessage: "Description is required"
                        }) : false,
                        required: true,
                        children: [
                          (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                            id: "global.description",
                            defaultMessage: "Description"
                          }) }),
                          (0, import_jsx_runtime.jsx)(Textarea, { value: values.description || "", onChange: handleChange }),
                          (0, import_jsx_runtime.jsx)(Field.Error, {})
                        ]
                      }
                    ) })
                  ] })
                ] }),
                !isLoadingPlugins && (0, import_jsx_runtime.jsx)(
                  UsersPermissions$1,
                  {
                    ref: permissionsRef,
                    permissions,
                    routes
                  }
                )
              ]
            }
          ) })
        ] })
      }
    )
  ] });
};
var ProtectedRolesCreatePage = () => (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.createRole, children: (0, import_jsx_runtime.jsx)(CreatePage, {}) });
var EditPage = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const {
    params: { id }
  } = useMatch(`/settings/users-permissions/roles/:id`);
  const { get: get2 } = useFetchClient();
  const { isLoading: isLoadingPlugins, routes } = usePlugins();
  const {
    data: role,
    isLoading: isLoadingRole,
    refetch: refetchRole
  } = useQuery(["users-permissions", "role", id], async () => {
    const {
      data: { role: role2 }
    } = await get2(`/users-permissions/roles/${id}`);
    return role2;
  });
  const permissionsRef = React.useRef();
  const { put } = useFetchClient();
  const { formatAPIError } = useAPIErrorHandler();
  const mutation = useMutation((body) => put(`/users-permissions/roles/${id}`, body), {
    onError(error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    },
    async onSuccess() {
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTrad("Settings.roles.created"),
          defaultMessage: "Role edited"
        })
      });
      await refetchRole();
    }
  });
  const handleEditRoleSubmit = async (data) => {
    const permissions = permissionsRef.current.getPermissions();
    await mutation.mutate({ ...data, ...permissions, users: [] });
  };
  if (isLoadingRole) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Main, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      { name: "Roles" }
    ) }),
    (0, import_jsx_runtime.jsx)(
      Formik,
      {
        enableReinitialize: true,
        initialValues: { name: role.name, description: role.description },
        onSubmit: handleEditRoleSubmit,
        validationSchema: createRoleSchema,
        children: ({ handleSubmit, values, handleChange, errors }) => (0, import_jsx_runtime.jsxs)(Form, { noValidate: true, onSubmit: handleSubmit, children: [
          (0, import_jsx_runtime.jsx)(
            Layouts.Header,
            {
              primaryAction: !isLoadingPlugins ? (0, import_jsx_runtime.jsx)(
                Button,
                {
                  disabled: role.code === "strapi-super-admin",
                  type: "submit",
                  loading: mutation.isLoading,
                  startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
                  children: formatMessage({
                    id: "global.save",
                    defaultMessage: "Save"
                  })
                }
              ) : null,
              title: role.name,
              subtitle: role.description,
              navigationAction: (0, import_jsx_runtime.jsx)(BackButton, {})
            }
          ),
          (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsxs)(
            Flex,
            {
              background: "neutral0",
              direction: "column",
              alignItems: "stretch",
              gap: 7,
              hasRadius: true,
              paddingTop: 6,
              paddingBottom: 6,
              paddingLeft: 7,
              paddingRight: 7,
              shadow: "filterShadow",
              children: [
                (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
                  (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
                    id: getTrad("EditPage.form.roles"),
                    defaultMessage: "Role details"
                  }) }),
                  (0, import_jsx_runtime.jsxs)(Grid.Root, { gap: 4, children: [
                    (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
                      Field.Root,
                      {
                        name: "name",
                        error: (errors == null ? void 0 : errors.name) ? formatMessage({
                          id: errors.name,
                          defaultMessage: "Name is required"
                        }) : false,
                        required: true,
                        children: [
                          (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                            id: "global.name",
                            defaultMessage: "Name"
                          }) }),
                          (0, import_jsx_runtime.jsx)(TextInput, { value: values.name || "", onChange: handleChange }),
                          (0, import_jsx_runtime.jsx)(Field.Error, {})
                        ]
                      }
                    ) }),
                    (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
                      Field.Root,
                      {
                        name: "description",
                        error: (errors == null ? void 0 : errors.description) ? formatMessage({
                          id: errors.description,
                          defaultMessage: "Description is required"
                        }) : false,
                        required: true,
                        children: [
                          (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                            id: "global.description",
                            defaultMessage: "Description"
                          }) }),
                          (0, import_jsx_runtime.jsx)(Textarea, { value: values.description || "", onChange: handleChange }),
                          (0, import_jsx_runtime.jsx)(Field.Error, {})
                        ]
                      }
                    ) })
                  ] })
                ] }),
                !isLoadingPlugins && (0, import_jsx_runtime.jsx)(
                  UsersPermissions$1,
                  {
                    ref: permissionsRef,
                    permissions: role.permissions,
                    routes
                  }
                )
              ]
            }
          ) })
        ] })
      }
    )
  ] });
};
var ProtectedRolesEditPage = () => (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.updateRole, children: (0, import_jsx_runtime.jsx)(EditPage, {}) });
var EditLink = dt(Link)`
  align-items: center;
  height: 3.2rem;
  width: 3.2rem;
  display: flex;
  justify-content: center;
  padding: ${({ theme }) => `${theme.spaces[2]}`};

  svg {
    height: 1.6rem;
    width: 1.6rem;

    path {
      fill: ${({ theme }) => theme.colors.neutral500};
    }
  }

  &:hover,
  &:focus {
    svg {
      path {
        fill: ${({ theme }) => theme.colors.neutral800};
      }
    }
  }
`;
var TableBody = ({ sortedRoles, canDelete, canUpdate, setRoleToDelete, onDelete }) => {
  const { formatMessage } = useIntl();
  const navigate = useNavigate();
  const [showConfirmDelete, setShowConfirmDelete] = onDelete;
  const checkCanDeleteRole = (role) => canDelete && !["public", "authenticated"].includes(role.type);
  const handleClickDelete = (id) => {
    setRoleToDelete(id);
    setShowConfirmDelete(!showConfirmDelete);
  };
  return (0, import_jsx_runtime.jsx)(Tbody, { children: sortedRoles == null ? void 0 : sortedRoles.map((role) => (0, import_jsx_runtime.jsxs)(Tr, { onClick: () => navigate(role.id.toString()), children: [
    (0, import_jsx_runtime.jsx)(Td, { width: "20%", children: (0, import_jsx_runtime.jsx)(Typography, { children: role.name }) }),
    (0, import_jsx_runtime.jsx)(Td, { width: "50%", children: (0, import_jsx_runtime.jsx)(Typography, { children: role.description }) }),
    (0, import_jsx_runtime.jsx)(Td, { width: "30%", children: (0, import_jsx_runtime.jsx)(Typography, { children: formatMessage(
      {
        id: "Roles.RoleRow.user-count",
        defaultMessage: "{number, plural, =0 {# user} one {# user} other {# users}}"
      },
      { number: role.nb_users }
    ) }) }),
    (0, import_jsx_runtime.jsx)(Td, { children: (0, import_jsx_runtime.jsxs)(Flex, { justifyContent: "end", onClick: (e) => e.stopPropagation(), children: [
      canUpdate ? (0, import_jsx_runtime.jsx)(
        EditLink,
        {
          tag: NavLink,
          to: role.id.toString(),
          "aria-label": formatMessage(
            { id: "app.component.table.edit", defaultMessage: "Edit {target}" },
            { target: `${role.name}` }
          ),
          children: (0, import_jsx_runtime.jsx)(ForwardRef$1r, {})
        }
      ) : null,
      checkCanDeleteRole(role) && (0, import_jsx_runtime.jsx)(
        IconButton,
        {
          onClick: () => handleClickDelete(role.id.toString()),
          variant: "ghost",
          label: formatMessage(
            { id: "global.delete-target", defaultMessage: "Delete {target}" },
            { target: `${role.name}` }
          ),
          children: (0, import_jsx_runtime.jsx)(ForwardRef$j, {})
        }
      )
    ] }) })
  ] }, role.name)) });
};
TableBody.defaultProps = {
  canDelete: false,
  canUpdate: false
};
TableBody.propTypes = {
  onDelete: import_prop_types.default.array.isRequired,
  setRoleToDelete: import_prop_types.default.func.isRequired,
  sortedRoles: import_prop_types.default.array.isRequired,
  canDelete: import_prop_types.default.bool,
  canUpdate: import_prop_types.default.bool
};
var RolesListPage = () => {
  const { trackUsage } = useTracking();
  const { formatMessage, locale } = useIntl();
  const { toggleNotification } = useNotification();
  const { notifyStatus } = useNotifyAT();
  const [{ query }] = useQueryParams();
  const _q = (query == null ? void 0 : query._q) || "";
  const [showConfirmDelete, setShowConfirmDelete] = (0, import_react.useState)(false);
  const [roleToDelete, setRoleToDelete] = (0, import_react.useState)();
  const { del, get: get2 } = useFetchClient();
  const {
    isLoading: isLoadingForPermissions,
    allowedActions: { canRead, canDelete, canCreate, canUpdate }
  } = useRBAC({
    create: PERMISSIONS.createRole,
    read: PERMISSIONS.readRoles,
    update: PERMISSIONS.updateRole,
    delete: PERMISSIONS.deleteRole
  });
  const {
    isLoading: isLoadingForData,
    data: { roles },
    isFetching,
    refetch
  } = useQuery("get-roles", () => fetchData(toggleNotification, formatMessage, notifyStatus), {
    initialData: {},
    enabled: canRead
  });
  const { contains } = useFilter(locale, {
    sensitivity: "base"
  });
  const formatter = useCollator(locale, {
    sensitivity: "base"
  });
  const isLoading = isLoadingForData || isFetching || isLoadingForPermissions;
  const handleShowConfirmDelete = () => {
    setShowConfirmDelete(!showConfirmDelete);
  };
  const deleteData = async (id, formatMessage2, toggleNotification2) => {
    try {
      await del(`/users-permissions/roles/${id}`);
    } catch (error) {
      toggleNotification2({
        type: "danger",
        message: formatMessage2({ id: "notification.error", defaultMessage: "An error occured" })
      });
    }
  };
  const fetchData = async (toggleNotification2, formatMessage2, notifyStatus2) => {
    try {
      const { data } = await get2("/users-permissions/roles");
      notifyStatus2("The roles have loaded successfully");
      return data;
    } catch (err) {
      toggleNotification2({
        type: "danger",
        message: formatMessage2({ id: "notification.error", defaultMessage: "An error occurred" })
      });
      throw new Error(err);
    }
  };
  const emptyLayout = {
    roles: {
      id: getTrad("Roles.empty"),
      defaultMessage: "You don't have any roles yet."
    },
    search: {
      id: getTrad("Roles.empty.search"),
      defaultMessage: "No roles match the search."
    }
  };
  const pageTitle = formatMessage({
    id: "global.roles",
    defaultMessage: "Roles"
  });
  const deleteMutation = useMutation((id) => deleteData(id, formatMessage, toggleNotification), {
    async onSuccess() {
      await refetch();
    }
  });
  const handleConfirmDelete = async () => {
    await deleteMutation.mutateAsync(roleToDelete);
    setShowConfirmDelete(!showConfirmDelete);
  };
  const sortedRoles = (roles || []).filter((role) => contains(role.name, _q) || contains(role.description, _q)).sort(
    (a, b) => formatter.compare(a.name, b.name) || formatter.compare(a.description, b.description)
  );
  const emptyContent = _q && !sortedRoles.length ? "search" : "roles";
  const colCount = 4;
  const rowCount = ((roles == null ? void 0 : roles.length) || 0) + 1;
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Layouts.Root, { children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      { name: pageTitle }
    ) }),
    (0, import_jsx_runtime.jsxs)(Page.Main, { children: [
      (0, import_jsx_runtime.jsx)(
        Layouts.Header,
        {
          title: formatMessage({
            id: "global.roles",
            defaultMessage: "Roles"
          }),
          subtitle: formatMessage({
            id: "Settings.roles.list.description",
            defaultMessage: "List of roles"
          }),
          primaryAction: canCreate ? (0, import_jsx_runtime.jsx)(
            LinkButton,
            {
              to: "new",
              tag: NavLink,
              onClick: () => trackUsage("willCreateRole"),
              startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$1d, {}),
              size: "S",
              children: formatMessage({
                id: getTrad("List.button.roles"),
                defaultMessage: "Add new role"
              })
            }
          ) : null
        }
      ),
      (0, import_jsx_runtime.jsx)(
        Layouts.Action,
        {
          startActions: (0, import_jsx_runtime.jsx)(
            SearchInput,
            {
              label: formatMessage({
                id: "app.component.search.label",
                defaultMessage: "Search"
              })
            }
          )
        }
      ),
      (0, import_jsx_runtime.jsxs)(Layouts.Content, { children: [
        !canRead && (0, import_jsx_runtime.jsx)(Page.NoPermissions, {}),
        canRead && sortedRoles && (sortedRoles == null ? void 0 : sortedRoles.length) ? (0, import_jsx_runtime.jsxs)(Table, { colCount, rowCount, children: [
          (0, import_jsx_runtime.jsx)(Thead, { children: (0, import_jsx_runtime.jsxs)(Tr, { children: [
            (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({ id: "global.name", defaultMessage: "Name" }) }) }),
            (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({
              id: "global.description",
              defaultMessage: "Description"
            }) }) }),
            (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "sigma", textColor: "neutral600", children: formatMessage({
              id: "global.users",
              defaultMessage: "Users"
            }) }) }),
            (0, import_jsx_runtime.jsx)(Th, { children: (0, import_jsx_runtime.jsx)(VisuallyHidden, { children: formatMessage({
              id: "global.actions",
              defaultMessage: "Actions"
            }) }) })
          ] }) }),
          (0, import_jsx_runtime.jsx)(
            TableBody,
            {
              sortedRoles,
              canDelete,
              canUpdate,
              permissions: PERMISSIONS,
              setRoleToDelete,
              onDelete: [showConfirmDelete, setShowConfirmDelete]
            }
          )
        ] }) : (0, import_jsx_runtime.jsx)(EmptyStateLayout, { content: formatMessage(emptyLayout[emptyContent]) })
      ] }),
      (0, import_jsx_runtime.jsx)(Dialog.Root, { open: showConfirmDelete, onOpenChange: handleShowConfirmDelete, children: (0, import_jsx_runtime.jsx)(ConfirmDialog, { onConfirm: handleConfirmDelete }) })
    ] })
  ] });
};
var ProtectedRolesListPage = () => {
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.accessRoles, children: (0, import_jsx_runtime.jsx)(RolesListPage, {}) });
};
var Roles = () => {
  return (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.accessRoles, children: (0, import_jsx_runtime.jsxs)(Routes, { children: [
    (0, import_jsx_runtime.jsx)(Route, { index: true, element: (0, import_jsx_runtime.jsx)(ProtectedRolesListPage, {}) }),
    (0, import_jsx_runtime.jsx)(Route, { path: "new", element: (0, import_jsx_runtime.jsx)(ProtectedRolesCreatePage, {}) }),
    (0, import_jsx_runtime.jsx)(Route, { path: ":id", element: (0, import_jsx_runtime.jsx)(ProtectedRolesEditPage, {}) })
  ] }) });
};
export {
  Roles as default
};
//# sourceMappingURL=index-ClNxNeM--LS7HCR6F.js.map
