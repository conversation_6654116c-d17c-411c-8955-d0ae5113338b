{"version": 3, "sources": ["../../../@strapi/content-manager/dist/_chunks/pt-BR-C71iDxnh.mjs"], "sourcesContent": ["const groups = \"Grupos\";\nconst models = \"Tipos de Coleção\";\nconst pageNotFound = \"Página não encontrada\";\nconst ptBR = {\n  \"App.schemas.data-loaded\": \"Os esquemas foram carregados com sucesso\",\n  \"ListViewTable.relation-loaded\": \"Os relacionamentos foram carregados\",\n  \"ListViewTable.relation-loading\": \"As relações estão carregando\",\n  \"ListViewTable.relation-more\": \"Esta relação contém mais entidades do que as exibidas\",\n  \"EditRelations.title\": \"Dados relacionais\",\n  \"HeaderLayout.button.label-add-entry\": \"Criar novo registro\",\n  \"api.id\": \"API ID\",\n  \"components.AddFilterCTA.add\": \"Filtros\",\n  \"components.AddFilterCTA.hide\": \"Filtros\",\n  \"components.DragHandle-label\": \"Arrastar\",\n  \"components.DraggableAttr.edit\": \"Clique para editar\",\n  \"components.DraggableCard.delete.field\": \"Remover {item}\",\n  \"components.DraggableCard.edit.field\": \"Editar {item}\",\n  \"components.DraggableCard.move.field\": \"Mover {item}\",\n  \"components.ListViewTable.row-line\": \"item {number}\",\n  \"components.DynamicZone.ComponentPicker-label\": \"Selecione um componente\",\n  \"components.DynamicZone.add-component\": \"Adicionar componente a {componentName}\",\n  \"components.DynamicZone.delete-label\": \"Remover {name}\",\n  \"components.DynamicZone.error-message\": \"O componente contém erro(s)\",\n  \"components.DynamicZone.missing-components\": \"Há {number, plural, =0 {# componentes faltando} one {# componente faltando} other {# componentes faltando}}\",\n  \"components.DynamicZone.move-down-label\": \"Mover pra cima\",\n  \"components.DynamicZone.move-up-label\": \"Mover pra baixo\",\n  \"components.DynamicZone.pick-compo\": \"Selecione um componente\",\n  \"components.DynamicZone.required\": \"Um componente é necessário\",\n  \"components.EmptyAttributesBlock.button\": \"Ir para página de configurações\",\n  \"components.EmptyAttributesBlock.description\": \"Você pode alterar suas configurações\",\n  \"components.FieldItem.linkToComponentLayout\": \"Definir layout do componente\",\n  \"components.FieldSelect.label\": \"Adicionar um campo\",\n  \"components.FilterOptions.button.apply\": \"Aplicar\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"Aplicar\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"Limpar tudo\",\n  \"components.FiltersPickWrapper.PluginHeader.description\": \"Definir as condições a serem aplicadas para filtrar os registros\",\n  \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"Filtros\",\n  \"components.FiltersPickWrapper.hide\": \"Esconder\",\n  \"components.LeftMenu.Search.label\": \"Procurar um tipo de conteúdo\",\n  \"components.LeftMenu.collection-types\": \"Tipos de Coleção\",\n  \"components.LeftMenu.single-types\": \"Tipos Singulares\",\n  \"components.LimitSelect.itemsPerPage\": \"Registros por página\",\n  \"components.NotAllowedInput.text\": \"Sem permissão para ver esse campo\",\n  \"components.RepeatableComponent.error-message\": \"Um ou mais componentes contêm erros\",\n  \"components.Search.placeholder\": \"Buscar registro...\",\n  \"components.Select.draft-info-title\": \"Estado: Rascunho\",\n  \"components.Select.publish-info-title\": \"Estado: Publicado\",\n  \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"Customizar visualização de edição.\",\n  \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"Customizar visualização de lista.\",\n  \"components.SettingsViewWrapper.pluginHeader.title\": \"Customizar visualização - {name}\",\n  \"components.TableDelete.delete\": \"Remover tudo\",\n  \"components.TableDelete.deleteSelected\": \"Remover selecionado\",\n  \"components.TableDelete.label\": \"{number, plural, one {# registro} other {# registros}} selecionados\",\n  \"components.TableEmpty.withFilters\": \"Nenhum {contentType} com os filtros aplicados...\",\n  \"components.TableEmpty.withSearch\": \"Nenhum {contentType} encontrado na pesquisa ({search})...\",\n  \"components.TableEmpty.withoutFilter\": \"Nenhum {contentType}...\",\n  \"components.empty-repeatable\": \"Nenhum registro ainda. Clique no botão abaixo para adicionar um.\",\n  \"components.notification.info.maximum-requirement\": \"Você atingiu o número máximo de campos\",\n  \"components.notification.info.minimum-requirement\": \"Um campo foi criado para atender aos requisitos mínimos\",\n  \"components.repeatable.reorder.error\": \"Um erro ocorreu ao reordenar o campo do seu componente. Por favor, tente novamente.\",\n  \"components.reset-entry\": \"Reiniciar\",\n  \"components.uid.apply\": \"aplicar\",\n  \"components.uid.available\": \"Disponível\",\n  \"components.uid.regenerate\": \"Regerar\",\n  \"components.uid.suggested\": \"sugerido\",\n  \"components.uid.unavailable\": \"Indisponível\",\n  \"containers.Edit.Link.Layout\": \"Configurar o layout\",\n  \"containers.Edit.Link.Model\": \"Editar o tipo de coleção\",\n  \"containers.Edit.addAnItem\": \"Adicione um item...\",\n  \"containers.Edit.clickToJump\": \"Clique para pular para o registro\",\n  \"containers.Edit.delete\": \"Remover\",\n  \"containers.Edit.delete-entry\": \"Remover este registro\",\n  \"containers.Edit.editing\": \"Editando...\",\n  \"containers.Edit.information\": \"Informação\",\n  \"containers.Edit.information.by\": \"Por\",\n  \"containers.Edit.information.created\": \"Criado\",\n  \"containers.Edit.information.draftVersion\": \"versão rascunho\",\n  \"containers.Edit.information.editing\": \"Editando\",\n  \"containers.Edit.information.lastUpdate\": \"Última atualização\",\n  \"containers.Edit.information.publishedVersion\": \"versão publicada\",\n  \"containers.Edit.pluginHeader.title.new\": \"Criar um registro\",\n  \"containers.Edit.reset\": \"Reiniciar\",\n  \"containers.Edit.returnList\": \"Retornar à lista\",\n  \"containers.Edit.seeDetails\": \"Detalhes\",\n  \"containers.Edit.submit\": \"Salvar\",\n  \"containers.EditSettingsView.modal-form.edit-field\": \"Editar o campo\",\n  \"containers.EditView.add.new-entry\": \"Adicionar um registro\",\n  \"containers.EditView.notification.errors\": \"O formulário contém erros\",\n  \"containers.Home.introduction\": \"Para editar seus registros, acesse o link específico no menu à esquerda. Esta extensão não permite editar configurações, ainda está em desenvolvimento.\",\n  \"containers.Home.pluginHeaderDescription\": \"Gerencie seus registros através de uma interface poderosa e elegante.\",\n  \"containers.Home.pluginHeaderTitle\": \"Gestão de conteúdos\",\n  \"containers.List.draft\": \"Rascunho\",\n  \"containers.List.errorFetchRecords\": \"Erro\",\n  \"containers.List.published\": \"Publicado\",\n  \"containers.list.displayedFields\": \"Campos exibidos\",\n  \"containers.list.items\": \"{number, plural, =0 {itens} one {item} other {itens}}\",\n  \"containers.list.table-headers.publishedAt\": \"Estado\",\n  \"containers.ListSettingsView.modal-form.edit-label\": \"Editar {fieldName}\",\n  \"containers.SettingPage.add.field\": \"Criar outro campo\",\n  \"containers.SettingPage.attributes\": \"Atributos\",\n  \"containers.SettingPage.attributes.description\": \"Define a ordem dos atributos\",\n  \"containers.SettingPage.editSettings.description\": \"Arraste e solte os campos para construir o layout\",\n  \"containers.SettingPage.editSettings.entry.title\": \"Título do registro\",\n  \"containers.SettingPage.editSettings.entry.title.description\": \"Defina o campo exibido do registro\",\n  \"containers.SettingPage.editSettings.relation-field.description\": \"Defina o campo exibido nas visualizações de edição e lista\",\n  \"containers.SettingPage.editSettings.title\": \"Editar (configurações)\",\n  \"containers.SettingPage.layout\": \"Layout\",\n  \"containers.SettingPage.listSettings.description\": \"Configurar as opções desse tipo de coleção\",\n  \"containers.SettingPage.listSettings.title\": \"Lista (configurações)\",\n  \"containers.SettingPage.pluginHeaderDescription\": \"Configurar as opções específicas desse tipo de coleção\",\n  \"containers.SettingPage.settings\": \"Configurações\",\n  \"containers.SettingPage.view\": \"Visualização\",\n  \"containers.SettingViewModel.pluginHeader.title\": \"Gestão de Conteúdo - {name}\",\n  \"containers.SettingsPage.Block.contentType.description\": \"Defina as configurações específicas\",\n  \"containers.SettingsPage.Block.contentType.title\": \"Tipos de Coleção\",\n  \"containers.SettingsPage.Block.generalSettings.description\": \"Configurar opções padrão para seus Tipos de Coleção\",\n  \"containers.SettingsPage.Block.generalSettings.title\": \"Geral\",\n  \"containers.SettingsPage.pluginHeaderDescription\": \"Configurar opções para todos os seus Tipos de Coleção e Grupos\",\n  \"containers.SettingsView.list.subtitle\": \"Configurar layout e exibição dos seus Tipos de Coleção e Grupos\",\n  \"containers.SettingsView.list.title\": \"Exibir configurações\",\n  \"edit-settings-view.link-to-ctb.components\": \"Editar o componente\",\n  \"edit-settings-view.link-to-ctb.content-types\": \"Editar o tipo de conteúdo\",\n  \"emptyAttributes.button\": \"Ir para o criador de Tipo de Coleção\",\n  \"emptyAttributes.description\": \"Adicione o primeiro campo ao seu Tipo de Coleção\",\n  \"emptyAttributes.title\": \"Nenhum campo\",\n  \"error.attribute.key.taken\": \"Este valor já existe\",\n  \"error.attribute.sameKeyAndName\": \"Não pode ser igual\",\n  \"error.attribute.taken\": \"O nome deste campo já existe\",\n  \"error.contentTypeName.taken\": \"Este nome já existe\",\n  \"error.model.fetch\": \"Ocorreu um erro durante a configuração dos modelos de busca.\",\n  \"error.record.create\": \"Ocorreu um erro durante a criação de registro.\",\n  \"error.record.delete\": \"Ocorreu um erro durante a remoção do registro.\",\n  \"error.record.fetch\": \"Ocorreu um erro durante o registro de busca.\",\n  \"error.record.update\": \"Ocorreu um erro durante a atualização do registro.\",\n  \"error.records.count\": \"Ocorreu um erro durante a contagem de registros da buscar.\",\n  \"error.records.fetch\": \"Ocorreu um erro durante os registros de busca.\",\n  \"error.schema.generation\": \"Ocorreu um erro durante a geração dos esquemas.\",\n  \"error.validation.json\": \"Isto não corresponde com o formato JSON\",\n  \"error.validation.max\": \"O valor é muito alto.\",\n  \"error.validation.maxLength\": \"O valor é muito logo.\",\n  \"error.validation.min\": \"O valor é muito baixo.\",\n  \"error.validation.minLength\": \"O valor é muito curto.\",\n  \"error.validation.minSupMax\": \"Não pode ser superior\",\n  \"error.validation.regex\": \"Este valor não corresponde ao regex.\",\n  \"error.validation.required\": \"O valor deste registro é obrigatório.\",\n  \"form.Input.bulkActions\": \"Habilitar ações em lote\",\n  \"form.Input.defaultSort\": \"Atributo de ordenação padrão\",\n  \"form.Input.description\": \"Descrição\",\n  \"form.Input.description.placeholder\": \"Nome exibido no perfil\",\n  \"form.Input.editable\": \"Campo editável\",\n  \"form.Input.filters\": \"Habilitar filtros\",\n  \"form.Input.label\": \"Rótulo\",\n  \"form.Input.label.inputDescription\": \"Este valor substitui o rótulo apresentado no cabeçalho da tabela\",\n  \"form.Input.pageEntries\": \"Entradas por página\",\n  \"form.Input.pageEntries.inputDescription\": \"Nota: Voce pode redefinir esse valor na página de configuração dos Tipos de Coleção.\",\n  \"form.Input.placeholder\": \"Placeholder\",\n  \"form.Input.placeholder.placeholder\": \"Meu valor incrível\",\n  \"form.Input.search\": \"Habilitar busca\",\n  \"form.Input.search.field\": \"Habilitar busca neste campo\",\n  \"form.Input.sort.field\": \"Habilitar ordenação neste campo\",\n  \"form.Input.sort.order\": \"Ordenação padrão\",\n  \"form.Input.wysiwyg\": \"Mostrar como WYSIWYG\",\n  \"global.displayedFields\": \"Campos exibidos\",\n  groups,\n  \"groups.numbered\": \"Grupos ({number})\",\n  \"header.name\": \"Conteúdo\",\n  \"link-to-ctb\": \"Editar o modelo\",\n  models,\n  \"models.numbered\": \"Tipos de Coleção ({number})\",\n  \"notification.error.displayedFields\": \"Você precisa ao menos um campo exibido\",\n  \"notification.error.relationship.fetch\": \"Ocorreu um erro durante a busca do relacionamento.\",\n  \"notification.info.SettingPage.disableSort\": \"Você precisa de um atributo com permissão de ordenação\",\n  \"notification.info.minimumFields\": \"Você precisa ter pelo menos um campo exibido\",\n  \"notification.upload.error\": \"Ocorreu um erro ao fazer upload dos seus arquivos\",\n  pageNotFound,\n  \"pages.ListView.header-subtitle\": \"{number, plural, =0 {# registros encontrados} one {# registro encontrado} other {# registros encontrados}}\",\n  \"pages.NoContentType.button\": \"Criar seu primeiro Tipo de Conteúdo\",\n  \"pages.NoContentType.text\": \"Você ainda não tem nenhum conteúdo. Recomendamos que você crie seu primeiro Tipo de Conteúdo.\",\n  \"permissions.not-allowed.create\": \"Você não tem permissão para criar documentos\",\n  \"permissions.not-allowed.update\": \"Você não tem permissão para ver esse documento\",\n  \"plugin.description.long\": \"Maneira rápida de ver, editar e excluir os dados em seu banco de dados.\",\n  \"plugin.description.short\": \"Maneira rápida de ver, editar e excluir os dados em seu banco de dados.\",\n  \"popover.display-relations.label\": \"Exibir relacionamentos\",\n  \"select.currently.selected\": \"{count} selecionado\",\n  \"success.record.delete\": \"Removido\",\n  \"success.record.publish\": \"Publicado\",\n  \"success.record.save\": \"Salvo\",\n  \"success.record.unpublish\": \"Despublicado\",\n  \"utils.data-loaded\": \"{number, plural, =1 {O registro foi carregado} other {Os registros foram carregados}} com sucesso\",\n  \"apiError.This attribute must be unique\": \"{field} deve ser único\",\n  \"popUpWarning.warning.publish-question\": \"Você ainda quer publicar esse conteúdo?\",\n  \"popUpwarning.warning.has-draft-relations.button-confirm\": \"Sim, publicar\",\n  \"popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, =0 { conteúdos relacionados não estão publicados} one { conteúdo relacionado não está publicado} other { conteúdos relacionados não estão publicados}}</b>.<br></br>Isso pode acarretar em links quebrados e erros em seu projeto.\"\n};\nexport {\n  ptBR as default,\n  groups,\n  models,\n  pageNotFound\n};\n//# sourceMappingURL=pt-BR-C71iDxnh.mjs.map\n"], "mappings": ";;;AAAA,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,OAAO;AAAA,EACX,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,2DAA2D;AAAA,EAC3D,oDAAoD;AACtD;", "names": []}