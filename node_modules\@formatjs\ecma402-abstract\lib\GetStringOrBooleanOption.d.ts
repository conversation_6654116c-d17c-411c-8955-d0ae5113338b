/**
 * https://tc39.es/ecma402/#sec-getstringorbooleanoption
 * @param opts
 * @param prop
 * @param values
 * @param trueValue
 * @param falsyValue
 * @param fallback
 */
export declare function GetStringOrBooleanOption<T extends object, K extends keyof T>(opts: T, prop: K, values: T[K][] | undefined, trueValue: T[K] | boolean, falsyValue: T[K] | boolean, fallback: T[K] | boolean): T[K] | boolean;
//# sourceMappingURL=GetStringOrBooleanOption.d.ts.map