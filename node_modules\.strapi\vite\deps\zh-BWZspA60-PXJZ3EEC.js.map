{"version": 3, "sources": ["../../../@strapi/content-manager/dist/_chunks/zh-BWZspA60.mjs"], "sourcesContent": ["const groups = \"群組\";\nconst models = \"集合型別\";\nconst pageNotFound = \"無法找到此頁面\";\nconst zh = {\n  \"App.schemas.data-loaded\": \"這個 schemas 已經成功載入\",\n  \"ListViewTable.relation-loaded\": \"關聯已經載入\",\n  \"ListViewTable.relation-loading\": \"關聯正在載入\",\n  \"ListViewTable.relation-more\": \"此關聯包含的實體比已顯示的還多\",\n  \"EditRelations.title\": \"關聯式資料\",\n  \"HeaderLayout.button.label-add-entry\": \"新增項目\",\n  \"api.id\": \"API ID\",\n  \"components.AddFilterCTA.add\": \"篩選器\",\n  \"components.AddFilterCTA.hide\": \"篩選器\",\n  \"components.DragHandle-label\": \"拖曳\",\n  \"components.DraggableAttr.edit\": \"點擊以編輯\",\n  \"components.DraggableCard.delete.field\": \"刪除 {item}\",\n  \"components.DraggableCard.edit.field\": \"編輯 {item}\",\n  \"components.DraggableCard.move.field\": \"移動 {item}\",\n  \"components.ListViewTable.row-line\": \"項 行 {number}\",\n  \"components.DynamicZone.ComponentPicker-label\": \"選一個組件\",\n  \"components.DynamicZone.add-component\": \"新增組件到 {componentName}\",\n  \"components.DynamicZone.delete-label\": \"刪除 {name}\",\n  \"components.DynamicZone.error-message\": \"這個組件有錯誤\",\n  \"components.DynamicZone.missing-components\": \"這裡 {number, plural, =0 {有 # 遺失的組件} one {有 # 遺失的組件} other {有 # 遺失的組件}}\",\n  \"components.DynamicZone.move-down-label\": \"組件往下移動\",\n  \"components.DynamicZone.move-up-label\": \"組件往上移動\",\n  \"components.DynamicZone.pick-compo\": \"選一個組件\",\n  \"components.DynamicZone.required\": \"組件是必填\",\n  \"components.EmptyAttributesBlock.button\": \"前往設定頁面\",\n  \"components.EmptyAttributesBlock.description\": \"您可以變更設定\",\n  \"components.FieldItem.linkToComponentLayout\": \"設定組件的排版\",\n  \"components.FieldSelect.label\": \"加入一個欄位\",\n  \"components.FilterOptions.button.apply\": \"套用\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.apply\": \"套用\",\n  \"components.FiltersPickWrapper.PluginHeader.actions.clearAll\": \"清除\",\n  \"components.FiltersPickWrapper.PluginHeader.description\": \"設定篩選資料的條件\",\n  \"components.FiltersPickWrapper.PluginHeader.title.filter\": \"篩選器\",\n  \"components.FiltersPickWrapper.hide\": \"隱藏\",\n  \"components.LeftMenu.Search.label\": \"搜尋內容型別\",\n  \"components.LeftMenu.collection-types\": \"集合型別\",\n  \"components.LeftMenu.single-types\": \"單筆型別\",\n  \"components.LimitSelect.itemsPerPage\": \"每個頁面檔案數量\",\n  \"components.NotAllowedInput.text\": \"沒有權限顯示這個欄位\",\n  \"components.RepeatableComponent.error-message\": \"這個組件包含錯誤\",\n  \"components.Search.placeholder\": \"搜尋...\",\n  \"components.Select.draft-info-title\": \"狀態: 草稿\",\n  \"components.Select.publish-info-title\": \"狀態: 已發布\",\n  \"components.SettingsViewWrapper.pluginHeader.description.edit-settings\": \"客製化編輯畫面\",\n  \"components.SettingsViewWrapper.pluginHeader.description.list-settings\": \"清單檢視的設定\",\n  \"components.SettingsViewWrapper.pluginHeader.title\": \"檢視設定 - {name}\",\n  \"components.TableDelete.delete\": \"刪除\",\n  \"components.TableDelete.deleteSelected\": \"删除所选\",\n  \"components.TableDelete.label\": \"已選擇 {number, plural, one {# 項目} other {# 項目}}\",\n  \"components.TableEmpty.withFilters\": \"依照套用的篩選器找不到 {contentType}...\",\n  \"components.TableEmpty.withSearch\": \"依照搜尋條件： ({search}) 找不到 {contentType}...\",\n  \"components.TableEmpty.withoutFilter\": \"找不到 {contentType}...\",\n  \"components.empty-repeatable\": \"還沒有項目，點擊下列的按鈕增加一個。\",\n  \"components.notification.info.maximum-requirement\": \"您已經到達欄位數的上限\",\n  \"components.notification.info.minimum-requirement\": \"增加一個欄位以滿足欄位數下限\",\n  \"components.repeatable.reorder.error\": \"重新排序組件欄位時發生錯誤，請再試一次。\",\n  \"components.reset-entry\": \"重設項目\",\n  \"components.uid.apply\": \"套用\",\n  \"components.uid.available\": \"可用\",\n  \"components.uid.regenerate\": \"重新產生\",\n  \"components.uid.suggested\": \"推薦的\",\n  \"components.uid.unavailable\": \"不可用\",\n  \"containers.Edit.Link.Layout\": \"設定排版\",\n  \"containers.Edit.Link.Model\": \"編輯集合型別\",\n  \"containers.Edit.addAnItem\": \"新增關聯...\",\n  \"containers.Edit.clickToJump\": \"跳到該筆資料\",\n  \"containers.Edit.delete\": \"刪除\",\n  \"containers.Edit.delete-entry\": \"刪除這個項目\",\n  \"containers.Edit.editing\": \"編輯中...\",\n  \"containers.Edit.information\": \"資訊\",\n  \"containers.Edit.information.by\": \"操作人\",\n  \"containers.Edit.information.created\": \"已新增\",\n  \"containers.Edit.information.draftVersion\": \"草稿版本\",\n  \"containers.Edit.information.editing\": \"編輯中\",\n  \"containers.Edit.information.lastUpdate\": \"最後更新\",\n  \"containers.Edit.information.publishedVersion\": \"已發布版本\",\n  \"containers.Edit.pluginHeader.title.new\": \"新增項目\",\n  \"containers.Edit.reset\": \"重設\",\n  \"containers.Edit.returnList\": \"回到清單\",\n  \"containers.Edit.seeDetails\": \"詳細資料\",\n  \"containers.Edit.submit\": \"儲存\",\n  \"containers.EditSettingsView.modal-form.edit-field\": \"編輯欄位\",\n  \"containers.EditView.add.new-entry\": \"新增項目\",\n  \"containers.EditView.notification.errors\": \"表單有些錯誤\",\n  \"containers.Home.introduction\": \"這個擴充功能還在開發階段，如果要編輯項目，請前往左邊選單中對應的連結\",\n  \"containers.Home.pluginHeaderDescription\": \"透過強大的介面來管理您的項目\",\n  \"containers.Home.pluginHeaderTitle\": \"內容管理員\",\n  \"containers.List.draft\": \"草稿\",\n  \"containers.List.errorFetchRecords\": \"錯誤\",\n  \"containers.List.published\": \"已發布\",\n  \"containers.list.displayedFields\": \"顯示欄位\",\n  \"containers.list.items\": \"{number, plural, =0 {項} one {項} other {項}}\",\n  \"containers.list.table-headers.publishedAt\": \"狀態\",\n  \"containers.ListSettingsView.modal-form.edit-label\": \"編輯 {fieldName}\",\n  \"containers.SettingPage.add.field\": \"插入其他欄位\",\n  \"containers.SettingPage.attributes\": \"屬性\",\n  \"containers.SettingPage.attributes.description\": \"調整欄位的順序\",\n  \"containers.SettingPage.editSettings.description\": \"拖曳欄位以規劃排版\",\n  \"containers.SettingPage.editSettings.entry.title\": \"項目標題\",\n  \"containers.SettingPage.editSettings.entry.title.description\": \"設定項目的顯示欄位\",\n  \"containers.SettingPage.editSettings.relation-field.description\": \"設定在編輯或清單顯示時這個項目顯示的欄位\",\n  \"containers.SettingPage.editSettings.title\": \"編輯 (設定)\",\n  \"containers.SettingPage.layout\": \"排版\",\n  \"containers.SettingPage.listSettings.description\": \"調整這個集合類型的選項\",\n  \"containers.SettingPage.listSettings.title\": \"列表 (設定)\",\n  \"containers.SettingPage.pluginHeaderDescription\": \"調整這個集合類型的特殊設定\",\n  \"containers.SettingPage.settings\": \"設定\",\n  \"containers.SettingPage.view\": \"檢視\",\n  \"containers.SettingViewModel.pluginHeader.title\": \"內容管理 - {name}\",\n  \"containers.SettingsPage.Block.contentType.description\": \"調整指定資料類型的選項\",\n  \"containers.SettingsPage.Block.contentType.title\": \"集合型別\",\n  \"containers.SettingsPage.Block.generalSettings.description\": \"為您的集合型別設定預設選項\",\n  \"containers.SettingsPage.Block.generalSettings.title\": \"總覽\",\n  \"containers.SettingsPage.pluginHeaderDescription\": \"調整所有集合型別及群組的設定\",\n  \"containers.SettingsView.list.subtitle\": \"設定您的設定您的集合型別及群組的排版\",\n  \"containers.SettingsView.list.title\": \"顯示設定\",\n  \"edit-settings-view.link-to-ctb.components\": \"編輯組件\",\n  \"edit-settings-view.link-to-ctb.content-types\": \"編輯內容型別\",\n  \"emptyAttributes.button\": \"前往集合型別建立工具\",\n  \"emptyAttributes.description\": \"新增您的第一個集合型別\",\n  \"emptyAttributes.title\": \"目前還沒有欄位\",\n  \"error.attribute.key.taken\": \"這個值已存在\",\n  \"error.attribute.sameKeyAndName\": \"不能等於\",\n  \"error.attribute.taken\": \"這個欄位名稱已存在\",\n  \"error.contentTypeName.taken\": \"這個名稱已存在\",\n  \"error.model.fetch\": \"讀取資料結構設定時發生錯誤\",\n  \"error.record.create\": \"增加資料時發生錯誤\",\n  \"error.record.delete\": \"刪除資料時發生錯誤\",\n  \"error.record.fetch\": \"讀取資料時發生錯誤\",\n  \"error.record.update\": \"更新資料時發生錯誤\",\n  \"error.records.count\": \"讀取資料數量時發生錯誤\",\n  \"error.records.fetch\": \"讀取資料時發生錯誤\",\n  \"error.schema.generation\": \"產生資料結構時發生錯誤\",\n  \"error.validation.json\": \"不符合 JSON 的格式\",\n  \"error.validation.max\": \"數值太大\",\n  \"error.validation.maxLength\": \"長度過長\",\n  \"error.validation.min\": \"數值太小\",\n  \"error.validation.minLength\": \"長度不夠\",\n  \"error.validation.minSupMax\": \"不能接受這個值\",\n  \"error.validation.regex\": \"此值無法通過正規表達式\",\n  \"error.validation.required\": \"必填欄位\",\n  \"form.Input.bulkActions\": \"啟用大量操作\",\n  \"form.Input.defaultSort\": \"預設排序設定\",\n  \"form.Input.description\": \"說明\",\n  \"form.Input.description.placeholder\": \"描述這個欄位\",\n  \"form.Input.editable\": \"可編輯欄位\",\n  \"form.Input.filters\": \"啟用篩選器\",\n  \"form.Input.label\": \"標籤\",\n  \"form.Input.label.inputDescription\": \"這個數值會顯示在表格的標題列名稱\",\n  \"form.Input.pageEntries\": \"每頁項目\",\n  \"form.Input.pageEntries.inputDescription\": \"注意：您可以在“集合型別”設定畫面中覆蓋此值。\",\n  \"form.Input.placeholder\": \"佔位字串\",\n  \"form.Input.placeholder.placeholder\": \"在文字框中顯示提示訊息\",\n  \"form.Input.search\": \"啟用搜尋功能\",\n  \"form.Input.search.field\": \"允許此欄位被搜尋\",\n  \"form.Input.sort.field\": \"允許以此欄位排序\",\n  \"form.Input.sort.order\": \"預設排序順序\",\n  \"form.Input.wysiwyg\": \"所見即所得顯示\",\n  \"global.displayedFields\": \"已顯示欄位\",\n  groups,\n  \"groups.numbered\": \"群組 ({number})\",\n  \"header.name\": \"內容\",\n  \"link-to-ctb\": \"編輯模型\",\n  models,\n  \"models.numbered\": \"集合型別 ({number})\",\n  \"notification.error.displayedFields\": \"您至少需要顯示一個欄位\",\n  \"notification.error.relationship.fetch\": \"讀取關聯資料時發生錯誤\",\n  \"notification.info.SettingPage.disableSort\": \"您至少需要允許一個用來排序的欄位\",\n  \"notification.info.minimumFields\": \"您至少需要一個用來顯示的欄位\",\n  \"notification.upload.error\": \"上傳文件的時候發生錯誤\",\n  pageNotFound,\n  \"pages.ListView.header-subtitle\": \"找到 {number, plural, =0 {# 項目} one {# 項目} other {# 項目}}\",\n  \"pages.NoContentType.button\": \"新增您第一個內容型別\",\n  \"pages.NoContentType.text\": \"這裡還沒有任何內容，建議您從建立第一個內容型別開始。\",\n  \"permissions.not-allowed.create\": \"您不被允許新增文件\",\n  \"permissions.not-allowed.update\": \"您不被允許查看文件\",\n  \"plugin.description.long\": \"快速瀏覽、編輯、刪除資料庫的資料\",\n  \"plugin.description.short\": \"快速瀏覽、編輯、刪除資料庫的資料\",\n  \"popover.display-relations.label\": \"顯示關聯\",\n  \"select.currently.selected\": \"目前已選 {count}\",\n  \"success.record.delete\": \"已刪除\",\n  \"success.record.publish\": \"已發布\",\n  \"success.record.save\": \"已儲存\",\n  \"success.record.unpublish\": \"取消發佈\",\n  \"utils.data-loaded\": \"這個{number, plural, =1 {項目} other {項目}}載入成功\",\n  \"apiError.This attribute must be unique\": \"{field} must be unique\",\n  \"popUpWarning.warning.has-draft-relations.title\": \"Confirmation\",\n  \"popUpWarning.warning.publish-question\": \"您依然要發布它嗎?\",\n  \"popUpwarning.warning.has-draft-relations.button-confirm\": \"是, 發布\",\n  \"popUpwarning.warning.has-draft-relations.message\": \"<b>{count, plural, =0 { 個內容關聯} one { 個內容關聯} other { 個內容關聯}}</b> 還沒有發佈。<br></br>它可能會在您的項目中產生關聯失效和錯誤。\",\n  \"relation.loadMore\": \"載入更多\",\n  \"relation.disconnect\": \"刪除\",\n  \"relation.isLoading\": \"關聯載入中\",\n  \"relation.notAvailable\": \"沒有可用關聯\",\n  \"relation.add\": \"加入關聯\",\n  \"relation.publicationState.draft\": \"草稿\",\n  \"relation.publicationState.published\": \"已發布\"\n};\nexport {\n  zh as default,\n  groups,\n  models,\n  pageNotFound\n};\n//# sourceMappingURL=zh-BWZspA60.mjs.map\n"], "mappings": ";;;AAAA,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,eAAe;AACrB,IAAM,KAAK;AAAA,EACT,2BAA2B;AAAA,EAC3B,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,+BAA+B;AAAA,EAC/B,uBAAuB;AAAA,EACvB,uCAAuC;AAAA,EACvC,UAAU;AAAA,EACV,+BAA+B;AAAA,EAC/B,gCAAgC;AAAA,EAChC,+BAA+B;AAAA,EAC/B,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,gDAAgD;AAAA,EAChD,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,wCAAwC;AAAA,EACxC,6CAA6C;AAAA,EAC7C,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,gCAAgC;AAAA,EAChC,yCAAyC;AAAA,EACzC,4DAA4D;AAAA,EAC5D,+DAA+D;AAAA,EAC/D,0DAA0D;AAAA,EAC1D,2DAA2D;AAAA,EAC3D,sCAAsC;AAAA,EACtC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,mCAAmC;AAAA,EACnC,gDAAgD;AAAA,EAChD,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,yEAAyE;AAAA,EACzE,yEAAyE;AAAA,EACzE,qDAAqD;AAAA,EACrD,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,gCAAgC;AAAA,EAChC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,+BAA+B;AAAA,EAC/B,oDAAoD;AAAA,EACpD,oDAAoD;AAAA,EACpD,uCAAuC;AAAA,EACvC,0BAA0B;AAAA,EAC1B,wBAAwB;AAAA,EACxB,4BAA4B;AAAA,EAC5B,6BAA6B;AAAA,EAC7B,4BAA4B;AAAA,EAC5B,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,6BAA6B;AAAA,EAC7B,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,gCAAgC;AAAA,EAChC,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,yBAAyB;AAAA,EACzB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,qDAAqD;AAAA,EACrD,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,gCAAgC;AAAA,EAChC,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,6CAA6C;AAAA,EAC7C,qDAAqD;AAAA,EACrD,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,iDAAiD;AAAA,EACjD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,+DAA+D;AAAA,EAC/D,kEAAkE;AAAA,EAClE,6CAA6C;AAAA,EAC7C,iCAAiC;AAAA,EACjC,mDAAmD;AAAA,EACnD,6CAA6C;AAAA,EAC7C,kDAAkD;AAAA,EAClD,mCAAmC;AAAA,EACnC,+BAA+B;AAAA,EAC/B,kDAAkD;AAAA,EAClD,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,6DAA6D;AAAA,EAC7D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,sCAAsC;AAAA,EACtC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,0BAA0B;AAAA,EAC1B,+BAA+B;AAAA,EAC/B,yBAAyB;AAAA,EACzB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,oBAAoB;AAAA,EACpB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,qBAAqB;AAAA,EACrB,2BAA2B;AAAA,EAC3B,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B;AAAA,EACA,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,eAAe;AAAA,EACf;AAAA,EACA,mBAAmB;AAAA,EACnB,sCAAsC;AAAA,EACtC,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B;AAAA,EACA,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,uBAAuB;AAAA,EACvB,4BAA4B;AAAA,EAC5B,qBAAqB;AAAA,EACrB,0CAA0C;AAAA,EAC1C,kDAAkD;AAAA,EAClD,yCAAyC;AAAA,EACzC,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,qBAAqB;AAAA,EACrB,uBAAuB;AAAA,EACvB,sBAAsB;AAAA,EACtB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,mCAAmC;AAAA,EACnC,uCAAuC;AACzC;", "names": []}