{"version": 3, "sources": ["../../../@strapi/admin/dist/admin/nl-CubaFTJw.mjs"], "sourcesContent": ["const Analytics = \"Analytics\";\nconst Documentation = \"Documentatie\";\nconst Email = \"E-mail\";\nconst Password = \"Wachtwoord\";\nconst Provider = \"Provider\";\nconst ResetPasswordToken = \"Wachtwoord Reset Token\";\nconst Role = \"Rol\";\nconst light = \"Licht\";\nconst dark = \"Donker\";\nconst Username = \"Gebruikersnaam\";\nconst Users = \"Gebruikers\";\nconst anErrorOccurred = \"Oeps! Er ging iets mis. Probeer het a.u.b. opnieuw.\";\nconst clearLabel = \"Wis\";\nconst or = \"OF\";\nconst skipToContent = \"Skip naar inhoud\";\nconst submit = \"Verzend\";\nconst nl = {\n\tAnalytics: Analytics,\n\t\"Auth.components.Oops.text\": \"Uw account is geblokkeerd.\",\n\t\"Auth.components.Oops.text.admin\": \"Als dit een fout is, neem dan contact op met uw beheerder.\",\n\t\"Auth.components.Oops.title\": \"Oeps...\",\n\t\"Auth.form.active.label\": \"Actief\",\n\t\"Auth.form.button.forgot-password\": \"E-mail versturen\",\n\t\"Auth.form.button.go-home\": \"TERUG NAAR HOME\",\n\t\"Auth.form.button.login\": \"Inloggen\",\n\t\"Auth.form.button.login.providers.error\": \"We kunnen je niet verbinden via de geselecteerde provider.\",\n\t\"Auth.form.button.login.strapi\": \"Log in via Strapi\",\n\t\"Auth.form.button.password-recovery\": \"Wachtwoordherstel\",\n\t\"Auth.form.button.register\": \"Beginnen\",\n\t\"Auth.form.confirmPassword.label\": \"Bevestig wachtwoord\",\n\t\"Auth.form.currentPassword.label\": \"Huidige wachtwoord\",\n\t\"Auth.form.email.label\": \"E-mail\",\n\t\"Auth.form.email.placeholder\": \"Bijv. <EMAIL>\",\n\t\"Auth.form.error.blocked\": \"Uw account werd geblokkeerd door de beheerder.\",\n\t\"Auth.form.error.code.provide\": \"Incorrecte code ingevoerd.\",\n\t\"Auth.form.error.confirmed\": \"Het e-mailadres voor uw account is nog niet bevestigd.\",\n\t\"Auth.form.error.email.invalid\": \"Dit e-mailadres is onjuist.\",\n\t\"Auth.form.error.email.provide\": \"Voer a.u.b. je gebruikersnaam of je e-mail in.\",\n\t\"Auth.form.error.email.taken\": \"E-mailadres is al in gebruik.\",\n\t\"Auth.form.error.invalid\": \"Gebruikersnaam of wachtwoord onjuist.\",\n\t\"Auth.form.error.params.provide\": \"Incorrecte parameters ingevoerd.\",\n\t\"Auth.form.error.password.format\": \"Het wachtwoord mag het `$` symbool niet meer dan drie keer bevatten.\",\n\t\"Auth.form.error.password.local\": \"Deze gebruiker heeft nooit een lokaal wachtwoord ingesteld, gebruik de provider welke gebruikt is tijdens het maken van het account om in te loggen.\",\n\t\"Auth.form.error.password.matching\": \"Wachtwoorden komen niet overeen.\",\n\t\"Auth.form.error.password.provide\": \"Voer a.u.b. je wachtwoord in.\",\n\t\"Auth.form.error.ratelimit\": \"Teveel pogingen, gelieve opnieuw te proberen binnen een minuut.\",\n\t\"Auth.form.error.user.not-exist\": \"Dit e-mailadres bestaat niet.\",\n\t\"Auth.form.error.username.taken\": \"Gebruikersnaam is al in gebruik.\",\n\t\"Auth.form.firstname.label\": \"Voornaam\",\n\t\"Auth.form.firstname.placeholder\": \"Bijv. Kai\",\n\t\"Auth.form.forgot-password.email.label\": \"Voer je e-mail in\",\n\t\"Auth.form.forgot-password.email.label.success\": \"E-mail succesvol verstuurd naar\",\n\t\"Auth.form.lastname.label\": \"Achternaam\",\n\t\"Auth.form.lastname.placeholder\": \"Bijv. Doe\",\n\t\"Auth.form.password.hide-password\": \"Wachtwoord verbergen\",\n\t\"Auth.form.password.hint\": \"Wachtwoord moet ten minste 8 karakters, 1 hoofdletter, 1 kleine letter en 1 nummer bevatten\",\n\t\"Auth.form.password.show-password\": \"Wachtwoord tonen\",\n\t\"Auth.form.register.news.label\": \"Houd me op de hoogte van de nieuwe functionaliteiten en aankomende verbeteringen (door dit te doen accepteer je de {terms} en het {policy}).\",\n\t\"Auth.form.register.subtitle\": \"Je inloggegevens worden alleen gebruikt om jezelf te authenticeren op het beheerders dashboard. Alle gegevens worden opgeslagen in je eigen database.\",\n\t\"Auth.form.rememberMe.label\": \"Onthoud mij\",\n\t\"Auth.form.username.label\": \"Gebruikersnaam\",\n\t\"Auth.form.username.placeholder\": \"Bijv. Kai_Doe\",\n\t\"Auth.form.welcome.subtitle\": \"Inloggen op je Strapi account\",\n\t\"Auth.form.welcome.title\": \"Welkom!\",\n\t\"Auth.link.forgot-password\": \"Wachtwoord vergeten?\",\n\t\"Auth.link.ready\": \"Klaar om in te loggen?\",\n\t\"Auth.link.signin\": \"Inloggen\",\n\t\"Auth.link.signin.account\": \"Heb je al een account?\",\n\t\"Auth.login.sso.divider\": \"Of log in met\",\n\t\"Auth.login.sso.loading\": \"Providers laden...\",\n\t\"Auth.login.sso.subtitle\": \"Log in met SSO\",\n\t\"Auth.privacy-policy-agreement.policy\": \"privacybeleid\",\n\t\"Auth.privacy-policy-agreement.terms\": \"voorwaarden\",\n\t\"Auth.reset-password.title\": \"Herstel wachtwoord\",\n\t\"Content Manager\": \"Content Manager\",\n\t\"Content Type Builder\": \"Content-Type Bouwer\",\n\tDocumentation: Documentation,\n\tEmail: Email,\n\t\"Files Upload\": \"Upload Bestanden\",\n\t\"HomePage.head.title\": \"Startpagina\",\n\t\"HomePage.roadmap\": \"Bekijk onze roadmap\",\n\t\"HomePage.welcome.congrats\": \"Gefeliciteerd!\",\n\t\"HomePage.welcome.congrats.content\": \"Je bent aangemeld als de eerste beheerder. Om de krachtige functies van Strapi te ontdekken,\",\n\t\"HomePage.welcome.congrats.content.bold\": \"raden we je aan om je eerste Collectie-Type aan te maken.\",\n\t\"Media Library\": \"Media Bibliotheek\",\n\t\"New entry\": \"Nieuwe invoer\",\n\tPassword: Password,\n\tProvider: Provider,\n\tResetPasswordToken: ResetPasswordToken,\n\tRole: Role,\n\t\"Roles & Permissions\": \"Rollen & Rechten\",\n\t\"Roles.ListPage.notification.delete-all-not-allowed\": \"Sommige rollen konden niet verwijderd worden omdat ze aan gebruikers zijn gekoppeld\",\n\t\"Roles.ListPage.notification.delete-not-allowed\": \"Een rol kan niet verwijderd worden als deze is gekoppeld aan gebruikers\",\n\t\"Roles.RoleRow.select-all\": \"Selecteer {name} voor bulkacties\",\n\t\"Roles.RoleRow.user-count\": \"{number, plural, =0 {# gebruiker} one {# gebruiker} other {# gebruikers}}\",\n\t\"Roles.components.List.empty.withSearch\": \"Er is geen rol die overeenkomt met de zoekopdracht ({search})...\",\n\t\"Settings.PageTitle\": \"Instellingen - {name}\",\n\t\"Settings.apiTokens.addFirstToken\": \"Voeg je eerste API Token toe\",\n\t\"Settings.apiTokens.addNewToken\": \"Voeg API Token toe\",\n\t\"Settings.tokens.copy.editMessage\": \"Voor veiligheidsredenen kun je je API Token maar één keer zien.\",\n\t\"Settings.tokens.copy.editTitle\": \"Deze token is niet meer toegankelijk.\",\n\t\"Settings.tokens.copy.lastWarning\": \"Maak een kopie van deze token, we laten deze niet nog eens zien!\",\n\t\"Settings.apiTokens.create\": \"Token toevoegen\",\n\t\"Settings.apiTokens.description\": \"Lijst van gegenereerde tokens met API-toegang\",\n\t\"Settings.apiTokens.emptyStateLayout\": \"Je hebt nog geen content...\",\n\t\"Settings.apiTokens.ListView.headers.name\": \"Naam\",\n\t\"Settings.apiTokens.ListView.headers.description\": \"Omschrijving\",\n\t\"Settings.apiTokens.ListView.headers.type\": \"Token type\",\n\t\"Settings.apiTokens.ListView.headers.createdAt\": \"Aangemaakt op\",\n\t\"Settings.apiTokens.ListView.headers.lastUsedAt\": \"Laatst gebruikt op\",\n\t\"Settings.tokens.notification.copied\": \"Token gekopieerd naar klembord.\",\n\t\"Settings.apiTokens.title\": \"API Tokens\",\n\t\"Settings.tokens.types.full-access\": \"Volledige toegang\",\n\t\"Settings.tokens.types.read-only\": \"Alleen lezen\",\n\t\"Settings.tokens.duration.7-days\": \"7 dagen\",\n\t\"Settings.tokens.duration.30-days\": \"30 dagen\",\n\t\"Settings.tokens.duration.90-days\": \"90 dagen\",\n\t\"Settings.tokens.duration.unlimited\": \"Unlimited\",\n\t\"Settings.tokens.form.duration\": \"Token duur\",\n\t\"Settings.tokens.form.type\": \"Token type\",\n\t\"Settings.tokens.duration.expiration-date\": \"Vervaldatum\",\n\t\"Settings.apiTokens.createPage.permissions.title\": \"Rechten\",\n\t\"Settings.apiTokens.createPage.permissions.description\": \"Hieronder staan alleen acties die aan een route zijn gebonden.\",\n\t\"Settings.tokens.RegenerateDialog.title\": \"Token opnieuw genereren\",\n\t\"Settings.tokens.popUpWarning.message\": \"Weet u zeker dat u dit token opnieuw wilt genereren?\",\n\t\"Settings.tokens.Button.cancel\": \"Annuleren\",\n\t\"Settings.tokens.Button.regenerate\": \"Regenereren\",\n\t\"Settings.application.description\": \"Beheerders dashboard's globale informatie\",\n\t\"Settings.application.edition-title\": \"huidige abonnement\",\n\t\"Settings.application.get-help\": \"Hulp krijgen\",\n\t\"Settings.application.link-pricing\": \"Bekijk alle abonnementen\",\n\t\"Settings.application.link-upgrade\": \"Upgrade je beheerders dashboard\",\n\t\"Settings.application.node-version\": \"node versie\",\n\t\"Settings.application.strapi-version\": \"strapi versie\",\n\t\"Settings.application.strapiVersion\": \"strapi versie\",\n\t\"Settings.application.title\": \"Overzicht\",\n\t\"Settings.application.customization\": \"Maatwerk\",\n\t\"Settings.application.customization.carousel.title\": \"Logo\",\n\t\"Settings.application.customization.carousel.change-action\": \"Pas logo aan\",\n\t\"Settings.application.customization.carousel.reset-action\": \"Herstel logo\",\n\t\"Settings.application.customization.carousel-slide.label\": \"Logo schuif\",\n\t\"Settings.application.customization.carousel-hint\": \"Pas Strapi dashboard logo aan (maximaal formaat: {dimension}x{dimension}, maximale bestandsgrootte: {size}KB)\",\n\t\"Settings.application.customization.modal.cancel\": \"Annuleren\",\n\t\"Settings.application.customization.modal.upload\": \"Upload logo\",\n\t\"Settings.application.customization.modal.tab.label\": \"Hoe wil je de bestanden uploaden?\",\n\t\"Settings.application.customization.modal.upload.from-computer\": \"Vanaf computer\",\n\t\"Settings.application.customization.modal.upload.file-validation\": \"maximaal formaat: {dimension}x{dimension}, maximale bestandsgrootte: {size}KB\",\n\t\"Settings.application.customization.modal.upload.error-format\": \"Verkeerd formaat ge-upload (alleen jpeg, jpg, png en svg worden geaccepteerd).\",\n\t\"Settings.application.customization.modal.upload.error-size\": \"Te groot bestand ge-upload (maximaal formaat: {dimension}x{dimension}, maximale bestandsgrootte: {size}KB)\",\n\t\"Settings.application.customization.modal.upload.error-network\": \"Netwerk fout\",\n\t\"Settings.application.customization.modal.upload.cta.browse\": \"Blader bestanden\",\n\t\"Settings.application.customization.modal.upload.drag-drop\": \"Hier naar toe slepen\",\n\t\"Settings.application.customization.modal.upload.from-url\": \"Van url\",\n\t\"Settings.application.customization.modal.upload.from-url.input-label\": \"URL\",\n\t\"Settings.application.customization.modal.upload.next\": \"Volgende\",\n\t\"Settings.application.customization.modal.pending\": \"Pas logo aan\",\n\t\"Settings.application.customization.modal.pending.choose-another\": \"Kies een ander logo\",\n\t\"Settings.application.customization.modal.pending.title\": \"Logo klaar om te uploaden\",\n\t\"Settings.application.customization.modal.pending.subtitle\": \"Manage the chosen logo before uploading it\",\n\t\"Settings.application.customization.modal.pending.upload\": \"Upload logo\",\n\t\"Settings.application.customization.modal.pending.card-badge\": \"image\",\n\t\"Settings.error\": \"Fout\",\n\t\"Settings.global\": \"Globale Instellingen\",\n\t\"Settings.permissions\": \"Administratiepaneel\",\n\t\"Settings.permissions.category\": \"Rechten instellen voor {category}\",\n\t\"Settings.permissions.category.plugins\": \"Rechten instellen voor de {category} plugin\",\n\t\"Settings.permissions.conditions.anytime\": \"Altijd\",\n\t\"Settings.permissions.conditions.apply\": \"Pas toe\",\n\t\"Settings.permissions.conditions.can\": \"Kan\",\n\t\"Settings.permissions.conditions.define-conditions\": \"Definieer voorwaarden\",\n\t\"Settings.permissions.conditions.links\": \"Links\",\n\t\"Settings.permissions.conditions.no-actions\": \"Selecteer eerst acties (creëer, lees, update, ...) voordat je voorwaarden definieert.\",\n\t\"Settings.permissions.conditions.none-selected\": \"Altijd\",\n\t\"Settings.permissions.conditions.or\": \"OF\",\n\t\"Settings.permissions.conditions.when\": \"Wanneer\",\n\t\"Settings.permissions.select-all-by-permission\": \"Selecteer alle {label} rechten\",\n\t\"Settings.permissions.select-by-permission\": \"Selecteer {label} recht\",\n\t\"Settings.permissions.users.create\": \"Nodig nieuwe gebruiker uit\",\n\t\"Settings.permissions.users.email\": \"E-mail\",\n\t\"Settings.permissions.users.firstname\": \"Voornaam\",\n\t\"Settings.permissions.users.lastname\": \"Achternaam\",\n\t\"Settings.permissions.users.user-status\": \"User status\",\n\t\"Settings.permissions.users.roles\": \"Roles\",\n\t\"Settings.permissions.users.username\": \"Username\",\n\t\"Settings.permissions.users.active\": \"Active\",\n\t\"Settings.permissions.users.inactive\": \"Inactive\",\n\t\"Settings.permissions.users.form.sso\": \"Verbind met SSO\",\n\t\"Settings.permissions.users.form.sso.description\": \"Wanneer dit aan staat (ON), kunnen gebruikers inloggen met SSO\",\n\t\"Settings.permissions.users.listview.header.subtitle\": \"Alle gebruikers die toegang hebben tot het Strapi beheerders dashboard\",\n\t\"Settings.permissions.users.tabs.label\": \"Tabs Rechten\",\n\t\"Settings.permissions.users.strapi-super-admin\": \"Super Admin\",\n\t\"Settings.permissions.users.strapi-editor\": \"Editor\",\n\t\"Settings.permissions.users.strapi-author\": \"Auteur\",\n\t\"Settings.profile.form.notify.data.loaded\": \"Je profielgegevens zijn geladen\",\n\t\"Settings.profile.form.section.experience.clear.select\": \"Wis de geselecteerde interfacetaal\",\n\t\"Settings.profile.form.section.experience.here\": \"documentatie\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage\": \"Interfacetaal\",\n\t\"Settings.profile.form.section.experience.interfaceLanguage.hint\": \"Hierdoor wordt alleen je eigen interface in de gekozen taal weergegeven.\",\n\t\"Settings.profile.form.section.experience.interfaceLanguageHelp\": \"Selectie zal de interfacetaal alleen voor jou veranderen. Raadpleeg deze {here} om andere talen beschikbaar te maken voor uw team.\",\n\t\"Settings.profile.form.section.experience.mode.label\": \"Interface modus\",\n\t\"Settings.profile.form.section.experience.mode.hint\": \"Toont uw interface in de gekozen modus.\",\n\t\"Settings.profile.form.section.experience.mode.option-label\": \"{name} modus\",\n\tlight: light,\n\tdark: dark,\n\t\"Settings.profile.form.section.experience.title\": \"Ervaring\",\n\t\"Settings.profile.form.section.head.title\": \"Gebruikersprofiel\",\n\t\"Settings.profile.form.section.profile.page.title\": \"Profiel pagina\",\n\t\"Settings.roles.create.description\": \"Definieer de rechten om aan de rol te geven\",\n\t\"Settings.roles.create.title\": \"Creëer een rol\",\n\t\"Settings.roles.created\": \"Rol gecreëerd\",\n\t\"Settings.roles.edit.title\": \"Wijzig een rol\",\n\t\"Settings.roles.form.button.users-with-role\": \"{number, plural, =0 {# gebruikers} one {# gebruiker} other {# gebruikers}} met deze rol\",\n\t\"Settings.roles.form.created\": \"Gecreëerd\",\n\t\"Settings.roles.form.description\": \"Naam en beschrijving van de rol\",\n\t\"Settings.roles.form.permission.property-label\": \"{label} rechten\",\n\t\"Settings.roles.form.permissions.attributesPermissions\": \"Velden rechten\",\n\t\"Settings.roles.form.permissions.create\": \"Creëer\",\n\t\"Settings.roles.form.permissions.delete\": \"Verwijder\",\n\t\"Settings.roles.form.permissions.publish\": \"Publiceer\",\n\t\"Settings.roles.form.permissions.read\": \"Lees\",\n\t\"Settings.roles.form.permissions.update\": \"Update\",\n\t\"Settings.roles.list.button.add\": \"Nieuwe rol\",\n\t\"Settings.roles.list.description\": \"Lijst van rollen\",\n\t\"Settings.roles.title.singular\": \"rol\",\n\t\"Settings.sso.description\": \"Configureer de instellingen voor SSO functie.\",\n\t\"Settings.sso.form.defaultRole.description\": \"Dit zal de nieuwe geverifieerde gebruiker aan de geselecteerde rol koppelen\",\n\t\"Settings.sso.form.defaultRole.description-not-allowed\": \"Je hebt niet de juiste rechten om de beheerdersrollen te bekijken\",\n\t\"Settings.sso.form.defaultRole.label\": \"Standaard rol\",\n\t\"Settings.sso.form.registration.description\": \"Creëer nieuwe gebruiker met SSO login als er nog geen account bestaat\",\n\t\"Settings.sso.form.registration.label\": \"Automatische registratie\",\n\t\"Settings.sso.title\": \"Single Sign-On\",\n\t\"Settings.webhooks.create\": \"Maak een webhook\",\n\t\"Settings.webhooks.create.header\": \"Voeg een nieuwe header toe\",\n\t\"Settings.webhooks.created\": \"Webhook aangemaakt\",\n\t\"Settings.webhooks.event.publish-tooltip\": \"Dit event bestaat alleen voor content waar het Concept/Publiceer systeem is ingeschakeld\",\n\t\"Settings.webhooks.events.create\": \"Creëer\",\n\t\"Settings.webhooks.events.update\": \"Update\",\n\t\"Settings.webhooks.form.events\": \"Events\",\n\t\"Settings.webhooks.form.headers\": \"Headers\",\n\t\"Settings.webhooks.form.url\": \"URL\",\n\t\"Settings.webhooks.headers.remove\": \"Verwijder header rij {number}\",\n\t\"Settings.webhooks.key\": \"Sleutel\",\n\t\"Settings.webhooks.list.button.add\": \"Maak een nieuwe webhook\",\n\t\"Settings.webhooks.list.description\": \"Ontvang POST veranderingen als notificatie.\",\n\t\"Settings.webhooks.list.empty.description\": \"Voeg de eerste toe aan deze lijst.\",\n\t\"Settings.webhooks.list.empty.link\": \"Bekijk onze documentatie\",\n\t\"Settings.webhooks.list.empty.title\": \"Er zijn nog geen webhooks\",\n\t\"Settings.webhooks.list.th.actions\": \"acties\",\n\t\"Settings.webhooks.list.th.status\": \"status\",\n\t\"Settings.webhooks.singular\": \"webhook\",\n\t\"Settings.webhooks.title\": \"Webhooks\",\n\t\"Settings.webhooks.to.delete\": \"{webhooksToDeleteLength, plural, one {# item} other {# items}} geselecteerd\",\n\t\"Settings.webhooks.trigger\": \"Trigger\",\n\t\"Settings.webhooks.trigger.cancel\": \"Trigger annuleren\",\n\t\"Settings.webhooks.trigger.pending\": \"Bezig...\",\n\t\"Settings.webhooks.trigger.save\": \"Sla op om te triggeren\",\n\t\"Settings.webhooks.trigger.success\": \"Success vol!\",\n\t\"Settings.webhooks.trigger.success.label\": \"Trigger succesvol\",\n\t\"Settings.webhooks.trigger.test\": \"Test-trigger\",\n\t\"Settings.webhooks.trigger.title\": \"Opslaan vóór het triggeren\",\n\t\"Settings.webhooks.value\": \"Waarde\",\n\t\"Usecase.back-end\": \"Back-end developer\",\n\t\"Usecase.button.skip\": \"Deze vraag overslaan\",\n\t\"Usecase.content-creator\": \"Content maker\",\n\t\"Usecase.front-end\": \"Front-end developer\",\n\t\"Usecase.full-stack\": \"Full-stack developer\",\n\t\"Usecase.input.work-type\": \"Wat voor werk doe je?\",\n\t\"Usecase.notification.success.project-created\": \"Project is succesvol aangemaakt\",\n\t\"Usecase.other\": \"Anders\",\n\t\"Usecase.title\": \"Vertel eens wat meer over jezelf\",\n\tUsername: Username,\n\tUsers: Users,\n\t\"Users & Permissions\": \"Gebruikers & Rechten\",\n\t\"Users.components.List.empty\": \"Er zijn geen gebruiker...\",\n\t\"Users.components.List.empty.withFilters\": \"Er zijn geen gebruikers met de geselecteerde filters...\",\n\t\"Users.components.List.empty.withSearch\": \"Er zijn geen gebruikers die overeenkomen met de zoekopdracht ({search})...\",\n\t\"admin.pages.MarketPlacePage.head\": \"Marktplaats - Plugins\",\n\t\"admin.pages.MarketPlacePage.offline.title\": \"Je bent offline\",\n\t\"admin.pages.MarketPlacePage.offline.subtitle\": \"U moet verbonden zijn met internet om toegang te krijgen tot Strapi Market.\",\n\t\"admin.pages.MarketPlacePage.plugins\": \"Plugins\",\n\t\"admin.pages.MarketPlacePage.plugin.copy\": \"Kopieer de installatieopdracht\",\n\t\"admin.pages.MarketPlacePage.plugin.copy.success\": \"Installatieopdracht klaar om in uw terminal te worden geplakt\",\n\t\"admin.pages.MarketPlacePage.plugin.info\": \"Learn more\",\n\t\"admin.pages.MarketPlacePage.plugin.info.label\": \"Meer informatie over {pluginName}\",\n\t\"admin.pages.MarketPlacePage.plugin.info.text\": \"Learn more\",\n\t\"admin.pages.MarketPlacePage.plugin.installed\": \"Geïnstalleerd\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.madeByStrapi\": \"Gemaakt door Strapi\",\n\t\"admin.pages.MarketPlacePage.plugin.tooltip.verified\": \"Plug-in geverifieerd door Strapi\",\n\t\"admin.pages.MarketPlacePage.plugin.version\": \"Werk uw Strapi-versie bij: \\\"{strapiAppVersion}\\\" naar: \\\"{versionRange}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.version.null\": \"Kan compatibiliteit met uw Strapi-versie niet verifiëren: \\\"{strapiAppVersion}\\\"\",\n\t\"admin.pages.MarketPlacePage.plugin.githubStars\": \"Deze plug-in heeft {starsCount} sterren op GitHub\",\n\t\"admin.pages.MarketPlacePage.plugin.downloads\": \"Deze plug-in heeft {downloadsCount} wekelijkse downloads\",\n\t\"admin.pages.MarketPlacePage.providers\": \"Aanbieders\",\n\t\"admin.pages.MarketPlacePage.provider.githubStars\": \"Deze provider heeft {starsCount} sterren op GitHub\",\n\t\"admin.pages.MarketPlacePage.provider.downloads\": \"Deze provider heeft {downloadsCount} wekelijkse downloads\",\n\t\"admin.pages.MarketPlacePage.search.clear\": \"Wis de zoekopdracht\",\n\t\"admin.pages.MarketPlacePage.search.empty\": \"Geen resultaat voor \\\"{target}\\\"\",\n\t\"admin.pages.MarketPlacePage.search.placeholder\": \"Zoek\",\n\t\"admin.pages.MarketPlacePage.submit.plugin.link\": \"Stuur je plugin in\",\n\t\"admin.pages.MarketPlacePage.submit.provider.link\": \"Submit provider\",\n\t\"admin.pages.MarketPlacePage.subtitle\": \"Haal meer uit Strapi\",\n\t\"admin.pages.MarketPlacePage.tab-group.label\": \"Plug-ins en providers voor Strapi\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.title\": \"Mis je een plug-in?\",\n\t\"admin.pages.MarketPlacePage.missingPlugin.description\": \"Vertel ons naar welke plug-in je op zoek bent en we laten het onze ontwikkelaars van plug-ins weten als ze op zoek zijn naar inspiratie!\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical\": \"Alfabetische volgorde\",\n\t\"admin.pages.MarketPlacePage.sort.newest\": \"Nieuwste\",\n\t\"admin.pages.MarketPlacePage.sort.alphabetical.selected\": \"Sorteer op alfabetische volgorde\",\n\t\"admin.pages.MarketPlacePage.sort.newest.selected\": \"Sorteer op nieuwste\",\n\t\"admin.pages.MarketPlacePage.filters.collections\": \"Collecties\",\n\t\"admin.pages.MarketPlacePage.filters.collectionsSelected\": \"{count, plural, =0 {Geen collecties} one {# collectie} other {# collecties}} geselecteerd\",\n\t\"admin.pages.MarketPlacePage.filters.categories\": \"Categories\",\n\t\"admin.pages.MarketPlacePage.filters.categoriesSelected\": \"{count, plural, =0 {Geen categorieën} one {# categorie} other {# categorieën}} geselecteerd\",\n\tanErrorOccurred: anErrorOccurred,\n\t\"app.component.CopyToClipboard.label\": \"Kopieer naar klembord\",\n\t\"app.component.search.label\": \"Zoek naar {target}\",\n\t\"app.component.table.duplicate\": \"Dupliceer {target}\",\n\t\"app.component.table.edit\": \"Pas {target} aan\",\n\t\"app.component.table.select.one-entry\": \"Selecteer {target}\",\n\t\"app.components.BlockLink.blog\": \"Blog\",\n\t\"app.components.BlockLink.blog.content\": \"Lees het laatste nieuws over Strapi en het ecosysteem.\",\n\t\"app.components.BlockLink.code\": \"Code voorbeelden\",\n\t\"app.components.BlockLink.code.content\": \"Leer door het testen van echte projecten die zijn ontwikkeld door de community.\",\n\t\"app.components.BlockLink.documentation.content\": \"Ontdek de essentiële concepten, handleidingen en instructies.\",\n\t\"app.components.BlockLink.tutorial\": \"Tutorials\",\n\t\"app.components.BlockLink.tutorial.content\": \"Volg stapsgewijze instructies om Strapi te gebruiken en aan te passen.\",\n\t\"app.components.Button.cancel\": \"Annuleer\",\n\t\"app.components.Button.confirm\": \"Bevestig\",\n\t\"app.components.Button.reset\": \"Resetten\",\n\t\"app.components.ComingSoonPage.comingSoon\": \"Binnenkort beschikbaar\",\n\t\"app.components.ConfirmDialog.title\": \"Bevestiging\",\n\t\"app.components.DownloadInfo.download\": \"Download bezig...\",\n\t\"app.components.DownloadInfo.text\": \"Dit kan even duren. Bedankt voor je geduld.\",\n\t\"app.components.EmptyAttributes.title\": \"Er zijn nog geen velden\",\n\t\"app.components.EmptyStateLayout.content-document\": \"Geen content gevonden\",\n\t\"app.components.EmptyStateLayout.content-permissions\": \"Je hebt niet de juiste rechten om die content te bekijken\",\n\t\"app.components.GuidedTour.CM.create.content\": \"<p>Maak en beheer alle inhoud hier in de content manager.</p><p>Bijvoorbeeld: als we verder gaan met het blog-websitevoorbeeld, kan men een artikel schrijven, opslaan en publiceren zoals ze willen.</p>< p>💡 Snelle tip - Vergeet niet op publiceren te klikken bij de inhoud die u maakt.</p>\",\n\t\"app.components.GuidedTour.CM.create.title\": \"⚡️ Maak content\",\n\t\"app.components.GuidedTour.CM.success.content\": \"<p>Geweldig, nog een laatste stap te gaan!</p><b>🚀  Zie inhoud in actie</b>\",\n\t\"app.components.GuidedTour.CM.success.cta.title\": \"Test de API\",\n\t\"app.components.GuidedTour.CM.success.title\": \"Stap 2: Voltooid ✅\",\n\t\"app.components.GuidedTour.CTB.create.content\": \"<p>Verzamelingstypen helpen u bij het beheren van meerdere inzendingen. Enkele typen zijn geschikt om slechts één inzending te beheren.</p> <p>Bijvoorbeeld: voor een blogwebsite zijn Artikelen een verzamelingstype, terwijl een startpagina een enkelvoudig type is. </p>\",\n\t\"app.components.GuidedTour.CTB.create.cta.title\": \"Een verzamelingstype bouwen\",\n\t\"app.components.GuidedTour.CTB.create.title\": \"🧠 Een eerste collectietype maken\",\n\t\"app.components.GuidedTour.CTB.success.content\": \"<p>Gaat goed!</p><b>⚡️ Wat zou je met de wereld willen delen?</b>\",\n\t\"app.components.GuidedTour.CTB.success.title\": \"Stap 1: Voltooid ✅\",\n\t\"app.components.GuidedTour.apiTokens.create.content\": \"<p>Genereer hier een authenticatietoken en haal de inhoud op die u zojuist hebt gemaakt.</p>\",\n\t\"app.components.GuidedTour.apiTokens.create.cta.title\": \"Genereer een API-token\",\n\t\"app.components.GuidedTour.apiTokens.create.title\": \"🚀 Zie inhoud in actie\",\n\t\"app.components.GuidedTour.apiTokens.success.content\": \"<p>Zie inhoud in actie door een HTTP-verzoek te doen:</p><ul><li><p>Naar deze URL: <light>https://'<'YOUR_DOMAIN'>'/api/'<'YOUR_CT'>'</light></p></li><li><p>Met de kop: <light>Authorization: bearer '<'YOUR_API_TOKEN'>'</light></p></li></ul><p>Voor meer manieren om met inhoud om te gaan, zie de <documentationLink>documentatie</documentationLink>.</p>\",\n\t\"app.components.GuidedTour.apiTokens.success.cta.title\": \"Ga terug naar de startpagina\",\n\t\"app.components.GuidedTour.apiTokens.success.title\": \"Stap 3: Voltooid ✅\",\n\t\"app.components.GuidedTour.create-content\": \"Inhoud maken\",\n\t\"app.components.GuidedTour.home.CM.title\": \"⚡️Wat zou je met de wereld willen delen?\",\n\t\"app.components.GuidedTour.home.CTB.cta.title\": \"Ga naar de Content-Type Bouwer\",\n\t\"app.components.GuidedTour.home.CTB.title\": \"🧠 Bouw de inhoudsstructuur\",\n\t\"app.components.GuidedTour.home.apiTokens.cta.title\": \"Test de API\",\n\t\"app.components.GuidedTour.skip\": \"Sla de rondleiding over\",\n\t\"app.components.GuidedTour.title\": \"3 stappen om te beginnen\",\n\t\"app.components.HomePage.button.blog\": \"Lees meer op de blog\",\n\t\"app.components.HomePage.community\": \"Word lid van de community\",\n\t\"app.components.HomePage.community.content\": \"Bespreek met teamleden, bijdragers en ontwikkelaars op verschillende kanalen.\",\n\t\"app.components.HomePage.create\": \"Creëer je eerste Content-Type\",\n\t\"app.components.HomePage.roadmap\": \"Bekijk onze roadmap\",\n\t\"app.components.HomePage.welcome\": \"Welkom aan boord 👋\",\n\t\"app.components.HomePage.welcome.again\": \"Welkom 👋\",\n\t\"app.components.HomePage.welcomeBlock.content\": \"Gefeliciteerd! Je bent ingelogd als de eerste beheerder. Om de krachtige functies van Strapi te ontdekken, raden we je aan om je eerste Collectie-Type te maken.\",\n\t\"app.components.HomePage.welcomeBlock.content.again\": \"We hopen dat je vooruitgang boekt met je project! Lees gerust het laatste nieuws over Strapi. We doen ons best om het product te verbeteren op basis van jouw feedback.\",\n\t\"app.components.HomePage.welcomeBlock.content.issues\": \"problemen.\",\n\t\"app.components.HomePage.welcomeBlock.content.raise\": \" of upgrade \",\n\t\"app.components.ImgPreview.hint\": \"Sleep je bestand naar dit vak of {browse} naar een bestand om te uploaden\",\n\t\"app.components.ImgPreview.hint.browse\": \"zoek\",\n\t\"app.components.InputFile.newFile\": \"Nieuw bestand\",\n\t\"app.components.InputFileDetails.open\": \"Open in nieuw tabblad\",\n\t\"app.components.InputFileDetails.originalName\": \"Originele naam:\",\n\t\"app.components.InputFileDetails.remove\": \"Verwijder dit bestand\",\n\t\"app.components.InputFileDetails.size\": \"Grootte:\",\n\t\"app.components.InstallPluginPage.Download.description\": \"Het kan enkele seconden duren om de plugin te downloaden en te installeren.\",\n\t\"app.components.InstallPluginPage.Download.title\": \"Download bezig...\",\n\t\"app.components.InstallPluginPage.description\": \"Breid je app zonder moeite uit.\",\n\t\"app.components.LeftMenu.collapse\": \"Vouw de navigatiebalk samen\",\n\t\"app.components.LeftMenu.expand\": \"Vouw de navigatiebalk uit\",\n\t\"app.components.LeftMenu.general\": \"Algemeen\",\n\t\"app.components.LeftMenu.logout\": \"Uitloggen\",\n\t\"app.components.LeftMenu.logo.alt\": \"Application logo\",\n\t\"app.components.LeftMenu.plugins\": \"Plugins\",\n\t\"app.components.LeftMenu.navbrand.title\": \"Strapi Dashboard\",\n\t\"app.components.LeftMenu.navbrand.workplace\": \"Werkplek\",\n\t\"app.components.LeftMenuFooter.help\": \"Help\",\n\t\"app.components.LeftMenuFooter.poweredBy\": \"Powered by \",\n\t\"app.components.LeftMenuLinkContainer.collectionTypes\": \"Collectie Types\",\n\t\"app.components.LeftMenuLinkContainer.configuration\": \"Configuraties\",\n\t\"app.components.LeftMenuLinkContainer.general\": \"Algemeen\",\n\t\"app.components.LeftMenuLinkContainer.noPluginsInstalled\": \"Nog geen plugins geïnstalleerd\",\n\t\"app.components.LeftMenuLinkContainer.plugins\": \"Plugins\",\n\t\"app.components.LeftMenuLinkContainer.singleTypes\": \"Enkele Types\",\n\t\"app.components.ListPluginsPage.deletePlugin.description\": \"Het kan enkele seconden duren om de plugin te deinstalleren.\",\n\t\"app.components.ListPluginsPage.deletePlugin.title\": \"Deinstalleren\",\n\t\"app.components.ListPluginsPage.description\": \"Lijst van alle plugins voor dit project\",\n\t\"app.components.ListPluginsPage.head.title\": \"Lijst plugins\",\n\t\"app.components.Logout.logout\": \"Logout\",\n\t\"app.components.Logout.profile\": \"Profile\",\n\t\"app.components.MarketplaceBanner\": \"Ontdek plugins die zijn gebouwd door de community en nog veel meer geweldige dingen om je project een kickstart te geven op Strapi Awesome.\",\n\t\"app.components.MarketplaceBanner.image.alt\": \"een strapi rocket logo\",\n\t\"app.components.MarketplaceBanner.link\": \"Bekijk het nu\",\n\t\"app.components.NotFoundPage.back\": \"Terug naar thuispagina\",\n\t\"app.components.NotFoundPage.description\": \"Niet Gevonden\",\n\t\"app.components.Official\": \"Officieel\",\n\t\"app.components.Onboarding.help.button\": \"Help knop\",\n\t\"app.components.Onboarding.label.completed\": \"% voltooid\",\n\t\"app.components.Onboarding.title\": \"Aan de slag-video's\",\n\t\"app.components.PluginCard.Button.label.download\": \"Download\",\n\t\"app.components.PluginCard.Button.label.install\": \"Al geïnstalleerd\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.autoReload.needed\": \"De autoReload-functie is vereist. Herstart de server met `strapi develop`\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.confirm\": \"Ik begrijp het!\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.environment\": \"Om veiligheidsredenen kan een plugin alleen worden gedownload in een ontwikkelomgeving.\",\n\t\"app.components.PluginCard.PopUpWarning.install.impossible.title\": \"Downloaden is niet mogelijk\",\n\t\"app.components.PluginCard.compatible\": \"Geschikt voor jouw app\",\n\t\"app.components.PluginCard.compatibleCommunity\": \"Geschikt voor de community\",\n\t\"app.components.PluginCard.more-details\": \"Meer details\",\n\t\"app.components.ToggleCheckbox.off-label\": \"Uit\",\n\t\"app.components.ToggleCheckbox.on-label\": \"Aan\",\n\t\"app.components.Users.MagicLink.connect\": \"Kopieer en deel deze link om toegang te geven aan deze gebruiker\",\n\t\"app.components.Users.MagicLink.connect.sso\": \"Stuur deze link naar de gebruiker, de eerste login kan gedaan worden via een SSO provider\",\n\t\"app.components.Users.ModalCreateBody.block-title.details\": \"Gebruiker details\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles\": \"Gebruiker's rollen\",\n\t\"app.components.Users.ModalCreateBody.block-title.roles.description\": \"Een gebruiker kan één of meerdere rollen hebben\",\n\t\"app.components.Users.SortPicker.button-label\": \"Sorteer op\",\n\t\"app.components.Users.SortPicker.sortby.email_asc\": \"E-mail (A tot Z)\",\n\t\"app.components.Users.SortPicker.sortby.email_desc\": \"E-mail (Z tot A)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_asc\": \"Voornaam (A tot Z)\",\n\t\"app.components.Users.SortPicker.sortby.firstname_desc\": \"Voornaam (Z tot A)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_asc\": \"Achternaam (A tot Z)\",\n\t\"app.components.Users.SortPicker.sortby.lastname_desc\": \"Achternaam (Z tot A)\",\n\t\"app.components.Users.SortPicker.sortby.username_asc\": \"Gebruikersnaam (A tot Z)\",\n\t\"app.components.Users.SortPicker.sortby.username_desc\": \"Gebruikersnaam (Z tot A)\",\n\t\"app.components.listPlugins.button\": \"Nieuwe plugin toevoegen\",\n\t\"app.components.listPlugins.title.none\": \"Geen plugins geïnstalleerd\",\n\t\"app.components.listPluginsPage.deletePlugin.error\": \"Er is een fout opgetreden tijdens het verwijderen van de plugin\",\n\t\"app.containers.App.notification.error.init\": \"Er is een fout opgetreden bij het aanvragen van de API\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.contact-admin\": \"Als je deze link niet ontvangt, neem dan contact op met je beheerder.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.text.email\": \"Het kan een paar minuten duren voordat je de e-mail met een link om je wachtwoord te herstellen ontvangt.\",\n\t\"app.containers.AuthPage.ForgotPasswordSuccess.title\": \"E-mail verzonden\",\n\t\"app.containers.Users.EditPage.form.active.label\": \"Actief\",\n\t\"app.containers.Users.EditPage.header.label\": \"Pas {name} aan\",\n\t\"app.containers.Users.EditPage.header.label-loading\": \"Pas gebruiker aan\",\n\t\"app.containers.Users.EditPage.roles-bloc-title\": \"Toegekende rollen\",\n\t\"app.containers.Users.ModalForm.footer.button-success\": \"Nodig gebruiker uit\",\n\t\"app.links.configure-view\": \"Configureer de weergave\",\n\t\"app.page.not.found\": \"Oeps! We kunnen de pagina die u zoekt niet vinden...\",\n\t\"app.static.links.cheatsheet\": \"Spiekbriefje\",\n\t\"app.utils.SelectOption.defaultMessage\": \" \",\n\t\"app.utils.add-filter\": \"Voeg filter toe\",\n\t\"app.utils.close-label\": \"Sluit\",\n\t\"app.utils.defaultMessage\": \" \",\n\t\"app.utils.duplicate\": \"Dupliceer\",\n\t\"app.utils.edit\": \"Pas aan\",\n\t\"app.utils.delete\": \"Verwijderen\",\n\t\"app.utils.errors.file-too-big.message\": \"Het bestand is te groot\",\n\t\"app.utils.filter-value\": \"Filter waarde\",\n\t\"app.utils.filters\": \"Filters\",\n\t\"app.utils.notify.data-loaded\": \"Het {target} is geladen\",\n\t\"app.utils.placeholder.defaultMessage\": \" \",\n\t\"app.utils.publish\": \"Publiceer\",\n\t\"app.utils.select-all\": \"Selecteer alles\",\n\t\"app.utils.select-field\": \"Selecteer veld\",\n\t\"app.utils.select-filter\": \"Selecteer filter\",\n\t\"app.utils.unpublish\": \"Depubliceer\",\n\tclearLabel: clearLabel,\n\t\"coming.soon\": \"Deze content is momenteel onder constructie en komt over een paar weken terug!\",\n\t\"component.Input.error.validation.integer\": \"De waarde moet een geheel getal zijn\",\n\t\"components.AutoReloadBlocker.description\": \"Start Strapi met een van de volgende commands:\",\n\t\"components.AutoReloadBlocker.header\": \"De herlaad feature is nodig voor deze extensie\",\n\t\"components.ErrorBoundary.title\": \"Er is iets fout gegaan...\",\n\t\"components.FilterOptions.FILTER_TYPES.$contains\": \"bevat\",\n\t\"components.FilterOptions.FILTER_TYPES.$containsi\": \"bevat (niet hoofdlettergevoelig)\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWith\": \"eindigt op\",\n\t\"components.FilterOptions.FILTER_TYPES.$endsWithi\": \"eindigt op (niet hoofdlettergevoelig)\",\n\t\"components.FilterOptions.FILTER_TYPES.$eq\": \"is\",\n\t\"components.FilterOptions.FILTER_TYPES.$eqi\": \"is (niet hoofdlettergevoelig)\",\n\t\"components.FilterOptions.FILTER_TYPES.$gt\": \"is groter dan\",\n\t\"components.FilterOptions.FILTER_TYPES.$gte\": \"is groter dan of gelijk aan\",\n\t\"components.FilterOptions.FILTER_TYPES.$lt\": \"is kleiner dan\",\n\t\"components.FilterOptions.FILTER_TYPES.$lte\": \"is kleiner dan of gelijk aan\",\n\t\"components.FilterOptions.FILTER_TYPES.$ne\": \"is niet\",\n\t\"components.FilterOptions.FILTER_TYPES.$nei\": \"is niet (niet hoofdlettergevoelig)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContains\": \"bevat niet\",\n\t\"components.FilterOptions.FILTER_TYPES.$notContainsi\": \"bevat niet (niet hoofdlettergevoelig)\",\n\t\"components.FilterOptions.FILTER_TYPES.$notNull\": \"is niet null\",\n\t\"components.FilterOptions.FILTER_TYPES.$null\": \"is null\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWith\": \"start met\",\n\t\"components.FilterOptions.FILTER_TYPES.$startsWithi\": \"start met (niet hoofdlettergevoelig)\",\n\t\"components.Input.error.attribute.key.taken\": \"Deze waarde bestaat al\",\n\t\"components.Input.error.attribute.sameKeyAndName\": \"Mag niet gelijk zijn\",\n\t\"components.Input.error.attribute.taken\": \"Deze veld naam bestaat al\",\n\t\"components.Input.error.contain.lowercase\": \"Wachtwoord moet op zijn minst één kleine letter bevatten\",\n\t\"components.Input.error.contain.number\": \"Wachtwoord moet op zijn minst één nummer bevatten\",\n\t\"components.Input.error.contain.uppercase\": \"Wachtwoord moet op zijn minst één hoofdletter bevatten\",\n\t\"components.Input.error.contentTypeName.taken\": \"Deze naam bestaat al\",\n\t\"components.Input.error.custom-error\": \"{errorMessage} \",\n\t\"components.Input.error.password.noMatch\": \"Wachtwoorden komen niet overeen\",\n\t\"components.Input.error.validation.email\": \"Dit is geen e-mailadres\",\n\t\"components.Input.error.validation.json\": \"Dit komt niet overeen met het JSON-formaat\",\n\t\"components.Input.error.validation.lowercase\": \"De waarde moet in kleine letters zijn\",\n\t\"components.Input.error.validation.max\": \"De waarde is te hoog {max}.\",\n\t\"components.Input.error.validation.maxLength\": \"De waarde is te lang {max}.\",\n\t\"components.Input.error.validation.min\": \"De waarde is te laag {min}.\",\n\t\"components.Input.error.validation.minLength\": \"De waarde is te kort {min}.\",\n\t\"components.Input.error.validation.minSupMax\": \"Mag niet superieur zijn.\",\n\t\"components.Input.error.validation.regex\": \"De ingevoerde waarde komt niet overeen met de regex.\",\n\t\"components.Input.error.validation.required\": \"Deze waarde is verplicht.\",\n\t\"components.Input.error.validation.unique\": \"Deze waarde is al gebruik.\",\n\t\"components.InputSelect.option.placeholder\": \"Kies hier\",\n\t\"components.ListRow.empty\": \"Er is geen data beschikbaar.\",\n\t\"components.NotAllowedInput.text\": \"Geen rechten om dit veld te bekijken\",\n\t\"components.OverlayBlocker.description\": \"Je gebruikt een feature waardoor de server opnieuw op moet starten. Een moment geduld terwijl de server opnieuw opstart.\",\n\t\"components.OverlayBlocker.description.serverError\": \"De server zou opnieuw opgestart moeten zijn, controleer de logs in de terminal.\",\n\t\"components.OverlayBlocker.title\": \"Wachten op herstart...\",\n\t\"components.OverlayBlocker.title.serverError\": \"Het herstarten duurt langer dan verwacht\",\n\t\"components.PageFooter.select\": \"items per pagina\",\n\t\"components.ProductionBlocker.description\": \"Om veiligheidsredenen schakelen we deze plugin uit in andere omgevingen.\",\n\t\"components.ProductionBlocker.header\": \"Deze extensie is alleen beschikbaar in ontwikkelaarsmodus\",\n\t\"components.Search.placeholder\": \"Zoeken...\",\n\t\"components.TableHeader.sort\": \"Sorteer op {label}\",\n\t\"components.Wysiwyg.ToggleMode.markdown-mode\": \"Markdown modus\",\n\t\"components.Wysiwyg.ToggleMode.preview-mode\": \"Voorbeeld modus\",\n\t\"components.Wysiwyg.collapse\": \"Samenvouwen\",\n\t\"components.Wysiwyg.selectOptions.H1\": \"H1 titel\",\n\t\"components.Wysiwyg.selectOptions.H2\": \"H2 titel\",\n\t\"components.Wysiwyg.selectOptions.H3\": \"H3 titel\",\n\t\"components.Wysiwyg.selectOptions.H4\": \"H4 titel\",\n\t\"components.Wysiwyg.selectOptions.H5\": \"H5 titel\",\n\t\"components.Wysiwyg.selectOptions.H6\": \"H6 titel\",\n\t\"components.Wysiwyg.selectOptions.title\": \"Voeg een titel toe\",\n\t\"components.WysiwygBottomControls.charactersIndicators\": \"karakters\",\n\t\"components.WysiwygBottomControls.fullscreen\": \"Uitklappen\",\n\t\"components.WysiwygBottomControls.uploadFiles\": \"Sleep bestanden naar dit vak, plak ze van het klembord of {browse}\",\n\t\"components.WysiwygBottomControls.uploadFiles.browse\": \"selecteer ze\",\n\t\"components.pagination.go-to\": \"Ga naar pagina {page}\",\n\t\"components.pagination.go-to-next\": \"Ga naar volgende pagina\",\n\t\"components.pagination.go-to-previous\": \"Ga naar vorige pagina\",\n\t\"components.pagination.remaining-links\": \"En {number} andere links\",\n\t\"components.popUpWarning.button.cancel\": \"Nee, annuleren\",\n\t\"components.popUpWarning.button.confirm\": \"Ja, bevestigen\",\n\t\"components.popUpWarning.message\": \"Weet je zeker dat je dit wilt verwijderen?\",\n\t\"components.popUpWarning.title\": \"Bevestig a.u.b.\",\n\t\"form.button.continue\": \"Doorgaan\",\n\t\"form.button.done\": \"Klaar\",\n\t\"global.search\": \"Zoeken\",\n\t\"global.actions\": \"Acties\",\n\t\"global.back\": \"Terug\",\n\t\"global.cancel\": \"Annuleren\",\n\t\"global.change-password\": \"Verander wachtwoord\",\n\t\"global.content-manager\": \"Content beheer\",\n\t\"global.continue\": \"Doorgaan\",\n\t\"global.delete\": \"Verwijderen\",\n\t\"global.delete-target\": \"Verwijder {target}\",\n\t\"global.description\": \"Omschrijving\",\n\t\"global.details\": \"Details\",\n\t\"global.disabled\": \"Uitgeschakeld\",\n\t\"global.documentation\": \"Documentatie\",\n\t\"global.enabled\": \"Ingeschakeld\",\n\t\"global.finish\": \"Einde\",\n\t\"global.marketplace\": \"Marktplaats\",\n\t\"global.name\": \"Naam\",\n\t\"global.none\": \"Geen\",\n\t\"global.password\": \"Wachtwoord\",\n\t\"global.plugins\": \"Plug-ins\",\n\t\"global.plugins.content-manager\": \"Content beheer\",\n\t\"global.plugins.content-manager.description\": \"Snelle manier om de gegevens in uw database te bekijken, bewerken en verwijderen.\",\n\t\"global.plugins.content-type-builder\": \"Content-Type Bouwer\",\n\t\"global.plugins.content-type-builder.description\": \"Modelleer de datastructuur van uw API. Creëer nieuwe velden en relaties in slechts een minuut. De bestanden worden automatisch aangemaakt en bijgewerkt in uw project.\",\n\t\"global.plugins.email\": \"E-mail\",\n\t\"global.plugins.email.description\": \"Configureer uw applicatie om e-mails te verzenden.\",\n\t\"global.plugins.upload\": \"Mediatheek\",\n\t\"global.plugins.upload.description\": \"Beheer van mediabestanden.\",\n\t\"global.plugins.graphql\": \"GraphQL\",\n\t\"global.plugins.graphql.description\": \"Voegt GraphQL-eindpunt toe met standaard API-methoden.\",\n\t\"global.plugins.documentation\": \"Documentatie\",\n\t\"global.plugins.documentation.description\": \"Maak een OpenAPI-document en visualiseer uw API met SWAGGER UI.\",\n\t\"global.plugins.i18n\": \"Internationalisering\",\n\t\"global.plugins.i18n.description\": \"Deze plug-in maakt het mogelijk om inhoud in verschillende talen te creëren, te lezen en bij te werken, zowel vanuit het beheerdersdashboard als vanuit de API.\",\n\t\"global.plugins.sentry\": \"Sentry\",\n\t\"global.plugins.sentry.description\": \"Stuur Strapi-foutgebeurtenissen naar Sentry.\",\n\t\"global.plugins.users-permissions\": \"Rollen & Machtigingen\",\n\t\"global.plugins.users-permissions.description\": \"Bescherm uw API met een volledig authenticatieproces op basis van JWT. Deze plug-in wordt ook geleverd met een ACL-strategie waarmee u de machtigingen tussen de groepen gebruikers kunt beheren.\",\n\t\"global.profile\": \"Profiel\",\n\t\"global.prompt.unsaved\": \"Weet je zeker dat je deze pagina wilt verlaten? Al de wijzigingen gaan verloren.\",\n\t\"global.reset-password\": \"Herstel wachtwoord\",\n\t\"global.roles\": \"Rollen\",\n\t\"global.save\": \"Bewaar\",\n\t\"global.see-more\": \"Zie meer\",\n\t\"global.select\": \"Kies\",\n\t\"global.select-all-entries\": \"Selecteer alle vermeldingen\",\n\t\"global.settings\": \"Instellingen\",\n\t\"global.type\": \"Type\",\n\t\"global.users\": \"Gebruikers\",\n\t\"notification.contentType.relations.conflict\": \"Content-Type heeft conflicterende relaties\",\n\t\"notification.default.title\": \"Informatie:\",\n\t\"notification.error\": \"Er is een fout opgetreden\",\n\t\"notification.error.layout\": \"Kon de lay-out niet laden\",\n\t\"notification.form.error.fields\": \"Het formulier bevat enkele fouten\",\n\t\"notification.form.success.fields\": \"Wijzigingen opgeslagen\",\n\t\"notification.link-copied\": \"Link gekopieerd naar het klembord\",\n\t\"notification.permission.not-allowed-read\": \"Je hebt niet de rechten om dit document te zien\",\n\t\"notification.success.delete\": \"Het item is verwijderd\",\n\t\"notification.success.saved\": \"Opgeslagen\",\n\t\"notification.success.title\": \"Succes:\",\n\t\"notification.success.apitokencreated\": \"API-token is gemaakt\",\n\t\"notification.success.apitokenedited\": \"API-token succesvol bewerkt\",\n\t\"notification.error.tokennamenotunique\": \"Naam al toegewezen aan een ander token\",\n\t\"notification.version.update.message\": \"Een nieuwe versie van Strapi is beschikbaar!\",\n\t\"notification.warning.title\": \"Waarschuwing:\",\n\t\"notification.warning.404\": \"404 - Niet gevonden\",\n\tor: or,\n\t\"request.error.model.unknown\": \"Dit model bestaat niet\",\n\tskipToContent: skipToContent,\n\tsubmit: submit\n};\n\nexport { Analytics, Documentation, Email, Password, Provider, ResetPasswordToken, Role, Username, Users, anErrorOccurred, clearLabel, dark, nl as default, light, or, skipToContent, submit };\n//# sourceMappingURL=nl-CubaFTJw.mjs.map\n"], "mappings": ";;;AAAA,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,QAAQ;AACd,IAAM,WAAW;AACjB,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAC3B,IAAM,OAAO;AACb,IAAM,QAAQ;AACd,IAAM,OAAO;AACb,IAAM,WAAW;AACjB,IAAM,QAAQ;AACd,IAAM,kBAAkB;AACxB,IAAM,aAAa;AACnB,IAAM,KAAK;AACX,IAAM,gBAAgB;AACtB,IAAM,SAAS;AACf,IAAM,KAAK;AAAA,EACV;AAAA,EACA,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,8BAA8B;AAAA,EAC9B,0BAA0B;AAAA,EAC1B,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0CAA0C;AAAA,EAC1C,iCAAiC;AAAA,EACjC,sCAAsC;AAAA,EACtC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,gCAAgC;AAAA,EAChC,6BAA6B;AAAA,EAC7B,iCAAiC;AAAA,EACjC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,2BAA2B;AAAA,EAC3B,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,kCAAkC;AAAA,EAClC,6BAA6B;AAAA,EAC7B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,2BAA2B;AAAA,EAC3B,oCAAoC;AAAA,EACpC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,oBAAoB;AAAA,EACpB,4BAA4B;AAAA,EAC5B,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB;AAAA,EACA;AAAA,EACA,gBAAgB;AAAA,EAChB,uBAAuB;AAAA,EACvB,oBAAoB;AAAA,EACpB,6BAA6B;AAAA,EAC7B,qCAAqC;AAAA,EACrC,0CAA0C;AAAA,EAC1C,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,4BAA4B;AAAA,EAC5B,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,sBAAsB;AAAA,EACtB,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,uCAAuC;AAAA,EACvC,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,4CAA4C;AAAA,EAC5C,iDAAiD;AAAA,EACjD,kDAAkD;AAAA,EAClD,uCAAuC;AAAA,EACvC,4BAA4B;AAAA,EAC5B,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,oCAAoC;AAAA,EACpC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,6BAA6B;AAAA,EAC7B,4CAA4C;AAAA,EAC5C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,sCAAsC;AAAA,EACtC,iCAAiC;AAAA,EACjC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,sCAAsC;AAAA,EACtC,8BAA8B;AAAA,EAC9B,sCAAsC;AAAA,EACtC,qDAAqD;AAAA,EACrD,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,2DAA2D;AAAA,EAC3D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,mDAAmD;AAAA,EACnD,sDAAsD;AAAA,EACtD,iEAAiE;AAAA,EACjE,mEAAmE;AAAA,EACnE,gEAAgE;AAAA,EAChE,8DAA8D;AAAA,EAC9D,iEAAiE;AAAA,EACjE,8DAA8D;AAAA,EAC9D,6DAA6D;AAAA,EAC7D,4DAA4D;AAAA,EAC5D,wEAAwE;AAAA,EACxE,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,mEAAmE;AAAA,EACnE,0DAA0D;AAAA,EAC1D,6DAA6D;AAAA,EAC7D,2DAA2D;AAAA,EAC3D,+DAA+D;AAAA,EAC/D,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,2CAA2C;AAAA,EAC3C,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,qDAAqD;AAAA,EACrD,yCAAyC;AAAA,EACzC,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,6CAA6C;AAAA,EAC7C,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,uDAAuD;AAAA,EACvD,yCAAyC;AAAA,EACzC,iDAAiD;AAAA,EACjD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,8DAA8D;AAAA,EAC9D,mEAAmE;AAAA,EACnE,kEAAkE;AAAA,EAClE,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,8DAA8D;AAAA,EAC9D;AAAA,EACA;AAAA,EACA,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,oDAAoD;AAAA,EACpD,qCAAqC;AAAA,EACrC,+BAA+B;AAAA,EAC/B,0BAA0B;AAAA,EAC1B,6BAA6B;AAAA,EAC7B,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,mCAAmC;AAAA,EACnC,iDAAiD;AAAA,EACjD,yDAAyD;AAAA,EACzD,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,wCAAwC;AAAA,EACxC,0CAA0C;AAAA,EAC1C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,6CAA6C;AAAA,EAC7C,yDAAyD;AAAA,EACzD,uCAAuC;AAAA,EACvC,8CAA8C;AAAA,EAC9C,wCAAwC;AAAA,EACxC,sBAAsB;AAAA,EACtB,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,6BAA6B;AAAA,EAC7B,2CAA2C;AAAA,EAC3C,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,kCAAkC;AAAA,EAClC,8BAA8B;AAAA,EAC9B,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,qCAAqC;AAAA,EACrC,sCAAsC;AAAA,EACtC,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,8BAA8B;AAAA,EAC9B,2BAA2B;AAAA,EAC3B,+BAA+B;AAAA,EAC/B,6BAA6B;AAAA,EAC7B,oCAAoC;AAAA,EACpC,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,2CAA2C;AAAA,EAC3C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,2BAA2B;AAAA,EAC3B,oBAAoB;AAAA,EACpB,uBAAuB;AAAA,EACvB,2BAA2B;AAAA,EAC3B,qBAAqB;AAAA,EACrB,sBAAsB;AAAA,EACtB,2BAA2B;AAAA,EAC3B,gDAAgD;AAAA,EAChD,iBAAiB;AAAA,EACjB,iBAAiB;AAAA,EACjB;AAAA,EACA;AAAA,EACA,uBAAuB;AAAA,EACvB,+BAA+B;AAAA,EAC/B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,oCAAoC;AAAA,EACpC,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,mDAAmD;AAAA,EACnD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,gDAAgD;AAAA,EAChD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,uDAAuD;AAAA,EACvD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,gDAAgD;AAAA,EAChD,yCAAyC;AAAA,EACzC,oDAAoD;AAAA,EACpD,kDAAkD;AAAA,EAClD,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,kDAAkD;AAAA,EAClD,oDAAoD;AAAA,EACpD,wCAAwC;AAAA,EACxC,+CAA+C;AAAA,EAC/C,mDAAmD;AAAA,EACnD,yDAAyD;AAAA,EACzD,iDAAiD;AAAA,EACjD,2CAA2C;AAAA,EAC3C,0DAA0D;AAAA,EAC1D,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,2DAA2D;AAAA,EAC3D,kDAAkD;AAAA,EAClD,0DAA0D;AAAA,EAC1D;AAAA,EACA,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,wCAAwC;AAAA,EACxC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,iCAAiC;AAAA,EACjC,yCAAyC;AAAA,EACzC,kDAAkD;AAAA,EAClD,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,4CAA4C;AAAA,EAC5C,sCAAsC;AAAA,EACtC,wCAAwC;AAAA,EACxC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,+CAA+C;AAAA,EAC/C,6CAA6C;AAAA,EAC7C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,kDAAkD;AAAA,EAClD,8CAA8C;AAAA,EAC9C,iDAAiD;AAAA,EACjD,+CAA+C;AAAA,EAC/C,sDAAsD;AAAA,EACtD,wDAAwD;AAAA,EACxD,oDAAoD;AAAA,EACpD,uDAAuD;AAAA,EACvD,yDAAyD;AAAA,EACzD,qDAAqD;AAAA,EACrD,4CAA4C;AAAA,EAC5C,2CAA2C;AAAA,EAC3C,gDAAgD;AAAA,EAChD,4CAA4C;AAAA,EAC5C,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,uCAAuC;AAAA,EACvC,qCAAqC;AAAA,EACrC,6CAA6C;AAAA,EAC7C,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,sDAAsD;AAAA,EACtD,kCAAkC;AAAA,EAClC,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,gDAAgD;AAAA,EAChD,0CAA0C;AAAA,EAC1C,wCAAwC;AAAA,EACxC,yDAAyD;AAAA,EACzD,mDAAmD;AAAA,EACnD,gDAAgD;AAAA,EAChD,oCAAoC;AAAA,EACpC,kCAAkC;AAAA,EAClC,mCAAmC;AAAA,EACnC,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,sCAAsC;AAAA,EACtC,2CAA2C;AAAA,EAC3C,wDAAwD;AAAA,EACxD,sDAAsD;AAAA,EACtD,gDAAgD;AAAA,EAChD,2DAA2D;AAAA,EAC3D,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,2DAA2D;AAAA,EAC3D,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,gCAAgC;AAAA,EAChC,iCAAiC;AAAA,EACjC,oCAAoC;AAAA,EACpC,8CAA8C;AAAA,EAC9C,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,2CAA2C;AAAA,EAC3C,2BAA2B;AAAA,EAC3B,yCAAyC;AAAA,EACzC,6CAA6C;AAAA,EAC7C,mCAAmC;AAAA,EACnC,mDAAmD;AAAA,EACnD,kDAAkD;AAAA,EAClD,+EAA+E;AAAA,EAC/E,qEAAqE;AAAA,EACrE,yEAAyE;AAAA,EACzE,mEAAmE;AAAA,EACnE,wCAAwC;AAAA,EACxC,iDAAiD;AAAA,EACjD,0CAA0C;AAAA,EAC1C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,0CAA0C;AAAA,EAC1C,8CAA8C;AAAA,EAC9C,4DAA4D;AAAA,EAC5D,0DAA0D;AAAA,EAC1D,sEAAsE;AAAA,EACtE,gDAAgD;AAAA,EAChD,oDAAoD;AAAA,EACpD,qDAAqD;AAAA,EACrD,wDAAwD;AAAA,EACxD,yDAAyD;AAAA,EACzD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,uDAAuD;AAAA,EACvD,wDAAwD;AAAA,EACxD,qCAAqC;AAAA,EACrC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,8CAA8C;AAAA,EAC9C,oEAAoE;AAAA,EACpE,4DAA4D;AAAA,EAC5D,uDAAuD;AAAA,EACvD,mDAAmD;AAAA,EACnD,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,kDAAkD;AAAA,EAClD,wDAAwD;AAAA,EACxD,4BAA4B;AAAA,EAC5B,sBAAsB;AAAA,EACtB,+BAA+B;AAAA,EAC/B,yCAAyC;AAAA,EACzC,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,4BAA4B;AAAA,EAC5B,uBAAuB;AAAA,EACvB,kBAAkB;AAAA,EAClB,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,0BAA0B;AAAA,EAC1B,qBAAqB;AAAA,EACrB,gCAAgC;AAAA,EAChC,wCAAwC;AAAA,EACxC,qBAAqB;AAAA,EACrB,wBAAwB;AAAA,EACxB,0BAA0B;AAAA,EAC1B,2BAA2B;AAAA,EAC3B,uBAAuB;AAAA,EACvB;AAAA,EACA,eAAe;AAAA,EACf,4CAA4C;AAAA,EAC5C,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,kCAAkC;AAAA,EAClC,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,mDAAmD;AAAA,EACnD,oDAAoD;AAAA,EACpD,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,6CAA6C;AAAA,EAC7C,8CAA8C;AAAA,EAC9C,sDAAsD;AAAA,EACtD,uDAAuD;AAAA,EACvD,kDAAkD;AAAA,EAClD,+CAA+C;AAAA,EAC/C,qDAAqD;AAAA,EACrD,sDAAsD;AAAA,EACtD,8CAA8C;AAAA,EAC9C,mDAAmD;AAAA,EACnD,0CAA0C;AAAA,EAC1C,4CAA4C;AAAA,EAC5C,yCAAyC;AAAA,EACzC,4CAA4C;AAAA,EAC5C,gDAAgD;AAAA,EAChD,uCAAuC;AAAA,EACvC,2CAA2C;AAAA,EAC3C,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,6CAA6C;AAAA,EAC7C,4BAA4B;AAAA,EAC5B,mCAAmC;AAAA,EACnC,yCAAyC;AAAA,EACzC,qDAAqD;AAAA,EACrD,mCAAmC;AAAA,EACnC,+CAA+C;AAAA,EAC/C,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uCAAuC;AAAA,EACvC,iCAAiC;AAAA,EACjC,+BAA+B;AAAA,EAC/B,+CAA+C;AAAA,EAC/C,8CAA8C;AAAA,EAC9C,+BAA+B;AAAA,EAC/B,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,uCAAuC;AAAA,EACvC,0CAA0C;AAAA,EAC1C,yDAAyD;AAAA,EACzD,+CAA+C;AAAA,EAC/C,gDAAgD;AAAA,EAChD,uDAAuD;AAAA,EACvD,+BAA+B;AAAA,EAC/B,oCAAoC;AAAA,EACpC,wCAAwC;AAAA,EACxC,yCAAyC;AAAA,EACzC,yCAAyC;AAAA,EACzC,0CAA0C;AAAA,EAC1C,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,wBAAwB;AAAA,EACxB,oBAAoB;AAAA,EACpB,iBAAiB;AAAA,EACjB,kBAAkB;AAAA,EAClB,eAAe;AAAA,EACf,iBAAiB;AAAA,EACjB,0BAA0B;AAAA,EAC1B,0BAA0B;AAAA,EAC1B,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,wBAAwB;AAAA,EACxB,sBAAsB;AAAA,EACtB,kBAAkB;AAAA,EAClB,mBAAmB;AAAA,EACnB,wBAAwB;AAAA,EACxB,kBAAkB;AAAA,EAClB,iBAAiB;AAAA,EACjB,sBAAsB;AAAA,EACtB,eAAe;AAAA,EACf,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,kBAAkB;AAAA,EAClB,kCAAkC;AAAA,EAClC,8CAA8C;AAAA,EAC9C,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,wBAAwB;AAAA,EACxB,oCAAoC;AAAA,EACpC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,0BAA0B;AAAA,EAC1B,sCAAsC;AAAA,EACtC,gCAAgC;AAAA,EAChC,4CAA4C;AAAA,EAC5C,uBAAuB;AAAA,EACvB,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,qCAAqC;AAAA,EACrC,oCAAoC;AAAA,EACpC,gDAAgD;AAAA,EAChD,kBAAkB;AAAA,EAClB,yBAAyB;AAAA,EACzB,yBAAyB;AAAA,EACzB,gBAAgB;AAAA,EAChB,eAAe;AAAA,EACf,mBAAmB;AAAA,EACnB,iBAAiB;AAAA,EACjB,6BAA6B;AAAA,EAC7B,mBAAmB;AAAA,EACnB,eAAe;AAAA,EACf,gBAAgB;AAAA,EAChB,+CAA+C;AAAA,EAC/C,8BAA8B;AAAA,EAC9B,sBAAsB;AAAA,EACtB,6BAA6B;AAAA,EAC7B,kCAAkC;AAAA,EAClC,oCAAoC;AAAA,EACpC,4BAA4B;AAAA,EAC5B,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,8BAA8B;AAAA,EAC9B,8BAA8B;AAAA,EAC9B,wCAAwC;AAAA,EACxC,uCAAuC;AAAA,EACvC,yCAAyC;AAAA,EACzC,uCAAuC;AAAA,EACvC,8BAA8B;AAAA,EAC9B,4BAA4B;AAAA,EAC5B;AAAA,EACA,+BAA+B;AAAA,EAC/B;AAAA,EACA;AACD;", "names": []}