import {
  useIntl
} from "./chunk-IKDDXE5L.js";
import "./chunk-MM3DOGUR.js";
import "./chunk-6NLSGXWT.js";
import "./chunk-DC3UNANX.js";
import "./chunk-5ZFC5BBE.js";
import "./chunk-CH6LMMF3.js";
import "./chunk-C7H2BX76.js";
import "./chunk-3UWI42TF.js";
import "./chunk-EGRHWZRV.js";
import "./chunk-S4GMEU6I.js";
import "./chunk-XPB4KQQT.js";
import "./chunk-D63J2BWQ.js";
import "./chunk-IZBVLW72.js";
import "./chunk-M5EKOYPZ.js";
import "./chunk-RPX6VIML.js";
import "./chunk-O6QFUROF.js";
import "./chunk-VOKTPSOW.js";
import "./chunk-N562NCQ4.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import {
  require_isEmpty
} from "./chunk-KKUAHZGP.js";
import "./chunk-VUQGR7S3.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Form2 as Form,
  Layouts,
  MemoizedInputRenderer,
  create2 as create,
  create4 as create2,
  create6 as create3,
  errorsTrads,
  useFetchClient,
  useMutation,
  useQuery,
  useQueryClient
} from "./chunk-ELTZWS66.js";
import {
  Page,
  useAPIErrorHandler,
  useNotification,
  useRBAC
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Button,
  Flex,
  Grid,
  Typography,
  useNotifyAT
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$4p
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  PERMISSIONS,
  getTrad
} from "./chunk-WE3SAIMN.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/index-B-yH0E2A.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_isEmpty = __toESM(require_isEmpty(), 1);
var layout = [
  {
    label: {
      id: getTrad("EditForm.inputToggle.label.email"),
      defaultMessage: "One account per email address"
    },
    hint: {
      id: getTrad("EditForm.inputToggle.description.email"),
      defaultMessage: "Disallow the user to create multiple accounts using the same email address with different authentication providers."
    },
    name: "unique_email",
    type: "boolean",
    size: 12
  },
  {
    label: {
      id: getTrad("EditForm.inputToggle.label.sign-up"),
      defaultMessage: "Enable sign-ups"
    },
    hint: {
      id: getTrad("EditForm.inputToggle.description.sign-up"),
      defaultMessage: "When disabled (OFF), the registration process is forbidden. No one can subscribe anymore no matter the used provider."
    },
    name: "allow_register",
    type: "boolean",
    size: 12
  },
  {
    label: {
      id: getTrad("EditForm.inputToggle.label.email-reset-password"),
      defaultMessage: "Reset password page"
    },
    hint: {
      id: getTrad("EditForm.inputToggle.description.email-reset-password"),
      defaultMessage: "URL of your application's reset password page."
    },
    placeholder: {
      id: getTrad("EditForm.inputToggle.placeholder.email-reset-password"),
      defaultMessage: "ex: https://youtfrontend.com/reset-password"
    },
    name: "email_reset_password",
    type: "string",
    size: 12
  },
  {
    label: {
      id: getTrad("EditForm.inputToggle.label.email-confirmation"),
      defaultMessage: "Enable email confirmation"
    },
    hint: {
      id: getTrad("EditForm.inputToggle.description.email-confirmation"),
      defaultMessage: "When enabled (ON), new registered users receive a confirmation email."
    },
    name: "email_confirmation",
    type: "boolean",
    size: 12
  },
  {
    label: {
      id: getTrad("EditForm.inputToggle.label.email-confirmation-redirection"),
      defaultMessage: "Redirection url"
    },
    hint: {
      id: getTrad("EditForm.inputToggle.description.email-confirmation-redirection"),
      defaultMessage: "After you confirmed your email, choose where you will be redirected."
    },
    placeholder: {
      id: getTrad("EditForm.inputToggle.placeholder.email-confirmation-redirection"),
      defaultMessage: "ex: https://youtfrontend.com/email-confirmation"
    },
    name: "email_confirmation_redirection",
    type: "string",
    size: 12
  }
];
var URL_REGEX = new RegExp("(^$)|((.+:\\/\\/.*)(d*)\\/?(.*))");
var schema = create3().shape({
  email_confirmation_redirection: create().when("email_confirmation", {
    is: true,
    then: create2().matches(URL_REGEX).required(),
    otherwise: create2().nullable()
  }),
  email_reset_password: create2(errorsTrads.string).matches(URL_REGEX, {
    id: errorsTrads.regex.id,
    defaultMessage: "This is not a valid URL"
  }).nullable()
});
var ProtectedAdvancedSettingsPage = () => (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.readAdvancedSettings, children: (0, import_jsx_runtime.jsx)(AdvancedSettingsPage, {}) });
var AdvancedSettingsPage = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { notifyStatus } = useNotifyAT();
  const queryClient = useQueryClient();
  const { get, put } = useFetchClient();
  const { formatAPIError } = useAPIErrorHandler();
  const {
    isLoading: isLoadingForPermissions,
    allowedActions: { canUpdate }
  } = useRBAC({ update: PERMISSIONS.updateAdvancedSettings });
  const { isLoading: isLoadingData, data } = useQuery(
    ["users-permissions", "advanced"],
    async () => {
      const { data: data2 } = await get("/users-permissions/advanced");
      return data2;
    },
    {
      onSuccess() {
        notifyStatus(
          formatMessage({
            id: getTrad("Form.advancedSettings.data.loaded"),
            defaultMessage: "Advanced settings data has been loaded"
          })
        );
      },
      onError() {
        toggleNotification({
          type: "danger",
          message: formatMessage({
            id: getTrad("notification.error"),
            defaultMessage: "An error occured"
          })
        });
      }
    }
  );
  const isLoading = isLoadingForPermissions || isLoadingData;
  const submitMutation = useMutation((body) => put("/users-permissions/advanced", body), {
    async onSuccess() {
      await queryClient.invalidateQueries(["users-permissions", "advanced"]);
      toggleNotification({
        type: "success",
        message: formatMessage({
          id: getTrad("notification.success.saved"),
          defaultMessage: "Saved"
        })
      });
    },
    onError(error) {
      toggleNotification({
        type: "danger",
        message: formatAPIError(error)
      });
    },
    refetchActive: true
  });
  const { isLoading: isSubmittingForm } = submitMutation;
  const handleSubmit = async (body) => {
    submitMutation.mutate({
      ...body,
      email_confirmation_redirection: body.email_confirmation ? body.email_confirmation_redirection : ""
    });
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Page.Main, { "aria-busy": isSubmittingForm, children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage(
      { id: "Settings.PageTitle", defaultMessage: "Settings - {name}" },
      {
        name: formatMessage({
          id: getTrad("HeaderNav.link.advancedSettings"),
          defaultMessage: "Advanced Settings"
        })
      }
    ) }),
    (0, import_jsx_runtime.jsx)(Form, { onSubmit: handleSubmit, initialValues: data.settings, validationSchema: schema, children: ({ values, isSubmitting, modified }) => {
      return (0, import_jsx_runtime.jsxs)(import_jsx_runtime.Fragment, { children: [
        (0, import_jsx_runtime.jsx)(
          Layouts.Header,
          {
            title: formatMessage({
              id: getTrad("HeaderNav.link.advancedSettings"),
              defaultMessage: "Advanced Settings"
            }),
            primaryAction: (0, import_jsx_runtime.jsx)(
              Button,
              {
                loading: isSubmitting,
                type: "submit",
                disabled: !modified || !canUpdate,
                startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
                size: "S",
                children: formatMessage({ id: "global.save", defaultMessage: "Save" })
              }
            )
          }
        ),
        (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsx)(
          Box,
          {
            background: "neutral0",
            hasRadius: true,
            shadow: "filterShadow",
            paddingTop: 6,
            paddingBottom: 6,
            paddingLeft: 7,
            paddingRight: 7,
            children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
              (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
                id: "global.settings",
                defaultMessage: "Settings"
              }) }),
              (0, import_jsx_runtime.jsx)(Grid.Root, { gap: 6, children: [
                {
                  label: {
                    id: getTrad("EditForm.inputSelect.label.role"),
                    defaultMessage: "Default role for authenticated users"
                  },
                  hint: {
                    id: getTrad("EditForm.inputSelect.description.role"),
                    defaultMessage: "It will attach the new authenticated user to the selected role."
                  },
                  options: data.roles.map((role) => ({
                    label: role.name,
                    value: role.type
                  })),
                  name: "default_role",
                  size: 6,
                  type: "enumeration"
                },
                ...layout
              ].map(({ size, ...field }) => (0, import_jsx_runtime.jsx)(
                Grid.Item,
                {
                  col: size,
                  direction: "column",
                  alignItems: "stretch",
                  children: (0, import_jsx_runtime.jsx)(
                    MemoizedInputRenderer,
                    {
                      ...field,
                      disabled: field.name === "email_confirmation_redirection" && values.email_confirmation === false,
                      label: formatMessage(field.label),
                      hint: field.hint ? formatMessage(field.hint) : void 0,
                      placeholder: field.placeholder ? formatMessage(field.placeholder) : void 0
                    }
                  )
                },
                field.name
              )) })
            ] })
          }
        ) })
      ] });
    } })
  ] });
};
export {
  AdvancedSettingsPage,
  ProtectedAdvancedSettingsPage
};
//# sourceMappingURL=index-B-yH0E2A-54NSKR4O.js.map
