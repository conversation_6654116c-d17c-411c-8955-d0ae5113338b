import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/content-manager/dist/_chunks/ca-5U32ON2v.mjs
var groups = "Grups";
var models = "Tipus de Col·lecció";
var pageNotFound = "Pàgina no trobada";
var ca = {
  "App.schemas.data-loaded": "Els esquemes s'han carregat correctament.",
  "ListViewTable.relation-loaded": "Les relacions s'han carregat",
  "ListViewTable.relation-loading": "Les relacions es carreguen",
  "ListViewTable.relation-more": "Aquesta relació conté més entitats de les que es mostren",
  "EditRelations.title": "Dades relacionades",
  "HeaderLayout.button.label-add-entry": "Crear nova entrada",
  "api.id": "ID d'API",
  "apiError.This attribute must be unique": "{field} ha de ser únic",
  "components.AddFilterCTA.add": "Filtres",
  "components.AddFilterCTA.hide": "Filtres",
  "components.DragHandle-label": "Arrossegar",
  "components.DraggableAttr.edit": "Click per editar",
  "components.DraggableCard.delete.field": "Esborrar {item}",
  "components.DraggableCard.edit.field": "Edita {item}",
  "components.DraggableCard.move.field": "Moure {item}",
  "components.ListViewTable.row-line": "número d'element {number}",
  "components.DynamicZone.ComponentPicker-label": "Trieu un component",
  "components.DynamicZone.add-component": "Afegiu un component a {componentName}",
  "components.DynamicZone.delete-label": "Eliminar {name}",
  "components.DynamicZone.error-message": "El component conté error(s)",
  "components.DynamicZone.missing-components": "Hi ha {number, plural, =0 {# components faltants} one {# component faltant} other {# components faltants}}",
  "components.DynamicZone.move-down-label": "Moure component cap avall",
  "components.DynamicZone.move-up-label": "Moure component cap amunt",
  "components.DynamicZone.pick-compo": "Trieu un component",
  "components.DynamicZone.required": "Cal un component",
  "components.EmptyAttributesBlock.button": "Anar a la pàgina de configuracions",
  "components.EmptyAttributesBlock.description": "Podeu canviar les vostres configuracions",
  "components.FieldItem.linkToComponentLayout": "Establir el disseny del component",
  "components.FieldSelect.label": "Afegir un camp",
  "components.FilterOptions.button.apply": "Aplicar",
  "components.FiltersPickWrapper.PluginHeader.actions.apply": "Aplicar",
  "components.FiltersPickWrapper.PluginHeader.actions.clearAll": "Netejar tot",
  "components.FiltersPickWrapper.PluginHeader.description": "Estableix les condicions a aplicar per filtrar registres",
  "components.FiltersPickWrapper.PluginHeader.title.filter": "Filtres",
  "components.FiltersPickWrapper.hide": "Amagar",
  "components.LeftMenu.Search.label": "Buscar un tipus de contingut",
  "components.LeftMenu.collection-types": "Tipus de col·lecció",
  "components.LeftMenu.single-types": "Tipus individuals",
  "components.LimitSelect.itemsPerPage": "registres per pàgina",
  "components.NotAllowedInput.text": "Sense permisos per veure aquest camp",
  "components.RepeatableComponent.error-message": "Els components contenen errors",
  "components.Search.placeholder": "Buscar un registre...",
  "components.Select.draft-info-title": "Estat: Esborrany",
  "components.Select.publish-info-title": "Estat: Publicat",
  "components.SettingsViewWrapper.pluginHeader.description.edit-settings": "Personalitza com veureu la vista d'edició.",
  "components.SettingsViewWrapper.pluginHeader.description.list-settings": "Definiu la configuració de la vista de llista.",
  "components.SettingsViewWrapper.pluginHeader.title": "Configurar la vista - {name}",
  "components.TableDelete.delete": "Eliminar-ho tot",
  "components.TableDelete.deleteSelected": "Eliminar seleccionats",
  "components.TableDelete.label": "{number, plural, one {# entrada seleccionada} other {# entrades seleccionades}}",
  "components.TableEmpty.withFilters": "No hi ha {contentType} amb els filtres aplicats...",
  "components.TableEmpty.withSearch": "No hi ha {contentType} que coincideixi amb la cerca ({search})...",
  "components.TableEmpty.withoutFilter": "No hi ha {contentType}...",
  "components.empty-repeatable": "Encara no hi ha cap entrada. Feu clic al botó de sota per afegir un.",
  "components.notification.info.maximum-requirement": "Ja has assolit el nombre màxim de camps",
  "components.notification.info.minimum-requirement": "S'ha afegit un camp per complir el requisit mínim",
  "components.repeatable.reorder.error": "S'ha produït un error en reordenar el camp del component. Torneu-ho a provar.",
  "components.reset-entry": "Restablir entrada",
  "components.uid.apply": "aplicar",
  "components.uid.available": "disponible",
  "components.uid.regenerate": "regenerat",
  "components.uid.suggested": "suggerit",
  "components.uid.unavailable": "no disponible",
  "containers.Edit.Link.Layout": "Configurar el disseny",
  "containers.Edit.Link.Model": "Edita el Tipus de Col·lecció",
  "containers.Edit.addAnItem": "Afegir un registre...",
  "containers.Edit.clickToJump": "Click per anar al registre",
  "containers.Edit.delete": "Eliminar",
  "containers.Edit.delete-entry": "Eliminar aquesta entrada",
  "containers.Edit.editing": "Editant...",
  "containers.Edit.information": "Informació",
  "containers.Edit.information.by": "Per",
  "containers.Edit.information.created": "Creat",
  "containers.Edit.information.draftVersion": "versió preliminar",
  "containers.Edit.information.editing": "Edició",
  "containers.Edit.information.lastUpdate": "Última actualització",
  "containers.Edit.information.publishedVersion": "versió publicada",
  "containers.Edit.pluginHeader.title.new": "Crea una entrada",
  "containers.Edit.reset": "Reiniciar",
  "containers.Edit.returnList": "Tornar a la llista",
  "containers.Edit.seeDetails": "Detalls",
  "containers.Edit.submit": "Desa",
  "containers.EditSettingsView.modal-form.edit-field": "Edita el camp",
  "containers.EditView.add.new-entry": "Afegir una entrada",
  "containers.EditView.notification.errors": "El formulari conté alguns errors",
  "containers.Home.introduction": "Per editar els vostres registres aneu al link en específic al menu de l'esquerra. Aquesta extensió no té una manera d'editar configuracions i encara està en desenvolupament continu.",
  "containers.Home.pluginHeaderDescription": "Gestiona els seus registres en una bella i poderosa interfície.",
  "containers.Home.pluginHeaderTitle": "Gestor de contingut",
  "containers.List.draft": "Esborrany",
  "containers.List.errorFetchRecords": "Error",
  "containers.List.published": "Publicat",
  "containers.list.displayedFields": "Camps mostrats",
  "containers.list.items": "{number, plural, =0 {elements} one {element} other {elements}}",
  "containers.list.table-headers.publishedAt": "Estat",
  "containers.ListSettingsView.modal-form.edit-label": "Edita l'etiqueta",
  "containers.SettingPage.add.field": "Inserir un altre camp",
  "containers.SettingPage.attributes": "Camps d'atributs",
  "containers.SettingPage.attributes.description": "Definiu l'ordre dels vostres atributs",
  "containers.SettingPage.editSettings.description": "Arrossega i deixa anar els camps per construir el disseny",
  "containers.SettingPage.editSettings.entry.title": "Títol de l'entrada",
  "containers.SettingPage.editSettings.entry.title.description": "Estableix el camp per mostrar a la teva entrada",
  "containers.SettingPage.editSettings.relation-field.description": "Estableix el camp mostrat a les vistes de llista i edició",
  "containers.SettingPage.editSettings.title": "Editar (configuracions)",
  "containers.SettingPage.layout": "Disseny",
  "containers.SettingPage.listSettings.description": "Configureu les opcions per a aquest Tipus de Col·lecció",
  "containers.SettingPage.listSettings.title": "Llista (configuracions)",
  "containers.SettingPage.pluginHeaderDescription": "Configureu els paràmetres específics per a aquest Tipus de Col·lecció",
  "containers.SettingPage.settings": "Ajustaments",
  "containers.SettingPage.view": "Veure",
  "containers.SettingViewModel.pluginHeader.title": "Administrador de contingut - {name}",
  "containers.SettingsPage.Block.contentType.description": "Configuracions específiques",
  "containers.SettingsPage.Block.contentType.title": "Tipus de Col·lecció",
  "containers.SettingsPage.Block.generalSettings.description": "Configureu les opcions per defecte per als vostres Tipus de Col·lecció",
  "containers.SettingsPage.Block.generalSettings.title": "General",
  "containers.SettingsPage.pluginHeaderDescription": "Configureu els paràmetres per a tots els vostres tipus i grups de col·leccions",
  "containers.SettingsView.list.subtitle": "Configureu el disseny i la visualització dels vostres Tipus i Grups de Col·leccions",
  "containers.SettingsView.list.title": "Configuracions de pantalla",
  "edit-settings-view.link-to-ctb.components": "Edita el component",
  "edit-settings-view.link-to-ctb.content-types": "Edita el tipus de contingut",
  "emptyAttributes.button": "Anar al constructor de Tipus de Col·lecció",
  "emptyAttributes.description": "Afegiu el vostre primer camp al vostre Tipus de Col·lecció",
  "emptyAttributes.title": "Encara no hi ha camps",
  "error.attribute.key.taken": "Aquest valor ja existeix",
  "error.attribute.sameKeyAndName": "No poden ser iguals",
  "error.attribute.taken": "Aquest camp ja existeix",
  "error.contentTypeName.taken": "Aquest nom ja existeix",
  "error.model.fetch": "S'ha produït un error a la consulta de configuració de models.",
  "error.record.create": "S'ha produït un error a la creació del registre.",
  "error.record.delete": "S'ha produït un error a l'eliminació del registre.",
  "error.record.fetch": "S'ha produït un error a la consulta del registre.",
  "error.record.update": "S'ha produït un error a l'actualització del registre.",
  "error.records.count": "S'ha produït un error a la consulta del nombre de registres.",
  "error.records.fetch": "S'ha produït un error a la consulta de registres.",
  "error.schema.generation": "S'ha produït un error a la generació d'esquema.",
  "error.validation.json": "Aquest valor no és un JSON",
  "error.validation.max": "El valor és molt alt.",
  "error.validation.maxLength": "El valor és molt llarg.",
  "error.validation.min": "El valor és molt baix.",
  "error.validation.minLength": "El valor és molt curt.",
  "error.validation.minSupMax": "No pot ser superior",
  "error.validation.regex": "El valor no compleix l'expressió regular.",
  "error.validation.required": "Aquesta dada és requerida.",
  "form.Input.bulkActions": "Habilitar accions en bloc",
  "form.Input.defaultSort": "Atribut per ordenar per defecte",
  "form.Input.description": "Descripció",
  "form.Input.description.placeholder": "Mostra el nom al perfil",
  "form.Input.editable": "Camp editable",
  "form.Input.filters": "Habilitar filtres",
  "form.Input.label": "Etiqueta",
  "form.Input.label.inputDescription": "Aquest valor sobreescriu l'etiqueta mostrada a la capçalera de la taula",
  "form.Input.pageEntries": "Entrades per pàgina",
  "form.Input.pageEntries.inputDescription": "Nota: Podeu anul·lar aquest valor a la pàgina de configuració de Tipus de col·lecció.",
  "form.Input.placeholder": "Text suggerit",
  "form.Input.placeholder.placeholder": "El meu valor meravellós",
  "form.Input.search": "Habilitar la cerca",
  "form.Input.search.field": "Habilitar la cerca per a aquest camp",
  "form.Input.sort.field": "Habilitar ordenat per a aquest camp",
  "form.Input.sort.order": "Ordre per defecte",
  "form.Input.wysiwyg": "Mostra com WYSIWYG",
  "global.displayedFields": "Camps mostrats",
  groups,
  "groups.numbered": "Grups ({number})",
  "header.name": "Contingut",
  "link-to-ctb": "Edita el model",
  models,
  "models.numbered": "Tipus de Col·lecció ({number})",
  "notification.error.displayedFields": "Vostè necessita com a mínim un camp mostrat",
  "notification.error.relationship.fetch": "S'ha produït un error a la consulta de la relació.",
  "notification.info.SettingPage.disableSort": "Necessiteu tenir una habilitat l'ordenadació en un atribut",
  "notification.info.minimumFields": "Ha de tenir com a mínim un camp mostrat",
  "notification.upload.error": "S'ha produït un error en pujar els fitxers",
  pageNotFound,
  "pages.ListView.header-subtitle": "{number, plural, =0 {# entrades trobades} one {# entrada trobada} other {# entrades trobades}}",
  "pages.NoContentType.button": "Crea el teu primer tipus de contingut",
  "pages.NoContentType.text": "Encara no teniu cap contingut, us recomanem que creeu el seu primer tipus de contingut.",
  "permissions.not-allowed.create": "No es permet crear un document",
  "permissions.not-allowed.update": "No se us permet veure aquest document",
  "plugin.description.long": "Veure, editar i eliminar informació de la base de dades de manera ràpida.",
  "plugin.description.short": "Veure, editar i eliminar informació de la base de dades de manera ràpida.",
  "popUpWarning.bodyMessage.contentType.delete": "Esteu segur de voler eliminar aquest registre?",
  "popUpWarning.bodyMessage.contentType.delete.all": "Esteu segur de voler eliminar aquests registres?",
  "popUpWarning.warning.publish-question": "Encara vols publicar-ho?",
  "popUpwarning.warning.has-draft-relations.button-confirm": "Sí, publicar",
  "popover.display-relations.label": "Mostrar relacions",
  "success.record.delete": "Eliminat",
  "success.record.publish": "Publicat",
  "success.record.save": "Desat",
  "success.record.unpublish": "Sense publicar",
  "utils.data-loaded": "{number, plural, =1 {L'entrada s'ha carregat correctament} other {Les entrades s'han carregat correctament}}"
};
export {
  ca as default,
  groups,
  models,
  pageNotFound
};
//# sourceMappingURL=ca-5U32ON2v-RBPDUL7P.js.map
