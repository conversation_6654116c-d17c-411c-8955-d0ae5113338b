import{m as e,n as L,ga as f,a as M,c as S,gb as ae,e as se,gc as O,r as b,L as w,gd as r,M as g,A as F,b0 as te,ge as _,gf as ne,bV as q,O as H,cn as h,w as T,s as p,aS as $,J as k,gg as z,G as I,Q as re,aC as ie,x as v,a9 as oe,aa as le,bF as D,N as de,i as ce,j as ue,l as R,T as ge,o as me,p as B,q as E,V as he,t as xe,v as A,I as G,F as fe,gh as je,gi as pe,b5 as V,b3 as be,b6 as ye,bM as Me,bN as Se}from"./strapi-YzJfjJ2z.js";const Q=s=>s.name!==void 0,N=({disabled:s,variant:n="default"})=>{const{formatMessage:t}=f(),[a,d]=b.useState(!1);return e.jsxs(g.<PERSON>,{open:a,onOpenChange:d,children:[e.jsx(g.<PERSON><PERSON>,{children:e.jsx(F,{variant:n,disabled:s,startIcon:e.jsx(te,{}),onClick:()=>d(!0),size:"S",children:t({id:r("Settings.list.actions.add"),defaultMessage:"Add new locale"})})}),e.jsx(ve,{onClose:()=>d(!1)})]})},J=ce().shape({code:R().nullable().required({id:"Settings.locales.modal.create.code.error",defaultMessage:"Please select a locale"}),name:R().nullable().max(50,{id:"Settings.locales.modal.create.name.error.min",defaultMessage:"The locale display name can only be less than 50 characters."}).required({id:"Settings.locales.modal.create.name.error.required",defaultMessage:"Please give the locale a display name"}),isDefault:ue()}),Ce={code:"",name:"",isDefault:!1},ve=({onClose:s})=>{const n=_(),{toggleNotification:t}=M(),{_unstableFormatAPIError:a,_unstableFormatValidationErrors:d}=S(),[c]=ne(),{formatMessage:l}=f(),i=q("CreateModal",u=>u.refetchPermissions),o=async(u,m)=>{try{const x=await c(u);if("error"in x){Q(x.error)&&x.error.name==="ValidationError"?m.setErrors(d(x.error)):t({type:"danger",message:a(x.error)});return}t({type:"success",message:l({id:r("Settings.locales.modal.create.success"),defaultMessage:"Created locale"})}),i(),s()}catch{t({type:"danger",message:l({id:"notification.error",defaultMessage:"An error occurred, please try again"})})}};return e.jsx(g.Content,{children:e.jsxs(H,{method:"POST",initialValues:Ce,validationSchema:J,onSubmit:o,children:[e.jsx(g.Header,{children:e.jsx(g.Title,{children:l({id:r("Settings.list.actions.add"),defaultMessage:"Add new locale"})})}),e.jsx(g.Body,{children:e.jsxs(h.Root,{variant:"simple",defaultValue:"basic",children:[e.jsxs(T,{justifyContent:"space-between",children:[e.jsx(p,{tag:"h2",variant:"beta",id:n,children:l({id:r("Settings.locales.modal.title"),defaultMessage:"Configuration"})}),e.jsxs(h.List,{"aria-labelledby":n,children:[e.jsx(h.Trigger,{value:"basic",children:l({id:r("Settings.locales.modal.base"),defaultMessage:"Basic settings"})}),e.jsx(h.Trigger,{value:"advanced",children:l({id:r("Settings.locales.modal.advanced"),defaultMessage:"Advanced settings"})})]})]}),e.jsx($,{}),e.jsxs(k,{paddingTop:7,paddingBottom:7,children:[e.jsx(h.Content,{value:"basic",children:e.jsx(K,{})}),e.jsx(h.Content,{value:"advanced",children:e.jsx(W,{})})]})]})}),e.jsxs(g.Footer,{children:[e.jsx(g.Close,{children:e.jsx(F,{variant:"tertiary",children:l({id:"app.components.Button.cancel",defaultMessage:"Cancel"})})}),e.jsx(U,{})]})]})})},U=()=>{const{formatMessage:s}=f(),n=D("SubmitButton",a=>a.isSubmitting),t=D("SubmitButton",a=>a.modified);return e.jsx(F,{type:"submit",startIcon:e.jsx(de,{}),disabled:n||!t,children:s({id:"global.save",defaultMessage:"Save"})})},K=({mode:s="create"})=>{const{formatMessage:n}=f(),{toggleNotification:t}=M(),{_unstableFormatAPIError:a}=S(),{data:d,error:c}=z();if(b.useEffect(()=>{c&&t({type:"danger",message:a(c)})},[c,a,t]),!Array.isArray(d))return null;const l=d.map(o=>({label:o.name,value:o.code})),i=[{disabled:s!=="create",label:{id:r("Settings.locales.modal.create.code.label"),defaultMessage:"Locales"},name:"code",options:l,placeholder:{id:"components.placeholder.select",defaultMessage:"Select"},required:!0,size:6,type:"enumeration"},{hint:{id:r("Settings.locales.modal.create.name.label.description"),defaultMessage:"Locale will be displayed under that name in the administration panel"},label:{id:r("Settings.locales.modal.create.name.label"),defaultMessage:"Locale display name"},name:"name",required:!0,size:6,type:"string"}].map(o=>({...o,hint:o.hint?n(o.hint):void 0,label:n(o.label),placeholder:o.placeholder?n(o.placeholder):void 0}));return e.jsx(I.Root,{gap:4,children:i.map(({size:o,...u})=>e.jsx(I.Item,{col:o,direction:"column",alignItems:"stretch",children:e.jsx(X,{...u})},u.name))})},W=({isDefaultLocale:s})=>{const{formatMessage:n}=f(),t=[{disabled:s,hint:{id:r("Settings.locales.modal.advanced.setAsDefault.hint"),defaultMessage:"One default locale is required, change it by selecting another one"},label:{id:r("Settings.locales.modal.advanced.setAsDefault"),defaultMessage:"Set as default locale"},name:"isDefault",size:6,type:"boolean"}].map(a=>({...a,hint:a.hint?n(a.hint):void 0,label:n(a.label)}));return e.jsx(I.Root,{gap:4,children:t.map(({size:a,...d})=>e.jsx(I.Item,{col:a,direction:"column",alignItems:"stretch",children:e.jsx(X,{...d})},d.name))})},X=s=>{switch(s.type){case"enumeration":return e.jsx(Ee,{...s});default:return e.jsx(re,{...s})}},Ee=({disabled:s,hint:n,label:t,name:a,options:d,placeholder:c,required:l})=>{const{value:i,error:o,onChange:u}=ie(a),{data:m=[]}=z(),x=j=>{if(Array.isArray(m)){const P=m.find(C=>C.code===j);u(a,j),u("name",P.name)}else u(a,j)};return e.jsxs(v.Root,{error:o,hint:n,name:a,required:l,children:[e.jsx(v.Label,{children:t}),e.jsx(oe,{disabled:s,onChange:x,placeholder:c,value:i,children:d.map(j=>e.jsx(le,{value:j.value,children:j.label},j.value))}),e.jsx(v.Error,{}),e.jsx(v.Hint,{})]})},Ae=({id:s,name:n})=>{const{formatMessage:t}=f(),{toggleNotification:a}=M(),{_unstableFormatAPIError:d}=S(),[c,l]=b.useState(!1),[i]=pe(),o=async()=>{try{const u=await i(s);if("error"in u){a({type:"danger",message:d(u.error)});return}a({type:"success",message:t({id:r("Settings.locales.modal.delete.success"),defaultMessage:"Deleted locale"})}),l(!1)}catch{a({type:"danger",message:t({id:"notification.error",defaultMessage:"An error occurred, please try again"})})}};return e.jsxs(V.Root,{open:c,onOpenChange:l,children:[e.jsx(V.Trigger,{children:e.jsx(G,{onClick:()=>l(!0),label:t({id:r("Settings.list.actions.delete"),defaultMessage:"Delete {name} locale"},{name:n}),variant:"ghost",children:e.jsx(be,{})})}),e.jsx(ye,{onConfirm:o})]})},Le=s=>{const{formatMessage:n}=f(),[t,a]=b.useState(!1);return e.jsxs(e.Fragment,{children:[e.jsx(G,{onClick:()=>a(!0),label:n({id:r("Settings.list.actions.edit"),defaultMessage:"Edit {name} locale"},{name:s.name}),variant:"ghost",children:e.jsx(fe,{})}),e.jsx(Y,{...s,open:t,onOpenChange:a})]})},Y=({id:s,code:n,isDefault:t,name:a,open:d,onOpenChange:c})=>{const{toggleNotification:l}=M(),{_unstableFormatAPIError:i,_unstableFormatValidationErrors:o}=S(),u=q("EditModal",C=>C.refetchPermissions),{formatMessage:m}=f(),x=_(),[j]=je(),P=async({code:C,...Z},ee)=>{try{const y=await j({id:s,...Z});if("error"in y){Q(y.error)&&y.error.name==="ValidationError"?ee.setErrors(o(y.error)):l({type:"danger",message:i(y.error)});return}l({type:"success",message:m({id:r("Settings.locales.modal.edit.success"),defaultMessage:"Updated locale"})}),u(),c(!1)}catch{l({type:"danger",message:m({id:"notification.error",defaultMessage:"An error occurred, please try again"})})}};return e.jsx(g.Root,{open:d,onOpenChange:c,children:e.jsx(g.Content,{children:e.jsxs(H,{method:"PUT",onSubmit:P,initialValues:{code:n,name:a,isDefault:t},validationSchema:J,children:[e.jsx(g.Header,{children:e.jsx(g.Title,{children:m({id:r("Settings.list.actions.edit"),defaultMessage:"Edit a locale"},{name:a})})}),e.jsx(g.Body,{children:e.jsxs(h.Root,{variant:"simple",defaultValue:"basic",children:[e.jsxs(T,{justifyContent:"space-between",children:[e.jsx(p,{tag:"h2",variant:"beta",id:x,children:m({id:r("Settings.locales.modal.title"),defaultMessage:"Configuration"})}),e.jsxs(h.List,{"aria-labelledby":x,children:[e.jsx(h.Trigger,{value:"basic",children:m({id:r("Settings.locales.modal.base"),defaultMessage:"Basic settings"})}),e.jsx(h.Trigger,{value:"advanced",children:m({id:r("Settings.locales.modal.advanced"),defaultMessage:"Advanced settings"})})]})]}),e.jsx($,{}),e.jsxs(k,{paddingTop:7,paddingBottom:7,children:[e.jsx(h.Content,{value:"basic",children:e.jsx(K,{mode:"edit"})}),e.jsx(h.Content,{value:"advanced",children:e.jsx(W,{isDefaultLocale:t})})]})]})}),e.jsxs(g.Footer,{children:[e.jsx(g.Close,{children:e.jsx(F,{variant:"tertiary",children:m({id:"app.components.Button.cancel",defaultMessage:"Cancel"})})}),e.jsx(U,{})]})]})})})},Ie=({locales:s=[],canDelete:n,canUpdate:t})=>{const[a,d]=b.useState(),{formatMessage:c}=f(),l=i=>()=>{t&&d(i)};return e.jsxs(ge,{colCount:4,rowCount:s.length+1,children:[e.jsx(me,{children:e.jsxs(B,{children:[e.jsx(E,{children:e.jsx(p,{variant:"sigma",textColor:"neutral600",children:c({id:r("Settings.locales.row.id"),defaultMessage:"ID"})})}),e.jsx(E,{children:e.jsx(p,{variant:"sigma",textColor:"neutral600",children:c({id:r("Settings.locales.row.displayName"),defaultMessage:"Display name"})})}),e.jsx(E,{children:e.jsx(p,{variant:"sigma",textColor:"neutral600",children:c({id:r("Settings.locales.row.default-locale"),defaultMessage:"Default locale"})})}),e.jsx(E,{children:e.jsx(he,{children:"Actions"})})]})}),e.jsx(xe,{children:s.map(i=>e.jsxs(b.Fragment,{children:[e.jsxs(B,{onClick:l(i.id),style:{cursor:t?"pointer":"default"},children:[e.jsx(A,{children:e.jsx(p,{textColor:"neutral800",children:i.id})}),e.jsx(A,{children:e.jsx(p,{textColor:"neutral800",children:i.name})}),e.jsx(A,{children:e.jsx(p,{textColor:"neutral800",children:i.isDefault?c({id:r("Settings.locales.default"),defaultMessage:"Default"}):null})}),e.jsx(A,{children:e.jsxs(T,{gap:1,justifyContent:"flex-end",onClick:o=>o.stopPropagation(),children:[t&&e.jsx(Le,{...i}),n&&!i.isDefault&&e.jsx(Ae,{...i})]})})]}),e.jsx(Y,{...i,onOpenChange:()=>d(void 0),open:a===i.id})]},i.id))})]})},Fe=()=>{const{formatMessage:s}=f(),{toggleNotification:n}=M(),{_unstableFormatAPIError:t}=S(),{data:a,isLoading:d,error:c}=ae(),{isLoading:l,allowedActions:{canUpdate:i,canCreate:o,canDelete:u}}=se(O);return b.useEffect(()=>{c&&n({type:"danger",message:t(c)})},[c,t,n]),d||l?e.jsx(L.Loading,{}):c||!Array.isArray(a)?e.jsx(L.Error,{}):e.jsxs(L.Main,{tabIndex:-1,children:[e.jsx(w.Header,{primaryAction:e.jsx(N,{disabled:!o}),title:s({id:r("plugin.name"),defaultMessage:"Internationalization"}),subtitle:s({id:r("Settings.list.description"),defaultMessage:"Configure the settings"})}),e.jsx(w.Content,{children:a.length>0?e.jsx(Ie,{locales:a,canDelete:u,canUpdate:i}):e.jsx(Me,{icon:e.jsx(Se,{width:void 0,height:void 0}),content:s({id:r("Settings.list.empty.title"),defaultMessage:"There are no locales"}),action:e.jsx(N,{disabled:!o,variant:"secondary"})})})]})},Te=()=>e.jsx(L.Protect,{permissions:O.read,children:e.jsx(Fe,{})});export{Te as ProtectedSettingsPage,Fe as SettingsPage};
