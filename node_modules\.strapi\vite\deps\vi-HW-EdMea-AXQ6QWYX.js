import "./chunk-WGAPYIUP.js";

// node_modules/@strapi/plugin-users-permissions/dist/_chunks/vi-HW-EdMea.mjs
var vi = {
  "BoundRoute.title": "Đến tới",
  "EditForm.inputSelect.description.role": "<PERSON><PERSON>h kèm người dùng mới đã được xác thực vào quyền được chọn.",
  "EditForm.inputSelect.label.role": "Quyền mặc định cho các người dùng đã được chứng thực",
  "EditForm.inputToggle.description.email": "Không cho phép người dùng tạo nhiều tài khoản có cùng địa chỉ email với nhiều nhà cung cấp chứng thực.",
  "EditForm.inputToggle.description.email-confirmation": "<PERSON><PERSON> đư<PERSON><PERSON> k<PERSON>ch ho<PERSON> (ON), người dùng đăng ký mới nhận được một email xác nhận.",
  "EditForm.inputToggle.description.email-confirmation-redirection": "<PERSON>u khi xác nhận email của bạn, chọn nơi bạn sẽ được đưa về.",
  "EditForm.inputToggle.description.email-reset-password": "URL của trang lấy lại mật khẩu của ứng dụng của bạn",
  "EditForm.inputToggle.description.sign-up": "Khi không kích hoạt (OFF), quá trình đăng ký bị cấm. Không ai có thể đăng ký thêm dù dùng bất kỳ nhà cung cấp nào.",
  "EditForm.inputToggle.label.email": "Một tài khoản trên một địa chỉ email",
  "EditForm.inputToggle.label.email-confirmation": "Kích hoạt email xác nhận",
  "EditForm.inputToggle.label.email-confirmation-redirection": "URL đưa về",
  "EditForm.inputToggle.label.email-reset-password": "Trang lấy lại mật khẩu",
  "EditForm.inputToggle.label.sign-up": "Kích hoạt đăng ký",
  "HeaderNav.link.advancedSettings": "Cài đặt nâng cao",
  "HeaderNav.link.emailTemplates": "Mẫu email",
  "HeaderNav.link.providers": "Các nhà cung cấp",
  "Plugin.permissions.plugins.description": "Định nghĩa tất cả hành động được phép cho {name} plugin.",
  "Plugins.header.description": "Chỉ các hành động đến bởi một đường dẫn được liệt kê bên dưới.",
  "Plugins.header.title": "Các Quyền",
  "Policies.header.hint": "Chọn các hành động của ứng dựng hoặc hành động của plugin và nhấn vào biểu tượng bánh răng để hiển thị đường đến",
  "Policies.header.title": "Các cài đặt nâng cao",
  "PopUpForm.Email.email_templates.inputDescription": "Nếu bạn không chắc sử dụng các biến như thế nào, {link}",
  "PopUpForm.Email.options.from.email.label": "Email người gửi",
  "PopUpForm.Email.options.from.email.placeholder": "<EMAIL>",
  "PopUpForm.Email.options.from.name.label": "Tên người gửi",
  "PopUpForm.Email.options.from.name.placeholder": "Kai Doe",
  "PopUpForm.Email.options.message.label": "Thông điệp",
  "PopUpForm.Email.options.object.label": "Chủ đề",
  "PopUpForm.Email.options.response_email.label": "Email phản hồi",
  "PopUpForm.Email.options.response_email.placeholder": "<EMAIL>",
  "PopUpForm.Providers.enabled.description": "Nếu không kích hoạt, người dùng sẽ không thể dùng nhà cung cấp này.",
  "PopUpForm.Providers.enabled.label": "Kích hoạt",
  "PopUpForm.Providers.key.label": "Client ID",
  "PopUpForm.Providers.key.placeholder": "VĂN BẢN",
  "PopUpForm.Providers.redirectURL.front-end.label": "URL chuyển tiếp đến ứng dụng bên ngoài của bạn",
  "PopUpForm.Providers.secret.label": "Client Secret",
  "PopUpForm.Providers.secret.placeholder": "VĂN BẢN",
  "PopUpForm.Providers.subdomain.label": "Host URI (Subdomain)",
  "PopUpForm.Providers.subdomain.placeholder": "my.subdomain.com",
  "PopUpForm.header.edit.email-templates": "Sửa Các Mẫu Email",
  "notification.success.submit": "Các cấu hình đã được cập nhật",
  "plugin.description.long": "Bảo vệ API của bạn với quá trình chứng thực đầy đủ dựa trên JWT. Plugin này cũng kèm với chiến lược ACL cho phép bạn quản lý quyền giữa các nhóm người dùng.",
  "plugin.description.short": "Bảo vệ API của bạn với quá trình chứng thực đầy đủ dựa trên JWT",
  "plugin.name": "Vai trò và Quyền"
};
export {
  vi as default
};
//# sourceMappingURL=vi-HW-EdMea-AXQ6QWYX.js.map
