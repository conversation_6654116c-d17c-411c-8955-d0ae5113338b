import{ax as o,m as t,n as e}from"./strapi-YzJfjJ2z.js";import{EditView as p}from"./EditViewPage-DU5vF00W-BHjmq8SS.js";import"./apiTokens-BOJ6JDtg-_9662LTw.js";import"./constants-CRj0ViV1-Q2dfXdfa.js";import"./TokenTypeSelect-Cr8pc-yV-C65ZkGgP.js";import"./transferTokens-CXTFej3W-kOI9Cp4M.js";import"./index-CJ4ZBILS.js";import"./index-BRVyLNfZ.js";import"./tail-B0SLYYv9.js";import"./_baseMap-LzQFtWYw.js";import"./_baseEach-BkkNIx9z.js";const g=()=>{const r=o(i=>i.admin_app.permissions.settings?.["api-tokens"].create);return t.jsx(e.Protect,{permissions:r,children:t.jsx(p,{})})};export{g as ProtectedCreateView};
