{"version": 3, "sources": ["../../../@strapi/plugin-users-permissions/dist/_chunks/pl-BdIzifBE.mjs"], "sourcesContent": ["const pl = {\n  \"BoundRoute.title\": \"Wywoływanie\",\n  \"EditForm.inputSelect.description.role\": \"Połączy nowego uwierzytelnionego użytkownika z wybraną rolą.\",\n  \"EditForm.inputSelect.label.role\": \"Domyślna rola dla uwierzytelnionych użytkowników\",\n  \"EditForm.inputToggle.description.email\": \"Nie zezwalaj użytkownikowi na tworzenie wielu kont za pomocą tego samego adresu e-mail u różnych dostawców uwierzytelniania.\",\n  \"EditForm.inputToggle.description.email-confirmation\": \"<PERSON><PERSON> (ON), nowo zarejestrowani uzytkownicy otrzymają wiadomość potwierdzającą.\",\n  \"EditForm.inputToggle.description.email-confirmation-redirection\": \"Po potwierdzeniu adresu email, wybierz gdzie zostaniesz przekierowany.\",\n  \"EditForm.inputToggle.description.email-reset-password\": \"Adres URL strony resetowania hasła aplikacji\",\n  \"EditForm.inputToggle.description.sign-up\": \"<PERSON> w<PERSON>u (OFF) proces rejestracji jest zabroniony. Nikt nie może już dołączyć bez względu na używanego dostawcę.\",\n  \"EditForm.inputToggle.label.email\": \"Jedno konto na adres email\",\n  \"EditForm.inputToggle.label.email-confirmation\": \"Zezwól na potwierdzenie adresu email\",\n  \"EditForm.inputToggle.label.email-confirmation-redirection\": \"Url przekierowania\",\n  \"EditForm.inputToggle.label.email-reset-password\": \"Strona resetowania hasła\",\n  \"EditForm.inputToggle.label.sign-up\": \"Włącz możliwość rejestracji\",\n  \"EditForm.inputToggle.placeholder.email-confirmation-redirection\": \"ex: https://yourfrontend.com/confirmation-redirection\",\n  \"EditForm.inputToggle.placeholder.email-reset-password\": \"ex: https://yourfrontend.com/reset-password\",\n  \"EditPage.form.roles\": \"Szczegóły roli\",\n  \"Email.template.data.loaded\": \"Szablon email został załadowany\",\n  \"Email.template.email_confirmation\": \"Potwierdzenie adresu email\",\n  \"Email.template.form.edit.label\": \"Edytuj szablon\",\n  \"Email.template.table.action.label\": \"akcja\",\n  \"Email.template.table.icon.label\": \"ikonka\",\n  \"Email.template.table.name.label\": \"nazwa\",\n  \"Form.advancedSettings.data.loaded\": \"Ustawienia zaawansowane zostały załadowane\",\n  \"HeaderNav.link.advancedSettings\": \"Zaawansowane\",\n  \"HeaderNav.link.emailTemplates\": \"Szablony e-mail\",\n  \"HeaderNav.link.providers\": \"Dostawcy\",\n  \"Plugin.permissions.plugins.description\": \"Określ dozwolone działania dla pluginu {name}.\",\n  \"Plugins.header.description\": \"Jedynie akcje związane z wywoływaniami są wymienione poniżej.\",\n  \"Plugins.header.title\": \"Uprawnienia\",\n  \"Policies.header.hint\": \"Wybierz działania aplikacji lub działania pluginu i kliknij ikonę koła zębatego, aby wyświetlić wywoływania\",\n  \"Policies.header.title\": \"Zaawansowane\",\n  \"PopUpForm.Email.email_templates.inputDescription\": \"Nie wiesz jak skonfigurować zmienne? {link}\",\n  \"PopUpForm.Email.link.documentation\": \"sprawdź dokumentację.\",\n  \"PopUpForm.Email.options.from.email.label\": \"Email nadawcy\",\n  \"PopUpForm.Email.options.from.email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Email.options.from.name.label\": \"Nazwa nadawcy\",\n  \"PopUpForm.Email.options.from.name.placeholder\": \"Jan Nowak\",\n  \"PopUpForm.Email.options.message.label\": \"Wiadomość\",\n  \"PopUpForm.Email.options.object.label\": \"Temat\",\n  \"PopUpForm.Email.options.object.placeholder\": \"Proszę potwierdź adres email dla %APP_NAME%\",\n  \"PopUpForm.Email.options.response_email.label\": \"Email zwrotny\",\n  \"PopUpForm.Email.options.response_email.placeholder\": \"<EMAIL>\",\n  \"PopUpForm.Providers.enabled.description\": \"W przypadku wyłączenia, użytkownicy nie będą mogli skorzystać z tego dostawcy.\",\n  \"PopUpForm.Providers.enabled.label\": \"Włączony\",\n  \"PopUpForm.Providers.key.label\": \"ID klienta\",\n  \"PopUpForm.Providers.key.placeholder\": \"TEKST\",\n  \"PopUpForm.Providers.redirectURL.front-end.label\": \"Adres przekierowania do własnej aplikacji\",\n  \"PopUpForm.Providers.redirectURL.label\": \"Adres przekierowania do dodania w twoich ustawieniach aplikacji: {provider}\",\n  \"PopUpForm.Providers.secret.label\": \"Klucz sekretny klienta\",\n  \"PopUpForm.Providers.secret.placeholder\": \"TEKST\",\n  \"PopUpForm.Providers.subdomain.label\": \"Host URI (Subdomain)\",\n  \"PopUpForm.Providers.subdomain.placeholder\": \"my.subdomain.com\",\n  \"PopUpForm.header.edit.email-templates\": \"Zmień szablony e-mail\",\n  \"PopUpForm.header.edit.providers\": \"Edytuj dostawcę\",\n  \"Providers.data.loaded\": \"Dostawcy zostali załadowani\",\n  \"Providers.status\": \"Status\",\n  \"Roles.empty\": \"Nie masz jeszcze żadnych ról.\",\n  \"Roles.empty.search\": \"Żadne role nie pasują do wyszukiwania.\",\n  \"Settings.roles.deleted\": \"Rola usunięta\",\n  \"Settings.roles.edited\": \"Rola edytowana\",\n  \"Settings.section-label\": \"Użytkownicy i Uprawnienia\",\n  \"components.Input.error.validation.email\": \"Ten email jest niepoprawny\",\n  \"components.Input.error.validation.json\": \"Nie pasuje do formatu JSON\",\n  \"components.Input.error.validation.max\": \"Wartość zbyt duża.\",\n  \"components.Input.error.validation.maxLength\": \"Wartość zbyt długa.\",\n  \"components.Input.error.validation.min\": \"Wartość zbyt mała.\",\n  \"components.Input.error.validation.minLength\": \"Wartość zbyt krótka.\",\n  \"components.Input.error.validation.minSupMax\": \"Nie może być wyższy\",\n  \"components.Input.error.validation.regex\": \"Wartość nie pasuje do regexa.\",\n  \"components.Input.error.validation.required\": \"Wartość wymagana.\",\n  \"components.Input.error.validation.unique\": \"Wartość już używana.\",\n  \"notification.success.submit\": \"Ustawienia zostały zaktualizowane\",\n  \"page.title\": \"Ustawienia - Role\",\n  \"plugin.description.long\": \"Chroń API za pomocą procesu pełnego uwierzytelniania opartego na JWT. Ten plugin zawiera również strategię ACL, która pozwala zarządzać uprawnieniami między grupami użytkowników.\",\n  \"plugin.description.short\": \"Chroń API za pomocą procesu pełnego uwierzytelniania opartego na JWT\",\n  \"plugin.name\": \"Role i Uprawnienia\",\n  \"popUpWarning.button.cancel\": \"Anuluj\",\n  \"popUpWarning.button.confirm\": \"Potwierdź\",\n  \"popUpWarning.title\": \"Proszę potwierdź\",\n  \"popUpWarning.warning.cancel\": \"Czy jesteś pewny, że chcesz anulować zmiany?\"\n};\nexport {\n  pl as default\n};\n//# sourceMappingURL=pl-BdIzifBE.mjs.map\n"], "mappings": ";;;AAAA,IAAM,KAAK;AAAA,EACT,oBAAoB;AAAA,EACpB,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,0CAA0C;AAAA,EAC1C,uDAAuD;AAAA,EACvD,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,4CAA4C;AAAA,EAC5C,oCAAoC;AAAA,EACpC,iDAAiD;AAAA,EACjD,6DAA6D;AAAA,EAC7D,mDAAmD;AAAA,EACnD,sCAAsC;AAAA,EACtC,mEAAmE;AAAA,EACnE,yDAAyD;AAAA,EACzD,uBAAuB;AAAA,EACvB,8BAA8B;AAAA,EAC9B,qCAAqC;AAAA,EACrC,kCAAkC;AAAA,EAClC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,mCAAmC;AAAA,EACnC,qCAAqC;AAAA,EACrC,mCAAmC;AAAA,EACnC,iCAAiC;AAAA,EACjC,4BAA4B;AAAA,EAC5B,0CAA0C;AAAA,EAC1C,8BAA8B;AAAA,EAC9B,wBAAwB;AAAA,EACxB,wBAAwB;AAAA,EACxB,yBAAyB;AAAA,EACzB,oDAAoD;AAAA,EACpD,sCAAsC;AAAA,EACtC,4CAA4C;AAAA,EAC5C,kDAAkD;AAAA,EAClD,2CAA2C;AAAA,EAC3C,iDAAiD;AAAA,EACjD,yCAAyC;AAAA,EACzC,wCAAwC;AAAA,EACxC,8CAA8C;AAAA,EAC9C,gDAAgD;AAAA,EAChD,sDAAsD;AAAA,EACtD,2CAA2C;AAAA,EAC3C,qCAAqC;AAAA,EACrC,iCAAiC;AAAA,EACjC,uCAAuC;AAAA,EACvC,mDAAmD;AAAA,EACnD,yCAAyC;AAAA,EACzC,oCAAoC;AAAA,EACpC,0CAA0C;AAAA,EAC1C,uCAAuC;AAAA,EACvC,6CAA6C;AAAA,EAC7C,yCAAyC;AAAA,EACzC,mCAAmC;AAAA,EACnC,yBAAyB;AAAA,EACzB,oBAAoB;AAAA,EACpB,eAAe;AAAA,EACf,sBAAsB;AAAA,EACtB,0BAA0B;AAAA,EAC1B,yBAAyB;AAAA,EACzB,0BAA0B;AAAA,EAC1B,2CAA2C;AAAA,EAC3C,0CAA0C;AAAA,EAC1C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,yCAAyC;AAAA,EACzC,+CAA+C;AAAA,EAC/C,+CAA+C;AAAA,EAC/C,2CAA2C;AAAA,EAC3C,8CAA8C;AAAA,EAC9C,4CAA4C;AAAA,EAC5C,+BAA+B;AAAA,EAC/B,cAAc;AAAA,EACd,2BAA2B;AAAA,EAC3B,4BAA4B;AAAA,EAC5B,eAAe;AAAA,EACf,8BAA8B;AAAA,EAC9B,+BAA+B;AAAA,EAC/B,sBAAsB;AAAA,EACtB,+BAA+B;AACjC;", "names": []}