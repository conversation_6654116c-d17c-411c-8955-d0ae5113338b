import {
  fn
} from "./chunk-UVVU2BYJ.js";
import {
  PERMISSIONS,
  getTrad,
  useIntl
} from "./chunk-S4GMEU6I.js";
import "./chunk-RPX6VIML.js";
import "./chunk-VOKTPSOW.js";
import "./chunk-7LS5AJR5.js";
import "./chunk-3E4PH4JJ.js";
import "./chunk-KKUAHZGP.js";
import "./chunk-ZESFYHFX.js";
import "./chunk-MMOBCIZG.js";
import {
  Layouts,
  useFetchClient,
  useMutation,
  useQuery
} from "./chunk-ELTZWS66.js";
import {
  Page,
  require_isEqual,
  require_set,
  useNotification
} from "./chunk-4C2ZQ5OG.js";
import {
  Box,
  Button,
  Field,
  Flex,
  Grid,
  Toggle,
  Typography,
  require_lib
} from "./chunk-FGNN7I5W.js";
import "./chunk-2JZ35VNI.js";
import "./chunk-WHKY4GB5.js";
import "./chunk-IYHTQODQ.js";
import {
  ForwardRef$4p
} from "./chunk-DICHSZ7M.js";
import {
  require_jsx_runtime
} from "./chunk-QBLEIVF7.js";
import "./chunk-6VAEU2GM.js";
import {
  require_react
} from "./chunk-BCGHH3YY.js";
import {
  __toESM
} from "./chunk-WGAPYIUP.js";

// node_modules/@strapi/upload/dist/_chunks/index-BWIqjWzC.mjs
var import_jsx_runtime = __toESM(require_jsx_runtime(), 1);
var import_react = __toESM(require_react(), 1);
var import_isEqual = __toESM(require_isEqual(), 1);
var import_qs = __toESM(require_lib(), 1);
var import_set = __toESM(require_set(), 1);
var init = (initialState2) => {
  return initialState2;
};
var initialState = {
  initialData: {
    responsiveDimensions: true,
    sizeOptimization: true,
    autoOrientation: false,
    videoPreview: false
  },
  modifiedData: {
    responsiveDimensions: true,
    sizeOptimization: true,
    autoOrientation: false,
    videoPreview: false
  }
};
var reducer = (state, action) => (
  // eslint-disable-next-line consistent-return
  fn(state, (drafState) => {
    switch (action.type) {
      case "GET_DATA_SUCCEEDED": {
        drafState.initialData = action.data;
        drafState.modifiedData = action.data;
        break;
      }
      case "ON_CHANGE": {
        (0, import_set.default)(drafState, ["modifiedData", ...action.keys.split(".")], action.value);
        break;
      }
      default:
        return state;
    }
  })
);
var SettingsPage = () => {
  const { formatMessage } = useIntl();
  const { toggleNotification } = useNotification();
  const { get, put } = useFetchClient();
  const [{ initialData, modifiedData }, dispatch] = (0, import_react.useReducer)(reducer, initialState, init);
  const { data, isLoading, refetch } = useQuery({
    queryKey: ["upload", "settings"],
    async queryFn() {
      const {
        data: { data: data2 }
      } = await get("/upload/settings");
      return data2;
    }
  });
  import_react.default.useEffect(() => {
    if (data) {
      dispatch({
        type: "GET_DATA_SUCCEEDED",
        data
      });
    }
  }, [data]);
  const isSaveButtonDisabled = (0, import_isEqual.default)(initialData, modifiedData);
  const { mutateAsync, isLoading: isSubmiting } = useMutation({
    async mutationFn(body) {
      return put("/upload/settings", body);
    },
    onSuccess() {
      refetch();
      toggleNotification({
        type: "success",
        message: formatMessage({ id: "notification.form.success.fields" })
      });
    },
    onError(err) {
      console.error(err);
    }
  });
  const handleSubmit = async (e) => {
    e.preventDefault();
    if (isSaveButtonDisabled) {
      return;
    }
    await mutateAsync(modifiedData);
  };
  const handleChange = ({ target: { name, value } }) => {
    dispatch({
      type: "ON_CHANGE",
      keys: name,
      value
    });
  };
  if (isLoading) {
    return (0, import_jsx_runtime.jsx)(Page.Loading, {});
  }
  return (0, import_jsx_runtime.jsxs)(Page.Main, { tabIndex: -1, children: [
    (0, import_jsx_runtime.jsx)(Page.Title, { children: formatMessage({
      id: getTrad("page.title"),
      defaultMessage: "Settings - Media Libray"
    }) }),
    (0, import_jsx_runtime.jsxs)("form", { onSubmit: handleSubmit, children: [
      (0, import_jsx_runtime.jsx)(
        Layouts.Header,
        {
          title: formatMessage({
            id: getTrad("settings.header.label"),
            defaultMessage: "Media Library"
          }),
          primaryAction: (0, import_jsx_runtime.jsx)(
            Button,
            {
              disabled: isSaveButtonDisabled,
              loading: isSubmiting,
              type: "submit",
              startIcon: (0, import_jsx_runtime.jsx)(ForwardRef$4p, {}),
              size: "S",
              children: formatMessage({
                id: "global.save",
                defaultMessage: "Save"
              })
            }
          ),
          subtitle: formatMessage({
            id: getTrad("settings.sub-header.label"),
            defaultMessage: "Configure the settings for the Media Library"
          })
        }
      ),
      (0, import_jsx_runtime.jsx)(Layouts.Content, { children: (0, import_jsx_runtime.jsx)(Layouts.Root, { children: (0, import_jsx_runtime.jsx)(Flex, { direction: "column", alignItems: "stretch", gap: 12, children: (0, import_jsx_runtime.jsx)(Box, { background: "neutral0", padding: 6, shadow: "filterShadow", hasRadius: true, children: (0, import_jsx_runtime.jsxs)(Flex, { direction: "column", alignItems: "stretch", gap: 4, children: [
        (0, import_jsx_runtime.jsx)(Flex, { children: (0, import_jsx_runtime.jsx)(Typography, { variant: "delta", tag: "h2", children: formatMessage({
          id: getTrad("settings.blockTitle"),
          defaultMessage: "Asset management"
        }) }) }),
        (0, import_jsx_runtime.jsxs)(Grid.Root, { gap: 6, children: [
          (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
            Field.Root,
            {
              hint: formatMessage({
                id: getTrad("settings.form.responsiveDimensions.description"),
                defaultMessage: "Enabling this option will generate multiple formats (small, medium and large) of the uploaded asset."
              }),
              name: "responsiveDimensions",
              children: [
                (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                  id: getTrad("settings.form.responsiveDimensions.label"),
                  defaultMessage: "Responsive friendly upload"
                }) }),
                (0, import_jsx_runtime.jsx)(
                  Toggle,
                  {
                    checked: modifiedData.responsiveDimensions,
                    offLabel: formatMessage({
                      id: "app.components.ToggleCheckbox.off-label",
                      defaultMessage: "Off"
                    }),
                    onLabel: formatMessage({
                      id: "app.components.ToggleCheckbox.on-label",
                      defaultMessage: "On"
                    }),
                    onChange: (e) => {
                      handleChange({
                        target: { name: "responsiveDimensions", value: e.target.checked }
                      });
                    }
                  }
                ),
                (0, import_jsx_runtime.jsx)(Field.Hint, {})
              ]
            }
          ) }),
          (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
            Field.Root,
            {
              hint: formatMessage({
                id: getTrad("settings.form.sizeOptimization.description"),
                defaultMessage: "Enabling this option will reduce the image size and slightly reduce its quality."
              }),
              name: "sizeOptimization",
              children: [
                (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                  id: getTrad("settings.form.sizeOptimization.label"),
                  defaultMessage: "Size optimization"
                }) }),
                (0, import_jsx_runtime.jsx)(
                  Toggle,
                  {
                    checked: modifiedData.sizeOptimization,
                    offLabel: formatMessage({
                      id: "app.components.ToggleCheckbox.off-label",
                      defaultMessage: "Off"
                    }),
                    onLabel: formatMessage({
                      id: "app.components.ToggleCheckbox.on-label",
                      defaultMessage: "On"
                    }),
                    onChange: (e) => {
                      handleChange({
                        target: { name: "sizeOptimization", value: e.target.checked }
                      });
                    }
                  }
                ),
                (0, import_jsx_runtime.jsx)(Field.Hint, {})
              ]
            }
          ) }),
          (0, import_jsx_runtime.jsx)(Grid.Item, { col: 6, s: 12, direction: "column", alignItems: "stretch", children: (0, import_jsx_runtime.jsxs)(
            Field.Root,
            {
              hint: formatMessage({
                id: getTrad("settings.form.autoOrientation.description"),
                defaultMessage: "Enabling this option will automatically rotate the image according to EXIF orientation tag."
              }),
              name: "autoOrientation",
              children: [
                (0, import_jsx_runtime.jsx)(Field.Label, { children: formatMessage({
                  id: getTrad("settings.form.autoOrientation.label"),
                  defaultMessage: "Auto orientation"
                }) }),
                (0, import_jsx_runtime.jsx)(
                  Toggle,
                  {
                    checked: modifiedData.autoOrientation,
                    offLabel: formatMessage({
                      id: "app.components.ToggleCheckbox.off-label",
                      defaultMessage: "Off"
                    }),
                    onLabel: formatMessage({
                      id: "app.components.ToggleCheckbox.on-label",
                      defaultMessage: "On"
                    }),
                    onChange: (e) => {
                      handleChange({
                        target: { name: "autoOrientation", value: e.target.checked }
                      });
                    }
                  }
                ),
                (0, import_jsx_runtime.jsx)(Field.Hint, {})
              ]
            }
          ) })
        ] })
      ] }) }) }) }) })
    ] })
  ] });
};
var ProtectedSettingsPage = () => (0, import_jsx_runtime.jsx)(Page.Protect, { permissions: PERMISSIONS.settings, children: (0, import_jsx_runtime.jsx)(SettingsPage, {}) });
export {
  SettingsPage,
  ProtectedSettingsPage as default
};
//# sourceMappingURL=index-BWIqjWzC-V4WILYOK.js.map
